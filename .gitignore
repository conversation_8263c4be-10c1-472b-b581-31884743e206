# Environment and Virtual Environment
.env
.venv
venv/
env/
ENV/
.run
.claude
.claude/settings.local.json
.opencode
.opencode/*
.claude/*
.claude/settings.local.json
.opencode.json
my_env.yml
environment.yml.bak

# UV specific
.uv/
uv.lock

# Python
*.egg-info/
build/
dist/
*.egg
*.pyc
*.pyo
*.py.class
*~
.pyc
.pyo
.DS_Store
__pycache__/
.pytest_cache/
.mypy_cache/
.ruff_cache/

# DynamoDB local data
.dynamodb*
src/.dynamodb*
*.db
*.db-journal

# Data directories (project specific)
data/
Empty Dockets/
qdrant_data/
src/data/law_firms/  # Assuming you still want to ignore this
src/lib/court_mdl_data/pacer_orders/
assets/research/
archived/
archive/*
deprecated/
config/*.yml
@config/*.yml

# Image assets and flowcharts
src/assets/img/
src/flowcharts/

# Debug and temporary files
debug_country_dropdown.png
filtered_facebook_ads.json
scripts/analysis/talc/jj_pacermon.html

# Python compiled files and cache
**/__pycache__/
*.pyc

# IDE and tools
.idea/
.vscode*
.aider*
.crush/

# Certificates and other specific files
DigiCertGlobalG2TLSRSASHA2562020CA1-1.crt.pem
reporting.md

# Log files (general project logs in root)
*.log

# Cache files
cache/
**/*cache*/
*.pkl
*.cache

# Processing results
processed_batch_results/
**/results/
*.jsonl

# Files that no longer exist but are causing repo-map errors
src/config/prompts/extract_case_data_system.md
src/config/prompts/extract_case_data_user.md
src/config/prompts/extract_mdl_attorneys_system.md
src/lib/bubble_api.py
src/lib/bubble_data_processor.py
src/lib/color_logging.py
src/lib/deepseek_client.py
src/lib/facebook_account_manager.py
src/lib/fb_ads_enhanced.py
src/lib/filings_by_jurisdiction.png
src/lib/flare_pos.py
src/lib/gpt_client.py
src/lib/graphql_parser2.py
src/lib/lawfirm_data_loader.py
src/lib/litigation_data_loader.py
src/lib/litigation_map_manager.py
src/lib/local_dynamo_mixin.py
src/lib/mailgun.py
src/lib/mailgun_manager.py
src/lib/mdl_parser.py
src/lib/mdl_scraper.py
src/lib/pacer_data_loader.py
src/lib/process_mdl.py
src/lib/unicourt_api.py
src/lib/updated_dynamodb_cli.py
src/lib/updated_report_generator3.py
src/pacer_v2/data_processor.py
src/pacer_v2/download_manager.py
src/pacer_v2/error_handling.py
src/pacer_v2/interaction_manager.py
src/pacer_v2/logger.py
src/pacer_v2/main_control.py
src/pacer_v2/navigation_manager.py
src/pacer_v2/session_manager.py
src/pacer_v2/utility_manager.py
src/pacer_v2/web_driver_setup.py

# Nuclear exclusions
*.pdf
*.csv

# Temporary HTML files
pacermon_html/

.claude/settings.local.json
cline_backup_rules.md
config/*
.claude/settings.local.json.claude/settings/

# AI Tool Generated Files - All except .claude
.*
!.claude/
!.git/
!.gitignore
!.gitattributes

# Claude Flow generated files
.mcp.json
claude-flow.config.json
.swarm/
.hive-mind/
.roo/
.serena/
.opencode/
memory/claude-flow-data.json
memory/sessions/*
!memory/sessions/README.md
memory/agents/*
!memory/agents/README.md
coordination/memory_bank/*
coordination/subtasks/*
coordination/orchestration/*
*.db
*.db-journal
*.db-wal
*.db-shm
*.sqlite
*.sqlite-journal
*.sqlite-wal
claude-flow
claude-flow.bat
claude-flow.ps1
hive-mind-prompt-*.txt
prompt*.md

# Other AI tool folders
.clinerules
.goosehints
.roomodes
.secrets.baseline
.yamllint.yml
.mcp-gemini.json
.nvmrc
.pre-commit-config.yaml
