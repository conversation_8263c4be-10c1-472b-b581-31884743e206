Transfer/Removal:
- Correct HTML parsing: cand_25_05776_<PERSON>_Doe_NLG_RG_v_Uber_Technologies_Inc_et_al.json
- Make sure transformer always runs gpt on items and properly cased for html only.
- 
- 
- Make sure html_only items attorney -> attorneys_gpt.
- Make sure transfers inherit mdl_num reports/transform
- Make sure pacer doesn't upload transfer queue!!
- MAKE SURE pacer always adds html_only flag if no docket downloaded. Make sure transformer doesn't remov eit.


customer-MOBILE_USERNAME-cc-us-sessid-SESSIONID-sesstime-10:<EMAIL>:7777
customer-RESIDENTIAL_USERNAME-cc-us-sessid-SESSIONID-sesstime-10:<EMAIL>:7777

# 1464116188369690 change from MLK Group -> Justice HQ
# 470169939516508 

                                                                                                                                                                                       │
│ Here is Claude's plan:                                                                                                                                                                  │
│ ╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮ │
│ │ Analysis of page_name Field Handling                                                                                                                                                │ │
│ │                                                                                                                                                                                     │ │
│ │ I've analyzed how the page_name field is handled in the law firms repository and found several issues:                                                                              │ │
│ │                                                                                                                                                                                     │ │
│ │ Current Issues:                                                                                                                                                                     │ │
│ │                                                                                                                                                                                     │ │
│ │ 1. Missing page_name in Field Mappings: The law_firms_repository.py doesn't include page_name in its field mappings. It only has:                                                   │ │
│ │   - id → ID                                                                                                                                                                         │ │
│ │   - name → Name                                                                                                                                                                     │ │
│ │   - ad_archive_last_updated → AdArchiveLastUpdated                                                                                                                                  │ │
│ │ 2. Interactive Service Doesn't Set page_name: When adding a law firm via interactive_service.py, it creates a record with these fields:                                             │ │
│ │   - ID                                                                                                                                                                              │ │
│ │   - Name                                                                                                                                                                            │ │
│ │   - category                                                                                                                                                                        │ │
│ │   - imageURI                                                                                                                                                                        │ │
│ │   - AdArchiveLastUpdated                                                                                                                                                            │ │
│ │   - NumAds                                                                                                                                                                          │ │
│ │                                                                                                                                                                                     │ │
│ │ But NO page_name field is set!                                                                                                                                                      │ │
│ │ 3. Case Conversion: The @convert_case_on_write decorator will automatically convert page_name to PageName using the default algorithm since it's not in the field mappings.         │ │
│ │                                                                                                                                                                                     │ │
│ │ Fix Plan:                                                                                                                                                                           │ │
│ │                                                                                                                                                                                     │ │
│ │ 1. Update law_firms_repository.py field mappings:                                                                                                                                   │ │
│ │   - Add 'page_name': 'PageName' to the _snake_to_pascal mappings                                                                                                                    │ │
│ │   - Add 'PageName': 'page_name' to the _pascal_to_snake mappings                                                                                                                    │ │
│ │ 2. Update interactive_service.py:                                                                                                                                                   │ │
│ │   - Add 'page_name': firm_data.get('name') to the db_record when creating/updating firms                                                                                            │ │
│ │   - This will use the Facebook page name as the default page_name                                                                                                                   │ │
│ │ 3. Update any other services that create law firm records to ensure they include the page_name field.                                                                               │ │
│ │                                                                                                                                                                                     │ │
│ │ This will ensure:                                                                                                                                                                   │ │
│ │ - The page_name field is properly stored in DynamoDB as PageName                                                                                                                    │ │
│ │ - It's returned as page_name when queried (snake_case)                                                                                                                              │ │
│ │ - The interactive service populates it when adding new firms                                                                                                                        │ │
│ │ - The camoufox session manager can retrieve it for searches                                                                                                                         │ │
│ │                                                                                                                                                                                     │ │
│ │ Would you like me to implement these fixes?                                                                                                                                         │ │
│ ╰─────────────────────────────────────────────────────────────────

 2:25-cv-13525

2:25-cv-13361
