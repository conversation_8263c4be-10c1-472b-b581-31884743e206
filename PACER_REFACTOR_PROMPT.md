# PACER Service Refactoring Guide

## Objective
Refactor the PACER services in the `refactor/containers-consolidated-final` branch to follow the same design patterns and architectural principles used in the transformer services. This is a complete changeover with no backward compatibility requirements.

## Current State Analysis
The PACER services currently have:
- Multiple service files with mixed responsibilities
- A basic container structure (`src/containers/pacer.py`)
- Component folders (`_components/`) with some modularization
- Various facade services but lacking consistent structure

## Design Patterns Reference

# Design Patterns Analysis

This document analyzes the design patterns used across the transformer services, container, and orchestration components in the codebase.

## Overview

The codebase demonstrates a sophisticated architecture that employs multiple design patterns to achieve modularity, maintainability, and scalability. The patterns are primarily concentrated in three main areas:

1. **Transformer Services** (`src/services/transformer/`)
2. **Dependency Injection Container** (`src/containers/transformer.py`)
3. **Processing Orchestration** (`src/services/orchestration/processing_orchestrator.py`)

## Core Design Patterns

### 1. Dependency Injection (DI) Pattern

**Location**: `src/containers/transformer.py`

**Implementation**: Uses the `dependency-injector` library to implement a comprehensive DI container.

**Key Features**:

- **Declarative Container**: `TransformerContainer` extends `containers.DeclarativeContainer`
- **Provider Types**: Singleton, Factory, and Configuration providers
- **Dependency Resolution**: Automatic dependency graph resolution
- **Lifecycle Management**: Singleton services for shared resources, Factory for per-request instances

**Example**:

```python
class TransformerContainer(containers.DeclarativeContainer):
    config = providers.Configuration()
    logger = providers.Dependency()
    
    deepseek_service = providers.Singleton(
        DeepSeekService,
        logger=logger,
        client=deepseek_client,
        prompt_manager=prompt_manager,
        config=config,
    )
```

### 2. Builder Pattern

**Location**: `src/services/transformer/builders.py`

**Implementation**: `DataTransformerBuilder` class for complex object construction.

**Key Features**:

- **Fluent Interface**: Method chaining for configuration
- **Step-by-step Construction**: Gradual building of complex DataTransformer instances
- **Validation**: Built-in validation during construction process
- **Flexibility**: Supports different configuration combinations

**Example**:

```python
DataTransformerBuilder()
    .with_all_dependencies(
        config=config,
        logger=logger,
        # ... other dependencies
    )
    .build()
```

### 3. Factory Pattern

**Location**: `src/services/transformer/component_factory.py`

**Implementation**: `ComponentFactory` for creating transformer components.

**Key Features**:

- **Abstract Factory**: Creates families of related objects
- **Component Creation**: Standardized creation of transformer components
- **Configuration-driven**: Uses configuration to determine component types
- **Registry Integration**: Works with component registries

### 4. Registry Pattern

**Location**: Multiple registry files throughout transformer services

**Implementation**: Various registry classes for managing components and actions.

**Key Registry Types**:

- `ActionRegistry`: Manages available actions and their mappings
- `ComponentFactoryRegistry`: Registers and retrieves component factories
- `DataTransformerRegistry`: Manages transformer instances
- `ErrorHandlerRegistry`: Handles error management strategies
- `SpecializedWorkflowsRegistry`: Manages workflow implementations

**Example**:

```python
class ActionRegistry:
    def register_action(self, action_name: str, action_class: type):
        # Registration logic
    
    def get_action(self, action_name: str):
        # Retrieval logic
```

### 5. Facade Pattern

**Location**: Service facade classes in transformer services

**Implementation**: High-level service interfaces that hide complex subsystem interactions.

**Key Facade Services**:

- `DocketProcessingService`: Unified interface for docket operations
- `MdlProcessingService`: Unified interface for MDL operations
- `FileProcessingService`: Unified interface for file operations
- `LawFirmProcessingService`: Unified interface for law firm operations
- `CaseClassificationService`: Unified interface for case classification
- `DataCleaningService`: Unified interface for data cleaning
- `DataUploadService`: Unified interface for data upload

**Example**:

```python
class DocketProcessingService(AsyncServiceBase):
    def __init__(self, docket_processor, html_processor, text_handler, validator):
        # Aggregates multiple specialized components
        
    async def process_docket(self, docket_data, json_path):
        # Orchestrates complex workflow using multiple components
```

### 6. Template Method Pattern

**Location**: `src/infrastructure/patterns/component_base.py`

**Implementation**: `ComponentImplementation` and `AsyncServiceBase` base classes.

**Key Features**:

- **Abstract Template**: Defines algorithm structure in base class
- **Hook Methods**: Subclasses implement specific steps
- **Standardized Error Handling**: Consistent error handling across all components
- **Logging Integration**: Standardized logging patterns

**Example**:

```python
class ComponentImplementation(ABC):
    async def perform_action(self, data: Any) -> Any:
        # Template method with standardized error handling
        try:
            result = await self._execute_action(data)  # Hook method
            return result
        except Exception as e:
            # Standardized error handling
            
    @abstractmethod
    async def _execute_action(self, data: Any) -> Any:
        # Hook method to be implemented by subclasses
```

### 7. Strategy Pattern

**Location**: Various component implementations

**Implementation**: Interchangeable algorithms for specific operations.

**Key Strategy Areas**:

- **Text Processing**: Different strategies for PDF text extraction
- **Data Cleaning**: Multiple cleaning strategies for different data types
- **Validation**: Different validation strategies for various data formats
- **Upload Strategies**: Different upload mechanisms (S3, local, etc.)

### 8. Observer Pattern

**Location**: Error handling and logging systems

**Implementation**: Event-driven error handling and logging.

**Key Features**:

- **Error Observers**: Components that react to error events
- **Logging Observers**: Centralized logging that observes component activities
- **Status Monitoring**: Components that monitor processing status

### 9. Command Pattern

**Location**: Job processing system

**Implementation**: `TransformationJob` encapsulates processing requests.

**Key Features**:

- **Job Encapsulation**: Each job contains all necessary information for execution
- **Queuing Support**: Jobs can be queued and executed asynchronously
- **Undo/Redo Capability**: Job state tracking enables rollback operations
- **Batch Processing**: Multiple commands can be processed together

**Example**:

```python
@dataclass
class TransformationJob:
    json_path: str
    force_reprocess: bool
    transformer: Any
    status: str = "pending"
    # Encapsulates all data needed for execution
```

### 10. Orchestrator Pattern

**Location**: `src/services/orchestration/processing_orchestrator.py`

**Implementation**: `ProcessingOrchestrator` coordinates complex workflows.

**Key Features**:

- **Workflow Coordination**: Manages complex multi-step processes
- **Service Integration**: Coordinates multiple services and components
- **Error Recovery**: Handles failures and implements recovery strategies
- **Resource Management**: Manages resources across the workflow

**Example**:

```python
class ProcessingOrchestrator(AsyncServiceBase):
    async def execute(self) -> Optional[List[Dict]]:
        # Orchestrates complex workflow
        # 1. Configuration validation
        # 2. Service coordination
        # 3. Error handling
        # 4. Resource cleanup
```

### 11. Async Context Manager Pattern

**Location**: Throughout async services

**Implementation**: Services implement `__aenter__` and `__aexit__` for resource management.

**Key Features**:

- **Resource Lifecycle**: Automatic resource acquisition and cleanup
- **Exception Safety**: Guaranteed cleanup even on exceptions
- **Async Support**: Proper async resource management

**Example**:

```python
async def __aenter__(self):
    self.log_info("Service entering async context")
    return self

async def __aexit__(self, exc_type, exc_val, exc_tb):
    # Cleanup operations
    await self.cleanup()
```

### 12. Component-Based Architecture Pattern

**Location**: Transformer component organization

**Implementation**: Modular components organized by functionality.

**Component Categories**:

- **Docket Components**: `_docket_components/`
- **MDL Components**: `_mdl_components/`
- **File Components**: `_file_components/`
- **Law Firm Components**: `_law_firm_components/`
- **Case Classification Components**: `_case_classification_components/`
- **Data Cleaning Components**: `_data_cleaning_components/`
- **Data Upload Components**: `_data_upload_components/`

### 13. Resource Pool Pattern

**Location**: `src/services/transformer/resource_pools.py`

**Implementation**: Manages pools of reusable resources.

**Key Features**:

- **Resource Reuse**: Efficient reuse of expensive resources
- **Lifecycle Management**: Automatic resource creation and destruction
- **Concurrency Control**: Thread-safe resource access

### 14. Adapter Pattern

**Location**: Various integration points

**Implementation**: Adapters for integrating different service interfaces.

**Key Adapters**:

- **Storage Adapters**: Adapt different storage backends
- **AI Service Adapters**: Adapt different AI service APIs
- **Database Adapters**: Adapt different database interfaces

## Pattern Interactions

### Dependency Injection + Factory Pattern

The DI container uses factories to create complex objects with proper dependency injection, creating a powerful combination for object creation and lifecycle management.

### Facade + Template Method

Service facades use template methods to provide consistent interfaces while allowing for specialized implementations in subclasses.

### Registry + Strategy

Registries store different strategy implementations, allowing for runtime selection of algorithms based on configuration or context.

### Command + Observer

Jobs (commands) emit events that are observed by monitoring and logging systems, providing comprehensive tracking of processing activities.

### Orchestrator + Facade

Orchestrators coordinate multiple facade services to implement complex business workflows while maintaining clean separation of concerns.

## Benefits of This Architecture

1. **Modularity**: Clear separation of concerns through component-based design
2. **Testability**: Dependency injection enables easy mocking and testing
3. **Maintainability**: Consistent patterns make code easier to understand and modify
4. **Scalability**: Async patterns and resource pooling support high-throughput processing
5. **Flexibility**: Strategy and factory patterns allow for easy extension and customization
6. **Reliability**: Template methods and error handling patterns ensure consistent behavior
7. **Observability**: Observer patterns and standardized logging provide comprehensive monitoring

## Conclusion

The codebase demonstrates a mature, enterprise-grade architecture that effectively combines multiple design patterns to create a robust, scalable, and maintainable system. The patterns work together synergistically to provide a comprehensive solution for complex data transformation workflows while maintaining clean code principles and separation of concerns.

## Target Architecture (Following Transformer Pattern)

### 1. Component-Based Architecture
Create focused component modules under service-specific component directories:

```
src/services/pacer/
├── _authentication_components/
│   ├── login_handler.py
│   ├── session_manager.py
│   └── credential_validator.py
├── _navigation_components/
│   ├── page_navigator.py
│   ├── url_builder.py
│   └── element_locator.py
├── _case_processing_components/
│   ├── case_parser.py
│   ├── case_validator.py
│   ├── case_enricher.py
│   └── case_transformer.py
├── _download_components/
│   ├── download_manager.py
│   ├── file_downloader.py
│   └── download_validator.py
├── _query_components/
│   ├── query_builder.py
│   ├── query_executor.py
│   └── result_parser.py
└── _report_components/
    ├── report_generator.py
    ├── report_formatter.py
    └── report_exporter.py
```

### 2. Facade Service Pattern
Create unified facade services that orchestrate component interactions:

```python
# Example: authentication_facade_service.py
class AuthenticationFacadeService(AsyncServiceBase):
    """Unified interface for PACER authentication operations."""
    
    def __init__(self,
                 login_handler: LoginHandler,
                 session_manager: SessionManager,
                 credential_validator: CredentialValidator,
                 config: Optional[Dict] = None,
                 logger: Optional[Any] = None):
        super().__init__(logger, config)
        self._login_handler = login_handler
        self._session_manager = session_manager
        self._credential_validator = credential_validator
    
    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        # Implementation following transformer pattern
```

### 3. Enhanced Dependency Injection Container
Refactor `src/containers/pacer.py` to follow the transformer container pattern:

```python
class PacerContainer(containers.DeclarativeContainer):
    """Container for PACER services with component-based DI."""
    
    # Component providers (Singletons for stateless components)
    login_handler = providers.Singleton(
        LoginHandler,
        logger=logger,
        config=config
    )
    
    session_manager = providers.Singleton(
        SessionManager,
        logger=logger,
        config=config,
        storage=storage_container.session_storage
    )
    
    # Facade services (Factories for request-scoped instances)
    authentication_facade = providers.Factory(
        AuthenticationFacadeService,
        login_handler=login_handler,
        session_manager=session_manager,
        credential_validator=credential_validator,
        logger=logger,
        config=config
    )
```

### 4. Registry Pattern Implementation
Create registries for dynamic component management:

```python
# Example: query_registry.py
class QueryRegistry:
    """Registry for managing query components and strategies."""
    
    def register_query_builder(self, court_type: str, builder: QueryBuilder):
        """Register court-specific query builders."""
        
    def get_query_builder(self, court_type: str) -> QueryBuilder:
        """Retrieve appropriate query builder."""
```

### 5. Builder Pattern for Complex Services
Implement builders for services with complex initialization:

```python
# Example: pacer_service_builder.py
class PacerServiceBuilder:
    """Builder for constructing PACER service instances."""
    
    def with_authentication(self, auth_service: AuthenticationFacadeService):
        """Add authentication capability."""
        
    def with_navigation(self, nav_service: NavigationFacadeService):
        """Add navigation capability."""
        
    def build(self) -> PacerOrchestrationService:
        """Build the complete service."""
```

## Refactoring Steps

### Step 1: Create Component Structure
1. Analyze each existing service file
2. Extract focused components into `_*_components/` directories
3. Ensure each component has a single responsibility
4. Follow the `ComponentImplementation` base class pattern

### Step 2: Implement Facade Services
1. Create facade services for each major domain:
   - `AuthenticationFacadeService`
   - `NavigationFacadeService`
   - `CaseProcessingFacadeService`
   - `DownloadOrchestrationFacadeService`
   - `QueryFacadeService`
   - `ReportingFacadeService`
2. Each facade should aggregate related components
3. Implement `_execute_action` method for action routing

### Step 3: Refactor Container
1. Update `PacerContainer` to use component-based providers
2. Use Singleton providers for stateless components
3. Use Factory providers for stateful services
4. Wire component dependencies properly

### Step 4: Implement Registries
1. Create registries for dynamic behavior:
   - `CourtRegistry` - court-specific configurations
   - `QueryStrategyRegistry` - query building strategies
   - `ParserRegistry` - HTML/data parsing strategies
2. Integrate registries with facade services

### Step 5: Add Builder Support
1. Create `PacerServiceBuilder` for complex service construction
2. Implement fluent interface for configuration
3. Add validation during build process

### Step 6: Migrate Existing Logic
1. Move logic from existing services to appropriate components
2. Update facade services to orchestrate component interactions
3. Ensure backward compatibility during migration

## Key Patterns to Implement

### 1. Template Method Pattern
Use `AsyncServiceBase` as the template with `_execute_action` as the hook method.

### 2. Strategy Pattern
Implement different strategies for:
- Query building (different court types)
- HTML parsing (different page structures)
- Download handling (different file types)

### 3. Observer Pattern
Add event-driven patterns for:
- Download progress monitoring
- Authentication state changes
- Error reporting

### 4. Command Pattern
Encapsulate PACER operations as command objects:
- `DownloadCommand`
- `QueryCommand`
- `LoginCommand`

## Integration with Existing Infrastructure

### Container Integration
The refactored PACER services must integrate seamlessly with existing containers and orchestrators:

#### 1. Update Existing Containers
Modify containers that depend on PACER services to use the new facade interfaces:

```python
# In src/containers/orchestration.py or wherever PACER services are injected
class OrchestrationContainer(containers.DeclarativeContainer):
    # Instead of individual PACER services, inject facades
    pacer_authentication = providers.Factory(
        pacer_container.authentication_facade
    )
    
    pacer_case_processing = providers.Factory(
        pacer_container.case_processing_facade
    )
    
    # Update orchestrator to use new facades
    pacer_orchestrator = providers.Singleton(
        PacerOrchestratorService,
        authentication_service=pacer_authentication,
        case_processing_service=pacer_case_processing,
        # ... other facade services
    )
```

#### 2. Orchestrator Modifications
Update orchestrators that use PACER services:

```python
# In existing orchestrators
class ProcessingOrchestrator(AsyncServiceBase):
    def __init__(self,
                 # Change from individual services to facades
                 pacer_auth_facade: AuthenticationFacadeService,
                 pacer_case_facade: CaseProcessingFacadeService,
                 pacer_download_facade: DownloadOrchestrationFacadeService,
                 ...):
        # Update initialization
        self._pacer_auth = pacer_auth_facade
        self._pacer_case = pacer_case_facade
        self._pacer_download = pacer_download_facade
```

### 3. Service Discovery Updates
Ensure service discovery mechanisms work with new structure:

```python
# Update service factory if used
class ServiceFactory:
    def get_pacer_service(self, service_type: str):
        # Map to new facade services
        service_map = {
            'authentication': self.container.authentication_facade,
            'case_processing': self.container.case_processing_facade,
            'download': self.container.download_orchestration_facade,
            # ... other mappings
        }
        return service_map.get(service_type)
```

## Migration Checklist

- [ ] Create component directory structure
- [ ] Extract components from existing services
- [ ] Implement facade services
- [ ] Update dependency injection container
- [ ] Create necessary registries
- [ ] Implement builders where needed
- [ ] Migrate existing service logic
- [ ] **Update all containers that inject PACER services**
- [ ] **Modify orchestrators to use facade interfaces**
- [ ] **Update service factories and discovery mechanisms**
- [ ] **Ensure all cross-service dependencies are resolved**
- [ ] Update tests for new structure
- [ ] Document new architecture
- [ ] Performance testing and optimization

## Benefits of This Refactoring

1. **Modularity**: Clear separation of concerns through components
2. **Testability**: Easy to mock dependencies and test in isolation
3. **Maintainability**: Consistent patterns across all services
4. **Scalability**: Easy to add new components and strategies
5. **Flexibility**: Registry and strategy patterns allow runtime configuration
6. **Observability**: Standardized logging and monitoring through base classes

## Example Component Implementation

```python
# _authentication_components/login_handler.py
from src.infrastructure.patterns.component_base import ComponentImplementation

class LoginHandler(ComponentImplementation):
    """Handles PACER login operations."""
    
    async def _execute_action(self, data: Any) -> Any:
        """Execute login-related actions."""
        action = data.get('action')
        
        if action == 'login':
            return await self._perform_login(data['credentials'])
        elif action == 'logout':
            return await self._perform_logout()
        else:
            raise PacerServiceError(f"Unknown action: {action}")
    
    async def _perform_login(self, credentials: Dict) -> Dict:
        """Perform PACER login."""
        # Implementation
```

## Important Considerations

### No Backward Compatibility Required
- This is a complete changeover - no need for gradual migration
- All PACER services will be refactored at once
- Errors will be fixed as they arise during integration
- No feature flags or compatibility layers needed

### Key Integration Points
1. **Orchestration Services**: Coordinate PACER operations with other services
2. **Storage Services**: Used by PACER for S3 and database operations
3. **AI Services**: Used for case classification and data extraction
4. **HTML Services**: Shared HTML parsing capabilities
5. **Repository Services**: Database access for PACER data

### Domain Separation
- **PACER Services**: Handle court document retrieval, authentication, and navigation
- **Transformer Services**: Process and transform already-retrieved data
- These are completely separate domains that do not call each other
- Both may be orchestrated together by higher-level orchestration services

### Performance Considerations
- Component abstraction adds minimal overhead
- Dependency injection improves testability without performance cost
- Registry pattern allows for runtime optimization
- Async patterns ensure high throughput is maintained

## Example Integration Update

Here's how an existing orchestrator might be updated:

```python
# BEFORE: src/services/orchestration/processing_orchestrator.py
class ProcessingOrchestrator:
    def __init__(self, 
                 pacer_case_processing: PacerCaseProcessingService,
                 pacer_html_processing: PacerHTMLProcessingService,
                 pacer_auth: PacerAuthenticationService):
        self.case_processing = pacer_case_processing
        self.html_processing = pacer_html_processing
        self.auth = pacer_auth

# AFTER: Using new facade services
class ProcessingOrchestrator:
    def __init__(self,
                 pacer_case_facade: CaseProcessingFacadeService,
                 pacer_auth_facade: AuthenticationFacadeService):
        # Facades provide all needed functionality
        self.case_processing = pacer_case_facade
        self.auth = pacer_auth_facade
        # HTML processing is now part of case processing facade
```

## Final Notes

This refactoring brings PACER services in line with the mature architecture patterns demonstrated in the transformer services. The result will be a more maintainable, testable, and scalable PACER service layer that integrates seamlessly with the rest of the system.