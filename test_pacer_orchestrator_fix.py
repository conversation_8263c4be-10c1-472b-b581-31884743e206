#!/usr/bin/env python3
"""
Test script to validate the pacer orchestrator service DeepSeek client fix.

This script mimics how the pacer orchestrator service creates and manages
DeepSeek clients to ensure the resource leak is fixed.
"""

import asyncio
import logging
import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

# Enable resource tracking early
from src.lib.utils.enable_resource_tracking import resource_tracker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_pacer_orchestrator_deepseek_lifecycle():
    """Test the DeepSeek client lifecycle as used in pacer orchestrator service."""
    logger.info("Testing pacer orchestrator DeepSeek client lifecycle...")
    
    try:
        # Import the pacer orchestrator service
        from src.pacer.pacer_orchestrator_service import PacerOrchestratorService
        
        # Create a minimal config for testing
        config = {
            "deepseek_api_key": "test-key",
            "DATA_DIR": "/tmp/test",
            "config_name": "test_config",
            "iso_date": "20240101"
        }
        
        # Create the service with logger
        service = PacerOrchestratorService(logger=logger, config=config)
        
        # Test async context manager usage
        async with service:
            logger.info("PacerOrchestratorService initialized with context manager")
            
            # Ensure DeepSeek service is initialized (mimics actual usage)
            await service._ensure_deepseek_service_initialized()
            
            if service.deepseek_service:
                logger.info("DeepSeek service is available and initialized")
            else:
                logger.info("DeepSeek service is not available (expected for test)")
                
        logger.info("PacerOrchestratorService context exited - resources should be cleaned up")
        
    except Exception as e:
        logger.error(f"Error testing pacer orchestrator service: {e}")


async def main():
    """Main test function."""
    logger.info("Starting pacer orchestrator DeepSeek fix validation...")
    
    # Print initial resource state
    logger.info("=== Initial Resource State ===")
    resource_tracker.print_unclosed_resources()
    
    # Run the test
    await test_pacer_orchestrator_deepseek_lifecycle()
    
    # Print final resource state
    logger.info("=== Final Resource State ===")
    resource_tracker.print_unclosed_resources()
    
    # Do comprehensive cleanup
    from src.lib.utils.resource_tracker import cleanup_all_resources
    await cleanup_all_resources()
    
    logger.info("Pacer orchestrator DeepSeek fix validation completed!")


if __name__ == "__main__":
    asyncio.run(main())