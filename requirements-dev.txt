# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml --extra dev -o requirements-dev.txt
aioboto3==15.0.0
    # via lexgenius (pyproject.toml)
aiobotocore==2.23.0
    # via aioboto3
aiofiles==24.1.0
    # via
    #   lexgenius (pyproject.toml)
    #   aioboto3
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.15
    # via
    #   lexgenius (pyproject.toml)
    #   aiobotocore
    #   litellm
aioitertools==0.12.0
    # via aiobotocore
aiosignal==1.4.0
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anyio==4.10.0
    # via
    #   httpx
    #   openai
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   outcome
    #   referencing
    #   trio
backoff==2.2.1
    # via lexgenius (pyproject.toml)
beautifulsoup4==4.13.4
    # via lexgenius (pyproject.toml)
black==25.1.0
    # via lexgenius (pyproject.toml)
boto3==1.38.27
    # via
    #   lexgenius (pyproject.toml)
    #   aiobotocore
    #   moto
botocore==1.38.27
    # via
    #   aiobotocore
    #   boto3
    #   moto
    #   s3transfer
certifi==2025.8.3
    # via
    #   httpcore
    #   httpx
    #   requests
    #   selenium
cffi==1.17.1
    # via cryptography
cfgv==3.4.0
    # via pre-commit
charset-normalizer==3.4.3
    # via requests
click==8.2.1
    # via
    #   black
    #   litellm
coverage==7.10.3
    # via pytest-cov
cryptography==45.0.6
    # via moto
css-inline==0.17.0
    # via lexgenius (pyproject.toml)
dependency-injector==4.48.1
    # via lexgenius (pyproject.toml)
distlib==0.4.0
    # via virtualenv
distro==1.9.0
    # via openai
dnspython==2.7.0
    # via email-validator
email-validator==2.2.0
    # via pydantic
execnet==2.1.1
    # via pytest-xdist
filelock==3.18.0
    # via
    #   huggingface-hub
    #   virtualenv
freezegun==1.5.5
    # via lexgenius (pyproject.toml)
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.7.0
    # via huggingface-hub
greenlet==3.2.4
    # via playwright
h11==0.16.0
    # via
    #   httpcore
    #   wsproto
hf-xet==1.1.7
    # via huggingface-hub
holidays==0.78
    # via lexgenius (pyproject.toml)
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via
    #   litellm
    #   openai
huggingface-hub==0.34.4
    # via
    #   lexgenius (pyproject.toml)
    #   tokenizers
identify==2.6.13
    # via pre-commit
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
    #   trio
    #   yarl
imagehash==4.3.2
    # via lexgenius (pyproject.toml)
importlib-metadata==8.7.0
    # via litellm
iniconfig==2.1.0
    # via pytest
jinja2==3.1.6
    # via
    #   litellm
    #   moto
jiter==0.10.0
    # via openai
jmespath==1.0.1
    # via
    #   aiobotocore
    #   boto3
    #   botocore
joblib==1.5.1
    # via scikit-learn
jsonschema==4.25.0
    # via litellm
jsonschema-specifications==2025.4.1
    # via jsonschema
litellm==1.75.5.post1
    # via lexgenius (pyproject.toml)
loguru==0.7.3
    # via lexgenius (pyproject.toml)
markdown-it-py==4.0.0
    # via rich
markupsafe==3.0.2
    # via
    #   jinja2
    #   werkzeug
mdurl==0.1.2
    # via markdown-it-py
moto==5.1.10
    # via lexgenius (pyproject.toml)
multidict==6.6.4
    # via
    #   aiobotocore
    #   aiohttp
    #   yarl
mypy==1.17.1
    # via lexgenius (pyproject.toml)
mypy-extensions==1.1.0
    # via
    #   black
    #   mypy
nodeenv==1.9.1
    # via pre-commit
numpy==2.3.2
    # via
    #   lexgenius (pyproject.toml)
    #   imagehash
    #   pandas
    #   pywavelets
    #   scikit-learn
    #   scipy
openai==1.99.9
    # via
    #   lexgenius (pyproject.toml)
    #   litellm
outcome==1.3.0.post0
    # via
    #   trio
    #   trio-websocket
packaging==25.0
    # via
    #   black
    #   huggingface-hub
    #   pytesseract
    #   pytest
    #   webdriver-manager
pandas==2.3.1
    # via lexgenius (pyproject.toml)
pathspec==0.12.1
    # via
    #   black
    #   mypy
pdf2image==1.17.0
    # via lexgenius (pyproject.toml)
pillow==11.3.0
    # via
    #   imagehash
    #   pdf2image
    #   pytesseract
platformdirs==4.3.8
    # via
    #   black
    #   virtualenv
playwright==1.54.0
    # via lexgenius (pyproject.toml)
pluggy==1.6.0
    # via
    #   pytest
    #   pytest-cov
pre-commit==4.3.0
    # via lexgenius (pyproject.toml)
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
psutil==7.0.0
    # via lexgenius (pyproject.toml)
py-partiql-parser==0.6.1
    # via moto
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via
    #   lexgenius (pyproject.toml)
    #   litellm
    #   openai
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via lexgenius (pyproject.toml)
pyee==13.0.0
    # via playwright
pygments==2.19.2
    # via
    #   pytest
    #   rich
pymupdf==1.26.3
    # via lexgenius (pyproject.toml)
pysocks==1.7.1
    # via urllib3
pytesseract==0.3.13
    # via lexgenius (pyproject.toml)
pytest==8.4.1
    # via
    #   lexgenius (pyproject.toml)
    #   pytest-asyncio
    #   pytest-cov
    #   pytest-mock
    #   pytest-timeout
    #   pytest-xdist
pytest-asyncio==1.1.0
    # via lexgenius (pyproject.toml)
pytest-cov==6.2.1
    # via lexgenius (pyproject.toml)
pytest-mock==3.14.1
    # via lexgenius (pyproject.toml)
pytest-timeout==2.4.0
    # via lexgenius (pyproject.toml)
pytest-xdist==3.8.0
    # via lexgenius (pyproject.toml)
python-dateutil==2.9.0.post0
    # via
    #   aiobotocore
    #   botocore
    #   freezegun
    #   holidays
    #   moto
    #   pandas
python-dotenv==1.1.1
    # via
    #   lexgenius (pyproject.toml)
    #   litellm
    #   pydantic-settings
    #   webdriver-manager
pytz==2025.2
    # via pandas
pywavelets==1.9.0
    # via imagehash
pyyaml==6.0.2
    # via
    #   lexgenius (pyproject.toml)
    #   huggingface-hub
    #   moto
    #   pre-commit
    #   responses
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2025.7.34
    # via tiktoken
requests==2.32.4
    # via
    #   lexgenius (pyproject.toml)
    #   huggingface-hub
    #   moto
    #   responses
    #   tiktoken
    #   webdriver-manager
responses==0.25.8
    # via
    #   lexgenius (pyproject.toml)
    #   moto
rich==14.1.0
    # via lexgenius (pyproject.toml)
rpds-py==0.27.0
    # via
    #   jsonschema
    #   referencing
ruff==0.12.8
    # via lexgenius (pyproject.toml)
s3transfer==0.13.1
    # via boto3
scikit-learn==1.7.1
    # via lexgenius (pyproject.toml)
scipy==1.16.1
    # via
    #   imagehash
    #   scikit-learn
selenium==4.35.0
    # via lexgenius (pyproject.toml)
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   openai
    #   trio
sortedcontainers==2.4.0
    # via trio
soupsieve==2.7
    # via beautifulsoup4
tenacity==9.1.2
    # via lexgenius (pyproject.toml)
threadpoolctl==3.6.0
    # via scikit-learn
tiktoken==0.11.0
    # via
    #   lexgenius (pyproject.toml)
    #   litellm
tokenizers==0.21.4
    # via litellm
tqdm==4.67.1
    # via
    #   lexgenius (pyproject.toml)
    #   huggingface-hub
    #   openai
trio==0.30.0
    # via
    #   selenium
    #   trio-websocket
trio-websocket==0.12.2
    # via selenium
typing-extensions==4.14.1
    # via
    #   aiosignal
    #   anyio
    #   beautifulsoup4
    #   huggingface-hub
    #   mypy
    #   openai
    #   pydantic
    #   pydantic-core
    #   pyee
    #   referencing
    #   selenium
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via pandas
urllib3==2.5.0
    # via
    #   botocore
    #   requests
    #   responses
    #   selenium
virtualenv==20.33.1
    # via pre-commit
webdriver-manager==4.0.2
    # via lexgenius (pyproject.toml)
websocket-client==1.8.0
    # via selenium
werkzeug==3.1.3
    # via moto
wrapt==1.17.3
    # via aiobotocore
wsproto==1.2.0
    # via trio-websocket
xmltodict==0.14.2
    # via moto
yarl==1.20.1
    # via aiohttp
zipp==3.23.0
    # via importlib-metadata
