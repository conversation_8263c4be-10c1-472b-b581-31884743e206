# PACER Detailed Process Flowcharts

This document provides comprehensive flowcharts for the PACER system workflows, including detailed download processes and court/docket lifecycle management.

## 1. Complete Download Process Flow

This flowchart shows the detailed process from clicking a complaint link to finalizing the download, including all three possible paths.

```mermaid
flowchart TD
    A[User Clicks Complaint Link] --> B[Navigate to Case Page]
    B --> C[Case Page Loads with Docket Information]
    C --> D[Scan Page for Complaint Document Link]
    
    D --> E{Complaint Link Found?}
    E -->|No| F[No Documents Available - Save HTML Only]
    E -->|Yes| G[Click Complaint Link]
    
    G --> H{Link Type?}
    H -->|Path 1: Direct PDF| I[Direct PDF Download]
    H -->|Path 2: Document Selection Page| J[Navigate to Document Selection]
    H -->|Path 3: ZIP Archive| K[ZIP Archive Download]
    
    %% Path 1: Direct PDF Download
    I --> I1[PDF Download Triggered]
    I1 --> I2[Wait for Download Complete Event]
    I2 --> I3{Download Success?}
    I3 -->|Yes| I4[Move PDF to Final Location]
    I3 -->|No| I5[Download Failed - Mark as Failed]
    I4 --> SAVE[Save Case with Download Metadata]
    I5 --> SAVE
    
    %% Path 2: Document Selection Page
    J --> J1[Document Selection Page Loads]
    J1 --> J2[Scan for Available Documents]
    J2 --> J3{Documents Available?}
    J3 -->|No| J4[No Documents - Save HTML Only]
    J3 -->|Yes| J5[Select Best Document]
    J5 --> J6[Click Document Download]
    J6 --> J7{Document Type?}
    J7 -->|PDF| J8[PDF Download Process]
    J7 -->|ZIP| J9[ZIP Download Process]
    J8 --> J10[Wait for PDF Download Complete]
    J9 --> J11[Wait for ZIP Download Complete]
    J10 --> J12{PDF Download Success?}
    J11 --> J13{ZIP Download Success?}
    J12 -->|Yes| J14[Move PDF to Final Location]
    J12 -->|No| J15[PDF Download Failed]
    J13 -->|Yes| J16[Move ZIP to Final Location]
    J13 -->|No| J17[ZIP Download Failed]
    J14 --> SAVE
    J15 --> SAVE
    J16 --> SAVE
    J17 --> SAVE
    J4 --> SAVE
    
    %% Path 3: ZIP Archive Download
    K --> K1[ZIP Download Triggered]
    K1 --> K2[Wait for ZIP Download Complete Event]
    K2 --> K3{ZIP Download Success?}
    K3 -->|Yes| K4[Move ZIP to Final Location]
    K3 -->|No| K5[ZIP Download Failed - Mark as Failed]
    K4 --> SAVE
    K5 --> SAVE
    
    %% HTML Only Path
    F --> F1[Extract Case Information from HTML]
    F1 --> F2[Mark as HTML Only]
    F2 --> SAVE
    
    %% Final Processing
    SAVE --> FINAL1[Update Case JSON with Results]
    FINAL1 --> FINAL2{Upload Enabled?}
    FINAL2 -->|Yes| FINAL3[Upload to S3]
    FINAL2 -->|No| FINAL4[Local Storage Only]
    FINAL3 --> END[Processing Complete]
    FINAL4 --> END
    
    %% Styling
    style I fill:#e1f5fe
    style J fill:#fff3e0  
    style K fill:#f3e5f5
    style F fill:#ffebee
    style SAVE fill:#e8f5e8
    style END fill:#e8f5e8
```

## 2. Download Orchestration Service Flow

Detailed workflow of the Download Orchestration Service showing decision points and error handling.

```mermaid
flowchart TD
    A[Case Details Input] --> B[Validate Case for Download]
    B --> C{Has Base Filename?}
    C -->|No| D[Return Skip - No Base Filename]
    C -->|Yes| E[Check Download Eligibility]
    
    E --> F{Is Relevant OR Is Ignore Download?}
    F -->|No| G[Return Skip - Not Eligible]
    F -->|Yes| H[Prepare Download Context]
    
    H --> I[Set Download Parameters]
    I --> J[Create Staging Directory]
    J --> K[Initialize Document Downloader]
    
    K --> L[Execute Download Process]
    L --> M{Download Trigger Successful?}
    
    %% Download trigger detection may fail, but download can still succeed
    M -->|Success Detected| N[Wait for Download Complete Event]
    M -->|Failed Detection| O[Still Wait for Download Event]
    
    N --> P{Download Event Fired?}
    O --> P
    
    P -->|Yes| Q[Download Complete Event Handler]
    P -->|No - Timeout| R[Download Timeout Failure]
    
    Q --> S{File Successfully Downloaded?}
    S -->|Yes| T[Handle Download Success]
    S -->|No| U[Handle Download Failure]
    
    %% Success Path
    T --> T1[Add Success Metadata to Case]
    T1 --> T2[Set download_status = 'success']
    T2 --> T3[Add Downloaded File Paths]
    T3 --> SAVE[Save Case Data via File Operations]
    
    %% Failure Paths  
    U --> U1[Add Failure Metadata to Case]
    U1 --> U2[Set download_status = 'failed']
    U2 --> U3[Add Error Details]
    U3 --> SAVE
    
    R --> R1[Add Timeout Metadata to Case]
    R1 --> R2[Set download_status = 'timeout']
    R2 --> R3[Add Timeout Error Details]
    R3 --> SAVE
    
    %% Skip Paths
    D --> SAVE
    G --> SAVE
    
    %% Final Save Processing
    SAVE --> SAVE1{Local Save Success?}
    SAVE1 -->|Yes| SAVE2{Upload to S3 Enabled?}
    SAVE1 -->|No| SAVE3[Return Save Error]
    
    SAVE2 -->|Yes| SAVE4[Upload to S3]
    SAVE2 -->|No| SAVE5[Return Local Success]
    
    SAVE4 --> SAVE6{S3 Upload Success?}
    SAVE6 -->|Yes| SAVE7[Return Complete Success]
    SAVE6 -->|No| SAVE8[Return Local Success + Upload Error]
    
    %% Return Results
    SAVE3 --> END[Return Download Result]
    SAVE5 --> END
    SAVE7 --> END  
    SAVE8 --> END
    
    %% Critical Fix: Always wait for download event
    %% even if trigger detection fails
    style O fill:#fff2cc
    style P fill:#e8f5e8
    style Q fill:#e8f5e8
```

## 3. Court Processing Lifecycle

Complete lifecycle of processing a single court from start to finish.

```mermaid
flowchart TD
    A[Court Processing Started] --> B[Initialize Court Processing Service]
    B --> C[Setup Download Paths]
    C --> D[Create Browser Context]
    
    D --> E[Navigate to Court Report Page]
    E --> F{Page Load Success?}
    F -->|No| G[Mark Court as Failed - Navigation Error]
    F -->|Yes| H[Extract Case List from Report]
    
    H --> I{Cases Found?}
    I -->|No| J[Complete Court - No Cases]
    I -->|Yes| K[Filter Cases by Configuration]
    
    K --> L[Start Case Processing Loop]
    L --> M[Take Next Case from List]
    M --> N{More Cases?}
    N -->|No| COMPLETE[Court Processing Complete]
    N -->|Yes| O[Navigate to Case Page]
    
    O --> P{Use New Docket Services?}
    P -->|Yes| Q[Create Docket Processing Orchestrator Service]
    P -->|No| R[Create Legacy Docket Processor]
    
    %% New Service Architecture Path
    Q --> Q1[Process Case via Orchestrator]
    Q1 --> Q2[Phase 1: HTML Content Processing]
    Q2 --> Q3{HTML Processing Success?}
    Q3 -->|No| Q4[Case Failed - HTML Processing]
    Q3 -->|Yes| Q5[Phase 2: Relevance & Classification]
    
    Q5 --> Q6[Apply Relevance Service]
    Q6 --> Q7[Apply Classification Service]
    Q7 --> Q8[Phase 3: Case Verification]
    
    Q8 --> Q9{Should Process Case?}
    Q9 -->|No| Q10[Skip Case - Save Metadata Only]
    Q9 -->|Yes| Q11[Phase 4: Download Workflow]
    
    Q11 --> Q12[Execute Download Orchestration]
    Q12 --> Q13{Download Success?}
    Q13 -->|Yes| Q14[Case Complete - Downloaded]
    Q13 -->|No| Q15[Case Complete - Download Failed]
    
    %% Legacy Path
    R --> R1[Process Case via Legacy Processor]
    R1 --> R2{Legacy Processing Success?}
    R2 -->|Yes| R3[Case Complete - Legacy Success]
    R2 -->|No| R4[Case Complete - Legacy Failed]
    
    %% Case Completion Paths
    Q4 --> CASE_DONE[Case Processing Done]
    Q10 --> CASE_DONE
    Q14 --> CASE_DONE
    Q15 --> CASE_DONE
    R3 --> CASE_DONE
    R4 --> CASE_DONE
    
    CASE_DONE --> CASE_LOG[Log Case Result]
    CASE_LOG --> CASE_CLEANUP[Cleanup Case Resources]
    CASE_CLEANUP --> M
    
    %% Court Completion
    J --> COURT_CLEANUP[Cleanup Court Resources]
    COMPLETE --> COURT_CLEANUP
    G --> COURT_CLEANUP
    
    COURT_CLEANUP --> COURT_LOG[Log Court Results]
    COURT_LOG --> CLOSE_BROWSER[Close Browser Context]
    CLOSE_BROWSER --> END[Court Processing End]
    
    %% Error Handling
    subgraph "Error Handling"
        ERR1[Navigation Errors] --> ERR_LOG[Log Error Details]
        ERR2[Processing Errors] --> ERR_LOG
        ERR3[Download Errors] --> ERR_LOG
        ERR_LOG --> ERR_CONTINUE[Continue with Next Case/Court]
    end
    
    %% Styling
    style Q fill:#e1f5fe
    style R fill:#fff3e0
    style COMPLETE fill:#e8f5e8
    style END fill:#e8f5e8
```

## 4. Single Docket Processing Lifecycle

Detailed lifecycle of processing a single docket through all phases.

```mermaid
flowchart TD
    A[Docket Processing Start] --> B[Receive Page + Initial Details]
    B --> C{Use New Docket Services?}
    
    C -->|Yes| D[New Service Architecture Path]
    C -->|No| LEGACY[Legacy DocketProcessor Path]
    
    D --> D1[Initialize All 8 Services]
    D1 --> D2[Configuration Service Ready]
    D2 --> PHASE1[PHASE 1: HTML Content Processing]
    
    PHASE1 --> P1_1[Case Processing Service.process_case_html]
    P1_1 --> P1_2[Wait for Page Content Stability]
    P1_2 --> P1_3[Validate HTML Content Matches Case]
    P1_3 --> P1_4{HTML Valid?}
    P1_4 -->|No| P1_FAIL[Return None - Invalid HTML]
    P1_4 -->|Yes| P1_5[Extract Case Details via HTMLCaseParser]
    P1_5 --> P1_6[Process MDL Flags and Special Cases]
    P1_6 --> P1_7[Check for Notice of Removal]
    P1_7 --> P1_8[Generate Base Filename]
    P1_8 --> P1_9[Add Processing Metadata]
    P1_9 --> PHASE2[PHASE 2: Relevance & Classification]
    
    PHASE2 --> P2_1[Relevance Service.determine_case_relevance]
    P2_1 --> P2_2{Has MDL Flag?}
    P2_2 -->|Yes| P2_3[Set Relevant - MDL Flag Override]
    P2_2 -->|No| P2_4{Matches ignore_download Pattern?}
    P2_4 -->|Yes| P2_5[Set Not Relevant - Ignore Download]
    P2_4 -->|No| P2_6{Excluded by Statute?}
    P2_6 -->|Yes| P2_7[Set Not Relevant - Excluded]
    P2_6 -->|No| P2_8{Explicitly Relevant?}
    P2_8 -->|Yes| P2_9[Set Relevant - Explicit Match]
    P2_8 -->|No| P2_10[Set Not Relevant - Default]
    
    P2_3 --> P2_CLASSIFY[Classification Service.classify_case]
    P2_5 --> P2_CLASSIFY
    P2_7 --> P2_CLASSIFY
    P2_9 --> P2_CLASSIFY
    P2_10 --> P2_CLASSIFY
    
    P2_CLASSIFY --> P2_C1{is_explicitly_requested?}
    P2_C1 -->|Yes| P2_C2[Skip Removal Detection for Reprocessing]
    P2_C1 -->|No| P2_C3[Check Removal by Cause and HTML]
    P2_C2 --> P2_C4[Check Transfer Status]
    P2_C3 --> P2_C4
    P2_C4 --> PHASE3[PHASE 3: Case Verification]
    
    PHASE3 --> P3_1[Verification Service.verify_case]
    P3_1 --> P3_2{is_explicitly_requested?}
    P3_2 -->|Yes| P3_3[Explicitly Requested Verification]
    P3_2 -->|No| P3_4[Report-Scraped Verification]
    
    P3_3 --> P3_5[Check Database GSI]
    P3_5 --> P3_6{Exists in DB?}
    P3_6 -->|Yes| P3_SKIP[Skip - Already in Database]
    P3_6 -->|No| P3_7[Check Local Artifacts Only PDF/ZIP]
    P3_7 --> P3_8{Artifacts Exist?}
    P3_8 -->|Yes| P3_SKIP
    P3_8 -->|No| P3_PROCESS[Proceed to Download Phase]
    
    P3_4 --> P3_9[Check Database GSI]
    P3_9 --> P3_10{Exists in DB?}
    P3_10 -->|Yes| P3_SKIP
    P3_10 -->|No| P3_11[Check All Local Files]
    P3_11 --> P3_12{Any Files Exist?}
    P3_12 -->|Yes| P3_SKIP
    P3_12 -->|No| P3_PROCESS
    
    P3_SKIP --> SAVE_ONLY[Save Metadata Only - Skip Download]
    P3_PROCESS --> PHASE4[PHASE 4: Download Workflow]
    
    PHASE4 --> P4_1[Download Orchestration Service.process_download_workflow]
    P4_1 --> P4_2[Validate Case for Download]
    P4_2 --> P4_3{Should Attempt Download?}
    P4_3 -->|No| P4_SKIP[Skip Download with Reason]
    P4_3 -->|Yes| P4_4[Prepare Download Context]
    P4_4 --> P4_5[Execute Document Download]
    P4_5 --> P4_6{Download Success?}
    P4_6 -->|Yes| P4_SUCCESS[Handle Download Success]
    P4_6 -->|No| P4_FAIL[Handle Download Failure]
    
    P4_SKIP --> P4_SAVE[Add Skip Metadata]
    P4_SUCCESS --> P4_SAVE_SUCCESS[Add Success Metadata]
    P4_FAIL --> P4_SAVE_FAIL[Add Failure Metadata]
    
    P4_SAVE --> FINAL_SAVE[File Operations Service.save_and_upload_case_data]
    P4_SAVE_SUCCESS --> FINAL_SAVE
    P4_SAVE_FAIL --> FINAL_SAVE
    SAVE_ONLY --> FINAL_SAVE
    
    FINAL_SAVE --> FS1[Create Court/Date Directory]
    FS1 --> FS2[Generate Filename]
    FS2 --> FS3[Clean and Prepare Case Data]
    FS3 --> FS4[Save to Local JSON]
    FS4 --> FS5{Upload Enabled?}
    FS5 -->|Yes| FS6[Upload to S3]
    FS5 -->|No| FS7[Local Only]
    FS6 --> FS8{Upload Success?}
    FS8 -->|Yes| SUCCESS[Complete Success]
    FS8 -->|No| PARTIAL[Local Success + Upload Error]
    FS7 --> SUCCESS
    
    LEGACY --> L1[Legacy DocketProcessor.process_docket_page]
    L1 --> L2{Legacy Success?}
    L2 -->|Yes| SUCCESS
    L2 -->|No| FAIL[Processing Failed]
    
    P1_FAIL --> FAIL
    PARTIAL --> END[Return Result to Orchestrator]
    SUCCESS --> END
    FAIL --> END
    
    style PHASE1 fill:#e3f2fd
    style PHASE2 fill:#fff3e0
    style PHASE3 fill:#f3e5f5
    style PHASE4 fill:#e8f5e8
    style SUCCESS fill:#c8e6c9
    style FAIL fill:#ffcdd2
```

## 5. Browser Context Management Flow

Shows how browser contexts are managed, especially for parallel processing.

```mermaid
flowchart TD
    A[Court Processing Request] --> B{run_parallel enabled?}
    B -->|No| C[Sequential Processing]
    B -->|Yes| D[Parallel Processing Setup]
    
    %% Sequential Processing
    C --> C1[Create Single Browser Context]
    C1 --> C2[Set Download Path for Court]
    C2 --> C3[Process Courts One by One]
    C3 --> C4[Use Same Context for All Courts]
    C4 --> C5[Close Context When All Done]
    C5 --> ENDSEQ[Processing Complete]
    
    %% Parallel Processing Setup
    D --> D1[Get List of Courts to Process]
    D1 --> D2[Create Template Context Options]
    D2 --> D3[For Each Court: Create Isolated Context]
    
    D3 --> D4[Court A: Create Isolated Context]
    D3 --> D5[Court B: Create Isolated Context]
    D3 --> D6[Court C: Create Isolated Context]
    
    %% Isolated Context Creation detailed for Court A
    D4 --> D4_1[Generate Unique Download Path]
    D4_1 --> D4_2["Path: data/YYYYMMDD/dockets/temp/cand_ctx_dl_report/attempt_1_uuid"]
    D4_2 --> D4_3[Set Context Options with Download Path]
    D4_3 --> D4_4[Create New Browser Context]
    D4_4 --> D4_5[Process Court A Cases]
    
    %% Similar for other courts simplified
    D5 --> D5_1[Unique Path for Court B]
    D5_1 --> D5_2[Process Court B Cases]
    
    D6 --> D6_1[Unique Path for Court C]
    D6_1 --> D6_2[Process Court C Cases]
    
    %% Parallel Execution
    D4_5 --> PARALLEL{All Courts Processing in Parallel}
    D5_2 --> PARALLEL
    D6_2 --> PARALLEL
    
    PARALLEL --> AWAIT["await asyncio.gather(*court_tasks)"]
    AWAIT --> CLEANUP[Cleanup All Contexts]
    
    %% Context Cleanup
    CLEANUP --> CLEANUP1[Close Court A Context]
    CLEANUP --> CLEANUP2[Close Court B Context]
    CLEANUP --> CLEANUP3[Close Court C Context]
    
    CLEANUP1 --> VERIFY_CLEANUP[Verify All Contexts Closed]
    CLEANUP2 --> VERIFY_CLEANUP
    CLEANUP3 --> VERIFY_CLEANUP
    
    VERIFY_CLEANUP --> ENDPARALLEL[Processing Complete]
    
    %% Error Handling for Context Management
    subgraph EH["Error Handling"]
        E1[Context Creation Error] --> E2[Log Error]
        E2 --> E3[Skip Court]
        E4[Context Cleanup Error] --> E5[Log Warning]
        E5 --> E6[Continue with Other Contexts]
    end
    
    %% Download Path Collision Avoidance
    subgraph DPM["Download Path Management"]
        P1["Base Path: data/YYYYMMDD/dockets/temp/"]
        P2["Court Prefix: {court_id}_ctx_dl_report/"]
        P3["Unique Suffix: attempt_1_{uuid}/"]
        P4["Final Path: {base}/{court_prefix}{unique_suffix}"]
        P1 --> P2
        P2 --> P3
        P3 --> P4
    end
    
    style D fill:#e1f5fe
    style PARALLEL fill:#fff3e0
    style CLEANUP fill:#f3e5f5
    style ENDSEQ fill:#e8f5e8
    style ENDPARALLEL fill:#e8f5e8
```

## 6. Error Recovery and Retry Flow

Shows how the system handles errors and implements retry logic.

```mermaid
flowchart TD
    A[Process Operation] --> B{Operation Success?}
    B -->|Success| SUCCESS[Continue Processing]
    B -->|Error| C[Determine Error Type]
    
    C --> D{Error Category?}
    D -->|Navigation Error| E[Browser/Page Error]
    D -->|Download Error| F[Download/File Error]
    D -->|Service Error| G[Service Processing Error]
    D -->|Configuration Error| H[Config/Setup Error]
    
    %% Browser/Page Errors
    E --> E1{Retry Possible?}
    E1 -->|Yes| E2[Retry Navigation]
    E1 -->|No| E3[Skip Case/Court]
    E2 --> E4{Retry Success?}
    E4 -->|Yes| SUCCESS
    E4 -->|No| E5{Max Retries Reached?}
    E5 -->|Yes| E3
    E5 -->|No| E2
    E3 --> LOG_SKIP[Log Skip Reason]
    
    %% Download Errors
    F --> F1{Download Retry Possible?}
    F1 -->|Yes| F2[Retry Download]
    F1 -->|No| F3[Mark Download Failed]
    F2 --> F4{Retry Success?}
    F4 -->|Yes| SUCCESS
    F4 -->|No| F5{Max Download Retries?}
    F5 -->|Yes| F3
    F5 -->|No| F2
    F3 --> F6[Save Case with Failure Metadata]
    F6 --> LOG_FAIL[Log Download Failure]
    
    %% Service Processing Errors
    G --> G1{Service Error Severity?}
    G1 -->|Recoverable| G2[Apply Service Fallback]
    G1 -->|Critical| G3[Skip Service Operation]
    G2 --> G4{Fallback Success?}
    G4 -->|Yes| SUCCESS
    G4 -->|No| G5[Use Default/Empty Result]
    G3 --> G5
    G5 --> G6[Continue with Degraded Function]
    G6 --> LOG_DEGRADE[Log Service Degradation]
    
    %% Configuration Errors
    H --> H1{Config Error Type?}
    H1 -->|Missing Critical Config| H2["Exit Process - sys.exit(1)"]
    H1 -->|Missing Optional Config| H3[Use Defaults]
    H1 -->|Invalid Config Format| H4[Log Error + Use Defaults]
    H3 --> SUCCESS
    H4 --> SUCCESS
    H2 --> TERMINATE[Process Termination]
    
    %% Logging and Monitoring
    LOG_SKIP --> MONITOR[Update Monitoring Metrics]
    LOG_FAIL --> MONITOR
    LOG_DEGRADE --> MONITOR
    MONITOR --> NOTIFY{Should Notify?}
    NOTIFY -->|Yes| ALERT[Send Alert/Notification]
    NOTIFY -->|No| CONTINUE[Continue Processing]
    ALERT --> CONTINUE
    
    %% Recovery Strategies
    subgraph RS["Recovery Strategies"]
        R1[Retry with Exponential Backoff]
        R2[Fallback to Alternative Method]
        R3[Graceful Degradation]
        R4[Skip and Continue]
        R5[Fail Fast for Critical Errors]
    end
    
    CONTINUE --> END[Error Handling Complete]
    SUCCESS --> END
    TERMINATE --> STOP[Process Stopped]
    
    style SUCCESS fill:#c8e6c9
    style TERMINATE fill:#ffcdd2
    style MONITOR fill:#fff3e0
    style END fill:#e8f5e8
```

## 7. Multiple Courts Workflow

Shows how the system processes multiple courts in a single run.

```mermaid
flowchart TD
    A[Start Multiple Courts Processing] --> B[Load Court Configuration]
    B --> C{process_single_court specified?}
    C -->|Yes| D[Use Specified Court List]
    C -->|No| E[Load All Available Courts]
    
    D --> F[Filter Court List]
    E --> F
    F --> G[Apply skip_courts Filter]
    G --> H[Apply start_at/after_court Logic]
    H --> I{run_parallel enabled?}
    
    I -->|Yes| PARALLEL[Parallel Processing Path]
    I -->|No| SEQUENTIAL[Sequential Processing Path]
    
    %% Sequential Processing
    SEQUENTIAL --> S1[Create Single Browser Context]
    S1 --> S2[Initialize Court Iterator]
    S2 --> S3[Process Next Court]
    S3 --> S4{More Courts?}
    S4 -->|Yes| S5[Navigate to Next Court]
    S4 -->|No| S6[Sequential Complete]
    S5 --> S7[Process Court Cases]
    S7 --> S8[Log Court Results]
    S8 --> S3
    S6 --> CLEANUP[Cleanup Resources]
    
    %% Parallel Processing
    PARALLEL --> P1[Create Template Context Options]
    P1 --> P2[Generate Isolated Contexts for Each Court]
    P2 --> P3["Court A: Isolated Context + Unique Download Path"]
    P2 --> P4["Court B: Isolated Context + Unique Download Path"]
    P2 --> P5["Court C: Isolated Context + Unique Download Path"]
    
    P3 --> P6[Process Court A Cases]
    P4 --> P7[Process Court B Cases]
    P5 --> P8[Process Court C Cases]
    
    P6 --> GATHER["await asyncio.gather all court tasks"]
    P7 --> GATHER
    P8 --> GATHER
    
    GATHER --> P9[Collect All Court Results]
    P9 --> P10[Close All Browser Contexts]
    P10 --> CLEANUP
    
    CLEANUP --> FINAL[Generate Summary Report]
    FINAL --> END[Multiple Courts Complete]
    
    %% Error Handling
    subgraph EH7["Error Handling"]
        ERR1[Court Navigation Failed] --> ERR2[Skip Court]
        ERR3[Context Creation Failed] --> ERR4[Log Error & Continue]
        ERR5[Parallel Processing Error] --> ERR6[Graceful Degradation]
    end
    
    style PARALLEL fill:#e1f5fe
    style SEQUENTIAL fill:#fff3e0
    style GATHER fill:#f3e5f5
    style END fill:#e8f5e8
```

## 8. Single Court Workflow

Detailed workflow for processing a single court.

```mermaid
flowchart TD
    A[Single Court Processing Start] --> B[Initialize Court Processing Service]
    B --> C[Setup Court-Specific Download Paths]
    C --> D[Create Browser Context for Court]
    
    D --> E[Navigate to Court Report Page]
    E --> F{Page Load Success?}
    F -->|No| G[Court Failed - Navigation Error]
    F -->|Yes| H[Extract Case List from Court Report]
    
    H --> I{Cases Found on Report?}
    I -->|No| J[Court Complete - No Cases Available]
    I -->|Yes| K[Apply Case Filtering]
    
    K --> L[Filter by date_filter]
    L --> M[Filter by explicit case requests]
    M --> N[Filter by court-specific rules]
    N --> O[Start Case Processing Loop]
    
    O --> P[Select Next Case]
    P --> Q{More Cases to Process?}
    Q -->|No| COURT_COMPLETE[All Cases Processed]
    Q -->|Yes| R[Navigate to Case Detail Page]
    
    R --> S{Case Page Load Success?}
    S -->|No| T[Case Failed - Navigation Error]
    S -->|Yes| U[Process Case via Docket Processor]
    
    U --> V{Use New Docket Services?}
    V -->|Yes| W[New Service Architecture]
    V -->|No| X[Legacy DocketProcessor]
    
    W --> W1[8-Service Orchestrator Processing]
    W1 --> CASE_RESULT[Case Processing Complete]
    
    X --> X1[Legacy Monolithic Processing]
    X1 --> CASE_RESULT
    
    T --> CASE_RESULT
    CASE_RESULT --> Y[Log Case Result]
    Y --> Z[Update Court Statistics]
    Z --> P
    
    COURT_COMPLETE --> AA[Generate Court Summary]
    AA --> BB[Log Court Statistics]
    BB --> CC[Cleanup Court Resources]
    CC --> DD[Close Browser Context]
    DD --> END[Single Court Complete]
    
    G --> DD
    J --> DD
    
    %% Case Processing Details
    subgraph CPT["Case Processing Types"]
        CP1[Normal Case Processing]
        CP2[HTML Only Processing]
        CP3[Failed Download Retry]
        CP4[Explicitly Requested Reprocessing]
    end
    
    style W fill:#e1f5fe
    style X fill:#fff3e0
    style END fill:#e8f5e8
```

## 9. Parallel Processing Workflows

Shows the different parallel processing scenarios and their coordination.

```mermaid
flowchart TD
    A[Parallel Processing Trigger] --> B{Parallel Processing Type?}
    B -->|Multiple Courts| C[Court-Level Parallelization]
    B -->|Single Court Multi-Cases| D[Case-Level Parallelization]
    B -->|Mixed Mode| E[Hybrid Parallelization]
    
    %% Court-Level Parallelization
    C --> C1[Create Isolated Browser Contexts]
    C1 --> C2["Court A: cand_ctx_dl_report/attempt_1_uuid1"]
    C1 --> C3["Court B: ilnd_ctx_dl_report/attempt_1_uuid2"]
    C1 --> C4["Court C: njd_ctx_dl_report/attempt_1_uuid3"]
    
    C2 --> C5[Process CAND Cases Sequentially]
    C3 --> C6[Process ILND Cases Sequentially]
    C4 --> C7[Process NJD Cases Sequentially]
    
    C5 --> COURT_SYNC[Court-Level Synchronization]
    C6 --> COURT_SYNC
    C7 --> COURT_SYNC
    
    %% Case-Level Parallelization (within single court)
    D --> D1[Single Court Context]
    D1 --> D2[Divide Case List into Batches]
    D2 --> D3["Batch 1: Cases 1-10"]
    D2 --> D4["Batch 2: Cases 11-20"]
    D2 --> D5["Batch 3: Cases 21-30"]
    
    D3 --> D6[Process Batch 1 Cases]
    D4 --> D7[Process Batch 2 Cases]
    D5 --> D8[Process Batch 3 Cases]
    
    D6 --> CASE_SYNC[Case-Level Synchronization]
    D7 --> CASE_SYNC
    D8 --> CASE_SYNC
    
    %% Hybrid Parallelization
    E --> E1[Multiple Courts + Case Batching]
    E1 --> E2["Court A: Process Cases in Parallel Batches"]
    E1 --> E3["Court B: Process Cases in Parallel Batches"]
    E1 --> E4["Court C: Process Cases in Parallel Batches"]
    
    E2 --> HYBRID_SYNC[Hybrid Synchronization]
    E3 --> HYBRID_SYNC
    E4 --> HYBRID_SYNC
    
    %% Synchronization and Cleanup
    COURT_SYNC --> SYNC1[Collect Court Results]
    CASE_SYNC --> SYNC2[Collect Case Results]
    HYBRID_SYNC --> SYNC3[Collect All Results]
    
    SYNC1 --> CLEANUP[Resource Cleanup]
    SYNC2 --> CLEANUP
    SYNC3 --> CLEANUP
    
    CLEANUP --> CL1[Close All Browser Contexts]
    CL1 --> CL2[Cleanup Download Directories]
    CL2 --> CL3[Aggregate Processing Statistics]
    CL3 --> END[Parallel Processing Complete]
    
    %% Download Path Management
    subgraph DPI["Download Path Isolation"]
        DP1["Base: data/YYYYMMDD/dockets/temp"]
        DP2["Court Isolation: court_id_ctx_dl_report"]
        DP3["Attempt Isolation: attempt_1_uuid"]
        DP4["Case Isolation: case_specific_staging"]
    end
    
    %% Error Handling
    subgraph PEH["Parallel Error Handling"]
        PE1[Context Creation Failure] --> PE2[Skip Failed Court/Batch]
        PE3[Processing Deadlock] --> PE4[Timeout and Restart]
        PE5[Resource Exhaustion] --> PE6[Graceful Degradation]
    end
    
    style C fill:#e1f5fe
    style D fill:#fff3e0
    style E fill:#f3e5f5
    style END fill:#e8f5e8
```

## 10. Process Review Cases Workflow

Shows how the system processes explicitly requested review cases.

```mermaid
flowchart TD
    A[process_review_cases: true] --> B[Load review_cases_final.json]
    B --> C{File Exists and Valid?}
    C -->|No| D[Error: review_cases_final.json not found]
    C -->|Yes| E[Parse Review Cases List]
    
    E --> F[Extract Court and Docket Information]
    F --> G[Set _is_explicitly_requested = true]
    G --> H[Group Cases by Court]
    
    H --> I[Initialize Court Processing]
    I --> J[For Each Court with Review Cases]
    J --> K[Create Browser Context for Court]
    
    K --> L[Navigate to Court System]
    L --> M[Process Review Cases for Court]
    M --> N[Take Next Review Case]
    
    N --> O{More Review Cases?}
    O -->|No| P[Court Review Complete]
    O -->|Yes| Q[Navigate to Specific Case]
    
    Q --> R[Direct Navigation to Case Docket]
    R --> S{Case Page Load Success?}
    S -->|No| T[Log Navigation Failure]
    S -->|Yes| U[Process Case with Explicit Flag]
    
    U --> V[Docket Processor with _is_explicitly_requested=true]
    V --> W[CRITICAL: Skip Removal Detection]
    W --> X[Use Artifact-Only Verification]
    X --> Y{Artifacts Already Exist?}
    Y -->|Yes| Z[Skip - Already Downloaded]
    Y -->|No| AA[Proceed with Download]
    
    AA --> BB[Download Orchestration]
    BB --> CC{Download Success?}
    CC -->|Yes| DD[Mark as Successfully Reprocessed]
    CC -->|No| EE[Mark as Failed Reprocessing]
    
    T --> CASE_DONE[Case Processing Complete]
    Z --> CASE_DONE
    DD --> CASE_DONE
    EE --> CASE_DONE
    
    CASE_DONE --> FF[Log Review Case Result]
    FF --> GG[Update Review Statistics]
    GG --> N
    
    P --> HH[Court Review Summary]
    HH --> II[Close Court Browser Context]
    II --> JJ[Move to Next Court]
    JJ --> J
    
    %% Final Summary
    J --> KK[All Courts Review Complete]
    KK --> LL[Generate Review Summary Report]
    LL --> MM[Log Total Review Statistics]
    MM --> END[Review Cases Processing Complete]
    
    D --> END
    
    %% Review Cases Features
    subgraph RCF["Review Cases Features"]
        RC1[Bypass Normal Court Report Scraping]
        RC2[Direct Case Navigation]
        RC3[Artifact-Only Verification]
        RC4[Skip Removal Detection]
        RC5[Force Reprocessing Capability]
    end
    
    style V fill:#fff2cc
    style X fill:#fff2cc
    style AA fill:#e8f5e8
    style END fill:#e8f5e8
```

## 11. Reprocess Failed Workflow

Shows how the system reprocesses previously failed downloads.

```mermaid
flowchart TD
    A[reprocess_failed: true] --> B[Load Failed Downloads List]
    B --> C[Query DynamoDB for Failed Cases]
    C --> D["Filter by IsDownloaded=false or download_failed=true"]
    D --> E[Extract Court and Docket Information]
    
    E --> F["Set _is_explicitly_requested = true"]
    F --> G[Group Failed Cases by Court]
    G --> H[Initialize Failed Case Processing]
    
    H --> I[For Each Court with Failed Cases]
    I --> J[Create Browser Context for Court]
    J --> K[Navigate to Court System]
    
    K --> L[Process Failed Cases for Court]
    L --> M[Take Next Failed Case]
    M --> N{More Failed Cases?}
    N -->|No| O[Court Failed Processing Complete]
    N -->|Yes| P[Navigate to Failed Case]
    
    P --> Q[Direct Navigation to Case Docket]
    Q --> R{Case Page Load Success?}
    R -->|No| S[Log Navigation Failure - Still Failed]
    R -->|Yes| T[Process Failed Case with Explicit Flag]
    
    T --> U["Docket Processor with _is_explicitly_requested=true"]
    U --> V["CRITICAL: Skip Removal Detection for Reprocessing"]
    V --> W[Use Artifact-Only Verification]
    W --> X[Check for PDF/ZIP files only]
    X --> Y{PDF/ZIP Already Exists?}
    Y -->|Yes| Z[Skip - Previously Downloaded]
    Y -->|No| AA[Attempt Download Retry]
    
    AA --> BB[Download Orchestration with Retry Logic]
    BB --> CC[Execute Document Download]
    CC --> DD{Download Success This Time?}
    DD -->|Yes| EE["Update Status: Successfully Recovered"]
    DD -->|No| FF[Still Failed - Update Failure Count]
    
    S --> CASE_DONE[Failed Case Processing Complete]
    Z --> CASE_DONE
    EE --> CASE_DONE
    FF --> CASE_DONE
    
    CASE_DONE --> GG[Log Failed Case Result]
    GG --> HH[Update Failed Case Statistics]
    HH --> II{Was Recovery Successful?}
    II -->|Yes| JJ[Remove from Failed List]
    II -->|No| KK[Increment Failure Count]
    JJ --> M
    KK --> M
    
    O --> LL[Court Failed Processing Summary]
    LL --> MM[Close Court Browser Context]
    MM --> NN[Move to Next Court]
    NN --> I
    
    %% Final Summary
    I --> OO[All Failed Cases Processed]
    OO --> PP[Generate Recovery Summary Report]
    PP --> QQ[Update Failed Downloads Database]
    QQ --> RR[Log Recovery Statistics]
    RR --> END[Failed Case Reprocessing Complete]
    
    %% Failed Case Features
    subgraph FCF["Failed Case Recovery Features"]
        FC1[Artifact-Only Verification]
        FC2[Skip All Relevance Checks]
        FC3[Force Download Retry]
        FC4[Enhanced Error Logging]
        FC5[Recovery Success Tracking]
    end
    
    %% Recovery Statistics
    subgraph RT["Recovery Tracking"]
        RT1[Previously Failed Count]
        RT2[Successfully Recovered Count]
        RT3[Still Failed Count]
        RT4[New Failures Count]
        RT5[Recovery Success Rate]
    end
    
    style U fill:#fff2cc
    style W fill:#fff2cc
    style AA fill:#ffeeee
    style EE fill:#e8f5e8
    style END fill:#e8f5e8
```

## 12. HTML Only Options and Workflow Impact

Shows how HTML-only processing affects different workflows.

```mermaid
flowchart TD
    A[HTML Only Processing Decision Point] --> B{HTML Only Trigger?}
    B -->|No Download Link Found| C[Automatic HTML Only]
    B -->|Configuration Flag| D[Forced HTML Only]
    B -->|Download Failed| E[Fallback HTML Only]
    
    %% Automatic HTML Only Path
    C --> C1[No Complaint Links Detected]
    C1 --> C2[Extract Case Information from HTML]
    C2 --> C3["Mark html_only = true"]
    C3 --> SAVE_HTML[Save Case with HTML Only Flag]
    
    %% Forced HTML Only Path
    D --> D1["html_only: true in Configuration"]
    D1 --> D2[Skip Download Link Detection]
    D2 --> D3[Extract Case Information from HTML]
    D3 --> D4["Mark html_only = true"]
    D4 --> SAVE_HTML
    
    %% Fallback HTML Only Path
    E --> E1[Download Attempt Failed]
    E1 --> E2[Download Timeout or Error]
    E2 --> E3[Fallback to HTML Processing]
    E3 --> E4[Extract Available Case Information]
    E4 --> E5["Mark html_only = true, download_failed = true"]
    E5 --> SAVE_HTML
    
    %% HTML Only Processing
    SAVE_HTML --> H1[Standard Case Processing]
    H1 --> H2[Apply Relevance Service]
    H2 --> H3[Apply Classification Service]
    H3 --> H4[Apply Verification Service]
    H4 --> H5[Skip Download Orchestration]
    H5 --> H6[File Operations Service Save]
    
    %% Impact on Different Workflows
    H6 --> I{Workflow Type?}
    I -->|Normal Court Processing| NORMAL[Continue to Next Case]
    I -->|Review Cases Processing| REVIEW[Mark Review Case as HTML Only]
    I -->|Failed Reprocessing| FAILED[Mark as Still Failed - No Download]
    I -->|Parallel Processing| PARALLEL[No Impact on Parallelization]
    
    %% Workflow-Specific Handling
    NORMAL --> N1[Normal workflow continues]
    N1 --> N2[Case counted in HTML-only statistics]
    N2 --> CONTINUE[Continue Processing]
    
    REVIEW --> R1[Review case marked as processed]
    R1 --> R2[May need manual review for missing documents]
    R2 --> CONTINUE
    
    FAILED --> F1[Failed case remains in failed list]
    F1 --> F2[HTML data preserved for analysis]
    F2 --> CONTINUE
    
    PARALLEL --> P1[Parallel processing unaffected]
    P1 --> P2[HTML-only cases process faster]
    P2 --> CONTINUE
    
    %% Data Impact
    CONTINUE --> DATA{Data Completeness Impact?}
    DATA -->|HTML Only| D_HTML[Limited case information]
    DATA -->|With Download| D_FULL[Complete case information]
    
    D_HTML --> DH1[Basic case metadata only]
    DH1 --> DH2[No document text analysis]
    DH2 --> DH3[Limited content insights]
    DH3 --> FINAL[Final Processing]
    
    D_FULL --> DF1[Complete case metadata]
    DF1 --> DF2[Full document text analysis]
    DF2 --> DF3[Rich content insights]
    DF3 --> FINAL
    
    FINAL --> END[Processing Complete]
    
    %% HTML Only Characteristics
    subgraph HOC["HTML Only Characteristics"]
        HOC1[Faster Processing]
        HOC2[Lower Resource Usage]
        HOC3[Basic Metadata Only]
        HOC4[No Document Content]
        HOC5[Reduced Analysis Capability]
    end
    
    %% Configuration Options
    subgraph CONFIG["HTML Only Configuration"]
        CONFIG1["html_only: true - Force HTML only"]
        CONFIG2["download_timeout: value - Timeout triggers fallback"]
        CONFIG3["max_download_attempts: value - Retry before fallback"]
        CONFIG4["fallback_to_html: true - Enable fallback"]
    end
    
    style C fill:#ffeeee
    style D fill:#fff3e0
    style E fill:#ffe0e0
    style SAVE_HTML fill:#e8f5e8
    style END fill:#e8f5e8
```

These detailed flowcharts provide comprehensive documentation of all major workflows in the PACER system, showing decision points, error handling, parallel processing coordination, and the impact of different processing modes including HTML-only operations.
