# PACER Download Workflow After HTML Upload

## Overview
This document describes the complete download workflow that executes after the HTML is successfully uploaded to S3. This workflow is preceded by the pre-HTML query workflow that handles docket number processing and PACER navigation. The workflow handles multiple paths depending on the page content and configuration.

## Prerequisites: Pre-HTML Query Workflow

Before this workflow begins, the system must complete the pre-HTML query sequence:

1. **Docket Number Processing**: Extract first 13 digits using format `N:YY-XX-NNNNN`
2. **PACER Query Navigation**: Navigate through `pacer_query.html` → `query_docket_sheet.html`
3. **Document Range Setup**: Auto-populate document fields (1,1) and execute report
4. **HTML Capture**: Successfully capture and upload docket sheet HTML to S3

## Docket Number Processing Specifications

The system enforces strict docket number formatting before any processing:

### Format Requirements
- **Pattern**: `N:YY-{2chars}-NNNNN` (exactly 13 characters)
- **District**: Single digit (1-9)
- **Year**: Two-digit year format
- **Case Type**: Two-character code (cv, cr, md, etc.)
- **Case Number**: Five-digit zero-padded number
- **ISO Dates**: All date operations use `YYYYMMDD` format

### Processing Rules
1. **First 13 Digits Only**: Extract only the first 13 characters
2. **Zero-Padding**: Apply to case numbers with <4 digits
3. **Judge Removal**: Strip judge initials and additional identifiers
4. **Validation**: Ensure compliance with required format

### Examples
- `2:25-cv-02364-AMS` → `2:25-cv-02364` (judge initials removed)
- `1:24-cv-123` → `1:24-cv-00123` (zero-padded)
- `3:25-cr-45-ABC-DEF` → `3:25-cr-00045` (multiple suffixes removed)

## Flow Chart

```mermaid
flowchart TD
    Start([HTML Successfully Uploaded]) --> CheckMode{Check html_only Mode}
    
    CheckMode -->|html_only = true| LogMode1[Log: Will stop after HTML capture]
    CheckMode -->|html_only = false| LogMode2[Log: Will continue to document downloads]
    
    LogMode1 --> MDLCheck{Check MDL Flags}
    LogMode2 --> MDLCheck
    
    MDLCheck -->|Match Found| SkipDownload1[Return: Skip download - MDL match]
    MDLCheck -->|No Match| IgnoreCheck{Check ignore_download Config}
    
    IgnoreCheck -->|Match Found| SkipDownload2[Return: Skip download - Ignore config match]
    IgnoreCheck -->|No Match| ProceedDownload[Proceed with Download Workflow]
    
    ProceedDownload --> CreateTempDir[Create Temp Download Directory<br/>dl_stage_court_docket_uuid]
    
    CreateTempDir --> AttachListener[Attach Download Event Listener<br/>on 'download' event]
    
    AttachListener --> TriggerAction[Trigger Download Action]
    
    TriggerAction --> CheckboxFlow{Handle Document Checkboxes}
    
    CheckboxFlow -->|Removal Case &<br/>Direct Link| RemovalDirect[Click Removal Link Directly<br/>Return: Download Initiated]
    CheckboxFlow -->|Checkboxes Selected| PostCheckbox[Check Post-Checkbox Actions]
    CheckboxFlow -->|No Action| TryViewDoc[Try Other Download Methods]
    
    PostCheckbox --> ClickDownloadSelected{Click 'Download Selected'?}
    ClickDownloadSelected -->|Success| CheckViewDoc1{Need 'View Document'<br/>on Billing Page?}
    ClickDownloadSelected -->|Failed| TryDownloadDocs[Try 'Download Documents']
    
    CheckViewDoc1 -->|Yes| ClickViewDoc1[Click 'View Document']
    CheckViewDoc1 -->|No| DownloadTriggered1[Download Triggered]
    ClickViewDoc1 --> DownloadTriggered1
    
    TryDownloadDocs --> ClickDownloadDocs{Click 'Download Documents'?}
    ClickDownloadDocs -->|Success| DownloadTriggered2[Download Triggered]
    ClickDownloadDocs -->|Failed| TryViewDoc
    
    TryViewDoc --> TryViewDocButton{Try 'View Document' Button?}
    TryViewDocButton -->|Success| ViewDocFlow[View Document Flow]
    TryViewDocButton -->|Failed| TryViewDocLink{Try 'View Document' Link?}
    
    TryViewDocLink -->|Success| GoToDocPage[Go to Document Page<br/>Fill doc_number = 1<br/>Click 'Run Report']
    TryViewDocLink -->|Failed| CheckVADA{Check VADA Buttons?}
    
    CheckVADA -->|Both Exist| TryVADA[Try 'Download All' or 'View All']
    CheckVADA -->|Not Both| TryFallback[Try Fallback Buttons]
    
    TryVADA --> ClickVADA{Click VADA Button?}
    ClickVADA -->|Success| DownloadTriggered3[Download Triggered]
    ClickVADA -->|Failed| TryFallback
    
    TryFallback --> TryHyperlink{Try Hyperlink '1'?}
    TryHyperlink -->|Success| CheckMultiDoc{Multi-Doc Page?}
    TryHyperlink -->|Failed| NoTrigger[No Download Trigger Found]
    
    CheckMultiDoc -->|Yes| MultiDocFlow[Handle Checkboxes<br/>Click 'Download Selected']
    CheckMultiDoc -->|No| DirectPDF[Direct PDF Download]
    
    MultiDocFlow --> CheckMultiDocSuccess{Success?}
    CheckMultiDocSuccess -->|Yes| CheckViewDoc2{Need 'View Document'?}
    CheckMultiDocSuccess -->|No| FailMultiDoc[Multi-Doc Flow Failed]
    
    CheckViewDoc2 -->|Yes| ClickViewDoc2[Click 'View Document']
    CheckViewDoc2 -->|No| DownloadTriggered4[Download Triggered]
    ClickViewDoc2 --> DownloadTriggered4
    
    RemovalDirect --> WaitForDownload[Wait for Download Event<br/>Timeout: 600s]
    DownloadTriggered1 --> WaitForDownload
    DownloadTriggered2 --> WaitForDownload
    DownloadTriggered3 --> WaitForDownload
    DownloadTriggered4 --> WaitForDownload
    ViewDocFlow --> WaitForDownload
    GoToDocPage --> WaitForDownload
    DirectPDF --> WaitForDownload
    
    WaitForDownload --> DownloadEvent{Download Event Fired?}
    
    DownloadEvent -->|Yes| SaveDownload[Save Download to Temp Dir<br/>Check File Exists & Size > 0]
    DownloadEvent -->|No/Timeout| CheckInlinePDF{Check for Inline PDF}
    
    SaveDownload --> CheckFailure{Download Failure?}
    CheckFailure -->|Yes| DownloadFailed[Download Failed]
    CheckFailure -->|No| FileStabilization[Wait for File Stabilization<br/>Check size remains constant]
    
    CheckInlinePDF -->|PDF in URL| GeneratePDF[Generate PDF with page.pdf]
    CheckInlinePDF -->|Embedded PDF| GeneratePDF
    CheckInlinePDF -->|No PDF| NoValidFile[No Valid File]
    
    GeneratePDF --> CheckPDFSize{PDF Size > 100 bytes?}
    CheckPDFSize -->|Yes| SaveInlinePDF[Save Inline PDF to Temp]
    CheckPDFSize -->|No| PDFFailed[PDF Generation Failed]
    
    SaveInlinePDF --> FileStabilization
    
    FileStabilization --> StableCheck{File Stable?}
    StableCheck -->|Yes| FinalizeDownload[Finalize Download<br/>- Move to final location<br/>- ZIP if configured<br/>- Set new_filename]
    StableCheck -->|No| StabilizationFailed[Stabilization Failed]
    
    FinalizeDownload --> FinalizeSuccess{Success?}
    FinalizeSuccess -->|Yes| UploadToS3[Upload to S3<br/>Set s3_link in case_details]
    FinalizeSuccess -->|No| FinalizeFailed[Finalize Failed]
    
    UploadToS3 --> Cleanup[Cleanup Temp Directory]
    
    NoTrigger --> ReturnFalse[Return: false]
    FailMultiDoc --> ReturnFalse
    NoValidFile --> ReturnFalse
    DownloadFailed --> ReturnFalse
    PDFFailed --> ReturnFalse
    StabilizationFailed --> ReturnFalse
    FinalizeFailed --> ReturnFalse
    
    Cleanup --> ReturnTrue[Return: true]
    ReturnFalse --> End([End])
    ReturnTrue --> End
    
    style Start fill:#90EE90
    style End fill:#FFB6C1
    style DownloadTriggered1 fill:#87CEEB
    style DownloadTriggered2 fill:#87CEEB
    style DownloadTriggered3 fill:#87CEEB
    style DownloadTriggered4 fill:#87CEEB
    style ReturnTrue fill:#90EE90
    style ReturnFalse fill:#FFB6C1
```

## Key Decision Points

### 1. HTML-Only Mode Check
- If `html_only = true`, the workflow logs but continues (doesn't skip immediately)
- Allows document numbering workflow to complete before stopping

### 2. Skip Download Checks
- **MDL Flags**: Matches case flags against configured MDL flags
- **Ignore Download**: Matches court/attorney/law firm against ignore configuration

### 3. Download Trigger Methods (in order of preference)
1. **Document Checkboxes** - For multi-document selection
2. **View Document Button** - Direct button click
3. **View Document Link** - Link that leads to document number input
4. **VADA Buttons** - "View All" and "Download All" buttons
5. **Fallback Buttons** - Alternative selectors
6. **Hyperlink '1'** - Document link that may lead to multi-doc page

### 4. Post-Click Handling
- After "Download Selected", may need to click "View Document" on billing page
- Multi-document pages require checkbox selection before download

### 5. Download Capture Methods
1. **Download Event** - Browser download event with file save
2. **Inline PDF** - PDF displayed in browser, captured with page.pdf()

### 6. File Processing Steps
1. **Stabilization** - Wait for file size to remain constant
2. **Finalization** - Move to final location, optional ZIP
3. **S3 Upload** - Upload to S3 and set CDN link

## Error Handling

- **Timeout Protection**: 60-second overall timeout wrapper
- **Download Timeout**: 600-second timeout for download event
- **Page Closed Checks**: Multiple checks for closed page/context
- **Fallback Paths**: Multiple download trigger attempts
- **Temp Directory**: Preserved on failure if configured

## Special Cases

### Removal Cases
- May click removal link directly without separate download button
- Checkbox handler detects and processes these automatically

### Multi-Document Pages
Detection criteria:
- "document selection menu" text
- Multiple checkboxes with name="document_*"
- "Download Selected" button present
- Attachment table with descriptions

### Inline PDFs
Detection methods:
- URL contains ".pdf"
- Embedded PDF elements (embed, object, iframe)
- Captures using Playwright's page.pdf() method

## Configuration Impact

Key configuration options that affect the workflow:

### Download Workflow Configuration
- `html_only`: Determines if PDF download should be attempted
- `mdl_flags`: List of MDL flags that trigger HTML-only processing
- `ignore_download`: Court/attorney/law firm combinations to skip
- `trigger_action_initial_delay_ms`: Delay before trigger attempts
- `download_save_as_completion_timeout_s`: Timeout for download event
- `inline_pdf_check_delay_ms`: Delay before checking for inline PDF
- `cleanup_temp_downloads_on_failure`: Whether to remove temp files
- `zip_pdf_downloads`: Whether to ZIP downloaded PDFs
- `context_download_path`: Base path for temporary downloads

### Pre-HTML Query Configuration  
- `docket_search_timeout_ms`: Timeout for "Find This Case" operation (default: 30000)
- `query_form_delay_ms`: Delay before filling query form fields (default: 1000)
- `documents_numbered_from_`: Always set to `"1"` for single document retrieval
- `documents_numbered_to_`: Always set to `"1"` for single document retrieval

## Implementation Details

### Key Methods
- `execute_download_workflow()` - Main entry point (not shown in this service)
- `handle_pdf_download_async()` - Main download handler with timeout wrapper
- `_trigger_download_action()` - Attempts various download triggers
- `_handle_document_checkboxes_async()` - Checkbox selection logic (via navigation service)
- `_wait_for_file_stabilization()` - Ensures download is complete
- `_upload_document_to_s3()` - S3 upload with CDN link generation

### Dependencies
- `PacerNavigator` - Page interaction wrapper
- `PacerNavigationService` - Checkbox handling logic
- `PacerFileOperationsService` - File management and S3 access
- `PacerFileManagementService` - Download finalization
- `S3AsyncStorage` - S3 upload operations

### Pre-Workflow Dependencies
- `DocketUtils` - Docket number normalization and validation
- `ElementLocator` - PACER form field location and interaction
- `LoginHandler` - Authentication and session management

## Complete Workflow Integration

This workflow integrates with the pre-HTML query workflow to provide end-to-end case processing:

1. **Pre-HTML Phase** (see `download_workflow_guide.md`):
   - Docket number processing and normalization
   - PACER query navigation
   - Form completion and report execution
   
2. **HTML Capture Phase**:
   - Docket sheet HTML extraction
   - S3 upload and CDN link generation
   
3. **Post-HTML Phase** (this document):
   - Skip condition evaluation
   - Document download execution
   - File processing and finalization

The complete workflow ensures robust, consistent processing from initial docket number input through final document storage and CDN availability.