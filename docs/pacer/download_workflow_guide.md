# PACER Download Workflow Guide

## Overview

This document describes the download workflows in the new PACER service architecture. The download process has been completely refactored from the legacy monolithic design into a service-oriented architecture that provides better maintainability, testability, and reliability.

## Key Components

The download workflow is orchestrated by several specialized services:

1. **PacerDownloadOrchestrationService**: Coordinates the entire download workflow
2. **PacerDocketProcessingOrchestratorService**: Orchestrates the complete docket processing workflow
3. **PacerFileOperationsService**: Handles file saving, S3 uploads, and directory management
4. **PacerNavigationService**: Manages browser navigation and interaction
5. **PacerFileManagementService**: Manages file paths and operations

## Download Workflow Sequence

### 0. Pre-HTML Query Workflow

Before HTML capture, the system must navigate the PACER query interface to access the docket sheet:

```mermaid
flowchart TD
    A[Start Process] --> B[Navigate to pacer_query.html]
    B --> C[Extract First 13 Digits from Docket Number]
    C --> D[Enter Digits in Case Search Field]
    D --> E[Tab Off Field to Trigger Validation]
    E --> F[Click 'Find This Case' Button]
    F --> G{Is 'Run Query' Enabled?}
    G -->|No| H[Handle Search Error]
    G -->|Yes| I[Click 'Run Query' Button]
    I --> J[Navigate to query_docket_sheet.html]
    J --> K[Auto-populate documents_numbered_from_ = '1']
    K --> L[Auto-populate documents_numbered_to_ = '1']
    L --> M[Click 'Run Report' Button]
    M --> N[Proceed to Docket Sheet Processing]
    H --> O[End - Error State]
    N --> P[Begin HTML Capture Phase]
```

#### Docket Number Processing Requirements

**Format Specification**: The system extracts exactly **13 characters** from docket numbers in the format `N:YY-{2chars}-NNNNN`:

1. **First 13 Digits Only**: Extract only the first 13 characters from full docket numbers
   - Input: `2:25-cv-02364-AMS-JDE` → Extract: `2:25-cv-02364`
   - Input: `1:24-cv-123-ABC` → Extract: `1:24-cv-00123` (with zero-padding)

2. **Zero-Padding Rules**: Numbers with fewer than 4 digits in the case number portion must be zero-padded:
   - `2:25-cv-123` → `2:25-cv-00123`
   - `1:24-cv-45` → `1:24-cv-00045`
   - `3:25-cv-1` → `3:25-cv-00001`

3. **Stop-After-Number Validation**: Processing stops after the 5-digit case number:
   - Remove judge initials: `-AMS`, `-JDE`, etc.
   - Remove additional identifiers beyond the base docket format

4. **ISO Date Format**: All date operations use `YYYYMMDD` format for consistency across the system

#### Pre-HTML Navigation Steps

**Step 1: PACER Query Page Navigation**
- Land on `pacer_query.html`
- Apply docket number extraction rules to get first 13 digits
- Enter extracted digits in the case search field
- Tab off the field to trigger PACER's client-side validation
- Wait for "Find This Case" button to become active
- Click "Find This Case" button
- Verify that "Run Query" button becomes enabled
- Click "Run Query" button to proceed

**Step 2: Query Docket Sheet Form**
- Navigate to `query_docket_sheet.html` page
- Locate document range fields:
  - `documents_numbered_from_` field
  - `documents_numbered_to_` field
- Auto-populate both fields with value `"1"` for single document retrieval
- Click "Run Report" button to execute the query
- Wait for navigation to the actual docket sheet page

### 1. HTML Capture and Processing

After successful navigation, the system captures and processes the HTML content:

```mermaid
flowchart TD
    A[Start Process] --> B[Process HTML Content]
    B --> C[Extract Case Details]
    C --> D[Upload HTML to S3]
    D --> E[Apply Document Numbering]
    E --> F{HTML Only Mode?}
    F -->|Yes| G[Skip Document Downloads]
    F -->|No| H[Proceed to Document Downloads]
```

The HTML capture phase:
1. Extracts case details from the docket page
2. Uploads HTML content to S3
3. Sets the `s3_html` field in case details
4. Applies document numbering workflow to generate document list
5. Checks if HTML-only mode is enabled

### 2. Relevance and Classification

For non-explicitly requested cases, relevance and classification filters are applied:

```mermaid
flowchart TD
    A[Apply Relevance Filters] --> B{Is MDL Case?}
    B -->|Yes| C[Mark as Relevant]
    B -->|No| D[Check Defendant Relevance]
    D --> E{Needs Review?}
    E -->|Yes| F[Save as Review Case]
    E -->|No| G[Continue Processing]
    G --> H{Is Transfer Case?}
    H -->|Yes| I[Process Transfer Logic]
    H -->|No| J{Is Removal Case?}
    J -->|Yes| K[Apply Removal Rules]
    J -->|No| L[Continue to Verification]
```

### 3. Case Verification

The system verifies if the case should be processed:

```mermaid
flowchart TD
    A[Verify Case] --> B{Already Downloaded?}
    B -->|Yes| C[Skip Processing]
    B -->|No| D{Explicitly Requested?}
    D -->|Yes| E[Force Processing]
    D -->|No| F{Meets Relevance Criteria?}
    F -->|Yes| G[Continue to Download]
    F -->|No| H[Skip Processing]
```

### 4. Document Download Workflow

The document download workflow is handled by `PacerDownloadOrchestrationService`:

```mermaid
flowchart TD
    A[Execute Download Workflow] --> B[Check Skip Conditions]
    B --> C{Should Skip Download?}
    C -->|Yes| D[Process HTML-Only Case]
    C -->|No| E[Apply Document Numbering]
    E --> F[Capture HTML]
    F --> G{HTML-Only Mode?}
    G -->|Yes| H[Stop After HTML Capture]
    G -->|No| I[Run Relevance Check]
    I --> J{Needs Review?}
    J -->|Yes| K[Save as Review Case]
    J -->|No| L[Check for Existing Artifacts]
    L --> M{Artifact Exists?}
    M -->|Yes| N[Skip Download]
    M -->|No| O[Click Complaint Link]
    O --> P[Handle PDF Download]
    P --> Q{Download Successful?}
    Q -->|Yes| R[Upload to S3]
    Q -->|No| S[Handle Download Failure]
```

### 5. Download Execution

The actual download execution involves:

```mermaid
flowchart TD
    A[Handle PDF Download] --> B[Create Temp Directory]
    B --> C[Attach Download Event Listener]
    C --> D[Trigger Download Action]
    D --> E{Download Event Fired?}
    E -->|Yes| F[Save Downloaded File]
    E -->|No| G[Check for Inline PDF]
    G --> H{Is PDF Page?}
    H -->|Yes| I[Capture with page.pdf]
    H -->|No| J[Download Failed]
    F --> K[Wait for File Stabilization]
    I --> K
    K --> L[Finalize Download]
    L --> M[Upload to S3]
    M --> N[Set s3_link in Case Details]
```

## Docket Number Format Specifications

The system enforces strict docket number formatting throughout the workflow:

### Format Requirements

| Component | Description | Example |
|-----------|-------------|---------|
| **District** | Single digit (1-9) | `2` |
| **Year** | Two-digit year | `25` (for 2025) |
| **Case Type** | Two-character code | `cv`, `cr`, `md`, etc. |
| **Case Number** | Five-digit zero-padded number | `02364` |
| **Total Length** | Exactly 13 characters | `2:25-cv-02364` |

### Processing Rules

1. **Extraction**: Take only first 13 characters of any docket number
2. **Normalization**: Apply zero-padding to case numbers <4 digits  
3. **Validation**: Ensure format matches `N:YY-XX-NNNNN` pattern
4. **Truncation**: Remove judge initials and additional identifiers

### Examples

| Input | Processed | Notes |
|-------|-----------|-------|
| `2:25-cv-02364-AMS-JDE` | `2:25-cv-02364` | Judge initials removed |
| `1:24-cv-123` | `1:24-cv-00123` | Zero-padded to 5 digits |
| `3:25-cr-45-ABC` | `3:25-cr-00045` | Zero-padded, judge removed |
| `2:25-md-1-XYZ-123` | `2:25-md-00001` | Multiple suffixes removed |

## Key Improvements in the New Architecture

### 1. Isolated Download Paths

The new architecture maintains the critical download path infrastructure that prevents file corruption during parallel processing:

```
data/YYYYMMDD/dockets/temp/COURT_multidocket_dl_UUID/
```

Each court and docket gets its own isolated download path with a unique UUID to prevent conflicts.

### 2. File Stabilization

The system implements a robust file stabilization mechanism:

- Monitors file size changes over time
- Ensures files are completely downloaded before processing
- Handles zero-byte files and partial downloads
- Configurable timeout and stability parameters

### 3. HTML Capture Before Downloads

HTML content is captured and uploaded to S3 before any document downloads are attempted:

- Sets `s3_html` field in case details
- Preserves HTML even if document downloads fail
- Enables HTML-only mode for certain cases

### 4. Comprehensive Error Handling

The download workflow includes extensive error handling:

- Browser context validation
- Download path verification
- Download event monitoring
- Timeout handling
- Cleanup of temporary files

### 5. Download Modes

The system supports multiple download modes:

- **Full Download**: Downloads both HTML and PDF documents
- **HTML Only**: Captures HTML but skips PDF downloads
- **Explicit Request**: Forces download regardless of relevance

## Configuration Options

Key configuration options that affect the download workflow:

| Option | Description | Default |
|--------|-------------|---------|
| `html_only` | Capture HTML but skip document downloads | `false` |
| `context_download_path` | Path for browser downloads | `data/YYYYMMDD/dockets/temp/` |
| `zip_pdf_downloads` | Compress PDF downloads | `true` |
| `cleanup_temp_downloads_on_failure` | Remove temp files on failure | `false` |
| `download_save_as_completion_timeout_s` | Timeout for download completion | `600` |
| `inline_pdf_check_delay_ms` | Delay before checking for inline PDFs | `3500` |
| `docket_search_timeout_ms` | Timeout for "Find This Case" operation | `30000` |
| `query_form_delay_ms` | Delay before filling query form fields | `1000` |

## Special Case Handling

### MDL Cases

MDL cases receive special handling:

- MDL flags override ignore_download rules
- MDL cases are always considered relevant
- MDL number is preserved in case details

### Removal Cases

Removal cases have specific download workflows:

- Special checkbox handling for removal documents
- Different link selection strategy
- Failure tracking for removal cases

### Transfer Cases

Transfer cases are processed with special logic:

- Source court identification
- Duplicate checking across courts
- S3 link preservation from source case

## HTML-Only Processing

Cases can be processed in HTML-only mode under these conditions:

1. `html_only: true` is set in configuration
2. Case matches ignore_download configuration
3. Case matches MDL flags configuration
4. Case needs review based on relevance criteria

HTML-only processing:
1. Captures and uploads HTML content
2. Sets `s3_html` field in case details
3. Skips document download attempts
4. Marks case as `html_only: true`

## Troubleshooting

Common download issues and solutions:

1. **Missing Download Path**: Ensure `context_download_path` is properly set
2. **Zero-byte Files**: Check file stabilization parameters
3. **Missing S3 Links**: Verify S3 storage configuration
4. **Download Timeouts**: Adjust timeout parameters
5. **Browser Context Issues**: Validate browser context configuration

## Integration with Other Services

The download workflow integrates with:

1. **RelevanceService**: Determines if case should be downloaded
2. **PacerFileManagementService**: Manages file paths and operations
3. **S3AsyncStorage**: Handles S3 uploads
4. **PacerNavigationService**: Manages browser navigation
5. **PacerRepository**: Checks for existing cases

## Conclusion

The new download workflow architecture provides a robust, maintainable solution for downloading court documents. By separating concerns into focused services, the system achieves better reliability, testability, and extensibility while maintaining backward compatibility with the legacy system.