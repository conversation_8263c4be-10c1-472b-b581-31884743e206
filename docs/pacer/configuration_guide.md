# PACER Configuration Guide

This guide explains how to configure the PACER service architecture, including feature flags, service-specific settings, and critical configuration files.

## Feature Flags

### Main Service Architecture Flags

```yaml
# config/scrape.yml
use_new_pacer_services: false        # Controls other PACER services
use_new_docket_services: true        # Controls docket processing architecture
use_new_transformer_services: false  # Controls transformer services
```

### Docket Services Configuration

#### Enable New Architecture
```yaml
# Enable new service-oriented architecture
use_new_docket_services: true

# Additional settings for new services
upload: true                    # Enable S3 uploads
headless: true                 # Run browser in headless mode
use_docket_report_log: true    # Enable critical ignore_download validation
```

#### Legacy Architecture (Default)
```yaml
# Use original monolithic processor
use_new_docket_services: false

# Legacy settings still work
upload: true
headless: true
use_docket_report_log: false   # Optional ignore_download validation
```

## Critical Configuration Files

### ignore_download.json

**Location**: `src/config/pacer/ignore_download/ignore_download.json`

**Purpose**: Defines patterns for cases that should be skipped during processing

**Critical Behavior**: When `use_docket_report_log: true`, missing or invalid ignore_download.json causes `sys.exit(1)`

```json
[
  {
    "court_id": "njd",
    "law_firm": "Weitz & Luxenberg",
    "attorney_name": "Ellen Relkin"
  },
  {
    "court_id": "cand", 
    "law_firm": "Beasley Allen",
    "attorney_name": "Andy <PERSON>field"
  },
  {
    "court_id": "ilnd",
    "law_firm": "Simmons Hanly Conroy",
    "attorney_name": "John Simmons"
  }
]
```

**Matching Logic**:
- All fields must match (case-insensitive)
- Court ID, law firm, and attorney name
- Used by Relevance Service for skip decisions

### relevance_config.json

**Location**: `src/config/pacer/relevance_config.json`

**Purpose**: Defines relevance criteria for case processing

```json
{
  "explicitly_relevant_causes": [
    "personal injury",
    "product liability", 
    "wrongful death",
    "class action",
    "mass tort"
  ],
  "excluded_cause_statutes": [
    "28:1331",
    "28:1332", 
    "42:1983"
  ],
  "relevant_nos_keywords": [
    "personal injury",
    "product liability",
    "wrongful death",
    "defective product",
    "toxic tort"
  ]
}
```

**Usage**:
- `explicitly_relevant_causes`: Causes that make cases relevant
- `excluded_cause_statutes`: Statute references that exclude cases
- `relevant_nos_keywords`: Keywords in Nature of Suit that indicate relevance

### stability_config.json

**Location**: `src/config/pacer/stability_config.json`

**Purpose**: Page stability and timing configuration

```json
{
  "timeout_s": 45,
  "check_interval_s": 0.5,
  "required_duration_s": 2.0
}
```

**Parameters**:
- `timeout_s`: Maximum wait time for page stability
- `check_interval_s`: How often to check for stability  
- `required_duration_s`: How long page must be stable

**Defaults** (if file missing):
```json
{
  "timeout_s": 45,
  "check_interval_s": 0.5, 
  "required_duration_s": 2.0
}
```

### paths_config.json

**Location**: `src/config/pacer/paths_config.json`

**Purpose**: File path configuration

```json
{
  "relevant_defendants_relative_path": "defendants/relevant_defendants.json"
}
```

### relevant_defendants.json

**Location**: `src/config/pacer/defendants/relevant_defendants.json`

**Purpose**: List of defendants to match for relevance

```json
{
  "defendants": [
    "3m company",
    "dupont",
    "monsanto",
    "johnson & johnson", 
    "pfizer",
    "bayer",
    "merck",
    "bristol-myers squibb"
  ]
}
```

**Usage**:
- Converted to lowercase for case-insensitive matching
- Used by Relevance Service for defendant matching
- Can be string or list in case data

## Service-Specific Configuration

### Configuration Service

```python
# Initialization
configuration_service = PacerConfigurationService(
    config=main_config,
    config_dir=Path("src/config/pacer")  # Auto-detected if not provided
)

# Critical behavior settings
main_config = {
    'use_docket_report_log': True,  # Enables sys.exit(1) on missing ignore_download.json
    'court_id': 'njd'
}
```

**Auto-Detection Logic**:
1. Check provided `config_dir` parameter
2. Try `src/config/pacer` relative to current directory  
3. Try `config/pacer` relative to current directory
4. Fall back to current directory

### File Operations Service

```python
# Configuration for file operations
file_ops_config = {
    'data_dir': '/path/to/data',           # Override default data directory
    'upload': True,                        # Enable S3 uploads
    's3_bucket': 'lexgenius-dockets'    # S3 bucket name
}

# Environment variable override
# LEXGENIUS_DATA_DIR=/custom/data/path
```

**Data Directory Priority**:
1. `config['data_dir']` if provided
2. `LEXGENIUS_DATA_DIR` environment variable
3. `./data` (default)

### Case Processing Service

```python
# Processing configuration
processing_config = {
    'court_id': 'njd',                # Required for MDL special handling
    'cdn_base_url': 'https://cdn.lexgenius.ai'  # For URL generation
}
```

**Special MDL Handling**:
- **ILND + MDL 3060**: Special flags and processing
- **CAND + MDL 3047**: Special flags and processing

### Relevance Service

```python
# Relevance service configuration
relevance_config = {
    'court_id': 'njd',
    'flags': ['MDL2738']  # MDL flags for processing
}
```

**Priority Order**:
1. **MDL Flag** - Overrides all other rules
2. **Ignore Download** - Skip if matches pattern
3. **Excluded Statute** - Skip if excluded
4. **Explicitly Relevant** - Process if relevant
5. **Not Relevant** - Default skip

### Case Verification Service

```python
# Verification configuration  
verification_config = {
    'court_id': 'njd',
    'test_mode': False  # Enable for testing
}
```

**Critical Verification Logic**:
- **Explicitly Requested** (`_is_explicitly_requested=True`)
  - Uses artifact-only verification (PDF/ZIP only)
  - Enables failed download reprocessing
- **Report-Scraped** (`_is_explicitly_requested=False`)
  - Uses full verification (includes JSON files)
  - Standard duplicate prevention

### Download Orchestration Service

```python
# Download configuration
download_config = {
    'context_download_path': '/tmp/downloads/njd_uuid',  # Required
    'headless': True,
    'download_timeout': 60,
    'upload_to_s3': True
}
```

## Environment Variables

### Required Environment Variables

```bash
# AWS Configuration (for S3 and DynamoDB)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=us-east-1

# PACER Credentials
PACER_USERNAME=your_username
PACER_PASSWORD=your_password

# Optional Data Directory Override
LEXGENIUS_DATA_DIR=/custom/data/path
```

### Development Environment

```bash
# .env file for development
AWS_PROFILE=lexgenius-dev
LEXGENIUS_DATA_DIR=./data
PACER_USERNAME=dev_user
PACER_PASSWORD=dev_password

# Logging configuration
LOG_LEVEL=DEBUG
LOG_FORMAT=detailed
```

## Configuration Validation

### Service Initialization Validation

Each service validates its configuration on initialization:

```python
# Configuration Service validation
def validate_configuration_completeness(self) -> bool:
    """Validate that all required configurations are present."""
    
    # Check required configs
    required_configs = ['relevance_config', 'stability_config']
    for config_name in required_configs:
        config_data = getattr(self, config_name, {})
        if not config_data:
            return False
    
    # Check file dependencies
    if not self.relevant_defendants_path or not self.relevant_defendants_path.exists():
        return False
    
    return True
```

### Critical Validation Rules

#### ignore_download.json Validation

```python
if config.get('use_docket_report_log', False):
    # CRITICAL: Missing or invalid ignore_download.json causes sys.exit(1)
    try:
        ignore_config = self._load_json_config('ignore_download/ignore_download.json')
        if not ignore_config:
            print("CRITICAL: ignore_download.json is required when use_docket_report_log=True")
            sys.exit(1)
    except Exception as e:
        print(f"CRITICAL: Failed to load ignore_download.json: {e}")
        sys.exit(1)
```

#### Required Field Validation

```python
# Each entry must have required fields
for entry in ignore_download_config:
    required_fields = ['court_id', 'law_firm', 'attorney_name']
    for field in required_fields:
        if field not in entry:
            raise ValueError(f"Missing required field '{field}' in ignore_download.json")
```

## Testing Configuration

### Test-Specific Configurations

```yaml
# config/scrape_test.yml
use_new_docket_services: true
process_single_court: ['njd']
run_parallel: false
test_mode: true
use_docket_report_log: false  # Disable critical validation for tests
upload: false                 # Disable S3 uploads for tests
```

### Mock Configuration

```python
# Test configuration with mocked dependencies
test_config = {
    'use_new_docket_services': True,
    'court_id': 'test',
    'test_mode': True,
    'upload': False,
    'use_docket_report_log': False
}

# Mock services for testing
mock_s3_storage = AsyncMock()
mock_pacer_repository = AsyncMock()
mock_file_manager = AsyncMock()
```

### Configuration File Mocking

```python
# Mock configuration files for testing
@patch('pathlib.Path.exists')
@patch('builtins.open', mock_open(read_data='[]'))
def test_empty_ignore_download_config(mock_exists, mock_file):
    mock_exists.return_value = True
    
    service = PacerConfigurationService(
        config={'use_docket_report_log': False},
        config_dir=Path('/mock/config')
    )
    
    assert service.ignore_download_config == []
```

## Performance Configuration

### Parallel Processing

```yaml
# Enable parallel processing
run_parallel: true
num_workers: 4  # Number of parallel workers

# Parallel-specific settings
context_download_path: '/tmp/downloads/{court_id}_{uuid}'  # UUID collision avoidance
browser_pool_size: 4                                      # Browser instance pool
```

### Memory Management

```yaml
# Memory optimization settings
case_batch_size: 100      # Process cases in batches
cleanup_temp_files: true  # Clean up temporary files
max_file_cache_size: 1000 # Maximum cached files
```

### Download Optimization

```yaml
# Download performance settings
download_timeout: 60        # Download timeout in seconds
max_concurrent_downloads: 3 # Maximum concurrent downloads per court
retry_failed_downloads: 3   # Number of retry attempts
```

## Debugging Configuration

### Verbose Logging

```yaml
# Enable detailed logging
log_level: DEBUG
log_service_calls: true
log_html_content: false     # WARNING: Large logs
log_database_queries: true
```

### Development Flags

```yaml
# Development and debugging flags
debug_mode: true
save_html_content: true     # Save HTML for debugging
skip_document_downloads: false  # Skip downloads for faster testing
validate_all_configs: true     # Extra configuration validation
```

### Service-Level Debugging

```python
# Service-specific debug configuration
debug_config = {
    'debug_configuration_service': True,
    'debug_file_operations': True,
    'debug_case_processing': True,
    'debug_relevance_checks': True,
    'debug_verification_logic': True,
    'debug_download_workflow': True
}
```

## Migration Configuration

### Gradual Migration Settings

```yaml
# Phase 1: Single court testing
use_new_docket_services: true
process_single_court: ['njd']
run_parallel: false
migration_phase: 'testing'

# Phase 2: Multi-court expansion  
process_single_court: ['njd', 'cand', 'nysd']
migration_phase: 'expansion'

# Phase 3: Full production
process_single_court: []
run_parallel: true
migration_phase: 'production'
```

### Rollback Configuration

```yaml
# Instant rollback capability
use_new_docket_services: false  # Back to legacy
migration_phase: 'rollback'
preserve_new_service_logs: true # Keep logs for analysis
```

### A/B Testing Configuration

```yaml
# A/B testing between architectures
use_new_docket_services: true
compare_with_legacy: true        # Run both and compare results
legacy_comparison_ratio: 0.1     # 10% of cases run through both
save_comparison_results: true    # Save comparison data
```

This configuration guide provides comprehensive coverage of all configuration options for the PACER service architecture, enabling both development and production deployment scenarios.
