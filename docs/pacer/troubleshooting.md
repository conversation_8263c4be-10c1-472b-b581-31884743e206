# PACER Troubleshooting Guide

This guide helps diagnose and resolve common issues with the PACER service architecture.

## Common Issues and Solutions

### Service Initialization Failures

#### Issue: Configuration Service Fails to Initialize

**Symptoms:**
```
CRITICAL: ignore_download.json is required when use_docket_report_log=True
```

**Cause:** Missing or invalid ignore_download.json file with critical validation enabled.

**Solution:**
```bash
# 1. Check if file exists
ls -la src/config/pacer/ignore_download/ignore_download.json

# 2. If missing, create minimal file
mkdir -p src/config/pacer/ignore_download
echo '[]' > src/config/pacer/ignore_download/ignore_download.json

# 3. Or disable critical validation for testing
# In config file, set: use_docket_report_log: false

# 4. Validate JSON syntax
python -m json.tool src/config/pacer/ignore_download/ignore_download.json
```

#### Issue: Service Import Errors

**Symptoms:**
```
ImportError: No module named 'src.services.pacer'
```

**Cause:** Python path or module structure issues.

**Solution:**
```bash
# 1. Verify directory structure
ls -la src/services/pacer/

# 2. Check __init__.py files exist
find src/services/pacer/ -name "__init__.py"

# 3. Add to Python path if needed
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 4. Verify imports work
python -c "from src.services.pacer.configuration_service import PacerConfigurationService; print('OK')"
```

#### Issue: Dependency Injection Failures

**Symptoms:**
```
TypeError: __init__() missing 1 required positional argument: 'pacer_repository'
```

**Cause:** Missing or incorrect dependencies passed to services.

**Solution:**
```python
# Verify all required dependencies are provided
required_deps = {
    'pacer_repository': PacerRepository,
    'file_manager': FileManager,
    'document_downloader': DocumentDownloader,
    's3_storage': S3ManagerAsync
}

# Check each dependency is not None
for name, dep in required_deps.items():
    if dep is None:
        print(f"Missing dependency: {name}")
```

### Case Processing Issues

#### Issue: HTML Processing Timeouts

**Symptoms:**
```
TimeoutError: Page content did not stabilize within timeout
```

**Cause:** Page takes too long to load or is unstable.

**Diagnosis:**
```python
# Check page content manually
async def debug_page_content(page):
    try:
        # Wait longer for debugging
        await page.wait_for_load_state('networkidle', timeout=60000)
        content = await page.content()
        print(f"Page content length: {len(content)}")
        print(f"Contains case info: {'docket' in content.lower()}")
        return content
    except Exception as e:
        print(f"Page content error: {e}")
        return None
```

**Solution:**
```yaml
# Increase timeouts in stability_config.json
{
  "timeout_s": 60,        # Increase from 45
  "check_interval_s": 1.0, # Increase check interval
  "required_duration_s": 3.0
}
```

#### Issue: Case Details Extraction Failures

**Symptoms:**
```
Case processing returned None - HTML parsing failed
```

**Cause:** HTML structure changed or parser couldn't extract case details.

**Diagnosis:**
```python
# Debug HTML content and parsing
def debug_html_parsing(html_content, case_details):
    print(f"HTML content preview: {html_content[:500]}...")
    
    # Check for expected content
    docket_num = case_details.get('docket_num', '')
    if docket_num in html_content:
        print(f"✓ Docket number {docket_num} found in HTML")
    else:
        print(f"✗ Docket number {docket_num} NOT found in HTML")
    
    # Check HTMLCaseParser
    from src.pacer.html_case_parser import HTMLCaseParser
    parser = HTMLCaseParser(html_content)
    extracted = parser.extract_case_details()
    print(f"Extracted details: {extracted}")
```

**Solution:**
```python
# Save HTML for analysis when debugging
if config.get('debug_mode', False):
    debug_dir = Path('debug/html_content')
    debug_dir.mkdir(parents=True, exist_ok=True)
    
    debug_file = debug_dir / f"{case_details['docket_num'].replace(':', '_')}.html"
    with open(debug_file, 'w') as f:
        f.write(html_content)
```

### Verification Logic Issues

#### Issue: Failed Download Reprocessing Not Working

**Symptoms:**
Cases marked for reprocessing are still being skipped.

**Cause:** Verification logic not properly distinguishing explicitly requested cases.

**Diagnosis:**
```python
# Debug verification logic
def debug_verification(case_details):
    is_explicit = case_details.get('_is_explicitly_requested', False)
    print(f"Is explicitly requested: {is_explicit}")
    
    # Check what verification method would be used
    if is_explicit:
        print("Would use artifact-only verification")
        # Check for PDF/ZIP files only
    else:
        print("Would use full verification")
        # Check for all files including JSON
    
    # Show actual files in directory
    court_id = case_details['court_id']
    docket_num = case_details['docket_num']
    print(f"Checking files for {court_id} {docket_num}")
```

**Solution:**
```python
# Ensure _is_explicitly_requested flag is set correctly
initial_details = {
    'docket_num': '3:25-cv-12345',
    '_is_explicitly_requested': True  # Critical flag
}

# Verify flag propagates through workflow
result = await orchestrator.process_docket_case(page, initial_details)
assert result.get('_is_explicitly_requested', False) is True
```

#### Issue: ignore_download Matching Not Working

**Symptoms:**
Cases that should be ignored are being processed.

**Cause:** Case-insensitive matching or field comparison issues.

**Diagnosis:**
```python
# Debug ignore_download matching
def debug_ignore_matching(case_details, ignore_config):
    case_court = case_details.get('court_id', '').lower()
    case_firm = case_details.get('law_firm', '').lower()
    case_attorney = case_details.get('attorney_name', '').lower()
    
    print(f"Case: court={case_court}, firm={case_firm}, attorney={case_attorney}")
    
    for entry in ignore_config:
        entry_court = entry['court_id'].lower()
        entry_firm = entry['law_firm'].lower()
        entry_attorney = entry['attorney_name'].lower()
        
        print(f"Entry: court={entry_court}, firm={entry_firm}, attorney={entry_attorney}")
        
        court_match = case_court == entry_court
        firm_match = case_firm == entry_firm
        attorney_match = case_attorney == entry_attorney
        
        print(f"Matches: court={court_match}, firm={firm_match}, attorney={attorney_match}")
        
        if court_match and firm_match and attorney_match:
            print("✓ IGNORE MATCH FOUND")
            return True
    
    print("✗ No ignore match")
    return False
```

**Solution:**
```python
# Ensure all required fields are present and properly formatted
case_details = {
    'court_id': 'njd',  # Must match ignore_download.json
    'law_firm': 'Test Firm',  # Must match exactly (case-insensitive)
    'attorney_name': 'Test Attorney'  # Must match exactly (case-insensitive)
}
```

### File Operations Issues

#### Issue: S3 Upload Failures

**Symptoms:**
```
S3 upload error: NoCredentialsError: Unable to locate credentials
```

**Cause:** AWS credentials not configured or expired.

**Diagnosis:**
```bash
# Check AWS credentials
aws sts get-caller-identity

# Check AWS configuration
aws configure list

# Test S3 access
aws s3 ls s3://lexgenius-dockets/
```

**Solution:**
```bash
# Configure AWS credentials
aws configure set aws_access_key_id YOUR_ACCESS_KEY
aws configure set aws_secret_access_key YOUR_SECRET_KEY
aws configure set default.region us-east-1

# Or use environment variables
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
export AWS_DEFAULT_REGION=us-east-1

# Or use IAM roles if running on EC2
```

#### Issue: File Save Permissions

**Symptoms:**
```
PermissionError: [Errno 13] Permission denied: '/data/2025/06/14/njd'
```

**Cause:** Insufficient permissions to create directories or write files.

**Solution:**
```bash
# Check current permissions
ls -la data/

# Fix permissions
chmod -R 755 data/
chown -R $USER data/

# Or change data directory
export LEXGENIUS_DATA_DIR="$HOME/lexgenius_data"
mkdir -p "$LEXGENIUS_DATA_DIR"
```

#### Issue: Disk Space Issues

**Symptoms:**
```
OSError: [Errno 28] No space left on device
```

**Diagnosis:**
```bash
# Check disk space
df -h

# Check data directory size
du -sh data/

# Find largest files
find data/ -type f -size +100M -ls

# Check for temp files
find /tmp -name "*lexgenius*" -ls
```

**Solution:**
```bash
# Clean old data
find data/ -type f -mtime +30 -delete

# Clean temp files
rm -rf /tmp/downloads/*

# Configure cleanup in file operations service
cleanup_config = {
    'cleanup_temp_files': True,
    'max_file_cache_size': 1000,
    'cleanup_after_days': 7
}
```

### Download Issues

#### Issue: Document Download Failures

**Symptoms:**
```
Download failed: Connection timeout
```

**Cause:** Network issues, PACER server problems, or timeout too short.

**Diagnosis:**
```python
# Debug download context
def debug_download_context(case_details):
    context = {
        'docket_num': case_details['docket_num'],
        'base_filename': case_details['base_filename'],
        'context_download_path': case_details.get('context_download_path'),
        'download_timeout': case_details.get('download_timeout', 60)
    }
    print(f"Download context: {context}")
    
    # Check download path exists
    download_path = Path(context['context_download_path'])
    if download_path.exists():
        print(f"✓ Download path exists: {download_path}")
    else:
        print(f"✗ Download path missing: {download_path}")
```

**Solution:**
```yaml
# Increase download timeout
download_timeout: 120  # Increase from 60 seconds

# Configure retry logic
download_retries: 3
retry_delay: 5

# Enable debug logging
log_download_errors: true
save_failed_download_context: true
```

#### Issue: Staging Directory Problems

**Symptoms:**
```
Download staging error: Directory not found
```

**Cause:** Staging directory not created or permissions issues.

**Diagnosis:**
```python
# Check staging directory setup
def debug_staging_directory(download_result):
    staging_dir = download_result.get('staging_directory')
    if staging_dir:
        staging_path = Path(staging_dir)
        print(f"Staging directory: {staging_path}")
        print(f"Exists: {staging_path.exists()}")
        print(f"Is directory: {staging_path.is_dir()}")
        if staging_path.exists():
            files = list(staging_path.rglob('*'))
            print(f"Files in staging: {len(files)}")
            for f in files[:5]:  # Show first 5 files
                print(f"  {f}")
```

**Solution:**
```python
# Ensure staging directory is created with proper permissions
staging_dir = Path(context_download_path) / 'staging' / case_id
staging_dir.mkdir(parents=True, exist_ok=True)
staging_dir.chmod(0o755)
```

### Performance Issues

#### Issue: Slow Processing

**Symptoms:**
Processing takes significantly longer than expected.

**Diagnosis:**
```python
# Add timing to services
import time

def time_service_operations():
    start_time = time.time()
    
    # Service operation
    result = service.process_data(data)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"Service operation took {duration:.2f} seconds")
    
    # Log if too slow
    if duration > 5.0:
        print(f"WARNING: Slow operation detected")
```

**Solution:**
```python
# Optimize database queries
# Use connection pooling
# Enable parallel processing where safe
# Add caching for frequently accessed data

# Configuration optimizations
performance_config = {
    'run_parallel': True,
    'num_workers': 4,
    'cache_size': 1000,
    'batch_size': 50
}
```

#### Issue: Memory Leaks

**Symptoms:**
Memory usage continuously increases during processing.

**Diagnosis:**
```python
import tracemalloc

# Start memory tracing
tracemalloc.start()

# Process cases
for case in cases:
    process_case(case)
    
    # Check memory every 100 cases
    if case_count % 100 == 0:
        current, peak = tracemalloc.get_traced_memory()
        print(f"Memory usage: {current / 1024 / 1024:.1f} MB")
```

**Solution:**
```python
# Clear references explicitly
def process_case_with_cleanup(case_data):
    try:
        result = process_case(case_data)
        return result
    finally:
        # Clean up large objects
        if 'html_content' in locals():
            del html_content
        if 'page_content' in locals():
            del page_content
        
        # Force garbage collection periodically
        import gc
        if case_count % 100 == 0:
            gc.collect()
```

## Debugging Tools

### Enable Debug Logging

```python
import logging

# Set up debug logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log'),
        logging.StreamHandler()
    ]
)

# Enable service-specific debug logging
logger = logging.getLogger('PacerServices')
logger.setLevel(logging.DEBUG)
```

### Configuration Debugging

```python
def debug_service_configuration():
    """Debug service configuration and initialization"""
    
    # Check configuration service
    try:
        config_service = PacerConfigurationService(config)
        print("✓ Configuration service initialized")
        
        # Check each config file
        configs = {
            'relevance': config_service.get_relevance_config(),
            'ignore_download': config_service.get_ignore_download_config(),
            'defendants': config_service.get_relevant_defendants()
        }
        
        for name, config_data in configs.items():
            if config_data:
                print(f"✓ {name} config loaded: {len(config_data)} entries")
            else:
                print(f"✗ {name} config empty or failed to load")
                
    except Exception as e:
        print(f"✗ Configuration service failed: {e}")
```

### Service Health Check

```python
def check_service_health():
    """Comprehensive service health check"""
    
    health_status = {
        'configuration': False,
        'file_operations': False,
        'database': False,
        's3_storage': False
    }
    
    # Test configuration service
    try:
        config_service = PacerConfigurationService(config)
        health_status['configuration'] = config_service.validate_configuration_completeness()
    except Exception as e:
        print(f"Configuration service error: {e}")
    
    # Test file operations
    try:
        file_ops = PacerFileOperationsService(
            logger=logging.getLogger('test'), config=config, s3_storage=None
        )
        test_dir = file_ops.create_court_date_directory('test', '06/14/25')
        health_status['file_operations'] = test_dir.exists()
    except Exception as e:
        print(f"File operations error: {e}")
    
    # Test database connectivity
    try:
        # Test database connection
        result = pacer_repository.check_docket_exists('test', '3:25-cv-00001')
        health_status['database'] = True
    except Exception as e:
        print(f"Database error: {e}")
    
    # Test S3 connectivity
    try:
        # Test S3 connection
        s3_storage.upload_file_content(b'test', 'test/health_check.txt')
        health_status['s3_storage'] = True
    except Exception as e:
        print(f"S3 storage error: {e}")
    
    return health_status
```

### Case Processing Debugger

```python
def debug_case_processing(case_details):
    """Debug individual case processing"""
    
    print(f"Debugging case: {case_details.get('docket_num', 'UNKNOWN')}")
    
    # Check required fields
    required_fields = ['docket_num', 'court_id']
    for field in required_fields:
        if field in case_details:
            print(f"✓ {field}: {case_details[field]}")
        else:
            print(f"✗ Missing {field}")
    
    # Check relevance determination
    relevance_service = PacerRelevanceService(config, court_id, config_service)
    relevance_result = relevance_service.determine_case_relevance(case_details)
    print(f"Relevance: {relevance_result}")
    
    # Check verification logic
    verification_service = PacerCaseVerificationService(config, court_id, pacer_repo, file_manager, end_date)
    verification_result = await verification_service.verify_case(case_details)
    print(f"Verification: {verification_result}")
```

## Log Analysis

### Common Log Patterns

#### Successful Processing
```
INFO - PacerCaseProcessingService - Case processing completed: 3:25-cv-12345
INFO - PacerRelevanceService - Case determined relevant: MDL flag
INFO - PacerCaseVerificationService - Case verification passed
INFO - PacerDownloadOrchestrationService - Download completed successfully
INFO - PacerFileOperationsService - Case saved and uploaded
```

#### Failed Processing
```
ERROR - PacerCaseProcessingService - HTML processing failed: Timeout
ERROR - PacerDownloadOrchestrationService - Download failed: Connection timeout
WARNING - PacerCaseVerificationService - Database check failed, continuing
```

#### Configuration Issues
```
CRITICAL - PacerConfigurationService - ignore_download.json required but missing
ERROR - PacerConfigurationService - Invalid JSON in relevance_config.json
WARNING - PacerFileOperationsService - S3 upload failed, saved locally only
```

### Log Analysis Scripts

```bash
# Count processing success/failure
grep "Case processing completed" logs/pacer.log | wc -l
grep "processing failed" logs/pacer.log | wc -l

# Find most common errors
grep "ERROR" logs/pacer.log | cut -d'-' -f4- | sort | uniq -c | sort -nr

# Check processing times
grep "Processing time" logs/pacer.log | awk '{print $NF}' | sort -n

# Monitor real-time processing
tail -f logs/pacer.log | grep -E "(completed|failed|ERROR)"
```

## Emergency Procedures

### Service Recovery

```bash
# 1. Stop all processing
pkill -f "python.*main.py"

# 2. Check system resources
df -h
free -h
ps aux | grep python

# 3. Clear temporary files
rm -rf /tmp/downloads/*
rm -rf /tmp/staging/*

# 4. Restart with minimal configuration
python src/main.py --params config/scrape_minimal.yml

# 5. Monitor for stability
timeout 300 tail -f logs/pacer.log
```

### Rollback to Legacy

```bash
# 1. Stop new services
pkill -f "python.*main.py.*new"

# 2. Switch configuration
cp config/scrape_legacy.yml config/scrape_active.yml

# 3. Restart with legacy processor
python src/main.py --params config/scrape_active.yml

# 4. Verify legacy operation
grep "DocketProcessor" logs/pacer.log | tail -5
```

### Data Recovery

```bash
# 1. Check for partial data
find data/ -name "*.json" -size 0 -delete  # Remove empty files
find data/ -name "*.tmp" -delete           # Remove temp files

# 2. Verify data integrity
python scripts/verify_data_integrity.py data/2025/06/14/

# 3. Recover from S3 if needed
aws s3 sync s3://lexgenius-dockets/pacer/2025/06/14/ data/2025/06/14/

# 4. Restart processing for missing cases
python scripts/identify_missing_cases.py --date 06/14/25
```

This troubleshooting guide provides comprehensive solutions for common issues and debugging tools for the PACER service architecture.
