# PACER Migration Guide

This guide provides step-by-step instructions for migrating from the legacy monolithic DocketProcessor to the new service-oriented architecture.

## Migration Overview

The migration is designed to be **zero-downtime** with **full backward compatibility**. The legacy DocketProcessor remains completely unchanged and functional, allowing for gradual migration and instant rollback if needed.

## Migration Strategy

### Phase-Based Approach

The migration follows a three-phase approach:

1. **Phase 1: Testing** - Single court validation
2. **Phase 2: Expansion** - Multi-court rollout  
3. **Phase 3: Production** - Full deployment

### Safety Mechanisms

- **Feature Flag Control** - Instant enable/disable capability
- **Legacy Preservation** - Original processor unchanged
- **Rollback Support** - Immediate fallback to legacy system
- **Gradual Rollout** - Court-by-court migration control

## Pre-Migration Checklist

### Environment Validation

```bash
# 1. Verify Python environment
python --version  # Should be 3.9+

# 2. Check required dependencies
pip list | grep -E "(pytest|playwright|boto3|asyncio)"

# 3. Validate AWS connectivity
aws s3 ls  # Should list S3 buckets
aws dynamodb list-tables  # Should list DynamoDB tables

# 4. Check PACER credentials
echo $PACER_USERNAME
echo $PACER_PASSWORD
```

### Configuration File Validation

```bash
# 1. Verify configuration files exist
ls -la src/config/pacer/
ls -la src/config/pacer/ignore_download/ignore_download.json
ls -la src/config/pacer/defendants/relevant_defendants.json

# 2. Validate JSON syntax
python -m json.tool src/config/pacer/ignore_download/ignore_download.json
python -m json.tool src/config/pacer/relevance_config.json

# 3. Check configuration completeness
python -c "
from src.services.pacer.configuration_service import PacerConfigurationService
config = {'use_docket_report_log': False}
service = PacerConfigurationService(config)
print('Configuration validation:', service.validate_configuration_completeness())
"
```

### Test Suite Execution

```bash
# 1. Run all PACER service tests
pytest tests/unit/services/pacer/ -v --tb=short

# 2. Verify test coverage
pytest tests/unit/services/pacer/ --cov=src/services/pacer --cov-report=term-missing

# 3. Run integration tests
pytest tests/integration/pacer/ -v

# Expected Results:
# - 452 unit tests passing
# - 95%+ code coverage
# - All integration tests passing
```

## Phase 1: Testing (Single Court)

### Objective
Validate new services with a single court to ensure functionality and identify any issues.

### Configuration

Create test configuration file:

```yaml
# config/scrape_test_new_services.yml
date: "06/14/25"
scraper: true
post_process: true
upload: false  # Disable S3 uploads for testing
report_generator: false
fb_ads: false

# New service architecture settings
use_new_docket_services: true

# Single court testing
process_single_court: ['njd']  # Start with NJ District Court
run_parallel: false           # Sequential processing for easier debugging

# Safety settings
use_docket_report_log: false  # Disable critical exit behavior for testing
headless: true
num_workers: 1

# Debugging settings (optional)
debug_mode: true
log_level: "INFO"
```

### Execution

```bash
# 1. Run single court test
python src/main.py --params config/scrape_test_new_services.yml

# 2. Monitor execution
tail -f logs/pacer_processing.log

# 3. Verify results
ls -la data/2025/06/14/njd/
```

### Validation Steps

#### 1. Service Initialization
```bash
# Check that all services initialize properly
grep "Service initialized" logs/pacer_processing.log

# Expected output:
# PacerConfigurationService initialized
# PacerFileOperationsService initialized  
# PacerCaseProcessingService initialized
# PacerRelevanceService initialized
# PacerCaseClassificationService initialized
# PacerCaseVerificationService initialized
# PacerDownloadOrchestrationService initialized
# PacerDocketProcessingOrchestratorService initialized
```

#### 2. Case Processing
```bash
# Verify cases are being processed
grep "Processing case" logs/pacer_processing.log | head -5

# Check for successful completions
grep "Case processing completed" logs/pacer_processing.log | wc -l
```

#### 3. File Operations
```bash
# Verify JSON files are created
find data/2025/06/14/njd/ -name "*.json" | head -5

# Check file content structure
python -c "
import json
import glob
files = glob.glob('data/2025/06/14/njd/*.json')
if files:
    with open(files[0]) as f:
        data = json.load(f)
    print('Required fields present:')
    for field in ['docket_num', 'court_id', '_orchestrated_by']:
        print(f'  {field}: {field in data}')
"
```

#### 4. Compare with Legacy
Run the same test with legacy processor:

```yaml
# config/scrape_test_legacy.yml
# Same configuration but with:
use_new_docket_services: false
```

```bash
# Run legacy version
python src/main.py --params config/scrape_test_legacy.yml

# Compare results
python scripts/compare_processing_results.py \
  data/2025/06/14/njd/ \
  data_legacy/2025/06/14/njd/
```

### Phase 1 Success Criteria

- [ ] All services initialize without errors
- [ ] Cases are processed successfully  
- [ ] JSON files are created with correct structure
- [ ] No critical errors in logs
- [ ] Results comparable to legacy processor
- [ ] Processing time within acceptable range (< 2x legacy)

### Troubleshooting Phase 1

#### Common Issues

**Service Initialization Failures**
```bash
# Check configuration file syntax
python -m json.tool config/scrape_test_new_services.yml

# Verify dependencies
python -c "
from src.services.pacer.configuration_service import PacerConfigurationService
print('Configuration service imports successfully')
"
```

**Configuration Loading Errors**
```bash
# Check config directory permissions
ls -la src/config/pacer/
chmod -R 755 src/config/pacer/

# Validate JSON files
for file in src/config/pacer/*.json; do
    echo "Checking $file"
    python -m json.tool "$file" > /dev/null && echo "  ✓ Valid" || echo "  ✗ Invalid"
done
```

**Processing Errors**
```bash
# Check detailed error logs
grep -A 5 -B 5 "ERROR" logs/pacer_processing.log

# Verify test data
python scripts/validate_test_environment.py
```

## Phase 2: Expansion (Multi-Court)

### Objective
Expand testing to multiple courts to validate scalability and cross-court consistency.

### Configuration

```yaml
# config/scrape_test_multi_court.yml
date: "06/14/25"
scraper: true
post_process: true
upload: false  # Still disable uploads

# New service architecture
use_new_docket_services: true

# Multi-court testing
process_single_court: ['njd', 'cand', 'nysd']  # Expand to 3 courts
run_parallel: false  # Still sequential for easier monitoring

# Enable more production-like settings
use_docket_report_log: true  # Enable critical validation
headless: true
num_workers: 1
```

### Execution Strategy

#### Sequential Court Processing
```bash
# Process courts one at a time for easier monitoring
for court in njd cand nysd; do
    echo "Processing court: $court"
    
    # Create court-specific config
    sed "s/\['njd', 'cand', 'nysd'\]/\['$court'\]/" \
        config/scrape_test_multi_court.yml > \
        config/scrape_test_$court.yml
    
    # Run processing
    python src/main.py --params config/scrape_test_$court.yml
    
    # Validate results
    echo "Results for $court:"
    ls -la data/2025/06/14/$court/ | wc -l
    
    # Brief pause between courts
    sleep 10
done
```

#### Parallel Court Processing Test
```yaml
# config/scrape_test_parallel.yml
# Same as multi-court but with:
run_parallel: true
num_workers: 3
```

### Validation Steps

#### 1. Cross-Court Consistency
```bash
# Verify all courts processed
for court in njd cand nysd; do
    count=$(find data/2025/06/14/$court/ -name "*.json" | wc -l)
    echo "$court: $count cases processed"
done

# Check for court-specific differences
python scripts/analyze_court_differences.py data/2025/06/14/
```

#### 2. Performance Analysis
```bash
# Extract timing information
grep "Processing time" logs/pacer_processing.log | \
    awk '{print $4}' | \
    python scripts/calculate_performance_stats.py

# Compare with legacy performance
python scripts/compare_performance.py \
    logs/pacer_processing.log \
    logs/legacy_processing.log
```

#### 3. Error Rate Analysis
```bash
# Count errors by court
for court in njd cand nysd; do
    errors=$(grep "$court.*ERROR" logs/pacer_processing.log | wc -l)
    total=$(grep "$court.*Processing case" logs/pacer_processing.log | wc -l)
    if [ $total -gt 0 ]; then
        rate=$(echo "scale=2; $errors * 100 / $total" | bc)
        echo "$court: $errors errors out of $total cases ($rate%)"
    fi
done
```

### Phase 2 Success Criteria

- [ ] All courts process successfully
- [ ] Error rate < 1% per court
- [ ] Performance within 2x of legacy
- [ ] No memory leaks or resource issues
- [ ] Consistent results across courts
- [ ] Parallel processing works correctly

## Phase 3: Production Deployment

### Objective
Deploy to full production with all courts and production settings.

### Pre-Production Validation

```bash
# 1. Final test run with production-like settings
# config/scrape_pre_production.yml
use_new_docket_services: true
process_single_court: []  # All courts
run_parallel: true
upload: true              # Enable S3 uploads
use_docket_report_log: true
num_workers: 4

# 2. Run pre-production test
python src/main.py --params config/scrape_pre_production.yml

# 3. Monitor for 30 minutes minimum
timeout 1800 tail -f logs/pacer_processing.log

# 4. Validate S3 uploads
aws s3 ls s3://lexgenius-dockets/pacer/2025/06/14/ --recursive | head -10
```

### Production Configuration

```yaml
# config/scrape_production_new.yml
date: "06/14/25"
scraper: true
post_process: true
upload: true
report_generator: true

# Production new service settings
use_new_docket_services: true

# Full production settings
process_single_court: []  # All courts
run_parallel: true
use_docket_report_log: true
headless: true
num_workers: 4

# Production optimizations
cleanup_temp_files: true
max_file_cache_size: 1000
download_timeout: 60
```

### Deployment Process

#### 1. Gradual Rollout
```bash
# Option A: Percentage-based rollout
# Start with 10% of courts
total_courts=$(python -c "from src.config.court_list import COURTS; print(len(COURTS))")
subset_size=$((total_courts / 10))

python scripts/create_court_subset.py --size $subset_size > config/court_subset_10pct.yml

# Process subset
python src/main.py --params config/scrape_production_subset.yml
```

#### 2. Blue-Green Deployment
```bash
# Run both new and legacy in parallel for comparison
# Terminal 1: New services
python src/main.py --params config/scrape_production_new.yml &
NEW_PID=$!

# Terminal 2: Legacy services  
python src/main.py --params config/scrape_production_legacy.yml &
LEGACY_PID=$!

# Monitor both
watch 'ps aux | grep -E "(python.*main.py|$NEW_PID|$LEGACY_PID)"'

# Compare results after completion
python scripts/compare_production_results.py
```

#### 3. Full Deployment
```bash
# Deploy to production with monitoring
python src/main.py --params config/scrape_production_new.yml 2>&1 | \
    tee logs/production_deployment.log

# Monitor key metrics
python scripts/monitor_production_deployment.py &
MONITOR_PID=$!

# Set up alerts
python scripts/setup_deployment_alerts.py
```

### Production Monitoring

#### Real-Time Monitoring
```bash
# 1. Processing rate
watch 'grep "Case processing completed" logs/production_deployment.log | wc -l'

# 2. Error monitoring
watch 'grep -E "(ERROR|CRITICAL)" logs/production_deployment.log | tail -5'

# 3. Resource usage
watch 'ps aux | grep python | grep main.py | awk "{print \$3,\$4,\$6}"'

# 4. S3 upload monitoring
watch 'aws s3 ls s3://lexgenius-dockets/pacer/2025/06/14/ --recursive | wc -l'
```

#### Health Checks
```bash
# Create health check script
cat > scripts/health_check.py << 'EOF'
#!/usr/bin/env python3
import json
import subprocess
import sys
from pathlib import Path

def check_service_health():
    """Check health of PACER services"""
    checks = {
        'log_errors': 0,
        'files_created': 0,
        's3_uploads': 0,
        'processing_rate': 0
    }
    
    # Check for recent errors
    result = subprocess.run(['grep', '-c', 'ERROR', 'logs/production_deployment.log'], 
                          capture_output=True, text=True)
    checks['log_errors'] = int(result.stdout.strip()) if result.returncode == 0 else 0
    
    # Check files created in last hour
    files = list(Path('data/2025/06/14').rglob('*.json'))
    recent_files = [f for f in files if f.stat().st_mtime > time.time() - 3600]
    checks['files_created'] = len(recent_files)
    
    # More health checks...
    
    return checks

if __name__ == '__main__':
    health = check_service_health()
    print(json.dumps(health, indent=2))
    
    # Exit with error if unhealthy
    if health['log_errors'] > 10 or health['files_created'] == 0:
        sys.exit(1)
EOF

# Run health checks
python scripts/health_check.py
```

### Rollback Procedures

#### Immediate Rollback
```bash
# 1. Stop current processing
pkill -f "python.*main.py.*scrape_production_new"

# 2. Switch to legacy configuration
cp config/scrape_production_legacy.yml config/scrape_production_active.yml

# 3. Restart with legacy services
python src/main.py --params config/scrape_production_active.yml &

# 4. Verify rollback
sleep 30
python scripts/verify_rollback_success.py
```

#### Gradual Rollback
```bash
# 1. Reduce new service usage gradually
sed -i 's/process_single_court: \[\]/process_single_court: ["problematic_court"]/' \
    config/scrape_production_new.yml

# 2. Process specific courts with legacy
python src/main.py --params config/scrape_production_legacy_subset.yml

# 3. Monitor and adjust as needed
```

### Phase 3 Success Criteria

- [ ] All courts process successfully at production scale
- [ ] Error rate < 0.5% across all courts
- [ ] Performance equal to or better than legacy
- [ ] S3 uploads working correctly
- [ ] No resource leaks or stability issues
- [ ] Monitoring and alerting functional
- [ ] Rollback procedures tested and working

## Post-Migration Tasks

### 1. Documentation Updates
```bash
# Update operational documentation
git add docs/pacer/
git commit -m "docs: update PACER architecture documentation post-migration"

# Update configuration examples
cp config/scrape_production_new.yml config/scrape.yml
git add config/scrape.yml
git commit -m "config: update default configuration to use new services"
```

### 2. Legacy Code Deprecation Plan
```bash
# Create deprecation timeline
cat > DEPRECATION_PLAN.md << 'EOF'
# Legacy DocketProcessor Deprecation Plan

## Timeline
- Month 1-2: Monitor new services in production
- Month 3: Mark legacy processor as deprecated
- Month 6: Remove legacy processor from active use
- Month 12: Remove legacy processor code

## Steps
1. Add deprecation warnings to legacy processor
2. Update documentation to recommend new services
3. Remove legacy processor from default configurations
4. Archive legacy processor code
EOF
```

### 3. Performance Baseline Establishment
```bash
# Collect performance baseline data
python scripts/collect_performance_baseline.py --days 30

# Create performance dashboard
python scripts/create_performance_dashboard.py

# Set up automated performance monitoring
python scripts/setup_performance_monitoring.py
```

### 4. Training and Knowledge Transfer
```bash
# Create operational runbooks
python scripts/generate_operational_runbooks.py

# Update team documentation
git add docs/operations/
git commit -m "docs: add new service operational procedures"
```

## Migration Validation

### Final Validation Checklist

- [ ] **Functionality**: All original functionality preserved
- [ ] **Performance**: Performance equal to or better than legacy
- [ ] **Reliability**: Error rates within acceptable limits
- [ ] **Scalability**: Handles production load successfully
- [ ] **Monitoring**: Full observability and alerting in place
- [ ] **Documentation**: Complete and up-to-date documentation
- [ ] **Training**: Team trained on new architecture
- [ ] **Rollback**: Rollback procedures tested and ready

### Success Metrics

| Metric | Target | Measurement |
|--------|--------|-------------|
| **Error Rate** | < 0.5% | Errors per processed case |
| **Performance** | ≤ 1.2x legacy | Average processing time per case |
| **Uptime** | > 99.9% | Service availability |
| **Memory Usage** | ≤ legacy + 10% | Peak memory consumption |
| **Test Coverage** | > 95% | Code coverage percentage |
| **Documentation** | 100% | All services documented |

### Long-Term Monitoring

```bash
# Set up weekly reports
cat > scripts/weekly_migration_report.py << 'EOF'
#!/usr/bin/env python3
"""Generate weekly migration success report"""

def generate_weekly_report():
    # Collect metrics from last week
    # Compare with baseline
    # Generate report
    pass

if __name__ == '__main__':
    generate_weekly_report()
EOF

# Schedule weekly reports
echo "0 9 * * MON python scripts/weekly_migration_report.py" | crontab -
```

This migration guide provides a comprehensive, safe approach to transitioning from the legacy monolithic architecture to the new service-oriented architecture while maintaining full operational safety and the ability to rollback at any point.
