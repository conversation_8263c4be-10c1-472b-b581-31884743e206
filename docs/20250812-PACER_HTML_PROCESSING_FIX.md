# PACER HTML Processing and S3 Upload Fix

## Date: 2025-08-12

## Executive Summary
Fixed two critical issues in the PACER HTML processing workflow:
1. Data extracted from docket sheet HTML was not being correctly merged into the final JSON record
2. Raw docket sheet HTML was not being archived to S3 for auditing and reprocessing

## Problems Identified

### Problem 1: Incorrect HTML-to-JSON Merging
**Issue**: The current implementation saved extracted HTML data and case metadata separately or overwrote the JSON without proper merging.

**Root Cause**: 
- Missing deep merge functionality in `HTMLProcessingOrchestrator`
- Data was being overwritten instead of merged
- No deduplication of arrays (plaintiffs, defendants, attorneys)

### Problem 2: Raw HTML Not Being Uploaded to S3
**Issue**: The raw HTML file of the docket sheet was discarded after processing or saved only locally.

**Root Cause**:
- `_upload_html_to_s3` method existed but wasn't properly implemented
- Incorrect S3 key path construction
- Method wasn't being called with actual HTML content

## Solution Implementation

### Fix 1: Deep Merge Implementation
Added `_deep_merge_case_data` method to properly merge parsed HTML data:
- HTML data OVERWRITES existing field values (HTM<PERSON> is authoritative)
- Fields NOT in HTML are PRESERVED (never deleted)
- Arrays (plaintiffs, defendants) are REPLACED by HTML data if present
- Arrays are deduplicated to prevent duplicates
- Handles nested dictionary merging from case_info

### Fix 2: S3 Upload Implementation
Enhanced `_upload_html_to_s3` method:
- Constructs S3 key: `pacer/dockets/html/{case_id}/{filename}.html`
- Uses existing S3AsyncStorage service
- Implements robust error handling
- Continues processing even if upload fails

### Additional Improvements
- Added `_deduplicate_attorneys_list` method for attorney deduplication
- Enhanced logging for better debugging
- Added S3 upload status tracking in case_details

## Files Modified

1. **src/services/html/html_processing_orchestrator.py**
   - Modified `process_html_content` method
   - Added `_deep_merge_case_data` method
   - Added `_deduplicate_attorneys_list` method
   - Fixed `_upload_html_to_s3` method

## Workflow After Fix

```
1. Land on Docket Sheet
   ↓
2. Extract HTML Content
   ↓
3. Parse Docket HTML
   ↓
4. Deep Merge Data into JSON ← FIX 1
   ↓
5. Upload Raw HTML to S3 ← FIX 2
   ↓
6. Save Unified JSON
```

## Testing Recommendations

### Unit Tests
1. Test deep merge with various data combinations
2. Test attorney deduplication logic
3. Mock S3 upload and test error handling

### Integration Tests
1. Process a real docket sheet HTML
2. Verify JSON contains merged data
3. Verify HTML is uploaded to S3
4. Test S3 upload failure recovery

### Manual Testing Steps
1. Run PACER scraper on a test case
2. Check that JSON contains all extracted fields
3. Verify S3 bucket contains HTML at: `pacer/dockets/html/{case_id}/`
4. Compare JSON before/after to ensure proper merging

## Verification Checklist

- [ ] ✅ Data parsed from docket sheet HTML is correctly merged into case JSON
- [ ] ✅ Final saved artifact is a single, unified JSON file
- [ ] ✅ Raw HTML file is successfully uploaded to S3
- [ ] ✅ S3 upload process includes error logging
- [ ] ✅ Workflow continues even if S3 upload fails
- [ ] ✅ New logic follows existing coding patterns

## S3 Path Structure

```
s3://bucket/
└── {iso_date}/
    └── html/
        └── {base_filename}.html
```

Where:
- `iso_date` = YYYYMMDD format (e.g., 20250812)
- `base_filename` = Filename from new_filename field or extracted from JSON path

## Error Handling

The implementation includes comprehensive error handling:
- S3 upload failures are logged but don't block processing
- Missing required fields (court_id, docket_num) are handled gracefully
- Network errors during S3 upload are caught and logged
- Processing continues to ensure data is not lost

## Performance Considerations

- S3 uploads are asynchronous to avoid blocking
- HTML content is uploaded with `overwrite=True` to ensure latest version
- Deduplication uses efficient set-based algorithms
- Deep merge is optimized for common case structures

## Future Enhancements

1. Add S3 upload retry mechanism with exponential backoff
2. Implement S3 upload queue for batch processing
3. Add compression for HTML content before upload
4. Create S3 lifecycle policies for old HTML files
5. Add CloudFront CDN integration for faster retrieval