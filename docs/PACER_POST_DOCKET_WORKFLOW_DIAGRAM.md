# PACER Post-Docket Sheet Processing Workflow

## Overview
This diagram details the complete workflow from landing on the docket sheet to the end of processing, including all decision points and error handling.

## Workflow Diagram

```mermaid
flowchart TD
    Start([Land on Docket Sheet]) --> ExtractHTML[Step 7: Extract HTML Content<br/>navigator.page.content]
    
    ExtractHTML -->|Success| ParseDocket[Step 8: Parse Docket HTML<br/>docket_processor.process_docket]
    ExtractHTML -->|ERROR: Page navigating| NavigationError[Navigation Error<br/>Retry with wait]
    
    NavigationError --> WaitStable[Wait for page stability<br/>networkidle/domcontentloaded]
    WaitStable --> ExtractHTML
    
    ParseDocket --> ExtractCase[Extract Case Details<br/>- versus<br/>- filing_date<br/>- docket_num<br/>- court_id]
    
    ExtractCase --> ValidateFields{Validate Required Fields}
    ValidateFields -->|Missing fields| ValidationError[Log validation error<br/>Skip to next docket]
    ValidateFields -->|Valid| RelevanceCheck
    
    %% RELEVANCE CHECK
    RelevanceCheck[Step 9: Relevance Check<br/>relevance_service.check_relevance] --> IsRelevant{Is Relevant?}
    
    IsRelevant -->|No| SaveReview[Save to Process Review Cases<br/>file_operations.save_case_data]
    SaveReview --> NextRecord1[Process Next Docket]
    
    IsRelevant -->|Yes| TransferCheck[Step 10: Transfer Check<br/>Check is_transferred flag]
    
    %% TRANSFER CHECK
    TransferCheck --> IsTransferred{Is Transferred?}
    
    IsTransferred -->|Yes| CheckTransferor[Check Transferor in DynamoDB<br/>pacer_repository.check_docket_exists]
    IsTransferred -->|No| IgnoreCheck
    
    CheckTransferor --> TransferorExists{Transferor Exists?}
    TransferorExists -->|Yes| SaveTransferJSON[Save Transfer JSON Locally<br/>Skip S3 upload]
    SaveTransferJSON --> NextRecord2[Process Next Docket]
    TransferorExists -->|No| IgnoreCheck
    
    %% IGNORE DOWNLOAD CHECK
    IgnoreCheck[Step 11: Ignore Download Service<br/>ignore_download_service.check_ignore]
    
    IgnoreCheck --> MatchFound{Match Found?}
    
    MatchFound -->|Yes| CheckHTMLOnly{html_only: true?}
    MatchFound -->|No| DownloadWorkflow
    
    CheckHTMLOnly -->|Yes| InheritMDL[Inherit MDL Number<br/>Save JSON]
    InheritMDL --> NextRecord3[Process Next Docket]
    CheckHTMLOnly -->|No| DownloadWorkflow
    
    %% DOWNLOAD WORKFLOW
    DownloadWorkflow[Step 12: Execute Download Workflow<br/>download_orchestrator.execute_download_workflow]
    
    DownloadWorkflow --> ClassifyCase[Classify Case<br/>- Social Security check<br/>- Criminal check<br/>- Civil rights check]
    
    ClassifyCase --> RequiresDownload{Requires Download?}
    
    RequiresDownload -->|No| SaveCaseNoDownload[Save Case JSON<br/>No documents downloaded]
    SaveCaseNoDownload --> NextRecord4[Process Next Docket]
    
    RequiresDownload -->|Yes| NavigateDocs[Navigate to Documents Page]
    
    NavigateDocs --> DownloadDocs[Download Documents<br/>- Complaint<br/>- Docket entries<br/>- PDFs]
    
    DownloadDocs --> SaveWithDocs[Save Case JSON with Documents<br/>- Local storage<br/>- S3 upload]
    
    SaveWithDocs --> FinalCleanup[Cleanup & Finalize<br/>- Remove temp fields<br/>- Update status]
    
    FinalCleanup --> NextRecord5[Process Next Docket]
    
    %% End states
    NextRecord1 --> End([End/Continue to Next])
    NextRecord2 --> End
    NextRecord3 --> End
    NextRecord4 --> End
    NextRecord5 --> End
    ValidationError --> End
```

## Detailed Step Descriptions

### Step 7: Extract HTML Content
- **Action**: `await navigator.page.content()`
- **Error Case**: "Unable to retrieve content because page is navigating"
- **Recovery**: Wait for page stability using networkidle or domcontentloaded

### Step 8: Parse Docket HTML
- **Component**: `docket_processor.process_docket()`
- **Extracts**:
  - Case title (versus field)
  - Filing date
  - Docket number
  - Court ID
  - Parties information
  - All docket fields/entries.

### Step 9: Relevance Check
- **Component**: `relevance_service.check_relevance()`
- **Decision Point**: Is the case relevant for processing?
- **If Not Relevant**: Save to review queue and skip

### Step 10: Transfer Check
- **Check**: `is_transferred` flag
- **If Transferred**: 
  - Check if transferor exists in DynamoDB
  - If exists, save locally only (no S3) and skip

### Step 11: Ignore Download Service
- **Component**: `ignore_download_service.check_ignore()`
- **Checks**: MDL patterns, specific case types
- **If Match with `html_only: true`**:
  - Inherit MDL number
  - Save JSON
  - Skip download

### Step 12: Download Workflow
- **Component**: `download_orchestrator.execute_download_workflow()`
- **Sub-steps**:
  1. Classification (Social Security, Criminal, Civil Rights)
  2. Determine if download required
  3. Navigate to documents page if needed
  4. Download documents (Complaint, PDFs)
  5. Save complete case data with documents

## Error Handling Points

1. **Navigation Errors**: Retry with stability waits
2. **Validation Errors**: Log and skip to next
3. **DynamoDB Errors**: Handle connection issues
4. **Download Errors**: Partial save and continue
5. **S3 Upload Errors**: Save locally and log

## Data Flow

```yaml
Input: Docket Sheet HTML Page
↓
Case Details Extraction:
  - versus: "Plaintiff v. Defendant"
  - filing_date: "2025-01-15"
  - docket_num: "1:25-cv-09395"
  - court_id: "ilnd"
↓
Enrichment & Classification:
  - relevance_score: 0.85
  - is_transferred: false
  - requires_download: true
  - mdl_num: null
↓
Output: Complete Case JSON
  - Local: data/{iso_date}/dockets/{filename}.json
  - S3: s3://bucket/{iso_date}/{court_id}/{filename}.json
```

## Current Issue

The workflow is failing at **Step 7** with:
```
Page.content: Unable to retrieve content because the page is navigating and changing the content
```

### Root Cause
The page is still navigating/loading after clicking "Run Report" button, and the code attempts to extract HTML content before the page has stabilized.

### Solution Needed
Add proper wait conditions after navigation:
```python
# Wait for navigation to complete
await navigator.page.wait_for_load_state("networkidle")
# Or wait for specific element
await navigator.page.wait_for_selector("table.docket-table", state="visible")
# Then extract content
html_content = await navigator.page.content()
```