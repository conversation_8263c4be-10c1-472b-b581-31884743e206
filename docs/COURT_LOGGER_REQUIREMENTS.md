# Court Logger Requirements for PACER Components

## Overview
All PACER workflow components MUST use court-specific loggers that write to `logs/pacer/{court_id}.log` to ensure proper logging isolation and debugging capabilities.

## Mandatory Requirements

### 1. Component Inheritance
ALL components MUST inherit from one of these base classes:
- `AsyncServiceBase` - For services with lifecycle management
- `ComponentImplementation` - For utility components and processors

```python
from src.infrastructure.patterns.component_base import AsyncServiceBase, ComponentImplementation

class MyPacerComponent(AsyncServiceBase):  # or ComponentImplementation
    def __init__(self, logger: LoggerProtocol, config: dict = None):
        super().__init__(logger, config)
```

### 2. Logger Injection
Components MUST accept a logger via dependency injection:
- **NEVER** create your own logger with `logging.getLogger()`
- **ALWAYS** use the injected logger from the base class
- **NEVER** override `self.logger` after initialization

```python
# ✅ CORRECT
class CaseParserService(ComponentImplementation):
    def __init__(self, logger: LoggerProtocol, html_content: str = None):
        super().__init__(logger)  # Base class handles logger
        # Use inherited logging methods
        self.log_info("Component initialized")

# ❌ INCORRECT
class BadComponent:
    def __init__(self, logger: LoggerProtocol):
        self.logger = logging.getLogger(__name__)  # DON'T DO THIS
```

### 3. Standardized Logging Methods
Use the inherited logging methods from the base class:

```python
# ✅ Use these standardized methods
self.log_debug("Debug message")
self.log_info("Info message")
self.log_warning("Warning message")
self.log_error("Error message", exc_info=True)
self.log_exception("Exception occurred")

# ❌ Don't use these
self.logger.debug("message")  # Bypasses standardization
print("Debug info")          # Never use print statements
```

### 4. Court Logger Context
Components that need court-specific logging should use the court logger context:

```python
# For components processing court-specific data
with self.create_court_logging_context(court_id, iso_date) as court_logger:
    # All logs within this block go to court-specific file
    self.process_court_data()
```

## Implementation Examples

### Service Component Example
```python
from src.infrastructure.patterns.component_base import AsyncServiceBase

class MyPacerService(AsyncServiceBase):
    def __init__(self, logger: LoggerProtocol, config: dict = None):
        super().__init__(logger, config)
        
    async def _execute_action(self, data):
        self.log_info("Processing PACER data")
        try:
            # Process data
            result = self.process_data(data)
            self.log_info("Processing completed successfully")
            return result
        except Exception as e:
            self.log_error(f"Processing failed: {e}", exc_info=True)
            raise
```

### Utility Component Example
```python
from src.infrastructure.patterns.component_base import ComponentImplementation

class FilePathBuilder(ComponentImplementation):
    def __init__(self, logger: LoggerProtocol, base_dir: str = "data"):
        super().__init__(logger)
        self.base_dir = base_dir
        
    async def _execute_action(self, data):
        # Required abstract method implementation
        return {"action": "file_path_utility", "data": data}
        
    def generate_path(self, case_details):
        self.log_debug(f"Generating path for case {case_details.get('docket_num')}")
        # Path generation logic
        return path
```

## Court Logger File Structure
```
logs/
└── pacer/
    ├── ilnd.log    # Northern District of Illinois
    ├── cand.log    # Northern District of California
    └── nysd.log    # Southern District of New York
```

## Common Anti-Patterns to Avoid

### ❌ Creating Own Logger
```python
# DON'T DO THIS
logger = logging.getLogger(__name__)
logger.info("Message")
```

### ❌ Using Print Statements
```python
# DON'T DO THIS
print(f"Processing case {docket_num}")
```

### ❌ Overriding Injected Logger
```python
# DON'T DO THIS
def __init__(self, logger):
    super().__init__(logger)
    self.logger = logging.getLogger("my_logger")  # Overrides injected logger
```

### ❌ Not Inheriting from Base Classes
```python
# DON'T DO THIS
class MyComponent:  # Missing base class inheritance
    def __init__(self, logger):
        self.logger = logger
```

## Benefits of Following These Requirements

1. **Isolation**: Each court's logs are separate for easier debugging
2. **Consistency**: Standardized logging format across all components
3. **Debugging**: Easy to trace issues for specific courts/cases
4. **Maintenance**: Centralized logging configuration
5. **Testing**: Predictable logging behavior for tests

## Validation Checklist

Before submitting a PACER component, ensure:

- [ ] Component inherits from `AsyncServiceBase` or `ComponentImplementation`
- [ ] Logger is injected via constructor parameter
- [ ] No direct calls to `logging.getLogger()`
- [ ] No `print()` statements in production code
- [ ] Uses standardized logging methods (`self.log_info()`, etc.)
- [ ] Does not override `self.logger` after initialization
- [ ] Implements required abstract methods if applicable

## Testing Court Logger Integration

```python
# Test that your component works with court loggers
import logging
from your_component import YourComponent

# Create court-specific logger
court_logger = logging.getLogger("pacer.court.test")
# Add handlers as needed

# Test instantiation
component = YourComponent(court_logger)
# Verify logging works as expected
```

## Questions or Issues?

If you encounter issues with court logger integration:
1. Check that the base class is properly imported
2. Verify logger is passed to `super().__init__()`
3. Ensure no direct `logging.getLogger()` calls exist
4. Check that logging calls use the standardized methods

This architecture ensures all PACER components log consistently to court-specific files for optimal debugging and maintenance.