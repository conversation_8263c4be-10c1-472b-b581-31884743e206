# ADR-003: S3 Service Dependency Architecture Fix

## Status
Accepted

## Context
The main dependency injection issue has been fixed, but the S3 service is not reaching the HTML upload functionality. This creates a critical gap in the PACER processing pipeline where HTML content cannot be uploaded to S3 storage.

### Current Architecture Issues

1. **Storage Container**: <PERSON><PERSON>ly creates `s3_async_storage` (S3AsyncStorage) with correct AWS configuration
2. **Pacer Container**: Creates `s3_manager` (S3Manager component) that wraps `s3_async_storage`
3. **HTML Processing Services**: Expect direct `s3_async_storage` parameter, not wrapper
4. **Dependency Gap**: HTML processing facade passes `s3_manager` but orchestrator expects `s3_async_storage`

### Root Cause Analysis

```
Storage Container
├── s3_async_storage (S3AsyncStorage) ✓ WORKING
│
Pacer Container  
├── s3_manager (S3Manager) ✓ WORKING - wraps s3_async_storage
├── html_processing_facade ❌ BROKEN
│   ├── Receives: s3_manager
│   ├── Needs: s3_async_storage
│   └── Passes to: HTMLProcessingOrchestrator
│
HTML Processing
├── HTMLProcessingOrchestrator ❌ BROKEN
│   ├── Expects: s3_async_storage parameter
│   ├── Receives: s3_manager (incompatible)
│   └── Upload: FAILS - cannot call upload_content method
```

### Service Interface Mismatch

**HTMLProcessingOrchestrator expects:**
```python
async def upload_content(content, object_key, content_type, overwrite)
```

**S3Manager provides:**
```python
async def _execute_action(data) -> Any
```

**S3AsyncStorage provides:**
```python  
async def upload_content(content, object_key, content_type, overwrite)
```

## Decision

Implement a dual-service dependency architecture that provides both component wrappers and direct storage services where needed.

### Solution Architecture

```
Storage Container
├── s3_async_storage (S3AsyncStorage) ✓
│
Pacer Container
├── s3_manager (S3Manager) ✓ - for component-based operations
├── html_processing_facade ✓ FIXED
│   ├── Receives: s3_manager AND s3_async_storage
│   ├── Uses: s3_manager for component operations
│   ├── Passes: s3_async_storage to orchestrator
│   └── Bridge: Maps between component and service interfaces
│
HTML Processing  
├── HTMLProcessingOrchestrator ✓ FIXED
│   ├── Receives: s3_async_storage (direct service)
│   ├── Calls: upload_content() directly
│   └── Upload: SUCCESS
```

## Implementation Plan

### Phase 1: Container Updates
1. Update `storage.py` to ensure S3 service is properly exposed
2. Update `pacer.py` to provide both s3_manager and s3_async_storage to HTML facade
3. Verify dependency injection flow

### Phase 2: Service Updates  
1. Update `HtmlProcessingFacadeService` to receive both services
2. Update `HTMLProcessingOrchestrator` to use s3_async_storage directly
3. Create service bridge if needed for backward compatibility

### Phase 3: Testing & Validation
1. Test complete S3 dependency flow
2. Verify HTML upload functionality
3. Validate CDN URL generation
4. Test error handling and fallbacks

## Consequences

### Positive
- ✅ Fixes critical S3 HTML upload functionality
- ✅ Maintains backward compatibility with existing s3_manager component usage
- ✅ Provides clear separation between component and service interfaces
- ✅ Enables direct S3 operations where needed
- ✅ Preserves existing logging and error handling

### Negative
- ⚠️ Increases complexity by requiring dual service injection
- ⚠️ May require updates to other components using similar patterns
- ⚠️ Need to maintain both component and service interfaces

### Risks & Mitigations
- **Risk**: Other services may have similar dependency issues
- **Mitigation**: Audit all containers for similar service/component mismatches
- **Risk**: Performance impact from dual service injection
- **Mitigation**: Use Factory providers to avoid unnecessary instantiation

## Technical Specifications

### Storage Container
```python
# Ensure S3 service is accessible
s3_async_storage = providers.Singleton(
    S3AsyncStorage,
    logger=logger,
    bucket_name=s3_bucket_name,
    aws_access_key=aws_access_key,
    aws_secret_key=aws_secret_key,
    aws_region=aws_region,
    disable_versioning=True
)
```

### Pacer Container  
```python
# Provide both component and service
s3_manager = providers.Singleton(
    S3Manager, 
    logger=logger, 
    config=config, 
    s3_async_storage=storage_container.s3_async_storage
)

html_processing_facade = providers.Factory(
    HtmlProcessingFacadeService,
    logger=logger,
    config=config,
    html_parser=html_parser,
    law_firm_corrector=law_firm_corrector,
    s3_manager=s3_manager,  # Component interface
    s3_async_storage=storage_container.s3_async_storage,  # Direct service
    field_consistency_manager=field_consistency_manager,
    transfer_info_processor=transfer_info_processor
)
```

### HTML Processing Service
```python
class HtmlProcessingFacadeService(AsyncServiceBase):
    def __init__(self, s3_manager=None, s3_async_storage=None, **kwargs):
        self.s3_manager = s3_manager  # For component operations
        self.s3_async_storage = s3_async_storage  # For direct S3 calls
        
    async def process_html_content(self, **kwargs):
        # Use s3_async_storage for direct upload operations
        orchestrator = HTMLProcessingOrchestrator(
            s3_async_storage=self.s3_async_storage,
            **kwargs
        )
```

## Testing Strategy

1. **Unit Tests**: Verify each service receives correct dependencies
2. **Integration Tests**: Test complete HTML upload workflow  
3. **End-to-End Tests**: Validate PACER processing with S3 upload
4. **Regression Tests**: Ensure existing functionality still works

## Rollback Plan

If implementation fails:
1. Revert container changes
2. Use component-only architecture temporarily  
3. Investigate alternative service bridge patterns
4. Consider refactoring component interfaces

## Related Documents
- [Storage Container Configuration](/src/containers/storage.py)
- [PACER Container Architecture](/src/containers/pacer.py)  
- [HTML Processing Service](/src/services/html/html_processing_orchestrator.py)
- [S3 Async Storage](/src/infrastructure/storage/s3_async.py)