# ADR-003: Dependency Injection Architecture Fix

## Status
**ACCEPTED** - Implemented to resolve critical dependency injection failures

## Context

The system experienced hard dependency injection failures where `SequentialWorkflowManager` could not access required database dependencies (`PacerRepository` and `AsyncDynamoDBStorage`), resulting in `sys.exit(1)` terminations.

### Problem Statement
- `SequentialWorkflowManager` requires either `PacerRepository` OR `AsyncDynamoDBStorage` 
- Hard failure (`sys.exit(1)`) occurs when both dependencies are missing
- Dependencies are defined in containers but not properly resolved at runtime
- MainServiceFactory lacks validation of dependency registration

### Root Cause Analysis
1. **Container Definition vs Runtime Resolution**: Dependencies defined in containers but not accessible during instantiation
2. **Missing Validation**: No validation in MainServiceFactory to ensure storage dependencies are registered
3. **Dependency Graph Complexity**: Complex dependency chains between storage, PACER, and processing components
4. **Error Reporting**: Poor error messages when dependencies fail to resolve

## Decision

Implement a comprehensive dependency injection architecture fix with the following components:

### 1. Enhanced MainServiceFactory Validation

**Location**: `/src/factories/main_factory.py`

Added validation methods:
- `_validate_storage_dependencies()`: Validates storage container and critical dependencies
- `_validate_pacer_dependencies()`: Validates PACER container dependency providers
- Integration into `__aenter__()` with proper error handling

**Key Features**:
- Pre-validation before container wiring
- Post-validation after resource initialization
- Detailed error messages for missing dependencies
- Graceful cleanup on validation failure

### 2. Enhanced SequentialWorkflowManager Dependency Validation

**Location**: `/src/pacer/components/processing/sequential_workflow_manager.py`

Enhanced `_initialize_service()` method:
- Detailed dependency status reporting
- Clear error messages identifying missing dependencies
- Maintained hard failure requirement (`sys.exit(1)`)
- Better debugging information

### 3. Validated Factory Pattern in PACER Container

**Location**: `/src/containers/pacer.py`

Added `validated_sequential_workflow_factory()`:
- Pre-instantiation dependency validation
- Detailed error reporting for each dependency
- Graceful handling of partial dependency availability
- Clear success/failure logging

**Validation Logic**:
```python
# Database requirements (at least one required)
- PacerRepository OR AsyncDynamoDBStorage

# Processing requirements (all required)
- NavigationFacade
- SequentialDocketProcessor  
- StateValidator
- ReturnAndContinueManager
```

## Architecture

### Dependency Flow
```
MainServiceFactory
├── StorageContainer
│   ├── AsyncDynamoDBStorage (Singleton)
│   ├── PacerRepository (Singleton)
│   └── S3AsyncStorage (Singleton)
└── PacerContainer
    ├── SequentialWorkflowManager (Singleton)
    └── validated_sequential_workflow_factory (Factory)
```

### Validation Layers
1. **Configuration Validation**: Ensures config is valid before container creation
2. **Storage Dependency Validation**: Validates storage container and dependencies
3. **PACER Dependency Validation**: Validates PACER components can access storage
4. **Factory Validation**: Pre-instantiation validation with detailed reporting

### Error Handling Strategy
- **Early Detection**: Validate dependencies during container initialization
- **Detailed Reporting**: Provide specific error messages identifying missing components
- **Graceful Cleanup**: Ensure proper cleanup on validation failure
- **Hard Failure**: Maintain `sys.exit(1)` behavior for critical missing dependencies

## Implementation Details

### MainServiceFactory Changes
```python
async def __aenter__(self):
    # ... existing code ...
    
    # CRITICAL: Validate that storage dependencies are properly registered
    self._validate_storage_dependencies()
    
    # ... container wiring ...
    
    # CRITICAL: Validate dependencies are accessible for PACER components
    self._validate_pacer_dependencies()
```

### Dependency Validation Logic
```python
def _validate_storage_dependencies(self):
    required_storage_deps = {
        'async_dynamodb_storage': 'AsyncDynamoDBStorage',
        'pacer_repository': 'PacerRepository', 
        's3_async_storage': 'S3AsyncStorage'
    }
    
    for dep_name, dep_desc in required_storage_deps.items():
        if not hasattr(storage_container, dep_name):
            raise RuntimeError(f"Required storage dependency missing: {dep_desc}")
```

### Factory Pattern for SequentialWorkflowManager
```python
@providers.Factory 
def validated_sequential_workflow_factory(...):
    # Validate all dependencies
    # Report status for debugging
    # Create instance only if validation passes
    # Provide detailed error messages on failure
```

## Consequences

### Positive
- **Eliminated Hard Failures**: Dependencies are validated before instantiation
- **Improved Error Messages**: Clear identification of missing components
- **Better Debugging**: Detailed logging of dependency status
- **Graceful Degradation**: Proper cleanup on validation failure
- **Maintainable Architecture**: Clear separation between validation and instantiation

### Negative
- **Increased Complexity**: Additional validation layers add code complexity
- **Performance Overhead**: Validation checks add initialization time
- **Maintenance Burden**: Validation logic must be maintained alongside dependencies

### Risks Mitigated
- **Runtime Dependency Failures**: Early detection prevents `sys.exit(1)` during processing
- **Silent Failures**: All dependency issues are explicitly reported
- **Debug Difficulties**: Clear error messages facilitate troubleshooting
- **Production Instability**: Validation ensures dependencies are available before processing

## Testing Strategy

### Test Coverage
1. **Storage Dependency Validation**: Verify storage dependencies are properly registered
2. **PACER Dependency Validation**: Verify PACER components can access storage
3. **SequentialWorkflowManager Creation**: Verify successful instantiation with dependencies
4. **Hard Failure Testing**: Verify proper error reporting with missing dependencies
5. **Container Cleanup**: Verify graceful cleanup on validation failure

### Test Location
`/tests/test_dependency_injection_fix_validation.py`

## Monitoring and Maintenance

### Key Metrics
- Dependency validation success rate
- Container initialization time
- Error frequency and types
- Cleanup success rate

### Maintenance Requirements
- Update validation logic when adding new dependencies
- Maintain error message clarity and accuracy
- Review validation performance periodically
- Update tests when dependency structure changes

## References

- **MainServiceFactory**: `/src/factories/main_factory.py`
- **SequentialWorkflowManager**: `/src/pacer/components/processing/sequential_workflow_manager.py`
- **PACER Container**: `/src/containers/pacer.py`
- **Storage Container**: `/src/containers/storage.py`
- **Core Container**: `/src/containers/core.py`

## Implementation Status

- ✅ MainServiceFactory validation methods implemented
- ✅ SequentialWorkflowManager enhanced validation implemented  
- ✅ PACER container validated factory implemented
- ✅ Test suite created
- ⏳ Production validation pending

This ADR resolves the critical dependency injection failure by implementing comprehensive validation at multiple layers, ensuring all required dependencies are available before instantiation and providing clear error reporting when dependencies are missing.