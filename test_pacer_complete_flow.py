#!/usr/bin/env python3
"""Test script to verify the complete PACER processing flow follows the flowchart."""

import asyncio
import json
import logging
from pathlib import Path
from datetime import datetime
from unittest.mock import Mock, MagicMock, AsyncMock

# Add the project to the path
import sys
sys.path.insert(0, '/Users/<USER>/PycharmProjects/lexgenius')

from src.pacer.jobs.docket_job import DocketProcessingJob
from src.pacer.jobs.job_processor import JobProcessor


def create_mock_services():
    """Create mock services for testing."""
    
    # Mock case processing service
    case_processing_service = AsyncMock()
    case_processing_service.execute = AsyncMock(side_effect=lambda data: {
        "court_id": "cand",
        "docket_num": "3:21-cv-00123",
        "versus": "Smith v. Jones Corporation",
        "filing_date": "01/15/2021",
        "base_filename": "cand_21_00123_Smith_v_Jones_Corporation",
        "is_removal": False,
        "mdl_flag": False
    } if data["action"] in ["validate_page_content", "update_case_details"] else 
    "cand_21_00123_Smith_v_Jones_Corporation" if data["action"] == "create_base_filename" else None)
    
    # Mock download orchestration service
    download_service = AsyncMock()
    download_service.execute = AsyncMock(return_value="https://cdn.lexgenius.ai/20250805/html/test.html")
    download_service.execute_download_workflow = AsyncMock(return_value={
        "is_downloaded": False,
        "html_only": True
    })
    
    # Mock file operations service
    file_ops_service = AsyncMock()
    file_ops_service.execute = AsyncMock(return_value=None)
    
    # Mock relevance engine
    relevance_engine = AsyncMock()
    relevance_engine._determine_case_review_status = AsyncMock(return_value=False)
    
    # Mock classification service
    classification_service = AsyncMock()
    classification_service.execute = AsyncMock(return_value={
        "case_type": "civil",
        "jurisdiction": "federal"
    })
    
    # Mock verification service
    verification_service = AsyncMock()
    verification_service.execute = AsyncMock(return_value={"should_proceed": True})
    
    return {
        "case_processing_service": case_processing_service,
        "download_orchestration_service": download_service,
        "file_operations_service": file_ops_service,
        "relevance_engine": relevance_engine,
        "classification_service": classification_service,
        "verification_service": verification_service
    }


async def test_complete_flow():
    """Test the complete docket processing flow."""
    print("\n=== Testing Complete PACER Processing Flow ===")
    
    # Set up logging to capture flow
    log_output = []
    
    class TestHandler(logging.Handler):
        def emit(self, record):
            log_output.append(self.format(record))
    
    logger = logging.getLogger("test_court")
    logger.setLevel(logging.DEBUG)
    handler = TestHandler()
    handler.setFormatter(logging.Formatter("%(levelname)s - %(message)s"))
    logger.addHandler(handler)
    
    # Create mock services
    services = create_mock_services()
    
    # Create mock browser context
    mock_context = AsyncMock()
    mock_page = AsyncMock()
    mock_page.goto = AsyncMock()
    mock_page.wait_for_load_state = AsyncMock()
    mock_page.content = AsyncMock(return_value="<html>Test HTML</html>")
    mock_page.is_closed = Mock(return_value=False)
    mock_page.close = AsyncMock()
    mock_context.new_page = AsyncMock(return_value=mock_page)
    
    # Create test job
    iso_date = datetime.now().strftime("%Y%m%d")
    job = DocketProcessingJob(
        court_id="cand",
        row_num=1,
        total_rows=10,
        docket_link_href="/cgi-bin/DktRpt.pl?123456",
        docket_num="3:21-cv-00123",
        initial_versus_text="Smith v. Jones Corporation",
        initial_filing_date="01/15/2021",
        relevance_engine=services["relevance_engine"],
        config={"iso_date": iso_date, "is_explicitly_requested": False},
        file_management_service=None,
        download_orchestration_service=services["download_orchestration_service"],
        case_processing_service=services["case_processing_service"],
        file_operations_service=services["file_operations_service"],
        file_manager=None,
        court_logger=logger
    )
    
    # Create job processor
    processor = JobProcessor(logger=logger, config={})
    
    # Process the job
    await processor._process_docket_job(job, mock_context)
    
    # Verify the flow by checking log output
    print("\n=== Checking Flow Execution ===")
    
    required_phases = [
        "=== STARTING DOCKET JOB ===",
        "========== PHASE 1: HTML CONTENT PROCESSING ==========",
        "[P1_1] Case Processing Service.process_case_html",
        "[P1_2] Waiting for page content stability",
        "[P1_3] Validating HTML content matches case",
        "[P1_4] HTML validation passed",
        "[P1_5] Extracting case details via HTMLCaseParser",
        "[P1_6] Processing MDL flags and special cases",
        "[P1_7] Checking for Notice of Removal",
        "[P1_8] Generating base filename",
        "[P1_9] Adding processing metadata",
        "========== PHASE 2: RELEVANCE & CLASSIFICATION ==========",
        "[P2_1] Relevance Service.determine_case_relevance",
        "[P2_CLASSIFY] Classification Service.classify_case",
        "[P2_C4] Checking Transfer Status",
        "========== PHASE 3: CASE VERIFICATION ==========",
        "[P3_1] Verification Service.verify_case",
        "========== PHASE 4: DOWNLOAD WORKFLOW ==========",
        "========== FINAL SAVE ==========",
        "[FS1] Creating court/date directory",
        "[FS2] Filename:",
        "[FS3] Cleaning and preparing case data",
        "[FS4] Saving to local JSON",
        "=== FINISHED DOCKET JOB"
    ]
    
    log_text = "\n".join(log_output)
    
    missing_phases = []
    for phase in required_phases:
        if phase not in log_text:
            missing_phases.append(phase)
            print(f"❌ Missing: {phase}")
        else:
            print(f"✅ Found: {phase}")
    
    if missing_phases:
        print(f"\n❌ Flow incomplete! Missing {len(missing_phases)} phases")
        print("\nFull log output:")
        for line in log_output:
            print(line)
    else:
        print("\n✅ All phases executed in correct order!")
    
    # Check final files were created
    expected_json = Path("data") / iso_date / "dockets" / "cand_21_00123_Smith_v_Jones_Corporation.json"
    expected_log = Path("data") / iso_date / "logs" / "docket_report" / "cand.json"
    
    print(f"\nChecking for JSON file: {expected_json}")
    if expected_json.exists():
        print("✅ JSON file created")
    else:
        print("⚠️  JSON file not found (mock services don't actually create files)")
    
    print(f"Checking for log file: {expected_log}")
    if expected_log.exists():
        print("✅ Docket report log file exists")
        # Check if our test entry is in there
        with open(expected_log, 'r') as f:
            logs = json.load(f)
            test_entry_found = any(
                log.get("docket_num") == "3:21-cv-00123" and 
                log.get("base_filename") == "cand_21_00123_Smith_v_Jones_Corporation"
                for log in logs if isinstance(log, dict)
            )
            if test_entry_found:
                print("✅ Test entry found in docket report log")
            else:
                print("⚠️  Test entry not found in log (may be using mocks)")
    else:
        print("⚠️  Docket report log not found")
    
    return len(missing_phases) == 0


async def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing Complete PACER Processing Flow")
    print("=" * 60)
    
    try:
        success = await test_complete_flow()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ Complete flow test passed!")
        else:
            print("❌ Flow test failed - some phases missing")
        print("=" * 60)
        
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())