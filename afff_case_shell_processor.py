#!/usr/bin/env python3
"""
AFFF Case Shell Processor
Aggregates AFFF case filings from case shell, processes them, and uploads to DynamoDB.

Usage:
    python afff_case_shell_processor.py --date YYYYMMDD  # Process specific date
    python afff_case_shell_processor.py -all            # Upload all items from attorney_comprehensive_data.json
    
The -all flag:
- Reads attorney_comprehensive_data.json from output directory
- For each attorney, uses their law_firm value
- For each filing, converts date from MM/DD/YYYY to YYYYMMDD
- Uses docket_num from filing, or AFFF_{doc_num} if not present
- Uploads all filings to DynamoDB with converted date as added_on
"""

import argparse
import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import re
import shutil

from dotenv import load_dotenv
from playwright.async_api import async_playwright, <PERSON>, Browser
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn, Bar<PERSON><PERSON>umn, TaskProgressColumn
from rich.logging import RichHandler
from rich.panel import Panel
from rich.table import Table

# Load environment variables
load_dotenv()

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import existing services
from src.pacer.authentication_service import PacerAuthenticationService
from src.pacer._browser_components.navigator import PacerNavigator
from src.pacer.file_operations_service import PacerFileOperationsService
from src.infrastructure.storage.s3_async import S3AsyncStorage
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository
from scripts.analysis.AFFF.generate_afff_mdl_report import parse_docket_text

# Setup logging with rich
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    handlers=[RichHandler(rich_tracebacks=True)]
)
logger = logging.getLogger(__name__)
console = Console()


class AFFFCaseShellProcessor:
    """Handles AFFF case shell processing workflow."""
    
    def __init__(self, date_str: Optional[str], force: bool = False):
        self.date_str = date_str  # YYYYMMDD format or None for --all mode
        self.force = force  # Force upload without duplicate checking
        self.console = console
        self.config = self._load_config()
        
        # Services
        self.auth_service = None
        self.file_ops_service = None
        self.s3_storage = None
        self.dynamodb_storage = None
        self.pacer_repo = None
        
        # Browser state
        self.browser = None
        self.context = None
        self.page = None
        self.navigator = None
        
        # Data paths
        self.s3_html_key = f"{date_str}/AFFF/docket_report.html"
        self.local_output_dir = Path("scripts/analysis/AFFF/output")
        self.local_output_dir.mkdir(parents=True, exist_ok=True)
        
        # Attorney lookup paths
        self.attorney_lookup_file = Path("scripts/analysis/AFFF/attorney_law_firm_lookup.json")
        self.attorney_backups_dir = Path("scripts/analysis/AFFF/backups")
        self.attorney_backups_dir.mkdir(parents=True, exist_ok=True)
        
        # Attorney lookup data
        self.attorney_lookup = {}
        self.attorney_lookup_map = {}  # Case-insensitive map
        self.new_attorneys = []
        self.session_attorney_updates = {}  # Track attorney updates for current session
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from environment and defaults."""
        return {
            'username_prod': os.environ.get('PACER_USERNAME'),
            'password_prod': os.environ.get('PACER_PASSWORD'),
            'aws_region': os.environ.get('AWS_REGION', 'us-west-2'),
            'aws_access_key': os.environ.get('AWS_ACCESS_KEY_ID'),
            'aws_secret_key': os.environ.get('AWS_SECRET_ACCESS_KEY'),
            'bucket_name': os.environ.get('S3_BUCKET_NAME', 'lexgenius'),
            'headless': False,  # Always non-headless for AFFF scraping
            'data_dir': os.environ.get('DATA_DIR', 'data')
        }
    
    def _load_attorney_lookup(self) -> None:
        """Load attorney law firm lookup data."""
        if self.attorney_lookup_file.exists():
            try:
                with open(self.attorney_lookup_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.attorney_lookup = data.get('attorneys', {})
                    
                    # Create case-insensitive map
                    self.attorney_lookup_map = {}
                    for name in self.attorney_lookup.keys():
                        self.attorney_lookup_map[name.lower()] = name
                    
                    logger.info(f"Loaded {len(self.attorney_lookup)} attorneys from lookup file")
                    
                    # Debug: Show the specific names we're looking for
                    target_names = ["lawrence cohan", "timothy young"]
                    for target in target_names:
                        if target in self.attorney_lookup_map:
                            actual_name = self.attorney_lookup_map[target]
                            law_firm = self.attorney_lookup[actual_name].get('law_firm', 'Unknown')
                            logger.info(f"🎯 TARGET FOUND: '{target}' -> '{actual_name}' -> '{law_firm}'")
                        else:
                            logger.info(f"❌ TARGET MISSING: '{target}' not in lookup map")
                    
                    # Debug: Show all lowercase keys containing "cohan" or "young"
                    debug_keys = [k for k in self.attorney_lookup_map.keys() if "cohan" in k or "young" in k]
                    if debug_keys:
                        logger.info(f"🔍 Keys containing 'cohan' or 'young': {debug_keys}")
            except Exception as e:
                logger.error(f"Error loading attorney lookup: {e}")
                self.attorney_lookup = {}
                self.attorney_lookup_map = {}
        else:
            logger.warning(f"Attorney lookup file not found: {self.attorney_lookup_file}")
            self.attorney_lookup = {}
            self.attorney_lookup_map = {}
    
    def _create_attorney_backup(self) -> Optional[Path]:
        """Create backup of attorney lookup file."""
        if not self.attorney_lookup_file.exists():
            return None
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"attorney_law_firm_lookup_backup_{timestamp}.json"
        backup_path = self.attorney_backups_dir / backup_name
        
        try:
            shutil.copy2(self.attorney_lookup_file, backup_path)
            logger.info(f"Created backup: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return None
    
    async def initialize_services(self):
        """Initialize all required services."""
        try:
            # Authentication service
            auth_logger = logging.getLogger('pacer_auth')
            self.auth_service = PacerAuthenticationService(auth_logger, self.config)
            
            # S3 storage
            s3_logger = logging.getLogger('s3_storage')
            self.s3_storage = S3AsyncStorage(
                logger=s3_logger,
                config=self.config,
                bucket_name=self.config['bucket_name'],
                aws_access_key=self.config['aws_access_key'],
                aws_secret_key=self.config['aws_secret_key'],
                aws_region=self.config['aws_region']
            )
            
            # File operations service
            file_ops_logger = logging.getLogger('file_operations')
            self.file_ops_service = PacerFileOperationsService(
                logger=file_ops_logger,
                config=self.config,
                s3_async_storage=self.s3_storage
            )
            
            # DynamoDB storage
            dynamodb_logger = logging.getLogger('dynamodb_storage')
            self.dynamodb_storage = AsyncDynamoDBStorage(
                config={
                    'aws_region': self.config['aws_region'],
                    'aws_access_key': self.config['aws_access_key'],
                    'aws_secret_key': self.config['aws_secret_key']
                },
                logger=dynamodb_logger
            )
            await self.dynamodb_storage.__aenter__()
            self.pacer_repo = PacerRepository(self.dynamodb_storage)
            
            # Load attorney lookup
            self._load_attorney_lookup()
            
            logger.info("✅ All services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            raise
    
    async def cleanup(self):
        """Clean up resources."""
        if self.browser:
            await self.browser.close()
        if self.dynamodb_storage:
            await self.dynamodb_storage.__aexit__(None, None, None)
        if self.s3_storage:
            await self.s3_storage.close()
    
    async def setup_browser(self) -> bool:
        """Set up browser for PACER scraping."""
        try:
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(
                headless=self.config['headless'],
                args=['--disable-blink-features=AutomationControlled']
            )
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            self.page = await self.context.new_page()
            
            # Create screenshot directory
            screenshot_dir = self.local_output_dir / "screenshots"
            screenshot_dir.mkdir(parents=True, exist_ok=True)
            
            # Initialize navigator with required parameters and reasonable timeout
            self.navigator = PacerNavigator(
                page=self.page,
                config=self.config,
                screenshot_dir=str(screenshot_dir),
                timeout_ms=10000  # 10 second default timeout instead of 25
            )
            
            logger.info("✅ Browser setup complete")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup browser: {e}")
            return False
    
    async def scrape_afff_data(self) -> bool:
        """Scrape AFFF case shell data from PACER."""
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("[cyan]Scraping AFFF data from PACER...", total=8)
                
                # Step 1: Setup browser
                progress.update(task, description="[cyan]Setting up browser...")
                if not await self.setup_browser():
                    return False
                progress.advance(task)
                
                # Step 2: Login to PACER
                progress.update(task, description="[cyan]Logging into PACER...")
                court_logger = logging.getLogger('court_scd_pacer')
                if not await self.auth_service.perform_ecf_login_sequence(
                    self.navigator, 'scd', court_logger
                ):
                    logger.error("Failed to login to PACER")
                    return False
                progress.advance(task)
                
                # Step 3: Navigate to query page
                progress.update(task, description="[cyan]Navigating to query page...")
                query_url = "https://ecf.scd.uscourts.gov/cgi-bin/iquery.pl"
                await self.navigator.goto(query_url, timeout_override_ms=5000)  # 5 second timeout
                await asyncio.sleep(0.5)  # Brief wait for page load
                progress.advance(task)
                
                # Step 4: Enter case number and click Find This Case
                progress.update(task, description="[cyan]Entering case number...")
                case_input = self.page.locator("input#case_number_text_area_0[name='case_num']").first
                await case_input.fill("2:25-av-55555")
                
                # Tab off the field to enable the Find This Case button
                await case_input.press("Tab")
                await asyncio.sleep(0.5)  # Give time for JavaScript to enable button
                
                # Click Find This Case button
                find_case_btn = self.page.locator("input#case_number_find_button_0[value='Find This Case']").first
                await find_case_btn.click()
                await asyncio.sleep(0.5)  # Brief wait for Run Query to appear
                
                # Click Run Query button that appears after Find This Case
                run_query_btn = self.page.locator("input[name='button1'][value='Run Query']").first
                await run_query_btn.click()
                await self.page.wait_for_load_state('domcontentloaded')
                progress.advance(task)
                
                # Step 5: Click on Docket Report link
                progress.update(task, description="[cyan]Accessing docket report...")
                docket_link = self.page.locator("a:has-text('Docket Report')").first
                await docket_link.click()
                await self.page.wait_for_load_state('domcontentloaded')
                progress.advance(task)
                
                # Step 6: Enter date range on the docket report form
                progress.update(task, description="[cyan]Setting date range...")
                # Convert YYYYMMDD to MM/DD/YYYY (full year format)
                year = self.date_str[:4]
                month = self.date_str[4:6]
                day = self.date_str[6:8]
                date_formatted = f"{month}/{day}/{year}"
                
                logger.info(f"Using date format: {date_formatted}")
                
                # Fill date fields - using name attribute as shown in HTML
                from_date_input = self.page.locator("input[name='date_from']").first
                to_date_input = self.page.locator("input[name='date_to']").first
                
                # Clear fields first and then fill
                await from_date_input.clear()
                await from_date_input.fill(date_formatted)
                
                await to_date_input.clear()
                await to_date_input.fill(date_formatted)
                
                # Wait a moment for JavaScript validation
                await asyncio.sleep(0.5)
                
                # Click Run Report button - it's actually a button with onclick, not submit
                run_report_btn = self.page.locator("input[name='button1'][value='Run Report']").first
                await run_report_btn.click()
                
                # Wait for the report to load
                await self.page.wait_for_load_state('networkidle', timeout=10000)  # 10 second timeout
                progress.advance(task)
                
                # Step 7: Capture and upload HTML
                progress.update(task, description="[cyan]Uploading to S3...")
                html_content = await self.page.content()
                
                # Upload to S3
                success = await self.s3_storage.upload_file_content(
                    content=html_content.encode('utf-8'),
                    key=self.s3_html_key,
                    content_type='text/html'
                )
                
                if success:
                    logger.info(f"✅ Successfully uploaded HTML to S3: {self.s3_html_key}")
                else:
                    logger.error("Failed to upload HTML to S3")
                    return False
                    
                progress.advance(task)
                
            return True
            
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
            return False
        finally:
            if self.browser:
                await self.browser.close()
                self.browser = None
    
    async def process_afff_data(self, use_local_file: Optional[str] = None) -> bool:
        """Process AFFF data from S3 HTML or local file.
        
        Args:
            use_local_file: Optional path to local HTML file for testing
        """
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=self.console
            ) as progress:
                task = progress.add_task("[cyan]Processing AFFF data...", total=4)
                
                # Step 1: Get HTML content
                if use_local_file:
                    progress.update(task, description="[cyan]Loading local HTML file...")
                    with open(use_local_file, 'r', encoding='utf-8') as f:
                        html_content = f.read()
                    logger.info(f"Loaded HTML from local file: {use_local_file}")
                else:
                    progress.update(task, description="[cyan]Downloading HTML from S3...")
                    html_bytes = await self.s3_storage.download_content(self.s3_html_key)
                    if not html_bytes:
                        logger.error(f"Failed to download HTML from S3: {self.s3_html_key}")
                        return False
                    html_content = html_bytes.decode('utf-8')
                progress.advance(task)
                
                # Step 2: Parse HTML
                progress.update(task, description="[cyan]Parsing docket entries...")
                parsed_data = self._parse_html_table(html_content)
                logger.info(f"Parsed {len(parsed_data)} docket entries")
                progress.advance(task)
                
                # Step 3: Process with AFFF logic
                progress.update(task, description="[cyan]Processing attorney data...")
                processed_data = []
                self.new_attorneys = []  # Reset for this processing run
                self.session_attorney_updates = {}  # Track attorney updates for this session
                
                for entry in parsed_data:
                    # Parse docket text to extract attorney info
                    parsed = parse_docket_text(entry['docket_text'])
                    
                    # Look up law firm for attorney
                    law_firm = "Unknown"
                    if parsed['attorney']:
                        attorney_lower = parsed['attorney'].lower()
                        logger.info(f"🔍 LOOKUP DEBUG: Extracted attorney: '{parsed['attorney']}' -> lowercase: '{attorney_lower}'")
                        
                        # Check if we already updated this attorney in this session
                        if parsed['attorney'] in self.session_attorney_updates:
                            law_firm = self.session_attorney_updates[parsed['attorney']]
                            logger.info(f"✅ SESSION UPDATE: '{parsed['attorney']}' -> '{law_firm}'")
                        elif attorney_lower in self.attorney_lookup_map:
                            # Found in lookup (case-insensitive)
                            actual_name = self.attorney_lookup_map[attorney_lower]
                            law_firm = self.attorney_lookup[actual_name].get('law_firm', 'Unknown')
                            
                            # If law firm is Unknown, prompt user to update it
                            if law_firm == "Unknown":
                                logger.info(f"🔄 UNKNOWN LAW FIRM: '{parsed['attorney']}' found with Unknown law firm")
                                
                                # Interactive prompt for law firm
                                new_law_firm = self._prompt_for_law_firm(parsed['attorney'])
                                
                                # If user provided a law firm, update session tracking
                                if new_law_firm and new_law_firm != "Unknown":
                                    law_firm = new_law_firm
                                    self.session_attorney_updates[parsed['attorney']] = law_firm
                                    # Update the attorney lookup in memory and file
                                    self._update_attorney_lookup_immediate(parsed['attorney'], law_firm)
                                    logger.info(f"✅ UPDATED: '{parsed['attorney']}' -> '{law_firm}'")
                                else:
                                    logger.info(f"⚠️ REMAINS UNKNOWN: '{parsed['attorney']}' will stay as Unknown")
                            else:
                                logger.info(f"✅ MATCH FOUND: '{parsed['attorney']}' -> '{law_firm}'")
                        else:
                            # New attorney not in lookup - prompt user
                            if parsed['attorney'] not in self.new_attorneys:
                                self.new_attorneys.append(parsed['attorney'])
                                logger.info(f"❌ NO MATCH: '{parsed['attorney']}' not found in lookup")
                                
                                # Interactive prompt for law firm
                                law_firm = self._prompt_for_law_firm(parsed['attorney'])
                                
                                # If user provided a law firm, update session tracking
                                if law_firm and law_firm != "Unknown":
                                    self.session_attorney_updates[parsed['attorney']] = law_firm
                                    # Update the attorney lookup in memory and file
                                    self._update_attorney_lookup_immediate(parsed['attorney'], law_firm)
                                    logger.info(f"✅ UPDATED: '{parsed['attorney']}' -> '{law_firm}'")
                    
                    # Determine docket_num with proper fallback logic
                    docket_num = parsed['case_number'] or entry.get('docket_num')
                    if not docket_num:
                        docket_num = f"AFFF_{entry['doc_num']}"
                    
                    # Extract versus from docket text and normalize it
                    # Look for versus pattern in the docket text directly - FIXED
                    import re
                    
                    # Remove COMPLAINT prefix and extract full versus
                    docket_text_clean = re.sub(r'^COMPLAINT\s+', '', entry['docket_text'], flags=re.IGNORECASE)
                    
                    # Better regex to capture full plaintiff names
                    versus_match = re.search(r'([A-Z][a-zA-Z\s.,]+?)\s+v[s.]?\s+(.+?)(?:\s+\(Filing|\s+Charleston|\s+and\s+JURY|$)', docket_text_clean, re.IGNORECASE)
                    if versus_match:
                        plaintiff = versus_match.group(1).strip()
                        defendant = versus_match.group(2).strip()
                        
                        # Apply proper title case to plaintiff
                        plaintiff = self._format_plaintiff_name(plaintiff)
                        
                        versus = f"{plaintiff} v. {defendant}"
                        logger.info(f"EXTRACTED versus from docket_text: '{versus}'")
                        
                        versus = self._normalize_versus_field(versus)
                        logger.info(f"NORMALIZED versus: '{versus}'")
                    elif parsed['plaintiff'] and parsed['defendant']:
                        versus = f"{parsed['plaintiff']} v. {parsed['defendant']}"
                        logger.info(f"BEFORE normalization: '{versus}'")
                        versus = self._normalize_versus_field(versus)
                        logger.info(f"AFTER normalization: '{versus}'")
                    else:
                        versus = "No versus info"
                    
                    # Merge with entry data
                    processed_entry = {
                        'date': entry['date'],
                        'doc_num': entry['doc_num'],
                        'doc_url': entry.get('doc_url'),
                        'docket_text': entry['docket_text'],
                        'attorney': parsed['attorney'],
                        'law_firm': law_firm,
                        'plaintiff': parsed['plaintiff'],
                        'defendant': parsed['defendant'],
                        'docket_num': docket_num,
                        'case_number': parsed['case_number'] or docket_num,
                        'versus': versus
                    }
                    processed_data.append(processed_entry)
                
                # Now update all processed entries with session updates
                self._apply_session_updates_to_processed_data(processed_data)
                
                logger.info(f"Found {len(self.new_attorneys)} new attorneys")
                logger.info(f"Updated {len(self.session_attorney_updates)} attorneys in this session")
                progress.advance(task)
                
                # Step 4: Save results
                progress.update(task, description="[cyan]Saving processed data...")
                output_file = self.local_output_dir / f"afff_processed_{self.date_str}.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(processed_data, f, indent=2, ensure_ascii=False)
                
                logger.info(f"✅ Saved processed data to {output_file}")
                
                # Update attorney_law_firms.json (append new attorneys, never overwrite)
                self._update_attorney_law_firms_output(processed_data)
                
                # Note: Attorney lookup is updated immediately during processing
                
                progress.advance(task)
                
            return True
            
        except Exception as e:
            logger.error(f"Error during processing: {e}")
            return False
    
    def _update_attorney_law_firms_output(self, processed_data: List[Dict[str, Any]]) -> None:
        """Update attorney_law_firms.json file with new attorneys (NEVER OVERWRITE)."""
        try:
            # Load existing attorney_law_firms.json
            attorney_law_firms_file = self.local_output_dir / "attorney_law_firms.json"
            if attorney_law_firms_file.exists():
                with open(attorney_law_firms_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            else:
                existing_data = {}
            
            # Process new filings
            new_attorneys_added = []
            for entry in processed_data:
                attorney_name = entry.get('attorney')
                if not attorney_name:
                    continue
                
                # If attorney not in existing data, add them
                if attorney_name not in existing_data:
                    existing_data[attorney_name] = {
                        'law_firm': entry.get('law_firm', 'Unknown'),
                        'filing_count': 0,
                        'filings': []
                    }
                    new_attorneys_added.append(attorney_name)
                    logger.info(f"Added new attorney to output: {attorney_name} -> {entry.get('law_firm', 'Unknown')}")
                
                # Always update filing info (append new filings)
                existing_data[attorney_name]['filing_count'] += 1
                existing_data[attorney_name]['filings'].append({
                    'date': entry['date'],
                    'doc_num': entry['doc_num'],
                    'docket_num': entry.get('case_number', ''),
                    'versus': entry.get('versus', '')
                })
            
            # Sort by filing count
            sorted_data = dict(sorted(
                existing_data.items(),
                key=lambda x: x[1]['filing_count'],
                reverse=True
            ))
            
            # Save updated data
            with open(attorney_law_firms_file, 'w', encoding='utf-8') as f:
                json.dump(sorted_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Updated attorney_law_firms.json")
            logger.info(f"   - Total attorneys: {len(sorted_data)}")
            logger.info(f"   - New attorneys added: {len(new_attorneys_added)}")
            
            # Show attorneys with Unknown firms
            unknown_firms = [name for name, data in sorted_data.items() if data['law_firm'] == 'Unknown']
            if unknown_firms:
                logger.warning(f"⚠️ {len(unknown_firms)} attorneys still have Unknown law firms")
                self.console.print("\n[yellow]Attorneys with Unknown law firms:[/yellow]")
                for attorney in sorted(unknown_firms):
                    self.console.print(f"  - {attorney}")
            
        except Exception as e:
            logger.error(f"Error updating attorney law firms file: {e}")
    
    async def _update_attorney_lookup(self) -> None:
        """Update attorney lookup file with new attorneys."""
        try:
            # Create backup first
            self._create_attorney_backup()
            
            # Load current data or create new structure
            if self.attorney_lookup_file.exists():
                with open(self.attorney_lookup_file, 'r', encoding='utf-8') as f:
                    lookup_data = json.load(f)
            else:
                lookup_data = {"attorneys": {}, "metadata": {}}
            
            # Add new attorneys
            added_count = 0
            for attorney_name in self.new_attorneys:
                if attorney_name not in lookup_data["attorneys"]:
                    lookup_data["attorneys"][attorney_name] = {
                        "law_firm": "Unknown",
                        "notes": f"Added from AFFF case shell on {self.date_str}"
                    }
                    added_count += 1
                    logger.info(f"Added new attorney to lookup: {attorney_name}")
            
            # Update metadata
            lookup_data["metadata"]["total_attorneys"] = len(lookup_data["attorneys"])
            lookup_data["metadata"]["last_updated"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Save updated lookup
            with open(self.attorney_lookup_file, 'w', encoding='utf-8') as f:
                json.dump(lookup_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Added {added_count} new attorneys to lookup file")
            
            # Save list of new attorneys
            new_attorneys_file = self.local_output_dir / "new_attorneys_added.json"
            with open(new_attorneys_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "date": self.date_str,
                    "new_attorneys": self.new_attorneys,
                    "count": len(self.new_attorneys)
                }, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Saved new attorneys list to {new_attorneys_file}")
            
        except Exception as e:
            logger.error(f"Error updating attorney lookup: {e}")
    
    def _parse_html_table(self, html_content: str) -> List[Dict[str, Any]]:
        """Parse HTML table to extract docket entries."""
        entries = []
        
        # Looking for the specific table structure with Date Filed, #, and Docket Text
        # The actual data rows have this pattern:
        # <tr><td width="94" valign="top" nowrap="">DATE</td><td...>DOC_NUM</td><td valign="top">DOCKET_TEXT</td></tr>
        
        import re
        
        # More specific pattern for the actual docket entries
        row_pattern = r'<tr>\s*<td[^>]*width="94"[^>]*>(.*?)</td>\s*<td[^>]*>(.*?)</td>\s*<td[^>]*valign="top"[^>]*>(.*?)</td>\s*</tr>'
        
        matches = re.finditer(row_pattern, html_content, re.DOTALL | re.IGNORECASE)
        
        for match in matches:
            date_cell = match.group(1).strip()
            doc_num_cell = match.group(2).strip()
            docket_text_cell = match.group(3).strip()
            
            # Clean the date (remove any remaining tags)
            date = re.sub(r'<[^>]+>', '', date_cell).strip()
            
            # Extract doc number and URL from the link
            # The doc number is in an anchor tag like: <a href="https://ecf.scd.uscourts.gov/doc1/163014838041">4327</a>
            doc_link_match = re.search(r'<a\s+href="([^"]+)"[^>]*>(\d+)</a>', doc_num_cell)
            doc_url = None
            if doc_link_match:
                doc_url = doc_link_match.group(1)
                doc_num = doc_link_match.group(2)
            else:
                # Fallback: just extract any number
                doc_num = re.sub(r'<[^>]+>', '', doc_num_cell).strip()
            
            # Clean docket text
            # Remove HTML comments like <!--SB-->
            docket_text = re.sub(r'<!--.*?-->', '', docket_text_cell)
            # Remove all HTML tags but preserve text
            docket_text = re.sub(r'<[^>]+>', ' ', docket_text).strip()
            # Normalize whitespace
            docket_text = ' '.join(docket_text.split())
            
            # Skip if not a valid entry
            if not date or not doc_num or not doc_num.isdigit():
                continue
            
            # Skip header row
            if date.lower() == 'date filed' or doc_num == '#':
                continue
            
            # Extract case number from docket text
            # Looking for pattern like "2:25-cv-06138-RMG"
            case_match = re.search(r'(\d+:\d+-cv-\d+)(?:-[A-Z]+)?', docket_text)
            docket_num = case_match.group(1) if case_match else f"AFFF_{doc_num}"
            
            # Convert date to YYYYMMDD
            try:
                # Parse MM/DD/YYYY format (which is what's in the HTML)
                date_obj = datetime.strptime(date, '%m/%d/%Y')
                date_yyyymmdd = date_obj.strftime('%Y%m%d')
            except:
                logger.warning(f"Could not parse date: {date}")
                date_yyyymmdd = date  # Keep original if parsing fails
            
            entries.append({
                'date': date_yyyymmdd,
                'doc_num': doc_num,
                'doc_url': doc_url,
                'docket_text': docket_text,
                'docket_num': docket_num
            })
            
            logger.debug(f"Parsed entry: doc#{doc_num}, date={date_yyyymmdd}, case={docket_num}")
        
        return entries
    
    async def upload_to_dynamodb(self) -> bool:
        """Upload processed data to DynamoDB Pacer table."""
        try:
            # Load processed data
            input_file = self.local_output_dir / f"afff_processed_{self.date_str}.json"
            if not input_file.exists():
                logger.error(f"Processed file not found: {input_file}")
                return False
            
            with open(input_file, 'r', encoding='utf-8') as f:
                processed_data = json.load(f)
            
            if not processed_data:
                logger.warning("No data to upload")
                return True
            
            logger.info(f"Loaded {len(processed_data)} records from {input_file.name}")
            
            # Check for Unknown law firms and prompt user to update them
            await self._handle_unknown_attorneys_during_upload(processed_data)
            
            # Check for duplicates (unless force is enabled)
            if self.force:
                self.console.print(f"\n[bold yellow]🚀 FORCE MODE: Skipping duplicate check, uploading all {len(processed_data)} records[/bold yellow]")
                new_records = processed_data
                existing_records = []
            else:
                existing_records, new_records = await self._check_duplicate_records(processed_data)
                
                if existing_records:
                    self.console.print(f"\n[yellow]Found {len(existing_records)} duplicate records (already in DynamoDB)[/yellow]")
                    logger.info(f"Skipping {len(existing_records)} duplicate records")
                
                if not new_records:
                    self.console.print("[green]✅ All records already exist in DynamoDB - nothing to upload[/green]")
                    return True
            
            # Show preview and get user confirmation
            upload_title = f"Single Date Upload ({self.date_str})" + (" - FORCE MODE" if self.force else "")
            if not self._show_upload_preview(new_records, upload_title):
                self.console.print("[yellow]Upload cancelled by user[/yellow]")
                return False
            
            # Upload records (new records in normal mode, all records in force mode)
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=self.console
            ) as progress:
                task = progress.add_task(
                    f"[cyan]Uploading {len(new_records)} new records to DynamoDB...", 
                    total=len(new_records)
                )
                
                success_count = 0
                fail_count = 0
                
                # Process in batches
                batch_size = 25
                for i in range(0, len(new_records), batch_size):
                    batch = new_records[i:i + batch_size]
                    
                    # Transform and upload each entry
                    tasks = []
                    for entry in batch:
                        transformed = self._transform_for_dynamodb(entry)
                        tasks.append(self.pacer_repo.add_or_update_record(transformed))
                    
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    for result in results:
                        if isinstance(result, Exception):
                            fail_count += 1
                            logger.error(f"Upload error: {result}")
                        elif result:
                            success_count += 1
                        else:
                            fail_count += 1
                    
                    progress.update(task, advance=len(batch))
                
                logger.info(f"✅ Upload complete: {success_count} successful, {fail_count} failed")
                if existing_records:
                    logger.info(f"📋 Summary: {len(existing_records)} duplicates skipped, {success_count} new records uploaded")
                
            return fail_count == 0
            
        except Exception as e:
            logger.error(f"Error during upload: {e}")
            return False
    
    async def _check_duplicate_records(self, records: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Check which records already exist in DynamoDB.
        
        Args:
            records: List of records to check
            
        Returns:
            Tuple of (existing_records, new_records)
        """
        existing_records = []
        new_records = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console
        ) as progress:
            task = progress.add_task(
                "[cyan]Checking for duplicate records...", 
                total=len(records)
            )
            
            for record in records:
                try:
                    # Extract docket_num from the record
                    docket_num = record.get('docket_num', '').strip()
                    if not docket_num:
                        # Use fallback if docket_num is empty
                        docket_num = record.get('case_number', '').strip()
                        if not docket_num:
                            # Use doc_num as fallback with AFFF prefix (like the parsing logic)
                            docket_num = f"AFFF_{record.get('doc_num', 'unknown')}"
                    
                    # Check if record exists in DynamoDB
                    exists = await self.pacer_repo.check_docket_exists('scd', docket_num)
                    
                    if exists:
                        existing_records.append(record)
                        logger.debug(f"Found existing record: {docket_num}")
                    else:
                        new_records.append(record)
                        logger.debug(f"New record: {docket_num}")
                        
                except Exception as e:
                    logger.warning(f"Error checking duplicate for record {record.get('docket_num', 'unknown')}: {e}")
                    # If we can't check, assume it's new to be safe
                    new_records.append(record)
                
                progress.update(task, advance=1)
        
        logger.info(f"Duplicate check complete: {len(existing_records)} existing, {len(new_records)} new")
        return existing_records, new_records

    async def _check_duplicate_filings(self, filings_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Check which filings from attorney comprehensive data already exist in DynamoDB.
        
        Args:
            filings_data: Dictionary with attorneys and their filings
            
        Returns:
            Tuple of (existing_filings, new_filings) where each filing includes attorney info
        """
        existing_filings = []
        new_filings = []
        
        # Count total filings for progress tracking
        total_filings = sum(len(attorney_info['filings']) for attorney_info in filings_data.values())
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console
        ) as progress:
            task = progress.add_task(
                "[cyan]Checking for duplicate filings...", 
                total=total_filings
            )
            
            for attorney_name, attorney_info in filings_data.items():
                law_firm = attorney_info.get('law_firm', 'Unknown')
                filings = attorney_info.get('filings', [])
                
                for filing in filings:
                    try:
                        # Extract docket_num from filing
                        docket_num = filing.get('docket_num', '').strip()
                        if not docket_num:
                            docket_num = f"AFFF_{filing.get('doc_num', 'unknown')}"
                        
                        # Check if record exists in DynamoDB
                        exists = await self.pacer_repo.check_docket_exists('scd', docket_num)
                        
                        # Add attorney info to filing for preview
                        filing_with_attorney = filing.copy()
                        filing_with_attorney['attorney'] = attorney_name
                        filing_with_attorney['law_firm'] = law_firm
                        filing_with_attorney['docket_num'] = docket_num
                        
                        if exists:
                            existing_filings.append(filing_with_attorney)
                            logger.debug(f"Found existing filing: {docket_num}")
                        else:
                            new_filings.append(filing_with_attorney)
                            logger.debug(f"New filing: {docket_num}")
                            
                    except Exception as e:
                        logger.warning(f"Error checking duplicate for filing {filing.get('docket_num', 'unknown')}: {e}")
                        # If we can't check, assume it's new to be safe
                        filing_with_attorney = filing.copy()
                        filing_with_attorney['attorney'] = attorney_name
                        filing_with_attorney['law_firm'] = law_firm
                        # Ensure docket_num is set for error case too
                        docket_num = filing.get('docket_num', '').strip()
                        if not docket_num:
                            docket_num = f"AFFF_{filing.get('doc_num', 'unknown')}"
                        filing_with_attorney['docket_num'] = docket_num
                        new_filings.append(filing_with_attorney)
                    
                    progress.update(task, advance=1)
        
        logger.info(f"Bulk duplicate check complete: {len(existing_filings)} existing, {len(new_filings)} new")
        return existing_filings, new_filings

    def _show_upload_preview(self, records: List[Dict[str, Any]], title: str) -> bool:
        """Show preview of records to be uploaded and ask for user confirmation.
        
        Args:
            records: List of records to preview
            title: Title for the preview
            
        Returns:
            True if user confirms upload, False otherwise
        """
        if not records:
            self.console.print(f"[yellow]No records to upload for {title}[/yellow]")
            return False
        
        # Show summary
        self.console.print(f"\n[bold cyan]{title}[/bold cyan]")
        self.console.print(f"Total records to upload: [bold]{len(records)}[/bold]")
        
        # Create preview table
        table = Table(title="Preview of Records to Upload")
        table.add_column("Docket Number", style="cyan", no_wrap=True)
        table.add_column("Versus", style="green")  # Remove max_width to show full text
        table.add_column("Law Firm", style="yellow")
        
        # Show ALL records
        for i, record in enumerate(records):
            # Use proper fallback logic for docket_num
            docket_num = record.get('docket_num') or record.get('case_number')
            if not docket_num:
                docket_num = f"AFFF_{record.get('doc_num', 'unknown')}"
            
            versus = record.get('versus', 'Unknown')
            law_firm = record.get('law_firm', 'Unknown')
            
            # NO TRUNCATION - show full text to debug the normalization issue
            table.add_row(str(docket_num), versus, law_firm)
        
        self.console.print(table)
        
        # Ask for confirmation
        return Confirm.ask(f"\nProceed with uploading {len(records)} records to DynamoDB?")

    def _transform_for_dynamodb(self, entry: Dict[str, Any]) -> Dict[str, Any]:
        """Transform processed entry to DynamoDB Pacer table format."""
        # Extract attorney and law firm info
        attorneys = []
        law_firms = []
        if entry.get('attorney'):
            law_firm = entry.get('law_firm', 'Unknown')
            attorneys.append({
                'attorney_name': entry['attorney'],
                'law_firm': law_firm
            })
            if law_firm and law_firm != 'Unknown':
                law_firms.append(law_firm)
        
        # Extract plaintiffs and defendants
        plaintiffs = []
        defendants = []
        if entry.get('plaintiff'):
            plaintiffs.append(entry['plaintiff'])
        if entry.get('defendant'):
            defendants.append(entry['defendant'])
        
        # Generate timestamp
        processing_timestamp = datetime.now().isoformat()
        
        # Build DynamoDB record
        record = {
            'filing_date': entry['date'],  # YYYYMMDD format
            'docket_num': entry.get('case_number') or entry.get('docket_num') or f"AFFF_{entry['doc_num']}",
            'court_id': 'scd',
            'doc_num': entry['doc_num'],
            'doc_url': entry.get('doc_url'),
            'versus': entry.get('versus', ''),
            'processing_timestamp': processing_timestamp,
            'service_version': 'afff_case_shell',
            '_saved_timestamp': processing_timestamp,
            '_saved_by': 'afff_case_shell_processor',
            'date_filed': entry['date'],
            'attorney': attorneys,
            'attorneys': attorneys,  # Legacy field
            'attorneys_gpt': attorneys,  # Legacy field
            'lead_case': '2:18-mn-02873-RMG',
            'plaintiff': plaintiffs,
            'plaintiffs': plaintiffs,  # Legacy field
            'plaintiffs_gpt': plaintiffs,  # Legacy field
            'defendant': defendants,
            'defendants': defendants,  # Legacy field
            'assigned_to': 'Judge Richard M. Gergel',
            'nos': '365 P.I.',
            'jurisdiction': 'Diversity',
            'demand': None,
            'cause': '28:1332 Diversity-Personal Injury',
            'mdl_num': '2873',
            'law_firms': law_firms,
            'law_firm': law_firms[0] if law_firms else 'Unknown',
            'transferred_in': False,
            'is_transferred': False,
            'pending_cto': False,
            'is_removal': False,
            's3_link': None,
            'added_on': self.date_str,
            'docket_text': entry.get('docket_text', '')
        }
        
        return record
    
    async def upload_all_to_dynamodb(self, batch_size: int = 5, batch_delay: float = 0.5) -> bool:
        """Upload attorney comprehensive data to DynamoDB.
        
        Args:
            batch_size: Number of items to upload per batch (default: 5)
            batch_delay: Delay in seconds between batches (default: 0.5)
        """
        try:
            # Load attorney_comprehensive_data.json
            comprehensive_file = self.local_output_dir / "attorney_comprehensive_data.json"
            
            if not comprehensive_file.exists():
                logger.error(f"File not found: {comprehensive_file}")
                return False
            
            logger.info(f"Loading {comprehensive_file.name}")
            
            with open(comprehensive_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            attorneys_data = data.get('attorneys', {})
            
            if not attorneys_data:
                logger.warning("No attorney data found in file")
                return True
            
            # Count total filings
            total_filings = sum(len(attorney_info['filings']) for attorney_info in attorneys_data.values())
            logger.info(f"Found {len(attorneys_data)} attorneys with {total_filings} total filings")
            
            # Check for duplicates (unless force is enabled)
            if self.force:
                self.console.print(f"\n[bold yellow]🚀 FORCE MODE: Skipping duplicate check, uploading all {total_filings} filings[/bold yellow]")
                # Convert all filings to the format expected by upload logic
                new_filings = []
                for attorney_name, attorney_info in attorneys_data.items():
                    law_firm = attorney_info.get('law_firm', 'Unknown')
                    for filing in attorney_info.get('filings', []):
                        filing_with_attorney = filing.copy()
                        filing_with_attorney['attorney'] = attorney_name
                        filing_with_attorney['law_firm'] = law_firm
                        # Ensure docket_num is set
                        docket_num = filing.get('docket_num', '').strip()
                        if not docket_num:
                            docket_num = f"AFFF_{filing.get('doc_num', 'unknown')}"
                        filing_with_attorney['docket_num'] = docket_num
                        new_filings.append(filing_with_attorney)
                existing_filings = []
            else:
                existing_filings, new_filings = await self._check_duplicate_filings(attorneys_data)
                
                if existing_filings:
                    self.console.print(f"\n[yellow]Found {len(existing_filings)} duplicate filings (already in DynamoDB)[/yellow]")
                    logger.info(f"Skipping {len(existing_filings)} duplicate filings")
                
                if not new_filings:
                    self.console.print("[green]✅ All filings already exist in DynamoDB - nothing to upload[/green]")
                    return True
            
            # Show preview and get user confirmation
            upload_title = "Bulk Upload (All Attorney Data)" + (" - FORCE MODE" if self.force else "")
            if not self._show_upload_preview(new_filings, upload_title):
                self.console.print("[yellow]Upload cancelled by user[/yellow]")
                return False
            
            # Upload filings (new filings in normal mode, all filings in force mode)
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=self.console
            ) as progress:
                task = progress.add_task(
                    f"[cyan]Uploading {len(new_filings)} new filings to DynamoDB...", 
                    total=len(new_filings)
                )
                
                success_count = 0
                fail_count = 0
                
                # Process new filings in smaller batches with throttling
                for i in range(0, len(new_filings), batch_size):
                    batch = new_filings[i:i + batch_size]
                    
                    # Transform and upload each filing with individual error handling
                    for filing in batch:
                        retry_count = 0
                        max_retries = 3
                        base_delay = 1.0
                        
                        while retry_count <= max_retries:
                            try:
                                # Transform filing to DynamoDB format
                                transformed = self._transform_filing_for_dynamodb(
                                    filing, filing['attorney'], filing['law_firm']
                                )
                                
                                # Upload with retry logic
                                success = await self.pacer_repo.add_or_update_record(transformed)
                                
                                if success:
                                    success_count += 1
                                    break
                                else:
                                    fail_count += 1
                                    logger.warning(f"Failed to upload filing: {filing.get('docket_num', 'unknown')}")
                                    break
                                    
                            except Exception as e:
                                error_msg = str(e)
                                if 'ProvisionedThroughputExceededException' in error_msg or 'throughput' in error_msg.lower():
                                    retry_count += 1
                                    if retry_count <= max_retries:
                                        delay = base_delay * (2 ** (retry_count - 1))  # Exponential backoff
                                        logger.warning(f"Throughput limit hit, waiting {delay}s before retry {retry_count}/{max_retries}")
                                        await asyncio.sleep(delay)
                                        continue
                                
                                fail_count += 1
                                logger.error(f"Error uploading filing {filing.get('docket_num', 'unknown')}: {e}")
                                break
                        
                        progress.update(task, advance=1)
                    
                    # Add delay between batches to avoid throughput limits
                    if i + batch_size < len(new_filings):
                        await asyncio.sleep(batch_delay)
                
                logger.info(f"\n✅ Upload complete: {success_count} successful, {fail_count} failed")
                if existing_filings:
                    logger.info(f"📋 Summary: {len(existing_filings)} duplicates skipped, {success_count} new filings uploaded")
                
            return fail_count == 0
            
        except Exception as e:
            logger.error(f"Error during upload: {e}")
            return False
    
    def _normalize_versus_field(self, versus_text: str) -> str:
        """
        Apply simple, working text normalization to Versus field.
        
        Args:
            versus_text: Original Versus field text
            
        Returns:
            Normalized versus text
        """
        import re
        
        if not versus_text:
            return versus_text
            
        updated_text = versus_text
        
        # Step 1: Remove "and jury demand" (case insensitive)
        updated_text = re.sub(r'\s+and\s+jury\s+demand', '', updated_text, flags=re.IGNORECASE)
        
        # Step 2: Convert "AGC Chemicals Americas Inc." to "AGC Chemicals Inc."
        updated_text = re.sub(r'\bAGC\s+Chemicals\s+Americas\s+Inc\.?', 'AGC Chemicals Inc.', updated_text, flags=re.IGNORECASE)
        
        # Step 3: Handle ALL 3M cases FIRST - if text contains 3M, standardize it
        if re.search(r'\b3M\b', updated_text, re.IGNORECASE):
            # Look for versus pattern with any separator (v., vs, vs., v)
            versus_match = re.search(r'(.+?)\s+v[s.]?\s+.*3M.*', updated_text, re.IGNORECASE)
            if versus_match:
                plaintiff = versus_match.group(1).strip()
                updated_text = f"{plaintiff} v. 3M Company et al."
            else:
                # No versus pattern found, just replace everything with 3M
                updated_text = "3M Company et al."
        
        return updated_text

    def _format_plaintiff_name(self, plaintiff_name: str) -> str:
        """
        Apply proper title case formatting to plaintiff names.
        
        Args:
            plaintiff_name: Raw plaintiff name
            
        Returns:
            Properly formatted plaintiff name
        """
        if not plaintiff_name:
            return plaintiff_name
        
        # Split by spaces and title case each word
        words = plaintiff_name.split()
        formatted_words = []
        
        for word in words:
            # Handle special cases that should remain as-is or have specific formatting
            word_upper = word.upper()
            if word_upper in ['III', 'II', 'IV', 'JR.', 'JR', 'SR.', 'SR']:
                formatted_words.append(word_upper)
            elif word in [',']:
                formatted_words.append(word)
            else:
                # Regular title case
                formatted_words.append(word.capitalize())
        
        return ' '.join(formatted_words)

    def _prompt_for_law_firm(self, attorney_name: str) -> str:
        """Prompt user for law firm name for unknown attorney.
        
        Args:
            attorney_name: Name of the attorney
            
        Returns:
            Law firm name or "Unknown" if user hits enter
        """
        try:
            # Show similar names for reference
            attorney_lower = attorney_name.lower()
            similar_names = [name for name in self.attorney_lookup_map.keys() 
                           if any(word in name.lower() for word in attorney_lower.split())]
            
            self.console.print(f"\n[bold yellow]🔍 Unknown Attorney Found:[/bold yellow]")
            self.console.print(f"  Attorney: [cyan]{attorney_name}[/cyan]")
            
            if similar_names[:3]:
                self.console.print(f"  Similar names in lookup: {similar_names[:3]}")
            
            # Prompt for law firm
            law_firm = Prompt.ask(
                f"Enter law firm for '[cyan]{attorney_name}[/cyan]' (or press Enter for 'Unknown')",
                default="Unknown"
            )
            
            if law_firm.strip():
                law_firm = law_firm.strip()
                if law_firm.lower() != "unknown":
                    self.console.print(f"  ✅ [green]Will use '{law_firm}' for {attorney_name}[/green]")
                    return law_firm
            
            self.console.print(f"  ⚠️ [yellow]'{attorney_name}' will remain as 'Unknown'[/yellow]")
            return "Unknown"
            
        except (KeyboardInterrupt, EOFError):
            self.console.print(f"\n  ⚠️ [yellow]Interrupted - '{attorney_name}' will remain as 'Unknown'[/yellow]")
            return "Unknown"
        except Exception as e:
            logger.error(f"Error prompting for law firm: {e}")
            return "Unknown"
    
    def _update_attorney_lookup_immediate(self, attorney_name: str, law_firm: str) -> None:
        """Update attorney lookup in memory and file immediately (no backup).
        
        Args:
            attorney_name: Name of the attorney
            law_firm: Law firm name
        """
        try:
            # Update in-memory lookup
            self.attorney_lookup[attorney_name] = {
                'law_firm': law_firm,
                'notes': f"Added interactively on {self.date_str}"
            }
            self.attorney_lookup_map[attorney_name.lower()] = attorney_name
            
            # Load current file data
            if self.attorney_lookup_file.exists():
                with open(self.attorney_lookup_file, 'r', encoding='utf-8') as f:
                    lookup_data = json.load(f)
            else:
                lookup_data = {"attorneys": {}, "metadata": {}}
            
            # Update file data
            lookup_data["attorneys"][attorney_name] = {
                "law_firm": law_firm,
                "notes": f"Added interactively on {self.date_str}"
            }
            
            # Update metadata
            lookup_data["metadata"]["total_attorneys"] = len(lookup_data["attorneys"])
            lookup_data["metadata"]["last_updated"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Save updated lookup (no backup)
            with open(self.attorney_lookup_file, 'w', encoding='utf-8') as f:
                json.dump(lookup_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Updated attorney lookup: {attorney_name} -> {law_firm}")
            
        except Exception as e:
            logger.error(f"Error updating attorney lookup: {e}")
    
    def _apply_session_updates_to_processed_data(self, processed_data: List[Dict[str, Any]]) -> None:
        """Apply session attorney updates to all processed entries.
        
        Args:
            processed_data: List of processed entries to update
        """
        if not self.session_attorney_updates:
            return
        
        updates_applied = 0
        for entry in processed_data:
            attorney_name = entry.get('attorney')
            if attorney_name in self.session_attorney_updates:
                old_law_firm = entry.get('law_firm', 'Unknown')
                new_law_firm = self.session_attorney_updates[attorney_name]
                
                if old_law_firm != new_law_firm:
                    entry['law_firm'] = new_law_firm
                    updates_applied += 1
                    logger.info(f"🔄 Updated entry: {attorney_name} -> {new_law_firm}")
        
        if updates_applied > 0:
            logger.info(f"✅ Applied {updates_applied} session updates to processed data")

    async def _handle_unknown_attorneys_during_upload(self, processed_data: List[Dict[str, Any]]) -> None:
        """Handle unknown attorneys during upload by prompting user to update them.
        
        Args:
            processed_data: List of processed entries to check and update
        """
        try:
            # Find all entries with Unknown law firms
            unknown_attorneys = {}
            for entry in processed_data:
                attorney = entry.get('attorney')
                law_firm = entry.get('law_firm')
                
                if attorney and law_firm == "Unknown":
                    if attorney not in unknown_attorneys:
                        unknown_attorneys[attorney] = []
                    unknown_attorneys[attorney].append(entry)
            
            if not unknown_attorneys:
                logger.info("✅ No Unknown attorneys found in processed data")
                return
            
            self.console.print(f"\n[bold yellow]🔍 Found {len(unknown_attorneys)} attorneys with Unknown law firms:[/bold yellow]")
            for attorney, entries in unknown_attorneys.items():
                self.console.print(f"  - {attorney} ({len(entries)} entries)")
            
            # Load attorney lookup for session tracking
            self._load_attorney_lookup()
            self.session_attorney_updates = {}
            
            # Process each unknown attorney
            for attorney, entries in unknown_attorneys.items():
                logger.info(f"🔄 Processing Unknown attorney: {attorney}")
                
                # Prompt for law firm
                new_law_firm = self._prompt_for_law_firm(attorney)
                
                if new_law_firm and new_law_firm != "Unknown":
                    # Update all entries for this attorney
                    for entry in entries:
                        entry['law_firm'] = new_law_firm
                    
                    # Track session update
                    self.session_attorney_updates[attorney] = new_law_firm
                    
                    # Update the attorney lookup file immediately
                    self._update_attorney_lookup_immediate(attorney, new_law_firm)
                    
                    logger.info(f"✅ Updated {len(entries)} entries for {attorney} -> {new_law_firm}")
                else:
                    logger.info(f"⚠️ {attorney} will remain as Unknown")
            
            # Update the processed file with new law firms
            if self.session_attorney_updates:
                input_file = self.local_output_dir / f"afff_processed_{self.date_str}.json"
                with open(input_file, 'w', encoding='utf-8') as f:
                    json.dump(processed_data, f, indent=2, ensure_ascii=False)
                
                logger.info(f"💾 Updated processed file with {len(self.session_attorney_updates)} attorney updates")
                
                # Show summary
                self.console.print(f"\n[bold green]📊 Attorney Updates Summary:[/bold green]")
                for attorney, law_firm in self.session_attorney_updates.items():
                    entry_count = len(unknown_attorneys[attorney])
                    self.console.print(f"  ✅ {attorney} -> {law_firm} ({entry_count} entries)")
            
        except Exception as e:
            logger.error(f"Error handling unknown attorneys during upload: {e}")

    def _transform_filing_for_dynamodb(self, filing: Dict[str, Any], attorney_name: str, law_firm: str) -> Dict[str, Any]:
        """Transform filing from attorney comprehensive data to DynamoDB format.
        Converts date from MM/DD/YYYY to YYYYMMDD for filing_date and added_on fields."""
        
        # Convert date from MM/DD/YYYY to YYYYMMDD
        try:
            date_obj = datetime.strptime(filing['date'], '%m/%d/%Y')
            date_yyyymmdd = date_obj.strftime('%Y%m%d')
        except:
            logger.warning(f"Could not parse date: {filing['date']}")
            # Fallback: try to extract numbers
            date_yyyymmdd = filing['date'].replace('/', '')
        
        # Normalize versus field
        versus = self._normalize_versus_field(filing.get('versus', ''))
        
        # Parse versus to extract plaintiff and defendant
        plaintiff = ''
        defendant = ''
        if ' v. ' in versus:
            parts = versus.split(' v. ', 1)
            plaintiff = parts[0].strip()
            defendant = parts[1].strip()
        
        # Generate timestamp
        processing_timestamp = datetime.now().isoformat()
        
        # Build DynamoDB record
        # Ensure docket_num is never empty
        docket_num = filing.get('docket_num', '').strip()
        if not docket_num:
            docket_num = f"AFFF_{filing.get('doc_num', 'unknown')}"
        
        record = {
            'filing_date': date_yyyymmdd,  # Primary key part 1
            'docket_num': docket_num,  # Primary key part 2
            'court_id': 'scd',
            'doc_num': filing.get('doc_num'),
            'versus': versus,
            'processing_timestamp': processing_timestamp,
            'service_version': 'afff_attorney_comprehensive',
            '_saved_timestamp': processing_timestamp,
            '_saved_by': 'afff_case_shell_processor',
            'date_filed': date_yyyymmdd,
            'added_on': date_yyyymmdd,  # Use converted date for AddedOn
            'attorney': [{
                'attorney_name': attorney_name,
                'law_firm': law_firm
            }],
            'attorneys': [{
                'attorney_name': attorney_name,
                'law_firm': law_firm
            }],
            'attorneys_gpt': [{
                'attorney_name': attorney_name,
                'law_firm': law_firm
            }],
            'lead_case': '2:18-mn-02873-RMG',
            'plaintiff': [plaintiff] if plaintiff else [],
            'plaintiffs': [plaintiff] if plaintiff else [],
            'plaintiffs_gpt': [plaintiff] if plaintiff else [],
            'defendant': [defendant] if defendant else [],
            'defendants': [defendant] if defendant else [],
            'assigned_to': 'Judge Richard M. Gergel',
            'nos': '365 P.I.',
            'jurisdiction': 'Diversity',
            'demand': None,
            'cause': '28:1332 Diversity-Personal Injury',
            'mdl_num': '2873',
            'law_firms': [law_firm] if law_firm and law_firm != 'Unknown' else [],
            'law_firm': law_firm,
            'transferred_in': False,
            'is_transferred': False,
            'pending_cto': False,
            'is_removal': False,
            's3_link': None,
            'docket_text': ''  # Not available in comprehensive data
        }
        
        return record
    
    async def run_interactive(self):
        """Run interactive CLI menu."""
        date_display = self.date_str if self.date_str else "All Dates"
        force_display = " [bold red](FORCE MODE)[/bold red]" if self.force else ""
        self.console.print(Panel.fit(
            "[bold cyan]AFFF Case Shell Processor[/bold cyan]\n"
            f"Date: [yellow]{date_display}[/yellow]{force_display}",
            border_style="cyan"
        ))
        
        await self.initialize_services()
        
        while True:
            self.console.print("\n[bold]Select an option:[/bold]")
            self.console.print("1. Scrape AFFF data from PACER")
            self.console.print("2. Process downloaded data from S3")
            self.console.print("3. Process local HTML file (for testing)")
            self.console.print("4. Upload to DynamoDB")
            self.console.print("5. Run full pipeline (1→2→4)")
            self.console.print("6. Exit")
            
            choice = Prompt.ask("Enter your choice", choices=["1", "2", "3", "4", "5", "6"])
            
            if choice == "1":
                success = await self.scrape_afff_data()
                if success:
                    self.console.print("[green]✅ Scraping completed successfully![/green]")
                else:
                    self.console.print("[red]❌ Scraping failed![/red]")
                    
            elif choice == "2":
                success = await self.process_afff_data()
                if success:
                    self.console.print("[green]✅ Processing completed successfully![/green]")
                else:
                    self.console.print("[red]❌ Processing failed![/red]")
                    
            elif choice == "3":
                # Process local HTML file
                local_file = Prompt.ask("Enter path to local HTML file", default="examples/scd_case_shell.html")
                if Path(local_file).exists():
                    success = await self.process_afff_data(use_local_file=local_file)
                    if success:
                        self.console.print("[green]✅ Processing completed successfully![/green]")
                    else:
                        self.console.print("[red]❌ Processing failed![/red]")
                else:
                    self.console.print(f"[red]File not found: {local_file}[/red]")
                    
            elif choice == "4":
                success = await self.upload_to_dynamodb()
                if success:
                    self.console.print("[green]✅ Upload completed successfully![/green]")
                else:
                    self.console.print("[red]❌ Upload failed![/red]")
                    
            elif choice == "5":
                self.console.print("\n[bold cyan]Running full pipeline...[/bold cyan]")
                
                # Scrape
                if await self.scrape_afff_data():
                    self.console.print("[green]✅ Scraping completed![/green]")
                    
                    # Process
                    if await self.process_afff_data():
                        self.console.print("[green]✅ Processing completed![/green]")
                        
                        # Upload
                        if await self.upload_to_dynamodb():
                            self.console.print("[green]✅ Upload completed![/green]")
                            self.console.print("\n[bold green]🎉 Full pipeline completed successfully![/bold green]")
                        else:
                            self.console.print("[red]❌ Upload failed![/red]")
                    else:
                        self.console.print("[red]❌ Processing failed![/red]")
                else:
                    self.console.print("[red]❌ Scraping failed![/red]")
                    
            elif choice == "6":
                if Confirm.ask("Are you sure you want to exit?"):
                    break
        
        await self.cleanup()
        self.console.print("\n[cyan]Goodbye![/cyan]")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="AFFF Case Shell Processor")
    parser.add_argument('--date', help='Date in YYYYMMDD format')
    parser.add_argument('-all', '--all', action='store_true', help='Upload all items to DynamoDB without date filtering')
    parser.add_argument('--force', action='store_true', help='Force upload, overwriting existing records without checking for duplicates')
    
    args = parser.parse_args()
    
    # Validate arguments
    if not args.all and not args.date:
        console.print("[bold red]Error:[/bold red] Either --date or -all must be specified")
        sys.exit(1)
    
    if args.all and args.date:
        console.print("[bold red]Error:[/bold red] Cannot specify both --date and -all")
        sys.exit(1)
    
    # Validate date format if provided
    if args.date:
        try:
            datetime.strptime(args.date, '%Y%m%d')
        except ValueError:
            console.print("[bold red]Error:[/bold red] Date must be in YYYYMMDD format")
            sys.exit(1)
    
    # Create and run processor
    if args.all:
        # Special mode to upload all existing data
        processor = AFFFCaseShellProcessor(None, force=args.force)
        await processor.initialize_services()
        try:
            success = await processor.upload_all_to_dynamodb()
            if success:
                console.print("[bold green]✅ Successfully uploaded all items to DynamoDB![/bold green]")
            else:
                console.print("[bold red]❌ Upload failed![/bold red]")
        finally:
            await processor.cleanup()
    else:
        # Normal date-based processing
        processor = AFFFCaseShellProcessor(args.date, force=args.force)
        try:
            await processor.run_interactive()
        except KeyboardInterrupt:
            console.print("\n[yellow]Interrupted by user[/yellow]")
            await processor.cleanup()
        except Exception as e:
            console.print(f"\n[bold red]Fatal error:[/bold red] {e}")
            logger.exception("Fatal error")
            await processor.cleanup()
            sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())