<html>
<head>
  <link rel="shortcut icon" href="/favicon.ico">
  <title>CM/ECF LIVE - U.S. District Court for the District of New Jersey</title>
  <script type="text/javascript">var default_base_path = "/"; </script>
  <script type="text/javascript">if (top != self) {
    top.location.replace(location.href);
  }</script>
  <link rel="stylesheet" type="text/css" href="/css/default.css">
  <script type="text/javascript" src="/lib/core.js"></script>
  <link rel="stylesheet" type="text/css" href="/css/print.css" media="print">
  <script type="text/javascript" src="/cgi-bin/menu.pl?id=-1"></script>
</head>
<body bgcolor="FFFFFF" text="000000" onload="SetFocus()">
<iframe id="_yuiResizeMonitor"
        style="position: absolute; visibility: visible; width: 10em; height: 10em; top: -160px; left: -160px; border-width: 0px;"></iframe>
<div id="topmenu" class="yuimenubar yui-module yui-overlay visible"
     style="position: static; display: block; z-index: 30; visibility: visible;">
  <div class="bd"><img src="/graphics/logo-cmecf-sm.png" class="cmecfLogo" id="cmecfLogo" alt="CM/ECF" title="">
    <ul class="first-of-type">
      <li class="yuimenubaritem first-of-type" id="yui-gen0" groupindex="0" index="0"><a class="yuimenubaritemlabel"
                                                                                         href="/cgi-bin/iquery.pl"><u>Q</u>uery</a>
      </li>
      <li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen1" groupindex="0" index="1"><a
        class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Reports">Reports
        <div class="spritedownarrow"></div>
      </a></li>
      <li class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen2" groupindex="0" index="2"><a
        class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Utilities"><u>U</u>tilities
        <div class="spritedownarrow"></div>
      </a></li>
      <li class="yuimenubaritem" id="yui-gen3" groupindex="0" index="3">
        <a class="yuimenubaritemlabel" onclick="CMECF.MainMenu.showHelpPage(); return false">Help</a></li>

      <li class="yuimenubaritem" id="yui-gen4" groupindex="0" index="4"><a class="yuimenubaritemlabel"
                                                                           href="/cgi-bin/login.pl?logout">Log Out</a>
      </li>
    </ul>
    <hr class="hrmenuseparator">
  </div>
</div>
<script type="text/javascript">if (navigator.appVersion.indexOf("MSIE") == -1) {
  window.setTimeout(CMECF.MainMenu.createMenu, 0);
} else {
  CMECF.util.Event.addListener(window, "load", CMECF.MainMenu.createMenu);
}</script>
<div id="cmecfMainContent" style="height: 1141px;"><input type="hidden" id="cmecfMainContentScroll" value="0">
  <script language="JavaScript">
    var IsForm = false;
    var FirstField;

    function SetFocus() {
      if (IsForm) {
        if (FirstField) {
          var ind = FirstField.indexOf('document.', 0);
          if (ind == 0) {
            eval(FirstField);
          } else {
            var Code = "document.forms[0]." + FirstField + ".focus();";
            eval(Code);
          }
        } else {
          var Cnt = 0;
          while (document.forms[0].elements[Cnt] != null) {
            try {
              if (document.forms[0].elements[Cnt].type != "hidden" &&
                !document.forms[0].elements[Cnt].disabled &&
                !document.forms[0].elements[Cnt].readOnly) {
                document.forms[0].elements[Cnt].focus();
                break;
              }
            } catch (e) {
            }
            Cnt += 1;
          }
        }
      }
      return (true);
    }
  </script>
  <form enctype="multipart/form-data" method="POST" action="../cgi-bin/iquery.pl?629623620215048-L_1_0-1">
    <!---ShowPage(iquery.htm)--->

    <!-- RPM Packages: ao-dcecf-web-* -->
    <!-- RPM Permissions: 750 -->
    <!-- RPM Owner: ecf_web -->
    <!-- RPM Group: ecf_web -->
    <!-- RPM Flags: configure -->

    <script src="/lib/DateLib.js" language="Javascript"></script>

    <script language="JavaScript">
      var mon_array = "31/28/31/30/31/30/31/31/30/31/30/31";
      var MON;
      MON = mon_array.split("/");
      var G_yr, G_mn, G_dy;

      function GetNWDData() {
        return ("MNYR2025MN0101040511121819202526MN0201020809121516172223MN0301020809151622232930MN04050612131819202627MN0503041011171824252631MN0601070814151921222829MN07040506121319202627MN0802030910161723243031MN09010607131420212728MN10040511121318192526MN1101020809111516222327282930MN1206071314202125262728MNYR2026MN01030410111718242531MN020107081415212228MN03010708141521222829MN040405111218192526MN0502030910161723243031MN060607131420212728MN070405111218192526MN0801020809151622232930MN090506121319202627MN10030410111718242531MN11010708141521222829MN120506121319202627");
      }
    </script>


    <script language="JavaScript">
      function chkNsearch() {
        var per_type = document.forms[0].UserType.value;
        var str;
        str = document.forms[0].last_name.value +
          document.forms[0].first_name.value +
          document.forms[0].middle_name.value;
        var len = str.length;
        var pos = 0;
        var lname = document.forms[0].last_name.value;
        var lnlen = lname.length;
        var question_mark = 0;
        var cnt = 0;
        if (str.length > 0) {
          while (pos < lnlen) {
            if (lname.charAt(pos) != "*" && lname.charAt(pos) != " " && lname.charAt(pos) != "?") {
              question_mark = 0;
              if (document.forms[0].ExactMatch.checked) {
                return true;
              }
              if (cnt > 0) {
                return (true);
              } else {
                cnt = cnt + 1;
              }
            } else {
              if (per_type != "crt" && per_type != "jud") {
                cnt = 0;
              } else {
                if (lname.charAt(pos) != "?") {
                  cnt = 0;
                  question_mark = 0;
                } else {
                  if (question_mark) {
                    cnt = 0;
                  } else {
                    question_mark = 1;
                  }
                }
              }
            }
            pos = pos + 1;
          }
          if (per_type != "crt" && per_type != "jud") {
            alert("A minimum of two consecutive characters of the last name is required for a name search.");
          } else {
            alert("A minimum of two consecutive characters of the last name (or two characters separated by a question mark) is required for a name search.");
          }
          document.forms[0].last_name.focus();
          return (false);
        }
        return (true);
      }
    </script>


    <script language="JavaScript">

      if (!CMECF.CaseNumberSelect) {
        CMECF.namespace("CaseNumberSelect");
      }

      if (!CMECF.CaseNumberSelect.ClearFields) {
        CMECF.CaseNumberSelect.ClearFields = function () {
        };
      }

      function CheckLooseQry() {
        if (!document.forms[0].lastentry_to.value && !document.forms[0].lastentry_from.value
          && !document.forms[0].Qry_filed_to.value && !document.forms[0].Qry_filed_from.value

          && !preNsearch('JustCheck', 'NotType') && document.forms[0].all_case_ids.value == 0
          && ((document.forms[0].nature_suit.value && !document.forms[0].cause_action.value) ||
            (!document.forms[0].nature_suit.value && document.forms[0].cause_action.value))) {

          var fRet;
          fRet = confirm('This search could produce many records and lead to a large billing charge.  Click OK to run the search.');
          return fRet;
        }
        return true;
      }

      function preNsearch(JustCheck, NotType) {
        // prepare for name search
        var gotname = false
        // It turns out that during the preCsearch, we blank out
        // name fields (just in case) which constitutes a "change",
        // which triggers preNsearch, which then blanks name, so we
        // will have to use gotname to detect that name clues exist.
        for (var i = 0; i < document.forms[0].elements.length; i++) {
          if (document.forms[0].elements[i].name == "last_name" ||
            document.forms[0].elements[i].name == "first_name" ||
            document.forms[0].elements[i].name == "middle_name" ||
            (document.forms[0].elements[i].name == "person_type" && !NotType) ||
            document.forms[0].elements[i].name == "Prisoner_id") {
            if (document.forms[0].elements[i].value > "")
              gotname = true;
          }
        }

        if (gotname != true)
          return (false);
        if (!JustCheck) CMECF.CaseNumberSelect.ClearFields();
        return (true);
      }


      function CheckStatusSel() {
        for (var i = 0; i < document.forms[0].case_status.length; i++) {
          if (document.forms[0].case_status[i].checked)
            return true;
        }
        return false;
      }

      function VerifyDate(datefield) {
        function takeYear(theDate) {
          x = theDate.getYear();
          var y = x % 100;
          y += (y < 38) ? 2000 : 1900;
          return y;
        }

        var today = new Date();
        var current_date = (today.getMonth() + 1) + "/" + today.getDate() + "/" + takeYear(today);
        if (datefield.value != "") {
          CMECF.CaseNumberSelect.ClearFields();
        }
        if (!CheckDate(datefield.value, 0, datefield.forms, 'Date Filed')) {
          datefield.focus();
          datefield.select();
          return (false);
        }
        if (datefield.name == 'Qry_filed_from') {
          if (CompareDateFT(current_date, datefield.value, 'From Date Filed ' + datefield.value + ' must not be later than Current Date ' + current_date) == false) {
            return (false);
          }
          if (CompareDateFT(document.forms[0].Qry_filed_to.value, datefield.value, 'To Date Filed ' + document.forms[0].Qry_filed_to.value + ' must be later than From Date Filed ' + datefield.value) == false) {
            return (false);
          }
        }
        if (datefield.name == 'Qry_filed_to') {
          if (CompareDateFT(current_date, datefield.value, 'To Date Filed ' + datefield.value + ' must not be later than Current Date ' + current_date) == false) {
            return (false);
          }
          if (CompareDateFT(datefield.value, document.forms[0].Qry_filed_from.value, 'To Date Filed ' + datefield.value + ' must be later than From Date Filed ' + document.forms[0].Qry_filed_from.value) == false) {
            return (false);
          }
        }
        if (datefield.name == 'lastentry_to') {
          if (CompareDateFT(current_date, datefield.value, 'Last Entry Date To ' + datefield.value + ' must not be later than Current Date ' + current_date) == false) {
            return (false);
          }
          if (CompareDateFT(datefield.value, document.forms[0].lastentry_from.value, 'Last Entry Date To ' + datefield.value + ' must be later than Last Entry Date From ' + document.forms[0].lastentry_from.value) == false) {
            return (false);
          }
        }
        if (datefield.name == 'lastentry_from') {
          if (CompareDateFT(current_date, datefield.value, 'Last Entry Date From ' + datefield.value + ' must not be later than Current Date ' + current_date) == false) {
            return (false);
          }
          if (CompareDateFT(document.forms[0].lastentry_to.value, datefield.value, 'Last Entry Date To ' + document.forms[0].lastentry_to.value + ' must be later than Last Entry Date From ' + datefield.value) == false) {
            return (false);
          }
        }
        return (true);
      }


      function prompt(msg) {
        window.status = msg
      }

      function clearDesktopCookie() {
        document.cookie = "uiexperience=dtp;secure;path=/;expires=Thu, 01-Jan-1970 00:00:01 GMT';";
      }

    </script>

    <input type="hidden" name="UserType" value="">
    <h2 style="color:#0000cc; margin-top:0em">Query</h2>


    <table border="1" align="center">
      <tbody>
      <tr>
        <th><font size="+1">
          WARNING: Search results from this screen are NOT subject to the 30 page limit<br>on PACER charges. Please be
          as specific as possible with your search criteria.
        </font></th>
      </tr>
      </tbody>
    </table>
    <br>

    <table border="0" cellpadding="6" cellspacing="0">
      <tbody>
      <tr>

        <td bgcolor="#cccccc">
          <b>Search Clues</b>

          <div style="float:right; margin: auto !important; margin: -1.2em 0 0 0;" class="legend"><a
            href="mobile_query.pl" onclick="clearDesktopCookie()">Mobile Query</a></div>

          <table bgcolor="#ffffcc" border="0" cellpadding="4" cellspacing="0">
            <tbody>
            <tr>
              <td valign="top"> Case Number</td>
              <td>

                <input type="hidden" id="all_case_ids" name="all_case_ids" style="background-color:lightblue" value="0"><span
                id="case_number_area" focus_next="1" optional="1" style="white-space: nowrap;">
&nbsp;
<input type="text" id="case_number_text_area_0" name="case_num" autocomplete="off"><input type="button"
                                                                                          id="case_number_show_button_0"
                                                                                          style="display: none; width: 8em;"
                                                                                          value="Show Case List"><input
                type="button" id="case_number_hide_button_0" style="display: none; width: 8em;"
                value="Hide Case List"><input type="button" id="case_number_find_button_0"
                                              style="display: none; width: 8em;" value="Find This Case"><span
                id="case_number_message_area_0"></span><div id="case_number_pick_area_0"
                                                            style="display: none;"></div></span>
                <script language="javascript">setTimeout(function () {
                  new CMECF.CaseNumberSelect('cookie', '')
                }, 0)</script>


              </td>
            </tr>
            <tr>
              <td colspan="2" align="center"><b>or search by</b></td>
            </tr>
            <tr>
              <td> Case Status:</td>
              <td>
                <input type="radio" name="case_status" value="open" onclick="CMECF.CaseNumberSelect.ClearFields();">Open&nbsp;&nbsp;&nbsp;
                <input type="radio" name="case_status" value="closed" onclick="CMECF.CaseNumberSelect.ClearFields();">Closed&nbsp;&nbsp;&nbsp;
                <input type="radio" name="case_status" value="all" onclick="CMECF.CaseNumberSelect.ClearFields();">All&nbsp;&nbsp;&nbsp;
              </td>
            </tr>
            <tr>
              <td> Filed Date</td>
              <td>
                <input type="text" size="10" maxlength="10" name="Qry_filed_from"
                       onchange="if(!VerifyDate(this)) {this.focus();this.select();return false;} else{return true;}">
                to
                <input type="text" size="10" maxlength="10" name="Qry_filed_to"
                       onchange="if(!VerifyDate(this)) {this.focus();this.select();return false;} else{return true;}">
              </td>
            </tr>
            <tr>
              <td> Last Entry Date</td>
              <td>
                <input type="text" size="10" maxlength="10" name="lastentry_from"
                       onchange="if(!VerifyDate(this)) {this.focus();this.select();return false;} else{return true;}">
                to
                <input type="text" size="10" maxlength="10" name="lastentry_to"
                       onchange="if(!VerifyDate(this)) {this.focus();this.select();return false;} else{return true;}">
              </td>
            </tr>

            <tr>
              <td> Nature of Suit</td>
              <td>
                <select id="nature_suit" name="nature_suit" multiple="" size="4">
                  <option></option>
                  <option value="0">0 (zero)
                  </option>
                  <option value="110">110 (Insurance)
                  </option>
                  <option value="120">120 (Contract: Marine)
                  </option>
                  <option value="130">130 (Miller Act)
                  </option>
                  <option value="140">140 (Negotiable Instrument)
                  </option>
                  <option value="150">150 (Contract: Recovery/Enforcement)
                  </option>
                  <option value="151">151 (Contract: Recovery Medicare)
                  </option>
                  <option value="152">152 (Contract: Recovery Student Loan)
                  </option>
                  <option value="153">153 (Contract: Recovery Veteran Ben.)
                  </option>
                  <option value="160">160 (Stockholders Suits)
                  </option>
                  <option value="190">190 (Contract: Other)
                  </option>
                  <option value="195">195 (Contract Product Liability)
                  </option>
                  <option value="196">196 (Contract: Franchise)
                  </option>
                  <option value="210">210 (Condemnation)
                  </option>
                  <option value="220">220 (Real Property: Foreclosure)
                  </option>
                  <option value="230">230 (Rent Lease &amp; Ejectment)
                  </option>
                  <option value="240">240 (Torts to Land)
                  </option>
                  <option value="245">245 (Tort Product Liability)
                  </option>
                  <option value="290">290 (Real Property: Other)
                  </option>
                  <option value="310">310 (Airplane)
                  </option>
                  <option value="315">315 (Airplane Product Liability)
                  </option>
                  <option value="320">320 (Assault Libel &amp; Slander)
                  </option>
                  <option value="330">330 (Federal Employer's Liability)
                  </option>
                  <option value="340">340 (Marine)
                  </option>
                  <option value="345">345 (Marine Product Liability)
                  </option>
                  <option value="350">350 (Motor Vehicle)
                  </option>
                  <option value="355">355 (Motor Vehicle Prod. Liability)
                  </option>
                  <option value="360">360 (P.I.: Other)
                  </option>
                  <option value="362">362 (Personal Inj. Med. Malpractice)
                  </option>
                  <option value="365">365 (Personal Inj. Prod. Liability)
                  </option>
                  <option value="367">367 (Personal Injury: Health Care/Pharmaceutical Personal Injury Product
                    Liability)
                  </option>
                  <option value="368">368 (P.I. : Asbestos)
                  </option>
                  <option value="370">370 (Other Fraud)
                  </option>
                  <option value="371">371 (Truth in Lending)
                  </option>
                  <option value="375">375 (Other Statutes: False Claims Act)
                  </option>
                  <option value="376">376 (Qui Tam (31 U.S.C. § 3729(a)))
                  </option>
                  <option value="380">380 (Personal Property: Other)
                  </option>
                  <option value="385">385 (Prop. Damage Prod. Liability)
                  </option>
                  <option value="400">400 (State Reapportionment)
                  </option>
                  <option value="410">410 (Anti-Trust)
                  </option>
                  <option value="422">422 (Bankruptcy Appeal (801))
                  </option>
                  <option value="423">423 (Bankruptcy Withdrawal)
                  </option>
                  <option value="430">430 (Banks and Banking)
                  </option>
                  <option value="440">440 (Civil Rights: Other)
                  </option>
                  <option value="441">441 (Civil Rights: Voting)
                  </option>
                  <option value="442">442 (Civil Rights: Jobs)
                  </option>
                  <option value="443">443 (Civil Rights: Accommodations)
                  </option>
                  <option value="444">444 (Civil Rights Welfare)
                  </option>
                  <option value="445">445 (Civil Rights: Americans with Disabilities - Employment)
                  </option>
                  <option value="446">446 (Civil Rights: Americans with Disabilities - Other)
                  </option>
                  <option value="448">448 (Civil Rights: Education)
                  </option>
                  <option value="450">450 (Commerce ICC Rates, Etc.)
                  </option>
                  <option value="460">460 (Deportation)
                  </option>
                  <option value="462">462 (Naturalization Application)
                  </option>
                  <option value="463">463 (Habeas Corpus - Alien Detainee)
                  </option>
                  <option value="465">465 (Other Immigration Actions)
                  </option>
                  <option value="470">470 (Racketeer/Corrupt Organization)
                  </option>
                  <option value="480">480 (Consumer Credit)
                  </option>
                  <option value="485">485 (Telephone Consumer Protection Act (TCPA))
                  </option>
                  <option value="490">490 (Cable/Satellite TV)
                  </option>
                  <option value="510">510 (Prisoner: Vacate Sentence)
                  </option>
                  <option value="530">530 (Habeas Corpus (General))
                  </option>
                  <option value="535">535 (Death Penalty - Habeas Corpus)
                  </option>
                  <option value="540">540 (Mandamus &amp; Other)
                  </option>
                  <option value="550">550 (Prisoner: Civil Rights)
                  </option>
                  <option value="555">555 (Prison Condition)
                  </option>
                  <option value="560">560 (Prisoner Petitions: Civil Detainee: Conditions of Confinement)
                  </option>
                  <option value="610">610 (Forfeiture and Penalty - Agricultural Acts)
                  </option>
                  <option value="620">620 (Forfeiture and Penalty - Food and Drug Acts)
                  </option>
                  <option value="625">625 (Drug Related Seizure of Property)
                  </option>
                  <option value="630">630 (Liquor Laws)
                  </option>
                  <option value="640">640 (Forfeiture and Penalty - Railroad and Trucks)
                  </option>
                  <option value="650">650 (Airline Regulations)
                  </option>
                  <option value="660">660 (Occupational Safety And Health)
                  </option>
                  <option value="690">690 (Forfeit/Penalty: Other)
                  </option>
                  <option value="710">710 (Labor: Fair Standards)
                  </option>
                  <option value="720">720 (Labor: Labor/Mgt. Relations)
                  </option>
                  <option value="730">730 (Labor Management Report &amp; Disclosure)
                  </option>
                  <option value="740">740 (Labor: Railway Labor Act)
                  </option>
                  <option value="751">751 (Labor: Family and Medical Leave Act)
                  </option>
                  <option value="790">790 (Labor: Other)
                  </option>
                  <option value="791">791 (Labor: E.R.I.S.A.)
                  </option>
                  <option value="810">810 (Selective Service)
                  </option>
                  <option value="820">820 (Copyright)
                  </option>
                  <option value="830">830 (Patent)
                  </option>
                  <option value="835">835 (Patent - Abbreviated New Drug Application(ANDA))
                  </option>
                  <option value="840">840 (Trademark)
                  </option>
                  <option value="850">850 (Securities/Commodities)
                  </option>
                  <option value="861">861 (Social Security: HIA)
                  </option>
                  <option value="862">862 (Social Security: Black Lung)
                  </option>
                  <option value="863">863 (Social Security: DIWC/DIWW)
                  </option>
                  <option value="864">864 (Social Security: SSID Tit. XVI)
                  </option>
                  <option value="865">865 (Social Security: RSI Tax Suits)
                  </option>
                  <option value="870">870 (Taxes)
                  </option>
                  <option value="871">871 (Tax Suits: IRS-Third Party)
                  </option>
                  <option value="875">875 (Customer Challenge 12 USC 3410)
                  </option>
                  <option value="880">880 (Defend Trade Secrets Act (of 2016))
                  </option>
                  <option value="890">890 (Other Statutory Actions)
                  </option>
                  <option value="891">891 (Agriculture Acts)
                  </option>
                  <option value="892">892 (Economic Stabilization Act)
                  </option>
                  <option value="893">893 (Environmental Matters)
                  </option>
                  <option value="894">894 (Energy Allocation Act)
                  </option>
                  <option value="895">895 (Freedom of Information Act)
                  </option>
                  <option value="896">896 (Other Statutes: Arbitration)
                  </option>
                  <option value="899">899 (Other Statutes: Administrative Procedures Act/Review or Appeal of Agency
                    Decision)
                  </option>
                  <option value="900">900 (Appeal of Fee Determination - Equal Access to Justice)
                  </option>
                  <option value="950">950 (Constitutional - State Statute)
                  </option>
                  <option value="990">990 (Other)
                  </option>
                  <option value="999">999 (Invalid)
                  </option>
                </select>
                <script>document.forms[0].nature_suit.onchange = CMECF.CaseNumberSelect.ClearFields;</script>
              </td>
            </tr>
            <tr>
              <td> Cause of Action</td>
              <td>
                <select id="cause_action" name="cause_action" multiple="" size="4">
                  <option></option>
                  <option value="0">0 (No cause code entered)
                  </option>
                  <option value="02:0431">02:0431 (02:431 Fed. Election Commission: Failure Enforce C)
                  </option>
                  <option value="02:0437">02:0437 (02:437 Federal Election Commission)
                  </option>
                  <option value="05:0075">05:0075 (05:75(2) Contract - Reduction in Grade)
                  </option>
                  <option value="05:0551">05:0551 (05:551 Administrative Procedure Act)
                  </option>
                  <option value="05:0552fi">05:0552fi (05:552 Freedom of Information Act)
                  </option>
                  <option value="05:0552pa">05:0552pa (05:552 Right to Privacy Act)
                  </option>
                  <option value="05:0554">05:0554 (05:0554 Constitutionality of Maritime Statutes)
                  </option>
                  <option value="05:0701">05:0701 (05:0701 Maritime Subsidy Board)
                  </option>
                  <option value="05:0702">05:0702 (05:702 Administrative Procedure Act)
                  </option>
                  <option value="05:0704">05:0704 (05:704 Labor Litigation)
                  </option>
                  <option value="05:7703">05:7703 (05:7703 Discrimination - Review of Agency Act)
                  </option>
                  <option value="05:8470">05:8470 (05:8470 Enforcement of Judgment against Retirement Benefits)
                  </option>
                  <option value="07:0006">07:0006 (7:6(b) Federal Commodity Exchange Regulation)
                  </option>
                  <option value="07:0025">07:0025 (7:25 Fraud - Commodities Leverage Contracts)
                  </option>
                  <option value="07:0181">07:0181 (07:181 Packers &amp; Stockyard Act)
                  </option>
                  <option value="07:0499">07:0499 (07:499 Agricultural Commodities Act)
                  </option>
                  <option value="07:0601">07:0601 (07:601 USDA Condemnation)
                  </option>
                  <option value="07:2321">07:2321 (07:2321 Plant Variety Protection Act)
                  </option>
                  <option value="08:1105">08:1105 (8:1105(a) Aliens: Habeas Corpus to Release INS Det)
                  </option>
                  <option value="08:1182">08:1182 (8:1182 Defend. Denial of Pla. Appl. for Alien Employment Cer)
                  </option>
                  <option value="08:1252">08:1252 (08:1252(a)(2) Injunction for Deportation)
                  </option>
                  <option value="08:1260">08:1260 (08:1260 Aliens: Access to Records)
                  </option>
                  <option value="08:1324">08:1324 (08:1324 Aliens: Complaint for Forfeiture)
                  </option>
                  <option value="08:1329">08:1329 (08:1329 Writ of Mandamus to Adjudicate Visa Petiti)
                  </option>
                  <option value="08:1446">08:1446 (8:1446 Petition for Naturalization Hearing)
                  </option>
                  <option value="08:1451(j)">08:1451(j) (8:1451(j) Motion to Correct Order &amp; Judg. of
                    Naturalization)
                  </option>
                  <option value="09:0001">09:0001 (09:1 U.S. Arbitration Act)
                  </option>
                  <option value="09:0009">09:0009 (9:9 Motion to Confirm Arbitration Loan)
                  </option>
                  <option value="09:0010">09:0010 (09:0010 Petition to Vacate Arbitration Award)
                  </option>
                  <option value="10:1408">10:1408 (10:1408 Claim for Military Retirement)
                  </option>
                  <option value="10:1552">10:1552 (10:1552 Armed Forces: Action to Correct Records)
                  </option>
                  <option value="10:1553">10:1553 (10:1553 Armed Forces: FOIA General)
                  </option>
                  <option value="10:2305">10:2305 (10:2305 Review of Federal Contract)
                  </option>
                  <option value="11:0101">11:0101 (11:101 Bankruptcy)
                  </option>
                  <option value="12:0022">12:0022 (12:22 Securities Fraud)
                  </option>
                  <option value="12:0635">12:0635 (12:635 Breach of Insurance Contract)
                  </option>
                  <option value="12:1461">12:1461 (12:1461 Homeowners Loan Act)
                  </option>
                  <option value="12:1464">12:1464 (12:1464 Federal Savings and Loan)
                  </option>
                  <option value="12:1703">12:1703 (12:1703 Default of HUD Loan)
                  </option>
                  <option value="12:1725">12:1725 (12:1725 Collection under Contract Guaranty)
                  </option>
                  <option value="12:1730">12:1730 (12:1730 Collection Under Contract Guaranty)
                  </option>
                  <option value="12:1819">12:1819 (12:1819 Default of Promissory Note)
                  </option>
                  <option value="12:1821">12:1821 (12:1821 Default of Loan by Promissary Note)
                  </option>
                  <option value="12:191">12:191 (12:191 Bank Foreclosure)
                  </option>
                  <option value="12:192">12:192 (12:192 Liquidation of National Bank)
                  </option>
                  <option value="12:1971">12:1971 (12:1971 Relief under Bank Holding Act)
                  </option>
                  <option value="12:1972">12:1972 (12:1972 Tying Arrangement Act)
                  </option>
                  <option value="12:3410">12:3410 (12:3410 Right to Financial Privacy Act)
                  </option>
                  <option value="15:0001">15:0001 (15:1 Antitrust Litigation)
                  </option>
                  <option value="15:0002at">15:0002at (15:2 Antitrust Litigation)
                  </option>
                  <option value="15:0002fl">15:0002fl (15:2(a) Fair Labor Standards Act)
                  </option>
                  <option value="15:0005">15:0005 (15:5(a) Fair Labor Standards Act)
                  </option>
                  <option value="15:0015">15:0015 (15:15 Antitrust Litigation)
                  </option>
                  <option value="15:0025">15:0025 (15:25 Clayton Act)
                  </option>
                  <option value="15:0044">15:0044 (15:44 Trademark Infringement)
                  </option>
                  <option value="15:0045">15:0045 (15:0045 Federal Trade Commission Act)
                  </option>
                  <option value="15:0052">15:0052 (15:0052 Federal Trade Commission Act)
                  </option>
                  <option value="15:0053">15:0053 (15:0053 Federal Trade Commission Act)
                  </option>
                  <option value="15:0077">15:0077 (15:77 Securities Fraud)
                  </option>
                  <option value="15:0078">15:0078 (15:78m(a) Securities Exchange Act)
                  </option>
                  <option value="15:0631">15:0631 (15:631 Small Business Act)
                  </option>
                  <option value="15:0717">15:0717 (15:717 Natural Gas Act)
                  </option>
                  <option value="15:0754">15:0754 (15:754 Emergency Petroleum Allocation-Admin.&amp; Enforcement)
                  </option>
                  <option value="15:1051">15:1051 (15:1051 Trademark Infringement)
                  </option>
                  <option value="15:1114">15:1114 (15:1114 Trademark Infringement)
                  </option>
                  <option value="15:1121">15:1121 (15:1121 Trademark Infringement)
                  </option>
                  <option value="15:1125">15:1125 (15:1125 Trademark Infringement (Lanham Act))
                  </option>
                  <option value="15:1126">15:1126 (15:1126 Patent Infringement)
                  </option>
                  <option value="15:1127">15:1127 (15:1127 Trademark Infringement)
                  </option>
                  <option value="15:1601">15:1601 (15:1601 Truth in Lending)
                  </option>
                  <option value="15:1640">15:1640 (15:1640 Truth in Lending)
                  </option>
                  <option value="15:1681">15:1681 (15:1681 Fair Credit Reporting Act)
                  </option>
                  <option value="15:1692">15:1692 (15:1692 Fair Debt Collection Act)
                  </option>
                  <option value="15:1938">15:1938 (15:1938 Fair Labor Standards Act)
                  </option>
                  <option value="15:1981">15:1981 (15:1981 Fraud-Motor Vehicle (Odometer))
                  </option>
                  <option value="15:1988">15:1988 (15:1988 Fraud-Motor Vehicle (Odometer))
                  </option>
                  <option value="15:1989">15:1989 (15:1989 Fraud-Motor Vehicle (Odometer))
                  </option>
                  <option value="15:2301">15:2301 (15:2301 Magnuson-Moss Warranty Act)
                  </option>
                  <option value="15:2801">15:2801 (15:2801 Petroleum Marketing Practices Act)
                  </option>
                  <option value="15:53(b)">15:53(b) (15:53(b) - Prelim &amp; Perm Inj Relief &amp; other Equita)
                  </option>
                  <option value="16:0668">16:0668 (16:668 Bald Eagle Protection Act)
                  </option>
                  <option value="16:0703">16:0703 (16:703 Migratory Bird Act)
                  </option>
                  <option value="16:1538">16:1538 (16:1538 Endangered Species Act)
                  </option>
                  <option value="16:3371">16:3371 (16:3371 Wildlife Under the Lacey Act)
                  </option>
                  <option value="16:3372">16:3372 (16:3372 Conservation: Complaint for Forfeiture)
                  </option>
                  <option value="16:3373">16:3373 (16:3373 Appeal of Decision - DOI)
                  </option>
                  <option value="16:3374">16:3374 (16:3374 Conservation: Complaint for Forfeiture)
                  </option>
                  <option value="17:0101">17:0101 (17:101 Copyright Infringement)
                  </option>
                  <option value="17:0501">17:0501 (17:501 Copyright Infringement)
                  </option>
                  <option value="17:0504">17:0504 (17:504 Copyright Infringement)
                  </option>
                  <option value="18:0241">18:0241 (18:241 Conspiracy Against Citizen Rights)
                  </option>
                  <option value="18:157(3)">18:157(3) (18:157(3) Bankruptcy Fraud)
                  </option>
                  <option value="18:1836a">18:1836a (18:1836(a) Injunction against Misappropriation of Trade Secrets)
                  </option>
                  <option value="18:1836b">18:1836b (18:1836(b) Civil Action to Protect Trade Secrets)
                  </option>
                  <option value="18:1961">18:1961 (18:1961 Racketeering (RICO) Act)
                  </option>
                  <option value="18:1962">18:1962 (18:1962 Racketeering (RICO) Act)
                  </option>
                  <option value="18:1964">18:1964 (18:1964 Racketeering (RICO) Act)
                  </option>
                  <option value="18:2511">18:2511 (18:2511 Wiretapping)
                  </option>
                  <option value="18:3114">18:3114 (18:3114 Motion for Return of Seized Property)
                  </option>
                  <option value="18:4208">18:4208 (18:4208(B) Agency Action Review)
                  </option>
                  <option value="19:1305">19:1305 (19:1305 Custom Duties:0Forfeiture-Immoral Articles)
                  </option>
                  <option value="20:1080">20:1080 (20:1080 Student Loan Recovery)
                  </option>
                  <option value="20:1400">20:1400 (20:1400 Civil Rights of Handicapped Child)
                  </option>
                  <option value="20:1401">20:1401 (20:1401 Education: Handicapped Child Act)
                  </option>
                  <option value="21:0841">21:0841 (21:841 Forfeiture Property-Drugs)
                  </option>
                  <option value="21:0881">21:0881 (21:881 Forfeiture Property-Drugs)
                  </option>
                  <option value="21:0881a">21:0881a (21:881 Forfeiture Property - Aircraft)
                  </option>
                  <option value="21:0881re">21:0881re (21:881 Forfeiture Property - Real Estate)
                  </option>
                  <option value="23:0134">23:0134 (23:134 P.I.- Auto Negligence)
                  </option>
                  <option value="23:1441">23:1441 (23:1441 Contract Real Estate)
                  </option>
                  <option value="24:1000">24:1000 (24:1000 Hospitals &amp; Asylums: Withdrawal Liability)
                  </option>
                  <option value="25:0640">25:0640 (25:640 Indian Tribal Rights)
                  </option>
                  <option value="25:1901">25:1901 (25:1901 Indian Child Welfare Act)
                  </option>
                  <option value="26:6212">26:6212 (26:6212 Injunctive Relief from IRS Lien)
                  </option>
                  <option value="26:6213">26:6213 (26:6213 Injunctive Relief from IRS Lien)
                  </option>
                  <option value="26:6502">26:6502 (26:6502 IRS:Enforcement of tax liens)
                  </option>
                  <option value="26:6532">26:6532 (26:6532 IRS: Refund of Tax Penalty)
                  </option>
                  <option value="26:6702">26:6702 (26:6702 IRS: Refund of Income Tax Penalty)
                  </option>
                  <option value="26:6703">26:6703 (26:6703 IRS: Refund of Tax Penalty)
                  </option>
                  <option value="26:7401">26:7401 (26:7401 IRS: Tax Liability)
                  </option>
                  <option value="26:7402">26:7402 (26:7402 IRS: Petition to Enforce IRS Summons)
                  </option>
                  <option value="26:7402a">26:7402a (26:7402(a) Injunctive Relief - Internal Revenue Code)
                  </option>
                  <option value="26:7403">26:7403 (26:7403 Suit to Enforce Federal Tax Lien)
                  </option>
                  <option value="26:7422rt">26:7422rt (26:7422 IRS: Refund Taxes)
                  </option>
                  <option value="26:7422rx">26:7422rx (26:7422 IRS: Refund Excise Tax)
                  </option>
                  <option value="26:7426">26:7426 (26:7426 IRS: Wrongful Levy for Taxes)
                  </option>
                  <option value="26:7429">26:7429 (26:7429 IRS: Tax Jeopardy Assessment)
                  </option>
                  <option value="26:7609">26:7609 (26:7609 IRS: Petition to Quash IRS Summons)
                  </option>
                  <option value="27:0185">27:0185 (27:185 Enforcement of Arbitration Award)
                  </option>
                  <option value="28:0157c">28:0157c (28:0157(c)(1) Findings, Concl. &amp; Proposed Judgment)
                  </option>
                  <option value="28:0157d">28:0157d (28:0157 Motion for Withdrawal of Reference)
                  </option>
                  <option value="28:0158">28:0158 (28:0158 Notice of Appeal re Bankruptcy Matter (BA)
                  </option>
                  <option value="28:0185">28:0185 (28:185 Suit to Compel Arbitration)
                  </option>
                  <option value="28:0451">28:0451 (28:451 Employment Discrimination)
                  </option>
                  <option value="28:0754">28:0754 (28:754 Receiver of Property in Different Districts)
                  </option>
                  <option value="28:0794">28:0794 (28:794 Rehabilitation Act)
                  </option>
                  <option value="28:1001">28:1001 (28:1001 E.R.I.S.A.)
                  </option>
                  <option value="28:1132">28:1132 (28:1132 E.R.I.S.A.)
                  </option>
                  <option value="28:1330">28:1330 (28:1330 Breach of Contract)
                  </option>
                  <option value="28:1331">28:1331 (28:1331 Fed. Question)
                  </option>
                  <option value="28:1331al">28:1331al (28:1331 Fed. Question: Airline Crash)
                  </option>
                  <option value="28:1331at">28:1331at (28:1331 Fed. Question: Anti-trust)
                  </option>
                  <option value="28:1331au">28:1331au (28:1331 Fed. Question: Auto Negligence)
                  </option>
                  <option value="28:1331b">28:1331b (28:1331 Federal Question: Bivens Act)
                  </option>
                  <option value="28:1331bc">28:1331bc (28:1331 Fed. Question: Breach of Contract)
                  </option>
                  <option value="28:1331ca">28:1331ca (28:1331 Fed Question: Fed Communications Act of 1)
                  </option>
                  <option value="28:1331cm">28:1331cm (28:1331 Fed. Question: Interstate Commerce Act)
                  </option>
                  <option value="28:1331cv">28:1331cv (28:1331 Federal Question: Other Civil Rights)
                  </option>
                  <option value="28:1331ed">28:1331ed (28:1331 Fed. Question: Employment Discrimination)
                  </option>
                  <option value="28:1331ej">28:1331ej (28:1331 Federal Question: Enforcement of Judgment)
                  </option>
                  <option value="28:1331es">28:1331es (28:1331 Enforcement of Administrative Subpoena)
                  </option>
                  <option value="28:1331fl">28:1331fl (28:1331 Fed. Question: Fair Labor Standards)
                  </option>
                  <option value="28:1331ij">28:1331ij (28:1331 Federal Question-Injunctive &amp; Declaratory Relief)
                  </option>
                  <option value="28:1331in">28:1331in (28:1331 Fed. Question: Insurance Contract)
                  </option>
                  <option value="28:1331mm">28:1331mm (28:1331 Fed. Question: Medical Malpractice)
                  </option>
                  <option value="28:1331ng">28:1331ng (28:1331 Fed. Question: Natural Gas Act)
                  </option>
                  <option value="28:1331pi">28:1331pi (28:1331 Fed. Question: Personal Injury)
                  </option>
                  <option value="28:1331rd">28:1331rd (28:1331 Fed. Question: Review Agency Decision)
                  </option>
                  <option value="28:1331rp">28:1331rp (28:1331(a) Fed. Question: Real Property)
                  </option>
                  <option value="28:1331rr">28:1331rr (28:1331 Fed. Question: Railway Labor Act)
                  </option>
                  <option value="28:1331sv">28:1331sv (28:1331 Fed. Question: Securities Violation)
                  </option>
                  <option value="28:1331tr">28:1331tr (28:1331 Fed. Question: Trademark)
                  </option>
                  <option value="28:1331tt">28:1331tt (28:1331 Fed. Question: Tort Action)
                  </option>
                  <option value="28:1331v">28:1331v (28:1331 Fed. Question: Violation 5th &amp; 8th Amendme)
                  </option>
                  <option value="28:1331wl">28:1331wl (28:1331 Federal Question: EPA Waste Lien)
                  </option>
                  <option value="28:1331wt">28:1331wt (28:1331 Fed. Question: Water Rights)
                  </option>
                  <option value="28:1332ac">28:1332ac (28:1332 Diversity-Account Receivable)
                  </option>
                  <option value="28:1332al">28:1332al (28:1332 Diversity-Airline Crash)
                  </option>
                  <option value="28:1332as">28:1332as (28:1332 Diversity-Asbestos Litigation)
                  </option>
                  <option value="28:1332au">28:1332au (28:1332 Diversity-Auto Negligence)
                  </option>
                  <option value="28:1332bc">28:1332bc (28:1332 Diversity-Breach of Contract)
                  </option>
                  <option value="28:1332co">28:1332co (28:1332 Diversity-Conversion)
                  </option>
                  <option value="28:1332ct">28:1332ct (28:1332 Diversity-(Citizenship))
                  </option>
                  <option value="28:1332det">28:1332det (28:1332 Diversity: Forcible Detainer)
                  </option>
                  <option value="28:1332df">28:1332df (28:1332 Diversity-Contract Default)
                  </option>
                  <option value="28:1332ds">28:1332ds (28:1332 Diversity-Contract Dispute)
                  </option>
                  <option value="28:1332ed">28:1332ed (28:1332 Diversity-Employment Discrimination)
                  </option>
                  <option value="28:1332fd">28:1332fd (28:1332 Diversity-Breach of Fiduciary Duty)
                  </option>
                  <option value="28:1332fr">28:1332fr (28:1332 Diversity-Fraud)
                  </option>
                  <option value="28:1332ia">28:1332ia (28:1332 Diversity-Interpleader Action)
                  </option>
                  <option value="28:1332ij">28:1332ij (28:1332 Diversity-Injunctive &amp; Declaratory Relief)
                  </option>
                  <option value="28:1332in">28:1332in (28:1332 Diversity-Insurance Contract)
                  </option>
                  <option value="28:1332jd">28:1332jd (28:1332 Diversity-Declaratory Judgment)
                  </option>
                  <option value="28:1332lb">28:1332lb (28:1332 Diversity-Libel,Assault,Slander)
                  </option>
                  <option value="28:1332lm">28:1332lm (28:1332 Diversity - Legal Malpractice)
                  </option>
                  <option value="28:1332ma">28:1332ma (28:1332 Diversity-Miller Act)
                  </option>
                  <option value="28:1332mm">28:1332mm (28:1332 Diversity-Medical Malpractice)
                  </option>
                  <option value="28:1332mv">28:1332mv (28:1332 Diversity-Motor Vehicle Product Liability)
                  </option>
                  <option value="28:1332ni">28:1332ni (28:1332 Diversity-Negotiable Instrument)
                  </option>
                  <option value="28:1332nm">28:1332nm (28:1332 Diversity-Non-Motor Vehicle)
                  </option>
                  <option value="28:1332nr">28:1332nr (28:1332 Diversity-Notice of Removal)
                  </option>
                  <option value="28:1332oc">28:1332oc (28:1332 Diversity-Other Contract)
                  </option>
                  <option value="28:1332pd">28:1332pd (28:1332 Diversity-Property Damage)
                  </option>
                  <option value="28:1332pi">28:1332pi (28:1332 Diversity-Personal Injury)
                  </option>
                  <option value="28:1332pl">28:1332pl (28:1332 Diversity-Product Liability)
                  </option>
                  <option value="28:1332pr">28:1332pr (28:1332 Diversity-Petition for Removal)
                  </option>
                  <option value="28:1332qt">28:1332qt (28:1332 Diversity-Petition to Quiet Title)
                  </option>
                  <option value="28:1332ri">28:1332ri (28:1332 Diversity-Racketeering (RICO) Act)
                  </option>
                  <option value="28:1332sa">28:1332sa (28:1332 - Diversity: Securities &amp; Exchange Commiss)
                  </option>
                  <option value="28:1332sf">28:1332sf (28:1332 - Diversity: Securities Fraud)
                  </option>
                  <option value="28:1332ss">28:1332ss (28:1332 Diversity - Stockholders Suits)
                  </option>
                  <option value="28:1332tl">28:1332tl (28:1332 Diversity-Torts to Land)
                  </option>
                  <option value="28:1332tm">28:1332tm (28:1332 Diversity-Tort/Motor Vehicle (P.I.))
                  </option>
                  <option value="28:1332tn">28:1332tn (28:1332 Diversity-Tort/Non-Motor Vehicle)
                  </option>
                  <option value="28:1332wd">28:1332wd (28:1332 Diversity-Wrongful Death)
                  </option>
                  <option value="28:1333">28:1333 (28:1333 Admiralty)
                  </option>
                  <option value="28:1334">28:1334 (28:1334 Bankruptcy Appeal)
                  </option>
                  <option value="28:1334c">28:1334c (28:1334(c) R&amp;R re motions for abstention (non-cor)
                  </option>
                  <option value="28:1335">28:1335 (28:1335 Interpleader Action)
                  </option>
                  <option value="28:1337">28:1337 (28:1337 Sherman-Clayton Act)
                  </option>
                  <option value="28:1338cp">28:1338cp (28:1338 Copyright Infringement)
                  </option>
                  <option value="28:1338pt">28:1338pt (28:1338 Patent Infringement)
                  </option>
                  <option value="28:1338tr">28:1338tr (28:1338 Trademark Infringement)
                  </option>
                  <option value="28:1340">28:1340 (28:1340 IRS: Custom Duties)
                  </option>
                  <option value="28:1340er">28:1340er (28:1340 Recovery of Erroneous Refund)
                  </option>
                  <option value="28:1341">28:1341 (28:1341 Complaint for Forfeiture)
                  </option>
                  <option value="28:1343">28:1343 (28:1343 Violation of Civil Rights)
                  </option>
                  <option value="28:1345">28:1345 (28:1345 USA Plaintiff)
                  </option>
                  <option value="28:1345co">28:1345co (28:1345 Replevin &amp; Conversion)
                  </option>
                  <option value="28:1345db">28:1345db (28:1345 Debt to US - FHA/HUD Title I)
                  </option>
                  <option value="28:1345df">28:1345df (28:1345 Default of Promissory Note)
                  </option>
                  <option value="28:1345er">28:1345er (28:1345 Recovery of Erroneous Refund)
                  </option>
                  <option value="28:1345fc">28:1345fc (28:1345 Foreclosure)
                  </option>
                  <option value="28:1345ff">28:1345ff (28:1345 Complaint for Forfeiture)
                  </option>
                  <option value="28:1345hl">28:1345hl (28:1345 VA Home Loan Guaranty Debt)
                  </option>
                  <option value="28:1345ij">28:1345ij (28:1345ij Injunction Relief)
                  </option>
                  <option value="28:1345mc">28:1345mc (28:1345 Medical Care Recovery)
                  </option>
                  <option value="28:1345mi">28:1345mi (28:1345 Mining Claim Ejectment)
                  </option>
                  <option value="28:1345pd">28:1345pd (28:1345 Property Damage)
                  </option>
                  <option value="28:1345pe">28:1345pe (28:1345 Property Ejectment)
                  </option>
                  <option value="28:1345rc">28:1345rc (28:1345 Recovery of Debt to US)
                  </option>
                  <option value="28:1345st">28:1345st (28:1345 Default of Student Loan)
                  </option>
                  <option value="28:1345tp">28:1345tp (28:1345 Trespass on Public Land)
                  </option>
                  <option value="28:1345va">28:1345va (28:1345 Recovery of VA Overpayment)
                  </option>
                  <option value="28:1346bc">28:1346bc (28:1346 Breach of Contract)
                  </option>
                  <option value="28:1346rc">28:1346rc (28:1346 Recovery of IRS Tax)
                  </option>
                  <option value="28:1346tc">28:1346tc (28:1346 Tort Claim)
                  </option>
                  <option value="28:1346wd">28:1346wd (28:1346 Wrongful Death)
                  </option>
                  <option value="28:1349">28:1349 (28:1348 Corporation organized under Federal Law as party)
                  </option>
                  <option value="28:1352">28:1352 (28:1352 Miller Act)
                  </option>
                  <option value="28:1355">28:1355 (28:1355 Petition for Return of Property)
                  </option>
                  <option value="28:1358">28:1358 (28:1358 Land Condemnation)
                  </option>
                  <option value="28:1361">28:1361 (28:1361 Petition for Writ of Mandamus)
                  </option>
                  <option value="28:1362ic">28:1362ic (28:1362 Indian Tribal Controversy)
                  </option>
                  <option value="28:1362iw">28:1362iw (28:1362 Declaration re: Indian Tribal Water Rights)
                  </option>
                  <option value="28:1364">28:1364 (28:1364 Auto Negligence)
                  </option>
                  <option value="28:1391">28:1391 (28:1391 Personal Injury)
                  </option>
                  <option value="28:1402">28:1402 (28:1402 Medical Malpractice)
                  </option>
                  <option value="28:1407">28:1407 (28:1407 Airline Crash)
                  </option>
                  <option value="28:1441ac">28:1441ac (28:1441 Notice of Removal- Account Receivable)
                  </option>
                  <option value="28:1441al">28:1441al (28:1441 Notice of Removal- Airline Crash)
                  </option>
                  <option value="28:1441as">28:1441as (28:1441 Notice of Removal- Asbestos Litigation)
                  </option>
                  <option value="28:1441au">28:1441au (28:1441 Notice of Removal- Auto Negligence)
                  </option>
                  <option value="28:1441bc">28:1441bc (28:1441 Notice of Removal- Breach of Contract)
                  </option>
                  <option value="28:1441cv">28:1441cv (28:1441 Notice of Removal- Civil Rights Act)
                  </option>
                  <option value="28:1441df">28:1441df (28:1441 Notice of Removal- Contract Default)
                  </option>
                  <option value="28:1441dj">28:1441dj (28:1441 Notice of Removal- Declaratory Judgment)
                  </option>
                  <option value="28:1441ds">28:1441ds (28:1441 Notice of Removal- Contract Dispute)
                  </option>
                  <option value="28:1441ed">28:1441ed (28:1441 Notice of Removal - Employment Discrim)
                  </option>
                  <option value="28:1441fc">28:1441fc (28:1441 Notice of Removal - Fair Credit Reporti)
                  </option>
                  <option value="28:1441fr">28:1441fr (28:1441 Notice of Removal- Fraud)
                  </option>
                  <option value="28:1441ij">28:1441ij (28:1441 Notice of Removal- Injunctive/Declarato)
                  </option>
                  <option value="28:1441in">28:1441in (28:1441 Notice of Removal- Insurance Contract)
                  </option>
                  <option value="28:1441int">28:1441int (28:1441 Notice of Removal- Action for Interplea)
                  </option>
                  <option value="28:1441lb">28:1441lb (28:1441 Notice of Removal Libel,Assault,Slander)
                  </option>
                  <option value="28:1441lm">28:1441lm (28:1441 Notice of Removal- Labor/Mgmnt. Relatio)
                  </option>
                  <option value="28:1441mm">28:1441mm (28:1441 Notice of Removal- Medical Malpractice)
                  </option>
                  <option value="28:1441ni">28:1441ni (28:1441 Notice of Removal- Negotiable Instrumen)
                  </option>
                  <option value="28:1441nm">28:1441nm (28:1441 Notice of Removal- Non-Motor Vehicle)
                  </option>
                  <option value="28:1441nr">28:1441nr (28:1441 Notice of Removal)
                  </option>
                  <option value="28:1441oc">28:1441oc (28:1441 Notice of Removal--Other Contract)
                  </option>
                  <option value="28:1441pd">28:1441pd (28:1441 Notice of Removal- Property Damage)
                  </option>
                  <option value="28:1441pi">28:1441pi (28:1441 Notice of Removal- Personal Injury)
                  </option>
                  <option value="28:1441pl">28:1441pl (28:1441 Notice of Removal- Product Liability)
                  </option>
                  <option value="28:1441pr">28:1441pr (28:1441 Notice of Removal)
                  </option>
                  <option value="28:1441qt">28:1441qt (28:1441 Notice of Removal- Petition to Quiet Ti)
                  </option>
                  <option value="28:1441ri">28:1441ri (28:1441 Notice of Removal- Racketeering (RICO))
                  </option>
                  <option value="28:1441sa">28:1441sa (28:1441 - Notice of Removal: SEC Act)
                  </option>
                  <option value="28:1441sf">28:1441sf (28:1441 - Notice of Removal: Securities Fraud)
                  </option>
                  <option value="28:1441tl">28:1441tl (28:1441 Notice of Removal- Torts to Land)
                  </option>
                  <option value="28:1441tm">28:1441tm (28:1441 Notice of Removal- Tort/Motor Vehicle ()
                  </option>
                  <option value="28:1441tn">28:1441tn (28:1441 Notice of Removal- Tort/Non-Motor Vehic)
                  </option>
                  <option value="28:1441wd">28:1441wd (28:1441 Notice of Removal- Wrongful Death)
                  </option>
                  <option value="28:1442bc">28:1442bc (28:1442 Notice of Removal- Breach of Contract)
                  </option>
                  <option value="28:1442nr">28:1442nr (28:1442 Notice of Removal)
                  </option>
                  <option value="28:1442pr">28:1442pr (28:1442 Notice of Removal)
                  </option>
                  <option value="28:1443">28:1443 (28:1443(1) Rent, Lease &amp; Ejectment)
                  </option>
                  <option value="28:1444">28:1444 (28:1444 Notice of Removal- Foreclosure)
                  </option>
                  <option value="28:1446in">28:1446in (28:1446 Breach of Contract- Insurance)
                  </option>
                  <option value="28:1446nr">28:1446nr (28:1446 Notice of Removal)
                  </option>
                  <option value="28:1446pd">28:1446pd (28:1446 Notice for Removal- Property Damage (P.I)
                  </option>
                  <option value="28:1446pi">28:1446pi (28:1446 Notice for Removal- Personal Injury)
                  </option>
                  <option value="28:1446pl">28:1446pl (28:1446pl Notice for Removal - Product Liability)
                  </option>
                  <option value="28:1446pr">28:1446pr (28:1446 Notice for Removal)
                  </option>
                  <option value="28:1452">28:1452 (28:1452 R&amp;R re motions to remand (non-core))
                  </option>
                  <option value="28:1651">28:1651 (28:1651 Petition for Writ of Coram Nobis)
                  </option>
                  <option value="28:1651hc">28:1651hc (28:1651 Petition for Writ of Habeas Corpus)
                  </option>
                  <option value="28:1651mn">28:1651mn (28:1651 Petition for Writ of Mandamus)
                  </option>
                  <option value="28:1782">28:1782 (28:1782 Letter rogatory - appointment)
                  </option>
                  <option value="28:1983">28:1983 (28:1983 Civil Rights)
                  </option>
                  <option value="28:2201">28:2201 (28:2201 Constitutionality of State Statute(s))
                  </option>
                  <option value="28:2201dj">28:2201dj (28:2201 Declaratory Judgment)
                  </option>
                  <option value="28:2201ij">28:2201ij (28:2201 Injunction)
                  </option>
                  <option value="28:2201in">28:2201in (28:2201 Declaratory Judgment (Insurance))
                  </option>
                  <option value="28:2241">28:2241 (28:2241 Petition for Writ of Habeas Corpus (federa)
                  </option>
                  <option value="28:2254">28:2254 (28:2254 Petition for Writ of Habeas Corpus (State))
                  </option>
                  <option value="28:2254se">28:2254se (28:2254 Ptn for Writ of H/C - Stay of Execution)
                  </option>
                  <option value="28:2255">28:2255 (28:2255 Motion to Vacate / Correct Illegal Sentenc)
                  </option>
                  <option value="28:2271">28:2271 (28:2271 Federal Tort Claims Act)
                  </option>
                  <option value="28:2345">28:2345 (28:2345 Medicare Recovery)
                  </option>
                  <option value="28:2409">28:2409 (28:2409(a) Quiet Title Action)
                  </option>
                  <option value="28:2410">28:2410 (28:2410 Quiet Title)
                  </option>
                  <option value="28:2671">28:2671 (28:2671 Federal Tort Claims Act)
                  </option>
                  <option value="28:2674">28:2674 (28:2674 Federal Tort Claims Act)
                  </option>
                  <option value="28:7402">28:7402 (28:7402 Refund of Taxes)
                  </option>
                  <option value="28:7422">28:7422 (28:7422 Appeal from Administrative Decision)
                  </option>
                  <option value="29:0151">29:0151 (29:151 Labor: Review of Agency Action)
                  </option>
                  <option value="29:0160">29:0160 (29:160(1) National Labor Relations Act)
                  </option>
                  <option value="29:0184">29:0184 (29:184 Violation Collection Bargain Agreement)
                  </option>
                  <option value="29:0185ep">29:0185ep (29:185 Employee Pension Plan)
                  </option>
                  <option value="29:0185lm">29:0185lm (29:185 Labor/Mgt. Relations (Contracts))
                  </option>
                  <option value="29:0201do">29:0201do (29:201 Denial of Overtime Compensation)
                  </option>
                  <option value="29:0201fl">29:0201fl (29:201 Fair Labor Standards Act)
                  </option>
                  <option value="29:0203">29:0203 (29:203 Equal Pay Act)
                  </option>
                  <option value="29:0206">29:0206 (29:206 Collect Unpaid Wages)
                  </option>
                  <option value="29:0401">29:0401 (29:0401 Labor Management Disclosure Act)
                  </option>
                  <option value="29:0621">29:0621 (29:621 Job Discrimination (Age))
                  </option>
                  <option value="29:0623">29:0623 (29:623 Job Discrimination (Age))
                  </option>
                  <option value="29:0626">29:0626 (29:626 Job Discrimination (Age))
                  </option>
                  <option value="29:0633">29:0633 (29:633 Job Discrimination (Age))
                  </option>
                  <option value="29:0651">29:0651 (29:651 Occupational Safety/Health)
                  </option>
                  <option value="29:0754">29:0754 (29:754 Discrimination)
                  </option>
                  <option value="29:0791">29:0791 (29:791 Job Discrimination (Rehabilitation Act))
                  </option>
                  <option value="29:0794">29:0794 (29:0794 Job Discrimination (Handicap))
                  </option>
                  <option value="29:1001">29:1001 (29:1001 E.R.I.S.A.: Employee Retirement)
                  </option>
                  <option value="29:1002">29:1002 (29:1002 E.R.I.S.A.: Employee Retirement)
                  </option>
                  <option value="29:1104">29:1104 (29:1104 Recovery of Benefits to Employee)
                  </option>
                  <option value="29:1109">29:1109 (29:1109 Breach of Fiduciary Duties)
                  </option>
                  <option value="29:1131">29:1131 (29:1131 ERISA - Collection of Delinquent Trust Fun)
                  </option>
                  <option value="29:1132">29:1132 (29:1132 E.R.I.S.A.-Employee Benefits)
                  </option>
                  <option value="29:1145">29:1145 (29:1145 E.R.I.S.A.)
                  </option>
                  <option value="29:1149">29:1149 (29:1149 Recover Pension &amp; Profit Sharing)
                  </option>
                  <option value="29:1337">29:1337 (29:1337 E.R.I.S.A.)
                  </option>
                  <option value="29:1362">29:1362 (29:1362 ERISA)
                  </option>
                  <option value="29:1381">29:1381 (29:1381 E.R.I.S.A.)
                  </option>
                  <option value="29:1401">29:1401 (29:1401(b)(2) Appeal of Arbitration Award)
                  </option>
                  <option value="29:1451">29:1451 (29:1451 E.R.I.S.A.)
                  </option>
                  <option value="29:1801">29:1801 (29:1801 Farmworker Rights)
                  </option>
                  <option value="29:2601">29:2601 (29:2601 Family Medical Leave Act)
                  </option>
                  <option value="29:790">29:790 (29:790 Rehabilitation of Labor)
                  </option>
                  <option value="30:0181">30:0181 (30:181 Environment: Review of Agency Action)
                  </option>
                  <option value="30:0801">30:0801 (30:0801 Penalities/Federal Mine Safety Health Act)
                  </option>
                  <option value="30:1201">30:1201 (30:1201 Environment: Review of Agency Action)
                  </option>
                  <option value="30:1202">30:1202 (30:1202 Mining Reclamation Act)
                  </option>
                  <option value="30:1276">30:1276 (30:1276 Interior: Review of Agency Action)
                  </option>
                  <option value="31:3545">31:3545 (31:3545 Action to Recovery Money)
                  </option>
                  <option value="31:3729">31:3729 (31:3729 False Claims Act)
                  </option>
                  <option value="31:3730">31:3730 (31:3730 Qui Tam False Claims Act)
                  </option>
                  <option value="31:3731">31:3731 (31:3731 Fraud)
                  </option>
                  <option value="33:1319cw">33:1319cw (33:1319 Clean Water Act)
                  </option>
                  <option value="33:1319pv">33:1319pv (33:1319 Pollutants &amp; Permit Violations)
                  </option>
                  <option value="33:1365">33:1365 (33:1365 Environmental Matters)
                  </option>
                  <option value="35:0145">35:0145 (35:145 Patent Infringement)
                  </option>
                  <option value="35:0183">35:0183 (35:183 Patent Infringement)
                  </option>
                  <option value="35:0271">35:0271 (35:271 Patent Infringement)
                  </option>
                  <option value="375/1">375/1 (375/1 (False Claims Act - U.S. Plaintiff))
                  </option>
                  <option value="38:1681">38:1681 (38:1681 Recovery of VA Overpayment)
                  </option>
                  <option value="38:1686">38:1686 (38:1686 Recovery of VA Overpayment)
                  </option>
                  <option value="38:1780">38:1780 (38:1780 Recovery of VA Overpayment)
                  </option>
                  <option value="38:2011">38:2011 (38:2011 - Veteran's Readjustment Assistance Act of)
                  </option>
                  <option value="38:2021">38:2021 (Right to re-employment of inducted persons)
                  </option>
                  <option value="38:3116">38:3116 (38:3116 VA Overpayment)
                  </option>
                  <option value="38:775">38:775 (Recovery of Servicemen's Group Life Insurance)
                  </option>
                  <option value="39:3005">39:3005 (39:3005 Detention of Mail for Temporary Periods)
                  </option>
                  <option value="39:409">39:409 (39:409 Postal Service)
                  </option>
                  <option value="40:0258">40:0258 (40:258(a) Public Buildings &amp; Property: Land Condem)
                  </option>
                  <option value="40:0270">40:0270 (40:270 Miller Act)
                  </option>
                  <option value="40:0875">40:0875 (40:875 Public Buildings &amp; Property: Negligence)
                  </option>
                  <option value="40:3131">40:3131 (40:3131 Miller Act)
                  </option>
                  <option value="41:0251">41:0251 (41:251 Public Contracts-Review of Agency Action)
                  </option>
                  <option value="41:1463">41:1463 (41:1463 Public Contracts: Unlawful Employment Prac)
                  </option>
                  <option value="42:0205">42:0205 (42:205 Denial Social Security Benefits)
                  </option>
                  <option value="42:0206">42:0206 (42:206 Social Security Benefits)
                  </option>
                  <option value="42:0247">42:0247 (42:247 Personal Injury-Swine Flu)
                  </option>
                  <option value="42:0402">42:0402 (42:402 Social Security Benefits)
                  </option>
                  <option value="42:0405id">42:0405id (42:405 Review of HHS Decision (SSID))
                  </option>
                  <option value="42:0405wc">42:0405wc (42:405 Review of HHS Decision (DIWC))
                  </option>
                  <option value="42:0405ww">42:0405ww (42:405 Review of HHS Decision (DIWW))
                  </option>
                  <option value="42:0416">42:0416 (42:416 Denial of Social Security Benefits)
                  </option>
                  <option value="42:0427">42:0427 (42:427 Social Security Benefits)
                  </option>
                  <option value="42:12101">42:12101 (42:12101 Americans with Disabilities Act)
                  </option>
                  <option value="42:1383">42:1383 (42:1383 Review of HHS Decision)
                  </option>
                  <option value="42:1395">42:1395 (42:1395 HHS: Adverse Reimbursement Review)
                  </option>
                  <option value="42:1396">42:1396 (42:1396 - Tort Negligence)
                  </option>
                  <option value="42:1471">42:1471 (42:1471 Declaratory &amp; Injunctive Relief - Foreclo)
                  </option>
                  <option value="42:1981cv">42:1981cv (42:1981 Civil Rights)
                  </option>
                  <option value="42:1981hs">42:1981hs (42:1981 Housing Discrimination)
                  </option>
                  <option value="42:1981jb">42:1981jb (42:1981 Job Discrimination (Race))
                  </option>
                  <option value="42:1981sx">42:1981sx (42:1981 Sex Discrimination)
                  </option>
                  <option value="42:1983cv">42:1983cv (42:1983 Civil Rights Act)
                  </option>
                  <option value="42:1983ed">42:1983ed (42:1983 Civil Rights (Employment Discrimination))
                  </option>
                  <option value="42:1983pr">42:1983pr (42:1983 Prisoner Civil Rights)
                  </option>
                  <option value="42:1986">42:1986 (42:1986 Neglect of Duty)
                  </option>
                  <option value="42:2000ag">42:2000ag (42:2000 Job Discrimination (Age))
                  </option>
                  <option value="42:2000e">42:2000e (42:2000e Job Discrimination (Employment))
                  </option>
                  <option value="42:2000pb">42:2000pb (42:2000 Job Discrimination (Public Accomodations))
                  </option>
                  <option value="42:2000ra">42:2000ra (42:2000 Job Discrimination (Race))
                  </option>
                  <option value="42:2000sx">42:2000sx (42:2000 Job Discrimination (Sex))
                  </option>
                  <option value="42:2003">42:2003 (42:2003 Job Discrimination)
                  </option>
                  <option value="42:2005">42:2005 (42:2005 Review of Agency Action-HHS)
                  </option>
                  <option value="42:2615">42:2615 (42:2616 Medical Recovery Act)
                  </option>
                  <option value="42:2651">42:2651 (42:2651 Medical Care Recovery)
                  </option>
                  <option value="42:3601">42:3601 (42:405 Fair Housing Act)
                  </option>
                  <option value="42:4000">42:4000 (42:4000 National Flood Insurance Act)
                  </option>
                  <option value="42:4001">42:4001 (42:4001 National Insurance Flood Act)
                  </option>
                  <option value="42:4053">42:4053 (42:4053 Breach of Insurance Contract)
                  </option>
                  <option value="42:4072">42:4072 (42:4072 Payment of Flood Insurance Claim)
                  </option>
                  <option value="42:4321">42:4321 (42:4321 Review of Agency Action-Environment)
                  </option>
                  <option value="42:4332">42:4332 (42:4332 Environmental Policy - Coop of Agency Repo)
                  </option>
                  <option value="42:6901en">42:6901en (42:6901 Environmental Cleanup Expenses)
                  </option>
                  <option value="42:6901rs">42:6901rs (42:6901 Resource &amp; Recovery Act)
                  </option>
                  <option value="42:7413">42:7413 (42:7413(b) Clean Air Act)
                  </option>
                  <option value="42:7604cl">42:7604cl (42:7604 Clear Air Act (Emission Standards))
                  </option>
                  <option value="42:7604ir">42:7604ir (42:7604 Petition to Quash IRS Summons)
                  </option>
                  <option value="42:9607">42:9607 (42:9607 Real Property Tort to Land)
                  </option>
                  <option value="42:9613">42:9613 (42:9613 CERCLA)
                  </option>
                  <option value="43:945">43:945 (43:945 Compensation for Land Condemnation)
                  </option>
                  <option value="43:945a">43:945a (43:945a Complaint in Condemnation)
                  </option>
                  <option value="43:946">43:946 (43:946 Complaint in Condemnation - Eminent Domain)
                  </option>
                  <option value="45:0051">45:0051 (45:51 Railways: Fed. Employer's Liability Act)
                  </option>
                  <option value="45:0151">45:0151 (45:151 Railway Labor Act)
                  </option>
                  <option value="45:0184">45:0184 (45:184 Action to Set Aside Award of a System Board)
                  </option>
                  <option value="45:1395">45:1395 (45:1395 Railroads: Adverse Reimbursement Review)
                  </option>
                  <option value="45:7457">45:7457 (45:7457 Compel Reclamation Under Clear Air Act)
                  </option>
                  <option value="46:0185">46:0185 (46:0185 PETITION FOR LIMITATION LIABILITY)
                  </option>
                  <option value="46:0688">46:0688 (46:688 Jones Act)
                  </option>
                  <option value="46:0740">46:0740 (46:0740 DAMAGE OR INJURY CAUSED BY VESSEL)
                  </option>
                  <option value="46:0741">46:0741 (46:741 Shipping)
                  </option>
                  <option value="46:0761">46:0761 (46:761 Shipping: Damages for Death on High Seas)
                  </option>
                  <option value="46:1101">46:1101 (46:1101 Violation of Maritime Regulations)
                  </option>
                  <option value="46:1156">46:1156 (46:1156 Administrative Procedure Act)
                  </option>
                  <option value="47:227">47:227 (47:227 Restrictions of Use of Telephone Equipment)
                  </option>
                  <option value="47:227(b)">47:227(b) (47:227(b)(3) Telephone Consumer Protection Act of 1991)
                  </option>
                  <option value="47:251">47:251 (47:251 Telecommunications Act of 1996 - Interconnection)
                  </option>
                  <option value="48:0883">48:0883 (48:883 Violation of US Coastal Law)
                  </option>
                  <option value="48:1985">48:1985 (48:1985 Conspiracy/Deprivation Civil Rights)
                  </option>
                  <option value="49:0081">49:0081 (49:81 Damaged Goods While Being Transported)
                  </option>
                  <option value="49:0781">49:0781 (49:781 Forfeiture)
                  </option>
                  <option value="49:11503">49:11503 (49:11503 Railroad Revitalization Regulatory Reform)
                  </option>
                  <option value="49:11702">49:11702 (49:11702(a)(4) Violations of Interstate Commerce A)
                  </option>
                  <option value="49:1471">49:1471 (49:1471 Federal Aviation Act)
                  </option>
                  <option value="49:1903">49:1903 (49:1903 Petition to enforce administrative summon)
                  </option>
                  <option value="8:287">8:287 (8:287 Petition to Enforce INS Subpoena)
                  </option>
                  <option value="99:9999">99:9999 (99:9999 Report cause code (DO NOT DELETE!))
                  </option>
                  <option value="adsup">adsup (Administrative Subpoena)
                  </option>
                  <option value="attydisc">attydisc (Attorney Discipline)
                  </option>
                  <option value="contempt">contempt (Contempt Proceedings)
                  </option>
                  <option value="disbar">disbar (Disbarment Proceedings)
                  </option>
                  <option value="fordepo">fordepo (Foreign Deposition)
                  </option>
                  <option value="misc">misc (Civil Miscellaneous Case)
                  </option>
                  <option value="motcomp">motcomp (Motion to Compel)
                  </option>
                  <option value="motquash">motquash (Motion to Quash)
                  </option>
                  <option value="motret">motret (Motion for Return of Property)
                  </option>
                  <option value="petenf">petenf (Petition to Enforce IRS Summons)
                  </option>
                  <option value="petper">petper (Petition to Perpetuate Testimony)
                  </option>
                  <option value="regjgm">regjgm (Registration of Foreign Judgment)
                  </option>
                  <option value="tranbkref">tranbkref (Transmission of Bankruptcy Reference)
                  </option>
                  <option value="tranff">tranff (Transmission of Proposed Findings of Fact and Concl. of Law)
                  </option>
                  <option value="writgar">writgar (Application for a Writ of Garnishment)
                  </option>
                </select>
                <script>document.forms[0].cause_action.onchange = CMECF.CaseNumberSelect.ClearFields;</script>
              </td>
            </tr>

            <tr>
              <td nowrap=""> Last/Business Name</td>
              <td nowrap="">
                <input type="text" size="20" maxlength="200" name="last_name"
                       onfocus="prompt('Capitalization counts. The asterisk wildcard may be used to broaden the criteria entered.')"
                       onchange="preNsearch()">
                &nbsp; &nbsp;
                <input type="checkbox" name="ExactMatch" value="yes">
                Exact matches only
              </td>
            </tr>
            <tr>
              <td> First Name</td>
              <td>
                <input type="text" size="15" maxlength="15" name="first_name"
                       onfocus="prompt('Capitalization counts. The asterisk wildcard may be used to broaden the criteria entered.')"
                       onchange="preNsearch()">
                &nbsp; &nbsp;
                Middle Name
                <input type="text" size="15" maxlength="15" name="middle_name"
                       onfocus="prompt('Capitalization counts. The asterisk wildcard may be used to broaden the criteria entered.')"
                       onchange="preNsearch()">
              </td>

            </tr>
            <tr>

              <td> Type</td>
              <td>


                <select id="person_type" name="person_type">
                  <option></option>
                  <option value="aty">Attorney
                  </option>
                  <option value="pty">Party
                  </option>
                </select>

                <script>document.forms[0].person_type.onchange = CMECF.CaseNumberSelect.ClearFields;</script>

              </td>
            </tr>

            </tbody>
          </table>
        </td>
      </tr>
      </tbody>
    </table>
    <script language="JavaScript">
      var timerId;
      var BeenHere = 0;
      IsForm = true;

      function empty(s) {
        var whitespace = " \t\n\r";
        if (s == null || s.length == 0) {
          return (true);
        }
        // Is s only whitespace characters?
        for (var i = 0; i < s.length; i++) {
          var c = s.charAt(i);
          if (whitespace.indexOf(c) == -1) return false;
        }
        return (true);
      }

      function ClearTimer() {
        BeenHere = 0;
        clearTimeout(timerId);
        return (true);
      }

      var FormId = 0;

      function ProcessForm() {
        if (BeenHere == 1) {
          alert("Submission already made, please wait for response from server");
          return (false);
        }
        BeenHere = 1;
        timerId = setTimeout("ClearTimer()", 5000);
        if (!chkNsearch()) {
          ClearTimer();
          return false;
        }
        if (!CheckDate(document.forms[0].Qry_filed_from.value, 0)) {
          ClearTimer();
          return false;
        }
        if (!CheckDate(document.forms[0].Qry_filed_to.value, 0)) {
          ClearTimer();
          return false;
        }
        if (!CheckDate(document.forms[0].lastentry_from.value, 0)) {
          ClearTimer();
          return false;
        }
        if (!CheckDate(document.forms[0].lastentry_to.value, 0)) {
          ClearTimer();
          return false;
        }
        document.forms[FormId].submit();
        return true
      }

      function RunAfterClear() {

      }
    </script>
    <table>
      <tbody>
      <tr>
        <td><input name="button1" value="Run Query" type="button" onclick="ProcessForm()">
        </td>
        <td></td>
        <td></td>
        <td><input name="reset" type="RESET" value="Clear" onclick="setTimeout('RunAfterClear()',100)"></td>
      </tr>
      <tr></tr>
      </tbody>
    </table>
  </form><!---ShowPage(iquery_process_form.htm)--->

  <!-- RPM Packages: ao-dcecf-web-* -->
  <!-- RPM Permissions: 750 -->
  <!-- RPM Owner: ecf_web -->
  <!-- RPM Group: ecf_web -->
  <!-- RPM Flags: configure -->

  <script type="text/javascript">
    function ProcessForm() {
      if (BeenHere == 1) {
        return (false);
      }
      BeenHere = 1;
      timerId = setTimeout("ClearTimer()", 5000);
      if (!chkNsearch()) {
        ClearTimer();
        return false;
      }
      if (!CheckDate(document.forms[0].Qry_filed_from.value, 0)) {
        ClearTimer();
        return false;
      }
      if (!CheckDate(document.forms[0].Qry_filed_to.value, 0)) {
        ClearTimer();
        return false;
      }
      if (!CheckDate(document.forms[0].lastentry_from.value, 0)) {
        ClearTimer();
        return false;
      }
      if (!CheckDate(document.forms[0].lastentry_to.value, 0)) {
        ClearTimer();
        return false;
      }
      if (!CheckLooseQry()) {
        ClearTimer();
        return false;
      }
      document.forms[FormId].submit();
      return true
    }
  </script>
</div>
<div class="calendly-lifecycle-optibutton_singleton"></div>
<div class="calendly-frame-tag" manager_id="a638593f-5f39-4dd7-9ce9-087526db0967"
     id="a638593f-5f39-4dd7-9ce9-087526db0967" style="height: 0px; width: 0px; visibility: hidden;"></div>
</body>
</html>
