
## 4. Single Docket Processing Lifecycle

Detailed lifecycle of processing a single docket through all phases.

```mermaid
flowchart TD
    A[Docket Processing Start] --> B[Receive Page + Initial Details]
    B --> C{Use New Docket Services?}
    
    C -->|Yes| D[New Service Architecture Path]
    C -->|No| LEGACY[Legacy DocketProcessor Path]
    
    D --> D1[Initialize All 8 Services]
    D1 --> D2[Configuration Service Ready]
    D2 --> PHASE1[PHASE 1: HTML Content Processing]
    
    PHASE1 --> P1_1[Case Processing Service.process_case_html]
    P1_1 --> P1_2[Wait for Page Content Stability]
    P1_2 --> P1_3[Validate HTML Content Matches Case]
    P1_3 --> P1_4{HTML Valid?}
    P1_4 -->|No| P1_FAIL[Return None - Invalid HTML]
    P1_4 -->|Yes| P1_5[Extract Case Details via HTMLCaseParser]
    P1_5 --> P1_6[Process MDL Flags and Special Cases]
    P1_6 --> P1_7[Check for Notice of Removal]
    P1_7 --> P1_8[Generate Base Filename]
    P1_8 --> P1_9[Add Processing Metadata]
    P1_9 --> PHASE2[PHASE 2: Relevance & Classification]
    
    PHASE2 --> P2_1[Relevance Service.determine_case_relevance]
    P2_1 --> P2_2{Has MDL Flag?}
    P2_2 -->|Yes| P2_3[Set Relevant - MDL Flag Override]
    P2_2 -->|No| P2_4{Matches ignore_download Pattern?}
    P2_4 -->|Yes| P2_5[Set Not Relevant - Ignore Download]
    P2_4 -->|No| P2_6{Excluded by Statute?}
    P2_6 -->|Yes| P2_7[Set Not Relevant - Excluded]
    P2_6 -->|No| P2_8{Explicitly Relevant?}
    P2_8 -->|Yes| P2_9[Set Relevant - Explicit Match]
    P2_8 -->|No| P2_10[Set Not Relevant - Default]
    
    P2_3 --> P2_CLASSIFY[Classification Service.classify_case]
    P2_5 --> P2_CLASSIFY
    P2_7 --> P2_CLASSIFY
    P2_9 --> P2_CLASSIFY
    P2_10 --> P2_CLASSIFY
    
    P2_CLASSIFY --> P2_C1{is_explicitly_requested?}
    P2_C1 -->|Yes| P2_C2[Skip Removal Detection for Reprocessing]
    P2_C1 -->|No| P2_C3[Check Removal by Cause and HTML]
    P2_C2 --> P2_C4[Check Transfer Status]
    P2_C3 --> P2_C4
    P2_C4 --> PHASE3[PHASE 3: Case Verification]
    
    PHASE3 --> P3_1[Verification Service.verify_case]
    P3_1 --> P3_2{is_explicitly_requested?}
    P3_2 -->|Yes| P3_3[Explicitly Requested Verification]
    P3_2 -->|No| P3_4[Report-Scraped Verification]
    
    P3_3 --> P3_5[Check Database GSI]
    P3_5 --> P3_6{Exists in DB?}
    P3_6 -->|Yes| P3_SKIP[Skip - Already in Database]
    P3_6 -->|No| P3_7[Check Local Artifacts Only PDF/ZIP]
    P3_7 --> P3_8{Artifacts Exist?}
    P3_8 -->|Yes| P3_SKIP
    P3_8 -->|No| P3_PROCESS[Proceed to Download Phase]
    
    P3_4 --> P3_9[Check Database GSI]
    P3_9 --> P3_10{Exists in DB?}
    P3_10 -->|Yes| P3_SKIP
    P3_10 -->|No| P3_11[Check All Local Files]
    P3_11 --> P3_12{Any Files Exist?}
    P3_12 -->|Yes| P3_SKIP
    P3_12 -->|No| P3_PROCESS
    
    P3_SKIP --> SAVE_ONLY[Save Metadata Only - Skip Download]
    P3_PROCESS --> PHASE4[PHASE 4: Download Workflow]
    
    PHASE4 --> P4_1[Download Orchestration Service.process_download_workflow]
    P4_1 --> P4_2[Validate Case for Download]
    P4_2 --> P4_3{Should Attempt Download?}
    P4_3 -->|No| P4_SKIP[Skip Download with Reason]
    P4_3 -->|Yes| P4_4[Prepare Download Context]
    P4_4 --> P4_5[Execute Document Download]
    P4_5 --> P4_6{Download Success?}
    P4_6 -->|Yes| P4_SUCCESS[Handle Download Success]
    P4_6 -->|No| P4_FAIL[Handle Download Failure]
    
    P4_SKIP --> P4_SAVE[Add Skip Metadata]
    P4_SUCCESS --> P4_SAVE_SUCCESS[Add Success Metadata]
    P4_FAIL --> P4_SAVE_FAIL[Add Failure Metadata]
    
    P4_SAVE --> FINAL_SAVE[File Operations Service.save_and_upload_case_data]
    P4_SAVE_SUCCESS --> FINAL_SAVE
    P4_SAVE_FAIL --> FINAL_SAVE
    SAVE_ONLY --> FINAL_SAVE
    
    FINAL_SAVE --> FS1[Create Court/Date Directory]
    FS1 --> FS2[Generate Filename]
    FS2 --> FS3[Clean and Prepare Case Data]
    FS3 --> FS4[Save to Local JSON]
    FS4 --> FS5{Upload Enabled?}
    FS5 -->|Yes| FS6[Upload to S3]
    FS5 -->|No| FS7[Local Only]
    FS6 --> FS8{Upload Success?}
    FS8 -->|Yes| SUCCESS[Complete Success]
    FS8 -->|No| PARTIAL[Local Success + Upload Error]
    FS7 --> SUCCESS
    
    LEGACY --> L1[Legacy DocketProcessor.process_docket_page]
    L1 --> L2{Legacy Success?}
    L2 -->|Yes| SUCCESS
    L2 -->|No| FAIL[Processing Failed]
    
    P1_FAIL --> FAIL
    PARTIAL --> END[Return Result to Orchestrator]
    SUCCESS --> END
    FAIL --> END
    
    style PHASE1 fill:#e3f2fd
    style PHASE2 fill:#fff3e0
    style PHASE3 fill:#f3e5f5
    style PHASE4 fill:#e8f5e8
    style SUCCESS fill:#c8e6c9
    style FAIL fill:#ffcdd2
```
