# PACER Court Processing Implementation Analysis
*Branch: uv-20250804-pacer*

## Overview

This document provides a complete technical analysis of the working court processing implementation in the PACER orchestrator service. This analysis should be used as the definitive reference for implementing the new architecture to process courts and dockets **THE EXACT SAME WAY**.

## File Locations

- **Main Orchestrator**: `/src/services/pacer/pacer_orchestrator_service.py`
- **File Management**: `/src/services/pacer/file_management_service.py`
- **Directory Setup**: Lines 131-149 in file_management_service.py
- **Core Processing**: Lines 582-1318 in pacer_orchestrator_service.py

## Entry Point and Initialization

### Main Method: `process_courts()`
**Location**: Lines 582-1318 in `pacer_orchestrator_service.py`

**Method Signature**:
```python
async def process_courts(
    self,
    court_ids: list[str],
    context: BrowserContext | None,
    iso_date: str,
    start_date: DateType,
    end_date: DateType,
    docket_list_input: list[dict[str, Any]] | None = None,
) -> dict[str, Any]:
```

**Initialization Steps**:
1. **Line 605-608**: Resource tracking logging
2. **Line 618**: Ensure DeepSeek service initialization
3. **Line 662**: Initialize S3 storage if configured  
4. **Line 665**: Setup directories for the ISO date
5. **Line 668**: Load historical review cases

## Directory Creation Logic

### Method: `setup_directories(iso_date: str)`
**Location**: Lines 131-149 in `file_management_service.py`

**Directory Structure Created**:
```
data/{iso_date}/
├── dockets/
│   ├── temp/
│   └── .locks/
├── logs/
│   ├── pacer/           # Court-specific log files
│   └── docket_report/   # Docket report JSON files
├── html/
└── screenshots/
```

**Implementation**:
```python
def setup_directories(self, iso_date: str) -> None:
    """Creates essential directories for the run date idempotently."""
    default_data_path = get_default_data_path(self.config)
    base_path = Path(self.config.get('data_path', default_data_path))
    date_dir = base_path / iso_date
    subdirs = ['dockets', 'logs', 'html', 'screenshots']
    
    for subdir in subdirs:
        (date_dir / subdir).mkdir(parents=True, exist_ok=True)
    
    # Create specific subdirectories
    (date_dir / 'dockets' / 'temp').mkdir(parents=True, exist_ok=True)
    (date_dir / 'dockets' / '.locks').mkdir(parents=True, exist_ok=True)
    (date_dir / 'logs' / 'pacer').mkdir(parents=True, exist_ok=True)
    (date_dir / 'logs' / 'docket_report').mkdir(parents=True, exist_ok=True)
```

## Pre-processing Checks: Docket Report Log Files

### Decision Logic Location: Lines 713-750

### Docket Report File Pattern
**Path**: `data/{iso_date}/logs/docket_report/{court_id}.json`

**Path Construction (Lines 730-736)**:
```python
docket_report_path = (
    Path(self.config.get("data_path", "./data"))
    / iso_date
    / "logs"
    / "docket_report" 
    / f"{court_id}.json"
)
```

### Three Processing Modes

#### 1. Special Docket List Mode (Lines 721-726)
**Trigger**: When `docket_list_input` parameter provided
- Sets `_court_specific_dockets` attribute with grouped dockets by court
- All courts treated as having "reports" 
- Skips file existence checking
- Sets `get_new_docket_report = False`

#### 2. Resume from Existing Reports (Lines 727-744)
**Trigger**: When `get_new_docket_report = False` (default behavior)
- **For each court**: Checks if `{court_id}.json` exists in docket_report directory
- **If exists**: Adds to `courts_with_reports` list
- **If missing**: Adds to `courts_needing_reports` list
- **Logging**: Reports found/missing status per court

#### 3. Force New Reports (Lines 745-750)
**Trigger**: When `get_new_docket_report = True`
- **All courts**: Added to `courts_needing_reports` list
- **Behavior**: Ignores existing docket report files
- **Use case**: When forcing fresh report generation

## Core Processing Decision Tree

### Parallel vs Sequential Processing (Line 774)

**Parallel Processing Triggers**:
- `config.run_parallel = True` OR
- `config.parallel = True` OR  
- `len(court_ids) > 1` (automatic parallel for multiple courts)

**Automatic Parallel Logging (Lines 767-772)**:
```python
if (len(court_ids) > 1) and not (
    self.config.get("run_parallel", False) or self.config.get("parallel", False)
):
    self.log_info(
        f"⚡ AUTOMATIC PARALLEL: Enabling parallel processing because process_single_court has {len(court_ids)} courts"
    )
```

## Parallel Processing Workflow (Lines 774-894)

### Task Creation Strategy
**Lines 799-840**: Creates tasks for all courts simultaneously

**For Courts with Existing Reports (Lines 819-827)**:
```python
task = self._process_court_with_docket_log_isolated_context(
    court_id=court_id,
    iso_date=iso_date,
    workflow_config=workflow_config,
)
```

**For Courts Needing Reports (Lines 828-836)**:
```python
task = self._process_court_with_isolated_context(
    court_id=court_id,
    processing_mode="report_generation",
    workflow_config=workflow_config,
)
```

### Concurrency Control
- **Line 793**: `num_workers = self.config.get("num_workers", 10)`
- **Semaphore**: Controls maximum concurrent browsers via `_max_concurrent_browsers`
- **Sliding Window**: Uses `asyncio.as_completed()` for result processing (Line 848)

### Progress Tracking (Lines 857-871)
- **Completion Count**: Tracks completed tasks out of total
- **Progress Percentage**: Calculates and logs completion percentage  
- **Active Browser Monitoring**: Logs active browser count every 5 completions

## Sequential Processing Workflow (Lines 895-1281)

### Two-Phase Approach

#### Phase 1: Report Generation (Lines 904-981)
**Only for courts in `courts_needing_reports`**

**Key Method Call (Lines 911-919)**:
```python
report_results = await self._generate_reports_for_courts(
    courts_needing_reports,
    context,
    iso_date,
    start_date,
    end_date,
    workflow_config,
    manage_browser_lifecycle=False,  # Critical: Keep browser open
)
```

**Session Management (Lines 963-980)**:
- **Browser Reuse**: Stores `report_browser_service` and `report_browser_context`
- **Session Storage**: Saves navigator, page, and court_logger per court
- **Lifecycle**: Browser stays open for Phase 2 processing

#### Phase 2: Case Processing (Lines 988-1117)  
**For all courts in `courts_with_reports` (existing + newly generated)**

**Skip Logic (Lines 990-997)**:
```python
courts_still_to_process = [
    c for c in courts_with_reports if c not in results
]
```

**Processing Method (Lines 1064-1072)**:
```python
result = await self._process_court_with_docket_log(
    court_id=court_id,
    context=sequential_context,
    iso_date=iso_date,
    workflow_config=workflow_config,
)
```

**Browser Context Handling (Lines 1010-1059)**:
- **Reuse**: Uses `report_browser_context` if available
- **Create New**: Creates browser context if none available  
- **Resource Tracking**: Comprehensive logging of context creation

## Docket Report Log Loading

### Method: `load_docket_report_log()`
**Location**: Lines 534-580

**File Structure Expected**:
```json
{
  "cases": [
    {
      "docket_num": "...",
      "court_id": "...",
      // Additional case fields
    }
  ],
  "metadata": {
    "generated_at": "...",
    "total_cases": "..."
  }
}
```

**Loading Process**:
1. **Line 549-552**: Construct file path `data/{iso_date}/logs/docket_report/{court_id}.json`
2. **Line 554-556**: Check file existence, return None if missing
3. **Line 563-564**: Load and parse JSON content
4. **Line 567**: Extract cases array from JSON
5. **Line 570-574**: Log loaded case count and metadata

## Case Processing with Docket Logs

### Method: `_process_court_with_docket_log()`
**Location**: Lines 1527-1649

### Data Loading Logic (Lines 1564-1603)

#### Special Docket Lists (Lines 1567-1585)
**When `_court_specific_dockets` exists**:
```python
if (
    hasattr(self, "_court_specific_dockets")
    and court_id in self._court_specific_dockets
):
    docket_list = self._court_specific_dockets[court_id]
    cases = []
    for docket_item in docket_list:
        case = {
            "docket_num": docket_item.get("docket_num"),
            "court_id": court_id,
            **docket_item,  # Include all fields from docket item
        }
        cases.append(case)
```

#### File-Based Loading (Lines 1587-1593)
```python
cases = await self.load_docket_report_log(iso_date, court_id)
if not cases:
    result["error"] = f"No docket report log found for {court_id}"
    return result
```

### Session Management (Lines 1610-1622)
**Reuses existing browser sessions from report generation**:
```python
if (
    hasattr(self, "_current_court_sessions")
    and court_id in self._current_court_sessions
):
    existing_navigator, existing_page, existing_court_logger = (
        self._current_court_sessions[court_id]
    )
```

### Core Processing (Lines 1624-1632)
```python
processed_cases = await self._process_cases_with_new_services(
    court_id=court_id,
    cases=cases,
    context=context,
    workflow_config=workflow_config,
    existing_navigator=existing_navigator,
    existing_page=existing_page,
    existing_court_logger=existing_court_logger,
)
```

## Report Generation for New Cases

### Method: `_run_court_report_generation_workflow()`
**Location**: Lines 3236-3400+

### Complete Workflow Steps

#### 1. Page and Navigator Setup (Lines 3254-3273)
```python
page = await context.new_page()
navigator = PacerNavigator(
    page,
    config=self.config,
    screenshot_dir=screenshot_dir,
    timeout_ms=self.config.get("timeout", 30) * 1000,
)
```

#### 2. Court-Specific Logging (Lines 3275-3287)
**Log File Path**: `data/{iso_date}/logs/pacer/{court_id}.log`
```python
logs_dir = (
    Path(self.config.get("data_path", "./data"))
    / iso_date
    / "logs"
    / "pacer"
)
logs_dir.mkdir(parents=True, exist_ok=True)
log_file_path = logs_dir / f"{court_id}.log"
court_logger = self.file_service.setup_court_file_logger(
    f"court_{court_id}_pacer", log_file_path
)
```

#### 3. ECF Authentication (Lines 3295-3316)
```python
if not await self.auth_service.perform_ecf_login_sequence(
    navigator, court_id, court_logger
):
    # Handle login failure with screenshot
    return {
        "court_id": court_id,
        "status": "failed", 
        "error": "ECF Login Failed",
        "source": "report_service",
    }
```

#### 4. Civil Cases Report Generation (Lines 3323-3340)
```python
report_service_instance = self.service_factory.create_report_service(
    court_id=court_id,
    navigator=navigator,
    from_date_str=from_date_str,
    to_date_str=to_date_str,
    iso_date=iso_date,
    logger=court_logger,
)

async with report_service_instance:
    await report_service_instance.navigate_to_report_menu()
    await report_service_instance.navigate_to_case_filed_report()
    await report_service_instance.configure_case_filed_report()
    has_cases = await report_service_instance.run_report()
```

#### 5. Immediate Case Processing (Lines 3355-3395)
**If cases found, process in same browser session**:
```python
if has_cases:
    processing_result = await self._process_court_after_report_generation(
        court_id=court_id,
        context=context,
        iso_date=iso_date,
        workflow_config=workflow_config,
        navigator=navigator,
        page=page,
        court_logger=court_logger,
    )
```

## Error Handling Strategies

### Parallel Processing Error Handling (Lines 873-890)
**Exception Handling in Sliding Window**:
```python
for completed_task in asyncio.as_completed(all_tasks):
    try:
        result = await completed_task
        # Process success
    except Exception as e:
        task_id = id(completed_task)
        court_id, task_type = task_id_to_metadata.get(task_id, ("unknown", "unknown"))
        results[court_id] = {
            "court_id": court_id,
            "status": "failed",
            "error": str(e),
            "source": task_type,
        }
```

### Sequential Processing Error Handling (Lines 1081-1090)
**Per-Court Exception Handling**:
```python
for court_id in courts_still_to_process:
    try:
        result = await self._process_court_with_docket_log(...)
        results[court_id] = result
    except Exception as e:
        results[court_id] = {
            "court_id": court_id,
            "status": "failed", 
            "error": str(e),
            "source": "docket_report_log",
        }
```

### Browser Resource Cleanup

#### Context Tracking (Throughout)
```python
await self._track_browser_context(context)
# ... processing ...
await self._untrack_browser_context(context)
```

#### Sequential Cleanup (Lines 1096-1117)
```python
finally:
    if browser_service and sequential_context and not report_browser_context:
        try:
            await self._untrack_browser_context(sequential_context)
            await sequential_context.close()
            await browser_service.close()
        except Exception as e:
            self.log_warning(f"Error closing sequential browser resources: {e}")
```

#### Court Session Cleanup (Lines 1283-1294)
```python
if court_sessions:
    for court_id, (_navigator, page, _court_logger) in court_sessions.items():
        try:
            if page and not page.is_closed():
                await page.close()
        except Exception as e:
            self.log_warning(f"Error closing page for court {court_id}: {e}")
```

## Result Structure and Completion

### Result Compilation (Lines 1306-1318)
```python
summary = self._summarize_results(results)
return {
    "summary": summary,
    "court_results": results,
    "config": {
        "iso_date": iso_date,
        "court_count": len(court_ids),
        "date_range": f"{start_date} to {end_date}",
    },
}
```

### Individual Court Result Structure
```python
{
    "court_id": str,
    "status": "success" | "failed" | "success_no_cases", 
    "source": "docket_report_log" | "report_service" | "report_service_with_processing",
    "cases_loaded": int,
    "cases_processed": int, 
    "cases_found": int,
    "error": str | None,
    # Optional session data for browser reuse
    "_navigator": PacerNavigator,
    "_page": Page,
    "_court_logger": Logger,
}
```

## Key Configuration Parameters

### Critical Settings
- **`get_new_docket_report`**: Boolean controlling report generation vs resume
- **`run_parallel`** / **`parallel`**: Boolean enabling parallel processing
- **`num_workers`**: Integer controlling parallel task concurrency
- **`data_path`**: String path to data directory (default: "./data")
- **`headless`**: Boolean for browser headless mode
- **`timeout_ms`**: Integer timeout for browser operations

### Resource Management
- **`_max_concurrent_browsers`**: Semaphore limit for browser instances
- **`_browser_semaphore`**: AsyncIO semaphore for browser control
- **`_active_browser_contexts`**: Set tracking open browser contexts

## Critical Implementation Requirements

### 1. **EXACT Directory Structure**
Must create the same directory structure with identical paths:
- `data/{iso_date}/logs/docket_report/{court_id}.json`
- `data/{iso_date}/logs/pacer/{court_id}.log`

### 2. **EXACT File Format**  
Docket report JSON must have identical structure with `cases` array and `metadata` object.

### 3. **EXACT Decision Logic**
Must implement the same three-mode decision tree:
- Special docket list mode
- Resume from existing reports  
- Force new reports

### 4. **EXACT Processing Flow**
- Same parallel vs sequential logic
- Same browser session reuse patterns
- Same error handling and recovery

### 5. **EXACT Resource Management**
- Same browser context tracking
- Same cleanup procedures
- Same concurrency controls

## Implementation Checklist

- [ ] Directory structure creation matches exactly
- [ ] Docket report log file loading works identically  
- [ ] Three-mode decision logic implemented
- [ ] Parallel processing with same concurrency controls
- [ ] Sequential processing with two-phase approach
- [ ] Browser session reuse between report generation and processing
- [ ] Same error handling strategies
- [ ] Same resource cleanup procedures
- [ ] Same result structure and summarization
- [ ] Same logging patterns and messages

---

**CRITICAL**: The new architecture must process courts and dockets using this EXACT same logic, file structures, and decision trees to ensure 100% compatibility and behavioral consistency.