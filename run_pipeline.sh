#!/usr/bin/env bash

# This script runs the main Python application for one or more configurations.
# It is self-contained and ensures the correct Python virtual environment is
# activated before execution, making it independent of the calling shell's state.

# --- [KEPT] Signal handling for graceful shutdown ---
cleanup() {
    echo ""
    echo "⚠️  Pipeline interrupted. Cleaning up..."
    if [ ! -z "$CURRENT_PID" ]; then
        echo "Terminating current process (PID: $CURRENT_PID)..."
        kill -TERM "$CURRENT_PID" 2>/dev/null
        sleep 2
        if kill -0 "$CURRENT_PID" 2>/dev/null; then
            echo "Process did not terminate, force killing..."
            kill -KILL "$CURRENT_PID" 2>/dev/null
        fi
    fi
    echo "Pipeline cleanup completed."
    exit 130
}

trap cleanup SIGINT SIGTERM

# --- [KEPT] Default values and argument parsing ---
CONFIG_NAMES=""
CURRENT_PID=""

while [[ "$#" -gt 0 ]]; do
    case $1 in
        --config) CONFIG_NAMES="$2"; shift ;;
        *) echo "Unknown parameter passed: $1"; exit 1 ;;
    esac
    shift
done

if [ -z "$CONFIG_NAMES" ]; then
    echo "Usage: $0 --config <config_name1>[,<config_name2>,...]"
    exit 1
fi

# --- [KEPT] Path setup ---
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$SCRIPT_DIR"

#
# --- THIS IS THE CRITICAL FIX ---
#
# Activate the Python virtual environment. This action is performed *inside*
# the script's own clean shell, ensuring it has the correct environment.
#
echo "▶️ Activating virtual environment for script: ${PROJECT_ROOT}/.venv"
source "${PROJECT_ROOT}/.venv/bin/activate"

# --- [KEPT] Loop through configs and run the Python script ---
IFS=',' read -ra CONFIG_ARRAY <<< "$CONFIG_NAMES"

for config_name in "${CONFIG_ARRAY[@]}"; do
    config_file_path="${PROJECT_ROOT}/config/${config_name}.yml"
    main_script_path="${PROJECT_ROOT}/src/main.py"

    if [ ! -f "$config_file_path" ]; then
        echo "Error: Config file not found at $config_file_path" >&2
        continue
    fi

    echo "---"
    echo "Running pipeline step: $config_name"

    # Use `python` (which now correctly points to the venv's python)
    python "$main_script_path" --params "$config_file_path" &
    CURRENT_PID=$!

    wait $CURRENT_PID
    EXIT_CODE=$?
    CURRENT_PID=""

    if [ $EXIT_CODE -ne 0 ]; then
        echo "Error running pipeline step: $config_name (exit code: $EXIT_CODE)" >&2
        # To stop on the first error, uncomment the next line
        # exit 1
    fi
done

echo "✅ All pipeline steps completed."