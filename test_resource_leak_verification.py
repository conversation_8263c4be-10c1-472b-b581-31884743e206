#!/usr/bin/env python3
"""
Final verification test for the resource leak fix.

This script runs a comprehensive test to ensure that the DeepSeek client
resource leak has been properly resolved.
"""

import asyncio
import logging
import os
import sys
import gc

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

# Enable resource tracking early
from src.lib.utils.enable_resource_tracking import resource_tracker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_multiple_client_creations():
    """Test creating multiple DeepSeek clients to verify no leaks accumulate."""
    logger.info("Testing multiple DeepSeek client creations...")
    
    try:
        from src.infrastructure.external.deepseek_client import DeepSeekClient
        from src.services.ai.deepseek_service import DeepSeekService
        from src.services.ai.prompt_manager import PromptManager
        
        clients = []
        services = []
        
        # Create multiple clients and services (simulating multiple pipeline runs)
        for i in range(3):
            logger.info(f"Creating DeepSeek client and service #{i+1}")
            
            # Create client
            client = DeepSeekClient(
                api_key="test-key", 
                config={"temperature": 0.0}, 
                logger=logger
            )
            
            # Create service
            prompt_manager = PromptManager(logger=logger, config={})
            service = DeepSeekService(
                logger=logger,
                client=client,
                prompt_manager=prompt_manager,
                config={"temperature": 0.0},
            )
            
            # Initialize service
            await service.initialize()
            
            clients.append(client)
            services.append(service)
            
            logger.info(f"Client #{i+1} created and initialized")
        
        logger.info("All clients created. Checking resource state...")
        resource_tracker.print_unclosed_resources()
        
        # Now cleanup all clients manually (simulating our fix)
        for i, service in enumerate(services):
            if hasattr(service, 'client') and service.client:
                if hasattr(service.client, 'close_session'):
                    await service.client.close_session()
                    logger.info(f"✅ Client #{i+1} session closed")
        
        logger.info("All clients cleaned up. Final resource check...")
        resource_tracker.print_unclosed_resources()
        
    except Exception as e:
        logger.error(f"Error in multiple client test: {e}")


async def test_context_manager_pattern():
    """Test the recommended context manager pattern."""
    logger.info("Testing context manager pattern (recommended approach)...")
    
    try:
        from src.infrastructure.external.deepseek_client import DeepSeekClient
        
        # Test multiple context managers
        for i in range(3):
            logger.info(f"Testing context manager #{i+1}")
            async with DeepSeekClient(api_key="test-key", logger=logger) as client:
                logger.info(f"Context manager #{i+1} active")
                # Simulate some work
                await asyncio.sleep(0.01)
            logger.info(f"Context manager #{i+1} exited")
        
        logger.info("All context managers completed. Checking resources...")
        resource_tracker.print_unclosed_resources()
        
    except Exception as e:
        logger.error(f"Error in context manager test: {e}")


async def main():
    """Main test function."""
    logger.info("=" * 60)
    logger.info("RESOURCE LEAK FIX VERIFICATION TEST")
    logger.info("=" * 60)
    
    # Print initial resource state
    logger.info("=== Initial Resource State ===")
    resource_tracker.print_unclosed_resources()
    
    # Test 1: Multiple client creations (old problematic pattern, now fixed)
    await test_multiple_client_creations()
    
    logger.info("=== Resource State After Multiple Clients Test ===")
    resource_tracker.print_unclosed_resources()
    
    # Test 2: Context manager pattern (recommended approach)
    await test_context_manager_pattern()
    
    logger.info("=== Resource State After Context Manager Test ===")
    resource_tracker.print_unclosed_resources()
    
    # Force garbage collection
    gc.collect()
    await asyncio.sleep(0.1)
    
    # Final comprehensive cleanup
    logger.info("=== Running Comprehensive Cleanup ===")
    from src.lib.utils.resource_tracker import cleanup_all_resources
    await cleanup_all_resources()
    
    logger.info("=== Final Resource State ===")
    resource_tracker.print_unclosed_resources()
    
    logger.info("=" * 60)
    logger.info("✅ RESOURCE LEAK FIX VERIFICATION COMPLETED")
    logger.info("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())