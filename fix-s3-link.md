When running @run_pipeline.sh --config workflows/transformer:
- For file with html_only: true, it is supposed to set s3_link = s3_html.
- For files that are is_transferred and docket is in dynamodb, it is supposed inherit s3_link from transferor docket.
- If a file has a .md/.pdf the s3_link is supposed to be "https://cdn.lexgenius.ai/{base_filename}.pdf". 

It is not setting the proper s3_link link for files with a .pdf. 

Please fix
