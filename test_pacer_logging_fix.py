#!/usr/bin/env python3
"""Test script to verify PACER logging and JSON saving fixes."""

import asyncio
import json
import logging
from pathlib import Path
from datetime import datetime
from unittest.mock import Mock, MagicMock

# Add the project to the path
import sys
sys.path.insert(0, '/Users/<USER>/PycharmProjects/lexgenius')

from src.pacer.jobs.docket_job import DocketProcessingJob
from src.pacer.jobs.job_processor import JobProcessor
from src.pacer._case_processing_components.case_transformer import CaseTransformer
from src.pacer._file_components.file_manager import FileManager
from src.infrastructure.patterns.component_base import ComponentImplementation


async def test_base_filename_generation():
    """Test that base_filename is generated correctly."""
    print("\n=== Testing base_filename generation ===")
    
    # Create a logger
    logger = logging.getLogger("test")
    logger.setLevel(logging.DEBUG)
    handler = logging.StreamHandler()
    handler.setLevel(logging.DEBUG)
    logger.addHandler(handler)
    
    # Create case transformer
    transformer = CaseTransformer(logger=logger, config={})
    
    # Test cases
    test_cases = [
        {
            "court_id": "cand",
            "docket_num": "3:21-cv-00123",
            "versus": "Smith v. Jones Corporation"
        },
        {
            "court_id": "nysd",
            "docket_num": "1:22-cv-04567",
            "versus": "Doe et al v. Big Corp Inc."
        },
        {
            "court_id": "txnd",
            "docket_num": "unknown",
            "versus": ""
        },
    ]
    
    for case in test_cases:
        result = transformer._create_base_filename(case.copy())
        print(f"Input: {case}")
        print(f"Generated base_filename: {result.get('base_filename')}")
        assert result.get('base_filename'), "base_filename should not be empty"
    
    print("✓ Base filename generation test passed")


async def test_json_saving():
    """Test that JSON files are saved correctly."""
    print("\n=== Testing JSON file saving ===")
    
    # Create test directory
    iso_date = datetime.now().strftime("%Y%m%d")
    test_dir = Path("data") / iso_date / "test"
    
    # Create logger
    logger = logging.getLogger("test")
    
    # Create file manager
    file_manager = FileManager(logger=logger, config={})
    
    # Test case details
    case_details = {
        "court_id": "cand",
        "docket_num": "3:21-cv-00123",
        "versus": "Test Case v. Test Corp",
        "base_filename": "cand_21_00123_Test_Case_v_Test_Corp",
        "filing_date": "2021-01-15",
        "html_only": True,
        "mdl_num": "MDL-2873"
    }
    
    # Save to JSON
    await file_manager.save_case_data_to_json(case_details, "cand", iso_date)
    
    # Verify file was created
    expected_file = Path("data") / iso_date / "dockets" / f"{case_details['base_filename']}.json"
    assert expected_file.exists(), f"JSON file should exist at {expected_file}"
    
    # Verify content
    with open(expected_file, 'r') as f:
        saved_data = json.load(f)
    
    assert saved_data["court_id"] == "cand"
    assert saved_data["docket_num"] == "3:21-cv-00123"
    assert saved_data["base_filename"] == case_details["base_filename"]
    
    print(f"✓ JSON file saved successfully to {expected_file}")


async def test_docket_report_log():
    """Test that docket report logs are saved correctly."""
    print("\n=== Testing docket report log saving ===")
    
    iso_date = datetime.now().strftime("%Y%m%d")
    
    # Create mock job
    job = Mock(spec=DocketProcessingJob)
    job.court_id = "cand"
    job.row_num = 1
    job.total_rows = 10
    job.docket_num = "3:21-cv-00123"
    job.docket_link_href = "/cgi-bin/DktRpt.pl?123456"
    job.initial_versus_text = "Smith v. Jones"
    job.initial_filing_date = "01/15/2021"
    job.base_filename = "cand_21_00123_Smith_v_Jones"
    job.status = "success"
    job.error_message = None
    job.is_removal_case = False
    job.removal_link_number = None
    job.artifacts_downloaded = []
    job.case_details = {"court_id": "cand", "docket_num": "3:21-cv-00123"}
    job.config = {"iso_date": iso_date}
    
    # Create logger
    logger = logging.getLogger("test")
    logger.setLevel(logging.DEBUG)
    handler = logging.StreamHandler()
    handler.setLevel(logging.DEBUG)
    logger.addHandler(handler)
    job.court_logger = logger
    
    # Create job processor
    processor = JobProcessor(logger=logger, config={})
    
    # Save docket report log
    await processor._save_docket_report_log(job)
    
    # Verify file was created
    log_file = Path("data") / iso_date / "logs" / "docket_report" / "cand.json"
    assert log_file.exists(), f"Docket report log should exist at {log_file}"
    
    # Verify content
    with open(log_file, 'r') as f:
        logs = json.load(f)
    
    assert isinstance(logs, list), "Logs should be a list"
    assert len(logs) > 0, "Should have at least one log entry"
    
    last_log = logs[-1]
    assert last_log["court_id"] == "cand"
    assert last_log["docket_num"] == "3:21-cv-00123"
    assert last_log["base_filename"] == "cand_21_00123_Smith_v_Jones"
    assert last_log["status"] == "success"
    
    print(f"✓ Docket report log saved successfully to {log_file}")


async def test_court_logger():
    """Test that court-specific logger creates correct log files."""
    print("\n=== Testing court-specific logger ===")
    
    iso_date = datetime.now().strftime("%Y%m%d")
    
    # Create a mock component to test create_court_logger
    class TestComponent(ComponentImplementation):
        async def _execute_action(self, data):
            pass
    
    component = TestComponent(logger=logging.getLogger("test"), config={"DATA_DIR": "data"})
    
    # Create court logger
    court_logger = component.create_court_logger("cand", iso_date)
    
    # Log a test message
    court_logger.info("Test message for CAND court")
    
    # Verify log file was created
    log_file = Path("data") / iso_date / "logs" / "pacer" / "cand.log"
    assert log_file.exists(), f"Court log file should exist at {log_file}"
    
    # Verify content
    with open(log_file, 'r') as f:
        content = f.read()
    
    assert "Test message for CAND court" in content
    assert "pacer.court.cand" in content
    
    print(f"✓ Court-specific log saved successfully to {log_file}")


async def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing PACER Logging and JSON Saving Fixes")
    print("=" * 60)
    
    try:
        await test_base_filename_generation()
        await test_json_saving()
        await test_docket_report_log()
        await test_court_logger()
        
        print("\n" + "=" * 60)
        print("✅ All tests passed successfully!")
        print("=" * 60)
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())