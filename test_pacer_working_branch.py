#!/usr/bin/env python3
"""
Quick test to verify PACER working branch functionality.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent))

from src.config_models.base import WorkflowConfig
from src.factories.main_factory import MainServiceFactory


async def test_pacer_orchestrator_creation():
    """Test that we can create a PACER orchestrator via the factory."""
    print("\n=== Testing PACER Orchestrator Creation ===")
    
    # Create basic config
    from datetime import datetime
    config_dict = {
        'name': 'test_pacer',
        'date': datetime.now().strftime('%m/%d/%y'),
        'headless': True,
        'run_parallel': False,
        'timeout_ms': 30000,
        'scraper': True,
        'post_process': False,
        'upload': False,
        'project_root': str(Path.cwd()),
    }
    
    try:
        # Create workflow config
        workflow_config = WorkflowConfig(**config_dict)
        
        # Use MainServiceFactory
        async with MainServiceFactory(workflow_config, None) as factory:
            print("✅ MainServiceFactory created successfully")
            
            # Try to create PACER orchestrator
            orchestrator = await factory.create_pacer_orchestrator_service()
            print("✅ PacerOrchestratorService created successfully")
            print(f"✅ Orchestrator type: {type(orchestrator).__name__}")
            
            # Check if orchestrator has required methods
            required_methods = ['process_courts', 'process_single_docket']
            for method in required_methods:
                if hasattr(orchestrator, method):
                    print(f"✅ Method '{method}' found")
                else:
                    print(f"❌ Method '{method}' missing")
            
            return True
            
    except Exception as e:
        print(f"❌ Failed to create orchestrator: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_basic_config_loading():
    """Test that basic configuration loading works."""
    print("\n=== Testing Basic Configuration Loading ===")
    
    try:
        from src.utils.pacer_standalone import load_config_local
        
        config = load_config_local()
        print(f"✅ Config loaded successfully")
        print(f"✅ Config keys: {list(config.keys())}")
        
        # Check critical config values
        critical_keys = ['headless', 'run_parallel', 'project_root']
        for key in critical_keys:
            if key in config:
                print(f"✅ {key}: {config[key]}")
            else:
                print(f"❌ Missing key: {key}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests."""
    print("=" * 50)
    print("PACER Working Branch Verification")
    print("=" * 50)
    
    results = []
    
    # Test basic config
    results.append(await test_basic_config_loading())
    
    # Test orchestrator creation
    results.append(await test_pacer_orchestrator_creation())
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("✅ All tests passed - working branch is functional")
        return True
    else:
        print("❌ Some tests failed - issues detected")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)