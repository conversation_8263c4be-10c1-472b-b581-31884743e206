"""
Test for dependency injection fixes.

Tests that the MainServiceFactory properly creates and registers PacerRepository 
and AsyncDynamoDBStorage, and that SequentialWorkflowManager can be instantiated 
with proper dependencies.
"""

import pytest
import asyncio
import sys
from unittest.mock import Mock, AsyncMock, patch
from src.pacer.factories.service_factory import PacerServiceFactory, create_pacer_service_factory


class TestDependencyInjectionFix:
    """Test dependency injection registration fixes."""

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger."""
        logger = Mock()
        logger.info = Mock()
        logger.error = Mock()
        logger.warning = Mock()
        return logger

    @pytest.fixture
    def mock_config(self):
        """Create mock configuration."""
        return {
            'dynamodb': {
                'region': 'us-east-1',
                'table_name': 'test-table'
            },
            'sequential_workflow': {
                'max_retries': 3
            }
        }

    @pytest.mark.asyncio
    async def test_service_factory_creates_pacer_repository(self, mock_logger, mock_config):
        """Test that PacerRepository can be created from the service factory."""
        # Create service factory
        factory = PacerServiceFactory(config=mock_config, logger=mock_logger)
        
        with patch('src.repositories.pacer_repository.PacerRepository') as mock_repo_class:
            mock_repo_instance = Mock()
            mock_repo_class.return_value = mock_repo_instance
            
            with patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage') as mock_storage_class:
                mock_storage_instance = Mock()
                mock_storage_class.return_value = mock_storage_instance
                
                # Should be able to get PacerRepository without errors
                repo = await factory.get_pacer_repository()
                
                # Verify repository was created
                assert repo is not None
                mock_repo_class.assert_called_once()

    @pytest.mark.asyncio
    async def test_service_factory_creates_async_dynamodb_storage(self, mock_logger, mock_config):
        """Test that AsyncDynamoDBStorage can be created from the service factory."""
        # Create service factory
        factory = PacerServiceFactory(config=mock_config, logger=mock_logger)
        
        with patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage') as mock_storage_class:
            mock_storage_instance = Mock()
            mock_storage_class.return_value = mock_storage_instance
            
            # Should be able to get AsyncDynamoDBStorage without errors
            storage = await factory.get_async_dynamodb_storage()
            
            # Verify storage was created
            assert storage is not None
            mock_storage_class.assert_called_once()

    @pytest.mark.asyncio
    async def test_sequential_workflow_manager_with_dependencies(self, mock_logger, mock_config):
        """Test that SequentialWorkflowManager can be instantiated with proper dependencies."""
        # Create service factory
        factory = PacerServiceFactory(config=mock_config, logger=mock_logger)
        
        # Mock all the dependencies
        mocks = {}
        
        with patch('src.pacer.components.processing.sequential_workflow_manager.SequentialWorkflowManager') as mock_swm_class, \
             patch('src.repositories.pacer_repository.PacerRepository') as mock_repo_class, \
             patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage') as mock_storage_class, \
             patch('src.pacer._browser_components.navigator.PacerNavigator') as mock_nav_class, \
             patch('src.pacer.components.processing.docket_processor.DocketProcessor') as mock_dp_class:
            
            # Set up mock instances
            mock_swm_instance = Mock()
            mock_swm_class.return_value = mock_swm_instance
            
            mock_repo_instance = Mock()
            mock_repo_class.return_value = mock_repo_instance
            
            mock_storage_instance = Mock()
            mock_storage_class.return_value = mock_storage_instance
            
            mock_nav_instance = Mock()
            mock_nav_class.return_value = mock_nav_instance
            
            mock_dp_instance = Mock()
            mock_dp_class.return_value = mock_dp_instance
            
            # Should be able to get SequentialWorkflowManager without errors
            swm = await factory.get_sequential_workflow_manager()
            
            # Verify manager was created
            assert swm is not None
            mock_swm_class.assert_called_once()
            
            # Verify it was called with proper dependency arguments
            call_args = mock_swm_class.call_args
            assert call_args is not None
            assert 'navigation_facade' in call_args.kwargs
            assert 'docket_processor' in call_args.kwargs
            assert 'pacer_repository' in call_args.kwargs
            assert 'async_dynamodb_storage' in call_args.kwargs

    @pytest.mark.asyncio 
    async def test_sequential_workflow_manager_hard_fail_behavior(self, mock_logger, mock_config):
        """Test that SequentialWorkflowManager fails hard when dependencies are missing."""
        from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager
        
        # Test case 1: No dependencies at all - should cause hard fail
        with patch('sys.exit') as mock_exit:
            swm = SequentialWorkflowManager(
                navigation_facade=None,
                docket_processor=None,
                pacer_repository=None,
                async_dynamodb_storage=None,
                logger=mock_logger
            )
            
            # Initialize should trigger hard fail
            await swm._initialize_service()
            
            # Verify sys.exit was called with code 1
            mock_exit.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_sequential_workflow_manager_with_pacer_repository_only(self, mock_logger, mock_config):
        """Test that SequentialWorkflowManager works with just PacerRepository."""
        from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager
        
        # Mock dependencies
        mock_nav = Mock()
        mock_docket_processor = Mock()
        mock_repo = Mock()
        
        # Create with only PacerRepository (no AsyncDynamoDBStorage)
        swm = SequentialWorkflowManager(
            navigation_facade=mock_nav,
            docket_processor=mock_docket_processor,
            pacer_repository=mock_repo,
            async_dynamodb_storage=None,
            logger=mock_logger
        )
        
        # Should initialize successfully (no sys.exit)
        with patch('sys.exit') as mock_exit:
            await swm._initialize_service()
            # sys.exit should not be called
            mock_exit.assert_not_called()

    @pytest.mark.asyncio
    async def test_sequential_workflow_manager_with_async_storage_only(self, mock_logger, mock_config):
        """Test that SequentialWorkflowManager works with just AsyncDynamoDBStorage."""
        from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager
        
        # Mock dependencies
        mock_nav = Mock()
        mock_docket_processor = Mock()
        mock_storage = Mock()
        
        # Create with only AsyncDynamoDBStorage (no PacerRepository)
        swm = SequentialWorkflowManager(
            navigation_facade=mock_nav,
            docket_processor=mock_docket_processor,
            pacer_repository=None,
            async_dynamodb_storage=mock_storage,
            logger=mock_logger
        )
        
        # Should initialize successfully (no sys.exit)
        with patch('sys.exit') as mock_exit:
            await swm._initialize_service()
            # sys.exit should not be called
            mock_exit.assert_not_called()

    @pytest.mark.asyncio
    async def test_complete_dependency_injection_workflow(self, mock_logger, mock_config):
        """Test the complete dependency injection workflow end-to-end."""
        # Test that we can create the factory, initialize it, and get all required services
        factory = PacerServiceFactory(config=mock_config, logger=mock_logger)
        
        with patch('src.repositories.pacer_repository.PacerRepository') as mock_repo_class, \
             patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage') as mock_storage_class, \
             patch('src.pacer.components.processing.sequential_workflow_manager.SequentialWorkflowManager') as mock_swm_class, \
             patch('src.pacer._browser_components.navigator.PacerNavigator') as mock_nav_class, \
             patch('src.pacer.components.processing.docket_processor.DocketProcessor') as mock_dp_class:
            
            # Set up mock returns
            mock_repo_class.return_value = Mock()
            mock_storage_class.return_value = Mock()
            mock_swm_class.return_value = Mock()
            mock_nav_class.return_value = Mock()
            mock_dp_class.return_value = Mock()
            
            # Initialize factory
            await factory.initialize()
            assert factory.is_initialized()
            
            # Get all the new services
            repo = await factory.get_pacer_repository()
            storage = await factory.get_async_dynamodb_storage()
            swm = await factory.get_sequential_workflow_manager()
            
            # Verify all services were created
            assert repo is not None
            assert storage is not None
            assert swm is not None
            
            # Verify factory methods were called
            mock_repo_class.assert_called_once()
            mock_storage_class.assert_called_once() 
            mock_swm_class.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])