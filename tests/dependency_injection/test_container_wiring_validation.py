"""
Test utilities for container wiring validation.

This test suite provides utilities and tests for validating that
dependency injection container wiring works correctly.
"""
import pytest
from unittest.mock import Mock, patch, AsyncMock
from dependency_injector import containers, providers
from src.containers.storage import StorageContainer
from src.containers.pacer_core import Pacer<PERSON>oreContainer
from src.containers.core import MainContainer


class ContainerWiringValidator:
    """Utility class for validating container wiring."""
    
    @staticmethod
    def validate_provider_types(container, expected_providers):
        """
        Validate that container has expected providers with correct types.
        
        Args:
            container: Container instance to validate
            expected_providers: Dict mapping provider names to expected types
        """
        for provider_name, expected_type in expected_providers.items():
            assert hasattr(container, provider_name), f"Missing provider: {provider_name}"
            provider = getattr(container, provider_name)
            assert isinstance(provider, expected_type), f"Provider {provider_name} has wrong type: {type(provider)} vs {expected_type}"
    
    @staticmethod
    def validate_dependency_resolution(container, provider_name, dependency_names):
        """
        Validate that a provider can resolve its dependencies.
        
        Args:
            container: Container instance
            provider_name: Name of provider to test
            dependency_names: List of dependency names that should be resolvable
        """
        assert hasattr(container, provider_name), f"Missing provider: {provider_name}"
        provider = getattr(container, provider_name)
        
        # For Factory and Singleton providers, check if they can be called
        if isinstance(provider, (providers.Factory, providers.Singleton)):
            try:
                # This would fail if dependencies can't be resolved
                # Note: This doesn't actually instantiate in tests, just validates wiring
                pass
            except Exception as e:
                pytest.fail(f"Provider {provider_name} failed dependency resolution: {e}")
    
    @staticmethod
    def validate_container_structure(container, expected_structure):
        """
        Validate container structure matches expected hierarchy.
        
        Args:
            container: Container instance
            expected_structure: Dict describing expected container structure
        """
        for section_name, section_info in expected_structure.items():
            if section_info.get('required', True):
                assert hasattr(container, section_name), f"Missing required section: {section_name}"
            
            if hasattr(container, section_name):
                section = getattr(container, section_name)
                
                # Validate section type if specified
                if 'type' in section_info:
                    expected_type = section_info['type']
                    assert isinstance(section, expected_type), f"Section {section_name} has wrong type"
                
                # Validate subsections if specified
                if 'subsections' in section_info:
                    for subsection in section_info['subsections']:
                        assert hasattr(section, subsection), f"Missing subsection {subsection} in {section_name}"
    
    @staticmethod
    def validate_configuration_propagation(container, config_dict):
        """
        Validate that configuration propagates correctly through container.
        
        Args:
            container: Container instance
            config_dict: Configuration dictionary to test with
        """
        if hasattr(container, 'config'):
            container.config.from_dict(config_dict)
            
            # Test that config is accessible
            # This would be expanded based on specific container requirements


class TestContainerWiringValidation:
    """Test container wiring validation utilities and scenarios."""

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger for testing."""
        return Mock()

    @pytest.fixture
    def sample_config(self):
        """Create sample configuration for testing."""
        return {
            'aws_region': 'us-west-2',
            'aws_access_key': 'test_key',
            'aws_secret_key': 'test_secret',
            'dynamodb_endpoint': 'http://localhost:8000',
            's3_bucket_name': 'test-bucket',
            'headless': True,
            'run_parallel': True,
            'timeout_ms': 60000
        }

    def test_storage_container_wiring_validation(self, mock_logger, sample_config):
        """Test StorageContainer wiring validation."""
        container = StorageContainer()
        
        # Configure container
        container.config.from_dict(sample_config)
        container.logger.override(mock_logger)
        container.aws_region.override(sample_config['aws_region'])
        container.aws_access_key.override(sample_config['aws_access_key'])
        container.aws_secret_key.override(sample_config['aws_secret_key'])
        container.dynamodb_endpoint.override(sample_config['dynamodb_endpoint'])
        container.s3_bucket_name.override(sample_config['s3_bucket_name'])
        
        # Test provider types
        expected_providers = {
            'async_dynamodb_storage': providers.Singleton,
            's3_async_storage': providers.Singleton,
            'pacer_repository': providers.Singleton,
            'fb_archive_repository': providers.Singleton,
            'law_firms_repository': providers.Singleton,
            'district_courts_repository': providers.Singleton,
            'fb_image_hash_repository': providers.Singleton,
            'pacer_dockets_repository': providers.Singleton,
            'session_storage': providers.Singleton,
            'court_lookup': providers.Singleton,
            'law_firm_handler': providers.Singleton,
            'openai_client': providers.Singleton
        }
        
        ContainerWiringValidator.validate_provider_types(container, expected_providers)

    def test_pacer_core_container_wiring_validation(self, mock_logger, sample_config):
        """Test PacerCoreContainer wiring validation."""
        container = PacerCoreContainer()
        
        # Configure container
        container.config.from_dict(sample_config)
        container.logger.override(mock_logger)
        container.shutdown_event.override(AsyncMock())
        container.storage_container.override(Mock())
        
        # Test authentication components
        auth_providers = {
            'credential_validator': providers.Singleton,
            'ecf_login_handler': providers.Singleton,
            'login_handler': providers.Singleton,
            'session_manager': providers.Singleton
        }
        
        ContainerWiringValidator.validate_provider_types(container, auth_providers)
        
        # Test browser components
        browser_providers = {
            'playwright_manager': providers.Singleton,
            'context_factory': providers.Factory
        }
        
        ContainerWiringValidator.validate_provider_types(container, browser_providers)
        
        # Test case processing components
        case_processing_providers = {
            'case_enricher': providers.Singleton,
            'case_parser': providers.Singleton,
            'case_transformer': providers.Singleton,
            'case_validator': providers.Singleton
        }
        
        ContainerWiringValidator.validate_provider_types(container, case_processing_providers)

    def test_container_structure_validation(self, mock_logger, sample_config):
        """Test container structure validation."""
        container = StorageContainer()
        
        expected_structure = {
            'async_dynamodb_storage': {
                'required': True,
                'type': providers.Singleton
            },
            's3_async_storage': {
                'required': True,
                'type': providers.Singleton
            },
            'pacer_repository': {
                'required': True,
                'type': providers.Singleton
            },
            'session_storage': {
                'required': True,
                'type': providers.Singleton
            },
            'law_firm_handler': {
                'required': True,
                'type': providers.Singleton
            }
        }
        
        ContainerWiringValidator.validate_container_structure(container, expected_structure)

    def test_dependency_resolution_validation(self, mock_logger, sample_config):
        """Test dependency resolution validation."""
        container = StorageContainer()
        
        # Configure container
        container.config.from_dict(sample_config)
        container.logger.override(mock_logger)
        container.aws_region.override(sample_config['aws_region'])
        container.aws_access_key.override(sample_config['aws_access_key'])
        container.aws_secret_key.override(sample_config['aws_secret_key'])
        container.dynamodb_endpoint.override(sample_config['dynamodb_endpoint'])
        container.s3_bucket_name.override(sample_config['s3_bucket_name'])
        
        # Test that providers can resolve dependencies
        ContainerWiringValidator.validate_dependency_resolution(
            container, 
            'pacer_repository', 
            ['storage', 'logger']
        )
        
        ContainerWiringValidator.validate_dependency_resolution(
            container,
            'async_dynamodb_storage',
            ['config', 'logger']
        )

    def test_configuration_propagation_validation(self, mock_logger, sample_config):
        """Test configuration propagation validation."""
        container = StorageContainer()
        
        # Test configuration propagation
        ContainerWiringValidator.validate_configuration_propagation(container, sample_config)
        
        # Verify configuration is accessible
        if hasattr(container, 'config'):
            # Configuration should be available to providers
            pass

    def test_cross_container_dependency_validation(self, mock_logger, sample_config):
        """Test validation of dependencies across containers."""
        # Create storage container
        storage_container = StorageContainer()
        storage_container.config.from_dict(sample_config)
        storage_container.logger.override(mock_logger)
        storage_container.aws_region.override(sample_config['aws_region'])
        storage_container.aws_access_key.override(sample_config['aws_access_key'])
        storage_container.aws_secret_key.override(sample_config['aws_secret_key'])
        storage_container.dynamodb_endpoint.override(sample_config['dynamodb_endpoint'])
        storage_container.s3_bucket_name.override(sample_config['s3_bucket_name'])
        
        # Create PACER container
        pacer_container = PacerCoreContainer()
        pacer_container.config.from_dict(sample_config)
        pacer_container.logger.override(mock_logger)
        pacer_container.shutdown_event.override(AsyncMock())
        pacer_container.storage_container.override(storage_container)
        
        # Validate that PACER container can access storage dependencies
        assert pacer_container.storage_container() == storage_container

    def test_wiring_order_validation(self, mock_logger, sample_config):
        """Test that container wiring happens in correct order."""
        container = StorageContainer()
        
        # Configure dependencies in order
        container.config.from_dict(sample_config)
        container.logger.override(mock_logger)
        
        # Test that base dependencies are configured before dependent services
        container.aws_region.override(sample_config['aws_region'])
        container.aws_access_key.override(sample_config['aws_access_key'])
        container.aws_secret_key.override(sample_config['aws_secret_key'])
        container.dynamodb_endpoint.override(sample_config['dynamodb_endpoint'])
        container.s3_bucket_name.override(sample_config['s3_bucket_name'])
        
        # Now storage services should be configurable
        # This tests that the wiring order allows dependencies to be resolved

    def test_module_wiring_validation(self):
        """Test module wiring validation for dependency injection."""
        # Test that modules can be wired to containers
        modules_to_test = [
            'src.repositories',
            'src.infrastructure.storage',
            'src.pacer.services',
            'src.pacer.components'
        ]
        
        # This would test that modules can be successfully wired
        # The exact test depends on how modules are structured
        for module in modules_to_test:
            # Test module import and structure
            try:
                __import__(module)
            except ImportError:
                # Module doesn't exist - that's okay for this test
                pass

    def test_provider_override_validation(self, mock_logger, sample_config):
        """Test provider override validation."""
        container = StorageContainer()
        
        # Test original configuration
        container.config.from_dict(sample_config)
        container.logger.override(mock_logger)
        
        # Test override
        new_logger = Mock()
        container.logger.override(new_logger)
        
        # Validate override took effect
        # This would be container-specific validation

    def test_singleton_behavior_validation(self, mock_logger, sample_config):
        """Test singleton behavior validation across wiring."""
        container = StorageContainer()
        
        # Configure container
        container.config.from_dict(sample_config)
        container.logger.override(mock_logger)
        container.aws_region.override(sample_config['aws_region'])
        container.aws_access_key.override(sample_config['aws_access_key'])
        container.aws_secret_key.override(sample_config['aws_secret_key'])
        container.dynamodb_endpoint.override(sample_config['dynamodb_endpoint'])
        container.s3_bucket_name.override(sample_config['s3_bucket_name'])
        
        # Test singleton behavior
        with patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage') as mock_storage:
            mock_instance = Mock()
            mock_storage.return_value = mock_instance
            
            # Get the same service multiple times
            service1 = container.async_dynamodb_storage()
            service2 = container.async_dynamodb_storage()
            
            # Should be the same instance (singleton)
            assert service1 is service2
            # Should only be instantiated once
            assert mock_storage.call_count == 1

    def test_factory_behavior_validation(self, mock_logger, sample_config):
        """Test factory behavior validation across wiring."""
        container = PacerCoreContainer()
        
        # Configure container
        container.config.from_dict(sample_config)
        container.logger.override(mock_logger)
        container.shutdown_event.override(AsyncMock())
        container.storage_container.override(Mock())
        
        # Test factory behavior for services that should create new instances
        with patch('src.pacer.services.configuration_service.ConfigurationService') as mock_service:
            mock_service.return_value = Mock()
            
            # Get the service multiple times
            service1 = container.configuration_service()
            service2 = container.configuration_service()
            
            # Should be different instances (factory)
            assert mock_service.call_count == 2

    def test_container_cleanup_validation(self, mock_logger, sample_config):
        """Test container cleanup validation after wiring."""
        container = StorageContainer()
        
        # Configure and use container
        container.config.from_dict(sample_config)
        container.logger.override(mock_logger)
        
        # Simulate container usage
        # Then test cleanup
        
        # For this test, we just verify the container can be properly disposed of
        # In real scenarios, this would test resource cleanup

    def test_error_recovery_validation(self, mock_logger, sample_config):
        """Test error recovery validation in container wiring."""
        container = StorageContainer()
        
        # Configure container
        container.config.from_dict(sample_config)
        container.logger.override(mock_logger)
        
        # Test recovery from configuration errors
        try:
            container.aws_region.override(None)  # Invalid configuration
            # Container should handle this gracefully
        except Exception:
            # Error handling should be graceful
            pass
        
        # Container should still be usable after error recovery
        container.aws_region.override(sample_config['aws_region'])

    def test_circular_dependency_detection_validation(self):
        """Test validation of circular dependency detection."""
        # This test would validate that circular dependencies are properly detected
        # and handled by the DI container
        
        # Create a test scenario with potential circular dependencies
        class TestContainerA(containers.DeclarativeContainer):
            service_b = providers.Dependency()
            service_a = providers.Factory(Mock, dependency=service_b)
        
        class TestContainerB(containers.DeclarativeContainer):
            service_a = providers.Dependency()
            service_b = providers.Factory(Mock, dependency=service_a)
        
        # This would test circular dependency detection
        # The exact test depends on DI framework capabilities