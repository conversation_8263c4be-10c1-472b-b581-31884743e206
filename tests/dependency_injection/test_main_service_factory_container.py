"""
Comprehensive tests for MainServiceFactory container creation and validation.

This test suite validates that the MainServiceFactory can successfully create
and configure the dependency injection container with all required dependencies.
"""
import pytest
import asyncio
import os
from unittest.mock import Mock, patch, AsyncMock
from src.factories.main_factory import MainServiceFactory
from src.config_models.base import WorkflowConfig
from dependency_injector.wiring import inject, Provide


class TestMainServiceFactoryContainer:
    """Test MainServiceFactory container creation and configuration."""

    @pytest.fixture
    def mock_config(self):
        """Create a mock WorkflowConfig for testing."""
        config = Mock(spec=WorkflowConfig)
        config.headless = True
        config.run_parallel = True
        config.timeout_ms = 60000
        config.config_name = "test_config"
        config.model_dump = Mock(return_value={
            "headless": True,
            "run_parallel": True,
            "timeout_ms": 60000,
            "pacer": {},
            "storage": {},
            "fb_ads": {},
            "transformer": {},
            "reports": {}
        })
        return config

    @pytest.fixture
    def shutdown_event(self):
        """Create a shutdown event for testing."""
        return asyncio.Event()

    @pytest.mark.asyncio
    async def test_factory_initialization_success(self, mock_config, shutdown_event):
        """Test successful MainServiceFactory initialization."""
        # Set required environment variables
        with patch.dict(os.environ, {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root'
        }):
            factory = MainServiceFactory(mock_config, shutdown_event)
            
            # Test factory basic initialization
            assert factory.config == mock_config
            assert factory.shutdown_event == shutdown_event
            assert factory.logger is not None
            assert factory._container is None

    @pytest.mark.asyncio
    async def test_container_creation_and_wiring(self, mock_config, shutdown_event):
        """Test that the DI container is created and wired correctly."""
        with patch.dict(os.environ, {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_key', 
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root'
        }):
            factory = MainServiceFactory(mock_config, shutdown_event)
            
            # Mock container creation to avoid actual initialization
            with patch('src.factories.main_factory.create_container') as mock_create:
                mock_container = Mock()
                mock_container.wire = Mock()
                mock_container.init_resources = Mock(return_value=None)
                mock_container.storage = Mock()
                mock_container.pacer = Mock()
                
                # Mock storage validation attributes
                mock_container.storage.async_dynamodb_storage = Mock()
                mock_container.storage.pacer_repository = Mock()
                mock_container.storage.s3_async_storage = Mock()
                
                mock_create.return_value = mock_container
                
                async with factory:
                    # Verify container was created
                    assert factory._container is not None
                    assert factory._container == mock_container
                    
                    # Verify wiring was called
                    mock_container.wire.assert_called_once()
                    
                    # Verify resource initialization was called
                    mock_container.init_resources.assert_called_once()

    @pytest.mark.asyncio
    async def test_config_preparation_and_validation(self, mock_config, shutdown_event):
        """Test configuration preparation and validation."""
        with patch.dict(os.environ, {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root',
            'PACER_USERNAME_PROD': 'test_user',
            'PACER_PASSWORD_PROD': 'test_pass'
        }):
            factory = MainServiceFactory(mock_config, shutdown_event)
            
            # Test config preparation
            config_dict = factory._prepare_config_dict()
            
            # Verify required sections are present
            assert 'pacer' in config_dict
            assert 'storage' in config_dict
            assert 'fb_ads' in config_dict
            assert 'transformer' in config_dict
            assert 'reports' in config_dict
            assert 'project_root' in config_dict
            assert 'directories' in config_dict
            
            # Verify PACER configuration includes root-level values
            pacer_config = config_dict['pacer']
            assert pacer_config['headless'] == True
            assert pacer_config['run_parallel'] == True
            assert pacer_config['timeout_ms'] == 60000
            
            # Verify environment variables are included
            assert config_dict['aws_region'] == 'us-west-2'
            assert config_dict['aws_access_key'] == 'test_key'
            assert config_dict['s3_bucket_name'] == 'test-bucket'
            assert config_dict['username_prod'] == 'test_user'
            assert config_dict['password_prod'] == 'test_pass'

    @pytest.mark.asyncio
    async def test_storage_dependency_validation(self, mock_config, shutdown_event):
        """Test that storage dependencies are properly validated."""
        with patch.dict(os.environ, {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root'
        }):
            factory = MainServiceFactory(mock_config, shutdown_event)
            
            # Mock container with proper storage attributes
            mock_container = Mock()
            mock_container.wire = Mock()
            mock_container.init_resources = Mock(return_value=None)
            mock_container.storage = Mock()
            mock_container.pacer = Mock()
            
            # Mock all required storage dependencies
            mock_container.storage.async_dynamodb_storage = Mock(return_value=Mock())
            mock_container.storage.pacer_repository = Mock(return_value=Mock())  
            mock_container.storage.s3_async_storage = Mock(return_value=Mock())
            
            with patch('src.factories.main_factory.create_container', return_value=mock_container):
                async with factory:
                    # Validation should pass without raising exceptions
                    assert factory._container is not None

    @pytest.mark.asyncio
    async def test_pacer_dependency_validation(self, mock_config, shutdown_event):
        """Test that PACER dependencies are properly validated."""
        with patch.dict(os.environ, {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root'
        }):
            factory = MainServiceFactory(mock_config, shutdown_event)
            
            # Mock container with PACER dependencies
            mock_container = Mock()
            mock_container.wire = Mock()
            mock_container.init_resources = Mock(return_value=None)
            mock_container.storage = Mock()
            mock_container.pacer = Mock()
            
            # Mock storage dependencies
            mock_container.storage.async_dynamodb_storage = Mock(return_value=Mock())
            mock_container.storage.pacer_repository = Mock(return_value=Mock())
            mock_container.storage.s3_async_storage = Mock(return_value=Mock())
            
            # Mock PACER dependencies
            mock_container.pacer.sequential_workflow_manager = Mock(return_value=Mock())
            mock_container.pacer.sequential_docket_processor = Mock(return_value=Mock())
            mock_container.pacer.verified_sequential_workflow_factory = Mock(return_value=Mock())
            
            with patch('src.factories.main_factory.create_container', return_value=mock_container):
                async with factory:
                    # PACER validation should pass
                    assert factory._container is not None

    @pytest.mark.asyncio
    async def test_container_cleanup_on_exit(self, mock_config, shutdown_event):
        """Test proper container cleanup on context exit."""
        with patch.dict(os.environ, {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root'
        }):
            factory = MainServiceFactory(mock_config, shutdown_event)
            
            mock_container = Mock()
            mock_container.wire = Mock()
            mock_container.init_resources = Mock(return_value=None)
            mock_container.shutdown_resources = Mock(return_value=None)
            mock_container.unwire = Mock()
            mock_container.storage = Mock()
            mock_container.pacer = Mock()
            
            # Mock storage dependencies
            mock_container.storage.async_dynamodb_storage = Mock(return_value=Mock())
            mock_container.storage.pacer_repository = Mock(return_value=Mock())
            mock_container.storage.s3_async_storage = Mock(return_value=Mock())
            
            with patch('src.factories.main_factory.create_container', return_value=mock_container):
                async with factory:
                    assert factory._container is not None
                
                # After exit, container should be cleaned up
                assert factory._container is None
                mock_container.shutdown_resources.assert_called_once()
                mock_container.unwire.assert_called_once()

    @pytest.mark.asyncio
    async def test_error_handling_on_container_creation_failure(self, mock_config, shutdown_event):
        """Test error handling when container creation fails."""
        with patch.dict(os.environ, {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root'
        }):
            factory = MainServiceFactory(mock_config, shutdown_event)
            
            # Mock container creation failure
            with patch('src.factories.main_factory.create_container', side_effect=Exception("Container creation failed")):
                with pytest.raises(RuntimeError, match="MainServiceFactory initialization failed"):
                    async with factory:
                        pass
                
                # Container should be None after failed initialization
                assert factory._container is None

    @pytest.mark.asyncio
    async def test_error_handling_on_wiring_failure(self, mock_config, shutdown_event):
        """Test error handling when container wiring fails."""
        with patch.dict(os.environ, {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root'
        }):
            factory = MainServiceFactory(mock_config, shutdown_event)
            
            mock_container = Mock()
            mock_container.wire = Mock(side_effect=Exception("Wiring failed"))
            mock_container.unwire = Mock()
            mock_container.storage = Mock()
            mock_container.pacer = Mock()
            
            # Mock storage dependencies
            mock_container.storage.async_dynamodb_storage = Mock(return_value=Mock())
            mock_container.storage.pacer_repository = Mock(return_value=Mock())
            mock_container.storage.s3_async_storage = Mock(return_value=Mock())
            
            with patch('src.factories.main_factory.create_container', return_value=mock_container):
                with pytest.raises(RuntimeError, match="MainServiceFactory initialization failed"):
                    async with factory:
                        pass
                
                # Unwire should be called on failure
                mock_container.unwire.assert_called_once()

    @pytest.mark.asyncio
    async def test_missing_storage_dependencies_error(self, mock_config, shutdown_event):
        """Test error handling for missing storage dependencies."""
        with patch.dict(os.environ, {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root'
        }):
            factory = MainServiceFactory(mock_config, shutdown_event)
            
            # Mock container missing storage dependencies
            mock_container = Mock()
            mock_container.wire = Mock()
            mock_container.init_resources = Mock(return_value=None)
            mock_container.pacer = Mock()
            # Don't set storage attribute to simulate missing dependency
            
            with patch('src.factories.main_factory.create_container', return_value=mock_container):
                with pytest.raises(RuntimeError, match="Storage container not found"):
                    async with factory:
                        pass

    def test_get_storage_services_when_container_not_initialized(self, mock_config, shutdown_event):
        """Test storage service getters when container is not initialized."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Should return None when container not initialized
        assert factory.get_dynamodb_storage() is None
        assert factory.get_s3_storage() is None

    @pytest.mark.asyncio
    async def test_get_storage_services_when_container_initialized(self, mock_config, shutdown_event):
        """Test storage service getters when container is initialized."""
        with patch.dict(os.environ, {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root'
        }):
            factory = MainServiceFactory(mock_config, shutdown_event)
            
            mock_dynamodb = Mock()
            mock_s3 = Mock()
            mock_container = Mock()
            mock_container.wire = Mock()
            mock_container.init_resources = Mock(return_value=None)
            mock_container.storage = Mock()
            mock_container.pacer = Mock()
            
            # Mock storage services
            mock_container.storage.async_dynamodb_storage = Mock(return_value=mock_dynamodb)
            mock_container.storage.pacer_repository = Mock(return_value=Mock())
            mock_container.storage.s3_async_storage = Mock(return_value=mock_s3)
            
            with patch('src.factories.main_factory.create_container', return_value=mock_container):
                async with factory:
                    # Should return actual storage services
                    assert factory.get_dynamodb_storage() == mock_dynamodb
                    assert factory.get_s3_storage() == mock_s3

    @pytest.mark.asyncio
    async def test_configuration_validation_failure(self, shutdown_event):
        """Test configuration validation failure scenarios."""
        # Test with None config
        with pytest.raises(ValueError, match="Configuration is required"):
            factory = MainServiceFactory(None, shutdown_event)
            factory._validate_configuration()

    def test_config_dict_validation_failure(self, mock_config, shutdown_event):
        """Test configuration dictionary validation failure scenarios."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Test with non-dict config
        with pytest.raises(ValueError, match="Config dict must be a dictionary"):
            factory._validate_config_dict("not_a_dict")
        
        # Test with missing project root
        config_dict = {"pacer": {}, "storage": {}}
        with pytest.raises(ValueError, match="Project root not configured"):
            factory._validate_config_dict(config_dict)