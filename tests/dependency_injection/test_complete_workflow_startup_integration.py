"""
Integration test for complete workflow startup with dependency injection.

This test suite validates that the entire workflow can start up successfully
with all dependencies properly injected and initialized.
"""
import pytest
import asyncio
import os
from unittest.mock import Mock, patch, AsyncMock
from src.factories.main_factory import MainServiceFactory
from src.config_models.base import WorkflowConfig


class TestCompleteWorkflowStartupIntegration:
    """Test complete workflow startup integration with dependency injection."""

    @pytest.fixture
    def complete_workflow_config(self):
        """Create a complete workflow configuration for testing."""
        config = Mock(spec=WorkflowConfig)
        config.headless = True
        config.run_parallel = True
        config.timeout_ms = 60000
        config.config_name = "integration_test_config"
        config.court_id = "ilnd"
        config.case_type = "civil"
        config.date_from = "2024-01-01"
        config.date_to = "2024-01-31"
        config.batch_size = 10
        config.max_retries = 3
        
        config.model_dump = Mock(return_value={
            "headless": True,
            "run_parallel": True,
            "timeout_ms": 60000,
            "court_id": "ilnd",
            "case_type": "civil",
            "date_from": "2024-01-01",
            "date_to": "2024-01-31",
            "batch_size": 10,
            "max_retries": 3,
            "pacer": {
                "workflow": {},
                "browser": {"headless": True}
            },
            "storage": {},
            "fb_ads": {},
            "transformer": {},
            "reports": {}
        })
        return config

    @pytest.fixture
    def complete_environment_vars(self):
        """Create complete environment variables for testing."""
        return {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_access_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret_key',
            'S3_BUCKET_NAME': 'test-integration-bucket',
            'DYNAMODB_ENDPOINT': 'http://localhost:8000',
            'PACER_USERNAME_PROD': 'test_pacer_user',
            'PACER_PASSWORD_PROD': 'test_pacer_pass',
            'DEEPSEEK_API_KEY': 'test_deepseek_key',
            'OPENAI_API_KEY': 'test_openai_key',
            'LLAVA_BASE_URL': 'http://localhost:11434',
            'LLAVA_MODEL': 'llava',
            'LLAVA_TIMEOUT': '60',
            'FB_CIPHERS': 'TLS_AES_128_GCM_SHA256',
            'LEXGENIUS_PROJECT_ROOT': '/test/project/root'
        }

    @pytest.fixture
    def shutdown_event(self):
        """Create a shutdown event for testing."""
        return asyncio.Event()

    @pytest.mark.asyncio
    async def test_complete_workflow_factory_initialization(self, complete_workflow_config, complete_environment_vars, shutdown_event):
        """Test complete workflow factory initialization with all dependencies."""
        with patch.dict(os.environ, complete_environment_vars):
            factory = MainServiceFactory(complete_workflow_config, shutdown_event)
            
            # Mock all required services and containers
            with patch('src.factories.main_factory.create_container') as mock_create_container:
                mock_container = self._create_complete_mock_container()
                mock_create_container.return_value = mock_container
                
                async with factory:
                    # Verify factory initialized successfully
                    assert factory._container is not None
                    assert factory._container == mock_container
                    
                    # Verify container was properly configured and wired
                    mock_container.wire.assert_called_once()
                    mock_container.init_resources.assert_called_once()

    @pytest.mark.asyncio
    async def test_pacer_orchestrator_service_complete_creation(self, complete_workflow_config, complete_environment_vars, shutdown_event):
        """Test complete PACER orchestrator service creation through factory."""
        with patch.dict(os.environ, complete_environment_vars):
            factory = MainServiceFactory(complete_workflow_config, shutdown_event)
            
            with patch('src.factories.main_factory.create_container') as mock_create_container:
                mock_container = self._create_complete_mock_container()
                mock_create_container.return_value = mock_container
                
                # Mock all PACER service dependencies
                with self._mock_pacer_service_dependencies():
                    async with factory:
                        # Create PACER orchestrator service
                        pacer_service = await factory.create_pacer_orchestrator_service()
                        
                        # Verify service was created successfully
                        assert pacer_service is not None
                        assert hasattr(pacer_service, 'workflow_orchestrator') or hasattr(pacer_service, 'logger')

    @pytest.mark.asyncio
    async def test_reports_orchestrator_service_complete_creation(self, complete_workflow_config, complete_environment_vars, shutdown_event):
        """Test complete reports orchestrator service creation through factory."""
        with patch.dict(os.environ, complete_environment_vars):
            factory = MainServiceFactory(complete_workflow_config, shutdown_event)
            
            with patch('src.factories.main_factory.create_container') as mock_create_container:
                mock_container = self._create_complete_mock_container()
                mock_create_container.return_value = mock_container
                
                async with factory:
                    # Create reports orchestrator service
                    reports_service = await factory.create_reports_orchestrator_service(is_weekly=True)
                    
                    # Verify service was created successfully
                    assert reports_service is not None
                    assert reports_service.is_weekly == True

    @pytest.mark.asyncio
    async def test_scraping_orchestrator_complete_creation(self, complete_workflow_config, complete_environment_vars, shutdown_event):
        """Test complete scraping orchestrator creation through factory."""
        with patch.dict(os.environ, complete_environment_vars):
            factory = MainServiceFactory(complete_workflow_config, shutdown_event)
            
            with patch('src.factories.main_factory.create_container') as mock_create_container:
                mock_container = self._create_complete_mock_container()
                mock_create_container.return_value = mock_container
                
                with self._mock_pacer_service_dependencies():
                    async with factory:
                        # Create scraping orchestrator
                        scraping_service = await factory.create_scraping_orchestrator()
                        
                        # Verify service was created successfully
                        assert scraping_service is not None
                        assert hasattr(scraping_service, 'config')
                        assert hasattr(scraping_service, 'pacer_service')

    @pytest.mark.asyncio
    async def test_processing_orchestrator_complete_creation(self, complete_workflow_config, complete_environment_vars, shutdown_event):
        """Test complete processing orchestrator creation through factory."""
        with patch.dict(os.environ, complete_environment_vars):
            factory = MainServiceFactory(complete_workflow_config, shutdown_event)
            
            with patch('src.factories.main_factory.create_container') as mock_create_container:
                mock_container = self._create_complete_mock_container()
                mock_create_container.return_value = mock_container
                
                async with factory:
                    # Create processing orchestrator
                    processing_service = await factory.create_processing_orchestrator()
                    
                    # Verify service was created successfully
                    assert processing_service is not None
                    assert hasattr(processing_service, 'config')

    @pytest.mark.asyncio
    async def test_upload_orchestrator_complete_creation(self, complete_workflow_config, complete_environment_vars, shutdown_event):
        """Test complete upload orchestrator creation through factory."""
        with patch.dict(os.environ, complete_environment_vars):
            factory = MainServiceFactory(complete_workflow_config, shutdown_event)
            
            with patch('src.factories.main_factory.create_container') as mock_create_container:
                mock_container = self._create_complete_mock_container()
                mock_create_container.return_value = mock_container
                
                async with factory:
                    # Create upload orchestrator
                    upload_service = await factory.create_upload_orchestrator()
                    
                    # Verify service was created successfully
                    assert upload_service is not None
                    assert hasattr(upload_service, 'config')

    @pytest.mark.asyncio
    async def test_fb_ads_orchestrator_complete_creation(self, complete_workflow_config, complete_environment_vars, shutdown_event):
        """Test complete FB ads orchestrator creation through factory."""
        with patch.dict(os.environ, complete_environment_vars):
            factory = MainServiceFactory(complete_workflow_config, shutdown_event)
            
            with patch('src.factories.main_factory.create_container') as mock_create_container:
                mock_container = self._create_complete_mock_container()
                mock_create_container.return_value = mock_container
                
                async with factory:
                    # Create FB ads orchestrator
                    fb_ads_service = await factory.create_fb_ads_orchestrator()
                    
                    # Verify service was created successfully
                    assert fb_ads_service is not None
                    assert hasattr(fb_ads_service, 'config')

    @pytest.mark.asyncio
    async def test_storage_services_complete_access(self, complete_workflow_config, complete_environment_vars, shutdown_event):
        """Test complete storage services access through factory."""
        with patch.dict(os.environ, complete_environment_vars):
            factory = MainServiceFactory(complete_workflow_config, shutdown_event)
            
            with patch('src.factories.main_factory.create_container') as mock_create_container:
                mock_container = self._create_complete_mock_container()
                mock_create_container.return_value = mock_container
                
                async with factory:
                    # Get storage services
                    dynamodb_storage = factory.get_dynamodb_storage()
                    s3_storage = factory.get_s3_storage()
                    
                    # Verify storage services are accessible
                    assert dynamodb_storage is not None
                    assert s3_storage is not None

    @pytest.mark.asyncio
    async def test_complete_workflow_dependency_chain_validation(self, complete_workflow_config, complete_environment_vars, shutdown_event):
        """Test validation of complete dependency chain in workflow startup."""
        with patch.dict(os.environ, complete_environment_vars):
            factory = MainServiceFactory(complete_workflow_config, shutdown_event)
            
            with patch('src.factories.main_factory.create_container') as mock_create_container:
                mock_container = self._create_complete_mock_container()
                mock_create_container.return_value = mock_container
                
                with self._mock_pacer_service_dependencies():
                    async with factory:
                        # Verify all containers are accessible
                        assert hasattr(factory._container, 'storage')
                        assert hasattr(factory._container, 'pacer')
                        assert hasattr(factory._container, 'fb_ads')
                        assert hasattr(factory._container, 'transformer')
                        assert hasattr(factory._container, 'reports')
                        
                        # Test dependency resolution across containers
                        storage_container = factory._container.storage
                        pacer_container = factory._container.pacer
                        
                        # Verify cross-container dependencies work
                        assert storage_container is not None
                        assert pacer_container is not None

    @pytest.mark.asyncio
    async def test_complete_workflow_initialization_sequence(self, complete_workflow_config, complete_environment_vars, shutdown_event):
        """Test complete workflow initialization sequence."""
        with patch.dict(os.environ, complete_environment_vars):
            factory = MainServiceFactory(complete_workflow_config, shutdown_event)
            
            with patch('src.factories.main_factory.create_container') as mock_create_container:
                mock_container = self._create_complete_mock_container()
                mock_create_container.return_value = mock_container
                
                initialization_steps = []
                
                # Track initialization sequence
                def track_wire(*args, **kwargs):
                    initialization_steps.append('wire')
                
                def track_init_resources(*args, **kwargs):
                    initialization_steps.append('init_resources')
                    return None
                
                mock_container.wire.side_effect = track_wire
                mock_container.init_resources.side_effect = track_init_resources
                
                async with factory:
                    # Verify initialization sequence is correct
                    assert 'wire' in initialization_steps
                    assert 'init_resources' in initialization_steps
                    assert initialization_steps.index('wire') < initialization_steps.index('init_resources')

    @pytest.mark.asyncio
    async def test_complete_workflow_cleanup_sequence(self, complete_workflow_config, complete_environment_vars, shutdown_event):
        """Test complete workflow cleanup sequence."""
        with patch.dict(os.environ, complete_environment_vars):
            factory = MainServiceFactory(complete_workflow_config, shutdown_event)
            
            with patch('src.factories.main_factory.create_container') as mock_create_container:
                mock_container = self._create_complete_mock_container()
                mock_create_container.return_value = mock_container
                
                cleanup_steps = []
                
                # Track cleanup sequence
                def track_shutdown_resources(*args, **kwargs):
                    cleanup_steps.append('shutdown_resources')
                    return None
                
                def track_unwire(*args, **kwargs):
                    cleanup_steps.append('unwire')
                
                mock_container.shutdown_resources.side_effect = track_shutdown_resources
                mock_container.unwire.side_effect = track_unwire
                
                async with factory:
                    pass  # Just enter and exit context
                
                # Verify cleanup sequence is correct
                assert 'shutdown_resources' in cleanup_steps
                assert 'unwire' in cleanup_steps
                assert cleanup_steps.index('shutdown_resources') < cleanup_steps.index('unwire')

    @pytest.mark.asyncio
    async def test_workflow_startup_with_partial_environment_config(self, complete_workflow_config, shutdown_event):
        """Test workflow startup with partial environment configuration."""
        # Test with minimal environment variables
        minimal_env = {
            'AWS_REGION': 'us-west-2',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root'
        }
        
        with patch.dict(os.environ, minimal_env, clear=True):
            factory = MainServiceFactory(complete_workflow_config, shutdown_event)
            
            with patch('src.factories.main_factory.create_container') as mock_create_container:
                mock_container = self._create_complete_mock_container()
                mock_create_container.return_value = mock_container
                
                async with factory:
                    # Should handle missing environment variables gracefully
                    config_dict = factory._prepare_config_dict()
                    
                    # Verify defaults are used for missing variables
                    assert config_dict['aws_access_key'] == ''
                    assert config_dict['aws_secret_key'] == ''
                    assert config_dict['aws_region'] == 'us-west-2'
                    assert config_dict['s3_bucket_name'] == 'test-bucket'

    def _create_complete_mock_container(self):
        """Create a complete mock container with all required dependencies."""
        mock_container = Mock()
        
        # Mock container methods
        mock_container.wire = Mock()
        mock_container.init_resources = Mock(return_value=None)
        mock_container.shutdown_resources = Mock(return_value=None)
        mock_container.unwire = Mock()
        
        # Mock storage container
        mock_storage = Mock()
        mock_storage.async_dynamodb_storage = Mock(return_value=Mock())
        mock_storage.pacer_repository = Mock(return_value=Mock())
        mock_storage.s3_async_storage = Mock(return_value=Mock())
        mock_storage.district_courts_repository = Mock(return_value=Mock())
        mock_storage.law_firms_repository = Mock(return_value=Mock())
        mock_storage.fb_archive_repository = Mock(return_value=Mock())
        mock_container.storage = mock_storage
        
        # Mock PACER container
        mock_pacer = Mock()
        mock_pacer.sequential_workflow_manager = Mock(return_value=Mock())
        mock_pacer.sequential_docket_processor = Mock(return_value=Mock())
        mock_pacer.verified_sequential_workflow_factory = Mock(return_value=Mock())
        mock_pacer.pacer_orchestrator = Mock(return_value=Mock())
        mock_pacer.shutdown_event = Mock()
        mock_pacer.shutdown_event.override = Mock()
        mock_container.pacer = mock_pacer
        
        # Mock other containers
        mock_container.fb_ads = Mock()
        mock_container.fb_ads.ai_orchestrator = Mock(return_value=Mock())
        mock_container.fb_ads.deepseek_service = Mock(return_value=Mock())
        mock_container.fb_ads.prompt_manager = Mock(return_value=Mock())
        
        mock_container.transformer = Mock()
        mock_container.transformer.data_transformer = Mock(return_value=Mock())
        mock_container.transformer.data_upload_service = Mock(return_value=Mock())
        mock_container.transformer.file_handler_core = Mock(return_value=Mock())
        mock_container.transformer.shutdown_event = Mock()
        mock_container.transformer.shutdown_event.override = Mock()
        
        mock_container.reports = Mock()
        mock_container.reports.reports_orchestrator = Mock(return_value=Mock())
        
        return mock_container
    
    def _mock_pacer_service_dependencies(self):
        """Context manager to mock PACER service dependencies."""
        return patch.multiple(
            'src.pacer.components.processing.court_processor',
            CourtProcessor=Mock(return_value=Mock()),
            DocketProcessor=Mock(return_value=Mock()),
            RowProcessor=Mock(return_value=Mock()),
            DownloadManager=Mock(return_value=Mock()),
            FileOperationsService=Mock(return_value=Mock()),
            NavigationFacade=Mock(return_value=Mock()),
            ReportFacade=Mock(return_value=Mock()),
            BrowserService=Mock(return_value=Mock()),
            ConfigurationService=Mock(return_value=Mock()),
            CaseProcessingService=Mock(return_value=Mock()),
            RelevanceService=Mock(return_value=Mock()),
            ClassificationService=Mock(return_value=Mock()),
            VerificationService=Mock(return_value=Mock()),
            S3AsyncStorage=Mock(return_value=Mock()),
            PacerDataMerger=Mock(return_value=Mock()),
            FilenameValidator=Mock(return_value=Mock()),
            DocketOrchestrator=Mock(return_value=Mock())
        )