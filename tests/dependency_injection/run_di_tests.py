#!/usr/bin/env python3
"""
Test runner for dependency injection test suite.

This script provides a convenient way to run all dependency injection tests
with various options and configurations.
"""
import subprocess
import sys
import os
import argparse


def run_tests(test_type=None, verbose=False, coverage=False, markers=None, specific_file=None):
    """
    Run dependency injection tests with specified options.
    
    Args:
        test_type: Type of tests to run ('unit', 'integration', 'performance', etc.)
        verbose: Enable verbose output
        coverage: Enable coverage reporting
        markers: Pytest markers to filter tests
        specific_file: Run only a specific test file
    """
    # Base command
    cmd = ['python', '-m', 'pytest', 'tests/dependency_injection/']
    
    # Add specific file if provided
    if specific_file:
        cmd[-1] = f'tests/dependency_injection/{specific_file}'
    
    # Add verbose flag
    if verbose:
        cmd.append('-v')
    
    # Add coverage options
    if coverage:
        cmd.extend([
            '--cov=src.factories',
            '--cov=src.containers', 
            '--cov-report=html',
            '--cov-report=term'
        ])
    
    # Add marker filtering
    if markers:
        cmd.extend(['-m', markers])
    elif test_type:
        cmd.extend(['-m', test_type])
    
    # Add asyncio mode
    cmd.append('--asyncio-mode=auto')
    
    print(f"Running command: {' '.join(cmd)}")
    print("=" * 60)
    
    # Run the tests
    try:
        result = subprocess.run(cmd, cwd=os.getcwd(), check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\nTest execution interrupted by user")
        return 1
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def main():
    """Main entry point for test runner."""
    parser = argparse.ArgumentParser(description='Run dependency injection tests')
    
    parser.add_argument(
        '--type', 
        choices=['unit', 'integration', 'performance', 'slow'],
        help='Type of tests to run'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )
    
    parser.add_argument(
        '--coverage', '-c',
        action='store_true',
        help='Enable coverage reporting'
    )
    
    parser.add_argument(
        '--markers', '-m',
        help='Pytest markers to filter tests (e.g., "not slow")'
    )
    
    parser.add_argument(
        '--file', '-f',
        help='Run specific test file (e.g., test_main_service_factory_container.py)'
    )
    
    parser.add_argument(
        '--list-tests',
        action='store_true',
        help='List available test files'
    )
    
    args = parser.parse_args()
    
    if args.list_tests:
        print("Available test files:")
        test_dir = os.path.join(os.getcwd(), 'tests', 'dependency_injection')
        if os.path.exists(test_dir):
            for file in sorted(os.listdir(test_dir)):
                if file.startswith('test_') and file.endswith('.py'):
                    print(f"  - {file}")
        return 0
    
    return run_tests(
        test_type=args.type,
        verbose=args.verbose,
        coverage=args.coverage,
        markers=args.markers,
        specific_file=args.file
    )


if __name__ == '__main__':
    sys.exit(main())