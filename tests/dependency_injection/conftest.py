"""
Pytest configuration and fixtures for dependency injection tests.

This module provides common fixtures and configuration for all
dependency injection tests.
"""
import pytest
import asyncio
import os
from unittest.mock import Mock, AsyncMock, patch


@pytest.fixture(scope="session")
def event_loop():
    """Create an event loop for the test session."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_logger():
    """Create a mock logger with common logging methods."""
    logger = Mock()
    logger.info = Mock()
    logger.warning = Mock()
    logger.error = Mock()
    logger.debug = Mock()
    logger.critical = Mock()
    return logger


@pytest.fixture
def mock_shutdown_event():
    """Create a mock shutdown event."""
    return AsyncMock()


@pytest.fixture
def basic_config():
    """Create basic configuration dictionary for testing."""
    return {
        'headless': True,
        'run_parallel': True,
        'timeout_ms': 60000,
        'aws_region': 'us-west-2',
        'aws_access_key': 'test_access_key',
        'aws_secret_key': 'test_secret_key',
        'dynamodb_endpoint': 'http://localhost:8000',
        's3_bucket_name': 'test-bucket'
    }


@pytest.fixture
def environment_variables():
    """Create environment variables for testing."""
    return {
        'AWS_REGION': 'us-west-2',
        'AWS_ACCESS_KEY_ID': 'test_access_key',
        'AWS_SECRET_ACCESS_KEY': 'test_secret_key',
        'S3_BUCKET_NAME': 'test-bucket',
        'DYNAMODB_ENDPOINT': 'http://localhost:8000',
        'PACER_USERNAME_PROD': 'test_user',
        'PACER_PASSWORD_PROD': 'test_pass',
        'LEXGENIUS_PROJECT_ROOT': '/test/project/root'
    }


@pytest.fixture
def mock_workflow_config():
    """Create a mock WorkflowConfig for testing."""
    config = Mock()
    config.headless = True
    config.run_parallel = True
    config.timeout_ms = 60000
    config.config_name = "test_config"
    config.model_dump = Mock(return_value={
        "headless": True,
        "run_parallel": True,
        "timeout_ms": 60000,
        "pacer": {},
        "storage": {},
        "fb_ads": {},
        "transformer": {},
        "reports": {}
    })
    return config


@pytest.fixture
def mock_storage_services():
    """Create mock storage services for testing."""
    return {
        'async_dynamodb_storage': Mock(),
        'pacer_repository': Mock(),
        's3_async_storage': Mock(),
        'district_courts_repository': Mock(),
        'law_firms_repository': Mock(),
        'fb_archive_repository': Mock(),
        'fb_image_hash_repository': Mock(),
        'pacer_dockets_repository': Mock()
    }


@pytest.fixture
def mock_pacer_services():
    """Create mock PACER services for testing."""
    return {
        'court_processor': Mock(),
        'docket_processor': Mock(),
        'row_processor': Mock(),
        'download_manager': Mock(),
        'file_operations_service': Mock(),
        'navigation_facade': Mock(),
        'report_facade': Mock(),
        'browser_service': Mock(),
        'configuration_service': Mock(),
        'case_processing_service': Mock(),
        'relevance_service': Mock(),
        'classification_service': Mock(),
        'verification_service': Mock()
    }


@pytest.fixture
def clean_environment():
    """Provide a clean environment context manager."""
    original_env = os.environ.copy()
    
    def _clean_env(env_vars=None):
        return patch.dict(os.environ, env_vars or {}, clear=True)
    
    yield _clean_env
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


@pytest.fixture
def isolated_container():
    """Provide isolated container testing utilities."""
    created_containers = []
    
    def create_container(container_class, **kwargs):
        """Create a container and track it for cleanup."""
        container = container_class()
        for key, value in kwargs.items():
            if hasattr(container, key):
                getattr(container, key).override(value)
        created_containers.append(container)
        return container
    
    yield create_container
    
    # Cleanup containers
    for container in created_containers:
        try:
            if hasattr(container, 'unwire'):
                container.unwire()
        except:
            pass


@pytest.fixture
def performance_timer():
    """Provide performance timing utilities for tests."""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.perf_counter()
        
        def stop(self):
            self.end_time = time.perf_counter()
        
        @property
        def elapsed(self):
            if self.start_time is None or self.end_time is None:
                return None
            return self.end_time - self.start_time
        
        def __enter__(self):
            self.start()
            return self
        
        def __exit__(self, *args):
            self.stop()
    
    return Timer


@pytest.fixture
def memory_monitor():
    """Provide memory monitoring utilities for tests."""
    import psutil
    import os
    
    class MemoryMonitor:
        def __init__(self):
            self.process = psutil.Process(os.getpid())
            self.initial_memory = None
            self.peak_memory = None
        
        def start(self):
            self.initial_memory = self.process.memory_info().rss
            self.peak_memory = self.initial_memory
        
        def update(self):
            current_memory = self.process.memory_info().rss
            if current_memory > self.peak_memory:
                self.peak_memory = current_memory
        
        @property
        def memory_increase(self):
            if self.initial_memory is None:
                return None
            current_memory = self.process.memory_info().rss
            return current_memory - self.initial_memory
        
        @property
        def peak_increase(self):
            if self.initial_memory is None:
                return None
            return self.peak_memory - self.initial_memory
    
    return MemoryMonitor


@pytest.fixture
def dependency_mocker():
    """Provide utilities for mocking dependencies in tests."""
    active_patches = []
    
    def mock_dependency(module_path, **mocks):
        """Mock multiple dependencies in a module."""
        patch_obj = patch.multiple(module_path, **mocks)
        active_patches.append(patch_obj)
        return patch_obj.start()
    
    def mock_class(class_path, return_value=None, side_effect=None):
        """Mock a single class."""
        if return_value is None:
            return_value = Mock()
        
        patch_obj = patch(class_path, return_value=return_value, side_effect=side_effect)
        active_patches.append(patch_obj)
        return patch_obj.start()
    
    yield {
        'mock_dependency': mock_dependency,
        'mock_class': mock_class
    }
    
    # Cleanup patches
    for patch_obj in active_patches:
        try:
            patch_obj.stop()
        except:
            pass


@pytest.fixture
def container_test_utils():
    """Provide utilities for testing containers."""
    
    def validate_provider_exists(container, provider_name):
        """Validate that a provider exists in the container."""
        assert hasattr(container, provider_name), f"Missing provider: {provider_name}"
    
    def validate_provider_type(container, provider_name, expected_type):
        """Validate that a provider has the expected type."""
        validate_provider_exists(container, provider_name)
        provider = getattr(container, provider_name)
        assert isinstance(provider, expected_type), f"Provider {provider_name} has wrong type"
    
    def validate_container_structure(container, expected_providers):
        """Validate that container has all expected providers."""
        for provider_name, expected_type in expected_providers.items():
            validate_provider_type(container, provider_name, expected_type)
    
    return {
        'validate_provider_exists': validate_provider_exists,
        'validate_provider_type': validate_provider_type,
        'validate_container_structure': validate_container_structure
    }


# Configure pytest markers
def pytest_configure(config):
    """Configure custom pytest markers."""
    config.addinivalue_line("markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')")
    config.addinivalue_line("markers", "integration: marks tests as integration tests")
    config.addinivalue_line("markers", "unit: marks tests as unit tests")
    config.addinivalue_line("markers", "performance: marks tests as performance tests")
    config.addinivalue_line("markers", "memory: marks tests that monitor memory usage")