"""
Comprehensive tests for PacerCoreContainer integration validation.

This test suite validates that the PacerCoreContainer properly integrates
with other containers and provides correct PACER service dependencies.
"""
import pytest
from unittest.mock import Mock, patch, AsyncMock
from dependency_injector import containers, providers
from src.containers.pacer_core import Pacer<PERSON>oreContainer
from src.containers.storage import StorageContainer


class TestPacerCoreContainerIntegration:
    """Test PacerCoreContainer integration and dependency validation."""

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger for testing."""
        return Mock()

    @pytest.fixture
    def mock_shutdown_event(self):
        """Create a mock shutdown event for testing."""
        return AsyncMock()

    @pytest.fixture
    def pacer_config(self):
        """Create PACER configuration for testing."""
        return {
            'headless': True,
            'run_parallel': True,
            'timeout_ms': 60000,
            'username_prod': 'test_user',
            'password_prod': 'test_pass'
        }

    @pytest.fixture
    def mock_storage_container(self, mock_logger):
        """Create a mock storage container for testing."""
        storage_container = Mock()
        
        # Mock storage services
        storage_container.async_dynamodb_storage = Mock(return_value=Mock())
        storage_container.s3_async_storage = Mock(return_value=Mock())
        storage_container.pacer_repository = Mock(return_value=Mock())
        storage_container.district_courts_repository = Mock(return_value=Mock())
        
        return storage_container

    @pytest.fixture
    def pacer_container(self, mock_logger, mock_shutdown_event, pacer_config, mock_storage_container):
        """Create and configure a PacerCoreContainer for testing."""
        container = PacerCoreContainer()
        
        # Configure container dependencies
        container.config.from_dict(pacer_config)
        container.logger.override(mock_logger)
        container.shutdown_event.override(mock_shutdown_event)
        container.storage_container.override(mock_storage_container)
        
        return container

    def test_pacer_container_initialization(self, pacer_container):
        """Test that PacerCoreContainer initializes properly."""
        assert pacer_container is not None
        assert isinstance(pacer_container, containers.DeclarativeContainer)

    def test_authentication_component_providers(self, pacer_container):
        """Test authentication component providers."""
        auth_components = [
            'credential_validator',
            'ecf_login_handler', 
            'login_handler',
            'session_manager'
        ]
        
        for component in auth_components:
            assert hasattr(pacer_container, component), f"Missing auth component: {component}"
            provider = getattr(pacer_container, component)
            assert isinstance(provider, providers.Singleton), f"Auth component {component} should be Singleton"

    def test_browser_component_providers(self, pacer_container):
        """Test browser component providers."""
        browser_components = [
            'playwright_manager',
            'context_factory'
        ]
        
        for component in browser_components:
            assert hasattr(pacer_container, component), f"Missing browser component: {component}"

    def test_case_processing_component_providers(self, pacer_container):
        """Test case processing component providers."""
        case_processing_components = [
            'case_enricher',
            'case_parser',
            'case_transformer', 
            'case_validator'
        ]
        
        for component in case_processing_components:
            assert hasattr(pacer_container, component), f"Missing case processing component: {component}"
            provider = getattr(pacer_container, component)
            assert isinstance(provider, providers.Singleton), f"Case processing component {component} should be Singleton"

    def test_download_component_providers(self, pacer_container):
        """Test download component providers."""
        download_components = [
            'download_manager',
            'download_validator',
            'file_downloader'
        ]
        
        for component in download_components:
            assert hasattr(pacer_container, component), f"Missing download component: {component}"
            provider = getattr(pacer_container, component)
            assert isinstance(provider, providers.Singleton), f"Download component {component} should be Singleton"

    def test_core_service_providers(self, pacer_container):
        """Test core service providers."""
        core_services = [
            'configuration_service',
            'browser_service',
            'case_processing_service',
            'relevance_service',
            'verification_service',
            'download_orchestration_service',
            'file_operations_service'
        ]
        
        for service in core_services:
            assert hasattr(pacer_container, service), f"Missing core service: {service}"
            provider = getattr(pacer_container, service)
            assert isinstance(provider, providers.Factory), f"Core service {service} should be Factory"

    def test_processor_component_providers(self, pacer_container):
        """Test phase-separated processor component providers."""
        processor_components = [
            'court_processor',
            'docket_processor',
            'row_processor'
        ]
        
        for component in processor_components:
            assert hasattr(pacer_container, component), f"Missing processor component: {component}"
            provider = getattr(pacer_container, component)
            assert isinstance(provider, providers.Factory), f"Processor component {component} should be Factory"

    def test_facade_service_providers(self, pacer_container):
        """Test facade service providers."""
        facade_services = [
            'court_processing_facade',
            'row_processing_facade',
            'navigation_facade',
            'docket_orchestrator_facade'
        ]
        
        for facade in facade_services:
            assert hasattr(pacer_container, facade), f"Missing facade service: {facade}"
            provider = getattr(pacer_container, facade)
            assert isinstance(provider, providers.Factory), f"Facade service {facade} should be Factory"

    @patch('src.pacer.services.configuration_service.ConfigurationService')
    def test_configuration_service_instantiation(self, mock_service_class, pacer_container):
        """Test ConfigurationService instantiation with correct dependencies."""
        mock_instance = Mock()
        mock_service_class.return_value = mock_instance
        
        # Get service instance
        service = pacer_container.configuration_service()
        
        # Verify service was created with correct parameters
        mock_service_class.assert_called_once()
        call_args = mock_service_class.call_args
        
        assert 'logger' in call_args.kwargs
        assert 'config' in call_args.kwargs

    @patch('src.pacer.services.browser_service.BrowserService') 
    def test_browser_service_instantiation(self, mock_service_class, pacer_container):
        """Test BrowserService instantiation with correct dependencies."""
        mock_instance = Mock()
        mock_service_class.return_value = mock_instance
        
        # Get service instance
        service = pacer_container.browser_service()
        
        # Verify service was created with correct parameters
        mock_service_class.assert_called_once()
        call_args = mock_service_class.call_args
        
        assert 'logger' in call_args.kwargs
        assert 'config' in call_args.kwargs

    @patch('src.pacer.components.processing.court_processor.CourtProcessor')
    def test_court_processor_instantiation(self, mock_processor_class, pacer_container):
        """Test CourtProcessor instantiation with correct dependencies."""
        mock_instance = Mock()
        mock_processor_class.return_value = mock_instance
        
        # Mock the dependent services
        with patch.object(pacer_container, 'pacer_browser_service') as mock_browser:
            with patch.object(pacer_container, 'pacer_configuration_service') as mock_config:
                mock_browser.return_value = Mock()
                mock_config.return_value = Mock()
                
                # Get processor instance
                processor = pacer_container.court_processor()
                
                # Verify processor was created with correct dependencies
                mock_processor_class.assert_called_once()
                call_args = mock_processor_class.call_args
                
                assert 'browser_service' in call_args.kwargs
                assert 'configuration_service' in call_args.kwargs
                assert 'logger' in call_args.kwargs
                assert 'config' in call_args.kwargs

    @patch('src.pacer.components.processing.docket_processor.DocketProcessor')
    def test_docket_processor_instantiation(self, mock_processor_class, pacer_container):
        """Test DocketProcessor instantiation with correct dependencies."""
        mock_instance = Mock()
        mock_processor_class.return_value = mock_instance
        
        # Mock the dependent services
        with patch.object(pacer_container, 'pacer_case_processing_service') as mock_case:
            with patch.object(pacer_container, 'pacer_relevance_service') as mock_relevance:
                mock_case.return_value = Mock()
                mock_relevance.return_value = Mock()
                
                # Get processor instance
                processor = pacer_container.docket_processor()
                
                # Verify processor was created with correct dependencies
                mock_processor_class.assert_called_once()
                call_args = mock_processor_class.call_args
                
                assert 'case_processing_service' in call_args.kwargs
                assert 'relevance_service' in call_args.kwargs
                assert 'logger' in call_args.kwargs
                assert 'config' in call_args.kwargs

    @patch('src.pacer.components.processing.row_processor.RowProcessor')
    def test_row_processor_instantiation(self, mock_processor_class, pacer_container):
        """Test RowProcessor instantiation with correct dependencies."""
        mock_instance = Mock()
        mock_processor_class.return_value = mock_instance
        
        # Mock the dependent services
        with patch.object(pacer_container, 'pacer_relevance_service') as mock_relevance:
            with patch.object(pacer_container, 'pacer_classification_service') as mock_classification:
                with patch.object(pacer_container, 'pacer_verification_service') as mock_verification:
                    mock_relevance.return_value = Mock()
                    mock_classification.return_value = Mock()
                    mock_verification.return_value = Mock()
                    
                    # Get processor instance
                    processor = pacer_container.row_processor()
                    
                    # Verify processor was created with correct dependencies
                    mock_processor_class.assert_called_once()
                    call_args = mock_processor_class.call_args
                    
                    assert 'relevance_service' in call_args.kwargs
                    assert 'classification_service' in call_args.kwargs
                    assert 'verification_service' in call_args.kwargs
                    assert 'logger' in call_args.kwargs
                    assert 'config' in call_args.kwargs

    @patch('src.pacer.facades.docket_orchestrator.DocketOrchestrator')
    def test_docket_orchestrator_facade_instantiation(self, mock_orchestrator_class, pacer_container):
        """Test DocketOrchestrator facade instantiation with all dependencies."""
        mock_instance = Mock()
        mock_orchestrator_class.return_value = mock_instance
        
        # Mock all the required dependencies
        with patch.object(pacer_container, 'court_processor') as mock_court:
            with patch.object(pacer_container, 'docket_processor') as mock_docket:
                with patch.object(pacer_container, 'row_processor') as mock_row:
                    with patch.object(pacer_container, 'download_manager') as mock_download:
                        with patch.object(pacer_container, 'pacer_file_operations_service') as mock_file:
                            with patch.object(pacer_container, 'navigation_facade') as mock_nav:
                                # Set up mock returns
                                mock_court.return_value = Mock()
                                mock_docket.return_value = Mock()
                                mock_row.return_value = Mock()
                                mock_download.return_value = Mock()
                                mock_file.return_value = Mock()
                                mock_nav.return_value = Mock()
                                
                                # Get orchestrator instance
                                orchestrator = pacer_container.docket_orchestrator_facade()
                                
                                # Verify orchestrator was created with all dependencies
                                mock_orchestrator_class.assert_called_once()
                                call_args = mock_orchestrator_class.call_args
                                
                                assert 'court_processor' in call_args.kwargs
                                assert 'docket_processor' in call_args.kwargs
                                assert 'row_processor' in call_args.kwargs
                                assert 'download_manager' in call_args.kwargs
                                assert 'file_operations_service' in call_args.kwargs
                                assert 'navigation_facade' in call_args.kwargs
                                assert 'logger' in call_args.kwargs
                                assert 'config' in call_args.kwargs

    def test_storage_container_integration(self, pacer_container, mock_storage_container):
        """Test integration with storage container dependencies."""
        # Verify storage container is properly injected
        assert pacer_container.storage_container() == mock_storage_container
        
        # Test that storage dependencies are accessible
        with patch('src.pacer.components.download.download_manager.DownloadManager') as mock_dl:
            mock_dl.return_value = Mock()
            
            # Get download manager (should use storage container)
            download_manager = pacer_container.download_manager()
            
            # Verify it was created (storage integration working)
            mock_dl.assert_called_once()

    def test_workflow_orchestrator_integration(self, pacer_container):
        """Test WorkflowOrchestrator integration with DocketOrchestrator."""
        assert hasattr(pacer_container, 'workflow_orchestrator')
        
        # Mock the WorkflowOrchestrator class and its dependencies
        with patch('src.pacer.components.processing.workflow_orchestrator_di.WorkflowOrchestrator') as mock_workflow:
            with patch.object(pacer_container, 'docket_orchestrator_facade') as mock_docket_orch:
                with patch.object(pacer_container, 'row_processing_facade') as mock_row_facade:
                    with patch.object(pacer_container, 'file_operations_service') as mock_file_ops:
                        with patch.object(pacer_container, 'relevance_service') as mock_relevance:
                            # Set up mocks
                            mock_workflow.return_value = Mock()
                            mock_docket_orch.return_value = Mock()
                            mock_row_facade.return_value = Mock()
                            mock_file_ops.return_value = Mock()
                            mock_relevance.return_value = Mock()
                            
                            # Get workflow orchestrator
                            workflow = pacer_container.workflow_orchestrator()
                            
                            # Verify it was created with proper dependencies
                            mock_workflow.assert_called_once()
                            call_args = mock_workflow.call_args
                            
                            assert 'docket_orchestrator' in call_args.kwargs
                            assert 'row_facade' in call_args.kwargs
                            assert 'file_service' in call_args.kwargs
                            assert 'relevance_service' in call_args.kwargs

    def test_pacer_orchestrator_service_integration(self, pacer_container):
        """Test PacerOrchestratorService integration."""
        assert hasattr(pacer_container, 'pacer_orchestrator')
        
        # Mock the PacerOrchestratorService class and its dependencies
        with patch('src.services.pacer.pacer_orchestrator_service.PacerOrchestratorService') as mock_pacer_orch:
            with patch.object(pacer_container, 'workflow_orchestrator') as mock_workflow:
                # Set up mocks
                mock_pacer_orch.return_value = Mock()
                mock_workflow.return_value = Mock()
                
                # Get PACER orchestrator
                orchestrator = pacer_container.pacer_orchestrator()
                
                # Verify it was created with workflow orchestrator dependency
                mock_pacer_orch.assert_called_once()
                call_args = mock_pacer_orch.call_args
                
                assert 'workflow_orchestrator' in call_args.kwargs
                assert 'logger' in call_args.kwargs

    def test_singleton_vs_factory_behavior(self, pacer_container):
        """Test that singletons and factories behave correctly."""
        # Test singleton behavior (components)
        with patch('src.pacer.components.authentication.credential_validator.CredentialValidator') as mock_validator:
            mock_instance = Mock()
            mock_validator.return_value = mock_instance
            
            validator1 = pacer_container.credential_validator()
            validator2 = pacer_container.credential_validator()
            
            # Should be the same instance (singleton)
            assert validator1 is validator2
            assert mock_validator.call_count == 1
        
        # Test factory behavior (services)
        with patch('src.pacer.services.configuration_service.ConfigurationService') as mock_service:
            mock_service.return_value = Mock()
            
            service1 = pacer_container.configuration_service()
            service2 = pacer_container.configuration_service()
            
            # Should be different instances (factory)
            assert mock_service.call_count == 2

    def test_dependency_chain_resolution(self, pacer_container):
        """Test that complex dependency chains resolve correctly."""
        # Test a complex dependency chain: DocketOrchestrator -> processors -> services -> components
        
        with patch('src.pacer.facades.docket_orchestrator.DocketOrchestrator') as mock_orchestrator:
            with patch('src.pacer.components.processing.court_processor.CourtProcessor') as mock_court:
                with patch('src.pacer.services.browser_service.BrowserService') as mock_browser:
                    with patch('src.pacer.components.browser.playwright_manager.PlaywrightManager') as mock_playwright:
                        # Set up the chain
                        mock_playwright.return_value = Mock()
                        mock_browser.return_value = Mock()
                        mock_court.return_value = Mock() 
                        mock_orchestrator.return_value = Mock()
                        
                        # Get the top-level orchestrator
                        orchestrator = pacer_container.docket_orchestrator_facade()
                        
                        # Verify the entire chain was resolved
                        assert mock_orchestrator.called
                        # The exact call pattern depends on the implementation details

    def test_error_handling_missing_storage_container(self, mock_logger, mock_shutdown_event, pacer_config):
        """Test error handling when storage container is missing."""
        container = PacerCoreContainer()
        container.config.from_dict(pacer_config)
        container.logger.override(mock_logger)
        container.shutdown_event.override(mock_shutdown_event)
        # Don't set storage_container override
        
        # Attempting to use storage-dependent services should handle the missing dependency
        # The exact behavior depends on the implementation

    def test_configuration_override_propagation(self, pacer_container):
        """Test that configuration overrides propagate to all components."""
        # Verify that when config is overridden, it's available to all components
        
        with patch('src.pacer.services.configuration_service.ConfigurationService') as mock_service:
            mock_service.return_value = Mock()
            
            service = pacer_container.configuration_service()
            
            # Verify config was passed to the service
            call_args = mock_service.call_args
            assert 'config' in call_args.kwargs
            
            # The config should contain the test values
            config = call_args.kwargs['config']
            # Exact validation depends on how config is structured

    def test_metrics_and_s3_services_integration(self, pacer_container):
        """Test metrics reporting and S3 management services integration."""
        # Test that placeholder/real services are properly configured
        assert hasattr(pacer_container, 'metrics_reporting_service')
        assert hasattr(pacer_container, 's3_management_service')
        
        # Test instantiation
        with patch('src.pacer.services.s3_service.S3Service') as mock_s3:
            mock_s3.return_value = Mock()
            
            s3_service = pacer_container.s3_management_service()
            
            mock_s3.assert_called_once()
            call_args = mock_s3.call_args
            assert 'logger' in call_args.kwargs
            assert 'config' in call_args.kwargs