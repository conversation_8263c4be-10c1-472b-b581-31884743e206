# Dependency Injection Test Suite

This directory contains comprehensive tests for the dependency injection system used throughout the LexGenius project.

## Test Structure

### Core Test Files

- **`test_main_service_factory_container.py`** - Tests for MainServiceFactory container creation and validation
- **`test_storage_container_resolution.py`** - Tests for StorageContainer dependency resolution
- **`test_pacer_core_container_integration.py`** - Tests for PacerCoreContainer integration validation
- **`test_docket_orchestrator_dependencies.py`** - Tests for DocketOrchestrator instantiation with proper dependencies
- **`test_workflow_manager_dependencies.py`** - Tests for SequentialWorkflowManager dependency injection validation
- **`test_error_handling_scenarios.py`** - Tests for error handling in missing dependencies scenarios
- **`test_complete_workflow_startup_integration.py`** - Integration tests for complete workflow startup
- **`test_container_wiring_validation.py`** - Test utilities for container wiring validation
- **`test_edge_cases_and_scenarios.py`** - Edge case and error scenario validation tests

### Support Files

- **`conftest.py`** - Common fixtures and configuration for all DI tests
- **`__init__.py`** - Package initialization
- **`README.md`** - This documentation file

## Test Categories

### 1. Container Creation Tests
Tests that validate containers can be created with proper configuration:
- MainServiceFactory initialization
- Container configuration preparation
- Environment variable handling
- Configuration validation

### 2. Dependency Resolution Tests
Tests that validate dependencies are properly resolved:
- Storage service resolution
- Repository dependency injection
- Cross-container dependencies
- Provider instantiation

### 3. Integration Tests
Tests that validate end-to-end dependency injection:
- Complete workflow startup
- Service orchestrator creation
- Multi-container integration
- Real-world usage scenarios

### 4. Error Handling Tests
Tests that validate proper error handling:
- Missing dependencies
- Invalid configuration
- Container creation failures
- Resource initialization errors

### 5. Edge Case Tests
Tests that cover unusual scenarios:
- Large configurations
- Unicode/special characters
- Concurrent access
- Memory pressure
- Performance with many providers

### 6. Wiring Validation Tests
Tests that validate container wiring:
- Provider types
- Dependency chains
- Module wiring
- Override behavior

## Running the Tests

### Run All DI Tests
```bash
pytest tests/dependency_injection/ -v
```

### Run Specific Test Categories
```bash
# Unit tests only
pytest tests/dependency_injection/ -m unit -v

# Integration tests only
pytest tests/dependency_injection/ -m integration -v

# Performance tests only
pytest tests/dependency_injection/ -m performance -v

# Exclude slow tests
pytest tests/dependency_injection/ -m "not slow" -v
```

### Run Specific Test Files
```bash
# Test MainServiceFactory
pytest tests/dependency_injection/test_main_service_factory_container.py -v

# Test storage container
pytest tests/dependency_injection/test_storage_container_resolution.py -v

# Test error handling
pytest tests/dependency_injection/test_error_handling_scenarios.py -v
```

### Run with Coverage
```bash
pytest tests/dependency_injection/ --cov=src.factories --cov=src.containers --cov-report=html -v
```

## Test Requirements

### Dependencies
The tests require the following packages:
- `pytest`
- `pytest-asyncio`
- `pytest-cov` (for coverage reporting)
- `psutil` (for memory monitoring tests)

### Environment Setup
Some tests require environment variables:
```bash
export AWS_REGION=us-west-2
export AWS_ACCESS_KEY_ID=test_key
export AWS_SECRET_ACCESS_KEY=test_secret
export S3_BUCKET_NAME=test-bucket
export LEXGENIUS_PROJECT_ROOT=/path/to/project
```

## Test Fixtures

### Common Fixtures (from conftest.py)
- `mock_logger` - Mock logger with standard methods
- `mock_shutdown_event` - Mock asyncio event
- `basic_config` - Basic configuration dictionary
- `environment_variables` - Test environment variables
- `mock_workflow_config` - Mock WorkflowConfig object
- `mock_storage_services` - Mock storage service collection
- `mock_pacer_services` - Mock PACER service collection

### Utility Fixtures
- `clean_environment` - Clean environment context manager
- `isolated_container` - Container isolation utilities
- `performance_timer` - Performance timing utilities
- `memory_monitor` - Memory usage monitoring
- `dependency_mocker` - Dependency mocking utilities
- `container_test_utils` - Container validation utilities

## Test Patterns

### Container Testing Pattern
```python
def test_container_creation(self, mock_logger, basic_config):
    container = StorageContainer()
    
    # Configure container
    container.config.from_dict(basic_config)
    container.logger.override(mock_logger)
    
    # Test provider access
    provider = container.async_dynamodb_storage
    assert isinstance(provider, providers.Singleton)
```

### Dependency Injection Testing Pattern
```python
@pytest.mark.asyncio
async def test_service_creation(self, mock_config, shutdown_event):
    factory = MainServiceFactory(mock_config, shutdown_event)
    
    with patch('src.factories.main_factory.create_container') as mock_create:
        mock_container = Mock()
        mock_create.return_value = mock_container
        
        async with factory:
            service = await factory.create_pacer_orchestrator_service()
            assert service is not None
```

### Error Handling Testing Pattern
```python
def test_error_scenario(self, mock_config):
    factory = MainServiceFactory(mock_config, None)
    
    with patch('dependency.path', side_effect=Exception("Test error")):
        with pytest.raises(RuntimeError, match="Expected error message"):
            # Test operation that should fail
            pass
```

## Validation Criteria

### Container Tests Must Validate
1. **Provider Existence** - All expected providers are present
2. **Provider Types** - Providers have correct types (Singleton, Factory, etc.)
3. **Dependency Resolution** - Dependencies can be resolved without errors
4. **Configuration Propagation** - Configuration reaches all components
5. **Cleanup Behavior** - Resources are properly cleaned up

### Integration Tests Must Validate
1. **End-to-End Workflows** - Complete workflows can start successfully
2. **Cross-Container Dependencies** - Dependencies work across containers
3. **Service Creation** - All services can be created through factories
4. **Resource Management** - Resources are properly initialized and cleaned up
5. **Error Recovery** - System can recover from partial failures

### Error Handling Tests Must Validate
1. **Graceful Degradation** - System handles missing dependencies gracefully
2. **Error Propagation** - Errors are properly propagated and reported
3. **Cleanup on Failure** - Resources are cleaned up even when errors occur
4. **Recovery Mechanisms** - System can recover from transient failures

## Performance Expectations

### Container Creation
- Container creation should complete in < 100ms
- Memory usage should be < 50MB for basic containers
- No memory leaks during repeated creation/destruction

### Dependency Resolution
- Service instantiation should complete in < 10ms
- Singleton behavior should be maintained across calls
- No circular dependency issues

### Integration Tests
- Complete workflow startup should complete in < 5 seconds
- Memory usage should stabilize after initialization
- No resource leaks during normal operation

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure all dependencies are installed
   - Check Python path includes project root
   - Verify module structure is correct

2. **Container Creation Failures**
   - Check environment variables are set
   - Verify configuration is properly formatted
   - Ensure all required dependencies are available

3. **Test Timeouts**
   - Increase pytest timeout for integration tests
   - Check for deadlocks in async code
   - Verify mock behaviors are correct

4. **Memory Issues**
   - Run tests individually if experiencing memory pressure
   - Check for circular references in test fixtures
   - Ensure proper cleanup in teardown methods

### Debug Techniques

1. **Enable Verbose Logging**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **Use pytest Debugging**
   ```bash
   pytest tests/dependency_injection/test_file.py::test_function -v -s --pdb
   ```

3. **Memory Profiling**
   ```bash
   pytest tests/dependency_injection/ --profile
   ```

## Contributing

### Adding New Tests
1. Follow the existing test patterns
2. Use appropriate fixtures from conftest.py
3. Add proper documentation and comments
4. Include both positive and negative test cases
5. Add performance benchmarks for critical paths

### Test Naming Convention
- Test files: `test_<component>_<aspect>.py`
- Test classes: `Test<Component><Aspect>`
- Test methods: `test_<specific_behavior>`

### Documentation Requirements
- Document test purpose and scope
- Explain complex test scenarios
- Include examples of expected behavior
- Document any special setup requirements