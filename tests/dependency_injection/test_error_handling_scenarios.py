"""
Comprehensive tests for error handling in dependency injection scenarios.

This test suite validates error handling for missing dependencies, 
invalid configurations, and other edge cases in the DI system.
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from dependency_injector.errors import Error as DIError
from src.factories.main_factory import MainServiceFactory
from src.containers.storage import StorageContainer
from src.containers.pacer_core import PacerCoreContainer


class TestDependencyInjectionErrorHandling:
    """Test error handling scenarios in dependency injection."""

    @pytest.fixture
    def mock_config(self):
        """Create a mock config for error testing."""
        config = Mock()
        config.headless = True
        config.run_parallel = True
        config.timeout_ms = 60000
        config.model_dump = Mock(return_value={
            "headless": True,
            "run_parallel": True,
            "timeout_ms": 60000
        })
        return config

    @pytest.fixture
    def shutdown_event(self):
        """Create a shutdown event for testing."""
        return asyncio.Event()

    def test_main_factory_missing_environment_variables(self, mock_config, shutdown_event):
        """Test MainServiceFactory behavior with missing environment variables."""
        # Test with completely empty environment
        with patch.dict('os.environ', {}, clear=True):
            factory = MainServiceFactory(mock_config, shutdown_event)
            
            # Should handle missing environment variables gracefully
            config_dict = factory._prepare_config_dict()
            
            # Verify defaults are used for missing environment variables
            assert config_dict['aws_region'] == 'us-west-2'  # Default
            assert config_dict['s3_bucket_name'] == 'lexgenius-data'  # Default
            assert config_dict['aws_access_key'] == ''  # Empty when not set
            assert config_dict['aws_secret_key'] == ''  # Empty when not set

    @pytest.mark.asyncio
    async def test_container_creation_failure_handling(self, mock_config, shutdown_event):
        """Test handling of container creation failures."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Mock container creation to fail
        with patch('src.factories.main_factory.create_container', side_effect=Exception("Container creation failed")):
            with pytest.raises(RuntimeError, match="MainServiceFactory initialization failed"):
                async with factory:
                    pass

    @pytest.mark.asyncio
    async def test_container_wiring_failure_handling(self, mock_config, shutdown_event):
        """Test handling of container wiring failures."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Mock container that fails during wiring
        mock_container = Mock()
        mock_container.wire = Mock(side_effect=Exception("Wiring failed"))
        mock_container.unwire = Mock()
        mock_container.storage = Mock()
        mock_container.pacer = Mock()
        
        # Mock storage dependencies for validation
        mock_container.storage.async_dynamodb_storage = Mock(return_value=Mock())
        mock_container.storage.pacer_repository = Mock(return_value=Mock())
        mock_container.storage.s3_async_storage = Mock(return_value=Mock())
        
        with patch('src.factories.main_factory.create_container', return_value=mock_container):
            with pytest.raises(RuntimeError, match="MainServiceFactory initialization failed"):
                async with factory:
                    pass
            
            # Verify cleanup was attempted
            mock_container.unwire.assert_called_once()

    @pytest.mark.asyncio
    async def test_resource_initialization_failure_handling(self, mock_config, shutdown_event):
        """Test handling of resource initialization failures."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Mock container that fails during resource initialization
        mock_container = Mock()
        mock_container.wire = Mock()
        mock_container.init_resources = Mock(side_effect=Exception("Resource init failed"))
        mock_container.shutdown_resources = Mock(return_value=None)
        mock_container.unwire = Mock()
        mock_container.storage = Mock()
        mock_container.pacer = Mock()
        
        # Mock storage dependencies
        mock_container.storage.async_dynamodb_storage = Mock(return_value=Mock())
        mock_container.storage.pacer_repository = Mock(return_value=Mock())
        mock_container.storage.s3_async_storage = Mock(return_value=Mock())
        
        with patch('src.factories.main_factory.create_container', return_value=mock_container):
            with pytest.raises(RuntimeError, match="MainServiceFactory initialization failed"):
                async with factory:
                    pass

    def test_storage_container_missing_configuration_error(self):
        """Test StorageContainer behavior with missing configuration."""
        container = StorageContainer()
        
        # Don't set any configuration overrides
        # Attempting to create services should handle missing config appropriately
        
        # Test that required configuration dependencies are properly defined
        assert hasattr(container, 'aws_region')
        assert hasattr(container, 'aws_access_key')
        assert hasattr(container, 'aws_secret_key')
        assert hasattr(container, 'dynamodb_endpoint')
        assert hasattr(container, 's3_bucket_name')

    def test_storage_container_invalid_configuration_error(self):
        """Test StorageContainer behavior with invalid configuration."""
        container = StorageContainer()
        
        # Set invalid configuration values
        container.aws_region.override(None)
        container.aws_access_key.override(None)
        container.aws_secret_key.override(None)
        container.s3_bucket_name.override(None)
        
        # Services should handle None configuration appropriately
        # The exact behavior depends on the implementation

    def test_pacer_container_missing_storage_dependency_error(self):
        """Test PacerCoreContainer behavior with missing storage dependency."""
        container = PacerCoreContainer()
        
        # Configure basic dependencies but omit storage_container
        container.config.from_dict({'headless': True})
        container.logger.override(Mock())
        container.shutdown_event.override(AsyncMock())
        # Don't set storage_container override
        
        # Attempting to create storage-dependent services should handle missing dependency
        # The exact behavior depends on the implementation

    def test_pacer_container_invalid_logger_dependency_error(self):
        """Test PacerCoreContainer behavior with invalid logger dependency."""
        container = PacerCoreContainer()
        
        # Set invalid logger dependency
        container.config.from_dict({'headless': True})
        container.logger.override(None)  # Invalid logger
        container.shutdown_event.override(AsyncMock())
        container.storage_container.override(Mock())
        
        # Services should handle None logger appropriately
        # Some services might raise errors, others might create a default logger

    @pytest.mark.asyncio
    async def test_factory_cleanup_on_partial_initialization_failure(self, mock_config, shutdown_event):
        """Test cleanup when factory initialization partially fails."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Mock container that succeeds in creation but fails validation
        mock_container = Mock()
        mock_container.wire = Mock()
        mock_container.init_resources = Mock(return_value=None)
        mock_container.shutdown_resources = Mock(return_value=None)
        mock_container.unwire = Mock()
        mock_container.storage = Mock()
        # Don't set pacer attribute to fail validation
        
        # Mock storage dependencies
        mock_container.storage.async_dynamodb_storage = Mock(return_value=Mock())
        mock_container.storage.pacer_repository = Mock(return_value=Mock())
        mock_container.storage.s3_async_storage = Mock(return_value=Mock())
        
        with patch('src.factories.main_factory.create_container', return_value=mock_container):
            with pytest.raises(RuntimeError):
                async with factory:
                    pass
            
            # Verify cleanup was performed
            mock_container.shutdown_resources.assert_called_once()
            mock_container.unwire.assert_called_once()

    def test_dependency_injection_circular_dependency_detection(self):
        """Test detection and handling of circular dependencies."""
        # This is a conceptual test for circular dependency detection
        # The actual implementation would depend on the DI framework capabilities
        
        # Example: ServiceA depends on ServiceB which depends on ServiceA
        # The DI container should detect and handle this appropriately
        
        from dependency_injector import containers, providers
        
        class CircularTestContainer(containers.DeclarativeContainer):
            # This would create a circular dependency if implemented
            pass
        
        # The test would verify that circular dependencies are detected

    def test_missing_required_dependency_provider_error(self):
        """Test error handling when required dependency providers are missing."""
        container = StorageContainer()
        
        # Verify that attempting to access a non-existent provider raises appropriate error
        with pytest.raises(AttributeError):
            _ = container.non_existent_provider

    def test_provider_instantiation_error_handling(self):
        """Test error handling during provider instantiation."""
        container = StorageContainer()
        
        # Mock logger and configuration
        container.logger.override(Mock())
        container.aws_region.override('us-west-2')
        container.aws_access_key.override('test_key')
        container.aws_secret_key.override('test_secret')
        container.dynamodb_endpoint.override('http://localhost:8000')
        container.s3_bucket_name.override('test-bucket')
        
        # Mock a service class that raises an error during instantiation
        with patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage', side_effect=Exception("Service instantiation failed")):
            with pytest.raises(Exception, match="Service instantiation failed"):
                container.async_dynamodb_storage()

    @pytest.mark.asyncio
    async def test_async_service_initialization_error_handling(self):
        """Test error handling for async service initialization errors."""
        # Mock an async service that fails during initialization
        mock_service = Mock()
        mock_service.initialize = AsyncMock(side_effect=Exception("Async init failed"))
        
        # Test that initialization errors are properly propagated
        with pytest.raises(Exception, match="Async init failed"):
            await mock_service.initialize()

    def test_configuration_validation_error_handling(self, mock_config, shutdown_event):
        """Test configuration validation error handling."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Test invalid configuration dict
        with pytest.raises(ValueError, match="Config dict must be a dictionary"):
            factory._validate_config_dict("invalid_config")
        
        # Test missing project root
        invalid_config = {"pacer": {}, "storage": {}}
        with pytest.raises(ValueError, match="Project root not configured"):
            factory._validate_config_dict(invalid_config)

    def test_container_state_validation_error_handling(self, mock_config, shutdown_event):
        """Test container state validation error handling."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Test validation with uninitialized container
        with pytest.raises(RuntimeError, match="Container is not initialized"):
            factory._validate_container_state()

    def test_storage_dependency_validation_error_handling(self, mock_config, shutdown_event):
        """Test storage dependency validation error handling."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Test validation with uninitialized container
        with pytest.raises(RuntimeError, match="Container must be initialized"):
            factory._validate_storage_dependencies()
        
        # Test validation with container missing storage
        factory._container = Mock()
        # Don't set storage attribute
        with pytest.raises(RuntimeError, match="Storage container not found"):
            factory._validate_storage_dependencies()
        
        # Test validation with missing storage dependencies
        factory._container.storage = Mock()
        # Don't set required storage dependencies
        with pytest.raises(RuntimeError, match="Required storage dependency missing"):
            factory._validate_storage_dependencies()

    def test_pacer_dependency_validation_error_handling(self, mock_config, shutdown_event):
        """Test PACER dependency validation error handling."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Test validation with uninitialized container
        with pytest.raises(RuntimeError, match="Container must be initialized"):
            factory._validate_pacer_dependencies()
        
        # Test validation with container missing PACER
        factory._container = Mock()
        # Don't set pacer attribute
        with pytest.raises(RuntimeError, match="PACER container not found"):
            factory._validate_pacer_dependencies()

    @pytest.mark.asyncio
    async def test_cleanup_error_handling(self, mock_config, shutdown_event):
        """Test error handling during cleanup operations."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Mock container that raises errors during cleanup
        mock_container = Mock()
        mock_container.shutdown_resources = Mock(side_effect=Exception("Cleanup failed"))
        mock_container.unwire = Mock(side_effect=Exception("Unwire failed"))
        
        factory._container = mock_container
        
        # Cleanup should handle errors gracefully and not raise
        await factory._cleanup_container()
        
        # Verify cleanup was attempted despite errors
        mock_container.shutdown_resources.assert_called_once()
        mock_container.unwire.assert_called_once()
        
        # Container should be reset to None even after cleanup errors
        assert factory._container is None

    def test_dependency_injection_framework_error_handling(self):
        """Test handling of dependency injection framework specific errors."""
        # Test handling of dependency-injector specific errors
        
        from dependency_injector.errors import Error as DIError
        
        # Mock a scenario that would raise a DI framework error
        with patch('dependency_injector.containers.DeclarativeContainer', side_effect=DIError("DI framework error")):
            with pytest.raises(DIError, match="DI framework error"):
                from dependency_injector import containers
                
                class TestContainer(containers.DeclarativeContainer):
                    pass

    def test_environment_variable_validation_error_handling(self, mock_config, shutdown_event):
        """Test error handling for environment variable validation."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Test with missing critical environment variables
        with patch.dict('os.environ', {}, clear=True):
            config_dict = factory._prepare_config_dict()
            
            # Should handle missing environment variables gracefully
            # by providing defaults or empty strings
            assert isinstance(config_dict, dict)
            assert 'aws_region' in config_dict
            assert 'aws_access_key' in config_dict
            assert 'aws_secret_key' in config_dict

    @pytest.mark.asyncio
    async def test_service_creation_timeout_handling(self, mock_config, shutdown_event):
        """Test handling of service creation timeouts."""
        factory = MainServiceFactory(mock_config, shutdown_event)
        
        # Mock a service that takes too long to create
        async def slow_service_creation():
            await asyncio.sleep(10)  # Simulate slow creation
            return Mock()
        
        # This would test timeout handling if implemented
        # The actual implementation would depend on whether timeouts are configured

    def test_memory_management_error_handling(self):
        """Test error handling for memory management issues."""
        # Test handling of memory-related errors during DI
        
        # Mock a scenario with insufficient memory
        with patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage', side_effect=MemoryError("Out of memory")):
            container = StorageContainer()
            container.logger.override(Mock())
            container.aws_region.override('us-west-2')
            container.aws_access_key.override('test_key')
            container.aws_secret_key.override('test_secret')
            container.dynamodb_endpoint.override('http://localhost:8000')
            container.s3_bucket_name.override('test-bucket')
            
            with pytest.raises(MemoryError, match="Out of memory"):
                container.async_dynamodb_storage()

    def test_network_related_error_handling(self):
        """Test error handling for network-related dependency issues."""
        # Test handling of network errors during service initialization
        
        import requests
        
        # Mock network-related service that fails due to network issues
        with patch('requests.get', side_effect=requests.ConnectionError("Network unreachable")):
            # Test that services handle network errors appropriately
            # This would be specific to services that make network calls during init
            pass