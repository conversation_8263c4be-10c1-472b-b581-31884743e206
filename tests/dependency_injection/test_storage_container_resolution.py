"""
Comprehensive tests for StorageContainer dependency resolution.

This test suite validates that the StorageContainer can properly resolve
all storage dependencies and repositories with correct wiring.
"""
import pytest
import os
from unittest.mock import Mock, patch, AsyncMock
from dependency_injector import containers, providers
from src.containers.storage import StorageContainer


class TestStorageContainerResolution:
    """Test StorageContainer dependency resolution and validation."""

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger for testing."""
        return Mock()

    @pytest.fixture
    def storage_config(self):
        """Create storage configuration for testing."""
        return {
            'aws_region': 'us-west-2',
            'aws_access_key': 'test_access_key',
            'aws_secret_key': 'test_secret_key',
            'dynamodb_endpoint': 'http://localhost:8000',
            's3_bucket_name': 'test-bucket'
        }

    @pytest.fixture
    def storage_container(self, mock_logger, storage_config):
        """Create and configure a StorageContainer for testing."""
        container = StorageContainer()
        container.config.from_dict(storage_config)
        container.logger.override(mock_logger)
        container.aws_region.override(storage_config['aws_region'])
        container.aws_access_key.override(storage_config['aws_access_key'])
        container.aws_secret_key.override(storage_config['aws_secret_key'])
        container.dynamodb_endpoint.override(storage_config['dynamodb_endpoint'])
        container.s3_bucket_name.override(storage_config['s3_bucket_name'])
        return container

    def test_storage_container_initialization(self, storage_container):
        """Test that StorageContainer initializes properly."""
        assert storage_container is not None
        assert isinstance(storage_container, containers.DeclarativeContainer)

    def test_async_dynamodb_storage_provider(self, storage_container):
        """Test AsyncDynamoDBStorage provider configuration."""
        # Test that provider exists
        assert hasattr(storage_container, 'async_dynamodb_storage')
        
        # Test provider type
        provider = storage_container.async_dynamodb_storage
        assert isinstance(provider, providers.Singleton)

    def test_s3_async_storage_provider(self, storage_container):
        """Test S3AsyncStorage provider configuration."""
        # Test that provider exists
        assert hasattr(storage_container, 's3_async_storage')
        
        # Test provider type
        provider = storage_container.s3_async_storage
        assert isinstance(provider, providers.Singleton)

    def test_repository_providers_exist(self, storage_container):
        """Test that all required repository providers exist."""
        required_repositories = [
            'pacer_repository',
            'fb_archive_repository', 
            'law_firms_repository',
            'district_courts_repository',
            'fb_image_hash_repository',
            'pacer_dockets_repository'
        ]
        
        for repo_name in required_repositories:
            assert hasattr(storage_container, repo_name), f"Missing repository: {repo_name}"
            provider = getattr(storage_container, repo_name)
            assert isinstance(provider, providers.Singleton), f"Repository {repo_name} should be Singleton"

    @patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage')
    def test_async_dynamodb_storage_instantiation(self, mock_dynamodb_class, storage_container):
        """Test AsyncDynamoDBStorage instantiation with correct parameters."""
        # Create mock instance
        mock_instance = Mock()
        mock_dynamodb_class.return_value = mock_instance
        
        # Get storage instance
        storage = storage_container.async_dynamodb_storage()
        
        # Verify class was called with correct parameters
        mock_dynamodb_class.assert_called_once()
        call_args = mock_dynamodb_class.call_args
        
        # Check config parameter
        config_arg = call_args.kwargs.get('config') or call_args.args[0]
        assert 'aws_region' in config_arg
        assert 'dynamodb_endpoint' in config_arg
        
        # Check logger parameter
        assert 'logger' in call_args.kwargs or len(call_args.args) >= 2

    @patch('src.infrastructure.storage.s3_async.S3AsyncStorage')
    def test_s3_async_storage_instantiation(self, mock_s3_class, storage_container):
        """Test S3AsyncStorage instantiation with correct parameters."""
        # Create mock instance
        mock_instance = Mock()
        mock_s3_class.return_value = mock_instance
        
        # Get storage instance
        storage = storage_container.s3_async_storage()
        
        # Verify class was called with correct parameters
        mock_s3_class.assert_called_once()
        call_args = mock_s3_class.call_args
        
        # Check required parameters are present
        assert 'logger' in call_args.kwargs
        assert 'bucket_name' in call_args.kwargs
        assert 'aws_access_key' in call_args.kwargs
        assert 'aws_secret_key' in call_args.kwargs
        assert 'aws_region' in call_args.kwargs
        assert call_args.kwargs.get('disable_versioning') == True

    @patch('src.repositories.pacer_repository.PacerRepository')
    @patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage')
    def test_pacer_repository_instantiation(self, mock_dynamodb_class, mock_repo_class, storage_container):
        """Test PacerRepository instantiation with AsyncDynamoDBStorage dependency."""
        # Create mock instances
        mock_storage = Mock()
        mock_repo = Mock()
        mock_dynamodb_class.return_value = mock_storage
        mock_repo_class.return_value = mock_repo
        
        # Get repository instance
        repository = storage_container.pacer_repository()
        
        # Verify repository was created with storage dependency
        mock_repo_class.assert_called_once()
        call_args = mock_repo_class.call_args
        
        assert 'storage' in call_args.kwargs
        assert 'logger' in call_args.kwargs

    @patch('src.repositories.district_courts_repository.DistrictCourtsRepository')
    @patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage')
    def test_district_courts_repository_instantiation(self, mock_dynamodb_class, mock_repo_class, storage_container):
        """Test DistrictCourtsRepository instantiation with correct dependencies."""
        # Create mock instances
        mock_storage = Mock()
        mock_repo = Mock()
        mock_dynamodb_class.return_value = mock_storage
        mock_repo_class.return_value = mock_repo
        
        # Get repository instance
        repository = storage_container.district_courts_repository()
        
        # Verify repository was created
        mock_repo_class.assert_called_once()
        call_args = mock_repo_class.call_args
        
        assert 'storage' in call_args.kwargs
        assert 'logger' in call_args.kwargs

    def test_utility_services_providers(self, storage_container):
        """Test that utility service providers are configured correctly."""
        # Test session storage
        assert hasattr(storage_container, 'session_storage')
        assert isinstance(storage_container.session_storage, providers.Singleton)
        
        # Test court lookup
        assert hasattr(storage_container, 'court_lookup')
        assert isinstance(storage_container.court_lookup, providers.Singleton)
        
        # Test law firm handler
        assert hasattr(storage_container, 'law_firm_handler')
        assert isinstance(storage_container.law_firm_handler, providers.Singleton)
        
        # Test OpenAI client
        assert hasattr(storage_container, 'openai_client')
        assert isinstance(storage_container.openai_client, providers.Singleton)

    @patch('src.utils.law_firm.LawFirmNameHandler')
    def test_law_firm_handler_instantiation(self, mock_handler_class, storage_container):
        """Test LawFirmNameHandler instantiation."""
        mock_instance = Mock()
        mock_handler_class.return_value = mock_instance
        
        # Get handler instance
        handler = storage_container.law_firm_handler()
        
        # Verify handler was created
        mock_handler_class.assert_called_once()

    def test_session_storage_instantiation(self, storage_container):
        """Test session storage instantiation returns a dict."""
        session_storage = storage_container.session_storage()
        assert isinstance(session_storage, dict)

    def test_court_lookup_instantiation(self, storage_container):
        """Test court lookup instantiation returns a dict."""
        court_lookup = storage_container.court_lookup()
        assert isinstance(court_lookup, dict)

    def test_singleton_behavior(self, storage_container):
        """Test that singleton providers return the same instance."""
        # Test AsyncDynamoDBStorage singleton behavior
        with patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage') as mock_class:
            mock_instance = Mock()
            mock_class.return_value = mock_instance
            
            storage1 = storage_container.async_dynamodb_storage()
            storage2 = storage_container.async_dynamodb_storage()
            
            # Should be the same instance (singleton)
            assert storage1 is storage2
            # Class should only be called once
            assert mock_class.call_count == 1

    def test_repository_singleton_behavior(self, storage_container):
        """Test that repository providers return the same instance."""
        with patch('src.repositories.pacer_repository.PacerRepository') as mock_class:
            with patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage'):
                mock_instance = Mock()
                mock_class.return_value = mock_instance
                
                repo1 = storage_container.pacer_repository()
                repo2 = storage_container.pacer_repository()
                
                # Should be the same instance (singleton)
                assert repo1 is repo2

    def test_factory_methods_exist(self, storage_container):
        """Test that factory methods are available."""
        # Test create_initialized_storage factory
        assert hasattr(storage_container, 'create_initialized_storage')
        
        # Test create_database_services factory  
        assert hasattr(storage_container, 'create_database_services')

    @patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage')
    @patch('src.repositories.pacer_repository.PacerRepository')
    def test_create_database_services_factory(self, mock_repo_class, mock_storage_class, storage_container):
        """Test create_database_services factory method."""
        # Create mock instances
        mock_storage = Mock()
        mock_repo = Mock()
        mock_storage_class.return_value = mock_storage
        mock_repo_class.return_value = mock_repo
        
        # Get database services bundle
        services = storage_container.create_database_services()
        
        # Verify the services bundle structure
        assert isinstance(services, dict)
        assert 'async_storage' in services
        assert 'pacer_repo' in services
        assert 'logger' in services

    def test_all_repositories_have_correct_dependencies(self, storage_container):
        """Test that all repositories are configured with correct dependencies."""
        repository_classes = [
            ('pacer_repository', 'src.repositories.pacer_repository.PacerRepository'),
            ('fb_archive_repository', 'src.repositories.fb_archive_repository.FBArchiveRepository'),
            ('law_firms_repository', 'src.repositories.law_firms_repository.LawFirmsRepository'),
            ('district_courts_repository', 'src.repositories.district_courts_repository.DistrictCourtsRepository'),
            ('fb_image_hash_repository', 'src.repositories.fb_image_hash_repository.FBImageHashRepository'),
            ('pacer_dockets_repository', 'src.repositories.pacer_dockets_repository.PacerDocketsRepository')
        ]
        
        for repo_name, repo_class_path in repository_classes:
            provider = getattr(storage_container, repo_name)
            
            # Verify provider is configured correctly
            assert isinstance(provider, providers.Singleton)
            
            # Check that provider has the expected dependencies
            # This validates that storage and logger are properly injected

    def test_container_configuration_override(self, mock_logger):
        """Test that container configuration can be overridden properly."""
        container = StorageContainer()
        
        # Override configuration
        test_config = {
            'aws_region': 'eu-west-1',
            'aws_access_key': 'new_access_key',
            'aws_secret_key': 'new_secret_key',
            'dynamodb_endpoint': 'http://localhost:9000',
            's3_bucket_name': 'new-test-bucket'
        }
        
        container.config.from_dict(test_config)
        container.logger.override(mock_logger)
        container.aws_region.override(test_config['aws_region'])
        container.aws_access_key.override(test_config['aws_access_key'])
        container.aws_secret_key.override(test_config['aws_secret_key'])
        container.dynamodb_endpoint.override(test_config['dynamodb_endpoint'])
        container.s3_bucket_name.override(test_config['s3_bucket_name'])
        
        # Verify overrides took effect
        with patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage') as mock_class:
            container.async_dynamodb_storage()
            
            # Check that the overridden values are used
            call_args = mock_class.call_args
            config_arg = call_args.kwargs.get('config') or call_args.args[0]
            
            assert config_arg['aws_region'] == 'eu-west-1'
            assert config_arg['dynamodb_endpoint'] == 'http://localhost:9000'

    def test_debug_district_courts_repository(self, storage_container):
        """Test debug version of district courts repository."""
        assert hasattr(storage_container, 'district_courts_repository_debug')
        
        # Verify it's a singleton provider
        provider = storage_container.district_courts_repository_debug
        assert isinstance(provider, providers.Singleton)

    @patch('src.infrastructure.external.openai_client.OpenAIClient')
    def test_openai_client_instantiation(self, mock_client_class, storage_container):
        """Test OpenAIClient instantiation with correct parameters."""
        mock_instance = Mock()
        mock_client_class.return_value = mock_instance
        
        # Get OpenAI client instance
        client = storage_container.openai_client()
        
        # Verify client was created with correct parameters
        mock_client_class.assert_called_once()
        call_args = mock_client_class.call_args
        
        assert 'api_key' in call_args.kwargs
        assert 'logger' in call_args.kwargs
        assert 'config' in call_args.kwargs

    def test_container_dependency_resolution_chain(self, storage_container):
        """Test that the full dependency resolution chain works correctly."""
        # This tests that repositories can get their storage dependencies
        # which in turn get their configuration dependencies
        
        with patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage') as mock_storage:
            with patch('src.repositories.pacer_repository.PacerRepository') as mock_repo:
                mock_storage_instance = Mock()
                mock_repo_instance = Mock()
                mock_storage.return_value = mock_storage_instance
                mock_repo.return_value = mock_repo_instance
                
                # Get repository (should trigger the whole chain)
                repository = storage_container.pacer_repository()
                
                # Verify the chain was executed
                mock_storage.assert_called_once()  # Storage was created
                mock_repo.assert_called_once()     # Repository was created with storage
                
                # Verify storage was passed to repository
                repo_call_args = mock_repo.call_args
                assert 'storage' in repo_call_args.kwargs

    def test_error_handling_missing_configuration(self):
        """Test error handling when required configuration is missing."""
        container = StorageContainer()
        
        # Don't set any configuration
        # Attempting to create services should handle missing config gracefully
        # or raise appropriate errors
        
        # Note: Actual behavior depends on the implementation
        # This test ensures we can detect configuration issues