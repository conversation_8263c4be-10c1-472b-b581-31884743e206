"""
Edge case and error scenario validation tests for dependency injection.

This test suite covers edge cases, boundary conditions, and unusual
scenarios in the dependency injection system.
"""
import pytest
import gc
import threading
import time
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from src.factories.main_factory import MainServiceFactory
from src.containers.storage import StorageContainer
from src.containers.pacer_core import PacerCoreContainer


class TestDependencyInjectionEdgeCases:
    """Test edge cases and unusual scenarios in dependency injection."""

    @pytest.fixture
    def mock_config(self):
        """Create a mock config for edge case testing."""
        config = Mock()
        config.headless = True
        config.run_parallel = True
        config.timeout_ms = 60000
        config.model_dump = Mock(return_value={
            "headless": True,
            "run_parallel": True,
            "timeout_ms": 60000
        })
        return config

    def test_very_large_configuration_handling(self, mock_config):
        """Test handling of very large configuration objects."""
        # Create a very large configuration
        large_config = {}
        for i in range(10000):
            large_config[f'key_{i}'] = f'value_{i}' * 100  # Large values
        
        mock_config.model_dump.return_value = large_config
        
        factory = MainServiceFactory(mock_config, None)
        
        # Should handle large configuration without issues
        config_dict = factory._prepare_config_dict()
        assert isinstance(config_dict, dict)
        assert len(config_dict) >= 10000

    def test_unicode_and_special_characters_in_config(self, mock_config):
        """Test handling of unicode and special characters in configuration."""
        special_config = {
            'unicode_key': '测试中文字符',
            'emoji_key': '🚀🔥💯',
            'special_chars': '!@#$%^&*()[]{}|;:,.<>?',
            'newlines': 'line1\nline2\r\nline3',
            'tabs': 'col1\tcol2\tcol3',
            'quotes': 'single\'quotes"double"quotes',
            'backslashes': 'path\\to\\file',
            'null_bytes': 'text\x00with\x00nulls'
        }
        
        mock_config.model_dump.return_value = special_config
        
        factory = MainServiceFactory(mock_config, None)
        config_dict = factory._prepare_config_dict()
        
        # Should handle special characters properly
        assert config_dict['unicode_key'] == '测试中文字符'
        assert config_dict['emoji_key'] == '🚀🔥💯'
        assert '\n' in config_dict['newlines']

    def test_deeply_nested_configuration_structures(self, mock_config):
        """Test handling of deeply nested configuration structures."""
        # Create deeply nested configuration
        nested_config = {}
        current = nested_config
        for i in range(100):  # 100 levels deep
            current[f'level_{i}'] = {}
            current = current[f'level_{i}']
        current['deep_value'] = 'reached_bottom'
        
        mock_config.model_dump.return_value = nested_config
        
        factory = MainServiceFactory(mock_config, None)
        config_dict = factory._prepare_config_dict()
        
        # Should handle deep nesting
        assert isinstance(config_dict, dict)

    def test_container_with_extremely_long_provider_names(self):
        """Test container behavior with extremely long provider names."""
        container = StorageContainer()
        
        # Test accessing providers with very long names
        long_name = 'a' * 1000  # 1000 character name
        
        # Should handle long names gracefully
        try:
            getattr(container, long_name, None)
        except Exception as e:
            # Should not crash, might return None or raise AttributeError
            assert isinstance(e, AttributeError)

    def test_concurrent_container_access(self):
        """Test concurrent access to container from multiple threads."""
        container = StorageContainer()
        container.logger.override(Mock())
        container.aws_region.override('us-west-2')
        container.aws_access_key.override('test_key')
        container.aws_secret_key.override('test_secret')
        container.dynamodb_endpoint.override('http://localhost:8000')
        container.s3_bucket_name.override('test-bucket')
        
        results = []
        errors = []
        
        def access_container():
            try:
                with patch('src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage') as mock_storage:
                    mock_storage.return_value = Mock()
                    service = container.async_dynamodb_storage()
                    results.append(service)
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads accessing container concurrently
        threads = []
        for i in range(10):
            thread = threading.Thread(target=access_container)
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All threads should complete without errors
        assert len(errors) == 0
        # All results should be the same instance (singleton behavior)
        if results:
            first_result = results[0]
            for result in results[1:]:
                assert result is first_result

    def test_memory_pressure_during_di_operations(self):
        """Test dependency injection behavior under memory pressure."""
        # Create memory pressure by allocating large objects
        large_objects = []
        try:
            # Allocate large amounts of memory
            for i in range(100):
                large_objects.append([0] * 1000000)  # Large lists
            
            # Now test DI operations under memory pressure
            container = StorageContainer()
            container.logger.override(Mock())
            
            # Should still work under memory pressure
            assert container is not None
            
        except MemoryError:
            # If we run out of memory, that's expected in this test
            pass
        finally:
            # Clean up memory
            large_objects.clear()
            gc.collect()

    def test_container_with_none_dependencies_at_runtime(self):
        """Test container behavior when dependencies become None at runtime."""
        container = StorageContainer()
        
        # Initially configure properly
        container.logger.override(Mock())
        container.aws_region.override('us-west-2')
        
        # Later override with None
        container.logger.override(None)
        container.aws_region.override(None)
        
        # Container should handle None dependencies appropriately
        # Behavior depends on implementation - might raise errors or use defaults

    def test_container_state_after_garbage_collection(self):
        """Test container state after intensive garbage collection."""
        container = StorageContainer()
        container.logger.override(Mock())
        
        # Create and destroy many objects to trigger GC
        for i in range(1000):
            temp_objects = [Mock() for _ in range(100)]
            del temp_objects
        
        # Force garbage collection
        gc.collect()
        
        # Container should still be functional
        assert container is not None
        assert hasattr(container, 'logger')

    def test_provider_with_recursive_dependencies(self):
        """Test provider behavior with recursive dependency structures."""
        from dependency_injector import containers, providers
        
        class RecursiveTestContainer(containers.DeclarativeContainer):
            # Create a provider that could potentially recurse
            recursive_service = providers.Factory(
                lambda self: self,  # Returns itself
                self=providers.Self()
            )
        
        container = RecursiveTestContainer()
        
        # Should handle recursive structures appropriately
        # Exact behavior depends on DI framework implementation

    def test_container_with_lambda_providers(self):
        """Test container behavior with lambda function providers."""
        from dependency_injector import containers, providers
        
        class LambdaTestContainer(containers.DeclarativeContainer):
            lambda_service = providers.Factory(
                lambda x, y: f"{x}-{y}",
                x="test",
                y="value"
            )
        
        container = LambdaTestContainer()
        
        # Should handle lambda providers
        result = container.lambda_service()
        assert result == "test-value"

    def test_container_with_async_providers(self):
        """Test container behavior with async function providers."""
        from dependency_injector import containers, providers
        
        async def async_factory():
            return "async_result"
        
        class AsyncTestContainer(containers.DeclarativeContainer):
            async_service = providers.Factory(async_factory)
        
        container = AsyncTestContainer()
        
        # Should handle async providers appropriately
        # Note: This might not work directly with dependency-injector
        # but tests the pattern

    def test_container_performance_with_many_providers(self):
        """Test container performance with a large number of providers."""
        from dependency_injector import containers, providers
        
        # Dynamically create a container with many providers
        provider_dict = {}
        for i in range(1000):  # 1000 providers
            provider_dict[f'service_{i}'] = providers.Factory(Mock, name=f'service_{i}')
        
        TestContainer = type('TestContainer', (containers.DeclarativeContainer,), provider_dict)
        container = TestContainer()
        
        # Should handle many providers efficiently
        start_time = time.time()
        service = getattr(container, 'service_500')()
        end_time = time.time()
        
        # Should be reasonably fast (less than 1 second)
        assert (end_time - start_time) < 1.0
        assert service is not None

    def test_container_with_circular_configuration_references(self):
        """Test container behavior with circular configuration references."""
        config_a = {'ref_to_b': None}
        config_b = {'ref_to_a': config_a}
        config_a['ref_to_b'] = config_b
        
        # Test with circular configuration
        container = StorageContainer()
        
        try:
            container.config.from_dict(config_a)
            # Should handle circular references appropriately
        except Exception:
            # Might raise an error - that's acceptable behavior
            pass

    def test_container_with_extremely_large_dependency_graph(self):
        """Test container with an extremely large dependency graph."""
        from dependency_injector import containers, providers
        
        # Create a large dependency graph
        provider_dict = {}
        
        # Create base services
        for i in range(100):
            provider_dict[f'base_service_{i}'] = providers.Factory(Mock, id=i)
        
        # Create services that depend on multiple base services
        for i in range(50):
            dependencies = {}
            for j in range(min(10, 100)):  # Each service depends on up to 10 base services
                dependencies[f'dep_{j}'] = providers.Dependency(f'base_service_{j}')
            
            provider_dict[f'complex_service_{i}'] = providers.Factory(
                Mock,
                **dependencies
            )
        
        TestContainer = type('TestContainer', (containers.DeclarativeContainer,), provider_dict)
        container = TestContainer()
        
        # Should handle large dependency graphs
        assert container is not None

    def test_container_with_malformed_provider_configurations(self):
        """Test container behavior with malformed provider configurations."""
        container = StorageContainer()
        
        # Test with malformed configurations
        malformed_configs = [
            {'aws_region': {'invalid': 'nested_config'}},  # Should be string
            {'s3_bucket_name': []},  # Should be string, not list
            {'timeout_ms': 'not_a_number'},  # Should be number
        ]
        
        for malformed_config in malformed_configs:
            try:
                container.config.from_dict(malformed_config)
                # Some malformed configs might be accepted, others might raise errors
            except Exception:
                # Expected for some malformed configurations
                pass

    def test_container_resource_exhaustion_scenarios(self):
        """Test container behavior under resource exhaustion."""
        containers_created = []
        
        try:
            # Create many containers to exhaust resources
            for i in range(10000):
                container = StorageContainer()
                container.logger.override(Mock())
                containers_created.append(container)
        
        except (MemoryError, OSError):
            # Expected when resources are exhausted
            pass
        
        finally:
            # Clean up
            containers_created.clear()
            gc.collect()

    def test_container_with_mock_side_effects(self):
        """Test container behavior when mocks have side effects."""
        container = StorageContainer()
        
        # Mock that raises different exceptions on different calls
        problematic_mock = Mock()
        call_count = 0
        
        def side_effect(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise ValueError("First call fails")
            elif call_count == 2:
                raise ConnectionError("Second call fails")
            else:
                return Mock()  # Third call succeeds
        
        problematic_mock.side_effect = side_effect
        
        container.logger.override(problematic_mock)
        
        # Test that container handles problematic dependencies
        try:
            logger1 = container.logger()
        except ValueError:
            pass  # Expected
        
        try:
            logger2 = container.logger()
        except ConnectionError:
            pass  # Expected
        
        logger3 = container.logger()  # Should succeed
        assert logger3 is not None

    def test_container_state_persistence_across_modifications(self):
        """Test container state persistence when modified during runtime."""
        container = StorageContainer()
        
        # Initial configuration
        container.logger.override(Mock(name="original_logger"))
        original_logger = container.logger()
        
        # Modify container during runtime
        container.logger.override(Mock(name="new_logger"))
        new_logger = container.logger()
        
        # State should reflect the new configuration
        assert original_logger != new_logger
        assert original_logger.name == "original_logger"
        assert new_logger.name == "new_logger"

    def test_container_behavior_with_class_inheritance_edge_cases(self):
        """Test container behavior with complex class inheritance scenarios."""
        from dependency_injector import containers, providers
        
        class BaseTestContainer(containers.DeclarativeContainer):
            base_service = providers.Factory(Mock, type="base")
        
        class MiddleTestContainer(BaseTestContainer):
            middle_service = providers.Factory(Mock, type="middle")
        
        class FinalTestContainer(MiddleTestContainer):
            final_service = providers.Factory(Mock, type="final")
            # Override base service
            base_service = providers.Factory(Mock, type="overridden_base")
        
        container = FinalTestContainer()
        
        # Should handle inheritance correctly
        base_service = container.base_service()
        middle_service = container.middle_service()
        final_service = container.final_service()
        
        assert base_service.type == "overridden_base"  # Should be overridden
        assert middle_service.type == "middle"
        assert final_service.type == "final"