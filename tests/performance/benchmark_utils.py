"""
Benchmark utilities for dependency injection container performance testing.

Provides timing, memory profiling, and analysis utilities for comprehensive
performance benchmarking of the DI container system.
"""

import time
import psutil
import gc
import sys
import tracemalloc
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass
from contextlib import contextmanager
from functools import wraps
import threading
import asyncio
import json
from pathlib import Path


@dataclass
class PerformanceMetrics:
    """Container for performance metrics data."""
    
    # Timing metrics
    execution_time: float
    cpu_time: float
    wall_time: float
    
    # Memory metrics
    memory_before_mb: float
    memory_after_mb: float
    memory_peak_mb: float
    memory_delta_mb: float
    
    # Object metrics
    objects_before: int
    objects_after: int
    objects_delta: int
    
    # Container specific metrics
    services_registered: int
    services_created: int
    dependency_resolutions: int
    
    # Additional context
    test_name: str
    iterations: int
    metadata: Dict[str, Any]


@dataclass
class BenchmarkResults:
    """Container for benchmark results across multiple runs."""
    
    test_name: str
    total_runs: int
    metrics: List[PerformanceMetrics]
    
    # Statistical analysis
    avg_execution_time: float
    min_execution_time: float
    max_execution_time: float
    std_execution_time: float
    
    avg_memory_delta: float
    max_memory_peak: float
    
    # Performance assessment
    performance_grade: str  # A, B, C, D, F
    regression_detected: bool
    recommendations: List[str]


class PerformanceTimer:
    """High-precision timing utility for benchmarking."""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.cpu_start = None
        self.cpu_end = None
    
    def start(self):
        """Start timing measurement."""
        gc.collect()  # Clean up before measurement
        self.cpu_start = time.process_time()
        self.start_time = time.perf_counter()
    
    def stop(self):
        """Stop timing measurement."""
        self.end_time = time.perf_counter()
        self.cpu_end = time.process_time()
    
    @property
    def execution_time(self) -> float:
        """Get execution time in milliseconds."""
        if self.start_time is None or self.end_time is None:
            raise ValueError("Timer not properly started/stopped")
        return (self.end_time - self.start_time) * 1000
    
    @property
    def cpu_time(self) -> float:
        """Get CPU time in milliseconds."""
        if self.cpu_start is None or self.cpu_end is None:
            raise ValueError("Timer not properly started/stopped")
        return (self.cpu_end - self.cpu_start) * 1000


class MemoryProfiler:
    """Memory usage profiling utility."""
    
    def __init__(self):
        self.process = psutil.Process()
        self.memory_before = None
        self.memory_after = None
        self.peak_memory = None
        self.objects_before = None
        self.objects_after = None
        self.tracemalloc_enabled = False
    
    def start_profiling(self):
        """Start memory profiling."""
        gc.collect()
        
        # Get baseline memory usage
        self.memory_before = self.process.memory_info().rss / 1024 / 1024  # MB
        self.objects_before = len(gc.get_objects())
        
        # Start detailed memory tracking
        if not tracemalloc.is_tracing():
            tracemalloc.start()
            self.tracemalloc_enabled = True
        
        self.peak_memory = self.memory_before
    
    def update_peak_memory(self):
        """Update peak memory usage during operation."""
        current_memory = self.process.memory_info().rss / 1024 / 1024
        self.peak_memory = max(self.peak_memory or 0, current_memory)
    
    def stop_profiling(self) -> Dict[str, float]:
        """Stop memory profiling and return results."""
        gc.collect()
        
        self.memory_after = self.process.memory_info().rss / 1024 / 1024  # MB
        self.objects_after = len(gc.get_objects())
        
        if self.tracemalloc_enabled:
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            self.peak_memory = max(self.peak_memory or 0, peak / 1024 / 1024)
        
        return {
            'memory_before_mb': self.memory_before,
            'memory_after_mb': self.memory_after,
            'memory_peak_mb': self.peak_memory,
            'memory_delta_mb': self.memory_after - self.memory_before,
            'objects_before': self.objects_before,
            'objects_after': self.objects_after,
            'objects_delta': self.objects_after - self.objects_before
        }


class ContainerMetricsCollector:
    """Collects metrics specific to DI containers."""
    
    def __init__(self):
        self.services_registered = 0
        self.services_created = 0
        self.dependency_resolutions = 0
        self.resolution_times = []
    
    def record_service_registration(self):
        """Record a service registration."""
        self.services_registered += 1
    
    def record_service_creation(self, creation_time: float = None):
        """Record a service creation."""
        self.services_created += 1
        if creation_time:
            self.resolution_times.append(creation_time)
    
    def record_dependency_resolution(self):
        """Record a dependency resolution."""
        self.dependency_resolutions += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get collected metrics."""
        return {
            'services_registered': self.services_registered,
            'services_created': self.services_created,
            'dependency_resolutions': self.dependency_resolutions,
            'avg_resolution_time': sum(self.resolution_times) / len(self.resolution_times) if self.resolution_times else 0,
            'max_resolution_time': max(self.resolution_times) if self.resolution_times else 0
        }


@contextmanager
def benchmark_context(test_name: str, iterations: int = 1, metadata: Dict[str, Any] = None):
    """Context manager for comprehensive benchmarking."""
    
    timer = PerformanceTimer()
    profiler = MemoryProfiler()
    collector = ContainerMetricsCollector()
    
    # Start measurements
    profiler.start_profiling()
    timer.start()
    
    try:
        yield collector
    finally:
        # Stop measurements
        timer.stop()
        memory_metrics = profiler.stop_profiling()
        container_metrics = collector.get_metrics()
        
        # Create performance metrics
        metrics = PerformanceMetrics(
            execution_time=timer.execution_time,
            cpu_time=timer.cpu_time,
            wall_time=timer.execution_time,  # Same as execution_time for synchronous operations
            memory_before_mb=memory_metrics['memory_before_mb'],
            memory_after_mb=memory_metrics['memory_after_mb'],
            memory_peak_mb=memory_metrics['memory_peak_mb'],
            memory_delta_mb=memory_metrics['memory_delta_mb'],
            objects_before=memory_metrics['objects_before'],
            objects_after=memory_metrics['objects_after'],
            objects_delta=memory_metrics['objects_delta'],
            services_registered=container_metrics['services_registered'],
            services_created=container_metrics['services_created'],
            dependency_resolutions=container_metrics['dependency_resolutions'],
            test_name=test_name,
            iterations=iterations,
            metadata=metadata or {}
        )
        
        # Store results for later analysis
        _store_benchmark_result(metrics)


def benchmark_function(iterations: int = 1, metadata: Dict[str, Any] = None):
    """Decorator for benchmarking functions."""
    
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            test_name = f"{func.__module__}.{func.__name__}"
            
            with benchmark_context(test_name, iterations, metadata) as collector:
                return func(*args, collector=collector, **kwargs)
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            test_name = f"{func.__module__}.{func.__name__}"
            
            with benchmark_context(test_name, iterations, metadata) as collector:
                return await func(*args, collector=collector, **kwargs)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return wrapper
    
    return decorator


class PerformanceAnalyzer:
    """Analyzes performance results and detects regressions."""
    
    def __init__(self, baseline_file: Optional[str] = None):
        self.baseline_file = baseline_file
        self.baseline_results = self._load_baseline() if baseline_file else {}
        self.results = []
    
    def _load_baseline(self) -> Dict[str, Any]:
        """Load baseline performance results."""
        if not self.baseline_file or not Path(self.baseline_file).exists():
            return {}
        
        try:
            with open(self.baseline_file, 'r') as f:
                return json.load(f)
        except Exception:
            return {}
    
    def analyze_metrics(self, metrics: PerformanceMetrics) -> BenchmarkResults:
        """Analyze performance metrics and detect regressions."""
        
        # Get baseline for comparison
        baseline = self.baseline_results.get(metrics.test_name, {})
        
        # Performance assessment
        grade = self._calculate_performance_grade(metrics)
        regression = self._detect_regression(metrics, baseline)
        recommendations = self._generate_recommendations(metrics, baseline)
        
        results = BenchmarkResults(
            test_name=metrics.test_name,
            total_runs=1,
            metrics=[metrics],
            avg_execution_time=metrics.execution_time,
            min_execution_time=metrics.execution_time,
            max_execution_time=metrics.execution_time,
            std_execution_time=0.0,
            avg_memory_delta=metrics.memory_delta_mb,
            max_memory_peak=metrics.memory_peak_mb,
            performance_grade=grade,
            regression_detected=regression,
            recommendations=recommendations
        )
        
        self.results.append(results)
        return results
    
    def _calculate_performance_grade(self, metrics: PerformanceMetrics) -> str:
        """Calculate performance grade based on metrics."""
        
        # Define performance thresholds (adjustable based on requirements)
        excellent_time = 10.0  # ms
        good_time = 50.0  # ms
        acceptable_time = 200.0  # ms
        poor_time = 1000.0  # ms
        
        excellent_memory = 5.0  # MB
        good_memory = 20.0  # MB
        acceptable_memory = 50.0  # MB
        poor_memory = 200.0  # MB
        
        time_score = 0
        memory_score = 0
        
        # Time score
        if metrics.execution_time <= excellent_time:
            time_score = 4
        elif metrics.execution_time <= good_time:
            time_score = 3
        elif metrics.execution_time <= acceptable_time:
            time_score = 2
        elif metrics.execution_time <= poor_time:
            time_score = 1
        else:
            time_score = 0
        
        # Memory score
        if metrics.memory_delta_mb <= excellent_memory:
            memory_score = 4
        elif metrics.memory_delta_mb <= good_memory:
            memory_score = 3
        elif metrics.memory_delta_mb <= acceptable_memory:
            memory_score = 2
        elif metrics.memory_delta_mb <= poor_memory:
            memory_score = 1
        else:
            memory_score = 0
        
        # Overall grade
        avg_score = (time_score + memory_score) / 2
        
        if avg_score >= 3.5:
            return "A"
        elif avg_score >= 2.5:
            return "B"
        elif avg_score >= 1.5:
            return "C"
        elif avg_score >= 0.5:
            return "D"
        else:
            return "F"
    
    def _detect_regression(self, metrics: PerformanceMetrics, baseline: Dict[str, Any]) -> bool:
        """Detect performance regression compared to baseline."""
        
        if not baseline:
            return False
        
        baseline_time = baseline.get('avg_execution_time', 0)
        baseline_memory = baseline.get('avg_memory_delta', 0)
        
        # Define regression thresholds (20% increase is considered regression)
        time_regression_threshold = 1.2
        memory_regression_threshold = 1.5
        
        time_regression = (baseline_time > 0 and 
                          metrics.execution_time > baseline_time * time_regression_threshold)
        
        memory_regression = (baseline_memory > 0 and 
                            metrics.memory_delta_mb > baseline_memory * memory_regression_threshold)
        
        return time_regression or memory_regression
    
    def _generate_recommendations(self, metrics: PerformanceMetrics, baseline: Dict[str, Any]) -> List[str]:
        """Generate performance improvement recommendations."""
        
        recommendations = []
        
        # Time-based recommendations
        if metrics.execution_time > 200:  # ms
            recommendations.append("Consider optimizing service initialization - execution time is high")
        
        if metrics.execution_time > 1000:  # ms
            recommendations.append("Critical: Service creation is taking over 1 second - review container architecture")
        
        # Memory-based recommendations
        if metrics.memory_delta_mb > 50:  # MB
            recommendations.append("High memory usage detected - review service lifecycle and caching")
        
        if metrics.memory_delta_mb > 200:  # MB
            recommendations.append("Critical: Excessive memory usage - potential memory leak in DI container")
        
        # Object count recommendations
        if metrics.objects_delta > 10000:
            recommendations.append("High object creation count - consider object pooling or singleton patterns")
        
        # Dependency resolution recommendations
        if metrics.dependency_resolutions > metrics.services_created * 5:
            recommendations.append("Excessive dependency resolutions - consider caching resolved dependencies")
        
        # Baseline comparison recommendations
        if baseline:
            baseline_time = baseline.get('avg_execution_time', 0)
            if baseline_time > 0 and metrics.execution_time > baseline_time * 1.5:
                recommendations.append(f"Performance regression detected: {((metrics.execution_time / baseline_time - 1) * 100):.1f}% slower than baseline")
        
        return recommendations
    
    def save_baseline(self, filename: str):
        """Save current results as baseline for future comparisons."""
        baseline_data = {}
        
        for result in self.results:
            baseline_data[result.test_name] = {
                'avg_execution_time': result.avg_execution_time,
                'avg_memory_delta': result.avg_memory_delta,
                'max_memory_peak': result.max_memory_peak,
                'timestamp': time.time()
            }
        
        with open(filename, 'w') as f:
            json.dump(baseline_data, f, indent=2)
    
    def generate_report(self) -> str:
        """Generate comprehensive performance report."""
        
        report = []
        report.append("=" * 80)
        report.append("DEPENDENCY INJECTION CONTAINER PERFORMANCE REPORT")
        report.append("=" * 80)
        report.append("")
        
        for result in self.results:
            report.append(f"Test: {result.test_name}")
            report.append(f"Grade: {result.performance_grade}")
            report.append(f"Execution Time: {result.avg_execution_time:.2f} ms")
            report.append(f"Memory Delta: {result.avg_memory_delta:.2f} MB")
            report.append(f"Peak Memory: {result.max_memory_peak:.2f} MB")
            report.append(f"Regression Detected: {result.regression_detected}")
            
            if result.recommendations:
                report.append("Recommendations:")
                for rec in result.recommendations:
                    report.append(f"  - {rec}")
            
            report.append("-" * 40)
            report.append("")
        
        # Overall summary
        grades = [r.performance_grade for r in self.results]
        regressions = sum(1 for r in self.results if r.regression_detected)
        
        report.append("SUMMARY:")
        report.append(f"Total Tests: {len(self.results)}")
        grade_dist = {g: grades.count(g) for g in set(grades)}
        report.append(f"Grade Distribution: {grade_dist}")
        report.append(f"Regressions Detected: {regressions}")
        
        return "\n".join(report)


# Global storage for benchmark results
_benchmark_results = []


def _store_benchmark_result(metrics: PerformanceMetrics):
    """Store benchmark result for later analysis."""
    _benchmark_results.append(metrics)


def get_stored_results() -> List[PerformanceMetrics]:
    """Get all stored benchmark results."""
    return _benchmark_results.copy()


def clear_stored_results():
    """Clear all stored benchmark results."""
    _benchmark_results.clear()


# Performance thresholds for different container operations
PERFORMANCE_THRESHOLDS = {
    'container_initialization': {
        'excellent': 5.0,    # ms
        'good': 25.0,        # ms
        'acceptable': 100.0, # ms
        'poor': 500.0        # ms
    },
    'service_resolution': {
        'excellent': 1.0,    # ms
        'good': 5.0,         # ms
        'acceptable': 20.0,  # ms
        'poor': 100.0        # ms
    },
    'dependency_wiring': {
        'excellent': 10.0,   # ms
        'good': 50.0,        # ms
        'acceptable': 200.0, # ms
        'poor': 1000.0       # ms
    },
    'factory_creation': {
        'excellent': 2.0,    # ms
        'good': 10.0,        # ms
        'acceptable': 50.0,  # ms
        'poor': 200.0        # ms
    }
}