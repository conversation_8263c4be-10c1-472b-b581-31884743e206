"""
Advanced Performance Analyzer for Dependency Injection Container Benchmarks.

Provides comprehensive analysis, regression detection, and performance
optimization recommendations for DI container systems.
"""

import json
import statistics
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
from datetime import datetime, timedelta


@dataclass
class PerformanceThreshold:
    """Performance threshold definition."""
    
    name: str
    excellent: float
    good: float
    acceptable: float
    poor: float
    unit: str
    description: str
    
    def evaluate(self, value: float) -> str:
        """Evaluate a value against thresholds."""
        if value <= self.excellent:
            return "excellent"
        elif value <= self.good:
            return "good"
        elif value <= self.acceptable:
            return "acceptable"
        elif value <= self.poor:
            return "poor"
        else:
            return "critical"


@dataclass
class BenchmarkMetadata:
    """Metadata for benchmark runs."""
    
    timestamp: float
    git_commit: Optional[str] = None
    branch: Optional[str] = None
    python_version: Optional[str] = None
    platform: Optional[str] = None
    cpu_count: Optional[int] = None
    memory_total_gb: Optional[float] = None
    tags: Optional[List[str]] = None


@dataclass
class PerformanceRegression:
    """Performance regression detection result."""
    
    test_name: str
    metric: str
    baseline_value: float
    current_value: float
    regression_percentage: float
    severity: str  # "minor", "moderate", "severe", "critical"
    threshold_exceeded: bool
    recommendation: str


@dataclass
class PerformanceTrend:
    """Performance trend analysis."""
    
    metric: str
    trend_direction: str  # "improving", "stable", "degrading"
    slope: float
    correlation: float
    confidence: float
    recent_change_percentage: float


class PerformanceDatabase:
    """Database for storing and retrieving performance benchmark results."""
    
    def __init__(self, db_path: str = "performance_history.json"):
        self.db_path = Path(db_path)
        self.data = self._load_data()
    
    def _load_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """Load performance data from file."""
        if self.db_path.exists():
            try:
                with open(self.db_path, 'r') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return {}
        return {}
    
    def save_data(self):
        """Save performance data to file."""
        try:
            with open(self.db_path, 'w') as f:
                json.dump(self.data, f, indent=2)
        except IOError as e:
            print(f"Warning: Could not save performance data: {e}")
    
    def store_benchmark_result(self, test_name: str, metrics: Dict[str, Any], 
                              metadata: Optional[BenchmarkMetadata] = None):
        """Store a benchmark result."""
        
        if test_name not in self.data:
            self.data[test_name] = []
        
        result = {
            'timestamp': time.time(),
            'metrics': metrics,
            'metadata': asdict(metadata) if metadata else {}
        }
        
        self.data[test_name].append(result)
        self.save_data()
    
    def get_benchmark_history(self, test_name: str, days: int = 30) -> List[Dict[str, Any]]:
        """Get benchmark history for a test."""
        
        if test_name not in self.data:
            return []
        
        cutoff_time = time.time() - (days * 24 * 60 * 60)
        recent_results = [
            result for result in self.data[test_name]
            if result['timestamp'] > cutoff_time
        ]
        
        # Sort by timestamp
        return sorted(recent_results, key=lambda x: x['timestamp'])
    
    def get_latest_baseline(self, test_name: str) -> Optional[Dict[str, Any]]:
        """Get the latest benchmark result for baseline comparison."""
        
        history = self.get_benchmark_history(test_name, days=365)  # Last year
        return history[-1] if history else None


class DIContainerPerformanceAnalyzer:
    """Advanced performance analyzer for DI container benchmarks."""
    
    def __init__(self, db_path: str = "tests/performance/performance_history.json"):
        self.db = PerformanceDatabase(db_path)
        self.thresholds = self._initialize_thresholds()
        self.regression_thresholds = {
            'minor': 10.0,      # 10% regression
            'moderate': 25.0,   # 25% regression  
            'severe': 50.0,     # 50% regression
            'critical': 100.0   # 100% regression
        }
    
    def _initialize_thresholds(self) -> Dict[str, PerformanceThreshold]:
        """Initialize performance thresholds for different metrics."""
        
        return {
            'container_initialization_ms': PerformanceThreshold(
                name="Container Initialization",
                excellent=5.0, good=25.0, acceptable=100.0, poor=500.0,
                unit="ms", description="Time to initialize DI container"
            ),
            'service_resolution_ms': PerformanceThreshold(
                name="Service Resolution",
                excellent=1.0, good=5.0, acceptable=20.0, poor=100.0,
                unit="ms", description="Time to resolve a service from container"
            ),
            'dependency_wiring_ms': PerformanceThreshold(
                name="Dependency Wiring",
                excellent=10.0, good=50.0, acceptable=200.0, poor=1000.0,
                unit="ms", description="Time to wire complex dependencies"
            ),
            'factory_creation_ms': PerformanceThreshold(
                name="Factory Creation",
                excellent=2.0, good=10.0, acceptable=50.0, poor=200.0,
                unit="ms", description="Time to create service factory"
            ),
            'memory_usage_mb': PerformanceThreshold(
                name="Memory Usage",
                excellent=5.0, good=20.0, acceptable=50.0, poor=200.0,
                unit="MB", description="Memory consumed by container operations"
            ),
            'object_creation_count': PerformanceThreshold(
                name="Object Creation",
                excellent=100, good=1000, acceptable=5000, poor=20000,
                unit="objects", description="Number of objects created"
            )
        }
    
    def analyze_benchmark_result(self, test_name: str, metrics: Dict[str, Any],
                                metadata: Optional[BenchmarkMetadata] = None) -> Dict[str, Any]:
        """Analyze a single benchmark result."""
        
        analysis = {
            'test_name': test_name,
            'timestamp': time.time(),
            'metrics': metrics,
            'threshold_evaluations': {},
            'performance_grade': None,
            'regression_analysis': None,
            'recommendations': [],
            'historical_trend': None
        }
        
        # Evaluate against thresholds
        for metric_name, value in metrics.items():
            threshold_key = self._map_metric_to_threshold(metric_name)
            if threshold_key in self.thresholds:
                threshold = self.thresholds[threshold_key]
                evaluation = threshold.evaluate(value)
                analysis['threshold_evaluations'][metric_name] = {
                    'value': value,
                    'evaluation': evaluation,
                    'threshold': asdict(threshold)
                }
        
        # Calculate overall performance grade
        analysis['performance_grade'] = self._calculate_performance_grade(
            analysis['threshold_evaluations']
        )
        
        # Regression analysis
        baseline = self.db.get_latest_baseline(test_name)
        if baseline:
            analysis['regression_analysis'] = self._analyze_regressions(
                metrics, baseline['metrics']
            )
        
        # Historical trend analysis
        history = self.db.get_benchmark_history(test_name, days=30)
        if len(history) > 3:
            analysis['historical_trend'] = self._analyze_trends(history)
        
        # Generate recommendations
        analysis['recommendations'] = self._generate_recommendations(analysis)
        
        # Store result
        self.db.store_benchmark_result(test_name, metrics, metadata)
        
        return analysis
    
    def _map_metric_to_threshold(self, metric_name: str) -> str:
        """Map metric name to threshold key."""
        
        mapping = {
            'execution_time': 'container_initialization_ms',
            'execution_time_ms': 'container_initialization_ms',
            'resolution_time': 'service_resolution_ms',
            'resolution_time_ms': 'service_resolution_ms',
            'wiring_time': 'dependency_wiring_ms',
            'wiring_time_ms': 'dependency_wiring_ms',
            'factory_time': 'factory_creation_ms',
            'factory_time_ms': 'factory_creation_ms',
            'memory_delta_mb': 'memory_usage_mb',
            'memory_usage_mb': 'memory_usage_mb',
            'objects_created': 'object_creation_count',
            'objects_delta': 'object_creation_count'
        }
        
        return mapping.get(metric_name, metric_name)
    
    def _calculate_performance_grade(self, evaluations: Dict[str, Any]) -> str:
        """Calculate overall performance grade."""
        
        if not evaluations:
            return "Unknown"
        
        # Score mapping
        score_map = {
            'excellent': 5,
            'good': 4,
            'acceptable': 3,
            'poor': 2,
            'critical': 1
        }
        
        scores = []
        for eval_data in evaluations.values():
            evaluation = eval_data['evaluation']
            scores.append(score_map.get(evaluation, 1))
        
        avg_score = sum(scores) / len(scores)
        
        if avg_score >= 4.5:
            return "A+"
        elif avg_score >= 4.0:
            return "A"
        elif avg_score >= 3.5:
            return "B"
        elif avg_score >= 3.0:
            return "C"
        elif avg_score >= 2.0:
            return "D"
        else:
            return "F"
    
    def _analyze_regressions(self, current_metrics: Dict[str, Any],
                           baseline_metrics: Dict[str, Any]) -> List[PerformanceRegression]:
        """Analyze performance regressions compared to baseline."""
        
        regressions = []
        
        for metric, current_value in current_metrics.items():
            if metric in baseline_metrics:
                baseline_value = baseline_metrics[metric]
                
                if baseline_value > 0:  # Avoid division by zero
                    change_percentage = ((current_value - baseline_value) / baseline_value) * 100
                    
                    # For most metrics, increase is bad (except for throughput-like metrics)
                    is_regression = change_percentage > 0
                    
                    # Determine severity
                    abs_change = abs(change_percentage)
                    severity = "minor"
                    threshold_exceeded = False
                    
                    if abs_change >= self.regression_thresholds['critical']:
                        severity = "critical"
                        threshold_exceeded = True
                    elif abs_change >= self.regression_thresholds['severe']:
                        severity = "severe"
                        threshold_exceeded = True
                    elif abs_change >= self.regression_thresholds['moderate']:
                        severity = "moderate"
                        threshold_exceeded = True
                    elif abs_change >= self.regression_thresholds['minor']:
                        severity = "minor"
                        threshold_exceeded = True
                    
                    if is_regression and threshold_exceeded:
                        regression = PerformanceRegression(
                            test_name="",  # Will be filled by caller
                            metric=metric,
                            baseline_value=baseline_value,
                            current_value=current_value,
                            regression_percentage=change_percentage,
                            severity=severity,
                            threshold_exceeded=threshold_exceeded,
                            recommendation=self._get_regression_recommendation(metric, severity)
                        )
                        regressions.append(regression)
        
        return regressions
    
    def _analyze_trends(self, history: List[Dict[str, Any]]) -> Dict[str, PerformanceTrend]:
        """Analyze performance trends over time."""
        
        trends = {}
        
        if len(history) < 3:
            return trends
        
        # Extract metrics over time
        metrics_over_time = {}
        timestamps = []
        
        for result in history:
            timestamps.append(result['timestamp'])
            for metric, value in result['metrics'].items():
                if metric not in metrics_over_time:
                    metrics_over_time[metric] = []
                metrics_over_time[metric].append(value)
        
        # Analyze each metric
        for metric, values in metrics_over_time.items():
            if len(values) >= 3:
                trend = self._calculate_trend(timestamps, values)
                trends[metric] = trend
        
        return trends
    
    def _calculate_trend(self, timestamps: List[float], values: List[float]) -> PerformanceTrend:
        """Calculate trend for a specific metric."""
        
        # Calculate slope using linear regression
        n = len(timestamps)
        sum_x = sum(timestamps)
        sum_y = sum(values)
        sum_xy = sum(x * y for x, y in zip(timestamps, values))
        sum_x2 = sum(x * x for x in timestamps)
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        
        # Calculate correlation coefficient
        mean_x = sum_x / n
        mean_y = sum_y / n
        
        numerator = sum((x - mean_x) * (y - mean_y) for x, y in zip(timestamps, values))
        denominator_x = sum((x - mean_x) ** 2 for x in timestamps)
        denominator_y = sum((y - mean_y) ** 2 for y in values)
        
        correlation = numerator / (denominator_x * denominator_y) ** 0.5 if denominator_x * denominator_y > 0 else 0
        
        # Determine trend direction
        if abs(slope) < 0.001:  # Essentially flat
            trend_direction = "stable"
        elif slope > 0:
            trend_direction = "degrading"  # For most metrics, increase is bad
        else:
            trend_direction = "improving"
        
        # Calculate recent change
        recent_change = 0
        if len(values) >= 2:
            recent_change = ((values[-1] - values[-2]) / values[-2]) * 100 if values[-2] != 0 else 0
        
        # Calculate confidence based on correlation strength
        confidence = min(abs(correlation), 1.0)
        
        return PerformanceTrend(
            metric="",  # Will be filled by caller
            trend_direction=trend_direction,
            slope=slope,
            correlation=correlation,
            confidence=confidence,
            recent_change_percentage=recent_change
        )
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate performance improvement recommendations."""
        
        recommendations = []
        
        # Threshold-based recommendations
        evaluations = analysis.get('threshold_evaluations', {})
        for metric, eval_data in evaluations.items():
            evaluation = eval_data['evaluation']
            value = eval_data['value']
            
            if evaluation in ['poor', 'critical']:
                if 'time' in metric or 'ms' in metric:
                    recommendations.append(
                        f"Critical: {metric} is {evaluation} ({value:.2f}ms). "
                        f"Consider optimizing container initialization or service resolution."
                    )
                elif 'memory' in metric:
                    recommendations.append(
                        f"Critical: {metric} is {evaluation} ({value:.2f}MB). "
                        f"Review memory usage patterns and consider caching strategies."
                    )
                elif 'object' in metric:
                    recommendations.append(
                        f"Warning: {metric} is {evaluation} ({value} objects). "
                        f"Consider object pooling or reducing object creation."
                    )
        
        # Regression-based recommendations
        regressions = analysis.get('regression_analysis', [])
        for regression in regressions:
            if regression.severity in ['severe', 'critical']:
                recommendations.append(
                    f"Performance regression detected in {regression.metric}: "
                    f"{regression.regression_percentage:.1f}% slower than baseline. "
                    f"{regression.recommendation}"
                )
        
        # Trend-based recommendations
        trends = analysis.get('historical_trend', {})
        for metric, trend in trends.items():
            if trend.trend_direction == "degrading" and trend.confidence > 0.7:
                recommendations.append(
                    f"Degrading performance trend in {metric} "
                    f"(confidence: {trend.confidence:.1f}). Monitor closely."
                )
        
        # Overall grade recommendations
        grade = analysis.get('performance_grade', 'Unknown')
        if grade in ['D', 'F']:
            recommendations.append(
                f"Overall performance grade is {grade}. "
                f"Consider architectural review of DI container system."
            )
        
        return recommendations
    
    def _get_regression_recommendation(self, metric: str, severity: str) -> str:
        """Get specific recommendation for a performance regression."""
        
        if 'time' in metric or 'ms' in metric:
            if severity == 'critical':
                return "Immediate action required - review recent container changes"
            else:
                return "Optimize service resolution or dependency wiring"
        elif 'memory' in metric:
            return "Review memory management and service lifecycle"
        elif 'object' in metric:
            return "Optimize object creation patterns"
        else:
            return "Investigate recent changes to container implementation"
    
    def generate_performance_dashboard(self, output_dir: str = "tests/performance/reports"):
        """Generate performance dashboard with charts and reports."""
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate summary report
        summary_report = self._generate_summary_report()
        with open(output_path / "performance_summary.txt", 'w') as f:
            f.write(summary_report)
        
        # Generate detailed charts if matplotlib is available
        if HAS_MATPLOTLIB:
            try:
                self._generate_performance_charts(output_path)
            except Exception as e:
                print(f"Warning: Chart generation failed: {e}")
        else:
            print("Warning: matplotlib not available, skipping chart generation")
        
        # Generate JSON report for integration with CI/CD
        json_report = self._generate_json_report()
        with open(output_path / "performance_report.json", 'w') as f:
            json.dump(json_report, f, indent=2)
    
    def _generate_summary_report(self) -> str:
        """Generate summary performance report."""
        
        report = []
        report.append("=" * 80)
        report.append("DEPENDENCY INJECTION CONTAINER PERFORMANCE DASHBOARD")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Analyze recent performance across all tests
        all_tests = list(self.db.data.keys())
        recent_results = {}
        
        for test_name in all_tests:
            recent = self.db.get_benchmark_history(test_name, days=7)
            if recent:
                recent_results[test_name] = recent[-1]  # Latest result
        
        if recent_results:
            report.append("RECENT PERFORMANCE SUMMARY:")
            report.append("-" * 40)
            
            for test_name, result in recent_results.items():
                metrics = result['metrics']
                timestamp = datetime.fromtimestamp(result['timestamp'])
                
                report.append(f"Test: {test_name}")
                report.append(f"Last Run: {timestamp.strftime('%Y-%m-%d %H:%M')}")
                
                # Show key metrics
                if 'execution_time' in metrics:
                    report.append(f"Execution Time: {metrics['execution_time']:.2f} ms")
                if 'memory_delta_mb' in metrics:
                    report.append(f"Memory Usage: {metrics['memory_delta_mb']:.2f} MB")
                
                report.append("")
        
        # Performance trends
        report.append("PERFORMANCE TRENDS (Last 30 Days):")
        report.append("-" * 40)
        
        for test_name in all_tests:
            history = self.db.get_benchmark_history(test_name, days=30)
            if len(history) > 3:
                trends = self._analyze_trends(history)
                
                report.append(f"Test: {test_name}")
                for metric, trend in trends.items():
                    report.append(f"  {metric}: {trend.trend_direction} "
                                f"(confidence: {trend.confidence:.1f})")
                report.append("")
        
        return "\n".join(report)
    
    def _generate_performance_charts(self, output_path: Path):
        """Generate performance charts."""
        
        for test_name in self.db.data.keys():
            history = self.db.get_benchmark_history(test_name, days=30)
            
            if len(history) > 2:
                self._create_test_chart(test_name, history, output_path)
    
    def _create_test_chart(self, test_name: str, history: List[Dict[str, Any]], 
                          output_path: Path):
        """Create performance chart for a specific test."""
        
        timestamps = [datetime.fromtimestamp(r['timestamp']) for r in history]
        metrics_data = {}
        
        # Collect metrics
        for result in history:
            for metric, value in result['metrics'].items():
                if metric not in metrics_data:
                    metrics_data[metric] = []
                metrics_data[metric].append(value)
        
        # Create subplots for different metrics
        fig, axes = plt.subplots(len(metrics_data), 1, figsize=(12, 4 * len(metrics_data)))
        if len(metrics_data) == 1:
            axes = [axes]
        
        for i, (metric, values) in enumerate(metrics_data.items()):
            axes[i].plot(timestamps, values, marker='o')
            axes[i].set_title(f"{test_name} - {metric}")
            axes[i].set_xlabel("Time")
            axes[i].set_ylabel(metric)
            axes[i].grid(True)
        
        plt.tight_layout()
        
        # Save chart
        chart_filename = f"{test_name.replace('.', '_')}_performance.png"
        plt.savefig(output_path / chart_filename)
        plt.close()
    
    def _generate_json_report(self) -> Dict[str, Any]:
        """Generate JSON performance report for CI/CD integration."""
        
        report = {
            'timestamp': time.time(),
            'summary': {},
            'tests': {},
            'overall_status': 'pass'
        }
        
        critical_regressions = 0
        
        for test_name in self.db.data.keys():
            recent = self.db.get_benchmark_history(test_name, days=1)
            if recent:
                latest = recent[-1]
                baseline = self.db.get_latest_baseline(test_name)
                
                test_report = {
                    'latest_metrics': latest['metrics'],
                    'timestamp': latest['timestamp'],
                    'status': 'pass'
                }
                
                if baseline:
                    regressions = self._analyze_regressions(
                        latest['metrics'], baseline['metrics']
                    )
                    
                    test_report['regressions'] = [asdict(r) for r in regressions]
                    
                    # Check for critical regressions
                    if any(r.severity == 'critical' for r in regressions):
                        test_report['status'] = 'fail'
                        critical_regressions += 1
                
                report['tests'][test_name] = test_report
        
        # Overall status
        if critical_regressions > 0:
            report['overall_status'] = 'fail'
            report['summary']['critical_regressions'] = critical_regressions
        
        return report
    
    def check_performance_gates(self, test_results: Dict[str, Dict[str, Any]]) -> bool:
        """Check if performance results pass defined gates."""
        
        gates_passed = True
        
        for test_name, metrics in test_results.items():
            analysis = self.analyze_benchmark_result(test_name, metrics)
            
            # Check for critical performance issues
            grade = analysis['performance_grade']
            if grade == 'F':
                print(f"FAIL: {test_name} has performance grade F")
                gates_passed = False
            
            # Check for critical regressions
            regressions = analysis.get('regression_analysis', [])
            critical_regressions = [r for r in regressions if r.severity == 'critical']
            if critical_regressions:
                print(f"FAIL: {test_name} has critical performance regressions")
                gates_passed = False
        
        return gates_passed


# Convenience functions for integration with test suites
def create_performance_analyzer(db_path: str = None) -> DIContainerPerformanceAnalyzer:
    """Create a performance analyzer instance."""
    
    if db_path is None:
        db_path = "tests/performance/performance_history.json"
    
    return DIContainerPerformanceAnalyzer(db_path)


def analyze_benchmark_results(test_results: Dict[str, Dict[str, Any]], 
                            db_path: str = None) -> Dict[str, Any]:
    """Analyze benchmark results and return comprehensive analysis."""
    
    analyzer = create_performance_analyzer(db_path)
    
    analysis_results = {}
    for test_name, metrics in test_results.items():
        analysis = analyzer.analyze_benchmark_result(test_name, metrics)
        analysis_results[test_name] = analysis
    
    return analysis_results


def generate_ci_performance_report(test_results: Dict[str, Dict[str, Any]],
                                 output_file: str = "performance_ci_report.json") -> bool:
    """Generate performance report for CI/CD and return pass/fail status."""
    
    analyzer = create_performance_analyzer()
    
    # Analyze all results
    for test_name, metrics in test_results.items():
        analyzer.analyze_benchmark_result(test_name, metrics)
    
    # Generate reports
    analyzer.generate_performance_dashboard()
    
    # Check performance gates
    gates_passed = analyzer.check_performance_gates(test_results)
    
    # Generate CI report
    ci_report = analyzer._generate_json_report()
    ci_report['gates_passed'] = gates_passed
    
    with open(output_file, 'w') as f:
        json.dump(ci_report, f, indent=2)
    
    return gates_passed