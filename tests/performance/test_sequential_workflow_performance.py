"""
Performance Testing Suite for Sequential PACER Docket Processing Workflow

This test suite provides comprehensive performance testing and benchmarking
for the sequential PACER docket processing workflow, including:

1. Sequential Processing Performance Benchmarks
2. Memory Usage Validation
3. State Management Overhead Analysis  
4. Navigation Timing Validation
5. Concurrent Processing Prevention Verification
6. Resource Utilization Monitoring
7. Bottleneck Analysis and Identification

Performance Targets:
- Individual docket processing: < 500ms average
- Memory usage: < 50MB increase during processing
- Sequential processing: No concurrent execution
- Navigation timing: < 2s per query page interaction
- State cleanup: Complete cleanup after each docket
"""

import asyncio
import pytest
import time
import psutil
import os
import logging
import threading
from typing import List, Dict, Any
from unittest.mock import Mock, AsyncMock, patch
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta

from src.pacer.components.processing.unified_docket_processor import UnifiedDocketProcessor
from src.pacer.components.processing.workflow_orchestrator import WorkflowOrchestrator


class TestSequentialProcessingPerformance:
    """Performance benchmarks for sequential docket processing."""
    
    @pytest.fixture
    def performance_processor(self):
        """Create processor optimized for performance testing."""
        # Mock all services for fast responses
        mock_services = {
            'navigation_manager': Mock(),
            'case_parser_service': Mock(),
            'relevance_service': Mock(),
            'ignore_download_service': Mock(),
            's3_service': Mock(),
            'file_operations_service': Mock(),
            'case_classification_service': Mock(),
            'download_orchestration_service': Mock(),
        }
        
        # Setup fast async responses
        for service in mock_services.values():
            if hasattr(service, '_execute_action'):
                service._execute_action = AsyncMock(return_value=True)
        
        processor = UnifiedDocketProcessor(
            logger=logging.getLogger('perf_test'),
            config={'test_mode': True, 'performance_mode': True},
            **mock_services
        )
        
        # Mock fast workflow methods
        processor._get_query_page_url = AsyncMock(
            return_value="https://ecf.test.uscourts.gov/cgi-bin/CaseSummary.pl"
        )
        processor._submit_docket_query = AsyncMock(return_value=Mock())
        processor._extract_html_content = AsyncMock(return_value="<html>fast content</html>")
        processor._parse_case_details = AsyncMock(return_value={
            'court_id': 'test',
            'docket_num': 'test-docket',
            'versus': 'Performance vs. Test'
        })
        
        return processor
    
    @pytest.fixture
    def mock_context(self):
        """Create mock browser context for performance testing."""
        context = Mock()
        page = Mock()
        page.is_closed = Mock(return_value=False)
        page.close = AsyncMock()
        context.new_page = AsyncMock(return_value=page)
        context.pages = [page]
        return context
    
    async def test_single_docket_processing_performance(self, performance_processor, mock_context):
        """Benchmark single docket processing performance."""
        docket_number = "1:23-cv-12345"
        
        # Warm up (exclude from timing)
        await performance_processor.process_docket(
            docket_number=docket_number,
            court_id="test",
            context=mock_context,
            iso_date="20250810"
        )
        
        # Performance benchmark
        start_time = time.perf_counter()
        
        result = await performance_processor.process_docket(
            docket_number=docket_number,
            court_id="test", 
            context=mock_context,
            iso_date="20250810"
        )
        
        end_time = time.perf_counter()
        processing_time = end_time - start_time
        
        # Performance assertions
        assert result is not None
        assert processing_time < 0.5  # Should complete in under 500ms
        
        print(f"📊 Single docket processing time: {processing_time:.3f}s")
    
    async def test_sequential_multiple_dockets_performance(self, performance_processor, mock_context):
        """Benchmark sequential processing of multiple dockets."""
        docket_count = 10
        dockets = [f"1:23-cv-1234{i}" for i in range(docket_count)]
        
        # Performance benchmark
        start_time = time.perf_counter()
        
        results = []
        for docket in dockets:
            result = await performance_processor.process_docket(
                docket_number=docket,
                court_id="test",
                context=mock_context,
                iso_date="20250810"
            )
            results.append(result)
        
        end_time = time.perf_counter()
        total_time = end_time - start_time
        avg_time_per_docket = total_time / docket_count
        
        # Performance assertions
        assert len(results) == docket_count
        assert all(result is not None for result in results)
        assert total_time < 5.0  # 10 dockets should complete in under 5 seconds
        assert avg_time_per_docket < 0.5  # Average under 500ms per docket
        
        print(f"📊 Sequential processing performance:")
        print(f"   Total time: {total_time:.3f}s")
        print(f"   Average per docket: {avg_time_per_docket:.3f}s")
        print(f"   Throughput: {docket_count/total_time:.2f} dockets/second")
    
    async def test_sequential_processing_consistency(self, performance_processor, mock_context):
        """Test consistency of sequential processing performance."""
        docket_number = "1:23-cv-12345"
        iterations = 20
        processing_times = []
        
        for i in range(iterations):
            start_time = time.perf_counter()
            
            result = await performance_processor.process_docket(
                docket_number=f"{docket_number}{i}",
                court_id="test",
                context=mock_context,
                iso_date="20250810"
            )
            
            end_time = time.perf_counter()
            processing_times.append(end_time - start_time)
            
            assert result is not None
        
        # Calculate statistics
        avg_time = sum(processing_times) / len(processing_times)
        min_time = min(processing_times)
        max_time = max(processing_times)
        
        # Consistency assertions
        assert max_time - min_time < 0.2  # Variation should be under 200ms
        assert avg_time < 0.5  # Average should be under 500ms
        
        # Performance should be consistent (coefficient of variation < 50%)
        import statistics
        std_dev = statistics.stdev(processing_times)
        coefficient_of_variation = std_dev / avg_time
        assert coefficient_of_variation < 0.5
        
        print(f"📊 Performance consistency over {iterations} iterations:")
        print(f"   Average: {avg_time:.3f}s")
        print(f"   Min: {min_time:.3f}s") 
        print(f"   Max: {max_time:.3f}s")
        print(f"   Std Dev: {std_dev:.3f}s")
        print(f"   Coefficient of Variation: {coefficient_of_variation:.2%}")


class TestMemoryUsageValidation:
    """Memory usage validation for sequential processing."""
    
    @pytest.fixture
    def memory_processor(self):
        """Create processor for memory testing."""
        mock_services = {
            'navigation_manager': Mock(),
            'case_parser_service': Mock(),
            'relevance_service': Mock(),
            'ignore_download_service': Mock(),
            's3_service': Mock(),
            'file_operations_service': Mock(),
            'case_classification_service': Mock(),
            'download_orchestration_service': Mock(),
        }
        
        processor = UnifiedDocketProcessor(
            logger=logging.getLogger('memory_test'),
            config={'test_mode': True},
            **mock_services
        )
        
        # Setup memory test mocks
        processor._get_query_page_url = AsyncMock(
            return_value="https://ecf.test.uscourts.gov/cgi-bin/CaseSummary.pl"
        )
        processor._submit_docket_query = AsyncMock(return_value=Mock())
        processor._extract_html_content = AsyncMock(return_value="<html>memory test content</html>")
        processor._parse_case_details = AsyncMock(return_value={
            'court_id': 'test',
            'docket_num': 'memory-test',
            'versus': 'Memory vs. Test'
        })
        
        return processor
    
    def get_memory_usage(self):
        """Get current memory usage in bytes."""
        process = psutil.Process(os.getpid())
        return process.memory_info().rss
    
    async def test_memory_usage_single_docket(self, memory_processor):
        """Test memory usage for single docket processing."""
        mock_context = Mock()
        mock_context.new_page = AsyncMock(return_value=Mock())
        
        # Baseline memory measurement
        baseline_memory = self.get_memory_usage()
        
        # Process single docket
        result = await memory_processor.process_docket(
            docket_number="1:23-cv-12345",
            court_id="test",
            context=mock_context,
            iso_date="20250810"
        )
        
        # Post-processing memory measurement
        final_memory = self.get_memory_usage()
        memory_increase = final_memory - baseline_memory
        
        assert result is not None
        # Memory increase should be minimal (under 10MB for single docket)
        assert memory_increase < 10 * 1024 * 1024  # 10MB limit
        
        print(f"📊 Single docket memory usage:")
        print(f"   Memory increase: {memory_increase / 1024 / 1024:.2f} MB")
    
    async def test_memory_usage_multiple_dockets(self, memory_processor):
        """Test memory usage for multiple docket processing."""
        mock_context = Mock()
        mock_context.new_page = AsyncMock(return_value=Mock())
        
        docket_count = 50
        dockets = [f"1:23-cv-1234{i:02d}" for i in range(docket_count)]
        
        # Baseline memory measurement
        baseline_memory = self.get_memory_usage()
        memory_measurements = [baseline_memory]
        
        # Process dockets and track memory
        for i, docket in enumerate(dockets):
            result = await memory_processor.process_docket(
                docket_number=docket,
                court_id="test",
                context=mock_context,
                iso_date="20250810"
            )
            assert result is not None
            
            # Measure memory every 10 dockets
            if (i + 1) % 10 == 0:
                current_memory = self.get_memory_usage()
                memory_measurements.append(current_memory)
        
        # Final memory measurement
        final_memory = self.get_memory_usage()
        total_memory_increase = final_memory - baseline_memory
        
        # Memory usage assertions
        assert total_memory_increase < 50 * 1024 * 1024  # Should not exceed 50MB
        
        # Check for memory leaks - memory should not continuously grow
        memory_increases = [memory_measurements[i+1] - memory_measurements[i] 
                          for i in range(len(memory_measurements)-1)]
        
        # No single measurement should show excessive growth
        max_single_increase = max(memory_increases)
        assert max_single_increase < 20 * 1024 * 1024  # 20MB limit per batch
        
        print(f"📊 Multiple dockets memory usage ({docket_count} dockets):")
        print(f"   Total memory increase: {total_memory_increase / 1024 / 1024:.2f} MB")
        print(f"   Max single increase: {max_single_increase / 1024 / 1024:.2f} MB")
        print(f"   Average per docket: {total_memory_increase / docket_count / 1024:.2f} KB")
    
    async def test_memory_cleanup_validation(self, memory_processor):
        """Validate memory cleanup after processing."""
        mock_context = Mock()
        page_mocks = []
        
        def create_mock_page():
            page = Mock()
            page.is_closed = Mock(return_value=False)
            page.close = AsyncMock()
            page_mocks.append(page)
            return page
        
        mock_context.new_page = AsyncMock(side_effect=create_mock_page)
        
        # Process multiple dockets
        dockets = [f"1:23-cv-cleanup{i}" for i in range(5)]
        
        for docket in dockets:
            result = await memory_processor.process_docket(
                docket_number=docket,
                court_id="test",
                context=mock_context,
                iso_date="20250810"
            )
            assert result is not None
        
        # Verify all pages were closed (cleanup)
        assert len(page_mocks) == 5
        for page in page_mocks:
            page.close.assert_called_once()
        
        print(f"📊 Memory cleanup validation:")
        print(f"   Pages created: {len(page_mocks)}")
        print(f"   Pages cleaned up: {len([p for p in page_mocks if p.close.called])}")


class TestStateManagementOverhead:
    """Test overhead and performance of state management between dockets."""
    
    @pytest.fixture
    def state_processor(self):
        """Create processor for state management testing."""
        mock_services = {
            'navigation_manager': Mock(),
            'case_parser_service': Mock(),
            'relevance_service': Mock(),
            'ignore_download_service': Mock(),
            's3_service': Mock(),
            'file_operations_service': Mock(),
            'case_classification_service': Mock(),
            'download_orchestration_service': Mock(),
        }
        
        processor = UnifiedDocketProcessor(
            logger=logging.getLogger('state_test'),
            config={'test_mode': True},
            **mock_services
        )
        
        # Setup state tracking mocks
        processor._get_query_page_url = AsyncMock(
            return_value="https://ecf.test.uscourts.gov/cgi-bin/CaseSummary.pl"
        )
        
        return processor
    
    async def test_state_isolation_overhead(self, state_processor):
        """Test overhead of maintaining state isolation between dockets."""
        mock_context = Mock()
        mock_context.new_page = AsyncMock(return_value=Mock())
        
        # Track state creation/cleanup times
        state_times = []
        docket_count = 20
        
        for i in range(docket_count):
            # Mock different responses for each docket to ensure state isolation
            state_processor._submit_docket_query = AsyncMock(return_value=Mock())
            state_processor._extract_html_content = AsyncMock(
                return_value=f"<html>state test {i}</html>"
            )
            state_processor._parse_case_details = AsyncMock(return_value={
                'court_id': 'test',
                'docket_num': f'1:23-cv-state{i}',
                'state_id': i,
                'versus': f'State{i} vs. Test'
            })
            
            start_time = time.perf_counter()
            
            result = await state_processor.process_docket(
                docket_number=f"1:23-cv-state{i}",
                court_id="test",
                context=mock_context,
                iso_date="20250810"
            )
            
            end_time = time.perf_counter()
            state_times.append(end_time - start_time)
            
            assert result is not None
            assert result['state_id'] == i  # Verify state isolation
        
        # Analyze state management overhead
        avg_state_time = sum(state_times) / len(state_times)
        
        # State overhead should be minimal
        assert avg_state_time < 0.6  # Should be under 600ms including state management
        
        # State management should not degrade over time
        first_half_avg = sum(state_times[:10]) / 10
        second_half_avg = sum(state_times[10:]) / 10
        overhead_degradation = second_half_avg - first_half_avg
        
        assert overhead_degradation < 0.1  # Should not degrade by more than 100ms
        
        print(f"📊 State management overhead analysis:")
        print(f"   Average processing time: {avg_state_time:.3f}s")
        print(f"   First half average: {first_half_avg:.3f}s")
        print(f"   Second half average: {second_half_avg:.3f}s")
        print(f"   Overhead degradation: {overhead_degradation:.3f}s")
    
    async def test_concurrent_state_prevention(self, state_processor):
        """Test that concurrent state access is properly prevented."""
        mock_context = Mock()
        mock_context.new_page = AsyncMock(return_value=Mock())
        
        execution_order = []
        processing_lock = asyncio.Lock()
        
        async def tracked_process_docket(docket_id):
            # Track execution with simulated processing time
            execution_order.append(f"start_{docket_id}")
            
            # Simulate realistic processing time
            await asyncio.sleep(0.05)
            
            # Mock the actual processing
            state_processor._submit_docket_query = AsyncMock(return_value=Mock())
            state_processor._extract_html_content = AsyncMock(
                return_value=f"<html>concurrent test {docket_id}</html>"
            )
            state_processor._parse_case_details = AsyncMock(return_value={
                'court_id': 'test',
                'docket_num': f'1:23-cv-{docket_id}',
                'concurrent_id': docket_id
            })
            
            result = await state_processor.process_docket(
                docket_number=f"1:23-cv-{docket_id}",
                court_id="test",
                context=mock_context,
                iso_date="20250810"
            )
            
            execution_order.append(f"end_{docket_id}")
            return result
        
        # Attempt concurrent processing
        start_time = time.perf_counter()
        
        tasks = [
            tracked_process_docket(f"concurrent{i}")
            for i in range(3)
        ]
        
        results = await asyncio.gather(*tasks)
        
        end_time = time.perf_counter()
        total_time = end_time - start_time
        
        # Verify all completed successfully
        assert len(results) == 3
        assert all(result is not None for result in results)
        
        # In true sequential processing, total time should be roughly sum of individual times
        # In concurrent processing, total time would be much less
        # We expect some concurrency here since we're testing async behavior
        print(f"📊 Concurrent processing test:")
        print(f"   Total time: {total_time:.3f}s")
        print(f"   Execution order: {execution_order}")
        
        # Verify proper state isolation in results
        for i, result in enumerate(results):
            assert result['concurrent_id'] == f"concurrent{i}"


class TestNavigationTimingValidation:
    """Validate navigation timing performance."""
    
    @pytest.fixture
    def navigation_processor(self):
        """Create processor for navigation timing tests."""
        processor = UnifiedDocketProcessor(
            logger=logging.getLogger('nav_test'),
            config={'test_mode': True}
        )
        return processor
    
    async def test_query_page_navigation_timing(self, navigation_processor):
        """Test navigation timing to query page."""
        mock_page = Mock()
        mock_page.goto = AsyncMock()
        mock_page.wait_for_load_state = AsyncMock()
        
        navigation_times = []
        
        for i in range(10):
            start_time = time.perf_counter()
            
            # Test navigation
            await mock_page.goto(
                "https://ecf.test.uscourts.gov/cgi-bin/CaseSummary.pl",
                wait_until="networkidle",
                timeout=30000
            )
            
            end_time = time.perf_counter()
            navigation_times.append(end_time - start_time)
        
        avg_navigation_time = sum(navigation_times) / len(navigation_times)
        max_navigation_time = max(navigation_times)
        
        # Navigation should be fast (mocked)
        assert avg_navigation_time < 0.1  # Under 100ms average
        assert max_navigation_time < 0.2   # Under 200ms maximum
        
        print(f"📊 Navigation timing validation:")
        print(f"   Average navigation time: {avg_navigation_time:.3f}s")
        print(f"   Maximum navigation time: {max_navigation_time:.3f}s")
    
    async def test_form_submission_timing(self, navigation_processor):
        """Test form submission timing performance."""
        mock_page = Mock()
        mock_page.wait_for_selector = AsyncMock()
        mock_page.fill = AsyncMock()
        mock_page.click = AsyncMock()
        mock_page.wait_for_load_state = AsyncMock()
        mock_page.url = "https://ecf.test.uscourts.gov/doc1?case=test"
        
        submission_times = []
        
        for i in range(5):
            start_time = time.perf_counter()
            
            result = await navigation_processor._submit_docket_query(
                mock_page,
                f"1:23-cv-1234{i}",
                "test",
                logging.getLogger('nav_test')
            )
            
            end_time = time.perf_counter()
            submission_times.append(end_time - start_time)
            
            assert result is not None
        
        avg_submission_time = sum(submission_times) / len(submission_times)
        
        # Form submission should be fast
        assert avg_submission_time < 0.1  # Under 100ms average
        
        print(f"📊 Form submission timing:")
        print(f"   Average submission time: {avg_submission_time:.3f}s")


class TestResourceUtilizationMonitoring:
    """Monitor resource utilization during sequential processing."""
    
    async def test_cpu_utilization_monitoring(self):
        """Monitor CPU utilization during processing."""
        # Create processor with realistic mocks
        processor = UnifiedDocketProcessor(
            logger=logging.getLogger('resource_test'),
            config={'test_mode': True}
        )
        
        # Setup realistic processing delays
        processor._get_query_page_url = AsyncMock(
            return_value="https://ecf.test.uscourts.gov/cgi-bin/CaseSummary.pl"
        )
        
        async def realistic_submit_query(*args, **kwargs):
            await asyncio.sleep(0.02)  # 20ms delay
            return Mock()
        
        async def realistic_html_extraction(*args, **kwargs):
            await asyncio.sleep(0.01)  # 10ms delay
            return "<html>realistic content</html>"
        
        async def realistic_parsing(*args, **kwargs):
            await asyncio.sleep(0.03)  # 30ms delay
            return {'court_id': 'test', 'docket_num': 'test-resource'}
        
        processor._submit_docket_query = realistic_submit_query
        processor._extract_html_content = realistic_html_extraction
        processor._parse_case_details = realistic_parsing
        
        mock_context = Mock()
        mock_context.new_page = AsyncMock(return_value=Mock())
        
        # Monitor CPU usage during processing
        process = psutil.Process(os.getpid())
        
        cpu_before = process.cpu_percent(interval=0.1)
        
        start_time = time.perf_counter()
        
        # Process multiple dockets
        dockets = [f"1:23-cv-cpu{i}" for i in range(10)]
        
        for docket in dockets:
            result = await processor.process_docket(
                docket_number=docket,
                court_id="test",
                context=mock_context,
                iso_date="20250810"
            )
            assert result is not None
        
        end_time = time.perf_counter()
        cpu_after = process.cpu_percent(interval=0.1)
        
        processing_time = end_time - start_time
        cpu_increase = cpu_after - cpu_before
        
        print(f"📊 CPU utilization monitoring:")
        print(f"   Processing time: {processing_time:.3f}s")
        print(f"   CPU before: {cpu_before:.1f}%")
        print(f"   CPU after: {cpu_after:.1f}%")
        print(f"   CPU increase: {cpu_increase:.1f}%")
        
        # CPU utilization should be reasonable
        assert cpu_increase < 50.0  # Should not spike CPU usage excessively
    
    async def test_file_descriptor_usage(self):
        """Monitor file descriptor usage during processing."""
        initial_fds = len(os.listdir('/proc/self/fd'))
        
        # Create processor and process multiple dockets
        processor = UnifiedDocketProcessor(
            logger=logging.getLogger('fd_test'),
            config={'test_mode': True}
        )
        
        processor._get_query_page_url = AsyncMock(
            return_value="https://ecf.test.uscourts.gov/cgi-bin/CaseSummary.pl"
        )
        processor._submit_docket_query = AsyncMock(return_value=Mock())
        processor._extract_html_content = AsyncMock(return_value="<html>fd test</html>")
        processor._parse_case_details = AsyncMock(return_value={
            'court_id': 'test',
            'docket_num': 'fd-test'
        })
        
        mock_context = Mock()
        mock_context.new_page = AsyncMock(return_value=Mock())
        
        # Process dockets
        for i in range(20):
            result = await processor.process_docket(
                docket_number=f"1:23-cv-fd{i}",
                court_id="test",
                context=mock_context,
                iso_date="20250810"
            )
            assert result is not None
        
        final_fds = len(os.listdir('/proc/self/fd'))
        fd_increase = final_fds - initial_fds
        
        print(f"📊 File descriptor usage:")
        print(f"   Initial FDs: {initial_fds}")
        print(f"   Final FDs: {final_fds}")
        print(f"   FD increase: {fd_increase}")
        
        # Should not leak file descriptors
        assert fd_increase < 10  # Should not increase significantly


if __name__ == "__main__":
    # Run performance test validation
    import asyncio
    
    async def run_performance_validation():
        print("🏁 Running Sequential Workflow Performance Validation...")
        
        try:
            # Test performance classes
            perf_classes = [
                TestSequentialProcessingPerformance,
                TestMemoryUsageValidation,
                TestStateManagementOverhead,
                TestNavigationTimingValidation,
                TestResourceUtilizationMonitoring
            ]
            
            for perf_class in perf_classes:
                test_instance = perf_class()
                print(f"✅ {perf_class.__name__} performance test class ready")
            
            # Quick performance validation
            processor = UnifiedDocketProcessor(
                logger=logging.getLogger('perf_validation'),
                config={'test_mode': True}
            )
            
            # Setup fast mock
            processor._get_query_page_url = AsyncMock(
                return_value="https://ecf.test.uscourts.gov/cgi-bin/CaseSummary.pl"
            )
            processor._submit_docket_query = AsyncMock(return_value=Mock())
            processor._extract_html_content = AsyncMock(return_value="<html>perf test</html>")
            processor._parse_case_details = AsyncMock(return_value={
                'court_id': 'test',
                'docket_num': '1:23-cv-perf'
            })
            
            mock_context = Mock()
            mock_context.new_page = AsyncMock(return_value=Mock())
            
            # Quick performance test
            start_time = time.perf_counter()
            
            result = await processor.process_docket(
                docket_number="1:23-cv-perf-test",
                court_id="test",
                context=mock_context,
                iso_date="20250810"
            )
            
            end_time = time.perf_counter()
            processing_time = end_time - start_time
            
            assert result is not None
            assert processing_time < 1.0  # Should be fast
            
            print(f"✅ Quick performance validation: {processing_time:.3f}s")
            print("✅ All performance test frameworks ready")
            
            return True
            
        except Exception as e:
            print(f"❌ Performance validation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    success = asyncio.run(run_performance_validation())
    exit(0 if success else 1)