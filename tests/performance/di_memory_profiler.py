"""
Memory Profiler for Dependency Injection Container Performance Analysis.

Provides detailed memory usage analysis for DI containers including
heap profiling, object tracking, and memory leak detection.
"""

import gc
import sys
import tracemalloc
import traceback
import weakref
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
from collections import defaultdict, Counter
import psutil
import time
from pathlib import Path
import json

try:
    import objgraph
    HAS_OBJGRAPH = True
except ImportError:
    HAS_OBJGRAPH = False

try:
    from pympler import tracker, muppy, summary
    HAS_PYMPLER = True
except ImportError:
    HAS_PYMPLER = False


@dataclass
class MemorySnapshot:
    """Snapshot of memory usage at a specific point in time."""
    
    timestamp: float
    rss_mb: float  # Resident Set Size in MB
    vms_mb: float  # Virtual Memory Size in MB
    heap_mb: float  # Heap usage in MB
    objects_count: int
    gc_objects: Dict[str, int]  # Count by type
    tracemalloc_current: Optional[int] = None
    tracemalloc_peak: Optional[int] = None
    
    def __post_init__(self):
        if self.tracemalloc_current:
            self.heap_mb = self.tracemalloc_current / 1024 / 1024
    
    @property
    def total_memory_mb(self) -> float:
        """Total memory usage combining RSS and heap."""
        return max(self.rss_mb, self.heap_mb or 0)


@dataclass
class MemoryDelta:
    """Memory usage change between two snapshots."""
    
    before: MemorySnapshot
    after: MemorySnapshot
    
    @property
    def rss_delta_mb(self) -> float:
        return self.after.rss_mb - self.before.rss_mb
    
    @property
    def heap_delta_mb(self) -> float:
        before_heap = self.before.heap_mb or 0
        after_heap = self.after.heap_mb or 0
        return after_heap - before_heap
    
    @property
    def objects_delta(self) -> int:
        return self.after.objects_count - self.before.objects_count
    
    @property
    def duration_seconds(self) -> float:
        return self.after.timestamp - self.before.timestamp
    
    def get_type_deltas(self) -> Dict[str, int]:
        """Get object count changes by type."""
        deltas = {}
        all_types = set(self.before.gc_objects.keys()) | set(self.after.gc_objects.keys())
        
        for obj_type in all_types:
            before_count = self.before.gc_objects.get(obj_type, 0)
            after_count = self.after.gc_objects.get(obj_type, 0)
            delta = after_count - before_count
            if delta != 0:
                deltas[obj_type] = delta
        
        return deltas


class ContainerMemoryProfiler:
    """Advanced memory profiler for DI containers."""
    
    def __init__(self, enable_detailed_tracking: bool = True):
        self.enable_detailed_tracking = enable_detailed_tracking
        self.process = psutil.Process()
        self.snapshots: List[MemorySnapshot] = []
        self.is_profiling = False
        self.start_time = None
        
        # Object tracking
        self.tracked_objects: Set[weakref.ref] = set()
        self.object_creation_stacks: Dict[id, List[str]] = {}
        
        # Tracemalloc setup
        self.tracemalloc_enabled = False
        
        # Pympler tracker if available
        self.pympler_tracker = None
        if HAS_PYMPLER and enable_detailed_tracking:
            self.pympler_tracker = tracker.SummaryTracker()
    
    def start_profiling(self):
        """Start memory profiling."""
        if self.is_profiling:
            return
        
        self.is_profiling = True
        self.start_time = time.time()
        
        # Enable tracemalloc if not already enabled
        if not tracemalloc.is_tracing():
            tracemalloc.start()
            self.tracemalloc_enabled = True
        
        # Take initial snapshot
        self.take_snapshot("start")
        
        # Start detailed tracking if enabled
        if self.enable_detailed_tracking and HAS_OBJGRAPH:
            # Track growth of specific types
            objgraph.show_growth()
    
    def stop_profiling(self) -> MemoryDelta:
        """Stop profiling and return memory delta."""
        if not self.is_profiling:
            raise ValueError("Profiling not started")
        
        # Take final snapshot
        self.take_snapshot("end")
        
        self.is_profiling = False
        
        # Stop tracemalloc if we enabled it
        if self.tracemalloc_enabled:
            tracemalloc.stop()
            self.tracemalloc_enabled = False
        
        # Return delta between first and last snapshots
        if len(self.snapshots) >= 2:
            return MemoryDelta(self.snapshots[0], self.snapshots[-1])
        else:
            raise ValueError("Insufficient snapshots for delta calculation")
    
    def take_snapshot(self, label: str = None) -> MemorySnapshot:
        """Take a memory usage snapshot."""
        
        # Force garbage collection for accurate measurement
        gc.collect()
        
        # Get process memory info
        memory_info = self.process.memory_info()
        rss_mb = memory_info.rss / 1024 / 1024
        vms_mb = memory_info.vms / 1024 / 1024
        
        # Get object count
        objects_count = len(gc.get_objects())
        
        # Get GC object counts by type
        gc_objects = self._get_gc_object_counts()
        
        # Get tracemalloc info if available
        tracemalloc_current = None
        tracemalloc_peak = None
        
        if tracemalloc.is_tracing():
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc_current = current
            tracemalloc_peak = peak
        
        snapshot = MemorySnapshot(
            timestamp=time.time(),
            rss_mb=rss_mb,
            vms_mb=vms_mb,
            heap_mb=tracemalloc_current / 1024 / 1024 if tracemalloc_current else None,
            objects_count=objects_count,
            gc_objects=gc_objects,
            tracemalloc_current=tracemalloc_current,
            tracemalloc_peak=tracemalloc_peak
        )
        
        self.snapshots.append(snapshot)
        return snapshot
    
    def _get_gc_object_counts(self) -> Dict[str, int]:
        """Get count of objects by type from garbage collector."""
        
        type_counts = Counter()
        
        for obj in gc.get_objects():
            obj_type = type(obj).__name__
            type_counts[obj_type] += 1
        
        # Convert to dict and limit to top types to avoid huge output
        return dict(type_counts.most_common(50))
    
    def track_object(self, obj: Any) -> bool:
        """Track a specific object for lifecycle analysis."""
        
        try:
            # Create weak reference to avoid keeping object alive
            weak_ref = weakref.ref(obj)
            self.tracked_objects.add(weak_ref)
            
            # Store creation stack trace if detailed tracking enabled
            if self.enable_detailed_tracking:
                obj_id = id(obj)
                stack = traceback.format_stack()
                self.object_creation_stacks[obj_id] = stack
            
            return True
        except TypeError:
            # Object doesn't support weak references
            return False
    
    def get_live_tracked_objects(self) -> int:
        """Get count of tracked objects that are still alive."""
        
        live_refs = set()
        for ref in self.tracked_objects:
            if ref() is not None:
                live_refs.add(ref)
        
        # Update tracked objects to remove dead references
        self.tracked_objects = live_refs
        return len(live_refs)
    
    def detect_memory_leaks(self, threshold_mb: float = 10.0) -> List[Dict[str, Any]]:
        """Detect potential memory leaks based on snapshots."""
        
        leaks = []
        
        if len(self.snapshots) < 2:
            return leaks
        
        # Check for steady memory growth
        for i in range(1, len(self.snapshots)):
            delta = MemoryDelta(self.snapshots[i-1], self.snapshots[i])
            
            if delta.rss_delta_mb > threshold_mb:
                leaks.append({
                    'type': 'rss_growth',
                    'delta_mb': delta.rss_delta_mb,
                    'duration_seconds': delta.duration_seconds,
                    'rate_mb_per_second': delta.rss_delta_mb / delta.duration_seconds,
                    'snapshot_before': i-1,
                    'snapshot_after': i
                })
            
            if delta.heap_delta_mb > threshold_mb:
                leaks.append({
                    'type': 'heap_growth',
                    'delta_mb': delta.heap_delta_mb,
                    'duration_seconds': delta.duration_seconds,
                    'rate_mb_per_second': delta.heap_delta_mb / delta.duration_seconds,
                    'snapshot_before': i-1,
                    'snapshot_after': i
                })
        
        # Check for excessive object growth
        if len(self.snapshots) >= 2:
            first_snapshot = self.snapshots[0]
            last_snapshot = self.snapshots[-1]
            delta = MemoryDelta(first_snapshot, last_snapshot)
            
            type_deltas = delta.get_type_deltas()
            for obj_type, count_delta in type_deltas.items():
                if count_delta > 10000:  # Threshold for excessive object creation
                    leaks.append({
                        'type': 'object_growth',
                        'object_type': obj_type,
                        'count_delta': count_delta,
                        'duration_seconds': delta.duration_seconds,
                        'rate_objects_per_second': count_delta / delta.duration_seconds
                    })
        
        return leaks
    
    def analyze_memory_usage_patterns(self) -> Dict[str, Any]:
        """Analyze memory usage patterns across all snapshots."""
        
        if len(self.snapshots) < 2:
            return {'error': 'Insufficient snapshots for analysis'}
        
        analysis = {
            'total_snapshots': len(self.snapshots),
            'profiling_duration_seconds': self.snapshots[-1].timestamp - self.snapshots[0].timestamp,
            'memory_stats': {},
            'object_stats': {},
            'growth_analysis': {},
            'efficiency_metrics': {}
        }
        
        # Memory statistics
        rss_values = [s.rss_mb for s in self.snapshots]
        heap_values = [s.heap_mb for s in self.snapshots if s.heap_mb is not None]
        
        analysis['memory_stats'] = {
            'rss_min_mb': min(rss_values),
            'rss_max_mb': max(rss_values),
            'rss_avg_mb': sum(rss_values) / len(rss_values),
            'rss_delta_mb': rss_values[-1] - rss_values[0],
            'heap_min_mb': min(heap_values) if heap_values else None,
            'heap_max_mb': max(heap_values) if heap_values else None,
            'heap_avg_mb': sum(heap_values) / len(heap_values) if heap_values else None,
            'heap_delta_mb': heap_values[-1] - heap_values[0] if len(heap_values) >= 2 else None
        }
        
        # Object statistics
        object_counts = [s.objects_count for s in self.snapshots]
        analysis['object_stats'] = {
            'objects_min': min(object_counts),
            'objects_max': max(object_counts),
            'objects_avg': sum(object_counts) / len(object_counts),
            'objects_delta': object_counts[-1] - object_counts[0]
        }
        
        # Growth analysis
        if len(self.snapshots) >= 3:
            growth_rates = []
            for i in range(1, len(self.snapshots)):
                delta = MemoryDelta(self.snapshots[i-1], self.snapshots[i])
                if delta.duration_seconds > 0:
                    growth_rate = delta.rss_delta_mb / delta.duration_seconds
                    growth_rates.append(growth_rate)
            
            if growth_rates:
                analysis['growth_analysis'] = {
                    'avg_growth_rate_mb_per_second': sum(growth_rates) / len(growth_rates),
                    'max_growth_rate_mb_per_second': max(growth_rates),
                    'growth_stability': self._calculate_growth_stability(growth_rates)
                }
        
        # Efficiency metrics
        total_delta = MemoryDelta(self.snapshots[0], self.snapshots[-1])
        analysis['efficiency_metrics'] = {
            'memory_efficiency_score': self._calculate_memory_efficiency(total_delta),
            'object_efficiency_score': self._calculate_object_efficiency(total_delta),
            'overall_efficiency_grade': self._calculate_overall_efficiency_grade(analysis)
        }
        
        return analysis
    
    def _calculate_growth_stability(self, growth_rates: List[float]) -> float:
        """Calculate stability of memory growth (0-1, 1 being most stable)."""
        
        if not growth_rates:
            return 1.0
        
        # Calculate coefficient of variation
        mean_rate = sum(growth_rates) / len(growth_rates)
        if mean_rate == 0:
            return 1.0
        
        variance = sum((rate - mean_rate) ** 2 for rate in growth_rates) / len(growth_rates)
        std_dev = variance ** 0.5
        cv = std_dev / abs(mean_rate)
        
        # Convert to stability score (lower CV = higher stability)
        stability = max(0.0, 1.0 - cv)
        return stability
    
    def _calculate_memory_efficiency(self, delta: MemoryDelta) -> float:
        """Calculate memory efficiency score (0-100)."""
        
        # Penalize excessive memory usage
        memory_penalty = min(delta.rss_delta_mb / 100.0, 1.0)  # 100MB = full penalty
        base_score = max(0.0, 1.0 - memory_penalty)
        
        return base_score * 100
    
    def _calculate_object_efficiency(self, delta: MemoryDelta) -> float:
        """Calculate object creation efficiency score (0-100)."""
        
        # Penalize excessive object creation
        object_penalty = min(delta.objects_delta / 50000.0, 1.0)  # 50k objects = full penalty
        base_score = max(0.0, 1.0 - object_penalty)
        
        return base_score * 100
    
    def _calculate_overall_efficiency_grade(self, analysis: Dict[str, Any]) -> str:
        """Calculate overall efficiency grade A-F."""
        
        memory_score = analysis['efficiency_metrics']['memory_efficiency_score']
        object_score = analysis['efficiency_metrics']['object_efficiency_score']
        
        overall_score = (memory_score + object_score) / 2
        
        if overall_score >= 90:
            return 'A'
        elif overall_score >= 80:
            return 'B'
        elif overall_score >= 70:
            return 'C'
        elif overall_score >= 60:
            return 'D'
        else:
            return 'F'
    
    def generate_memory_report(self) -> str:
        """Generate comprehensive memory analysis report."""
        
        analysis = self.analyze_memory_usage_patterns()
        leaks = self.detect_memory_leaks()
        
        report = []
        report.append("=" * 80)
        report.append("DEPENDENCY INJECTION CONTAINER MEMORY ANALYSIS REPORT")
        report.append("=" * 80)
        report.append("")
        
        # Summary
        if 'error' not in analysis:
            report.append("SUMMARY:")
            report.append(f"Profiling Duration: {analysis['profiling_duration_seconds']:.2f} seconds")
            report.append(f"Total Snapshots: {analysis['total_snapshots']}")
            report.append(f"Overall Efficiency Grade: {analysis['efficiency_metrics']['overall_efficiency_grade']}")
            report.append("")
            
            # Memory statistics
            memory_stats = analysis['memory_stats']
            report.append("MEMORY USAGE:")
            report.append(f"RSS Memory Delta: {memory_stats['rss_delta_mb']:.2f} MB")
            report.append(f"RSS Peak Usage: {memory_stats['rss_max_mb']:.2f} MB")
            report.append(f"RSS Average: {memory_stats['rss_avg_mb']:.2f} MB")
            
            if memory_stats['heap_delta_mb'] is not None:
                report.append(f"Heap Memory Delta: {memory_stats['heap_delta_mb']:.2f} MB")
                report.append(f"Heap Peak Usage: {memory_stats['heap_max_mb']:.2f} MB")
            
            report.append("")
            
            # Object statistics
            object_stats = analysis['object_stats']
            report.append("OBJECT CREATION:")
            report.append(f"Objects Created: {object_stats['objects_delta']}")
            report.append(f"Peak Object Count: {object_stats['objects_max']}")
            report.append(f"Average Object Count: {object_stats['objects_avg']:.0f}")
            report.append("")
            
            # Efficiency metrics
            efficiency = analysis['efficiency_metrics']
            report.append("EFFICIENCY METRICS:")
            report.append(f"Memory Efficiency: {efficiency['memory_efficiency_score']:.1f}/100")
            report.append(f"Object Efficiency: {efficiency['object_efficiency_score']:.1f}/100")
            report.append("")
        
        # Memory leaks
        if leaks:
            report.append("POTENTIAL MEMORY LEAKS DETECTED:")
            for i, leak in enumerate(leaks, 1):
                report.append(f"{i}. {leak['type'].upper()}")
                if 'delta_mb' in leak:
                    report.append(f"   Memory Delta: {leak['delta_mb']:.2f} MB")
                    report.append(f"   Growth Rate: {leak['rate_mb_per_second']:.2f} MB/sec")
                if 'object_type' in leak:
                    report.append(f"   Object Type: {leak['object_type']}")
                    report.append(f"   Count Delta: {leak['count_delta']}")
                report.append("")
        else:
            report.append("NO MEMORY LEAKS DETECTED")
            report.append("")
        
        # Recommendations
        report.append("RECOMMENDATIONS:")
        
        if 'error' not in analysis:
            memory_delta = analysis['memory_stats']['rss_delta_mb']
            object_delta = analysis['object_stats']['objects_delta']
            
            if memory_delta > 50:
                report.append("- High memory usage detected - review container caching strategies")
            
            if object_delta > 10000:
                report.append("- Excessive object creation - consider object pooling")
            
            if leaks:
                report.append("- Memory leaks detected - review service lifecycle management")
            
            efficiency_grade = analysis['efficiency_metrics']['overall_efficiency_grade']
            if efficiency_grade in ['D', 'F']:
                report.append("- Poor efficiency detected - consider container architecture review")
        
        if not any("- " in line for line in report[-10:]):
            report.append("- Performance is within acceptable limits")
        
        return "\n".join(report)
    
    def save_detailed_analysis(self, filename: str):
        """Save detailed analysis to JSON file."""
        
        analysis = self.analyze_memory_usage_patterns()
        leaks = self.detect_memory_leaks()
        
        # Convert snapshots to serializable format
        snapshots_data = []
        for snapshot in self.snapshots:
            snapshot_data = {
                'timestamp': snapshot.timestamp,
                'rss_mb': snapshot.rss_mb,
                'vms_mb': snapshot.vms_mb,
                'heap_mb': snapshot.heap_mb,
                'objects_count': snapshot.objects_count,
                'gc_objects': dict(list(snapshot.gc_objects.items())[:20])  # Limit size
            }
            snapshots_data.append(snapshot_data)
        
        detailed_data = {
            'analysis': analysis,
            'memory_leaks': leaks,
            'snapshots': snapshots_data,
            'tracked_objects_alive': self.get_live_tracked_objects()
        }
        
        with open(filename, 'w') as f:
            json.dump(detailed_data, f, indent=2)


# Context manager for easy memory profiling
class memory_profile:
    """Context manager for memory profiling."""
    
    def __init__(self, profiler: Optional[ContainerMemoryProfiler] = None,
                 enable_detailed_tracking: bool = True):
        self.profiler = profiler or ContainerMemoryProfiler(enable_detailed_tracking)
        self.delta = None
    
    def __enter__(self) -> ContainerMemoryProfiler:
        self.profiler.start_profiling()
        return self.profiler
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.delta = self.profiler.stop_profiling()
        return False
    
    def get_delta(self) -> Optional[MemoryDelta]:
        return self.delta


# Convenience functions
def profile_container_memory(func, *args, **kwargs) -> Tuple[Any, MemoryDelta, str]:
    """Profile memory usage of a function that operates on DI containers."""
    
    with memory_profile() as profiler:
        result = func(*args, **kwargs)
    
    delta = profiler.stop_profiling()
    report = profiler.generate_memory_report()
    
    return result, delta, report


def compare_container_memory_usage(func1, func2, *args, **kwargs) -> Dict[str, Any]:
    """Compare memory usage between two container operations."""
    
    # Profile first function
    with memory_profile() as profiler1:
        result1 = func1(*args, **kwargs)
    delta1 = profiler1.stop_profiling()
    
    # Profile second function
    with memory_profile() as profiler2:
        result2 = func2(*args, **kwargs)
    delta2 = profiler2.stop_profiling()
    
    return {
        'function1': {
            'result': result1,
            'memory_delta_mb': delta1.rss_delta_mb,
            'objects_delta': delta1.objects_delta,
            'duration_seconds': delta1.duration_seconds
        },
        'function2': {
            'result': result2,
            'memory_delta_mb': delta2.rss_delta_mb,
            'objects_delta': delta2.objects_delta,
            'duration_seconds': delta2.duration_seconds
        },
        'comparison': {
            'memory_difference_mb': delta2.rss_delta_mb - delta1.rss_delta_mb,
            'objects_difference': delta2.objects_delta - delta1.objects_delta,
            'speed_difference_seconds': delta2.duration_seconds - delta1.duration_seconds,
            'memory_efficiency_winner': 'function1' if delta1.rss_delta_mb < delta2.rss_delta_mb else 'function2'
        }
    }