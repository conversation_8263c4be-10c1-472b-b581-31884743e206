"""
Comprehensive Performance Benchmarks for Dependency Injection Container.

Tests all aspects of the DI container system to ensure fixes don't introduce
performance regressions and the system remains suitable for production use.
"""

import pytest
import asyncio
import logging
import gc
from typing import Dict, Any, Optional
from unittest.mock import Mock, patch

# Import the DI system components
from src.infrastructure.di.base_container import BaseContainer, ServiceDescriptor
from src.infrastructure.di.service_registry import ServiceRegistry
from src.pacer.factories.service_factory import PacerServiceFactory, PacerCoreContainer
from src.containers.pacer_core import Pacer<PERSON>oreContainer as PacerCoreContainerV2

# Import benchmark utilities
from .benchmark_utils import (
    benchmark_function, benchmark_context, PerformanceAnalyzer,
    ContainerMetricsCollector, PERFORMANCE_THRESHOLDS
)


class MockService:
    """Mock service for testing container performance."""
    
    def __init__(self, name: str = "mock", logger=None, config=None, dependency=None):
        self.name = name
        self.logger = logger
        self.config = config
        self.dependency = dependency
        self.initialized = True
    
    def cleanup(self):
        pass


class ComplexMockService:
    """Mock service with complex dependencies for testing."""
    
    def __init__(self, service_a=None, service_b=None, service_c=None, 
                 logger=None, config=None):
        self.service_a = service_a
        self.service_b = service_b  
        self.service_c = service_c
        self.logger = logger
        self.config = config


class PerformanceBenchmarkSuite:
    """Main performance benchmark suite for DI containers."""
    
    def __init__(self):
        self.analyzer = PerformanceAnalyzer()
        self.logger = logging.getLogger(__name__)
    
    @benchmark_function(iterations=1, metadata={"category": "base_container"})
    def test_base_container_initialization_performance(self, collector: ContainerMetricsCollector):
        """Test BaseContainer initialization performance."""
        
        # Test basic container creation
        container = BaseContainer()
        collector.record_service_registration()
        
        # Register multiple services
        for i in range(100):
            descriptor = ServiceDescriptor(
                instance=MockService(f"service_{i}"),
                singleton=True,
                tags=[f"tag_{i % 10}"]
            )
            container.register(f"service_{i}", descriptor)
            collector.record_service_registration()
        
        # Test service retrieval
        for i in range(50):
            service = container.get(f"service_{i}")
            collector.record_service_creation()
            assert service.name == f"service_{i}"
        
        # Test alias operations
        for i in range(25):
            container.add_alias(f"alias_{i}", f"service_{i}")
        
        return container
    
    @benchmark_function(iterations=1, metadata={"category": "service_registry"})
    def test_service_registry_dependency_resolution_performance(self, collector: ContainerMetricsCollector):
        """Test ServiceRegistry dependency resolution performance."""
        
        # Create registry with logger
        mock_logger = Mock()
        registry = ServiceRegistry(logger=mock_logger)
        collector.record_service_registration()
        
        # Register services with dependencies
        registry.register("config", ServiceDescriptor(instance={"key": "value"}))
        collector.record_service_registration()
        
        registry.register("logger", ServiceDescriptor(instance=mock_logger))
        collector.record_service_registration()
        
        # Register services with class-based resolution
        for i in range(50):
            dependencies = {}
            if i > 0:
                dependencies["dependency"] = f"service_{i-1}"
            
            descriptor = ServiceDescriptor(
                service_class="tests.performance.test_di_container_performance.MockService",
                dependencies=dependencies,
                singleton=True,
                tags=[f"batch_{i // 10}"]
            )
            registry.register(f"service_{i}", descriptor)
            collector.record_service_registration()
        
        # Test dependency resolution
        resolved_services = []
        for i in range(25):
            service = registry.get(f"service_{i}")
            collector.record_service_creation()
            collector.record_dependency_resolution()
            resolved_services.append(service)
        
        # Test complex dependency resolution
        complex_descriptor = ServiceDescriptor(
            service_class="tests.performance.test_di_container_performance.ComplexMockService",
            dependencies={
                "service_a": "service_0",
                "service_b": "service_1", 
                "service_c": "service_2",
                "logger": "logger",
                "config": "config"
            },
            singleton=True
        )
        registry.register("complex_service", complex_descriptor)
        collector.record_service_registration()
        
        complex_service = registry.get("complex_service")
        collector.record_service_creation()
        collector.record_dependency_resolution()
        
        # Test registry validation
        validation_errors = registry.validate()
        assert len(validation_errors) == 0
        
        return registry
    
    @benchmark_function(iterations=1, metadata={"category": "pacer_container"})
    def test_pacer_core_container_performance(self, collector: ContainerMetricsCollector):
        """Test PacerCoreContainer performance with full service wiring."""
        
        # Mock configuration and logger
        mock_config = {
            "headless": True,
            "configuration": {},
            "browser": {},
            "case_processing": {},
            "relevance": {},
            "classification": {},
            "verification": {},
            "download_orchestration": {},
            "file_operations": {},
            "metrics_reporting": {},
            "s3_management": {},
            "docket_processor": {},
            "pacer_orchestrator": {},
            "court_processing": {},
            "sequential_workflow": {}
        }
        
        mock_logger = Mock()
        mock_storage_container = Mock()
        mock_shutdown_event = Mock()
        
        # Create container
        container = PacerCoreContainerV2()
        collector.record_service_registration()
        
        # Configure container
        container.config.override(mock_config)
        container.logger.override(mock_logger)
        container.storage_container.override(mock_storage_container)
        container.shutdown_event.override(mock_shutdown_event)
        
        # Count registered services
        collector.services_registered = 50  # Approximate count from container
        
        # Test service creation performance
        # Note: Many services require actual implementations, so we'll test what we can
        
        try:
            # Test component creation
            credential_validator = container.credential_validator()
            collector.record_service_creation()
            
            path_builder = container.path_builder()
            collector.record_service_creation()
            
            csv_exporter = container.csv_exporter()
            collector.record_service_creation()
            
        except Exception as e:
            # Expected due to missing implementations
            self.logger.warning(f"Expected service creation failure: {e}")
        
        return container
    
    @benchmark_function(iterations=1, metadata={"category": "service_factory"})
    async def test_pacer_service_factory_performance(self, collector: ContainerMetricsCollector):
        """Test PacerServiceFactory performance and lifecycle management."""
        
        # Mock configuration
        config = {
            "configuration": {},
            "browser": {"headless": True},
            "case_processing": {},
            "relevance": {},
            "classification": {},
            "verification": {},
            "download_orchestration": {},
            "file_operations": {},
            "metrics_reporting": {},
            "s3_management": {},
            "docket_processor": {},
            "pacer_orchestrator": {},
            "court_processing": {}
        }
        
        mock_logger = Mock()
        
        # Create factory
        factory = PacerServiceFactory(config=config, logger=mock_logger)
        collector.record_service_registration()
        
        # Test initialization performance
        try:
            await factory.initialize()
            collector.record_service_creation()
        except Exception as e:
            # Expected due to missing service implementations
            self.logger.warning(f"Factory initialization failed as expected: {e}")
        
        # Test service retrieval performance
        try:
            config_service = await factory.get_configuration_service()
            collector.record_service_creation()
        except Exception:
            pass
        
        try:
            case_processing_service = await factory.get_case_processing_service()
            collector.record_service_creation()
        except Exception:
            pass
        
        # Test factory shutdown
        try:
            await factory.shutdown()
        except Exception:
            pass
        
        return factory
    
    @benchmark_function(iterations=1, metadata={"category": "memory_usage"})
    def test_container_memory_usage_patterns(self, collector: ContainerMetricsCollector):
        """Test memory usage patterns across different container operations."""
        
        containers = []
        
        # Create multiple containers to test memory scaling
        for i in range(10):
            container = BaseContainer()
            
            # Register varying numbers of services
            for j in range(50):
                descriptor = ServiceDescriptor(
                    instance=MockService(f"service_{i}_{j}"),
                    singleton=True,
                    metadata={"container": i, "service": j}
                )
                container.register(f"service_{i}_{j}", descriptor)
                collector.record_service_registration()
            
            containers.append(container)
        
        # Test service retrieval across containers
        for i, container in enumerate(containers):
            for j in range(25):  # Retrieve half the services
                service = container.get(f"service_{i}_{j}")
                collector.record_service_creation()
        
        # Test container cleanup
        for container in containers:
            container.clear()
        
        return containers
    
    @benchmark_function(iterations=1, metadata={"category": "dependency_wiring"})
    def test_complex_dependency_wiring_performance(self, collector: ContainerMetricsCollector):
        """Test performance of complex dependency wiring scenarios."""
        
        registry = ServiceRegistry()
        collector.record_service_registration()
        
        # Create a complex dependency graph
        # Level 0: Base services
        for i in range(10):
            descriptor = ServiceDescriptor(
                service_class="tests.performance.test_di_container_performance.MockService",
                dependencies={"name": f"base_{i}"},
                singleton=True
            )
            registry.register(f"base_{i}", descriptor)
            collector.record_service_registration()
        
        # Level 1: Services depending on base services
        for i in range(20):
            base_dep = f"base_{i % 10}"
            descriptor = ServiceDescriptor(
                service_class="tests.performance.test_di_container_performance.MockService",
                dependencies={"dependency": base_dep, "name": f"level1_{i}"},
                singleton=True
            )
            registry.register(f"level1_{i}", descriptor)
            collector.record_service_registration()
        
        # Level 2: Services with multiple dependencies
        for i in range(15):
            dependencies = {
                "service_a": f"base_{i % 10}",
                "service_b": f"level1_{i % 20}",
                "service_c": f"level1_{(i + 1) % 20}"
            }
            descriptor = ServiceDescriptor(
                service_class="tests.performance.test_di_container_performance.ComplexMockService",
                dependencies=dependencies,
                singleton=True
            )
            registry.register(f"level2_{i}", descriptor)
            collector.record_service_registration()
        
        # Test dependency resolution
        for i in range(15):
            service = registry.get(f"level2_{i}")
            collector.record_service_creation()
            collector.record_dependency_resolution()
        
        # Test dependency analysis
        for i in range(10):
            dependencies = registry.resolve_dependencies(f"level2_{i}")
            collector.record_dependency_resolution()
        
        initialization_order = registry.get_initialization_order()
        assert len(initialization_order) > 0
        
        return registry
    
    @benchmark_function(iterations=10, metadata={"category": "stress_test"})
    def test_container_stress_performance(self, collector: ContainerMetricsCollector):
        """Stress test container performance under load."""
        
        container = BaseContainer()
        
        # Register many services rapidly
        for i in range(1000):
            descriptor = ServiceDescriptor(
                instance=MockService(f"stress_{i}"),
                singleton=i % 2 == 0,  # Mix singleton and non-singleton
                tags=[f"stress", f"batch_{i // 100}"]
            )
            container.register(f"stress_{i}", descriptor)
            collector.record_service_registration()
        
        # Rapid service retrieval
        for i in range(500):
            service = container.get(f"stress_{i}")
            collector.record_service_creation()
            assert service is not None
        
        # Test tag-based operations
        stress_services = container.get_services_by_tag("stress")
        assert len(stress_services) == 1000
        
        return container
    
    def run_all_benchmarks(self) -> Dict[str, Any]:
        """Run all performance benchmarks and return results."""
        
        results = {}
        
        # Run synchronous benchmarks
        sync_benchmarks = [
            self.test_base_container_initialization_performance,
            self.test_service_registry_dependency_resolution_performance,
            self.test_pacer_core_container_performance,
            self.test_container_memory_usage_patterns,
            self.test_complex_dependency_wiring_performance,
            self.test_container_stress_performance
        ]
        
        for benchmark in sync_benchmarks:
            try:
                benchmark()
                self.logger.info(f"Completed benchmark: {benchmark.__name__}")
            except Exception as e:
                self.logger.error(f"Benchmark failed: {benchmark.__name__}: {e}")
        
        # Run async benchmarks
        async def run_async_benchmarks():
            try:
                await self.test_pacer_service_factory_performance()
                self.logger.info("Completed async benchmark: test_pacer_service_factory_performance")
            except Exception as e:
                self.logger.error(f"Async benchmark failed: {e}")
        
        asyncio.run(run_async_benchmarks())
        
        # Analyze results
        from .benchmark_utils import get_stored_results
        stored_results = get_stored_results()
        
        for metrics in stored_results:
            benchmark_result = self.analyzer.analyze_metrics(metrics)
            results[metrics.test_name] = {
                'grade': benchmark_result.performance_grade,
                'execution_time_ms': metrics.execution_time,
                'memory_delta_mb': metrics.memory_delta_mb,
                'services_created': metrics.services_created,
                'regression_detected': benchmark_result.regression_detected,
                'recommendations': benchmark_result.recommendations
            }
        
        return results
    
    def generate_performance_report(self) -> str:
        """Generate comprehensive performance report."""
        return self.analyzer.generate_report()


# Pytest integration
class TestDIContainerPerformance:
    """Pytest test class for DI container performance."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.benchmark_suite = PerformanceBenchmarkSuite()
        
        # Clear any existing results
        from .benchmark_utils import clear_stored_results
        clear_stored_results()
    
    def test_base_container_performance(self):
        """Test base container performance meets requirements."""
        
        result = self.benchmark_suite.test_base_container_initialization_performance()
        assert result is not None
        
        # Check performance meets requirements
        from .benchmark_utils import get_stored_results
        results = get_stored_results()
        
        if results:
            metrics = results[-1]
            threshold = PERFORMANCE_THRESHOLDS['container_initialization']['acceptable']
            assert metrics.execution_time < threshold, \
                f"Base container initialization too slow: {metrics.execution_time}ms > {threshold}ms"
    
    def test_service_registry_performance(self):
        """Test service registry performance meets requirements."""
        
        result = self.benchmark_suite.test_service_registry_dependency_resolution_performance()
        assert result is not None
        
        # Check performance meets requirements
        from .benchmark_utils import get_stored_results
        results = get_stored_results()
        
        if results:
            metrics = results[-1]
            threshold = PERFORMANCE_THRESHOLDS['dependency_wiring']['acceptable']
            assert metrics.execution_time < threshold, \
                f"Service registry too slow: {metrics.execution_time}ms > {threshold}ms"
    
    def test_pacer_container_performance(self):
        """Test PACER container performance meets requirements."""
        
        result = self.benchmark_suite.test_pacer_core_container_performance()
        assert result is not None
        
        # Check performance meets requirements
        from .benchmark_utils import get_stored_results
        results = get_stored_results()
        
        if results:
            metrics = results[-1]
            # PACER container is more complex, so use higher threshold
            threshold = PERFORMANCE_THRESHOLDS['dependency_wiring']['poor']
            assert metrics.execution_time < threshold, \
                f"PACER container too slow: {metrics.execution_time}ms > {threshold}ms"
    
    @pytest.mark.asyncio
    async def test_service_factory_performance(self):
        """Test service factory performance meets requirements."""
        
        await self.benchmark_suite.test_pacer_service_factory_performance()
        
        # Check performance meets requirements
        from .benchmark_utils import get_stored_results
        results = get_stored_results()
        
        if results:
            metrics = results[-1]
            threshold = PERFORMANCE_THRESHOLDS['factory_creation']['acceptable']
            assert metrics.execution_time < threshold, \
                f"Service factory too slow: {metrics.execution_time}ms > {threshold}ms"
    
    def test_memory_usage_reasonable(self):
        """Test memory usage is reasonable."""
        
        result = self.benchmark_suite.test_container_memory_usage_patterns()
        assert result is not None
        
        # Check memory usage
        from .benchmark_utils import get_stored_results
        results = get_stored_results()
        
        if results:
            metrics = results[-1]
            # Memory usage should be reasonable for container operations
            assert metrics.memory_delta_mb < 100, \
                f"Excessive memory usage: {metrics.memory_delta_mb}MB"
    
    def test_dependency_wiring_performance(self):
        """Test complex dependency wiring performance."""
        
        result = self.benchmark_suite.test_complex_dependency_wiring_performance()
        assert result is not None
        
        # Check performance meets requirements
        from .benchmark_utils import get_stored_results
        results = get_stored_results()
        
        if results:
            metrics = results[-1]
            threshold = PERFORMANCE_THRESHOLDS['dependency_wiring']['acceptable']
            assert metrics.execution_time < threshold, \
                f"Dependency wiring too slow: {metrics.execution_time}ms > {threshold}ms"
    
    def test_stress_performance(self):
        """Test container performance under stress."""
        
        result = self.benchmark_suite.test_container_stress_performance()
        assert result is not None
        
        # Check performance meets requirements even under stress
        from .benchmark_utils import get_stored_results
        results = get_stored_results()
        
        if results:
            metrics = results[-1]
            # Stress test gets higher threshold
            threshold = PERFORMANCE_THRESHOLDS['dependency_wiring']['poor'] * 2
            assert metrics.execution_time < threshold, \
                f"Stress test too slow: {metrics.execution_time}ms > {threshold}ms"
    
    def test_generate_performance_report(self):
        """Test performance report generation."""
        
        # Run a subset of benchmarks
        self.benchmark_suite.test_base_container_initialization_performance()
        self.benchmark_suite.test_service_registry_dependency_resolution_performance()
        
        # Generate report
        report = self.benchmark_suite.generate_performance_report()
        assert len(report) > 0
        assert "DEPENDENCY INJECTION CONTAINER PERFORMANCE REPORT" in report
        
        print("\n" + report)


if __name__ == "__main__":
    # Run benchmarks standalone
    suite = PerformanceBenchmarkSuite()
    results = suite.run_all_benchmarks()
    
    print("\nPerformance Benchmark Results:")
    print("=" * 50)
    
    for test_name, result in results.items():
        print(f"\nTest: {test_name}")
        print(f"Grade: {result['grade']}")
        print(f"Execution Time: {result['execution_time_ms']:.2f} ms")
        print(f"Memory Delta: {result['memory_delta_mb']:.2f} MB")
        print(f"Services Created: {result['services_created']}")
        print(f"Regression: {result['regression_detected']}")
        
        if result['recommendations']:
            print("Recommendations:")
            for rec in result['recommendations']:
                print(f"  - {rec}")
    
    print("\n" + suite.generate_performance_report())