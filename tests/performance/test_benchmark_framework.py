"""
Test script to validate the benchmark framework without requiring full DI imports.

This validates the performance benchmarking infrastructure is working correctly.
"""

import sys
import os
import time
import gc
from pathlib import Path

# Add the src directory to the path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

try:
    from tests.performance.benchmark_utils import (
        benchmark_function, benchmark_context, PerformanceAnalyzer,
        ContainerMetricsCollector, PERFORMANCE_THRESHOLDS
    )
    from tests.performance.di_memory_profiler import ContainerMemoryProfiler, memory_profile
    print("✓ Successfully imported benchmark utilities")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


class MockContainer:
    """Mock container for testing benchmark framework."""
    
    def __init__(self):
        self.services = {}
        self.instances = {}
    
    def register(self, name, service_class):
        self.services[name] = service_class
    
    def get(self, name):
        if name not in self.instances:
            service_class = self.services.get(name, lambda: f"mock_service_{name}")
            self.instances[name] = service_class()
        return self.instances[name]


class MockService:
    """Mock service for testing."""
    
    def __init__(self, name="mock"):
        self.name = name
        self.data = [i for i in range(1000)]  # Create some objects


class BenchmarkFrameworkValidator:
    """Validates the benchmark framework functionality."""
    
    def __init__(self):
        self.analyzer = PerformanceAnalyzer()
    
    @benchmark_function(iterations=1, metadata={"category": "framework_test"})
    def test_basic_container_operations(self, collector: ContainerMetricsCollector):
        """Test basic container operations to validate benchmark framework."""
        
        # Create mock container
        container = MockContainer()
        collector.record_service_registration()
        
        # Register services
        for i in range(100):
            container.register(f"service_{i}", lambda i=i: MockService(f"service_{i}"))
            collector.record_service_registration()
        
        # Retrieve services
        services = []
        for i in range(50):
            service = container.get(f"service_{i}")
            collector.record_service_creation()
            services.append(service)
        
        # Simulate some work
        time.sleep(0.01)  # 10ms of work
        
        return container
    
    def test_memory_profiling(self):
        """Test memory profiling functionality."""
        
        print("\n=== Testing Memory Profiling ===")
        
        with memory_profile() as profiler:
            # Create objects to test memory tracking
            containers = []
            for i in range(10):
                container = MockContainer()
                
                # Register many services
                for j in range(100):
                    container.register(f"service_{i}_{j}", 
                                     lambda i=i, j=j: MockService(f"service_{i}_{j}"))
                
                containers.append(container)
                
                # Track some objects
                profiler.track_object(container)
        
        # Generate memory report
        memory_report = profiler.generate_memory_report()
        print(memory_report)
        
        # Validate memory delta
        delta = profiler.stop_profiling()
        print(f"\nMemory Delta: {delta.rss_delta_mb:.2f} MB")
        print(f"Objects Created: {delta.objects_delta}")
        print(f"Duration: {delta.duration_seconds:.2f} seconds")
        
        return delta
    
    def test_performance_analysis(self):
        """Test performance analysis functionality."""
        
        print("\n=== Testing Performance Analysis ===")
        
        # Run the benchmark
        self.test_basic_container_operations()
        
        # Get stored results
        from tests.performance.benchmark_utils import get_stored_results
        results = get_stored_results()
        
        if results:
            metrics = results[-1]
            print(f"Execution Time: {metrics.execution_time:.2f} ms")
            print(f"Memory Delta: {metrics.memory_delta_mb:.2f} MB")
            print(f"Services Created: {metrics.services_created}")
            
            # Analyze with performance analyzer
            benchmark_result = self.analyzer.analyze_metrics(metrics)
            print(f"Performance Grade: {benchmark_result.performance_grade}")
            print(f"Regression Detected: {benchmark_result.regression_detected}")
            
            if benchmark_result.recommendations:
                print("Recommendations:")
                for rec in benchmark_result.recommendations:
                    print(f"  - {rec}")
        
        return results
    
    def test_thresholds(self):
        """Test performance threshold evaluation."""
        
        print("\n=== Testing Performance Thresholds ===")
        
        for threshold_name, threshold in PERFORMANCE_THRESHOLDS.items():
            print(f"\n{threshold_name}:")
            print(f"  Excellent: ≤ {threshold['excellent']} {threshold.get('unit', 'ms')}")
            print(f"  Good: ≤ {threshold['good']} {threshold.get('unit', 'ms')}")
            print(f"  Acceptable: ≤ {threshold['acceptable']} {threshold.get('unit', 'ms')}")
            print(f"  Poor: ≤ {threshold['poor']} {threshold.get('unit', 'ms')}")
    
    def run_validation(self):
        """Run all validation tests."""
        
        print("=" * 60)
        print("DEPENDENCY INJECTION CONTAINER BENCHMARK FRAMEWORK VALIDATION")
        print("=" * 60)
        
        try:
            # Test basic benchmarking
            print("\n1. Testing Basic Benchmarking...")
            self.test_basic_container_operations()
            print("✓ Basic benchmarking test passed")
            
            # Test memory profiling
            print("\n2. Testing Memory Profiling...")
            delta = self.test_memory_profiling()
            if delta.rss_delta_mb > 0:
                print("✓ Memory profiling test passed")
            else:
                print("⚠ Memory profiling shows no memory change")
            
            # Test performance analysis
            print("\n3. Testing Performance Analysis...")
            results = self.test_performance_analysis()
            if results:
                print("✓ Performance analysis test passed")
            else:
                print("✗ Performance analysis test failed")
            
            # Test thresholds
            print("\n4. Testing Performance Thresholds...")
            self.test_thresholds()
            print("✓ Threshold validation passed")
            
            # Generate final report
            print("\n5. Generating Performance Report...")
            report = self.analyzer.generate_report()
            print(report)
            
            print("\n" + "=" * 60)
            print("BENCHMARK FRAMEWORK VALIDATION COMPLETED SUCCESSFULLY")
            print("=" * 60)
            
            return True
            
        except Exception as e:
            print(f"\n✗ Validation failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main validation function."""
    
    # Clear any existing results
    from tests.performance.benchmark_utils import clear_stored_results
    clear_stored_results()
    
    # Run validation
    validator = BenchmarkFrameworkValidator()
    success = validator.run_validation()
    
    if success:
        print("\n🎉 All benchmark framework tests passed!")
        print("\nThe dependency injection container performance")
        print("benchmarking system is ready for use.")
        return 0
    else:
        print("\n❌ Some benchmark framework tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())