# Dependency Injection Container Performance Benchmarks

This directory contains comprehensive performance benchmarks for the dependency injection container system to ensure fixes don't introduce performance regressions and the system remains suitable for production use.

## Overview

The benchmarking suite provides detailed analysis of:

1. **Container Initialization Time** - Measuring startup and configuration overhead
2. **Dependency Resolution Speed** - Service lookup and resolution performance
3. **Memory Usage Patterns** - Memory consumption and leak detection
4. **Service Instantiation Performance** - Factory and service creation timing
5. **Container Wiring Overhead** - Complex dependency graph resolution

## Files

### Core Benchmark Suite
- `test_di_container_performance.py` - Main performance test suite with comprehensive benchmarks
- `benchmark_utils.py` - Timing, memory profiling, and analysis utilities
- `di_memory_profiler.py` - Advanced memory usage analysis and leak detection
- `performance_analyzer.py` - Performance analysis, regression detection, and reporting

### Reports and Data
- `performance_history.json` - Historical performance data for trend analysis
- `reports/` - Generated performance reports and charts
- `baseline_performance.json` - Baseline performance metrics for regression detection

## Running Benchmarks

### Quick Performance Check
```bash
# Run all benchmarks
python -m pytest tests/performance/test_di_container_performance.py -v

# Run specific benchmark
python -m pytest tests/performance/test_di_container_performance.py::TestDIContainerPerformance::test_base_container_performance -v
```

### Standalone Benchmark Execution
```bash
# Run comprehensive benchmark suite
cd tests/performance
python test_di_container_performance.py
```

### Memory Profiling
```bash
# Run with detailed memory analysis
python -c "
from tests.performance.di_memory_profiler import ContainerMemoryProfiler, memory_profile
from tests.performance.test_di_container_performance import PerformanceBenchmarkSuite

with memory_profile() as profiler:
    suite = PerformanceBenchmarkSuite()
    suite.test_base_container_initialization_performance()

print(profiler.generate_memory_report())
"
```

## Performance Thresholds

The benchmarks enforce the following performance requirements:

### Container Initialization
- **Excellent**: < 5ms
- **Good**: < 25ms  
- **Acceptable**: < 100ms
- **Poor**: < 500ms
- **Critical**: ≥ 500ms

### Service Resolution
- **Excellent**: < 1ms
- **Good**: < 5ms
- **Acceptable**: < 20ms
- **Poor**: < 100ms
- **Critical**: ≥ 100ms

### Dependency Wiring
- **Excellent**: < 10ms
- **Good**: < 50ms
- **Acceptable**: < 200ms
- **Poor**: < 1000ms
- **Critical**: ≥ 1000ms

### Memory Usage
- **Excellent**: < 5MB
- **Good**: < 20MB
- **Acceptable**: < 50MB
- **Poor**: < 200MB
- **Critical**: ≥ 200MB

## Benchmark Categories

### 1. Base Container Performance
Tests fundamental container operations:
- Service registration
- Service retrieval
- Alias management
- Basic lifecycle operations

### 2. Service Registry Performance
Tests enhanced container features:
- Lazy loading via class names
- Complex dependency resolution
- Validation and error handling
- Initialization order calculation

### 3. PACER Container Performance
Tests full production container:
- Complete service wiring
- All 50+ registered services
- Real-world configuration
- Component integration

### 4. Service Factory Performance
Tests high-level factory pattern:
- Factory initialization
- Service lifecycle management
- Async operations
- Shutdown procedures

### 5. Memory Usage Analysis
Tests memory efficiency:
- Container memory scaling
- Service instance caching
- Memory leak detection
- Garbage collection impact

### 6. Stress Testing
Tests performance under load:
- High service counts (1000+)
- Rapid service creation/retrieval
- Complex dependency graphs
- Tag-based operations

## Memory Profiling Features

### Real-time Memory Tracking
- RSS (Resident Set Size) monitoring
- Heap usage via tracemalloc
- Object count tracking
- GC statistics

### Memory Leak Detection
- Automatic leak detection based on growth patterns
- Weak reference tracking for object lifecycle
- Memory growth rate analysis
- Object type growth monitoring

### Advanced Analysis
- Memory efficiency scoring
- Performance grade calculation (A-F)
- Historical trend analysis
- Regression detection

## Performance Analysis

### Regression Detection
The system automatically detects performance regressions by comparing current results to historical baselines:

- **Minor**: 10-25% performance degradation
- **Moderate**: 25-50% performance degradation  
- **Severe**: 50-100% performance degradation
- **Critical**: >100% performance degradation

### Trend Analysis
Historical performance data is analyzed to identify:
- Performance improvement/degradation trends
- Seasonal performance patterns
- Correlation between different metrics
- Confidence levels for trend predictions

### Automated Recommendations
The analyzer provides specific recommendations based on:
- Threshold violations
- Detected regressions
- Memory usage patterns
- Historical trends

## Integration with CI/CD

### Performance Gates
The benchmark suite can be integrated with CI/CD pipelines to enforce performance requirements:

```bash
# Run benchmarks and check gates
python -c "
from tests.performance.performance_analyzer import generate_ci_performance_report

# Your benchmark results
results = {...}

# Generate report and check gates
gates_passed = generate_ci_performance_report(results)
exit(0 if gates_passed else 1)
"
```

### JSON Reports
Performance results are automatically exported in JSON format for integration with monitoring systems:

```json
{
  "timestamp": 1691234567.89,
  "overall_status": "pass",
  "summary": {
    "critical_regressions": 0
  },
  "tests": {
    "test_base_container": {
      "latest_metrics": {...},
      "status": "pass",
      "regressions": []
    }
  }
}
```

## Interpreting Results

### Performance Grades
- **A+/A**: Excellent performance, suitable for high-load production
- **B**: Good performance, minor optimization opportunities
- **C**: Acceptable performance, monitor for regressions
- **D**: Poor performance, optimization recommended
- **F**: Critical performance issues, immediate action required

### Memory Analysis
- **Memory Delta**: Total memory consumed by operation
- **Peak Memory**: Maximum memory usage during operation
- **Object Creation**: Number of objects created
- **Efficiency Score**: 0-100 rating of memory efficiency

### Regression Severity
- **Minor**: Performance degradation within acceptable limits
- **Moderate**: Noticeable performance impact, investigation recommended
- **Severe**: Significant performance impact, optimization required
- **Critical**: Unacceptable performance impact, immediate fixes needed

## Troubleshooting Performance Issues

### High Initialization Time
1. Review service registration patterns
2. Check for expensive operations during container setup
3. Consider lazy initialization strategies
4. Optimize dependency graph complexity

### Slow Service Resolution
1. Examine dependency chain depth
2. Check for circular dependencies
3. Review caching strategies
4. Optimize service factory implementations

### Memory Leaks
1. Review service lifecycle management
2. Check for strong references in cached objects
3. Verify proper cleanup in service destructors
4. Monitor singleton instance creation

### Excessive Object Creation
1. Implement object pooling for frequently created services
2. Review singleton vs prototype patterns
3. Optimize dependency injection strategies
4. Consider flyweight pattern for lightweight objects

## Extending the Benchmark Suite

### Adding New Benchmarks
```python
@benchmark_function(iterations=1, metadata={"category": "custom"})
def test_custom_container_operation(self, collector: ContainerMetricsCollector):
    # Your benchmark code here
    container = CustomContainer()
    collector.record_service_registration()
    
    # Perform operations
    result = container.custom_operation()
    collector.record_service_creation()
    
    return result
```

### Custom Performance Thresholds
```python
CUSTOM_THRESHOLDS = {
    'custom_metric_ms': PerformanceThreshold(
        name="Custom Operation",
        excellent=1.0, good=5.0, acceptable=20.0, poor=100.0,
        unit="ms", description="Custom container operation time"
    )
}
```

### Integration with External Monitoring
The benchmark system can be extended to report metrics to external monitoring systems like Prometheus, DataDog, or custom telemetry platforms.

## Best Practices

1. **Run benchmarks regularly** - Include in CI/CD pipeline
2. **Establish baselines** - Set performance baselines for new features
3. **Monitor trends** - Track performance over time, not just absolute values
4. **Profile before optimizing** - Use detailed memory profiling to identify bottlenecks
5. **Test realistic scenarios** - Benchmark with production-like configurations
6. **Document performance requirements** - Clearly define acceptable performance thresholds
7. **Automate regression detection** - Set up alerts for performance degradations

## Dependencies

Required packages:
- `psutil` - System and process monitoring
- `pytest` - Test framework integration
- `tracemalloc` - Built-in memory profiling (Python 3.4+)

Optional packages for enhanced features:
- `objgraph` - Object reference tracking
- `pympler` - Advanced memory analysis
- `matplotlib` - Performance chart generation
- `pandas` - Data analysis and manipulation