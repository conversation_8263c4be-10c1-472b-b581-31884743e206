#!/usr/bin/env python3
"""
Dependency Injection Container Validation Test

This test validates that ALL required dependencies are properly injected
in the PACER container setup, specifically for SequentialWorkflowManager.

Tests:
1. PacerRepository is ALWAYS injected
2. AsyncDynamoDBStorage is ALWAY<PERSON> injected  
3. S3AsyncStorage is AL<PERSON>YS injected
4. Court-specific logger is ALWAYS injected
"""

import asyncio
import pytest
from unittest.mock import Mock, AsyncMock, patch
from src.containers.pacer import PacerContainer
from src.containers.storage import StorageContainer
from src.containers.core import CoreContainer
from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager


@pytest.fixture
async def test_storage_container():
    """Create a test storage container with mocked dependencies."""
    storage_container = StorageContainer()
    
    # Override with mocks for testing
    storage_container.async_dynamodb_storage.override(Mock())
    storage_container.pacer_repository.override(Mock())
    storage_container.s3_async_storage.override(Mock())
    storage_container.court_lookup.override(Mock())
    storage_container.openai_client.override(Mock())
    
    return storage_container


@pytest.fixture
async def test_core_container():
    """Create a test core container with logger."""
    core_container = CoreContainer()
    
    # Configure with test config
    test_config = {
        "log_dir": "/tmp/test_logs",
        "DATA_DIR": "/tmp/test_data"
    }
    core_container.config.from_dict(test_config)
    
    return core_container


@pytest.fixture
async def test_pacer_container(test_core_container, test_storage_container):
    """Create a test PACER container with all dependencies."""
    pacer_container = PacerContainer()
    
    # Configure with test config
    test_config = {
        "headless": True,
        "timeout_ms": 30000,
        "test_mode": True
    }
    pacer_container.config.from_dict(test_config)
    
    # Inject dependencies
    pacer_container.logger.override(test_core_container.logger())
    pacer_container.storage_container.override(test_storage_container)
    pacer_container.shutdown_event.override(asyncio.Event())
    
    return pacer_container


@pytest.mark.asyncio
async def test_sequential_workflow_manager_dependency_injection(test_pacer_container):
    """Test that SequentialWorkflowManager receives ALL required dependencies."""
    print("\n=== Testing SequentialWorkflowManager Dependency Injection ===")
    
    # Test the sequential workflow manager provider
    sequential_workflow_manager = test_pacer_container.sequential_workflow_manager()
    
    # Validate that all critical dependencies are injected
    assert sequential_workflow_manager is not None, "SequentialWorkflowManager not created"
    
    # Check database dependencies
    assert sequential_workflow_manager.pacer_repository is not None, "PacerRepository not injected"
    assert sequential_workflow_manager.async_dynamodb_storage is not None, "AsyncDynamoDBStorage not injected"
    
    # Check processing dependencies
    assert sequential_workflow_manager.navigation_facade is not None, "NavigationFacade not injected"
    assert sequential_workflow_manager.docket_processor is not None, "DocketProcessor not injected"
    
    print("✅ ALL required dependencies properly injected")


@pytest.mark.asyncio
async def test_database_enabled_sequential_processor_factory(test_pacer_container):
    """Test the database-enabled sequential processor factory."""
    print("\n=== Testing Database-Enabled Sequential Processor Factory ===")
    
    # Test the factory provider
    factory_instance = test_pacer_container.database_enabled_sequential_processor()
    
    # Validate factory creates instance with all dependencies
    assert factory_instance is not None, "Factory did not create instance"
    assert factory_instance.pacer_repository is not None, "Factory: PacerRepository not injected"
    assert factory_instance.async_dynamodb_storage is not None, "Factory: AsyncDynamoDBStorage not injected"
    
    print("✅ Database-enabled factory working correctly")


@pytest.mark.asyncio
async def test_storage_container_dependencies(test_storage_container):
    """Test that storage container provides all required dependencies."""
    print("\n=== Testing Storage Container Dependencies ===")
    
    # Test PacerRepository
    pacer_repo = test_storage_container.pacer_repository()
    assert pacer_repo is not None, "PacerRepository not available from storage container"
    print("✅ PacerRepository available")
    
    # Test AsyncDynamoDBStorage
    async_storage = test_storage_container.async_dynamodb_storage()
    assert async_storage is not None, "AsyncDynamoDBStorage not available from storage container"
    print("✅ AsyncDynamoDBStorage available")
    
    # Test S3AsyncStorage
    s3_storage = test_storage_container.s3_async_storage()
    assert s3_storage is not None, "S3AsyncStorage not available from storage container"
    print("✅ S3AsyncStorage available")


@pytest.mark.asyncio
async def test_sequential_workflow_manager_initialization():
    """Test that SequentialWorkflowManager initializes with proper dependency validation."""
    print("\n=== Testing SequentialWorkflowManager Initialization ===")
    
    # Create mocks for all required dependencies
    mock_logger = Mock()
    mock_config = {"test": True}
    mock_navigation_facade = Mock()
    mock_docket_processor = Mock()
    mock_pacer_repository = Mock()
    mock_async_dynamodb_storage = Mock()
    
    # Create SequentialWorkflowManager with all dependencies
    workflow_manager = SequentialWorkflowManager(
        logger=mock_logger,
        config=mock_config,
        navigation_facade=mock_navigation_facade,
        docket_processor=mock_docket_processor,
        pacer_repository=mock_pacer_repository,
        async_dynamodb_storage=mock_async_dynamodb_storage
    )
    
    # Test initialization
    await workflow_manager.initialize()
    
    # Validate all dependencies are stored
    assert workflow_manager.navigation_facade is not None, "NavigationFacade not stored"
    assert workflow_manager.docket_processor is not None, "DocketProcessor not stored"
    assert workflow_manager.pacer_repository is not None, "PacerRepository not stored"
    assert workflow_manager.async_dynamodb_storage is not None, "AsyncDynamoDBStorage not stored"
    
    print("✅ SequentialWorkflowManager initialized with all dependencies")


@pytest.mark.asyncio
async def test_missing_dependency_validation():
    """Test that SequentialWorkflowManager fails properly when dependencies are missing."""
    print("\n=== Testing Missing Dependency Validation ===")
    
    # Create SequentialWorkflowManager with missing critical dependencies
    workflow_manager = SequentialWorkflowManager(
        logger=Mock(),
        config={"test": True},
        navigation_facade=None,  # MISSING
        docket_processor=None,   # MISSING
        pacer_repository=None,   # MISSING
        async_dynamodb_storage=None  # MISSING
    )
    
    # Test that initialization fails with proper error
    with pytest.raises(ValueError) as exc_info:
        await workflow_manager.initialize()
    
    error_message = str(exc_info.value)
    assert "Missing CRITICAL dependencies" in error_message, f"Expected dependency error, got: {error_message}"
    assert "NavigationFacade" in error_message, "NavigationFacade not mentioned in error"
    assert "DocketProcessor" in error_message, "DocketProcessor not mentioned in error"
    
    print("✅ Proper validation of missing dependencies")


@pytest.mark.asyncio
async def test_container_wiring():
    """Test that the container can be properly wired without errors."""
    print("\n=== Testing Container Wiring ===")
    
    # Create and configure container
    container = PacerContainer()
    
    # Configure with minimal settings
    test_config = {
        "headless": True,
        "timeout_ms": 30000,
        "test_mode": True
    }
    container.config.from_dict(test_config)
    
    # Override dependencies with mocks
    container.logger.override(Mock())
    
    # Create mock storage container
    mock_storage = Mock()
    mock_storage.async_dynamodb_storage = Mock()
    mock_storage.pacer_repository = Mock()
    mock_storage.s3_async_storage = Mock()
    container.storage_container.override(mock_storage)
    container.shutdown_event.override(asyncio.Event())
    
    # Test that key providers can be accessed without errors
    try:
        # Access navigation facade
        nav_facade = container.navigation_facade()
        assert nav_facade is not None, "NavigationFacade not accessible"
        
        # Access sequential docket processor
        seq_processor = container.sequential_docket_processor()
        assert seq_processor is not None, "SequentialDocketProcessor not accessible"
        
        # Access sequential workflow manager
        workflow_manager = container.sequential_workflow_manager()
        assert workflow_manager is not None, "SequentialWorkflowManager not accessible"
        
        print("✅ All key providers accessible through container")
        
    except Exception as e:
        pytest.fail(f"Container wiring failed: {e}")


if __name__ == "__main__":
    """Run the dependency injection validation tests."""
    import sys
    import logging
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    print("🔍 Running Dependency Injection Container Validation Tests...")
    
    # Run tests manually if not using pytest
    async def run_tests():
        try:
            print("\n" + "="*70)
            print("DEPENDENCY INJECTION VALIDATION TESTS")
            print("="*70)
            
            # Test 1: Storage Container Dependencies
            storage_container = StorageContainer()
            storage_container.async_dynamodb_storage.override(Mock())
            storage_container.pacer_repository.override(Mock())
            storage_container.s3_async_storage.override(Mock())
            await test_storage_container_dependencies(storage_container)
            
            # Test 2: Sequential Workflow Manager Initialization
            await test_sequential_workflow_manager_initialization()
            
            # Test 3: Missing Dependency Validation
            await test_missing_dependency_validation()
            
            print("\n" + "="*70)
            print("✅ ALL DEPENDENCY INJECTION TESTS PASSED")
            print("✅ Container setup is correctly configured")
            print("✅ All required dependencies are properly injected")
            print("="*70)
            
        except Exception as e:
            print(f"\n❌ DEPENDENCY INJECTION TEST FAILED: {e}")
            sys.exit(1)
    
    # Run the tests
    asyncio.run(run_tests())