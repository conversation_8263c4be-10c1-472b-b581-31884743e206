#!/usr/bin/env python3
"""
Test Storage Dependency Injection Fix

This test verifies that the storage dependency injection fix properly allows
SequentialWorkflowManager to receive PacerRepository and AsyncDynamoDBStorage.

CRITICAL TEST SCENARIOS:
1. MainServiceFactory creates container with storage dependencies
2. Storage dependencies are properly registered in StorageContainer
3. PacerCoreContainer properly injects storage into SequentialWorkflowManager
4. Dependency injection chain works end-to-end
"""

import asyncio
import logging
import os
import sys
import traceback
from typing import Dict, Any

# Ensure we can import from src
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.config_models.base import WorkflowConfig
from src.factories.main_factory import MainServiceFactory

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_storage_dependency_injection():
    """Test that storage dependencies are properly injected into SequentialWorkflowManager."""
    
    logger.info("🔍 Starting Storage Dependency Injection Test")
    
    # Create minimal config for testing
    test_config = WorkflowConfig(
        name="test_config",
        date="01/01/2024",
        headless=True,
        run_parallel=False,
        timeout_ms=30000,
        courts=['test'],
        start_date='01/01/2024',
        end_date='01/01/2024'
    )
    
    # Create shutdown event for testing
    shutdown_event = asyncio.Event()
    
    try:
        logger.info("🏗️ Creating MainServiceFactory...")
        async with MainServiceFactory(test_config, shutdown_event) as factory:
            logger.info("✅ MainServiceFactory created successfully")
            
            # Test 1: Verify storage container exists and has required dependencies
            logger.info("🔍 Test 1: Validating storage container...")
            storage_container = factory._container.storage
            assert storage_container is not None, "Storage container should exist"
            
            # Test storage dependencies are accessible
            async_storage = storage_container.async_dynamodb_storage()
            pacer_repo = storage_container.pacer_repository()
            s3_storage = storage_container.s3_async_storage()
            
            logger.info(f"✅ AsyncDynamoDBStorage: {type(async_storage).__name__}")
            logger.info(f"✅ PacerRepository: {type(pacer_repo).__name__}")
            logger.info(f"✅ S3AsyncStorage: {type(s3_storage).__name__}")
            
            # Test 2: Verify PACER container exists and has SequentialWorkflowManager
            logger.info("🔍 Test 2: Validating PACER container...")
            pacer_container = factory._container.pacer
            assert pacer_container is not None, "PACER container should exist"
            
            # Test that sequential workflow manager is accessible
            if hasattr(pacer_container, 'sequential_workflow_manager'):
                logger.info("✅ SequentialWorkflowManager provider found in PACER container")
                
                # Test 3: Try to create SequentialWorkflowManager instance
                logger.info("🔍 Test 3: Creating SequentialWorkflowManager instance...")
                try:
                    sequential_workflow = pacer_container.sequential_workflow_manager()
                    logger.info(f"✅ SequentialWorkflowManager created: {type(sequential_workflow).__name__}")
                    
                    # Test 4: Verify storage dependencies are injected
                    logger.info("🔍 Test 4: Checking storage dependency injection...")
                    
                    has_async_storage = hasattr(sequential_workflow, 'async_dynamodb_storage') and sequential_workflow.async_dynamodb_storage is not None
                    has_pacer_repo = hasattr(sequential_workflow, 'pacer_repository') and sequential_workflow.pacer_repository is not None
                    
                    logger.info(f"📦 AsyncDynamoDBStorage injected: {has_async_storage}")
                    logger.info(f"📦 PacerRepository injected: {has_pacer_repo}")
                    
                    if has_async_storage or has_pacer_repo:
                        logger.info("✅ SUCCESS: Storage dependencies are properly injected!")
                        return True
                    else:
                        logger.error("❌ FAILURE: Storage dependencies are NOT injected")
                        return False
                        
                except Exception as e:
                    logger.error(f"❌ FAILURE: Could not create SequentialWorkflowManager: {e}")
                    logger.error(f"Traceback: {traceback.format_exc()}")
                    return False
            else:
                logger.error("❌ FAILURE: SequentialWorkflowManager provider not found in PACER container")
                
                # List available providers for debugging
                available_providers = [attr for attr in dir(pacer_container) if not attr.startswith('_')]
                logger.info(f"Available PACER providers: {available_providers}")
                return False
                
    except Exception as e:
        logger.error(f"❌ CRITICAL FAILURE: MainServiceFactory setup failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


async def test_validated_factory():
    """Test the validated sequential workflow factory."""
    
    logger.info("🔍 Testing Validated Sequential Workflow Factory")
    
    # Create minimal config for testing
    test_config = WorkflowConfig(
        name="test_config",
        date="01/01/2024",
        headless=True,
        run_parallel=False,
        timeout_ms=30000,
        courts=['test'],
        start_date='01/01/2024',
        end_date='01/01/2024'
    )
    
    # Create shutdown event for testing
    shutdown_event = asyncio.Event()
    
    try:
        async with MainServiceFactory(test_config, shutdown_event) as factory:
            pacer_container = factory._container.pacer
            
            # Test the validated factory if it exists
            if hasattr(pacer_container, 'verified_sequential_workflow_factory'):
                logger.info("✅ Verified Sequential Workflow Factory found")
                
                try:
                    # Test the validated factory
                    validated_workflow = pacer_container.verified_sequential_workflow_factory()
                    logger.info(f"✅ Validated SequentialWorkflowManager created: {type(validated_workflow).__name__}")
                    
                    # Check storage injection
                    has_async_storage = hasattr(validated_workflow, 'async_dynamodb_storage') and validated_workflow.async_dynamodb_storage is not None
                    has_pacer_repo = hasattr(validated_workflow, 'pacer_repository') and validated_workflow.pacer_repository is not None
                    
                    logger.info(f"📦 Validated AsyncDynamoDBStorage injected: {has_async_storage}")
                    logger.info(f"📦 Validated PacerRepository injected: {has_pacer_repo}")
                    
                    return has_async_storage or has_pacer_repo
                    
                except Exception as e:
                    logger.error(f"❌ Validated factory failed: {e}")
                    logger.error(f"Traceback: {traceback.format_exc()}")
                    return False
            else:
                logger.warning("⚠️ Verified Sequential Workflow Factory not found")
                return False
                
    except Exception as e:
        logger.error(f"❌ Test setup failed: {e}")
        return False


async def main():
    """Run all storage dependency injection tests."""
    
    logger.info("🚀 STORAGE DEPENDENCY INJECTION TEST SUITE")
    logger.info("=" * 60)
    
    # Set required environment variables for testing
    os.environ.setdefault('AWS_REGION', 'us-west-2')
    os.environ.setdefault('S3_BUCKET_NAME', 'test-bucket')
    
    tests = [
        ("Basic Storage Dependency Injection", test_storage_dependency_injection),
        ("Validated Factory Test", test_validated_factory),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        logger.info("-" * 40)
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"💥 {test_name}: CRASHED - {e}")
            results[test_name] = False
    
    # Final results
    logger.info("\n" + "=" * 60)
    logger.info("🏁 FINAL TEST RESULTS")
    logger.info("=" * 60)
    
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\nSUMMARY: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED - Storage dependency injection is working!")
        return True
    else:
        logger.error("🚨 SOME TESTS FAILED - Storage dependency injection needs fixes")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)