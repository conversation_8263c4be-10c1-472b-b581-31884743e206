"""
Test script to validate the Sequential Workflow Manager pre-check implementation.

This test validates that the LOCAL ARTIFACT CHECK and DYNAMODB DUPLICATE CHECK
are properly implemented at the beginning of process_single_docket_sequential().
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from unittest.mock import Mock, AsyncMock, MagicMock
from datetime import datetime

from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager


class MockCourtLogger:
    """Mock court logger for testing."""
    
    def __init__(self):
        self.logs = []
    
    def info(self, message, *args, **kwargs):
        self.logs.append(('INFO', message))
        print(f"[COURT LOGGER INFO] {message}")
    
    def warning(self, message, *args, **kwargs):
        self.logs.append(('WARNING', message))
        print(f"[COURT LOGGER WARNING] {message}")
    
    def error(self, message, *args, **kwargs):
        self.logs.append(('ERROR', message))
        print(f"[COURT LOGGER ERROR] {message}")
    
    def debug(self, message, *args, **kwargs):
        self.logs.append(('DEBUG', message))
        print(f"[COURT LOGGER DEBUG] {message}")


class MockNavigator:
    """Mock navigator for testing."""
    
    def __init__(self):
        self.page = Mock()
        self.page.url = "https://ecf.cand.uscourts.gov/cgi-bin/iquery.pl"


async def test_sequential_workflow_pre_checks():
    """Test that pre-checks are properly implemented."""
    
    print("🧪 TESTING SEQUENTIAL WORKFLOW PRE-CHECKS")
    print("=" * 60)
    
    # Setup mock components
    court_logger = MockCourtLogger()
    navigator = MockNavigator()
    
    # Create sequential workflow manager
    workflow_manager = SequentialWorkflowManager(
        navigation_facade=Mock(),
        docket_processor=Mock(),
        state_validator=Mock(),
        return_manager=Mock(),
        court_logger=court_logger,
        logger=Mock(),
        config={}
    )
    
    # Test data
    court_id = "cand"
    docket_info = {
        'docket_num': '3:25-cv-43190',
        'case_title': 'Test Case v. Defendant',
        'versus': 'Test Case v. Defendant'
    }
    workflow_config = {
        'iso_date': '2025-08-10'
    }
    
    print(f"Court ID: {court_id}")
    print(f"Docket Number: {docket_info['docket_num']}")
    print(f"Case Title: {docket_info['case_title']}")
    print(f"ISO Date: {workflow_config['iso_date']}")
    print()
    
    try:
        # Call the method - this should trigger the pre-checks
        result = await workflow_manager.process_single_docket_sequential(
            court_id=court_id,
            docket_info=docket_info,
            navigator=navigator,
            workflow_config=workflow_config
        )
        
        print("✅ METHOD EXECUTION COMPLETED")
        print(f"Result Status: {result.get('status', 'unknown')}")
        print(f"Workflow Steps: {result.get('workflow_steps_completed', [])}")
        
        if 'local_artifact_check' in result:
            print(f"Local Artifact Check: {result['local_artifact_check']}")
        
        if 'dynamodb_duplicate_check' in result:
            print(f"DynamoDB Check: {result['dynamodb_duplicate_check']}")
        
        print()
        print("📋 COURT LOGGER OUTPUT:")
        print("-" * 40)
        for level, message in court_logger.logs:
            if "PRE-CHECK" in message:
                print(f"[{level}] {message}")
        
        # Validate that pre-checks were executed
        pre_check_logs = [msg for level, msg in court_logger.logs if "PRE-CHECK" in msg]
        
        if len(pre_check_logs) >= 2:
            print("\n✅ PRE-CHECKS VALIDATION:")
            print("✓ LOCAL ARTIFACT CHECK found in logs")
            print("✓ DYNAMODB DUPLICATE CHECK found in logs")
            print("✓ Pre-checks are properly implemented!")
        else:
            print("\n❌ PRE-CHECKS VALIDATION:")
            print(f"❌ Expected at least 2 PRE-CHECK log messages, found {len(pre_check_logs)}")
            print("❌ Pre-checks may not be properly implemented!")
        
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_sequential_workflow_pre_checks())