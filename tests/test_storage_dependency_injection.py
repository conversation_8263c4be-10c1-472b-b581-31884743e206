"""
Test Storage Dependency Injection Architecture

This test validates that storage dependencies properly flow through the container
hierarchy to reach SequentialWorkflowManager.
"""
import asyncio
import pytest
from unittest.mock import Mock, AsyncMock, patch
import os

# Mock environment variables needed for container setup
@pytest.fixture(autouse=True)
def mock_env():
    with patch.dict(os.environ, {
        'AWS_REGION': 'us-west-2',
        'S3_BUCKET_NAME': 'test-bucket',
        'AWS_ACCESS_KEY_ID': 'test-key',
        'AWS_SECRET_ACCESS_KEY': 'test-secret'
    }):
        yield

@pytest.fixture
def mock_config():
    """Create a mock config for testing."""
    config = Mock()
    config.headless = True
    config.run_parallel = True  
    config.timeout_ms = 60000
    return config

@pytest.mark.asyncio
async def test_storage_dependency_injection_flow(mock_config):
    """Test that storage dependencies flow through containers to SequentialWorkflowManager."""
    from src.containers.core import create_container
    
    # Create container with test configuration
    config_dict = {
        'aws_region': 'us-west-2',
        's3_bucket_name': 'test-bucket',
        'aws_access_key': 'test-key',
        'aws_secret_key': 'test-secret',
        'headless': True,
        'run_parallel': True,
        'timeout_ms': 60000,
        'pacer': {
            'headless': True,
            'run_parallel': True,
            'timeout_ms': 60000
        },
        'storage': {},
        'DATA_DIR': 'test_data'
    }
    
    container = create_container(config_dict)
    
    # Test storage container creation
    assert hasattr(container, 'storage'), "Storage container not found in main container"
    
    storage_container = container.storage
    
    # Test storage dependencies are accessible
    async_storage = storage_container.async_dynamodb_storage()
    assert async_storage is not None, "AsyncDynamoDBStorage not available"
    
    pacer_repo = storage_container.pacer_repository()
    assert pacer_repo is not None, "PacerRepository not available"
    
    s3_storage = storage_container.s3_async_storage()
    assert s3_storage is not None, "S3AsyncStorage not available"
    
    print("✅ All storage dependencies validated")

@pytest.mark.asyncio 
async def test_pacer_container_storage_access(mock_config):
    """Test that PACER container can access storage dependencies."""
    from src.containers.core import create_container
    
    config_dict = {
        'aws_region': 'us-west-2',
        's3_bucket_name': 'test-bucket', 
        'aws_access_key': 'test-key',
        'aws_secret_key': 'test-secret',
        'headless': True,
        'run_parallel': True,
        'timeout_ms': 60000,
        'pacer': {
            'headless': True,
            'run_parallel': True,
            'timeout_ms': 60000
        },
        'storage': {},
        'DATA_DIR': 'test_data'
    }
    
    container = create_container(config_dict)
    
    # Override shutdown_event dependency for PACER container
    shutdown_event = asyncio.Event()
    with container.pacer.shutdown_event.override(shutdown_event):
        
        # Test PACER container creation
        assert hasattr(container, 'pacer'), "PACER container not found in main container"
        
        pacer_container = container.pacer
        
        # Test DocketOrchestrator can be created with storage dependencies
        if hasattr(pacer_container, 'docket_orchestrator_facade'):
            docket_orchestrator = pacer_container.docket_orchestrator_facade()
            assert docket_orchestrator is not None, "DocketOrchestrator not available"
            
            # Verify storage dependencies are injected
            assert hasattr(docket_orchestrator, 'pacer_repository'), "DocketOrchestrator missing pacer_repository"
            assert hasattr(docket_orchestrator, 'async_dynamodb_storage'), "DocketOrchestrator missing async_dynamodb_storage"
            
            print("✅ DocketOrchestrator has storage dependencies")
        else:
            print("⚠️ DocketOrchestrator facade not found - container may need update")

@pytest.mark.asyncio
async def test_sequential_workflow_manager_dependency_injection():
    """Test that SequentialWorkflowManager can receive storage dependencies properly."""
    from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager
    
    # Create mock dependencies
    mock_logger = Mock()
    mock_config = {'test': 'config'}
    mock_navigation_facade = Mock()
    mock_docket_processor = Mock()
    mock_pacer_repository = Mock()
    mock_async_storage = Mock() 
    
    # Create SequentialWorkflowManager with dependencies
    manager = SequentialWorkflowManager(
        navigation_facade=mock_navigation_facade,
        docket_processor=mock_docket_processor,
        pacer_repository=mock_pacer_repository,
        async_dynamodb_storage=mock_async_storage,
        logger=mock_logger,
        config=mock_config
    )
    
    # Validate dependencies are set
    assert manager.pacer_repository == mock_pacer_repository, "PacerRepository not injected"
    assert manager.async_dynamodb_storage == mock_async_storage, "AsyncDynamoDBStorage not injected"
    assert manager.navigation_facade == mock_navigation_facade, "NavigationFacade not injected"
    assert manager.docket_processor == mock_docket_processor, "DocketProcessor not injected"
    
    print("✅ SequentialWorkflowManager correctly receives storage dependencies")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])