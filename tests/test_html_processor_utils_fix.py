#!/usr/bin/env python3
"""
Test to verify HTMLProcessorUtils fix for process_html_content method call.

This test verifies that HTMLProcessorUtils can properly handle both:
1. HTML integration service (with process_docket_html method)
2. Direct HTML processing service (with process_html_content method)
"""

import asyncio
import logging
from unittest.mock import AsyncMock, MagicMock
from typing import Dict, Any

from src.transformer.components.docket.html_processor_utils import HTMLProcessorUtils


class MockHTMLIntegrationService:
    """Mock HTML integration service with process_docket_html method."""
    
    def __init__(self):
        self.process_docket_html = AsyncMock(return_value=True)
        self.validate_html_processing_results = MagicMock(return_value={
            'changes': ['Added attorney information'],
            'total_changes': 1,
            'processing_successful': True
        })

    async def process_docket_html(self, data: dict, json_path: str, court_id: str) -> bool:
        return await self.process_docket_html(data, json_path, court_id)


class MockHTMLProcessingService:
    """Mock HTML processing service with process_html_content method."""
    
    def __init__(self):
        self.process_html_content = AsyncMock(return_value={'attorney': ['Mock Attorney']})
        self.validate_html_processing_results = MagicMock(return_value={
            'changes': ['Added attorney information'],
            'total_changes': 1,
            'processing_successful': True
        })

    async def process_html_content(self, case_details: dict, html_content: str, json_path: str) -> dict:
        return await self.process_html_content(case_details, html_content, json_path)


async def test_html_processor_utils_with_integration_service():
    """Test HTMLProcessorUtils with HTML integration service."""
    print("Testing HTMLProcessorUtils with HTML integration service...")
    
    # Create mock integration service
    mock_integration_service = MockHTMLIntegrationService()
    
    # Create HTMLProcessorUtils instance
    utils = HTMLProcessorUtils(
        html_processing_service=mock_integration_service,
        logger=logging.getLogger(__name__)
    )
    
    # Mock the _extract_s3_html_link and _download_html_content methods
    utils._extract_s3_html_link = MagicMock(return_value="https://s3.mock.com/test.html")
    utils._download_html_content = AsyncMock(return_value="<html>Mock HTML content</html>")
    
    # Test data with court_id
    test_data = {
        'court_id': 'ilnd',
        'docket_num': '1:23-cv-12345',
        'new_filename': 'test_case.json',
        's3_html': 'https://s3.mock.com/test.html'
    }
    
    # Test process_s3_html
    result = await utils.process_s3_html(test_data, '/path/to/test.json')
    
    print(f"Integration service test result: {result}")
    print("✓ Integration service test passed")
    
    return result


async def test_html_processor_utils_with_processing_service():
    """Test HTMLProcessorUtils with direct HTML processing service."""
    print("Testing HTMLProcessorUtils with direct HTML processing service...")
    
    # Create mock processing service
    mock_processing_service = MockHTMLProcessingService()
    
    # Create HTMLProcessorUtils instance
    utils = HTMLProcessorUtils(
        html_processing_service=mock_processing_service,
        logger=logging.getLogger(__name__)
    )
    
    # Mock the _extract_s3_html_link and _download_html_content methods
    utils._extract_s3_html_link = MagicMock(return_value="https://s3.mock.com/test.html")
    utils._download_html_content = AsyncMock(return_value="<html>Mock HTML content</html>")
    
    # Test data without court_id (for direct processing)
    test_data = {
        'docket_num': '1:23-cv-12345',
        'new_filename': 'test_case.json',
        's3_html': 'https://s3.mock.com/test.html'
    }
    
    # Test process_s3_html
    result = await utils.process_s3_html(test_data, '/path/to/test.json')
    
    print(f"Direct processing service test result: {result}")
    print("✓ Direct processing service test passed")
    
    return result


async def test_html_processor_utils_missing_court_id():
    """Test HTMLProcessorUtils with integration service but missing court_id."""
    print("Testing HTMLProcessorUtils with integration service but missing court_id...")
    
    # Create mock integration service
    mock_integration_service = MockHTMLIntegrationService()
    
    # Create HTMLProcessorUtils instance
    utils = HTMLProcessorUtils(
        html_processing_service=mock_integration_service,
        logger=logging.getLogger(__name__)
    )
    
    # Test data without court_id
    test_data = {
        'docket_num': '1:23-cv-12345',
        'new_filename': 'test_case.json',
        's3_html': 'https://s3.mock.com/test.html'
    }
    
    # Test process_s3_html - should return False due to missing court_id
    result = await utils.process_s3_html(test_data, '/path/to/test.json')
    
    print(f"Missing court_id test result: {result}")
    assert result is False, "Should return False when court_id is missing for integration service"
    print("✓ Missing court_id test passed")
    
    return result


async def main():
    """Run all tests."""
    print("Running HTMLProcessorUtils fix verification tests...\n")
    
    try:
        # Test with integration service
        await test_html_processor_utils_with_integration_service()
        print()
        
        # Test with direct processing service  
        await test_html_processor_utils_with_processing_service()
        print()
        
        # Test missing court_id scenario
        await test_html_processor_utils_missing_court_id()
        print()
        
        print("🎉 All tests passed! HTMLProcessorUtils fix is working correctly.")
        print("\nSummary:")
        print("- Fixed method call compatibility issue")
        print("- Added proper service type detection")
        print("- Added validate_html_processing_results method to integration service")
        print("- Handles both integration service and direct processing service")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())