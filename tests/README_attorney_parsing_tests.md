# Attorney Parsing Test Suite

## Overview

The `test_attorney_parsing_fix.py` test suite provides comprehensive validation of attorney extraction functionality from PACER HTML docket sheets. This suite ensures robust parsing across various case types, HTML structures, and edge cases.

## Test Coverage

### 1. Standard Case Testing
- **test_standard_case_attorney_extraction**: Tests extraction from standard civil cases with multiple attorneys per party
- **test_case_parser_service_direct_usage**: Validates direct usage of CaseParserService with simple HTML structures

### 2. Removal Case Handling
- **test_removal_case_detection**: Verifies proper detection of removal cases based on HTML content
- **test_is_removal_detection_positive**: Tests positive removal case detection with "Notice of Removal" cause
- **test_is_removal_detection_negative**: Tests negative removal case detection with standard causes

### 3. Terminology Variations
- **test_petitioner_respondent_terminology**: Validates parsing when HTML uses "Petitioner/Respondent" instead of "Plaintiff/Defendant"

### 4. Component Integration
- **test_html_parser_component_usage**: Tests the HtmlParser component wrapper around CaseParserService
- **test_invalid_action_handling**: Validates error handling for unknown actions
- **test_empty_html_content_handling**: Tests graceful handling of None, empty, or whitespace-only HTML
- **test_malformed_html_handling**: Ensures robustness with malformed HTML structures

### 5. Data Integrity
- **test_json_serialization_compatibility**: Verifies that parsed results can be serialized to and from JSON

### 6. Integration Testing
- **test_real_html_file_parsing**: Integration test using actual ILND docket sheet HTML file
- **test_real_removal_case_parsing**: Integration test using actual removal case HTML file

## Key Features Tested

### Attorney Extraction
- Multiple attorneys per party
- Proper law firm association
- Contact information parsing (email, phone, address)
- "Attorney to be Noticed" designation

### PRO SE Filtering
- Detection of PRO SE representation
- Proper filtering in removal cases (PRO SE plaintiffs should not appear as attorneys)
- Self-representation identification

### Party Representation
- Plaintiff vs. defendant attorney categorization
- Party name association with attorneys
- Deduplication of attorneys representing multiple parties

### Case Type Detection
- Removal case identification using multiple indicators:
  - "28:1442 Notice of Removal" cause
  - "Notice of Removal" text patterns
  - Case structure analysis

### Error Handling
- Graceful degradation with missing HTML elements
- Malformed HTML tolerance
- Empty content handling
- Invalid action protection

## Test Data Sources

### HTML Fixtures
The test suite uses realistic HTML fixtures based on actual PACER docket sheets:

1. **Standard Case HTML**: Representative of typical civil litigation with attorney representation
2. **Removal Case HTML**: Includes PRO SE plaintiffs and professional defendant attorneys
3. **Petitioner/Respondent HTML**: Uses alternative terminology for party designation
4. **Edge Case HTML**: Contains missing or incomplete attorney information

### Integration Files
- `/example_pacer/ilnd_docket_sheet.html`: Real PACER docket sheet from Illinois Northern District
- `/example_pacer/removal_case.html`: Real removal case with PRO SE representation

## Validation Criteria

### Basic Structure
All tests verify the presence of required fields:
- `case_info`: Case metadata and details
- `plaintiffs`: Array of plaintiff information
- `defendants`: Array of defendant information  
- `attorney`: Array of extracted attorney data

### Attorney Data Quality
Each attorney entry should contain:
- `attorney_name`: Full attorney name
- `law_firm`: Associated law firm or organization
- `represents`: Party type (plaintiff/defendant)
- Contact information when available

### PRO SE Handling
In removal cases:
- PRO SE plaintiffs should not appear in the attorney list
- Only professional legal counsel should be extracted
- Self-representation should be properly identified and filtered

## Running the Tests

```bash
# Run complete test suite
python -m pytest tests/test_attorney_parsing_fix.py -v

# Run specific test category
python -m pytest tests/test_attorney_parsing_fix.py::TestAttorneyParsing::test_removal_case_detection -v

# Run with coverage
python -m pytest tests/test_attorney_parsing_fix.py --cov=src.services.html.case_parser_service

# Run integration tests only
python -m pytest tests/test_attorney_parsing_fix.py -k "integration" -v
```

## Expected Outcomes

### Successful Test Run
- All 13 tests should pass
- No exceptions or crashes during parsing
- Proper data structure validation
- JSON serialization compatibility

### Performance Expectations
- Each test should complete within 1 second
- Memory usage should remain stable
- No resource leaks during parsing

## Error Scenarios Covered

1. **HTML Structure Variations**: Different table layouts, missing elements
2. **Content Gaps**: Missing attorney information, incomplete contact details
3. **Data Quality Issues**: Malformed names, invalid email addresses
4. **Edge Cases**: Empty representations, duplicate attorneys
5. **System Errors**: Network timeouts, file access issues

This comprehensive test suite ensures that the attorney parsing functionality is robust, reliable, and handles the full spectrum of PACER HTML variations encountered in production.