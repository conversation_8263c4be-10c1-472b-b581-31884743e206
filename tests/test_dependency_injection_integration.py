#!/usr/bin/env python3
"""
Integration Test for Dependency Injection Architecture Fix

This test validates that the dependency injection fix resolves the hard failure issue
by ensuring SequentialWorkflowManager can access required dependencies.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.factories.main_factory import MainServiceFactory
from src.config_models.base import WorkflowConfig


def create_test_config():
    """Create a minimal test configuration."""
    return {
        "headless": True,
        "run_parallel": True,
        "timeout_ms": 60000,
        "config_name": "dependency_injection_test",
        "aws_region": "us-west-2",
        "dynamodb_endpoint": None,
        "s3_bucket_name": "test-bucket",
        "aws_access_key": "",
        "aws_secret_key": "",
        "DATA_DIR": os.path.join(os.getcwd(), "data"),
        "log_dir": None,
        "storage": {},
        "pacer": {"headless": True, "run_parallel": True, "timeout_ms": 60000},
    }


async def test_dependency_injection_fix():
    """
    Test that demonstrates the dependency injection fix works.
    
    This test should complete successfully without sys.exit(1) if the fix is working.
    """
    print("🔍 Testing Dependency Injection Architecture Fix...")
    
    # Create test configuration
    config_dict = create_test_config()
    print(f"✅ Created test configuration: {config_dict.get('config_name')}")
    
    # Create shutdown event
    shutdown_event = asyncio.Event()
    
    try:
        # Create MainServiceFactory with test configuration
        print("🏗️  Creating MainServiceFactory...")
        factory = MainServiceFactory(config=config_dict, shutdown_event=shutdown_event)
        
        # Test container initialization
        print("🔧 Initializing dependency injection container...")
        async with factory:
            print("✅ MainServiceFactory initialized successfully")
            
            # Validate container exists
            assert factory._container is not None, "Container should be initialized"
            print("✅ DI Container created successfully")
            
            # Validate storage container
            assert hasattr(factory._container, 'storage'), "Storage container should exist"
            storage_container = factory._container.storage
            print("✅ Storage container accessible")
            
            # Validate storage dependencies
            required_storage_deps = ['async_dynamodb_storage', 'pacer_repository', 's3_async_storage']
            for dep_name in required_storage_deps:
                assert hasattr(storage_container, dep_name), f"Storage should have {dep_name}"
            print("✅ All storage dependencies registered")
            
            # Validate PACER container
            assert hasattr(factory._container, 'pacer'), "PACER container should exist"
            pacer_container = factory._container.pacer
            print("✅ PACER container accessible")
            
            # Validate PACER dependencies
            pacer_deps = ['sequential_workflow_manager', 'validated_sequential_workflow_factory']
            for dep_name in pacer_deps:
                if hasattr(pacer_container, dep_name):
                    print(f"✅ PACER dependency available: {dep_name}")
                else:
                    print(f"ℹ️  PACER dependency not found (may be optional): {dep_name}")
            
            # Try to access the sequential workflow manager
            if hasattr(pacer_container, 'sequential_workflow_manager'):
                try:
                    # This should NOT result in sys.exit(1) if dependencies are properly injected
                    sequential_manager_provider = pacer_container.sequential_workflow_manager
                    print("✅ SequentialWorkflowManager provider accessible")
                    
                    # Test that the provider is properly configured
                    assert sequential_manager_provider is not None
                    print("✅ SequentialWorkflowManager provider is not None")
                    
                except Exception as e:
                    print(f"⚠️  SequentialWorkflowManager provider access issue: {e}")
            
            print("🎉 Dependency injection architecture fix validation PASSED!")
            return True
            
    except SystemExit as e:
        print(f"❌ CRITICAL FAILURE: sys.exit({e.code}) called - dependency injection fix did NOT work!")
        return False
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_storage_dependency_validation():
    """Test storage dependency validation specifically."""
    print("🧪 Testing Storage Dependency Validation...")
    
    config_dict = create_test_config()
    shutdown_event = asyncio.Event()
    
    factory = MainServiceFactory(config=config_dict, shutdown_event=shutdown_event)
    
    # Test validation methods exist
    assert hasattr(factory, '_validate_storage_dependencies'), "Storage validation method should exist"
    assert hasattr(factory, '_validate_pacer_dependencies'), "PACER validation method should exist"
    print("✅ Validation methods exist")
    
    # Test that we can create the container and validate dependencies
    async with factory:
        # This should complete without errors if validation works
        print("✅ Storage dependency validation completed successfully")
        return True


def main():
    """Run all dependency injection tests."""
    async def run_all_tests():
        print("=" * 60)
        print("DEPENDENCY INJECTION ARCHITECTURE FIX VALIDATION")
        print("=" * 60)
        
        tests_passed = 0
        total_tests = 2
        
        # Test 1: Main dependency injection fix
        try:
            if await test_dependency_injection_fix():
                tests_passed += 1
                print("✅ Main dependency injection test PASSED")
            else:
                print("❌ Main dependency injection test FAILED")
        except Exception as e:
            print(f"❌ Main dependency injection test FAILED with exception: {e}")
        
        print("-" * 60)
        
        # Test 2: Storage dependency validation
        try:
            if await test_storage_dependency_validation():
                tests_passed += 1
                print("✅ Storage dependency validation test PASSED")
            else:
                print("❌ Storage dependency validation test FAILED")
        except Exception as e:
            print(f"❌ Storage dependency validation test FAILED with exception: {e}")
        
        print("=" * 60)
        print(f"RESULTS: {tests_passed}/{total_tests} tests passed")
        
        if tests_passed == total_tests:
            print("🎉 ALL TESTS PASSED - Dependency injection fix is working!")
            return True
        else:
            print("❌ SOME TESTS FAILED - Dependency injection fix needs more work")
            return False
    
    # Run tests
    try:
        if asyncio.run(run_all_tests()):
            sys.exit(0)
        else:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test runner failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()