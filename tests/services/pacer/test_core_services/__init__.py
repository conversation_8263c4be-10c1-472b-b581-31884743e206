# /tests/services/pacer/test_core_services/__init__.py
"""
Test suite for PACER Core Services (Phase 5 Refactoring).

This test suite provides comprehensive coverage for the 8 core services
that implement the 4-phase workflow described in docket_processing.md:

Core Services:
1. ConfigurationService - Configuration management
2. CaseProcessingService - Phase 1: HTML Content Processing  
3. CaseClassificationService - Phase 2: Relevance & Classification
4. CaseVerificationService - Phase 3: Case Verification
5. DownloadOrchestrationService - Phase 4: Download Workflow
6. FileOperationsService - Final Save Operations
7. RelevanceAnalysisService - Relevance determination logic
8. AnalyticsService - Processing metrics and reporting

Test Coverage:
- Unit tests for each service with mocked dependencies
- Integration tests for complete workflow through all phases
- Workflow compliance tests against docket_processing.md specifications
- Error handling and edge case scenarios
- Concurrent processing and performance testing
- AsyncServiceBase inheritance verification

Usage:
    pytest tests/services/pacer/test_core_services/ -v
    pytest tests/services/pacer/test_core_services/test_integration_workflow.py -v
    pytest tests/services/pacer/test_core_services/test_configuration_service.py -v
"""