# /tests/services/pacer/test_core_services/test_configuration_service.py
"""
Unit tests for ConfigurationService - Core Service #1
"""
import pytest
from unittest.mock import AsyncMock, Mock, patch
from pathlib import Path

from src.pacer._core_services.configuration.configuration_service import ConfigurationService
from src.infrastructure.protocols.exceptions import PacerServiceError


class TestConfigurationServiceUnit:
    """Unit tests for ConfigurationService with mocked dependencies."""

    @pytest.fixture
    def service(self, mock_json_loader, mock_logger, base_config):
        """Create ConfigurationService instance with mocked dependencies."""
        return ConfigurationService(
            json_loader=mock_json_loader,
            logger=mock_logger,
            config=base_config
        )

    def test_service_inherits_from_async_service_base(self, service):
        """Test that ConfigurationService inherits from AsyncServiceBase."""
        from src.infrastructure.patterns.component_base import AsyncServiceBase
        assert isinstance(service, AsyncServiceBase)

    def test_service_initialization(self, service, mock_json_loader):
        """Test service initialization with dependencies."""
        assert service._json_loader == mock_json_loader
        assert service._all_configs is None
        assert isinstance(service.config_dir, Path)

    @pytest.mark.asyncio
    async def test_initialize_service_success(self, service, mock_json_loader):
        """Test successful service initialization."""
        # Setup mock loader responses
        mock_json_loader.load_config.side_effect = [
            {"patterns": ["test"]},  # relevance
            {"timeout": 5000},       # stability  
            {"data_dir": "/tmp"},    # paths
            {"patterns": ["ignore"]}, # ignore_download
            {"defendants": ["Test"]}  # relevant_defendants
        ]
        
        await service.initialize()
        
        assert service._initialized
        assert service._all_configs is not None
        assert len(service._all_configs) == 5
        assert "relevance" in service._all_configs
        assert mock_json_loader.load_config.call_count == 5

    @pytest.mark.asyncio
    async def test_initialize_service_config_load_failure(self, service, mock_json_loader):
        """Test service initialization failure when config loading fails."""
        mock_json_loader.load_config.side_effect = FileNotFoundError("Config not found")
        
        with pytest.raises(PacerServiceError, match="Configuration loading failed"):
            await service.initialize()

    @pytest.mark.asyncio
    async def test_execute_action_get_all_configs(self, service, mock_json_loader):
        """Test execute_action with get_all_configs action."""
        # Setup
        await service.initialize()
        
        # Execute
        result = await service._execute_action({"action": "get_all_configs"})
        
        # Verify
        assert isinstance(result, dict)
        assert len(result) == 5

    @pytest.mark.asyncio
    async def test_execute_action_get_relevance_config(self, service):
        """Test execute_action with get_relevance_config action."""
        await service.initialize()
        
        result = await service._execute_action({"action": "get_relevance_config"})
        
        assert isinstance(result, dict)

    @pytest.mark.asyncio
    async def test_execute_action_unknown_action(self, service):
        """Test execute_action with unknown action raises error."""
        with pytest.raises(PacerServiceError, match="Unknown action"):
            await service._execute_action({"action": "invalid_action"})

    @pytest.mark.asyncio
    async def test_get_config_value_success(self, service, mock_json_loader):
        """Test successful config value retrieval."""
        # Setup nested config
        mock_json_loader.load_config.side_effect = [
            {"timeout": {"page_load": 5000, "navigation": 10000}},
            {},  # stability
            {},  # paths  
            {},  # ignore_download
            {}   # relevant_defendants
        ]
        await service.initialize()
        
        # Test dot notation access
        result = await service.get_config_value("relevance", "timeout.page_load")
        
        assert result == 5000

    @pytest.mark.asyncio
    async def test_get_config_value_not_found_returns_default(self, service):
        """Test get_config_value returns default when key not found."""
        await service.initialize()
        
        result = await service.get_config_value("relevance", "nonexistent.key", "default")
        
        assert result == "default"

    @pytest.mark.asyncio
    async def test_update_config_value_success(self, service):
        """Test successful config value update."""
        await service.initialize()
        
        await service.update_config_value("relevance", "new.setting", "value")
        
        result = await service.get_config_value("relevance", "new.setting")
        assert result == "value"

    @pytest.mark.asyncio
    async def test_reload_configs_success(self, service, mock_json_loader):
        """Test successful config reload."""
        await service.initialize()
        initial_configs = service._all_configs
        
        # Change mock return values
        mock_json_loader.load_config.side_effect = [
            {"updated": True},  # relevance
            {},  # stability
            {},  # paths
            {},  # ignore_download
            {}   # relevant_defendants
        ]
        
        result = await service.reload_configs()
        
        assert result != initial_configs
        assert result["relevance"]["updated"] is True

    def test_is_initialized_false_before_init(self, service):
        """Test is_initialized returns False before initialization."""
        assert not service.is_initialized()

    @pytest.mark.asyncio
    async def test_is_initialized_true_after_init(self, service):
        """Test is_initialized returns True after initialization."""
        await service.initialize()
        assert service.is_initialized()

    @pytest.mark.asyncio
    async def test_get_relevant_defendants_success(self, service, mock_json_loader):
        """Test successful retrieval of relevant defendants."""
        mock_json_loader.load_config.side_effect = [
            {},  # relevance
            {},  # stability
            {},  # paths
            {},  # ignore_download
            {"defendants": ["Defendant A", "Defendant B"]}  # relevant_defendants
        ]
        await service.initialize()
        
        result = await service.get_relevant_defendants()
        
        assert result == ["Defendant A", "Defendant B"]

    @pytest.mark.asyncio
    async def test_concurrent_config_access(self, service):
        """Test that concurrent config access works correctly."""
        import asyncio
        
        await service.initialize()
        
        # Simulate concurrent access
        tasks = [
            service.get_relevance_config(),
            service.get_stability_config(),
            service.get_paths_config()
        ]
        
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 3
        for result in results:
            assert isinstance(result, dict)


class TestConfigurationServiceIntegration:
    """Integration tests for ConfigurationService with real file system."""

    @pytest.fixture
    def real_json_loader(self):
        """Real JsonConfigLoader for integration testing."""
        from src.pacer._config_components.json_config_loader import JsonConfigLoader
        return JsonConfigLoader()

    @pytest.fixture
    def temp_config_dir(self, tmp_path):
        """Create temporary config directory with test files."""
        config_dir = tmp_path / "config"
        config_dir.mkdir()
        
        # Create test config files
        (config_dir / "relevance_config.json").write_text('''{
            "explicitly_relevant": ["PFAS", "AFFF"],
            "excluded_statutes": ["15:78"],
            "mdl_override": true
        }''')
        
        (config_dir / "stability_config.json").write_text('''{
            "timeout": {"page_load": 5000, "navigation": 10000}
        }''')
        
        (config_dir / "paths_config.json").write_text('''{
            "data_dir": "/tmp/pacer_data",
            "output_dir": "/tmp/pacer_output"
        }''')
        
        ignore_dir = config_dir / "ignore_download"
        ignore_dir.mkdir()
        (ignore_dir / "ignore_download.json").write_text('''{
            "patterns": [".*dismissed.*", ".*settled.*"]
        }''')
        
        defendants_dir = config_dir / "defendants"
        defendants_dir.mkdir()
        (defendants_dir / "relevant_defendants.json").write_text('''{
            "defendants": ["3M Company", "DuPont"]
        }''')
        
        return config_dir

    @pytest.mark.asyncio
    async def test_integration_load_real_configs(self, real_json_loader, mock_logger, temp_config_dir):
        """Test loading real configuration files."""
        config = {"config_dir": str(temp_config_dir)}
        service = ConfigurationService(real_json_loader, mock_logger, config)
        
        await service.initialize()
        
        # Test all configs loaded
        all_configs = await service.get_all_configs()
        assert len(all_configs) == 5
        
        # Test specific values
        relevance = await service.get_relevance_config()
        assert "PFAS" in relevance["explicitly_relevant"]
        assert relevance["mdl_override"] is True
        
        defendants = await service.get_relevant_defendants()
        assert "3M Company" in defendants


class TestConfigurationServiceErrorHandling:
    """Test error handling scenarios for ConfigurationService."""

    @pytest.mark.asyncio
    async def test_missing_config_file_error(self, mock_logger, base_config):
        """Test handling of missing configuration files."""
        from src.pacer._config_components.json_config_loader import JsonConfigLoader
        
        loader = JsonConfigLoader()
        config = {"config_dir": "/nonexistent/path"}
        service = ConfigurationService(loader, mock_logger, config)
        
        with pytest.raises(PacerServiceError):
            await service.initialize()

    @pytest.mark.asyncio
    async def test_invalid_json_error(self, mock_json_loader, mock_logger, base_config):
        """Test handling of invalid JSON in config files."""
        mock_json_loader.load_config.side_effect = ValueError("Invalid JSON")
        service = ConfigurationService(mock_json_loader, mock_logger, base_config)
        
        with pytest.raises(PacerServiceError, match="Configuration loading failed"):
            await service.initialize()

    @pytest.mark.asyncio
    async def test_partial_config_load_failure(self, mock_json_loader, mock_logger, base_config):
        """Test handling when some config files load and others fail."""
        def side_effect(path):
            if "relevance" in str(path):
                return {"patterns": ["test"]}
            else:
                raise FileNotFoundError("File not found")
        
        mock_json_loader.load_config.side_effect = side_effect
        service = ConfigurationService(mock_json_loader, mock_logger, base_config)
        
        with pytest.raises(PacerServiceError):
            await service.initialize()