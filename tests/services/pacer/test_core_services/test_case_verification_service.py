# /tests/services/pacer/test_core_services/test_case_verification_service.py
"""
Unit tests for CaseVerificationService - Core Service #4
This service handles Phase 3: Case Verification
"""
import pytest
from unittest.mock import AsyncMock, Mock

from src.pacer.case_verification_facade_service import CaseVerificationFacadeService
from src.infrastructure.protocols.exceptions import PacerServiceError


class TestCaseVerificationServiceUnit:
    """Unit tests for CaseVerificationService with mocked dependencies."""

    @pytest.fixture
    def mock_case_verifier(self):
        """Mock case verifier component.""" 
        verifier = AsyncMock()
        verifier.execute = AsyncMock(return_value={
            "should_download": True,
            "verification_status": "proceed",
            "exists_in_db": False,
            "local_files_exist": False
        })
        return verifier

    @pytest.fixture
    def service(self, mock_case_verifier, mock_logger, base_config):
        """Create CaseVerificationService instance with mocked dependencies."""
        return CaseVerificationFacadeService(
            case_verifier=mock_case_verifier,
            logger=mock_logger,
            config=base_config
        )

    def test_service_inherits_from_async_service_base(self, service):
        """Test that CaseVerificationService inherits from AsyncServiceBase."""
        from src.infrastructure.patterns.component_base import AsyncServiceBase
        assert isinstance(service, AsyncServiceBase)

    def test_service_initialization_with_dependencies(self, service, mock_case_verifier):
        """Test service initialization with required dependencies."""
        assert service._verifier == mock_case_verifier

    @pytest.mark.asyncio
    async def test_execute_method_routes_to_execute_action(self, service, sample_case_details):
        """Test that execute method properly routes to _execute_action."""
        data = {
            "action": "verify_case",
            "case_details": sample_case_details,
            "is_explicitly_requested": False
        }
        
        with pytest.patch.object(service, '_execute_action') as mock_execute_action:
            mock_execute_action.return_value = {"verified": True}
            
            result = await service.execute(data)
            
            assert result == {"verified": True}
            mock_execute_action.assert_called_once_with(data)

    @pytest.mark.asyncio
    async def test_execute_action_verify_case(self, service, sample_case_details):
        """Test _execute_action with verify_case action."""
        with pytest.patch.object(service, 'verify_case') as mock_verify:
            mock_verify.return_value = {
                "should_download": True,
                "verification_status": "proceed",
                "reason": "new case, no existing files"
            }
            
            data = {
                "action": "verify_case",
                "case_details": sample_case_details,
                "is_explicitly_requested": False
            }
            
            result = await service._execute_action(data)
            
            assert result["should_download"] is True
            mock_verify.assert_called_once_with(sample_case_details, False)

    @pytest.mark.asyncio
    async def test_execute_action_check_database(self, service, sample_case_details):
        """Test _execute_action with check_database action."""
        with pytest.patch.object(service, 'check_database') as mock_check_db:
            mock_check_db.return_value = {
                "exists": False,
                "gsi_record": None
            }
            
            data = {
                "action": "check_database",
                "case_details": sample_case_details
            }
            
            result = await service._execute_action(data)
            
            assert result["exists"] is False
            mock_check_db.assert_called_once_with(sample_case_details)

    @pytest.mark.asyncio
    async def test_execute_action_check_local_files(self, service, sample_case_details):
        """Test _execute_action with check_local_files action."""
        with pytest.patch.object(service, 'check_local_files') as mock_check_local:
            mock_check_local.return_value = {
                "pdf_exists": False,
                "zip_exists": False,
                "any_files": False,
                "file_count": 0
            }
            
            data = {
                "action": "check_local_files",
                "case_details": sample_case_details,
                "check_type": "pdf_zip_only"
            }
            
            result = await service._execute_action(data)
            
            assert result["any_files"] is False
            mock_check_local.assert_called_once_with(sample_case_details, "pdf_zip_only")

    @pytest.mark.asyncio
    async def test_execute_action_unknown_action_raises_error(self, service):
        """Test that unknown action raises PacerServiceError."""
        with pytest.raises(PacerServiceError, match="Unknown action"):
            await service._execute_action({"action": "invalid_action"})

    @pytest.mark.asyncio
    async def test_phase_3_explicitly_requested_verification(self, service, sample_case_details):
        """Test Phase 3 workflow for explicitly requested cases (P3_2 -> P3_3)."""
        data = {
            "action": "verify_case",
            "case_details": sample_case_details,
            "is_explicitly_requested": True
        }
        
        with pytest.patch.object(service, 'verify_case') as mock_verify:
            # P3_5: Check Database GSI
            # P3_6: Exists in DB? -> No
            # P3_7: Check Local Artifacts Only PDF/ZIP
            # P3_8: Artifacts Exist? -> No -> P3_PROCESS
            mock_verify.return_value = {
                "verification_type": "explicitly_requested",
                "db_checked": True,
                "exists_in_db": False,
                "local_artifacts_checked": "pdf_zip_only",
                "artifacts_exist": False,
                "should_download": True,
                "verification_status": "proceed"
            }
            
            result = await service._execute_action(data)
            
            assert result["verification_type"] == "explicitly_requested"
            assert result["should_download"] is True
            assert result["verification_status"] == "proceed"

    @pytest.mark.asyncio
    async def test_phase_3_explicitly_requested_exists_in_db(self, service, sample_case_details):
        """Test Phase 3 explicitly requested - case exists in DB (P3_6 -> P3_SKIP)."""
        data = {
            "action": "verify_case", 
            "case_details": sample_case_details,
            "is_explicitly_requested": True
        }
        
        with pytest.patch.object(service, 'verify_case') as mock_verify:
            mock_verify.return_value = {
                "verification_type": "explicitly_requested",
                "db_checked": True,
                "exists_in_db": True,
                "gsi_record": {"case_id": "12345", "last_updated": "2024-01-01"},
                "should_download": False,
                "verification_status": "skip",
                "reason": "already in database"
            }
            
            result = await service._execute_action(data)
            
            assert result["should_download"] is False
            assert result["verification_status"] == "skip"
            assert "already in database" in result["reason"]

    @pytest.mark.asyncio
    async def test_phase_3_explicitly_requested_local_artifacts_exist(self, service, sample_case_details):
        """Test Phase 3 explicitly requested - local artifacts exist (P3_8 -> P3_SKIP)."""
        data = {
            "action": "verify_case",
            "case_details": sample_case_details,
            "is_explicitly_requested": True
        }
        
        with pytest.patch.object(service, 'verify_case') as mock_verify:
            mock_verify.return_value = {
                "verification_type": "explicitly_requested",
                "db_checked": True,
                "exists_in_db": False,
                "local_artifacts_checked": "pdf_zip_only",
                "artifacts_exist": True,
                "artifact_files": ["docket.pdf", "case.zip"],
                "should_download": False,
                "verification_status": "skip",
                "reason": "local artifacts exist"
            }
            
            result = await service._execute_action(data)
            
            assert result["should_download"] is False
            assert result["verification_status"] == "skip"
            assert result["artifacts_exist"] is True

    @pytest.mark.asyncio
    async def test_phase_3_report_scraped_verification(self, service, sample_case_details):
        """Test Phase 3 workflow for report-scraped cases (P3_2 -> P3_4)."""
        data = {
            "action": "verify_case",
            "case_details": sample_case_details,
            "is_explicitly_requested": False
        }
        
        with pytest.patch.object(service, 'verify_case') as mock_verify:
            # P3_9: Check Database GSI
            # P3_10: Exists in DB? -> No
            # P3_11: Check All Local Files  
            # P3_12: Any Files Exist? -> No -> P3_PROCESS
            mock_verify.return_value = {
                "verification_type": "report_scraped",
                "db_checked": True,
                "exists_in_db": False,
                "local_files_checked": "all",
                "any_files_exist": False,
                "should_download": True,
                "verification_status": "proceed"
            }
            
            result = await service._execute_action(data)
            
            assert result["verification_type"] == "report_scraped"
            assert result["should_download"] is True
            assert result["local_files_checked"] == "all"

    @pytest.mark.asyncio
    async def test_phase_3_report_scraped_exists_in_db(self, service, sample_case_details):
        """Test Phase 3 report-scraped - case exists in DB (P3_10 -> P3_SKIP)."""
        data = {
            "action": "verify_case",
            "case_details": sample_case_details, 
            "is_explicitly_requested": False
        }
        
        with pytest.patch.object(service, 'verify_case') as mock_verify:
            mock_verify.return_value = {
                "verification_type": "report_scraped",
                "db_checked": True,
                "exists_in_db": True,
                "gsi_record": {"case_id": "12345"},
                "should_download": False,
                "verification_status": "skip",
                "reason": "already in database"
            }
            
            result = await service._execute_action(data)
            
            assert result["should_download"] is False
            assert result["verification_status"] == "skip"

    @pytest.mark.asyncio
    async def test_phase_3_report_scraped_local_files_exist(self, service, sample_case_details):
        """Test Phase 3 report-scraped - local files exist (P3_12 -> P3_SKIP).""" 
        data = {
            "action": "verify_case",
            "case_details": sample_case_details,
            "is_explicitly_requested": False
        }
        
        with pytest.patch.object(service, 'verify_case') as mock_verify:
            mock_verify.return_value = {
                "verification_type": "report_scraped",
                "db_checked": True,
                "exists_in_db": False,
                "local_files_checked": "all",
                "any_files_exist": True,
                "existing_files": ["docket.html", "case.json", "metadata.txt"],
                "should_download": False,
                "verification_status": "skip",
                "reason": "local files exist"
            }
            
            result = await service._execute_action(data)
            
            assert result["should_download"] is False
            assert result["any_files_exist"] is True
            assert len(result["existing_files"]) == 3

    @pytest.mark.asyncio
    async def test_database_connectivity_check(self, service, sample_case_details):
        """Test database connectivity during verification."""
        data = {
            "action": "check_database",
            "case_details": sample_case_details
        }
        
        with pytest.patch.object(service, 'check_database') as mock_check_db:
            mock_check_db.return_value = {
                "db_connected": True,
                "exists": False,
                "query_time_ms": 45
            }
            
            result = await service._execute_action(data)
            
            assert result["db_connected"] is True
            assert result["query_time_ms"] == 45

    @pytest.mark.asyncio
    async def test_local_file_system_check(self, service, sample_case_details):
        """Test local file system verification."""
        data = {
            "action": "check_local_files",
            "case_details": sample_case_details,
            "check_type": "all"
        }
        
        with pytest.patch.object(service, 'check_local_files') as mock_check_local:
            mock_check_local.return_value = {
                "path_accessible": True,
                "directory_exists": True,
                "files_found": ["case.json"],
                "total_size_bytes": 1024,
                "last_modified": "2024-01-01T00:00:00Z"
            }
            
            result = await service._execute_action(data)
            
            assert result["path_accessible"] is True
            assert result["total_size_bytes"] == 1024

    @pytest.mark.asyncio
    async def test_concurrent_verification_requests(self, service):
        """Test handling multiple concurrent verification requests."""
        import asyncio
        
        # Create multiple verification tasks
        cases = [
            {"case_number": f"1:23-cv-1234{i}", "case_name": f"Case {i}"}
            for i in range(3)
        ]
        
        tasks = []
        for case in cases:
            data = {
                "action": "verify_case",
                "case_details": case,
                "is_explicitly_requested": False
            }
            tasks.append(service._execute_action(data))
        
        with pytest.patch.object(service, 'verify_case') as mock_verify:
            mock_verify.return_value = {
                "should_download": True,
                "verification_status": "proceed"
            }
            
            results = await asyncio.gather(*tasks)
            
            assert len(results) == 3
            assert all(result["should_download"] for result in results)
            assert mock_verify.call_count == 3


class TestCaseVerificationServiceErrorHandling:
    """Test error handling scenarios for CaseVerificationService."""

    @pytest.fixture
    def service_with_failing_verifier(self, mock_logger, base_config):
        """Create service with failing verifier component."""
        failing_verifier = AsyncMock()
        failing_verifier.execute.side_effect = Exception("Verification failed")
        
        return CaseVerificationFacadeService(
            case_verifier=failing_verifier,
            logger=mock_logger,
            config=base_config
        )

    @pytest.mark.asyncio
    async def test_verifier_failure_handling(self, service_with_failing_verifier, sample_case_details):
        """Test handling of verifier component failure."""
        data = {
            "action": "verify_case",
            "case_details": sample_case_details,
            "is_explicitly_requested": False
        }
        
        with pytest.patch.object(service_with_failing_verifier, 'verify_case') as mock_verify:
            mock_verify.side_effect = Exception("Verification failed")
            
            with pytest.raises(Exception, match="Verification failed"):
                await service_with_failing_verifier._execute_action(data)

    @pytest.mark.asyncio
    async def test_database_connection_failure(self, service, sample_case_details):
        """Test handling of database connection failure."""
        data = {
            "action": "check_database",
            "case_details": sample_case_details
        }
        
        with pytest.patch.object(service, 'check_database') as mock_check_db:
            mock_check_db.side_effect = ConnectionError("Database unavailable")
            
            with pytest.raises(ConnectionError):
                await service._execute_action(data)

    @pytest.mark.asyncio
    async def test_file_system_permission_error(self, service, sample_case_details):
        """Test handling of file system permission errors."""
        data = {
            "action": "check_local_files",
            "case_details": sample_case_details,
            "check_type": "all"
        }
        
        with pytest.patch.object(service, 'check_local_files') as mock_check_local:
            mock_check_local.side_effect = PermissionError("Access denied")
            
            with pytest.raises(PermissionError):
                await service._execute_action(data)

    @pytest.mark.asyncio
    async def test_missing_case_details_for_verification(self, service):
        """Test handling of missing case details."""
        data = {
            "action": "verify_case",
            "is_explicitly_requested": False
            # Missing case_details
        }
        
        with pytest.raises(KeyError):
            await service._execute_action(data)

    @pytest.mark.asyncio
    async def test_invalid_verification_configuration(self, service, sample_case_details):
        """Test handling of invalid verification configuration."""
        data = {
            "action": "verify_case",
            "case_details": sample_case_details,
            "is_explicitly_requested": False
        }
        
        with pytest.patch.object(service, 'verify_case') as mock_verify:
            mock_verify.side_effect = KeyError("Verification rules not configured")
            
            with pytest.raises(KeyError):
                await service._execute_action(data)