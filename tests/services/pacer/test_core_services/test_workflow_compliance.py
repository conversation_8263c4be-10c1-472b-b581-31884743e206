# /tests/services/pacer/test_core_services/test_workflow_compliance.py
"""
Workflow compliance tests that verify execution order matches docket_processing.md
Tests the decision tree and phase transitions exactly as documented
"""
import pytest
from unittest.mock import AsyncMock, Mock, call, patch
from typing import List, Dict, Any


class TestWorkflowComplianceDetailed:
    """Detailed compliance tests against docket_processing.md workflow."""

    @pytest.fixture
    def workflow_tracker(self):
        """Track workflow execution for compliance verification."""
        class WorkflowTracker:
            def __init__(self):
                self.execution_log = []
                self.decision_points = []
                self.phase_transitions = []
            
            def log_step(self, step: str, data: Dict = None):
                self.execution_log.append({
                    "step": step,
                    "timestamp": len(self.execution_log),
                    "data": data or {}
                })
            
            def log_decision(self, condition: str, result: bool, next_step: str):
                self.decision_points.append({
                    "condition": condition,
                    "result": result,
                    "next_step": next_step
                })
            
            def log_phase_transition(self, from_phase: str, to_phase: str):
                self.phase_transitions.append({
                    "from": from_phase,
                    "to": to_phase
                })
            
            def verify_execution_order(self, expected_steps: List[str]) -> bool:
                actual_steps = [entry["step"] for entry in self.execution_log]
                return actual_steps == expected_steps
            
            def verify_decision_path(self, expected_decisions: List[Dict]) -> bool:
                return self.decision_points == expected_decisions
        
        return WorkflowTracker()

    @pytest.mark.asyncio
    async def test_phase_1_complete_step_sequence(self, workflow_tracker):
        """Test Phase 1 executes all steps in correct order per docket_processing.md."""
        # Expected Phase 1 sequence from docket_processing.md:
        # P1_1 -> P1_2 -> P1_3 -> P1_4 -> P1_5 -> P1_6 -> P1_7 -> P1_8 -> P1_9 -> PHASE2
        expected_steps = [
            "P1_1_process_case_html",
            "P1_2_wait_page_stability", 
            "P1_3_validate_html_content",
            "P1_4_html_valid_check",
            "P1_5_extract_case_details",
            "P1_6_process_mdl_flags",
            "P1_7_check_removal_notice",
            "P1_8_generate_base_filename",
            "P1_9_add_processing_metadata",
            "PHASE2_transition"
        ]
        
        # Mock Phase 1 execution
        for step in expected_steps:
            workflow_tracker.log_step(step)
        
        # Verify execution order
        assert workflow_tracker.verify_execution_order(expected_steps)

    @pytest.mark.asyncio
    async def test_phase_2_mdl_flag_decision_branch(self, workflow_tracker):
        """Test Phase 2 MDL flag decision branch (P2_2 -> P2_3)."""
        # P2_1: Relevance Service.determine_case_relevance
        workflow_tracker.log_step("P2_1_determine_case_relevance")
        
        # P2_2: Has MDL Flag? -> YES -> P2_3
        workflow_tracker.log_decision("has_mdl_flag", True, "P2_3_set_relevant_mdl")
        workflow_tracker.log_step("P2_3_set_relevant_mdl")
        
        # P2_CLASSIFY: Classification Service.classify_case
        workflow_tracker.log_step("P2_CLASSIFY_classify_case")
        
        expected_decisions = [{
            "condition": "has_mdl_flag",
            "result": True, 
            "next_step": "P2_3_set_relevant_mdl"
        }]
        
        assert workflow_tracker.verify_decision_path(expected_decisions)

    @pytest.mark.asyncio
    async def test_phase_2_ignore_download_decision_branch(self, workflow_tracker):
        """Test Phase 2 ignore download pattern branch (P2_4 -> P2_5)."""
        workflow_tracker.log_step("P2_1_determine_case_relevance")
        
        # P2_2: Has MDL Flag? -> NO -> P2_4
        workflow_tracker.log_decision("has_mdl_flag", False, "P2_4_check_ignore_pattern")
        
        # P2_4: Matches ignore_download Pattern? -> YES -> P2_5
        workflow_tracker.log_decision("matches_ignore_download", True, "P2_5_set_not_relevant_ignore")
        workflow_tracker.log_step("P2_5_set_not_relevant_ignore")
        
        expected_decisions = [
            {"condition": "has_mdl_flag", "result": False, "next_step": "P2_4_check_ignore_pattern"},
            {"condition": "matches_ignore_download", "result": True, "next_step": "P2_5_set_not_relevant_ignore"}
        ]
        
        assert workflow_tracker.verify_decision_path(expected_decisions)

    @pytest.mark.asyncio
    async def test_phase_2_excluded_statute_decision_branch(self, workflow_tracker):
        """Test Phase 2 excluded statute branch (P2_6 -> P2_7)."""
        workflow_tracker.log_step("P2_1_determine_case_relevance")
        
        # P2_2: Has MDL Flag? -> NO
        workflow_tracker.log_decision("has_mdl_flag", False, "P2_4_check_ignore_pattern")
        
        # P2_4: Matches ignore_download Pattern? -> NO -> P2_6
        workflow_tracker.log_decision("matches_ignore_download", False, "P2_6_check_excluded_statute")
        
        # P2_6: Excluded by Statute? -> YES -> P2_7
        workflow_tracker.log_decision("excluded_by_statute", True, "P2_7_set_not_relevant_excluded") 
        workflow_tracker.log_step("P2_7_set_not_relevant_excluded")
        
        expected_decisions = [
            {"condition": "has_mdl_flag", "result": False, "next_step": "P2_4_check_ignore_pattern"},
            {"condition": "matches_ignore_download", "result": False, "next_step": "P2_6_check_excluded_statute"},
            {"condition": "excluded_by_statute", "result": True, "next_step": "P2_7_set_not_relevant_excluded"}
        ]
        
        assert workflow_tracker.verify_decision_path(expected_decisions)

    @pytest.mark.asyncio
    async def test_phase_2_explicitly_relevant_decision_branch(self, workflow_tracker):
        """Test Phase 2 explicitly relevant branch (P2_8 -> P2_9).""" 
        workflow_tracker.log_step("P2_1_determine_case_relevance")
        
        # Decision chain: No MDL, No ignore pattern, No excluded statute
        workflow_tracker.log_decision("has_mdl_flag", False, "P2_4_check_ignore_pattern")
        workflow_tracker.log_decision("matches_ignore_download", False, "P2_6_check_excluded_statute")
        workflow_tracker.log_decision("excluded_by_statute", False, "P2_8_check_explicitly_relevant")
        
        # P2_8: Explicitly Relevant? -> YES -> P2_9
        workflow_tracker.log_decision("explicitly_relevant", True, "P2_9_set_relevant_explicit")
        workflow_tracker.log_step("P2_9_set_relevant_explicit")
        
        # Verify final decision was explicitly relevant
        final_decision = workflow_tracker.decision_points[-1]
        assert final_decision["condition"] == "explicitly_relevant"
        assert final_decision["result"] is True

    @pytest.mark.asyncio
    async def test_phase_2_default_not_relevant_branch(self, workflow_tracker):
        """Test Phase 2 default not relevant branch (P2_8 -> P2_10)."""
        workflow_tracker.log_step("P2_1_determine_case_relevance")
        
        # Decision chain leading to default
        workflow_tracker.log_decision("has_mdl_flag", False, "P2_4_check_ignore_pattern")
        workflow_tracker.log_decision("matches_ignore_download", False, "P2_6_check_excluded_statute")
        workflow_tracker.log_decision("excluded_by_statute", False, "P2_8_check_explicitly_relevant")
        
        # P2_8: Explicitly Relevant? -> NO -> P2_10 (default not relevant)
        workflow_tracker.log_decision("explicitly_relevant", False, "P2_10_set_not_relevant_default")
        workflow_tracker.log_step("P2_10_set_not_relevant_default")
        
        # Verify default path taken
        final_step = workflow_tracker.execution_log[-1]
        assert final_step["step"] == "P2_10_set_not_relevant_default"

    @pytest.mark.asyncio
    async def test_phase_3_explicitly_requested_verification_path(self, workflow_tracker):
        """Test Phase 3 explicitly requested verification path (P3_2 -> P3_3)."""
        workflow_tracker.log_step("P3_1_verification_service_verify_case")
        
        # P3_2: is_explicitly_requested? -> YES -> P3_3
        workflow_tracker.log_decision("is_explicitly_requested", True, "P3_3_explicitly_requested_verification")
        workflow_tracker.log_step("P3_3_explicitly_requested_verification")
        
        # P3_5: Check Database GSI
        workflow_tracker.log_step("P3_5_check_database_gsi")
        
        # P3_6: Exists in DB? -> NO -> P3_7
        workflow_tracker.log_decision("exists_in_db", False, "P3_7_check_local_artifacts_pdf_zip")
        workflow_tracker.log_step("P3_7_check_local_artifacts_pdf_zip")
        
        # P3_8: Artifacts Exist? -> NO -> P3_PROCESS
        workflow_tracker.log_decision("artifacts_exist", False, "P3_PROCESS_proceed_download")
        workflow_tracker.log_step("P3_PROCESS_proceed_download")
        
        # Verify explicitly requested path
        verification_decision = workflow_tracker.decision_points[0]
        assert verification_decision["condition"] == "is_explicitly_requested"
        assert verification_decision["result"] is True

    @pytest.mark.asyncio
    async def test_phase_3_report_scraped_verification_path(self, workflow_tracker):
        """Test Phase 3 report-scraped verification path (P3_2 -> P3_4)."""
        workflow_tracker.log_step("P3_1_verification_service_verify_case")
        
        # P3_2: is_explicitly_requested? -> NO -> P3_4
        workflow_tracker.log_decision("is_explicitly_requested", False, "P3_4_report_scraped_verification")
        workflow_tracker.log_step("P3_4_report_scraped_verification")
        
        # P3_9: Check Database GSI  
        workflow_tracker.log_step("P3_9_check_database_gsi")
        
        # P3_10: Exists in DB? -> NO -> P3_11
        workflow_tracker.log_decision("exists_in_db", False, "P3_11_check_all_local_files")
        workflow_tracker.log_step("P3_11_check_all_local_files")
        
        # P3_12: Any Files Exist? -> NO -> P3_PROCESS
        workflow_tracker.log_decision("any_files_exist", False, "P3_PROCESS_proceed_download")
        workflow_tracker.log_step("P3_PROCESS_proceed_download")
        
        # Verify report-scraped path
        verification_decision = workflow_tracker.decision_points[0] 
        assert verification_decision["condition"] == "is_explicitly_requested"
        assert verification_decision["result"] is False

    @pytest.mark.asyncio
    async def test_phase_3_skip_scenarios(self, workflow_tracker):
        """Test Phase 3 skip scenarios (P3_6/P3_10 -> P3_SKIP, P3_8/P3_12 -> P3_SKIP)."""
        # Scenario 1: Exists in DB -> P3_SKIP
        workflow_tracker.log_step("P3_5_check_database_gsi")
        workflow_tracker.log_decision("exists_in_db", True, "P3_SKIP_already_in_database")
        workflow_tracker.log_step("P3_SKIP_already_in_database")
        workflow_tracker.log_step("SAVE_ONLY_metadata_save")
        
        # Reset tracker for second scenario
        workflow_tracker.execution_log.clear()
        workflow_tracker.decision_points.clear()
        
        # Scenario 2: Local files exist -> P3_SKIP
        workflow_tracker.log_step("P3_11_check_all_local_files")
        workflow_tracker.log_decision("any_files_exist", True, "P3_SKIP_local_files_exist")
        workflow_tracker.log_step("P3_SKIP_local_files_exist")
        workflow_tracker.log_step("SAVE_ONLY_metadata_save")
        
        # Verify skip paths lead to metadata-only save
        final_step = workflow_tracker.execution_log[-1]
        assert final_step["step"] == "SAVE_ONLY_metadata_save"

    @pytest.mark.asyncio
    async def test_phase_4_download_workflow_complete_sequence(self, workflow_tracker):
        """Test Phase 4 complete download workflow sequence."""
        # Expected Phase 4 sequence from docket_processing.md:
        # P4_1 -> P4_2 -> P4_3 -> P4_4 -> P4_5 -> P4_6 -> P4_SUCCESS -> P4_SAVE_SUCCESS -> FINAL_SAVE
        expected_steps = [
            "P4_1_download_orchestration_process_workflow",
            "P4_2_validate_case_for_download",
            "P4_3_should_attempt_download",
            "P4_4_prepare_download_context",
            "P4_5_execute_document_download", 
            "P4_6_download_success_check",
            "P4_SUCCESS_handle_download_success",
            "P4_SAVE_SUCCESS_add_success_metadata",
            "FINAL_SAVE_file_operations_save_upload"
        ]
        
        # Log successful download path
        for step in expected_steps:
            workflow_tracker.log_step(step)
        
        # Log key decisions
        workflow_tracker.log_decision("should_attempt_download", True, "P4_4_prepare_download_context")
        workflow_tracker.log_decision("download_success", True, "P4_SUCCESS_handle_download_success")
        
        # Verify complete sequence
        assert workflow_tracker.verify_execution_order(expected_steps)

    @pytest.mark.asyncio
    async def test_phase_4_download_skip_scenario(self, workflow_tracker):
        """Test Phase 4 download skip scenario (P4_3 -> P4_SKIP)."""
        workflow_tracker.log_step("P4_1_download_orchestration_process_workflow")
        workflow_tracker.log_step("P4_2_validate_case_for_download")
        
        # P4_3: Should Attempt Download? -> NO -> P4_SKIP
        workflow_tracker.log_decision("should_attempt_download", False, "P4_SKIP_skip_download")
        workflow_tracker.log_step("P4_SKIP_skip_download")
        workflow_tracker.log_step("P4_SAVE_add_skip_metadata")
        workflow_tracker.log_step("FINAL_SAVE_file_operations_save_upload")
        
        # Verify skip decision and flow
        skip_decision = workflow_tracker.decision_points[0]
        assert skip_decision["condition"] == "should_attempt_download"
        assert skip_decision["result"] is False

    @pytest.mark.asyncio
    async def test_phase_4_download_failure_scenario(self, workflow_tracker):
        """Test Phase 4 download failure scenario (P4_6 -> P4_FAIL)."""
        workflow_tracker.log_step("P4_1_download_orchestration_process_workflow")
        workflow_tracker.log_step("P4_2_validate_case_for_download")
        
        # Download attempt made but fails
        workflow_tracker.log_decision("should_attempt_download", True, "P4_4_prepare_download_context")
        workflow_tracker.log_step("P4_4_prepare_download_context")
        workflow_tracker.log_step("P4_5_execute_document_download")
        
        # P4_6: Download Success? -> NO -> P4_FAIL
        workflow_tracker.log_decision("download_success", False, "P4_FAIL_handle_download_failure")
        workflow_tracker.log_step("P4_FAIL_handle_download_failure")
        workflow_tracker.log_step("P4_SAVE_FAIL_add_failure_metadata")
        workflow_tracker.log_step("FINAL_SAVE_file_operations_save_upload")
        
        # Verify failure handling
        failure_decision = workflow_tracker.decision_points[1]
        assert failure_decision["condition"] == "download_success"
        assert failure_decision["result"] is False

    @pytest.mark.asyncio
    async def test_final_save_operations_complete_sequence(self, workflow_tracker):
        """Test Final Save operations complete sequence (FS1 -> FS8)."""
        # Expected Final Save sequence from docket_processing.md:
        # FS1 -> FS2 -> FS3 -> FS4 -> FS5 -> FS6 -> FS8 -> SUCCESS
        expected_steps = [
            "FS1_create_court_date_directory",
            "FS2_generate_filename",
            "FS3_clean_prepare_case_data",
            "FS4_save_to_local_json",
            "FS5_upload_enabled_check",
            "FS6_upload_to_s3",
            "FS8_upload_success_check",
            "SUCCESS_complete_success"
        ]
        
        # Log final save sequence
        for step in expected_steps:
            workflow_tracker.log_step(step)
        
        # Log key decisions
        workflow_tracker.log_decision("upload_enabled", True, "FS6_upload_to_s3")
        workflow_tracker.log_decision("upload_success", True, "SUCCESS_complete_success")
        
        # Verify complete final save sequence
        assert workflow_tracker.verify_execution_order(expected_steps)

    @pytest.mark.asyncio
    async def test_final_save_local_only_scenario(self, workflow_tracker):
        """Test Final Save local-only scenario (FS5 -> FS7 -> SUCCESS)."""
        workflow_tracker.log_step("FS1_create_court_date_directory")
        workflow_tracker.log_step("FS2_generate_filename")
        workflow_tracker.log_step("FS3_clean_prepare_case_data")
        workflow_tracker.log_step("FS4_save_to_local_json")
        
        # FS5: Upload Enabled? -> NO -> FS7
        workflow_tracker.log_decision("upload_enabled", False, "FS7_local_only")
        workflow_tracker.log_step("FS7_local_only")
        workflow_tracker.log_step("SUCCESS_complete_success")
        
        # Verify local-only path
        upload_decision = workflow_tracker.decision_points[0]
        assert upload_decision["condition"] == "upload_enabled"
        assert upload_decision["result"] is False

    @pytest.mark.asyncio
    async def test_final_save_upload_failure_scenario(self, workflow_tracker):
        """Test Final Save upload failure scenario (FS8 -> PARTIAL)."""
        workflow_tracker.log_step("FS1_create_court_date_directory")
        workflow_tracker.log_step("FS2_generate_filename")
        workflow_tracker.log_step("FS3_clean_prepare_case_data")
        workflow_tracker.log_step("FS4_save_to_local_json")
        
        # Upload enabled but fails
        workflow_tracker.log_decision("upload_enabled", True, "FS6_upload_to_s3")
        workflow_tracker.log_step("FS6_upload_to_s3")
        
        # FS8: Upload Success? -> NO -> PARTIAL
        workflow_tracker.log_decision("upload_success", False, "PARTIAL_local_success_upload_error")
        workflow_tracker.log_step("PARTIAL_local_success_upload_error")
        
        # Verify partial success handling
        upload_result_decision = workflow_tracker.decision_points[1]
        assert upload_result_decision["condition"] == "upload_success"
        assert upload_result_decision["result"] is False

    @pytest.mark.asyncio
    async def test_complete_workflow_phase_transitions(self, workflow_tracker):
        """Test complete workflow phase transitions match docket_processing.md."""
        expected_transitions = [
            {"from": "DOCKET_START", "to": "PHASE1"},
            {"from": "PHASE1", "to": "PHASE2"},
            {"from": "PHASE2", "to": "PHASE3"},
            {"from": "PHASE3", "to": "PHASE4"},
            {"from": "PHASE4", "to": "FINAL_SAVE"},
            {"from": "FINAL_SAVE", "to": "SUCCESS"}
        ]
        
        # Log phase transitions for successful workflow
        for transition in expected_transitions:
            workflow_tracker.log_phase_transition(transition["from"], transition["to"])
        
        # Verify all phase transitions occurred
        assert workflow_tracker.phase_transitions == expected_transitions

    @pytest.mark.asyncio
    async def test_legacy_vs_new_services_decision_point(self, workflow_tracker):
        """Test legacy vs new services decision point (C -> D or LEGACY)."""
        # Test new services path
        workflow_tracker.log_step("DOCKET_PROCESSING_START")
        workflow_tracker.log_step("RECEIVE_PAGE_INITIAL_DETAILS")
        
        # C: Use New Docket Services? -> YES -> D (New Service Architecture Path)
        workflow_tracker.log_decision("use_new_docket_services", True, "D_new_service_architecture_path")
        workflow_tracker.log_step("D_new_service_architecture_path")
        workflow_tracker.log_step("D1_initialize_all_8_services")
        workflow_tracker.log_step("D2_configuration_service_ready")
        workflow_tracker.log_step("PHASE1_html_content_processing")
        
        # Verify new services path taken
        decision = workflow_tracker.decision_points[0]
        assert decision["condition"] == "use_new_docket_services"
        assert decision["result"] is True
        
        # Test legacy path
        workflow_tracker.execution_log.clear()
        workflow_tracker.decision_points.clear()
        
        workflow_tracker.log_step("DOCKET_PROCESSING_START")
        workflow_tracker.log_step("RECEIVE_PAGE_INITIAL_DETAILS")
        
        # C: Use New Docket Services? -> NO -> LEGACY
        workflow_tracker.log_decision("use_new_docket_services", False, "LEGACY_docket_processor_path")
        workflow_tracker.log_step("LEGACY_docket_processor_path")
        workflow_tracker.log_step("L1_legacy_docket_processor_process_docket_page")
        
        # Verify legacy path taken
        decision = workflow_tracker.decision_points[0]
        assert decision["condition"] == "use_new_docket_services"
        assert decision["result"] is False

    @pytest.mark.asyncio
    async def test_no_workflow_steps_skipped_verification(self, workflow_tracker):
        """Verify no mandatory workflow steps are skipped inappropriately."""
        # Define mandatory steps that must always execute for new services path
        mandatory_steps = [
            "D1_initialize_all_8_services",
            "D2_configuration_service_ready", 
            "PHASE1_html_content_processing",
            "PHASE2_relevance_classification",
            "PHASE3_case_verification",
            # Phase 4 and Final Save are conditional based on verification
        ]
        
        # Simulate complete workflow execution
        for step in mandatory_steps:
            workflow_tracker.log_step(step)
        
        # Verify all mandatory steps present
        actual_steps = [entry["step"] for entry in workflow_tracker.execution_log]
        for mandatory_step in mandatory_steps:
            assert mandatory_step in actual_steps, f"Mandatory step {mandatory_step} was skipped"

    @pytest.mark.asyncio
    async def test_decision_tree_exhaustiveness(self, workflow_tracker):
        """Test that all decision branches in docket_processing.md are covered."""
        # Phase 2 decision tree should cover all possible outcomes
        phase_2_outcomes = [
            "P2_3_set_relevant_mdl",           # MDL flag -> relevant
            "P2_5_set_not_relevant_ignore",   # ignore download -> not relevant
            "P2_7_set_not_relevant_excluded", # excluded statute -> not relevant  
            "P2_9_set_relevant_explicit",     # explicitly relevant -> relevant
            "P2_10_set_not_relevant_default"  # default -> not relevant
        ]
        
        # Phase 3 decision tree outcomes
        phase_3_outcomes = [
            "P3_SKIP_already_in_database",    # exists in DB -> skip
            "P3_SKIP_local_files_exist",      # local files exist -> skip
            "P3_PROCESS_proceed_download"     # no conflicts -> proceed
        ]
        
        # Phase 4 decision tree outcomes  
        phase_4_outcomes = [
            "P4_SKIP_skip_download",          # should not download -> skip
            "P4_SUCCESS_handle_download_success", # download success -> success
            "P4_FAIL_handle_download_failure"  # download failure -> fail
        ]
        
        # Final Save outcomes
        final_save_outcomes = [
            "SUCCESS_complete_success",       # local + upload success -> success
            "PARTIAL_local_success_upload_error", # local success, upload fail -> partial  
            "SUCCESS_complete_success"        # local only success -> success
        ]
        
        all_outcomes = (
            phase_2_outcomes + 
            phase_3_outcomes + 
            phase_4_outcomes + 
            final_save_outcomes
        )
        
        # Each outcome represents a valid end state from decision tree
        # Verify we have comprehensive coverage
        assert len(phase_2_outcomes) == 5, "Phase 2 should have exactly 5 possible outcomes"
        assert len(phase_3_outcomes) == 3, "Phase 3 should have exactly 3 possible outcomes" 
        assert len(phase_4_outcomes) == 3, "Phase 4 should have exactly 3 possible outcomes"
        assert len(set(all_outcomes)) > 0, "Should have comprehensive outcome coverage"