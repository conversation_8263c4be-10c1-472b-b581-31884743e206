# /tests/services/pacer/test_core_services/test_case_classification_service.py
"""
Unit tests for CaseClassificationService - Core Service #3  
This service handles Phase 2: Relevance & Classification
"""
import pytest
from unittest.mock import AsyncMock, Mock

from src.pacer.case_classification_facade_service import CaseClassificationFacadeService
from src.infrastructure.protocols.exceptions import PacerServiceError


class TestCaseClassificationServiceUnit:
    """Unit tests for CaseClassificationService with mocked dependencies."""

    @pytest.fixture
    def mock_case_classifier(self):
        """Mock case classifier component."""
        classifier = AsyncMock()
        classifier.execute = AsyncMock(return_value={
            "is_removal": False,
            "is_transfer": False,
            "case_type": "civil_rights",
            "classification_confidence": 0.95
        })
        return classifier

    @pytest.fixture
    def service(self, mock_case_classifier, mock_logger, base_config):
        """Create CaseClassificationService instance with mocked dependencies."""
        return CaseClassificationFacadeService(
            case_classifier=mock_case_classifier,
            logger=mock_logger,
            config=base_config
        )

    def test_service_inherits_from_async_service_base(self, service):
        """Test that CaseClassificationService inherits from AsyncServiceBase."""
        from src.infrastructure.patterns.component_base import AsyncServiceBase
        assert isinstance(service, AsyncServiceBase)

    def test_service_initialization_with_dependencies(self, service, mock_case_classifier):
        """Test service initialization with required dependencies."""
        assert service._classifier == mock_case_classifier

    @pytest.mark.asyncio
    async def test_execute_method_routes_to_execute_action(self, service, sample_case_details):
        """Test that execute method properly routes to _execute_action."""
        data = {
            "action": "classify_case",
            "case_details": sample_case_details,
            "is_explicitly_requested": False
        }
        
        with pytest.patch.object(service, '_execute_action') as mock_execute_action:
            mock_execute_action.return_value = {"classified": True}
            
            result = await service.execute(data)
            
            assert result == {"classified": True}
            mock_execute_action.assert_called_once_with(data)

    @pytest.mark.asyncio
    async def test_execute_action_classify_case(self, service, sample_case_details):
        """Test _execute_action with classify_case action."""
        with pytest.patch.object(service, 'classify_case') as mock_classify:
            mock_classify.return_value = {
                "classification": "civil_rights",
                "is_removal": False,
                "is_transfer": False
            }
            
            data = {
                "action": "classify_case",
                "case_details": sample_case_details,
                "is_explicitly_requested": False
            }
            
            result = await service._execute_action(data)
            
            assert result["classification"] == "civil_rights"
            mock_classify.assert_called_once_with(sample_case_details, False)

    @pytest.mark.asyncio
    async def test_execute_action_determine_relevance(self, service, sample_case_details):
        """Test _execute_action with determine_relevance action."""
        with pytest.patch.object(service, 'determine_relevance') as mock_relevance:
            mock_relevance.return_value = {
                "relevance": "relevant",
                "reason": "PFAS case detected",
                "mdl_override": False
            }
            
            data = {
                "action": "determine_relevance", 
                "case_details": sample_case_details
            }
            
            result = await service._execute_action(data)
            
            assert result["relevance"] == "relevant"
            mock_relevance.assert_called_once_with(sample_case_details)

    @pytest.mark.asyncio
    async def test_execute_action_unknown_action_raises_error(self, service):
        """Test that unknown action raises PacerServiceError."""
        with pytest.raises(PacerServiceError, match="Unknown action"):
            await service._execute_action({"action": "invalid_action"})

    @pytest.mark.asyncio
    async def test_phase_2_workflow_mdl_flag_override(self, service, sample_case_details, sample_relevance_config):
        """Test Phase 2 workflow with MDL flag override (P2_2 -> P2_3)."""
        # Case with MDL flag should be set as relevant regardless of other factors
        mdl_case = {**sample_case_details, "mdl_flag": True, "mdl_number": "MDL-2873"}
        
        with pytest.patch.object(service, 'determine_relevance') as mock_relevance:
            mock_relevance.return_value = {
                "relevance": "relevant",
                "reason": "MDL flag override",
                "mdl_override": True
            }
            
            data = {
                "action": "determine_relevance",
                "case_details": mdl_case
            }
            
            result = await service._execute_action(data)
            
            assert result["relevance"] == "relevant"
            assert result["mdl_override"] is True

    @pytest.mark.asyncio
    async def test_phase_2_workflow_ignore_download_pattern(self, service, sample_case_details):
        """Test Phase 2 workflow with ignore download pattern (P2_4 -> P2_5)."""
        # Case matching ignore download pattern should be set as not relevant
        ignore_case = {
            **sample_case_details,
            "case_name": "Doe v. Smith - Case Dismissed",
            "status": "Dismissed"
        }
        
        with pytest.patch.object(service, 'determine_relevance') as mock_relevance:
            mock_relevance.return_value = {
                "relevance": "not_relevant", 
                "reason": "ignore_download pattern match",
                "pattern_matched": ".*dismissed.*"
            }
            
            data = {
                "action": "determine_relevance",
                "case_details": ignore_case
            }
            
            result = await service._execute_action(data)
            
            assert result["relevance"] == "not_relevant"
            assert "ignore_download" in result["reason"]

    @pytest.mark.asyncio
    async def test_phase_2_workflow_excluded_by_statute(self, service, sample_case_details):
        """Test Phase 2 workflow with excluded statute (P2_6 -> P2_7)."""
        excluded_case = {
            **sample_case_details,
            "cause": "15:78 Securities Exchange Act",
            "nature_of_suit": "Securities"
        }
        
        with pytest.patch.object(service, 'determine_relevance') as mock_relevance:
            mock_relevance.return_value = {
                "relevance": "not_relevant",
                "reason": "excluded by statute 15:78",
                "excluded_statute": "15:78"
            }
            
            data = {
                "action": "determine_relevance", 
                "case_details": excluded_case
            }
            
            result = await service._execute_action(data)
            
            assert result["relevance"] == "not_relevant"
            assert "15:78" in result["reason"]

    @pytest.mark.asyncio
    async def test_phase_2_workflow_explicitly_relevant(self, service, sample_case_details):
        """Test Phase 2 workflow with explicitly relevant case (P2_8 -> P2_9)."""
        pfas_case = {
            **sample_case_details,
            "case_name": "PFAS Contamination Litigation", 
            "cause": "42:1983 Civil Rights - Environmental"
        }
        
        with pytest.patch.object(service, 'determine_relevance') as mock_relevance:
            mock_relevance.return_value = {
                "relevance": "relevant",
                "reason": "explicitly relevant - PFAS detected",
                "matched_terms": ["PFAS"]
            }
            
            data = {
                "action": "determine_relevance",
                "case_details": pfas_case
            }
            
            result = await service._execute_action(data)
            
            assert result["relevance"] == "relevant"
            assert "PFAS" in str(result["matched_terms"])

    @pytest.mark.asyncio
    async def test_phase_2_workflow_default_not_relevant(self, service, sample_case_details):
        """Test Phase 2 workflow default not relevant (P2_8 -> P2_10)."""
        generic_case = {
            **sample_case_details,
            "case_name": "Generic Contract Dispute",
            "cause": "Contract Dispute",
            "mdl_flag": False
        }
        
        with pytest.patch.object(service, 'determine_relevance') as mock_relevance:
            mock_relevance.return_value = {
                "relevance": "not_relevant",
                "reason": "default - no matching criteria",
                "default_applied": True
            }
            
            data = {
                "action": "determine_relevance",
                "case_details": generic_case
            }
            
            result = await service._execute_action(data)
            
            assert result["relevance"] == "not_relevant"
            assert result["default_applied"] is True

    @pytest.mark.asyncio
    async def test_classification_explicitly_requested_skip_removal(self, service, sample_case_details):
        """Test classification for explicitly requested cases (P2_C1 -> P2_C2)."""
        # Explicitly requested cases should skip removal detection for reprocessing
        data = {
            "action": "classify_case",
            "case_details": sample_case_details,
            "is_explicitly_requested": True
        }
        
        with pytest.patch.object(service, 'classify_case') as mock_classify:
            mock_classify.return_value = {
                "classification": "reprocessing",
                "removal_detection_skipped": True,
                "is_transfer": False
            }
            
            result = await service._execute_action(data)
            
            assert result["removal_detection_skipped"] is True

    @pytest.mark.asyncio
    async def test_classification_removal_by_cause_and_html(self, service, sample_case_details):
        """Test classification with removal detection (P2_C1 -> P2_C3)."""
        removal_case = {
            **sample_case_details,
            "cause": "Removed from State Court",
            "nature_of_suit": "Other"
        }
        
        data = {
            "action": "classify_case",
            "case_details": removal_case,
            "is_explicitly_requested": False
        }
        
        with pytest.patch.object(service, 'classify_case') as mock_classify:
            mock_classify.return_value = {
                "classification": "removal_case",
                "is_removal": True,
                "is_transfer": False,
                "removal_reason": "State court removal detected"
            }
            
            result = await service._execute_action(data)
            
            assert result["is_removal"] is True
            assert result["classification"] == "removal_case"

    @pytest.mark.asyncio
    async def test_classification_transfer_status_check(self, service, sample_case_details):
        """Test classification with transfer status check (P2_C4)."""
        data = {
            "action": "classify_case",
            "case_details": sample_case_details,
            "is_explicitly_requested": False
        }
        
        with pytest.patch.object(service, 'classify_case') as mock_classify:
            mock_classify.return_value = {
                "classification": "standard_case",
                "is_removal": False,
                "is_transfer": True,
                "transfer_court": "ndca"
            }
            
            result = await service._execute_action(data)
            
            assert result["is_transfer"] is True
            assert result["transfer_court"] == "ndca"

    @pytest.mark.asyncio
    async def test_concurrent_classification_requests(self, service):
        """Test handling multiple concurrent classification requests."""
        import asyncio
        
        # Create multiple classification tasks
        cases = [
            {"case_number": f"1:23-cv-1234{i}", "case_name": f"Case {i}", "mdl_flag": False}
            for i in range(3)
        ]
        
        tasks = []
        for case in cases:
            data = {
                "action": "classify_case",
                "case_details": case,
                "is_explicitly_requested": False
            }
            tasks.append(service._execute_action(data))
        
        with pytest.patch.object(service, 'classify_case') as mock_classify:
            mock_classify.return_value = {"classification": "processed"}
            
            results = await asyncio.gather(*tasks)
            
            assert len(results) == 3
            assert all(result["classification"] == "processed" for result in results)
            assert mock_classify.call_count == 3


class TestCaseClassificationServiceErrorHandling:
    """Test error handling scenarios for CaseClassificationService."""

    @pytest.fixture  
    def service_with_failing_classifier(self, mock_logger, base_config):
        """Create service with failing classifier component."""
        failing_classifier = AsyncMock()
        failing_classifier.execute.side_effect = Exception("Classification failed")
        
        return CaseClassificationFacadeService(
            case_classifier=failing_classifier,
            logger=mock_logger,
            config=base_config
        )

    @pytest.mark.asyncio
    async def test_classifier_failure_handling(self, service_with_failing_classifier, sample_case_details):
        """Test handling of classifier component failure."""
        data = {
            "action": "classify_case",
            "case_details": sample_case_details,
            "is_explicitly_requested": False
        }
        
        with pytest.patch.object(service_with_failing_classifier, 'classify_case') as mock_classify:
            mock_classify.side_effect = Exception("Classification failed")
            
            with pytest.raises(Exception, match="Classification failed"):
                await service_with_failing_classifier._execute_action(data)

    @pytest.mark.asyncio
    async def test_missing_case_details(self, service):
        """Test handling of missing case details."""
        data = {
            "action": "classify_case",
            "is_explicitly_requested": False
            # Missing case_details
        }
        
        with pytest.raises(KeyError):
            await service._execute_action(data)

    @pytest.mark.asyncio
    async def test_invalid_case_data_structure(self, service):
        """Test handling of invalid case data structure."""
        invalid_case = "not_a_dict"  # Should be a dict
        
        data = {
            "action": "classify_case", 
            "case_details": invalid_case,
            "is_explicitly_requested": False
        }
        
        with pytest.patch.object(service, 'classify_case') as mock_classify:
            mock_classify.side_effect = AttributeError("'str' object has no attribute")
            
            with pytest.raises(AttributeError):
                await service._execute_action(data)

    @pytest.mark.asyncio
    async def test_relevance_config_missing(self, service, sample_case_details):
        """Test handling when relevance configuration is missing."""
        data = {
            "action": "determine_relevance",
            "case_details": sample_case_details
        }
        
        with pytest.patch.object(service, 'determine_relevance') as mock_relevance:
            mock_relevance.side_effect = KeyError("Relevance config not found")
            
            with pytest.raises(KeyError):
                await service._execute_action(data)