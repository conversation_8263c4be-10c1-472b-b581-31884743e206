# /tests/services/pacer/test_core_services/test_case_processing_service.py
"""
Unit tests for CaseProcessingService - Core Service #2
This service handles Phase 1: HTML Content Processing
"""
import pytest
from unittest.mock import AsyncMock, Mock

from src.pacer._core_services.case_processing.case_processing_service import CaseProcessingService
from src.infrastructure.protocols.exceptions import PacerServiceError


class TestCaseProcessingServiceUnit:
    """Unit tests for CaseProcessingService with mocked dependencies."""

    @pytest.fixture
    def mock_case_validator(self):
        """Mock case validator component."""
        validator = AsyncMock()
        validator.execute = AsyncMock(return_value={"valid": True})
        return validator

    @pytest.fixture
    def mock_case_parser(self):
        """Mock case parser component."""
        parser = AsyncMock()
        parser.execute = AsyncMock(return_value={
            "case_number": "1:23-cv-12345",
            "case_name": "Test Case",
            "mdl_flag": False,
            "cause": "42:1983 Civil Rights"
        })
        return parser

    @pytest.fixture
    def mock_case_enricher(self):
        """Mock case enricher component."""
        enricher = AsyncMock()
        enricher.execute = AsyncMock(return_value={
            "enhanced": True,
            "metadata": {"processing_date": "2024-01-01"}
        })
        return enricher

    @pytest.fixture
    def mock_case_transformer(self):
        """Mock case transformer component."""
        transformer = AsyncMock()
        transformer.execute = AsyncMock(return_value={
            "transformed": True,
            "filename_base": "1-23-cv-12345_test_case"
        })
        return transformer

    @pytest.fixture
    def service(self, mock_case_validator, mock_case_parser, mock_case_enricher, 
                mock_case_transformer, mock_logger, base_config):
        """Create CaseProcessingService instance with mocked dependencies."""
        return CaseProcessingService(
            case_validator=mock_case_validator,
            case_parser=mock_case_parser,
            case_enricher=mock_case_enricher,
            case_transformer=mock_case_transformer,
            logger=mock_logger,
            config=base_config
        )

    def test_service_inherits_from_async_service_base(self, service):
        """Test that CaseProcessingService inherits from AsyncServiceBase."""
        from src.infrastructure.patterns.component_base import AsyncServiceBase
        assert isinstance(service, AsyncServiceBase)

    def test_service_initialization_with_dependencies(self, service, mock_case_validator,
                                                    mock_case_parser, mock_case_enricher,
                                                    mock_case_transformer):
        """Test service initialization with all required dependencies."""
        assert service._validator == mock_case_validator
        assert service._parser == mock_case_parser
        assert service._enricher == mock_case_enricher
        assert service._transformer == mock_case_transformer

    @pytest.mark.asyncio
    async def test_execute_method_routes_to_execute_action(self, service, mock_page, sample_case_details):
        """Test that execute method properly routes to _execute_action."""
        data = {
            "action": "process_case",
            "page": mock_page,
            "initial_details": sample_case_details
        }
        
        with pytest.patch.object(service, '_execute_action') as mock_execute_action:
            mock_execute_action.return_value = {"success": True}
            
            result = await service.execute(data)
            
            assert result == {"success": True}
            mock_execute_action.assert_called_once_with(data)

    @pytest.mark.asyncio
    async def test_execute_action_process_case(self, service, mock_page, sample_case_details):
        """Test _execute_action with process_case action."""
        with pytest.patch.object(service, 'process_case') as mock_process_case:
            mock_process_case.return_value = {"processed": True}
            
            data = {
                "action": "process_case", 
                "page": mock_page,
                "initial_details": sample_case_details
            }
            
            result = await service._execute_action(data)
            
            assert result == {"processed": True}
            mock_process_case.assert_called_once_with(mock_page, sample_case_details)

    @pytest.mark.asyncio
    async def test_execute_action_validate_page_content(self, service, mock_page, sample_case_details):
        """Test _execute_action with validate_page_content action."""
        data = {
            "action": "validate_page_content",
            "page": mock_page,
            "case_details": sample_case_details
        }
        
        result = await service._execute_action(data)
        
        # Verify validator was called with correct parameters
        service._validator.execute.assert_called_once_with({
            "action": "validate_page_content",
            "page": mock_page,
            "case_details": sample_case_details
        })

    @pytest.mark.asyncio
    async def test_execute_action_update_case_details(self, service, sample_html_content, sample_case_details):
        """Test _execute_action with update_case_details action."""
        with pytest.patch.object(service, 'update_case_details') as mock_update:
            mock_update.return_value = {"updated": True}
            
            data = {
                "action": "update_case_details",
                "html_content": sample_html_content,
                "initial_details": sample_case_details
            }
            
            result = await service._execute_action(data)
            
            assert result == {"updated": True}
            mock_update.assert_called_once_with(sample_html_content, sample_case_details)

    @pytest.mark.asyncio
    async def test_execute_action_create_base_filename(self, service, sample_case_details):
        """Test _execute_action with create_base_filename action."""
        with pytest.patch.object(service, 'create_base_filename') as mock_create:
            mock_create.return_value = "1-23-cv-12345_test_case"
            
            data = {
                "action": "create_base_filename",
                "case_details": sample_case_details
            }
            
            result = await service._execute_action(data)
            
            assert result == "1-23-cv-12345_test_case"
            mock_create.assert_called_once_with(sample_case_details)

    @pytest.mark.asyncio
    async def test_execute_action_unknown_action_raises_error(self, service):
        """Test that unknown action raises PacerServiceError."""
        with pytest.raises(PacerServiceError, match="Unknown action"):
            await service._execute_action({"action": "invalid_action"})

    @pytest.mark.asyncio
    async def test_phase_1_workflow_step_by_step(self, service, mock_page, sample_case_details):
        """Test Phase 1 workflow according to docket_processing.md."""
        # Step P1_1: Case Processing Service.process_case_html
        # Step P1_2: Wait for Page Content Stability
        # Step P1_3: Validate HTML Content Matches Case
        # Step P1_4: HTML Valid check
        # Step P1_5: Extract Case Details via HTMLCaseParser
        # Step P1_6: Process MDL Flags and Special Cases
        # Step P1_7: Check for Notice of Removal
        # Step P1_8: Generate Base Filename
        # Step P1_9: Add Processing Metadata
        
        # Mock the process_case method to simulate the full workflow
        with pytest.patch.object(service, 'process_case') as mock_process:
            expected_result = {
                "html_valid": True,
                "case_details": {
                    **sample_case_details,
                    "mdl_processed": True,
                    "removal_checked": True
                },
                "filename_base": "1-23-cv-12345_test_case",
                "metadata": {
                    "processing_phase": "1",
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            }
            mock_process.return_value = expected_result
            
            data = {
                "action": "process_case",
                "page": mock_page,
                "initial_details": sample_case_details
            }
            
            result = await service._execute_action(data)
            
            assert result["html_valid"] is True
            assert "case_details" in result
            assert "filename_base" in result
            assert "metadata" in result

    @pytest.mark.asyncio
    async def test_html_validation_failure_scenario(self, service, mock_page, sample_case_details,
                                                  mock_case_validator):
        """Test Phase 1 failure scenario when HTML validation fails."""
        # Mock validation failure (Step P1_4 -> P1_FAIL)
        mock_case_validator.execute.return_value = {"valid": False, "reason": "Case mismatch"}
        
        with pytest.patch.object(service, 'process_case') as mock_process:
            mock_process.return_value = None  # Should return None for invalid HTML
            
            data = {
                "action": "process_case",
                "page": mock_page,
                "initial_details": sample_case_details
            }
            
            result = await service._execute_action(data)
            
            assert result is None

    @pytest.mark.asyncio
    async def test_mdl_flag_processing(self, service, mock_case_parser, sample_case_details):
        """Test MDL flag processing in Phase 1."""
        # Mock case with MDL flag
        mock_case_parser.execute.return_value = {
            **sample_case_details,
            "mdl_flag": True,
            "mdl_number": "MDL-2873"
        }
        
        data = {
            "action": "update_case_details",
            "html_content": "<html>MDL case content</html>",
            "initial_details": sample_case_details
        }
        
        with pytest.patch.object(service, 'update_case_details') as mock_update:
            mock_update.return_value = {"mdl_processed": True, "mdl_flag": True}
            
            result = await service._execute_action(data)
            
            assert result["mdl_processed"] is True
            assert result["mdl_flag"] is True

    @pytest.mark.asyncio
    async def test_removal_detection_processing(self, service, sample_case_details):
        """Test Notice of Removal detection in Phase 1."""
        # Case with removal indicators
        removal_case = {
            **sample_case_details,
            "cause": "Removal from State Court",
            "nature_of_suit": "Other"
        }
        
        with pytest.patch.object(service, 'update_case_details') as mock_update:
            mock_update.return_value = {
                "removal_detected": True,
                "removal_reason": "State court removal"
            }
            
            data = {
                "action": "update_case_details",
                "html_content": "<html>Removal case content</html>",
                "initial_details": removal_case
            }
            
            result = await service._execute_action(data)
            
            assert result["removal_detected"] is True

    @pytest.mark.asyncio
    async def test_concurrent_processing_requests(self, service, mock_page, sample_case_details):
        """Test handling multiple concurrent processing requests."""
        import asyncio
        
        # Create multiple processing tasks
        tasks = []
        for i in range(3):
            case_details = {**sample_case_details, "case_number": f"1:23-cv-1234{i}"}
            data = {
                "action": "process_case",
                "page": mock_page,
                "initial_details": case_details
            }
            tasks.append(service._execute_action(data))
        
        with pytest.patch.object(service, 'process_case') as mock_process:
            mock_process.return_value = {"processed": True}
            
            results = await asyncio.gather(*tasks)
            
            assert len(results) == 3
            assert all(result["processed"] for result in results)
            assert mock_process.call_count == 3


class TestCaseProcessingServiceErrorHandling:
    """Test error handling scenarios for CaseProcessingService."""

    @pytest.fixture
    def service_with_failing_components(self, mock_logger, base_config):
        """Create service with components that fail."""
        failing_validator = AsyncMock()
        failing_validator.execute.side_effect = Exception("Validation failed")
        
        failing_parser = AsyncMock() 
        failing_parser.execute.side_effect = Exception("Parser failed")
        
        working_enricher = AsyncMock()
        working_enricher.execute.return_value = {"enriched": True}
        
        working_transformer = AsyncMock()
        working_transformer.execute.return_value = {"transformed": True}
        
        return CaseProcessingService(
            case_validator=failing_validator,
            case_parser=failing_parser,
            case_enricher=working_enricher,
            case_transformer=working_transformer,
            logger=mock_logger,
            config=base_config
        )

    @pytest.mark.asyncio
    async def test_validator_failure_handling(self, service_with_failing_components, mock_page, sample_case_details):
        """Test handling of validator component failure."""
        data = {
            "action": "validate_page_content",
            "page": mock_page,
            "case_details": sample_case_details
        }
        
        with pytest.raises(Exception, match="Validation failed"):
            await service_with_failing_components._execute_action(data)

    @pytest.mark.asyncio
    async def test_parser_failure_handling(self, service_with_failing_components, sample_html_content, sample_case_details):
        """Test handling of parser component failure."""  
        with pytest.patch.object(service_with_failing_components, 'update_case_details') as mock_update:
            mock_update.side_effect = Exception("Parser failed")
            
            data = {
                "action": "update_case_details",
                "html_content": sample_html_content,
                "initial_details": sample_case_details
            }
            
            with pytest.raises(Exception, match="Parser failed"):
                await service_with_failing_components._execute_action(data)

    @pytest.mark.asyncio
    async def test_missing_required_data_fields(self, service, mock_page):
        """Test handling of missing required data fields."""
        incomplete_data = {
            "action": "process_case",
            "page": mock_page
            # Missing initial_details
        }
        
        with pytest.raises(KeyError):
            await service._execute_action(incomplete_data)

    @pytest.mark.asyncio
    async def test_invalid_page_object(self, service, sample_case_details):
        """Test handling of invalid page object."""
        data = {
            "action": "process_case",
            "page": None,  # Invalid page
            "initial_details": sample_case_details
        }
        
        with pytest.patch.object(service, 'process_case') as mock_process:
            mock_process.side_effect = AttributeError("'NoneType' object has no attribute")
            
            with pytest.raises(AttributeError):
                await service._execute_action(data)