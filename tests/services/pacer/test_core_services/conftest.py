# /tests/services/pacer/test_core_services/conftest.py
"""
Shared fixtures and test configuration for PACER core services tests.
"""
import pytest
from unittest.mock import AsyncMock, Mock, MagicMock
from typing import Dict, Any, Optional
from pathlib import Path

from src.infrastructure.protocols.logger import LoggerProtocol
from src.pacer.components.configuration.json_config_loader import JsonConfigLoader


@pytest.fixture
def mock_logger():
    """Mock logger for testing."""
    logger = Mock(spec=LoggerProtocol)
    logger.debug = Mock()
    logger.info = Mock()
    logger.warning = Mock()
    logger.error = Mock()
    logger.exception = Mock()
    return logger


@pytest.fixture
def base_config():
    """Base configuration for testing services."""
    return {
        "config_dir": "tests/fixtures/config",
        "timeout": {
            "page_load": 5000,
            "element_wait": 3000,
            "navigation": 10000
        },
        "paths": {
            "data_dir": "/tmp/test_data",
            "output_dir": "/tmp/test_output"
        },
        "features": {
            "s3_upload": False,
            "local_save": True,
            "debug_mode": True
        }
    }


@pytest.fixture
def mock_json_loader():
    """Mock JSON configuration loader."""
    loader = Mock(spec=JsonConfigLoader)
    loader.load_config = Mock(return_value={
        "defaults": {"timeout": 5000},
        "patterns": {"case_number": r"\d{1,2}:\d{2}-\w{2}-\d{5}"}
    })
    return loader


@pytest.fixture
def sample_case_details():
    """Sample case details for testing."""
    return {
        "case_number": "1:23-cv-12345",
        "case_name": "Doe v. Smith Industries",
        "court": "cand",
        "judge": "Hon. Jane Smith",
        "filing_date": "2023-12-01",
        "status": "Active",
        "mdl_flag": False,
        "cause": "42:1983 Civil Rights",
        "nature_of_suit": "440 Other Civil Rights",
        "jurisdiction": "Federal Question",
        "plaintiffs": ["John Doe", "Jane Doe"],
        "defendants": ["Smith Industries", "John Smith"],
        "lead_attorney": "Attorney Smith"
    }


@pytest.fixture
def sample_html_content():
    """Sample HTML content for testing."""
    return '''
    <html>
        <body>
            <div class="case-header">
                <h1>1:23-cv-12345 Doe v. Smith Industries</h1>
                <div class="case-info">
                    <span>Judge: Hon. Jane Smith</span>
                    <span>Filed: 12/01/2023</span>
                </div>
            </div>
            <div class="case-details">
                <table>
                    <tr><td>Cause:</td><td>42:1983 Civil Rights</td></tr>
                    <tr><td>Nature of Suit:</td><td>440 Other Civil Rights</td></tr>
                    <tr><td>Jurisdiction:</td><td>Federal Question</td></tr>
                </table>
            </div>
        </body>
    </html>
    '''


@pytest.fixture
def mock_page():
    """Mock Playwright page object."""
    page = AsyncMock()
    page.url = "https://ecf.cand.uscourts.gov/cgi-bin/DktRpt.pl?123456"
    page.content = AsyncMock(return_value="<html><body>Test content</body></html>")
    page.wait_for_load_state = AsyncMock()
    page.query_selector = AsyncMock()
    page.query_selector_all = AsyncMock(return_value=[])
    page.evaluate = AsyncMock()
    page.locator = AsyncMock()
    return page


@pytest.fixture
def mock_components_factory():
    """Factory for creating mock components."""
    def create_mock_component(component_name: str):
        mock = AsyncMock()
        mock.execute = AsyncMock(return_value={"status": "success", "component": component_name})
        mock.validate = AsyncMock(return_value=True)
        mock.process = AsyncMock()
        return mock
    return create_mock_component


@pytest.fixture
def sample_relevance_config():
    """Sample relevance configuration."""
    return {
        "explicitly_relevant": [
            "PFAS", "AFFF", "Aqueous Film Forming Foam",
            "perfluorooctanoic acid", "PFOA", "PFOS"
        ],
        "explicitly_not_relevant": [
            "trademark", "patent", "copyright",
            "employment", "contract dispute"
        ],
        "excluded_statutes": [
            "15:78", "11:101", "26:7441"
        ],
        "mdl_override": True,
        "default_relevance": False
    }


@pytest.fixture
def sample_ignore_download_config():
    """Sample ignore download configuration."""
    return {
        "patterns": [
            ".*dismissed.*",
            ".*settled.*",
            ".*bankruptcy.*"
        ],
        "case_types": [
            "trademark",
            "patent"
        ],
        "courts": []
    }


@pytest.fixture
def sample_verification_rules():
    """Sample verification rules."""
    return {
        "explicitly_requested": {
            "check_database": True,
            "check_local_artifacts": "pdf_zip_only",
            "skip_on_exists": True
        },
        "report_scraped": {
            "check_database": True,
            "check_local_files": "all",
            "skip_on_exists": True
        }
    }


@pytest.fixture
def mock_database():
    """Mock database interface."""
    db = AsyncMock()
    db.query = AsyncMock()
    db.execute = AsyncMock()
    db.fetch_one = AsyncMock()
    db.fetch_all = AsyncMock(return_value=[])
    return db


@pytest.fixture
def mock_s3_client():
    """Mock S3 client."""
    s3 = AsyncMock()
    s3.upload_file = AsyncMock(return_value={"success": True, "url": "s3://bucket/key"})
    s3.download_file = AsyncMock()
    s3.list_objects = AsyncMock(return_value=[])
    return s3


@pytest.fixture
def mock_file_system():
    """Mock file system operations."""
    fs = Mock()
    fs.exists = Mock(return_value=False)
    fs.mkdir = Mock()
    fs.write_text = Mock()
    fs.read_text = Mock(return_value="test content")
    fs.unlink = Mock()
    return fs


@pytest.fixture
def workflow_test_data():
    """Test data for workflow integration tests."""
    return {
        "initial_case": {
            "case_number": "1:23-cv-12345",
            "case_name": "Test Case",
            "court": "cand",
            "is_explicitly_requested": False
        },
        "phase_1_result": {
            "html_valid": True,
            "case_details": {
                "case_number": "1:23-cv-12345",
                "mdl_flag": False,
                "cause": "42:1983 Civil Rights"
            },
            "filename_base": "1-23-cv-12345_test_case"
        },
        "phase_2_result": {
            "relevance": "relevant",
            "classification": {
                "is_removal": False,
                "is_transfer": False,
                "case_type": "civil_rights"
            }
        },
        "phase_3_result": {
            "should_download": True,
            "verification_status": "proceed"
        },
        "phase_4_result": {
            "download_success": True,
            "files_downloaded": ["docket.pdf", "complaint.pdf"],
            "s3_urls": []
        }
    }


class MockAsyncServiceBase:
    """Mock base class for testing service inheritance."""
    
    def __init__(self, logger=None, config=None):
        self.logger = logger or Mock()
        self.config = config or {}
        self._initialized = False
        self._dependencies = {}
    
    async def initialize(self):
        self._initialized = True
    
    async def cleanup(self):
        self._initialized = False
    
    def log_info(self, message, extra=None):
        self.logger.info(message, extra=extra)
    
    def log_debug(self, message, extra=None):
        self.logger.debug(message, extra=extra)
    
    def log_warning(self, message, extra=None):
        self.logger.warning(message, extra=extra)
    
    def log_error(self, message, extra=None, exc_info=False):
        self.logger.error(message, extra=extra, exc_info=exc_info)


@pytest.fixture
def error_scenarios():
    """Common error scenarios for testing."""
    return {
        "config_load_error": {
            "error": FileNotFoundError("Config file not found"),
            "expected_exception": "PacerServiceError"
        },
        "page_timeout_error": {
            "error": TimeoutError("Page load timeout"),
            "expected_exception": "PacerServiceError"
        },
        "invalid_case_data": {
            "data": {"invalid": "data"},
            "expected_exception": "ValidationError"
        },
        "s3_upload_failure": {
            "error": Exception("S3 connection failed"),
            "expected_exception": "UploadError"
        }
    }