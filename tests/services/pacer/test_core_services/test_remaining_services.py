# /tests/services/pacer/test_core_services/test_remaining_services.py
"""
Unit tests for remaining PACER core services:
- DownloadOrchestrationService (Core Service #5)  
- FileOperationsService (Core Service #6)
- RelevanceAnalysisService (Core Service #7)
- AnalyticsService (Core Service #8)
"""
import pytest
from unittest.mock import AsyncMock, Mock

from src.pacer.download_orchestration_facade_service import DownloadOrchestrationFacadeService
from src.pacer.file_management_facade_service import FileManagementFacadeService
from src.pacer.relevance_facade_service import RelevanceFacadeService
from src.pacer.analytics_facade_service import AnalyticsFacadeService
from src.infrastructure.protocols.exceptions import PacerServiceError


class TestDownloadOrchestrationServiceUnit:
    """Unit tests for DownloadOrchestrationService - Core Service #5 (Phase 4)."""

    @pytest.fixture
    def mock_download_manager(self):
        """Mock download manager component."""
        manager = AsyncMock()
        manager.execute = AsyncMock(return_value={
            "download_success": True,
            "files_downloaded": ["docket.pdf", "complaint.pdf"],
            "total_size_bytes": 1048576
        })
        return manager

    @pytest.fixture
    def service(self, mock_download_manager, mock_logger, base_config):
        """Create DownloadOrchestrationService instance."""
        return DownloadOrchestrationFacadeService(
            download_manager=mock_download_manager,
            logger=mock_logger,
            config=base_config
        )

    def test_service_inherits_from_async_service_base(self, service):
        """Test DownloadOrchestrationService inherits from AsyncServiceBase."""
        from src.infrastructure.patterns.component_base import AsyncServiceBase
        assert isinstance(service, AsyncServiceBase)

    @pytest.mark.asyncio
    async def test_execute_action_process_download_workflow(self, service, sample_case_details):
        """Test _execute_action with process_download_workflow action."""
        with pytest.patch.object(service, 'process_download_workflow') as mock_process:
            mock_process.return_value = {
                "download_success": True,
                "workflow_status": "completed"
            }
            
            data = {
                "action": "process_download_workflow",
                "case_details": sample_case_details
            }
            
            result = await service._execute_action(data)
            
            assert result["download_success"] is True
            mock_process.assert_called_once_with(sample_case_details)

    @pytest.mark.asyncio
    async def test_phase_4_validate_case_for_download(self, service, sample_case_details):
        """Test Phase 4 step P4_2: Validate Case for Download."""
        with pytest.patch.object(service, 'validate_case_for_download') as mock_validate:
            mock_validate.return_value = {
                "valid_for_download": True,
                "validation_checks": ["case_number", "court", "access_permissions"]
            }
            
            data = {
                "action": "validate_case_for_download",
                "case_details": sample_case_details
            }
            
            result = await service._execute_action(data)
            
            assert result["valid_for_download"] is True
            assert len(result["validation_checks"]) == 3

    @pytest.mark.asyncio
    async def test_phase_4_should_attempt_download_decision(self, service, sample_case_details):
        """Test Phase 4 step P4_3: Should Attempt Download decision."""
        # Test case that should be downloaded
        data = {
            "action": "should_attempt_download",
            "case_details": sample_case_details
        }
        
        with pytest.patch.object(service, 'should_attempt_download') as mock_should:
            mock_should.return_value = {
                "should_download": True,
                "reason": "case meets download criteria"
            }
            
            result = await service._execute_action(data)
            
            assert result["should_download"] is True

    @pytest.mark.asyncio
    async def test_phase_4_download_success_handling(self, service, sample_case_details):
        """Test Phase 4 step P4_6 -> P4_SUCCESS: Handle Download Success."""
        data = {
            "action": "handle_download_result",
            "case_details": sample_case_details,
            "download_result": {
                "success": True,
                "files": ["docket.pdf"],
                "metadata": {"size": 1024}
            }
        }
        
        with pytest.patch.object(service, 'handle_download_result') as mock_handle:
            mock_handle.return_value = {
                "result_type": "success",
                "success_metadata": {"processed_at": "2024-01-01T00:00:00Z"}
            }
            
            result = await service._execute_action(data)
            
            assert result["result_type"] == "success"

    @pytest.mark.asyncio
    async def test_phase_4_download_failure_handling(self, service, sample_case_details):
        """Test Phase 4 step P4_6 -> P4_FAIL: Handle Download Failure."""
        data = {
            "action": "handle_download_result",
            "case_details": sample_case_details,
            "download_result": {
                "success": False,
                "error": "Network timeout",
                "retry_count": 3
            }
        }
        
        with pytest.patch.object(service, 'handle_download_result') as mock_handle:
            mock_handle.return_value = {
                "result_type": "failure", 
                "failure_metadata": {"error": "Network timeout", "retry_count": 3}
            }
            
            result = await service._execute_action(data)
            
            assert result["result_type"] == "failure"
            assert result["failure_metadata"]["error"] == "Network timeout"


class TestFileOperationsServiceUnit:
    """Unit tests for FileOperationsService - Core Service #6 (Final Save)."""

    @pytest.fixture
    def mock_file_manager(self):
        """Mock file manager component."""
        manager = AsyncMock()
        manager.execute = AsyncMock(return_value={
            "file_saved": True,
            "file_path": "/tmp/case.json",
            "s3_uploaded": False
        })
        return manager

    @pytest.fixture
    def service(self, mock_file_manager, mock_logger, base_config):
        """Create FileOperationsService instance."""
        return FileManagementFacadeService(
            file_manager=mock_file_manager,
            logger=mock_logger,
            config=base_config
        )

    def test_service_inherits_from_async_service_base(self, service):
        """Test FileOperationsService inherits from AsyncServiceBase."""
        from src.infrastructure.patterns.component_base import AsyncServiceBase
        assert isinstance(service, AsyncServiceBase)

    @pytest.mark.asyncio
    async def test_execute_action_save_and_upload_case_data(self, service, sample_case_details):
        """Test _execute_action with save_and_upload_case_data action."""
        with pytest.patch.object(service, 'save_and_upload_case_data') as mock_save:
            mock_save.return_value = {
                "save_success": True,
                "upload_success": False,
                "local_path": "/tmp/case.json"
            }
            
            data = {
                "action": "save_and_upload_case_data",
                "case_data": sample_case_details,
                "download_result": {"files": ["docket.pdf"]}
            }
            
            result = await service._execute_action(data)
            
            assert result["save_success"] is True
            mock_save.assert_called_once()

    @pytest.mark.asyncio
    async def test_final_save_create_directory(self, service, sample_case_details):
        """Test Final Save step FS1: Create Court/Date Directory."""
        data = {
            "action": "create_court_date_directory",
            "case_details": sample_case_details,
            "iso_date": "2024-01-01"
        }
        
        with pytest.patch.object(service, 'create_court_date_directory') as mock_create:
            mock_create.return_value = {
                "directory_created": True,
                "directory_path": "/tmp/data/2024-01-01/cand",
                "already_existed": False
            }
            
            result = await service._execute_action(data)
            
            assert result["directory_created"] is True
            assert "cand" in result["directory_path"]

    @pytest.mark.asyncio
    async def test_final_save_generate_filename(self, service, sample_case_details):
        """Test Final Save step FS2: Generate Filename."""
        data = {
            "action": "generate_filename",
            "case_details": sample_case_details,
            "filename_base": "1-23-cv-12345_test_case"
        }
        
        with pytest.patch.object(service, 'generate_filename') as mock_generate:
            mock_generate.return_value = {
                "filename": "1-23-cv-12345_test_case.json",
                "full_path": "/tmp/data/2024-01-01/cand/1-23-cv-12345_test_case.json"
            }
            
            result = await service._execute_action(data)
            
            assert result["filename"].endswith(".json")
            assert "1-23-cv-12345" in result["filename"]

    @pytest.mark.asyncio
    async def test_final_save_upload_enabled_check(self, service, sample_case_details):
        """Test Final Save step FS5: Upload Enabled check."""
        # Test with upload disabled
        data = {
            "action": "check_upload_enabled",
            "case_data": sample_case_details
        }
        
        with pytest.patch.object(service, 'check_upload_enabled') as mock_check:
            mock_check.return_value = {
                "upload_enabled": False,
                "reason": "upload disabled in config"
            }
            
            result = await service._execute_action(data)
            
            assert result["upload_enabled"] is False

    @pytest.mark.asyncio
    async def test_final_save_s3_upload_success(self, service, sample_case_details):
        """Test Final Save step FS6 -> FS8: S3 Upload Success."""
        data = {
            "action": "upload_to_s3",
            "local_file": "/tmp/case.json", 
            "case_data": sample_case_details
        }
        
        with pytest.patch.object(service, 'upload_to_s3') as mock_upload:
            mock_upload.return_value = {
                "upload_success": True,
                "s3_url": "s3://bucket/cases/case.json",
                "upload_time_ms": 1500
            }
            
            result = await service._execute_action(data)
            
            assert result["upload_success"] is True
            assert result["s3_url"].startswith("s3://")


class TestRelevanceAnalysisServiceUnit:
    """Unit tests for RelevanceAnalysisService - Core Service #7 (Phase 2 Part 1)."""

    @pytest.fixture
    def service(self, mock_logger, base_config, sample_relevance_config):
        """Create RelevanceAnalysisService instance."""
        service = RelevanceFacadeService(
            logger=mock_logger,
            config={**base_config, "relevance": sample_relevance_config}
        )
        return service

    def test_service_inherits_from_async_service_base(self, service):
        """Test RelevanceAnalysisService inherits from AsyncServiceBase."""
        from src.infrastructure.patterns.component_base import AsyncServiceBase
        assert isinstance(service, AsyncServiceBase)

    @pytest.mark.asyncio
    async def test_execute_action_determine_relevance(self, service, sample_case_details):
        """Test _execute_action with determine_relevance action."""
        with pytest.patch.object(service, 'determine_relevance') as mock_determine:
            mock_determine.return_value = {
                "relevance": "relevant",
                "reason": "PFAS case detected",
                "confidence": 0.95
            }
            
            data = {
                "action": "determine_relevance",
                "case_details": sample_case_details
            }
            
            result = await service._execute_action(data)
            
            assert result["relevance"] == "relevant"
            mock_determine.assert_called_once_with(sample_case_details)

    @pytest.mark.asyncio
    async def test_relevance_mdl_flag_override(self, service):
        """Test relevance determination with MDL flag override (P2_2 -> P2_3)."""
        mdl_case = {
            "case_name": "Regular Contract Case",
            "cause": "Contract Dispute",
            "mdl_flag": True,
            "mdl_number": "MDL-2873"
        }
        
        data = {
            "action": "determine_relevance",
            "case_details": mdl_case
        }
        
        with pytest.patch.object(service, 'determine_relevance') as mock_determine:
            mock_determine.return_value = {
                "relevance": "relevant",
                "reason": "MDL flag override",
                "mdl_override": True
            }
            
            result = await service._execute_action(data)
            
            assert result["relevance"] == "relevant"
            assert result["mdl_override"] is True

    @pytest.mark.asyncio
    async def test_relevance_explicitly_relevant_terms(self, service):
        """Test relevance for explicitly relevant terms (P2_8 -> P2_9)."""
        pfas_case = {
            "case_name": "PFAS Water Contamination Lawsuit",
            "cause": "42:1983 Environmental Civil Rights",
            "mdl_flag": False
        }
        
        data = {
            "action": "determine_relevance",
            "case_details": pfas_case
        }
        
        with pytest.patch.object(service, 'determine_relevance') as mock_determine:
            mock_determine.return_value = {
                "relevance": "relevant",
                "reason": "explicitly relevant - PFAS detected",
                "matched_terms": ["PFAS"],
                "explicit_match": True
            }
            
            result = await service._execute_action(data)
            
            assert result["relevance"] == "relevant"
            assert "PFAS" in str(result["matched_terms"])

    @pytest.mark.asyncio
    async def test_relevance_excluded_statute(self, service):
        """Test relevance for excluded statute (P2_6 -> P2_7)."""
        excluded_case = {
            "case_name": "Securities Fraud Case",
            "cause": "15:78 Securities Exchange Act",
            "mdl_flag": False
        }
        
        data = {
            "action": "determine_relevance", 
            "case_details": excluded_case
        }
        
        with pytest.patch.object(service, 'determine_relevance') as mock_determine:
            mock_determine.return_value = {
                "relevance": "not_relevant",
                "reason": "excluded by statute 15:78",
                "excluded_statute": "15:78"
            }
            
            result = await service._execute_action(data)
            
            assert result["relevance"] == "not_relevant"
            assert "15:78" in result["reason"]


class TestAnalyticsServiceUnit:
    """Unit tests for AnalyticsService - Core Service #8."""

    @pytest.fixture
    def mock_mdl_summarizer(self):
        """Mock MDL summarizer component."""
        summarizer = AsyncMock()
        summarizer.execute = AsyncMock(return_value={
            "mdl_summary": {
                "total_cases": 150,
                "mdl_numbers": ["MDL-2873", "MDL-2996"],
                "top_courts": ["cand", "nysd"]
            }
        })
        return summarizer

    @pytest.fixture
    def service(self, mock_mdl_summarizer, mock_logger, base_config):
        """Create AnalyticsService instance."""
        return AnalyticsFacadeService(
            mdl_summarizer=mock_mdl_summarizer,
            logger=mock_logger,
            config=base_config
        )

    def test_service_inherits_from_async_service_base(self, service):
        """Test AnalyticsService inherits from AsyncServiceBase."""
        from src.infrastructure.patterns.component_base import AsyncServiceBase
        assert isinstance(service, AsyncServiceBase)

    @pytest.mark.asyncio
    async def test_execute_action_generate_mdl_summary(self, service):
        """Test _execute_action with generate_mdl_summary action."""
        with pytest.patch.object(service, 'generate_mdl_summary') as mock_generate:
            mock_generate.return_value = {
                "mdl_statistics": {
                    "total_mdl_cases": 145,
                    "active_mdls": ["MDL-2873"],
                    "completion_rate": 0.85
                }
            }
            
            data = {
                "action": "generate_mdl_summary",
                "date_range": "2024-01-01 to 2024-01-31"
            }
            
            result = await service._execute_action(data)
            
            assert result["mdl_statistics"]["total_mdl_cases"] == 145
            mock_generate.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_action_analyze_processing_metrics(self, service):
        """Test _execute_action with analyze_processing_metrics action."""
        with pytest.patch.object(service, 'analyze_processing_metrics') as mock_analyze:
            mock_analyze.return_value = {
                "processing_stats": {
                    "total_processed": 500,
                    "success_rate": 0.95,
                    "average_processing_time_ms": 2500,
                    "error_rate": 0.05
                }
            }
            
            data = {
                "action": "analyze_processing_metrics",
                "time_period": "last_24_hours"
            }
            
            result = await service._execute_action(data)
            
            assert result["processing_stats"]["success_rate"] == 0.95
            mock_analyze.assert_called_once()

    @pytest.mark.asyncio
    async def test_analytics_workflow_performance_tracking(self, service):
        """Test analytics for workflow performance tracking."""
        data = {
            "action": "track_workflow_performance",
            "workflow_data": {
                "phase_1_duration_ms": 1500,
                "phase_2_duration_ms": 800,
                "phase_3_duration_ms": 600,
                "phase_4_duration_ms": 3000,
                "total_duration_ms": 5900
            }
        }
        
        with pytest.patch.object(service, 'track_workflow_performance') as mock_track:
            mock_track.return_value = {
                "performance_analysis": {
                    "bottleneck_phase": "phase_4",
                    "efficiency_score": 0.78,
                    "recommendations": ["optimize download timeout"]
                }
            }
            
            result = await service._execute_action(data)
            
            assert result["performance_analysis"]["bottleneck_phase"] == "phase_4"


class TestCoreServicesErrorHandling:
    """Test error handling across all core services."""

    @pytest.mark.asyncio
    async def test_service_initialization_failures(self, mock_logger, base_config):
        """Test handling of service initialization failures."""
        # Test with invalid dependencies
        invalid_service = DownloadOrchestrationFacadeService(
            download_manager=None,  # Invalid dependency
            logger=mock_logger,
            config=base_config
        )
        
        with pytest.raises(AttributeError):
            await invalid_service._execute_action({"action": "process_download_workflow"})

    @pytest.mark.asyncio
    async def test_missing_configuration_handling(self, mock_logger):
        """Test handling of missing configuration."""
        service = RelevanceFacadeService(
            logger=mock_logger,
            config={}  # Empty config
        )
        
        with pytest.patch.object(service, 'determine_relevance') as mock_determine:
            mock_determine.side_effect = KeyError("Configuration missing")
            
            with pytest.raises(KeyError):
                await service._execute_action({
                    "action": "determine_relevance", 
                    "case_details": {"case_number": "test"}
                })

    @pytest.mark.asyncio
    async def test_network_timeout_handling(self, mock_download_manager, mock_logger, base_config):
        """Test handling of network timeouts during operations."""
        service = DownloadOrchestrationFacadeService(
            download_manager=mock_download_manager,
            logger=mock_logger,
            config=base_config
        )
        
        mock_download_manager.execute.side_effect = TimeoutError("Network timeout")
        
        with pytest.raises(TimeoutError):
            await service._execute_action({
                "action": "process_download_workflow",
                "case_details": {"case_number": "test"}
            })

    @pytest.mark.asyncio
    async def test_file_system_errors_handling(self, mock_file_manager, mock_logger, base_config):
        """Test handling of file system errors."""
        service = FileManagementFacadeService(
            file_manager=mock_file_manager,
            logger=mock_logger,
            config=base_config
        )
        
        mock_file_manager.execute.side_effect = PermissionError("Access denied")
        
        with pytest.raises(PermissionError):
            await service._execute_action({
                "action": "save_and_upload_case_data",
                "case_data": {"case_number": "test"}
            })

    @pytest.mark.asyncio
    async def test_concurrent_access_error_handling(self, mock_logger, base_config):
        """Test handling of concurrent access conflicts."""
        import asyncio
        
        service = RelevanceFacadeService(logger=mock_logger, config=base_config)
        
        # Simulate concurrent access conflict
        with pytest.patch.object(service, 'determine_relevance') as mock_determine:
            mock_determine.side_effect = RuntimeError("Concurrent access conflict")
            
            tasks = [
                service._execute_action({
                    "action": "determine_relevance",
                    "case_details": {"case_number": f"test-{i}"}
                })
                for i in range(3)
            ]
            
            with pytest.raises(RuntimeError):
                await asyncio.gather(*tasks)