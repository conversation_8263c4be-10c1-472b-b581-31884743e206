#!/usr/bin/env python3
"""
Test S3 Service Configuration Fix Validation

This test validates that the S3 service dependency injection issue has been resolved
and that HTML upload functionality is available through the PACER container.
"""

import asyncio
import logging
import os
import sys
from typing import Any, Dict

# Add project root to path
sys.path.insert(0, '/Users/<USER>/PycharmProjects/lexgenius')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_s3_service_availability():
    """Test that S3 service is available through dependency injection."""
    logger.info("🔍 Testing S3 service availability through DI container...")
    
    try:
        # Test 1: Import and create the container
        from src.containers.pacer_core import PacerCoreContainer
        from src.containers.storage import StorageContainer
        
        # Create a test configuration
        test_config = {
            'headless': True,
            'timeout_ms': 30000,
            's3_bucket_name': os.getenv('S3_BUCKET_NAME', 'test-bucket'),
            'aws_access_key': os.getenv('AWS_ACCESS_KEY_ID', 'test-key'),
            'aws_secret_key': os.getenv('AWS_SECRET_ACCESS_KEY', 'test-secret'),
            'aws_region': os.getenv('AWS_REGION', 'us-west-2')
        }
        
        # Create storage container
        storage_container = StorageContainer()
        storage_container.config.from_dict(test_config)
        storage_container.logger.override(logger)
        storage_container.aws_region.override(test_config['aws_region'])
        storage_container.aws_access_key.override(test_config['aws_access_key'])
        storage_container.aws_secret_key.override(test_config['aws_secret_key'])
        storage_container.s3_bucket_name.override(test_config['s3_bucket_name'])
        storage_container.dynamodb_endpoint.override(None)
        
        # Create PACER container
        container = PacerCoreContainer()
        container.config.from_dict(test_config)
        container.logger.override(logger)
        container.storage_container.override(storage_container)
        container.shutdown_event.override(asyncio.Event())
        
        # Wire the container
        container.wire(modules=[__name__])
        
        logger.info("✅ Container created and wired successfully")
        
        # Test 2: Get S3 management service
        try:
            s3_service = container.s3_management_service()
            logger.info(f"✅ S3ManagementService retrieved: {type(s3_service).__name__}")
            
            # Test 3: Initialize the service
            await s3_service.initialize()
            logger.info("✅ S3ManagementService initialized successfully")
            
            # Test 4: Check service health
            health = await s3_service.health_check()
            logger.info(f"✅ S3ManagementService health check: {health}")
            
            # Test 5: Test HTML upload action (mock test)
            test_html_upload_data = {
                'action': 'upload_html',
                'base_filename': 'test_case_12345',
                'html_content': '<html><body><h1>Test Docket</h1></body></html>',
                'iso_date': '2025-01-15',
                'court_logger': logger
            }
            
            upload_result = await s3_service._execute_action(test_html_upload_data)
            logger.info(f"✅ HTML upload test result: {upload_result}")
            
            if upload_result.get('success') or 'not enabled' in str(upload_result.get('error', '')):
                logger.info("✅ HTML upload functionality is working (or properly handled when S3 not configured)")
            else:
                logger.error(f"❌ HTML upload test failed: {upload_result}")
                return False
            
            logger.info("🎉 ALL S3 SERVICE TESTS PASSED!")
            return True
            
        except Exception as service_error:
            logger.error(f"❌ S3 service error: {service_error}", exc_info=True)
            return False
        
    except Exception as e:
        logger.error(f"❌ Container setup error: {e}", exc_info=True)
        return False
    
    finally:
        # Cleanup
        try:
            if 'container' in locals():
                container.unwire()
        except:
            pass


async def test_s3_service_direct():
    """Test S3Service directly to ensure it works."""
    logger.info("🔍 Testing S3Service directly...")
    
    try:
        from src.pacer.services.s3_service import S3Service
        
        # Create test configuration
        test_config = {
            's3_bucket_name': os.getenv('S3_BUCKET_NAME', 'test-bucket'),
            'aws_access_key': os.getenv('AWS_ACCESS_KEY_ID', 'test-key'),
            'aws_secret_key': os.getenv('AWS_SECRET_ACCESS_KEY', 'test-secret'),
            'aws_region': os.getenv('AWS_REGION', 'us-west-2')
        }
        
        # Create S3Service
        s3_service = S3Service(logger=logger, config=test_config)
        
        # Initialize
        await s3_service.initialize()
        logger.info("✅ S3Service direct initialization successful")
        
        # Test HTML upload action
        test_data = {
            'action': 'upload_html',
            'base_filename': 'direct_test_12345',
            'html_content': '<html><body><h1>Direct Test Docket</h1></body></html>',
            'iso_date': '2025-01-15',
            'court_logger': logger
        }
        
        result = await s3_service._execute_action(test_data)
        logger.info(f"✅ Direct S3Service HTML upload result: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Direct S3Service test error: {e}", exc_info=True)
        return False


async def test_s3_bridge_service():
    """Test the S3ManagementService bridge."""
    logger.info("🔍 Testing S3ManagementService bridge...")
    
    try:
        from src.pacer._core_services.s3_management.s3_management_service import S3ManagementService
        
        # Create test configuration
        test_config = {
            's3_bucket_name': os.getenv('S3_BUCKET_NAME', 'test-bucket'),
            'aws_access_key': os.getenv('AWS_ACCESS_KEY_ID', 'test-key'), 
            'aws_secret_key': os.getenv('AWS_SECRET_ACCESS_KEY', 'test-secret'),
            'aws_region': os.getenv('AWS_REGION', 'us-west-2')
        }
        
        # Create S3ManagementService
        s3_mgmt_service = S3ManagementService(logger=logger, config=test_config)
        
        # Initialize
        await s3_mgmt_service.initialize()
        logger.info("✅ S3ManagementService bridge initialization successful")
        
        # Test HTML upload action
        test_data = {
            'action': 'upload_html',
            'base_filename': 'bridge_test_12345',
            'html_content': '<html><body><h1>Bridge Test Docket</h1></body></html>',
            'iso_date': '2025-01-15',
            'court_logger': logger
        }
        
        result = await s3_mgmt_service._execute_action(test_data)
        logger.info(f"✅ S3ManagementService bridge HTML upload result: {result}")
        
        # Test health check
        health = await s3_mgmt_service.health_check()
        logger.info(f"✅ S3ManagementService bridge health: {health}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ S3ManagementService bridge test error: {e}", exc_info=True)
        return False


async def main():
    """Run all S3 service fix validation tests."""
    logger.info("🚀 Starting S3 Service Fix Validation Tests...")
    
    results = []
    
    # Test 1: Direct S3Service
    results.append(await test_s3_service_direct())
    
    # Test 2: S3ManagementService bridge
    results.append(await test_s3_bridge_service())
    
    # Test 3: Container dependency injection
    results.append(await test_s3_service_availability())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    logger.info(f"\n📊 S3 Service Fix Validation Results:")
    logger.info(f"   Tests Passed: {passed}/{total}")
    logger.info(f"   Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        logger.info("🎉 ALL S3 SERVICE FIX VALIDATION TESTS PASSED!")
        logger.info("✅ S3 service is now available for HTML upload functionality")
        return True
    else:
        logger.error("❌ Some S3 service tests failed - investigation needed")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)