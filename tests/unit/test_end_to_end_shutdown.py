import pytest
import asyncio
import logging
from datetime import datetime
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import sys
import os

# Add the project root to sys.path to enable imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

@pytest.mark.asyncio
async def test_complete_shutdown_flow(test_container):
    """Test that shutdown events flow correctly through the complete architecture."""
    shutdown_event = asyncio.Event()
    
    # Create mock config
    from pydantic import BaseModel
    config = MagicMock(spec=BaseModel)
    config.model_dump.return_value = {
        'directories': {'base_dir': '/test/path'},
        'DATA_DIR': '/test/data',
        'llm_provider': 'deepseek',
        'deepseek_api_key': 'test_key',
        'name': 'test_workflow',
        'scraper': True,
        'post_process': True,
        'upload': False,
        'upload_json_to_dynamodb': False,
        'fb_ads': False,
        'report_generator': False
    }
    # Add the missing method to the mock
    config.get_transformer_config_dict = MagicMock()
    config.get_transformer_config_dict.return_value = {
        'directories': {'base_dir': '/test/path'},
        'DATA_DIR': '/test/data'
    }
    # Add all the attributes that ProcessingOrchestrator.execute() expects
    config.scraper = True
    config.post_process = True
    config.upload = False
    config.upload_json_to_dynamodb = False  # Disable DynamoDB to avoid AWS issues
    config.fb_ads = False
    config.report_generator = False
    config.reprocess_files = False
    config.start_from_incomplete = False
    config.skip_files = None
    config.num_workers = 1
    config.normalize_law_firm_names = False
    config.reprocess_mdl_num = False
    
    # Test MainServiceFactory -> ProcessingOrchestrator -> DataTransformer chain
    with patch('src.factories.main_factory.ScrapingOrchestrator') as MockScrapingOrchestrator, \
         patch('src.factories.main_factory.ProcessingOrchestrator') as MockProcessingOrchestrator, \
         patch('src.services.orchestration.processing_orchestrator.DataTransformer') as MockDataTransformer, \
         patch('src.services.orchestration.processing_orchestrator.DeepSeekClient'), \
         patch('src.services.orchestration.processing_orchestrator.DeepSeekService') as mock_deepseek_service, \
         patch('src.services.orchestration.processing_orchestrator.GPTClient'), \
         patch('src.pacer.pacer_orchestrator_service.PacerOrchestratorService'), \
         patch('os.makedirs'), \
         patch('src.transformer.data_transformer.logging.FileHandler') as mock_file_handler, \
         patch.dict(os.environ, {'AWS_ACCESS_KEY': '', 'AWS_SECRET_KEY': ''}, clear=False), \
         patch('src.factories.main_factory.ContainerFactory') as MockContainerFactory, \
         patch('src.factories.main_factory.DIContainer') as MockDIContainer:
        
        # Set up the DI globals before importing MainServiceFactory
        import src.factories.main_factory
        src.factories.main_factory.DI_AVAILABLE = True
        src.factories.main_factory.ContainerFactory = MockContainerFactory
        src.factories.main_factory.DIContainer = MockDIContainer
        
        from src.factories.main_factory import MainServiceFactory
        from src.services.orchestration.main_orchestrator import MainOrchestrator
        
        # Create proper mock handler
        mock_handler = MagicMock()
        mock_handler.level = 0
        mock_file_handler.return_value = mock_handler
        
        # Setup DI container mocks
        mock_di_container = AsyncMock()
        mock_container_factory_instance = MagicMock()
        mock_container_factory_instance.create_container = AsyncMock(return_value=mock_di_container)
        MockContainerFactory.return_value = mock_container_factory_instance
        MockDIContainer.return_value = mock_di_container
        
        # Create mock DeepSeekService with proper async support
        mock_deepseek_instance = AsyncMock()
        mock_deepseek_instance.initialize = AsyncMock()
        mock_deepseek_instance.set_dependency = MagicMock()
        mock_deepseek_service.return_value = mock_deepseek_instance
        
        # Mock the data transformer
        mock_transformer_instance = AsyncMock()
        mock_transformer_instance._check_shutdown.return_value = False
        MockDataTransformer.return_value = mock_transformer_instance
        
        # Mock the processing orchestrator
        mock_processing_orchestrator = AsyncMock()
        mock_processing_orchestrator.execute.return_value = []
        MockProcessingOrchestrator.return_value = mock_processing_orchestrator
        
        # Mock the scraping orchestrator
        mock_scraping_orchestrator = AsyncMock()
        MockScrapingOrchestrator.return_value = mock_scraping_orchestrator
        
        # Test 1: Normal operation without shutdown
        async with MainServiceFactory(config, shutdown_event) as factory:
            orchestrator = MainOrchestrator(config, factory, shutdown_event)
            
            # Set up factory methods
            factory.create_scraping_orchestrator = AsyncMock(return_value=mock_scraping_orchestrator)
            factory.create_processing_orchestrator = AsyncMock(return_value=mock_processing_orchestrator)
            
            await orchestrator.run()
            
            # Verify orchestrators were created and executed
            factory.create_scraping_orchestrator.assert_called_once()
            factory.create_processing_orchestrator.assert_called_once()
            mock_scraping_orchestrator.execute.assert_called_once()
            mock_processing_orchestrator.execute.assert_called_once()
        
        # Reset mocks
        factory.create_scraping_orchestrator.reset_mock()
        factory.create_processing_orchestrator.reset_mock()
        mock_scraping_orchestrator.execute.reset_mock()
        mock_processing_orchestrator.execute.reset_mock()
        
        # Test 2: Shutdown before processing
        shutdown_event.set()
        
        async with MainServiceFactory(config, shutdown_event) as factory:
            orchestrator = MainOrchestrator(config, factory, shutdown_event)
            
            # Set up factory methods
            factory.create_scraping_orchestrator = AsyncMock(return_value=mock_scraping_orchestrator)
            factory.create_processing_orchestrator = AsyncMock(return_value=mock_processing_orchestrator)
            
            await orchestrator.run()
            
            # Should not have proceeded past first shutdown check
            factory.create_scraping_orchestrator.assert_not_called()
            factory.create_processing_orchestrator.assert_not_called()
        
        # Test 3: Verify shutdown_event is passed to DataTransformer
        shutdown_event.clear()  # Reset for this test
        
        async with MainServiceFactory(config, shutdown_event) as factory:
            factory.create_processing_orchestrator = AsyncMock(return_value=mock_processing_orchestrator)
            
            # Create ProcessingOrchestrator and verify shutdown_event is passed to DataTransformer
            from src.services.orchestration.processing_orchestrator import ProcessingOrchestrator
            
            # Create mock transformer instance that supports async context manager
            mock_transformer_instance = AsyncMock()
            mock_transformer_instance.__aenter__ = AsyncMock(return_value=mock_transformer_instance)
            mock_transformer_instance.__aexit__ = AsyncMock(return_value=None)
            mock_transformer_instance.start = AsyncMock(return_value=[])
            MockDataTransformer.return_value = mock_transformer_instance
            
            processing_orch = ProcessingOrchestrator(config, shutdown_event)
            
            # DataTransformer is only created during execute() -> _initialize_services()
            # Need to call execute to trigger the DataTransformer creation
            await processing_orch.execute()
            
            # Verify DataTransformer was called with shutdown_event
            MockDataTransformer.assert_called()
            call_kwargs = MockDataTransformer.call_args.kwargs
            assert 'shutdown_event' in call_kwargs
            assert call_kwargs['shutdown_event'] is shutdown_event

@pytest.mark.asyncio
async def test_data_transformer_shutdown_in_processing_phases(test_container):
    """Test that DataTransformer correctly handles shutdown between processing phases."""
    shutdown_event = asyncio.Event()
    
    config = {
        'directories': {'base_dir': '/test/path'},
        'DATA_DIR': '/test/data'
    }
    
    logger = logging.getLogger('test_shutdown_phases')
    logger.handlers.clear()
    
    from src.transformer.data_transformer import DataTransformer
        
    with patch('src.transformer.data_transformer.ComponentFactory'), \
         patch('src.transformer.data_transformer.DataProcessingEngine'), \
         patch('src.transformer.data_transformer.ErrorHandler'), \
         patch('src.transformer.data_transformer.FileOperationsManager'), \
         patch('src.transformer.data_transformer.SpecializedWorkflows'), \
         patch('src.transformer.data_transformer.time.time', return_value=1000.0), \
         patch('src.transformer.data_transformer.os.makedirs'), \
         patch('src.transformer.data_transformer.logging.FileHandler'), \
         patch.object(DataTransformer, '_setup_transformer_logging', return_value=None):
        
        transformer = DataTransformer(config, logger, shutdown_event=shutdown_event)
        
        # Test shutdown checks during workflow
        assert transformer._check_shutdown() is False
        
        # Set shutdown event
        shutdown_event.set()
        assert transformer._check_shutdown() is True
        
        # Test shutdown check functionality - this is the core test
        # The _check_shutdown method should work correctly
        assert transformer.shutdown_event is shutdown_event

@pytest.mark.asyncio
async def test_fb_ads_orchestrator_with_shutdown(test_container):
    """Test that FbAdsOrchestrator correctly handles shutdown events."""
    shutdown_event = asyncio.Event()
    
    config = MagicMock()
    config.model_dump.return_value = {'fb_ads': True}
    config.fb_ads = True
    
    from src.services.orchestration.fb_ads_orchestrator import FbAdsOrchestrator
    
    orchestrator = FbAdsOrchestrator(config, shutdown_event)
    
    # Test shutdown check method
    assert orchestrator._check_shutdown() is False
    
    shutdown_event.set()
    assert orchestrator._check_shutdown() is True
    
    # Test early exit on shutdown
    result = await orchestrator.execute()
    assert result is None  # Should exit early

@pytest.mark.asyncio
async def test_shutdown_event_propagation_chain(test_container):
    """Test the complete shutdown event propagation chain."""
    shutdown_event = asyncio.Event()
    
    # Test the chain: MainServiceFactory -> MainOrchestrator -> ProcessingOrchestrator -> DataTransformer
    with patch('src.main.PROJECT_ROOT', '/test/path'), \
         patch('src.main.MainServiceFactory') as MockMainServiceFactory, \
         patch('src.main.MainOrchestrator') as MockMainOrchestrator, \
         patch('src.main.os.path.exists', return_value=True), \
         patch('src.main.load_config_new') as mock_load_config, \
         patch('src.main.glob.glob', return_value=['/test/path/test.json']), \
         patch('src.main.setup_rich_logging'), \
         patch('src.main.os.makedirs'):
        
        # Main import - using mocks instead
        
        # Mock config
        mock_config = MagicMock()
        mock_load_config.return_value = mock_config
        
        # Mock factory as async context manager
        mock_factory_instance = AsyncMock()
        mock_factory_context = AsyncMock()
        mock_factory_context.__aenter__ = AsyncMock(return_value=mock_factory_instance)
        mock_factory_context.__aexit__ = AsyncMock(return_value=None)
        MockMainServiceFactory.return_value = mock_factory_context
        
        # Mock orchestrator
        mock_orchestrator_instance = AsyncMock()
        MockMainOrchestrator.return_value = mock_orchestrator_instance
        
        # Create DateProcessor with shutdown event
        processor = DateProcessor({
            'base_data_path': '/test/data',
            'config_yaml_path': '/test/config.yml',
            'post_process': True
        }, shutdown_event)
        
        processor._get_directory_path = MagicMock(return_value='/test/path')
        processor._get_json_files = MagicMock(return_value=['/test/path/test.json'])
        
        await processor.process_single_date('01/01/25')
        
        # Verify complete chain of shutdown_event propagation
        # DateProcessor -> MainServiceFactory
        MockMainServiceFactory.assert_called_once()
        factory_args = MockMainServiceFactory.call_args[0]
        assert len(factory_args) == 2
        assert factory_args[1] is shutdown_event
        
        # DateProcessor -> MainOrchestrator
        MockMainOrchestrator.assert_called_once()
        orchestrator_args = MockMainOrchestrator.call_args[0]
        assert len(orchestrator_args) == 3
        assert orchestrator_args[2] is shutdown_event

if __name__ == '__main__':
    pytest.main([__file__, '-v'])