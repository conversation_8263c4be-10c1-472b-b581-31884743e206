"""Unit tests for S3Service components and functionality.

Focuses on:
1. Individual S3Service method testing
2. Configuration validation
3. Error handling edge cases
4. Mock validation
"""

import pytest
import os
import tempfile
from unittest.mock import MagicMock, AsyncMock, patch
from pathlib import Path
from datetime import datetime

from src.pacer.services.s3_service import S3Service
from src.pacer._core_services.s3_management.s3_management_service import S3ManagementService


class TestS3ServiceConfiguration:
    """Test S3Service configuration handling."""
    
    def test_s3_service_init_with_config_service(self):
        """Test S3Service initialization with config service."""
        mock_logger = MagicMock()
        mock_config = {'test_key': 'test_value'}
        mock_config_service = MagicMock()
        
        service = S3Service(
            logger=mock_logger,
            config=mock_config,
            config_service=mock_config_service
        )
        
        assert service.logger == mock_logger
        assert service.config == mock_config
        assert service.config_service == mock_config_service
        assert service.s3_client is None
        assert service.enabled is False
    
    def test_s3_service_init_without_config_service(self):
        """Test S3Service initialization without config service."""
        mock_logger = MagicMock()
        mock_config = {'test_key': 'test_value'}
        
        service = S3Service(logger=mock_logger, config=mock_config)
        
        assert service.logger == mock_logger
        assert service.config == mock_config
        assert service.config_service is None
        assert service.enabled is False
    
    @pytest.mark.asyncio
    async def test_s3_service_initialize_with_config_service(self):
        """Test S3Service initialization process with config service."""
        mock_config_service = AsyncMock()
        mock_config_service.get_config_value.return_value = {
            'bucket_name': 'test-bucket',
            'region': 'us-east-1',
            'enabled': True
        }
        
        service = S3Service(config_service=mock_config_service)
        
        with patch.dict(os.environ, {
            'AWS_ACCESS_KEY_ID': 'test-key',
            'AWS_SECRET_ACCESS_KEY': 'test-secret'
        }):
            with patch('boto3.client') as mock_boto3:
                mock_client = MagicMock()
                mock_boto3.return_value = mock_client
                mock_client.list_objects_v2.return_value = {'Contents': []}
                
                await service._initialize_service()
                
                assert service.bucket_name == 'test-bucket'
                assert service.aws_region == 'us-east-1'
                assert service.enabled is True
                assert service.s3_client is not None
    
    @pytest.mark.asyncio
    async def test_s3_service_initialize_without_config_service(self):
        """Test S3Service initialization process without config service."""
        service = S3Service()
        
        with patch.dict(os.environ, {
            'S3_BUCKET_NAME': 'env-bucket',
            'AWS_REGION': 'us-west-2',
            'S3_ENABLED': 'true',
            'AWS_ACCESS_KEY_ID': 'env-key',
            'AWS_SECRET_ACCESS_KEY': 'env-secret'
        }):
            with patch('boto3.client') as mock_boto3:
                mock_client = MagicMock()
                mock_boto3.return_value = mock_client
                mock_client.list_objects_v2.return_value = {'Contents': []}
                
                await service._initialize_service()
                
                assert service.bucket_name == 'env-bucket'
                assert service.aws_region == 'us-west-2'
                assert service.enabled is True
    
    @pytest.mark.asyncio
    async def test_s3_service_initialize_boto3_not_available(self):
        """Test S3Service handles missing boto3 gracefully."""
        service = S3Service()
        service.log_warning = MagicMock()
        
        with patch.dict(os.environ, {
            'S3_BUCKET_NAME': 'test-bucket',
            'AWS_ACCESS_KEY_ID': 'test-key',
            'AWS_SECRET_ACCESS_KEY': 'test-secret',
            'S3_ENABLED': 'true'
        }):
            with patch('boto3.client', side_effect=ImportError('boto3 not found')):
                await service._initialize_service()
                
                assert service.enabled is False
                assert service.s3_client is None
                service.log_warning.assert_called_with('boto3 not available - S3 functionality disabled')


class TestS3ServiceMethods:
    """Test individual S3Service methods."""
    
    @pytest.fixture
    def mock_s3_service(self):
        """Create a mock S3Service for testing."""
        service = S3Service()
        service.enabled = True
        service.bucket_name = 'test-bucket'
        service.aws_region = 'us-west-2'
        service.s3_client = MagicMock()
        service.log_info = MagicMock()
        service.log_warning = MagicMock()
        service.log_error = MagicMock()
        service.log_debug = MagicMock()
        return service
    
    @pytest.mark.asyncio
    async def test_upload_file_success(self, mock_s3_service):
        """Test successful file upload."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
            temp_file.write('test content')
            temp_file_path = temp_file.name
        
        try:
            # Mock successful upload
            mock_s3_service.s3_client.upload_file.return_value = None
            
            result = await mock_s3_service.upload_file(
                temp_file_path,
                'test/key.txt'
            )
            
            expected_url = f"https://{mock_s3_service.bucket_name}.s3.{mock_s3_service.aws_region}.amazonaws.com/test/key.txt"
            assert result == expected_url
            
            mock_s3_service.s3_client.upload_file.assert_called_once_with(
                Bucket='test-bucket',
                Key='test/key.txt',
                Filename=temp_file_path
            )
        finally:
            os.unlink(temp_file_path)
    
    @pytest.mark.asyncio
    async def test_upload_file_not_found(self, mock_s3_service):
        """Test upload with non-existent file."""
        with pytest.raises(FileNotFoundError):
            await mock_s3_service.upload_file(
                '/non/existent/file.txt',
                'test/key.txt'
            )
    
    @pytest.mark.asyncio
    async def test_upload_file_with_metadata(self, mock_s3_service):
        """Test file upload with metadata."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
            temp_file.write('test content')
            temp_file_path = temp_file.name
        
        try:
            metadata = {'court': 'cand', 'case': '12345'}
            mock_s3_service.s3_client.upload_file.return_value = None
            
            await mock_s3_service.upload_file(
                temp_file_path,
                'test/key.txt',
                metadata=metadata
            )
            
            mock_s3_service.s3_client.upload_file.assert_called_once_with(
                Bucket='test-bucket',
                Key='test/key.txt',
                Filename=temp_file_path,
                ExtraArgs={'Metadata': metadata}
            )
        finally:
            os.unlink(temp_file_path)
    
    @pytest.mark.asyncio
    async def test_download_file_success(self, mock_s3_service):
        """Test successful file download."""
        with tempfile.TemporaryDirectory() as temp_dir:
            download_path = Path(temp_dir) / 'downloaded.txt'
            
            mock_s3_service.s3_client.download_file.return_value = None
            
            result = await mock_s3_service.download_file(
                'test/key.txt',
                str(download_path)
            )
            
            assert result is True
            mock_s3_service.s3_client.download_file.assert_called_once_with(
                Bucket='test-bucket',
                Key='test/key.txt',
                Filename=str(download_path)
            )
    
    @pytest.mark.asyncio
    async def test_file_exists_true(self, mock_s3_service):
        """Test file exists check when file exists."""
        mock_s3_service.s3_client.head_object.return_value = {'ContentLength': 100}
        
        result = await mock_s3_service.file_exists('test/key.txt')
        
        assert result is True
        mock_s3_service.s3_client.head_object.assert_called_once_with(
            Bucket='test-bucket',
            Key='test/key.txt'
        )
    
    @pytest.mark.asyncio
    async def test_file_exists_false(self, mock_s3_service):
        """Test file exists check when file doesn't exist."""
        from botocore.exceptions import ClientError
        
        error = ClientError(
            error_response={'Error': {'Code': 'NoSuchKey', 'Message': 'Not found'}},
            operation_name='HeadObject'
        )
        mock_s3_service.s3_client.head_object.side_effect = error
        
        result = await mock_s3_service.file_exists('test/nonexistent.txt')
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_delete_file_success(self, mock_s3_service):
        """Test successful file deletion."""
        mock_s3_service.s3_client.delete_object.return_value = {}
        
        result = await mock_s3_service.delete_file('test/key.txt')
        
        assert result is True
        mock_s3_service.s3_client.delete_object.assert_called_once_with(
            Bucket='test-bucket',
            Key='test/key.txt'
        )
    
    @pytest.mark.asyncio
    async def test_list_files(self, mock_s3_service):
        """Test file listing functionality."""
        mock_response = {
            'Contents': [
                {
                    'Key': 'test/file1.txt',
                    'Size': 100,
                    'LastModified': datetime(2023, 1, 15),
                    'ETag': '"abc123"'
                },
                {
                    'Key': 'test/file2.txt',
                    'Size': 200,
                    'LastModified': datetime(2023, 1, 16),
                    'ETag': '"def456"'
                }
            ]
        }
        mock_s3_service.s3_client.list_objects_v2.return_value = mock_response
        
        result = await mock_s3_service.list_files(prefix='test/', max_keys=10)
        
        assert len(result) == 2
        assert result[0]['key'] == 'test/file1.txt'
        assert result[0]['size'] == 100
        assert result[0]['etag'] == 'abc123'
        
        mock_s3_service.s3_client.list_objects_v2.assert_called_once_with(
            Bucket='test-bucket',
            Prefix='test/',
            MaxKeys=10
        )
    
    @pytest.mark.asyncio
    async def test_get_file_metadata(self, mock_s3_service):
        """Test file metadata retrieval."""
        mock_response = {
            'ContentLength': 1024,
            'LastModified': datetime(2023, 1, 15),
            'ContentType': 'text/html',
            'Metadata': {'court': 'cand', 'case': '12345'},
            'ETag': '"abc123"'
        }
        mock_s3_service.s3_client.head_object.return_value = mock_response
        
        result = await mock_s3_service.get_file_metadata('test/key.html')
        
        assert result['key'] == 'test/key.html'
        assert result['size'] == 1024
        assert result['content_type'] == 'text/html'
        assert result['metadata'] == {'court': 'cand', 'case': '12345'}
        assert result['etag'] == 'abc123'


class TestS3ServiceErrorHandling:
    """Test S3Service error handling scenarios."""
    
    @pytest.fixture
    def disabled_s3_service(self):
        """Create a disabled S3Service for testing."""
        service = S3Service()
        service.enabled = False
        service.s3_client = None
        return service
    
    @pytest.mark.asyncio
    async def test_upload_file_service_disabled(self, disabled_s3_service):
        """Test upload fails when service is disabled."""
        with pytest.raises(ValueError, match='S3 service is not enabled'):
            await disabled_s3_service.upload_file('test.txt', 'key.txt')
    
    @pytest.mark.asyncio
    async def test_download_file_service_disabled(self, disabled_s3_service):
        """Test download fails when service is disabled."""
        with pytest.raises(ValueError, match='S3 service is not enabled'):
            await disabled_s3_service.download_file('key.txt', 'test.txt')
    
    @pytest.mark.asyncio
    async def test_delete_file_service_disabled(self, disabled_s3_service):
        """Test delete fails when service is disabled."""
        with pytest.raises(ValueError, match='S3 service is not enabled'):
            await disabled_s3_service.delete_file('key.txt')
    
    @pytest.mark.asyncio
    async def test_file_exists_service_disabled(self, disabled_s3_service):
        """Test file exists returns False when service is disabled."""
        result = await disabled_s3_service.file_exists('key.txt')
        assert result is False
    
    @pytest.mark.asyncio
    async def test_list_files_service_disabled(self, disabled_s3_service):
        """Test list files returns empty when service is disabled."""
        result = await disabled_s3_service.list_files()
        assert result == []
    
    @pytest.mark.asyncio
    async def test_get_file_metadata_service_disabled(self, disabled_s3_service):
        """Test get metadata returns None when service is disabled."""
        result = await disabled_s3_service.get_file_metadata('key.txt')
        assert result is None
    
    @pytest.mark.asyncio
    async def test_generate_presigned_url_service_disabled(self, disabled_s3_service):
        """Test presigned URL returns None when service is disabled."""
        result = await disabled_s3_service.generate_presigned_url('key.txt')
        assert result is None


class TestS3ManagementServiceUnit:
    """Test S3ManagementService unit functionality."""
    
    def test_s3_management_service_init(self):
        """Test S3ManagementService initialization."""
        mock_logger = MagicMock()
        mock_config = {'test': 'value'}
        mock_config_service = MagicMock()
        
        service = S3ManagementService(
            logger=mock_logger,
            config=mock_config,
            config_service=mock_config_service
        )
        
        assert service.logger == mock_logger
        assert service.config == mock_config
        assert service.config_service == mock_config_service
        assert isinstance(service._s3_service, S3Service)
    
    @pytest.mark.asyncio
    async def test_s3_management_service_initialize(self):
        """Test S3ManagementService initialization process."""
        service = S3ManagementService()
        service.log_info = MagicMock()
        
        with patch.object(service._s3_service, 'initialize') as mock_init:
            await service._initialize_service()
            mock_init.assert_called_once()
            service.log_info.assert_called_with('S3ManagementService initialized successfully')
    
    @pytest.mark.asyncio
    async def test_s3_management_service_execute_action_delegation(self):
        """Test S3ManagementService delegates actions to S3Service."""
        service = S3ManagementService()
        test_data = {'action': 'test_action'}
        expected_result = {'success': True}
        
        with patch.object(service._s3_service, '_execute_action') as mock_execute:
            mock_execute.return_value = expected_result
            
            result = await service._execute_action(test_data)
            
            assert result == expected_result
            mock_execute.assert_called_once_with(test_data)
    
    @pytest.mark.asyncio
    async def test_s3_management_service_method_delegation(self):
        """Test S3ManagementService delegates methods to S3Service."""
        service = S3ManagementService()
        
        # Test upload_file delegation
        with patch.object(service._s3_service, 'upload_file') as mock_upload:
            mock_upload.return_value = 'test-url'
            result = await service.upload_file('local.txt', 's3-key.txt')
            assert result == 'test-url'
            mock_upload.assert_called_once_with('local.txt', 's3-key.txt', None)
        
        # Test download_file delegation
        with patch.object(service._s3_service, 'download_file') as mock_download:
            mock_download.return_value = True
            result = await service.download_file('s3-key.txt', 'local.txt')
            assert result is True
            mock_download.assert_called_once_with('s3-key.txt', 'local.txt')
        
        # Test file_exists delegation
        with patch.object(service._s3_service, 'file_exists') as mock_exists:
            mock_exists.return_value = True
            result = await service.file_exists('s3-key.txt')
            assert result is True
            mock_exists.assert_called_once_with('s3-key.txt')
        
        # Test delete_file delegation
        with patch.object(service._s3_service, 'delete_file') as mock_delete:
            mock_delete.return_value = True
            result = await service.delete_file('s3-key.txt')
            assert result is True
            mock_delete.assert_called_once_with('s3-key.txt')
    
    @pytest.mark.asyncio
    async def test_s3_management_service_properties(self):
        """Test S3ManagementService property access."""
        service = S3ManagementService()
        service._s3_service.enabled = True
        service._s3_service.bucket_name = 'test-bucket'
        
        assert service.enabled is True
        assert service.bucket_name == 'test-bucket'
    
    @pytest.mark.asyncio
    async def test_s3_management_service_health_check(self):
        """Test S3ManagementService health check."""
        service = S3ManagementService()
        service._initialized = True
        
        mock_s3_health = {
            'service': 'S3Service',
            'status': 'healthy',
            'enabled': True
        }
        
        with patch.object(service._s3_service, 'health_check') as mock_health:
            mock_health.return_value = mock_s3_health
            
            result = await service.health_check()
            
            assert result['service'] == 'S3ManagementService'
            assert result['status'] == 'healthy'
            assert result['underlying_s3_service'] == mock_s3_health


class TestS3ServiceValidationEdgeCases:
    """Test edge cases and validation scenarios."""
    
    @pytest.mark.asyncio
    async def test_html_upload_action_with_empty_content(self):
        """Test HTML upload action handles empty content."""
        service = S3Service()
        service.enabled = True
        service.s3_client = MagicMock()
        
        result = await service._execute_action({
            'action': 'upload_html',
            'base_filename': 'test',
            'html_content': '',  # Empty content
            'iso_date': '2023-01-15'
        })
        
        # Should still attempt upload even with empty content
        # The service should handle this gracefully
        assert isinstance(result, dict)
    
    @pytest.mark.asyncio
    async def test_html_upload_action_with_special_characters(self):
        """Test HTML upload action handles special characters."""
        service = S3Service()
        service.enabled = True
        service.s3_client = MagicMock()
        service._initialized = True
        
        special_content = '''<html>
        <body>
            <p>Special chars: äöü ñ 中文 🎉</p>
            <p>Quotes: "test" 'test'</p>
        </body>
        </html>'''
        
        with patch.object(service, 'upload_file') as mock_upload:
            mock_upload.return_value = 'https://test.s3.amazonaws.com/test.html'
            
            result = await service._execute_action({
                'action': 'upload_html',
                'base_filename': 'special_chars',
                'html_content': special_content,
                'iso_date': '2023-01-15',
                'court_logger': MagicMock()
            })
            
            assert result['success'] is True
            mock_upload.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_unknown_action_handling(self):
        """Test S3Service handles unknown actions correctly."""
        service = S3Service()
        
        with pytest.raises(ValueError, match='Unknown S3Service action'):
            await service._execute_action({
                'action': 'unknown_action',
                'data': 'test'
            })
    
    @pytest.mark.asyncio
    async def test_html_upload_with_very_long_filename(self):
        """Test HTML upload with very long base filename."""
        service = S3Service()
        service.enabled = True
        service.s3_client = MagicMock()
        service._initialized = True
        
        # Create a very long filename
        long_filename = 'a' * 200
        
        with patch.object(service, 'upload_file') as mock_upload:
            mock_upload.return_value = 'https://test.s3.amazonaws.com/long.html'
            
            result = await service._execute_action({
                'action': 'upload_html',
                'base_filename': long_filename,
                'html_content': '<html><body>Test</body></html>',
                'iso_date': '2023-01-15',
                'court_logger': MagicMock()
            })
            
            assert result['success'] is True
            # S3 key should contain the long filename
            assert long_filename in result['s3_key']


if __name__ == '__main__':
    # Run tests with pytest
    pytest.main([__file__, '-v', '--tb=short'])
