import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "src"))
import pytest

import asyncio

from dependency_injector import providers
from unittest.mock import Mock, AsyncMock, patch


@pytest.fixture
def test_container(test_container):
    """Create test DI container with proper mocking."""
    from tests.conftest_di_updated import TestApplicationContainer
    
    container = TestApplicationContainer()
    
    # Setup default mock configuration
    mock_config_data = {
        'config_name': 'test_config',
        'iso_date': '20240101',
        'DATA_DIR': '/tmp/test',
        'scraper': False,
        'post_process': False,
        'upload': False,
        'fb_ads': False,
        'report_generator': False
    }
    
    container.workflow_specific_config.from_dict(mock_config_data)
    
    yield container
    
    # Cleanup
    try:
        container.unwire()
    except:
        pass

"""
Unit tests for case conversion functionality in repositories
"""
from unittest.mock import Mock, AsyncMock
from src.repositories.pacer_repository import PacerRepository
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.repositories.law_firms_repository import LawFirmsRepository

class TestCaseConversion:
    """Test case conversion between snake_case and PascalCase"""
    
    @pytest.fixture
    def mock_storage(self):
        """Create mock storage"""
        storage = Mock()
        storage.put_item = AsyncMock()
        storage.get_item = AsyncMock()
        storage.query = AsyncMock()
        storage.scan = AsyncMock()
        return storage
    
    @pytest.fixture
    def pacer_repo(self, mock_storage):
        """Create PACER repository"""
        return PacerRepository(mock_storage)
    
    @pytest.fixture
    def fb_repo(self, mock_storage):
        """Create FB Archive repository"""
        return FBArchiveRepository(mock_storage)
    
    @pytest.fixture
    def law_firms_repo(self, mock_storage):
        """Create Law Firms repository"""
        return LawFirmsRepository(mock_storage)
    
    def test_snake_to_pascal_basic(self, pacer_repo, test_container):
        """Test basic snake_case to PascalCase conversion"""
        assert pacer_repo._snake_to_pascal('filing_date') == 'FilingDate'
        assert pacer_repo._snake_to_pascal('court_id') == 'CourtId'
        assert pacer_repo._snake_to_pascal('docket_num') == 'DocketNum'
        assert pacer_repo._snake_to_pascal('title') == 'Title'
        assert pacer_repo._snake_to_pascal('versus') == 'Versus'
        assert pacer_repo._snake_to_pascal('s3_link') == 'S3Link'
        assert pacer_repo._snake_to_pascal('s3_html') == 'S3Html'
        
    def test_snake_to_pascal_special_id_cases(self, pacer_repo, test_container):
        """Test special ID cases based on PACER repository mappings"""
        assert pacer_repo._snake_to_pascal('court_id') == 'CourtId'
        assert pacer_repo._snake_to_pascal('transferee_court_id') == 'TransfereeCourtId'
        # For non-PACER specific fields, test the base implementation
        assert pacer_repo._snake_to_pascal('attorney_id') == 'AttorneyID'
        
    def test_snake_to_pascal_ids_plural(self, pacer_repo, test_container):
        """Test fields ending with _ids"""
        assert pacer_repo._snake_to_pascal('case_ids') == 'CaseIDs'
        assert pacer_repo._snake_to_pascal('attorney_ids') == 'AttorneyIDs'
        assert pacer_repo._snake_to_pascal('plaintiff_ids') == 'PlaintiffIDs'
        
    def test_pascal_to_snake_basic(self, pacer_repo, test_container):
        """Test basic PascalCase to snake_case conversion"""
        assert pacer_repo._pascal_to_snake('FilingDate') == 'filing_date'
        assert pacer_repo._pascal_to_snake('CourtId') == 'court_id'
        assert pacer_repo._pascal_to_snake('DocketNum') == 'docket_num'
        assert pacer_repo._pascal_to_snake('S3Link') == 's3_link'
        assert pacer_repo._pascal_to_snake('S3Html') == 's3_html'
        
    def test_pascal_to_snake_special_id_cases(self, pacer_repo, test_container):
        """Test special ID cases: Id should become _id"""
        assert pacer_repo._pascal_to_snake('AdArchiveId') == 'ad_archive_id'
        assert pacer_repo._pascal_to_snake('PageId') == 'page_id'
        assert pacer_repo._pascal_to_snake('CourtId') == 'court_id'
        assert pacer_repo._pascal_to_snake('TransfereeCourtId') == 'transferee_court_id'
        assert pacer_repo._pascal_to_snake('AttorneyId') == 'attorney_id'
        
    def test_pascal_to_snake_ids_plural(self, pacer_repo, test_container):
        """Test fields ending with IDs"""
        assert pacer_repo._pascal_to_snake('CaseIDs') == 'case_ids'
        assert pacer_repo._pascal_to_snake('AttorneyIDs') == 'attorney_ids'
        assert pacer_repo._pascal_to_snake('PlaintiffIDs') == 'plaintiff_ids'
        
    def test_convert_dict_to_pascal(self, pacer_repo, test_container):
        """Test full dictionary conversion to PascalCase"""
        input_dict = {
            'court_id': 'ohnd',
            'docket_num': '1:25-sf-66071',
            'filing_date': '20250616',
            'title': 'Test Case Title',
            'versus': 'Plaintiff v. Defendant',
            's3_link': 'https://cdn.lexgenius.ai/file.pdf',
            's3_html': '/path/to/file.html',
            'attorney_ids': ['123', '456'],
            'nested_data': {
                'law_firm_id': 'LF123',
                'attorney_name': 'John Doe'
            }
        }
        
        result = pacer_repo._convert_dict_to_pascal(input_dict)
        
        assert result == {
            'CourtId': 'ohnd',
            'DocketNum': '1:25-sf-66071',
            'FilingDate': '20250616',
            'Title': 'Test Case Title',
            'Versus': 'Plaintiff v. Defendant',
            'S3Link': 'https://cdn.lexgenius.ai/file.pdf',
            'S3Html': '/path/to/file.html',
            'AttorneyIDs': ['123', '456'],
            'NestedData': {
                'LawFirmID': 'LF123',
                'AttorneyName': 'John Doe'
            }
        }
        
    def test_convert_dict_to_snake(self, pacer_repo, test_container):
        """Test full dictionary conversion to snake_case"""
        input_dict = {
            'CourtId': 'ohnd',
            'DocketNum': '1:25-sf-66071',
            'FilingDate': '20250616',
            'Title': 'Test Case Title',
            'Versus': 'Plaintiff v. Defendant',
            'S3Link': 'https://cdn.lexgenius.ai/file.pdf',
            'S3Html': '/path/to/file.html',
            'AdArchiveId': '12345',
            'PageId': '67890',
            'AttorneyIds': ['123', '456'],
            'NestedData': {
                'LawFirmId': 'LF123',
                'AttorneyName': 'John Doe'
            }
        }
        
        result = pacer_repo._convert_dict_to_snake(input_dict)
        
        assert result == {
            'court_id': 'ohnd',
            'docket_num': '1:25-sf-66071',
            'filing_date': '20250616',
            'title': 'Test Case Title',
            'versus': 'Plaintiff v. Defendant',
            's3_link': 'https://cdn.lexgenius.ai/file.pdf',
            's3_html': '/path/to/file.html',
            'ad_archive_id': '12345',
            'page_id': '67890',
            'attorney_ids': ['123', '456'],
            'nested_data': {
                'law_firm_id': 'LF123',
                'attorney_name': 'John Doe'
            }
        }
        
    @pytest.mark.asyncio
    async def test_write_decorator_converts_to_pascal(self, pacer_repo, mock_storage, test_container):
        """Test that write decorator converts snake_case to PascalCase"""
        record = {
            'court_id': 'ohnd',
            'docket_num': '1:25-sf-66071',
            'filing_date': '20250616',
            's3_link': 'https://cdn.lexgenius.ai/file.pdf',
            'case_category': 'tort',
            'law_firm_count': 1
        }
        
        await pacer_repo.add_or_update_record(record)
        
        # Verify put_item was called with PascalCase fields
        mock_storage.put_item.assert_called_once()
        call_args = mock_storage.put_item.call_args[0]
        stored_record = call_args[1]
        
        assert 'CourtId' in stored_record
        assert 'DocketNum' in stored_record
        assert 'FilingDate' in stored_record
        assert 'S3Link' in stored_record
        assert 'CaseCategory' in stored_record
        assert 'LawFirmCount' in stored_record
        
        # Snake case fields should not exist
        assert 'court_id' not in stored_record
        assert 's3_link' not in stored_record
        assert 'case_category' not in stored_record
        
    @pytest.mark.asyncio
    async def test_read_decorator_converts_to_snake(self, pacer_repo, mock_storage, test_container):
        """Test that read decorator converts PascalCase to snake_case"""
        mock_storage.get_item.return_value = {
            'CourtId': 'ohnd',
            'DocketNum': '1:25-sf-66071',
            'FilingDate': '20250616',
            'S3Link': 'https://cdn.lexgenius.ai/file.pdf',
            'CaseCategory': 'tort',
            'LawFirmCount': 1,
            'AdArchiveId': '12345',
            'PageId': '67890'
        }
        
        result = await pacer_repo.get_by_filing_date_and_docket('20250616', '1:25-sf-66071')
        
        assert result['court_id'] == 'ohnd'
        assert result['docket_num'] == '1:25-sf-66071'
        assert result['filing_date'] == '20250616'
        assert result['s3_link'] == 'https://cdn.lexgenius.ai/file.pdf'
        assert result['case_category'] == 'tort'
        assert result['law_firm_count'] == 1
        assert result['ad_archive_id'] == '12345'
        assert result['page_id'] == '67890'
        
        # PascalCase fields should not exist
        assert 'CourtId' not in result
        assert 'S3Link' not in result
        assert 'AdArchiveId' not in result
        
    @pytest.mark.asyncio
    async def test_query_results_converted_to_snake(self, pacer_repo, mock_storage, test_container):
        """Test that query results are converted to snake_case"""
        mock_storage.query.return_value = [
            {
                'CourtId': 'ohnd',
                'DocketNum': '1:25-sf-66071',
                'S3Link': 'https://cdn.lexgenius.ai/file1.pdf'
            },
            {
                'CourtId': 'ohnd',
                'DocketNum': '1:25-sf-66072',
                'S3Link': 'https://cdn.lexgenius.ai/file2.pdf'
            }
        ]
        
        results = await pacer_repo.query_by_filing_date('20250616')
        
        assert len(results) == 2
        for result in results:
            assert 'court_id' in result
            assert 'docket_num' in result
            assert 's3_link' in result
            assert 'CourtId' not in result
            assert 'S3Link' not in result
            
    def test_fb_archive_repo_conversions(self, fb_repo, test_container):
        """Test conversions work in FB Archive repository"""
        assert fb_repo._snake_to_pascal('ad_archive_id') == 'AdArchiveID'
        assert fb_repo._snake_to_pascal('page_id') == 'PageID'
        assert fb_repo._pascal_to_snake('AdArchiveID') == 'ad_archive_id'
        assert fb_repo._pascal_to_snake('PageID') == 'page_id'
        
    def test_law_firms_repo_conversions(self, law_firms_repo, test_container):
        """Test conversions work in Law Firms repository"""
        assert law_firms_repo._snake_to_pascal('page_id') == 'PageID'
        assert law_firms_repo._snake_to_pascal('firm_id') == 'FirmID'
        assert law_firms_repo._pascal_to_snake('PageID') == 'page_id'
        assert law_firms_repo._pascal_to_snake('FirmID') == 'firm_id'
        
    def test_none_values_skipped(self, pacer_repo, test_container):
        """Test that None values are skipped during conversion"""
        input_dict = {
            'court_id': 'ohnd',
            'docket_num': None,
            'filing_date': '20250616',
            'empty_field': None
        }
        
        result = pacer_repo._convert_dict_to_pascal(input_dict)
        
        assert 'CourtId' in result
        assert 'FilingDate' in result
        assert 'DocketNum' not in result
        assert 'EmptyField' not in result
        
    def test_internal_fields_handling(self, pacer_repo, test_container):
        """Test handling of internal fields starting with underscore"""
        input_dict = {
            'court_id': 'ohnd',
            '_internal_field': 'value',
            '_processing_notes': 'special case',
            'regular_field': 'normal'
        }
        
        result = pacer_repo._convert_dict_to_pascal(input_dict)
        
        assert 'CourtId' in result
        assert 'RegularField' in result
        # _processing_notes is special case that should be converted
        assert 'ProcessingNotes' in result
        # Other internal fields should be skipped
        assert '_internal_field' not in result

    def test_convert_projection_expression(self, pacer_repo, test_container):
        """Test projection expression field name conversion"""
        # Single field
        result = pacer_repo._convert_projection_expression("court_id")
        assert result == "CourtId"
        
        # Multiple fields
        result = pacer_repo._convert_projection_expression("court_id, docket_num, filing_date")
        assert result == "CourtId, DocketNum, FilingDate"
        
        # Multiple fields with extra spacing
        result = pacer_repo._convert_projection_expression("court_id,  docket_num  ,   filing_date")
        assert result == "CourtId, DocketNum, FilingDate"
        
        # Special ID fields
        result = pacer_repo._convert_projection_expression("ad_archive_id, page_id")
        assert result == "AdArchiveID, PageID"
        
        # Empty string
        result = pacer_repo._convert_projection_expression("")
        assert result == ""
        
        # None
        result = pacer_repo._convert_projection_expression(None)
        assert result is None

    def test_convert_expression_attribute_names(self, pacer_repo, test_container):
        """Test expression attribute names conversion"""
        input_names = {
            "#court": "court_id",
            "#docket": "docket_num", 
            "#date": "filing_date",
            "#archive": "ad_archive_id"
        }
        
        result = pacer_repo._convert_expression_attribute_names(input_names)
        
        expected = {
            "#court": "CourtId",
            "#docket": "DocketNum",
            "#date": "FilingDate", 
            "#archive": "AdArchiveID"
        }
        
        assert result == expected
        
        # Empty dict
        result = pacer_repo._convert_expression_attribute_names({})
        assert result == {}
        
        # None
        result = pacer_repo._convert_expression_attribute_names(None)
        assert result is None

    def test_convert_expression_parameters(self, pacer_repo, test_container):
        """Test full expression parameter conversion"""
        params = {
            "projection_expression": "court_id, docket_num, filing_date",
            "expression_attribute_names": {
                "#court": "court_id",
                "#status": "is_active"
            },
            "filter_expression": "mock_filter",  # Should pass through
            "other_param": "unchanged"  # Should pass through
        }
        
        result = pacer_repo._convert_expression_parameters(**params)
        
        assert result["projection_expression"] == "CourtId, DocketNum, FilingDate"
        assert result["expression_attribute_names"] == {
            "#court": "CourtId", 
            "#status": "IsActive"
        }
        assert result["filter_expression"] == "mock_filter"
        assert result["other_param"] == "unchanged"

    @pytest.mark.asyncio
    async def test_query_with_projection_expression_conversion(self, pacer_repo, mock_storage, test_container):
        """Test that query method converts projection expressions"""
        mock_storage.query.return_value = [
            {"CourtId": "ohnd", "DocketNum": "123"}
        ]
        
        # Call query with snake_case projection expression
        await pacer_repo.query(
            projection_expression="court_id, docket_num",
            key_condition_expression="mock_condition"
        )
        
        # Verify storage was called with PascalCase projection
        mock_storage.query.assert_called_once()
        call_args = mock_storage.query.call_args
        
        # Check that the projection expression was converted to PascalCase
        assert call_args.kwargs["projection_expression"] == "CourtId, DocketNum"

    @pytest.mark.asyncio
    async def test_scan_with_expression_conversion(self, pacer_repo, mock_storage, test_container):
        """Test that scan method converts expression parameters"""
        mock_storage.scan.return_value = [
            {"CourtId": "ohnd", "DocketNum": "123"}
        ]
        
        # Call scan with snake_case expressions
        await pacer_repo.scan(
            projection_expression="court_id, filing_date",
            expression_attribute_names={
                "#court": "court_id",
                "#date": "filing_date"
            }
        )
        
        # Verify storage was called with PascalCase expressions
        mock_storage.scan.assert_called_once()
        call_args = mock_storage.scan.call_args
        
        assert call_args.kwargs["projection_expression"] == "CourtId, FilingDate"
        assert call_args.kwargs["expression_attribute_names"] == {
            "#court": "CourtId",
            "#date": "FilingDate"
        }

    @pytest.mark.asyncio
    async def test_expression_conversion_disabled_when_case_conversion_disabled(self, pacer_repo, mock_storage, test_container):
        """Test that expression conversion is skipped when case conversion is disabled"""
        mock_storage.scan.return_value = []
        
        # Disable case conversion
        pacer_repo.enable_case_conversion = False
        
        # Call scan with snake_case expressions
        await pacer_repo.scan(
            projection_expression="court_id, filing_date"
        )
        
        # Verify storage was called with original snake_case expression
        mock_storage.scan.assert_called_once()
        call_args = mock_storage.scan.call_args
        
        assert call_args.kwargs["projection_expression"] == "court_id, filing_date"