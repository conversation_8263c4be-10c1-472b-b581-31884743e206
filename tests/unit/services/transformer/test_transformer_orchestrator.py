"""
Comprehensive unit tests for TransformerOrchestrator.

This test suite covers:
- Main transformation workflow (transform_document)
- Batch processing (transform_batch) 
- Job execution (process_transformation_job)
- Health checks (comprehensive_health_check)
- Statistics and metrics (get_statistics)
- Error handling and recovery scenarios
- Service dependency management

Uses pytest-asyncio for async testing with proper mocking patterns.
"""
import pytest
import asyncio
import logging
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, Mock, patch
from typing import Dict, List, Any

# Import the class under test and its dependencies
from src.transformer.transformer_orchestrator import TransformerOrchestrator
from src.transformer.factories.service_factory import TransformerServiceFactory
from src.transformer.facades.document_processing_service import DocumentProcessingService
from src.transformer.facades.enrichment_service import EnrichmentService
from src.transformer.facades.persistence_service import PersistenceService
from src.transformer.facades.job_execution_service import JobExecutionService
from src.transformer.jobs.job_models import TransformationJob
from src.infrastructure.protocols.exceptions import TransformerServiceError
from unittest.mock import Mock


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    return {
        'config_name': 'test_config',
        'iso_date': '20240101',
        'DATA_DIR': '/tmp/test',
        'scraper': False,
        'post_process': False,
        'upload': False,
        'fb_ads': False,
        'report_generator': False
    }

@pytest.fixture
def mock_logger():
    """Mock logger for testing."""
    return Mock(spec=logging.Logger)
class TestTransformationJob:
    """Helper class to create test transformation jobs."""
    
    @staticmethod
    def create_basic_job(docket_num: str = "test-docket-123") -> Dict:
        """Create a basic transformation job for testing."""
        return {
            'docket_num': docket_num,
            'court': 'test-court',
            'case_type': 'civil',
            'data': {
                'title': 'Test Case',
                'parties': ['Plaintiff A', 'Defendant B'],
                'documents': []
            }
        }
    
    @staticmethod
    def create_transformation_job_obj(data: Dict = None, output_path: str = None) -> TransformationJob:
        """Create TransformationJob object for testing."""
        job = TransformationJob(
            json_path="/test/path/test.json",
            force_reprocess=False,
            transformer=Mock(),
        )
        # Set the data manually since it's not in constructor
        job_data = data or TestTransformationJob.create_basic_job()
        job.working_data = job_data
        job.data = job_data  # For compatibility with transform_document
        if output_path:
            job.output_path = output_path
        return job


@pytest.fixture
def mock_logger():
    """Create mock logger for testing."""
    logger = Mock(spec=logging.Logger)
    logger.info = Mock()
    logger.error = Mock() 
    logger.warning = Mock()
    logger.debug = Mock()
    return logger


@pytest.fixture
def test_config():
    """Create test configuration."""
    return {
        'output_dir': '/test/output',
        'upload_to_s3': True,
        'upload_to_dynamodb': False,
        'max_concurrent_jobs': 3,
        'retry_attempts': 2,
        'job_timeout': 60
    }


@pytest.fixture
def mock_document_service():
    """Create mock DocumentProcessingService."""
    service = AsyncMock(spec=DocumentProcessingService)
    service.process_document = AsyncMock(return_value={
        'docket_num': 'test-123',
        'processed': True,
        'classification': 'civil',
        'cleaned_data': {'title': 'Processed Test Case'}
    })
    service.health_check = AsyncMock(return_value={'status': 'healthy'})
    service.cleanup = AsyncMock()
    return service


@pytest.fixture
def mock_enrichment_service():
    """Create mock EnrichmentService."""
    service = AsyncMock(spec=EnrichmentService)
    service.enrich_document = AsyncMock(return_value={
        'docket_num': 'test-123',
        'processed': True,
        'enriched': True,
        'mdl_info': {'mdl_number': '12345'},
        'law_firm_data': {'firm': 'Test Firm'},
        'llm_analysis': {'summary': 'Test case summary'}
    })
    service.health_check = AsyncMock(return_value={'status': 'healthy'})
    service.cleanup = AsyncMock()
    return service


@pytest.fixture
def mock_persistence_service():
    """Create mock PersistenceService."""
    service = AsyncMock(spec=PersistenceService)
    service.save_document = AsyncMock(return_value=True)
    service.upload_batch = AsyncMock(return_value=[{
        'type': 's3',
        'status': 'success',
        'url': 'https://test-bucket.s3.amazonaws.com/test.json'
    }])
    service.load_document = AsyncMock(return_value={
        'docket_num': 'test-123',
        'data': 'test data'
    })
    service.health_check = AsyncMock(return_value={'status': 'healthy'})
    service.cleanup = AsyncMock()
    return service


@pytest.fixture
def mock_job_service():
    """Create mock JobExecutionService."""
    service = AsyncMock(spec=JobExecutionService)
    service.register_job_handler = Mock()
    service.start_workers = AsyncMock()
    service.stop_workers = AsyncMock()
    service.execute_batch_jobs = AsyncMock(return_value=[{
        'status': 'success',
        'docket_num': 'test-123',
        'output_path': '/test/output/test.json'
    }])
    service.execute_transformation_job = AsyncMock(return_value={
        'status': 'success',
        'output_path': '/test/output/test.json'
    })
    service.get_statistics = AsyncMock(return_value={
        'total_jobs': 5,
        'successful_jobs': 4,
        'failed_jobs': 1
    })
    service.health_check = AsyncMock(return_value={'status': 'healthy'})
    service.cleanup = AsyncMock()
    return service


@pytest.fixture
def mock_service_factory():
    """Create mock TransformerServiceFactory."""
    factory = Mock(spec=TransformerServiceFactory)
    factory.register_service = Mock()
    factory.register_component = Mock()
    factory.get_service = Mock()
    factory.get_component = Mock()
    return factory


@pytest.fixture
def shutdown_event():
    """Create shutdown event for testing."""
    return asyncio.Event()


@pytest.fixture
async def orchestrator(
    mock_document_service,
    mock_enrichment_service, 
    mock_persistence_service,
    mock_job_service,
    mock_service_factory,
    test_config,
    mock_logger,
    shutdown_event
):
    """Create TransformerOrchestrator instance for testing."""
    orchestrator = TransformerOrchestrator(
        document_service=mock_document_service,
        enrichment_service=mock_enrichment_service,
        persistence_service=mock_persistence_service,
        job_service=mock_job_service,
        service_factory=mock_service_factory,
        config=test_config,
        logger=mock_logger,
        shutdown_event=shutdown_event
    )
    
    yield orchestrator
    
    # Cleanup after test
    await orchestrator.cleanup()


class TestTransformerOrchestrator:
    """Main test class for TransformerOrchestrator."""

    @pytest.mark.asyncio
    async def test_initialization(
        self,
        mock_document_service,
        mock_enrichment_service,
        mock_persistence_service,
        mock_job_service,
        mock_service_factory,
        test_config,
        mock_logger
    ):
        """Test proper initialization of TransformerOrchestrator."""
        orchestrator = TransformerOrchestrator(
            document_service=mock_document_service,
            enrichment_service=mock_enrichment_service,
            persistence_service=mock_persistence_service,
            job_service=mock_job_service,
            service_factory=mock_service_factory,
            config=test_config,
            logger=mock_logger
        )
        
        # Verify dependencies are set
        assert orchestrator._document_service == mock_document_service
        assert orchestrator._enrichment_service == mock_enrichment_service
        assert orchestrator._persistence_service == mock_persistence_service
        assert orchestrator._job_service == mock_job_service
        assert orchestrator._service_factory == mock_service_factory
        assert orchestrator.config == test_config
        assert orchestrator.logger == mock_logger
        
        # Verify metrics initialized
        assert orchestrator._metrics['total_processed'] == 0
        assert orchestrator._metrics['successful'] == 0
        assert orchestrator._metrics['failed'] == 0
        
        # Verify shutdown event
        assert not orchestrator._shutdown_event.is_set()
        
        await orchestrator.cleanup()

    @pytest.mark.asyncio
    async def test_transform_document_success(self, orchestrator):
        """Test successful document transformation workflow."""
        # Prepare test data
        job_data = TestTransformationJob.create_basic_job("test-docket-456")
        job = TestTransformationJob.create_transformation_job_obj(job_data)
        
        # Execute transformation
        result = await orchestrator.transform_document(job)
        
        # Verify workflow was executed correctly
        orchestrator._document_service.process_document.assert_called_once()
        orchestrator._enrichment_service.enrich_document.assert_called_once()
        orchestrator._persistence_service.save_document.assert_called_once()
        orchestrator._persistence_service.upload_batch.assert_called_once()
        
        # Verify result structure
        assert result['status'] == 'success'
        assert result['docket_num'] == 'test-docket-456'
        assert 'output_path' in result
        assert 'timestamp' in result
        assert 'data' in result
        assert 'upload_results' in result['data']
        
        # Verify metrics updated
        assert orchestrator._metrics['total_processed'] == 1
        assert orchestrator._metrics['successful'] == 1
        assert orchestrator._metrics['failed'] == 0

    @pytest.mark.asyncio
    async def test_transform_document_processing_failure(self, orchestrator):
        """Test document transformation with processing service failure."""
        # Setup failure in document processing
        orchestrator._document_service.process_document.side_effect = Exception("Processing failed")
        
        job_data = TestTransformationJob.create_basic_job("test-fail-123")
        job = TestTransformationJob.create_transformation_job_obj(job_data)
        
        # Execute transformation
        result = await orchestrator.transform_document(job)
        
        # Verify failure was handled correctly
        assert result['status'] == 'failed'
        assert result['docket_num'] == 'test-fail-123'
        assert 'error' in result
        assert 'Processing failed' in result['error']
        
        # Verify metrics updated for failure
        assert orchestrator._metrics['total_processed'] == 1
        assert orchestrator._metrics['successful'] == 0
        assert orchestrator._metrics['failed'] == 1

    @pytest.mark.asyncio
    async def test_transform_document_enrichment_failure(self, orchestrator):
        """Test document transformation with enrichment service failure."""
        # Setup failure in enrichment
        orchestrator._enrichment_service.enrich_document.side_effect = Exception("Enrichment failed")
        
        job_data = TestTransformationJob.create_basic_job("test-enrich-fail")
        job = TestTransformationJob.create_transformation_job_obj(job_data)
        
        result = await orchestrator.transform_document(job)
        
        # Verify processing happened but enrichment failed
        orchestrator._document_service.process_document.assert_called_once()
        orchestrator._enrichment_service.enrich_document.assert_called_once()
        
        # Verify failure result
        assert result['status'] == 'failed'
        assert 'Enrichment failed' in result['error']

    @pytest.mark.asyncio
    async def test_transform_document_persistence_failure(self, orchestrator):
        """Test document transformation with persistence failure."""
        # Setup failure in persistence
        orchestrator._persistence_service.save_document.return_value = False
        
        job_data = TestTransformationJob.create_basic_job("test-persist-fail")
        job = TestTransformationJob.create_transformation_job_obj(job_data)
        
        result = await orchestrator.transform_document(job)
        
        # Verify processing and enrichment happened
        orchestrator._document_service.process_document.assert_called_once()
        orchestrator._enrichment_service.enrich_document.assert_called_once()
        orchestrator._persistence_service.save_document.assert_called_once()
        
        # Verify failure result
        assert result['status'] == 'failed'
        assert 'Failed to save document' in result['error']

    @pytest.mark.asyncio
    async def test_transform_document_no_uploads_configured(self, orchestrator):
        """Test document transformation with no upload services configured."""
        # Disable uploads in config
        orchestrator.config['upload_to_s3'] = False
        orchestrator.config['upload_to_dynamodb'] = False
        
        job_data = TestTransformationJob.create_basic_job("test-no-upload")
        job = TestTransformationJob.create_transformation_job_obj(job_data)
        
        result = await orchestrator.transform_document(job)
        
        # Verify upload_batch was not called
        orchestrator._persistence_service.upload_batch.assert_not_called()
        
        # Verify successful result without upload_results
        assert result['status'] == 'success'
        assert 'upload_results' not in result['data']

    @pytest.mark.asyncio
    async def test_transform_batch_success(self, orchestrator):
        """Test successful batch transformation."""
        # Create multiple test jobs
        jobs = [
            TestTransformationJob.create_transformation_job_obj(
                TestTransformationJob.create_basic_job(f"batch-{i}")
            ) for i in range(3)
        ]
        
        # Setup batch results
        batch_results = [
            {'status': 'success', 'docket_num': f'batch-{i}'} 
            for i in range(3)
        ]
        orchestrator._job_service.execute_batch_jobs.return_value = batch_results
        
        # Execute batch transformation
        results = await orchestrator.transform_batch(jobs)
        
        # Verify job service interactions
        orchestrator._job_service.register_job_handler.assert_called_once()
        orchestrator._job_service.start_workers.assert_called_once()
        orchestrator._job_service.execute_batch_jobs.assert_called_once_with(jobs)
        orchestrator._job_service.stop_workers.assert_called_once()
        
        # Verify results
        assert len(results) == 3
        assert all(r['status'] == 'success' for r in results)

    @pytest.mark.asyncio
    async def test_transform_batch_mixed_results(self, orchestrator):
        """Test batch transformation with mixed success/failure results."""
        jobs = [TestTransformationJob.create_transformation_job_obj() for _ in range(3)]
        
        # Setup mixed results
        mixed_results = [
            {'status': 'success', 'docket_num': 'batch-0'},
            {'status': 'failed', 'docket_num': 'batch-1', 'error': 'Test error'},
            {'status': 'success', 'docket_num': 'batch-2'}
        ]
        orchestrator._job_service.execute_batch_jobs.return_value = mixed_results
        
        results = await orchestrator.transform_batch(jobs)
        
        # Verify mixed results handled correctly
        assert len(results) == 3
        successful = sum(1 for r in results if r['status'] == 'success')
        failed = sum(1 for r in results if r['status'] == 'failed')
        assert successful == 2
        assert failed == 1

    @pytest.mark.asyncio
    async def test_transform_batch_job_service_failure(self, orchestrator):
        """Test batch transformation with job service failure."""
        jobs = [TestTransformationJob.create_transformation_job_obj()]
        
        # Setup job service failure
        orchestrator._job_service.execute_batch_jobs.side_effect = Exception("Job service failed")
        
        # Verify exception is propagated and cleanup happens
        with pytest.raises(Exception, match="Job service failed"):
            await orchestrator.transform_batch(jobs)
        
        # Verify cleanup was called
        orchestrator._job_service.stop_workers.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_transformation_job(self, orchestrator):
        """Test processing single transformation job through job service."""
        job = TestTransformationJob.create_transformation_job_obj()
        
        expected_result = {
            'status': 'success',
            'output_path': '/test/output/test.json',
            'job_id': 'test-job-123'
        }
        orchestrator._job_service.execute_transformation_job.return_value = expected_result
        
        result = await orchestrator.process_transformation_job(job)
        
        # Verify job handler registration and execution
        orchestrator._job_service.register_job_handler.assert_called_once()
        orchestrator._job_service.execute_transformation_job.assert_called_once_with(job)
        
        # Verify result
        assert result == expected_result

    @pytest.mark.asyncio
    async def test_job_transformation_handler(self, orchestrator):
        """Test the internal job transformation handler."""
        # Create test job and context
        job = TestTransformationJob.create_transformation_job_obj()
        context = Mock()
        context.progress = 0
        context.metadata = {}
        
        # Execute handler
        result = await orchestrator._job_transformation_handler(job, context)
        
        # Verify progress updates
        assert context.progress == 1.0
        assert context.metadata['step'] == 'completed'
        
        # Verify service calls
        orchestrator._document_service.process_document.assert_called_once()
        orchestrator._enrichment_service.enrich_document.assert_called_once()
        orchestrator._persistence_service.save_document.assert_called_once()
        
        # Verify result
        assert result['status'] == 'success'
        assert 'output_path' in result
        assert 'data' in result

    @pytest.mark.asyncio
    async def test_process_file_success(self, orchestrator):
        """Test processing single file (legacy support)."""
        file_path = "/test/input/test.json"
        
        # Execute file processing
        result = await orchestrator.process_file(file_path)
        
        # Verify file was loaded
        orchestrator._persistence_service.load_document.assert_called_once_with(file_path)
        
        # Verify transformation was performed
        orchestrator._document_service.process_document.assert_called_once()
        orchestrator._enrichment_service.enrich_document.assert_called_once()
        
        # Verify result
        assert result['status'] == 'success'

    @pytest.mark.asyncio
    async def test_process_file_load_failure(self, orchestrator):
        """Test processing file with load failure."""
        file_path = "/test/nonexistent.json"
        
        # Setup load failure
        orchestrator._persistence_service.load_document.return_value = None
        
        result = await orchestrator.process_file(file_path)
        
        # Verify failure result
        assert result['status'] == 'failed'
        assert result['file_path'] == file_path
        assert result['error'] == 'Failed to load file'

    @pytest.mark.asyncio
    async def test_comprehensive_health_check_all_healthy(self, orchestrator):
        """Test comprehensive health check with all services healthy."""
        health_result = await orchestrator.comprehensive_health_check()
        
        # Verify all services were checked
        orchestrator._document_service.health_check.assert_called_once()
        orchestrator._enrichment_service.health_check.assert_called_once()
        orchestrator._persistence_service.health_check.assert_called_once()
        orchestrator._job_service.health_check.assert_called_once()
        
        # Verify overall health
        assert health_result['orchestrator'] == 'healthy'
        assert len(health_result['services']) == 4
        assert all(
            service['status'] == 'healthy' 
            for service in health_result['services'].values()
        )
        assert 'timestamp' in health_result
        assert 'metrics' in health_result

    @pytest.mark.asyncio
    async def test_comprehensive_health_check_degraded_service(self, orchestrator):
        """Test health check with degraded service."""
        # Setup degraded service
        orchestrator._document_service.health_check.return_value = {
            'status': 'degraded',
            'issues': ['High memory usage']
        }
        
        health_result = await orchestrator.comprehensive_health_check()
        
        # Verify overall status is degraded
        assert health_result['orchestrator'] == 'degraded'
        assert health_result['services']['document_processing']['status'] == 'degraded'

    @pytest.mark.asyncio
    async def test_comprehensive_health_check_service_error(self, orchestrator):
        """Test health check with service throwing error."""
        # Setup service error
        orchestrator._enrichment_service.health_check.side_effect = Exception("Service unavailable")
        
        health_result = await orchestrator.comprehensive_health_check()
        
        # Verify error handling
        assert health_result['orchestrator'] == 'unhealthy'
        assert health_result['services']['enrichment']['status'] == 'error'
        assert 'Service unavailable' in health_result['services']['enrichment']['error']

    @pytest.mark.asyncio
    async def test_get_statistics_basic(self, orchestrator):
        """Test getting basic statistics."""
        # Update some metrics
        orchestrator._metrics.update({
            'total_processed': 10,
            'successful': 8,
            'failed': 2,
            'start_time': datetime.now()
        })
        
        stats = await orchestrator.get_statistics()
        
        # Verify job service statistics were retrieved
        orchestrator._job_service.get_statistics.assert_called_once()
        
        # Verify basic stats
        assert stats['total_processed'] == 10
        assert stats['successful'] == 8
        assert stats['failed'] == 2
        assert stats['success_rate'] == 0.8
        assert stats['failure_rate'] == 0.2
        assert 'job_execution' in stats
        assert 'runtime_seconds' in stats

    @pytest.mark.asyncio
    async def test_get_statistics_no_processing_done(self, orchestrator):
        """Test statistics when no processing has been done."""
        stats = await orchestrator.get_statistics()
        
        # Verify no division by zero
        assert stats['total_processed'] == 0
        assert 'success_rate' not in stats
        assert 'failure_rate' not in stats

    @pytest.mark.asyncio
    async def test_get_statistics_with_end_time(self, orchestrator):
        """Test statistics calculation with end time set."""
        start_time = datetime.now()
        # Add 30 seconds properly
        end_time = datetime.fromtimestamp(start_time.timestamp() + 30)
        
        orchestrator._metrics['start_time'] = start_time
        orchestrator._metrics['end_time'] = end_time
        
        stats = await orchestrator.get_statistics()
        
        # Verify runtime calculation uses end_time (should be around 30 seconds)
        assert abs(stats['runtime_seconds'] - 30) < 1  # Allow small variance

    def test_get_output_path(self, orchestrator):
        """Test output path generation."""
        docket_num = "test-case-123"
        
        with patch('src.transformer.transformer_orchestrator.datetime') as mock_datetime:
            mock_datetime.now.return_value.strftime.return_value = "20240101_120000"
            
            output_path = orchestrator._get_output_path(docket_num)
            
            expected_path = "/test/output/test-case-123_20240101_120000.json"
            assert output_path == expected_path

    @pytest.mark.asyncio
    async def test_execute_action_transform_document(self, orchestrator):
        """Test _execute_action routing for transform_document."""
        job_data = TestTransformationJob.create_basic_job()
        job = TestTransformationJob.create_transformation_job_obj(job_data)
        
        action_data = {
            'action': 'transform_document',
            'data': job
        }
        
        result = await orchestrator._execute_action(action_data)
        
        # Verify transform_document was called
        assert result['status'] == 'success'

    @pytest.mark.asyncio
    async def test_execute_action_invalid_data_format(self, orchestrator):
        """Test _execute_action with invalid data format."""
        with pytest.raises(TransformerServiceError, match="Invalid data format"):
            await orchestrator._execute_action("invalid_data")

    @pytest.mark.asyncio
    async def test_execute_action_unknown_action(self, orchestrator):
        """Test _execute_action with unknown action."""
        action_data = {
            'action': 'unknown_action',
            'data': {}
        }
        
        with pytest.raises(TransformerServiceError, match="Unknown action: unknown_action"):
            await orchestrator._execute_action(action_data)

    @pytest.mark.asyncio
    async def test_cleanup(self, orchestrator):
        """Test cleanup method calls cleanup on all services."""
        await orchestrator.cleanup()
        
        # Verify all services cleanup was called
        orchestrator._document_service.cleanup.assert_called_once()
        orchestrator._enrichment_service.cleanup.assert_called_once()
        orchestrator._persistence_service.cleanup.assert_called_once()
        orchestrator._job_service.cleanup.assert_called_once()
        
        # Verify end_time was set
        assert orchestrator._metrics['end_time'] is not None

    @pytest.mark.asyncio
    async def test_cleanup_with_service_errors(self, orchestrator):
        """Test cleanup continues even if services throw errors."""
        # Setup cleanup errors
        orchestrator._document_service.cleanup.side_effect = Exception("Cleanup failed")
        orchestrator._enrichment_service.cleanup.side_effect = Exception("Another error")
        
        # Should not raise exception
        await orchestrator.cleanup()
        
        # Verify all cleanup attempts were made
        orchestrator._document_service.cleanup.assert_called_once()
        orchestrator._enrichment_service.cleanup.assert_called_once()
        orchestrator._persistence_service.cleanup.assert_called_once()
        orchestrator._job_service.cleanup.assert_called_once()

    @pytest.mark.asyncio
    async def test_shutdown(self, orchestrator):
        """Test graceful shutdown."""
        with patch.object(orchestrator, 'get_statistics') as mock_stats:
            mock_stats.return_value = {
                'total_processed': 5,
                'successful': 4,
                'failed': 1,
                'success_rate': 0.8
            }
            
            await orchestrator.shutdown()
            
            # Verify shutdown event was set
            assert orchestrator._shutdown_event.is_set()
            
            # Verify cleanup was called
            orchestrator._document_service.cleanup.assert_called_once()
            
            # Verify statistics were retrieved for display
            mock_stats.assert_called_once()


class TestTransformerServiceFactory:
    """Test class for TransformerServiceFactory."""

    def test_factory_initialization(self, test_config, mock_logger):
        """Test factory initialization."""
        factory = TransformerServiceFactory(config=test_config, logger=mock_logger)
        
        assert factory.config == test_config
        assert factory.logger == mock_logger
        assert factory._services == {}
        assert factory._components == {}

    def test_register_and_get_service(self, test_config, mock_logger):
        """Test service registration and retrieval."""
        factory = TransformerServiceFactory(config=test_config, logger=mock_logger)
        
        mock_service = Mock()
        factory.register_service('test_service', mock_service)
        
        # Verify registration
        factory.logger.info.assert_called_with("Registered service: test_service")
        
        # Verify retrieval
        retrieved_service = factory.get_service('test_service')
        assert retrieved_service == mock_service
        
        # Test non-existent service
        assert factory.get_service('nonexistent') is None

    def test_register_and_get_component(self, test_config, mock_logger):
        """Test component registration and retrieval."""
        factory = TransformerServiceFactory(config=test_config, logger=mock_logger)
        
        mock_component = Mock()
        factory.register_component('test_component', mock_component)
        
        # Verify registration
        factory.logger.info.assert_called_with("Registered component: test_component")
        
        # Verify retrieval
        retrieved_component = factory.get_component('test_component')
        assert retrieved_component == mock_component
        
        # Test non-existent component
        assert factory.get_component('nonexistent') is None

    @pytest.mark.asyncio
    async def test_create_core_services(self, test_config, mock_logger):
        """Test creation of core services."""
        factory = TransformerServiceFactory(config=test_config, logger=mock_logger)
        
        # Register some mock components
        mock_components = {
            'litigation_classifier': Mock(),
            'transfer_handler': Mock(),
            'docket_cleaner': Mock(),
            'llm_engine': Mock()
        }
        
        for name, component in mock_components.items():
            factory.register_component(name, component)
        
        with patch('src.transformer.transformer_orchestrator.DocumentProcessingService') as MockDocService, \
             patch('src.transformer.transformer_orchestrator.EnrichmentService') as MockEnrichService, \
             patch('src.transformer.transformer_orchestrator.PersistenceService') as MockPersistService, \
             patch('src.transformer.transformer_orchestrator.JobExecutionService') as MockJobService:
            
            # Setup mocks
            MockDocService.return_value = Mock()
            MockEnrichService.return_value = Mock()
            MockPersistService.return_value = Mock()
            MockJobService.return_value = Mock()
            
            services = await factory.create_core_services()
            
            # Verify all services were created
            assert 'document_processing' in services
            assert 'enrichment' in services
            assert 'persistence' in services
            assert 'job_execution' in services
            
            # Verify services were instantiated with correct parameters
            MockDocService.assert_called_once()
            MockEnrichService.assert_called_once()
            MockPersistService.assert_called_once()
            MockJobService.assert_called_once()


class TestIntegrationScenarios:
    """Integration test scenarios for TransformerOrchestrator."""

    @pytest.mark.asyncio
    async def test_full_workflow_integration(self, orchestrator):
        """Test complete workflow integration scenario."""
        # Simulate realistic data flow
        input_data = {
            'docket_num': '1:24-cv-12345',
            'court': 'District of Massachusetts',
            'case_type': 'civil',
            'filing_date': '2024-01-15',
            'parties': [
                {'name': 'John Doe', 'role': 'plaintiff'},
                {'name': 'Acme Corp', 'role': 'defendant'}
            ],
            'documents': [
                {'type': 'complaint', 'date': '2024-01-15', 'pages': 25},
                {'type': 'motion', 'date': '2024-02-01', 'pages': 10}
            ]
        }
        
        job = TestTransformationJob.create_transformation_job_obj(input_data)
        
        # Execute full transformation
        result = await orchestrator.transform_document(job)
        
        # Verify complete workflow
        assert result['status'] == 'success'
        assert result['docket_num'] == '1:24-cv-12345'
        
        # Verify all services were called in correct order
        orchestrator._document_service.process_document.assert_called_once()
        orchestrator._enrichment_service.enrich_document.assert_called_once()
        orchestrator._persistence_service.save_document.assert_called_once()
        
        # Verify final result has all expected components
        assert 'data' in result
        assert 'upload_results' in result['data']

    @pytest.mark.asyncio
    async def test_concurrent_processing_scenario(self, orchestrator):
        """Test concurrent processing of multiple documents."""
        # Create multiple jobs
        jobs = [
            TestTransformationJob.create_transformation_job_obj(
                TestTransformationJob.create_basic_job(f"concurrent-{i}")
            ) for i in range(5)
        ]
        
        # Setup batch results for 5 jobs
        batch_results = [
            {'status': 'success', 'docket_num': f'concurrent-{i}'}
            for i in range(5)
        ]
        orchestrator._job_service.execute_batch_jobs.return_value = batch_results
        
        # Execute batch processing
        results = await orchestrator.transform_batch(jobs)
        
        # Verify all jobs were processed
        assert len(results) == 5
        assert all(r['status'] == 'success' for r in results)
        
        # Verify batch execution was used
        orchestrator._job_service.execute_batch_jobs.assert_called_once()

    @pytest.mark.asyncio
    async def test_error_recovery_scenario(self, orchestrator):
        """Test error recovery and partial failure handling."""
        # Setup intermittent failure
        call_count = 0
        
        def side_effect(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 2:  # Fail on second call
                raise Exception("Temporary failure")
            return {'processed': True, 'docket_num': f'test-{call_count}'}
        
        orchestrator._document_service.process_document.side_effect = side_effect
        
        # Process multiple jobs
        jobs = [
            TestTransformationJob.create_transformation_job_obj(
                TestTransformationJob.create_basic_job(f"recovery-{i}")
            ) for i in range(3)
        ]
        
        # Execute individual transformations to test error handling
        results = []
        for job in jobs:
            result = await orchestrator.transform_document(job)
            results.append(result)
        
        # Verify mixed results
        statuses = [r['status'] for r in results]
        assert 'success' in statuses
        assert 'failed' in statuses
        
        # Verify metrics reflect mixed results
        assert orchestrator._metrics['successful'] > 0
        assert orchestrator._metrics['failed'] > 0

    @pytest.mark.asyncio
    async def test_performance_monitoring_scenario(self, orchestrator):
        """Test performance monitoring and statistics collection."""
        # Process several documents
        for i in range(3):
            job_data = TestTransformationJob.create_basic_job(f"perf-test-{i}")
            job = TestTransformationJob.create_transformation_job_obj(job_data)
            await orchestrator.transform_document(job)
        
        # Check statistics
        stats = await orchestrator.get_statistics()
        
        # Verify performance metrics
        assert stats['total_processed'] == 3
        assert stats['successful'] == 3
        assert stats['success_rate'] == 1.0
        assert 'runtime_seconds' in stats
        assert 'job_execution' in stats
        
        # Check health status
        health = await orchestrator.comprehensive_health_check()
        assert health['orchestrator'] == 'healthy'
        assert health['metrics']['total_processed'] == 3