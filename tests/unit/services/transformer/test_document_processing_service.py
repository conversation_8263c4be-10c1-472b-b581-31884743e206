"""
Comprehensive unit tests for DocumentProcessingService.

Tests the consolidation of:
- CaseClassificationService functionality
- DataCleaningService functionality
- DocketProcessingService functionality
- HTMLIntegrationService functionality
"""
import pytest
import asyncio
import logging
from typing import Dict, Any
from unittest.mock import Mock, AsyncMock, patch, MagicMock

from src.transformer.facades.document_processing_service import DocumentProcessingService
from src.infrastructure.protocols.exceptions import TransformerServiceError

# Mock components
from src.transformer.components.case_classification.classifier import LitigationClassifier
from src.transformer.components.case_classification.transfer_handler import TransferHandler
from src.transformer.components.data_cleaning.cleaner import DocketDataCleaner
from src.transformer.components.data_cleaning.llm_engine import DocketLLMEngine
from src.transformer.components.data_cleaning.court_processor import CourtDataProcessor
from src.transformer.components.docket.docket_processor import DocketProcessor
from src.transformer.components.docket.html_processor import HTMLProcessor
from src.transformer.components.docket.text_handler import TextHandler
from src.transformer.components.docket.validator import DocketValidator


@pytest.fixture
def mock_logger():
    """Mock logger for testing."""
    return Mock(spec=logging.Logger)


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    return {
        'test_mode': True,
        'html_processing': {'enabled': True},
        'classification': {'enabled': True},
        'cleaning': {'enabled': True}
    }


@pytest.fixture
def mock_litigation_classifier():
    """Mock litigation classifier component."""
    mock = Mock()
    mock.identify_litigation_by_text = Mock(return_value=(
        'mass_tort',  # litigation_type
        0.85,         # confidence
        {'classification_reason': 'keyword_match'}  # details
    ))
    return mock


@pytest.fixture
def mock_transfer_handler():
    """Mock transfer handler component."""
    mock = Mock()
    mock.process_transfers = AsyncMock(return_value=None)
    return mock


@pytest.fixture
def mock_docket_cleaner():
    """Mock docket data cleaner component."""
    mock = Mock()
    mock.clean_and_flatten = Mock(return_value=None)
    return mock


@pytest.fixture
def mock_llm_engine():
    """Mock LLM engine component."""
    mock = Mock()
    mock.run_llm_extraction = AsyncMock(return_value=None)
    return mock


@pytest.fixture
def mock_court_processor():
    """Mock court processor component."""
    mock = Mock()
    mock.process_court_info = AsyncMock(return_value=None)
    return mock


@pytest.fixture
def mock_docket_processor():
    """Mock docket processor component."""
    mock = Mock()
    mock.process = AsyncMock(return_value={
        'docket_num': '1:23-cv-12345',
        'processed': True,
        'all_text': 'Sample docket text content'
    })
    return mock


@pytest.fixture
def mock_html_processor():
    """Mock HTML processor component."""
    mock = Mock()
    mock.process_html = AsyncMock(return_value={
        'extracted_data': {'case_name': 'Test Case'},
        'html_content': '<html>processed</html>'
    })
    return mock


@pytest.fixture
def mock_text_handler():
    """Mock text handler component."""
    mock = Mock()
    mock.process_text = AsyncMock(return_value={
        'entities': {'names': ['John Doe'], 'dates': ['2023-01-01']}
    })
    return mock


@pytest.fixture
def mock_validator():
    """Mock document validator component."""
    mock = Mock()
    mock.validate = AsyncMock(return_value={'is_valid': True, 'errors': []})
    return mock


@pytest.fixture
def service_with_all_components(
    mock_logger, mock_config, mock_litigation_classifier, mock_transfer_handler,
    mock_docket_cleaner, mock_llm_engine, mock_court_processor,
    mock_docket_processor, mock_html_processor, mock_text_handler, mock_validator
):
    """Create DocumentProcessingService with all mock components."""
    return DocumentProcessingService(
        litigation_classifier=mock_litigation_classifier,
        transfer_handler=mock_transfer_handler,
        docket_cleaner=mock_docket_cleaner,
        llm_engine=mock_llm_engine,
        court_processor=mock_court_processor,
        docket_processor=mock_docket_processor,
        html_processor=mock_html_processor,
        text_handler=mock_text_handler,
        validator=mock_validator,
        config=mock_config,
        logger=mock_logger
    )


@pytest.fixture
def service_minimal(mock_logger, mock_config):
    """Create DocumentProcessingService with minimal components."""
    return DocumentProcessingService(
        config=mock_config,
        logger=mock_logger
    )


@pytest.fixture
def sample_document_data():
    """Sample document data for testing."""
    return {
        'docket_num': '1:23-cv-12345',
        'case_name': 'Test Case v. Defendant',
        'court_id': 'cand',
        'filing_date': '2023-01-01',
        'plaintiff': 'Test Plaintiff',
        'defendant': 'Test Defendant'
    }


@pytest.fixture
def sample_html_document_data():
    """Sample document data with HTML content for testing."""
    return {
        'docket_num': '1:23-cv-67890',
        'html_content': '<html><body><h1>Test Case</h1></body></html>',
        'case_name': 'HTML Test Case'
    }


class TestDocumentProcessingServiceInitialization:
    """Test service initialization and component injection."""

    def test_initialization_with_all_components(self, service_with_all_components):
        """Test service initializes with all components properly injected."""
        service = service_with_all_components
        
        assert service._litigation_classifier is not None
        assert service._transfer_handler is not None
        assert service._docket_cleaner is not None
        assert service._llm_engine is not None
        assert service._court_processor is not None
        assert service._docket_processor is not None
        assert service._html_processor is not None
        assert service._text_handler is not None
        assert service._validator is not None

    def test_initialization_with_minimal_components(self, service_minimal):
        """Test service initializes with minimal components (all None)."""
        service = service_minimal
        
        assert service._litigation_classifier is None
        assert service._transfer_handler is None
        assert service._docket_cleaner is None
        assert service._llm_engine is None
        assert service._court_processor is None
        assert service._docket_processor is None
        assert service._html_processor is None
        assert service._text_handler is None
        assert service._validator is None

    def test_initialization_logging(self, mock_logger, mock_config):
        """Test initialization logging is called."""
        DocumentProcessingService(logger=mock_logger, config=mock_config)
        mock_logger.info.assert_called()


class TestDocumentProcessingServiceActionRouting:
    """Test _execute_action method routing."""

    @pytest.mark.asyncio
    async def test_execute_action_invalid_data_format(self, service_with_all_components):
        """Test _execute_action raises error for invalid data format."""
        with pytest.raises(TransformerServiceError) as exc_info:
            await service_with_all_components._execute_action("invalid_data")
        
        assert "Invalid data format" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_execute_action_unknown_action(self, service_with_all_components):
        """Test _execute_action raises error for unknown action."""
        data = {'action': 'unknown_action', 'data': {}}
        
        with pytest.raises(TransformerServiceError) as exc_info:
            await service_with_all_components._execute_action(data)
        
        assert "Unknown action: unknown_action" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_execute_action_process_document(self, service_with_all_components, sample_document_data):
        """Test _execute_action routes to process_document correctly."""
        data = {'action': 'process_document', 'data': sample_document_data}
        
        with patch.object(service_with_all_components, 'process_document', return_value=sample_document_data) as mock_process:
            result = await service_with_all_components._execute_action(data)
            
            mock_process.assert_called_once_with(sample_document_data)
            assert result == sample_document_data

    @pytest.mark.asyncio
    async def test_execute_action_classify_case(self, service_with_all_components, sample_document_data):
        """Test _execute_action routes to classify_case correctly."""
        data = {'action': 'classify_case', 'data': sample_document_data}
        
        with patch.object(service_with_all_components, 'classify_case', return_value=sample_document_data) as mock_classify:
            result = await service_with_all_components._execute_action(data)
            
            mock_classify.assert_called_once_with(sample_document_data)
            assert result == sample_document_data


class TestProcessDocumentWorkflow:
    """Test main process_document workflow method."""

    @pytest.mark.asyncio
    async def test_process_document_complete_workflow(self, service_with_all_components, sample_document_data):
        """Test complete document processing workflow."""
        result = await service_with_all_components.process_document(sample_document_data)
        
        # Verify all processing steps were called
        service_with_all_components._docket_cleaner.clean_and_flatten.assert_called_once()
        service_with_all_components._court_processor.process_court_info.assert_called_once()
        service_with_all_components._litigation_classifier.identify_litigation_by_text.assert_called_once()
        service_with_all_components._transfer_handler.process_transfers.assert_called_once()
        service_with_all_components._docket_processor.process.assert_called_once()
        service_with_all_components._validator.validate.assert_called_once()
        
        # Verify data structure
        assert 'docket_num' in result
        assert result['docket_num'] == '1:23-cv-12345'

    @pytest.mark.asyncio
    async def test_process_document_with_html_content(self, service_with_all_components, sample_html_document_data):
        """Test document processing with HTML content."""
        result = await service_with_all_components.process_document(sample_html_document_data)
        
        # Verify HTML processing was called
        service_with_all_components._html_processor.process_html.assert_called_once()
        
        # Verify HTML processing was at least attempted
        # The docket processor may override some results, but HTML processing should have been called
        assert service_with_all_components._html_processor.process_html.called

    @pytest.mark.asyncio
    async def test_process_document_html_only_flag(self, service_with_all_components):
        """Test document processing with html_only flag skips classification."""
        html_only_data = {
            'docket_num': '1:23-cv-11111',
            'html_only': True,
            'html_content': '<html>test</html>'
        }
        
        result = await service_with_all_components.process_document(html_only_data)
        
        # Classification should be skipped
        service_with_all_components._litigation_classifier.identify_litigation_by_text.assert_not_called()
        # Transfer processing happens within classify_case but for html_only cases it returns early

    @pytest.mark.asyncio
    async def test_process_document_html_only_in_flags(self, service_with_all_components):
        """Test document processing with html_only in flags list."""
        html_only_data = {
            'docket_num': '1:23-cv-22222',
            'flags': ['html_only', 'other_flag'],
            'html_content': '<html>test</html>'
        }
        
        await service_with_all_components.process_document(html_only_data)
        
        # Classification should be skipped
        service_with_all_components._litigation_classifier.identify_litigation_by_text.assert_not_called()

    @pytest.mark.asyncio
    async def test_process_document_validation_failure(self, service_with_all_components, sample_document_data):
        """Test document processing continues when validation fails."""
        # Make validator return failure
        service_with_all_components._validator.validate.return_value = {
            'is_valid': False,
            'errors': ['Missing required field']
        }
        
        result = await service_with_all_components.process_document(sample_document_data)
        
        # Processing should complete despite validation failure
        assert 'docket_num' in result

    @pytest.mark.asyncio
    async def test_process_document_error_handling(self, service_with_all_components, sample_document_data):
        """Test error handling in process_document."""
        # Make docket processor raise an exception
        service_with_all_components._docket_processor.process.side_effect = Exception("Processing failed")
        
        with pytest.raises(TransformerServiceError) as exc_info:
            await service_with_all_components.process_document(sample_document_data)
        
        assert "Document processing failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_process_document_missing_components(self, service_minimal, sample_document_data):
        """Test document processing with missing components degrades gracefully."""
        # Should not raise errors even with missing components
        result = await service_minimal.process_document(sample_document_data)
        assert 'docket_num' in result


class TestCaseClassificationMethods:
    """Test case classification methods (from CaseClassificationService)."""

    @pytest.mark.asyncio
    async def test_classify_case_successful(self, service_with_all_components, sample_document_data):
        """Test successful case classification."""
        result = await service_with_all_components.classify_case(sample_document_data)
        
        service_with_all_components._litigation_classifier.identify_litigation_by_text.assert_called_once_with('')
        service_with_all_components._transfer_handler.process_transfers.assert_called_once_with(result)
        
        assert result['litigation_type'] == 'mass_tort'
        assert result['classification_confidence'] == 0.85

    @pytest.mark.asyncio
    async def test_classify_case_missing_classifier(self, service_minimal, sample_document_data):
        """Test case classification with missing classifier."""
        result = await service_minimal.classify_case(sample_document_data)
        
        # Should return original data unchanged
        assert result == sample_document_data

    @pytest.mark.asyncio
    async def test_classify_case_html_only(self, service_with_all_components):
        """Test case classification skipped for html_only cases."""
        html_only_data = {
            'docket_num': '1:23-cv-33333',
            'html_only': True
        }
        
        result = await service_with_all_components.classify_case(html_only_data)
        
        # Classification should be skipped but transfers still processed
        service_with_all_components._litigation_classifier.identify_litigation_by_text.assert_not_called()
        service_with_all_components._transfer_handler.process_transfers.assert_called_once()

    @pytest.mark.asyncio
    async def test_classify_case_missing_transfer_handler(self, mock_logger, mock_config, mock_litigation_classifier):
        """Test case classification with missing transfer handler."""
        service = DocumentProcessingService(
            litigation_classifier=mock_litigation_classifier,
            transfer_handler=None,  # Missing transfer handler
            logger=mock_logger,
            config=mock_config
        )
        
        sample_document_data = {
            'docket_num': '1:23-cv-12345',
            'case_name': 'Test Case',
            'pdf_text': 'Sample text for classification'
        }
        
        result = await service.classify_case(sample_document_data)
        
        # Should still classify but not process transfers
        mock_litigation_classifier.identify_litigation_by_text.assert_called_once()
        assert result['litigation_type'] == 'mass_tort'


class TestDataCleaningMethods:
    """Test data cleaning methods (from DataCleaningService)."""

    @pytest.mark.asyncio
    async def test_clean_docket_data_complete(self, service_with_all_components, sample_document_data):
        """Test complete data cleaning process."""
        pdf_text_data = dict(sample_document_data)
        pdf_text_data['pdf_text'] = 'Sample PDF text content'
        
        result = await service_with_all_components.clean_docket_data(pdf_text_data)
        
        service_with_all_components._docket_cleaner.clean_and_flatten.assert_called_once()
        service_with_all_components._court_processor.process_court_info.assert_called_once()
        service_with_all_components._llm_engine.run_llm_extraction.assert_called_once_with(result, 'Sample PDF text content')

    @pytest.mark.asyncio
    async def test_clean_docket_data_without_pdf_text(self, service_with_all_components, sample_document_data):
        """Test data cleaning without PDF text."""
        result = await service_with_all_components.clean_docket_data(sample_document_data)
        
        service_with_all_components._docket_cleaner.clean_and_flatten.assert_called_once()
        service_with_all_components._court_processor.process_court_info.assert_called_once()
        service_with_all_components._llm_engine.run_llm_extraction.assert_not_called()

    @pytest.mark.asyncio
    async def test_clean_docket_data_mdl_2873_without_pdf(self, service_with_all_components):
        """Test special handling for MDL 2873 cases without PDF text."""
        mdl_data = {
            'docket_num': '1:23-cv-44444',
            'mdl_num': '2873'
        }
        
        result = await service_with_all_components.clean_docket_data(mdl_data)
        
        # Should still process even without PDF text for MDL 2873
        service_with_all_components._docket_cleaner.clean_and_flatten.assert_called_once()
        service_with_all_components._llm_engine.run_llm_extraction.assert_not_called()

    @pytest.mark.asyncio
    async def test_clean_docket_data_missing_components(self, service_minimal, sample_document_data):
        """Test data cleaning with missing components."""
        result = await service_minimal.clean_docket_data(sample_document_data)
        
        # Should return original data without errors
        assert result == sample_document_data


class TestDocketProcessingMethods:
    """Test docket processing methods (from DocketProcessingService)."""

    @pytest.mark.asyncio
    async def test_process_docket_successful(self, service_with_all_components, sample_document_data):
        """Test successful docket processing."""
        result = await service_with_all_components.process_docket(sample_document_data)
        
        service_with_all_components._docket_processor.process.assert_called_once_with(sample_document_data)
        service_with_all_components._text_handler.process_text.assert_called_once_with('Sample docket text content')
        
        assert result['processed'] == True
        assert 'extracted_entities' in result
        assert result['extracted_entities']['names'] == ['John Doe']

    @pytest.mark.asyncio
    async def test_process_docket_without_text_handler(self, mock_logger, mock_config, mock_docket_processor, sample_document_data):
        """Test docket processing without text handler."""
        service = DocumentProcessingService(
            docket_processor=mock_docket_processor,
            text_handler=None,  # Missing text handler
            logger=mock_logger,
            config=mock_config
        )
        
        result = await service.process_docket(sample_document_data)
        
        mock_docket_processor.process.assert_called_once()
        assert 'extracted_entities' not in result

    @pytest.mark.asyncio
    async def test_process_docket_missing_processor(self, service_minimal, sample_document_data):
        """Test docket processing with missing processor."""
        result = await service_minimal.process_docket(sample_document_data)
        
        # Should return original data unchanged
        assert result == sample_document_data


class TestHTMLProcessingMethods:
    """Test HTML processing methods (from HTMLIntegrationService)."""

    @pytest.mark.asyncio
    async def test_process_html_content_from_data(self, service_with_all_components, sample_html_document_data):
        """Test HTML processing from html_content in data."""
        result = await service_with_all_components.process_html_content(sample_html_document_data)
        
        service_with_all_components._html_processor.process_html.assert_called_once_with('<html><body><h1>Test Case</h1></body></html>')
        
        assert result['html_processed'] == True
        assert 'extracted_data' in result
        assert result['extracted_data']['case_name'] == 'Test Case'

    @pytest.mark.asyncio
    async def test_process_html_content_from_file_path(self, service_with_all_components, sample_document_data, tmp_path):
        """Test HTML processing from html_path file."""
        # Create temporary HTML file
        html_file = tmp_path / "test.html"
        html_content = "<html><body><h2>File Test</h2></body></html>"
        html_file.write_text(html_content)
        
        data_with_path = dict(sample_document_data)
        data_with_path['html_path'] = str(html_file)
        
        result = await service_with_all_components.process_html_content(data_with_path)
        
        service_with_all_components._html_processor.process_html.assert_called_once_with(html_content)
        assert result['html_processed'] == True

    @pytest.mark.asyncio
    async def test_process_html_content_file_not_found(self, service_with_all_components, sample_document_data):
        """Test HTML processing with non-existent file path."""
        data_with_bad_path = dict(sample_document_data)
        data_with_bad_path['html_path'] = '/nonexistent/file.html'
        
        result = await service_with_all_components.process_html_content(data_with_bad_path)
        
        # Should return original data without processing
        service_with_all_components._html_processor.process_html.assert_not_called()
        assert 'html_processed' not in result

    @pytest.mark.asyncio
    async def test_process_html_content_no_html(self, service_with_all_components, sample_document_data):
        """Test HTML processing with no HTML content or path."""
        result = await service_with_all_components.process_html_content(sample_document_data)
        
        service_with_all_components._html_processor.process_html.assert_not_called()
        assert result == sample_document_data

    @pytest.mark.asyncio
    async def test_process_html_content_missing_processor(self, service_minimal, sample_html_document_data):
        """Test HTML processing with missing processor."""
        result = await service_minimal.process_html_content(sample_html_document_data)
        
        # Should return original data unchanged
        assert result == sample_html_document_data


class TestValidationMethods:
    """Test document validation methods."""

    @pytest.mark.asyncio
    async def test_validate_document_successful(self, service_with_all_components, sample_document_data):
        """Test successful document validation."""
        result = await service_with_all_components.validate_document(sample_document_data)
        
        service_with_all_components._validator.validate.assert_called_once_with(sample_document_data)
        
        assert result['is_valid'] == True
        assert result['errors'] == []

    @pytest.mark.asyncio
    async def test_validate_document_with_errors(self, service_with_all_components, sample_document_data):
        """Test document validation with errors."""
        service_with_all_components._validator.validate.return_value = {
            'is_valid': False,
            'errors': ['Missing required field: case_name']
        }
        
        result = await service_with_all_components.validate_document(sample_document_data)
        
        assert result['is_valid'] == False
        assert len(result['errors']) == 1
        assert 'Missing required field: case_name' in result['errors']

    @pytest.mark.asyncio
    async def test_validate_document_missing_validator(self, service_minimal, sample_document_data):
        """Test document validation with missing validator."""
        result = await service_minimal.validate_document(sample_document_data)
        
        # Should return default valid response
        assert result['is_valid'] == True
        assert result['errors'] == []


class TestHelperMethods:
    """Test helper methods."""

    def test_is_html_only_flag_true(self, service_with_all_components):
        """Test _is_html_only with html_only flag set to True."""
        data = {'html_only': True, 'docket_num': '1:23-cv-55555'}
        
        assert service_with_all_components._is_html_only(data) == True

    def test_is_html_only_flag_false(self, service_with_all_components):
        """Test _is_html_only with html_only flag set to False."""
        data = {'html_only': False, 'docket_num': '1:23-cv-66666'}
        
        assert service_with_all_components._is_html_only(data) == False

    def test_is_html_only_in_flags_list(self, service_with_all_components):
        """Test _is_html_only with html_only in flags list."""
        data = {'flags': ['html_only', 'other_flag'], 'docket_num': '1:23-cv-77777'}
        
        assert service_with_all_components._is_html_only(data) == True

    def test_is_html_only_not_in_flags_list(self, service_with_all_components):
        """Test _is_html_only with html_only not in flags list."""
        data = {'flags': ['other_flag'], 'docket_num': '1:23-cv-88888'}
        
        assert service_with_all_components._is_html_only(data) == False

    def test_is_html_only_no_flag_or_flags(self, service_with_all_components):
        """Test _is_html_only with no html_only flag or flags list."""
        data = {'docket_num': '1:23-cv-99999'}
        
        assert service_with_all_components._is_html_only(data) == False


class TestHealthCheckAndCleanup:
    """Test health check and cleanup methods."""

    @pytest.mark.asyncio
    async def test_health_check_all_components_available(self, service_with_all_components):
        """Test health check with all components available."""
        result = await service_with_all_components.health_check()
        
        assert result['service'] == 'DocumentProcessingService'
        assert result['status'] == 'healthy'
        
        # Check all components are marked as available
        expected_components = [
            'litigation_classifier', 'transfer_handler', 'docket_cleaner',
            'llm_engine', 'court_processor', 'docket_processor',
            'html_processor', 'text_handler', 'validator'
        ]
        
        for component in expected_components:
            assert result['components'][component] == 'available'

    @pytest.mark.asyncio
    async def test_health_check_missing_critical_components(self, service_minimal):
        """Test health check with missing critical components."""
        result = await service_minimal.health_check()
        
        assert result['service'] == 'DocumentProcessingService'
        assert result['status'] == 'degraded'
        assert 'missing_critical' in result
        assert 'docket_cleaner' in result['missing_critical']
        assert 'docket_processor' in result['missing_critical']

    @pytest.mark.asyncio
    async def test_health_check_partial_components(self, mock_logger, mock_config, mock_docket_cleaner, mock_docket_processor):
        """Test health check with some components available."""
        service = DocumentProcessingService(
            docket_cleaner=mock_docket_cleaner,
            docket_processor=mock_docket_processor,
            logger=mock_logger,
            config=mock_config
        )
        
        result = await service.health_check()
        
        assert result['status'] == 'healthy'  # Critical components are available
        assert result['components']['docket_cleaner'] == 'available'
        assert result['components']['docket_processor'] == 'available'
        assert result['components']['litigation_classifier'] == 'unavailable'

    @pytest.mark.asyncio
    async def test_cleanup_all_components(self, service_with_all_components):
        """Test cleanup with all components."""
        # Add cleanup method to some mock components
        service_with_all_components._docket_cleaner.cleanup = AsyncMock()
        service_with_all_components._html_processor.cleanup = AsyncMock()
        
        await service_with_all_components.cleanup()
        
        # Components with cleanup method should be called
        service_with_all_components._docket_cleaner.cleanup.assert_called_once()
        service_with_all_components._html_processor.cleanup.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_component_error(self, service_with_all_components):
        """Test cleanup handles component cleanup errors gracefully."""
        # Make component cleanup raise an exception
        service_with_all_components._docket_cleaner.cleanup = AsyncMock(side_effect=Exception("Cleanup failed"))
        
        # Should not raise exception
        await service_with_all_components.cleanup()

    @pytest.mark.asyncio
    async def test_cleanup_minimal_service(self, service_minimal):
        """Test cleanup with minimal service (no components)."""
        # Should not raise errors
        await service_minimal.cleanup()


class TestErrorScenarios:
    """Test error handling and edge cases."""

    @pytest.mark.asyncio
    async def test_component_failure_during_processing(self, service_with_all_components, sample_document_data):
        """Test handling of component failures during processing."""
        # Make classification fail
        service_with_all_components._litigation_classifier.identify_litigation_by_text.side_effect = Exception("Classification failed")
        
        with pytest.raises(TransformerServiceError):
            await service_with_all_components.process_document(sample_document_data)

    @pytest.mark.asyncio
    async def test_data_corruption_handling(self, service_with_all_components):
        """Test handling of corrupted or malformed data."""
        corrupted_data = {
            'docket_num': None,  # Invalid docket number
            'case_name': '',     # Empty case name
            'invalid_field': {'nested': {'deep': 'data'}}  # Unexpected nested data
        }
        
        # Should process without raising errors
        result = await service_with_all_components.process_document(corrupted_data)
        assert 'docket_num' in result

    @pytest.mark.asyncio
    async def test_async_context_manager(self, service_with_all_components):
        """Test service as async context manager."""
        async with service_with_all_components as service:
            assert service is not None
            # Service should be initialized (if implementation supports it)

    @pytest.mark.asyncio
    async def test_logging_during_operations(self, service_with_all_components, sample_document_data, mock_logger):
        """Test that appropriate logging occurs during operations."""
        await service_with_all_components.process_document(sample_document_data)
        
        # Verify info logging was called for processing steps
        mock_logger.info.assert_called()
        
        # Check some expected log messages were made
        call_args_list = [call[0][0] for call in mock_logger.info.call_args_list]
        assert any("Starting document processing" in msg for msg in call_args_list)
        assert any("Document processing completed" in msg for msg in call_args_list)


class TestPerformanceAndScalability:
    """Test performance characteristics and scalability."""

    @pytest.mark.asyncio
    async def test_concurrent_document_processing(self, service_with_all_components):
        """Test processing multiple documents concurrently."""
        documents = [
            {'docket_num': f'1:23-cv-{i:05d}', 'case_name': f'Case {i}'}
            for i in range(5)
        ]
        
        # Mock docket processor to return original docket_num instead of fixed one
        def mock_process(data):
            result = dict(data)
            result.update({
                'processed': True,
                'all_text': 'Sample docket text content'
            })
            return result
        
        service_with_all_components._docket_processor.process = AsyncMock(side_effect=mock_process)
        
        # Process all documents concurrently
        tasks = [service_with_all_components.process_document(doc) for doc in documents]
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 5
        for i, result in enumerate(results):
            assert result['docket_num'] == f'1:23-cv-{i:05d}'

    @pytest.mark.asyncio
    async def test_large_document_processing(self, service_with_all_components):
        """Test processing of large document data."""
        large_document = {
            'docket_num': '1:23-cv-large',
            'case_name': 'Large Case',
            'pdf_text': 'A' * 10000,  # Large text content
            'html_content': '<html>' + 'B' * 5000 + '</html>',  # Large HTML
            'metadata': {'large_field': list(range(1000))}  # Large metadata
        }
        
        # Mock docket processor to preserve the original docket_num
        def mock_process_large(data):
            result = dict(data)
            result.update({
                'processed': True,
                'all_text': 'Large document text content processed'
            })
            return result
        
        service_with_all_components._docket_processor.process = AsyncMock(side_effect=mock_process_large)
        
        result = await service_with_all_components.process_document(large_document)
        assert result['docket_num'] == '1:23-cv-large'

    def test_memory_efficiency_component_references(self, service_with_all_components):
        """Test that component references don't create memory leaks."""
        import gc
        
        # Get initial reference count
        initial_refs = len(gc.get_referrers(service_with_all_components._litigation_classifier))
        
        # Create and destroy service instances
        for _ in range(10):
            temp_service = DocumentProcessingService(
                litigation_classifier=service_with_all_components._litigation_classifier,
                logger=service_with_all_components.logger,
                config=service_with_all_components.config
            )
            del temp_service
        
        gc.collect()  # Force garbage collection
        
        # Reference count should not have grown significantly
        final_refs = len(gc.get_referrers(service_with_all_components._litigation_classifier))
        assert final_refs <= initial_refs + 2  # Allow some tolerance