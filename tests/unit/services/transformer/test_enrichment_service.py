"""
Comprehensive unit tests for EnrichmentService.

Tests the consolidation of:
- MDLProcessingService functionality (MDL info, descriptions, lookups)
- LawFirmProcessingService functionality (law firm normalization and integration)
- LLM-based enrichment functionality

Key workflows tested:
- Document enrichment orchestration
- MDL information processing and validation
- Law firm processing and normalization
- LLM-based enrichment (when available)
- Health monitoring and error handling
"""
import pytest
import asyncio
import logging
import pandas as pd
from typing import Dict, Any, Optional, List
from unittest.mock import Mock, AsyncMock, patch, MagicMock

from src.transformer.facades.enrichment_service import EnrichmentService
from src.infrastructure.protocols.exceptions import TransformerServiceError

# Mock MDL components
from src.transformer.components.mdl.mdl_processor import MDLProcessor
from src.transformer.components.mdl.description_manager import MDLDescriptionManager
from src.transformer.components.mdl.lookup_manager import MDLLookupManager
from src.transformer.components.mdl.persistence_manager import MDLPersistenceManager
from src.transformer.components.mdl.data_processor import MDLDataProcessor

# Mock law firm components
from src.transformer.components.law_firm.processor import LawFirmProcessor
from src.transformer.components.law_firm.integration import LawFirmIntegration


@pytest.fixture
def mock_logger():
    """Mock logger for testing."""
    return Mock(spec=logging.Logger)


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    return {
        'test_mode': True,
        'enable_llm_enrichment': False,
        'llm_enrichment_case_types': ['mass_tort', 'product_liability'],
        'mdl_processing': {'enabled': True},
        'law_firm_processing': {'enabled': True}
    }


@pytest.fixture
def mock_llm_client():
    """Mock LLM client for testing."""
    mock = Mock()
    mock.query = AsyncMock(return_value={
        'entities': {'plaintiff': 'John Doe', 'defendant': 'Acme Corp'},
        'summary': 'Product liability case involving defective product',
        'inferred': {'case_category': 'commercial_litigation'}
    })
    return mock


# ==================== MDL Component Fixtures ====================

@pytest.fixture
def mock_mdl_processor():
    """Mock MDL processor component."""
    mock = Mock(spec=MDLProcessor)
    mock.add_mdl_info_to_docket = AsyncMock(return_value={
        'mdl_num': '2929',
        'mdl_name': 'Test MDL Litigation',
        'court': 'ndca',
        'judge': 'Hon. Test Judge',
        'is_tag_along': False,
        'transfer_date': '2023-01-15'
    })
    return mock


@pytest.fixture
def mock_mdl_description_manager():
    """Mock MDL description manager component."""
    mock = Mock(spec=MDLDescriptionManager)
    mock.get_mdl_description = AsyncMock(return_value={
        'mdl_num': '2929',
        'description': 'Multi-district litigation for test product liability cases',
        'category': 'product_liability',
        'last_updated': '2023-01-15'
    })
    mock.update_descriptions = AsyncMock(return_value=True)
    return mock


@pytest.fixture
def mock_mdl_lookup_manager():
    """Mock MDL lookup manager component."""
    mock = Mock(spec=MDLLookupManager)
    mock.lookup_mdl = AsyncMock(return_value={
        'mdl_num': '2929',
        'status': 'active',
        'filing_date': '2020-03-15',
        'court_info': {
            'court_id': 'ndca',
            'district': 'Northern District of California'
        }
    })
    return mock


@pytest.fixture
def mock_mdl_persistence_manager():
    """Mock MDL persistence manager component."""
    mock = Mock(spec=MDLPersistenceManager)
    
    # Create mock DataFrame for MDL litigation data
    mock_df = pd.DataFrame({
        'mdl_num': ['2929', '2928'],
        'mdl_name': ['Test MDL 1', 'Test MDL 2'],
        'court': ['ndca', 'sdny'],
        'judge': ['Judge A', 'Judge B']
    })
    
    mock.load_mdl_litigation_data = AsyncMock(return_value=mock_df)
    mock.save_mdl_data = AsyncMock(return_value=True)
    return mock


@pytest.fixture
def mock_mdl_data_processor():
    """Mock MDL data processor component."""
    mock = Mock(spec=MDLDataProcessor)
    
    mock.filter_mdl_docket_patterns = AsyncMock(return_value=[
        '/path/to/mdl_case_1.json',
        '/path/to/mdl_case_2.json'
    ])
    
    mock.validate_docket_mdl_data = AsyncMock(return_value=True)
    return mock


# ==================== Law Firm Component Fixtures ====================

@pytest.fixture
def mock_law_firm_processor():
    """Mock law firm processor component."""
    mock = Mock(spec=LawFirmProcessor)
    
    mock.normalize_law_firms = AsyncMock(return_value={
        'normalized_firms': [
            {
                'name': 'Smith & Associates LLP',
                'normalized_name': 'smith_associates_llp',
                'attorneys': ['John Smith', 'Jane Smith']
            },
            {
                'name': 'Wilson Law Group',
                'normalized_name': 'wilson_law_group',
                'attorneys': ['Bob Wilson']
            }
        ]
    })
    
    mock.process_attorney_data = AsyncMock(return_value=True)
    return mock


@pytest.fixture
def mock_law_firm_integration():
    """Mock law firm integration component."""
    mock = Mock(spec=LawFirmIntegration)
    
    mock.process_law_firms = AsyncMock(return_value=True)
    mock.integrate_firm_data = AsyncMock(return_value={
        'firms_integrated': 2,
        'attorneys_processed': 3,
        'integration_status': 'success'
    })
    return mock


# ==================== Service Fixtures ====================

@pytest.fixture
def enrichment_service_full(
    mock_logger,
    mock_config,
    mock_llm_client,
    mock_mdl_processor,
    mock_mdl_description_manager,
    mock_mdl_lookup_manager,
    mock_mdl_persistence_manager,
    mock_mdl_data_processor,
    mock_law_firm_processor,
    mock_law_firm_integration
):
    """EnrichmentService with all components mocked."""
    return EnrichmentService(
        # MDL components
        mdl_processor=mock_mdl_processor,
        mdl_description_manager=mock_mdl_description_manager,
        mdl_lookup_manager=mock_mdl_lookup_manager,
        mdl_persistence_manager=mock_mdl_persistence_manager,
        mdl_data_processor=mock_mdl_data_processor,
        # Law firm components
        law_firm_processor=mock_law_firm_processor,
        law_firm_integration=mock_law_firm_integration,
        # LLM client
        llm_client=mock_llm_client,
        # Base config
        config=mock_config,
        logger=mock_logger
    )


@pytest.fixture
def enrichment_service_minimal(mock_logger, mock_config):
    """EnrichmentService with minimal components (for degraded mode testing)."""
    return EnrichmentService(
        config=mock_config,
        logger=mock_logger
    )


# ==================== Main Entry Point Tests ====================

class TestEnrichmentServiceMainWorkflow:
    """Test the main enrichment workflow orchestration."""

    @pytest.mark.asyncio
    async def test_enrich_document_full_workflow(self, enrichment_service_full):
        """Test complete document enrichment workflow with all components."""
        # Arrange
        test_data = {
            'docket_num': '1:23-cv-12345',
            'case_name': 'Test v. Corp',
            'mdl_num': '2929',
            'plaintiff_attorneys': ['John Smith'],
            'defendant_attorneys': ['Jane Wilson']
        }

        # Act
        result = await enrichment_service_full.enrich_document(test_data)

        # Assert
        assert result['enrichment_completed'] is True
        assert 'mdl_info' in result
        assert result['has_mdl'] is True
        assert result['law_firms_processed'] is True
        assert 'normalized_law_firms' in result
        
        # Verify MDL processing was called
        enrichment_service_full._mdl_processor.add_mdl_info_to_docket.assert_called_once()
        
        # Verify law firm processing was called
        enrichment_service_full._law_firm_integration.process_law_firms.assert_called_once()

    @pytest.mark.asyncio
    async def test_enrich_document_selective_processing(self, enrichment_service_full):
        """Test that enrichment selectively processes based on data content."""
        # Arrange - data without MDL or law firm info
        test_data = {
            'docket_num': '1:23-cv-12345',
            'case_name': 'Simple Contract Case'
        }

        # Act
        result = await enrichment_service_full.enrich_document(test_data)

        # Assert
        assert result['enrichment_completed'] is True
        
        # Verify MDL processing was NOT called (no MDL indicators)
        enrichment_service_full._mdl_processor.add_mdl_info_to_docket.assert_not_called()
        
        # Verify law firm processing was NOT called (no attorney info)
        enrichment_service_full._law_firm_integration.process_law_firms.assert_not_called()

    @pytest.mark.asyncio
    async def test_enrich_document_with_llm_enabled(self, enrichment_service_full):
        """Test document enrichment with LLM enrichment enabled."""
        # Arrange
        enrichment_service_full.config['enable_llm_enrichment'] = True
        test_data = {
            'docket_num': '1:23-cv-12345',
            'case_name': 'Product Liability Case'
        }

        # Act
        with patch.object(enrichment_service_full, 'enrich_with_llm', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = test_data
            result = await enrichment_service_full.enrich_document(test_data)

        # Assert
        mock_llm.assert_called_once_with(test_data)

    @pytest.mark.asyncio
    async def test_enrich_document_error_handling(self, enrichment_service_full):
        """Test error handling in main enrichment workflow."""
        # Arrange
        test_data = {'docket_num': '1:23-cv-12345'}
        enrichment_service_full._mdl_processor.add_mdl_info_to_docket.side_effect = Exception("MDL error")

        # Act & Assert
        with pytest.raises(TransformerServiceError, match="Document enrichment failed"):
            await enrichment_service_full.enrich_document(test_data)


# ==================== MDL Processing Tests ====================

class TestMDLProcessing:
    """Test MDL-related functionality consolidated from MDLProcessingService."""

    @pytest.mark.asyncio
    async def test_add_mdl_info_to_docket_success(self, enrichment_service_full):
        """Test successful MDL information addition to docket."""
        # Arrange
        test_data = {
            'docket_num': '1:23-cv-12345',
            'mdl_num': '2929'
        }

        # Act
        result = await enrichment_service_full.add_mdl_info_to_docket(test_data)

        # Assert
        assert 'mdl_info' in result
        assert result['has_mdl'] is True
        assert 'mdl_description' in result
        
        # Verify components were called
        enrichment_service_full._mdl_processor.add_mdl_info_to_docket.assert_called_once()
        enrichment_service_full._mdl_description_manager.get_mdl_description.assert_called_once_with('2929')

    @pytest.mark.asyncio
    async def test_add_mdl_info_without_processor(self, enrichment_service_minimal):
        """Test MDL info addition when processor is not available."""
        # Arrange
        test_data = {'docket_num': '1:23-cv-12345'}

        # Act
        result = await enrichment_service_minimal.add_mdl_info_to_docket(test_data)

        # Assert
        assert result == test_data  # Data unchanged
        # Warning should be logged but no error thrown

    @pytest.mark.asyncio
    async def test_add_mdl_info_with_custom_litigation_data(self, enrichment_service_full):
        """Test MDL info addition with custom litigation DataFrame."""
        # Arrange
        test_data = {'docket_num': '1:23-cv-12345'}
        custom_df = pd.DataFrame({'mdl_num': ['2930'], 'mdl_name': ['Custom MDL']})

        # Act
        result = await enrichment_service_full.add_mdl_info_to_docket(test_data, custom_df)

        # Assert
        enrichment_service_full._mdl_processor.add_mdl_info_to_docket.assert_called_once_with(
            test_data, custom_df
        )

    @pytest.mark.asyncio
    async def test_filter_mdl_docket_patterns_success(self, enrichment_service_full):
        """Test MDL docket pattern filtering."""
        # Arrange
        input_paths = [
            '/path/to/regular_case.json',
            '/path/to/mdl_case_1.json',
            '/path/to/mdl_case_2.json',
            '/path/to/another_case.json'
        ]

        # Act
        result = await enrichment_service_full.filter_mdl_docket_patterns(input_paths)

        # Assert
        assert len(result) == 2  # Only MDL cases returned
        assert '/path/to/mdl_case_1.json' in result
        assert '/path/to/mdl_case_2.json' in result
        
        enrichment_service_full._mdl_data_processor.filter_mdl_docket_patterns.assert_called_once_with(input_paths)

    @pytest.mark.asyncio
    async def test_filter_mdl_docket_patterns_without_processor(self, enrichment_service_minimal):
        """Test MDL pattern filtering when processor is not available."""
        # Arrange
        input_paths = ['/path/to/case1.json', '/path/to/case2.json']

        # Act
        result = await enrichment_service_minimal.filter_mdl_docket_patterns(input_paths)

        # Assert
        assert result == input_paths  # All paths returned unchanged

    @pytest.mark.asyncio
    async def test_validate_docket_mdl_data_success(self, enrichment_service_full):
        """Test successful MDL data validation."""
        # Arrange
        test_data = {
            'docket_num': '1:23-cv-12345',
            'mdl_info': {'mdl_num': '2929', 'valid': True}
        }

        # Act
        result = await enrichment_service_full.validate_docket_mdl_data(test_data)

        # Assert
        assert result is True
        enrichment_service_full._mdl_data_processor.validate_docket_mdl_data.assert_called_once_with(test_data)

    @pytest.mark.asyncio
    async def test_validate_docket_mdl_data_without_processor(self, enrichment_service_minimal):
        """Test MDL validation when processor is not available."""
        # Arrange
        test_data = {'docket_num': '1:23-cv-12345'}

        # Act
        result = await enrichment_service_minimal.validate_docket_mdl_data(test_data)

        # Assert
        assert result is True  # Default to valid when no processor

    @pytest.mark.asyncio
    async def test_update_mdl_descriptions_success(self, enrichment_service_full):
        """Test successful MDL description updates."""
        # Arrange
        mdl_data = {
            'mdl_num': '2929',
            'description': 'Updated description',
            'category': 'product_liability'
        }

        # Act
        result = await enrichment_service_full.update_mdl_descriptions(mdl_data)

        # Assert
        assert result is True
        enrichment_service_full._mdl_description_manager.update_descriptions.assert_called_once_with(mdl_data)

    @pytest.mark.asyncio
    async def test_get_mdl_lookup_success(self, enrichment_service_full):
        """Test successful MDL lookup."""
        # Arrange
        mdl_num = '2929'

        # Act
        result = await enrichment_service_full.get_mdl_lookup(mdl_num)

        # Assert
        assert result is not None
        assert result['mdl_num'] == '2929'
        enrichment_service_full._mdl_lookup_manager.lookup_mdl.assert_called_once_with(mdl_num)

    @pytest.mark.asyncio
    async def test_get_mdl_lookup_without_manager(self, enrichment_service_minimal):
        """Test MDL lookup when manager is not available."""
        # Act
        result = await enrichment_service_minimal.get_mdl_lookup('2929')

        # Assert
        assert result is None


# ==================== Law Firm Processing Tests ====================

class TestLawFirmProcessing:
    """Test law firm processing functionality consolidated from LawFirmProcessingService."""

    @pytest.mark.asyncio
    async def test_process_law_firms_success(self, enrichment_service_full):
        """Test successful law firm processing."""
        # Arrange
        test_data = {
            'docket_num': '1:23-cv-12345',
            'plaintiff_attorneys': ['John Smith', 'Jane Smith'],
            'defendant_attorneys': ['Bob Wilson'],
            'law_firms': ['Smith & Associates', 'Wilson Law Group']
        }

        # Act
        result = await enrichment_service_full.process_law_firms(test_data)

        # Assert
        assert result['law_firms_processed'] is True
        assert 'normalized_law_firms' in result
        
        # Verify components were called
        enrichment_service_full._law_firm_integration.process_law_firms.assert_called_once_with(test_data)
        enrichment_service_full._law_firm_processor.normalize_law_firms.assert_called_once_with(test_data)

    @pytest.mark.asyncio
    async def test_process_law_firms_integration_only(self, enrichment_service_full):
        """Test law firm processing with only integration component."""
        # Arrange
        enrichment_service_full._law_firm_processor = None  # Remove processor
        test_data = {
            'docket_num': '1:23-cv-12345',
            'attorneys': ['John Doe']
        }

        # Act
        result = await enrichment_service_full.process_law_firms(test_data)

        # Assert
        assert result['law_firms_processed'] is True
        assert 'normalized_law_firms' not in result  # No processor available
        
        enrichment_service_full._law_firm_integration.process_law_firms.assert_called_once_with(test_data)

    @pytest.mark.asyncio
    async def test_process_law_firms_without_integration(self, enrichment_service_minimal):
        """Test law firm processing when integration is not available."""
        # Arrange
        test_data = {'docket_num': '1:23-cv-12345', 'attorneys': ['John Doe']}

        # Act
        result = await enrichment_service_minimal.process_law_firms(test_data)

        # Assert
        assert result == test_data  # Data unchanged
        # Warning should be logged but no error thrown

    @pytest.mark.asyncio
    async def test_process_law_firms_integration_failure(self, enrichment_service_full):
        """Test law firm processing when integration fails."""
        # Arrange
        enrichment_service_full._law_firm_integration.process_law_firms.return_value = False
        test_data = {'docket_num': '1:23-cv-12345', 'attorneys': ['John Doe']}

        # Act
        result = await enrichment_service_full.process_law_firms(test_data)

        # Assert
        assert 'law_firms_processed' not in result
        assert 'normalized_law_firms' not in result  # Processor not called on failure


# ==================== LLM Enrichment Tests ====================

class TestLLMEnrichment:
    """Test LLM-based enrichment functionality."""

    @pytest.mark.asyncio
    async def test_enrich_with_llm_success(self, enrichment_service_full):
        """Test successful LLM enrichment."""
        # Arrange
        test_data = {
            'docket_num': '1:23-cv-12345',
            'case_name': 'Product Liability Case',
            'court': 'ndca',
            'parties': ['Plaintiff A', 'Defendant B']
        }

        # Mock LLM call
        with patch.object(enrichment_service_full, '_call_llm', new_callable=AsyncMock) as mock_llm_call:
            mock_llm_call.return_value = {
                'entities': {'plaintiff': 'Plaintiff A', 'defendant': 'Defendant B'},
                'summary': 'Product liability case analysis',
                'inferred': {'case_category': 'tort'}
            }

            # Act
            result = await enrichment_service_full.enrich_with_llm(test_data)

        # Assert
        assert 'llm_enrichment' in result
        assert 'entities' in result
        assert result['entities']['plaintiff'] == 'Plaintiff A'
        assert result['case_summary'] == 'Product liability case analysis'
        assert 'inferred_data' in result

    @pytest.mark.asyncio
    async def test_enrich_with_llm_without_client(self, enrichment_service_minimal):
        """Test LLM enrichment when client is not available."""
        # Arrange
        test_data = {'docket_num': '1:23-cv-12345'}

        # Act
        result = await enrichment_service_minimal.enrich_with_llm(test_data)

        # Assert
        assert result == test_data  # Data unchanged
        assert 'llm_enrichment' not in result

    @pytest.mark.asyncio
    async def test_enrich_with_llm_error_handling(self, enrichment_service_full):
        """Test LLM enrichment error handling."""
        # Arrange
        test_data = {'docket_num': '1:23-cv-12345'}
        
        with patch.object(enrichment_service_full, '_call_llm', new_callable=AsyncMock) as mock_llm_call:
            mock_llm_call.side_effect = Exception("LLM service error")

            # Act
            result = await enrichment_service_full.enrich_with_llm(test_data)

        # Assert
        assert result == test_data  # Data unchanged, error logged but not raised
        assert 'llm_enrichment' not in result

    def test_prepare_llm_context(self, enrichment_service_full):
        """Test LLM context preparation."""
        # Arrange
        test_data = {
            'docket_num': '1:23-cv-12345',
            'case_name': 'Test Case',
            'court': 'ndca',
            'judge': 'Hon. Test Judge',
            'parties': ['Plaintiff', 'Defendant'],
            'irrelevant_field': 'should_not_appear'
        }

        # Act
        context = enrichment_service_full._prepare_llm_context(test_data)

        # Assert
        assert 'docket_num: 1:23-cv-12345' in context
        assert 'case_name: Test Case' in context
        assert 'court: ndca' in context
        assert 'irrelevant_field' not in context

    def test_build_enrichment_prompt(self, enrichment_service_full):
        """Test enrichment prompt building."""
        # Arrange
        context = "docket_num: 1:23-cv-12345\ncase_name: Test Case"

        # Act
        prompt = enrichment_service_full._build_enrichment_prompt(context)

        # Assert
        assert "legal case information" in prompt
        assert context in prompt
        assert "Key entities" in prompt
        assert "JSON" in prompt

    def test_merge_llm_enrichment(self, enrichment_service_full):
        """Test merging LLM enrichment results."""
        # Arrange
        data = {
            'docket_num': '1:23-cv-12345',
            'entities': {'existing': 'entity'}
        }
        llm_response = {
            'entities': {'plaintiff': 'John Doe', 'defendant': 'Acme Corp'},
            'summary': 'Case summary',
            'inferred': {'category': 'commercial'}
        }

        # Act
        enrichment_service_full._merge_llm_enrichment(data, llm_response)

        # Assert
        assert data['entities']['existing'] == 'entity'  # Preserved
        assert data['entities']['plaintiff'] == 'John Doe'  # Added
        assert data['case_summary'] == 'Case summary'
        assert data['inferred_data'] == {'category': 'commercial'}


# ==================== Decision Logic Tests ====================

class TestDecisionLogic:
    """Test the decision logic for when to apply different enrichments."""

    def test_should_process_mdl_with_mdl_num(self, enrichment_service_full):
        """Test MDL processing decision with MDL number."""
        data = {'mdl_num': '2929'}
        assert enrichment_service_full._should_process_mdl(data) is True

    def test_should_process_mdl_with_is_mdl_flag(self, enrichment_service_full):
        """Test MDL processing decision with is_mdl flag."""
        data = {'is_mdl': True}
        assert enrichment_service_full._should_process_mdl(data) is True

    def test_should_process_mdl_with_docket_pattern(self, enrichment_service_full):
        """Test MDL processing decision with MDL in docket number."""
        data = {'docket_num': '1:23-mdl-12345'}
        assert enrichment_service_full._should_process_mdl(data) is True

    def test_should_not_process_mdl_without_indicators(self, enrichment_service_full):
        """Test MDL processing decision without indicators."""
        data = {'docket_num': '1:23-cv-12345'}
        assert enrichment_service_full._should_process_mdl(data) is False

    def test_should_process_law_firms_with_plaintiff_attorneys(self, enrichment_service_full):
        """Test law firm processing decision with plaintiff attorneys."""
        data = {'plaintiff_attorneys': ['John Doe']}
        assert enrichment_service_full._should_process_law_firms(data) is True

    def test_should_process_law_firms_with_defendant_attorneys(self, enrichment_service_full):
        """Test law firm processing decision with defendant attorneys."""
        data = {'defendant_attorneys': ['Jane Smith']}
        assert enrichment_service_full._should_process_law_firms(data) is True

    def test_should_process_law_firms_with_general_attorneys(self, enrichment_service_full):
        """Test law firm processing decision with general attorneys field."""
        data = {'attorneys': ['Bob Wilson']}
        assert enrichment_service_full._should_process_law_firms(data) is True

    def test_should_process_law_firms_with_law_firms_field(self, enrichment_service_full):
        """Test law firm processing decision with law_firms field."""
        data = {'law_firms': ['Smith & Associates']}
        assert enrichment_service_full._should_process_law_firms(data) is True

    def test_should_not_process_law_firms_without_attorney_info(self, enrichment_service_full):
        """Test law firm processing decision without attorney information."""
        data = {'docket_num': '1:23-cv-12345'}
        assert enrichment_service_full._should_process_law_firms(data) is False

    def test_should_enrich_with_llm_when_enabled(self, enrichment_service_full):
        """Test LLM enrichment decision when enabled in config."""
        enrichment_service_full.config['enable_llm_enrichment'] = True
        data = {'docket_num': '1:23-cv-12345'}
        assert enrichment_service_full._should_enrich_with_llm(data) is True

    def test_should_enrich_with_llm_for_specific_case_types(self, enrichment_service_full):
        """Test LLM enrichment decision for specific case types."""
        data = {'case_type': 'mass_tort'}
        assert enrichment_service_full._should_enrich_with_llm(data) is True

    def test_should_not_enrich_with_llm_when_disabled(self, enrichment_service_full):
        """Test LLM enrichment decision when disabled."""
        enrichment_service_full.config['enable_llm_enrichment'] = False
        data = {'case_type': 'contract'}
        assert enrichment_service_full._should_enrich_with_llm(data) is False

    def test_should_not_enrich_with_llm_without_client(self, enrichment_service_minimal):
        """Test LLM enrichment decision without client."""
        data = {'case_type': 'mass_tort'}
        assert enrichment_service_minimal._should_enrich_with_llm(data) is False


# ==================== Action Routing Tests ====================

class TestActionRouting:
    """Test the _execute_action method for routing different actions."""

    @pytest.mark.asyncio
    async def test_execute_action_enrich_document(self, enrichment_service_full):
        """Test action routing for document enrichment."""
        # Arrange
        action_data = {
            'action': 'enrich_document',
            'data': {'docket_num': '1:23-cv-12345'}
        }

        # Act
        with patch.object(enrichment_service_full, 'enrich_document', new_callable=AsyncMock) as mock_method:
            mock_method.return_value = {'enriched': True}
            result = await enrichment_service_full._execute_action(action_data)

        # Assert
        mock_method.assert_called_once_with({'docket_num': '1:23-cv-12345'})
        assert result == {'enriched': True}

    @pytest.mark.asyncio
    async def test_execute_action_add_mdl_info(self, enrichment_service_full):
        """Test action routing for MDL info addition."""
        # Arrange
        action_data = {
            'action': 'add_mdl_info',
            'data': {'docket_num': '1:23-cv-12345'}
        }

        # Act
        with patch.object(enrichment_service_full, 'add_mdl_info_to_docket', new_callable=AsyncMock) as mock_method:
            mock_method.return_value = {'mdl_added': True}
            result = await enrichment_service_full._execute_action(action_data)

        # Assert
        mock_method.assert_called_once_with({'docket_num': '1:23-cv-12345'})

    @pytest.mark.asyncio
    async def test_execute_action_process_law_firms(self, enrichment_service_full):
        """Test action routing for law firm processing."""
        # Arrange
        action_data = {
            'action': 'process_law_firms',
            'data': {'attorneys': ['John Doe']}
        }

        # Act
        with patch.object(enrichment_service_full, 'process_law_firms', new_callable=AsyncMock) as mock_method:
            mock_method.return_value = {'processed': True}
            result = await enrichment_service_full._execute_action(action_data)

        # Assert
        mock_method.assert_called_once_with({'attorneys': ['John Doe']})

    @pytest.mark.asyncio
    async def test_execute_action_unknown_action(self, enrichment_service_full):
        """Test error handling for unknown actions."""
        # Arrange
        action_data = {
            'action': 'unknown_action',
            'data': {}
        }

        # Act & Assert
        with pytest.raises(TransformerServiceError, match="Unknown action: unknown_action"):
            await enrichment_service_full._execute_action(action_data)

    @pytest.mark.asyncio
    async def test_execute_action_invalid_data_format(self, enrichment_service_full):
        """Test error handling for invalid data format."""
        # Act & Assert
        with pytest.raises(TransformerServiceError, match="Invalid data format"):
            await enrichment_service_full._execute_action("invalid_data")


# ==================== Health Check Tests ====================

class TestHealthCheck:
    """Test health monitoring functionality."""

    @pytest.mark.asyncio
    async def test_health_check_all_components_available(self, enrichment_service_full):
        """Test health check with all components available."""
        # Act
        health_status = await enrichment_service_full.health_check()

        # Assert
        assert health_status['service'] == 'EnrichmentService'
        assert health_status['status'] == 'healthy'
        assert health_status['components']['mdl_processor'] == 'available'
        assert health_status['components']['law_firm_integration'] == 'available'
        assert health_status['components']['llm_client'] == 'available'

    @pytest.mark.asyncio
    async def test_health_check_missing_components(self, enrichment_service_minimal):
        """Test health check with missing components."""
        # Act
        health_status = await enrichment_service_minimal.health_check()

        # Assert
        assert health_status['service'] == 'EnrichmentService'
        assert health_status['status'] == 'degraded'
        assert health_status['components']['mdl_processor'] == 'unavailable'
        assert health_status['components']['law_firm_integration'] == 'unavailable'
        assert 'missing_critical' in health_status
        assert 'mdl_processor' in health_status['missing_critical']
        assert 'law_firm_integration' in health_status['missing_critical']

    @pytest.mark.asyncio
    async def test_health_check_partial_components(self, mock_logger, mock_config, mock_mdl_processor):
        """Test health check with partial components available."""
        # Arrange
        service = EnrichmentService(
            mdl_processor=mock_mdl_processor,
            # law_firm_integration missing
            config=mock_config,
            logger=mock_logger
        )

        # Act
        health_status = await service.health_check()

        # Assert
        assert health_status['status'] == 'degraded'
        assert health_status['components']['mdl_processor'] == 'available'
        assert health_status['components']['law_firm_integration'] == 'unavailable'
        assert 'law_firm_integration' in health_status['missing_critical']


# ==================== Cleanup Tests ====================

class TestCleanup:
    """Test resource cleanup functionality."""

    @pytest.mark.asyncio
    async def test_cleanup_with_components_having_cleanup(self, enrichment_service_full):
        """Test cleanup when components have cleanup methods."""
        # Arrange - add cleanup methods to mock components
        for component in [
            enrichment_service_full._mdl_processor,
            enrichment_service_full._law_firm_integration,
        ]:
            component.cleanup = AsyncMock()

        # Act
        await enrichment_service_full.cleanup()

        # Assert
        enrichment_service_full._mdl_processor.cleanup.assert_called_once()
        enrichment_service_full._law_firm_integration.cleanup.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_component_error_handling(self, enrichment_service_full):
        """Test cleanup error handling for component cleanup failures."""
        # Arrange
        enrichment_service_full._mdl_processor.cleanup = AsyncMock(side_effect=Exception("Cleanup error"))

        # Act (should not raise exception)
        await enrichment_service_full.cleanup()

        # Assert - error should be logged but not raised

    @pytest.mark.asyncio
    async def test_cleanup_minimal_service(self, enrichment_service_minimal):
        """Test cleanup with minimal service (no components)."""
        # Act (should not raise exception)
        await enrichment_service_minimal.cleanup()

        # Assert - should complete without error


# ==================== Edge Cases and Error Scenarios ====================

class TestEdgeCases:
    """Test edge cases and error scenarios."""

    @pytest.mark.asyncio
    async def test_enrich_document_empty_data(self, enrichment_service_full):
        """Test document enrichment with empty data."""
        # Act
        result = await enrichment_service_full.enrich_document({})

        # Assert
        assert result['enrichment_completed'] is True

    @pytest.mark.asyncio
    async def test_add_mdl_info_with_none_mdl_processor_result(self, enrichment_service_full):
        """Test MDL info addition when processor returns None."""
        # Arrange
        enrichment_service_full._mdl_processor.add_mdl_info_to_docket.return_value = None
        test_data = {'docket_num': '1:23-cv-12345'}

        # Act
        result = await enrichment_service_full.add_mdl_info_to_docket(test_data)

        # Assert
        assert 'mdl_info' not in result
        assert 'has_mdl' not in result

    @pytest.mark.asyncio
    async def test_process_law_firms_with_empty_attorney_lists(self, enrichment_service_full):
        """Test law firm processing with empty attorney lists."""
        # Arrange
        test_data = {
            'docket_num': '1:23-cv-12345',
            'plaintiff_attorneys': [],
            'defendant_attorneys': []
        }

        # Act
        result = await enrichment_service_full.enrich_document(test_data)

        # Assert
        # Should not process law firms due to empty lists
        enrichment_service_full._law_firm_integration.process_law_firms.assert_not_called()

    def test_should_process_mdl_case_insensitive(self, enrichment_service_full):
        """Test MDL processing decision is case insensitive."""
        data = {'docket_num': '1:23-MDL-12345'}
        assert enrichment_service_full._should_process_mdl(data) is True

    @pytest.mark.asyncio
    async def test_component_integration_error_propagation(self, enrichment_service_full):
        """Test that component errors are properly propagated."""
        # Arrange
        enrichment_service_full._mdl_processor.add_mdl_info_to_docket.side_effect = TransformerServiceError("Component error")
        test_data = {'mdl_num': '2929'}

        # Act & Assert
        with pytest.raises(TransformerServiceError, match="Document enrichment failed"):
            await enrichment_service_full.enrich_document(test_data)