"""
Comprehensive unit tests for PersistenceService.

Tests the consolidation of:
- FileProcessingService functionality (file I/O, caching, PDF handling)
- DataUploadService functionality (S3 uploads, DynamoDB uploads)

Key testing areas:
- Document save/load operations in various formats
- Storage and database upload operations
- JSON file operations with caching
- Batch processing and uploads
- Error handling and fallback scenarios
- Component availability and degradation handling
"""
import pytest
import asyncio
import json
import logging
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Optional
from unittest.mock import Mock, AsyncMock, patch, MagicMock, mock_open

from src.transformer.facades.persistence_service import PersistenceService
from src.infrastructure.protocols.exceptions import TransformerServiceError

# Mock components for dependency injection
from src.transformer.components.file.file_handler_core import FileHandlerCore
from src.transformer.components.file.file_handler_utils import FileHandlerUtils
from src.transformer.components.file.cached_pdf_handler import CachedPdfHandler
from src.transformer.components.file.docket_file_manager import DocketFileManager
from src.transformer.components.file.file_operations_manager import FileOperationsManager
from src.transformer.components.data_upload.uploader import Uploader


@pytest.fixture
def mock_logger():
    """Mock logger for testing."""
    return Mock(spec=logging.Logger)


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    return {
        'test_mode': True,
        'upload_json_to_s3': True,
        'upload_json_to_dynamodb': True,
        's3_bucket': 'test-bucket',
        'dynamodb_table': 'test-table',
        'backup_dir': '/tmp/backups',
        'enable_caching': True
    }


@pytest.fixture
def mock_file_handler_core():
    """Mock file handler core component."""
    mock = Mock(spec=FileHandlerCore)
    mock.save_json_async = AsyncMock(return_value=True)
    mock.load_json_async = AsyncMock(return_value={'test': 'data'})
    return mock


@pytest.fixture
def mock_file_handler_utils():
    """Mock file handler utils component."""
    mock = Mock(spec=FileHandlerUtils)
    mock.ensure_directory = Mock(return_value=True)
    mock.validate_path = Mock(return_value=True)
    return mock


@pytest.fixture
def mock_cached_pdf_handler():
    """Mock cached PDF handler component."""
    mock = Mock(spec=CachedPdfHandler)
    mock.save_pdf = AsyncMock(return_value=True)
    mock.load_pdf = AsyncMock(return_value='PDF text content')
    mock.extract_text = AsyncMock(return_value='Extracted PDF text')
    return mock


@pytest.fixture
def mock_docket_file_manager():
    """Mock docket file manager component."""
    mock = Mock(spec=DocketFileManager)
    mock.finalize_files = AsyncMock(return_value=True)
    mock.manage_file_lifecycle = AsyncMock(return_value=True)
    return mock


@pytest.fixture
def mock_file_operations_manager():
    """Mock file operations manager component."""
    mock = Mock(spec=FileOperationsManager)
    mock.process_file = AsyncMock(return_value={'file': 'processed', 'status': 'success'})
    mock.batch_process = AsyncMock(return_value=[{'file1': 'processed'}, {'file2': 'processed'}])
    return mock


@pytest.fixture
def mock_uploader():
    """Mock uploader component."""
    mock = Mock(spec=Uploader)
    mock.upload_batch_to_aws_async = AsyncMock(return_value={
        's3': ['file1.json', 'file2.json'],
        'dynamodb': ['file1.json']
    })
    return mock


@pytest.fixture
def mock_s3_manager():
    """Mock S3 manager client."""
    mock = Mock()
    mock.upload_json = AsyncMock(return_value=True)
    mock.upload_file = AsyncMock(return_value=True)
    mock.download_file = AsyncMock(return_value='downloaded content')
    return mock


@pytest.fixture
def mock_dynamodb_client():
    """Mock DynamoDB client."""
    mock = Mock()
    mock.put_item = AsyncMock(return_value=True)
    mock.get_item = AsyncMock(return_value={'Item': {'test': 'data'}})
    mock.batch_write_item = AsyncMock(return_value=True)
    return mock


@pytest.fixture
def service_with_all_components(
    mock_logger, mock_config, mock_file_handler_core, mock_file_handler_utils,
    mock_cached_pdf_handler, mock_docket_file_manager, mock_file_operations_manager,
    mock_uploader, mock_s3_manager, mock_dynamodb_client
):
    """Create PersistenceService with all mock components."""
    return PersistenceService(
        file_handler_core=mock_file_handler_core,
        file_handler_utils=mock_file_handler_utils,
        cached_pdf_handler=mock_cached_pdf_handler,
        docket_file_manager=mock_docket_file_manager,
        file_operations_manager=mock_file_operations_manager,
        uploader=mock_uploader,
        s3_manager=mock_s3_manager,
        dynamodb_client=mock_dynamodb_client,
        config=mock_config,
        logger=mock_logger
    )


@pytest.fixture
def service_minimal(mock_logger, mock_config):
    """Create PersistenceService with minimal components (fallback mode)."""
    return PersistenceService(
        config=mock_config,
        logger=mock_logger
    )


@pytest.fixture
def sample_document_data():
    """Sample document data for testing."""
    return {
        'docket_num': '1:23-cv-12345',
        'case_name': 'Test Case v. Defendant',
        'court_id': 'cand',
        'filing_date': '2023-01-01',
        'all_text': 'Sample case text content',
        'pdf_text': 'PDF extracted text',
        'timestamp': '2023-01-01T12:00:00Z'
    }


@pytest.fixture
def sample_json_paths():
    """Sample JSON file paths for batch operations."""
    return [
        '/tmp/docket_1_23_cv_12345.json',
        '/tmp/docket_1_23_cv_12346.json',
        '/tmp/docket_1_23_cv_12347.json'
    ]


class TestPersistenceServiceInitialization:
    """Test service initialization and component injection."""

    def test_initialization_with_all_components(self, service_with_all_components):
        """Test service initializes with all components properly injected."""
        service = service_with_all_components
        
        # Verify file components
        assert service._file_handler_core is not None
        assert service._file_handler_utils is not None
        assert service._cached_pdf_handler is not None
        assert service._docket_file_manager is not None
        assert service._file_operations_manager is not None
        
        # Verify upload components
        assert service._uploader is not None
        
        # Verify external services
        assert service._s3_manager is not None
        assert service._dynamodb_client is not None
        
        # Verify cache initialization
        assert service._file_cache == {}

    def test_initialization_with_minimal_components(self, service_minimal):
        """Test service initializes with minimal components (all None)."""
        service = service_minimal
        
        # All components should be None for fallback behavior
        assert service._file_handler_core is None
        assert service._file_handler_utils is None
        assert service._cached_pdf_handler is None
        assert service._docket_file_manager is None
        assert service._file_operations_manager is None
        assert service._uploader is None
        assert service._s3_manager is None
        assert service._dynamodb_client is None

    def test_initialization_logging(self, mock_logger, mock_config):
        """Test initialization logging is called."""
        PersistenceService(logger=mock_logger, config=mock_config)
        mock_logger.info.assert_called_with("PersistenceService initialized with all components")


class TestActionRouting:
    """Test _execute_action method routing."""

    @pytest.mark.asyncio
    async def test_execute_action_invalid_data_format(self, service_with_all_components):
        """Test _execute_action raises error for invalid data format."""
        with pytest.raises(TransformerServiceError) as exc_info:
            await service_with_all_components._execute_action("invalid_data")
        
        assert "Invalid data format for PersistenceService" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_execute_action_unknown_action(self, service_with_all_components):
        """Test _execute_action raises error for unknown action."""
        data = {'action': 'unknown_action', 'data': {}}
        
        with pytest.raises(TransformerServiceError) as exc_info:
            await service_with_all_components._execute_action(data)
        
        assert "Unknown action: unknown_action" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_execute_action_save_document(self, service_with_all_components, sample_document_data):
        """Test _execute_action routes to save_document correctly."""
        action_data = {
            'data': sample_document_data,
            'path': '/tmp/test.json',
            'format': 'json'
        }
        data = {'action': 'save_document', 'data': action_data}
        
        with patch.object(service_with_all_components, 'save_document', return_value=True) as mock_save:
            result = await service_with_all_components._execute_action(data)
            
            mock_save.assert_called_once_with(action_data)
            assert result is True

    @pytest.mark.asyncio
    async def test_execute_action_load_document(self, service_with_all_components):
        """Test _execute_action routes to load_document correctly."""
        action_data = {'path': '/tmp/test.json', 'format': 'json'}
        data = {'action': 'load_document', 'data': action_data}
        
        with patch.object(service_with_all_components, 'load_document', return_value={'loaded': 'data'}) as mock_load:
            result = await service_with_all_components._execute_action(data)
            
            mock_load.assert_called_once_with(action_data)
            assert result == {'loaded': 'data'}


class TestSaveDocumentOperations:
    """Test save_document main entry point."""

    @pytest.mark.asyncio
    async def test_save_document_json_format(self, service_with_all_components, sample_document_data):
        """Test saving document in JSON format."""
        result = await service_with_all_components.save_document(
            sample_document_data, 
            '/tmp/test.json', 
            'json'
        )
        
        service_with_all_components._file_handler_core.save_json_async.assert_called_once_with(
            '/tmp/test.json', 
            sample_document_data
        )
        assert result is True

    @pytest.mark.asyncio
    async def test_save_document_pdf_format(self, service_with_all_components, sample_document_data):
        """Test saving document in PDF format."""
        with patch.object(service_with_all_components, '_save_pdf', return_value=True) as mock_save_pdf:
            result = await service_with_all_components.save_document(
                sample_document_data, 
                '/tmp/test.pdf', 
                'pdf'
            )
            
            mock_save_pdf.assert_called_once_with('/tmp/test.pdf', sample_document_data)
            assert result is True

    @pytest.mark.asyncio
    async def test_save_document_text_format(self, service_with_all_components, sample_document_data):
        """Test saving document in text format."""
        with patch.object(service_with_all_components, '_save_text', return_value=True) as mock_save_text:
            result = await service_with_all_components.save_document(
                sample_document_data, 
                '/tmp/test.txt', 
                'text'
            )
            
            mock_save_text.assert_called_once_with('/tmp/test.txt', sample_document_data)
            assert result is True

    @pytest.mark.asyncio
    async def test_save_document_unsupported_format(self, service_with_all_components, sample_document_data):
        """Test saving document with unsupported format."""
        result = await service_with_all_components.save_document(
            sample_document_data, 
            '/tmp/test.xyz', 
            'unsupported'
        )
        
        assert result is False

    @pytest.mark.asyncio
    async def test_save_document_error_handling(self, service_with_all_components, sample_document_data):
        """Test save_document error handling."""
        service_with_all_components._file_handler_core.save_json_async.side_effect = Exception("Save failed")
        
        result = await service_with_all_components.save_document(
            sample_document_data, 
            '/tmp/test.json', 
            'json'
        )
        
        assert result is False


class TestLoadDocumentOperations:
    """Test load_document main entry point."""

    @pytest.mark.asyncio
    async def test_load_document_json_format(self, service_with_all_components):
        """Test loading document in JSON format."""
        with patch.object(service_with_all_components, 'load_json_async', return_value={'loaded': 'data'}) as mock_load:
            result = await service_with_all_components.load_document('/tmp/test.json', 'json')
            
            mock_load.assert_called_once_with('/tmp/test.json')
            assert result == {'loaded': 'data'}

    @pytest.mark.asyncio
    async def test_load_document_pdf_format(self, service_with_all_components):
        """Test loading document in PDF format."""
        with patch.object(service_with_all_components, '_load_pdf', return_value={'pdf_text': 'content'}) as mock_load_pdf:
            result = await service_with_all_components.load_document('/tmp/test.pdf', 'pdf')
            
            mock_load_pdf.assert_called_once_with('/tmp/test.pdf')
            assert result == {'pdf_text': 'content'}

    @pytest.mark.asyncio
    async def test_load_document_text_format(self, service_with_all_components):
        """Test loading document in text format."""
        with patch.object(service_with_all_components, '_load_text', return_value={'text': 'content'}) as mock_load_text:
            result = await service_with_all_components.load_document('/tmp/test.txt', 'text')
            
            mock_load_text.assert_called_once_with('/tmp/test.txt')
            assert result == {'text': 'content'}

    @pytest.mark.asyncio
    async def test_load_document_unsupported_format(self, service_with_all_components):
        """Test loading document with unsupported format."""
        result = await service_with_all_components.load_document('/tmp/test.xyz', 'unsupported')
        
        assert result is None

    @pytest.mark.asyncio
    async def test_load_document_error_handling(self, service_with_all_components):
        """Test load_document error handling."""
        service_with_all_components._file_handler_core.load_json_async.side_effect = Exception("Load failed")
        
        result = await service_with_all_components.load_document('/tmp/test.json', 'json')
        
        assert result is None


class TestJSONFileOperations:
    """Test JSON file operations with caching."""

    @pytest.mark.asyncio
    async def test_save_json_async_with_component(self, service_with_all_components, sample_document_data):
        """Test saving JSON with file handler component."""
        result = await service_with_all_components.save_json_async('/tmp/test.json', sample_document_data)
        
        service_with_all_components._file_handler_core.save_json_async.assert_called_once_with(
            '/tmp/test.json', 
            sample_document_data
        )
        assert result is True

    @pytest.mark.asyncio
    async def test_save_json_async_fallback_mode(self, service_minimal, sample_document_data):
        """Test saving JSON in fallback mode (no components)."""
        with patch('pathlib.Path') as mock_path, \
             patch('builtins.open', mock_open()) as mock_file, \
             patch('json.dump') as mock_json_dump:
            
            mock_path.return_value.parent.mkdir.return_value = None
            
            result = await service_minimal.save_json_async('/tmp/test.json', sample_document_data)
            
            mock_file.assert_called_once_with('/tmp/test.json', 'w', encoding='utf-8')
            mock_json_dump.assert_called_once()
            assert result is True

    @pytest.mark.asyncio
    async def test_save_json_async_fallback_error(self, service_minimal, sample_document_data):
        """Test saving JSON fallback error handling."""
        with patch('builtins.open', side_effect=IOError("Write failed")):
            result = await service_minimal.save_json_async('/tmp/test.json', sample_document_data)
            assert result is False

    @pytest.mark.asyncio
    async def test_load_json_async_with_component(self, service_with_all_components):
        """Test loading JSON with file handler component."""
        expected_data = {'test': 'data'}
        service_with_all_components._file_handler_core.load_json_async.return_value = expected_data
        
        result = await service_with_all_components.load_json_async('/tmp/test.json')
        
        service_with_all_components._file_handler_core.load_json_async.assert_called_once_with('/tmp/test.json')
        assert result == expected_data
        # Verify data is cached
        assert service_with_all_components._file_cache['/tmp/test.json'] == expected_data

    @pytest.mark.asyncio
    async def test_load_json_async_from_cache(self, service_with_all_components):
        """Test loading JSON from cache."""
        cached_data = {'cached': 'data'}
        service_with_all_components._file_cache['/tmp/test.json'] = cached_data
        
        result = await service_with_all_components.load_json_async('/tmp/test.json')
        
        # Component should not be called due to cache hit
        service_with_all_components._file_handler_core.load_json_async.assert_not_called()
        assert result == cached_data

    @pytest.mark.asyncio
    async def test_load_json_async_fallback_mode(self, service_minimal):
        """Test loading JSON in fallback mode."""
        test_data = {'fallback': 'data'}
        
        with patch('builtins.open', mock_open(read_data=json.dumps(test_data))), \
             patch('json.load', return_value=test_data):
            
            result = await service_minimal.load_json_async('/tmp/test.json')
            
            assert result == test_data
            # Verify data is cached
            assert service_minimal._file_cache['/tmp/test.json'] == test_data

    @pytest.mark.asyncio
    async def test_load_json_async_fallback_error(self, service_minimal):
        """Test loading JSON fallback error handling."""
        with patch('builtins.open', side_effect=FileNotFoundError("File not found")):
            result = await service_minimal.load_json_async('/tmp/test.json')
            assert result is None


class TestStorageOperations:
    """Test cloud storage operations."""

    @pytest.mark.asyncio
    async def test_upload_to_storage_s3(self, service_with_all_components, sample_document_data):
        """Test uploading to S3 storage."""
        with patch.object(service_with_all_components, '_upload_to_s3', return_value=True) as mock_s3:
            result = await service_with_all_components.upload_to_storage(sample_document_data, 's3')
            
            mock_s3.assert_called_once_with(sample_document_data)
            assert result is True

    @pytest.mark.asyncio
    async def test_upload_to_storage_unsupported(self, service_with_all_components, sample_document_data):
        """Test uploading to unsupported storage type."""
        result = await service_with_all_components.upload_to_storage(sample_document_data, 'azure')
        
        assert result is False

    @pytest.mark.asyncio
    async def test_upload_to_storage_error(self, service_with_all_components, sample_document_data):
        """Test storage upload error handling."""
        with patch.object(service_with_all_components, '_upload_to_s3', side_effect=Exception("Upload failed")):
            result = await service_with_all_components.upload_to_storage(sample_document_data, 's3')
            assert result is False

    @pytest.mark.asyncio
    async def test_upload_to_s3_success(self, service_with_all_components, sample_document_data):
        """Test successful S3 upload."""
        result = await service_with_all_components._upload_to_s3(sample_document_data)
        
        service_with_all_components._s3_manager.upload_json.assert_called_once()
        call_args = service_with_all_components._s3_manager.upload_json.call_args
        assert call_args[0][0] == 'test-bucket'  # bucket
        assert call_args[0][1] == 'dockets/1:23-cv-12345.json'  # key
        assert '"docket_num": "1:23-cv-12345"' in call_args[0][2]  # JSON content
        assert result is True

    @pytest.mark.asyncio
    async def test_upload_to_s3_no_manager(self, service_minimal, sample_document_data):
        """Test S3 upload without manager (fallback)."""
        result = await service_minimal._upload_to_s3(sample_document_data)
        assert result is False

    @pytest.mark.asyncio
    async def test_upload_to_s3_error_handling(self, service_with_all_components, sample_document_data):
        """Test S3 upload error handling."""
        service_with_all_components._s3_manager.upload_json.side_effect = Exception("S3 error")
        
        result = await service_with_all_components._upload_to_s3(sample_document_data)
        assert result is False


class TestDatabaseOperations:
    """Test database operations."""

    @pytest.mark.asyncio
    async def test_upload_to_database_dynamodb(self, service_with_all_components, sample_document_data):
        """Test uploading to DynamoDB database."""
        with patch.object(service_with_all_components, '_upload_to_dynamodb', return_value=True) as mock_db:
            result = await service_with_all_components.upload_to_database(sample_document_data, 'dynamodb')
            
            mock_db.assert_called_once_with(sample_document_data)
            assert result is True

    @pytest.mark.asyncio
    async def test_upload_to_database_unsupported(self, service_with_all_components, sample_document_data):
        """Test uploading to unsupported database type."""
        result = await service_with_all_components.upload_to_database(sample_document_data, 'mongodb')
        
        assert result is False

    @pytest.mark.asyncio
    async def test_upload_to_database_error(self, service_with_all_components, sample_document_data):
        """Test database upload error handling."""
        with patch.object(service_with_all_components, '_upload_to_dynamodb', side_effect=Exception("DB failed")):
            result = await service_with_all_components.upload_to_database(sample_document_data, 'dynamodb')
            assert result is False

    @pytest.mark.asyncio
    async def test_upload_to_dynamodb_success(self, service_with_all_components, sample_document_data):
        """Test successful DynamoDB upload."""
        result = await service_with_all_components._upload_to_dynamodb(sample_document_data)
        
        service_with_all_components._dynamodb_client.put_item.assert_called_once()
        call_args = service_with_all_components._dynamodb_client.put_item.call_args
        assert call_args[1]['TableName'] == 'test-table'
        
        # Verify item structure
        item = call_args[1]['Item']
        assert item['docket_num']['S'] == '1:23-cv-12345'
        assert item['court']['S'] == ''
        assert '"docket_num": "1:23-cv-12345"' in item['data']['S']
        assert result is True

    @pytest.mark.asyncio
    async def test_upload_to_dynamodb_no_client(self, service_minimal, sample_document_data):
        """Test DynamoDB upload without client (fallback)."""
        result = await service_minimal._upload_to_dynamodb(sample_document_data)
        assert result is False

    @pytest.mark.asyncio
    async def test_upload_to_dynamodb_error_handling(self, service_with_all_components, sample_document_data):
        """Test DynamoDB upload error handling."""
        service_with_all_components._dynamodb_client.put_item.side_effect = Exception("DynamoDB error")
        
        result = await service_with_all_components._upload_to_dynamodb(sample_document_data)
        assert result is False

    def test_prepare_dynamodb_item(self, service_with_all_components, sample_document_data):
        """Test DynamoDB item preparation."""
        item = service_with_all_components._prepare_dynamodb_item(sample_document_data)
        
        expected_item = {
            'docket_num': {'S': '1:23-cv-12345'},
            'court': {'S': 'cand'},
            'case_name': {'S': 'Test Case v. Defendant'},
            'filing_date': {'S': '2023-01-01'},
            'data': {'S': json.dumps(sample_document_data)},
        }
        
        assert item == expected_item


class TestFileProcessingOperations:
    """Test file processing operations from FileProcessingService."""

    @pytest.mark.asyncio
    async def test_process_files_success(self, service_with_all_components, sample_json_paths):
        """Test processing multiple files successfully."""
        result = await service_with_all_components.process_files(sample_json_paths)
        
        # Should call process_file for each path
        assert service_with_all_components._file_operations_manager.process_file.call_count == len(sample_json_paths)
        
        # Should return processed results
        assert len(result) == len(sample_json_paths)
        for item in result:
            assert item == {'file': 'processed', 'status': 'success'}

    @pytest.mark.asyncio
    async def test_process_files_no_manager(self, service_minimal, sample_json_paths):
        """Test processing files without manager (fallback)."""
        result = await service_minimal.process_files(sample_json_paths)
        assert result == []

    @pytest.mark.asyncio
    async def test_process_files_partial_failure(self, service_with_all_components, sample_json_paths):
        """Test processing files with some failures."""
        # Make second file fail
        side_effects = [
            {'file': 'processed', 'status': 'success'},
            Exception("Processing failed"),
            {'file': 'processed', 'status': 'success'}
        ]
        service_with_all_components._file_operations_manager.process_file.side_effect = side_effects
        
        result = await service_with_all_components.process_files(sample_json_paths)
        
        # Should return only successful results
        assert len(result) == 2

    @pytest.mark.asyncio
    async def test_finalize_files_success(self, service_with_all_components, sample_json_paths):
        """Test finalizing files successfully."""
        result = await service_with_all_components.finalize_files(sample_json_paths)
        
        service_with_all_components._docket_file_manager.finalize_files.assert_called_once_with(sample_json_paths)
        assert result is True

    @pytest.mark.asyncio
    async def test_finalize_files_no_manager(self, service_minimal, sample_json_paths):
        """Test finalizing files without manager (fallback)."""
        result = await service_minimal.finalize_files(sample_json_paths)
        assert result is True  # Returns True as default fallback


class TestBatchUploadOperations:
    """Test batch upload operations from DataUploadService."""

    @pytest.mark.asyncio
    async def test_upload_batch_success(self, service_with_all_components, sample_json_paths):
        """Test successful batch upload."""
        result = await service_with_all_components.upload_batch(
            sample_json_paths,
            ['s3', 'dynamodb'],
            force_upload=True
        )
        
        service_with_all_components._uploader.upload_batch_to_aws_async.assert_called_once_with(
            sample_json_paths,
            {'s3', 'dynamodb'},
            True
        )
        
        assert 's3' in result
        assert 'dynamodb' in result
        assert len(result['s3']) == 2
        assert len(result['dynamodb']) == 1

    @pytest.mark.asyncio
    async def test_upload_batch_from_config(self, service_with_all_components, sample_json_paths):
        """Test batch upload using config settings."""
        result = await service_with_all_components.upload_batch(sample_json_paths)
        
        # Should use config settings for upload types
        service_with_all_components._uploader.upload_batch_to_aws_async.assert_called_once()
        call_args = service_with_all_components._uploader.upload_batch_to_aws_async.call_args
        upload_types = call_args[0][1]
        assert 's3' in upload_types
        assert 'dynamodb' in upload_types

    @pytest.mark.asyncio
    async def test_upload_batch_no_uploader(self, service_minimal, sample_json_paths):
        """Test batch upload without uploader (fallback)."""
        result = await service_minimal.upload_batch(sample_json_paths)
        assert result == {}

    @pytest.mark.asyncio
    async def test_upload_batch_no_upload_types(self, mock_logger):
        """Test batch upload with config that has no upload types enabled."""
        config = {'upload_json_to_s3': False, 'upload_json_to_dynamodb': False}
        service = PersistenceService(logger=mock_logger, config=config)
        
        result = await service.upload_batch(['/tmp/test.json'])
        assert result == {}


class TestBackupOperations:
    """Test data backup functionality."""

    @pytest.mark.asyncio
    async def test_backup_data_with_path(self, service_with_all_components, sample_document_data):
        """Test backup with specified path."""
        backup_path = '/tmp/backup/test_backup.json'
        
        with patch.object(service_with_all_components, 'save_json_async', return_value=True) as mock_save:
            result = await service_with_all_components.backup_data(sample_document_data, backup_path)
            
            mock_save.assert_called_once_with(backup_path, sample_document_data)
            assert result is True

    @pytest.mark.asyncio
    async def test_backup_data_auto_path(self, service_with_all_components, sample_document_data):
        """Test backup with automatically generated path."""
        with patch.object(service_with_all_components, 'save_json_async', return_value=True) as mock_save, \
             patch('pathlib.Path') as mock_path:
            
            mock_path.return_value.parent.mkdir.return_value = None
            
            result = await service_with_all_components.backup_data(sample_document_data)
            
            mock_save.assert_called_once()
            call_args = mock_save.call_args[0][0]
            assert '/tmp/backups/1:23-cv-12345_2023-01-01T12:00:00Z.json' in call_args
            assert result is True

    @pytest.mark.asyncio
    async def test_backup_data_error_handling(self, service_with_all_components, sample_document_data):
        """Test backup error handling."""
        with patch.object(service_with_all_components, 'save_json_async', side_effect=Exception("Backup failed")):
            result = await service_with_all_components.backup_data(sample_document_data)
            assert result is False


class TestPDFOperations:
    """Test PDF format operations."""

    @pytest.mark.asyncio
    async def test_save_pdf_success(self, service_with_all_components):
        """Test saving PDF successfully."""
        pdf_data = {'pdf_content': 'PDF binary content', 'docket_num': '1:23-cv-12345'}
        
        result = await service_with_all_components._save_pdf('/tmp/test.pdf', pdf_data)
        
        service_with_all_components._cached_pdf_handler.save_pdf.assert_called_once_with(
            '/tmp/test.pdf',
            'PDF binary content'
        )
        assert result is True

    @pytest.mark.asyncio
    async def test_save_pdf_with_pdf_text(self, service_with_all_components):
        """Test saving PDF using pdf_text field."""
        pdf_data = {'pdf_text': 'PDF text content', 'docket_num': '1:23-cv-12345'}
        
        result = await service_with_all_components._save_pdf('/tmp/test.pdf', pdf_data)
        
        service_with_all_components._cached_pdf_handler.save_pdf.assert_called_once_with(
            '/tmp/test.pdf',
            'PDF text content'
        )
        assert result is True

    @pytest.mark.asyncio
    async def test_save_pdf_no_content(self, service_with_all_components):
        """Test saving PDF with no content."""
        pdf_data = {'docket_num': '1:23-cv-12345'}
        
        result = await service_with_all_components._save_pdf('/tmp/test.pdf', pdf_data)
        
        service_with_all_components._cached_pdf_handler.save_pdf.assert_not_called()
        assert result is False

    @pytest.mark.asyncio
    async def test_save_pdf_no_handler(self, service_minimal):
        """Test saving PDF without handler (fallback)."""
        pdf_data = {'pdf_content': 'content'}
        
        result = await service_minimal._save_pdf('/tmp/test.pdf', pdf_data)
        assert result is False

    @pytest.mark.asyncio
    async def test_load_pdf_success(self, service_with_all_components):
        """Test loading PDF successfully."""
        result = await service_with_all_components._load_pdf('/tmp/test.pdf')
        
        service_with_all_components._cached_pdf_handler.load_pdf.assert_called_once_with('/tmp/test.pdf')
        assert result == {'pdf_text': 'PDF text content', 'pdf_path': '/tmp/test.pdf'}

    @pytest.mark.asyncio
    async def test_load_pdf_no_content(self, service_with_all_components):
        """Test loading PDF with no content returned."""
        service_with_all_components._cached_pdf_handler.load_pdf.return_value = None
        
        result = await service_with_all_components._load_pdf('/tmp/test.pdf')
        assert result is None

    @pytest.mark.asyncio
    async def test_load_pdf_no_handler(self, service_minimal):
        """Test loading PDF without handler (fallback)."""
        result = await service_minimal._load_pdf('/tmp/test.pdf')
        assert result is None

    @pytest.mark.asyncio
    async def test_load_pdf_error_handling(self, service_with_all_components):
        """Test loading PDF error handling."""
        service_with_all_components._cached_pdf_handler.load_pdf.side_effect = Exception("PDF load failed")
        
        result = await service_with_all_components._load_pdf('/tmp/test.pdf')
        assert result is None


class TestTextOperations:
    """Test text format operations."""

    @pytest.mark.asyncio
    async def test_save_text_with_all_text(self, service_with_all_components):
        """Test saving text using all_text field."""
        text_data = {'all_text': 'Document text content', 'docket_num': '1:23-cv-12345'}
        
        with patch('pathlib.Path') as mock_path, \
             patch('builtins.open', mock_open()) as mock_file:
            
            mock_path.return_value.parent.mkdir.return_value = None
            
            result = await service_with_all_components._save_text('/tmp/test.txt', text_data)
            
            mock_file.assert_called_once_with('/tmp/test.txt', 'w', encoding='utf-8')
            mock_file.return_value.write.assert_called_once_with('Document text content')
            assert result is True

    @pytest.mark.asyncio
    async def test_save_text_with_text_field(self, service_with_all_components):
        """Test saving text using text field."""
        text_data = {'text': 'Simple text content', 'docket_num': '1:23-cv-12345'}
        
        with patch('pathlib.Path') as mock_path, \
             patch('builtins.open', mock_open()) as mock_file:
            
            mock_path.return_value.parent.mkdir.return_value = None
            
            result = await service_with_all_components._save_text('/tmp/test.txt', text_data)
            
            mock_file.return_value.write.assert_called_once_with('Simple text content')
            assert result is True

    @pytest.mark.asyncio
    async def test_save_text_json_fallback(self, service_with_all_components):
        """Test saving text with JSON fallback when no text fields."""
        text_data = {'docket_num': '1:23-cv-12345', 'complex': {'nested': 'data'}}
        
        with patch('pathlib.Path') as mock_path, \
             patch('builtins.open', mock_open()) as mock_file:
            
            mock_path.return_value.parent.mkdir.return_value = None
            
            result = await service_with_all_components._save_text('/tmp/test.txt', text_data)
            
            # Should write JSON representation
            written_content = mock_file.return_value.write.call_args[0][0]
            assert '"docket_num": "1:23-cv-12345"' in written_content
            assert result is True

    @pytest.mark.asyncio
    async def test_save_text_error_handling(self, service_with_all_components):
        """Test saving text error handling."""
        text_data = {'text': 'content'}
        
        with patch('builtins.open', side_effect=IOError("Write failed")):
            result = await service_with_all_components._save_text('/tmp/test.txt', text_data)
            assert result is False

    @pytest.mark.asyncio
    async def test_load_text_success(self, service_with_all_components):
        """Test loading text successfully."""
        text_content = "Sample text file content"
        
        with patch('builtins.open', mock_open(read_data=text_content)):
            result = await service_with_all_components._load_text('/tmp/test.txt')
            
            assert result == {'text': text_content, 'text_path': '/tmp/test.txt'}

    @pytest.mark.asyncio
    async def test_load_text_error_handling(self, service_with_all_components):
        """Test loading text error handling."""
        with patch('builtins.open', side_effect=FileNotFoundError("File not found")):
            result = await service_with_all_components._load_text('/tmp/test.txt')
            assert result is None


class TestCacheManagement:
    """Test file cache management."""

    def test_clear_cache_specific_path(self, service_with_all_components):
        """Test clearing cache for specific path."""
        # Set up cache
        service_with_all_components._file_cache['/tmp/test1.json'] = {'data': '1'}
        service_with_all_components._file_cache['/tmp/test2.json'] = {'data': '2'}
        
        service_with_all_components.clear_cache('/tmp/test1.json')
        
        assert '/tmp/test1.json' not in service_with_all_components._file_cache
        assert '/tmp/test2.json' in service_with_all_components._file_cache

    def test_clear_cache_all(self, service_with_all_components):
        """Test clearing entire cache."""
        # Set up cache
        service_with_all_components._file_cache['/tmp/test1.json'] = {'data': '1'}
        service_with_all_components._file_cache['/tmp/test2.json'] = {'data': '2'}
        
        service_with_all_components.clear_cache()
        
        assert len(service_with_all_components._file_cache) == 0

    def test_clear_cache_nonexistent_path(self, service_with_all_components):
        """Test clearing cache for nonexistent path."""
        service_with_all_components._file_cache['/tmp/test.json'] = {'data': 'test'}
        
        # Should not raise error
        service_with_all_components.clear_cache('/tmp/nonexistent.json')
        
        assert '/tmp/test.json' in service_with_all_components._file_cache


class TestHealthCheckAndStatus:
    """Test health check and service status."""

    @pytest.mark.asyncio
    async def test_health_check_all_components_available(self, service_with_all_components):
        """Test health check with all components available."""
        result = await service_with_all_components.health_check()
        
        assert result['service'] == 'PersistenceService'
        assert result['status'] == 'healthy'
        assert result['cache_size'] == 0
        
        # Check all components are marked as available
        expected_components = [
            'file_handler_core', 'file_handler_utils', 'cached_pdf_handler',
            'docket_file_manager', 'file_operations_manager', 'uploader',
            's3_manager', 'dynamodb_client'
        ]
        
        for component in expected_components:
            assert result['components'][component] == 'available'

    @pytest.mark.asyncio
    async def test_health_check_missing_critical_components(self, service_minimal):
        """Test health check with missing critical components."""
        result = await service_minimal.health_check()
        
        assert result['service'] == 'PersistenceService'
        assert result['status'] == 'degraded'
        assert 'missing_critical' in result
        assert 'file_handler_core' in result['missing_critical']
        assert 'uploader' in result['missing_critical']

    @pytest.mark.asyncio
    async def test_health_check_with_cache(self, service_with_all_components):
        """Test health check with cached files."""
        service_with_all_components._file_cache['/tmp/test1.json'] = {'data': '1'}
        service_with_all_components._file_cache['/tmp/test2.json'] = {'data': '2'}
        
        result = await service_with_all_components.health_check()
        assert result['cache_size'] == 2

    @pytest.mark.asyncio
    async def test_health_check_partial_components(self, mock_logger, mock_config, mock_file_handler_core):
        """Test health check with partial components available."""
        service = PersistenceService(
            file_handler_core=mock_file_handler_core,
            logger=mock_logger,
            config=mock_config
        )
        
        result = await service.health_check()
        
        assert result['status'] == 'degraded'  # Missing uploader (critical)
        assert result['components']['file_handler_core'] == 'available'
        assert result['components']['uploader'] == 'unavailable'


class TestCleanupOperations:
    """Test service cleanup operations."""

    @pytest.mark.asyncio
    async def test_cleanup_all_components(self, service_with_all_components):
        """Test cleanup with all components."""
        # Add cleanup methods to some mock components
        service_with_all_components._file_handler_core.cleanup = AsyncMock()
        service_with_all_components._uploader.cleanup = AsyncMock()
        service_with_all_components._s3_manager.cleanup = AsyncMock()
        
        await service_with_all_components.cleanup()
        
        # Cache should be cleared
        assert len(service_with_all_components._file_cache) == 0
        
        # Components with cleanup method should be called
        service_with_all_components._file_handler_core.cleanup.assert_called_once()
        service_with_all_components._uploader.cleanup.assert_called_once()
        service_with_all_components._s3_manager.cleanup.assert_called_once()

    @pytest.mark.asyncio
    async def test_cleanup_component_error(self, service_with_all_components):
        """Test cleanup handles component cleanup errors gracefully."""
        # Make component cleanup raise an exception
        service_with_all_components._file_handler_core.cleanup = AsyncMock(
            side_effect=Exception("Cleanup failed")
        )
        
        # Should not raise exception
        await service_with_all_components.cleanup()

    @pytest.mark.asyncio
    async def test_cleanup_minimal_service(self, service_minimal):
        """Test cleanup with minimal service (no components)."""
        # Should not raise errors
        await service_minimal.cleanup()

    @pytest.mark.asyncio
    async def test_cleanup_cache_clearing(self, service_with_all_components):
        """Test cleanup clears cache properly."""
        # Set up cache
        service_with_all_components._file_cache['/tmp/test1.json'] = {'data': '1'}
        service_with_all_components._file_cache['/tmp/test2.json'] = {'data': '2'}
        
        await service_with_all_components.cleanup()
        
        assert len(service_with_all_components._file_cache) == 0


class TestErrorScenariosAndEdgeCases:
    """Test error handling and edge cases."""

    @pytest.mark.asyncio
    async def test_concurrent_operations(self, service_with_all_components, sample_document_data):
        """Test concurrent save/load operations."""
        # Perform multiple operations concurrently
        tasks = [
            service_with_all_components.save_document(sample_document_data, f'/tmp/test{i}.json', 'json')
            for i in range(5)
        ]
        
        results = await asyncio.gather(*tasks)
        assert all(result is True for result in results)

    @pytest.mark.asyncio
    async def test_large_data_handling(self, service_with_all_components):
        """Test handling of large data structures."""
        large_data = {
            'docket_num': '1:23-cv-large',
            'large_text': 'A' * 100000,  # 100KB of text
            'large_list': list(range(10000)),
            'nested_data': {'level_' + str(i): {'data': 'B' * 1000} for i in range(100)}
        }
        
        result = await service_with_all_components.save_document(large_data, '/tmp/large.json', 'json')
        assert result is True

    @pytest.mark.asyncio
    async def test_malformed_data_handling(self, service_with_all_components):
        """Test handling of malformed or invalid data."""
        malformed_data = {
            'docket_num': None,
            'invalid_json': {'circular_ref': None},
            'bytes_data': b'binary_content',
            'special_chars': '\x00\x01\x02'
        }
        
        # Should handle gracefully without crashing
        result = await service_with_all_components.save_document(malformed_data, '/tmp/malformed.json', 'json')
        # Result may be False due to JSON serialization issues, but shouldn't crash
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_component_failure_isolation(self, service_with_all_components, sample_document_data):
        """Test that component failures don't affect other operations."""
        # Make S3 manager fail
        service_with_all_components._s3_manager.upload_json.side_effect = Exception("S3 failed")
        
        # File operations should still work
        file_result = await service_with_all_components.save_document(
            sample_document_data, 
            '/tmp/test.json', 
            'json'
        )
        assert file_result is True
        
        # S3 upload should fail gracefully
        s3_result = await service_with_all_components.upload_to_storage(sample_document_data, 's3')
        assert s3_result is False

    def test_service_configuration_validation(self, mock_logger):
        """Test service behavior with various configuration combinations."""
        configs = [
            {},  # Empty config
            {'upload_json_to_s3': True, 'upload_json_to_dynamodb': False},
            {'s3_bucket': None, 'dynamodb_table': ''},
            {'backup_dir': '/invalid/path', 'enable_caching': False}
        ]
        
        for config in configs:
            # Should initialize without errors
            service = PersistenceService(logger=mock_logger, config=config)
            assert service is not None

    @pytest.mark.asyncio
    async def test_memory_usage_under_stress(self, service_with_all_components):
        """Test memory usage during intensive operations."""
        import gc
        
        # Perform many operations to test memory management
        for i in range(50):
            data = {'docket_num': f'1:23-cv-{i:05d}', 'data': 'test' * 1000}
            await service_with_all_components.save_json_async(f'/tmp/test{i}.json', data)
        
        # Force garbage collection
        gc.collect()
        
        # Cache should not grow unbounded (implement cache size limits in actual service)
        assert len(service_with_all_components._file_cache) <= 50

    @pytest.mark.asyncio 
    async def test_logging_during_operations(self, service_with_all_components, sample_document_data, mock_logger):
        """Test that appropriate logging occurs during operations."""
        await service_with_all_components.save_document(sample_document_data, '/tmp/test.json', 'json')
        
        # Verify info logging was called
        mock_logger.info.assert_called()
        
        # Check some expected log messages were made
        call_args_list = [call[0][0] for call in mock_logger.info.call_args_list]
        assert any("Saving document" in msg for msg in call_args_list)

    @pytest.mark.asyncio
    async def test_service_as_context_manager(self, service_with_all_components):
        """Test service usage as async context manager if supported."""
        # This tests future extensibility
        try:
            async with service_with_all_components as service:
                assert service is not None
        except AttributeError:
            # Context manager not implemented yet - this is expected
            pass


class TestPerformanceAndScalability:
    """Test performance characteristics and scalability aspects."""

    @pytest.mark.asyncio
    async def test_batch_operations_efficiency(self, service_with_all_components, sample_json_paths):
        """Test efficiency of batch operations vs individual operations."""
        # Test batch processing
        batch_result = await service_with_all_components.process_files(sample_json_paths)
        
        # Test individual processing
        individual_results = []
        for path in sample_json_paths:
            try:
                result = await service_with_all_components._file_operations_manager.process_file(path)
                individual_results.append(result)
            except:
                pass
        
        # Both should produce similar results
        assert len(batch_result) == len(individual_results)

    @pytest.mark.asyncio
    async def test_caching_performance_benefits(self, service_with_all_components):
        """Test performance benefits of caching."""
        test_path = '/tmp/cache_test.json'
        test_data = {'test': 'cached_data'}
        
        # First load - should hit component
        service_with_all_components._file_handler_core.load_json_async.return_value = test_data
        result1 = await service_with_all_components.load_json_async(test_path)
        
        # Second load - should hit cache
        service_with_all_components._file_handler_core.load_json_async.reset_mock()
        result2 = await service_with_all_components.load_json_async(test_path)
        
        assert result1 == result2
        # Component should not be called second time
        service_with_all_components._file_handler_core.load_json_async.assert_not_called()

    @pytest.mark.asyncio
    async def test_upload_batch_optimization(self, service_with_all_components, sample_json_paths):
        """Test batch upload optimization over individual uploads."""
        # Batch upload should use specialized batch method
        await service_with_all_components.upload_batch(sample_json_paths)
        
        # Should call batch method once, not individual methods multiple times
        service_with_all_components._uploader.upload_batch_to_aws_async.assert_called_once()

    def test_component_lazy_initialization(self, mock_logger, mock_config):
        """Test that service can be initialized without all components and degrade gracefully."""
        # Partial initialization should work
        service = PersistenceService(
            file_handler_core=None,
            uploader=None,
            logger=mock_logger,
            config=mock_config
        )
        
        assert service._file_handler_core is None
        assert service._uploader is None
        # Service should still be functional for fallback operations


# Integration-style tests for end-to-end workflows
class TestIntegrationWorkflows:
    """Test end-to-end workflows combining multiple operations."""

    @pytest.mark.asyncio
    async def test_complete_document_persistence_workflow(self, service_with_all_components, sample_document_data):
        """Test complete document persistence workflow."""
        # 1. Save document
        save_result = await service_with_all_components.save_document(
            sample_document_data, 
            '/tmp/workflow_test.json', 
            'json'
        )
        assert save_result is True
        
        # 2. Load document back
        with patch.object(service_with_all_components, 'load_json_async', return_value=sample_document_data):
            load_result = await service_with_all_components.load_document('/tmp/workflow_test.json', 'json')
            assert load_result == sample_document_data
        
        # 3. Upload to storage
        storage_result = await service_with_all_components.upload_to_storage(sample_document_data, 's3')
        assert storage_result is True
        
        # 4. Upload to database
        db_result = await service_with_all_components.upload_to_database(sample_document_data, 'dynamodb')
        assert db_result is True
        
        # 5. Create backup
        backup_result = await service_with_all_components.backup_data(sample_document_data)
        assert backup_result is True

    @pytest.mark.asyncio
    async def test_batch_processing_workflow(self, service_with_all_components, sample_json_paths):
        """Test batch processing workflow."""
        # 1. Process files
        process_results = await service_with_all_components.process_files(sample_json_paths)
        assert len(process_results) == len(sample_json_paths)
        
        # 2. Batch upload
        upload_results = await service_with_all_components.upload_batch(sample_json_paths)
        assert 's3' in upload_results or 'dynamodb' in upload_results
        
        # 3. Finalize files
        finalize_result = await service_with_all_components.finalize_files(sample_json_paths)
        assert finalize_result is True

    @pytest.mark.asyncio
    async def test_multi_format_document_handling(self, service_with_all_components, sample_document_data):
        """Test handling documents in multiple formats."""
        formats = ['json', 'pdf', 'text']
        
        for fmt in formats:
            # Save in format
            save_result = await service_with_all_components.save_document(
                sample_document_data, 
                f'/tmp/test.{fmt}', 
                fmt
            )
            
            # Load from format
            with patch.object(service_with_all_components, f'_load_{fmt}', return_value=sample_document_data):
                load_result = await service_with_all_components.load_document(f'/tmp/test.{fmt}', fmt)
            
            # Both operations should succeed (or gracefully handle unsupported operations)
            assert isinstance(save_result, bool)
            if load_result is not None:
                assert isinstance(load_result, dict)