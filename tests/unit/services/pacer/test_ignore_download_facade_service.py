import pytest
from unittest.mock import Mock

from src.pacer.ignore_download_facade_service import IgnoreDownloadFacadeService


@pytest.fixture
def ignore_download_facade():
    config = {
        "ignore_downloads": {
            "nysd": ["1:23-cv-12345", "1:23-cv-67890"],
            "cand": ["3:22-cv-00001"]
        }
    }
    return IgnoreDownloadFacadeService(logger=Mock(), config=config)

@pytest.mark.asyncio
async def test_should_ignore_download_true(ignore_download_facade):
    """Test that should_ignore_download returns True for a configured case."""
    case_details = {"court_id": "nysd", "docket_num": "1:23-cv-12345"}

    result = await ignore_download_facade.execute({
        "action": "should_ignore_download",
        "case_details": case_details
    })

    assert result is True

@pytest.mark.asyncio
async def test_should_ignore_download_false(ignore_download_facade):
    """Test that should_ignore_download returns False for a non-configured case."""
    case_details = {"court_id": "nysd", "docket_num": "1:23-cv-99999"}

    result = await ignore_download_facade.execute({
        "action": "should_ignore_download",
        "case_details": case_details
    })

    assert result is False

@pytest.mark.asyncio
async def test_should_ignore_download_false_for_different_court(ignore_download_facade):
    """Test that should_ignore_download returns False for a case in a different court."""
    case_details = {"court_id": "casd", "docket_num": "1:23-cv-12345"}

    result = await ignore_download_facade.execute({
        "action": "should_ignore_download",
        "case_details": case_details
    })

    assert result is False

@pytest.mark.asyncio
async def test_invalid_action(ignore_download_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await ignore_download_facade.execute({"action": "invalid_action"})
