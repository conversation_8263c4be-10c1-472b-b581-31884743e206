import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.case_processing_facade_service import CaseProcessingFacadeService
from src.pacer._case_processing_components.case_validator import CaseValidator
from src.pacer._case_processing_components.case_parser import CaseParser
from src.pacer._case_processing_components.case_enricher import CaseEnricher
from src.pacer._case_processing_components.case_transformer import CaseTransformer


@pytest.fixture
def mock_case_validator():
    return AsyncMock(spec=CaseValidator)

@pytest.fixture
def mock_case_parser():
    return AsyncMock(spec=CaseParser)

@pytest.fixture
def mock_case_enricher():
    return AsyncMock(spec=CaseEnricher)

@pytest.fixture
def mock_case_transformer():
    return AsyncMock(spec=CaseTransformer)

@pytest.fixture
def case_processing_facade(
    mock_case_validator,
    mock_case_parser,
    mock_case_enricher,
    mock_case_transformer
):
    return CaseProcessingFacadeService(
        logger=Mock(),
        config={},
        case_validator=mock_case_validator,
        case_parser=mock_case_parser,
        case_enricher=mock_case_enricher,
        case_transformer=mock_case_transformer,
    )

@pytest.mark.asyncio
async def test_process_case_action(
    case_processing_facade,
    mock_case_validator,
    mock_case_parser,
    mock_case_enricher,
    mock_case_transformer
):
    """Test that the 'process_case' action calls all components in order."""
    page = AsyncMock()
    initial_details = {"docket_num": "1:23-cv-12345"}

    # Mock the return values of each component
    mock_case_parser.execute.return_value = {"parsed": True, **initial_details}
    mock_case_validator.execute.return_value = {"validated": True, "parsed": True, **initial_details}
    mock_case_enricher.execute.return_value = {"enriched": True, "validated": True, "parsed": True, **initial_details}
    mock_case_transformer.execute.return_value = {"transformed": True, "enriched": True, "validated": True, "parsed": True, **initial_details}

    result = await case_processing_facade.execute({
        "action": "process_case",
        "page": page,
        "initial_details": initial_details
    })

    # Assert that each component was called once with the correct data
    mock_case_parser.execute.assert_called_once()
    mock_case_validator.execute.assert_called_once()
    mock_case_enricher.execute.assert_called_once()
    mock_case_transformer.execute.assert_called_once()

    # Assert that the final result contains the 'transformed' key
    assert result["transformed"] is True

@pytest.mark.asyncio
async def test_invalid_action(case_processing_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await case_processing_facade.execute({"action": "invalid_action"})
