import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.navigation_facade_service import NavigationFacadeService
from src.pacer._navigation_components.url_builder import UrlBuilder
from src.pacer._navigation_components.element_locator import ElementLocator
from src.pacer._navigation_components.page_navigator import PageNavigator


@pytest.fixture
def mock_url_builder():
    return Mock(spec=UrlBuilder)

@pytest.fixture
def mock_element_locator():
    return Mock(spec=ElementLocator)

@pytest.fixture
def mock_page_navigator():
    return AsyncMock(spec=PageNavigator)

@pytest.fixture
def navigation_facade(mock_url_builder, mock_element_locator, mock_page_navigator):
    return NavigationFacadeService(
        logger=Mock(),
        config={},
        url_builder=mock_url_builder,
        element_locator=mock_element_locator,
        page_navigator=mock_page_navigator,
    )

@pytest.mark.asyncio
async def test_go_to_query_page_action(navigation_facade, mock_url_builder, mock_page_navigator):
    """Test that the 'go_to_query_page' action calls the correct components."""
    navigator = AsyncMock()
    court_id = "nysd"

    mock_url_builder.execute.return_value = "http://query.page"

    await navigation_facade.execute({
        "action": "go_to_query_page",
        "navigator": navigator,
        "court_id": court_id,
    })

    mock_url_builder.execute.assert_called_once_with({
        "action": "get_query_url",
        "court_id": court_id,
    })
    mock_page_navigator.execute.assert_called_once_with({
        "action": "go_to_url",
        "navigator": navigator,
        "url": "http://query.page",
    })

@pytest.mark.asyncio
async def test_invalid_action(navigation_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await navigation_facade.execute({"action": "invalid_action"})
