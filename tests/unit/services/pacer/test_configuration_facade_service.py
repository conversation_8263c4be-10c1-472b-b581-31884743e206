import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.configuration_facade_service import ConfigurationFacadeService
from src.pacer._config_components.json_config_loader import JsonConfigLoader
from src.pacer._config_components.pacer_config_provider import PacerConfigProvider


@pytest.fixture
def mock_config_loader():
    return AsyncMock(spec=JsonConfigLoader)

@pytest.fixture
def mock_config_provider():
    return Mock(spec=PacerConfigProvider)

@pytest.fixture
def configuration_facade(mock_config_loader, mock_config_provider):
    return ConfigurationFacadeService(
        logger=Mock(),
        config={},
        config_loader=mock_config_loader,
        config_provider=mock_config_provider,
    )

@pytest.mark.asyncio
async def test_load_config_action(configuration_facade, mock_config_loader, mock_config_provider):
    """Test that the 'load_config' action calls the correct components."""
    mock_config_loader.execute.return_value = {"loaded": True}
    mock_config_provider.execute.return_value = {"provided": True}

    result = await configuration_facade.execute({"action": "load_config"})

    mock_config_loader.execute.assert_called_once_with({"action": "load_config"})
    mock_config_provider.execute.assert_called_once_with({"action": "get_config", "loaded_config": {"loaded": True}})

    assert result == {"provided": True}

@pytest.mark.asyncio
async def test_invalid_action(configuration_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await configuration_facade.execute({"action": "invalid_action"})
