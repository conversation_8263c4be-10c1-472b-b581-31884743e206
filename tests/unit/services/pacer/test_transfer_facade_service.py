import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.transfer_facade_service import TransferFacadeService
from src.pacer._transfer_components.transfer_processor import TransferProcessor


@pytest.fixture
def mock_transfer_processor():
    return AsyncMock(spec=TransferProcessor)

@pytest.fixture
def transfer_facade(mock_transfer_processor):
    return TransferFacadeService(
        logger=Mock(),
        config={},
        transfer_processor=mock_transfer_processor,
    )

@pytest.mark.asyncio
async def test_process_transfer_case_action(transfer_facade, mock_transfer_processor):
    """Test that the 'process_transfer_case' action calls the transfer processor."""
    case_details = {"docket_num": "1:23-cv-12345", "transferor_court": "casd"}

    mock_transfer_processor.execute.return_value = {"processed": True, **case_details}

    result = await transfer_facade.execute({
        "action": "process_transfer_case",
        "case_details": case_details,
    })

    mock_transfer_processor.execute.assert_called_once_with({
        "action": "process_transfer",
        "case_details": case_details,
    })

    assert result["processed"] is True

@pytest.mark.asyncio
async def test_invalid_action(transfer_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await transfer_facade.execute({"action": "invalid_action"})
