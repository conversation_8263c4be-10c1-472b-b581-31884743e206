import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.row_processing_facade_service import RowProcessingFacadeService
from src.pacer._processing_components.report_processor import ReportProcessor
from src.pacer._processing_components.single_docket_processor import SingleDocketProcessor


@pytest.fixture
def mock_report_processor():
    return AsyncMock(spec=ReportProcessor)

@pytest.fixture
def mock_single_docket_processor():
    return AsyncMock(spec=SingleDocketProcessor)

@pytest.fixture
def row_processing_facade(mock_report_processor, mock_single_docket_processor):
    return RowProcessingFacadeService(
        logger=Mock(),
        config={},
        report_processor=mock_report_processor,
        single_docket_processor=mock_single_docket_processor,
    )

@pytest.mark.asyncio
async def test_process_report_rows_action(row_processing_facade, mock_report_processor):
    """Test that the 'process_report_rows' action calls the report processor."""
    report_data = {"original_page": AsyncMock()}

    await row_processing_facade.execute({
        "action": "process_report_rows",
        **report_data
    })

    mock_report_processor.execute.assert_called_once_with({
        "action": "process_report",
        **report_data
    })

@pytest.mark.asyncio
async def test_process_single_row_docket_action(row_processing_facade, mock_single_docket_processor):
    """Test that the 'process_single_row_docket' action calls the single docket processor."""
    docket_data = {"case_page": AsyncMock()}

    await row_processing_facade.execute({
        "action": "process_single_row_docket",
        **docket_data
    })

    mock_single_docket_processor.execute.assert_called_once_with({
        "action": "process_docket",
        **docket_data
    })

@pytest.mark.asyncio
async def test_invalid_action(row_processing_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await row_processing_facade.execute({"action": "invalid_action"})
