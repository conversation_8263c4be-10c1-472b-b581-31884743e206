import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.file_management_facade_service import FileManagementFacadeService
from src.pacer._file_components.directory_manager import DirectoryManager
from src.pacer._file_components.file_manager import FileManager
from src.pacer._file_components.path_builder import PathBuilder


@pytest.fixture
def mock_directory_manager():
    return Mock(spec=DirectoryManager)

@pytest.fixture
def mock_file_manager():
    return AsyncMock(spec=FileManager)

@pytest.fixture
def mock_path_builder():
    return Mock(spec=PathBuilder)

@pytest.fixture
def file_management_facade(mock_directory_manager, mock_file_manager, mock_path_builder):
    return FileManagementFacadeService(
        logger=Mock(),
        config={},
        directory_manager=mock_directory_manager,
        file_manager=mock_file_manager,
        path_builder=mock_path_builder,
    )

@pytest.mark.asyncio
async def test_setup_directories_action(file_management_facade, mock_directory_manager):
    """Test that the 'setup_directories' action calls the directory manager."""
    iso_date = "20230101"

    await file_management_facade.execute({
        "action": "setup_directories",
        "iso_date": iso_date,
    })

    mock_directory_manager.execute.assert_called_once_with({
        "action": "setup_directories",
        "iso_date": iso_date,
    })

@pytest.mark.asyncio
async def test_save_case_data_action(file_management_facade, mock_file_manager):
    """Test that the 'save_case_data' action calls the file manager."""
    case_data = {"docket_num": "1:23-cv-12345"}

    await file_management_facade.execute({
        "action": "save_case_data",
        "case_data": case_data,
    })

    mock_file_manager.execute.assert_called_once_with({
        "action": "save_json",
        "data": case_data,
    })

@pytest.mark.asyncio
async def test_invalid_action(file_management_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await file_management_facade.execute({"action": "invalid_action"})
