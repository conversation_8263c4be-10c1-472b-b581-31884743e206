import pytest
from unittest.mock import Mock, AsyncMock, patch

from src.pacer.pacer_orchestrator_service import PacerOrchestratorService
from src.pacer.browser_facade_service import BrowserFacadeService
from src.pacer.court_processing_facade_service import CourtProcessingFacadeService
from src.pacer.configuration_facade_service import ConfigurationFacadeService


@pytest.fixture
def mock_browser_facade():
    return AsyncMock(spec=BrowserFacadeService)

@pytest.fixture
def mock_court_facade():
    return AsyncMock(spec=CourtProcessingFacadeService)

@pytest.fixture
def mock_config_facade():
    return AsyncMock(spec=ConfigurationFacadeService)

@pytest.fixture
def pacer_orchestrator(mock_browser_facade, mock_court_facade, mock_config_facade):
    return PacerOrchestratorService(
        logger=Mock(),
        config={"run_parallel": False},
        browser_facade=mock_browser_facade,
        court_processing_facade=mock_court_facade,
        config_facade=mock_config_facade,
    )

@pytest.mark.asyncio
async def test_run_main_workflow_sequential(pacer_orchestrator, mock_config_facade, mock_court_facade):
    """Test the main workflow in sequential mode."""
    courts = ["nysd", "cand"]
    mock_config_facade.execute.return_value = {"courts_to_process": courts}

    # Mock the context manager for the browser facade
    mock_browser_facade = AsyncMock()
    pacer_orchestrator.browser_facade = mock_browser_facade

    await pacer_orchestrator.execute({"action": "run_main_workflow"})

    assert mock_court_facade.execute.call_count == 2
    mock_browser_facade.__aenter__.assert_called_once()
    mock_browser_facade.__aexit__.assert_called_once()

@pytest.mark.asyncio
async def test_run_main_workflow_parallel(pacer_orchestrator, mock_config_facade, mock_court_facade):
    """Test the main workflow in parallel mode."""
    pacer_orchestrator.run_parallel = True
    courts = ["nysd", "cand"]
    mock_config_facade.execute.return_value = {"courts_to_process": courts}

    with patch.object(pacer_orchestrator, "_process_single_court_isolated", new_callable=AsyncMock) as mock_process_isolated:
        await pacer_orchestrator.execute({"action": "run_main_workflow"})
        assert mock_process_isolated.call_count == 2

@pytest.mark.asyncio
async def test_invalid_action(pacer_orchestrator):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await pacer_orchestrator.execute({"action": "invalid_action"})
