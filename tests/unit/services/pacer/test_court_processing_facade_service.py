import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.court_processing_facade_service import CourtProcessingFacadeService
from src.pacer._processing_components.download_path_manager import DownloadPathManager
from src.pacer._processing_components.workflow_orchestrator import WorkflowOrchestrator


@pytest.fixture
def mock_download_path_manager():
    return AsyncMock(spec=DownloadPathManager)

@pytest.fixture
def mock_workflow_orchestrator():
    return AsyncMock(spec=WorkflowOrchestrator)

@pytest.fixture
def court_processing_facade(mock_download_path_manager, mock_workflow_orchestrator):
    return CourtProcessingFacadeService(
        logger=Mock(),
        config={},
        download_path_manager=mock_download_path_manager,
        workflow_orchestrator=mock_workflow_orchestrator,
    )

@pytest.mark.asyncio
async def test_handle_court_workflow_action(
    court_processing_facade,
    mock_download_path_manager,
    mock_workflow_orchestrator
):
    """Test that the 'handle_court_workflow' action calls the workflow orchestrator."""
    workflow_config = {
        "processing_mode": "date_range",
        "court_id": "nysd",
        "workflow_config": {"iso_date": "20230101"}
    }

    mock_download_path_manager.execute.return_value = "/tmp/download"
    mock_workflow_orchestrator.execute.return_value = {"status": "success"}

    result = await court_processing_facade.execute({
        "action": "handle_court_workflow",
        **workflow_config
    })

    mock_download_path_manager.execute.assert_called_once()
    mock_workflow_orchestrator.execute.assert_called_once()

    assert result == {"status": "success"}

@pytest.mark.asyncio
async def test_setup_download_path_action(court_processing_facade, mock_download_path_manager):
    """Test that the 'setup_download_path' action calls the download path manager."""
    path_config = {"court_id": "nysd", "iso_date": "20230101", "mode": "report"}
    mock_download_path_manager.execute.return_value = "/tmp/download"

    result = await court_processing_facade.execute({
        "action": "setup_download_path",
        **path_config
    })

    mock_download_path_manager.execute.assert_called_once_with({
        "action": "setup_download_path",
        **path_config
    })
    assert result == "/tmp/download"

@pytest.mark.asyncio
async def test_invalid_action(court_processing_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await court_processing_facade.execute({"action": "invalid_action"})
