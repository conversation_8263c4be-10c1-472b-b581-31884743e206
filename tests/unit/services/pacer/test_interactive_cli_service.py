import pytest
from unittest.mock import Mock, AsyncMock, patch

from src.pacer.interactive_service import PacerInteractiveService
from src.pacer.query_facade_service import QueryFacadeService
from src.pacer.export_facade_service import ExportFacadeService


@pytest.fixture
def mock_query_facade():
    return AsyncMock(spec=QueryFacadeService)

@pytest.fixture
def mock_export_facade():
    return AsyncMock(spec=ExportFacadeService)

@pytest.fixture
def interactive_service(mock_query_facade, mock_export_facade):
    return PacerInteractiveService(
        logger=Mock(),
        config={},
        query_service=mock_query_facade,
        export_facade=mock_export_facade,
    )

@pytest.mark.asyncio
@patch("src.pacer.interactive_service.Prompt.ask")
@patch("src.pacer.interactive_service.Confirm.ask")
async def test_law_firm_search_and_export(
    mock_confirm,
    mock_prompt,
    interactive_service,
    mock_query_facade,
    mock_export_facade
):
    """Test the interactive law firm search and export workflow."""
    # Simulate user input
    mock_prompt.side_effect = ["Test Firm", "test_firm.csv"]
    mock_confirm.return_value = True

    # Mock service responses
    mock_query_facade.search_by_law_firm_with_totals.return_value = {
        "total_count": 10,
        "mdl_breakdown": {"1234": 5, "5678": 5},
        "results": [{"docket": "1"}, {"docket": "2"}],
    }

    await interactive_service.execute({"action": "interactive_law_firm_search"})

    # Assertions
    mock_query_facade.search_by_law_firm_with_totals.assert_called_once_with("Test Firm")
    mock_export_facade.execute.assert_called_once_with({
        "action": "export_to_csv",
        "data": [{"docket": "1"}, {"docket": "2"}],
        "filename": "test_firm.csv",
    })

@pytest.mark.asyncio
@patch("src.pacer.interactive_service.Confirm.ask", return_value=False)
async def test_law_firm_search_no_export(
    mock_confirm,
    interactive_service,
    mock_query_facade,
    mock_export_facade
):
    """Test the interactive law firm search without exporting."""
    mock_query_facade.search_by_law_firm_with_totals.return_value = {
        "total_count": 1, "mdl_breakdown": {}, "results": [{"docket": "1"}]
    }

    with patch("src.pacer.interactive_service.Prompt.ask", return_value="Test Firm"):
        await interactive_service.execute({"action": "interactive_law_firm_search"})

    mock_export_facade.execute.assert_not_called()

@pytest.mark.asyncio
async def test_invalid_action(interactive_service):
    """Test that an invalid action raises an error."""
    with pytest.raises(PacerServiceError):
        await interactive_service.execute({"action": "invalid_action"})
