import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.case_classification_facade_service import CaseClassificationFacadeService
from src.pacer._classification_components.case_classifier import CaseClassifier


@pytest.fixture
def mock_case_classifier():
    return AsyncMock(spec=CaseClassifier)

@pytest.fixture
def case_classification_facade(mock_case_classifier):
    return CaseClassificationFacadeService(
        logger=Mock(),
        config={},
        case_classifier=mock_case_classifier,
    )

@pytest.mark.asyncio
async def test_classify_case_action(case_classification_facade, mock_case_classifier):
    """Test that the 'classify_case' action calls the case classifier component."""
    case_details = {"docket_num": "1:23-cv-12345"}
    html_content = "<html>...</html>"

    mock_case_classifier.execute.return_value = {
        "docket_num": "1:23-cv-12345",
        "is_removal": True
    }

    result = await case_classification_facade.execute({
        "action": "classify_case",
        "case_details": case_details,
        "html_content": html_content,
    })

    mock_case_classifier.execute.assert_called_once_with({
        "action": "classify_case",
        "case_details": case_details,
        "html_content": html_content,
    })
    assert result["is_removal"] is True

@pytest.mark.asyncio
async def test_invalid_action(case_classification_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await case_classification_facade.execute({"action": "invalid_action"})
