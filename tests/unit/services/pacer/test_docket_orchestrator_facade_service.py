import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.docket_orchestrator_facade_service import DocketOrchestratorFacadeService
from src.pacer.html_processing_facade_service import HtmlProcessingFacadeService
from src.pacer.relevance_facade_service import <PERSON>lev<PERSON>FacadeService as RelevanceService
from src.pacer.case_classification_facade_service import CaseClassificationFacadeService as PacerCaseClassificationService
from src.pacer.case_verification_facade_service import CaseVerificationFacadeService
from src.pacer.download_orchestration_facade_service import DownloadOrchestrationFacadeService


@pytest.fixture
def mock_html_facade():
    return AsyncMock(spec=HtmlProcessingFacadeService)

@pytest.fixture
def mock_relevance_service():
    mock = AsyncMock(spec=RelevanceService)
    mock.should_skip_processing.return_value = False
    return mock

@pytest.fixture
def mock_classification_service():
    return Mock(spec=PacerCaseClassificationService)

@pytest.fixture
def mock_verification_facade():
    return AsyncMock(spec=CaseVerificationFacadeService)

@pytest.fixture
def mock_download_facade():
    return AsyncMock(spec=DownloadOrchestrationFacadeService)

@pytest.fixture
def docket_orchestrator(
    mock_html_facade,
    mock_relevance_service,
    mock_classification_service,
    mock_verification_facade,
    mock_download_facade,
):
    return DocketOrchestratorFacadeService(
        logger=Mock(),
        config={},
        html_processing_facade=mock_html_facade,
        relevance_service=mock_relevance_service,
        case_classification_service=mock_classification_service,
        case_verification_facade=mock_verification_facade,
        download_orchestration_facade=mock_download_facade,
    )

@pytest.mark.asyncio
async def test_process_docket_case_workflow(docket_orchestrator, mock_html_facade, mock_verification_facade, mock_download_facade):
    """Test the full, successful workflow of processing a docket case."""
    page = AsyncMock()
    page.content.return_value = "<html></html>"
    initial_details = {"docket_num": "1:23-cv-12345"}

    # Mock return values
    mock_html_facade.execute.return_value = {"html_processed": True, **initial_details}
    mock_verification_facade.verify_case.return_value = True
    mock_download_facade.execute.return_value = {"downloaded": True, "html_processed": True, **initial_details}

    result = await docket_orchestrator.execute({
        "action": "process_docket_case",
        "page": page,
        "initial_details": initial_details,
    })

    # Assert that all facades were called
    mock_html_facade.execute.assert_called_once()
    mock_verification_facade.verify_case.assert_called_once()
    mock_download_facade.execute.assert_called_once()

    assert result["downloaded"] is True

@pytest.mark.asyncio
async def test_skip_on_relevance(docket_orchestrator, mock_relevance_service, mock_download_facade):
    """Test that processing is skipped if relevance service flags it."""
    page = AsyncMock()
    initial_details = {"docket_num": "1:23-cv-12345"}

    mock_relevance_service.should_skip_processing.return_value = True

    await docket_orchestrator.execute({
        "action": "process_docket_case",
        "page": page,
        "initial_details": initial_details,
    })

    mock_download_facade.execute.assert_not_called()

@pytest.mark.asyncio
async def test_skip_on_verification(docket_orchestrator, mock_verification_facade, mock_download_facade):
    """Test that processing is skipped if verification service flags it."""
    page = AsyncMock()
    initial_details = {"docket_num": "1:23-cv-12345"}

    mock_verification_facade.verify_case.return_value = False

    await docket_orchestrator.execute({
        "action": "process_docket_case",
        "page": page,
        "initial_details": initial_details,
    })

    mock_download_facade.execute.assert_not_called()

@pytest.mark.asyncio
async def test_invalid_action(docket_orchestrator):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await docket_orchestrator.execute({"action": "invalid_action"})