import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.download_orchestration_facade_service import DownloadOrchestrationFacadeService
from src.pacer._download_components.download_validator import DownloadValidator
from src.pacer._download_components.file_downloader import FileDownloader
from src.pacer._download_components.download_manager import DownloadManager


@pytest.fixture
def mock_download_validator():
    return AsyncMock(spec=DownloadValidator)

@pytest.fixture
def mock_file_downloader():
    return AsyncMock(spec=FileDownloader)

@pytest.fixture
def mock_download_manager():
    return AsyncMock(spec=DownloadManager)

@pytest.fixture
def download_orchestration_facade(
    mock_download_validator,
    mock_file_downloader,
    mock_download_manager
):
    return DownloadOrchestrationFacadeService(
        logger=Mock(),
        config={},
        download_validator=mock_download_validator,
        file_downloader=mock_file_downloader,
        download_manager=mock_download_manager,
    )

@pytest.mark.asyncio
async def test_execute_download_workflow_action(
    download_orchestration_facade,
    mock_download_validator,
    mock_file_downloader,
    mock_download_manager
):
    """Test the 'execute_download_workflow' action."""
    case_details = {"docket_num": "1:23-cv-12345"}
    page = AsyncMock()

    # Mock return values
    mock_download_validator.execute.return_value = True
    mock_file_downloader.execute.return_value = {"downloaded": True, **case_details}

    result = await download_orchestration_facade.execute({
        "action": "execute_download_workflow",
        "case_details": case_details,
        "is_explicitly_requested": False,
        "page": page
    })

    mock_download_validator.execute.assert_called_once()
    mock_file_downloader.execute.assert_called_once()

    assert result["downloaded"] is True

@pytest.mark.asyncio
async def test_skip_download_if_invalid(download_orchestration_facade, mock_download_validator, mock_file_downloader):
    """Test that download is skipped if validator returns False."""
    case_details = {"docket_num": "1:23-cv-12345"}
    page = AsyncMock()

    mock_download_validator.execute.return_value = False

    await download_orchestration_facade.execute({
        "action": "execute_download_workflow",
        "case_details": case_details,
        "is_explicitly_requested": False,
        "page": page
    })

    mock_download_validator.execute.assert_called_once()
    mock_file_downloader.execute.assert_not_called()

@pytest.mark.asyncio
async def test_invalid_action(download_orchestration_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await download_orchestration_facade.execute({"action": "invalid_action"})
