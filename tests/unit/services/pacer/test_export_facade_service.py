import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.export_facade_service import ExportFacadeService
from src.pacer._export_components.csv_exporter import CsvExporter


@pytest.fixture
def mock_csv_exporter():
    return AsyncMock(spec=CsvExporter)

@pytest.fixture
def export_facade(mock_csv_exporter):
    return ExportFacadeService(
        logger=Mock(),
        config={},
        csv_exporter=mock_csv_exporter,
    )

@pytest.mark.asyncio
async def test_export_to_csv_action(export_facade, mock_csv_exporter):
    """Test that the 'export_to_csv' action calls the CsvExporter component."""
    data = [{"col1": "a", "col2": "b"}]
    filename = "test.csv"

    await export_facade.execute({
        "action": "export_to_csv",
        "data": data,
        "filename": filename,
    })

    mock_csv_exporter.execute.assert_called_once_with({
        "action": "export",
        "data": data,
        "filename": filename,
    })

@pytest.mark.asyncio
async def test_invalid_action(export_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await export_facade.execute({"action": "invalid_action"})
