import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.authentication_facade_service import AuthenticationFacadeService
from src.pacer._authentication_components.login_handler import <PERSON><PERSON>Handler
from src.pacer._authentication_components.session_manager import SessionManager
from src.pacer._authentication_components.credential_validator import CredentialValidator
from src.pacer._authentication_components.ecf_login_handler import ECFLoginHandler


@pytest.fixture
def mock_login_handler():
    return AsyncMock(spec=LoginHandler)

@pytest.fixture
def mock_session_manager():
    return AsyncMock(spec=SessionManager)

@pytest.fixture
def mock_credential_validator():
    return Mock(spec=CredentialValidator)

@pytest.fixture
def mock_ecf_login_handler():
    return AsyncMock(spec=ECFLoginHandler)

@pytest.fixture
def authentication_facade(
    mock_login_handler,
    mock_session_manager,
    mock_credential_validator,
    mock_ecf_login_handler
):
    return AuthenticationFacadeService(
        logger=Mock(),
        config={},
        login_handler=mock_login_handler,
        session_manager=mock_session_manager,
        credential_validator=mock_credential_validator,
        ecf_login_handler=mock_ecf_login_handler,
    )

@pytest.mark.asyncio
async def test_login_action(authentication_facade, mock_login_handler):
    """Test that the 'login' action calls the login handler."""
    credentials = {"username": "test", "password": "password"}
    mock_login_handler.execute.return_value = {"session_id": "123"}

    result = await authentication_facade.execute({
        "action": "login",
        "credentials": credentials
    })

    mock_login_handler.execute.assert_called_once_with({
        "action": "login",
        "credentials": credentials
    })
    assert result == {"session_id": "123"}

@pytest.mark.asyncio
async def test_logout_action(authentication_facade, mock_session_manager):
    """Test that the 'logout' action calls the session manager."""
    session_id = "123"

    await authentication_facade.execute({
        "action": "logout",
        "session_id": session_id
    })

    mock_session_manager.execute.assert_called_once_with({
        "action": "logout",
        "session_id": session_id
    })

@pytest.mark.asyncio
async def test_validate_credentials_action(authentication_facade, mock_credential_validator):
    """Test that the 'validate_credentials' action calls the credential validator."""
    credentials = {"username": "test", "password": "password"}
    mock_credential_validator.execute.return_value = True

    result = await authentication_facade.execute({
        "action": "validate_credentials",
        "credentials": credentials
    })

    mock_credential_validator.execute.assert_called_once_with({
        "action": "validate",
        "credentials": credentials
    })
    assert result is True

@pytest.mark.asyncio
async def test_ecf_login_action(authentication_facade, mock_ecf_login_handler):
    """Test that the 'ecf_login' action calls the ECF login handler."""
    navigator = Mock()
    court_id = "nysd"

    await authentication_facade.execute({
        "action": "ecf_login",
        "navigator": navigator,
        "court_id": court_id
    })

    mock_ecf_login_handler.execute.assert_called_once_with({
        "action": "login",
        "navigator": navigator,
        "court_id": court_id
    })

@pytest.mark.asyncio
async def test_invalid_action(authentication_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await authentication_facade.execute({"action": "invalid_action"})
