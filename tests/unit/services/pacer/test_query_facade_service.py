import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.query_facade_service import QueryFacadeService
from src.pacer._query_components.query_builder import QueryBuilder
from src.pacer._query_components.result_parser import ResultParser


@pytest.fixture
def mock_query_builder():
    return Mock(spec=QueryBuilder)

@pytest.fixture
def mock_result_parser():
    return Mock(spec=ResultParser)

@pytest.fixture
def mock_repository():
    return AsyncMock()

@pytest.fixture
def query_facade(mock_query_builder, mock_result_parser, mock_repository):
    return QueryFacadeService(
        logger=Mock(),
        config={},
        query_builder=mock_query_builder,
        result_parser=mock_result_parser,
        repository=mock_repository,
    )

@pytest.mark.asyncio
async def test_query_by_mdl_num_action(query_facade, mock_query_builder, mock_repository, mock_result_parser):
    """Test the 'query_by_mdl_num' action."""
    mdl_num = "1234"
    query = "MDL_QUERY"
    raw_results = [{"raw": "data"}]
    parsed_results = [{"parsed": "data"}]

    mock_query_builder.execute.return_value = query
    mock_repository.query.return_value = raw_results
    mock_result_parser.execute.return_value = parsed_results

    result = await query_facade.execute({
        "action": "query_by_mdl_num",
        "mdl_num": mdl_num
    })

    mock_query_builder.execute.assert_called_once_with({"action": "build_mdl_query", "mdl_num": mdl_num})
    mock_repository.query.assert_called_once_with(query)
    mock_result_parser.execute.assert_called_once_with({"action": "parse_results", "results": raw_results})

    assert result == parsed_results

@pytest.mark.asyncio
async def test_invalid_action(query_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await query_facade.execute({"action": "invalid_action"})
