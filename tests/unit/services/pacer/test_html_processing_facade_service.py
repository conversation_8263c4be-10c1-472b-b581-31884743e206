import pytest
from unittest.mock import Mock, AsyncMock

from src.pacer.html_processing_facade_service import HtmlProcessingFacadeService
from src.pacer._case_processing_components.html_parser import HtmlParser
from src.pacer._case_processing_components.law_firm_corrector import LawFirmCorrector
from src.pacer._download_components.s3_manager import S3Manager
from src.pacer._case_processing_components.field_consistency_manager import FieldConsistencyManager
from src.pacer._case_processing_components.transfer_info_processor import TransferInfoProcessor


@pytest.fixture
def mock_html_parser():
    return AsyncMock(spec=HtmlParser)

@pytest.fixture
def mock_law_firm_corrector():
    return AsyncMock(spec=LawFirmCorrector)

@pytest.fixture
def mock_s3_manager():
    return AsyncMock(spec=S3Manager)

@pytest.fixture
def mock_field_consistency_manager():
    return AsyncMock(spec=FieldConsistencyManager)

@pytest.fixture
def mock_transfer_info_processor():
    return AsyncMock(spec=TransferInfoProcessor)

@pytest.fixture
def html_processing_facade(
    mock_html_parser,
    mock_law_firm_corrector,
    mock_s3_manager,
    mock_field_consistency_manager,
    mock_transfer_info_processor,
):
    return HtmlProcessingFacadeService(
        logger=Mock(),
        config={},
        html_parser=mock_html_parser,
        law_firm_corrector=mock_law_firm_corrector,
        s3_manager=mock_s3_manager,
        field_consistency_manager=mock_field_consistency_manager,
        transfer_info_processor=mock_transfer_info_processor,
    )

@pytest.mark.asyncio
async def test_process_html_content_workflow(
    html_processing_facade,
    mock_html_parser,
    mock_law_firm_corrector,
    mock_s3_manager,
    mock_field_consistency_manager,
    mock_transfer_info_processor,
):
    """Test the full workflow of the process_html_content action."""
    case_details = {"docket_num": "1:23-cv-12345", "case_in_other_court": "some info"}
    html_content = "<html>...</html>"
    json_path = "/path/to/file.json"

    # Mock return values
    mock_html_parser.execute.return_value = [{"attorney_name": "test"}]
    mock_law_firm_corrector.execute.return_value = [{"attorney_name": "test", "law_firm": "corrected"}]
    mock_s3_manager.execute.side_effect = [True, "http://s3.link"] # upload, find
    mock_transfer_info_processor.execute.return_value = {**case_details, "transferred_from": "some info"}
    mock_field_consistency_manager.execute.return_value = {"final": "details"}

    result = await html_processing_facade.execute({
        "action": "process_html_content",
        "case_details": case_details,
        "html_content": html_content,
        "json_path": json_path,
    })

    # Assert calls
    mock_html_parser.execute.assert_called_once()
    mock_law_firm_corrector.execute.assert_called_once()
    assert mock_s3_manager.execute.call_count == 2
    mock_transfer_info_processor.execute.assert_called_once()
    mock_field_consistency_manager.execute.assert_called_once()

    assert result == {"final": "details"}

@pytest.mark.asyncio
async def test_invalid_action(html_processing_facade):
    """Test that an invalid action raises a ValueError."""
    with pytest.raises(ValueError):
        await html_processing_facade.execute({"action": "invalid_action"})
