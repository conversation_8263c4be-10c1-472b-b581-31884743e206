import pytest
import os
import asyncio
from pathlib import Path
from unittest.mock import Mock, AsyncMock

# Updated imports to use core services
from src.pacer._core_services.file_operations.file_operations_service import FileOperationsService
from src.pacer.pacer_orchestrator_service import PacerOrchestratorService

@pytest.fixture
def mock_file_operations_service():
    return AsyncMock(spec=FileOperationsService)

@pytest.fixture
def mock_pacer_orchestrator():
    return AsyncMock(spec=PacerOrchestratorService)

@pytest.mark.asyncio
async def test_download_path_setup(mock_file_operations_service):
    """
    CRITICAL: Test that download paths are properly initialized using FileOperationsService.
    This ensures the download path infrastructure is correctly set up.
    """
    court_id = "nysd"
    iso_date = "20230101"
    mode = "report"
    expected_path = f"/tmp/downloads/{court_id}/{iso_date}"

    mock_file_operations_service.execute.return_value = expected_path

    result = await mock_file_operations_service.execute({
        "action": "setup_directories",
        "court_id": court_id,
        "iso_date": iso_date,
        "mode": mode
    })

    mock_file_operations_service.execute.assert_called_once_with({
        "action": "setup_directories",
        "court_id": court_id,
        "iso_date": iso_date,
        "mode": mode
    })
    assert court_id in result
    assert iso_date in result

@pytest.mark.asyncio
async def test_parallel_download_path_coordination(mock_file_operations_service):
    """
    CRITICAL: Test download path coordination during parallel processing using core services.
    """
    courts = ["cacd", "nysd", "txnd", "flsd"]
    iso_date = "20250115"
    mode = "report"

    async def side_effect(data):
        return f"/tmp/downloads/{data['court_id']}"

    mock_file_operations_service.execute.side_effect = side_effect

    tasks = [mock_file_operations_service.execute({
        "action": "setup_directories",
        "court_id": court,
        "iso_date": iso_date,
        "mode": mode
    }) for court in courts]

    download_paths = await asyncio.gather(*tasks)

    assert len(download_paths) == len(courts)
    assert len(set(download_paths)) == len(courts), "Download paths should be unique"

def test_download_path_security_validation():
    """
    CRITICAL: Test download path security validation.
    """
    malicious_ids = ["../../../etc/passwd", "..\\..\\windows\\system32"]

    def sanitize_id(court_id: str) -> str:
        import re
        return re.sub(r'[\.\/\\]+', '', court_id)

    for malicious_id in malicious_ids:
        sanitized = sanitize_id(malicious_id)
        assert ".." not in sanitized
        assert "/" not in sanitized
        assert "\\" not in sanitized
