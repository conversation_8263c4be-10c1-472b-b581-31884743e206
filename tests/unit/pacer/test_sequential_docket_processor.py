"""
Unit tests for Sequential Docket Processor Architecture

Tests the 6-step sequential workflow implementation with:
- Individual step execution
- State transitions
- Error handling and recovery
- Integration points
- Navigation control
"""

import asyncio
import pytest
from datetime import datetime
from unittest.mock import AsyncMock, Mock, patch

from src.pacer.components.processing.sequential_docket_processor import (
    SequentialDocketProcessor,
    WorkflowStep,
    WorkflowContext,
    StepResult,
    WorkflowState,
    ParseHtmlStep,
    RelevanceCheckStep,
    TransferHandlingStep,
    IgnoreListCheckStep,
    DocumentDownloadStep,
    ReturnAndContinueStep,
    ErrorRecoveryManager
)


class TestWorkflowStep:
    """Test base workflow step functionality"""
    
    class TestStep(WorkflowStep):
        """Test implementation of WorkflowStep"""
        
        def __init__(self, name: str, should_succeed: bool = True):
            super().__init__(name)
            self.should_succeed = should_succeed
            
        async def execute(self, context: WorkflowContext) -> StepResult:
            if self.should_succeed:
                return StepResult(success=True, data={"step_executed": self.name})
            else:
                return StepResult(success=False, error=f"Test failure in {self.name}")
    
    def test_step_initialization(self):
        """Test step initialization"""
        step = self.TestStep("test_step")
        assert step.name == "test_step"
        assert step.state == WorkflowState.PENDING
        assert step.execution_time is None
    
    @pytest.mark.asyncio
    async def test_step_execution_success(self):
        """Test successful step execution"""
        step = self.TestStep("test_step", should_succeed=True)
        context = Mock(spec=WorkflowContext)
        
        result = await step.execute(context)
        
        assert result.success is True
        assert result.data["step_executed"] == "test_step"
        assert result.error is None
    
    @pytest.mark.asyncio
    async def test_step_execution_failure(self):
        """Test failed step execution"""
        step = self.TestStep("test_step", should_succeed=False)
        context = Mock(spec=WorkflowContext)
        
        result = await step.execute(context)
        
        assert result.success is False
        assert result.error == "Test failure in test_step"
    
    @pytest.mark.asyncio
    async def test_step_error_handling(self):
        """Test step error handling"""
        step = self.TestStep("test_step")
        context = Mock(spec=WorkflowContext)
        context.court_logger = Mock()
        
        # Mock an exception
        test_error = Exception("Test exception")
        
        result = await step.handle_error(test_error, context)
        
        assert result.success is False
        assert "Test exception" in result.error
        assert result.skip_to_return is True


class TestParseHtmlStep:
    """Test HTML parsing step"""
    
    @pytest.fixture
    def mock_html_parser(self):
        """Mock HTML parser service"""
        parser = AsyncMock()
        parser._execute_action.return_value = [
            {"name": "John Doe", "role": "plaintiff"},
            {"name": "Jane Smith", "role": "defendant"}
        ]
        return parser
    
    @pytest.fixture
    def sample_context(self):
        """Sample workflow context"""
        context = Mock(spec=WorkflowContext)
        context.html_content = "<html><body>Sample docket content</body></html>"
        context.court_logger = Mock()
        context.court_logger.log_phase_start = Mock()
        context.court_logger.log_phase_complete = Mock()
        context.case_details = {}
        return context
    
    @pytest.mark.asyncio
    async def test_parse_html_success(self, mock_html_parser, sample_context):
        """Test successful HTML parsing"""
        step = ParseHtmlStep(mock_html_parser)
        
        result = await step.execute(sample_context)
        
        assert result.success is True
        assert len(result.data["parsed_attorneys"]) == 2
        assert result.data["html_metadata"]["attorneys_count"] == 2
        
        mock_html_parser._execute_action.assert_called_once_with({
            "action": "extract_attorneys",
            "html_content": sample_context.html_content
        })
    
    @pytest.mark.asyncio
    async def test_parse_html_empty_content(self, mock_html_parser):
        """Test HTML parsing with empty content"""
        step = ParseHtmlStep(mock_html_parser)
        context = Mock(spec=WorkflowContext)
        context.html_content = ""
        context.court_logger = Mock()
        context.court_logger.log_phase_start = Mock()
        
        result = await step.execute(context)
        
        assert result.success is False
        assert result.skip_to_return is True
        assert "No HTML content provided" in result.error


class TestRelevanceCheckStep:
    """Test relevance checking step"""
    
    @pytest.fixture
    def mock_relevance_service(self):
        """Mock relevance service"""
        service = AsyncMock()
        service._execute_action.return_value = {
            "is_relevant": True,
            "score": 0.85,
            "reasons": ["defendant match"]
        }
        return service
    
    @pytest.fixture
    def mock_s3_service(self):
        """Mock S3 service"""
        service = AsyncMock()
        service._execute_action.return_value = {"upload_success": True}
        return service
    
    @pytest.fixture
    def context_with_defendants(self):
        """Context with defendant information"""
        context = Mock(spec=WorkflowContext)
        context.case_details = {
            "versus": "John Doe v. ABC Corp",
            "defendants": ["ABC Corp"]
        }
        context.court_logger = Mock()
        context.court_logger.log_phase_start = Mock()
        context.court_logger.log_phase_complete = Mock()
        context.court_logger.debug = Mock()
        context.config = {"iso_date": "20250110"}
        return context
    
    @pytest.mark.asyncio
    async def test_relevance_check_relevant(self, mock_relevance_service, mock_s3_service, context_with_defendants):
        """Test relevance check with relevant case"""
        step = RelevanceCheckStep(mock_relevance_service, mock_s3_service)
        
        result = await step.execute(context_with_defendants)
        
        assert result.success is True
        assert result.data["is_relevant"] is True
        assert result.data["relevance_score"] == 0.85
        assert result.skip_to_return is False
    
    @pytest.mark.asyncio
    async def test_relevance_check_irrelevant(self, mock_relevance_service, context_with_defendants):
        """Test relevance check with irrelevant case"""
        # Mock irrelevant result
        mock_relevance_service._execute_action.return_value = {
            "is_relevant": False,
            "score": 0.2
        }
        
        step = RelevanceCheckStep(mock_relevance_service)
        
        result = await step.execute(context_with_defendants)
        
        assert result.success is True
        assert result.data["is_relevant"] is False
        assert result.skip_to_return is True
    
    @pytest.mark.asyncio 
    async def test_s3_upload_failure(self, mock_relevance_service, context_with_defendants):
        """Test graceful handling of S3 upload failure"""
        mock_s3_service = AsyncMock()
        mock_s3_service._execute_action.side_effect = Exception("S3 upload failed")
        
        step = RelevanceCheckStep(mock_relevance_service, mock_s3_service)
        
        result = await step.execute(context_with_defendants)
        
        # Should still succeed despite S3 failure
        assert result.success is True
        context_with_defendants.court_logger.warning.assert_called()


class TestTransferHandlingStep:
    """Test transfer handling step"""
    
    @pytest.fixture
    def context_with_transfer(self):
        """Context with transfer indicators"""
        context = Mock(spec=WorkflowContext)
        context.case_details = {
            "case_title": "Transfer case from NDCA",
            "nature_of_suit": "Civil rights removal"
        }
        context.court_logger = Mock()
        context.court_logger.log_phase_start = Mock()
        context.court_logger.log_phase_complete = Mock()
        context.court_logger.info = Mock()
        return context
    
    @pytest.mark.asyncio
    async def test_no_transfer_detected(self):
        """Test handling when no transfer is detected"""
        step = TransferHandlingStep()
        context = Mock(spec=WorkflowContext)
        context.case_details = {"case_title": "Normal civil case"}
        context.court_logger = Mock()
        context.court_logger.log_phase_start = Mock()
        context.court_logger.log_phase_complete = Mock()
        
        result = await step.execute(context)
        
        assert result.success is True
        assert result.data["transfer_status"] == "none"
        assert result.skip_to_return is False
    
    @pytest.mark.asyncio
    async def test_transfer_detected_high_confidence(self, context_with_transfer):
        """Test handling when high-confidence transfer is detected"""
        step = TransferHandlingStep()
        
        result = await step.execute(context_with_transfer)
        
        assert result.success is True
        assert result.data["transfer_status"] == "detected"
        # High confidence should trigger skip
        if result.data["handling_decision"] == "skip_processing":
            assert result.skip_to_return is True


class TestIgnoreListCheckStep:
    """Test ignore list checking step"""
    
    @pytest.fixture
    def mock_ignore_service(self):
        """Mock ignore download service"""
        service = AsyncMock()
        service._execute_action.return_value = False  # Not in ignore list
        return service
    
    @pytest.fixture
    def basic_context(self):
        """Basic context for ignore list testing"""
        context = Mock(spec=WorkflowContext)
        context.case_details = {"court_id": "cand", "docket_num": "1:23-cv-12345"}
        context.config = {"_processing_explicit_dockets": False}
        context.court_logger = Mock()
        context.court_logger.log_phase_start = Mock()
        context.court_logger.log_phase_complete = Mock()
        return context
    
    @pytest.mark.asyncio
    async def test_ignore_check_continue(self, mock_ignore_service, basic_context):
        """Test ignore check when case should continue"""
        step = IgnoreListCheckStep(mock_ignore_service)
        
        result = await step.execute(basic_context)
        
        assert result.success is True
        assert result.data["final_ignore_decision"] is False
        assert result.skip_to_return is False
    
    @pytest.mark.asyncio
    async def test_ignore_check_ignore(self, mock_ignore_service, basic_context):
        """Test ignore check when case should be ignored"""
        mock_ignore_service._execute_action.return_value = True  # In ignore list
        
        step = IgnoreListCheckStep(mock_ignore_service)
        
        result = await step.execute(basic_context)
        
        assert result.success is True
        assert result.data["final_ignore_decision"] is True
        assert result.skip_to_return is True
    
    @pytest.mark.asyncio
    async def test_ignore_check_explicit_override(self, mock_ignore_service, basic_context):
        """Test ignore check with explicit processing override"""
        mock_ignore_service._execute_action.return_value = True  # In ignore list
        basic_context.config["_processing_explicit_dockets"] = True  # Override
        
        step = IgnoreListCheckStep(mock_ignore_service)
        
        result = await step.execute(basic_context)
        
        assert result.success is True
        assert result.data["should_ignore"] is True
        assert result.data["explicit_override"] is True
        assert result.data["final_ignore_decision"] is False  # Override should allow processing
        assert result.skip_to_return is False


class TestDocumentDownloadStep:
    """Test document download step"""
    
    @pytest.fixture
    def mock_download_service(self):
        """Mock download orchestration service"""
        service = AsyncMock()
        service.execute_download_workflow.return_value = {
            "download_count": 5,
            "files_downloaded": ["doc1.pdf", "doc2.pdf"]
        }
        return service
    
    @pytest.fixture
    def context_with_page(self):
        """Context with browser page"""
        context = Mock(spec=WorkflowContext)
        context.case_details = {"court_id": "cand", "docket_num": "1:23-cv-12345"}
        context.config = {"_processing_explicit_dockets": False}
        context.court_logger = Mock()
        context.court_logger.log_phase_start = Mock()
        context.court_logger.log_phase_complete = Mock()
        
        # Mock browser context with page
        mock_page = Mock()
        context.browser_context = Mock()
        context.browser_context.pages = [mock_page]
        
        return context
    
    @pytest.mark.asyncio
    async def test_document_download_success(self, mock_download_service, context_with_page):
        """Test successful document download"""
        step = DocumentDownloadStep(mock_download_service)
        
        result = await step.execute(context_with_page)
        
        assert result.success is True
        assert result.data["downloads_completed"] == 5
        assert "updated_case_details" in result.data
        
        mock_download_service.execute_download_workflow.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_document_download_no_page(self, mock_download_service):
        """Test document download with no available page"""
        step = DocumentDownloadStep(mock_download_service)
        
        context = Mock(spec=WorkflowContext)
        context.browser_context = Mock()
        context.browser_context.pages = []  # No pages
        context.court_logger = Mock()
        context.court_logger.log_phase_start = Mock()
        context.court_logger.warning = Mock()
        
        result = await step.execute(context)
        
        assert result.success is False
        assert result.data["downloads_completed"] == 0
        assert "No page available" in result.error


class TestReturnAndContinueStep:
    """Test return and continue step"""
    
    @pytest.fixture
    def mock_page(self):
        """Mock browser page"""
        page = AsyncMock()
        page.goto = AsyncMock()
        page.locator.return_value.is_visible = AsyncMock(return_value=True)
        page.locator.return_value.wait_for = AsyncMock()
        return page
    
    @pytest.fixture
    def context_with_browser(self, mock_page):
        """Context with browser context"""
        context = Mock(spec=WorkflowContext)
        context.court_id = "cand"
        context.court_logger = Mock()
        context.court_logger.log_phase_start = Mock()
        context.court_logger.log_phase_complete = Mock()
        context.court_logger.info = Mock()
        context.court_logger.debug = Mock()
        
        context.browser_context = Mock()
        context.browser_context.pages = [mock_page]
        context.browser_context.new_page = AsyncMock(return_value=mock_page)
        
        return context
    
    @pytest.mark.asyncio
    async def test_return_to_query_success(self, context_with_browser, mock_page):
        """Test successful return to query page"""
        step = ReturnAndContinueStep()
        
        result = await step.execute(context_with_browser)
        
        assert result.success is True
        assert result.data["query_page_ready"] is True
        assert result.data["navigation_status"] == "success"
        
        # Verify navigation occurred
        mock_page.goto.assert_called_once()
        assert "ecf.cand.uscourts.gov" in mock_page.goto.call_args[0][0]
    
    @pytest.mark.asyncio
    async def test_return_unknown_court(self):
        """Test return to query page with unknown court"""
        step = ReturnAndContinueStep()
        
        context = Mock(spec=WorkflowContext)
        context.court_id = "unknown_court"
        context.court_logger = Mock()
        context.court_logger.log_phase_start = Mock()
        
        result = await step.execute(context)
        
        assert result.success is False
        assert "No query URL configured" in result.error


class TestErrorRecoveryManager:
    """Test error recovery manager"""
    
    @pytest.fixture
    def mock_context(self):
        """Mock workflow context"""
        context = Mock(spec=WorkflowContext)
        context.court_logger = Mock()
        context.court_logger.error = Mock()
        context.court_logger.warning = Mock()
        return context
    
    def test_error_classification(self):
        """Test error type classification"""
        manager = ErrorRecoveryManager()
        
        # Test recoverable errors
        timeout_error = Exception("Connection timeout")
        assert manager._classify_error(timeout_error) == "recoverable"
        
        network_error = Exception("Network error occurred")
        assert manager._classify_error(network_error) == "recoverable"
        
        # Test critical errors
        auth_error = Exception("Authentication failed")
        assert manager._classify_error(auth_error) == "critical"
        
        # Test step-level errors
        generic_error = Exception("Something went wrong")
        assert manager._classify_error(generic_error) == "step_level"
    
    @pytest.mark.asyncio
    async def test_handle_recoverable_error(self, mock_context):
        """Test handling of recoverable errors"""
        manager = ErrorRecoveryManager()
        
        timeout_error = Exception("Connection timeout")
        
        result = await manager.handle_step_error("test_step", timeout_error, mock_context)
        
        assert result.success is False
        assert result.skip_to_return is True
    
    @pytest.mark.asyncio
    async def test_handle_critical_error(self, mock_context):
        """Test handling of critical errors"""
        manager = ErrorRecoveryManager()
        
        auth_error = Exception("Authentication expired")
        
        result = await manager.handle_step_error("test_step", auth_error, mock_context)
        
        assert result.success is False
        assert "Critical failure" in result.error


class TestSequentialDocketProcessor:
    """Test main sequential docket processor"""
    
    @pytest.fixture
    def mock_services(self):
        """Mock all required services"""
        return {
            "html_parser_service": AsyncMock(),
            "relevance_service": AsyncMock(),
            "ignore_download_service": AsyncMock(),
            "download_orchestration_service": AsyncMock(),
            "s3_service": AsyncMock(),
            "transfer_service": AsyncMock(),
            "navigation_manager": AsyncMock()
        }
    
    @pytest.fixture
    def processor(self, mock_services):
        """Sequential docket processor with mocked services"""
        config = {"iso_date": "20250110"}
        
        return SequentialDocketProcessor(
            config=config,
            **mock_services
        )
    
    @pytest.fixture
    def mock_browser_context(self):
        """Mock browser context"""
        context = Mock()
        context.pages = [Mock()]
        context.new_page = AsyncMock()
        return context
    
    @pytest.fixture
    def mock_court_logger(self):
        """Mock court logger"""
        logger = Mock()
        logger.log_phase_start = Mock()
        logger.log_phase_complete = Mock()
        logger.info = Mock()
        logger.error = Mock()
        logger.warning = Mock()
        return logger
    
    @pytest.mark.asyncio
    async def test_successful_workflow_execution(self, processor, mock_browser_context, mock_court_logger):
        """Test complete successful workflow execution"""
        # Mock successful responses from all services
        processor.steps["parse_html"].html_parser_service._execute_action.return_value = [
            {"name": "Test Attorney"}
        ]
        processor.steps["relevance_check"].relevance_service._execute_action.return_value = {
            "is_relevant": True, "score": 0.8
        }
        processor.steps["ignore_list_check"].ignore_download_service._execute_action.return_value = False
        processor.steps["document_download"].download_orchestration_service.execute_download_workflow.return_value = {
            "download_count": 3
        }
        
        # Mock return step page validation
        with patch.object(processor.steps["return_and_continue"], '_validate_query_page_ready', return_value=True):
            result = await processor.process_docket(
                court_id="cand",
                docket_number="1:23-cv-12345",
                html_content="<html>test content</html>",
                browser_context=mock_browser_context,
                court_logger=mock_court_logger
            )
        
        assert result["workflow_status"] == "success"
        assert result["docket_number"] == "1:23-cv-12345"
        assert result["court_id"] == "cand"
        assert "case_details" in result
        assert "step_summary" in result
    
    @pytest.mark.asyncio
    async def test_workflow_with_irrelevant_case(self, processor, mock_browser_context, mock_court_logger):
        """Test workflow with irrelevant case (should skip to return)"""
        # Mock irrelevant case
        processor.steps["parse_html"].html_parser_service._execute_action.return_value = [
            {"name": "Test Attorney"}
        ]
        processor.steps["relevance_check"].relevance_service._execute_action.return_value = {
            "is_relevant": False, "score": 0.2
        }
        
        # Mock return step
        with patch.object(processor.steps["return_and_continue"], '_validate_query_page_ready', return_value=True):
            result = await processor.process_docket(
                court_id="cand",
                docket_number="1:23-cv-12345",
                html_content="<html>test content</html>",
                browser_context=mock_browser_context,
                court_logger=mock_court_logger
            )
        
        # Should still succeed but skip intermediate steps
        assert result["workflow_status"] in ["success", "partial_success"]
        
        # Verify relevance check was executed
        assert "relevance_check" in result["step_summary"]
        
        # Document download should not have been executed (skipped)
        download_step_result = result["step_summary"].get("document_download", {})
        assert download_step_result.get("state") != "success" or download_step_result.get("state") is None
    
    @pytest.mark.asyncio
    async def test_workflow_with_step_failure(self, processor, mock_browser_context, mock_court_logger):
        """Test workflow handling of step failures"""
        # Mock parse step failure
        processor.steps["parse_html"].html_parser_service._execute_action.side_effect = Exception("Parse failed")
        
        # Mock return step
        with patch.object(processor.steps["return_and_continue"], '_validate_query_page_ready', return_value=True):
            result = await processor.process_docket(
                court_id="cand",
                docket_number="1:23-cv-12345",
                html_content="<html>test content</html>",
                browser_context=mock_browser_context,
                court_logger=mock_court_logger
            )
        
        # Should handle failure gracefully
        assert result["workflow_status"] == "partial_success"
        assert "parse_html" in result["step_summary"]
        assert result["step_summary"]["parse_html"]["state"] == "failed"
    
    def test_workflow_step_order(self, processor):
        """Test that workflow steps are in correct order"""
        expected_order = [
            "parse_html",
            "relevance_check", 
            "transfer_handling",
            "ignore_list_check",
            "document_download",
            "return_and_continue"
        ]
        
        assert processor.workflow_order == expected_order
        
        # Verify all steps exist
        for step_name in expected_order:
            assert step_name in processor.steps
    
    def test_next_step_determination(self, processor):
        """Test next step determination logic"""
        # Test normal progression
        next_step = processor._get_next_step("parse_html", StepResult(success=True))
        assert next_step == "relevance_check"
        
        # Test explicit next step override
        result_with_override = StepResult(success=True, next_step="return_and_continue")
        next_step = processor._get_next_step("parse_html", result_with_override)
        assert next_step == "return_and_continue"
        
        # Test end of workflow
        next_step = processor._get_next_step("return_and_continue", StepResult(success=True))
        assert next_step is None


# Integration test fixtures and utilities
@pytest.fixture
def integration_test_data():
    """Sample data for integration tests"""
    return {
        "court_id": "cand",
        "docket_number": "1:23-cv-12345",
        "html_content": """
        <html>
            <body>
                <h1>Case: 1:23-cv-12345</h1>
                <div>John Doe v. ABC Corporation</div>
                <table>
                    <tr><td>Attorney:</td><td>Jane Smith, Esq.</td></tr>
                </table>
            </body>
        </html>
        """,
        "initial_case_details": {
            "court_id": "cand",
            "docket_num": "1:23-cv-12345",
            "versus": "John Doe v. ABC Corporation"
        }
    }


class TestWorkflowIntegration:
    """Integration tests for complete workflow scenarios"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_successful_workflow(self, integration_test_data):
        """Test complete end-to-end workflow execution"""
        # This would require more complex setup with actual service mocks
        # but demonstrates the testing approach for integration scenarios
        pass
    
    @pytest.mark.asyncio
    async def test_workflow_state_persistence(self, integration_test_data):
        """Test that workflow state is properly maintained across steps"""
        # Test that context data flows correctly between steps
        pass
    
    @pytest.mark.asyncio
    async def test_error_recovery_scenarios(self, integration_test_data):
        """Test various error recovery scenarios"""
        # Test different failure modes and recovery paths
        pass