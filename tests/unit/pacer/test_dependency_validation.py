"""
Tests for the dependency validation system.
"""

import pytest
import sys
from unittest.mock import Mock, patch
from src.pacer.utils.dependency_validator import DependencyValidator
from src.pacer.utils.dependency_integration import DependencyIntegration
from src.pacer.utils.validated_component_factory import ValidatedComponentFactory


class TestDependencyValidator:
    """Test the core dependency validator functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_logger = Mock()
        self.mock_logger.info = Mock()
        self.mock_logger.warning = Mock()
        self.mock_logger.error = Mock()
        self.mock_logger.critical = Mock()
        self.mock_logger.debug = Mock()
    
    def test_validate_required_dependencies_success(self):
        """Test successful validation of required dependencies."""
        dependencies = {
            'logger': self.mock_logger,
            's3_service': Mock(),
            'pacer_db': Mock()
        }
        required = ['logger', 's3_service', 'pacer_db']
        
        # Should not raise any exception
        DependencyValidator.validate_required_dependencies(
            dependencies, required, self.mock_logger, "TestComponent"
        )
    
    def test_validate_required_dependencies_missing(self):
        """Test validation fails when dependencies are missing."""
        dependencies = {
            'logger': self.mock_logger,
            's3_service': None  # Missing dependency
        }
        required = ['logger', 's3_service', 'pacer_db']
        
        with patch('sys.exit') as mock_exit:
            DependencyValidator.validate_required_dependencies(
                dependencies, required, self.mock_logger, "TestComponent"
            )
            
            # Should call sys.exit(1)
            mock_exit.assert_called_once_with(1)
            
            # Should log critical error
            self.mock_logger.critical.assert_called()
    
    def test_validate_component_dependencies_known_component(self):
        """Test validation of a known component type."""
        # Create a mock component that looks like DocketProcessor
        mock_component = Mock()
        mock_component.__class__.__name__ = 'DocketProcessor'
        mock_component.s3_service = Mock()
        mock_component.logger = self.mock_logger
        mock_component.html_parser = Mock()
        
        # Should not raise exception
        DependencyValidator.validate_component_dependencies(
            mock_component, self.mock_logger
        )
    
    def test_validate_component_dependencies_unknown_component(self):
        """Test validation of unknown component type."""
        mock_component = Mock()
        mock_component.__class__.__name__ = 'UnknownComponent'
        
        # Should not raise exception for unknown components
        DependencyValidator.validate_component_dependencies(
            mock_component, self.mock_logger
        )
    
    def test_validate_initialization_order_success(self):
        """Test successful initialization order validation."""
        order = ['logger', 's3_service', 'docket_processor']
        dependencies_map = {
            'logger': [],
            's3_service': ['logger'],
            'docket_processor': ['logger', 's3_service']
        }
        
        # Should not raise exception
        DependencyValidator.validate_initialization_order(
            order, dependencies_map, self.mock_logger
        )
    
    def test_validate_initialization_order_failure(self):
        """Test initialization order validation failure."""
        order = ['docket_processor', 's3_service', 'logger']  # Wrong order
        dependencies_map = {
            'logger': [],
            's3_service': ['logger'],
            'docket_processor': ['logger', 's3_service']
        }
        
        with patch('sys.exit') as mock_exit:
            DependencyValidator.validate_initialization_order(
                order, dependencies_map, self.mock_logger
            )
            
            # Should call sys.exit(1)
            mock_exit.assert_called_once_with(1)
    
    def test_create_dependency_report(self):
        """Test creation of dependency report."""
        mock_component1 = Mock()
        mock_component1.__class__.__name__ = 'TestComponent1'
        mock_component1.dependency1 = Mock()
        
        mock_component2 = Mock()
        mock_component2.__class__.__name__ = 'TestComponent2'
        mock_component2.dependency2 = Mock()
        
        components = [mock_component1, mock_component2]
        
        report = DependencyValidator.create_dependency_report(
            components, self.mock_logger
        )
        
        assert 'TestComponent1' in report
        assert 'TestComponent2' in report
        assert 'dependencies' in report['TestComponent1']
        assert 'status' in report['TestComponent1']


class TestDependencyIntegration:
    """Test the dependency integration utilities."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_logger = Mock()
        self.integration = DependencyIntegration()
    
    def test_create_startup_validator(self):
        """Test creation of startup validator."""
        validator = self.integration.create_startup_validator(self.mock_logger)
        
        assert hasattr(validator, 'validate_component')
        assert hasattr(validator, 'get_validation_summary')
        assert hasattr(validator, 'ensure_all_valid')
    
    def test_startup_validator_validation(self):
        """Test startup validator component validation."""
        validator = self.integration.create_startup_validator(self.mock_logger)
        
        # Create a valid mock component
        mock_component = Mock()
        mock_component.__class__.__name__ = 'TestComponent'
        
        # Should return True for unknown component (no validation errors)
        result = validator.validate_component(mock_component)
        assert result is True
    
    def test_startup_validator_summary(self):
        """Test startup validator summary generation."""
        validator = self.integration.create_startup_validator(self.mock_logger)
        
        summary = validator.get_validation_summary()
        
        assert 'validated_components' in summary
        assert 'validation_errors' in summary
        assert 'total_validated' in summary
        assert 'total_errors' in summary
        assert 'success_rate' in summary


class TestValidatedComponentFactory:
    """Test the validated component factory."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_logger = Mock()
        self.factory = ValidatedComponentFactory(self.mock_logger)
    
    def test_factory_initialization(self):
        """Test factory initialization."""
        assert self.factory.logger == self.mock_logger
        assert hasattr(self.factory, 'validator')
        assert hasattr(self.factory, 'integration')
    
    def test_validate_existing_component_success(self):
        """Test validation of existing component."""
        mock_component = Mock()
        mock_component.__class__.__name__ = 'UnknownComponent'
        
        # Should return True for unknown component (no errors)
        result = self.factory.validate_existing_component(mock_component)
        assert result is True
    
    @patch('src.pacer.utils.validated_component_factory.DependencyValidator')
    def test_validate_existing_component_failure(self, mock_validator_class):
        """Test validation failure of existing component."""
        mock_validator = mock_validator_class.return_value
        mock_validator.validate_component_dependencies.side_effect = Exception("Validation failed")
        
        mock_component = Mock()
        
        result = self.factory.validate_existing_component(mock_component)
        assert result is False


class TestEmergencyDependencyCheck:
    """Test the emergency dependency check functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_logger = Mock()
    
    @patch('builtins.__import__')
    def test_emergency_check_success(self, mock_import):
        """Test successful emergency dependency check."""
        from src.pacer.utils.dependency_integration import emergency_dependency_check
        
        # Mock successful imports
        mock_import.return_value = Mock()
        
        # Should not raise exception
        emergency_dependency_check(self.mock_logger)
        
        # Should log success
        self.mock_logger.info.assert_called()
    
    @patch('builtins.__import__')
    @patch('sys.exit')
    def test_emergency_check_failure(self, mock_exit, mock_import):
        """Test emergency dependency check failure."""
        from src.pacer.utils.dependency_integration import emergency_dependency_check
        
        # Mock import failure
        mock_import.side_effect = ImportError("Module not found")
        
        emergency_dependency_check(self.mock_logger)
        
        # Should call sys.exit(1)
        mock_exit.assert_called_once_with(1)
        
        # Should log critical error
        self.mock_logger.critical.assert_called()


def test_dependency_validation_integration():
    """Integration test for the complete dependency validation system."""
    mock_logger = Mock()
    
    # Test that all main components can be imported and instantiated
    validator = DependencyValidator()
    integration = DependencyIntegration()
    factory = ValidatedComponentFactory(mock_logger)
    
    assert validator is not None
    assert integration is not None
    assert factory is not None
    
    # Test that the factory can create a startup validator
    startup_validator = integration.create_startup_validator(mock_logger)
    assert startup_validator is not None
    
    # Test summary generation
    summary = startup_validator.get_validation_summary()
    assert isinstance(summary, dict)
    assert all(key in summary for key in [
        'validated_components', 'validation_errors', 
        'total_validated', 'total_errors', 'success_rate'
    ])