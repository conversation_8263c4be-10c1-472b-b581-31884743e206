"""
Comprehensive Tests for Data Merging and JSON Filename Validation.

Tests all aspects of the enhanced data merging and filename validation system:
- HTML content merging into case data dictionaries
- create_base_filename method integration and validation
- Data integrity verification across checkpoint integrations
- Filename consistency across all workflow paths
"""

import pytest
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch
from datetime import datetime

# Import the components to test
from src.pacer.components.data_merging.data_merger import PacerDataMerger
from src.pacer.components.validation.filename_validator import FilenameValidator
from src.pacer.services.file_operations_service import FileOperationsService


class TestPacerDataMerger:
    """Test comprehensive data merging functionality."""

    @pytest.fixture
    async def data_merger(self):
        """Create data merger instance for testing."""
        merger = PacerDataMerger()
        await merger.initialize()
        return merger

    @pytest.fixture
    def sample_case_data(self):
        """Sample case data for testing."""
        return {
            'court_id': 'ilnd',
            'docket_num': '1:25-cv-09286',
            'versus': '<PERSON> v. <PERSON>',
            'filing_date': '2025-01-15',
            'base_filename': 'ilnd_25_cv_09286_john_doe_v_jane_smith'
        }

    @pytest.fixture
    def sample_html_content(self):
        """Sample HTML content for testing."""
        return """
        <html>
            <body>
                <div class="case-info">
                    <h1>Case Details</h1>
                    <p>Case Number: 1:25-cv-09286</p>
                    <p>Title: John Doe v. Jane Smith</p>
                </div>
            </body>
        </html>
        """

    @pytest.mark.asyncio
    async def test_merge_html_content_success(self, data_merger, sample_case_data, sample_html_content):
        """Test successful HTML content merging."""
        result = await data_merger.merge_html_content(
            sample_case_data, 
            sample_html_content,
            {'source': 'pacer_page', 'extracted_timestamp': datetime.now().isoformat()}
        )
        
        # Verify HTML content is merged
        assert 'html_content' in result
        assert result['html_content'] == sample_html_content
        assert result['html_content_size'] == len(sample_html_content)
        assert 'html_content_hash' in result
        assert result['html_content_truncated'] is False
        
        # Verify metadata
        assert '_html_merged_timestamp' in result
        assert result['_html_merge_status'] == 'success'
        assert result['_data_merge_version'] == '1.0'

    @pytest.mark.asyncio
    async def test_merge_html_content_large_html(self, data_merger, sample_case_data):
        """Test HTML content merging with large HTML that gets truncated."""
        large_html = "<html>" + "x" * 5000 + "</html>"
        
        result = await data_merger.merge_html_content(sample_case_data, large_html)
        
        # Verify truncation logic
        assert result['html_content'] == large_html
        assert result['html_content_size'] == len(large_html)
        assert result['html_content_truncated'] is True
        assert len(result['html_content_preview']) <= 2003  # 2000 + "...[TRUNCATED]"

    @pytest.mark.asyncio
    async def test_merge_checkpoint_data_preserve_primary(self, data_merger):
        """Test checkpoint data merging with preserve_primary strategy."""
        primary_data = {
            'court_id': 'ilnd',
            'docket_num': '1:25-cv-09286',
            'versus': 'Original vs Title'
        }
        
        checkpoint_data = {
            'versus': 'Updated vs Title',  # Should not override
            'filing_date': '2025-01-15',   # Should be added
            'new_field': 'new_value'       # Should be added
        }
        
        result = await data_merger.merge_checkpoint_data(
            primary_data, checkpoint_data, 'preserve_primary'
        )
        
        assert result['versus'] == 'Original vs Title'  # Primary preserved
        assert result['filing_date'] == '2025-01-15'     # Checkpoint added
        assert result['new_field'] == 'new_value'        # Checkpoint added
        assert '_checkpoint_merged_timestamp' in result

    @pytest.mark.asyncio
    async def test_create_merged_record_comprehensive(self, data_merger, sample_case_data, sample_html_content):
        """Test comprehensive merged record creation."""
        s3_metadata = {
            's3_key': 'test/path/file.html',
            'upload_status': 'success'
        }
        
        processing_metadata = {
            'processor': 'test_processor',
            'version': '1.0'
        }
        
        result = await data_merger.create_merged_record(
            case_data=sample_case_data,
            html_content=sample_html_content,
            s3_metadata=s3_metadata,
            processing_metadata=processing_metadata
        )
        
        # Verify all data is merged
        assert 'html_content' in result
        assert 's3_upload' in result
        assert result['s3_upload'] == s3_metadata
        assert 'processing_metadata' in result
        assert result['processing_metadata'] == processing_metadata
        assert result['_record_merge_complete'] is True

    @pytest.mark.asyncio
    async def test_validate_data_integrity_success(self, data_merger, sample_case_data):
        """Test successful data integrity validation."""
        result = await data_merger.validate_data_integrity(sample_case_data)
        
        assert result['is_valid'] is True
        assert len(result['issues']) == 0
        assert result['fields_validated'] == len(sample_case_data)

    @pytest.mark.asyncio
    async def test_validate_data_integrity_missing_fields(self, data_merger):
        """Test data integrity validation with missing required fields."""
        incomplete_data = {
            'court_id': 'ilnd'
            # Missing docket_num
        }
        
        result = await data_merger.validate_data_integrity(incomplete_data)
        
        assert result['is_valid'] is False
        assert any('Missing required field: docket_num' in issue for issue in result['issues'])

    @pytest.mark.asyncio
    async def test_validate_data_integrity_html_hash_mismatch(self, data_merger):
        """Test HTML content hash validation."""
        case_data = {
            'court_id': 'ilnd',
            'docket_num': '1:25-cv-09286',
            'html_content': 'test content',
            'html_content_hash': 'wrong_hash'
        }
        
        result = await data_merger.validate_data_integrity(case_data)
        
        assert result['is_valid'] is False
        assert any('HTML content hash mismatch' in issue for issue in result['issues'])


class TestFilenameValidator:
    """Test comprehensive filename validation functionality."""

    @pytest.fixture
    async def filename_validator(self):
        """Create filename validator instance for testing."""
        validator = FilenameValidator()
        await validator.initialize()
        return validator

    @pytest.fixture
    def sample_case_data(self):
        """Sample case data for filename validation."""
        return {
            'court_id': 'ilnd',
            'docket_num': '1:25-cv-09286',
            'versus': 'John Doe v. Jane Smith',
            'base_filename': 'ilnd_25_cv_09286_john_doe_v_jane_smith'
        }

    @pytest.mark.asyncio
    async def test_validate_filename_success(self, filename_validator, sample_case_data):
        """Test successful filename validation."""
        result = await filename_validator.validate_filename(
            sample_case_data, 
            'ilnd_25_cv_09286_john_doe_v_jane_smith.json'
        )
        
        assert result['is_valid'] is True
        assert result['needs_correction'] is False
        assert len(result['validation_issues']) == 0
        assert result['filename_source'] == 'explicit'

    @pytest.mark.asyncio
    async def test_validate_filename_forbidden_chars(self, filename_validator, sample_case_data):
        """Test filename validation with forbidden characters."""
        bad_filename = 'ilnd_25_cv_09286_john<doe>v:jane.json'
        
        result = await filename_validator.validate_filename(sample_case_data, bad_filename)
        
        assert result['is_valid'] is False
        assert result['needs_correction'] is True
        assert any('Forbidden characters found' in issue for issue in result['validation_issues'])
        assert '<' not in result['corrected_filename']
        assert '>' not in result['corrected_filename']
        assert ':' not in result['corrected_filename']

    @pytest.mark.asyncio
    async def test_validate_filename_too_long(self, filename_validator, sample_case_data):
        """Test filename validation with excessive length."""
        long_filename = 'x' * 250 + '.json'
        
        result = await filename_validator.validate_filename(sample_case_data, long_filename)
        
        assert result['is_valid'] is False
        assert result['needs_correction'] is True
        assert any('Filename too long' in issue for issue in result['validation_issues'])
        assert len(result['corrected_filename']) <= 200

    @pytest.mark.asyncio
    async def test_generate_validated_filename_from_base(self, filename_validator, sample_case_data):
        """Test generating validated filename using base_filename logic."""
        filename = await filename_validator.generate_validated_filename(sample_case_data)
        
        assert filename.endswith('.json')
        assert 'ilnd' in filename.lower()
        assert '25_cv_09286' in filename

    @pytest.mark.asyncio
    async def test_validate_batch_filenames(self, filename_validator):
        """Test batch filename validation."""
        case_data_list = [
            {
                'court_id': 'ilnd',
                'docket_num': '1:25-cv-09286',
                'base_filename': 'good_filename'
            },
            {
                'court_id': 'nysd',
                'docket_num': '1:25-cv-12345',
                'base_filename': 'bad<filename>with:forbidden'
            }
        ]
        
        result = await filename_validator.validate_batch_filenames(case_data_list)
        
        assert result['summary']['total_cases'] == 2
        assert result['summary']['valid_filenames'] == 1
        assert result['summary']['invalid_filenames'] == 1
        assert result['summary']['corrections_made'] == 1

    @pytest.mark.asyncio
    async def test_generate_base_filename_consistency(self, filename_validator):
        """Test base filename generation consistency with CaseTransformer logic."""
        case_data = {
            'court_id': 'ILND',  # Test case conversion
            'docket_num': '1:25-cv-09286',  # Test colon removal
            'versus': 'John Doe & Co. v. Jane Smith, Inc.'  # Test special char handling
        }
        
        base_filename = await filename_validator._generate_base_filename(case_data)
        
        assert base_filename.startswith('ilnd_')
        assert '25_cv_09286' in base_filename
        assert ':' not in base_filename
        assert '&' not in base_filename
        assert ',' not in base_filename
        assert len(base_filename) <= 200


class TestEnhancedFileOperationsService:
    """Test enhanced file operations service integration."""

    @pytest.fixture
    async def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    @pytest.fixture
    async def file_ops_service(self, temp_dir):
        """Create enhanced file operations service for testing."""
        config = {
            'file_operations': {
                'storage': {
                    'base_directory': str(temp_dir)
                }
            }
        }
        
        service = FileOperationsService(config=config)
        await service.initialize()
        return service

    @pytest.fixture
    def sample_case_data(self):
        """Sample case data for file operations."""
        return {
            'court_id': 'ilnd',
            'docket_num': '1:25-cv-09286',
            'versus': 'John Doe v. Jane Smith',
            'filing_date': '2025-01-15',
            'base_filename': 'ilnd_25_cv_09286_john_doe_v_jane_smith'
        }

    @pytest.mark.asyncio
    async def test_save_case_data_with_html_merging(self, file_ops_service, sample_case_data, temp_dir):
        """Test enhanced save_case_data with HTML merging."""
        html_content = '<html><body>Test HTML</body></html>'
        s3_metadata = {'s3_key': 'test/file.html', 'status': 'uploaded'}
        
        file_path = await file_ops_service.save_case_data(
            case_data=sample_case_data,
            iso_date='20250115',
            html_content=html_content,
            s3_metadata=s3_metadata
        )
        
        # Verify file was created
        assert os.path.exists(file_path)
        
        # Verify content includes merged data
        with open(file_path, 'r') as f:
            saved_data = json.load(f)
        
        assert saved_data['html_content'] == html_content
        assert 's3_upload' in saved_data
        assert saved_data['s3_upload'] == s3_metadata
        assert saved_data['_saved_by'] == 'EnhancedFileOperationsService'
        assert saved_data['_save_version'] == '2.0'

    @pytest.mark.asyncio
    async def test_save_case_data_filename_validation(self, file_ops_service, temp_dir):
        """Test filename validation during save operation."""
        case_data = {
            'court_id': 'ilnd',
            'docket_num': '1:25-cv-09286',
            'versus': 'Bad<filename>with:forbidden!chars',
            'base_filename': 'bad<filename>with:forbidden!chars'
        }
        
        file_path = await file_ops_service.save_case_data(case_data, '20250115')
        
        # Verify file was created with corrected filename
        assert os.path.exists(file_path)
        
        # Verify filename doesn't contain forbidden characters
        filename = os.path.basename(file_path)
        assert '<' not in filename
        assert '>' not in filename
        assert ':' not in filename
        assert '!' not in filename

    @pytest.mark.asyncio
    async def test_save_case_data_backward_compatibility(self, file_ops_service, sample_case_data):
        """Test backward compatibility with save_case_data_to_json method."""
        file_path = await file_ops_service.save_case_data_to_json(
            sample_case_data, 
            'ilnd', 
            '20250115'
        )
        
        assert os.path.exists(file_path)
        
        with open(file_path, 'r') as f:
            saved_data = json.load(f)
        
        assert saved_data['court_id'] == 'ilnd'
        assert saved_data['_enhanced_save_version'] == '2.0'

    @pytest.mark.asyncio
    async def test_get_save_statistics(self, file_ops_service, sample_case_data):
        """Test enhanced save statistics collection."""
        # Perform some saves to generate statistics
        await file_ops_service.save_case_data(sample_case_data, '20250115')
        await file_ops_service.save_case_data(sample_case_data, '20250115', '<html>content</html>')
        
        stats = await file_ops_service.get_save_statistics()
        
        assert stats['total_saves'] == 2
        assert stats['merged_saves'] == 2
        assert stats['filename_validations'] == 2
        assert 'merger_statistics' in stats
        assert 'validator_statistics' in stats


class TestDataMergingIntegration:
    """Integration tests for data merging across workflow paths."""

    @pytest.mark.asyncio
    async def test_workflow_orchestrator_integration(self):
        """Test data merging integration with workflow orchestrator."""
        # This would test the actual workflow orchestrator integration
        # Mock the workflow orchestrator and verify enhanced save calls
        
        # Mock workflow orchestrator components
        with patch('src.pacer.components.processing.workflow_orchestrator_new.PacerWorkflowOrchestrator') as mock_orchestrator:
            # Mock file operations service
            mock_file_ops = AsyncMock()
            mock_orchestrator.return_value.file_operations_service = mock_file_ops
            
            # Test data would be processed through the orchestrator
            docket_result = {
                'court_id': 'ilnd',
                'docket_num': '1:25-cv-09286',
                'html_content': '<html>test</html>',
                '_s3_html_key': 'test/file.html'
            }
            
            # Simulate the enhanced save call
            mock_file_ops.save_case_data.return_value = '/path/to/saved/file.json'
            
            # Verify the save call includes HTML content and S3 metadata
            await mock_file_ops.save_case_data(
                case_data=docket_result,
                iso_date='20250115',
                html_content=docket_result['html_content'],
                s3_metadata={'s3_key': docket_result['_s3_html_key']}
            )
            
            mock_file_ops.save_case_data.assert_called_once()
            call_args = mock_file_ops.save_case_data.call_args
            assert call_args[1]['html_content'] == '<html>test</html>'
            assert call_args[1]['s3_metadata']['s3_key'] == 'test/file.html'

    @pytest.mark.asyncio
    async def test_sequential_workflow_integration(self):
        """Test data merging integration with sequential workflow manager."""
        # Mock sequential workflow manager components
        with patch('src.pacer.components.processing.sequential_workflow_manager.FileOperationsService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # Test both early save and complete save scenarios
            test_cases = [
                {
                    'scenario': 'early_save_html_only',
                    'html_only': True,
                    'expected_s3_metadata': {
                        'save_trigger': 'html_only',
                        'processing_step': 'step_7_early_save'
                    }
                },
                {
                    'scenario': 'complete_save',
                    'html_only': False,
                    'expected_s3_metadata': {
                        'save_trigger': 'complete_processing',
                        'processing_step': 'step_7_complete_save'
                    }
                }
            ]
            
            for test_case in test_cases:
                mock_service.reset_mock()
                
                # Simulate the sequential workflow save call
                docket_result = {
                    'court_id': 'ilnd',
                    'docket_num': '1:25-cv-09286',
                    'html_content': '<html>test</html>'
                }
                
                # This would be called by the sequential workflow manager
                await mock_service.save_case_data(
                    case_data=docket_result,
                    iso_date='20250115',
                    html_content=docket_result['html_content'],
                    s3_metadata=test_case['expected_s3_metadata']
                )
                
                # Verify the call was made with correct parameters
                mock_service.save_case_data.assert_called_once()
                call_args = mock_service.save_case_data.call_args
                assert call_args[1]['case_data'] == docket_result
                assert call_args[1]['html_content'] == '<html>test</html>'
                assert call_args[1]['s3_metadata']['save_trigger'] == test_case['expected_s3_metadata']['save_trigger']


if __name__ == '__main__':
    pytest.main([__file__, '-v'])