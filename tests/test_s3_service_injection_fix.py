"""
Test S3 Service Injection Fix

This test validates that S3 services are properly injected into HTML processing components
and that HTML uploads work correctly through the dependency injection system.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../src')))

from src.containers.storage import StorageContainer
from src.containers.pacer import PacerContainer
from src.services.html.html_processing_orchestrator import HTMLProcessingOrchestrator
from src.services.html.enhanced_html_processing_orchestrator import EnhancedHTMLProcessingOrchestrator
from src.services.html.html_service_factory import HtmlServiceFactory
from src.pacer.utils.s3_injection_helper import S3InjectionHelper


class TestS3ServiceInjectionFix:
    """Test suite for S3 service injection fixes."""
    
    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger for testing."""
        logger = Mock()
        logger.info = Mock()
        logger.warning = Mock()
        logger.error = Mock()
        logger.debug = Mock()
        return logger
    
    @pytest.fixture
    def mock_s3_async_storage(self):
        """Create a mock S3AsyncStorage for testing."""
        s3_storage = Mock()
        s3_storage.upload_content = AsyncMock(return_value=True)
        s3_storage.file_exists = AsyncMock(return_value=True)
        s3_storage.bucket_name = 'test-bucket'
        return s3_storage
    
    @pytest.fixture
    def mock_s3_manager(self, mock_s3_async_storage):
        """Create a mock S3Manager for testing."""
        s3_manager = Mock()
        s3_manager.s3_async_storage = mock_s3_async_storage
        s3_manager.execute = AsyncMock(return_value={
            'success': True,
            's3_key': 'test/html/test.html',
            's3_html': 'https://cdn.lexgenius.ai/test/html/test.html'
        })
        return s3_manager
    
    def test_s3_injection_helper_create_bundle(self, mock_s3_async_storage, mock_logger):
        """Test S3InjectionHelper.create_s3_service_bundle."""
        bundle = S3InjectionHelper.create_s3_service_bundle(mock_s3_async_storage, mock_logger)
        
        assert bundle['s3_async_storage'] == mock_s3_async_storage
        assert bundle['s3_service'] == mock_s3_async_storage
        assert bundle['logger'] == mock_logger
        assert bundle['enabled'] is True
        assert bundle['bucket_name'] == 'test-bucket'
    
    def test_s3_injection_helper_verify_injection(self, mock_s3_async_storage, mock_logger):
        """Test S3InjectionHelper.verify_s3_injection."""
        # Test component with S3 injection
        component_with_s3 = Mock()
        component_with_s3.s3_async_storage = mock_s3_async_storage
        
        assert S3InjectionHelper.verify_s3_injection(component_with_s3, mock_logger) is True
        
        # Test component without S3 injection
        component_without_s3 = Mock()
        component_without_s3.s3_async_storage = None
        
        assert S3InjectionHelper.verify_s3_injection(component_without_s3, mock_logger) is False
    
    def test_html_service_factory_with_s3(self, mock_logger, mock_s3_async_storage):
        """Test HtmlServiceFactory creates orchestrator with S3 injection."""
        orchestrator = HtmlServiceFactory.create_html_processing_orchestrator(
            logger=mock_logger,
            config={},
            court_id='test_court',
            html_data_updater=None,
            s3_async_storage=mock_s3_async_storage
        )
        
        assert isinstance(orchestrator, HTMLProcessingOrchestrator)
        assert orchestrator.s3_async_storage == mock_s3_async_storage
        assert orchestrator.court_id == 'test_court'
    
    def test_enhanced_html_processing_orchestrator_initialization(self, mock_logger, mock_s3_async_storage, mock_s3_manager):
        """Test EnhancedHTMLProcessingOrchestrator initialization with various S3 configurations."""
        # Test with direct S3AsyncStorage
        orchestrator1 = EnhancedHTMLProcessingOrchestrator(
            logger=mock_logger,
            config={},
            court_id='test_court',
            s3_async_storage=mock_s3_async_storage
        )
        assert orchestrator1.s3_async_storage == mock_s3_async_storage
        
        # Test with S3Manager
        orchestrator2 = EnhancedHTMLProcessingOrchestrator(
            logger=mock_logger,
            config={},
            court_id='test_court',
            s3_manager=mock_s3_manager
        )
        assert orchestrator2.s3_manager == mock_s3_manager
        assert orchestrator2.s3_async_storage == mock_s3_async_storage  # Should be extracted
    
    @pytest.mark.asyncio
    async def test_enhanced_html_processing_s3_upload(self, mock_logger, mock_s3_async_storage):
        """Test enhanced HTML processing S3 upload functionality."""
        orchestrator = EnhancedHTMLProcessingOrchestrator(
            logger=mock_logger,
            config={'iso_date': '20250812'},
            court_id='test_court',
            s3_async_storage=mock_s3_async_storage
        )
        
        case_details = {
            'court_id': 'test_court',
            'docket_num': '1:23-cv-12345',
            'new_filename': 'test_case.json'
        }
        
        html_content = '<html><body>Test HTML</body></html>'
        json_path = '/data/20250812/dockets/test_case.json'
        
        result = await orchestrator.process_html_content(case_details, html_content, json_path)
        
        # Verify S3 upload was called
        mock_s3_async_storage.upload_content.assert_called_once()
        
        # Verify S3 information was added to case details
        assert 's3_html_key' in result or 's3_html' in result
    
    @pytest.mark.asyncio
    async def test_enhanced_html_processing_fallback_upload(self, mock_logger, mock_s3_manager):
        """Test enhanced HTML processing with S3Manager fallback."""
        orchestrator = EnhancedHTMLProcessingOrchestrator(
            logger=mock_logger,
            config={'iso_date': '20250812'},
            court_id='test_court',
            s3_manager=mock_s3_manager
        )
        
        case_details = {
            'court_id': 'test_court',
            'docket_num': '1:23-cv-12345',
            'base_filename': 'test_case'
        }
        
        html_content = '<html><body>Test HTML</body></html>'
        json_path = '/data/20250812/dockets/test_case.json'
        
        result = await orchestrator.process_html_content(case_details, html_content, json_path)
        
        # Verify S3Manager execute was called
        mock_s3_manager.execute.assert_called()
        
        # Verify result contains S3 information
        assert result is not None
    
    def test_enhanced_html_processing_s3_status(self, mock_logger, mock_s3_async_storage, mock_s3_manager):
        """Test enhanced HTML processing S3 status reporting."""
        orchestrator = EnhancedHTMLProcessingOrchestrator(
            logger=mock_logger,
            config={},
            court_id='test_court',
            s3_async_storage=mock_s3_async_storage,
            s3_manager=mock_s3_manager
        )
        
        status = orchestrator.get_s3_status()
        
        assert status['has_s3_async_storage'] is True
        assert status['has_s3_manager'] is True
        assert status['court_id'] == 'test_court'
        assert status['bucket_name'] == 'test-bucket'
        assert status['validation_passed'] is True
    
    def test_storage_container_s3_bundle_creation(self, mock_logger):
        """Test StorageContainer S3 service bundle creation."""
        # This would require mocking the entire container system
        # For now, we test the concept
        mock_s3_storage = Mock()
        mock_s3_storage.bucket_name = 'test-bucket'
        
        # Test the factory function concept
        s3_bundle = S3InjectionHelper.create_s3_service_bundle(mock_s3_storage, mock_logger)
        html_orchestrator = S3InjectionHelper.create_html_processing_orchestrator_with_s3(
            mock_s3_storage, mock_logger, {}, 'test_court'
        )
        
        assert s3_bundle['enabled'] is True
        assert isinstance(html_orchestrator, HTMLProcessingOrchestrator)
        assert html_orchestrator.s3_async_storage == mock_s3_storage


if __name__ == "__main__":
    pytest.main([__file__, "-v"])