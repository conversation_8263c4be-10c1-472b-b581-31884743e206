"""Test fixtures and utilities for S3 HTML upload testing.

Provides:
1. Mock S3 clients and services
2. Test data generators
3. Configuration fixtures
4. Helper utilities
"""

import pytest
import tempfile
import os
from datetime import datetime
from typing import Dict, Any, List
from unittest.mock import MagicMock, AsyncMock
from pathlib import Path

from src.pacer.services.s3_service import S3Service
from src.pacer._core_services.s3_management.s3_management_service import S3ManagementService


class S3TestFixtures:
    """Collection of S3 test fixtures and utilities."""
    
    @staticmethod
    def create_mock_s3_client():
        """Create a mock S3 client with common methods."""
        client = MagicMock()
        
        # Setup default behaviors
        client.upload_file.return_value = None
        client.download_file.return_value = None
        client.delete_object.return_value = {}
        client.head_object.return_value = {
            'ContentLength': 1024,
            'LastModified': datetime.now(),
            'ContentType': 'text/html',
            'Metadata': {},
            'ETag': '"abc123"'
        }
        client.list_objects_v2.return_value = {'Contents': []}
        client.get_bucket_location.return_value = {'LocationConstraint': 'us-west-2'}
        client.generate_presigned_url.return_value = 'https://example.com/presigned'
        
        return client
    
    @staticmethod
    def create_mock_s3_service(enabled=True, bucket_name='test-bucket'):
        """Create a mock S3Service with default configuration."""
        service = S3Service()
        service.enabled = enabled
        service.bucket_name = bucket_name
        service.aws_region = 'us-west-2'
        service.aws_access_key_id = 'test-key'
        service.aws_secret_access_key = 'test-secret'
        service._initialized = enabled
        
        if enabled:
            service.s3_client = S3TestFixtures.create_mock_s3_client()
        else:
            service.s3_client = None
        
        # Mock logging methods
        service.log_info = MagicMock()
        service.log_warning = MagicMock()
        service.log_error = MagicMock()
        service.log_debug = MagicMock()
        
        return service
    
    @staticmethod
    def create_mock_s3_management_service(enabled=True):
        """Create a mock S3ManagementService."""
        service = S3ManagementService()
        service._s3_service = S3TestFixtures.create_mock_s3_service(enabled=enabled)
        service._initialized = True
        service.log_info = MagicMock()
        service.log_warning = MagicMock()
        service.log_error = MagicMock()
        return service
    
    @staticmethod
    def create_sample_html_content(case_name='Test v. Example', case_number='1:23-cv-00001'):
        """Generate sample HTML content for testing."""
        return f'''
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>PACER Docket Sheet - {case_name}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 10px; }}
                .case-info {{ margin: 20px 0; }}
                .docket-entries {{ margin-top: 30px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>PACER Docket Sheet</h1>
                <p>U.S. District Court - Test District</p>
            </div>
            
            <div class="case-info">
                <h2>{case_name}</h2>
                <p><strong>Case Number:</strong> {case_number}</p>
                <p><strong>Filing Date:</strong> {datetime.now().strftime('%m/%d/%Y')}</p>
                <p><strong>Nature of Suit:</strong> 890 Other Statutory Actions</p>
            </div>
            
            <div class="docket-entries">
                <h3>Docket Entries</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Date Filed</th>
                            <th>Entry</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{datetime.now().strftime('%m/%d/%Y')}</td>
                            <td>1</td>
                            <td>COMPLAINT filed by Test Plaintiff</td>
                        </tr>
                        <tr>
                            <td>{datetime.now().strftime('%m/%d/%Y')}</td>
                            <td>2</td>
                            <td>SUMMONS issued</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </body>
        </html>
        '''.strip()
    
    @staticmethod
    def create_large_html_content(size_mb=1):
        """Generate large HTML content for performance testing."""
        base_content = S3TestFixtures.create_sample_html_content()
        
        # Calculate how much padding needed to reach target size
        target_size = size_mb * 1024 * 1024
        current_size = len(base_content.encode('utf-8'))
        padding_size = max(0, target_size - current_size - 100)  # Leave room for closing tags
        
        padding = 'X' * padding_size
        
        # Insert padding before closing body tag
        return base_content.replace(
            '</body>',
            f'<div class="padding" style="display:none;">{padding}</div></body>'
        )
    
    @staticmethod
    def create_html_with_special_characters():
        """Generate HTML content with special characters and encoding challenges."""
        return '''
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <title>Special Characters Test - äöüß ñ 中文 🎉</title>
        </head>
        <body>
            <h1>Test Case with Special Characters</h1>
            <p>German: äöüß ÄÖÜ</p>
            <p>Spanish: ñ áéíóú</p>
            <p>Chinese: 中文测试</p>
            <p>Emojis: 🎉 🚀 ⚖️</p>
            <p>Quotes: "Double" 'Single' “Smart” ‘Smart’</p>
            <p>Math: αβγ ∈ ≡ ∞</p>
            <p>JSON-like: {"key": "value", "number": 123}</p>
            <p>HTML entities: &lt;script&gt;alert('test');&lt;/script&gt;</p>
            <script type="application/json">
            {
                "case": {
                    "name": "Test v. Example",
                    "special": "中文🎉"
                }
            }
            </script>
        </body>
        </html>
        '''.strip()
    
    @staticmethod
    def create_test_configurations():
        """Create various test configurations for S3 service."""
        return {
            'enabled_with_credentials': {
                'S3_ENABLED': 'true',
                'S3_BUCKET_NAME': 'test-bucket',
                'AWS_ACCESS_KEY_ID': 'test-key',
                'AWS_SECRET_ACCESS_KEY': 'test-secret',
                'AWS_REGION': 'us-west-2'
            },
            'enabled_without_credentials': {
                'S3_ENABLED': 'true',
                'S3_BUCKET_NAME': 'test-bucket',
                'AWS_REGION': 'us-west-2'
                # Missing credentials
            },
            'disabled': {
                'S3_ENABLED': 'false',
                'S3_BUCKET_NAME': 'test-bucket',
                'AWS_ACCESS_KEY_ID': 'test-key',
                'AWS_SECRET_ACCESS_KEY': 'test-secret'
            },
            'missing_bucket': {
                'S3_ENABLED': 'true',
                'AWS_ACCESS_KEY_ID': 'test-key',
                'AWS_SECRET_ACCESS_KEY': 'test-secret',
                'AWS_REGION': 'us-west-2'
                # Missing bucket name
            }
        }
    
    @staticmethod
    def create_mock_config_service(s3_config=None):
        """Create a mock configuration service."""
        if s3_config is None:
            s3_config = {
                'bucket_name': 'test-bucket',
                'region': 'us-west-2',
                'enabled': True
            }
        
        service = AsyncMock()
        service.get_config_value.return_value = s3_config
        return service
    
    @staticmethod
    def create_workflow_test_data():
        """Create test data for workflow testing."""
        return {
            'court_cases': [
                {
                    'court_id': 'cand',
                    'case_number': '1:23-cv-00001',
                    'case_name': 'Tech Corp v. Innovation LLC',
                    'filing_date': '2023-01-15',
                    'iso_date': '2023-01-15'
                },
                {
                    'court_id': 'nysd',
                    'case_number': '1:23-cv-00002',
                    'case_name': 'Finance Inc v. Banking Co',
                    'filing_date': '2023-01-16',
                    'iso_date': '2023-01-16'
                },
                {
                    'court_id': 'txnd',
                    'case_number': '3:23-cv-00003',
                    'case_name': 'Energy Solutions v. Green Power',
                    'filing_date': '2023-01-17',
                    'iso_date': '2023-01-17'
                }
            ],
            'expected_s3_keys': [
                '2023-01-15/html/cand_1_23_cv_00001.html',
                '2023-01-16/html/nysd_1_23_cv_00002.html',
                '2023-01-17/html/txnd_3_23_cv_00003.html'
            ],
            'expected_cdn_urls': [
                'https://cdn.lexgenius.ai/2023-01-15/html/cand_1_23_cv_00001.html',
                'https://cdn.lexgenius.ai/2023-01-16/html/nysd_1_23_cv_00002.html',
                'https://cdn.lexgenius.ai/2023-01-17/html/txnd_3_23_cv_00003.html'
            ]
        }
    
    @staticmethod
    def create_temporary_html_file(content=None):
        """Create a temporary HTML file for testing."""
        if content is None:
            content = S3TestFixtures.create_sample_html_content()
        
        temp_file = tempfile.NamedTemporaryFile(
            mode='w',
            suffix='.html',
            delete=False,
            encoding='utf-8'
        )
        
        temp_file.write(content)
        temp_file.close()
        
        return temp_file.name
    
    @staticmethod
    def cleanup_temporary_file(file_path):
        """Clean up a temporary file."""
        try:
            os.unlink(file_path)
        except (OSError, FileNotFoundError):
            pass  # File already deleted or doesn't exist
    
    @staticmethod
    def create_error_scenarios():
        """Create various error scenarios for testing."""
        return {
            'network_timeout': Exception('Request timeout after 30 seconds'),
            'access_denied': Exception('Access Denied'),
            'no_such_bucket': Exception('The specified bucket does not exist'),
            'invalid_credentials': Exception('The AWS Access Key Id you provided does not exist'),
            'rate_limited': Exception('SlowDown'),
            'service_unavailable': Exception('Service Unavailable'),
            'file_not_found': FileNotFoundError('No such file or directory'),
            'permission_denied': PermissionError('Permission denied'),
            'disk_full': OSError('No space left on device')
        }
    
    @staticmethod
    def create_mock_logger():
        """Create a mock logger for testing."""
        logger = MagicMock()
        logger.info = MagicMock()
        logger.warning = MagicMock()
        logger.error = MagicMock()
        logger.debug = MagicMock()
        return logger
    
    @staticmethod
    def assert_s3_upload_called_correctly(mock_client, expected_bucket, expected_key, expected_file_path=None):
        """Helper to assert S3 upload was called with correct parameters."""
        mock_client.upload_file.assert_called()
        call_args = mock_client.upload_file.call_args
        
        # Check the call arguments
        assert call_args.kwargs['Bucket'] == expected_bucket
        assert call_args.kwargs['Key'] == expected_key
        
        if expected_file_path:
            assert call_args.kwargs['Filename'] == expected_file_path
    
    @staticmethod
    def assert_s3_result_structure(result, success=True):
        """Helper to assert S3 operation result has correct structure."""
        assert isinstance(result, dict)
        assert 'success' in result
        assert result['success'] == success
        
        if success:
            if 'upload_html' in str(result):
                assert 's3_key' in result
                assert 's3_html' in result
                assert 's3_url' in result
        else:
            assert 'error' in result
    
    @staticmethod
    def create_performance_test_data(num_files=10):
        """Create test data for performance testing."""
        test_files = []
        
        for i in range(num_files):
            test_files.append({
                'base_filename': f'test_file_{i:03d}',
                'html_content': S3TestFixtures.create_sample_html_content(
                    case_name=f'Case {i+1} v. Defendant {i+1}',
                    case_number=f'1:23-cv-{i+1:05d}'
                ),
                'iso_date': f'2023-01-{(i % 28) + 1:02d}',
                'expected_s3_key': f'2023-01-{(i % 28) + 1:02d}/html/test_file_{i:03d}.html'
            })
        
        return test_files


# Pytest fixtures
@pytest.fixture
def mock_s3_client():
    """Pytest fixture for mock S3 client."""
    return S3TestFixtures.create_mock_s3_client()


@pytest.fixture
def mock_s3_service():
    """Pytest fixture for mock S3 service."""
    return S3TestFixtures.create_mock_s3_service()


@pytest.fixture
def disabled_s3_service():
    """Pytest fixture for disabled S3 service."""
    return S3TestFixtures.create_mock_s3_service(enabled=False)


@pytest.fixture
def mock_s3_management_service():
    """Pytest fixture for mock S3 management service."""
    return S3TestFixtures.create_mock_s3_management_service()


@pytest.fixture
def sample_html_content():
    """Pytest fixture for sample HTML content."""
    return S3TestFixtures.create_sample_html_content()


@pytest.fixture
def large_html_content():
    """Pytest fixture for large HTML content."""
    return S3TestFixtures.create_large_html_content()


@pytest.fixture
def special_chars_html_content():
    """Pytest fixture for HTML content with special characters."""
    return S3TestFixtures.create_html_with_special_characters()


@pytest.fixture
def s3_test_configurations():
    """Pytest fixture for S3 test configurations."""
    return S3TestFixtures.create_test_configurations()


@pytest.fixture
def mock_config_service():
    """Pytest fixture for mock configuration service."""
    return S3TestFixtures.create_mock_config_service()


@pytest.fixture
def workflow_test_data():
    """Pytest fixture for workflow test data."""
    return S3TestFixtures.create_workflow_test_data()


@pytest.fixture
def error_scenarios():
    """Pytest fixture for error scenarios."""
    return S3TestFixtures.create_error_scenarios()


@pytest.fixture
def mock_logger():
    """Pytest fixture for mock logger."""
    return S3TestFixtures.create_mock_logger()


@pytest.fixture
def performance_test_data():
    """Pytest fixture for performance test data."""
    return S3TestFixtures.create_performance_test_data()


@pytest.fixture
def temporary_html_file(sample_html_content):
    """Pytest fixture for temporary HTML file."""
    file_path = S3TestFixtures.create_temporary_html_file(sample_html_content)
    yield file_path
    S3TestFixtures.cleanup_temporary_file(file_path)


class S3TestHelpers:
    """Helper methods for S3 testing."""
    
    @staticmethod
    def validate_s3_url_format(url, bucket_name, s3_key, region='us-west-2'):
        """Validate S3 URL format."""
        expected_url = f"https://{bucket_name}.s3.{region}.amazonaws.com/{s3_key}"
        return url == expected_url
    
    @staticmethod
    def validate_cdn_url_format(url, s3_key):
        """Validate CDN URL format."""
        expected_url = f"https://cdn.lexgenius.ai/{s3_key}"
        return url == expected_url
    
    @staticmethod
    def create_base_filename(court_id, case_number):
        """Create base filename from court ID and case number."""
        # Sanitize case number for filename
        safe_case_number = case_number.replace(':', '_').replace('-', '_')
        return f"{court_id}_{safe_case_number}"
    
    @staticmethod
    def extract_s3_key_components(s3_key):
        """Extract components from S3 key."""
        parts = s3_key.split('/')
        if len(parts) >= 3:
            return {
                'iso_date': parts[0],
                'category': parts[1],
                'filename': parts[2]
            }
        return None
    
    @staticmethod
    def simulate_retry_scenario(mock_method, success_after_attempts=3):
        """Simulate a retry scenario where operation succeeds after N attempts."""
        side_effects = []
        
        # Add failures for first N-1 attempts
        for i in range(success_after_attempts - 1):
            side_effects.append(Exception(f'Temporary failure {i+1}'))
        
        # Add success for final attempt
        side_effects.append(None)  # Success (no exception)
        
        mock_method.side_effect = side_effects
        return side_effects


if __name__ == '__main__':
    # Example usage
    fixtures = S3TestFixtures()
    service = fixtures.create_mock_s3_service()
    html_content = fixtures.create_sample_html_content()
    
    print(f"Mock S3 Service enabled: {service.enabled}")
    print(f"HTML content length: {len(html_content)} chars")
    print("S3 test fixtures ready for use!")
