#!/usr/bin/env python3
"""Test runner for S3 HTML upload functionality validation.

Executes comprehensive test suite to validate:
1. S3 service dependency injection
2. HTML upload functionality
3. Error handling scenarios
4. Integration workflows
5. Performance scenarios
"""

import asyncio
import sys
import pytest
import os
from pathlib import Path
import subprocess
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class S3TestRunner:
    """Test runner for S3 HTML upload validation."""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {}
        self.start_time = None
        self.end_time = None
    
    def setup_test_environment(self):
        """Setup test environment and dependencies."""
        print("\n" + "="*80)
        print("🚀 S3 HTML UPLOAD FUNCTIONALITY VALIDATION")
        print("="*80)
        print(f"📁 Project Root: {self.project_root}")
        print(f"🕐 Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n📋 Test Scope:")
        print("   ✓ S3 service dependency injection validation")
        print("   ✓ HTML upload functionality with S3 service")
        print("   ✓ Error handling for missing S3 configuration")
        print("   ✓ End-to-end HTML upload workflow")
        print("   ✓ Performance and load testing")
        print("   ✓ Unit tests for individual components")
        
        # Check if required test files exist
        test_files = [
            'tests/integration/test_s3_html_upload_validation.py',
            'tests/unit/test_s3_service_unit_validation.py',
            'tests/fixtures/s3_test_fixtures.py'
        ]
        
        print("\n📂 Validating test files:")
        for test_file in test_files:
            file_path = self.project_root / test_file
            if file_path.exists():
                print(f"   ✅ {test_file}")
            else:
                print(f"   ❌ {test_file} - NOT FOUND")
                return False
        
        # Setup environment variables for testing
        os.environ['PYTHONDONTWRITEBYTECODE'] = '1'
        os.environ['PYTEST_DISABLE_PLUGIN_AUTOLOAD'] = '1'
        
        return True
    
    def run_integration_tests(self):
        """Run integration tests for S3 HTML upload."""
        print("\n" + "-"*60)
        print("🔧 RUNNING INTEGRATION TESTS")
        print("-"*60)
        
        test_file = self.project_root / 'tests/integration/test_s3_html_upload_validation.py'
        
        cmd = [
            sys.executable, '-m', 'pytest',
            str(test_file),
            '-v',
            '--tb=short',
            '--no-header',
            '--disable-warnings',
            '-x'  # Stop on first failure
        ]
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            self.test_results['integration'] = {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'success': result.returncode == 0
            }
            
            if result.returncode == 0:
                print("✅ Integration tests PASSED")
                # Show summary of passed tests
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'PASSED' in line or 'test session starts' in line or 'passed' in line:
                        print(f"   {line}")
            else:
                print("❌ Integration tests FAILED")
                print("\nSTDOUT:")
                print(result.stdout)
                print("\nSTDERR:")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print("⏰ Integration tests TIMED OUT (5 minutes)")
            self.test_results['integration'] = {
                'returncode': -1,
                'success': False,
                'error': 'Timeout after 5 minutes'
            }
        except Exception as e:
            print(f"💥 Integration tests ERROR: {e}")
            self.test_results['integration'] = {
                'returncode': -1,
                'success': False,
                'error': str(e)
            }
    
    def run_unit_tests(self):
        """Run unit tests for S3 service components."""
        print("\n" + "-"*60)
        print("🧪 RUNNING UNIT TESTS")
        print("-"*60)
        
        test_file = self.project_root / 'tests/unit/test_s3_service_unit_validation.py'
        
        cmd = [
            sys.executable, '-m', 'pytest',
            str(test_file),
            '-v',
            '--tb=short',
            '--no-header',
            '--disable-warnings'
        ]
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=180  # 3 minute timeout
            )
            
            self.test_results['unit'] = {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'success': result.returncode == 0
            }
            
            if result.returncode == 0:
                print("✅ Unit tests PASSED")
                # Show summary
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'passed' in line and ('failed' in line or 'error' in line or 'warnings' in line):
                        print(f"   📊 {line}")
            else:
                print("❌ Unit tests FAILED")
                print("\nSTDOUT:")
                print(result.stdout)
                print("\nSTDERR:")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print("⏰ Unit tests TIMED OUT (3 minutes)")
            self.test_results['unit'] = {
                'returncode': -1,
                'success': False,
                'error': 'Timeout after 3 minutes'
            }
        except Exception as e:
            print(f"💥 Unit tests ERROR: {e}")
            self.test_results['unit'] = {
                'returncode': -1,
                'success': False,
                'error': str(e)
            }
    
    def validate_s3_service_import(self):
        """Validate S3 service can be imported successfully."""
        print("\n" + "-"*60)
        print("📦 VALIDATING S3 SERVICE IMPORTS")
        print("-"*60)
        
        import_tests = [
            ('src.pacer.services.s3_service', 'S3Service'),
            ('src.pacer._core_services.s3_management.s3_management_service', 'S3ManagementService'),
            ('src.services.uploader.s3_upload_service', 'S3UploadService'),
            ('src.factories.main_factory', 'MainServiceFactory')
        ]
        
        import_results = []
        
        for module_name, class_name in import_tests:
            try:
                module = __import__(module_name, fromlist=[class_name])
                cls = getattr(module, class_name)
                print(f"   ✅ {module_name}.{class_name}")
                import_results.append(True)
            except Exception as e:
                print(f"   ❌ {module_name}.{class_name} - {e}")
                import_results.append(False)
        
        self.test_results['imports'] = {
            'success': all(import_results),
            'total': len(import_tests),
            'passed': sum(import_results)
        }
        
        if all(import_results):
            print("✅ All S3 service imports successful")
        else:
            print(f"❌ {len(import_tests) - sum(import_results)} import failures")
    
    def run_quick_smoke_test(self):
        """Run a quick smoke test to verify basic functionality."""
        print("\n" + "-"*60)
        print("💨 RUNNING SMOKE TEST")
        print("-"*60)
        
        try:
            # Import and create S3Service
            from src.pacer.services.s3_service import S3Service
            from tests.fixtures.s3_test_fixtures import S3TestFixtures
            
            # Test 1: Service creation
            print("   🧪 Test 1: S3Service creation...")
            service = S3Service()
            assert service is not None
            print("      ✅ S3Service created successfully")
            
            # Test 2: Mock service creation
            print("   🧪 Test 2: Mock S3Service creation...")
            mock_service = S3TestFixtures.create_mock_s3_service()
            assert mock_service.enabled is True
            assert mock_service.bucket_name == 'test-bucket'
            print("      ✅ Mock S3Service created successfully")
            
            # Test 3: HTML content generation
            print("   🧪 Test 3: HTML content generation...")
            html_content = S3TestFixtures.create_sample_html_content()
            assert len(html_content) > 100
            assert 'DOCTYPE html' in html_content
            print(f"      ✅ HTML content generated ({len(html_content)} chars)")
            
            # Test 4: Configuration creation
            print("   🧪 Test 4: Test configuration creation...")
            configs = S3TestFixtures.create_test_configurations()
            assert 'enabled_with_credentials' in configs
            assert 'disabled' in configs
            print(f"      ✅ Test configurations created ({len(configs)} configs)")
            
            self.test_results['smoke'] = {'success': True}
            print("✅ Smoke test PASSED")
            
        except Exception as e:
            print(f"❌ Smoke test FAILED: {e}")
            self.test_results['smoke'] = {
                'success': False,
                'error': str(e)
            }
    
    def generate_test_report(self):
        """Generate comprehensive test report."""
        self.end_time = datetime.now()
        duration = self.end_time - self.start_time
        
        print("\n" + "="*80)
        print("📊 S3 HTML UPLOAD VALIDATION REPORT")
        print("="*80)
        print(f"🕐 Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🕑 End Time: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  Duration: {duration.total_seconds():.1f} seconds")
        
        print("\n📋 TEST RESULTS SUMMARY:")
        
        total_tests = 0
        passed_tests = 0
        
        for test_name, result in self.test_results.items():
            if isinstance(result, dict) and 'success' in result:
                status = "✅ PASS" if result['success'] else "❌ FAIL"
                print(f"   {test_name.upper():<12} {status}")
                
                total_tests += 1
                if result['success']:
                    passed_tests += 1
                    
                if not result['success'] and 'error' in result:
                    print(f"      Error: {result['error']}")
        
        print(f"\n📈 OVERALL RESULTS:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {total_tests - passed_tests}")
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        print(f"   Success Rate: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("\n🎉 ALL TESTS PASSED! S3 HTML upload functionality is validated.")
            print("\n✅ VALIDATION COMPLETE:")
            print("   ✓ S3 service dependency injection working")
            print("   ✓ HTML upload functionality operational")
            print("   ✓ Error handling properly implemented")
            print("   ✓ Integration workflows functioning")
            print("   ✓ Performance scenarios validated")
        elif success_rate >= 80:
            print("\n⚠️  MOSTLY SUCCESSFUL - Some issues detected")
            print("   Review failed tests and address issues")
        else:
            print("\n❌ SIGNIFICANT ISSUES DETECTED")
            print("   Multiple test failures - requires investigation")
        
        return success_rate >= 90  # Consider 90%+ as overall success
    
    def run_all_tests(self):
        """Run complete test suite."""
        self.start_time = datetime.now()
        
        if not self.setup_test_environment():
            print("❌ Test environment setup failed")
            return False
        
        # Run tests in order
        self.validate_s3_service_import()
        self.run_quick_smoke_test()
        self.run_unit_tests()
        self.run_integration_tests()
        
        # Generate final report
        return self.generate_test_report()


def main():
    """Main test runner entry point."""
    runner = S3TestRunner()
    
    try:
        success = runner.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Test execution interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n\n💥 Test runner error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
