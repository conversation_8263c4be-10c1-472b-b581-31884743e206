"""
Complete S3 Service Injection Fix Test

This test validates the complete fix for S3 service injection including:
1. Storage container S3 service creation
2. HTML processing component S3 injection
3. Dependency injection chain validation
4. End-to-end HTML upload functionality
"""

import pytest
import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock, patch

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../src')))

from src.containers.enhanced_storage_container import EnhancedStorageContainer
from src.services.html.html_service_factory_enhanced import EnhancedHtmlServiceFactory
from src.services.html.enhanced_html_processing_orchestrator import EnhancedHTMLProcessingOrchestrator
from src.pacer.utils.s3_injection_helper import S3InjectionHelper


class TestCompleteS3InjectionFix:
    """Test suite for complete S3 service injection fix."""
    
    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger."""
        logger = Mock()
        logger.info = Mock()
        logger.warning = Mock()
        logger.error = Mock()
        logger.debug = Mock()
        logger.name = 'test_logger'
        return logger
    
    @pytest.fixture
    def mock_s3_async_storage(self):
        """Create a mock S3AsyncStorage."""
        s3_storage = Mock()
        s3_storage.upload_content = AsyncMock(return_value=True)
        s3_storage.upload_html_string_async = AsyncMock(return_value=True)
        s3_storage.file_exists = AsyncMock(return_value=True)
        s3_storage.list_existing_files_async = AsyncMock(return_value=[
            '20250812/html/test_case.html'
        ])
        s3_storage.bucket_name = 'test-bucket'
        s3_storage.aws_region = 'us-east-1'
        return s3_storage
    
    @pytest.fixture
    def mock_pacer_repository(self):
        """Create a mock PacerRepository."""
        repo = Mock()
        repo.save = AsyncMock(return_value=True)
        repo.find = AsyncMock(return_value=None)
        return repo
    
    @pytest.fixture
    def test_config(self):
        """Create test configuration."""
        return {
            'iso_date': '20250812',
            's3': {
                'bucket_name': 'test-bucket',
                'region': 'us-east-1'
            }
        }
    
    def test_s3_injection_helper_functionality(self, mock_s3_async_storage, mock_logger):
        """Test S3InjectionHelper core functionality."""
        # Test bundle creation
        bundle = S3InjectionHelper.create_s3_service_bundle(mock_s3_async_storage, mock_logger)
        assert bundle['s3_async_storage'] == mock_s3_async_storage
        assert bundle['enabled'] is True
        assert bundle['bucket_name'] == 'test-bucket'
        
        # Test orchestrator creation
        orchestrator = S3InjectionHelper.create_html_processing_orchestrator_with_s3(
            s3_async_storage=mock_s3_async_storage,
            logger=mock_logger,
            config={'iso_date': '20250812'},
            court_id='test_court'
        )
        
        assert orchestrator.s3_async_storage == mock_s3_async_storage
        assert orchestrator.court_id == 'test_court'
        
        # Test injection verification
        validation_result = S3InjectionHelper.verify_s3_injection(orchestrator, mock_logger)
        assert validation_result is True
    
    def test_enhanced_html_service_factory(self, mock_s3_async_storage, mock_pacer_repository, 
                                          mock_logger, test_config):
        """Test EnhancedHtmlServiceFactory functionality."""
        # Test enhanced orchestrator creation
        orchestrator = EnhancedHtmlServiceFactory.create_enhanced_html_processing_orchestrator(
            logger=mock_logger,
            config=test_config,
            court_id='test_court',
            s3_async_storage=mock_s3_async_storage
        )
        
        assert isinstance(orchestrator, EnhancedHTMLProcessingOrchestrator)
        assert orchestrator.s3_async_storage == mock_s3_async_storage
        
        # Test complete bundle creation
        bundle = EnhancedHtmlServiceFactory.create_html_processing_bundle_with_dependencies(
            logger=mock_logger,
            config=test_config,
            s3_async_storage=mock_s3_async_storage,
            pacer_db=mock_pacer_repository,
            court_id='test_court'
        )
        
        assert 'html_processing_orchestrator' in bundle
        assert 's3_bundle' in bundle
        assert bundle['s3_injection_verified'] is True
        assert bundle['court_id'] == 'test_court'
    
    @pytest.mark.asyncio
    async def test_enhanced_html_processing_orchestrator_upload(self, mock_s3_async_storage, 
                                                               mock_logger, test_config):
        """Test enhanced HTML processing orchestrator S3 upload functionality."""
        orchestrator = EnhancedHTMLProcessingOrchestrator(
            logger=mock_logger,
            config=test_config,
            court_id='test_court',
            s3_async_storage=mock_s3_async_storage
        )
        
        case_details = {
            'court_id': 'test_court',
            'docket_num': '1:23-cv-12345',
            'new_filename': 'test_case.json'
        }
        
        html_content = '<html><body><h1>Test Case</h1><p>Test content</p></body></html>'
        json_path = '/data/20250812/dockets/test_case.json'
        
        # Test processing
        result = await orchestrator.process_html_content(case_details, html_content, json_path)
        
        # Verify S3 upload was attempted
        mock_s3_async_storage.upload_content.assert_called()
        
        # Verify result contains S3 information
        assert result is not None
        assert isinstance(result, dict)
    
    @pytest.mark.asyncio
    async def test_s3_upload_fallback_mechanisms(self, mock_logger, test_config):
        """Test S3 upload fallback mechanisms in enhanced orchestrator."""
        # Create mock S3 manager
        mock_s3_manager = Mock()
        mock_s3_manager.execute = AsyncMock(return_value={
            'success': True,
            's3_key': '20250812/html/test_case.html',
            's3_html': 'https://cdn.lexgenius.ai/20250812/html/test_case.html'
        })
        mock_s3_manager.s3_async_storage = Mock()
        
        orchestrator = EnhancedHTMLProcessingOrchestrator(
            logger=mock_logger,
            config=test_config,
            court_id='test_court',
            s3_manager=mock_s3_manager
        )
        
        case_details = {
            'court_id': 'test_court',
            'docket_num': '1:23-cv-12345',
            'base_filename': 'test_case'
        }
        
        html_content = '<html><body>Test HTML</body></html>'
        json_path = '/data/20250812/dockets/test_case.json'
        
        # Test enhanced upload
        log_prefix = "[test_court][1:23-cv-12345] EnhancedHTMLProcessing:"
        result = await orchestrator._enhanced_s3_upload(case_details, html_content, json_path, log_prefix)
        
        # Verify S3 manager was used
        mock_s3_manager.execute.assert_called()
        assert result is True
    
    def test_enhanced_storage_container_factories(self, mock_logger):
        """Test EnhancedStorageContainer factory methods."""
        # This would require complex mocking, so we test the concepts
        
        # Test S3 injection helper integration
        mock_s3_storage = Mock()
        mock_s3_storage.bucket_name = 'test-bucket'
        
        bundle = S3InjectionHelper.create_s3_service_bundle(mock_s3_storage, mock_logger)
        assert bundle['enabled'] is True
        
        orchestrator = S3InjectionHelper.create_html_processing_orchestrator_with_s3(
            mock_s3_storage, mock_logger, {'iso_date': '20250812'}, 'test_court'
        )
        assert orchestrator.s3_async_storage == mock_s3_storage
    
    @pytest.mark.asyncio
    async def test_end_to_end_html_upload_workflow(self, mock_s3_async_storage, mock_logger, test_config):
        """Test end-to-end HTML upload workflow."""
        # Create enhanced orchestrator
        orchestrator = EnhancedHtmlServiceFactory.create_html_processing_orchestrator_with_s3_injection(
            logger=mock_logger,
            config=test_config,
            s3_async_storage=mock_s3_async_storage,
            court_id='cand',
            enhanced=True
        )
        
        # Prepare test data
        case_details = {
            'court_id': 'cand',
            'docket_num': '3:23-cv-12345',
            'new_filename': 'cand_23_12345_docket',
            'base_filename': 'cand_23_12345_docket'
        }
        
        html_content = '''
        <html>
        <head><title>PACER Case Document</title></head>
        <body>
        <h1>Case: 3:23-cv-12345</h1>
        <div class="attorneys">
            <h2>Attorneys</h2>
            <p>John Doe - Doe & Associates</p>
            <p>Jane Smith - Smith Law Firm</p>
        </div>
        </body>
        </html>
        '''
        
        json_path = '/data/20250812/dockets/cand_23_12345_docket.json'
        
        # Process HTML content
        result = await orchestrator.process_html_content(case_details, html_content, json_path)
        
        # Verify S3 upload was called
        mock_s3_async_storage.upload_content.assert_called()
        
        # Verify result structure
        assert result is not None
        assert 'court_id' in result
        assert result['court_id'] == 'cand'
        
        # Check S3 status
        s3_status = orchestrator.get_s3_status()
        assert s3_status['has_s3_async_storage'] is True
        assert s3_status['validation_passed'] is True
        assert s3_status['court_id'] == 'cand'
    
    def test_s3_injection_validation_edge_cases(self, mock_logger):
        """Test S3 injection validation edge cases."""
        # Test component without S3 injection
        component_no_s3 = Mock()
        component_no_s3.s3_async_storage = None
        
        validation = S3InjectionHelper.verify_s3_injection(component_no_s3, mock_logger)
        assert validation is False
        
        # Test component with S3 injection
        component_with_s3 = Mock()
        component_with_s3.s3_async_storage = Mock()
        
        validation = S3InjectionHelper.verify_s3_injection(component_with_s3, mock_logger)
        assert validation is True
    
    def test_enhanced_orchestrator_s3_status_reporting(self, mock_s3_async_storage, mock_logger):
        """Test enhanced orchestrator S3 status reporting."""
        orchestrator = EnhancedHTMLProcessingOrchestrator(
            logger=mock_logger,
            config={'iso_date': '20250812'},
            court_id='test_court',
            s3_async_storage=mock_s3_async_storage
        )
        
        status = orchestrator.get_s3_status()
        
        expected_keys = [
            'has_s3_async_storage', 'has_s3_manager', 's3_async_storage_type',
            's3_manager_type', 'bucket_name', 'court_id', 'validation_passed'
        ]
        
        for key in expected_keys:
            assert key in status
        
        assert status['has_s3_async_storage'] is True
        assert status['court_id'] == 'test_court'
        assert status['bucket_name'] == 'test-bucket'
        assert status['validation_passed'] is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])