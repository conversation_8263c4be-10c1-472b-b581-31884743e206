"""
Unit Tests for Dependency Injection Container

Focused unit tests to validate specific DI container functionality
without relying on heavy integration testing.

These tests specifically target the SystemExit(1) issues and ensure
proper dependency resolution throughout the container hierarchy.
"""

import unittest
import asyncio
import logging
import os
import sys
from unittest.mock import Mock, patch, MagicMock

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from config_models.base import WorkflowConfig
from factories.main_factory import MainServiceFactory
from infrastructure.di.base_container import BaseContainer, ServiceDescriptor


class TestDIContainerUnit(unittest.TestCase):
    """Unit tests for dependency injection container functionality."""

    def setUp(self):
        """Set up test environment."""
        self.logger = logging.getLogger(__name__)
        
        # Set up minimal environment
        self.test_env = {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root'
        }
        
        self.env_patch = patch.dict(os.environ, self.test_env)
        self.env_patch.start()

    def tearDown(self):
        """Clean up test environment."""
        self.env_patch.stop()

    def test_base_container_service_registration(self):
        """Test BaseContainer can register and retrieve services."""
        container = BaseContainer()
        
        # Test instance registration
        test_instance = "test_service_instance"
        descriptor = ServiceDescriptor(instance=test_instance)
        container.register("test_service", descriptor)
        
        # Test retrieval
        retrieved = container.get("test_service")
        self.assertEqual(retrieved, test_instance)
        
        # Test service existence
        self.assertTrue(container.has_service("test_service"))
        self.assertFalse(container.has_service("nonexistent"))

    def test_base_container_aliases(self):
        """Test BaseContainer alias functionality."""
        container = BaseContainer()
        
        # Register service
        test_instance = "aliased_service"
        descriptor = ServiceDescriptor(instance=test_instance)
        container.register("original_service", descriptor)
        
        # Add alias
        container.add_alias("service_alias", "original_service")
        
        # Test retrieval via alias
        retrieved = container.get("service_alias")
        self.assertEqual(retrieved, test_instance)

    def test_service_descriptor_validation(self):
        """Test ServiceDescriptor validation."""
        # Valid descriptor with instance
        descriptor1 = ServiceDescriptor(instance="test")
        self.assertIsNotNone(descriptor1)
        
        # Valid descriptor with service class
        descriptor2 = ServiceDescriptor(service_class="test.module.TestClass")
        self.assertIsNotNone(descriptor2)
        
        # Invalid descriptor - neither instance nor service_class
        with self.assertRaises(ValueError):
            ServiceDescriptor()
        
        # Invalid descriptor - both instance and service_class
        with self.assertRaises(ValueError):
            ServiceDescriptor(
                instance="test", 
                service_class="test.module.TestClass"
            )

    def test_main_service_factory_config_preparation(self):
        """Test MainServiceFactory config preparation without container creation."""
        config = WorkflowConfig(
            headless=True,
            run_parallel=False,
            timeout_ms=30000
        )
        
        factory = MainServiceFactory(config=config)
        
        # Test config dictionary preparation
        config_dict = factory._prepare_config_dict()
        
        # Validate required sections
        self.assertIn('pacer', config_dict)
        self.assertIn('storage', config_dict)
        self.assertIn('directories', config_dict)
        
        # Validate PACER config has required fields
        pacer_config = config_dict['pacer']
        self.assertIn('headless', pacer_config)
        self.assertIn('run_parallel', pacer_config)
        self.assertIn('timeout_ms', pacer_config)
        
        # Validate environment variables are included
        self.assertEqual(config_dict['aws_region'], 'us-west-2')
        self.assertEqual(config_dict['s3_bucket_name'], 'test-bucket')

    def test_main_service_factory_validation_methods(self):
        """Test MainServiceFactory validation methods."""
        config = WorkflowConfig(
            headless=True,
            run_parallel=False,
            timeout_ms=30000
        )
        
        factory = MainServiceFactory(config=config)
        
        # Test configuration validation
        factory._validate_configuration()  # Should not raise
        
        # Test with None config
        factory.config = None
        with self.assertRaises(ValueError):
            factory._validate_configuration()
        
        # Restore config
        factory.config = config
        
        # Test config dict validation
        valid_config_dict = {
            'pacer': {},
            'storage': {},
            'directories': {'base_dir': '/test'},
            'project_root': '/test/root'
        }
        factory._validate_config_dict(valid_config_dict)  # Should not raise
        
        # Test invalid config dict
        with self.assertRaises(ValueError):
            factory._validate_config_dict("not_a_dict")

    @patch('src.containers.core.create_container')
    def test_container_creation_error_handling(self, mock_create_container):
        """Test error handling during container creation."""
        # Mock container creation failure
        mock_create_container.side_effect = RuntimeError("Container creation failed")
        
        config = WorkflowConfig(
            headless=True,
            run_parallel=False,
            timeout_ms=30000
        )
        
        factory = MainServiceFactory(config=config)
        
        async def test_async():
            with self.assertRaises(RuntimeError):
                async with factory:
                    pass
        
        # Run async test
        asyncio.run(test_async())

    def test_storage_dependency_validation_logic(self):
        """Test storage dependency validation logic without actual containers."""
        config = WorkflowConfig(
            headless=True,
            run_parallel=False,
            timeout_ms=30000
        )
        
        factory = MainServiceFactory(config=config)
        
        # Mock container with storage
        mock_container = Mock()
        mock_storage = Mock()
        
        # Test missing storage container
        mock_container.storage = None
        factory._container = mock_container
        
        with self.assertRaises(RuntimeError):
            factory._validate_storage_dependencies()
        
        # Test missing storage dependencies
        mock_container.storage = mock_storage
        mock_storage.async_dynamodb_storage = None
        
        with self.assertRaises(RuntimeError):
            factory._validate_storage_dependencies()

    def test_pacer_dependency_validation_logic(self):
        """Test PACER dependency validation logic."""
        config = WorkflowConfig(
            headless=True,
            run_parallel=False,
            timeout_ms=30000
        )
        
        factory = MainServiceFactory(config=config)
        
        # Mock container with PACER
        mock_container = Mock()
        mock_pacer = Mock()
        
        # Test missing PACER container
        mock_container.pacer = None
        factory._container = mock_container
        
        with self.assertRaises(RuntimeError):
            factory._validate_pacer_dependencies()
        
        # Test successful validation with mock dependencies
        mock_container.pacer = mock_pacer
        mock_pacer.sequential_workflow_manager = Mock(return_value=Mock())
        
        # Should not raise
        factory._validate_pacer_dependencies()

    def test_error_handling_prevents_system_exit(self):
        """Test that proper error handling prevents SystemExit."""
        config = WorkflowConfig(
            headless=True,
            run_parallel=False,
            timeout_ms=30000
        )
        
        # Remove critical environment variables
        with patch.dict(os.environ, {}, clear=True):
            factory = MainServiceFactory(config=config)
            
            async def test_async():
                try:
                    async with factory:
                        pass
                except SystemExit:
                    self.fail("SystemExit was raised - should be handled gracefully")
                except Exception:
                    # Other exceptions are acceptable
                    pass
            
            # This should not raise SystemExit
            asyncio.run(test_async())

    def test_container_cleanup_logic(self):
        """Test container cleanup logic."""
        config = WorkflowConfig(
            headless=True,
            run_parallel=False,
            timeout_ms=30000
        )
        
        factory = MainServiceFactory(config=config)
        
        # Mock container
        mock_container = Mock()
        mock_container.shutdown_resources.return_value = None
        mock_container.unwire.return_value = None
        
        factory._container = mock_container
        
        async def test_cleanup():
            await factory._cleanup_container()
            
            # Verify cleanup methods were called
            mock_container.shutdown_resources.assert_called_once()
            mock_container.unwire.assert_called_once()
            
            # Verify container is set to None
            self.assertIsNone(factory._container)
        
        asyncio.run(test_cleanup())


class TestIntegrationSafety(unittest.TestCase):
    """Integration safety tests to ensure no SystemExit in production scenarios."""

    def setUp(self):
        """Set up integration test environment."""
        self.test_env = {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'LEXGENIUS_PROJECT_ROOT': '/test/root'
        }
        
        self.env_patch = patch.dict(os.environ, self.test_env)
        self.env_patch.start()

    def tearDown(self):
        """Clean up integration test environment."""
        self.env_patch.stop()

    def test_no_system_exit_on_missing_credentials(self):
        """Ensure no SystemExit when AWS credentials are missing."""
        # Remove AWS credentials
        with patch.dict(os.environ, {}, clear=True):
            config = WorkflowConfig(
                headless=True,
                run_parallel=False,
                timeout_ms=30000
            )
            
            factory = MainServiceFactory(config=config)
            
            async def test_async():
                try:
                    async with factory:
                        pass
                except SystemExit:
                    self.fail("SystemExit raised - should handle missing credentials gracefully")
                except Exception:
                    # Other exceptions are expected and acceptable
                    pass
            
            asyncio.run(test_async())

    def test_no_system_exit_on_invalid_config(self):
        """Ensure no SystemExit with invalid configuration."""
        # Create invalid config
        config = WorkflowConfig(
            headless=True,
            run_parallel=False,
            timeout_ms=-1000  # Invalid timeout
        )
        
        factory = MainServiceFactory(config=config)
        
        async def test_async():
            try:
                async with factory:
                    pass
            except SystemExit:
                self.fail("SystemExit raised - should handle invalid config gracefully")
            except Exception:
                # Other exceptions are expected and acceptable
                pass
        
        asyncio.run(test_async())


if __name__ == '__main__':
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run tests
    unittest.main(verbosity=2)