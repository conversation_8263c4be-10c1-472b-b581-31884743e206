#!/usr/bin/env python3
"""
Simple DI Test - Quick validation that basic DI functionality works.
"""

import os
import sys
from datetime import datetime
from pathlib import Path

# Add paths
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

# Set test environment
test_env = {
    'AWS_REGION': 'us-west-2',
    'AWS_ACCESS_KEY_ID': 'test_key',
    'AWS_SECRET_ACCESS_KEY': 'test_secret',
    'S3_BUCKET_NAME': 'test-bucket',
    'LEXGENIUS_PROJECT_ROOT': str(project_root)
}

for key, value in test_env.items():
    if key not in os.environ:
        os.environ[key] = value

async def main():
    """Simple test."""
    print("🧪 Running simple DI test...")
    
    try:
        # Test imports first
        print("Testing imports...")
        from config_models.base import WorkflowConfig
        from factories.main_factory import MainServiceFactory
        print("✅ Imports successful")
        
        # Test config creation
        print("Testing config creation...")
        config = WorkflowConfig(
            date=datetime.now(),
            name="simple_test",
            headless=True,
            timeout_ms=30000
        )
        print("✅ Config creation successful")
        
        # Test factory creation
        print("Testing factory creation...")
        factory = MainServiceFactory(config=config)
        print("✅ Factory creation successful")
        
        # Test container initialization
        print("Testing container initialization...")
        async with factory as f:
            print("✅ Container initialization successful")
            
            # Test basic storage access
            print("Testing storage access...")
            storage_container = f._container.storage
            
            # Try to get storage components
            try:
                async_db = storage_container.async_dynamodb_storage()
                print("✅ AsyncDynamoDBStorage creation successful")
            except Exception as e:
                print(f"⚠️ AsyncDynamoDBStorage creation failed: {e}")
            
            try:
                pacer_repo = storage_container.pacer_repository()
                print("✅ PacerRepository creation successful")
            except Exception as e:
                print(f"⚠️ PacerRepository creation failed: {e}")
            
        print("\n🎉 All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import asyncio
    success = asyncio.run(main())
    sys.exit(0 if success else 1)