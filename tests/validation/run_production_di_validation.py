#!/usr/bin/env python3
"""
Production DI Validation Runner

Quick validation script to test the dependency injection system.
This script can be run immediately to validate production readiness.

Usage:
    python tests/validation/run_production_di_validation.py
    
Exit Codes:
    0: All validations passed
    1: Some validations failed
    2: Critical error during validation
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

try:
    from config_models.base import WorkflowConfig
    from factories.main_factory import MainServiceFactory
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(2)


class QuickDIValidator:
    """Quick dependency injection validator for production readiness."""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.results = {}
        
    def _setup_logger(self):
        """Set up logging for validation."""
        logger = logging.getLogger('di_validator')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _setup_test_environment(self):
        """Set up test environment variables."""
        test_env = {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_access_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret_key',
            'S3_BUCKET_NAME': 'test-lexgenius-data',
            'DYNAMODB_ENDPOINT': 'http://localhost:8000',
            'PACER_USERNAME_PROD': 'test_user',
            'PACER_PASSWORD_PROD': 'test_pass',
            'LEXGENIUS_PROJECT_ROOT': str(project_root)
        }
        
        for key, value in test_env.items():
            if key not in os.environ:
                os.environ[key] = value
                
        self.logger.info("Test environment variables configured")
    
    async def test_container_creation(self):
        """Test 1: Basic container creation and initialization."""
        self.logger.info("🧪 Testing container creation...")
        
        try:
            from datetime import datetime
            config = WorkflowConfig(
                date=datetime.now(),
                name="validation_test",
                headless=True,
                run_parallel=False,
                timeout_ms=30000
            )
            
            shutdown_event = asyncio.Event()
            factory = MainServiceFactory(config=config, shutdown_event=shutdown_event)
            
            async with factory as initialized_factory:
                # Basic validation
                assert initialized_factory._container is not None, "Container is None"
                assert hasattr(initialized_factory._container, 'storage'), "Storage container missing"
                assert hasattr(initialized_factory._container, 'pacer'), "PACER container missing"
                
                self.logger.info("✅ Container creation: PASSED")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ Container creation failed: {e}")
            return False
    
    async def test_storage_dependencies(self):
        """Test 2: Storage dependency resolution."""
        self.logger.info("🧪 Testing storage dependencies...")
        
        try:
            from datetime import datetime
            config = WorkflowConfig(
                date=datetime.now(),
                name="storage_test",
                headless=True,
                run_parallel=False,
                timeout_ms=30000
            )
            
            shutdown_event = asyncio.Event()
            factory = MainServiceFactory(config=config, shutdown_event=shutdown_event)
            
            async with factory as initialized_factory:
                storage_container = initialized_factory._container.storage
                
                # Test critical storage dependencies
                async_db = storage_container.async_dynamodb_storage()
                assert async_db is not None, "AsyncDynamoDBStorage is None"
                
                pacer_repo = storage_container.pacer_repository()
                assert pacer_repo is not None, "PacerRepository is None"
                
                s3_storage = storage_container.s3_async_storage()
                assert s3_storage is not None, "S3AsyncStorage is None"
                
                self.logger.info("✅ Storage dependencies: PASSED")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ Storage dependencies failed: {e}")
            return False
    
    async def test_pacer_orchestrator_creation(self):
        """Test 3: PACER orchestrator creation through DI."""
        self.logger.info("🧪 Testing PACER orchestrator creation...")
        
        try:
            from datetime import datetime
            config = WorkflowConfig(
                date=datetime.now(),
                name="pacer_test",
                headless=True,
                run_parallel=False,
                timeout_ms=30000
            )
            
            shutdown_event = asyncio.Event()
            factory = MainServiceFactory(config=config, shutdown_event=shutdown_event)
            
            async with factory as initialized_factory:
                # Test orchestrator creation
                pacer_service = await initialized_factory.create_pacer_orchestrator_service()
                assert pacer_service is not None, "PACER orchestrator service is None"
                
                # Basic validation of orchestrator structure
                assert hasattr(pacer_service, 'workflow_orchestrator'), "No workflow_orchestrator attribute"
                
                self.logger.info("✅ PACER orchestrator creation: PASSED")
                return True
                
        except SystemExit as e:
            self.logger.error(f"❌ SystemExit during PACER orchestrator creation: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ PACER orchestrator creation failed: {e}")
            return False
    
    async def test_error_handling(self):
        """Test 4: Error handling without SystemExit."""
        self.logger.info("🧪 Testing error handling...")
        
        try:
            # Test with missing critical environment variables
            backup_env = {}
            critical_vars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY']
            
            # Backup and remove critical vars
            for var in critical_vars:
                if var in os.environ:
                    backup_env[var] = os.environ[var]
                    del os.environ[var]
            
            try:
                from datetime import datetime
                config = WorkflowConfig(
                    date=datetime.now(),
                    name="error_test",
                    headless=True,
                    run_parallel=False,
                    timeout_ms=30000
                )
                
                shutdown_event = asyncio.Event()
                factory = MainServiceFactory(config=config, shutdown_event=shutdown_event)
                
                async with factory as initialized_factory:
                    # If we get here, system handled missing deps gracefully
                    pass
                    
            except SystemExit:
                # This is what we want to avoid
                self.logger.error("❌ SystemExit raised instead of proper error handling")
                return False
            except Exception:
                # Other exceptions are acceptable
                pass
            finally:
                # Restore environment variables
                for var, value in backup_env.items():
                    os.environ[var] = value
            
            self.logger.info("✅ Error handling: PASSED")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error handling test failed: {e}")
            return False
    
    async def run_all_validations(self):
        """Run all validation tests."""
        self.logger.info("🚀 Starting Production DI Validation")
        self.logger.info(f"Project root: {project_root}")
        
        # Setup test environment
        self._setup_test_environment()
        
        # Run tests
        tests = [
            ("Container Creation", self.test_container_creation),
            ("Storage Dependencies", self.test_storage_dependencies),
            ("PACER Orchestrator Creation", self.test_pacer_orchestrator_creation),
            ("Error Handling", self.test_error_handling),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results[test_name] = "PASSED" if result else "FAILED"
            except Exception as e:
                self.logger.error(f"❌ Test '{test_name}' crashed: {e}")
                results[test_name] = f"CRASHED: {e}"
        
        return results
    
    def generate_report(self, results):
        """Generate validation report."""
        passed = sum(1 for r in results.values() if r == "PASSED")
        total = len(results)
        
        report = f"""
========================================
PRODUCTION DI VALIDATION REPORT
========================================
Timestamp: {datetime.now().isoformat()}
Results: {passed}/{total} PASSED

Test Details:
"""
        
        for test_name, result in results.items():
            status = "✅" if result == "PASSED" else "❌"
            report += f"{status} {test_name}: {result}\n"
        
        if passed == total:
            report += "\n🎉 ALL TESTS PASSED - DEPENDENCY INJECTION SYSTEM IS PRODUCTION READY\n"
        else:
            report += f"\n⚠️ {total - passed} TESTS FAILED - SYSTEM REQUIRES ATTENTION\n"
        
        report += "========================================"
        return report, passed == total


async def main():
    """Main validation function."""
    validator = QuickDIValidator()
    
    try:
        results = await validator.run_all_validations()
        report, all_passed = validator.generate_report(results)
        
        print(report)
        
        return 0 if all_passed else 1
        
    except Exception as e:
        validator.logger.critical(f"Critical error during validation: {e}")
        return 2


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)