"""
Production Validation: Complete Dependency Injection System Validation

This comprehensive test validates that the dependency injection system is properly 
configured and working correctly in production. Tests cover all critical components
from MainServiceFactory down to individual service dependencies.

VALIDATION COVERAGE:
1. MainServiceFactory container creation and initialization
2. StorageContainer dependency provision
3. PacerCoreContainer dependency resolution 
4. DocketOrchestrator dependency injection
5. SequentialWorkflowManager dependency validation
6. Error handling for missing dependencies
7. End-to-end workflow initialization

Author: Production Validation Specialist
Created: 2025-01-12
"""

import asyncio
import logging
import os
import sys
import unittest
from unittest.mock import Mock, patch, MagicMock
from typing import Any, Dict, Optional
import pytest

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from config_models.base import WorkflowConfig
from factories.main_factory import MainServiceFactory
from containers.core import MainContainer, create_container
from infrastructure.di.base_container import BaseContainer, ServiceDescriptor


class TestProductionDependencyInjectionValidation(unittest.TestCase):
    """
    Production validation tests for the complete dependency injection system.
    
    These tests ensure that the DI system works correctly in production scenarios
    without relying on mocks for critical dependency resolution paths.
    """

    def setUp(self):
        """Set up test environment with production-like configuration."""
        self.logger = logging.getLogger(__name__)
        
        # Create a realistic WorkflowConfig for testing
        self.test_config = WorkflowConfig(
            headless=True,
            run_parallel=False,
            timeout_ms=30000,
            config_name="production_validation_test"
        )
        
        # Mock environment variables for testing
        self.env_patches = {
            'AWS_REGION': 'us-west-2',
            'AWS_ACCESS_KEY_ID': 'test_access_key',
            'AWS_SECRET_ACCESS_KEY': 'test_secret_key',
            'S3_BUCKET_NAME': 'test-lexgenius-data',
            'DYNAMODB_ENDPOINT': 'http://localhost:8000',
            'PACER_USERNAME_PROD': 'test_user',
            'PACER_PASSWORD_PROD': 'test_pass',
            'LEXGENIUS_PROJECT_ROOT': '/test/project/root'
        }
        
        # Apply environment patches
        self.env_patch_objects = []
        for key, value in self.env_patches.items():
            patcher = patch.dict(os.environ, {key: value})
            patcher.start()
            self.env_patch_objects.append(patcher)

    def tearDown(self):
        """Clean up test environment."""
        # Stop all environment patches
        for patcher in self.env_patch_objects:
            patcher.stop()

    async def test_main_service_factory_container_creation(self):
        """
        VALIDATION 1: Test MainServiceFactory can create containers without errors.
        
        This test validates that the MainServiceFactory can successfully create
        and initialize a DI container with all required dependencies.
        """
        self.logger.info("🧪 VALIDATION 1: Testing MainServiceFactory container creation")
        
        # Create shutdown event for testing
        shutdown_event = asyncio.Event()
        
        try:
            # Create MainServiceFactory
            factory = MainServiceFactory(
                config=self.test_config,
                shutdown_event=shutdown_event
            )
            
            # Test container creation through async context
            async with factory as initialized_factory:
                # Validate factory is properly initialized
                self.assertIsNotNone(initialized_factory)
                self.assertIsNotNone(initialized_factory._container)
                self.assertIsInstance(initialized_factory._container, MainContainer)
                
                # Validate container has expected subcontainers
                self.assertTrue(hasattr(initialized_factory._container, 'storage'))
                self.assertTrue(hasattr(initialized_factory._container, 'pacer'))
                self.assertTrue(hasattr(initialized_factory._container, 'transformer'))
                self.assertTrue(hasattr(initialized_factory._container, 'fb_ads'))
                self.assertTrue(hasattr(initialized_factory._container, 'reports'))
                
                self.logger.info("✅ MainServiceFactory container creation: PASSED")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ MainServiceFactory container creation failed: {e}")
            self.fail(f"Container creation failed: {e}")

    async def test_storage_container_dependency_provision(self):
        """
        VALIDATION 2: Test StorageContainer properly provides PacerRepository and AsyncDynamoDBStorage.
        
        This test validates that the storage container can provide all required
        storage dependencies without SystemExit or other failures.
        """
        self.logger.info("🧪 VALIDATION 2: Testing StorageContainer dependency provision")
        
        shutdown_event = asyncio.Event()
        
        try:
            factory = MainServiceFactory(
                config=self.test_config,
                shutdown_event=shutdown_event
            )
            
            async with factory as initialized_factory:
                storage_container = initialized_factory._container.storage
                
                # Test AsyncDynamoDBStorage provision
                try:
                    async_dynamodb_storage = storage_container.async_dynamodb_storage()
                    self.assertIsNotNone(async_dynamodb_storage)
                    self.logger.info("✅ AsyncDynamoDBStorage provision: PASSED")
                except Exception as e:
                    self.logger.error(f"❌ AsyncDynamoDBStorage provision failed: {e}")
                    self.fail(f"AsyncDynamoDBStorage provision failed: {e}")
                
                # Test PacerRepository provision
                try:
                    pacer_repository = storage_container.pacer_repository()
                    self.assertIsNotNone(pacer_repository)
                    self.logger.info("✅ PacerRepository provision: PASSED")
                except Exception as e:
                    self.logger.error(f"❌ PacerRepository provision failed: {e}")
                    self.fail(f"PacerRepository provision failed: {e}")
                
                # Test S3AsyncStorage provision
                try:
                    s3_async_storage = storage_container.s3_async_storage()
                    self.assertIsNotNone(s3_async_storage)
                    self.logger.info("✅ S3AsyncStorage provision: PASSED")
                except Exception as e:
                    self.logger.error(f"❌ S3AsyncStorage provision failed: {e}")
                    self.fail(f"S3AsyncStorage provision failed: {e}")
                
                self.logger.info("✅ Storage container dependency provision: ALL PASSED")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ Storage container dependency provision failed: {e}")
            self.fail(f"Storage dependency provision failed: {e}")

    async def test_pacer_core_container_storage_dependencies(self):
        """
        VALIDATION 3: Test PacerCoreContainer receives storage dependencies correctly.
        
        This test validates that the PACER core container can access storage
        dependencies injected from the main container.
        """
        self.logger.info("🧪 VALIDATION 3: Testing PacerCoreContainer storage dependencies")
        
        shutdown_event = asyncio.Event()
        
        try:
            factory = MainServiceFactory(
                config=self.test_config,
                shutdown_event=shutdown_event
            )
            
            async with factory as initialized_factory:
                pacer_container = initialized_factory._container.pacer
                
                # Test that PACER container has access to storage dependencies
                self.assertTrue(hasattr(pacer_container, 'pacer_repository'))
                self.assertTrue(hasattr(pacer_container, 'async_dynamodb_storage'))
                
                # Test storage dependency resolution through PACER container
                try:
                    pacer_repo_from_pacer = pacer_container.pacer_repository()
                    self.assertIsNotNone(pacer_repo_from_pacer)
                    self.logger.info("✅ PacerRepository access via PACER container: PASSED")
                except Exception as e:
                    self.logger.error(f"❌ PacerRepository access via PACER container failed: {e}")
                    self.fail(f"PACER container PacerRepository access failed: {e}")
                
                try:
                    async_db_from_pacer = pacer_container.async_dynamodb_storage()
                    self.assertIsNotNone(async_db_from_pacer)
                    self.logger.info("✅ AsyncDynamoDBStorage access via PACER container: PASSED")
                except Exception as e:
                    self.logger.error(f"❌ AsyncDynamoDBStorage access via PACER container failed: {e}")
                    self.fail(f"PACER container AsyncDynamoDBStorage access failed: {e}")
                
                self.logger.info("✅ PACER core container storage dependencies: ALL PASSED")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ PACER core container storage dependencies failed: {e}")
            self.fail(f"PACER storage dependencies failed: {e}")

    async def test_docket_orchestrator_dependency_injection(self):
        """
        VALIDATION 4: Test DocketOrchestrator gets proper dependencies from container.
        
        This test validates that the DocketOrchestrator can be created with all
        required dependencies properly injected through the DI system.
        """
        self.logger.info("🧪 VALIDATION 4: Testing DocketOrchestrator dependency injection")
        
        shutdown_event = asyncio.Event()
        
        try:
            factory = MainServiceFactory(
                config=self.test_config,
                shutdown_event=shutdown_event
            )
            
            async with factory as initialized_factory:
                # Test DocketOrchestrator creation through factory method
                try:
                    pacer_orchestrator_service = await initialized_factory.create_pacer_orchestrator_service()
                    self.assertIsNotNone(pacer_orchestrator_service)
                    
                    # Validate that the service has the expected internal orchestrator
                    self.assertTrue(hasattr(pacer_orchestrator_service, 'workflow_orchestrator'))
                    docket_orchestrator = pacer_orchestrator_service.workflow_orchestrator
                    self.assertIsNotNone(docket_orchestrator)
                    
                    # Validate DocketOrchestrator has required dependencies
                    required_deps = [
                        'court_processor',
                        'docket_processor', 
                        'row_processor',
                        'download_manager',
                        'file_operations_service',
                        'navigation_facade',
                        'report_facade'
                    ]
                    
                    for dep_name in required_deps:
                        if hasattr(docket_orchestrator, dep_name):
                            dep_value = getattr(docket_orchestrator, dep_name)
                            self.assertIsNotNone(dep_value, f"Required dependency {dep_name} is None")
                            self.logger.info(f"✅ DocketOrchestrator dependency {dep_name}: PASSED")
                        else:
                            self.logger.warning(f"⚠️ DocketOrchestrator dependency {dep_name}: NOT FOUND")
                    
                    self.logger.info("✅ DocketOrchestrator dependency injection: PASSED")
                    return True
                    
                except Exception as e:
                    self.logger.error(f"❌ DocketOrchestrator creation failed: {e}")
                    self.fail(f"DocketOrchestrator dependency injection failed: {e}")
                
        except Exception as e:
            self.logger.error(f"❌ DocketOrchestrator dependency injection test failed: {e}")
            self.fail(f"DocketOrchestrator test failed: {e}")

    async def test_sequential_workflow_manager_dependencies(self):
        """
        VALIDATION 5: Test SequentialWorkflowManager receives all required dependencies.
        
        This test validates that the SequentialWorkflowManager can be created with
        proper storage and processing dependencies.
        """
        self.logger.info("🧪 VALIDATION 5: Testing SequentialWorkflowManager dependencies")
        
        shutdown_event = asyncio.Event()
        
        try:
            factory = MainServiceFactory(
                config=self.test_config,
                shutdown_event=shutdown_event
            )
            
            async with factory as initialized_factory:
                pacer_container = initialized_factory._container.pacer
                
                # Test SequentialWorkflowManager creation if available
                if hasattr(pacer_container, 'sequential_workflow_manager'):
                    try:
                        workflow_manager = pacer_container.sequential_workflow_manager()
                        self.assertIsNotNone(workflow_manager)
                        
                        # Validate SequentialWorkflowManager has storage dependencies
                        expected_deps = [
                            'pacer_repository',
                            'async_dynamodb_storage'
                        ]
                        
                        for dep_name in expected_deps:
                            if hasattr(workflow_manager, dep_name):
                                dep_value = getattr(workflow_manager, dep_name)
                                if dep_value is not None:
                                    self.logger.info(f"✅ SequentialWorkflowManager dependency {dep_name}: PASSED")
                                else:
                                    self.logger.warning(f"⚠️ SequentialWorkflowManager dependency {dep_name}: NULL")
                            else:
                                self.logger.warning(f"⚠️ SequentialWorkflowManager dependency {dep_name}: NOT FOUND")
                        
                        self.logger.info("✅ SequentialWorkflowManager dependencies: PASSED")
                        return True
                        
                    except Exception as e:
                        self.logger.error(f"❌ SequentialWorkflowManager creation failed: {e}")
                        self.fail(f"SequentialWorkflowManager dependency injection failed: {e}")
                else:
                    self.logger.info("ℹ️ SequentialWorkflowManager not found in container - skipping validation")
                    return True
                
        except Exception as e:
            self.logger.error(f"❌ SequentialWorkflowManager dependencies test failed: {e}")
            self.fail(f"SequentialWorkflowManager test failed: {e}")

    async def test_error_handling_for_missing_dependencies(self):
        """
        VALIDATION 6: Test error handling for truly missing dependencies.
        
        This test validates that the system properly handles missing dependencies
        with appropriate error messages instead of SystemExit(1).
        """
        self.logger.info("🧪 VALIDATION 6: Testing error handling for missing dependencies")
        
        # Test with intentionally broken configuration
        broken_config = WorkflowConfig(
            headless=True,
            run_parallel=False,
            timeout_ms=30000,
            config_name="broken_test_config"
        )
        
        # Remove critical environment variables to simulate missing dependencies
        with patch.dict(os.environ, {}, clear=True):
            shutdown_event = asyncio.Event()
            
            try:
                factory = MainServiceFactory(
                    config=broken_config,
                    shutdown_event=shutdown_event
                )
                
                # This should handle missing dependencies gracefully
                async with factory as initialized_factory:
                    # If we get here, the system handled missing deps gracefully
                    self.logger.info("✅ Missing dependencies handled gracefully")
                    return True
                    
            except RuntimeError as e:
                # Expected behavior - proper error handling
                self.logger.info(f"✅ Proper error handling for missing dependencies: {e}")
                return True
                
            except SystemExit as e:
                # This is what we want to avoid
                self.logger.error(f"❌ SystemExit raised instead of proper error handling: {e}")
                self.fail("SystemExit raised - dependency injection should handle missing deps gracefully")
                
            except Exception as e:
                # Other exceptions are acceptable as long as they're not SystemExit
                self.logger.info(f"✅ Proper exception handling for missing dependencies: {e}")
                return True

    async def test_end_to_end_workflow_initialization(self):
        """
        VALIDATION 7: Test complete workflow can initialize without SystemExit failures.
        
        This test validates that a complete workflow can be initialized through
        the dependency injection system without failures.
        """
        self.logger.info("🧪 VALIDATION 7: Testing end-to-end workflow initialization")
        
        shutdown_event = asyncio.Event()
        
        try:
            factory = MainServiceFactory(
                config=self.test_config,
                shutdown_event=shutdown_event
            )
            
            async with factory as initialized_factory:
                # Test creating multiple orchestrators to validate full workflow
                services_created = {}
                
                # Test PACER orchestrator
                try:
                    pacer_service = await initialized_factory.create_pacer_orchestrator_service()
                    services_created['pacer'] = pacer_service is not None
                    self.logger.info("✅ PACER orchestrator creation: PASSED")
                except Exception as e:
                    services_created['pacer'] = False
                    self.logger.warning(f"⚠️ PACER orchestrator creation failed: {e}")
                
                # Test Reports orchestrator
                try:
                    reports_service = await initialized_factory.create_reports_orchestrator_service()
                    services_created['reports'] = reports_service is not None
                    self.logger.info("✅ Reports orchestrator creation: PASSED")
                except Exception as e:
                    services_created['reports'] = False
                    self.logger.warning(f"⚠️ Reports orchestrator creation failed: {e}")
                
                # Test Processing orchestrator
                try:
                    processing_service = await initialized_factory.create_processing_orchestrator()
                    services_created['processing'] = processing_service is not None
                    self.logger.info("✅ Processing orchestrator creation: PASSED")
                except Exception as e:
                    services_created['processing'] = False
                    self.logger.warning(f"⚠️ Processing orchestrator creation failed: {e}")
                
                # Validate at least one service was created successfully
                successful_services = sum(services_created.values())
                if successful_services > 0:
                    self.logger.info(f"✅ End-to-end workflow initialization: {successful_services}/{len(services_created)} services created")
                    return True
                else:
                    self.fail("No services could be created - complete workflow initialization failed")
                
        except SystemExit as e:
            self.logger.error(f"❌ SystemExit during workflow initialization: {e}")
            self.fail("SystemExit raised during workflow initialization - DI system should prevent this")
            
        except Exception as e:
            self.logger.error(f"❌ End-to-end workflow initialization failed: {e}")
            self.fail(f"Workflow initialization failed: {e}")

    def test_base_container_functionality(self):
        """
        VALIDATION 8: Test BaseContainer functionality works correctly.
        
        This test validates that the base container infrastructure provides
        proper service registration and resolution.
        """
        self.logger.info("🧪 VALIDATION 8: Testing BaseContainer functionality")
        
        # Create a test container
        container = BaseContainer()
        
        # Test service registration
        test_service_instance = "test_service_instance"
        descriptor = ServiceDescriptor(
            instance=test_service_instance,
            singleton=True,
            tags=["test"]
        )
        
        container.register("test_service", descriptor)
        
        # Test service retrieval
        retrieved_service = container.get("test_service")
        self.assertEqual(retrieved_service, test_service_instance)
        
        # Test service existence check
        self.assertTrue(container.has_service("test_service"))
        self.assertFalse(container.has_service("nonexistent_service"))
        
        # Test tag-based service discovery
        tagged_services = container.get_services_by_tag("test")
        self.assertIn("test_service", tagged_services)
        
        self.logger.info("✅ BaseContainer functionality: ALL PASSED")

    async def run_all_validations(self):
        """
        Run all production validation tests in sequence.
        
        Returns a comprehensive validation report.
        """
        self.logger.info("🚀 Starting Production Dependency Injection Validation")
        
        validation_results = {}
        
        # Test 1: MainServiceFactory container creation
        try:
            await self.test_main_service_factory_container_creation()
            validation_results['container_creation'] = 'PASSED'
        except Exception as e:
            validation_results['container_creation'] = f'FAILED: {e}'
        
        # Test 2: Storage container dependency provision
        try:
            await self.test_storage_container_dependency_provision()
            validation_results['storage_dependencies'] = 'PASSED'
        except Exception as e:
            validation_results['storage_dependencies'] = f'FAILED: {e}'
        
        # Test 3: PACER core container storage dependencies
        try:
            await self.test_pacer_core_container_storage_dependencies()
            validation_results['pacer_storage_access'] = 'PASSED'
        except Exception as e:
            validation_results['pacer_storage_access'] = f'FAILED: {e}'
        
        # Test 4: DocketOrchestrator dependency injection
        try:
            await self.test_docket_orchestrator_dependency_injection()
            validation_results['docket_orchestrator_di'] = 'PASSED'
        except Exception as e:
            validation_results['docket_orchestrator_di'] = f'FAILED: {e}'
        
        # Test 5: SequentialWorkflowManager dependencies
        try:
            await self.test_sequential_workflow_manager_dependencies()
            validation_results['workflow_manager_deps'] = 'PASSED'
        except Exception as e:
            validation_results['workflow_manager_deps'] = f'FAILED: {e}'
        
        # Test 6: Error handling for missing dependencies
        try:
            await self.test_error_handling_for_missing_dependencies()
            validation_results['error_handling'] = 'PASSED'
        except Exception as e:
            validation_results['error_handling'] = f'FAILED: {e}'
        
        # Test 7: End-to-end workflow initialization
        try:
            await self.test_end_to_end_workflow_initialization()
            validation_results['e2e_workflow'] = 'PASSED'
        except Exception as e:
            validation_results['e2e_workflow'] = f'FAILED: {e}'
        
        # Test 8: Base container functionality
        try:
            self.test_base_container_functionality()
            validation_results['base_container'] = 'PASSED'
        except Exception as e:
            validation_results['base_container'] = f'FAILED: {e}'
        
        return validation_results

    def generate_validation_report(self, results: dict) -> str:
        """Generate a comprehensive validation report."""
        passed_tests = sum(1 for result in results.values() if result == 'PASSED')
        total_tests = len(results)
        
        report = f"""
========================================
PRODUCTION DEPENDENCY INJECTION VALIDATION REPORT
========================================

Test Results: {passed_tests}/{total_tests} PASSED

Detailed Results:
----------------
"""
        
        for test_name, result in results.items():
            status_emoji = "✅" if result == "PASSED" else "❌"
            report += f"{status_emoji} {test_name}: {result}\n"
        
        report += f"""
Summary:
--------
- Container Creation: {'PASSED' if results.get('container_creation') == 'PASSED' else 'FAILED'}
- Storage Dependencies: {'PASSED' if results.get('storage_dependencies') == 'PASSED' else 'FAILED'}
- PACER Storage Access: {'PASSED' if results.get('pacer_storage_access') == 'PASSED' else 'FAILED'}
- DocketOrchestrator DI: {'PASSED' if results.get('docket_orchestrator_di') == 'PASSED' else 'FAILED'}
- WorkflowManager Dependencies: {'PASSED' if results.get('workflow_manager_deps') == 'PASSED' else 'FAILED'}
- Error Handling: {'PASSED' if results.get('error_handling') == 'PASSED' else 'FAILED'}
- E2E Workflow: {'PASSED' if results.get('e2e_workflow') == 'PASSED' else 'FAILED'}
- Base Container: {'PASSED' if results.get('base_container') == 'PASSED' else 'FAILED'}

Production Readiness: {'READY' if passed_tests == total_tests else 'REQUIRES ATTENTION'}

========================================
"""
        return report


async def main():
    """Main function to run production validation."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create test instance
    validator = TestProductionDependencyInjectionValidation()
    validator.setUp()
    
    try:
        # Run all validations
        results = await validator.run_all_validations()
        
        # Generate and print report
        report = validator.generate_validation_report(results)
        print(report)
        
        # Return exit code based on results
        passed_tests = sum(1 for result in results.values() if result == 'PASSED')
        total_tests = len(results)
        
        if passed_tests == total_tests:
            print("🎉 ALL VALIDATIONS PASSED - SYSTEM IS PRODUCTION READY")
            return 0
        else:
            print(f"⚠️ {total_tests - passed_tests} VALIDATIONS FAILED - REQUIRES ATTENTION")
            return 1
            
    finally:
        validator.tearDown()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)