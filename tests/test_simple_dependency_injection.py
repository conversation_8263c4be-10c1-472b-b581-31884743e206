"""
Simple test for dependency injection fixes.

Tests the basic requirement that PacerRepository and AsyncDynamoDBStorage 
can be created and SequentialWorkflowManager can be instantiated.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch


class TestSimpleDependencyInjection:
    """Test basic dependency injection functionality."""

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger."""
        logger = Mock()
        logger.info = Mock()
        logger.error = Mock()
        logger.warning = Mock()
        return logger

    @pytest.fixture
    def mock_config(self):
        """Create mock configuration."""
        return {
            'region': 'us-east-1',
            'table_name': 'test-table'
        }

    def test_pacer_repository_creation(self, mock_logger, mock_config):
        """Test that PacerRepository can be imported and created."""
        from src.repositories.pacer_repository import PacerRepository
        from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
        
        # Mock storage
        with patch.object(AsyncDynamoDBStorage, '__init__', return_value=None):
            mock_storage = AsyncDynamoDBStorage(mock_config, mock_logger)
            
            # Should be able to create PacerRepository
            repo = PacerRepository(mock_storage, mock_logger)
            assert repo is not None

    def test_async_dynamodb_storage_creation(self, mock_logger, mock_config):
        """Test that AsyncDynamoDBStorage can be imported and created."""
        from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
        
        # Should be able to create AsyncDynamoDBStorage
        with patch.object(AsyncDynamoDBStorage, '__init__', return_value=None):
            storage = AsyncDynamoDBStorage(mock_config, mock_logger)
            assert storage is not None

    @pytest.mark.asyncio
    async def test_sequential_workflow_manager_with_dependencies(self, mock_logger, mock_config):
        """Test that SequentialWorkflowManager can be created with dependencies."""
        from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager
        
        # Mock all required dependencies
        mock_nav = Mock()
        mock_docket_processor = Mock()
        mock_repo = Mock()
        mock_storage = Mock()
        
        # Create SequentialWorkflowManager with mocked dependencies
        swm = SequentialWorkflowManager(
            navigation_facade=mock_nav,
            docket_processor=mock_docket_processor,
            pacer_repository=mock_repo,
            async_dynamodb_storage=mock_storage,
            logger=mock_logger
        )
        
        assert swm is not None
        assert swm.navigation_facade == mock_nav
        assert swm.docket_processor == mock_docket_processor
        assert swm.pacer_repository == mock_repo
        assert swm.async_dynamodb_storage == mock_storage

    @pytest.mark.asyncio
    async def test_sequential_workflow_manager_hard_fail_on_missing_deps(self, mock_logger):
        """Test that SequentialWorkflowManager fails hard when required dependencies are missing."""
        from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager
        
        # Create with missing dependencies
        swm = SequentialWorkflowManager(
            navigation_facade=None,  # Missing
            docket_processor=None,   # Missing  
            pacer_repository=None,   # Missing
            async_dynamodb_storage=None,  # Missing
            logger=mock_logger
        )
        
        # Should trigger hard fail (sys.exit) during initialization
        with patch('sys.exit') as mock_exit:
            await swm._initialize_service()
            
            # Verify sys.exit was called with code 1
            mock_exit.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_sequential_workflow_manager_succeeds_with_partial_deps(self, mock_logger):
        """Test that SequentialWorkflowManager succeeds with required minimum dependencies."""
        from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager
        
        # Mock minimal required dependencies
        mock_nav = Mock()
        mock_docket_processor = Mock()
        mock_repo = Mock()  # One of: repo or storage
        
        swm = SequentialWorkflowManager(
            navigation_facade=mock_nav,
            docket_processor=mock_docket_processor, 
            pacer_repository=mock_repo,
            async_dynamodb_storage=None,  # Optional since we have repo
            logger=mock_logger
        )
        
        # Should NOT trigger hard fail
        with patch('sys.exit') as mock_exit:
            await swm._initialize_service()
            
            # Verify sys.exit was NOT called
            mock_exit.assert_not_called()

    def test_main_service_factory_simple_import(self):
        """Test that we can import the main service factory classes."""
        try:
            # Try to import the core classes - they should exist
            from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
            from src.repositories.pacer_repository import PacerRepository
            from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager
            
            # If we get here, imports worked
            assert True
        except ImportError as e:
            pytest.fail(f"Failed to import required classes: {e}")

    def test_dependency_injection_interfaces(self):
        """Test that all required classes have the expected interfaces."""
        from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
        from src.repositories.pacer_repository import PacerRepository
        from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager
        
        # Check AsyncDynamoDBStorage has required constructor signature
        import inspect
        storage_sig = inspect.signature(AsyncDynamoDBStorage.__init__)
        assert 'config' in storage_sig.parameters
        assert 'logger' in storage_sig.parameters
        
        # Check PacerRepository has required constructor signature
        repo_sig = inspect.signature(PacerRepository.__init__)
        assert 'storage' in repo_sig.parameters
        
        # Check SequentialWorkflowManager has required constructor signature
        swm_sig = inspect.signature(SequentialWorkflowManager.__init__)
        assert 'pacer_repository' in swm_sig.parameters
        assert 'async_dynamodb_storage' in swm_sig.parameters


if __name__ == "__main__":
    pytest.main([__file__, "-v"])