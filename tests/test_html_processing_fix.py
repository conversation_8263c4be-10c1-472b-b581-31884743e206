"""
Test suite for PACER HTML processing and S3 upload fixes.
Tests the deep merge functionality and S3 upload implementation.
"""

import unittest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import asyncio
from typing import Dict, Any

# Mock the dependencies
class TestHTMLProcessingFix(unittest.TestCase):
    """Test the HTML processing orchestrator fixes."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_logger = Mock()
        self.mock_s3_storage = AsyncMock()
        self.mock_data_updater = Mock()
        
    async def test_deep_merge_case_data(self):
        """Test that deep merge properly combines HTML data with existing case details."""
        from src.services.html.html_processing_orchestrator import HTMLProcessingOrchestrator
        
        # Create orchestrator instance
        orchestrator = HTMLProcessingOrchestrator(
            logger=self.mock_logger,
            config={},
            s3_async_storage=self.mock_s3_storage
        )
        
        # Existing case details
        case_details = {
            'court_id': 'ilnd',
            'docket_num': '1:25-cv-09395',
            'plaintiff': ['<PERSON>'],
            'defendant': ['ABC Corp'],
            'attorney': []
        }
        
        # Parsed data from HTML
        parsed_data = {
            'case_info': {
                'versus': '<PERSON>e v. ABC Corp',
                'filing_date': '20250115',
                'assigned_to': 'Judge Smith'
            },
            'plaintiffs': [
                {'name': 'Jane Doe'},
                {'name': 'John Doe'}  # Duplicate
            ],
            'defendants': ['XYZ Inc', 'abc corp']  # One duplicate (case insensitive)
        }
        
        # Perform deep merge
        orchestrator._deep_merge_case_data(case_details, parsed_data)
        
        # Assertions
        self.assertEqual(case_details['versus'], 'John Doe v. ABC Corp')
        self.assertEqual(case_details['filing_date'], '20250115')
        self.assertEqual(case_details['assigned_to'], 'Judge Smith')
        self.assertEqual(len(case_details['plaintiff']), 2)  # John and Jane, no duplicates
        self.assertIn('John Doe', case_details['plaintiff'])
        self.assertIn('Jane Doe', case_details['plaintiff'])
        self.assertEqual(len(case_details['defendant']), 2)  # ABC Corp and XYZ Inc
        
    async def test_attorney_deduplication(self):
        """Test attorney deduplication functionality."""
        from src.services.html.html_processing_orchestrator import HTMLProcessingOrchestrator
        
        orchestrator = HTMLProcessingOrchestrator(
            logger=self.mock_logger,
            config={}
        )
        
        attorneys = [
            {'attorney_name': 'John Smith', 'law_firm': 'Smith & Associates'},
            {'attorney_name': 'John Smith', 'law_firm': 'Smith & Associates'},  # Duplicate
            {'attorney_name': 'Jane Doe', 'law_firm': 'Doe Law'},
            {'attorney_name': 'John Smith', 'law_firm': 'Different Firm'},  # Same name, different firm
        ]
        
        unique_attorneys = orchestrator._deduplicate_attorneys_list(attorneys)
        
        self.assertEqual(len(unique_attorneys), 3)  # 3 unique attorney/firm combinations
        
    async def test_s3_upload_html(self):
        """Test S3 HTML upload functionality."""
        from src.services.html.html_processing_orchestrator import HTMLProcessingOrchestrator
        
        # Mock S3 storage to return success
        self.mock_s3_storage.upload_content = AsyncMock(return_value=True)
        
        orchestrator = HTMLProcessingOrchestrator(
            logger=self.mock_logger,
            config={},
            s3_async_storage=self.mock_s3_storage
        )
        
        case_details = {
            'court_id': 'ilnd',
            'docket_num': '1:25-cv-09395',
            'new_filename': 'test_case'
        }
        
        html_content = '<html><body>Test HTML Content</body></html>'
        json_path = '/data/20250812/dockets/test_case.json'
        
        # Test upload
        success = await orchestrator._upload_html_to_s3(case_details, html_content, json_path)
        
        # Assertions
        self.assertTrue(success)
        self.mock_s3_storage.upload_content.assert_called_once()
        
        # Check S3 key format: {iso_date}/html/{base_filename}.html
        call_args = self.mock_s3_storage.upload_content.call_args
        s3_key = call_args.kwargs['object_key']
        # Should be in format: YYYYMMDD/html/filename.html
        self.assertRegex(s3_key, r'^\d{8}/html/.*\.html$')
        self.assertTrue(s3_key.endswith('.html'))
        
    async def test_s3_upload_error_handling(self):
        """Test that S3 upload errors are handled gracefully."""
        from src.services.html.html_processing_orchestrator import HTMLProcessingOrchestrator
        
        # Mock S3 storage to raise an error
        self.mock_s3_storage.upload_content = AsyncMock(side_effect=Exception("S3 Error"))
        
        orchestrator = HTMLProcessingOrchestrator(
            logger=self.mock_logger,
            config={},
            s3_async_storage=self.mock_s3_storage
        )
        
        case_details = {
            'court_id': 'ilnd',
            'docket_num': '1:25-cv-09395',
            'new_filename': 'test_case'
        }
        
        html_content = '<html><body>Test HTML Content</body></html>'
        json_path = '/data/20250812/dockets/test_case.json'
        
        # Test upload with error
        success = await orchestrator._upload_html_to_s3(case_details, html_content, json_path)
        
        # Should return False but not raise exception
        self.assertFalse(success)
        # Error should be logged
        self.mock_logger.error.assert_called()


def run_async_test(coro):
    """Helper to run async tests."""
    loop = asyncio.new_event_loop()
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


if __name__ == '__main__':
    # Run tests
    suite = unittest.TestLoader().loadTestsFromTestCase(TestHTMLProcessingFix)
    
    # Run each async test
    for test in suite:
        if hasattr(test, '_testMethodName'):
            test_method = getattr(test, test._testMethodName)
            if asyncio.iscoroutinefunction(test_method):
                print(f"Running {test._testMethodName}...")
                try:
                    run_async_test(test_method())
                    print(f"✅ {test._testMethodName} passed")
                except Exception as e:
                    print(f"❌ {test._testMethodName} failed: {e}")