#!/usr/bin/env python3
"""
PACER Migration Completion Test
Verifies all components work after migration
"""
import sys
import traceback
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

def test_core_services():
    """Test all core services can be imported"""
    print("Testing Core Services...")
    
    services = [
        "from src.pacer._core_services.browser.browser_service import BrowserService",
        "from src.pacer._core_services.configuration.configuration_service import ConfigurationService",
        "from src.pacer._core_services.case_processing.case_processing_service import CaseProcessingService",
        "from src.pacer._core_services.classification.classification_service import ClassificationService",
        "from src.pacer._core_services.relevance.relevance_service import RelevanceService",
        "from src.pacer._core_services.verification.verification_service import VerificationService",
        "from src.pacer._core_services.download_orchestration.download_orchestration_service import DownloadOrchestrationService",
        "from src.pacer._core_services.file_operations.file_operations_service import FileOperationsService",
        "from src.pacer._core_services.s3_management.s3_management_service import S3ManagementService",
        "from src.pacer._core_services.metrics_reporting.metrics_reporting_service import MetricsReportingService",
    ]
    
    for service_import in services:
        try:
            exec(service_import)
            print(f"  ✅ {service_import.split('import')[1].strip()}")
        except Exception as e:
            print(f"  ❌ Failed: {service_import}")
            print(f"     Error: {e}")
            return False
    
    return True

def test_components():
    """Test key components can be imported"""
    print("\nTesting Components...")
    
    components = [
        "from src.pacer.components.authentication.ecf_login_handler import ECFLoginHandler",
        "from src.pacer.components.authentication.session_manager import SessionManager",
        "from src.pacer.components.browser.context_factory import ContextFactory",
        "from src.pacer.components.browser.playwright_manager import PlaywrightManager",
        "from src.pacer.components.case_processing.case_parser import CaseParser",
        "from src.pacer.components.case_processing.case_validator import CaseValidator",
        "from src.pacer.components.download.download_manager import DownloadManager",
        "from src.pacer.components.navigation.page_navigator import PageNavigator",
    ]
    
    for component_import in components:
        try:
            exec(component_import)
            print(f"  ✅ {component_import.split('import')[1].strip()}")
        except Exception as e:
            print(f"  ❌ Failed: {component_import}")
            print(f"     Error: {e}")
            return False
    
    return True

def test_factories():
    """Test factory patterns"""
    print("\nTesting Factories...")
    
    try:
        from src.pacer.factories.simplified_factory import SimplifiedPacerServiceFactory
        print("  ✅ SimplifiedPacerServiceFactory")
        
        # Test factory instantiation
        factory = SimplifiedPacerServiceFactory()
        print("  ✅ Factory instantiation successful")
        
        return True
    except Exception as e:
        print(f"  ❌ Factory test failed: {e}")
        return False

def test_no_legacy_imports():
    """Verify no imports to deleted src.services.pacer remain"""
    print("\nChecking for legacy imports...")
    
    project_root = Path(__file__).parent.parent.parent
    src_dir = project_root / "src"
    
    # Check Python files for legacy imports
    legacy_found = False
    for py_file in src_dir.rglob("*.py"):
        if ".bak" in str(py_file) or "__pycache__" in str(py_file):
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            if "src.services.pacer" in content:
                print(f"  ⚠️ Legacy import found in: {py_file.relative_to(project_root)}")
                legacy_found = True
        except:
            pass
    
    if not legacy_found:
        print("  ✅ No legacy imports found")
        return True
    else:
        return False

def main():
    """Run all migration tests"""
    print("="*60)
    print("PACER MIGRATION COMPLETION TEST")
    print("="*60)
    
    all_passed = True
    
    # Run tests
    all_passed &= test_core_services()
    all_passed &= test_components()
    all_passed &= test_factories()
    all_passed &= test_no_legacy_imports()
    
    print("\n" + "="*60)
    if all_passed:
        print("✅ ALL TESTS PASSED - MIGRATION COMPLETE!")
        print("\nThe PACER module has been successfully migrated:")
        print("  • All core services functional")
        print("  • All components accessible")
        print("  • Factory patterns working")
        print("  • No legacy imports remaining")
        print("  • src/services/pacer directory removed")
        print("\n🎉 PACER MIGRATION SUCCESSFUL!")
    else:
        print("❌ SOME TESTS FAILED - Review and fix issues")
    print("="*60)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit(main())