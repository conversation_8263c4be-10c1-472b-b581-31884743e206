"""Integration tests for S3 HTML upload functionality validation.

Validates:
1. S3 service dependency injection
2. HTML upload functionality with S3 service
3. Error handling for missing S3 configuration
4. Complete HTML upload workflow
"""

import asyncio
import os
import pytest
import tempfile
from datetime import datetime
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

# Import test components
from src.pacer.services.s3_service import S3Service
from src.pacer._core_services.s3_management.s3_management_service import S3ManagementService
from src.services.uploader.s3_upload_service import S3UploadService
from src.factories.main_factory import MainServiceFactory
from src.config_models.base import WorkflowConfig


class TestS3ServiceDependencyInjection:
    """Test S3 service dependency injection validation."""
    
    @pytest.fixture
    def mock_config_with_s3(self):
        """Mock configuration with S3 enabled."""
        config = MagicMock(spec=WorkflowConfig)
        config.headless = True
        config.run_parallel = True
        config.timeout_ms = 60000
        return config
    
    @pytest.fixture
    def mock_config_without_s3(self):
        """Mock configuration without S3 settings."""
        config = MagicMock(spec=WorkflowConfig)
        config.headless = True
        config.run_parallel = False
        config.timeout_ms = 30000
        return config
    
    @pytest.fixture
    def mock_logger(self):
        """Mock logger for testing."""
        logger = MagicMock()
        return logger
    
    @pytest.fixture
    def mock_config_service(self):
        """Mock configuration service."""
        service = AsyncMock()
        service.get_config_value = AsyncMock(return_value={
            'bucket_name': 'test-bucket',
            'region': 'us-west-2',
            'enabled': True
        })
        return service
    
    @pytest.mark.asyncio
    async def test_s3_service_initialization_with_config(self, mock_logger, mock_config_service):
        """Test S3Service initializes correctly with proper configuration."""
        with patch.dict(os.environ, {
            'AWS_ACCESS_KEY_ID': 'test-key',
            'AWS_SECRET_ACCESS_KEY': 'test-secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'AWS_REGION': 'us-west-2',
            'S3_ENABLED': 'true'
        }):
            service = S3Service(
                logger=mock_logger,
                config={},
                config_service=mock_config_service
            )
            
            with patch('boto3.client') as mock_boto3:
                mock_client = MagicMock()
                mock_boto3.return_value = mock_client
                
                # Mock successful connection test
                mock_client.list_objects_v2.return_value = {'Contents': []}
                
                await service.initialize()
                
                assert service.enabled is True
                assert service.bucket_name == 'test-bucket'
                assert service.aws_region == 'us-west-2'
                assert service.s3_client is not None
                mock_boto3.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_s3_service_disabled_without_credentials(self, mock_logger):
        """Test S3Service is disabled when credentials are missing."""
        with patch.dict(os.environ, {}, clear=True):
            service = S3Service(logger=mock_logger, config={})
            await service.initialize()
            
            assert service.enabled is False
            assert service.s3_client is None
    
    @pytest.mark.asyncio
    async def test_s3_management_service_delegates_to_s3_service(self, mock_logger, mock_config_service):
        """Test S3ManagementService properly delegates to underlying S3Service."""
        management_service = S3ManagementService(
            logger=mock_logger,
            config={},
            config_service=mock_config_service
        )
        
        with patch.object(management_service._s3_service, 'initialize') as mock_init:
            await management_service.initialize()
            mock_init.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_main_factory_validates_s3_dependencies(self, mock_config_with_s3):
        """Test MainFactory validates S3 storage dependencies are available."""
        factory = MainServiceFactory(config=mock_config_with_s3)
        
        with patch('src.containers.core.create_container') as mock_create_container:
            mock_container = MagicMock()
            mock_storage = MagicMock()
            mock_storage.s3_async_storage = MagicMock(return_value=MagicMock())
            mock_storage.async_dynamodb_storage = MagicMock(return_value=MagicMock())
            mock_storage.pacer_repository = MagicMock(return_value=MagicMock())
            mock_container.storage = mock_storage
            mock_container.wire = MagicMock()
            mock_container.init_resources = MagicMock(return_value=None)
            mock_create_container.return_value = mock_container
            
            async with factory:
                # Validate that S3 storage dependency is accessible
                s3_storage = factory.get_s3_storage()
                assert s3_storage is not None


class TestS3HTMLUploadFunctionality:
    """Test HTML upload functionality with S3 service."""
    
    @pytest.fixture
    def mock_s3_service(self):
        """Mock S3 service for testing."""
        service = S3Service()
        service.enabled = True
        service.bucket_name = 'test-bucket'
        service.s3_client = MagicMock()
        service._initialized = True
        return service
    
    @pytest.fixture
    def sample_html_content(self):
        """Sample HTML content for testing."""
        return """
        <!DOCTYPE html>
        <html>
        <head><title>Test Docket</title></head>
        <body>
            <h1>Test Case: Example v. Defendant</h1>
            <p>Case Number: 1:23-cv-00001</p>
            <p>Filing Date: 2023-01-15</p>
        </body>
        </html>
        """
    
    @pytest.mark.asyncio
    async def test_s3_service_html_upload_action(self, mock_s3_service, sample_html_content):
        """Test S3Service upload_html action works correctly."""
        mock_logger = MagicMock()
        
        with patch.object(mock_s3_service, 'upload_file') as mock_upload:
            mock_upload.return_value = 'https://test-bucket.s3.us-west-2.amazonaws.com/2023-01-15/html/test.html'
            
            result = await mock_s3_service._execute_action({
                'action': 'upload_html',
                'base_filename': 'test',
                'html_content': sample_html_content,
                'iso_date': '2023-01-15',
                'court_logger': mock_logger
            })
            
            assert result['success'] is True
            assert result['s3_key'] == '2023-01-15/html/test.html'
            assert result['s3_html'] == 'https://cdn.lexgenius.ai/2023-01-15/html/test.html'
            assert 's3_url' in result
            
            # Verify upload_file was called with correct parameters
            mock_upload.assert_called_once()
            call_args = mock_upload.call_args
            assert call_args[0][1] == '2023-01-15/html/test.html'  # s3_key
    
    @pytest.mark.asyncio
    async def test_s3_service_html_upload_missing_params(self, mock_s3_service):
        """Test S3Service handles missing parameters correctly."""
        result = await mock_s3_service._execute_action({
            'action': 'upload_html',
            'base_filename': None,
            'html_content': None,
            'iso_date': '2023-01-15'
        })
        
        assert result['success'] is False
        assert 'Missing required parameters' in result['error']
    
    @pytest.mark.asyncio
    async def test_s3_upload_service_html_upload(self, sample_html_content):
        """Test S3UploadService HTML upload functionality."""
        mock_logger = MagicMock()
        mock_config = {'cloudfront_distribution_id': 'test-distribution'}
        
        with patch('src.services.uploader.s3_upload_service.UploadService.__init__') as mock_parent_init:
            mock_parent_init.return_value = None
            
            service = S3UploadService(
                logger=mock_logger,
                config=mock_config,
                s3_async_storage=MagicMock(),
                s3_client=MagicMock(),
                dynamodb_client=MagicMock(),
                bucket_name='test-bucket'
            )
            
            # Mock parent class methods
            service.upload_html_content = AsyncMock(return_value=True)
            service.invalidate_cloudfront_cache = AsyncMock(return_value=True)
            service.bucket_name = 'test-bucket'
            service.log_info = MagicMock()
            service.log_error = MagicMock()
            
            result = await service.upload_report_html(
                html_content=sample_html_content,
                date_str='20230115',
                is_weekly=False,
                is_web=True
            )
            
            assert result['date_specific'] is True
            assert result['root'] is True
            
            # Verify HTML content upload was called correctly
            assert service.upload_html_content.call_count == 2  # date-specific + root
    
    @pytest.mark.asyncio
    async def test_s3_service_file_operations(self, mock_s3_service):
        """Test S3Service file operation methods."""
        # Test file upload
        with patch('pathlib.Path.exists', return_value=True):
            with patch.object(mock_s3_service.s3_client, 'upload_file') as mock_upload:
                result = await mock_s3_service.upload_file(
                    '/tmp/test.html',
                    '2023-01-15/html/test.html'
                )
                
                assert result.startswith('https://')
                mock_upload.assert_called_once()
        
        # Test file existence check
        with patch.object(mock_s3_service.s3_client, 'head_object') as mock_head:
            exists = await mock_s3_service.file_exists('2023-01-15/html/test.html')
            assert exists is True
            mock_head.assert_called_once()
        
        # Test file deletion
        with patch.object(mock_s3_service.s3_client, 'delete_object') as mock_delete:
            result = await mock_s3_service.delete_file('2023-01-15/html/test.html')
            assert result is True
            mock_delete.assert_called_once()


class TestS3ErrorHandling:
    """Test error handling for S3 configuration issues."""
    
    @pytest.mark.asyncio
    async def test_s3_service_disabled_handling(self):
        """Test S3Service handles disabled state correctly."""
        service = S3Service()
        service.enabled = False
        service.s3_client = None
        
        result = await service._execute_action({
            'action': 'upload_html',
            'base_filename': 'test',
            'html_content': '<html></html>',
            'iso_date': '2023-01-15'
        })
        
        assert result['success'] is False
        assert 'S3 service not enabled' in result['error']
        assert result['s3_key'] == '2023-01-15/html/test.html'
        assert result['s3_html'] is None
    
    @pytest.mark.asyncio
    async def test_s3_service_upload_failure_handling(self):
        """Test S3Service handles upload failures correctly."""
        service = S3Service()
        service.enabled = True
        service.s3_client = MagicMock()
        service.bucket_name = 'test-bucket'
        service.aws_region = 'us-west-2'
        
        with patch.object(service.s3_client, 'upload_file') as mock_upload:
            mock_upload.side_effect = Exception('S3 connection failed')
            
            with patch('pathlib.Path.exists', return_value=True):
                with pytest.raises(Exception, match='S3 connection failed'):
                    await service.upload_file('/tmp/test.html', 'test.html')
    
    @pytest.mark.asyncio
    async def test_s3_service_retry_logic(self):
        """Test S3Service retry logic for failed operations."""
        service = S3Service()
        service.enabled = True
        service.s3_client = MagicMock()
        service.bucket_name = 'test-bucket'
        service.aws_region = 'us-west-2'
        service.log_warning = MagicMock()
        service.log_info = MagicMock()
        service.log_error = MagicMock()
        
        with patch.object(service.s3_client, 'upload_file') as mock_upload:
            # Fail twice, succeed on third attempt
            mock_upload.side_effect = [
                Exception('Temporary failure'),
                Exception('Another failure'),
                None  # Success
            ]
            
            with patch('pathlib.Path.exists', return_value=True):
                with patch('asyncio.sleep'):
                    result = await service.upload_file('/tmp/test.html', 'test.html')
                    
                    assert result.startswith('https://')
                    assert mock_upload.call_count == 3
                    assert service.log_warning.call_count == 2  # Two failures logged
    
    @pytest.mark.asyncio
    async def test_s3_service_health_check_with_errors(self):
        """Test S3Service health check handles errors correctly."""
        service = S3Service()
        service.enabled = True
        service.s3_client = MagicMock()
        service.bucket_name = 'test-bucket'
        service.aws_region = 'us-west-2'
        service.aws_access_key_id = 'test-key'
        service.aws_secret_access_key = 'test-secret'
        service._initialized = True
        
        with patch.object(service, '_test_connection', return_value=False):
            health = await service.health_check()
            
            assert health['service'] == 'S3Service'
            assert health['enabled'] is True
            assert health['connection_test'] is False


class TestS3IntegrationWorkflow:
    """Test complete HTML upload workflow integration."""
    
    @pytest.fixture
    def workflow_config(self):
        """Configuration for workflow testing."""
        return {
            'court_id': 'cand',
            'iso_date': '2023-01-15',
            'base_filename': 'cand_docket_12345',
            'html_content': '<!DOCTYPE html><html><body><h1>Test Docket</h1></body></html>'
        }
    
    @pytest.mark.asyncio
    async def test_complete_html_upload_workflow(self, workflow_config):
        """Test complete HTML upload workflow from start to finish."""
        mock_logger = MagicMock()
        
        # Create S3 service with mocked dependencies
        s3_service = S3Service(logger=mock_logger)
        s3_service.enabled = True
        s3_service.bucket_name = 'test-bucket'
        s3_service.s3_client = MagicMock()
        s3_service._initialized = True
        
        # Mock successful upload
        with patch.object(s3_service, 'upload_file') as mock_upload:
            mock_upload.return_value = 'https://test-bucket.s3.us-west-2.amazonaws.com/2023-01-15/html/cand_docket_12345.html'
            
            # Execute the complete workflow
            result = await s3_service._execute_action({
                'action': 'upload_html',
                'base_filename': workflow_config['base_filename'],
                'html_content': workflow_config['html_content'],
                'iso_date': workflow_config['iso_date'],
                'court_logger': mock_logger
            })
            
            # Validate successful workflow completion
            assert result['success'] is True
            assert result['s3_key'] == '2023-01-15/html/cand_docket_12345.html'
            assert result['s3_html'] == 'https://cdn.lexgenius.ai/2023-01-15/html/cand_docket_12345.html'
            assert result['s3_url'].startswith('https://')
            
            # Verify logging occurred
            mock_logger.info.assert_called()
    
    @pytest.mark.asyncio
    async def test_workflow_with_s3_management_service(self, workflow_config):
        """Test workflow using S3ManagementService facade."""
        mock_logger = MagicMock()
        
        # Create S3ManagementService
        management_service = S3ManagementService(logger=mock_logger)
        
        # Mock underlying S3Service
        mock_s3_service = MagicMock()
        mock_s3_service._execute_action = AsyncMock(return_value={
            'success': True,
            's3_key': '2023-01-15/html/cand_docket_12345.html',
            's3_html': 'https://cdn.lexgenius.ai/2023-01-15/html/cand_docket_12345.html',
            's3_url': 'https://test-bucket.s3.us-west-2.amazonaws.com/2023-01-15/html/cand_docket_12345.html'
        })
        management_service._s3_service = mock_s3_service
        
        # Execute workflow through management service
        result = await management_service._execute_action({
            'action': 'upload_html',
            'base_filename': workflow_config['base_filename'],
            'html_content': workflow_config['html_content'],
            'iso_date': workflow_config['iso_date'],
            'court_logger': mock_logger
        })
        
        # Validate delegation occurred
        assert result['success'] is True
        mock_s3_service._execute_action.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_workflow_error_recovery(self, workflow_config):
        """Test workflow handles and recovers from errors."""
        mock_logger = MagicMock()
        
        s3_service = S3Service(logger=mock_logger)
        s3_service.enabled = True
        s3_service.bucket_name = 'test-bucket'
        s3_service.s3_client = MagicMock()
        s3_service._initialized = True
        
        # Mock upload failure followed by success
        with patch.object(s3_service, 'upload_file') as mock_upload:
            mock_upload.side_effect = [
                Exception('Network timeout'),
                'https://test-bucket.s3.us-west-2.amazonaws.com/2023-01-15/html/cand_docket_12345.html'
            ]
            
            # First attempt should fail
            with pytest.raises(Exception):
                await s3_service._execute_action({
                    'action': 'upload_html',
                    'base_filename': workflow_config['base_filename'],
                    'html_content': workflow_config['html_content'],
                    'iso_date': workflow_config['iso_date'],
                    'court_logger': mock_logger
                })
            
            # Second attempt should succeed
            result = await s3_service._execute_action({
                'action': 'upload_html',
                'base_filename': workflow_config['base_filename'],
                'html_content': workflow_config['html_content'],
                'iso_date': workflow_config['iso_date'],
                'court_logger': mock_logger
            })
            
            assert result['success'] is True


class TestS3ServiceValidation:
    """Test S3 service validation and health checks."""
    
    @pytest.mark.asyncio
    async def test_s3_service_connection_validation(self):
        """Test S3Service validates connection correctly."""
        service = S3Service()
        service.enabled = True
        service.bucket_name = 'test-bucket'
        service.s3_client = MagicMock()
        
        # Test successful connection
        with patch.object(service.s3_client, 'list_objects_v2') as mock_list:
            mock_list.return_value = {'Contents': []}
            result = await service._test_connection()
            assert result is True
        
        # Test failed connection
        with patch.object(service.s3_client, 'list_objects_v2') as mock_list:
            mock_list.side_effect = Exception('Access denied')
            service.log_warning = MagicMock()
            result = await service._test_connection()
            assert result is False
            service.log_warning.assert_called()
    
    @pytest.mark.asyncio
    async def test_s3_service_bucket_info_retrieval(self):
        """Test S3Service retrieves bucket information correctly."""
        service = S3Service()
        service.enabled = True
        service.bucket_name = 'test-bucket'
        service.s3_client = MagicMock()
        
        with patch.object(service.s3_client, 'get_bucket_location') as mock_location:
            with patch.object(service.s3_client, 'list_objects_v2') as mock_list:
                mock_location.return_value = {'LocationConstraint': 'us-west-2'}
                mock_list.return_value = {
                    'Contents': [{'Key': 'test1'}, {'Key': 'test2'}],
                    'IsTruncated': False
                }
                
                info = await service.get_bucket_info()
                
                assert info['enabled'] is True
                assert info['bucket_name'] == 'test-bucket'
                assert info['region'] == 'us-west-2'
                assert info['sample_object_count'] == 2
                assert info['is_truncated'] is False
    
    @pytest.mark.asyncio
    async def test_s3_service_disabled_bucket_info(self):
        """Test S3Service handles disabled state in bucket info."""
        service = S3Service()
        service.enabled = False
        
        info = await service.get_bucket_info()
        assert info['enabled'] is False
    
    @pytest.mark.asyncio
    async def test_s3_service_presigned_url_generation(self):
        """Test S3Service generates presigned URLs correctly."""
        service = S3Service()
        service.enabled = True
        service.bucket_name = 'test-bucket'
        service.s3_client = MagicMock()
        service.log_info = MagicMock()
        
        with patch.object(service.s3_client, 'generate_presigned_url') as mock_presign:
            mock_presign.return_value = 'https://test-bucket.s3.amazonaws.com/test.html?signature=...'  
            
            url = await service.generate_presigned_url('test.html', expiration=3600)
            
            assert url.startswith('https://')
            assert 'signature=' in url
            mock_presign.assert_called_once_with(
                'get_object',
                Params={'Bucket': 'test-bucket', 'Key': 'test.html'},
                ExpiresIn=3600
            )
            service.log_info.assert_called()


# Performance and Load Testing
class TestS3PerformanceScenarios:
    """Test S3 service performance scenarios."""
    
    @pytest.mark.asyncio
    async def test_concurrent_html_uploads(self):
        """Test multiple concurrent HTML uploads."""
        service = S3Service()
        service.enabled = True
        service.bucket_name = 'test-bucket'
        service.s3_client = MagicMock()
        service._initialized = True
        
        # Mock successful uploads
        with patch.object(service, 'upload_file') as mock_upload:
            mock_upload.return_value = 'https://test-bucket.s3.us-west-2.amazonaws.com/test.html'
            
            # Create multiple upload tasks
            tasks = []
            for i in range(10):
                task = service._execute_action({
                    'action': 'upload_html',
                    'base_filename': f'test_{i}',
                    'html_content': f'<html><body>Test {i}</body></html>',
                    'iso_date': '2023-01-15',
                    'court_logger': MagicMock()
                })
                tasks.append(task)
            
            # Execute all tasks concurrently
            results = await asyncio.gather(*tasks)
            
            # Validate all uploads succeeded
            assert len(results) == 10
            assert all(result['success'] for result in results)
            assert mock_upload.call_count == 10
    
    @pytest.mark.asyncio
    async def test_large_html_content_upload(self):
        """Test upload of large HTML content."""
        service = S3Service()
        service.enabled = True
        service.bucket_name = 'test-bucket'
        service.s3_client = MagicMock()
        service._initialized = True
        
        # Create large HTML content (1MB)
        large_content = '<html><body>' + 'X' * (1024 * 1024) + '</body></html>'
        
        with patch.object(service, 'upload_file') as mock_upload:
            mock_upload.return_value = 'https://test-bucket.s3.us-west-2.amazonaws.com/large.html'
            
            result = await service._execute_action({
                'action': 'upload_html',
                'base_filename': 'large_file',
                'html_content': large_content,
                'iso_date': '2023-01-15',
                'court_logger': MagicMock()
            })
            
            assert result['success'] is True
            mock_upload.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_s3_service_timeout_handling(self):
        """Test S3Service handles timeout scenarios."""
        service = S3Service()
        service.enabled = True
        service.bucket_name = 'test-bucket'
        service.s3_client = MagicMock()
        service.log_warning = MagicMock()
        service.log_error = MagicMock()
        
        with patch.object(service.s3_client, 'upload_file') as mock_upload:
            # Simulate timeout on all retry attempts
            mock_upload.side_effect = Exception('Request timeout')
            
            with patch('pathlib.Path.exists', return_value=True):
                with patch('asyncio.sleep'):  # Speed up test
                    with pytest.raises(Exception, match='Request timeout'):
                        await service.upload_file('/tmp/test.html', 'test.html')
                    
                    # Verify retry attempts were made
                    assert mock_upload.call_count == 4  # Initial + 3 retries
                    assert service.log_warning.call_count == 3  # Log warnings for retries
                    assert service.log_error.call_count == 1  # Final error log


if __name__ == '__main__':
    # Run tests with pytest
    pytest.main([__file__, '-v', '--tb=short'])
