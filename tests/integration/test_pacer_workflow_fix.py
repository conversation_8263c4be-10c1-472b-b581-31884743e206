#!/usr/bin/env python3
"""
Integration test for PACER court processing workflow fixes.
Tests the three critical issues that were fixed:
1. System checks for existing docket_report_log files (resumption logic)
2. System properly runs civil cases report when log doesn't exist
3. System creates required data/{iso_date}/ folders
"""

import asyncio
import json
import os
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Any, Dict
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.pacer._core_services.file_operations.file_operations_service import FileOperationsService
from src.pacer.pacer_orchestrator_service import PacerOrchestratorService


class TestPacerWorkflowFix:
    """Test suite for verifying PACER workflow fixes."""

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger."""
        logger = MagicMock()
        logger.info = MagicMock()
        logger.error = MagicMock()
        logger.warning = MagicMock()
        logger.debug = MagicMock()
        return logger

    @pytest.fixture
    def iso_date(self):
        """Get ISO date for testing."""
        return datetime.now().strftime("%Y%m%d")

    @pytest.fixture
    def test_config(self, tmp_path):
        """Create test configuration."""
        return {
            "base_data_dir": str(tmp_path),
            "enable_logging": True,
            "max_retries": 3
        }

    @pytest.mark.asyncio
    async def test_directory_creation(self, mock_logger, test_config, iso_date, tmp_path):
        """Test that all required directories are created."""
        # Create file operations service
        file_service = FileOperationsService(mock_logger, test_config)
        
        # Initialize service
        await file_service.initialize()
        
        # Setup directories for the ISO date
        result = await file_service.execute({
            "action": "setup_directories",
            "iso_date": iso_date
        })
        
        # Verify all required directories were created
        base_path = tmp_path / "data" / iso_date
        
        required_dirs = [
            base_path / "dockets" / "temp",
            base_path / "dockets" / ".locks",
            base_path / "logs" / "pacer",
            base_path / "logs" / "docket_report",
            base_path / "html",
            base_path / "screenshots",
            base_path / "courts",
            base_path / "reports",
            base_path / "exports"
        ]
        
        for dir_path in required_dirs:
            assert dir_path.exists(), f"Directory {dir_path} was not created"
            assert dir_path.is_dir(), f"{dir_path} is not a directory"

    @pytest.mark.asyncio
    async def test_docket_log_checking(self, mock_logger, test_config, iso_date, tmp_path):
        """Test that system checks for existing docket_report_log files."""
        # Create orchestrator
        orchestrator = PacerOrchestratorService(mock_logger, test_config)
        
        # Create a mock docket report log file
        log_dir = tmp_path / "data" / iso_date / "logs" / "docket_report"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        court_id = "flmd"
        log_file = log_dir / f"{court_id}.json"
        
        # Create sample log data
        log_data = {
            "cases": [
                {
                    "court_id": court_id,
                    "docket_number": "8:24-cv-00001",
                    "title": "Test Case v. Defendant",
                    "filing_date": "2024-01-01"
                }
            ],
            "metadata": {
                "court_id": court_id,
                "generated_at": datetime.now().isoformat(),
                "total_cases": 1
            }
        }
        
        log_file.write_text(json.dumps(log_data, indent=2))
        
        # Test loading the log
        orchestrator.config = {"base_data_dir": str(tmp_path)}
        loaded_data = await orchestrator.load_docket_report_log(court_id, iso_date)
        
        assert loaded_data is not None, "Failed to load existing docket report log"
        assert "cases" in loaded_data, "Loaded data missing 'cases' field"
        assert len(loaded_data["cases"]) == 1, "Incorrect number of cases loaded"
        assert loaded_data["cases"][0]["docket_number"] == "8:24-cv-00001"

    @pytest.mark.asyncio
    async def test_civil_report_generation_when_no_log(self, mock_logger, test_config, iso_date, tmp_path):
        """Test that civil cases report is generated when no log exists."""
        # Create orchestrator with mocked services
        orchestrator = PacerOrchestratorService(mock_logger, test_config)
        orchestrator.config = {"base_data_dir": str(tmp_path)}
        
        # Mock browser service and docket orchestrator
        mock_browser = AsyncMock()
        mock_browser.get_browser_context = AsyncMock(return_value=MagicMock())
        
        mock_docket_orchestrator = AsyncMock()
        mock_docket_orchestrator.discover_dockets = AsyncMock(return_value={
            "cases": [
                {
                    "court_id": "flmd",
                    "docket_number": "8:24-cv-00002",
                    "title": "New Case v. Defendant"
                }
            ],
            "metadata": {
                "total_found": 1
            }
        })
        
        orchestrator._browser_service = mock_browser
        orchestrator._docket_orchestrator = mock_docket_orchestrator
        
        court_id = "flmd"
        
        # Ensure no log exists
        log_dir = tmp_path / "data" / iso_date / "logs" / "docket_report"
        log_file = log_dir / f"{court_id}.json"
        assert not log_file.exists(), "Log file should not exist initially"
        
        # Generate civil cases report
        result = await orchestrator.generate_civil_cases_report(
            court_id=court_id,
            iso_date=iso_date,
            start_date="2024-01-01",
            end_date="2024-01-31"
        )
        
        # Verify report was generated
        assert result is not None, "Failed to generate civil cases report"
        assert "cases" in result, "Generated report missing 'cases'"
        assert len(result["cases"]) > 0, "No cases in generated report"
        
        # Verify log file was created
        assert log_file.exists(), "Docket report log file was not created"
        
        # Verify log file content
        saved_data = json.loads(log_file.read_text())
        assert saved_data["cases"][0]["docket_number"] == "8:24-cv-00002"

    @pytest.mark.asyncio
    async def test_three_processing_modes(self, mock_logger, test_config, iso_date, tmp_path):
        """Test the three processing modes: special list, resume, and new."""
        orchestrator = PacerOrchestratorService(mock_logger, test_config)
        orchestrator.config = {"base_data_dir": str(tmp_path)}
        
        # Setup mock services
        mock_file_ops = AsyncMock()
        mock_file_ops.setup_directories = AsyncMock()
        
        mock_docket_orchestrator = AsyncMock()
        mock_docket_orchestrator.process_court = AsyncMock(return_value={
            "status": "completed",
            "processed_dockets": 5
        })
        
        orchestrator._file_operations_service = mock_file_ops
        orchestrator._docket_orchestrator = mock_docket_orchestrator
        
        # Mode 1: Special docket list (should not check for logs)
        request_with_list = {
            "court_id": "flmd",
            "iso_date": iso_date,
            "docket_list_input": ["8:24-cv-00001", "8:24-cv-00002"]
        }
        
        result = await orchestrator._process_single_court_with_context(
            court_id="flmd",
            config_data={},
            browser_context=None,
            request_data=request_with_list
        )
        
        assert result["status"] == "completed"
        mock_docket_orchestrator.process_court.assert_called_once()
        call_args = mock_docket_orchestrator.process_court.call_args[0][0]
        assert "docket_list_input" in call_args
        
        # Mode 2: Resume from existing log
        mock_docket_orchestrator.reset_mock()
        
        # Create existing log
        log_dir = tmp_path / "data" / iso_date / "logs" / "docket_report"
        log_dir.mkdir(parents=True, exist_ok=True)
        log_file = log_dir / "flmd.json"
        log_file.write_text(json.dumps({
            "cases": [{"docket_number": "8:24-cv-00003"}],
            "metadata": {"court_id": "flmd"}
        }))
        
        request_resume = {
            "court_id": "flmd",
            "iso_date": iso_date
        }
        
        result = await orchestrator._process_single_court_with_context(
            court_id="flmd",
            config_data={},
            browser_context=None,
            request_data=request_resume
        )
        
        assert result["status"] == "completed"
        
        # Mode 3: Generate new report (no existing log)
        mock_docket_orchestrator.reset_mock()
        
        # Remove existing log
        log_file.unlink()
        
        # Mock civil report generation
        with patch.object(orchestrator, 'generate_civil_cases_report', 
                         new=AsyncMock(return_value={"cases": [{"docket_number": "8:24-cv-00004"}]})):
            
            request_new = {
                "court_id": "flmd",
                "iso_date": iso_date,
                "start_date": "2024-01-01",
                "end_date": "2024-01-31"
            }
            
            result = await orchestrator._process_single_court_with_context(
                court_id="flmd",
                config_data={},
                browser_context=None,
                request_data=request_new
            )
            
            assert result["status"] == "completed"
            orchestrator.generate_civil_cases_report.assert_called_once()

    @pytest.mark.asyncio
    async def test_complete_workflow_integration(self, mock_logger, test_config, iso_date, tmp_path):
        """Test the complete court processing workflow end-to-end."""
        # Setup orchestrator with all services
        orchestrator = PacerOrchestratorService(mock_logger, test_config)
        orchestrator.config = {"base_data_dir": str(tmp_path)}
        
        # Mock all required services
        mock_browser = AsyncMock()
        mock_browser.get_browser_context = AsyncMock(return_value=MagicMock())
        
        mock_config = AsyncMock()
        mock_config.get_processing_config = AsyncMock(return_value={"timeout": 30})
        
        mock_file_ops = AsyncMock()
        mock_file_ops.setup_directories = AsyncMock()
        
        mock_docket_orchestrator = AsyncMock()
        mock_docket_orchestrator.process_court = AsyncMock(return_value={
            "status": "completed",
            "court_id": "flmd",
            "processed_dockets": 10,
            "cases_found": 10,
            "source": "new_report"
        })
        mock_docket_orchestrator.discover_dockets = AsyncMock(return_value={
            "cases": [{"docket_number": f"8:24-cv-{i:04d}"} for i in range(1, 11)],
            "metadata": {"total_found": 10}
        })
        
        orchestrator._browser_service = mock_browser
        orchestrator._configuration_service = mock_config
        orchestrator._file_operations_service = mock_file_ops
        orchestrator._docket_orchestrator = mock_docket_orchestrator
        
        # Execute court processing workflow
        request = {
            "court_ids": ["flmd", "flsd"],
            "mode": "sequential",
            "iso_date": iso_date,
            "start_date": "2024-01-01",
            "end_date": "2024-01-31"
        }
        
        result = await orchestrator.execute_court_processing(request)
        
        # Verify workflow completed successfully
        assert result["status"] == "completed"
        assert result["workflow"] == "court_processing"
        assert "results" in result
        
        # Verify both courts were processed
        assert "flmd" in result["results"]
        assert "flsd" in result["results"]
        
        # Verify each court result
        for court_id in ["flmd", "flsd"]:
            court_result = result["results"][court_id]
            assert court_result["status"] == "completed"
            assert court_result["processed_dockets"] == 10
            
        # Verify services were called appropriately
        assert mock_config.get_processing_config.called
        assert mock_browser.get_browser_context.called
        assert mock_docket_orchestrator.process_court.call_count == 2


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])