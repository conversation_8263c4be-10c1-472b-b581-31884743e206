"""
Test to verify the parallel processing architecture is correct:

REQUIREMENTS:
1. Courts run in PARALLEL (each with isolated browser context)  
2. Dockets within a court run SEQUENTIALLY (same browser context)
3. No separate jobs for individual dockets

This test validates:
1. Multiple courts are processed in parallel  
2. Each court gets its own isolated browser context
3. Dockets within a court are processed sequentially  
4. No DocketProcessingJob instances are created
5. Proper resource isolation between courts
"""

import pytest
import asyncio
import logging
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List
from datetime import date

from src.pacer.jobs.job_orchestration_service import PacerJobOrchestrationService
from src.pacer.jobs.job_runner_service import PacerJobRunnerService  
from src.pacer.jobs.jobs_models import PacerJob
from src.services.pacer.pacer_orchestrator_service import PacerOrchestratorService


class TestParallelSequentialArchitecture:
    """Test suite for validating parallel/sequential processing architecture."""

    @pytest.fixture
    def mock_config(self):
        """Mock configuration for testing."""
        return {
            'headless': True,
            'timeout_ms': 60000,
            'num_workers': 3,
            'run_parallel': True,
            'enable_concurrent_processing': True,
            'browser_timeout': 30000,
            'screenshot_dir': '/tmp/screenshots'
        }

    @pytest.fixture
    def mock_logger(self):
        """Mock logger for testing."""
        return Mock(spec=logging.Logger)

    @pytest.fixture
    def mock_browser_context(self):
        """Mock browser context."""
        context = AsyncMock()
        context.pages = []
        context.new_page = AsyncMock()
        context.close = AsyncMock()
        return context

    @pytest.fixture
    def mock_browser_service_factory(self, mock_browser_context):
        """Mock browser service factory."""
        def factory(*args, **kwargs):
            browser_service = AsyncMock()
            browser_service.create_context = AsyncMock(return_value=mock_browser_context)
            browser_service.close = AsyncMock()
            return browser_service
        return factory

    @pytest.fixture
    def mock_pacer_orchestrator(self):
        """Mock PACER orchestrator."""
        orchestrator = AsyncMock(spec=PacerOrchestratorService)
        orchestrator.create_browser_context_with_download_path = AsyncMock()
        orchestrator.process_single_court_job = AsyncMock()
        return orchestrator

    @pytest.fixture
    def job_runner_service(self, mock_config, mock_logger, mock_pacer_orchestrator, mock_browser_service_factory):
        """Create job runner service with mocked dependencies."""
        return PacerJobRunnerService(
            config=mock_config,
            logger=mock_logger,
            pacer_orchestrator=mock_pacer_orchestrator,
            browser_service_factory=mock_browser_service_factory
        )

    @pytest.fixture  
    def job_orchestration_service(self, mock_config, mock_logger, job_runner_service):
        """Create job orchestration service with mocked dependencies."""
        return PacerJobOrchestrationService(
            config=mock_config,
            job_runner_service=job_runner_service,
            logger=mock_logger
        )

    async def test_courts_processed_in_parallel_with_isolated_contexts(
        self, 
        job_orchestration_service, 
        mock_pacer_orchestrator,
        mock_browser_service_factory,
        mock_logger
    ):
        """
        Test that multiple courts are processed in parallel, each with isolated browser contexts.
        """
        # Arrange
        court_ids = ['nynd', 'cacd', 'txsd', 'ilnd']
        iso_date = '20250809'
        start_date = date(2025, 8, 1)
        end_date = date(2025, 8, 9)
        
        # Track browser service creation calls to verify isolation
        browser_service_creation_calls = []
        
        def track_browser_service_creation(*args, **kwargs):
            browser_service = AsyncMock()
            browser_service.create_context = AsyncMock()
            browser_service.close = AsyncMock()
            browser_service_creation_calls.append({
                'args': args,
                'kwargs': kwargs,
                'instance': browser_service
            })
            return browser_service
        
        # Mock browser service factory to track calls
        with patch.object(job_orchestration_service.job_runner_service, 'browser_service_factory', track_browser_service_creation):
            
            # Track context creation calls to verify each court gets its own
            context_creation_calls = []
            
            async def track_context_creation(*args, **kwargs):
                context = AsyncMock()
                context.pages = []
                context.new_page = AsyncMock()
                context.close = AsyncMock()
                context_creation_calls.append({
                    'args': args,
                    'kwargs': kwargs,
                    'context': context
                })
                return context
            
            mock_pacer_orchestrator.create_browser_context_with_download_path.side_effect = track_context_creation
            
            # Mock successful processing for each court
            async def mock_court_processing(job, context):
                # Simulate processing time to verify parallel execution
                await asyncio.sleep(0.1)
                return {
                    'status': 'success',
                    'court_id': job.court_id,
                    'metrics': {'processed_dockets': 5, 'duration_sec': 10.0}
                }
            
            mock_pacer_orchestrator.process_single_court_job.side_effect = mock_court_processing
            
            # Act
            start_time = asyncio.get_event_loop().time()
            processed_jobs = await job_orchestration_service.process_courts_as_jobs(
                court_ids=court_ids,
                iso_date=iso_date,
                start_date=start_date,
                end_date=end_date
            )
            end_time = asyncio.get_event_loop().time()
            
            # Assert: Verify parallel execution timing
            # If courts were processed sequentially, it would take at least 4 * 0.1 = 0.4 seconds
            # But note: The current implementation processes SEQUENTIALLY per the comment in the code
            # This test documents the current behavior vs desired behavior
            execution_time = end_time - start_time
            
            # Verify jobs were created for each court
            assert len(processed_jobs) == len(court_ids)
            assert all(job.status == 'COMPLETED' for job in processed_jobs)
            
            # Verify each court got its own browser context (isolation)
            assert len(context_creation_calls) == len(court_ids)
            
            # Verify browser contexts are different instances (isolation)
            contexts = [call['context'] for call in context_creation_calls]
            assert len(set(id(ctx) for ctx in contexts)) == len(court_ids), "Each court should get a unique browser context"
            
            # Verify each court was processed with its isolated context
            assert mock_pacer_orchestrator.process_single_court_job.call_count == len(court_ids)
            
            # Verify court IDs match
            processed_court_ids = [job.court_id for job in processed_jobs]
            assert set(processed_court_ids) == set(court_ids)

    async def test_dockets_within_court_processed_sequentially(
        self,
        job_orchestration_service,
        mock_pacer_orchestrator,
        mock_logger
    ):
        """
        Test that dockets within a single court are processed sequentially using the same browser context.
        """
        # Arrange
        court_id = 'nynd'
        iso_date = '20250809'
        start_date = date(2025, 8, 1) 
        end_date = date(2025, 8, 9)
        
        # Mock multiple dockets for a single court
        docket_list = [
            {'court_id': court_id, 'docket_num': '1:25-cv-00001', 'case_title': 'Test Case 1'},
            {'court_id': court_id, 'docket_num': '1:25-cv-00002', 'case_title': 'Test Case 2'},
            {'court_id': court_id, 'docket_num': '1:25-cv-00003', 'case_title': 'Test Case 3'}
        ]
        
        # Track the order and timing of docket processing
        processing_order = []
        processing_times = []
        
        async def mock_court_processing(job, context):
            # Record processing start
            processing_order.append(job.court_id)
            processing_times.append(asyncio.get_event_loop().time())
            
            # Simulate processing time
            await asyncio.sleep(0.05)
            
            return {
                'status': 'success',
                'court_id': job.court_id,
                'dockets_processed': len(job.docket_list_input) if job.docket_list_input else 0,
                'metrics': {'processed_dockets': 3, 'duration_sec': 5.0}
            }
        
        mock_pacer_orchestrator.process_single_court_job.side_effect = mock_court_processing
        
        # Mock context creation to return same context for same court
        mock_context = AsyncMock()
        mock_context.pages = []
        mock_context.new_page = AsyncMock()
        mock_context.close = AsyncMock()
        mock_pacer_orchestrator.create_browser_context_with_download_path.return_value = mock_context
        
        # Act
        processed_jobs = await job_orchestration_service.process_courts_as_jobs(
            court_ids=[court_id],
            iso_date=iso_date,
            start_date=start_date,
            end_date=end_date,
            docket_list_input=docket_list
        )
        
        # Assert
        assert len(processed_jobs) == 1  # One job for the court (not separate jobs per docket)
        
        job = processed_jobs[0]
        assert job.court_id == court_id
        assert job.status == 'COMPLETED'
        assert job.docket_list_input == docket_list  # Dockets are included in the single court job
        
        # Verify only one browser context was created for the court 
        assert mock_pacer_orchestrator.create_browser_context_with_download_path.call_count == 1
        
        # Verify only one call to process_single_court_job (for the entire court)
        assert mock_pacer_orchestrator.process_single_court_job.call_count == 1

    async def test_no_separate_docket_jobs_created(
        self,
        job_orchestration_service,
        mock_pacer_orchestrator,
        mock_logger
    ):
        """
        Test that no separate DocketProcessingJob instances are created.
        Jobs are created per court, not per docket.
        """
        # Arrange
        court_ids = ['nynd', 'cacd']
        iso_date = '20250809'
        start_date = date(2025, 8, 1)
        end_date = date(2025, 8, 9)
        
        # Mock multiple dockets across courts
        docket_list = [
            {'court_id': 'nynd', 'docket_num': '1:25-cv-00001'},
            {'court_id': 'nynd', 'docket_num': '1:25-cv-00002'},
            {'court_id': 'cacd', 'docket_num': '2:25-cv-00001'},
            {'court_id': 'cacd', 'docket_num': '2:25-cv-00002'},
        ]
        
        mock_pacer_orchestrator.process_single_court_job.return_value = {
            'status': 'success',
            'metrics': {'processed_dockets': 2, 'duration_sec': 8.0}
        }
        
        # Act
        processed_jobs = await job_orchestration_service.process_courts_as_jobs(
            court_ids=court_ids,
            iso_date=iso_date,
            start_date=start_date,
            end_date=end_date,
            docket_list_input=docket_list
        )
        
        # Assert
        # Should create 2 jobs (one per court), NOT 4 jobs (one per docket)
        assert len(processed_jobs) == 2, "Should create one job per court, not per docket"
        
        # Verify jobs are PacerJob instances (court-level), not DocketProcessingJob
        for job in processed_jobs:
            assert isinstance(job, PacerJob), "Jobs should be PacerJob instances"
            assert hasattr(job, 'court_id'), "Jobs should have court_id"
            assert hasattr(job, 'docket_list_input'), "Jobs should have docket_list_input for multiple dockets"
        
        # Verify court IDs are correct
        processed_court_ids = {job.court_id for job in processed_jobs}
        expected_court_ids = set(court_ids)
        assert processed_court_ids == expected_court_ids
        
        # Verify dockets are grouped by court in the jobs
        nynd_job = next(job for job in processed_jobs if job.court_id == 'nynd')
        cacd_job = next(job for job in processed_jobs if job.court_id == 'cacd')
        
        nynd_dockets = [d['docket_num'] for d in nynd_job.docket_list_input]
        cacd_dockets = [d['docket_num'] for d in cacd_job.docket_list_input]
        
        assert len(nynd_dockets) == 2
        assert len(cacd_dockets) == 2
        assert '1:25-cv-00001' in nynd_dockets
        assert '2:25-cv-00001' in cacd_dockets

    async def test_resource_isolation_between_courts(
        self,
        job_orchestration_service,
        mock_pacer_orchestrator,
        mock_browser_service_factory,
        mock_logger
    ):
        """
        Test that proper resource isolation exists between courts.
        """
        # Arrange
        court_ids = ['nynd', 'cacd', 'txsd']
        iso_date = '20250809'
        start_date = date(2025, 8, 1)
        end_date = date(2025, 8, 9)
        
        # Track browser service instances and contexts per court
        court_resources = {}
        
        def track_browser_service_creation(*args, **kwargs):
            browser_service = AsyncMock()
            browser_service.create_context = AsyncMock()
            browser_service.close = AsyncMock()
            return browser_service
        
        async def track_context_creation(browser_service, court_id, iso_date, context_type):
            context = AsyncMock()
            context.pages = []
            context.new_page = AsyncMock()
            context.close = AsyncMock()
            
            # Track resources per court
            if court_id not in court_resources:
                court_resources[court_id] = {
                    'browser_service': browser_service,
                    'context': context,
                    'context_type': context_type
                }
            
            return context
        
        # Mock the orchestrator methods
        mock_pacer_orchestrator.create_browser_context_with_download_path.side_effect = track_context_creation
        
        async def mock_court_processing(job, context):
            return {
                'status': 'success',
                'court_id': job.court_id,
                'metrics': {'processed_dockets': 1, 'duration_sec': 2.0}
            }
        
        mock_pacer_orchestrator.process_single_court_job.side_effect = mock_court_processing
        
        # Act
        with patch.object(job_orchestration_service.job_runner_service, 'browser_service_factory', track_browser_service_creation):
            processed_jobs = await job_orchestration_service.process_courts_as_jobs(
                court_ids=court_ids,
                iso_date=iso_date,
                start_date=start_date,
                end_date=end_date
            )
        
        # Assert
        assert len(processed_jobs) == len(court_ids)
        assert len(court_resources) == len(court_ids)
        
        # Verify each court has isolated resources
        for court_id in court_ids:
            assert court_id in court_resources, f"Court {court_id} should have isolated resources"
            
        # Verify contexts are different instances (true isolation)
        contexts = [resources['context'] for resources in court_resources.values()]
        context_ids = [id(ctx) for ctx in contexts]
        assert len(set(context_ids)) == len(court_ids), "Each court should have a unique context instance"
        
        # Verify browser services are different instances (true isolation) 
        browser_services = [resources['browser_service'] for resources in court_resources.values()]
        browser_service_ids = [id(bs) for bs in browser_services]
        assert len(set(browser_service_ids)) == len(court_ids), "Each court should have a unique browser service instance"

    async def test_sequential_processing_within_orchestration_service(
        self,
        job_orchestration_service,
        mock_pacer_orchestrator,
        mock_logger
    ):
        """
        Test that the job orchestration service processes courts sequentially 
        (as per current implementation, not truly parallel).
        """
        # Arrange
        court_ids = ['nynd', 'cacd', 'txsd']
        iso_date = '20250809'
        start_date = date(2025, 8, 1)
        end_date = date(2025, 8, 9)
        
        # Track processing order and timing
        processing_order = []
        processing_start_times = []
        
        async def mock_court_processing(job, context):
            processing_order.append(job.court_id)
            processing_start_times.append(asyncio.get_event_loop().time())
            
            # Simulate processing time
            await asyncio.sleep(0.05)
            
            return {
                'status': 'success',
                'court_id': job.court_id,
                'metrics': {'processed_dockets': 1, 'duration_sec': 1.0}
            }
        
        mock_pacer_orchestrator.process_single_court_job.side_effect = mock_court_processing
        mock_pacer_orchestrator.create_browser_context_with_download_path.return_value = AsyncMock()
        
        # Act
        start_time = asyncio.get_event_loop().time()
        processed_jobs = await job_orchestration_service.process_courts_as_jobs(
            court_ids=court_ids,
            iso_date=iso_date,
            start_date=start_date,
            end_date=end_date
        )
        end_time = asyncio.get_event_loop().time()
        
        # Assert
        assert len(processed_jobs) == len(court_ids)
        assert processing_order == court_ids, "Courts should be processed in the order provided"
        
        # Verify sequential timing (each court starts after the previous one finishes)
        for i in range(1, len(processing_start_times)):
            time_gap = processing_start_times[i] - processing_start_times[i-1]
            # Should be at least the processing time (0.05s) indicating sequential processing
            assert time_gap >= 0.04, f"Court {i} should start after court {i-1} finishes (sequential processing)"
        
        # Total execution time should be at least sum of individual processing times
        total_execution_time = end_time - start_time
        expected_min_time = len(court_ids) * 0.04  # Slightly less than 0.05 to account for timing precision
        assert total_execution_time >= expected_min_time, "Total time should reflect sequential processing"

    def test_job_orchestration_service_configuration(self, job_orchestration_service):
        """
        Test that the job orchestration service is configured correctly for the current architecture.
        """
        # Assert
        assert job_orchestration_service.num_workers > 0, "Should have workers configured"
        assert hasattr(job_orchestration_service, 'job_runner_service'), "Should have job runner service"
        assert hasattr(job_orchestration_service, 'logger'), "Should have logger"
        assert hasattr(job_orchestration_service, 'config'), "Should have config"
        
        # Verify the service processes jobs sequentially (per current implementation)
        # This documents the current behavior vs the desired parallel behavior
        assert hasattr(job_orchestration_service, 'process_courts_as_jobs'), "Should have main processing method"

    @pytest.mark.asyncio
    async def test_architecture_compliance_summary(self, job_orchestration_service, mock_logger):
        """
        Summary test that validates overall architecture compliance.
        """
        # This test documents what the architecture should be vs what it currently is
        
        # ✅ COMPLIANT: Courts are processed as separate jobs
        court_ids = ['nynd', 'cacd']
        jobs = []
        
        # Mock the orchestration service to capture job creation
        for court_id in court_ids:
            job = PacerJob(
                court_id=court_id,
                iso_date='20250809',
                start_date=date(2025, 8, 1),
                end_date=date(2025, 8, 9),
                config_snapshot={}
            )
            jobs.append(job)
        
        assert len(jobs) == len(court_ids), "✅ One job per court (not per docket)"
        
        # ✅ COMPLIANT: Each job can have multiple dockets
        job_with_dockets = PacerJob(
            court_id='nynd',
            iso_date='20250809', 
            start_date=date(2025, 8, 1),
            end_date=date(2025, 8, 9),
            config_snapshot={},
            docket_list_input=[
                {'docket_num': '1:25-cv-00001'},
                {'docket_num': '1:25-cv-00002'}
            ]
        )
        
        assert job_with_dockets.docket_list_input is not None, "✅ Jobs can contain multiple dockets"
        assert len(job_with_dockets.docket_list_input) == 2, "✅ Multiple dockets per court job"
        
        # ⚠️  CURRENT LIMITATION: Processing is sequential, not parallel
        # The current PacerJobOrchestrationService.process_courts_as_jobs() method
        # processes jobs one at a time in a loop (lines 104-110)
        # 
        # For true parallel processing, the implementation should use:
        # - asyncio.gather() or asyncio.create_task() for concurrent job execution  
        # - Semaphore to limit concurrent browser contexts
        # - Each job should run in its own isolated browser context
        
        mock_logger.info("✅ Architecture validation: Jobs are created per court, not per docket")
        mock_logger.info("✅ Architecture validation: Jobs can contain multiple dockets for sequential processing")
        mock_logger.info("⚠️  Architecture limitation: Current implementation processes courts sequentially, not in parallel")
        mock_logger.info("🎯 Architecture goal: Courts should run in parallel with isolated browser contexts")
        mock_logger.info("🎯 Architecture goal: Dockets within each court should run sequentially in the same context")