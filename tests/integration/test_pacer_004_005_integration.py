#!/usr/bin/env python3
"""
Integration tests for PACER-004 (Authentication Service) and PACER-005 (Browser Service).

Tests the integration between authentication and browser services ensuring:
- Class-level locks for PACER authentication
- Isolated browser contexts for 12-worker parallel processing
- Court-specific download paths maintained
- ECF login handler works correctly
- Session management functions properly
"""

import asyncio
import pytest
import tempfile
from pathlib import Path
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

# Test framework imports
from src.pacer._core_services.browser.browser_service import BrowserService
from src.pacer._core_services.browser.auth_manager import AuthenticationManager
from src.pacer._core_services.browser.browser_manager import BrowserManager
from src.pacer._core_services.browser.navigation_manager import NavigationManager
from src.pacer.components.authentication.ecf_login_handler import ECFLoginHandler
from src.pacer.components.authentication.session_manager import SessionManager
from src.pacer.components.authentication.credential_validator import CredentialValidator


class TestAuthenticationServiceIntegration:
    """Test Authentication Service integration (PACER-004)."""

    @pytest.fixture
    def auth_config(self):
        """Authentication configuration for testing."""
        return {
            'username_prod': 'test_user',
            'password_prod': 'test_pass',
            'ecf_username': 'ecf_user',
            'ecf_password': 'ecf_pass',
            'session_timeout': 30,
            'login_timeout': 30000,
            'retry_attempts': 3
        }

    @pytest.fixture
    def mock_logger(self):
        """Mock logger for testing."""
        logger = MagicMock()
        logger.info = MagicMock()
        logger.warning = MagicMock()
        logger.error = MagicMock()
        logger.debug = MagicMock()
        return logger

    @pytest.fixture
    def mock_page(self):
        """Mock Playwright page for testing."""
        page = AsyncMock()
        page.goto = AsyncMock()
        page.fill = AsyncMock()
        page.click = AsyncMock()
        page.wait_for_selector = AsyncMock()
        page.wait_for_load_state = AsyncMock()
        page.url = "https://pacer.uscourts.gov/test"
        page.text_content = AsyncMock(return_value="Test content")
        
        # Mock context
        page.context = AsyncMock()
        page.context.cookies = AsyncMock(return_value=[])
        
        return page

    @pytest.mark.asyncio
    async def test_authentication_manager_initialization(self, auth_config, mock_logger):
        """Test authentication manager initializes correctly."""
        auth_manager = AuthenticationManager(logger=mock_logger, config=auth_config)
        
        assert auth_manager._config == auth_config
        assert auth_manager._logger == mock_logger
        assert isinstance(auth_manager._authenticated_sessions, dict)
        assert isinstance(auth_manager._main_login_lock, asyncio.Lock)

    @pytest.mark.asyncio
    async def test_credential_validation(self, auth_config, mock_logger):
        """Test credential validation functionality."""
        auth_manager = AuthenticationManager(logger=mock_logger, config=auth_config)
        
        # Test main credentials
        credentials = await auth_manager.get_credentials({'credential_type': 'main'})
        assert credentials['username'] == 'test_user'
        assert credentials['password'] == 'test_pass'
        
        # Test ECF credentials
        ecf_credentials = await auth_manager.get_credentials({
            'credential_type': 'ecf',
            'court_id': 'cacd'
        })
        assert ecf_credentials['username'] == 'ecf_user'
        assert ecf_credentials['password'] == 'ecf_pass'

    @pytest.mark.asyncio
    async def test_session_management(self, auth_config, mock_logger):
        """Test session state management."""
        auth_manager = AuthenticationManager(logger=mock_logger, config=auth_config)
        
        # Test session validation (not authenticated initially)
        result = await auth_manager.validate_session({'context_id': 'test_context'})
        assert result is False
        
        # Mark as authenticated
        auth_manager._authenticated_sessions['test_context'] = True
        auth_manager._last_activity['test_context'] = datetime.now()
        
        # Test session validation (now authenticated)
        result = await auth_manager.validate_session({'context_id': 'test_context'})
        assert result is True
        
        # Test session clearing
        await auth_manager.clear_session({'context_id': 'test_context'})
        assert 'test_context' not in auth_manager._authenticated_sessions

    @pytest.mark.asyncio
    async def test_main_login_with_lock(self, auth_config, mock_logger, mock_page):
        """Test main PACER login with class-level locking."""
        auth_manager = AuthenticationManager(logger=mock_logger, config=auth_config)
        
        # Mock successful login sequence
        mock_page.wait_for_selector = AsyncMock(return_value=True)
        
        with patch.object(auth_manager, '_check_login_status', return_value=False):
            with patch.object(auth_manager, '_store_session_cookies'):
                result = await auth_manager.perform_login({
                    'page': mock_page,
                    'context_id': 'test_context'
                })
                
                assert result is True
                assert 'test_context' in auth_manager._authenticated_sessions
                
                # Verify login form was filled
                mock_page.fill.assert_any_call(auth_manager.USERNAME_SELECTOR, 'test_user')
                mock_page.fill.assert_any_call(auth_manager.PASSWORD_SELECTOR, 'test_pass')
                mock_page.click.assert_called_with(auth_manager.LOGIN_BUTTON_SELECTOR)

    @pytest.mark.asyncio
    async def test_ecf_login_handler(self, auth_config, mock_logger):
        """Test ECF login handler functionality."""
        ecf_handler = ECFLoginHandler(logger=mock_logger, config=auth_config)
        
        # Mock navigator for ECF login
        mock_navigator = AsyncMock()
        mock_navigator.goto = AsyncMock()
        mock_navigator.locator = AsyncMock()
        mock_navigator.page = AsyncMock()
        mock_navigator.page.locator = AsyncMock()
        mock_navigator.page.wait_for_load_state = AsyncMock()
        mock_navigator.save_screenshot = AsyncMock()
        
        # Mock successful login sequence
        mock_locator = AsyncMock()
        mock_locator.first = AsyncMock()
        mock_locator.first.click = AsyncMock()
        mock_locator.first.is_visible = AsyncMock(return_value=True)
        mock_navigator.locator.return_value = mock_locator
        
        with patch.object(ecf_handler, '_navigate_to_court_ecf', return_value=True):
            with patch.object(ecf_handler, '_login_to_court_ecf', return_value=True):
                with patch.object(ecf_handler, '_handle_client_code_page', return_value=True):
                    result = await ecf_handler.perform_ecf_login_sequence(
                        mock_navigator, 'cacd'
                    )
                    
                    assert result is True

    @pytest.mark.asyncio
    async def test_parallel_authentication_locks(self, auth_config, mock_logger, mock_page):
        """Test that authentication locks work for parallel processing."""
        auth_manager = AuthenticationManager(logger=mock_logger, config=auth_config)
        
        # Track login attempts
        login_attempts = []
        
        async def mock_login(context_id):
            login_attempts.append(f"start_{context_id}")
            
            # Simulate login process
            result = await auth_manager.perform_login({
                'page': mock_page,
                'context_id': context_id
            })
            
            login_attempts.append(f"end_{context_id}")
            return result
        
        # Mock successful login
        with patch.object(auth_manager, '_check_login_status', return_value=False):
            with patch.object(auth_manager, '_store_session_cookies'):
                mock_page.wait_for_selector = AsyncMock(return_value=True)
                
                # Start multiple login attempts concurrently
                tasks = [
                    mock_login('context_1'),
                    mock_login('context_2'),
                    mock_login('context_3')
                ]
                
                results = await asyncio.gather(*tasks)
                
                # All should succeed
                assert all(results)
                
                # Login attempts should be serialized due to the lock
                assert len(login_attempts) == 6  # 3 start + 3 end


class TestBrowserServiceIntegration:
    """Test Browser Service integration (PACER-005)."""

    @pytest.fixture
    def browser_config(self):
        """Browser configuration for testing."""
        return {
            'headless': True,
            'timeout': 30000,
            'viewport_width': 1920,
            'viewport_height': 1080,
            'max_navigation_retries': 3,
            'session_timeout': 30,
            'username_prod': 'test_user',
            'password_prod': 'test_pass'
        }

    @pytest.fixture
    def mock_logger(self):
        """Mock logger for testing."""
        logger = MagicMock()
        logger.info = MagicMock()
        logger.warning = MagicMock()
        logger.error = MagicMock()
        logger.debug = MagicMock()
        return logger

    @pytest.fixture
    def mock_playwright(self):
        """Mock Playwright instance for testing."""
        playwright = AsyncMock()
        
        # Mock browser
        browser = AsyncMock()
        browser.is_connected = MagicMock(return_value=True)
        browser.new_context = AsyncMock()
        
        # Mock context
        context = AsyncMock()
        context.new_page = AsyncMock()
        context.storage_state = AsyncMock(return_value={})
        context.clear_cookies = AsyncMock()
        context.clear_permissions = AsyncMock()
        browser.new_context.return_value = context
        
        # Mock page
        page = AsyncMock()
        page.set_default_timeout = MagicMock()
        page.set_default_navigation_timeout = MagicMock()
        page.goto = AsyncMock()
        page.url = "https://test.com"
        context.new_page.return_value = page
        
        # Mock browser type
        browser_type = AsyncMock()
        browser_type.launch = AsyncMock(return_value=browser)
        playwright.chromium = browser_type
        
        return playwright

    @pytest.mark.asyncio
    async def test_browser_service_initialization(self, browser_config, mock_logger):
        """Test browser service initializes correctly."""
        browser_service = BrowserService(logger=mock_logger, config=browser_config)
        
        assert browser_service._config == browser_config
        assert browser_service._logger == mock_logger
        assert isinstance(browser_service._contexts, dict)
        assert isinstance(browser_service._pages, dict)

    @pytest.mark.asyncio
    async def test_isolated_context_creation(self, browser_config, mock_logger, mock_playwright):
        """Test creation of isolated browser contexts for parallel processing."""
        browser_service = BrowserService(logger=mock_logger, config=browser_config)
        browser_service._playwright = mock_playwright
        browser_service._browser = mock_playwright.chromium.launch.return_value
        
        # Create multiple contexts
        context_ids = []
        for i in range(12):  # Test 12-worker parallel processing
            context_id = await browser_service.create_context({
                'context_id': f'worker_{i}'
            })
            context_ids.append(context_id)
        
        # Verify all contexts were created
        assert len(context_ids) == 12
        assert len(browser_service._contexts) == 12
        
        # Verify contexts are isolated
        for i, context_id in enumerate(context_ids):
            assert context_id == f'worker_{i}'
            assert context_id in browser_service._contexts

    @pytest.mark.asyncio
    async def test_court_specific_download_paths(self, browser_config, mock_logger, mock_playwright):
        """Test court-specific download paths are maintained."""
        browser_service = BrowserService(logger=mock_logger, config=browser_config)
        browser_service._playwright = mock_playwright
        browser_service._browser = mock_playwright.chromium.launch.return_value
        
        # Create contexts with court-specific configurations
        courts = ['cacd', 'nysd', 'dcd', 'txnd']
        
        for court in courts:
            with tempfile.TemporaryDirectory() as temp_dir:
                download_path = Path(temp_dir) / court
                download_path.mkdir(exist_ok=True)
                
                context_id = await browser_service.create_context({
                    'context_id': f'court_{court}',
                    'download_path': str(download_path)
                })
                
                # Verify context was created with court-specific configuration
                assert context_id == f'court_{court}'
                assert context_id in browser_service._contexts

    @pytest.mark.asyncio
    async def test_browser_navigation_manager(self, browser_config, mock_logger):
        """Test navigation manager functionality."""
        nav_manager = NavigationManager(logger=mock_logger, config=browser_config)
        
        # Test URL building
        url = await nav_manager.build_url({
            'path': '/test',
            'params': {'case': '123', 'court': 'cacd'}
        })
        
        assert 'test' in url
        assert 'case=123' in url
        assert 'court=cacd' in url
        
        # Test ECF URL building
        ecf_url = await nav_manager.build_ecf_url({
            'court_id': 'cacd',
            'path': '/cgi-bin/DktRpt.pl',
            'params': {'case_num': '123'}
        })
        
        assert 'ecf.cacd.uscourts.gov' in ecf_url
        assert 'DktRpt.pl' in ecf_url

    @pytest.mark.asyncio
    async def test_integrated_authentication_flow(self, browser_config, mock_logger, mock_playwright):
        """Test integrated authentication flow through browser service."""
        browser_service = BrowserService(logger=mock_logger, config=browser_config)
        browser_service._playwright = mock_playwright
        browser_service._browser = mock_playwright.chromium.launch.return_value
        
        # Create context and page
        context_id = await browser_service.create_context({'context_id': 'auth_test'})
        page_id = await browser_service.create_page({'context_id': context_id})
        
        # Mock successful authentication
        with patch.object(browser_service._auth_manager, 'perform_login', return_value=True):
            result = await browser_service.login({
                'page_id': page_id,
                'context_id': context_id
            })
            
            assert result is True
            assert browser_service._authenticated_sessions.get(context_id) is True

    @pytest.mark.asyncio
    async def test_browser_health_monitoring(self, browser_config, mock_logger, mock_playwright):
        """Test browser health monitoring functionality."""
        browser_service = BrowserService(logger=mock_logger, config=browser_config)
        browser_service._playwright = mock_playwright
        browser_service._browser = mock_playwright.chromium.launch.return_value
        
        # Test health check
        health = await browser_service.check_health({})
        
        assert health['service'] == 'BrowserService'
        assert health['browser_running'] is True
        assert health['contexts'] == 0
        assert health['pages'] == 0


class TestAuthenticationBrowserIntegration:
    """Test integration between Authentication and Browser services."""

    @pytest.fixture
    def integration_config(self):
        """Configuration for integration testing."""
        return {
            'headless': True,
            'username_prod': 'test_user',
            'password_prod': 'test_pass',
            'ecf_username': 'ecf_user',
            'ecf_password': 'ecf_pass',
            'session_timeout': 30,
            'browser_timeout': 30000
        }

    @pytest.fixture
    def mock_logger(self):
        """Mock logger for testing."""
        logger = MagicMock()
        logger.info = MagicMock()
        logger.warning = MagicMock()
        logger.error = MagicMock()
        logger.debug = MagicMock()
        return logger

    @pytest.mark.asyncio
    async def test_full_authentication_browser_flow(self, integration_config, mock_logger):
        """Test complete authentication + browser integration flow."""
        # Create browser service with authentication
        browser_service = BrowserService(logger=mock_logger, config=integration_config)
        
        # Mock Playwright initialization
        with patch('playwright.async_api.async_playwright') as mock_async_playwright:
            mock_playwright = AsyncMock()
            mock_browser = AsyncMock()
            mock_context = AsyncMock()
            mock_page = AsyncMock()
            
            # Setup mocks
            mock_async_playwright.return_value.start.return_value = mock_playwright
            mock_playwright.chromium.launch.return_value = mock_browser
            mock_browser.is_connected.return_value = True
            mock_browser.new_context.return_value = mock_context
            mock_context.new_page.return_value = mock_page
            mock_page.goto = AsyncMock()
            mock_page.wait_for_selector = AsyncMock()
            mock_context.storage_state = AsyncMock(return_value={})
            
            # Initialize browser service
            await browser_service._initialize_service()
            
            # Create context and page
            context_id = await browser_service.create_context({'context_id': 'integration_test'})
            page_id = await browser_service.create_page({'context_id': context_id})
            
            # Mock authentication success
            with patch.object(browser_service._auth_manager, 'perform_login', return_value=True):
                # Test login
                login_result = await browser_service.login({
                    'page_id': page_id,
                    'context_id': context_id
                })
                
                assert login_result is True
                
                # Test session validation
                session_valid = await browser_service.validate_session({'context_id': context_id})
                assert session_valid is True
                
                # Test navigation
                nav_result = await browser_service.navigate({
                    'page_id': page_id,
                    'url': 'https://pacer.uscourts.gov/test'
                })
                assert nav_result is True

    @pytest.mark.asyncio
    async def test_parallel_worker_authentication(self, integration_config, mock_logger):
        """Test authentication works correctly with 12 parallel workers."""
        browser_service = BrowserService(logger=mock_logger, config=integration_config)
        
        # Mock Playwright for parallel testing
        with patch('playwright.async_api.async_playwright') as mock_async_playwright:
            mock_playwright = AsyncMock()
            mock_browser = AsyncMock()
            
            mock_async_playwright.return_value.start.return_value = mock_playwright
            mock_playwright.chromium.launch.return_value = mock_browser
            mock_browser.is_connected.return_value = True
            
            # Setup context and page creation
            contexts_created = []
            pages_created = []
            
            async def create_mock_context(**kwargs):
                mock_context = AsyncMock()
                mock_context.new_page = AsyncMock()
                mock_context.storage_state = AsyncMock(return_value={})
                contexts_created.append(mock_context)
                
                async def create_mock_page():
                    mock_page = AsyncMock()
                    pages_created.append(mock_page)
                    return mock_page
                
                mock_context.new_page = create_mock_page
                return mock_context
            
            mock_browser.new_context = create_mock_context
            
            # Initialize browser service
            await browser_service._initialize_service()
            
            # Create 12 workers with authentication
            workers = []
            for i in range(12):
                context_id = await browser_service.create_context({'context_id': f'worker_{i}'})
                page_id = await browser_service.create_page({'context_id': context_id})
                workers.append((context_id, page_id))
            
            # Authenticate all workers in parallel
            with patch.object(browser_service._auth_manager, 'perform_login', return_value=True):
                auth_tasks = [
                    browser_service.login({
                        'page_id': page_id,
                        'context_id': context_id
                    })
                    for context_id, page_id in workers
                ]
                
                results = await asyncio.gather(*auth_tasks)
                
                # All authentications should succeed
                assert all(results)
                assert len(contexts_created) == 12
                assert len(pages_created) == 12


if __name__ == '__main__':
    # Run integration tests
    pytest.main([__file__, '-v', '--tb=short'])