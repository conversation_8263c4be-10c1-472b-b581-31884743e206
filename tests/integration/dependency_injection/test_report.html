
<!DOCTYPE html>
<html>
<head>
    <title>Dependency Injection Validation Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 30px; }
        .module { margin-bottom: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .module-header { background: #e9e9e9; padding: 10px; font-weight: bold; }
        .module-content { padding: 15px; }
        .passed { color: green; }
        .failed { color: red; }
        .status-passed { background: #d4edda; color: #155724; }
        .status-failed { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Dependency Injection Validation Test Report</h1>
        <p>Generated: 2025-08-12 17:06:31</p>
    </div>
    
    <div class="summary">
        <h2>Overall Summary</h2>
        <p><strong>Total Tests:</strong> 50</p>
        <p><strong>Passed:</strong> <span class="passed">50</span></p>
        <p><strong>Failed:</strong> <span class="failed">0</span></p>
        <p><strong>Success Rate:</strong> 100.0%</p>
    </div>
    
    <h2>Test Module Results</h2>

    <div class="module">
        <div class="module-header status-passed">
            test_main_service_factory_validation.py - PASSED
        </div>
        <div class="module-content">
            <p><strong>Duration:</strong> 1.41s</p>
            <p><strong>Tests:</strong> 11 total, 11 passed, 0 failed</p>
        </div>
    </div>

    <div class="module">
        <div class="module-header status-passed">
            test_service_instantiation_validation.py - PASSED
        </div>
        <div class="module-content">
            <p><strong>Duration:</strong> 3.36s</p>
            <p><strong>Tests:</strong> 13 total, 13 passed, 0 failed</p>
        </div>
    </div>

    <div class="module">
        <div class="module-header status-passed">
            test_container_integration_validation.py - PASSED
        </div>
        <div class="module-content">
            <p><strong>Duration:</strong> 1.55s</p>
            <p><strong>Tests:</strong> 13 total, 13 passed, 0 failed</p>
        </div>
    </div>

    <div class="module">
        <div class="module-header status-passed">
            test_repository_storage_validation.py - PASSED
        </div>
        <div class="module-content">
            <p><strong>Duration:</strong> 1.67s</p>
            <p><strong>Tests:</strong> 13 total, 13 passed, 0 failed</p>
        </div>
    </div>

</body>
</html>
