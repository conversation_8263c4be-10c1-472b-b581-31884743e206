"""
Dependency Injection Validation Test Runner.

This script runs the complete dependency injection validation test suite
and provides a comprehensive report of the results.

Usage:
    python tests/integration/dependency_injection/run_dependency_injection_validation.py
    
Or run individual test modules:
    pytest tests/integration/dependency_injection/test_main_service_factory_validation.py -v
    pytest tests/integration/dependency_injection/test_service_instantiation_validation.py -v
    pytest tests/integration/dependency_injection/test_container_integration_validation.py -v
    pytest tests/integration/dependency_injection/test_repository_storage_validation.py -v
"""

import asyncio
import logging
import os
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path


def setup_test_environment():
    """Set up the test environment with required paths and variables."""
    # Get the project root directory
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent.parent
    
    # Add src to Python path
    src_path = project_root / "src"
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    
    # Set environment variables for testing
    test_env = {
        "LEXGENIUS_PROJECT_ROOT": str(project_root),
        "AWS_ACCESS_KEY_ID": "test_key_id",
        "AWS_SECRET_ACCESS_KEY": "test_secret_key",
        "AWS_REGION": "us-west-2",
        "S3_BUCKET_NAME": "test-lexgenius-bucket",
        "DYNAMODB_ENDPOINT": "http://localhost:8000",
        "PACER_USERNAME_PROD": "test_username",
        "PACER_PASSWORD_PROD": "test_password",
        "PYTHONPATH": str(src_path)
    }
    
    os.environ.update(test_env)
    return project_root, test_env


def run_test_module(module_path, project_root):
    """Run a single test module and return results."""
    print(f"\n{'='*80}")
    print(f"Running: {module_path}")
    print(f"{'='*80}")
    
    start_time = time.time()
    
    try:
        # Run pytest on the specific module
        cmd = [
            sys.executable, "-m", "pytest", 
            str(module_path),
            "-v",
            "--tb=short",
            "--no-header",
            "--disable-warnings"
        ]
        
        result = subprocess.run(
            cmd,
            cwd=str(project_root),
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout per test module
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            "module": module_path.name,
            "success": result.returncode == 0,
            "duration": duration,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
        
    except subprocess.TimeoutExpired:
        return {
            "module": module_path.name,
            "success": False,
            "duration": time.time() - start_time,
            "stdout": "",
            "stderr": "Test timed out after 5 minutes",
            "returncode": -1
        }
    except Exception as e:
        return {
            "module": module_path.name,
            "success": False,
            "duration": time.time() - start_time,
            "stdout": "",
            "stderr": f"Failed to run test: {e}",
            "returncode": -2
        }


def parse_test_results(result):
    """Parse pytest output to extract test statistics."""
    stdout = result["stdout"]
    
    # Count passed/failed tests
    passed_tests = stdout.count("PASSED")
    failed_tests = stdout.count("FAILED")
    error_tests = stdout.count("ERROR")
    skipped_tests = stdout.count("SKIPPED")
    
    return {
        "passed": passed_tests,
        "failed": failed_tests,
        "errors": error_tests,
        "skipped": skipped_tests,
        "total": passed_tests + failed_tests + error_tests + skipped_tests
    }


def print_result_summary(result):
    """Print a summary of test results."""
    stats = parse_test_results(result)
    status = "✅ PASSED" if result["success"] else "❌ FAILED"
    
    print(f"\nModule: {result['module']}")
    print(f"Status: {status}")
    print(f"Duration: {result['duration']:.2f}s")
    print(f"Tests: {stats['total']} total, {stats['passed']} passed, {stats['failed']} failed")
    
    if not result["success"]:
        print(f"\nErrors/Failures:")
        if result["stderr"]:
            print(result["stderr"])
        if "FAILED" in result["stdout"]:
            # Extract failed test information
            lines = result["stdout"].split("\n")
            for line in lines:
                if "FAILED" in line or "ERROR" in line:
                    print(f"  {line}")


def generate_html_report(results, project_root):
    """Generate an HTML report of test results."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    total_tests = sum(parse_test_results(r)["total"] for r in results)
    total_passed = sum(parse_test_results(r)["passed"] for r in results)
    total_failed = sum(parse_test_results(r)["failed"] for r in results)
    
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Dependency Injection Validation Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .summary {{ background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 30px; }}
        .module {{ margin-bottom: 20px; border: 1px solid #ddd; border-radius: 5px; }}
        .module-header {{ background: #e9e9e9; padding: 10px; font-weight: bold; }}
        .module-content {{ padding: 15px; }}
        .passed {{ color: green; }}
        .failed {{ color: red; }}
        .status-passed {{ background: #d4edda; color: #155724; }}
        .status-failed {{ background: #f8d7da; color: #721c24; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Dependency Injection Validation Test Report</h1>
        <p>Generated: {timestamp}</p>
    </div>
    
    <div class="summary">
        <h2>Overall Summary</h2>
        <p><strong>Total Tests:</strong> {total_tests}</p>
        <p><strong>Passed:</strong> <span class="passed">{total_passed}</span></p>
        <p><strong>Failed:</strong> <span class="failed">{total_failed}</span></p>
        <p><strong>Success Rate:</strong> {(total_passed/total_tests*100) if total_tests > 0 else 0:.1f}%</p>
    </div>
    
    <h2>Test Module Results</h2>
"""
    
    for result in results:
        stats = parse_test_results(result)
        status_class = "status-passed" if result["success"] else "status-failed"
        status_text = "PASSED" if result["success"] else "FAILED"
        
        html_content += f"""
    <div class="module">
        <div class="module-header {status_class}">
            {result['module']} - {status_text}
        </div>
        <div class="module-content">
            <p><strong>Duration:</strong> {result['duration']:.2f}s</p>
            <p><strong>Tests:</strong> {stats['total']} total, {stats['passed']} passed, {stats['failed']} failed</p>
        </div>
    </div>
"""
    
    html_content += """
</body>
</html>
"""
    
    # Save HTML report
    report_path = project_root / "tests" / "integration" / "dependency_injection" / "test_report.html"
    with open(report_path, "w") as f:
        f.write(html_content)
    
    return report_path


def main():
    """Main function to run the complete dependency injection validation suite."""
    print("🚀 Dependency Injection Validation Test Suite")
    print("=" * 80)
    print("Setting up test environment...")
    
    # Setup environment
    project_root, test_env = setup_test_environment()
    
    # Define test modules to run
    test_dir = project_root / "tests" / "integration" / "dependency_injection"
    test_modules = [
        test_dir / "test_main_service_factory_validation.py",
        test_dir / "test_service_instantiation_validation.py", 
        test_dir / "test_container_integration_validation.py",
        test_dir / "test_repository_storage_validation.py"
    ]
    
    # Verify test modules exist
    missing_modules = [m for m in test_modules if not m.exists()]
    if missing_modules:
        print(f"❌ Missing test modules: {missing_modules}")
        return 1
    
    print(f"Found {len(test_modules)} test modules to run")
    print(f"Project root: {project_root}")
    
    # Run all test modules
    results = []
    start_time = time.time()
    
    for module in test_modules:
        result = run_test_module(module, project_root)
        results.append(result)
        print_result_summary(result)
    
    total_duration = time.time() - start_time
    
    # Generate overall summary
    print(f"\n{'='*80}")
    print("DEPENDENCY INJECTION VALIDATION SUMMARY")
    print(f"{'='*80}")
    
    total_modules = len(results)
    passed_modules = sum(1 for r in results if r["success"])
    failed_modules = total_modules - passed_modules
    
    total_tests = sum(parse_test_results(r)["total"] for r in results)
    total_passed_tests = sum(parse_test_results(r)["passed"] for r in results)
    total_failed_tests = sum(parse_test_results(r)["failed"] for r in results)
    
    print(f"Modules: {total_modules} total, {passed_modules} passed, {failed_modules} failed")
    print(f"Tests: {total_tests} total, {total_passed_tests} passed, {total_failed_tests} failed")
    print(f"Duration: {total_duration:.2f}s")
    
    if failed_modules == 0:
        print(f"\n✅ ALL DEPENDENCY INJECTION TESTS PASSED!")
        print("The dependency injection system is working correctly.")
    else:
        print(f"\n❌ {failed_modules} module(s) failed")
        print("Check the individual test results above for details.")
    
    # Generate HTML report
    try:
        report_path = generate_html_report(results, project_root)
        print(f"\n📊 HTML report generated: {report_path}")
    except Exception as e:
        print(f"\n⚠️  Failed to generate HTML report: {e}")
    
    # Return appropriate exit code
    return 0 if failed_modules == 0 else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)