"""
Service Instantiation Validation Tests.

This test suite validates that key services can be instantiated successfully
with proper dependency injection and that missing dependencies are handled correctly.

FOCUS AREAS:
- Service factory dependency registration
- Service instantiation with mocked dependencies  
- Error propagation for missing critical dependencies
- Validation of service initialization chains
"""

import asyncio
import logging
import os
import tempfile
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

# Import service classes for validation
from src.services.pacer.pacer_orchestrator_service import PacerOrchestratorService
from src.pacer.services.browser_service import BrowserService
from src.pacer.services.configuration_service import ConfigurationService
from src.pacer.services.case_processing_service import CaseProcessingService
from src.pacer.services.relevance_service import RelevanceService
from src.pacer.services.classification_service import ClassificationService
from src.pacer.services.verification_service import VerificationService

# Import factory classes
from src.factories.main_factory import MainServiceFactory
from src.factories.service_factory import ServiceFactory, AsyncServiceFactory

# Import storage classes
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.infrastructure.storage.dynamodb_base import DynamoDBBaseStorage

# Import repository classes
from src.repositories.pacer_repository import PacerRepository

# Import configuration
from src.config_models.base import WorkflowConfig


class TestServiceInstantiationValidation:
    """Test suite for validating service instantiation with dependency injection."""

    @pytest.fixture
    def mock_config(self):
        """Create a comprehensive mock configuration."""
        config = {
            "headless": True,
            "run_parallel": True,
            "timeout_ms": 60000,
            "pacer": {
                "headless": True,
                "run_parallel": True,
                "timeout_ms": 60000,
                "username_prod": "test_user",
                "password_prod": "test_pass"
            },
            "aws_region": "us-west-2",
            "aws_access_key": "test_key",
            "aws_secret_key": "test_secret",
            "s3_bucket_name": "test-bucket",
            "dynamodb_endpoint": "http://localhost:8000"
        }
        return config

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger for testing."""
        return logging.getLogger("test_service_validation")

    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Set up environment
            original_root = os.environ.get("LEXGENIUS_PROJECT_ROOT")
            os.environ["LEXGENIUS_PROJECT_ROOT"] = temp_dir
            
            # Create required directories
            os.makedirs(os.path.join(temp_dir, "data"), exist_ok=True)
            os.makedirs(os.path.join(temp_dir, "logs"), exist_ok=True)
            
            yield temp_dir
            
            # Cleanup
            if original_root:
                os.environ["LEXGENIUS_PROJECT_ROOT"] = original_root
            elif "LEXGENIUS_PROJECT_ROOT" in os.environ:
                del os.environ["LEXGENIUS_PROJECT_ROOT"]

    @pytest.mark.asyncio
    async def test_browser_service_instantiation(self, mock_config, mock_logger, temp_workspace):
        """
        TEST 1: Validate BrowserService can be instantiated with proper dependencies.
        """
        try:
            # Create BrowserService
            browser_service = BrowserService(
                logger=mock_logger,
                config=mock_config
            )
            
            assert browser_service is not None, "BrowserService should be created"
            assert browser_service.logger is not None, "BrowserService should have logger"
            assert browser_service.config is not None, "BrowserService should have config"
            
            # Test initialization
            await browser_service.initialize()
            
            print("✅ TEST 1 PASSED: BrowserService instantiation successful")
            
        except Exception as e:
            pytest.fail(f"BrowserService instantiation failed: {e}")

    @pytest.mark.asyncio
    async def test_configuration_service_instantiation(self, mock_config, mock_logger, temp_workspace):
        """
        TEST 2: Validate ConfigurationService can be instantiated with proper dependencies.
        """
        try:
            # Create ConfigurationService
            config_service = ConfigurationService(
                logger=mock_logger,
                config=mock_config
            )
            
            assert config_service is not None, "ConfigurationService should be created"
            assert config_service.logger is not None, "ConfigurationService should have logger"
            assert config_service.config is not None, "ConfigurationService should have config"
            
            # Test initialization
            await config_service.initialize()
            
            print("✅ TEST 2 PASSED: ConfigurationService instantiation successful")
            
        except Exception as e:
            pytest.fail(f"ConfigurationService instantiation failed: {e}")

    @pytest.mark.asyncio
    async def test_case_processing_service_instantiation(self, mock_config, mock_logger, temp_workspace):
        """
        TEST 3: Validate CaseProcessingService can be instantiated with proper dependencies.
        """
        try:
            # Create CaseProcessingService
            case_service = CaseProcessingService(
                logger=mock_logger,
                config=mock_config
            )
            
            assert case_service is not None, "CaseProcessingService should be created"
            assert case_service.logger is not None, "CaseProcessingService should have logger"
            assert case_service.config is not None, "CaseProcessingService should have config"
            
            # Test initialization
            await case_service.initialize()
            
            print("✅ TEST 3 PASSED: CaseProcessingService instantiation successful")
            
        except Exception as e:
            pytest.fail(f"CaseProcessingService instantiation failed: {e}")

    @pytest.mark.asyncio
    async def test_relevance_service_instantiation(self, mock_config, mock_logger, temp_workspace):
        """
        TEST 4: Validate RelevanceService can be instantiated with proper dependencies.
        """
        try:
            # Create RelevanceService
            relevance_service = RelevanceService(
                logger=mock_logger,
                config=mock_config
            )
            
            assert relevance_service is not None, "RelevanceService should be created"
            assert relevance_service.logger is not None, "RelevanceService should have logger"
            assert relevance_service.config is not None, "RelevanceService should have config"
            
            # Test initialization
            await relevance_service.initialize()
            
            print("✅ TEST 4 PASSED: RelevanceService instantiation successful")
            
        except Exception as e:
            pytest.fail(f"RelevanceService instantiation failed: {e}")

    @pytest.mark.asyncio
    async def test_service_factory_instantiation(self, mock_config, mock_logger, temp_workspace):
        """
        TEST 5: Validate ServiceFactory can create services with proper dependencies.
        """
        try:
            # Create mock storage
            mock_storage = MagicMock(spec=DynamoDBBaseStorage)
            
            # Create ServiceFactory
            service_factory = ServiceFactory(storage=mock_storage)
            
            assert service_factory is not None, "ServiceFactory should be created"
            assert service_factory.storage is not None, "ServiceFactory should have storage"
            
            # Test service creation
            pacer_service = service_factory.get_pacer_service()
            assert pacer_service is not None, "PACER service should be created"
            
            district_courts_service = service_factory.get_district_courts_service()
            assert district_courts_service is not None, "District courts service should be created"
            
            print("✅ TEST 5 PASSED: ServiceFactory instantiation and service creation successful")
            
        except Exception as e:
            pytest.fail(f"ServiceFactory instantiation failed: {e}")

    @pytest.mark.asyncio
    async def test_async_service_factory_instantiation(self, mock_config, mock_logger, temp_workspace):
        """
        TEST 6: Validate AsyncServiceFactory can create services with proper dependencies.
        """
        try:
            # Create mock async storage
            mock_async_storage = MagicMock(spec=AsyncDynamoDBStorage)
            
            # Create AsyncServiceFactory
            async_service_factory = AsyncServiceFactory(storage=mock_async_storage)
            
            assert async_service_factory is not None, "AsyncServiceFactory should be created"
            assert async_service_factory.storage is not None, "AsyncServiceFactory should have storage"
            
            # Test async service creation
            pacer_service = await async_service_factory.get_pacer_service()
            assert pacer_service is not None, "Async PACER service should be created"
            
            print("✅ TEST 6 PASSED: AsyncServiceFactory instantiation and service creation successful")
            
        except Exception as e:
            pytest.fail(f"AsyncServiceFactory instantiation failed: {e}")

    @pytest.mark.asyncio
    async def test_missing_storage_dependency_error(self, mock_config, mock_logger, temp_workspace):
        """
        TEST 7: Validate proper error handling when storage dependency is missing.
        """
        try:
            # Attempt to create AsyncServiceFactory without storage (should fail)
            with pytest.raises(ValueError, match="AsyncServiceFactory requires an initialized AsyncDynamoDBStorage"):
                AsyncServiceFactory(storage=None)
            
            print("✅ TEST 7 PASSED: Missing storage dependency error handling validated")
            
        except Exception as e:
            if "requires an initialized AsyncDynamoDBStorage" not in str(e):
                pytest.fail(f"Unexpected error for missing storage: {e}")

    @pytest.mark.asyncio
    async def test_service_dependency_chain_validation(self, mock_config, mock_logger, temp_workspace):
        """
        TEST 8: Validate complete service dependency chain works correctly.
        """
        try:
            # Create dependency chain: Storage -> Repository -> Service
            
            # 1. Create storage (mocked)
            mock_storage = MagicMock(spec=AsyncDynamoDBStorage)
            
            # 2. Create repository with storage
            pacer_repo = PacerRepository(storage=mock_storage)
            assert pacer_repo is not None, "PacerRepository should be created"
            assert pacer_repo.storage is not None, "Repository should have storage"
            
            # 3. Create services that use repository through factory
            async_factory = AsyncServiceFactory(storage=mock_storage)
            pacer_service = await async_factory.get_pacer_service()
            
            assert pacer_service is not None, "Service should be created"
            
            print("✅ TEST 8 PASSED: Service dependency chain validation successful")
            
        except Exception as e:
            pytest.fail(f"Service dependency chain validation failed: {e}")

    @pytest.mark.asyncio
    async def test_service_initialization_chain(self, mock_config, mock_logger, temp_workspace):
        """
        TEST 9: Validate that services can be initialized in proper dependency order.
        """
        try:
            # Create services in dependency order
            config_service = ConfigurationService(logger=mock_logger, config=mock_config)
            await config_service.initialize()
            
            browser_service = BrowserService(logger=mock_logger, config=mock_config)
            await browser_service.initialize()
            
            case_service = CaseProcessingService(logger=mock_logger, config=mock_config)
            await case_service.initialize()
            
            relevance_service = RelevanceService(logger=mock_logger, config=mock_config)
            await relevance_service.initialize()
            
            # All services should be initialized successfully
            assert config_service._initialized, "ConfigurationService should be initialized"
            assert browser_service._initialized, "BrowserService should be initialized"
            assert case_service._initialized, "CaseProcessingService should be initialized"
            assert relevance_service._initialized, "RelevanceService should be initialized"
            
            print("✅ TEST 9 PASSED: Service initialization chain successful")
            
        except Exception as e:
            pytest.fail(f"Service initialization chain failed: {e}")

    @pytest.mark.asyncio
    async def test_main_factory_service_integration(self, mock_config, mock_logger, temp_workspace):
        """
        TEST 10: Validate integration between MainServiceFactory and service creation.
        """
        # Set required environment variables
        os.environ.update({
            "PACER_USERNAME_PROD": "test_user",
            "PACER_PASSWORD_PROD": "test_pass",
            "AWS_ACCESS_KEY_ID": "test_key",
            "AWS_SECRET_ACCESS_KEY": "test_secret",
            "AWS_REGION": "us-west-2",
            "S3_BUCKET_NAME": "test-bucket"
        })
        
        # Create workflow config
        workflow_config = MagicMock()
        workflow_config.headless = True
        workflow_config.run_parallel = True
        workflow_config.timeout_ms = 60000
        workflow_config.model_dump = MagicMock(return_value=mock_config)
        
        factory = MainServiceFactory(config=workflow_config, shutdown_event=asyncio.Event())
        
        try:
            async with factory:
                # Test that storage services can be created
                dynamodb_storage = factory.get_dynamodb_storage()
                s3_storage = factory.get_s3_storage()
                
                assert dynamodb_storage is not None, "DynamoDB storage should be created"
                assert s3_storage is not None, "S3 storage should be created"
                
                # Test that factory can create complex services
                with patch('src.pacer.components.browser.navigator.PacerNavigator'):
                    with patch('src.pacer.components.authentication.ecf_login_handler.ECFLoginHandler'):
                        with patch('src.infrastructure.storage.s3_async.S3AsyncStorage'):
                            pacer_orchestrator = await factory.create_pacer_orchestrator_service()
                            assert pacer_orchestrator is not None, "PacerOrchestratorService should be created"
                
                print("✅ TEST 10 PASSED: MainFactory service integration successful")
                
        except Exception as e:
            pytest.fail(f"MainFactory service integration failed: {e}")

    @pytest.mark.asyncio
    async def test_service_error_propagation(self, mock_config, mock_logger, temp_workspace):
        """
        TEST 11: Validate proper error propagation in service creation chain.
        """
        try:
            # Create service with intentionally broken config
            broken_config = None  # This should cause issues
            
            try:
                service = BrowserService(logger=mock_logger, config=broken_config)
                await service.initialize()
                # If we get here, the service handled None config gracefully
                print("✅ TEST 11 PASSED: Service handles broken config gracefully")
            except Exception as init_error:
                # Expected - service should fail with broken config
                assert "config" in str(init_error).lower() or init_error is not None
                print("✅ TEST 11 PASSED: Service properly fails with broken config")
                
        except Exception as e:
            pytest.fail(f"Service error propagation test failed unexpectedly: {e}")

    @pytest.mark.asyncio
    async def test_concurrent_service_creation(self, mock_config, mock_logger, temp_workspace):
        """
        TEST 12: Validate that services can be created concurrently without issues.
        """
        try:
            # Create multiple services concurrently
            async def create_service(service_class, delay=0):
                if delay:
                    await asyncio.sleep(delay)
                service = service_class(logger=mock_logger, config=mock_config)
                await service.initialize()
                return service
            
            # Create services concurrently
            tasks = [
                create_service(ConfigurationService, 0.1),
                create_service(BrowserService, 0.2),
                create_service(CaseProcessingService, 0.1),
                create_service(RelevanceService, 0.3)
            ]
            
            services = await asyncio.gather(*tasks)
            
            assert len(services) == 4, "All services should be created"
            assert all(service is not None for service in services), "All services should be valid"
            assert all(service._initialized for service in services), "All services should be initialized"
            
            print("✅ TEST 12 PASSED: Concurrent service creation successful")
            
        except Exception as e:
            pytest.fail(f"Concurrent service creation failed: {e}")


def test_service_instantiation_validation_suite():
    """
    Main test function that documents the service instantiation validation test suite.
    """
    print("🚀 Starting Service Instantiation Validation Test Suite")
    print("=" * 80)
    
    test_descriptions = [
        "TEST 1: BrowserService instantiation validation",
        "TEST 2: ConfigurationService instantiation validation",
        "TEST 3: CaseProcessingService instantiation validation", 
        "TEST 4: RelevanceService instantiation validation",
        "TEST 5: ServiceFactory instantiation and service creation validation",
        "TEST 6: AsyncServiceFactory instantiation and service creation validation",
        "TEST 7: Missing storage dependency error handling validation",
        "TEST 8: Service dependency chain validation",
        "TEST 9: Service initialization chain validation",
        "TEST 10: MainFactory service integration validation",
        "TEST 11: Service error propagation validation",
        "TEST 12: Concurrent service creation validation"
    ]
    
    print("Test Suite Coverage:")
    for i, description in enumerate(test_descriptions, 1):
        print(f"  {i:2d}. {description}")
    
    print("\nTo run this test suite:")
    print("  pytest tests/integration/dependency_injection/test_service_instantiation_validation.py -v")
    print("=" * 80)


if __name__ == "__main__":
    test_service_instantiation_validation_suite()