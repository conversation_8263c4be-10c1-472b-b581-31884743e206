"""
Comprehensive Integration Test for Data Merging and Filename Validation Workflow.

This test validates the complete workflow integration to ensure:
1. HTML content is properly merged into case data before JSON save
2. All JSON filenames use validated create_base_filename method
3. Data consistency is maintained across all checkpoint integrations
4. All workflow paths (orchestrator, sequential, unified) work correctly
"""

import pytest
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch
from datetime import datetime

# Import components for integration testing
from src.pacer.components.data_merging.data_merger import PacerDataMerger
from src.pacer.components.validation.filename_validator import FilenameValidator
from src.pacer.services.file_operations_service import FileOperationsService


class TestComprehensiveWorkflowIntegration:
    """Comprehensive integration tests for data merging workflow."""

    @pytest.fixture
    async def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    @pytest.fixture
    async def enhanced_file_ops_service(self, temp_dir):
        """Create fully configured enhanced file operations service."""
        config = {
            'file_operations': {
                'storage': {
                    'base_directory': str(temp_dir),
                    's3_enabled': False
                }
            }
        }
        
        # Create service with all components
        service = FileOperationsService(config=config)
        await service.initialize()
        
        # Verify components are initialized
        assert service.data_merger is not None
        assert service.filename_validator is not None
        
        return service

    @pytest.fixture
    def comprehensive_case_data(self):
        """Comprehensive case data for testing."""
        return {
            'court_id': 'ilnd',
            'docket_num': '1:25-cv-09286',
            'versus': 'John Doe & Associates v. Jane Smith Corporation',
            'filing_date': '2025-01-15',
            'case_title': 'Complex Business Litigation Case',
            'plaintiffs': ['John Doe & Associates'],
            'defendants': ['Jane Smith Corporation', 'XYZ Holdings LLC'],
            'attorney': ['Attorney Name 1', 'Attorney Name 2'],
            'assigned_to': 'Judge William H. Smith',
            'jury_demand': 'Yes',
            'cause': '28:1332 Diversity-Contract',
            'nature_of_suit': '190 Other Contract',
            'date_filed': '01/15/2025',
            'demand': '$1,000,000',
            'is_removal': False,
            'html_only': False,
            'is_transferred': False
        }

    @pytest.fixture
    def complex_html_content(self):
        """Complex HTML content with case details."""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>PACER Case Details</title>
        </head>
        <body>
            <div class="case-header">
                <h1>Case 1:25-cv-09286-WHS</h1>
                <h2>John Doe & Associates v. Jane Smith Corporation</h2>
            </div>
            <div class="case-info">
                <table>
                    <tr><td>Filing Date:</td><td>01/15/2025</td></tr>
                    <tr><td>Judge:</td><td>William H. Smith</td></tr>
                    <tr><td>Nature of Suit:</td><td>190 Other Contract</td></tr>
                    <tr><td>Jury Demand:</td><td>Yes</td></tr>
                </table>
            </div>
            <div class="parties">
                <h3>Parties</h3>
                <div class="plaintiffs">
                    <h4>Plaintiffs:</h4>
                    <ul>
                        <li>John Doe & Associates</li>
                    </ul>
                </div>
                <div class="defendants">
                    <h4>Defendants:</h4>
                    <ul>
                        <li>Jane Smith Corporation</li>
                        <li>XYZ Holdings LLC</li>
                    </ul>
                </div>
            </div>
            <div class="proceedings">
                <h3>Proceedings</h3>
                <table class="proceeding-table">
                    <tr>
                        <th>Date Filed</th>
                        <th>Description</th>
                        <th>Docket Text</th>
                    </tr>
                    <tr>
                        <td>01/15/2025</td>
                        <td>Complaint</td>
                        <td>COMPLAINT against Jane Smith Corporation, XYZ Holdings LLC</td>
                    </tr>
                    <tr>
                        <td>01/20/2025</td>
                        <td>Motion to Dismiss</td>
                        <td>MOTION to Dismiss Complaint filed by Jane Smith Corporation</td>
                    </tr>
                </table>
            </div>
        </body>
        </html>
        """

    @pytest.fixture
    def s3_metadata(self):
        """S3 metadata for testing."""
        return {
            's3_key': 'pacer/2025/01/15/ilnd/dockets/html/case_1_25_cv_09286.html',
            'upload_timestamp': '2025-01-15T10:30:00Z',
            'upload_status': 'success',
            'file_size': 5432,
            'content_type': 'text/html',
            'etag': 'abc123def456'
        }

    @pytest.mark.asyncio
    async def test_complete_workflow_with_html_merging(
        self, 
        enhanced_file_ops_service, 
        comprehensive_case_data, 
        complex_html_content, 
        s3_metadata,
        temp_dir
    ):
        """Test complete workflow with comprehensive HTML merging."""
        
        # Execute enhanced save operation
        saved_path = await enhanced_file_ops_service.save_case_data(
            case_data=comprehensive_case_data,
            iso_date='20250115',
            html_content=complex_html_content,
            s3_metadata=s3_metadata
        )
        
        # Verify file was created
        assert os.path.exists(saved_path)
        
        # Load and verify saved data
        with open(saved_path, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        # Verify comprehensive data merging
        self._verify_comprehensive_data_merging(saved_data, comprehensive_case_data, complex_html_content, s3_metadata)
        
        # Verify filename validation
        self._verify_filename_validation(saved_path, comprehensive_case_data)
        
        # Verify data integrity
        self._verify_data_integrity(saved_data)

    def _verify_comprehensive_data_merging(self, saved_data, original_case_data, html_content, s3_metadata):
        """Verify comprehensive data merging was performed correctly."""
        
        # 1. Verify original case data is preserved
        for key, value in original_case_data.items():
            assert key in saved_data
            assert saved_data[key] == value
        
        # 2. Verify HTML content is merged
        assert 'html_content' in saved_data
        assert saved_data['html_content'] == html_content
        assert saved_data['html_content_size'] == len(html_content)
        assert 'html_content_hash' in saved_data
        
        # Verify HTML metadata
        assert 'html_extracted_timestamp' in saved_data
        assert saved_data['html_content_type'] == 'text/html'
        assert saved_data['html_encoding'] == 'utf-8'
        
        # 3. Verify S3 metadata is merged
        assert 's3_upload' in saved_data
        assert saved_data['s3_upload'] == s3_metadata
        assert 's3_upload_timestamp' in saved_data
        
        # 4. Verify merge metadata
        assert '_html_merged_timestamp' in saved_data
        assert saved_data['_html_merge_status'] == 'success'
        assert saved_data['_data_merge_version'] == '1.0'
        assert saved_data['_record_merge_complete'] is True
        
        # 5. Verify save metadata
        assert '_saved_timestamp' in saved_data
        assert saved_data['_saved_by'] == 'EnhancedFileOperationsService'
        assert saved_data['_save_version'] == '2.0'
        assert '_json_filename' in saved_data

    def _verify_filename_validation(self, saved_path, case_data):
        """Verify filename validation was applied correctly."""
        filename = os.path.basename(saved_path)
        
        # Verify filename structure
        assert filename.endswith('.json')
        assert 'ilnd' in filename.lower()
        assert '25_cv_09286' in filename
        
        # Verify no forbidden characters
        forbidden_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*', ' ']
        for char in forbidden_chars:
            assert char not in filename
        
        # Verify length is reasonable
        assert len(filename) <= 200

    def _verify_data_integrity(self, saved_data):
        """Verify data integrity across all merged components."""
        
        # 1. Verify required fields are present
        required_fields = ['court_id', 'docket_num']
        for field in required_fields:
            assert field in saved_data
            assert saved_data[field]
        
        # 2. Verify HTML integrity if present
        if 'html_content' in saved_data:
            html_content = saved_data['html_content']
            expected_size = saved_data.get('html_content_size', 0)
            assert len(html_content) == expected_size
            
            # Verify hash if present
            if 'html_content_hash' in saved_data:
                import hashlib
                actual_hash = hashlib.md5(html_content.encode()).hexdigest()
                assert actual_hash == saved_data['html_content_hash']
        
        # 3. Verify base_filename consistency
        if 'base_filename' in saved_data:
            base_filename = saved_data['base_filename']
            court_id = saved_data.get('court_id', '')
            docket_num = saved_data.get('docket_num', '')
            
            assert court_id.lower() in base_filename.lower()
            # Check for docket components
            if ':' in docket_num:
                clean_docket = docket_num.split(':', 1)[1]
                assert any(part in base_filename for part in clean_docket.replace('-', '_').split('_'))

    @pytest.mark.asyncio
    async def test_workflow_orchestrator_integration(self, enhanced_file_ops_service, comprehensive_case_data):
        """Test integration with PacerWorkflowOrchestrator pattern."""
        
        # Simulate workflow orchestrator docket processing
        html_content = '<html><body>Workflow orchestrator test</body></html>'
        
        # Mock S3 metadata that would come from orchestrator
        s3_metadata = {
            'upload_timestamp': datetime.now().isoformat(),
            's3_key': 'orchestrator/test/file.html',
            'upload_status': 'success'
        }
        
        # Execute save as orchestrator would
        saved_path = await enhanced_file_ops_service.save_case_data(
            case_data=comprehensive_case_data,
            iso_date='20250115',
            html_content=html_content,
            s3_metadata=s3_metadata
        )
        
        # Verify orchestrator-specific metadata
        with open(saved_path, 'r') as f:
            saved_data = json.load(f)
        
        assert 's3_upload' in saved_data
        assert saved_data['s3_upload']['s3_key'] == 'orchestrator/test/file.html'
        assert saved_data['html_content'] == html_content
        assert saved_data['_enhanced_save_version'] == '2.0'

    @pytest.mark.asyncio
    async def test_sequential_workflow_early_save(self, enhanced_file_ops_service, comprehensive_case_data):
        """Test integration with SequentialWorkflowManager early save pattern."""
        
        # Simulate sequential workflow early save scenario (html_only=True)
        case_data_html_only = comprehensive_case_data.copy()
        case_data_html_only['html_only'] = True
        
        html_content = '<html><body>Sequential early save test</body></html>'
        
        # Mock S3 metadata for early save
        s3_metadata = {
            'early_save_reason': 'html_only=True',
            'save_trigger': 'html_only',
            'upload_timestamp': datetime.now().isoformat(),
            's3_key': 'sequential/early/file.html',
            'processing_step': 'step_7_early_save'
        }
        
        # Execute early save as sequential manager would
        saved_path = await enhanced_file_ops_service.save_case_data(
            case_data=case_data_html_only,
            iso_date='20250115',
            html_content=html_content,
            s3_metadata=s3_metadata
        )
        
        # Verify early save specific metadata
        with open(saved_path, 'r') as f:
            saved_data = json.load(f)
        
        assert saved_data['html_only'] is True
        assert 's3_upload' in saved_data
        assert saved_data['s3_upload']['save_trigger'] == 'html_only'
        assert saved_data['s3_upload']['processing_step'] == 'step_7_early_save'

    @pytest.mark.asyncio
    async def test_sequential_workflow_complete_save(self, enhanced_file_ops_service, comprehensive_case_data):
        """Test integration with SequentialWorkflowManager complete save pattern."""
        
        # Simulate sequential workflow complete save scenario
        html_content = '<html><body>Sequential complete save test</body></html>'
        
        # Mock S3 metadata for complete save with download info
        s3_metadata = {
            'save_trigger': 'complete_processing',
            'upload_timestamp': datetime.now().isoformat(),
            's3_key': 'sequential/complete/file.html',
            'processing_step': 'step_7_complete_save',
            'download_completed': True,
            'documents_processed': 5
        }
        
        # Execute complete save as sequential manager would
        saved_path = await enhanced_file_ops_service.save_case_data(
            case_data=comprehensive_case_data,
            iso_date='20250115',
            html_content=html_content,
            s3_metadata=s3_metadata
        )
        
        # Verify complete save specific metadata
        with open(saved_path, 'r') as f:
            saved_data = json.load(f)
        
        assert 's3_upload' in saved_data
        assert saved_data['s3_upload']['save_trigger'] == 'complete_processing'
        assert saved_data['s3_upload']['download_completed'] is True
        assert saved_data['s3_upload']['documents_processed'] == 5

    @pytest.mark.asyncio
    async def test_edge_case_handling(self, enhanced_file_ops_service):
        """Test edge cases in data merging and filename validation."""
        
        # Test case with problematic characters
        edge_case_data = {
            'court_id': 'nysd',
            'docket_num': '1:25-cv-12345',
            'versus': 'Special Chars & Co., Inc. v. "Problem Names" <Ltd.>',
            'base_filename': 'problematic<filename>with:many!issues'
        }
        
        # HTML with large content
        large_html = '<html><body>' + 'x' * 10000 + '</body></html>'
        
        saved_path = await enhanced_file_ops_service.save_case_data(
            case_data=edge_case_data,
            iso_date='20250115',
            html_content=large_html
        )
        
        # Verify edge case handling
        assert os.path.exists(saved_path)
        
        filename = os.path.basename(saved_path)
        # Verify problematic characters are cleaned
        assert '<' not in filename
        assert '>' not in filename
        assert ':' not in filename
        assert '!' not in filename
        
        # Verify large HTML handling
        with open(saved_path, 'r') as f:
            saved_data = json.load(f)
        
        assert 'html_content' in saved_data
        assert saved_data['html_content'] == large_html
        assert saved_data['html_content_truncated'] is True
        assert len(saved_data['html_content_preview']) <= 2003

    @pytest.mark.asyncio
    async def test_data_consistency_across_checkpoints(self, enhanced_file_ops_service):
        """Test data consistency across multiple checkpoint integrations."""
        
        # Simulate multiple checkpoint saves for same case
        base_case_data = {
            'court_id': 'ilnd',
            'docket_num': '1:25-cv-09286',
            'versus': 'Test Case v. Consistency Check'
        }
        
        # Checkpoint 1: Initial save with basic data
        checkpoint1_path = await enhanced_file_ops_service.save_case_data(
            case_data=base_case_data,
            iso_date='20250115'
        )
        
        # Checkpoint 2: Save with HTML content
        html_content = '<html><body>Updated content</body></html>'
        checkpoint2_data = base_case_data.copy()
        checkpoint2_data['processing_step'] = 'html_extracted'
        
        checkpoint2_path = await enhanced_file_ops_service.save_case_data(
            case_data=checkpoint2_data,
            iso_date='20250115',
            html_content=html_content
        )
        
        # Checkpoint 3: Save with S3 metadata
        s3_metadata = {'s3_key': 'test/final/file.html', 'status': 'uploaded'}
        checkpoint3_data = checkpoint2_data.copy()
        checkpoint3_data['processing_step'] = 'final_processing'
        
        checkpoint3_path = await enhanced_file_ops_service.save_case_data(
            case_data=checkpoint3_data,
            iso_date='20250115',
            html_content=html_content,
            s3_metadata=s3_metadata
        )
        
        # Verify data consistency across checkpoints
        # All should have different filenames based on processing step
        assert os.path.basename(checkpoint1_path) != os.path.basename(checkpoint2_path)
        assert os.path.basename(checkpoint2_path) != os.path.basename(checkpoint3_path)
        
        # But all should maintain core data consistency
        for path in [checkpoint1_path, checkpoint2_path, checkpoint3_path]:
            with open(path, 'r') as f:
                data = json.load(f)
            
            # Core fields should be consistent
            assert data['court_id'] == 'ilnd'
            assert data['docket_num'] == '1:25-cv-09286'
            assert data['versus'] == 'Test Case v. Consistency Check'
            
        # Final checkpoint should have all enhancements
        with open(checkpoint3_path, 'r') as f:
            final_data = json.load(f)
        
        assert 'html_content' in final_data
        assert 's3_upload' in final_data
        assert final_data['processing_step'] == 'final_processing'

    @pytest.mark.asyncio
    async def test_performance_and_statistics(self, enhanced_file_ops_service, comprehensive_case_data):
        """Test performance monitoring and statistics collection."""
        
        # Perform multiple save operations
        html_content = '<html><body>Performance test</body></html>'
        
        for i in range(5):
            case_data = comprehensive_case_data.copy()
            case_data['test_iteration'] = i
            
            await enhanced_file_ops_service.save_case_data(
                case_data=case_data,
                iso_date='20250115',
                html_content=html_content if i % 2 == 0 else None
            )
        
        # Get comprehensive statistics
        stats = await enhanced_file_ops_service.get_save_statistics()
        
        # Verify statistics collection
        assert stats['total_saves'] == 5
        assert stats['merged_saves'] == 5
        assert stats['filename_validations'] == 5
        
        # Verify component statistics are included
        assert 'merger_statistics' in stats
        assert 'validator_statistics' in stats
        
        # Verify component-specific stats
        merger_stats = stats['merger_statistics']
        assert merger_stats['total_merges'] == 5
        assert merger_stats['html_merges'] == 3  # Only odd iterations had HTML
        
        validator_stats = stats['validator_statistics']
        assert validator_stats['total_validations'] == 5


if __name__ == '__main__':
    pytest.main([__file__, '-v', '--tb=short'])