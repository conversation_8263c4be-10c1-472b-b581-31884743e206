#!/usr/bin/env python3
"""
Test script to verify "(See above for address)" handling in attorney parsing.
"""

import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.services.html.case_parser_service import CaseParserService
from typing import Any


class SimpleLogger:
    """Simple logger implementation for testing."""
    
    def debug(self, message: str, extra: dict[str, Any] | None = None) -> None:
        print(f"[DEBUG] {message}")
    
    def info(self, message: str, extra: dict[str, Any] | None = None) -> None:
        print(f"[INFO] {message}")
    
    def warning(self, message: str, extra: dict[str, Any] | None = None) -> None:
        print(f"[WARNING] {message}")
    
    def error(self, message: str, extra: dict[str, Any] | None = None, exc_info: bool = False) -> None:
        print(f"[ERROR] {message}")
    
    def exception(self, message: str, extra: dict[str, Any] | None = None) -> None:
        print(f"[EXCEPTION] {message}")


def test_see_above_address_handling():
    """Test handling of '(See above for address)' in removal case."""
    print("Testing '(See above for address)' Handling")
    print("=" * 50)
    
    # Read the removal case HTML which contains "(See above for address)"
    with open(os.path.join(project_root, "example_pacer/removal_case.html"), "r") as f:
        html_content = f.read()
    
    # Create parser instance
    logger = SimpleLogger()
    parser = CaseParserService(logger, html_content)
    
    # Test the new parse_attorneys method
    attorneys = parser.parse_attorneys()
    
    # Find attorneys with "(See above for address)"
    attorneys_with_see_above = []
    attorneys_with_full_address = []
    
    for attorney in attorneys:
        if "(See above for address)" in attorney.get('law_firm', ''):
            attorneys_with_see_above.append(attorney)
        elif attorney.get('address1') or attorney.get('city'):
            attorneys_with_full_address.append(attorney)
    
    print(f"Found {len(attorneys)} total attorneys")
    print(f"Attorneys with '(See above for address)': {len(attorneys_with_see_above)}")
    print(f"Attorneys with full address: {len(attorneys_with_full_address)}")
    
    # Display details
    for i, attorney in enumerate(attorneys_with_see_above, 1):
        print(f"\nAttorney {i} with '(See above for address)':")
        print(f"  Name: {attorney.get('attorney_name', 'N/A')}")
        print(f"  Law Firm: {attorney.get('law_firm', 'N/A')}")
        print(f"  Address1: {attorney.get('address1', 'N/A')}")
        print(f"  City: {attorney.get('city', 'N/A')}")
        print(f"  State: {attorney.get('state', 'N/A')}")
        print(f"  Phone: {attorney.get('phone', 'N/A')}")
    
    for i, attorney in enumerate(attorneys_with_full_address, 1):
        print(f"\nAttorney {i} with full address:")
        print(f"  Name: {attorney.get('attorney_name', 'N/A')}")
        print(f"  Law Firm: {attorney.get('law_firm', 'N/A')}")
        print(f"  Address1: {attorney.get('address1', 'N/A')}")
        print(f"  City: {attorney.get('city', 'N/A')}")
        print(f"  State: {attorney.get('state', 'N/A')}")
        print(f"  Phone: {attorney.get('phone', 'N/A')}")
    
    # Validation
    print(f"\nValidation:")
    if len(attorneys_with_see_above) > 0:
        print(f"✅ Found {len(attorneys_with_see_above)} attorney(s) with '(See above for address)'")
        print("✅ These attorneys should inherit address from previous attorney")
    
    if len(attorneys_with_full_address) > 0:
        print(f"✅ Found {len(attorneys_with_full_address)} attorney(s) with full address information")
    
    return attorneys


if __name__ == "__main__":
    test_see_above_address_handling()