#!/usr/bin/env python3
"""
Simple validation script for attorney parsing improvements.
"""

import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.services.html.case_parser_service import CaseParserService
from typing import Any


class SimpleLogger:
    """Simple logger implementation for testing."""
    
    def debug(self, message: str, extra: dict[str, Any] | None = None) -> None:
        print(f"[DEBUG] {message}")
    
    def info(self, message: str, extra: dict[str, Any] | None = None) -> None:
        print(f"[INFO] {message}")
    
    def warning(self, message: str, extra: dict[str, Any] | None = None) -> None:
        print(f"[WARNING] {message}")
    
    def error(self, message: str, extra: dict[str, Any] | None = None, exc_info: bool = False) -> None:
        print(f"[ERROR] {message}")
    
    def exception(self, message: str, extra: dict[str, Any] | None = None) -> None:
        print(f"[EXCEPTION] {message}")


def test_standard_case():
    """Test attorney parsing for a standard civil case."""
    print("Testing Standard Civil Case Attorney Parsing")
    print("=" * 50)
    
    # Read the standard civil case HTML
    with open(os.path.join(project_root, "example_pacer/ilnd_docket_sheet.html"), "r") as f:
        html_content = f.read()
    
    # Create parser instance
    logger = SimpleLogger()
    parser = CaseParserService(logger, html_content)
    
    # Test the new parse_attorneys method
    attorneys = parser.parse_attorneys()
    
    print(f"Found {len(attorneys)} attorneys:")
    
    for i, attorney in enumerate(attorneys, 1):
        print(f"\nAttorney {i}:")
        print(f"  Name: {attorney.get('attorney_name', 'N/A')}")
        print(f"  Law Firm: {attorney.get('law_firm', 'N/A')}")
        print(f"  Represents: {attorney.get('represents', 'N/A')}")
        print(f"  Email: {attorney.get('email', 'N/A')}")
        print(f"  Phone: {attorney.get('phone', 'N/A')}")
        print(f"  Attorney to be Noticed: {attorney.get('attorney_to_be_noticed', False)}")
        print(f"  Pro SE: {attorney.get('is_pro_se', False)}")
    
    # Validate expected results
    expected_attorneys = ["Sherrell Dandy", "Tobias L Millrood"]
    found_attorney_names = [a.get('attorney_name', '') for a in attorneys]
    
    print(f"\nValidation:")
    for expected in expected_attorneys:
        if expected in found_attorney_names:
            print(f"✅ Found expected attorney: {expected}")
        else:
            print(f"❌ Missing expected attorney: {expected}")
    
    return attorneys


def test_removal_case():
    """Test attorney parsing for a removal case with PRO SE plaintiffs."""
    print("\n\nTesting Removal Case Attorney Parsing")
    print("=" * 50)
    
    # Read the removal case HTML
    with open(os.path.join(project_root, "example_pacer/removal_case.html"), "r") as f:
        html_content = f.read()
    
    # Create parser instance
    logger = SimpleLogger()
    parser = CaseParserService(logger, html_content)
    
    # Test case type detection
    is_removal = parser._is_removal_case()
    print(f"Detected as removal case: {is_removal}")
    
    # Test the new parse_attorneys method
    attorneys = parser.parse_attorneys()
    
    print(f"Found {len(attorneys)} attorneys:")
    
    for i, attorney in enumerate(attorneys, 1):
        print(f"\nAttorney {i}:")
        print(f"  Name: {attorney.get('attorney_name', 'N/A')}")
        print(f"  Law Firm: {attorney.get('law_firm', 'N/A')}")
        print(f"  Represents: {attorney.get('represents', 'N/A')}")
        print(f"  Email: {attorney.get('email', 'N/A')}")
        print(f"  Phone: {attorney.get('phone', 'N/A')}")
        print(f"  Pro SE: {attorney.get('is_pro_se', False)}")
    
    # Check for proper PRO SE filtering in removal cases
    pro_se_attorneys = [a for a in attorneys if a.get('is_pro_se', False)]
    real_attorneys = [a for a in attorneys if not a.get('is_pro_se', False)]
    
    print(f"\nPRO SE Handling Validation:")
    print(f"PRO SE attorneys found: {len(pro_se_attorneys)} (should be 0 for removal cases)")
    print(f"Real attorneys found: {len(real_attorneys)}")
    
    if len(pro_se_attorneys) == 0:
        print("✅ PRO SE attorneys correctly filtered out for removal case")
    else:
        print("❌ PRO SE attorneys should be filtered out for removal cases")
    
    return attorneys


def test_full_parsing_integration():
    """Test that the complete parsing flow works with new attorney parsing."""
    print("\n\nTesting Complete Parsing Integration")
    print("=" * 50)
    
    # Test with standard case
    with open(os.path.join(project_root, "example_pacer/ilnd_docket_sheet.html"), "r") as f:
        html_content = f.read()
    
    logger = SimpleLogger()
    parser = CaseParserService(logger, html_content)
    
    # Use the full parse method which should now use the new attorney parsing
    result = parser.parse()
    
    attorneys = result.get('attorney', [])
    case_info = result.get('case_info', {})
    plaintiffs = result.get('plaintiffs', [])
    defendants = result.get('defendants', [])
    
    print(f"Complete parsing results:")
    print(f"  Case versus: {case_info.get('versus', 'N/A')}")
    print(f"  Plaintiffs: {len(plaintiffs)}")
    print(f"  Defendants: {len(defendants)}")
    print(f"  Attorneys: {len(attorneys)}")
    
    # Check that attorneys have the new structure
    if attorneys:
        first_attorney = attorneys[0]
        has_represents_field = 'represents' in first_attorney
        has_is_pro_se_field = 'is_pro_se' in first_attorney
        
        print(f"\nNew attorney structure validation:")
        print(f"  Has 'represents' field: {has_represents_field}")
        print(f"  Has 'is_pro_se' field: {has_is_pro_se_field}")
        
        if has_represents_field and has_is_pro_se_field:
            print("✅ New attorney structure is working")
        else:
            print("❌ New attorney structure is missing fields")
    
    return result


def main():
    """Run all attorney parsing tests."""
    print("ATTORNEY PARSING IMPROVEMENT VALIDATION")
    print("=" * 60)
    
    try:
        # Test 1: Standard case attorney parsing
        standard_attorneys = test_standard_case()
        
        # Test 2: Removal case attorney parsing
        removal_attorneys = test_removal_case()
        
        # Test 3: Complete parsing integration
        complete_result = test_full_parsing_integration()
        
        print("\n" + "=" * 60)
        print("SUMMARY")
        print("=" * 60)
        print(f"Standard case attorneys found: {len(standard_attorneys)}")
        print(f"Removal case attorneys found: {len(removal_attorneys)}")
        print(f"Complete parsing working: {complete_result is not None}")
        
        if len(standard_attorneys) >= 2 and len(removal_attorneys) >= 1:
            print("\n✅ Attorney parsing improvements validated successfully!")
        else:
            print("\n❌ Some attorney parsing tests failed")
        
    except Exception as e:
        print(f"\n❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()