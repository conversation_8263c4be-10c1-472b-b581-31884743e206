#!/usr/bin/env python3
"""
Test Dependency Injection Architecture Fix

This test validates that the MainServiceFactory correctly registers and provides
all required dependencies for SequentialWorkflowManager, ensuring no hard failures occur.
"""

import pytest
import asyncio
import logging
from unittest.mock import Mock, patch
from src.factories.main_factory import MainServiceFactory
from src.config_models.base import WorkflowConfig


class TestDependencyInjectionFix:
    """Test the dependency injection architecture fix."""

    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=WorkflowConfig)
        config.headless = True
        config.run_parallel = True
        config.timeout_ms = 60000
        config.config_name = "test_config"
        return config

    @pytest.fixture
    def mock_shutdown_event(self):
        """Create a mock shutdown event."""
        return asyncio.Event()

    async def test_storage_dependency_validation(self, mock_config, mock_shutdown_event):
        """Test that storage dependencies are properly validated."""
        # Create MainServiceFactory
        factory = MainServiceFactory(config=mock_config, shutdown_event=mock_shutdown_event)
        
        # Test that storage dependency validation works
        async with factory:
            # If we get here without sys.exit(1), the dependencies are properly registered
            assert factory._container is not None
            assert hasattr(factory._container, 'storage')
            
            # Test that storage container has required dependencies
            storage_container = factory._container.storage
            assert hasattr(storage_container, 'async_dynamodb_storage')
            assert hasattr(storage_container, 'pacer_repository')
            assert hasattr(storage_container, 's3_async_storage')

    async def test_pacer_dependency_validation(self, mock_config, mock_shutdown_event):
        """Test that PACER dependencies can access storage components."""
        factory = MainServiceFactory(config=mock_config, shutdown_event=mock_shutdown_event)
        
        async with factory:
            # Test that PACER container exists and has required dependencies
            assert hasattr(factory._container, 'pacer')
            pacer_container = factory._container.pacer
            
            # Test that sequential workflow manager dependencies are available
            assert hasattr(pacer_container, 'sequential_workflow_manager')
            assert hasattr(pacer_container, 'validated_sequential_workflow_factory')

    async def test_sequential_workflow_manager_dependency_injection(self, mock_config, mock_shutdown_event):
        """Test that SequentialWorkflowManager gets all required dependencies."""
        factory = MainServiceFactory(config=mock_config, shutdown_event=mock_shutdown_event)
        
        async with factory:
            pacer_container = factory._container.pacer
            storage_container = factory._container.storage
            
            # Test that we can create a sequential workflow manager with dependencies
            # This should NOT result in sys.exit(1) if dependencies are properly injected
            try:
                # Use the validated factory to create the manager
                sequential_manager = pacer_container.validated_sequential_workflow_factory(
                    logger=factory.logger,
                    config=mock_config,
                    navigation_facade=Mock(),  # Mock for testing
                    sequential_docket_processor=Mock(),  # Mock for testing
                    state_validator=Mock(),  # Mock for testing
                    return_and_continue_manager=Mock(),  # Mock for testing
                    storage_container=storage_container
                )
                
                # If we get here, dependencies were properly validated and injected
                assert sequential_manager is not None
                
                # Test that the manager has the required dependencies
                # At least one of these should be available
                assert (sequential_manager.pacer_repository is not None or 
                       sequential_manager.async_dynamodb_storage is not None)
                
            except Exception as e:
                pytest.fail(f"SequentialWorkflowManager creation failed with dependency injection: {e}")

    async def test_hard_failure_behavior_with_missing_dependencies(self):
        """Test that the system properly reports missing dependencies."""
        # Create a config that would result in missing dependencies
        incomplete_config = Mock()
        incomplete_config.headless = True
        incomplete_config.config_name = "incomplete_test_config"
        
        factory = MainServiceFactory(config=incomplete_config, shutdown_event=asyncio.Event())
        
        # This should fail during container initialization, but gracefully
        with pytest.raises(RuntimeError):
            async with factory:
                pass  # Should not reach here due to missing dependencies

    def test_dependency_validation_error_messages(self, mock_config, mock_shutdown_event):
        """Test that dependency validation provides clear error messages."""
        # Test that validation methods exist and are callable
        factory = MainServiceFactory(config=mock_config, shutdown_event=mock_shutdown_event)
        
        # Test that validation methods exist
        assert hasattr(factory, '_validate_storage_dependencies')
        assert hasattr(factory, '_validate_pacer_dependencies')
        assert callable(factory._validate_storage_dependencies)
        assert callable(factory._validate_pacer_dependencies)

    async def test_container_cleanup_on_failure(self, mock_config, mock_shutdown_event):
        """Test that container cleanup works properly on failure."""
        factory = MainServiceFactory(config=mock_config, shutdown_event=mock_shutdown_event)
        
        # Test cleanup method exists
        assert hasattr(factory, '_cleanup_container')
        assert callable(factory._cleanup_container)
        
        # Test that cleanup can be called safely
        await factory._cleanup_container()  # Should not raise an exception


if __name__ == "__main__":
    # Run the tests
    import sys
    
    async def run_tests():
        """Run all tests manually."""
        print("🔍 Testing Dependency Injection Architecture Fix...")
        
        # Create test instance
        test_instance = TestDependencyInjectionFix()
        
        # Create fixtures
        mock_config = test_instance.mock_config()
        mock_shutdown_event = test_instance.mock_shutdown_event()
        
        try:
            # Test storage dependency validation
            print("✅ Testing storage dependency validation...")
            await test_instance.test_storage_dependency_validation(mock_config, mock_shutdown_event)
            print("✅ Storage dependency validation test passed")
            
            # Test PACER dependency validation  
            print("✅ Testing PACER dependency validation...")
            await test_instance.test_pacer_dependency_validation(mock_config, mock_shutdown_event)
            print("✅ PACER dependency validation test passed")
            
            # Test SequentialWorkflowManager dependency injection
            print("✅ Testing SequentialWorkflowManager dependency injection...")
            await test_instance.test_sequential_workflow_manager_dependency_injection(mock_config, mock_shutdown_event)
            print("✅ SequentialWorkflowManager dependency injection test passed")
            
            # Test container cleanup
            print("✅ Testing container cleanup...")
            await test_instance.test_container_cleanup_on_failure(mock_config, mock_shutdown_event)
            print("✅ Container cleanup test passed")
            
            print("🎉 All dependency injection tests passed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    # Run tests if called directly
    if asyncio.run(run_tests()):
        sys.exit(0)
    else:
        sys.exit(1)