#!/usr/bin/env python3
"""
Debug script to understand why attorney parsing is not working.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.html.case_parser_service import CaseParserService
from typing import Any


class SimpleLogger:
    """Simple logger implementation for testing."""
    
    def debug(self, message: str, extra: dict[str, Any] | None = None) -> None:
        print(f"[DEBUG] {message}")
    
    def info(self, message: str, extra: dict[str, Any] | None = None) -> None:
        print(f"[INFO] {message}")
    
    def warning(self, message: str, extra: dict[str, Any] | None = None) -> None:
        print(f"[WARNING] {message}")
    
    def error(self, message: str, extra: dict[str, Any] | None = None, exc_info: bool = False) -> None:
        print(f"[ERROR] {message}")
    
    def exception(self, message: str, extra: dict[str, Any] | None = None) -> None:
        print(f"[EXCEPTION] {message}")


def debug_attorney_parsing():
    """Debug attorney parsing to understand the issue."""
    
    # Read the standard civil case HTML
    with open("/Users/<USER>/PycharmProjects/lexgenius/example_pacer/ilnd_docket_sheet.html", "r") as f:
        html_content = f.read()
    
    # Create parser instance
    logger = SimpleLogger()
    parser = CaseParserService(logger, html_content)
    
    # Check if soup is created
    print(f"Soup created: {parser.soup is not None}")
    
    # Find main content
    main_content = parser.soup.find("div", id="cmecfMainContent")
    print(f"Main content found: {main_content is not None}")
    
    if main_content:
        # Look for tables
        tables = main_content.find_all("table")
        print(f"Number of tables found: {len(tables)}")
        
        # Look for attorney table
        attorney_table = None
        for i, table in enumerate(tables):
            table_text = table.get_text().lower()
            print(f"\nTable {i} preview: {table_text[:200]}...")
            if "represented" in table_text and ("plaintiff" in table_text or "defendant" in table_text):
                attorney_table = table
                print(f"Attorney table found at index {i}")
                break
        
        if attorney_table:
            # Look for attorney rows
            rows = attorney_table.find_all("tr")
            print(f"Number of rows in attorney table: {len(rows)}")
            
            for i, row in enumerate(rows[:10]):  # Check first 10 rows
                row_text = row.get_text().strip()
                if "plaintiff" in row_text.lower() or "defendant" in row_text.lower():
                    print(f"Row {i} (party header): {row_text}")
                elif "represented" in row_text.lower():
                    print(f"Row {i} (attorney row): {row_text}")
                    
                    # Check the structure
                    tds = row.find_all("td")
                    print(f"  Number of TDs: {len(tds)}")
                    if len(tds) >= 3:
                        party_td = tds[0]
                        represented_td = tds[1]
                        attorney_td = tds[2]
                        
                        party_bold = party_td.find("b")
                        if party_bold:
                            print(f"  Party name: {party_bold.get_text()}")
                        print(f"  Represented text: {represented_td.get_text()}")
                        print(f"  Attorney text: {attorney_td.get_text()[:200]}...")
    
    # Test the new parse_attorneys method
    print("\n" + "="*50)
    print("TESTING parse_attorneys() method")
    print("="*50)
    
    attorneys = parser.parse_attorneys()
    print(f"Attorneys found: {len(attorneys)}")
    
    for attorney in attorneys:
        print(f"Attorney: {attorney}")


if __name__ == "__main__":
    debug_attorney_parsing()