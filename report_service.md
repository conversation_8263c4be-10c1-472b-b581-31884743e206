```
# /src/services/pacer/report_service.py
imp
ort asyncio
import os
import re
from datetime import datetime
from typing import List, Dict, Any, Optional

# Removed dependency_injector imports - using container-based injection
from playwright.async_api import (Error as PlaywrightError, TimeoutError as PlaywrightTimeoutError)

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.infrastructure.protocols.logger import LoggerProtocol
from .browser.navigator import PacerNavigator
from ...infrastructure.decorators.async_decorators import retry_async
from ...utils.json_safety import safe_json_write


class ReportService(AsyncServiceBase):
    """Navigates to and configures PACER reports."""

    def __init__(self,
                 logger: LoggerProtocol ,
                 navigator: PacerNavigator ,
                 court_id: str ,
                 from_date_str: str ,
                 to_date_str: str ,
                 config: Dict[str, Any] ,
                 iso_date: str ,
                 ignore_download_service: Optional[Any] = None):  # Optional[PacerIgnoreDownloadService]
        # Initialize AsyncServiceBase with injected logger
        super().__init__(logger, config)

        self.navigator: PacerNavigator = navigator
        self.court_id: str = court_id
        self.from_date_str: str = from_date_str  # Expected mm/dd/yy format
        self.to_date_str: str = to_date_str  # Expected mm/dd/yy format
        self.iso_date: str = iso_date  # YYYYMMDD format for directory structure
        self.ignore_download_service = ignore_download_service

        # --- Define ALL selectors and constants as INSTANCE attributes ---
        self.REPORTS_MENU_SELECTOR: str = "a:text('Reports')"
        self.CIVIL_CASES_LINK_SELECTOR: str = "a:text('Civil Cases')"
        self.CIVIL_LINK_SELECTOR: str = "a:text('Civil')"
        self.CASES_FILED_REGEX_SELECTOR: str = "a:text-matches('/^Case(s)? Filed/i')"

        self.REPORT_LINK_SELECTORS: list[str] = [
            self.CIVIL_CASES_LINK_SELECTOR,
            self.CIVIL_LINK_SELECTOR,
            self.CASES_FILED_REGEX_SELECTOR,
        ]

        self.DATE_INPUT_SELECTOR: str = "input[name*='date'], input[name*='filed']"
        self.DATE_FROM_PRIMARY_SELECTOR: str = "input[name='filed_from']"
        self.DATE_TO_PRIMARY_SELECTOR: str = "input[name='filed_to']"
        self.DATE_FROM_ALT_SELECTOR: str = "input[name='date_from']"
        self.DATE_TO_ALT_SELECTOR: str = "input[name='date_to']"

        self.NATURE_OF_SUIT_SELECTOR: str = "select[name='nature_of_suit']"
        self.CASE_TYPE_SELECTOR: str = "select[name='case_type']"
        self.CASE_FLAGS_SELECTOR: str = "select[name='case_flags']"
        self.CLOSED_CASES_SELECTOR: str = "input[name='closed_cases']"

        self.RUN_REPORT_BUTTON_SELECTORS: list[str] = ["[name='button1']"]

        self.RESULTS_TABLE_SELECTOR: str = "#cmecfMainContent table:has(a[href*='DktRpt.pl'])"
        self.RESULTS_TABLE_DATA_ROW_SELECTOR: str = self.RESULTS_TABLE_SELECTOR + " tbody tr:nth-child(n+2)"
        self.NO_CASES_FOUND_SELECTOR: str = "text=/no cases found|Total number of cases reported: 0|Sorry - no data for the chosen selection criteria/i"

        self.XPATH_DATA_ROW_WITH_LINK_SELECTOR: str = (
            "//div[@id='cmecfMainContent']//tr["
            ".//a[contains(@href, 'DktRpt.pl') or contains(@href, 'iquery.pl')]"
            "]"
        )

        # Configuration Data
        self.COURT_CASE_TYPES: dict[str, list[str]] = {
            'nced': ['cv', 'MDL'], 'ohnd': ['cv', 'sf', 'dp'],
            'ilnd': ['cv', 'ad'], 'ilsd': ['cv', 'pq'],
            'insd': ['cv', 'ml'], 'nyed': ['cv', 'al', 'bi'],
            'gand': ['cv']
        }
        self.DEFAULT_CASE_TYPES: list[str] = ['cv']
        self.COURT_CASE_FLAGS: dict[str, list[str]] = {}
        self.NATURE_OF_SUIT_OPTIONS: list[str] = ['365', '367', '368', '360', '240', '245', '470']

        self.log_info(f"ReportService for {court_id} initialized.")

    async def _execute_action(self, data: Any) -> Any:
        """Execute ReportService actions."""
        if isinstance(data, dict):
            action = data.get('action')
            if action == 'navigate_to_report_menu':
                return await self.navigate_to_report_menu()
            elif action == 'navigate_to_case_filed_report':
                return await self.navigate_to_case_filed_report()
            elif action == 'configure_case_filed_report':
                return await self.configure_case_filed_report()
            elif action == 'run_report':
                return await self.run_report()
            elif action == 'verify_report_content':
                return await self._verify_report_content()
            elif action == 'extract_and_save_report_list':
                await self._extract_and_save_report_list()
                return None
            elif action == 'fill_date_fields':
                await self._fill_date_fields()
                return None
            elif action == 'select_case_type':
                await self._select_case_type()
                return None
            elif action == 'select_case_flags':
                await self._select_case_flags()
                return None
            elif action == 'check_include_closed_cases':
                await self._check_include_closed_cases()
                return None
            elif action == 'extract_mdl_num_from_flags':
                return self._extract_mdl_num_from_flags(data['flags'])
            elif action == 'parse_defendants_from_versus':
                return self._parse_defendants_from_versus(data['versus_text'])
        raise PacerServiceError("Invalid action data provided to ReportService")

    @retry_async(max_attempts=3, delay=3, exceptions=(PlaywrightTimeoutError, PlaywrightError))
    async def navigate_to_report_menu(self):
        self.log_info("Navigating to Reports menu...")
        await self.navigator.click(self.REPORTS_MENU_SELECTOR)
        await self.navigator.page.wait_for_timeout(1000)

    @retry_async(max_attempts=3, delay=3, exceptions=(PlaywrightTimeoutError, PlaywrightError))
    async def navigate_to_case_filed_report(self):
        self.log_info(f"Navigating to Case Filed/Civil report for court {self.court_id}...")
        try:
            async with self.navigator.page.expect_navigation(wait_until="domcontentloaded", timeout=25000):
                await self.navigator.click(self.REPORTS_MENU_SELECTOR, wait_for_nav=False, timeout_override_ms=15000)
            self.log_info(
                f"Clicked '{self.REPORTS_MENU_SELECTOR}'. Current page after 'Reports' click: {self.navigator.page.url}")
        except PlaywrightError as e:
            self.log_error(f"Failed to click '{self.REPORTS_MENU_SELECTOR}' or navigate: {e}")
            if self.navigator.page and not self.navigator.page.is_closed():
                await self.navigator.save_screenshot(f"reports_menu_click_nav_fail_{self.court_id}")
            raise

        if not self.navigator.page or self.navigator.page.is_closed():
            self.log_error(
                f"Page closed or invalid after clicking '{self.REPORTS_MENU_SELECTOR}' for {self.court_id}.")
            raise PlaywrightError(f"Page invalid after '{self.REPORTS_MENU_SELECTOR}' click for {self.court_id}")

        courts_needing_yui_clear = ['ilcd', 'wvnd', 'ned', 'miwd']
        if self.court_id.lower() in courts_needing_yui_clear:
            self.log_info(f"Applying YUI menu clearing logic for court {self.court_id} on {self.navigator.page.url}")
            try:
                await self.navigator.page.keyboard.press("Escape")
                self.log_info(f"Pressed Escape key for {self.court_id}.")
                await self.navigator.page.wait_for_timeout(500)
                body_locator = self.navigator.page.locator("body").first
                if await body_locator.count() > 0:
                    await body_locator.click(timeout=3000, no_wait_after=True, force=True)
                    self.log_info(
                        f"Clicked body to attempt to remove focus from any active menu for {self.court_id}.")
                    await self.navigator.page.wait_for_timeout(300)
                else:
                    self.log_debug(f"Body locator not found for focus click for {self.court_id}.")
            except PlaywrightError as e_clear:
                self.log_warning(
                    f"Non-critical error attempting to clear YUI menus for {self.court_id}: {type(e_clear).__name__} - {str(e_clear).splitlines()[0]}")
            except Exception as e_unexp_clear:
                self.log_warning(
                    f"Unexpected non-critical error during menu clear for {self.court_id}: {type(e_unexp_clear).__name__} - {str(e_unexp_clear)}")
        else:
            self.log_info(f"Skipping YUI menu clearing logic for court {self.court_id} (not in specific list).")

        scoped_report_page_link_selectors = [
            "div#cmecfMainContent a[href='/cgi-bin/CaseFiled-Rpt.pl']:text-matches('/Civil Cases/i')",
            "div#cmecfMainContent a[href='/cgi-bin/CaseFiled-Rpt.pl']:text-matches('/^Civil Case(s)? Filed/i')",
            "div#cmecfMainContent a:text-matches('/Civil Cases/i')",
            f"div#cmecfMainContent {self.CIVIL_CASES_LINK_SELECTOR}",
            f"div#cmecfMainContent {self.CIVIL_LINK_SELECTOR}",
            f"div#cmecfMainContent {self.CASES_FILED_REGEX_SELECTOR}",
        ]

        clicked_successfully = False
        for selector_index, selector_value in enumerate(scoped_report_page_link_selectors):
            self.log_debug(
                f"On reports landing page, attempting SCOPED link selector #{selector_index + 1}: '{selector_value}' for {self.court_id}")
            if not self.navigator.page or self.navigator.page.is_closed():
                self.log_warning(
                    f"Page became closed before attempting selector '{selector_value}' on reports landing page for {self.court_id}.")
                break
            try:
                link_locator_collection = await self.navigator.locator(selector_value)
                if await link_locator_collection.count() > 0:
                    link_locator_instance = link_locator_collection.first
                    try:
                        await link_locator_instance.wait_for(state='visible', timeout=12000)
                    except PlaywrightTimeoutError:
                        self.log_debug(
                            f"Link '{selector_value}' not visible on reports landing page for {self.court_id}. Trying next.")
                        continue
                    self.log_info(
                        f"Link '{selector_value}' is visible on reports landing page for {self.court_id}. Clicking (expecting navigation).")
                    async with self.navigator.page.expect_navigation(wait_until="domcontentloaded", timeout=30000):
                        await link_locator_instance.click(timeout=20000)
                    self.log_info(
                        f"Successfully clicked and navigated from reports landing page using selector '{selector_value}' for {self.court_id}. New URL: {self.navigator.page.url}")
                    clicked_successfully = True
                    break
                else:
                    self.log_debug(
                        f"Scoped report link selector '{selector_value}' not found on reports landing page for {self.court_id}.")
            except PlaywrightError as e:
                self.log_warning(
                    f"Attempt on reports landing page with SCOPED selector '{selector_value}' for {self.court_id} failed: {type(e).__name__} - {str(e).splitlines()[0]}. Trying next selector.")
                if self.navigator.page and not self.navigator.page.is_closed():
                    await self.navigator.save_screenshot(
                        f"report_landing_scoped_link_click_error_{self.court_id}_{selector_index}")

        if not clicked_successfully:
            page_content_for_debug = "Page closed or content unavailable"
            current_url_for_debug = "Page closed or URL unavailable"
            if self.navigator.page and not self.navigator.page.is_closed():
                page_content_for_debug = await self.navigator.page.content()
                current_url_for_debug = self.navigator.page.url
                await self.navigator.save_screenshot(f"navigate_report_fail_all_scoped_selectors_{self.court_id}")
            self.log_error(
                f"Failed to click any report link on the reports landing page for {self.court_id} after trying SCOPED selectors: {scoped_report_page_link_selectors}. Current page URL: {current_url_for_debug}. Page content snippet: {page_content_for_debug[:500]}")
            raise PlaywrightError(
                f"Failed to navigate from reports landing page for {self.court_id} (all scoped selectors failed).")

        if not self.navigator.page or self.navigator.page.is_closed():
            self.log_error(f"Page is unexpectedly closed before final report page verification for {self.court_id}.")
            raise PlaywrightError(f"Page closed before final report verification for {self.court_id}")

        try:
            date_input_locator_collection = await self.navigator.locator(self.DATE_INPUT_SELECTOR)
            await date_input_locator_collection.first.wait_for(state='visible', timeout=15000)
            self.log_info(
                f"Successfully navigated to and verified report configuration page for {self.court_id}: {self.navigator.page.url}")
        except PlaywrightError as e_verify:
            page_url_for_error = "UNKNOWN_URL (page closed)"
            page_content_for_error = "Page content unavailable"
            if self.navigator.page and not self.navigator.page.is_closed():
                page_url_for_error = self.navigator.page.url
                page_content_for_error = (await self.navigator.page.content())[:500]
                await self.navigator.save_screenshot(f"navigate_report_final_verify_fail_{self.court_id}")
            self.log_error(
                f"Final verification of report page failed for {self.court_id}: Target element ('{self.DATE_INPUT_SELECTOR}') not found on {page_url_for_error}. Content: {page_content_for_error}. Error: {e_verify}",
                exc_info=True)
            raise

    def _get_court_case_type_options(self) -> List[str]:
        return self.COURT_CASE_TYPES.get(self.court_id, self.DEFAULT_CASE_TYPES)

    async def _select_case_type(self):
        desired_options = self._get_court_case_type_options()
        if not desired_options:
            self.log_debug(f"No specific case types configured for court {self.court_id}.")
            return
        try:
            select_locator_obj = await self.navigator.locator(self.CASE_TYPE_SELECTOR)
            if await select_locator_obj.count() == 0:
                self.log_debug(
                    f"Case type select element '{self.CASE_TYPE_SELECTOR}' not found for {self.court_id}. Skipping.")
                return
            option_elements = await select_locator_obj.locator("option").all()
            available_option_values = [await opt.get_attribute("value") for opt in option_elements if
                                       await opt.get_attribute("value") is not None]
            self.log_debug(
                f"For {self.court_id}, desired case types: {desired_options}, available on page: {available_option_values}")
            options_to_actually_select = [opt for opt in desired_options if opt in available_option_values]
            if not options_to_actually_select:
                self.log_warning(
                    f"None of the desired case types {desired_options} are available in dropdown '{self.CASE_TYPE_SELECTOR}' for {self.court_id}. "
                    f"Available options: {available_option_values}. Skipping case type selection.")
                return
            if set(options_to_actually_select) != set(desired_options):
                missing_options = list(set(desired_options) - set(options_to_actually_select))
                self.log_warning(
                    f"Some desired case types for {self.court_id} are not available: {missing_options}. "
                    f"Will attempt to select: {options_to_actually_select}")
            self.log_info(
                f"Attempting to select case types for {self.court_id} in '{self.CASE_TYPE_SELECTOR}': {options_to_actually_select}")
            await self.navigator.select_option(self.CASE_TYPE_SELECTOR, options_to_actually_select)
            self.log_info(
                f"Successfully initiated selection for case types: {options_to_actually_select} for {self.court_id}")
        except PlaywrightError as e:
            self.log_warning(
                f"Non-critical PlaywrightError during case type selection for {self.court_id} using selector '{self.CASE_TYPE_SELECTOR}': {e}",
                exc_info=True)
        except Exception as e:
            self.log_error(
                f"Unexpected error selecting case types for {self.court_id} using selector '{self.CASE_TYPE_SELECTOR}': {e}",
                exc_info=True)

    def _get_court_case_flag_options(self) -> List[str]:
        return self.COURT_CASE_FLAGS.get(self.court_id, [])

    async def _select_case_flags(self):
        flag_options = self._get_court_case_flag_options()
        if not flag_options:
            self.log_debug(f"No case flags defined for {self.court_id}.")
            return
        try:
            flags_locator_obj = await self.navigator.locator(self.CASE_FLAGS_SELECTOR)
            if await flags_locator_obj.count() > 0:
                option_elements = await flags_locator_obj.locator("option").all()
                available_flag_values = [await opt.get_attribute("value") for opt in option_elements if
                                         await opt.get_attribute("value") is not None]
                self.log_debug(
                    f"For {self.court_id}, desired case flags: {flag_options}, available on page: {available_flag_values}")
                flags_to_actually_select = [opt for opt in flag_options if opt in available_flag_values]
                if not flags_to_actually_select:
                    self.log_warning(
                        f"None of the desired case flags {flag_options} are available in dropdown '{self.CASE_FLAGS_SELECTOR}' for {self.court_id}. "
                        f"Available options: {available_flag_values}. Skipping case flag selection.")
                    return
                if set(flags_to_actually_select) != set(flag_options):
                    missing_flags = list(set(flag_options) - set(flags_to_actually_select))
                    self.log_warning(
                        f"Some desired case flags for {self.court_id} are not available: {missing_flags}. "
                        f"Will attempt to select: {flags_to_actually_select}")
                self.log_info(
                    f"Attempting to select case flags for {self.court_id} in '{self.CASE_FLAGS_SELECTOR}': {flags_to_actually_select}")
                await self.navigator.select_option(self.CASE_FLAGS_SELECTOR, flags_to_actually_select)
                self.log_info(
                    f"Successfully initiated selection for case flags: {flags_to_actually_select} for {self.court_id}")
            else:
                self.log_debug(
                    f"Case flags dropdown '{self.CASE_FLAGS_SELECTOR}' not found for {self.court_id}. Skipping.")
        except PlaywrightError as e:
            self.log_warning(
                f"Non-critical error selecting case flags for {self.court_id} using '{self.CASE_FLAGS_SELECTOR}': {e}",
                exc_info=True)
        except Exception as e:
            self.log_error(
                f"Unexpected error selecting case flags for {self.court_id} using '{self.CASE_FLAGS_SELECTOR}': {e}",
                exc_info=True)

    async def _check_include_closed_cases(self):
        try:
            if self.court_id != 'gand':
                self.log_debug(f"Skipping 'Include closed cases' check for {self.court_id} (not gand).")
                return
            closed_cases_locator_obj = await self.navigator.locator(self.CLOSED_CASES_SELECTOR)
            if await closed_cases_locator_obj.count() > 0:
                await self.navigator.check(self.CLOSED_CASES_SELECTOR)
                self.log_debug(
                    f"Ensured 'Include closed cases' ('{self.CLOSED_CASES_SELECTOR}') is checked for {self.court_id}.")
            else:
                self.log_debug(
                    f"'Include closed cases' checkbox ('{self.CLOSED_CASES_SELECTOR}') not found for {self.court_id}.")
        except PlaywrightError as e:
            self.log_warning(
                f"Could not check 'closed_cases' checkbox ('{self.CLOSED_CASES_SELECTOR}') for {self.court_id}: {e}",
                exc_info=True)
        except Exception as e:
            self.log_error(
                f"Unexpected error checking 'closed_cases' ('{self.CLOSED_CASES_SELECTOR}') for {self.court_id}: {e}",
                exc_info=True)

    @retry_async(max_attempts=2, delay=3, exceptions=(PlaywrightTimeoutError, PlaywrightError))
    async def configure_case_filed_report(self):
        self.log_info(
            f"Configuring Case Filed report for {self.court_id} ({self.from_date_str} - {self.to_date_str})...")
        await self._fill_date_fields()
        try:
            nos_locator_obj = await self.navigator.locator(self.NATURE_OF_SUIT_SELECTOR)
            if await nos_locator_obj.count() > 0:
                if self.NATURE_OF_SUIT_OPTIONS:
                    await self.navigator.select_option(self.NATURE_OF_SUIT_SELECTOR, self.NATURE_OF_SUIT_OPTIONS)
                    self.log_info(
                        f"Selected Nature of Suit options for '{self.NATURE_OF_SUIT_SELECTOR}': {self.NATURE_OF_SUIT_OPTIONS}")
                else:
                    self.log_debug(
                        f"Nature of Suit selector '{self.NATURE_OF_SUIT_SELECTOR}' found, but no NATURE_OF_SUIT_OPTIONS defined to select.")
            else:
                self.log_debug(
                    f"Nature of Suit selector '{self.NATURE_OF_SUIT_SELECTOR}' not found. Skipping NOS selection.")
        except PlaywrightError as e:
            self.log_warning(
                f"Non-critical error selecting Nature of Suit using '{self.NATURE_OF_SUIT_SELECTOR}': {e}",
                exc_info=True)
        await self._select_case_type()
        await self._select_case_flags()
        await self._check_include_closed_cases()
        self.log_info(f"Report configuration complete for {self.court_id}.")

    async def _fill_date_fields(self):
        date_field_pairs_selectors = [
            (self.DATE_FROM_PRIMARY_SELECTOR, self.DATE_TO_PRIMARY_SELECTOR),
            (self.DATE_FROM_ALT_SELECTOR, self.DATE_TO_ALT_SELECTOR)
        ]
        filled = False
        for from_selector, to_selector in date_field_pairs_selectors:
            try:
                from_locator_obj = await self.navigator.locator(from_selector)
                to_locator_obj = await self.navigator.locator(to_selector)
                if await from_locator_obj.count() > 0 and await to_locator_obj.count() > 0:
                    await self.navigator.fill(from_selector, self.from_date_str)
                    await self.navigator.fill(to_selector, self.to_date_str)
                    self.log_info(f"Filled date fields using selectors '{from_selector}' and '{to_selector}'.")
                    filled = True
                    break
            except PlaywrightError as e:
                self.log_debug(
                    f"Date field pair using selectors '{from_selector}'/'{to_selector}' not found or failed to fill: {e}")
        if not filled:
            self.log_error("Failed to find and fill any known date field pairs.")
            if self.navigator.page and not self.navigator.page.is_closed():
                await self.navigator.save_screenshot("fill_date_fail_all_pairs")
            raise PlaywrightError("Could not fill date fields on report after trying defined pairs.")

    async def run_report(self) -> bool:
        self.log_info("Attempting to run the report...")
        page = self.navigator.page
        try:
            # Use PacerIgnoreDownloadService to check if document numbering should be applied
            if self.ignore_download_service and self.ignore_download_service.has_ignore_download_entries(self.court_id):
                self.log_info(
                    f"Court {self.court_id} has ignore_download entries. Filling document numbering fields with '1'")
                await self.ignore_download_service.fill_document_number_fields(page, self.court_id, "report")
            elif not self.ignore_download_service:
                self.log_warning(
                    "PacerIgnoreDownloadService not available to ReportService. Skipping ignore_download check.")
        except Exception as e:
            self.log_warning(f"Error in ignore_download document numbering logic using PacerIgnoreDownloadService: {e}")

        if not self.RUN_REPORT_BUTTON_SELECTORS:
            self.log_error("No RUN_REPORT_BUTTON_SELECTORS defined in ReportService.__init__.")
            raise ValueError("RUN_REPORT_BUTTON_SELECTORS is not defined or empty.")

        target_selector = self.RUN_REPORT_BUTTON_SELECTORS[0]
        self.log_debug(f"Locating 'Run Report' button using primary selector: {target_selector}")
        try:
            button_locator_collection = await self.navigator.locator(target_selector)
            if await button_locator_collection.count() == 0:
                self.log_error(f"Run Report button not found with primary selector: {target_selector}")
                raise PlaywrightError(f"Run Report button not found: {target_selector}")
            button_locator_instance = button_locator_collection.first
            await button_locator_instance.wait_for(state='visible', timeout=15000)
            if not await button_locator_instance.is_enabled(timeout=5000):
                self.log_error(f"Run Report button '{target_selector}' found but is not enabled.")
                raise PlaywrightError(f"Run Report button '{target_selector}' not enabled.")
            self.log_info(f"Button '{target_selector}' is visible and enabled. Clicking...")
            try:
                await button_locator_instance.click(timeout=60000, force=True)
                self.log_info("Clicked 'Run Report' button with force=True.")
            except PlaywrightTimeoutError:
                self.log_warning("Click timeout, trying to dispatch click event directly...")
                await button_locator_instance.dispatch_event('click')
                self.log_info("Dispatched click event to 'Run Report' button.")
            try:
                # Validate page state before waiting for load state
                if not page or page.is_closed():
                    self.log_error(f"Page is closed before wait_for_load_state for {self.court_id}")
                    raise PlaywrightError(f"Page closed before waiting for load state for {self.court_id}")
                
                await page.wait_for_load_state("domcontentloaded", timeout=60000)
                self.log_info(f"Page loaded after Run Report. New URL: {page.url}")
            except PlaywrightTimeoutError:
                self.log_warning("Navigation timeout after clicking Run Report. Checking for dynamic content...")
                # Validate page state before wait_for_timeout
                if not page or page.is_closed():
                    self.log_error(f"Page is closed during timeout recovery for {self.court_id}")
                    return False
                    
                await page.wait_for_timeout(10000)
                try:
                    # Validate page state before wait_for_selector
                    if not page or page.is_closed():
                        self.log_error(f"Page is closed before wait_for_selector for {self.court_id}")
                        return False
                        
                    await page.wait_for_selector(self.RESULTS_TABLE_SELECTOR, timeout=30000)
                    self.log_info("Report content detected despite navigation timeout.")
                except PlaywrightTimeoutError:
                    self.log_warning("No report table found after extended wait.")
                except PlaywrightError as e_selector:
                    # Handle context closure during selector wait
                    if (
                        "target page, context or browser has been closed"
                        in str(e_selector).lower()
                        or "execution context was destroyed" in str(e_selector).lower()
                    ):
                        self.log_error(
                            f"Page/context closed during selector wait for {self.court_id}. Error: {e_selector}"
                        )
                        return False
                    else:
                        self.log_error(f"PlaywrightError during selector wait: {e_selector}")
                        raise
        except PlaywrightError as e_click_nav:
            # Handle specific case where page/context has been closed
            if (
                "target page, context or browser has been closed"
                in str(e_click_nav).lower()
                or "execution context was destroyed" in str(e_click_nav).lower()
            ):
                self.log_error(
                    f"Page/context closed UNEXPECTEDLY during Run Report for {self.court_id}. Error: {e_click_nav}"
                )
                return False  # Graceful degradation instead of crashing
            else:
                self.log_error(
                    f"Critical PlaywrightError during 'Run Report' button click or navigation using '{target_selector}': {e_click_nav}",
                    exc_info=True)
                if self.navigator.page and not self.navigator.page.is_closed():
                    await self.navigator.save_screenshot(f"run_report_click_nav_critical_error_{self.court_id}")
                raise
        self.log_info("Report run initiated, proceeding to verify content...")
        
        # Final page state validation before proceeding
        if not page or page.is_closed():
            self.log_error(f"Page is closed before final verification for {self.court_id}")
            return False
            
        report_load_delay_ms = 2000
        await page.wait_for_timeout(report_load_delay_ms)
        has_cases = await self._verify_report_content()
        if has_cases:
            await self._extract_and_save_report_list()
        return has_cases

    async def _verify_report_content(self) -> bool:
        page = self.navigator.page
        log_prefix = f"[{self.court_id}] VerifyReport:"
        self.log_debug(f"{log_prefix} Verifying report content on URL: {page.url}...")
        try:
            no_cases_locator_obj = await self.navigator.locator(self.NO_CASES_FOUND_SELECTOR)
            all_no_cases_elements = await no_cases_locator_obj.all()
            for el_locator in all_no_cases_elements:
                if await el_locator.is_visible(timeout=7000):
                    no_cases_text_found = await el_locator.text_content()
                    self.log_info(f"{log_prefix} 'No cases found' message detected: '{no_cases_text_found}'.")
                    return False
        except PlaywrightError:
            self.log_debug(
                f"{log_prefix} No 'no cases found' message detected quickly via '{self.NO_CASES_FOUND_SELECTOR}', or error during check. Proceeding to check for data rows.")
        row_detection_timeout_ms = max(self.navigator.timeout, 60000)
        self.log_debug(
            f"{log_prefix} Waiting for at least one data row matching XPath: '{self.XPATH_DATA_ROW_WITH_LINK_SELECTOR}' with timeout {row_detection_timeout_ms}ms")
        try:
            data_rows_locator_obj = await self.navigator.locator(self.XPATH_DATA_ROW_WITH_LINK_SELECTOR)
            await data_rows_locator_obj.first.wait_for(state="visible", timeout=row_detection_timeout_ms)
            count = await data_rows_locator_obj.count()
            if count > 0:
                self.log_info(
                    f"{log_prefix} Found {count} data row(s) with docket links using XPath '{self.XPATH_DATA_ROW_WITH_LINK_SELECTOR}'. Cases present.")
                return True
            else:
                self.log_warning(
                    f"{log_prefix} XPath '{self.XPATH_DATA_ROW_WITH_LINK_SELECTOR}' matched a row initially (wait_for succeeded), but subsequent count is 0. This is unexpected. Assuming no cases to be safe.")
                if self.navigator.page and not self.navigator.page.is_closed():
                    await self.navigator.save_screenshot(f"verify_report_xpath_row_count_issue_{self.court_id}")
                return False
        except PlaywrightTimeoutError:
            self.log_error(
                f"{log_prefix} Timeout ({row_detection_timeout_ms}ms) waiting for data rows via XPath ('{self.XPATH_DATA_ROW_WITH_LINK_SELECTOR}') AND no 'no cases found' message was seen. Assuming no usable report results.")
            if self.navigator.page and not self.navigator.page.is_closed():
                await self.navigator.save_screenshot(f"verify_report_data_rows_xpath_timeout_{self.court_id}")
            return False
        except PlaywrightError as e_row_check:
            self.log_error(
                f"{log_prefix} PlaywrightError while checking for data rows via XPath ('{self.XPATH_DATA_ROW_WITH_LINK_SELECTOR}'): {e_row_check}",
                exc_info=True)
            if self.navigator.page and not self.navigator.page.is_closed():
                await self.navigator.save_screenshot(f"verify_report_data_rows_xpath_error_{self.court_id}")
            return False

    async def _extract_and_save_report_list(self) -> None:
        page = self.navigator.page
        log_prefix = f"[{self.court_id}] ExtractReportList:"
        self.log_info(f"{log_prefix} === STARTING DOCKET REPORT LIST EXTRACTION ===")
        self.log_info(f"{log_prefix} Current URL: {page.url if page else 'No page'}")
        self.log_info(f"{log_prefix} ReportService ISO date: {self.iso_date}")
        try:
            data_rows_locator_obj = await self.navigator.locator(self.XPATH_DATA_ROW_WITH_LINK_SELECTOR)
            row_count = await data_rows_locator_obj.count()
            self.log_info(f"{log_prefix} Found {row_count} data rows using XPath selector")
            if row_count == 0:
                self.log_warning(f"{log_prefix} No data rows found to extract.")
                if self.navigator.page and not self.navigator.page.is_closed():
                    await self.navigator.save_screenshot(f"no_data_rows_found_{self.court_id}")
                return
            report_list = []
            self.log_info(f"{log_prefix} Starting extraction from {row_count} rows...")
            for i in range(row_count):
                row_locator = data_rows_locator_obj.nth(i)
                try:
                    row_data = await self._extract_single_row_data(row_locator, i + 1, log_prefix)
                    if row_data:
                        report_list.append(row_data)
                        self.log_debug(
                            f"{log_prefix} Row {i + 1}: Extracted data for docket {row_data.get('docket_num', 'unknown')}")
                    else:
                        self.log_debug(f"{log_prefix} Row {i + 1}: No data extracted")
                except Exception as e:
                    self.log_warning(f"{log_prefix} Error extracting data from row {i + 1}: {e}")
                    continue
            self.log_info(f"{log_prefix} Extraction complete. Valid cases: {len(report_list)} / {row_count}")
            if report_list:
                self.log_info(f"{log_prefix} Proceeding to save {len(report_list)} cases to JSON...")
                await self._save_report_list_to_json(report_list, log_prefix)
                self.log_info(f"{log_prefix} ✅ EXTRACTION AND SAVE COMPLETED for {len(report_list)} cases.")
            else:
                self.log_warning(f"{log_prefix} ❌ No valid case data extracted from {row_count} rows.")
        except Exception as e:
            self.log_error(f"{log_prefix} ❌ EXCEPTION during report list extraction: {e}")
            if self.navigator.page and not self.navigator.page.is_closed():
                await self.navigator.save_screenshot(f"extract_report_list_error_{self.court_id}")

    def _extract_mdl_num_from_flags(self, flags: List[str]) -> Optional[str]:
        if not flags:
            return None
        for flag in flags:
            mdl_match = re.search(r'MDL[\s-]?(\d{4,})', str(flag), re.IGNORECASE)
            if mdl_match:
                extracted_mdl_num = mdl_match.group(1)
                self.log_info(f"Extracted mdl_num='{extracted_mdl_num}' from flag: '{flag}'")
                return extracted_mdl_num
        return None

    def _parse_defendants_from_versus(self, versus_text: str) -> List[Dict[str, str]]:
        if not versus_text or 'v.' not in versus_text.lower():
            return []
        try:
            parts = re.split(r'\s+v\.?\s+', versus_text, flags=re.IGNORECASE)
            if len(parts) < 2:
                return []
            defendants_text = parts[1].strip()
            defendants_text = re.sub(r'\s+et\s+al\.?$', '', defendants_text, flags=re.IGNORECASE)

            # Split on 'and' first as it's more reliable than comma
            and_split = re.split(r'\s+and\s+', defendants_text, flags=re.IGNORECASE)

            defendants_raw = []
            # Process each segment from the 'and' split
            for segment in and_split:
                segment = segment.strip()
                # Check if this segment contains common corporate suffixes that shouldn't be split on comma
                corporate_pattern = r'\b(?:Inc|LLC|Corp|Corporation|Co|Company|Ltd|Limited|LP|LLP|PLLC|PC)\.?\b'

                # If it contains a corporate suffix after a comma, don't split on comma
                if re.search(r',\s*' + corporate_pattern, segment, flags=re.IGNORECASE):
                    # Keep as single defendant - it's likely a corporate name with suffix
                    defendants_raw.append(segment)
                else:
                    # Safe to split on comma
                    comma_split = re.split(r',\s*', segment)
                    defendants_raw.extend(comma_split)

            defendants = []
            for defendant_name in defendants_raw:
                defendant_name = defendant_name.strip()
                if defendant_name and len(defendant_name) > 1:
                    defendants.append({'name': defendant_name})
            return defendants
        except Exception as e:
            self.log_debug(f"Error parsing defendants from versus '{versus_text}': {e}")
            return []

    async def _extract_single_row_data(self, row_locator, row_num: int, log_prefix: str) -> Optional[Dict[str, Any]]:
        try:
            await row_locator.wait_for(state='attached', timeout=5000)
            cell_locators = await row_locator.locator("td").all()
            if len(cell_locators) < 2:
                self.log_debug(f"{log_prefix} Row {row_num}: Found fewer than 2 cells. Skipping row.")
                return None
            docket_link_loc = cell_locators[0].locator("a").first
            if await docket_link_loc.count() == 0:
                self.log_debug(f"{log_prefix} Row {row_num}: Could not find docket link element. Skipping.")
                return None
            docket_text = await docket_link_loc.text_content(timeout=3000)
            docket_href = await docket_link_loc.get_attribute("href", timeout=3000)
            versus_loc = cell_locators[0].locator("b").first
            versus_text = await versus_loc.text_content(
                timeout=3000) if await versus_loc.count() > 0 else "Unknown Versus"
            date_cell_text = await cell_locators[1].text_content(timeout=3000)
            filing_date_cleaned = ""
            if date_cell_text:
                date_cell_cleaned = date_cell_text.strip()
                date_match = re.search(r"(?:Filed:?\s*|Case\s+filed:?\s*)?(\d{1,2}/\d{1,2}/\d{2,4})", date_cell_cleaned,
                                       re.IGNORECASE)
                if date_match:
                    filing_date_cleaned = date_match.group(1)
                else:
                    filing_date_cleaned = date_cell_cleaned
            versus_cleaned = versus_text.strip() if versus_text else ""
            defendants_from_versus = self._parse_defendants_from_versus(versus_cleaned)
            case_data = {
                'court_id': self.court_id,
                'docket_num': docket_text.strip() if docket_text else "",
                'versus': versus_cleaned,
                'filing_date': filing_date_cleaned,
                'docket_link': docket_href.strip() if docket_href else "",
                'extracted_at': datetime.now().isoformat(),
                'source': 'PACER Case Report',
                'defendants': defendants_from_versus,
                'defendant': defendants_from_versus
            }
            if len(cell_locators) >= 4:
                details_cell_loc = cell_locators[3]
                try:
                    inner_text = await details_cell_loc.evaluate("el => el.innerText") or ""
                    lines = inner_text.splitlines()
                    for line in lines:
                        line_clean = line.strip()
                        if line_clean.lower().startswith('cause:'):
                            case_data['cause'] = line_clean[len('cause:'):].strip()
                        elif line_clean.lower().startswith('nos:'):
                            case_data['nos'] = line_clean[len('nos:'):].strip()
                        elif line_clean.lower().startswith('case flags:') or line_clean.lower().startswith(
                                'case\u00a0flags:'):
                            flags_text = line_clean[
                                         len('case flags:'):].strip() if 'case flags:' in line_clean.lower() else line_clean[
                                                                                                                  len('case\u00a0flags:'):].strip()
                            case_data['case_flags'] = flags_text
                            case_data['flags'] = [flag.strip() for flag in flags_text.split(',') if flag.strip()]
                            mdl_num = self._extract_mdl_num_from_flags(case_data['flags'])
                            if mdl_num:
                                case_data['mdl_num'] = mdl_num
                except Exception as e_detail:
                    self.log_debug(f"{log_prefix} Row {row_num}: Error extracting details from cell 4: {e_detail}")
            return case_data
        except Exception as e:
            self.log_warning(f"{log_prefix} Row {row_num}: Error extracting row data: {e}")
            return None

    async def _save_report_list_to_json(self, report_list: List[Dict[str, Any]], log_prefix: str) -> None:
        try:
            self.log_info(f"{log_prefix} === DOCKET REPORT LIST SAVE DEBUG ===")
            self.log_info(f"{log_prefix} Court ID: {self.court_id}")
            self.log_info(f"{log_prefix} ISO Date (self.iso_date): {self.iso_date}")
            self.log_info(f"{log_prefix} From Date: {self.from_date_str}")
            self.log_info(f"{log_prefix} To Date: {self.to_date_str}")
            self.log_info(f"{log_prefix} Report list count: {len(report_list)}")
            if not self.iso_date:
                raise PacerServiceError("iso_date must be provided from config, not generated from today's date")
            scraping_date = self.iso_date
            current_date = self.iso_date
            self.log_info(f"{log_prefix} Determined scraping date: {scraping_date}")
            self.log_info(f"{log_prefix} Current date for comparison: {current_date}")
            if scraping_date != current_date:
                self.log_info(f"{log_prefix} ✅ Using SCRAPING date ({scraping_date}) - CORRECT!")
            else:
                self.log_warning(f"{log_prefix} ⚠️ Using current date ({current_date}) - may be wrong!")
            docket_report_dir = os.path.join('data', scraping_date, 'logs', 'docket_report')
            self.log_info(f"{log_prefix} Target directory: {docket_report_dir}")
            dir_exists_before = os.path.exists(docket_report_dir)
            self.log_info(f"{log_prefix} Directory exists before creation: {dir_exists_before}")
            os.makedirs(docket_report_dir, exist_ok=True)
            dir_exists_after = os.path.exists(docket_report_dir)
            self.log_info(f"{log_prefix} Directory exists after creation: {dir_exists_after}")
            if not dir_exists_after:
                self.log_error(f"{log_prefix} CRITICAL: Failed to create directory {docket_report_dir}")
                return
            filename = f"{self.court_id}.json"
            file_path = os.path.join(docket_report_dir, filename)
            self.log_info(f"{log_prefix} Target file path: {file_path}")
            self.log_info(f"{log_prefix} Absolute file path: {os.path.abspath(file_path)}")
            report_data = {
                'metadata': {
                    'court_id': self.court_id,
                    'generated_at': datetime.now().isoformat(),
                    'date_range': f"{self.from_date_str} to {self.to_date_str}",
                    'total_cases': len(report_list),
                    'source': 'PACER Case Filed Report',
                    'scraping_date_used': scraping_date,
                    'iso_date_provided': self.iso_date
                },
                'cases': report_list
            }
            self.log_info(f"{log_prefix} Prepared data structure with {len(report_list)} cases")
            self.log_info(f"{log_prefix} Attempting to save JSON file...")
            success = safe_json_write(file_path, report_data, indent=2)
            if success:
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    self.log_info(f"{log_prefix} ✅ SUCCESS: Saved report list to: {file_path}")
                    self.log_info(f"{log_prefix} ✅ File size: {file_size} bytes")
                    self.log_info(f"{log_prefix} ✅ File contains {len(report_list)} cases")
                else:
                    self.log_error(f"{log_prefix} ❌ File was marked as saved but doesn't exist: {file_path}")
            else:
                self.log_error(f"{log_prefix} ❌ safe_json_write returned False for: {file_path}")
        except Exception as e:
            self.log_error(f"{log_prefix} ❌ EXCEPTION in _save_report_list_to_json: {e}")

    async def __aenter__(self):
        # Ensure navigator is ready, if it exists and has _ensure_ready
        if hasattr(self.navigator, 'is_ready') and not self.navigator.is_ready:
            if hasattr(self.navigator, '_ensure_ready') and asyncio.iscoroutinefunction(self.navigator._ensure_ready):
                await self.navigator._ensure_ready()
            else:
                self.log_warning("Navigator does not have an async _ensure_ready method or is_ready attribute.")
        elif not hasattr(self.navigator, 'is_ready'):
            self.log_warning("Navigator does not have an is_ready attribute.")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # No specific cleanup actions identified for ReportService itself in the original ReportHandler.
        # If navigator or other resources managed by ReportService needed cleanup, it would go here.
        # For now, it's a pass-through, consistent with the original __aexit__.
        self.log_debug(f"ReportService for {self.court_id} exiting context manager.")
        pass


```
