#!/usr/bin/env python3
"""
Fix PACER workflow to implement resumption logic.
This script directly modifies the workflow_orchestrator.py file.
"""

import os
import sys

def apply_workflow_fix():
    """Apply the workflow fix directly to the file."""
    
    file_path = "src/pacer/components/processing/workflow_orchestrator.py"
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"Error: {file_path} not found")
        return False
    
    # Read the current file
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    # Find the import section and add json import
    import_line_idx = None
    for i, line in enumerate(lines):
        if line.startswith('import os'):
            import_line_idx = i
            break
    
    if import_line_idx:
        # Add json import before os import
        lines.insert(import_line_idx, 'import json\n')
    
    # Find the class and add new methods after execute_action
    for i, line in enumerate(lines):
        if 'raise ValueError(f"Unknown action for WorkflowOrchestrator:' in line:
            # Insert new methods after this line
            new_methods = '''
    def _ensure_directories_exist(self, iso_date: str) -> None:
        """Ensure required data directories exist for the given date."""
        directories = [
            f"data/{iso_date}",
            f"data/{iso_date}/logs",
            f"data/{iso_date}/courts",
            f"data/{iso_date}/downloads"
        ]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            self.log_debug(f"Ensured directory exists: {directory}")

    def _get_docket_log_path(self, court_id: str, iso_date: str) -> str:
        """Get the path to the docket report log file for a specific court and date."""
        return f"data/{iso_date}/logs/docket_report_list_{court_id}.json"

    async def _check_log_exists(self, path: str) -> bool:
        """Check if a docket report log file exists."""
        return os.path.exists(path)

    async def _load_docket_log(self, path: str):
        """Load existing docket report log from file."""
        try:
            with open(path, 'r') as f:
                data = json.load(f)
                self.log_info(f"Loaded docket log with {len(data.get('cases', []))} cases")
                return data
        except Exception as e:
            self.log_error(f"Failed to load docket log: {e}")
            return None

'''
            lines.insert(i + 1, new_methods)
            break
    
    # Find and replace the process_single_court_task login section
    in_login_section = False
    login_success_idx = None
    
    for i, line in enumerate(lines):
        if 'Login successful.' in line:
            login_success_idx = i
            break
    
    if login_success_idx:
        # Find the next "Navigate to Query Page" section
        nav_start_idx = None
        report_gen_idx = None
        
        for i in range(login_success_idx, min(login_success_idx + 50, len(lines))):
            if '# Navigate to Query Page' in lines[i]:
                nav_start_idx = i
            if '# Report Generation' in lines[i]:
                report_gen_idx = i
                break
        
        if nav_start_idx and report_gen_idx:
            # Replace the section between login and report generation
            replacement = '''
            # ===== CRITICAL FIX 1: Ensure directories exist =====
            self._ensure_directories_exist(iso_date)
            
            # ===== CRITICAL FIX 2: Check for existing docket_report_log =====
            log_file_path = self._get_docket_log_path(court_id, iso_date)
            self.log_info(f"{log_prefix} Checking for existing docket log at: {log_file_path}")
            
            docket_log_data = None
            has_cases = False
            
            if await self._check_log_exists(log_file_path):
                # PATH A: Resume from existing log
                self.log_info(f"{log_prefix} Found existing docket_report_log - RESUMING from previous session")
                docket_log_data = await self._load_docket_log(log_file_path)
                
                if docket_log_data and docket_log_data.get('cases'):
                    has_cases = True
                    self.log_info(f"{log_prefix} Loaded {len(docket_log_data['cases'])} cases from existing log")
                else:
                    self.log_warning(f"{log_prefix} Log file exists but is empty or invalid, will generate new report")
                    docket_log_data = None
            
            # Only generate new report if we don't have valid log data
            if not docket_log_data:
                # PATH B: Start new - Generate civil cases report
                self.log_info(f"{log_prefix} No existing docket_report_log - STARTING NEW session")
                
                # Navigate to Query Page
                if not self.nav_facade:
                    self.log_warning(f"{log_prefix} NavigationFacade not available - skipping navigation")
                else:
                    await self.nav_facade.execute({
                        "action": "go_to_query_page", "navigator": navigator, "court_id": court_id
                    })
                self.log_info(f"{log_prefix} Navigation to query page successful.")

'''
            # Remove old lines and insert new ones
            del lines[nav_start_idx:report_gen_idx]
            lines.insert(nav_start_idx, replacement)
    
    # Write the modified file
    with open(file_path, 'w') as f:
        f.writelines(lines)
    
    print(f"Successfully modified {file_path}")
    return True

if __name__ == "__main__":
    if apply_workflow_fix():
        print("Workflow fix applied successfully!")
        sys.exit(0)
    else:
        print("Failed to apply workflow fix")
        sys.exit(1)