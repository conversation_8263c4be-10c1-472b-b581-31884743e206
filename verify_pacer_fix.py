#!/usr/bin/env python3
"""
Verification script for PACER court processing fixes.
This script validates that all three critical issues have been resolved:
1. System checks for existing docket_report_log files (resumption logic)
2. System properly runs civil cases report when log doesn't exist  
3. System creates required data/{iso_date}/ folders
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

ISO_DATE = datetime.now().strftime("%Y%m%d")
DATA_PATH = "./data"


async def verify_directory_creation():
    """Verify that all required directories are created."""
    print("\n" + "="*60)
    print("1. VERIFYING DIRECTORY CREATION")
    print("="*60)
    
    from src.pacer._core_services.file_operations.file_operations_service import FileOperationsService
    
    # Create service with proper inheritance setup
    file_service = FileOperationsService(logger, {"data_path": DATA_PATH})
    await file_service.initialize()
    
    # Setup directories
    result = await file_service.execute({
        "action": "setup_directories",
        "iso_date": ISO_DATE
    })
    
    # Check all required directories
    base_path = Path(DATA_PATH) / ISO_DATE
    required_dirs = [
        "dockets/temp",
        "dockets/.locks", 
        "logs/pacer",
        "logs/docket_report",
        "html",
        "screenshots",
        "courts",
        "reports",
        "exports"
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        full_path = base_path / dir_path
        exists = full_path.exists() and full_path.is_dir()
        status = "✅" if exists else "❌"
        print(f"  {status} {dir_path}")
        if not exists:
            all_exist = False
    
    if all_exist:
        print("\n✅ SUCCESS: All required directories created!")
    else:
        print("\n❌ FAILURE: Some directories missing!")
    
    return all_exist


async def verify_docket_log_checking():
    """Verify that system checks for existing docket_report_log files."""
    print("\n" + "="*60)
    print("2. VERIFYING DOCKET LOG CHECKING (RESUMPTION)")
    print("="*60)
    
    from src.pacer.pacer_orchestrator_service import PacerOrchestratorService
    
    # Create orchestrator
    orchestrator = PacerOrchestratorService(logger, {"data_path": DATA_PATH})
    orchestrator._initialized = True
    
    # Create a test docket report log
    court_id = "test_court"
    log_dir = Path(DATA_PATH) / ISO_DATE / "logs" / "docket_report"
    log_dir.mkdir(parents=True, exist_ok=True)
    
    log_file = log_dir / f"{court_id}.json"
    test_data = {
        "cases": [
            {
                "court_id": court_id,
                "docket_number": "1:24-cv-12345",
                "title": "Test Case v. Test Defendant",
                "filing_date": "2024-01-15"
            },
            {
                "court_id": court_id,
                "docket_number": "1:24-cv-67890",
                "title": "Another Test v. Another Defendant",
                "filing_date": "2024-01-20"
            }
        ],
        "metadata": {
            "court_id": court_id,
            "generated_at": datetime.now().isoformat(),
            "total_cases": 2,
            "source": "test_verification"
        }
    }
    
    # Save test log
    log_file.write_text(json.dumps(test_data, indent=2))
    print(f"  Created test log: {log_file}")
    
    # Test loading the log
    loaded_data = await orchestrator.load_docket_report_log(court_id, ISO_DATE)
    
    if loaded_data:
        print(f"  ✅ Successfully loaded docket report log")
        print(f"  ✅ Cases found: {len(loaded_data.get('cases', []))}")
        print(f"  ✅ Metadata preserved: {loaded_data.get('metadata', {}).get('source')}")
        
        # Verify data integrity
        if loaded_data["cases"][0]["docket_number"] == "1:24-cv-12345":
            print(f"  ✅ Data integrity verified")
            return True
        else:
            print(f"  ❌ Data integrity check failed")
            return False
    else:
        print(f"  ❌ Failed to load docket report log")
        return False


async def verify_civil_report_generation():
    """Verify that civil cases report is generated when no log exists."""
    print("\n" + "="*60)
    print("3. VERIFYING CIVIL REPORT GENERATION")
    print("="*60)
    
    from src.pacer.pacer_orchestrator_service import PacerOrchestratorService
    from unittest.mock import AsyncMock, MagicMock
    
    # Create orchestrator with mocked services
    orchestrator = PacerOrchestratorService(logger, {"data_path": DATA_PATH})
    orchestrator._initialized = True
    
    # Mock browser and docket services
    mock_browser = AsyncMock()
    mock_browser.get_browser_context = AsyncMock(return_value=MagicMock())
    
    mock_docket = AsyncMock()
    mock_docket.discover_dockets = AsyncMock(return_value={
        "cases": [
            {
                "court_id": "new_court",
                "docket_number": "2:24-cv-99999",
                "title": "Generated Case v. Generated Defendant"
            }
        ],
        "metadata": {
            "total_found": 1,
            "generated_at": datetime.now().isoformat()
        }
    })
    
    orchestrator._browser_service = mock_browser
    orchestrator._docket_orchestrator = mock_docket
    
    court_id = "new_court"
    
    # Ensure no existing log
    log_file = Path(DATA_PATH) / ISO_DATE / "logs" / "docket_report" / f"{court_id}.json"
    if log_file.exists():
        log_file.unlink()
    
    print(f"  Verifying no existing log: {not log_file.exists()} ✅")
    
    # Generate civil cases report
    result = await orchestrator.generate_civil_cases_report(
        court_id=court_id,
        iso_date=ISO_DATE,
        start_date="2024-01-01",
        end_date="2024-01-31"
    )
    
    if result:
        print(f"  ✅ Civil cases report generated")
        print(f"  ✅ Cases returned: {len(result.get('cases', []))}")
        
        # Verify log file was created
        if log_file.exists():
            print(f"  ✅ Log file created: {log_file}")
            
            # Verify content
            saved_data = json.loads(log_file.read_text())
            if saved_data["cases"][0]["docket_number"] == "2:24-cv-99999":
                print(f"  ✅ Log file content verified")
                return True
            else:
                print(f"  ❌ Log file content incorrect")
                return False
        else:
            print(f"  ❌ Log file not created")
            return False
    else:
        print(f"  ❌ Failed to generate civil cases report")
        return False


async def verify_complete_workflow():
    """Verify the complete workflow with all three modes."""
    print("\n" + "="*60)
    print("4. VERIFYING COMPLETE WORKFLOW (THREE MODES)")
    print("="*60)
    
    from src.pacer.pacer_orchestrator_service import PacerOrchestratorService
    from unittest.mock import AsyncMock
    
    orchestrator = PacerOrchestratorService(logger, {"data_path": DATA_PATH})
    orchestrator._initialized = True
    
    # Mock docket orchestrator
    mock_docket = AsyncMock()
    mock_docket.process_court = AsyncMock(return_value={
        "status": "completed",
        "processed_dockets": 5
    })
    orchestrator._docket_orchestrator = mock_docket
    
    print("\n  Mode 1: Special Docket List")
    print("  " + "-"*30)
    request_special = {
        "court_id": "mode1_court",
        "iso_date": ISO_DATE,
        "docket_list_input": ["1:24-cv-00001", "1:24-cv-00002"]
    }
    
    result1 = await orchestrator._process_single_court_with_context(
        court_id="mode1_court",
        config_data={},
        browser_context=None,
        request_data=request_special
    )
    
    if result1["status"] == "completed":
        print(f"  ✅ Mode 1 (Special List) working")
    else:
        print(f"  ❌ Mode 1 (Special List) failed")
    
    print("\n  Mode 2: Resume from Existing Log")
    print("  " + "-"*30)
    
    # Create log for mode 2
    court_id = "mode2_court"
    log_dir = Path(DATA_PATH) / ISO_DATE / "logs" / "docket_report"
    log_file = log_dir / f"{court_id}.json"
    log_file.write_text(json.dumps({
        "cases": [{"docket_number": "2:24-cv-00001"}],
        "metadata": {"court_id": court_id}
    }))
    
    request_resume = {
        "court_id": court_id,
        "iso_date": ISO_DATE
    }
    
    result2 = await orchestrator._process_single_court_with_context(
        court_id=court_id,
        config_data={},
        browser_context=None,
        request_data=request_resume
    )
    
    if result2["status"] == "completed":
        print(f"  ✅ Mode 2 (Resume) working")
    else:
        print(f"  ❌ Mode 2 (Resume) failed")
    
    print("\n  Mode 3: Generate New Report")
    print("  " + "-"*30)
    
    # Remove log for mode 3
    court_id = "mode3_court"
    log_file = log_dir / f"{court_id}.json"
    if log_file.exists():
        log_file.unlink()
    
    # Mock civil report generation
    orchestrator.generate_civil_cases_report = AsyncMock(return_value={
        "cases": [{"docket_number": "3:24-cv-00001"}],
        "metadata": {"court_id": court_id}
    })
    
    request_new = {
        "court_id": court_id,
        "iso_date": ISO_DATE,
        "start_date": "2024-01-01",
        "end_date": "2024-01-31"
    }
    
    result3 = await orchestrator._process_single_court_with_context(
        court_id=court_id,
        config_data={},
        browser_context=None,
        request_data=request_new
    )
    
    if result3["status"] == "completed":
        print(f"  ✅ Mode 3 (Generate New) working")
    else:
        print(f"  ❌ Mode 3 (Generate New) failed")
    
    all_modes_work = all([
        result1["status"] == "completed",
        result2["status"] == "completed",
        result3["status"] == "completed"
    ])
    
    return all_modes_work


async def main():
    """Run all verification tests."""
    print("\n" + "🔧"*30)
    print("   PACER COURT PROCESSING FIX VERIFICATION")
    print("🔧"*30)
    
    results = {}
    
    # Run all verifications
    results["directory_creation"] = await verify_directory_creation()
    results["docket_log_checking"] = await verify_docket_log_checking()
    results["civil_report_generation"] = await verify_civil_report_generation()
    results["complete_workflow"] = await verify_complete_workflow()
    
    # Summary
    print("\n" + "="*60)
    print("FINAL VERIFICATION SUMMARY")
    print("="*60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {status}: {test_name.replace('_', ' ').title()}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! PACER WORKFLOW IS FIXED!")
        print("\nThe system now correctly:")
        print("  1. Creates all required data/{iso_date}/ directories")
        print("  2. Checks for and loads existing docket_report_log files")
        print("  3. Generates civil cases reports when no log exists")
        print("  4. Supports all three processing modes:")
        print("     - Special docket list mode")
        print("     - Resume from existing reports")
        print("     - Generate new reports")
    else:
        print("⚠️  SOME TESTS FAILED - REVIEW OUTPUT ABOVE")
    print("="*60)
    
    return all_passed


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)