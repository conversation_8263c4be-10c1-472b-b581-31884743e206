Objective: Reduce number of facades while maintaining SOLID design principles.

**TASK 1**:
1. Review @src/services/pacer.
2. Come up with a step-by-step refactoring plan to reduce number of pacer facades while maintaining SOLID
design principles.
3. Save as an .md file.

**TASK 2**:
1. Review @src/services/transformer.
2. Come up with a step-by-step refactoring plan to reduce number of transformer facades while maintaining SOLID
design principles.
3. Save as an .md file.
