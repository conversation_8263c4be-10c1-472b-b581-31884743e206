#!/usr/bin/env python3
"""Test script to verify SEQUENTIAL processing of dockets."""

import asyncio
import time
from datetime import datetime
from unittest.mock import AsyncMock, Mock
import sys
sys.path.insert(0, '/Users/<USER>/PycharmProjects/lexgenius')

from src.pacer._processing_components.report_processor import ReportProcessor
from src.pacer.jobs.docket_job import DocketProcessingJob


async def test_sequential_vs_parallel():
    """Test that dockets are processed sequentially, not in parallel."""
    print("\n=== Testing Sequential Processing ===")
    
    # Track job processing times
    job_start_times = []
    job_end_times = []
    
    # Create a mock job processor that simulates processing time
    async def mock_process_job(data):
        job = data["job"]
        start_time = time.time()
        job_start_times.append((job.row_num, start_time))
        print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Started Job-{job.row_num}")
        
        # Simulate processing time
        await asyncio.sleep(0.2)  # 200ms per job
        
        end_time = time.time()
        job_end_times.append((job.row_num, end_time))
        print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Finished Job-{job.row_num}")
        return "success"
    
    # Create mock services
    mock_job_processor = AsyncMock()
    mock_job_processor.execute = mock_process_job
    
    # Create a proper logger
    import logging
    logger = logging.getLogger("test")
    logger.setLevel(logging.INFO)
    
    # Create report processor with logger
    report_processor = ReportProcessor(
        logger=logger,
        job_processor=mock_job_processor,
        case_processing_service=AsyncMock(),
        download_orchestration_service=AsyncMock(),
        file_operations_service=AsyncMock()
    )
    
    # Create mock page with fake rows
    mock_page = AsyncMock()
    mock_rows = []
    for i in range(5):  # Create 5 mock rows
        mock_row = AsyncMock()
        mock_link = AsyncMock()
        mock_link.first.get_attribute = AsyncMock(return_value=f"/dkt.pl?{i}")
        mock_link.first.all_text_contents = AsyncMock(return_value=[f"3:25-cv-{i:05d}"])
        mock_link.count = AsyncMock(return_value=1)
        
        mock_row.locator = Mock(side_effect=lambda selector: mock_link if "DktRpt" in selector else AsyncMock(
            all_text_contents=AsyncMock(return_value=[f"Case v. Test {i}"]),
            nth=Mock(return_value=AsyncMock(all_text_contents=AsyncMock(return_value=["08/01/2025"])))
        ))
        mock_rows.append(mock_row)
    
    mock_page.locator = Mock(return_value=AsyncMock(all=AsyncMock(return_value=mock_rows)))
    
    # Test SEQUENTIAL processing
    print("\n--- Testing SEQUENTIAL Processing ---")
    job_start_times.clear()
    job_end_times.clear()
    
    processor_config = {"sequential_processing": True}
    
    start = time.time()
    await report_processor.execute({
        "action": "process_report",
        "original_page": mock_page,
        "court_id": "test",
        "iso_date": "20250805",
        "start_date_obj": datetime(2025, 8, 1),
        "end_date_obj": datetime(2025, 8, 5),
        "context": AsyncMock(),
        "processor_config": processor_config,
        "relevance_engine": None,
        "court_logger": Mock()
    })
    total_time = time.time() - start
    
    print(f"\nSequential processing took: {total_time:.2f} seconds")
    
    # Check that jobs ran sequentially (no overlap)
    sequential = True
    for i in range(len(job_start_times) - 1):
        job1_num, job1_start = job_start_times[i]
        job1_end = next(t for n, t in job_end_times if n == job1_num)
        job2_num, job2_start = job_start_times[i + 1]
        
        if job2_start < job1_end:
            sequential = False
            print(f"❌ Job-{job2_num} started before Job-{job1_num} finished!")
        else:
            time_gap = job2_start - job1_end
            print(f"✅ Job-{job2_num} started {time_gap:.3f}s after Job-{job1_num} finished")
    
    if sequential:
        print("\n✅ Jobs processed SEQUENTIALLY (one at a time)")
    else:
        print("\n❌ Jobs processed in PARALLEL (overlapping)")
    
    # Test PARALLEL processing for comparison
    print("\n--- Testing PARALLEL Processing (for comparison) ---")
    job_start_times.clear()
    job_end_times.clear()
    
    processor_config = {"sequential_processing": False}
    
    start = time.time()
    await report_processor.execute({
        "action": "process_report",
        "original_page": mock_page,
        "court_id": "test",
        "iso_date": "20250805",
        "start_date_obj": datetime(2025, 8, 1),
        "end_date_obj": datetime(2025, 8, 5),
        "context": AsyncMock(),
        "processor_config": processor_config,
        "relevance_engine": None,
        "court_logger": Mock()
    })
    total_time = time.time() - start
    
    print(f"\nParallel processing took: {total_time:.2f} seconds")
    
    # Check for parallel execution
    parallel = False
    for i in range(len(job_start_times) - 1):
        job1_num, job1_start = job_start_times[i]
        job1_end = next(t for n, t in job_end_times if n == job1_num)
        job2_num, job2_start = job_start_times[i + 1]
        
        if job2_start < job1_end:
            parallel = True
            overlap = job1_end - job2_start
            print(f"✅ Job-{job2_num} started while Job-{job1_num} was running (overlap: {overlap:.3f}s)")
    
    if parallel:
        print("\n✅ Jobs processed in PARALLEL (as expected)")
    else:
        print("\n⚠️ Jobs appear sequential even in parallel mode")
    
    return sequential


async def main():
    """Run the test."""
    print("=" * 60)
    print("Testing Sequential vs Parallel Processing")
    print("=" * 60)
    
    try:
        result = await test_sequential_vs_parallel()
        
        print("\n" + "=" * 60)
        if result:
            print("✅ SEQUENTIAL PROCESSING CONFIRMED!")
            print("Dockets will be processed one at a time, not all at once.")
        else:
            print("❌ WARNING: Sequential processing not working!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())