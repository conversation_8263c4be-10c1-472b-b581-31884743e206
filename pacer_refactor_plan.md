# PACER Refactoring Plan - Compliance with docket_processing.md

## Overview
This plan ensures strict compliance with the 4-phase workflow and decision trees documented in `docket_processing.md`. Each step is designed to be discrete and verifiable.

## Phase 1: Analysis Complete ✓

## Phase 2: Service Architecture Alignment

### Critical Workflow Fixes

- [x] **Step 1**: Fix DocketOrchestratorFacadeService Phase 1 workflow
  - Update `process_docket_case()` to call `CaseProcessingService.process_case_html`
  - Implement proper HTML validation sequence
  - Add page content stability check
  - Add HTML content validation step

- [x] **Step 2**: Implement proper Phase 2 Relevance Service workflow
  - Fix `RelevanceFacadeService.determine_case_relevance()` to follow exact decision tree
  - Add MDL flag override logic (P2_2 → P2_3)
  - Add ignore_download pattern matching (P2_4 → P2_5)  
  - Add statute exclusion check (P2_6 → P2_7)
  - Add explicit relevance check (P2_8 → P2_9)
  - Set proper relevance reasons for each path

- [x] **Step 3**: Implement Classification Service decision tree
  - Add `is_explicitly_requested` check in `CaseClassificationFacadeService`
  - Skip removal detection for reprocessing cases (P2_C1 → P2_C2)
  - Implement proper removal detection by cause and HTML (P2_C3)
  - Add transfer status checking (P2_C4)

- [x] **Step 4**: Fix Case Verification Service dual-path logic
  - Implement explicitly requested verification path (P3_2 → P3_3)
  - Implement report-scraped verification path (P3_2 → P3_4)
  - Add GSI database checks (P3_5, P3_9)
  - Add local artifacts checking with proper file type differentiation
  - Return proper skip/process decisions

- [x] **Step 5**: Align Download Orchestration Service with Phase 4
  - Implement "Validate Case for Download" step
  - Add "Should Attempt Download?" decision point
  - Implement "Prepare Download Context" step
  - Add proper success/failure handling with metadata
  - Implement skip metadata handling

- [x] **Step 6**: Implement File Operations Service final save workflow
  - Create court/date directory structure
  - Implement filename generation
  - Add data cleaning and preparation
  - Implement local JSON saving
  - Add S3 upload with error handling
  - Return proper completion states

### Service Method Alignment

- [x] **Step 7**: Rename service methods to match documentation
  - `CaseProcessingService.process_case_html()` 
  - `RelevanceService.determine_case_relevance()`
  - `ClassificationService.classify_case()`
  - `VerificationService.verify_case()`
  - `DownloadOrchestrationService.process_download_workflow()`
  - `FileOperationsService.save_and_upload_case_data()`

- [x] **Step 8**: Implement proper service interaction sequence
  - Ensure Phase 1 → Phase 2 → Phase 3 → Phase 4 → Final Save order
  - Add proper error propagation between phases
  - Implement phase-specific metadata tracking

### Decision Tree Implementation

- [x] **Step 9**: Add Legacy vs New Service Path Toggle
  - Implement "Use New Docket Services?" decision point at orchestrator level
  - Add legacy path fallback
  - Ensure backward compatibility

- [x] **Step 10**: Implement all documented decision points with exact logic
  - HTML Valid? (P1_4)
  - Has MDL Flag? (P2_2)  
  - Matches ignore_download Pattern? (P2_4)
  - Excluded by Statute? (P2_6)
  - Explicitly Relevant? (P2_8)
  - is_explicitly_requested? (P2_C1, P3_2)
  - Exists in DB? (P3_6, P3_10)
  - Artifacts Exist? (P3_8)
  - Any Files Exist? (P3_12)
  - Should Attempt Download? (P4_3)
  - Download Success? (P4_6)
  - Upload Enabled? (FS5)
  - Upload Success? (FS8)

### Error Handling & States

- [x] **Step 11**: Implement all documented error states
  - P1_FAIL: Return None - Invalid HTML
  - P3_SKIP: Skip - Already in Database  
  - P4_SKIP: Skip Download with Reason
  - P4_FAIL: Handle Download Failure
  - PARTIAL: Local Success + Upload Error
  - FAIL: Processing Failed

- [x] **Step 12**: Add processing metadata at each phase
  - Phase completion timestamps
  - Decision point outcomes
  - Skip reasons and error messages
  - Success/failure indicators

### Integration & Testing

- [x] **Step 13**: Update service dependencies and injection
  - Ensure all services receive proper dependencies
  - Update container configuration
  - Verify dependency injection works correctly

- [x] **Step 14**: Validate pipeline execution
  - Test `pipeline.sh --config scrape_pacer` 
  - Verify workflow follows exact documented sequence
  - Confirm decision trees work as specified

- [x] **Step 15**: Final compliance verification
  - Compare implementation against flowchart step-by-step
  - Verify all decision points are implemented
  - Ensure error states are properly handled
  - Confirm metadata tracking is complete

## Success Criteria

- [x] Every step in `docket_processing.md` flowchart is implemented
- [x] All decision trees follow exact documented logic
- [x] Pipeline executes successfully end-to-end
- [x] Service names and methods match documentation
- [x] Error handling covers all documented failure states
- [x] Processing metadata is tracked throughout workflow

EOF < /dev/null