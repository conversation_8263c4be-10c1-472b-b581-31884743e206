#!/usr/bin/env python3
"""
Test script to demonstrate CourtProcessor docket_report_log checking functionality.
"""
import asyncio
import os
import json
from pathlib import Path

# Add the src directory to Python path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.pacer.components.processing.court_processor import CourtProcessor


async def test_court_processor_docket_log_check():
    """Test the CourtProcessor's ability to check for existing docket report logs."""
    
    print("🧪 Testing CourtProcessor docket_report_log check functionality")
    print("=" * 60)
    
    # Initialize CourtProcessor
    processor = CourtProcessor()
    
    # Test parameters
    court_id = "ilnd"
    iso_date = "2024-08-09"
    
    # Test 1: Check path generation
    log_path = processor._get_docket_log_path(court_id, iso_date)
    print(f"📁 Generated log path: {log_path}")
    
    # Test 2: Check if log exists (should be False initially)
    exists = await processor._check_log_exists(log_path)
    print(f"📋 Log exists: {exists}")
    
    # Test 3: Create a mock docket log file
    print(f"\n🔧 Creating mock docket log file at: {log_path}")
    
    # Ensure directory exists
    os.makedirs(os.path.dirname(log_path), exist_ok=True)
    
    # Create mock log data
    mock_log_data = {
        "court_id": court_id,
        "iso_date": iso_date,
        "cases": [
            {
                "docket_num": "1:24-cv-12345",
                "case_title": "Test Case v. Another Party",
                "filed_date": "08/09/2024",
                "docket_url": f"https://ecf.{court_id}.uscourts.gov/cgi-bin/DktRpt.pl?12345",
            },
            {
                "docket_num": "1:24-cv-67890", 
                "case_title": "Second Test Case v. Different Party",
                "filed_date": "08/09/2024",
                "docket_url": f"https://ecf.{court_id}.uscourts.gov/cgi-bin/DktRpt.pl?67890",
            }
        ]
    }
    
    with open(log_path, 'w') as f:
        json.dump(mock_log_data, f, indent=2)
    
    # Test 4: Check if log exists now (should be True)
    exists_after = await processor._check_log_exists(log_path)
    print(f"📋 Log exists after creation: {exists_after}")
    
    # Test 5: Load the docket log
    print(f"\n📖 Loading docket log...")
    loaded_data = await processor._load_docket_log(log_path)
    
    if loaded_data:
        print(f"✅ Successfully loaded log with {len(loaded_data.get('cases', []))} cases")
        for i, case in enumerate(loaded_data.get('cases', []), 1):
            print(f"   {i}. {case.get('docket_num')} - {case.get('case_title', 'No title')}")
    else:
        print("❌ Failed to load log data")
    
    # Test 6: Show what PATH A vs PATH B would look like
    print(f"\n🚦 Workflow Path Analysis:")
    if exists_after and loaded_data and loaded_data.get('cases'):
        print(f"   PATH A: RESUME - Found existing log with {len(loaded_data['cases'])} cases")
        print(f"   → Would skip discover_court_dockets() and use existing data")
        print(f"   → Would route to Query workflow for processing")
    else:
        print(f"   PATH B: NEW - No existing log or empty log")
        print(f"   → Would call discover_court_dockets() to generate new report")
        print(f"   → Would route to Report workflow for generation")
    
    # Cleanup
    print(f"\n🧹 Cleaning up test file: {log_path}")
    try:
        os.remove(log_path)
        print("✅ Test file removed")
    except:
        print("⚠️ Could not remove test file")
    
    print(f"\n✅ CourtProcessor docket_report_log check test completed!")


if __name__ == "__main__":
    asyncio.run(test_court_processor_docket_log_check())