#!/usr/bin/env python3
"""
Nuclear Lexgenius Prevention Activator

This script can be imported or run to immediately activate nuclear prevention
of lexgenius.log file creation. Use this at the start of any script or process
where you want to ensure NO lexgenius.log files are created.

Usage:
    1. Import to activate: from scripts.activate_nuclear_prevention import activate
    2. Run directly: python scripts/activate_nuclear_prevention.py
    3. Use as module: python -m scripts.activate_nuclear_prevention
"""

import os
import sys

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


def activate(base_path: str = None, verbose: bool = True) -> dict:
    """
    Activate nuclear prevention of lexgenius.log creation.
    
    Args:
        base_path: Base path to monitor (defaults to current directory)
        verbose: Whether to print status messages
        
    Returns:
        Dict with prevention results
    """
    try:
        from pacer.utils.court_logger import activate_nuclear_lexgenius_prevention
        
        if base_path is None:
            base_path = os.getcwd()
        
        result = activate_nuclear_lexgenius_prevention(base_path)
        
        if verbose:
            print("🚨 NUCLEAR LEXGENIUS PREVENTION ACTIVATED 🚨")
            print(f"Removed {len(result['removed_handlers'])} handlers")
            print(f"Deleted {len(result['deleted_files'])} log files")
            print(f"Status: {result['status']}")
            
            if result['deleted_files']:
                print("Deleted files:")
                for file in result['deleted_files']:
                    print(f"  - {file}")
        
        return result
        
    except ImportError as e:
        if verbose:
            print(f"❌ Failed to import nuclear prevention: {e}")
            print("Make sure you're running from the correct directory")
        return {"status": "import_failed", "error": str(e)}
    except Exception as e:
        if verbose:
            print(f"❌ Failed to activate nuclear prevention: {e}")
        return {"status": "activation_failed", "error": str(e)}


def start_continuous_monitoring(base_path: str = None, interval: int = 5, verbose: bool = True):
    """
    Start continuous monitoring and deletion of lexgenius.log files.
    
    Args:
        base_path: Base path to monitor (defaults to current directory)
        interval: Check interval in seconds (default: 5)
        verbose: Whether to print status messages
    """
    try:
        from pacer.utils.court_logger import CourtLogger
        
        if base_path is None:
            base_path = os.getcwd()
        
        thread = CourtLogger.continuously_delete_lexgenius_logs(base_path, interval)
        
        if verbose:
            print(f"🔍 Started continuous monitoring (interval: {interval}s)")
            print(f"Monitoring path: {base_path}")
        
        return thread
        
    except ImportError as e:
        if verbose:
            print(f"❌ Failed to import continuous monitoring: {e}")
        return None
    except Exception as e:
        if verbose:
            print(f"❌ Failed to start continuous monitoring: {e}")
        return None


def emergency_cleanup(base_path: str = None, verbose: bool = True) -> list:
    """
    Emergency cleanup of all lexgenius.log files.
    
    Args:
        base_path: Base path to search (defaults to current directory)
        verbose: Whether to print status messages
        
    Returns:
        List of deleted files
    """
    try:
        from pacer.utils.court_logger import emergency_delete_all_lexgenius_logs
        
        if base_path is None:
            base_path = os.getcwd()
        
        deleted_files = emergency_delete_all_lexgenius_logs(base_path)
        
        if verbose:
            if deleted_files:
                print(f"🗑️ Emergency cleanup deleted {len(deleted_files)} files:")
                for file in deleted_files:
                    print(f"  - {file}")
            else:
                print("✅ No lexgenius.log files found to delete")
        
        return deleted_files
        
    except ImportError as e:
        if verbose:
            print(f"❌ Failed to import emergency cleanup: {e}")
        return []
    except Exception as e:
        if verbose:
            print(f"❌ Failed to run emergency cleanup: {e}")
        return []


def main():
    """Main function when run as script."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Nuclear Lexgenius Prevention Activator",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/activate_nuclear_prevention.py
  python scripts/activate_nuclear_prevention.py --path /custom/path
  python scripts/activate_nuclear_prevention.py --emergency-only
  python scripts/activate_nuclear_prevention.py --monitor --interval 10
        """
    )
    
    parser.add_argument(
        "--path", "-p",
        help="Base path to monitor (default: current directory)",
        default=None
    )
    
    parser.add_argument(
        "--emergency-only", "-e",
        action="store_true",
        help="Only run emergency cleanup, don't activate prevention"
    )
    
    parser.add_argument(
        "--monitor", "-m",
        action="store_true",
        help="Start continuous monitoring"
    )
    
    parser.add_argument(
        "--interval", "-i",
        type=int,
        default=5,
        help="Monitoring interval in seconds (default: 5)"
    )
    
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Suppress verbose output"
    )
    
    args = parser.parse_args()
    verbose = not args.quiet
    
    if args.emergency_only:
        # Only run emergency cleanup
        deleted_files = emergency_cleanup(args.path, verbose)
        sys.exit(0 if deleted_files is not None else 1)
    
    # Run full activation
    result = activate(args.path, verbose)
    
    if result.get("status") not in ["nuclear_prevention_active"]:
        if verbose:
            print("❌ Nuclear prevention activation failed")
        sys.exit(1)
    
    # Start continuous monitoring if requested
    if args.monitor:
        thread = start_continuous_monitoring(args.path, args.interval, verbose)
        
        if thread and verbose:
            print("Press Ctrl+C to stop monitoring...")
            try:
                # Keep the main thread alive
                import time
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 Monitoring stopped")
    
    if verbose:
        print("✅ Nuclear prevention setup complete")


# Auto-activate when imported (unless NO_AUTO_ACTIVATE is set)
if __name__ != "__main__" and not os.environ.get("NO_AUTO_ACTIVATE"):
    # Auto-activate with minimal output when imported
    try:
        activate(verbose=False)
    except Exception:
        pass  # Silently fail on import to avoid breaking other imports


if __name__ == "__main__":
    main()