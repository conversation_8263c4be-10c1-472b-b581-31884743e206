#!/usr/bin/env python3
"""
Test script to verify court logger context detection in core.py
"""

import sys
import os
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

def test_pacer_context_detection():
    """Test PACER context detection with simulated PACER operation."""
    from src.containers.core import _detect_pacer_context, _create_dual_logger_wrapper
    
    print("Testing PACER context detection...")
    
    # Simulate variables that would exist in a PACER operation
    court_id = "cand"  # This should be detected
    iso_date = "2025-01-10"  # This should be detected
    
    # Test context detection
    context = _detect_pacer_context()
    print(f"Detected context: {context}")
    
    # Test logger creation with explicit parameters
    config = {
        "DATA_DIR": "data",
        "log_dir": "logs"
    }
    
    print("\nTesting logger creation with court context...")
    try:
        logger_wrapper = _create_dual_logger_wrapper(config, court_id=court_id, iso_date=iso_date)
        print(f"Successfully created logger wrapper: {type(logger_wrapper)}")
        
        # Test logging
        logger_wrapper.info(f"Test log message for court {court_id}")
        print("✅ Court-specific logging test successful")
        
    except Exception as e:
        print(f"❌ Error creating court logger: {e}")
        
    # Test logger creation without court context (should create general logger)
    print("\nTesting logger creation without court context...")
    try:
        general_logger_wrapper = _create_dual_logger_wrapper(config)
        print(f"Successfully created general logger wrapper: {type(general_logger_wrapper)}")
        
        # Test logging
        general_logger_wrapper.info("Test log message for general operations")
        print("✅ General logging test successful")
        
    except Exception as e:
        print(f"❌ Error creating general logger: {e}")

def simulate_pacer_operation():
    """Simulate a PACER operation to test context detection."""
    from src.containers.core import _create_dual_logger_wrapper
    
    print("\n" + "="*60)
    print("SIMULATING PACER OPERATION")
    print("="*60)
    
    # These variables simulate what would exist in a real PACER operation
    court_id = "nysd"
    iso_date = datetime.now().strftime('%Y-%m-%d')
    
    config = {
        "DATA_DIR": os.path.join(os.getcwd(), "data"),
        "log_dir": None
    }
    
    print(f"Court ID: {court_id}")
    print(f"ISO Date: {iso_date}")
    
    try:
        # This should auto-detect the PACER context and create court-specific logger
        logger_wrapper = _create_dual_logger_wrapper(config)
        print(f"Logger created: {type(logger_wrapper)}")
        
        # Test various log levels
        logger_wrapper.info(f"Starting processing for court {court_id}")
        logger_wrapper.debug(f"Debug information for {court_id}")
        logger_wrapper.warning(f"Warning message for {court_id}")
        
        print("✅ PACER operation simulation successful")
        
        # Expected log file path
        expected_path = f"data/{iso_date}/logs/pacer/{court_id}.log"
        print(f"Expected log file: {expected_path}")
        
        if os.path.exists(expected_path):
            print("✅ Court-specific log file created successfully")
            with open(expected_path, 'r') as f:
                content = f.read()
                print(f"Log file content preview:\n{content[:200]}...")
        else:
            print("ℹ️  Court-specific log file not found (may be using fallback)")
            
    except Exception as e:
        print(f"❌ Error in PACER simulation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Court Logger Context Detection Test")
    print("="*50)
    
    test_pacer_context_detection()
    simulate_pacer_operation()
    
    print("\n" + "="*50)
    print("Test completed!")