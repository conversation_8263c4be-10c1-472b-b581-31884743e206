#!/usr/bin/env bash

# This script re-generates the lock files (requirements.txt) from pyproject.toml.
# Run this script *after* you have manually added or removed a dependency
# in the pyproject.toml file.

set -e # Exit immediately if a command fails

# Get the project root directory
PROJECT_ROOT="$( cd "$( dirname "${BASH_SOURCE[0]}" )/.." &> /dev/null && pwd )"

echo "🔒 Locking dependencies from pyproject.toml..."
cd "$PROJECT_ROOT"

# 1. Compile production dependencies
echo "   -> Compiling requirements.txt..."
uv pip compile pyproject.toml -o requirements.txt

# 2. Compile development dependencies
echo "   -> Compiling requirements-dev.txt..."
uv pip compile pyproject.toml --extra dev -o requirements-dev.txt

echo "✅ Lock files have been updated."
echo ""

# 3. Sync the local environment with the new lock files
"$PROJECT_ROOT/scripts/sync.sh"