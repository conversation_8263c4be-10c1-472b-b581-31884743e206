#!/usr/bin/env python3
"""
Quick runner script to validate the navigation fix is working correctly.

This script tests:
1. When docket_report_log EXISTS -> Goes to Query page
2. When docket_report_log DOES NOT EXIST -> Goes to Reports page
3. Proper parameter passing for resume_mode

Usage:
    python scripts/run_navigation_fix_test.py
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/Users/<USER>/PycharmProjects/lexgenius')

try:
    from tests.test_navigation_fix_validation import TestNavigationFixValidation
    
    async def run_validation():
        """Run the navigation fix validation test."""
        print("🔍 NAVIGATION FIX VALIDATION")
        print("-" * 50)
        
        test_instance = TestNavigationFixValidation()
        test_instance.setUp()
        
        try:
            await test_instance.run_all_tests()
            print("\n🎉 Navigation fix validation completed successfully!")
            return True
        except Exception as e:
            print(f"\n❌ Navigation fix validation failed: {e}")
            return False
        finally:
            test_instance.tearDown()
    
    if __name__ == "__main__":
        success = asyncio.run(run_validation())
        sys.exit(0 if success else 1)
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    sys.exit(1)