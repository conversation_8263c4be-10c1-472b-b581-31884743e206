#!/usr/bin/env python3
"""
Consolidated AFFF Analysis Script

This script combines the functionality of:
- analyze_afff_filings.py: CSV filing analysis with date filtering
- analyze_mdl2873_filings.py: Daily filing categorization by type
- calculate_afff_num_plaintiffs.py: DynamoDB plaintiff calculations

Features:
1. Show total filings from 01/01/25 (CSV analysis)
2. Show last 30 days of filings from current date (CSV analysis)
3. Show filings by day for last 7 days from current date (CSV analysis) 
4. Show filings by type for a given date (JSON analysis)
5. Calculate total plaintiffs for date range (DynamoDB analysis)
6. View raw vs filtered plaintiff counts (DynamoDB analysis)

Usage:
    python src/scripts/generate_afff_stats.py --date 20250608
    python src/scripts/generate_afff_stats.py --date 20250608 --local --port 8000
"""

import os
import sys
import json
import argparse
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from rich.console import Console
    from rich.table import Table
    from rich.prompt import Prompt
    from rich.panel import Panel
    from src.repositories.pacer_repository import PacerRepository
    from src.pacer.query_service import PacerQueryService
    from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
except ImportError as e:
    print(f"Error importing necessary libraries: {e}")
    sys.exit(1)

# Set project root - go up 3 levels from this script
# scripts/analysis/AFFF/generate_afff_stats.py -> lexgenius/
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

console = Console()
MDL_NUM = '2873'  # AFFF MDL Number

class AfffAnalyzer:
    def __init__(self, current_date: str):
        self.csv_data = None
        self.pacer_query_service = None
        self.storage = None
        self.current_date = current_date
        self.current_date_obj = datetime.strptime(current_date, '%Y%m%d')
        
    def load_csv_data(self) -> bool:
        """Load CSV filing data."""
        # CSV file is in the same directory as this script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(script_dir, "AFFF-Filings-Case-Shell.csv")
        
        try:
            console.print("[yellow]Loading CSV file...[/yellow]")
            
            # Read CSV without parsing dates first
            df = pd.read_csv(file_path)
            
            # Filter out voluntary dismissals (case insensitive) - check Docket Text column
            mask = True
            if 'Docket Text' in df.columns:
                mask = ~df['Docket Text'].astype(str).str.contains('voluntary dismissal', case=False, na=False)
            
            original_count = len(df)
            df = df[mask]
            filtered_count = original_count - len(df)
            
            if filtered_count > 0:
                console.print(f"[yellow]Filtered out {filtered_count} voluntary dismissal entries[/yellow]")
            
            # Store original dates and parse
            df['Original_Date'] = df['Date']
            df['Date'] = pd.to_datetime(df['Date'], format='%m/%d/%Y', errors='coerce')
            
            # Check for parsing issues
            null_dates = df[df['Date'].isnull()]
            if not null_dates.empty:
                console.print(f"[red]Warning: {len(null_dates)} rows with unparseable dates[/red]")
            
            # Drop debug column
            df = df.drop('Original_Date', axis=1)
            
            self.csv_data = df
            console.print(f"[green]Successfully loaded {len(df)} CSV records[/green]")
            return True
            
        except Exception as e:
            console.print(f"[red]Error loading CSV file: {str(e)}[/red]")
            return False
    
    async def init_pacer_service(self, use_local: bool = False, local_port: int = 8000) -> bool:
        """Initialize PACER query service for DynamoDB queries."""
        try:
            # Create storage configuration object with proper attributes
            class StorageConfig:
                def __init__(self, use_local, local_port):
                    self.use_local = use_local
                    self.local_port = local_port
                    self.dynamodb_endpoint = f'http://localhost:{local_port}' if use_local else None
                    self.aws_region = 'us-west-2'
                    self.dynamodb_max_retries = 10
                    self.dynamodb_base_delay = 1.0
                    self.dynamodb_max_delay = 60.0
            
            storage_config = StorageConfig(use_local, local_port)
            
            # Initialize async storage
            self.storage = AsyncDynamoDBStorage(storage_config)
            
            # Initialize repository and service
            repository = PacerRepository(self.storage)
            self.pacer_query_service = PacerQueryService(repository)
            
            console.print("[green]PACER Query Service initialized successfully.[/green]")
            return True
        except Exception as e:
            console.print(f"[red]Error initializing PACER Query Service: {e}[/red]")
            return False
    
    def show_csv_summary(self):
        """Show total filings from CSV data."""
        if self.csv_data is None:
            console.print("[red]CSV data not loaded[/red]")
            return
        
        # Filter from 01/01/25
        start_date = pd.to_datetime('2025-01-01')
        recent_data = self.csv_data[self.csv_data['Date'] >= start_date]
        
        table = Table(title="AFFF Filings Summary (from 01/01/25)")
        table.add_column("Metric", style="cyan")
        table.add_column("Count", justify="right", style="green")
        
        table.add_row("Total Filings Since 01/01/25", str(len(recent_data)))
        table.add_row("Total Filings Overall", str(len(self.csv_data)))
        
        if len(recent_data) > 0:
            table.add_row("Date Range", f"{recent_data['Date'].min().strftime('%Y-%m-%d')} to {recent_data['Date'].max().strftime('%Y-%m-%d')}")
        
        console.print(table)
    
    def show_last_30_days(self):
        """Show filings for last 30 days from current date."""
        if self.csv_data is None:
            console.print("[red]CSV data not loaded[/red]")
            return
        
        end_date = pd.to_datetime(self.current_date_obj)
        start_date = end_date - timedelta(days=30)
        
        filtered_data = self.csv_data[
            (self.csv_data['Date'] >= start_date) & 
            (self.csv_data['Date'] <= end_date)
        ]
        
        self._display_filings_table(filtered_data, f"Last 30 Days ({start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')})")
    
    def show_last_7_days_by_day(self):
        """Show filings by day for last 7 days from current date."""
        if self.csv_data is None:
            console.print("[red]CSV data not loaded[/red]")
            return
        
        end_date = pd.to_datetime(self.current_date_obj)
        start_date = end_date - timedelta(days=7)
        
        filtered_data = self.csv_data[
            (self.csv_data['Date'] >= start_date) & 
            (self.csv_data['Date'] <= end_date)
        ]
        
        # Group by date
        daily_counts = filtered_data.groupby(filtered_data['Date'].dt.date).size()
        
        table = Table(title="Daily Filings - Last 7 Days")
        table.add_column("Date", style="cyan")
        table.add_column("Filings", justify="right", style="green")
        
        total = 0
        for filing_date, count in daily_counts.items():
            table.add_row(filing_date.strftime("%Y-%m-%d"), str(count))
            total += count
        
        table.add_row("", "")  # Separator
        table.add_row("Total", str(total), style="bold")
        
        console.print(table)
    
    def analyze_daily_filings(self, date_str: str):
        """Analyze MDL 2873 filings by type for a specific date."""
        base_path = os.path.join(PROJECT_ROOT, "data", date_str, "dockets")
        
        if not os.path.exists(base_path):
            console.print(f"[red]Error: Directory not found: {base_path}[/red]")
            return
        
        # Initialize counters
        transferred = {"cases": 0, "plaintiffs": 0}
        pending_cto = {"cases": 0, "plaintiffs": 0}
        direct = {"cases": 0, "plaintiffs": 0}
        
        # Process JSON files
        for filename in os.listdir(base_path):
            if not filename.endswith('.json'):
                continue
            
            file_path = os.path.join(base_path, filename)
            
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                if data.get('mdl_num') != '2873':
                    continue
                
                num_plaintiffs = int(data.get('num_plaintiffs', 1))
                
                if data.get('transferred_in'):
                    transferred["cases"] += 1
                    transferred["plaintiffs"] += num_plaintiffs
                elif data.get('pending_cto'):
                    pending_cto["cases"] += 1
                    pending_cto["plaintiffs"] += num_plaintiffs
                else:
                    direct["cases"] += 1
                    direct["plaintiffs"] += num_plaintiffs
                    
            except Exception as e:
                console.print(f"[red]Error processing {filename}: {str(e)}[/red]")
        
        # Display results
        table = Table(
            title=f"MDL 2873 Filing Analysis for {date_str}",
            show_header=True,
            header_style="bold magenta"
        )
        
        table.add_column("Category", style="cyan")
        table.add_column("Cases", justify="right", style="green")
        table.add_column("Total Plaintiffs", justify="right", style="yellow")
        
        table.add_row("Transferred In", str(transferred["cases"]), str(transferred["plaintiffs"]))
        table.add_row("Pending CTO", str(pending_cto["cases"]), str(pending_cto["plaintiffs"]))
        table.add_row("Direct Filings", str(direct["cases"]), str(direct["plaintiffs"]))
        table.add_row(
            "Total",
            str(transferred["cases"] + pending_cto["cases"] + direct["cases"]),
            str(transferred["plaintiffs"] + pending_cto["plaintiffs"] + direct["plaintiffs"])
        )
        
        console.print(table)
    
    async def calculate_plaintiffs_for_range(self, start_date: str, end_date: str, use_local: bool = False):
        """Calculate total plaintiffs for date range from DynamoDB."""
        if self.pacer_query_service is None:
            if not await self.init_pacer_service(use_local):
                return
        
        try:
            async with self.storage:
                console.print(f"Fetching AFFF ({MDL_NUM}) dockets from {start_date} to {end_date}...")
                raw_items = await self.pacer_query_service.get_mdl_dockets_by_date_range(MDL_NUM, start_date, end_date)
                console.print(f"[green]Fetched {len(raw_items)} raw items.[/green]")
                
                if not raw_items:
                    console.print("[yellow]No dockets found for the specified criteria.[/yellow]")
                    return
                
                # Process dockets
                initial_items, filtered_items, dropped_items = self._process_dockets(raw_items)
                
                # Display summary
                table = Table(title=f"Plaintiff Analysis for {start_date} to {end_date}")
                table.add_column("Category", style="cyan")
                table.add_column("Cases", justify="right", style="green")
                table.add_column("Total Plaintiffs", justify="right", style="yellow")
                
                initial_plaintiffs = sum(item.get('num_plaintiffs_int', 0) for item in initial_items)
                filtered_plaintiffs = sum(item.get('num_plaintiffs_int', 0) for item in filtered_items)
                dropped_plaintiffs = sum(item.get('num_plaintiffs_int', 0) for item in dropped_items)
                
                table.add_row("Raw DB Count", str(len(initial_items)), str(initial_plaintiffs))
                table.add_row("Filtered (Adjusted)", str(len(filtered_items)), str(filtered_plaintiffs))
                table.add_row("Dropped (Transfers)", str(len(dropped_items)), str(dropped_plaintiffs))
                
                console.print(table)
                
                # Ask if user wants to see detailed breakdown
                if Prompt.ask("Show detailed breakdown?", choices=["y", "n"], default="n") == "y":
                    self._show_detailed_plaintiff_breakdown(initial_items, filtered_items, dropped_items)
                    
        except Exception as e:
            console.print(f"[red]Error fetching/processing dockets: {e}[/red]")
    
    def _process_dockets(self, items: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Process dockets to handle transfers and normalize plaintiff counts."""
        initial_items_processed = []
        potential_transfers = {}
        original_dockets = {}
        dropped_indices = set()
        dropped_items = []
        
        # First pass: Process TransferredIn, normalize NumPlaintiffs
        for index, item in enumerate(items):
            processed_item = item.copy()
            
            # Convert transferred_in
            transferred_in_val = processed_item.get('transferred_in')
            processed_item['transferred_in'] = bool(transferred_in_val and str(transferred_in_val) == '1')
            
            # Normalize num_plaintiffs
            num_plaintiffs = processed_item.get('num_plaintiffs')
            if num_plaintiffs is None or str(num_plaintiffs) == '0':
                processed_item['num_plaintiffs'] = '1'
            
            # Calculate integer version
            try:
                # Handle Decimal type from DynamoDB
                num_p = processed_item.get('num_plaintiffs', 1)
                if hasattr(num_p, '__int__'):  # Handles Decimal and similar types
                    processed_item['num_plaintiffs_int'] = int(num_p)
                else:
                    processed_item['num_plaintiffs_int'] = int(num_p)
            except (ValueError, TypeError):
                processed_item['num_plaintiffs_int'] = 1
            
            initial_items_processed.append(processed_item)
            
            # Index for transfer detection
            transferor_court_id = processed_item.get('transferor_court_id')
            transferor_docket_num = processed_item.get('transferor_docket_num')
            court_id = processed_item.get('court_id')
            docket_num = processed_item.get('docket_num')
            
            if transferor_court_id and transferor_docket_num:
                potential_transfers[(transferor_court_id, transferor_docket_num)] = index
            
            if court_id and docket_num:
                original_dockets[(court_id, docket_num)] = index
        
        # Second pass: Identify transfers to drop
        for transfer_key, transfer_index in potential_transfers.items():
            if transfer_key in original_dockets:
                original_index = original_dockets[transfer_key]
                if original_index not in dropped_indices:
                    dropped_indices.add(original_index)
                    dropped_items.append(initial_items_processed[original_index])
        
        # Third pass: Create filtered list
        filtered_items = []
        for index, item in enumerate(initial_items_processed):
            if index not in dropped_indices:
                filtered_items.append(item)
        
        return initial_items_processed, filtered_items, dropped_items
    
    def _show_detailed_plaintiff_breakdown(self, initial_items, filtered_items, dropped_items):
        """Show detailed breakdown of plaintiff calculations."""
        display_cols = [
            "filing_date", "docket_num", "court_id", "num_plaintiffs", "transferred_in",
            "transferor_court_id", "transferor_docket_num", "plaintiff", "defendant"
        ]
        
        while True:
            console.print("\n[bold cyan]Detailed Breakdown Options:[/bold cyan]")
            console.print("1. View raw items (initial DB fetch)")
            console.print("2. View filtered items (transfers removed)")
            console.print("3. View dropped items (identified transfers)")
            console.print("4. Back to main menu")
            
            choice = Prompt.ask("Enter choice", choices=["1", "2", "3", "4"], default="4")
            
            if choice == '1':
                self._display_items_table("Raw Items", initial_items, display_cols)
            elif choice == '2':
                self._display_items_table("Filtered Items", filtered_items, display_cols)
            elif choice == '3':
                self._display_items_table("Dropped Items", dropped_items, display_cols)
            else:
                break
    
    def _display_items_table(self, title: str, items: List[Dict[str, Any]], columns: List[str]):
        """Display items in a formatted table."""
        if not items:
            console.print(f"[yellow]No items to display for '{title}'[/yellow]")
            return
        
        table = Table(title=f"{title} ({len(items)})", show_header=True, header_style="bold magenta")
        
        for col in columns:
            table.add_column(col)
        
        for item in items:
            row_data = []
            for col in columns:
                value = item.get(col)
                if isinstance(value, bool):
                    row_data.append("[green]True[/green]" if value else "[red]False[/red]")
                elif value is None:
                    row_data.append("[dim]None[/dim]")
                else:
                    row_data.append(str(value))
            table.add_row(*row_data)
        
        console.print(table)
        
        # Show plaintiff sum
        total_plaintiffs = sum(item.get('num_plaintiffs_int', 0) for item in items)
        console.print(f"\n[bold green]Total Plaintiffs ({title}): {total_plaintiffs}[/bold green]\n")
    
    def _display_filings_table(self, df: pd.DataFrame, title: str):
        """Display filings in a table format."""
        if df.empty:
            console.print(f"[yellow]No filings found for {title}[/yellow]")
            return
        
        # Group by date
        daily_counts = df.groupby(df['Date'].dt.date).size()
        
        table = Table(title=title)
        table.add_column("Filing Date", style="cyan")
        table.add_column("Number of Filings", justify="right", style="green")
        
        total_filings = 0
        for filing_date, count in daily_counts.items():
            table.add_row(filing_date.strftime("%Y-%m-%d"), str(count))
            total_filings += count
        
        console.print(table)
        console.print(Panel.fit(
            f"[bold blue]Total Filings: {total_filings}",
            title="Summary",
            border_style="blue"
        ))

def validate_date(date_str: str) -> bool:
    """Validate date string format."""
    try:
        datetime.strptime(date_str, '%Y%m%d')
        return True
    except ValueError:
        return False

def format_date(date_str: str) -> Optional[str]:
    """Validate and format date string."""
    try:
        return datetime.strptime(date_str, '%Y%m%d').strftime('%Y%m%d')
    except ValueError:
        return None

def main():
    parser = argparse.ArgumentParser(description="Consolidated AFFF Analysis Tool")
    parser.add_argument("--date", required=True, help="Current date in YYYYMMDD format")
    parser.add_argument("--local", action="store_true", help="Use local DynamoDB instance")
    parser.add_argument("--port", type=int, default=8000, help="Port for local DynamoDB (default: 8000)")
    parser.add_argument("--test", action="store_true", help="Run test option 1 and exit")
    args = parser.parse_args()
    
    # Validate the date parameter
    current_date = format_date(args.date)
    if not current_date:
        console.print(f"[red]Error: Invalid date format '{args.date}'. Please use YYYYMMDD format.[/red]")
        sys.exit(1)
    
    analyzer = AfffAnalyzer(current_date)
    
    # Load CSV data at startup
    csv_loaded = analyzer.load_csv_data()
    
    # Test mode - run option 1 and exit
    if args.test:
        if csv_loaded:
            analyzer.show_csv_summary()
        else:
            console.print("[red]CSV data not available[/red]")
        return
    
    while True:
        console.print(f"\n[bold cyan]AFFF Consolidated Analysis Tool[/bold cyan]")
        console.print(f"[dim]Current Date: {current_date} ({analyzer.current_date_obj.strftime('%Y-%m-%d')})[/dim]")
        console.print("\n[yellow]CSV-based Analysis:[/yellow]")
        console.print("1. Show total filings from 01/01/25")
        console.print("2. Show last 30 days of filings")
        console.print("3. Show filings by day for last 7 days")
        
        console.print("\n[yellow]JSON-based Analysis:[/yellow]")
        console.print("4. Analyze filings by type for specific date")
        
        console.print("\n[yellow]DynamoDB Analysis:[/yellow]")
        console.print("5. Calculate plaintiffs for date range")
        
        console.print("\n[yellow]Utilities:[/yellow]")
        console.print("6. Reload CSV data")
        console.print("q. Quit")
        
        choice = Prompt.ask("Select option", choices=["1", "2", "3", "4", "5", "6", "q"], default="q")
        
        if choice == "1":
            if csv_loaded:
                analyzer.show_csv_summary()
            else:
                console.print("[red]CSV data not available[/red]")
        
        elif choice == "2":
            if csv_loaded:
                analyzer.show_last_30_days()
            else:
                console.print("[red]CSV data not available[/red]")
        
        elif choice == "3":
            if csv_loaded:
                analyzer.show_last_7_days_by_day()
            else:
                console.print("[red]CSV data not available[/red]")
        
        elif choice == "4":
            date_str = Prompt.ask("Enter date (YYYYMMDD)")
            if validate_date(date_str):
                analyzer.analyze_daily_filings(date_str)
            else:
                console.print("[red]Invalid date format. Use YYYYMMDD.[/red]")
        
        elif choice == "5":
            start_date = Prompt.ask("Enter start date (YYYYMMDD)")
            end_date = Prompt.ask("Enter end date (YYYYMMDD)", default=current_date)
            
            start_date_fmt = format_date(start_date)
            end_date_fmt = format_date(end_date)
            
            if not start_date_fmt or not end_date_fmt:
                console.print("[red]Invalid date format. Use YYYYMMDD.[/red]")
            elif start_date_fmt > end_date_fmt:
                console.print("[red]Start date cannot be after end date.[/red]")
            else:
                import asyncio
                asyncio.run(analyzer.calculate_plaintiffs_for_range(start_date_fmt, end_date_fmt, args.local))
        
        elif choice == "6":
            csv_loaded = analyzer.load_csv_data()
        
        else:
            console.print("[bold blue]Goodbye![/bold blue]")
            break

if __name__ == "__main__":
    main()