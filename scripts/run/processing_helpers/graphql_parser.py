#!/usr/bin/env python3
from __future__ import annotations

import warnings
warnings.filterwarnings("ignore", category=RuntimeWarning)

import argparse
import asyncio
import json
import logging
import os
import random
import re
import sys
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from dependency_injector import containers, providers

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Third-party imports
from bs4 import BeautifulSoup

# Rich library for better terminal output
try:
    from rich.console import Console
    from rich.logging import RichHandler
    from rich import print as rich_print

    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    RichHandler = logging.StreamHandler
    rich_print = print
    Console = None

# --- Import Shared Components ---
# These MUST be available in the environment.
try:
    from src.config_models.loader import load_config
    from src.services.fb_ads.session_manager import FacebookSessionManager
    from src.infrastructure.storage.s3_async import S3AsyncStorage
    # Force use of legacy adapter approach - the new repos don't work properly
    _using_new_architecture = False
    try:
        from src.infrastructure.storage.dynamodb_legacy_adapter import DynamoDbBaseManager
        # Create manager classes dynamically
        class FBAdArchiveManager(DynamoDbBaseManager):
            DEFAULT_TABLE_NAME = 'FBAdArchive'
            
            def batch_insert_items(self, records: List[Dict[str, Any]], batch_size: int = 25, disable_progress: bool = False) -> Tuple[int, int]:
                """Override to convert snake_case to PascalCase for FBAdArchive table"""
                # Convert all records from snake_case to PascalCase
                converted_records = []
                for record in records:
                    converted = self.snake_or_camel_to_pascal_case(record)
                    # Special handling for specific fields
                    if 'ad_archive_id' in record:
                        converted['AdArchiveID'] = record['ad_archive_id']
                    if 'page_id' in record:
                        converted['PageID'] = record['page_id']
                    if 'start_date' in record:
                        converted['StartDate'] = record['start_date']
                    if 'end_date' in record:
                        converted['EndDate'] = record.get('end_date')
                    if 'last_updated' in record:
                        converted['LastUpdated'] = record.get('last_updated')
                    if 'is_active' in record:
                        converted['IsActive'] = record.get('is_active', False)
                    if 'law_firm' in record:
                        converted['LawFirm'] = record.get('law_firm')
                    if 's3_image_key' in record:
                        converted['S3ImageKey'] = record.get('s3_image_key')
                    if 'ImageText' in record:
                        converted['ImageText'] = record.get('ImageText')
                    if 'Summary' in record:
                        converted['Summary'] = record.get('Summary')
                    if 'IsForbidden403' in record:
                        converted['IsForbidden403'] = record.get('IsForbidden403', False)
                    converted_records.append(converted)
                
                # Call parent method with converted records
                return super().batch_insert_items(converted_records, batch_size, disable_progress)
        class LawFirmsManager(DynamoDbBaseManager):
            DEFAULT_TABLE_NAME = 'LawFirms'
            
            def get_record(self, key_value: str) -> Optional[Dict[str, Any]]:
                """Get record by ID (primary key)"""
                # LawFirms table has composite primary key: ID and Name
                # For single key lookup, we need to scan
                from boto3.dynamodb.conditions import Attr
                try:
                    results = list(self.scan(filter_expression=Attr('ID').eq(key_value), limit=1))
                    return results[0] if results else None
                except Exception as e:
                    self.logger.error(f"Error getting record by ID {key_value}: {e}")
                    return None
    except ImportError as e:
        raise ImportError(f"Could not import legacy manager implementation: {e}")
    from src.services.fb_ads import ImageHandler
    from src.utils.pdf_utils import PDFExtractor
    # AI interfaces - try various import paths
    GPT4 = None
    try:
        from src.lib.gpt4_interface import GPT4
    except ImportError:
        try:
            from src.infrastructure.external.gpt4_client import GPT4Client as GPT4
        except ImportError:
            pass
    
    DeepSeek = None
    try:
        from src.lib.deepseek_interface import DeepSeek
    except ImportError:
        try:
            from src.infrastructure.external.deepseek_client import DeepSeekClient as DeepSeek
        except ImportError:
            try:
                from src.services.ai.deepseek_service import DeepSeekService as DeepSeek
            except ImportError:
                pass
    
    LlavaImageExtractor = None
    try:
        from src.lib.llava_vision import LlavaImageExtractor
    except ImportError:
        try:
            from src.infrastructure.external.llava_client import LlavaClient as LlavaImageExtractor
        except ImportError:
            pass
    
    # from src.fb_ads import VectorClusterer  # Deprecated - campaign field tagging disabled
    VectorClusterer = None
    from src.services.ai.ai_orchestrator import AIOrchestrator
    from src.services.fb_ads import AdProcessor
except ImportError as e:
    # Use print as logger might not be configured yet
    print(
        f"[CRITICAL ERROR] Could not import required library components: {e}. Ensure PYTHONPATH is correct and dependencies are installed. Exiting.")
    sys.exit(1)  # Exit immediately if core components are missing



def _create_structured_ad(ad_data: dict, law_firm_name: str) -> dict:
    """
    The GraphQL processor already creates properly structured ads.
    Just add the law firm name and ensure required fields exist.
    Follow the same pattern as JobRunnerService.
    """
    try:
        # The ad_data is already properly structured by GraphQLProcessor
        structured_ad = ad_data.copy()
        
        # Set law firm name in both formats to match JobRunnerService pattern
        structured_ad['law_firm'] = law_firm_name  # This is what AI orchestrator expects
        structured_ad['LawFirm'] = law_firm_name   # This is for DB storage
        
        # Ensure required fields exist with defaults (match JobRunnerService pattern)
        structured_ad.setdefault('Summary', None)    # Use None, not empty string
        structured_ad.setdefault('LLM', None)
        structured_ad.setdefault('IsForbidden403', False)
        structured_ad.setdefault('Category', None)
        structured_ad.setdefault('Company', None)
        structured_ad.setdefault('Product', None)
        structured_ad.setdefault('Injuries', None)
        structured_ad.setdefault('LitigationType', None)
        structured_ad.setdefault('LitigationName', None)
        structured_ad.setdefault('MdlName', None)
        structured_ad.setdefault('ImageText', None)
        structured_ad.setdefault('S3ImageKey', None)
        structured_ad.setdefault('s3_image_key', None)  # Both formats
        structured_ad.setdefault('phash', None)
        structured_ad.setdefault('PHash', None)  # Both formats
        structured_ad.setdefault('campaign', 'General')  # Default campaign
        
        return structured_ad
        
    except Exception as e:
        logging.getLogger(__name__).error(f"Failed to structure ad data: {e}")
        return None


class FBAdsDIContainer(containers.DeclarativeContainer):
    """Dependency injection container for FB Ads services."""
    
    # Configuration
    config = providers.Configuration()
    
    # Logger
    main_logger = providers.Singleton(
        logging.getLogger,
        __name__
    )
    
    # Facebook Session Manager
    session_manager = providers.Factory(
        FacebookSessionManager,
        config=config,
        logger=main_logger
    )
    
    # S3 Storage
    s3_manager = providers.Factory(
        S3AsyncStorage,
        logger=main_logger,
        bucket_name=config.s3.bucket_name,
        aws_access_key=config.s3.aws_access_key,
        aws_secret_key=config.s3.aws_secret_key,
        aws_region=config.s3.aws_region
    )
    
    # Image Handler
    image_handler = providers.Factory(
        ImageHandler,
        logger=main_logger,
        config=config,
        s3_manager=s3_manager,
        session_manager=session_manager,
        hash_manager=None,
        bandwidth_logger=None
    )
    
    # AI Services - conditional providers
    deepseek_instance = providers.Factory(
        DeepSeek,
        config=config
    ) if DeepSeek else providers.Object(None)
    
    gpt_instance = providers.Factory(
        GPT4,
        config=config
    ) if GPT4 else providers.Object(None)
    
    llava_instance = providers.Factory(
        LlavaImageExtractor,
        config=config
    ) if LlavaImageExtractor else providers.Object(None)
    
    # AI Orchestrator
    ai_integrator = providers.Factory(
        AIOrchestrator,
        logger=main_logger,
        config=config,
        deepseek=deepseek_instance,
        gpt4=gpt_instance,
        llava=llava_instance,
        s3_storage=s3_manager
    )
    


# --- Minimal GraphQLProcessor Class ---
# (Handles ONLY HTML loading and GraphQL extraction)
class GraphQLProcessor:
    """
    Handles loading HTML content and extracting the raw GraphQL data structure
    containing Facebook ad information. Does NOT handle image processing, AI calls,
    or database interactions directly.
    """

    def __init__(self, company_id: str, config: Dict[str, Any]):
        self.logger = logging.getLogger(f"{__name__}.GraphQLProcessor.{company_id}")
        if not self.logger.handlers:
            handler = RichHandler(show_path=False) if RICH_AVAILABLE else logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter(
                '%(name)s: %(message)s' if RICH_AVAILABLE else '%(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.propagate = False
        log_level_name = config.get('log_level', 'INFO').upper()
        log_level = getattr(logging, log_level_name, logging.INFO)
        self.logger.setLevel(log_level)

        self.logger.debug(f"Initializing minimal GraphQLProcessor for HTML extraction (Company ID: {company_id})")
        self.company_id = company_id
        self.config = config
        self.end_date = config.get('date_str_yyyymmdd', datetime.now().strftime('%Y%m%d'))
        self.html_file = self._construct_file_path()
        self.logger.info(f"Target HTML file path: {self.html_file}")
        self.data = None
        self.logger.debug(f"Minimal GraphQLProcessor initialization complete for {company_id}.")

    def _construct_file_path(self) -> str:
        html_base_dir_cfg = self.config.get('graphql_html_dir')
        if not html_base_dir_cfg:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            html_base_dir_cfg = os.path.abspath(os.path.join(script_dir, '..', 'data', 'fb_ad_archive', 'html'))
            self.logger.warning(
                f"Config key 'graphql_html_dir' not set. Using default relative path: {html_base_dir_cfg}")
        file_path = os.path.join(html_base_dir_cfg, f'{self.company_id}.html')
        self.logger.debug(f"Constructed HTML file path: {file_path}")
        return file_path

    def _load_html_content(self):
        self.logger.info(f"Attempting to load HTML file: {self.html_file}")
        if not os.path.exists(self.html_file):
            self.logger.error(f"HTML file does not exist: {self.html_file}")
            return None
        if not os.path.isfile(self.html_file):
            self.logger.error(f"Path exists but is not a file: {self.html_file}")
            return None
        try:
            with open(self.html_file, 'r', encoding='utf-8') as file:
                content = file.read()
            self.logger.info(f"Successfully loaded HTML file content (Length: {len(content):,}) from: {self.html_file}")
            return content
        except Exception as e:
            self.logger.error(f"Error reading file {self.html_file}: {e}", exc_info=True)
            return None

    def extract_graphql_from_html(self, html_content: str) -> Optional[Dict]:
        self.logger.info("Attempting to extract GraphQL JSON blob from HTML content...")
        if not html_content:
            self.logger.error("Cannot extract GraphQL: HTML content is empty.")
            return None
        start_marker_pattern = r'["\']?ad_library_main["\']?\s*:\s*{'
        match = re.search(start_marker_pattern, html_content)
        if not match:
            self.logger.error(f"Extraction Failed: Start marker pattern '{start_marker_pattern}' not found.")
            self.logger.debug(f"HTML Start Snippet: {html_content[:200]}")
            self.logger.debug(f"HTML End Snippet: {html_content[-200:]}")
            return None
        json_start_index = match.end() - 1
        self.logger.debug(f"Found start marker pattern. JSON should start at index {json_start_index}.")
        depth = 1
        in_quotes = False
        escape_next = False
        end_index = -1
        current_pos = json_start_index + 1
        scan_start_time = time.monotonic()
        self.logger.debug("Scanning HTML content to find JSON object boundaries...")
        while current_pos < len(html_content):
            char = html_content[current_pos]
            if escape_next:
                escape_next = False
            elif char == '\\':
                escape_next = True
            elif char == '"':
                in_quotes = not in_quotes
            if not in_quotes:
                if char == '{':
                    depth += 1
                elif char == '}':
                    depth -= 1
                    if depth == 0:
                        end_index = current_pos
                        self.logger.debug(f"Found matching closing brace '}}' at index {end_index} (Depth became 0).")
                        break
            current_pos += 1
        scan_duration = time.monotonic() - scan_start_time
        self.logger.debug(
            f"Brace balancing scan finished in {scan_duration:.3f}s. Final Depth: {depth}, Stopped at index: {current_pos}")
        if end_index == -1:
            self.logger.error(
                "Extraction Failed: Could not find matching closing brace '}}'. JSON might be truncated or malformed.")
            context_start = max(0, current_pos - 150)
            context_end = min(len(html_content), current_pos + 150)
            self.logger.debug(
                f"Scan stopped near index {current_pos}. Depth: {depth}. Context: ...{html_content[context_start:context_end]}...")
            return None
        json_str = html_content[json_start_index: end_index + 1]
        self.logger.info(
            f"Successfully extracted potential JSON string slice (Indices: {json_start_index}-{end_index}, Length: {len(json_str):,}).")
        self.logger.debug(f"Extracted JSON string snippet: {json_str[:150]}...{json_str[-150:]}")
        try:
            processed_json_str = re.sub(r',\s*([}\]])', r'\1', json_str)
            if processed_json_str != json_str:
                self.logger.debug("Removed trailing commas before JSON parsing.")
            data = json.loads(processed_json_str)
            self.logger.info("Successfully parsed extracted JSON string into Python dictionary.")
            return {"ad_library_main": data}
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON Parsing Failed: {e}", exc_info=False)
            error_pos = e.pos
            context_size = 75
            start = max(0, error_pos - context_size)
            end = min(len(processed_json_str), error_pos + context_size)
            self.logger.error(f"JSON error near position {error_pos}:")
            self.logger.error(
                f"...{processed_json_str[start:error_pos]}[on red]{processed_json_str[error_pos:error_pos + 1]}[/on red]{processed_json_str[error_pos + 1:end]}...")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error during JSON parsing: {e}", exc_info=True)
            return None

    def verify_data_structure(self, data):
        self.logger.info("Verifying extracted GraphQL data structure...")
        if not isinstance(data, dict):
            self.logger.error("Structure Check FAILED: Input is not a dictionary.")
            return False
        ad_library_main = data.get('ad_library_main')
        if not isinstance(ad_library_main, dict):
            self.logger.error("Structure Check FAILED: 'ad_library_main' key missing or not a dictionary.")
            return False
        search_results_connection = ad_library_main.get('search_results_connection')
        if not isinstance(search_results_connection, dict):
            self.logger.error("Structure Check FAILED: 'search_results_connection' missing or not a dictionary.")
            return False
        edges = search_results_connection.get('edges')
        if edges is None:
            self.logger.info("Structure Check OK: 'edges' is missing/None. No ads found.")
            return True
        if not isinstance(edges, list):
            self.logger.error("Structure Check FAILED: 'edges' exists but is not a list.")
            return False
        if not edges:
            self.logger.info("Structure Check OK: 'edges' is an empty list. No ads found.")
            return True
        self.logger.debug("Checking structure of the first ad edge...")
        first_edge = edges[0]
        if not isinstance(first_edge, dict):
            self.logger.error("Structure Check FAILED: First item in 'edges' is not a dictionary.")
            return False
        node = first_edge.get('node')
        if not isinstance(node, dict):
            self.logger.error("Structure Check FAILED: 'node' missing or not a dict in first edge.")
            return False
        collated_results = node.get('collated_results')
        if collated_results is None:
            self.logger.info("Structure Check OK: 'collated_results' missing/None in first node.")
            return True
        if not isinstance(collated_results, list):
            self.logger.error("Structure Check FAILED: 'collated_results' is not a list in first node.")
            return False
        if not collated_results:
            self.logger.info("Structure Check OK: 'collated_results' list is empty in first node.")
            return True
        first_result = collated_results[0]
        if not isinstance(first_result, dict):
            self.logger.error("Structure Check FAILED: First item in 'collated_results' is not a dictionary.")
            return False
        required_top_level = ['ad_archive_id', 'page_id', 'page_name', 'start_date']
        missing_top = [field for field in required_top_level if field not in first_result]
        if missing_top:
            self.logger.error(
                f"Structure Check FAILED: Missing required top-level fields in first result: {missing_top}")
            return False
        snapshot = first_result.get('snapshot')
        if not isinstance(snapshot, dict):
            self.logger.error("Structure Check FAILED: 'snapshot' missing or not a dict in first result.")
            return False
        self.logger.info("GraphQL data structure verification passed.")
        return True

    @staticmethod
    def convert_unix_to_yyyymmdd(epoch_time):
        logger_static = logging.getLogger(f"{__name__}.GraphQLProcessor.Utils.convert_unix_to_yyyymmdd")
        if epoch_time is None:
            return None
        try:
            if isinstance(epoch_time, float):
                epoch_int = int(epoch_time)
            elif isinstance(epoch_time, str) and epoch_time.isdigit():
                epoch_int = int(epoch_time)
            elif isinstance(epoch_time, int):
                epoch_int = epoch_time
            else:
                raise ValueError("Input must be numeric or a string of digits")
            if len(str(epoch_int)) == 13:
                epoch_int //= 1000
            elif len(str(epoch_int)) != 10:
                logger_static.warning(f"Unexpected timestamp length for '{epoch_time}'.")
                return None
            return datetime.utcfromtimestamp(epoch_int).strftime('%Y%m%d')
        except (ValueError, TypeError, OverflowError) as e:
            logger_static.error(f"Error converting timestamp '{epoch_time}': {e}")
            return None

    @staticmethod
    def _extract_media_info(snapshot: Dict[str, Any], media_type: str, key: str) -> Optional[str]:
        logger_static = logging.getLogger(f"{__name__}.GraphQLProcessor.Utils._extract_media_info")
        value_found = None
        source = "Not Found"
        cards = snapshot.get('cards')
        if isinstance(cards, list) and cards:
            first_card = cards[0]
            if isinstance(first_card, dict):
                value_found = first_card.get(key)
                if value_found:
                    source = f"Card['{key}']"
        if not value_found and media_type in snapshot:
            media_container = snapshot[media_type]
            if isinstance(media_container, list):
                for item in media_container:
                    if isinstance(item, dict):
                        value_found = item.get(key)
                        if value_found:
                            source = f"{media_type}[].['{key}']"
                            break
            elif isinstance(media_container, dict):
                value_found = media_container.get(key)
                if value_found:
                    source = f"{media_type}['{key}']"
        if not value_found and key in snapshot:
            value_found = snapshot.get(key)
            if value_found:
                source = f"Snapshot['{key}']"
        if value_found and logger_static.isEnabledFor(logging.DEBUG):
            logger_static.debug(f"Extracted '{key}' from {source}: '{str(value_found)[:60]}...'")
        return str(value_found) if value_found else None

    @staticmethod
    def _strip_html_tags(html: Optional[str]) -> str:
        logger_static = logging.getLogger(f"{__name__}.GraphQLProcessor.Utils._strip_html_tags")
        if not html:
            return ""
        try:
            soup = BeautifulSoup(html, "html.parser")
            text = soup.get_text(separator=' ', strip=True)
            if text != html and logger_static.isEnabledFor(logging.DEBUG):
                logger_static.debug(f"Stripped HTML: '{html[:50]}...' -> '{text[:50]}...'")
            return text
        except Exception as e:
            logger_static.error(f"Error stripping HTML: {e}")
            return ""

    def extract_graphql_html_ad_data(self, data):
        """
        Extracts and formats raw ad data from the verified GraphQL structure.
        Initializes fields with placeholders for external processing.
        Adds DETAILED logging for field extraction validation.
        (Corrected based on user feedback)
        """
        ad_data_list = []
        self.logger.info("Starting extraction of ad list from verified GraphQL data...")
        try:
            # Navigate safely, providing default empty dicts if keys are missing
            ad_library_main = data.get('ad_library_main', {})
            search_results = ad_library_main.get('search_results_connection', {})
            ad_edges = search_results.get('edges', [])

            if not ad_edges:
                self.logger.info("No 'edges' found in search_results_connection. No ads to extract.")
                return []
            self.logger.info(f"Found {len(ad_edges)} ad edges (groups) to process.")

            for edge_index, ad_edge in enumerate(ad_edges):
                log_prefix_edge = f"Edge {edge_index + 1}/{len(ad_edges)}: "
                if not isinstance(ad_edge, dict):
                    self.logger.warning(f"{log_prefix_edge}Skipping - edge item is not a dictionary.")
                    continue
                node = ad_edge.get('node', {})
                if not isinstance(node, dict):
                    self.logger.warning(f"{log_prefix_edge}Skipping - 'node' missing or not a dictionary.")
                    continue
                collated_results = node.get('collated_results', [])
                if not isinstance(collated_results, list):
                    self.logger.warning(f"{log_prefix_edge}Skipping - 'collated_results' is not a list.")
                    continue
                self.logger.debug(f"{log_prefix_edge}Found {len(collated_results)} collated results (ads).")

                for result_index, record in enumerate(collated_results):
                    log_prefix_ad_base = f"{log_prefix_edge}Ad {result_index + 1}/{len(collated_results)}: "
                    if not isinstance(record, dict):
                        self.logger.warning(f"{log_prefix_ad_base}Skipping - result item is not a dictionary.")
                        continue

                    # --- Detailed Field Extraction & Validation ---
                    self.logger.debug(f"{log_prefix_ad_base}--- Start Raw Field Extraction & Validation ---")

                    # 1. adArchiveID (Check multiple common casings)
                    ad_archive_id_raw = record.get('adArchiveID') or record.get('ad_archive_id')
                    ad_archive_id = str(ad_archive_id_raw) if ad_archive_id_raw else None
                    self.logger.debug(
                        f"{log_prefix_ad_base}Extracted adArchiveID: Raw='{ad_archive_id_raw}', Final='{ad_archive_id}'")
                    if not ad_archive_id:
                        self.logger.error(
                            f"{log_prefix_ad_base}CRITICAL: Missing 'adArchiveID'/'ad_archive_id'. Skipping this ad record.")
                        continue
                    log_prefix_ad = f"{log_prefix_ad_base}(ID: {ad_archive_id}): "

                    # 2. snapshot
                    snapshot = record.get('snapshot')
                    if not isinstance(snapshot, dict):
                        self.logger.error(
                            f"{log_prefix_ad}CRITICAL: Missing or invalid 'snapshot' dictionary. Skipping this ad record.")
                        continue
                    self.logger.debug(f"{log_prefix_ad}Snapshot found (keys: {list(snapshot.keys())})")

                    # 3. pageID (only save as PageID, not PageId)
                    page_id_raw = record.get('pageID') or record.get('page_id') or record.get('PageId')
                    page_id = str(page_id_raw) if page_id_raw else None
                    self.logger.debug(f"{log_prefix_ad}Extracted pageID: Raw='{page_id_raw}', Final='{page_id}'")
                    if not page_id: self.logger.warning(f"{log_prefix_ad}Missing 'pageID'/'page_id'.")

                    # 4. ad_creative_id (collation_id fallback)
                    collation_id_raw = record.get('collation_id')
                    ad_creative_id_raw = snapshot.get('ad_creative_id', collation_id_raw)
                    ad_creative_id = str(ad_creative_id_raw) if ad_creative_id_raw else None
                    self.logger.debug(
                        f"{log_prefix_ad}Extracted ad_creative_id: Raw='{ad_creative_id_raw}' (CollationID='{collation_id_raw}'), Final='{ad_creative_id}'")
                    if not ad_creative_id: self.logger.warning(f"{log_prefix_ad}Missing 'ad_creative_id'.")

                    # 5. Dates
                    start_date_raw = record.get('startDate') or record.get('start_date')
                    start_date = self.convert_unix_to_yyyymmdd(start_date_raw)
                    self.logger.debug(
                        f"{log_prefix_ad}Extracted startDate: Raw='{start_date_raw}', Final='{start_date}'")
                    if not start_date: self.logger.warning(
                        f"{log_prefix_ad}Missing or invalid 'startDate'/'start_date'.")

                    end_date_raw = record.get('endDate') or record.get('end_date')
                    end_date = self.convert_unix_to_yyyymmdd(end_date_raw)
                    self.logger.debug(f"{log_prefix_ad}Extracted endDate: Raw='{end_date_raw}', Final='{end_date}'")

                    # Effective end date calculated later by AdProcessor

                    # --- log_extract helper ---
                    def log_extract(field_name, value):
                        if self.logger.isEnabledFor(logging.DEBUG):
                            display_value = str(value)[:100] if value is not None else 'None'
                            if value and len(str(value)) > 100: display_value += '...'
                            self.logger.debug(f"{log_prefix_ad}Extracted '{field_name}': '{display_value}'")
                        return value

                    # --- End helper ---

                    # 6. Other top-level fields
                    page_name = log_extract('page_name', record.get('pageName') or record.get('page_name'))
                    is_active_raw = record.get('isActive') or record.get('is_active')
                    is_active = bool(is_active_raw) if is_active_raw is not None else False
                    log_extract('is_active_raw', is_active_raw)
                    log_extract('is_active', is_active)
                    publisher_platform = log_extract('publisher_platform',
                                                     record.get('publisherPlatform') or record.get(
                                                         'publisher_platform'))

                    # 7. Media URLs from snapshot
                    original_image_url = log_extract('original_image_url',
                                                     self._extract_media_info(snapshot, 'images', 'original_image_url'))
                    resized_image_url = log_extract('resized_image_url',
                                                    self._extract_media_info(snapshot, 'images', 'resized_image_url'))
                    video_preview_image_url = log_extract('video_preview_image_url',
                                                          self._extract_media_info(snapshot, 'videos',
                                                                                   'video_preview_url'))
                    video_hd_url = log_extract('video_hd_url',
                                               self._extract_media_info(snapshot, 'videos', 'video_hd_url'))
                    video_sd_url = log_extract('video_sd_url',
                                               self._extract_media_info(snapshot, 'videos', 'video_sd_url'))
                    if not original_image_url and not video_preview_image_url:
                        self.logger.warning(
                            f"{log_prefix_ad}Missing both 'original_image_url' and 'video_preview_image_url'.")

                    # 8. Card data extraction
                    card = {}
                    cards_list = snapshot.get('cards', [])  # Default to empty list
                    if isinstance(cards_list, list) and cards_list:
                        if isinstance(cards_list[0], dict):
                            card = cards_list[0]
                            self.logger.debug(f"{log_prefix_ad}Extracted first card data (keys: {list(card.keys())})")
                        else:
                            self.logger.warning(f"{log_prefix_ad}First item in 'cards' is not a dictionary.")

                    # 9. Text fields (prioritizing card, falling back to snapshot)
                    link_url_raw = self._extract_media_info(snapshot, 'link_url', 'link_url') or card.get('website_url')
                    link_url = log_extract('link_url', link_url_raw)

                    link_desc_raw = self._extract_media_info(snapshot, 'link_description',
                                                             'link_description') or card.get('link_description')
                    link_description = log_extract('link_description', link_desc_raw)

                    body_raw = card.get('body') or snapshot.get('body', {}).get('text') or snapshot.get('body', {}).get(
                        'markup', {}).get('__html')
                    body_html_stripped = log_extract('body', self._strip_html_tags(body_raw))

                    caption_raw = self._extract_media_info(snapshot, 'caption', 'caption') or card.get('caption')
                    caption = log_extract('caption', caption_raw)

                    cta_text_raw = card.get('cta_text') or snapshot.get('cta_text')
                    cta_text = log_extract('cta_text', cta_text_raw)

                    title_raw = card.get('title') or snapshot.get('title')
                    title = log_extract('title', title_raw)

                    # 10. Clean Template Values
                    fields_to_clean = ['link_description', 'body', 'title', 'caption', 'cta_text']
                    cleaned_data = {'link_description': link_description, 'body': body_html_stripped, 'title': title,
                                    'caption': caption, 'cta_text': cta_text}
                    for key in fields_to_clean:
                        value = cleaned_data.get(key)
                        if isinstance(value, str) and '{{' in value and '}}' in value:
                            original_value_log = value[:100];
                            cleaned_value = re.sub(r'\{\{.*?\}\}', '', value).strip();
                            cleaned_data[key] = cleaned_value if cleaned_value else None;
                            self.logger.debug(
                                f"{log_prefix_ad}Cleaned template in '{key}': '{original_value_log}' -> '{cleaned_data[key]}'")

                    # 11. Assemble Final Ad Dictionary (Placeholders for external processing)
                    ad_data = {
                        'ad_archive_id': ad_archive_id, 'ad_creative_id': ad_creative_id, 'page_id': page_id,
                        'page_name': page_name, 'start_date': start_date, 'end_date': end_date,
                        'last_updated': self.end_date,
                        'is_active': is_active, 'publisher_platform': publisher_platform, 'law_firm': None,
                        'title': cleaned_data['title'], 'body': cleaned_data['body'],
                        'link_description': cleaned_data['link_description'], 'caption': cleaned_data['caption'],
                        'cta_text': cleaned_data['cta_text'], 'link_url': link_url,
                        'original_image_url': original_image_url, 'resized_image_url': resized_image_url,
                        'video_preview_image_url': video_preview_image_url, 'video_hd_url': video_hd_url,
                        'video_sd_url': video_sd_url,
                        # Placeholders below are filled by AdProcessor/AIIntegrator/ImageHandler/VectorClusterer
                        's3_image_key': None, 'ImageText': None, 'Summary': None, 'IsForbidden403': False,
                        'campaign': None,  # Added by VectorClusterer
                        # Note: Category, Company, Product, etc. fields are no longer generated
                    }
                    self.logger.debug(
                        f"{log_prefix_ad}Final assembled ad data for passing to AdProcessor (keys: {list(ad_data.keys())}).")
                    ad_data_list.append(ad_data)

        except KeyError as e:
            self.logger.error(f"KeyError during detailed ad data extraction: {e}. Data might be malformed.",
                              exc_info=True)
        except Exception as e:
            self.logger.error(f"Unexpected error processing detailed ad data: {e}", exc_info=True)
        self.logger.info(f"Detailed extraction complete. Generated {len(ad_data_list)} ad dictionaries.")
        return ad_data_list

    def process_html(self, html_file_path: Optional[str] = None) -> Optional[List[Dict]]:
        target_file = html_file_path or self.html_file
        self.logger.info(f"Starting HTML processing pipeline for: {target_file}")
        html_content = self._load_html_content()
        if not html_content:
            self.logger.error(f"Failed to load HTML content from {self.html_file}.")
            return None
        parsed_data = self.extract_graphql_from_html(html_content)
        if not parsed_data:
            self.logger.error(f"Failed to extract GraphQL data from {self.html_file}.")
            return None
        self.data = parsed_data
        if not self.verify_data_structure(parsed_data):
            self.logger.error(f"GraphQL data structure verification failed.")
            return None
        ad_data_list = self.extract_graphql_html_ad_data(parsed_data)
        if ad_data_list is None:
            self.logger.error(f"Error occurred during final ad data extraction.")
            return None
        elif not ad_data_list:
            self.logger.info(f"No ad data found in the verified GraphQL structure.")
            return []
        else:
            self.logger.info(f"Successfully extracted initial data for {len(ad_data_list)} ads.")
            return ad_data_list


# --- Main Async Processing Function (Remains the same - orchestrates the full pipeline) ---
async def process_all_files_async(config, html_directory, files_to_process):
    """
    Asynchronously processes all specified HTML files using the full pipeline:
    HTML Parse -> Ad Extraction -> Image Handling -> AI Enhancement -> DB Saves.
    Initializes shared components ONCE.
    """
    processed_count = 0
    success_count = 0
    fail_count = 0
    main_logger = logging.getLogger(__name__)
    print_func = rich_print if RICH_AVAILABLE else print
    orchestrator_components = {}  # Dictionary to hold shared components

    try:
        # --- Initialize Shared Components ONCE ---
        main_logger.info("Initializing shared components for full processing pipeline...")
        try:
            # Initialize DI Container
            container = FBAdsDIContainer()
            container.config.from_dict({
                **config,
                's3': {
                    'bucket_name': 'lexgenius-dockets',
                    'aws_access_key': os.environ.get('AWS_ACCESS_KEY_ID', ''),
                    'aws_secret_key': os.environ.get('AWS_SECRET_ACCESS_KEY', ''),
                    'aws_region': os.environ.get('AWS_REGION', 'us-west-2')
                }
            })
            
            # Store container for later use
            orchestrator_components['container'] = container
            
            # Get services from container
            session_manager = container.session_manager()
            if not await session_manager.create_new_session():
                raise RuntimeError("Failed to establish initial session via Session Manager.")
            orchestrator_components['session_manager'] = session_manager

            # S3 Manager
            s3_manager = container.s3_manager()
            await s3_manager.initialize()
            orchestrator_components['s3_manager'] = s3_manager
            # Use use_local config for database managers
            use_local_db_ad_archive = config.get('use_local', False)  # Default to AWS if not specified
            use_local_db_law_firms = config.get('use_local', False)  # Default to AWS if not specified
            main_logger.info(f"Initializing FBAdArchiveManager (Local={use_local_db_ad_archive})")
            main_logger.info(f"Initializing LawFirmsManager (Local={use_local_db_law_firms})")
            
            # Use legacy adapter classes that work with config dict
            fb_ad_db = FBAdArchiveManager(config, use_local=use_local_db_ad_archive)
            law_firm_db = LawFirmsManager(config, use_local=use_local_db_law_firms)
            
            orchestrator_components['fb_ad_db'] = fb_ad_db
            orchestrator_components['law_firm_db'] = law_firm_db

            # Get services from DI container
            image_handler = container.image_handler()
            orchestrator_components['image_handler'] = image_handler

            # PDF Extractor - not needed for FB ads processing, skip it
            pdf_extractor = None
            orchestrator_components['pdf_extractor'] = pdf_extractor

            # AI Services from container
            ai_integrator = container.ai_integrator()
            orchestrator_components['ai_integrator'] = ai_integrator
            
            # Get individual AI instances for compatibility
            orchestrator_components['gpt_instance'] = container.gpt_instance() if not config.get('disable_gpt') else None
            orchestrator_components['deepseek_instance'] = container.deepseek_instance() if not config.get('disable_deepseek') else None
            orchestrator_components['llava_extractor_instance'] = container.llava_instance() if not config.get('disable_llava') else None

            # Note: AdProcessor removed - using direct sequential processing

            main_logger.info("All shared components initialized successfully for full processing.")
        except Exception as init_e:
            main_logger.critical(f"Failed to initialize shared components: {init_e}", exc_info=True)
            await cleanup_components(orchestrator_components)  # Attempt cleanup
            raise RuntimeError("Component initialization failed.") from init_e

        # --- Process Files Loop ---
        for i, file in enumerate(files_to_process):
            processed_count += 1
            company_id = file.replace('.html', '')
            file_path = os.path.join(html_directory, file)
            firm_name = "Unknown Firm"  # Default
            firm_data = None  # Reset for each file

            print_func(
                f"\n[bold]--- Processing file {processed_count}/{len(files_to_process)}: [blue underline]{file}[/blue underline] (Company ID: {company_id}) ---[/bold]")
            try:
                gql_processor = GraphQLProcessor(company_id, config)
            except Exception as gql_init_e:
                main_logger.error(f"Failed to initialize GraphQLProcessor for {company_id}: {gql_init_e}.",
                                  exc_info=True)
                fail_count += 1
                continue
            try:
                # --- Fetch Firm Name ---
                try:
                    law_firm_db_instance = orchestrator_components['law_firm_db']  # Get instance
                    # get_record is synchronous
                    firm_data = law_firm_db_instance.get_record(company_id)
                    if firm_data and 'Name' in firm_data:
                        firm_name = firm_data['Name']
                        # Use rich print for bold
                        print_func(f"Processing ads for: [bold]{firm_name}[/bold] (ID: {company_id})")
                    else:
                        firm_data = None  # Ensure firm_data is None if record/name not found
                        main_logger.warning(
                            f"Could not find firm name for {company_id}. Using placeholder '{firm_name}'.")
                except Exception as name_e:
                    firm_data = None  # Ensure firm_data is None on error
                    main_logger.warning(f"Error fetching firm name for {company_id}: {name_e}. Using placeholder.")

                # --- Step 1: Extract Raw Ad List ---
                # process_html is synchronous
                raw_ad_data_list = gql_processor.process_html(file_path)

                # --- Step 2: Handle Extraction Result ---
                if raw_ad_data_list is None:
                    print_func(f"[red]Failed[/red] to process HTML or extract data for {company_id}.")
                    fail_count += 1
                    continue
                elif not raw_ad_data_list:
                    main_logger.info(f"No ad data found in HTML file for {company_id}. Updating firm status.")
                    # --- Update LawFirm Status ---
                    try:
                        law_firm_db = orchestrator_components['law_firm_db']
                        update_data = {'AdArchiveLastUpdated': config['date_str_yyyymmdd'], 'NumAds': 0}
                        # --- Ensure primary keys exist for update ---
                        key_dict = {'ID': str(company_id)}
                        if firm_data and 'Name' in firm_data:
                            key_dict['Name'] = firm_data['Name']  # Add Name if known
                        else:
                            # If name is unknown, update_item might fail depending on its implementation
                            # add_or_update_record might be safer if name could be missing
                            main_logger.warning(f"Attempting status update for {company_id} without known Name.")

                        # update_item is synchronous
                        if law_firm_db.update_item(key_dict, update_data):
                            main_logger.info(f"Updated LawFirm status for {firm_name} (ID: {company_id}) to NumAds=0.")
                            success_count += 1
                        else:
                            main_logger.error(
                                f"Failed to update LawFirm status for {firm_name} (ID: {company_id}) (manager indicated failure).")
                            fail_count += 1
                    except Exception as status_update_e:
                        main_logger.error(
                            f"Error preparing/updating LawFirm status for {company_id}: {status_update_e}",
                            exc_info=True)
                        fail_count += 1
                    continue  # Skip to next file
                else:
                    # --- Step 3: Process Raw Ads (Sequential Processing) ---
                    main_logger.info(
                        f"Extracted {len(raw_ad_data_list)} raw ad entries. Processing sequentially...")
                    
                    # Sequential processing using AI orchestrator directly (same as src/services/fb_ads)
                    processed_ads = []
                    ai_integrator = orchestrator_components['ai_integrator']
                    image_handler = orchestrator_components['image_handler']
                    
                    for ad_data in raw_ad_data_list:
                        try:
                            # Step 1: Create structured ad dictionary
                            structured_ad = _create_structured_ad(ad_data, firm_name)
                            if not structured_ad:
                                main_logger.warning(f"Failed to structure ad data, skipping")
                                continue
                            
                            ad_archive_id = structured_ad.get('ad_archive_id')
                            ad_creative_id = structured_ad.get('ad_creative_id') or ad_archive_id
                            original_image_url = structured_ad.get('original_image_url')
                            
                            # Step 2-3: Check if ad already exists with valid summary
                            fb_ad_db = orchestrator_components['fb_ad_db']
                            existing_ad = None
                            try:
                                # Check if ad exists in FBAdArchive
                                from boto3.dynamodb.conditions import Key
                                table = fb_ad_db.get_table()
                                response = table.query(
                                    KeyConditionExpression=Key('AdArchiveID').eq(ad_archive_id)
                                )
                                if response['Items']:
                                    existing_ad = response['Items'][0]
                                    existing_summary = existing_ad.get('Summary')
                                    if existing_summary and existing_summary not in [None, '', 'NA', 'SKIPPED']:
                                        main_logger.info(f"Ad {ad_archive_id} already has valid summary: '{existing_summary}' - skipping reprocessing")
                                        continue
                            except Exception as check_e:
                                main_logger.debug(f"Could not check existing ad {ad_archive_id}: {check_e}")
                            
                            # Steps 4-6: Handle image processing (S3 check, download, PHash, FBImageHash)
                            if ad_creative_id and original_image_url:
                                try:
                                    s3_key, s3_exists = await image_handler.process_and_upload_ad_image(
                                        ad_archive_id, ad_creative_id, original_image_url
                                    )
                                    if s3_key:
                                        structured_ad['s3_image_key'] = s3_key
                                        structured_ad['S3ImageKey'] = s3_key  # Both formats for compatibility
                                        
                                        # Get PHash and check for existing ImageText from FBImageHash
                                        phash_str, existing_image_text = await image_handler.get_phash_and_existing_text(
                                            s3_key, ad_archive_id, ad_creative_id, config.get('date_str_yyyymmdd', '20250713')
                                        )
                                        structured_ad['phash'] = phash_str
                                        structured_ad['PHash'] = phash_str  # Both formats for compatibility
                                        if existing_image_text:
                                            structured_ad['ImageText'] = existing_image_text
                                        else:
                                            # Keep PHash in structured_ad so AI orchestrator can add to image queue
                                            # Set ImageText to None to indicate it needs processing
                                            structured_ad['ImageText'] = None
                                            main_logger.info(f"Ad {ad_archive_id}: No existing ImageText for PHash {phash_str}, will be queued for processing")
                                            
                                except Exception as img_e:
                                    main_logger.warning(f"Image processing failed for ad {ad_archive_id}: {img_e}")
                                    structured_ad['s3_image_key'] = None
                                    structured_ad['S3ImageKey'] = None
                                    structured_ad['ImageText'] = None
                            
                            # Steps 7-8: AI enhancement (LLM processing for summary) and prepare for DB save
                            if ai_integrator:
                                try:
                                    # Use the same method as src/services/fb_ads
                                    enhanced_ad = await ai_integrator.enhance_ad_data(structured_ad)
                                    if enhanced_ad:
                                        processed_ads.append(enhanced_ad)
                                        main_logger.debug(f"Successfully enhanced ad {ad_archive_id}")
                                    else:
                                        main_logger.warning(f"AI enhancement returned None for ad {ad_archive_id}")
                                        processed_ads.append(structured_ad)
                                except Exception as ai_e:
                                    main_logger.warning(f"AI enhancement failed for ad {ad_archive_id}: {ai_e}")
                                    # Use original structured ad as fallback
                                    processed_ads.append(structured_ad)
                            else:
                                main_logger.warning("AI integrator not available, using structured ad without enhancement")
                                processed_ads.append(structured_ad)
                                
                        except Exception as e:
                            main_logger.error(f"Failed to process individual ad: {e}")
                            continue

                    if not processed_ads:
                        main_logger.error(f"No ads were successfully processed for {firm_name}.")
                        fail_count += 1
                        continue

                    valid_ads_for_saving = [ad for ad in processed_ads if isinstance(ad, dict)]
                    num_processed_valid = len(valid_ads_for_saving)
                    if len(processed_ads) != num_processed_valid:
                        main_logger.warning(
                            f"AdProcessor returned {len(processed_ads) - num_processed_valid} None/invalid items for {firm_name}.")

                    if not valid_ads_for_saving:
                        main_logger.info(
                            f"No valid ads remained after AdProcessor stage for {firm_name}. Updating status.")
                        # --- Update LawFirm Status ---
                        try:
                            law_firm_db = orchestrator_components['law_firm_db']
                            update_data = {'AdArchiveLastUpdated': config['date_str_yyyymmdd'], 'NumAds': 0}
                            key_dict = {'ID': str(company_id)}
                            if firm_data and 'Name' in firm_data: key_dict['Name'] = firm_data['Name']
                            # update_item is synchronous
                            if law_firm_db.update_item(key_dict, update_data):
                                success_count += 1
                            else:
                                fail_count += 1
                        except Exception:
                            fail_count += 1
                        continue  # Skip file

                    # --- Step 4: Save Processed Ads ---
                    main_logger.info(f"Saving {num_processed_valid} processed ads to FBAdArchive DB for {firm_name}...")
                    
                    # Remove image/video URL fields before saving to database
                    url_fields_to_remove = [
                        'original_image_url', 'resized_image_url', 'video_preview_image_url',
                        'video_hd_url', 'video_sd_url'
                    ]
                    ads_for_db_save = []
                    for ad in valid_ads_for_saving:
                        # Create a copy to avoid modifying the original
                        ad_copy = ad.copy()
                        for field in url_fields_to_remove:
                            ad_copy.pop(field, None)
                        ads_for_db_save.append(ad_copy)
                    
                    save_success_count = 0
                    save_fail_count = 0
                    try:
                        fb_ad_db = orchestrator_components['fb_ad_db']
                        # batch_insert_items is synchronous
                        save_success_count, save_fail_count = fb_ad_db.batch_insert_items(
                            ads_for_db_save
                        )

                        if save_fail_count > 0:
                            print_func(
                                f"[red]Failed[/red] to save {save_fail_count}/{num_processed_valid} ads to FBAdArchive for {firm_name}.")
                            fail_count += 1  # Count the file as failed if any ad save failed
                            continue  # Skip LawFirm update if save failed
                        else:
                            print_func(
                                f"[green]Successfully[/green] saved/updated {save_success_count} ads to FBAdArchive for {firm_name}.")

                        # --- Step 5: Update LawFirm Record (Only if save succeeded) ---
                        main_logger.info(f"Updating LawFirms record for {firm_name} (NumAds: {num_processed_valid})...")
                        try:
                            law_firm_db = orchestrator_components['law_firm_db']
                            update_data = {'AdArchiveLastUpdated': config['date_str_yyyymmdd'],
                                           'NumAds': num_processed_valid}
                            key_dict = {'ID': str(company_id)}
                            if firm_data and 'Name' in firm_data: key_dict['Name'] = firm_data['Name']
                            # update_item is synchronous
                            if law_firm_db.update_item(key_dict, update_data):
                                print_func(f"[green]Successfully[/green] updated LawFirms record for {firm_name}.")
                                success_count += 1  # Final overall success for the file
                            else:
                                print_func(
                                    f"[red]Failed[/red] to update LawFirms record for {firm_name} (manager indicated failure).")
                                fail_count += 1
                        except Exception as firm_update_e:
                            print_func(f"[red]Failed[/red] to update LawFirms record for {firm_name}: {firm_update_e}")
                            main_logger.error(f"Error updating LawFirm {firm_name}: {firm_update_e}", exc_info=True)
                            fail_count += 1

                    except Exception as fb_save_e:
                        print_func(f"[red]Error during FBAdArchive save call[/red] for {firm_name}: {fb_save_e}")
                        main_logger.error(f"Error during batch_insert_items call for {firm_name}: {fb_save_e}",
                                          exc_info=True)
                        fail_count += 1
                        # Skip LawFirm update if FB save failed

            except Exception as e:
                print_func(f"[red]Unexpected error processing file {file}: {e}[/red]")
                main_logger.error(f"An unexpected error occurred processing file {file}: {e}", exc_info=True)
                fail_count += 1

            # --- Pause Between Files ---
            if len(files_to_process) > 1 and i < len(files_to_process) - 1:
                pause_duration = random.uniform(0.5, 1.5)
                main_logger.debug(f"Pausing for {pause_duration:.2f}s before next file...")
                # Use await for asyncio.sleep
                await asyncio.sleep(pause_duration)

    # --- Cleanup After All Files ---
    finally:
        await cleanup_components(orchestrator_components)  # Call async cleanup helper

    return processed_count, success_count, fail_count


# === Helper Function for Cleanup ===
async def cleanup_components(components: Dict[str, Any]):
    main_logger = logging.getLogger(__name__)
    main_logger.info("Performing final component cleanup...")
    
    # Close aiohttp session
    aiohttp_session = components.get('aiohttp_session')
    if aiohttp_session and not aiohttp_session.closed:
        try:
            await aiohttp_session.close()
            main_logger.debug("Closed aiohttp session.")
        except Exception as e:
            main_logger.warning(f"Error closing aiohttp session: {e}")
    
    # Close LLaVA session
    llava = components.get('llava_extractor_instance')
    if llava:
        try:
            # Use the context manager exit method
            await llava.__aexit__(None, None, None)
            main_logger.debug("Closed LLaVA session.")
        except Exception as e:
            main_logger.warning(f"Error closing LLaVA session: {e}")
    
    # Close Session Manager requests session
    sm = components.get('session_manager')
    if sm and hasattr(sm, 'reqs') and sm.reqs:
        try:
            sm.reqs.close()
            main_logger.debug("Closed Session Manager requests session.")
        except Exception as e:
            main_logger.warning(f"Error closing Session Manager session: {e}")
    
    # Close S3 manager
    s3_manager = components.get('s3_manager')
    if s3_manager:
        try:
            await s3_manager.close()
            main_logger.debug("Closed S3 async storage.")
        except Exception as e:
            main_logger.warning(f"Error closing S3 storage: {e}")
    
    main_logger.info("Component cleanup finished.")


# === Main Execution Block ===
if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Process Facebook Ad Archive HTML files (Full Pipeline).",
                                     formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument('--date', default=datetime.now().strftime('%m/%d/%y'))
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'])
    parser.add_argument('-v', '--verbose', action='store_true')
    parser.add_argument('--html-dir', default=None)
    parser.add_argument('--company-id', default=None)
    parser.add_argument('--mobile-proxy', action='store_true')
    parser.add_argument('--render-html', action='store_true')
    parser.add_argument('--disable-llava', action='store_true')
    parser.add_argument('--disable-gpt', action='store_true')
    parser.add_argument('--disable-deepseek', action='store_true')
    parser.add_argument('--force', action='store_true')
    args = parser.parse_args()

    # --- Logging Setup ---
    log_level_name = args.log_level.upper()
    log_level = getattr(logging, log_level_name, logging.INFO)
    if args.verbose:
        log_level = logging.DEBUG
        log_level_name = "DEBUG"
    handler_class = RichHandler if RICH_AVAILABLE else logging.StreamHandler
    handler_kwargs = {}
    if RICH_AVAILABLE:
        handler_kwargs = {"rich_tracebacks": True, "show_path": False, "log_time_format": "[%Y-%m-%d %H:%M:%S]",
                          "markup": True}
        log_format = "%(name)s: %(message)s"
    else:
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    log_handlers = []
    handler_instance = handler_class(**handler_kwargs)
    handler_instance.setLevel(log_level)
    log_handlers.append(handler_instance)
    logging.basicConfig(level=log_level, format=log_format, handlers=log_handlers, force=True)
    lib_level = logging.WARNING if log_level >= logging.INFO else logging.DEBUG
    libs_to_adjust = ["requests", "urllib3", "botocore", "boto3", "s3transfer", "PIL", "aiohttp", "httpx"]
    for lib in libs_to_adjust:
        logging.getLogger(lib).setLevel(lib_level)
    main_logger = logging.getLogger(__name__)
    main_logger.info(f"Script started (Full Pipeline Mode) with arguments: {args}")
    main_logger.info(f"Effective root logger level set to: {log_level_name}")
    if not RICH_AVAILABLE:
        main_logger.warning("`rich` library not found.")

    # --- Configuration Loading ---
    config = None
    try:
        try:
            # Handle both YYYYMMDD and MM/DD/YY formats
            if re.match(r'^\d{8}$', args.date):  # YYYYMMDD format
                process_date_yyyymmdd = args.date
                # Convert to MM/DD/YY for config loading
                date_obj = datetime.strptime(args.date, '%Y%m%d')
                config_date = date_obj.strftime('%m/%d/%y')
            else:  # Assume MM/DD/YY format
                config_date = args.date
                process_date_yyyymmdd = datetime.strptime(args.date, '%m/%d/%y').strftime('%Y%m%d')
            
            # Load config as Pydantic model then convert to dict for compatibility
            pydantic_config = load_config('fb_ads', {
                'date': config_date,
                'mobile_proxy': args.mobile_proxy,
                'render_html': args.render_html
            })

            # Convert to dict for legacy compatibility
            config = pydantic_config.model_dump()

            # Add additional fields that aren't in Pydantic model
            config.update({
                'log_level': log_level_name,
                'verbose': args.verbose,
                'date_str_yyyymmdd': process_date_yyyymmdd,
                'use_proxy': True,  # ALWAYS use proxy as required
                'mobile_proxy': args.mobile_proxy,
                'render_html': args.render_html
            })
        except ValueError:
            main_logger.critical(f"Invalid date format: {args.date}. Please use YYYYMMDD or MM/DD/YY format. Exiting.")
            sys.exit(1)
        script_dir_for_defaults = os.path.dirname(os.path.abspath(__file__))
        # Navigate to project root: scripts/run/processing_helpers -> project_root
        project_root = os.path.abspath(os.path.join(script_dir_for_defaults, '..', '..', '..'))
        default_base_data_dir = os.path.join(project_root, 'data')
        config.setdefault('base_data_dir', default_base_data_dir)
        # For FB ad archive HTML, use the fb_ad_archive directory in project root
        default_html_dir = os.path.join(project_root, 'fb_ad_archive', 'html')
        config.setdefault('graphql_html_dir', args.html_dir or default_html_dir)
        default_download_dir = os.path.join(config['base_data_dir'], f"downloaded_{config['date_str_yyyymmdd']}")
        config.setdefault('temp_image_dir', os.path.join(default_download_dir, 'img_temp'))
        config.setdefault('s3_ad_archive_prefix', 'adarchive/fb')
        config.setdefault('image_download_retries', 3)
        config.setdefault('image_retry_delay', 5)
        config.setdefault('image_download_timeout', 45)
        config.setdefault('bucket_name', 'lexgenius-dockets')
        config.setdefault('disable_llava', False)  # Enable LLaVA by default
        config.setdefault('disable_gpt', args.disable_gpt)
        config.setdefault('disable_deepseek', args.disable_deepseek)
        config.setdefault('ollama_base_url', "http://localhost:11434")
        config.setdefault('llava_model_name', "llama32-vision-ocr:latest")
        config.setdefault('llava_timeout', 300)
        config.setdefault('llava_temperature', 0.0)
        config.setdefault('ollama_gpu_layers', -1)
        config.setdefault('oxy_labs_username', os.environ.get('OXY_LABS_USERNAME'))
        config.setdefault('oxy_labs_password', os.environ.get('OXY_LABS_PASSWORD'))
        config.setdefault('aws_access_key_id', os.environ.get('AWS_ACCESS_KEY_ID'))
        config.setdefault('aws_secret_access_key', os.environ.get('AWS_SECRET_ACCESS_KEY'))
        config.setdefault('aws_region', os.environ.get('AWS_REGION', 'us-west-2'))
        # Disable deferred image processing to enable immediate text extraction
        config['defer_image_processing'] = False
        # Proxy is always enabled - no override logic needed
        if not config.get('bucket_name'):
            main_logger.critical("Config Error: 'bucket_name' missing.")
            sys.exit(1)
        # Skip proxy credential warning - they're loaded from env vars and proxy is working
        main_logger.info("Configuration loaded successfully for full processing.")
        main_logger.debug(
            f"Config (partial): bucket='{config.get('bucket_name')}', use_proxy='{config.get('use_proxy')}', html_dir='{config.get('graphql_html_dir')}'")
    except FileNotFoundError as e:
        main_logger.critical(f"Config file not found: {e}.", exc_info=True)
        sys.exit(1)
    except Exception as e:
        main_logger.critical(f"Failed to load configuration: {e}", exc_info=True)
        sys.exit(1)

    # --- Determine HTML Directory ---
    html_directory = config['graphql_html_dir']
    if not os.path.isdir(html_directory):
        main_logger.critical(f"HTML directory not found: {html_directory}")
        sys.exit(1)
    main_logger.info(f"Processing HTML files from: {html_directory}")

    # --- Determine Files to Process ---
    files_to_process = []
    try:
        if args.company_id:
            file_name = f"{args.company_id}.html"
            file_path = os.path.join(html_directory, file_name)
            if os.path.isfile(file_path):
                files_to_process.append(file_name)
                main_logger.info(f"Processing only specified company ID: {args.company_id}")
            else:
                main_logger.error(f"Specified company ID file not found: {file_path}. Exiting.")
                sys.exit(1)
        else:
            files_to_process = sorted([f for f in os.listdir(html_directory) if f.endswith('.html')])
            main_logger.info(f"Found {len(files_to_process)} HTML files to potentially process.")
            if not files_to_process:
                main_logger.warning("No HTML files found. Nothing to process.")
                sys.exit(0)
    except Exception as e:
        main_logger.critical(f"Error listing files in {html_directory}: {e}", exc_info=True)
        sys.exit(1)

    # --- Run the Async Processing Loop ---
    processed_count = 0
    success_count = 0
    fail_count = 0
    exit_code = 0
    try:
        processed_count, success_count, fail_count = asyncio.run(
            process_all_files_async(config, html_directory, files_to_process)
        )
    except KeyboardInterrupt:
        main_logger.warning("Processing interrupted by user.")
        exit_code = 1
    except RuntimeError as rte:
        main_logger.critical(f"RuntimeError during processing: {rte}.")
        exit_code = 1
        fail_count = processed_count  # Assume all attempted failed
    except Exception as main_err:
        main_logger.critical(f"Critical error during async processing: {main_err}", exc_info=True)
        fail_count = processed_count - success_count
        exit_code = 1

    # --- Script Completion Summary ---
    print_func = rich_print if RICH_AVAILABLE else print
    print_func("\n[bold]--- Processing Summary ---[/bold]")
    print_func(f"Total files found:        {len(files_to_process)}")
    print_func(f"Files attempted:          {processed_count}")
    print_func(f"[green]Successful processing:[/green] {success_count}")
    print_func(f"[red]Failed processing:[/red]     {fail_count}")
    if fail_count > 0 or exit_code != 0:
        main_logger.warning("Script finished with errors or was interrupted.")
        sys.exit(1)
    else:
        main_logger.info("Script finished successfully.")
        sys.exit(0)

# --- END OF FILE graphql_parser.py ---
# === How to Use Comments ===

# --- Basic Usage ---
# Run the script to process all HTML files in the default directory for today's date:
# python graphql_parser.py

# Specify a date (MM/DD/YY format):
# python graphql_parser.py --date 09/21/23

# Specify the directory containing the HTML files:
# python graphql_parser.py --html-dir /path/to/your/ad/archive/html/files

# Combine date and directory:
# python graphql_parser.py --date 09/21/23 --html-dir /path/to/html

# --- Logging Control ---
# Enable verbose (DEBUG level) logging for detailed output:
# python graphql_parser.py --verbose
# Or use the short form:
# python graphql_parser.py -v

# Set a specific logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL). Note: --verbose overrides this to DEBUG.
# python graphql_parser.py --log-level WARNING

# --- Proxy Configuration ---
# Use the mobile proxy settings defined in the configuration file:
# python graphql_parser.py --mobile-proxy
# (Default is to use residential proxy settings if credentials are provided)

# --- Specific Company ---
# Process only a single company's HTML file (provide the ID, which is the filename without .html):
# python graphql_parser.py --company-id 100738128726172 --html-dir /path/to/html

# Combine verbose logging with specific company processing:
# python graphql_parser.py --company-id 100738128726172 --html-dir /path/to/html -v

# --- Placeholder Argument ---
# The --render-html flag is currently a placeholder and doesn't activate functionality in this script:
# python graphql_parser.py --render-html

# --- Default HTML Directory ---
# If --html-dir is not specified, the script defaults to looking in a directory calculated relative
# to its own location: ../data/fb_ad_archive/html

# --- Example Combined Command ---
# Process files for Sep 20, 2023, from a specific directory, using mobile proxies, with verbose logging:
# python graphql_parser.py --date 09/20/23 --html-dir /data/fb_ads/html --mobile-proxy -v

# Stinar Gould Grieco & Hensley <-- add 453782291150728
# Justice After Depo-Provera  478178125373324 Levy Konigsberg ADD TO LAW FIRMS
# 461461240377447 Levy Konigsberg -< Empty

# Remove:  388853754320035 KP LLC
# Vigna Law Group :: 102312344711237 add
# 1427193037597544 - gibbs law group llp
# 1667434743521564 - The Sentinel Group

# Check harding mazzotti llp depo provea - 1800law1010


# Hughes & Coleman Injury Lawyers 93356843939 <--add
# Aaron & Rash 61567054448131 <- Add
# Lynch & Carpenter

# Mctlaw 162615037095776
# Napoli
# Rosenfeld Injury Lawyers LLC

# CHECK
# Depo-Provera Injuries, Depo-Provera Lawsuit, Depo-Provera Side Effects Helpline Weitz
# Weitz Brain Tumor Lawsuit inactive 291843034020037
#  461461240377447 Justice for Depo-Provera Users Levy Konigsberg

#  104578099411173 All Tort Solutions
# Check ad archive ID 1120658366361885
