#!/usr/bin/env python3
"""
Run Docket Refactor Validation

Executes both comprehensive tests and integration validation for the 
docket extraction refactor.
"""

import sys
import subprocess
import os

# Add the project root to the Python path
sys.path.append('/Users/<USER>/PycharmProjects/lexgenius')

def run_tests_and_validation():
    """Run all tests and validation scripts."""
    print("=" * 80)
    print("DOCKET EXTRACTION REFACTOR - COMPLETE VALIDATION SUITE")
    print("=" * 80)
    
    # Change to the project directory
    os.chdir('/Users/<USER>/PycharmProjects/lexgenius')
    
    success = True
    
    # Run comprehensive tests
    print("\n📝 RUNNING COMPREHENSIVE TESTS...")
    print("-" * 50)
    
    try:
        result = subprocess.run([
            sys.executable, 
            'tests/test_docket_extraction_refactor.py'
        ], capture_output=True, text=True, timeout=60)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
            
        if result.returncode != 0:
            print("❌ Comprehensive tests FAILED")
            success = False
        else:
            print("✅ Comprehensive tests PASSED")
            
    except subprocess.TimeoutExpired:
        print("❌ Comprehensive tests TIMED OUT")
        success = False
    except Exception as e:
        print(f"❌ Comprehensive tests ERROR: {e}")
        success = False
    
    # Run integration validation
    print("\n\n🔧 RUNNING INTEGRATION VALIDATION...")
    print("-" * 50)
    
    try:
        result = subprocess.run([
            sys.executable,
            'scripts/validate_docket_extraction_refactor.py'
        ], capture_output=True, text=True, timeout=60)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
            
        if result.returncode != 0:
            print("❌ Integration validation FAILED")
            success = False
        else:
            print("✅ Integration validation PASSED")
            
    except subprocess.TimeoutExpired:
        print("❌ Integration validation TIMED OUT")
        success = False
    except Exception as e:
        print(f"❌ Integration validation ERROR: {e}")
        success = False
    
    # Final summary
    print("\n" + "=" * 80)
    if success:
        print("🎉 ALL VALIDATION COMPLETE - SUCCESS!")
        print("=" * 80)
        print()
        print("REFACTORING SUMMARY:")
        print("✅ Created extract_docket_digits() function with strict 13-digit extraction")
        print("✅ Updated DocketOrchestrator to use robust extraction logic")
        print("✅ Enhanced CaseValidator with improved pattern matching")
        print("✅ Maintained backward compatibility with normalize_docket_number()")
        print("✅ Added comprehensive test suite")
        print("✅ Validated integration with existing PACER workflow")
        print()
        print("KEY IMPROVEMENTS:")
        print("- Extracts ONLY first 13 digits (N:YY-XX-NNNNN)")
        print("- Never extracts anything after the 5 case numbers")
        print("- Zero-pads case numbers < 5 digits")
        print("- Stops after case number, ignores judge initials")
        print("- Enhanced regex patterns for better matching")
        print()
        print("FILES MODIFIED:")
        print("- /src/utils/docket_utils.py (added extract_docket_digits)")
        print("- /src/pacer/facades/docket_orchestrator.py (updated extraction logic)")
        print("- /src/pacer/components/case_processing/case_validator.py (enhanced patterns)")
        print()
        print("DELIVERABLE: ✅ COMPLETE")
    else:
        print("❌ VALIDATION FAILED - REQUIRES ATTENTION")
        print("=" * 80)
        print("Some tests or validations failed. Please review the output above.")
    
    return success


if __name__ == "__main__":
    success = run_tests_and_validation()
    sys.exit(0 if success else 1)
