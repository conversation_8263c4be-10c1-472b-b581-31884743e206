#!/usr/bin/env python3
"""
Minimal authentication test to validate the rollback fix works.
This bypasses the container system and tests components directly.
"""

import asyncio
import sys
import os
import logging

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

async def test_minimal_authentication():
    """Test the minimal authentication flow directly."""
    
    print("🔍 Testing minimal authentication flow...")
    
    # Create basic config
    config = {
        'headless': False,  # Show browser for debugging
        'username_prod': os.getenv('PACER_USERNAME_PROD', 'test'),
        'password_prod': os.getenv('PACER_PASSWORD_PROD', 'test'),
        'screenshot_dir': './data/screenshots'
    }
    
    # Create logger
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    try:
        # Import components directly
        from src.pacer.components.browser.navigator import Pacer<PERSON>avigator
        from src.pacer.facades.authentication_facade import Authentication<PERSON>acade
        from playwright.async_api import async_playwright
        
        # Create browser context manually
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=1000)
            context = await browser.new_context()
            page = await context.new_page()
            
            # Create navigator
            navigator = PacerNavigator(page=page, logger=logger, config=config)
            
            # Create authentication facade
            auth_facade = AuthenticationFacade(logger=logger, config=config)
            
            print("🔐 Testing complete authentication sequence...")
            
            # Test the perform_login method 
            result = await auth_facade.perform_login(
                navigator=navigator,
                court_id="ilnd"
            )
            
            print(f"📊 Authentication result: {result}")
            
            if result:
                print("✅ Authentication rollback fix working!")
                
                # Test navigator handoff
                print("🔄 Testing navigator handoff...")
                current_url = navigator.page.url
                print(f"📍 Current URL after login: {current_url}")
                
                if navigator.is_ready:
                    print("✅ Navigator is ready for handoff!")
                    return True
                else:
                    print("❌ Navigator not ready after authentication!")
                    return False
            else:
                print("❌ Authentication failed!")
                return False
                
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'browser' in locals():
            await browser.close()

if __name__ == "__main__":
    print("🚀 Starting minimal authentication test...")
    
    # Check environment variables
    if not os.getenv('PACER_USERNAME_PROD') or not os.getenv('PACER_PASSWORD_PROD'):
        print("⚠️  Warning: PACER credentials not found in environment")
        print("   Set PACER_USERNAME_PROD and PACER_PASSWORD_PROD")
    
    result = asyncio.run(test_minimal_authentication())
    
    if result:
        print("\n✅ Minimal authentication test PASSED!")
        sys.exit(0)
    else:
        print("\n❌ Minimal authentication test FAILED!")
        sys.exit(1)