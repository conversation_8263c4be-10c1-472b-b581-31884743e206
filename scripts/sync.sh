#!/usr/bin/env bash

# This script synchronizes your virtual environment to exactly match the
# project's lock files (requirements.txt and requirements-dev.txt).
# It's the standard command to run after pulling new changes from git.

set -e # Exit immediately if a command fails

# Get the project root directory (the parent of this script's directory)
PROJECT_ROOT="$( cd "$( dirname "${BASH_SOURCE[0]}" )/.." &> /dev/null && pwd )"

echo "🔄 Syncing Python environment from lock files..."
cd "$PROJECT_ROOT"

# Use uv to make the venv match the lock files exactly.
# This adds missing packages and removes ones that don't belong.
uv pip sync requirements.txt requirements-dev.txt

echo "✅ Environment is up to date."