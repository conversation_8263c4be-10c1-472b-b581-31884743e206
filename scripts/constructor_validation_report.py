#!/usr/bin/env python3
"""
Comprehensive report on service constructor parameter usage in the codebase.
"""

import re
import os
from pathlib import Path

def analyze_service_constructors():
    """Analyze all service constructor calls in the codebase."""
    src_path = Path("/Users/<USER>/PycharmProjects/lexgenius/src")
    service_calls = []
    
    service_pattern = r'(\w*Service)\s*\('
    
    for py_file in src_path.rglob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                matches = re.findall(service_pattern, line)
                if matches and not line.strip().startswith('#') and not 'def ' in line and not 'class ' in line:
                    service_calls.append({
                        'file': str(py_file.relative_to(src_path.parent)),
                        'line': line_num,
                        'content': line.strip(),
                        'service': matches[0]
                    })
                    
        except Exception as e:
            print(f"Error reading {py_file}: {e}")
    
    return service_calls

def main():
    print("📊 Service Constructor Parameter Analysis Report")
    print("=" * 60)
    
    service_calls = analyze_service_constructors()
    
    print(f"Found {len(service_calls)} service constructor calls:")
    print()
    
    # Group by service type
    by_service = {}
    for call in service_calls:
        service = call['service']
        if service not in by_service:
            by_service[service] = []
        by_service[service].append(call)
    
    correct_count = 0
    incorrect_count = 0
    
    for service, calls in sorted(by_service.items()):
        print(f"📦 {service}:")
        for call in calls:
            # Check parameter usage
            if 'logger=' in call['content'] and 'court_logger=court_logger' not in call['content']:
                status = "✅"
                correct_count += 1
            elif 'court_logger=court_logger' in call['content'] and service in ['RelevanceService', 'ConfigurationService']:
                status = "❌"
                incorrect_count += 1  
            else:
                status = "ℹ️"
                
            print(f"  {status} {call['file']}:{call['line']}")
            print(f"     {call['content']}")
            print()
    
    print("📈 Summary:")
    print(f"  ✅ Correct constructor calls: {correct_count}")
    print(f"  ❌ Incorrect constructor calls: {incorrect_count}")
    print(f"  📊 Total analyzed: {len(service_calls)}")
    
    if incorrect_count == 0:
        print("\n🎉 All service constructors are using correct parameter names!")
        print("   Services are properly using logger= instead of court_logger= for their main logger parameter.")
    else:
        print(f"\n⚠️  Found {incorrect_count} service constructor calls with incorrect parameters.")
        print("   These should be fixed to use logger= instead of court_logger= for the main logger parameter.")
    
    return incorrect_count == 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)