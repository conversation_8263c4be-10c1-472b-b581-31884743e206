
import sys
import importlib.util
from pathlib import Path

def test_import(module_path):
    try:
        spec = importlib.util.find_spec(module_path)
        return spec is not None
    except Exception:
        return False

# Test key PACER modules
test_modules = [
    'src.pacer.core.base_service',
    'src.pacer.core.interfaces',
    'src.pacer.services.browser_service',
    'src.pacer.services.scraper_service',
    'src.pacer.orchestrator'
]

failed_imports = []
for module in test_modules:
    if not test_import(module):
        failed_imports.append(module)
        print(f"❌ Failed: {module}")
    else:
        print(f"✅ Success: {module}")

if failed_imports:
    print(f"\n⚠️ {len(failed_imports)} imports failed verification")
    sys.exit(1)
else:
    print("\n🎉 All imports verified successfully!")
    sys.exit(0)
