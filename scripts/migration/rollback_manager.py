#!/usr/bin/env python3
"""
PACER Migration Rollback Manager
Handles rollback procedures for Phase 6 migration with different strategies
"""

import json
import logging
import shutil
import subprocess
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import argparse
import os


class RollbackManager:
    """Manages rollback procedures for PACER migration"""
    
    def __init__(self, project_root: Path, config_path: Optional[Path] = None):
        self.project_root = project_root
        self.config_path = config_path or project_root / "config" / "migration_config.json"
        self.logger = logging.getLogger(__name__)
        self.backup_root = project_root / "_legacy"
        
        # Load configuration
        self.config = self._load_config()
        
        # Rollback strategies
        self.strategies = {
            'immediate': self._immediate_rollback,
            'gradual': self._gradual_rollback,
            'service_specific': self._service_specific_rollback,
            'emergency': self._emergency_rollback
        }
    
    def _load_config(self) -> Dict:
        """Load rollback configuration"""
        default_config = {
            'backup_retention_days': 30,
            'rollback_timeout_seconds': 300,
            'health_check_timeout': 60,
            'feature_flags_path': 'config/migration_flags.json',
            'emergency_contacts': [],
            'rollback_order': [
                'download_orchestration',
                'case_processing', 
                'browser',
                'authentication',
                'file_operations',
                'verification',
                'metrics_reporting',
                'configuration'
            ]
        }
        
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                    # Merge with defaults
                    default_config.update(config)
            except Exception as e:
                self.logger.warning(f"Failed to load config, using defaults: {e}")
        
        return default_config
    
    def create_rollback_point(self, name: str, services: List[str] = None) -> str:
        """Create a rollback point before migration"""
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        rollback_id = f"{name}_{timestamp}"
        rollback_path = self.backup_root / rollback_id
        
        self.logger.info(f"Creating rollback point: {rollback_id}")
        
        try:
            rollback_path.mkdir(parents=True, exist_ok=True)
            
            # Backup service files
            if services:
                for service in services:
                    self._backup_service(service, rollback_path)
            else:
                self._backup_all_services(rollback_path)
            
            # Backup configuration
            self._backup_configuration(rollback_path)
            
            # Create metadata
            metadata = {
                'rollback_id': rollback_id,
                'created_at': datetime.utcnow().isoformat(),
                'services': services or ['all'],
                'project_root': str(self.project_root),
                'git_commit': self._get_current_git_commit(),
                'feature_flags': self._get_current_feature_flags()
            }
            
            metadata_file = rollback_path / "rollback_metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            self.logger.info(f"Rollback point created: {rollback_path}")
            return rollback_id
            
        except Exception as e:
            self.logger.error(f"Failed to create rollback point: {e}")
            raise
    
    def _backup_service(self, service_name: str, backup_path: Path):
        """Backup a specific service"""
        service_path = self.project_root / "src" / "services" / "pacer"
        backup_service_path = backup_path / "services" / "pacer"
        backup_service_path.mkdir(parents=True, exist_ok=True)
        
        # Backup facade service files
        facade_patterns = [
            f"{service_name}_facade_service.py",
            f"{service_name}*facade*service.py"
        ]
        
        for pattern in facade_patterns:
            for file_path in service_path.glob(pattern):
                shutil.copy2(file_path, backup_service_path)
                self.logger.debug(f"Backed up: {file_path}")
    
    def _backup_all_services(self, backup_path: Path):
        """Backup all PACER services"""
        src_path = self.project_root / "src" / "services" / "pacer"
        dst_path = backup_path / "services" / "pacer"
        
        if src_path.exists():
            shutil.copytree(src_path, dst_path, dirs_exist_ok=True)
            self.logger.info(f"Backed up all services to: {dst_path}")
    
    def _backup_configuration(self, backup_path: Path):
        """Backup configuration files"""
        config_paths = [
            self.project_root / "config",
            self.project_root / "src" / "containers",
            self.project_root / "src" / "main.py",
            self.project_root / "src" / "cli",
        ]
        
        for config_path in config_paths:
            if config_path.exists():
                dst_path = backup_path / config_path.relative_to(self.project_root)
                if config_path.is_file():
                    dst_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(config_path, dst_path)
                else:
                    shutil.copytree(config_path, dst_path, dirs_exist_ok=True)
    
    def _get_current_git_commit(self) -> Optional[str]:
        """Get current git commit hash"""
        try:
            result = subprocess.run(
                ['git', 'rev-parse', 'HEAD'],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.stdout.strip() if result.returncode == 0 else None
        except Exception as e:
            self.logger.warning(f"Failed to get git commit: {e}")
            return None
    
    def _get_current_feature_flags(self) -> Dict:
        """Get current feature flag states"""
        flags_path = self.project_root / self.config['feature_flags_path']
        if flags_path.exists():
            try:
                with open(flags_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"Failed to read feature flags: {e}")
        return {}
    
    def execute_rollback(
        self, 
        rollback_id: str, 
        strategy: str = 'immediate',
        services: Optional[List[str]] = None
    ) -> bool:
        """Execute rollback using specified strategy"""
        self.logger.info(f"Starting rollback: {rollback_id} with strategy: {strategy}")
        
        # Validate rollback point exists
        rollback_path = self.backup_root / rollback_id
        if not rollback_path.exists():
            raise ValueError(f"Rollback point not found: {rollback_id}")
        
        # Load rollback metadata
        metadata = self._load_rollback_metadata(rollback_path)
        
        # Execute strategy
        strategy_func = self.strategies.get(strategy)
        if not strategy_func:
            raise ValueError(f"Unknown rollback strategy: {strategy}")
        
        try:
            success = strategy_func(rollback_path, metadata, services)
            
            if success:
                self.logger.info(f"Rollback completed successfully: {rollback_id}")
                self._log_rollback_event(rollback_id, strategy, "SUCCESS")
            else:
                self.logger.error(f"Rollback failed: {rollback_id}")
                self._log_rollback_event(rollback_id, strategy, "FAILED")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Rollback error: {e}")
            self._log_rollback_event(rollback_id, strategy, "ERROR", str(e))
            raise
    
    def _load_rollback_metadata(self, rollback_path: Path) -> Dict:
        """Load rollback metadata"""
        metadata_file = rollback_path / "rollback_metadata.json"
        if not metadata_file.exists():
            raise ValueError(f"Rollback metadata not found: {metadata_file}")
        
        with open(metadata_file, 'r') as f:
            return json.load(f)
    
    def _immediate_rollback(
        self, 
        rollback_path: Path, 
        metadata: Dict, 
        services: Optional[List[str]]
    ) -> bool:
        """Execute immediate rollback (fastest, stops all services)"""
        self.logger.info("Executing immediate rollback")
        
        steps = [
            ("Stop services", self._stop_services),
            ("Disable feature flags", self._disable_all_feature_flags),
            ("Restore files", lambda: self._restore_files(rollback_path, services)),
            ("Restore configuration", lambda: self._restore_configuration(rollback_path)),
            ("Start services", self._start_services),
            ("Validate rollback", self._validate_rollback)
        ]
        
        return self._execute_rollback_steps(steps)
    
    def _gradual_rollback(
        self, 
        rollback_path: Path, 
        metadata: Dict, 
        services: Optional[List[str]]
    ) -> bool:
        """Execute gradual rollback (slower, maintains availability)"""
        self.logger.info("Executing gradual rollback")
        
        # Get services to rollback
        target_services = services or metadata.get('services', ['all'])
        if 'all' in target_services:
            target_services = self.config['rollback_order']
        
        # Rollback services in reverse order
        for service in reversed(target_services):
            self.logger.info(f"Rolling back service: {service}")
            
            try:
                # Disable feature flag for this service
                self._disable_service_feature_flag(service)
                
                # Wait for traffic to drain
                time.sleep(5)
                
                # Restore service files
                self._restore_service_files(service, rollback_path)
                
                # Health check
                if not self._health_check_service(service):
                    self.logger.error(f"Health check failed for {service}")
                    return False
                
                self.logger.info(f"Successfully rolled back: {service}")
                
            except Exception as e:
                self.logger.error(f"Failed to rollback {service}: {e}")
                return False
        
        return True
    
    def _service_specific_rollback(
        self, 
        rollback_path: Path, 
        metadata: Dict, 
        services: Optional[List[str]]
    ) -> bool:
        """Execute service-specific rollback"""
        if not services:
            raise ValueError("Service-specific rollback requires service list")
        
        self.logger.info(f"Executing service-specific rollback for: {services}")
        
        for service in services:
            self.logger.info(f"Rolling back service: {service}")
            
            # Disable service
            self._disable_service_feature_flag(service)
            
            # Restore service files
            self._restore_service_files(service, rollback_path)
            
            # Validate service
            if not self._health_check_service(service):
                self.logger.error(f"Service rollback validation failed: {service}")
                return False
        
        return True
    
    def _emergency_rollback(
        self, 
        rollback_path: Path, 
        metadata: Dict, 
        services: Optional[List[str]]
    ) -> bool:
        """Execute emergency rollback (nuclear option)"""
        self.logger.critical("Executing EMERGENCY rollback")
        
        # Notify emergency contacts
        self._notify_emergency_contacts("EMERGENCY ROLLBACK IN PROGRESS")
        
        try:
            # Force stop everything
            self._force_stop_services()
            
            # Restore from git if possible
            git_commit = metadata.get('git_commit')
            if git_commit:
                self.logger.info(f"Attempting git reset to: {git_commit}")
                result = subprocess.run(
                    ['git', 'reset', '--hard', git_commit],
                    cwd=self.project_root,
                    capture_output=True,
                    timeout=60
                )
                if result.returncode == 0:
                    self.logger.info("Git reset successful")
                else:
                    self.logger.warning("Git reset failed, using file restore")
                    self._restore_files(rollback_path, services)
            else:
                self._restore_files(rollback_path, services)
            
            # Disable all feature flags
            self._disable_all_feature_flags()
            
            # Restart services
            self._start_services()
            
            # Extended health check
            time.sleep(30)  # Give services time to start
            if not self._validate_rollback():
                self.logger.critical("Emergency rollback validation failed")
                return False
            
            self._notify_emergency_contacts("EMERGENCY ROLLBACK COMPLETED")
            return True
            
        except Exception as e:
            self.logger.critical(f"Emergency rollback failed: {e}")
            self._notify_emergency_contacts(f"EMERGENCY ROLLBACK FAILED: {e}")
            raise
    
    def _execute_rollback_steps(self, steps: List[Tuple[str, callable]]) -> bool:
        """Execute a list of rollback steps"""
        for step_name, step_func in steps:
            self.logger.info(f"Executing: {step_name}")
            try:
                result = step_func()
                if result is False:  # Explicit False check
                    self.logger.error(f"Step failed: {step_name}")
                    return False
                self.logger.info(f"Step completed: {step_name}")
            except Exception as e:
                self.logger.error(f"Step error {step_name}: {e}")
                return False
        
        return True
    
    def _stop_services(self):
        """Stop PACER services"""
        self.logger.info("Stopping services")
        # This would integrate with your service management system
        # For now, just log the action
        return True
    
    def _start_services(self):
        """Start PACER services"""
        self.logger.info("Starting services")
        # This would integrate with your service management system
        return True
    
    def _force_stop_services(self):
        """Force stop all services"""
        self.logger.warning("Force stopping all services")
        # This would use more aggressive service shutdown
        return True
    
    def _disable_all_feature_flags(self):
        """Disable all migration feature flags"""
        flags_path = self.project_root / self.config['feature_flags_path']
        flags_path.parent.mkdir(parents=True, exist_ok=True)
        
        disabled_flags = {
            'use_new_authentication': False,
            'use_new_browser': False,
            'use_new_case_processing': False,
            'use_new_configuration': False,
            'use_new_download_orchestration': False,
            'use_new_file_operations': False,
            'use_new_metrics_reporting': False,
            'use_new_verification': False,
        }
        
        with open(flags_path, 'w') as f:
            json.dump(disabled_flags, f, indent=2)
        
        self.logger.info("Disabled all feature flags")
    
    def _disable_service_feature_flag(self, service: str):
        """Disable feature flag for specific service"""
        flags_path = self.project_root / self.config['feature_flags_path']
        
        flags = {}
        if flags_path.exists():
            with open(flags_path, 'r') as f:
                flags = json.load(f)
        
        flag_name = f'use_new_{service}'
        flags[flag_name] = False
        
        with open(flags_path, 'w') as f:
            json.dump(flags, f, indent=2)
        
        self.logger.info(f"Disabled feature flag: {flag_name}")
    
    def _restore_files(self, rollback_path: Path, services: Optional[List[str]]):
        """Restore files from rollback point"""
        backup_services_path = rollback_path / "services"
        target_services_path = self.project_root / "src" / "services"
        
        if backup_services_path.exists():
            # Remove current services
            if target_services_path.exists():
                shutil.rmtree(target_services_path)
            
            # Restore from backup
            shutil.copytree(backup_services_path, target_services_path)
            self.logger.info("Restored service files")
    
    def _restore_service_files(self, service: str, rollback_path: Path):
        """Restore files for a specific service"""
        # This would restore specific service files
        self.logger.info(f"Restoring files for service: {service}")
    
    def _restore_configuration(self, rollback_path: Path):
        """Restore configuration files"""
        config_backup = rollback_path / "config"
        if config_backup.exists():
            target_config = self.project_root / "config"
            if target_config.exists():
                shutil.rmtree(target_config)
            shutil.copytree(config_backup, target_config)
            self.logger.info("Restored configuration")
    
    def _health_check_service(self, service: str) -> bool:
        """Perform health check on service"""
        self.logger.info(f"Health checking service: {service}")
        # This would perform actual health checks
        # For now, simulate success
        time.sleep(2)
        return True
    
    def _validate_rollback(self) -> bool:
        """Validate that rollback was successful"""
        self.logger.info("Validating rollback")
        
        # Check that services are responding
        services_to_check = ['authentication', 'browser', 'case_processing']
        
        for service in services_to_check:
            if not self._health_check_service(service):
                return False
        
        self.logger.info("Rollback validation successful")
        return True
    
    def _notify_emergency_contacts(self, message: str):
        """Notify emergency contacts"""
        contacts = self.config.get('emergency_contacts', [])
        if contacts:
            self.logger.critical(f"EMERGENCY NOTIFICATION: {message}")
            # This would send actual notifications (email, Slack, PagerDuty, etc.)
    
    def _log_rollback_event(self, rollback_id: str, strategy: str, status: str, error: str = None):
        """Log rollback event for auditing"""
        event = {
            'timestamp': datetime.utcnow().isoformat(),
            'rollback_id': rollback_id,
            'strategy': strategy,
            'status': status,
            'error': error
        }
        
        log_file = self.project_root / "logs" / "rollback_events.json"
        log_file.parent.mkdir(exist_ok=True)
        
        # Append to log file
        events = []
        if log_file.exists():
            try:
                with open(log_file, 'r') as f:
                    events = json.load(f)
            except:
                events = []
        
        events.append(event)
        
        with open(log_file, 'w') as f:
            json.dump(events, f, indent=2)
    
    def list_rollback_points(self) -> List[Dict]:
        """List available rollback points"""
        rollback_points = []
        
        if not self.backup_root.exists():
            return rollback_points
        
        for rollback_dir in self.backup_root.iterdir():
            if rollback_dir.is_dir():
                metadata_file = rollback_dir / "rollback_metadata.json"
                if metadata_file.exists():
                    try:
                        with open(metadata_file, 'r') as f:
                            metadata = json.load(f)
                            rollback_points.append(metadata)
                    except Exception as e:
                        self.logger.warning(f"Failed to read metadata for {rollback_dir}: {e}")
        
        # Sort by creation time
        rollback_points.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        return rollback_points
    
    def cleanup_old_rollback_points(self):
        """Clean up old rollback points"""
        cutoff_date = datetime.utcnow() - timedelta(days=self.config['backup_retention_days'])
        
        for rollback_point in self.list_rollback_points():
            created_at = datetime.fromisoformat(rollback_point['created_at'].replace('Z', '+00:00'))
            if created_at < cutoff_date:
                rollback_path = self.backup_root / rollback_point['rollback_id']
                if rollback_path.exists():
                    shutil.rmtree(rollback_path)
                    self.logger.info(f"Cleaned up old rollback point: {rollback_point['rollback_id']}")


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description="PACER Rollback Manager")
    parser.add_argument('--project-root', type=Path, default=Path.cwd(),
                       help='Project root directory')
    parser.add_argument('--config', type=Path,
                       help='Configuration file path')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Create rollback point
    create_parser = subparsers.add_parser('create', help='Create rollback point')
    create_parser.add_argument('name', help='Rollback point name')
    create_parser.add_argument('--services', nargs='*', help='Specific services to backup')
    
    # Execute rollback
    rollback_parser = subparsers.add_parser('rollback', help='Execute rollback')
    rollback_parser.add_argument('rollback_id', help='Rollback point ID')
    rollback_parser.add_argument('--strategy', default='immediate',
                                choices=['immediate', 'gradual', 'service_specific', 'emergency'],
                                help='Rollback strategy')
    rollback_parser.add_argument('--services', nargs='*', help='Specific services to rollback')
    
    # List rollback points
    subparsers.add_parser('list', help='List available rollback points')
    
    # Cleanup old rollback points
    subparsers.add_parser('cleanup', help='Clean up old rollback points')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        manager = RollbackManager(args.project_root, args.config)
        
        if args.command == 'create':
            rollback_id = manager.create_rollback_point(args.name, args.services)
            print(f"Created rollback point: {rollback_id}")
        
        elif args.command == 'rollback':
            success = manager.execute_rollback(args.rollback_id, args.strategy, args.services)
            if success:
                print(f"Rollback completed successfully: {args.rollback_id}")
                return 0
            else:
                print(f"Rollback failed: {args.rollback_id}")
                return 1
        
        elif args.command == 'list':
            rollback_points = manager.list_rollback_points()
            if rollback_points:
                print("Available rollback points:")
                for rp in rollback_points:
                    print(f"  {rp['rollback_id']} - {rp['created_at']} - Services: {rp['services']}")
            else:
                print("No rollback points found")
        
        elif args.command == 'cleanup':
            manager.cleanup_old_rollback_points()
            print("Cleanup completed")
        
        return 0
        
    except Exception as e:
        logging.error(f"Command failed: {e}")
        return 1


if __name__ == '__main__':
    exit(main())