#!/usr/bin/env python3
"""
Import Migration Tool for PACER Phase 6 Refactoring
Automatically updates import statements from facade services to core services
"""

import ast
import os
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
import argparse
import json
from dataclasses import dataclass


@dataclass
class ImportChange:
    """Represents a single import change"""
    file_path: Path
    line_number: int
    old_import: str
    new_import: str
    import_type: str  # 'from', 'import', 'from_as'


class ImportMigrator:
    """Handles migration of import statements from facades to core services"""
    
    # Mapping from facade services to new core services
    FACADE_TO_CORE_MAPPING = {
        # Authentication
        'src.pacer.authentication_facade_service': 'src.pacer._core_services.authentication',
        'authentication_facade_service': '_core_services.authentication',
        'AuthenticationFacadeService': 'AuthenticationService',
        
        # Browser
        'src.pacer.browser_facade_service': 'src.pacer._core_services.browser',
        'browser_facade_service': '_core_services.browser',
        'BrowserFacadeService': 'BrowserService',
        
        # Case Processing (multiple facades consolidated)
        'src.pacer.case_processing_facade_service': 'src.pacer._core_services.case_processing',
        'src.pacer.case_classification_facade_service': 'src.pacer._core_services.case_processing',
        'src.pacer.court_processing_facade_service': 'src.pacer._core_services.case_processing',
        'src.pacer.html_processing_facade_service': 'src.pacer._core_services.case_processing',
        'src.pacer.query_facade_service': 'src.pacer._core_services.case_processing',
        'src.pacer.row_processing_facade_service': 'src.pacer._core_services.case_processing',
        'src.pacer.transfer_facade_service': 'src.pacer._core_services.case_processing',
        'case_processing_facade_service': '_core_services.case_processing',
        'case_classification_facade_service': '_core_services.case_processing',
        'court_processing_facade_service': '_core_services.case_processing',
        'html_processing_facade_service': '_core_services.case_processing',
        'query_facade_service': '_core_services.case_processing',
        'row_processing_facade_service': '_core_services.case_processing',
        'transfer_facade_service': '_core_services.case_processing',
        
        # Class name mappings
        'CaseProcessingFacadeService': 'CaseProcessingService',
        'CaseClassificationFacadeService': 'CaseProcessingService',
        'CourtProcessingFacadeService': 'CaseProcessingService',
        'HtmlProcessingFacadeService': 'CaseProcessingService',
        'QueryFacadeService': 'CaseProcessingService',
        'RowProcessingFacadeService': 'CaseProcessingService',
        'TransferFacadeService': 'CaseProcessingService',
        
        # Configuration
        'src.pacer.configuration_facade_service': 'src.pacer._core_services.configuration',
        'src.pacer.interactive_service': 'src.pacer._core_services.configuration',
        'configuration_facade_service': '_core_services.configuration',
        'interactive_service': '_core_services.configuration',
        'ConfigurationFacadeService': 'ConfigurationService',
        'PacerInteractiveService': 'ConfigurationService',
        
        # Download Orchestration (multiple facades consolidated)
        'src.pacer.docket_orchestrator_facade_service': 'src.pacer._core_services.download_orchestration',
        'src.pacer.download_orchestration_facade_service': 'src.pacer._core_services.download_orchestration',
        'src.pacer.ignore_download_facade_service': 'src.pacer._core_services.download_orchestration',
        'src.pacer.pacer_orchestrator_service': 'src.pacer._core_services.download_orchestration',
        'docket_orchestrator_facade_service': '_core_services.download_orchestration',
        'download_orchestration_facade_service': '_core_services.download_orchestration',
        'ignore_download_facade_service': '_core_services.download_orchestration',
        'pacer_orchestrator_service': '_core_services.download_orchestration',
        'DocketOrchestratorFacadeService': 'DownloadOrchestrationService',
        'DownloadOrchestrationFacadeService': 'DownloadOrchestrationService',
        'IgnoreDownloadFacadeService': 'DownloadOrchestrationService',
        'PacerOrchestratorService': 'DownloadOrchestrationService',
        
        # File Operations
        'src.pacer.file_management_facade_service': 'src.pacer._core_services.file_operations',
        'src.pacer.export_facade_service': 'src.pacer._core_services.file_operations',
        'file_management_facade_service': '_core_services.file_operations',
        'export_facade_service': '_core_services.file_operations',
        'FileManagementFacadeService': 'FileOperationsService',
        'ExportFacadeService': 'FileOperationsService',
        
        # Metrics and Reporting
        'src.pacer.analytics_facade_service': 'src.pacer._core_services.metrics_reporting',
        'src.pacer.report_facade_service': 'src.pacer._core_services.metrics_reporting',
        'analytics_facade_service': '_core_services.metrics_reporting',
        'report_facade_service': '_core_services.metrics_reporting',
        'AnalyticsFacadeService': 'MetricsReportingService',
        'ReportFacadeService': 'MetricsReportingService',
        
        # Verification
        'src.pacer.case_verification_facade_service': 'src.pacer._core_services.verification',
        'src.pacer.relevance_facade_service': 'src.pacer._core_services.verification',
        'case_verification_facade_service': '_core_services.verification',
        'relevance_facade_service': '_core_services.verification',
        'CaseVerificationFacadeService': 'VerificationService',
        'RelevanceFacadeService': 'VerificationService',
        
        # Navigation (consolidated into browser)
        'src.pacer.navigation_facade_service': 'src.pacer._core_services.browser',
        'navigation_facade_service': '_core_services.browser',
        'NavigationFacadeService': 'BrowserService',
    }
    
    # Patterns for different import styles
    IMPORT_PATTERNS = [
        # from module import class
        (r'from\s+([^\s]+)\s+import\s+([^,\n]+)', 'from_import'),
        # import module
        (r'import\s+([^\s,\n]+)', 'import'),
        # from module import class as alias
        (r'from\s+([^\s]+)\s+import\s+([^\s]+)\s+as\s+([^\s,\n]+)', 'from_import_as'),
    ]
    
    def __init__(self, project_root: Path, dry_run: bool = True):
        self.project_root = project_root
        self.dry_run = dry_run
        self.changes: List[ImportChange] = []
        self.logger = logging.getLogger(__name__)
        self.stats = {
            'files_processed': 0,
            'files_changed': 0,
            'imports_updated': 0,
            'errors': 0
        }
    
    def migrate_codebase(self, target_paths: Optional[List[str]] = None) -> List[ImportChange]:
        """Migrate all import statements in the codebase"""
        self.logger.info(f"Starting import migration {'(DRY RUN)' if self.dry_run else ''}")
        
        # Get all Python files to process
        if target_paths:
            python_files = []
            for target in target_paths:
                target_path = self.project_root / target
                if target_path.is_file() and target_path.suffix == '.py':
                    python_files.append(target_path)
                elif target_path.is_dir():
                    python_files.extend(target_path.rglob('*.py'))
        else:
            python_files = list(self.project_root.rglob('*.py'))
        
        # Filter out certain directories
        excluded_dirs = {'.git', '__pycache__', '.pytest_cache', 'venv', '.venv', 'node_modules'}
        python_files = [
            f for f in python_files 
            if not any(excluded in f.parts for excluded in excluded_dirs)
        ]
        
        self.logger.info(f"Processing {len(python_files)} Python files")
        
        for file_path in python_files:
            try:
                self._migrate_file_imports(file_path)
                self.stats['files_processed'] += 1
            except Exception as e:
                self.logger.error(f"Error processing {file_path}: {e}")
                self.stats['errors'] += 1
        
        self._log_summary()
        return self.changes
    
    def _migrate_file_imports(self, file_path: Path):
        """Migrate imports in a single file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
        except UnicodeDecodeError:
            self.logger.warning(f"Skipping binary file: {file_path}")
            return
        
        modified_content = original_content
        file_changes = []
        
        lines = original_content.split('\n')
        for line_num, line in enumerate(lines, 1):
            line_changes = self._process_line_imports(line, file_path, line_num)
            file_changes.extend(line_changes)
            
            # Apply changes to content
            for change in line_changes:
                modified_content = modified_content.replace(change.old_import, change.new_import)
        
        # Write changes if not dry run and there were changes
        if file_changes and not self.dry_run:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                self.logger.info(f"Updated {len(file_changes)} imports in {file_path}")
                self.stats['files_changed'] += 1
            except Exception as e:
                self.logger.error(f"Failed to write changes to {file_path}: {e}")
                self.stats['errors'] += 1
        
        self.changes.extend(file_changes)
        self.stats['imports_updated'] += len(file_changes)
    
    def _process_line_imports(self, line: str, file_path: Path, line_num: int) -> List[ImportChange]:
        """Process imports in a single line"""
        changes = []
        line = line.strip()
        
        if not line or line.startswith('#'):
            return changes
        
        # Check each import pattern
        for pattern, import_type in self.IMPORT_PATTERNS:
            matches = re.finditer(pattern, line)
            for match in matches:
                change = self._create_import_change(
                    match, import_type, line, file_path, line_num
                )
                if change:
                    changes.append(change)
        
        return changes
    
    def _create_import_change(
        self, 
        match: re.Match, 
        import_type: str, 
        line: str, 
        file_path: Path, 
        line_num: int
    ) -> Optional[ImportChange]:
        """Create an ImportChange object if migration is needed"""
        
        if import_type == 'from_import':
            module_name = match.group(1)
            imported_items = match.group(2)
            
            # Check if module needs migration
            new_module = self.FACADE_TO_CORE_MAPPING.get(module_name)
            if new_module:
                # Handle class name changes
                new_items = imported_items
                for old_class, new_class in self.FACADE_TO_CORE_MAPPING.items():
                    if old_class in imported_items and old_class.endswith('Service'):
                        new_items = new_items.replace(old_class, new_class)
                
                old_import = f"from {module_name} import {imported_items}"
                new_import = f"from {new_module} import {new_items}"
                
                return ImportChange(
                    file_path=file_path,
                    line_number=line_num,
                    old_import=old_import,
                    new_import=new_import,
                    import_type='from_import'
                )
        
        elif import_type == 'import':
            module_name = match.group(1)
            new_module = self.FACADE_TO_CORE_MAPPING.get(module_name)
            if new_module:
                old_import = f"import {module_name}"
                new_import = f"import {new_module}"
                
                return ImportChange(
                    file_path=file_path,
                    line_number=line_num,
                    old_import=old_import,
                    new_import=new_import,
                    import_type='import'
                )
        
        elif import_type == 'from_import_as':
            module_name = match.group(1)
            class_name = match.group(2)
            alias = match.group(3)
            
            new_module = self.FACADE_TO_CORE_MAPPING.get(module_name)
            new_class = self.FACADE_TO_CORE_MAPPING.get(class_name, class_name)
            
            if new_module or new_class != class_name:
                final_module = new_module or module_name
                old_import = f"from {module_name} import {class_name} as {alias}"
                new_import = f"from {final_module} import {new_class} as {alias}"
                
                return ImportChange(
                    file_path=file_path,
                    line_number=line_num,
                    old_import=old_import,
                    new_import=new_import,
                    import_type='from_import_as'
                )
        
        return None
    
    def _log_summary(self):
        """Log migration summary"""
        self.logger.info("Import migration summary:")
        self.logger.info(f"  Files processed: {self.stats['files_processed']}")
        self.logger.info(f"  Files changed: {self.stats['files_changed']}")
        self.logger.info(f"  Imports updated: {self.stats['imports_updated']}")
        self.logger.info(f"  Errors: {self.stats['errors']}")
        
        if self.dry_run:
            self.logger.info("DRY RUN: No files were actually modified")
    
    def generate_report(self, output_file: Optional[Path] = None) -> Dict:
        """Generate a detailed migration report"""
        report = {
            'migration_summary': self.stats,
            'changes_by_file': {},
            'changes_by_type': {},
            'facade_usage_count': {}
        }
        
        # Group changes by file
        for change in self.changes:
            file_key = str(change.file_path.relative_to(self.project_root))
            if file_key not in report['changes_by_file']:
                report['changes_by_file'][file_key] = []
            
            report['changes_by_file'][file_key].append({
                'line_number': change.line_number,
                'old_import': change.old_import,
                'new_import': change.new_import,
                'type': change.import_type
            })
        
        # Group changes by type
        for change in self.changes:
            import_type = change.import_type
            if import_type not in report['changes_by_type']:
                report['changes_by_type'][import_type] = 0
            report['changes_by_type'][import_type] += 1
        
        # Count facade usage
        for change in self.changes:
            for old_facade in self.FACADE_TO_CORE_MAPPING:
                if old_facade in change.old_import:
                    if old_facade not in report['facade_usage_count']:
                        report['facade_usage_count'][old_facade] = 0
                    report['facade_usage_count'][old_facade] += 1
        
        # Save report if requested
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            self.logger.info(f"Migration report saved to {output_file}")
        
        return report
    
    def validate_imports(self) -> bool:
        """Validate that all imports are still valid after migration"""
        self.logger.info("Validating post-migration imports...")
        
        # Try to import the main pacer module
        try:
            import subprocess
            result = subprocess.run([
                'python', '-c', 
                '''
import sys
sys.path.insert(0, "src")
try:
    import src.pacer
    print("SUCCESS: All PACER imports validated")
except ImportError as e:
    print(f"FAILED: Import validation failed: {e}")
    sys.exit(1)
                '''
            ], cwd=str(self.project_root), capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.logger.info("Import validation successful")
                return True
            else:
                self.logger.error(f"Import validation failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.logger.error("Import validation timed out")
            return False
        except Exception as e:
            self.logger.error(f"Import validation error: {e}")
            return False


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description="PACER Import Migration Tool")
    parser.add_argument('--project-root', type=Path, default=Path.cwd(),
                       help='Project root directory')
    parser.add_argument('--target-paths', nargs='*',
                       help='Specific paths to migrate (relative to project root)')
    parser.add_argument('--dry-run', action='store_true', default=False,
                       help='Show what would be changed without making changes')
    parser.add_argument('--report', type=Path,
                       help='Generate detailed report file')
    parser.add_argument('--validate', action='store_true',
                       help='Validate imports after migration')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.DEBUG if args.verbose else logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # Validate project root
    if not args.project_root.exists():
        logger.error(f"Project root does not exist: {args.project_root}")
        return 1
    
    # Create migrator
    migrator = ImportMigrator(args.project_root, dry_run=args.dry_run)
    
    try:
        # Run migration
        changes = migrator.migrate_codebase(args.target_paths)
        
        if changes:
            logger.info(f"Migration completed with {len(changes)} changes")
        else:
            logger.info("No imports needed migration")
        
        # Generate report if requested
        if args.report:
            report = migrator.generate_report(args.report)
        
        # Validate imports if requested
        if args.validate and not args.dry_run:
            if not migrator.validate_imports():
                logger.error("Import validation failed")
                return 1
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Migration interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        return 1


if __name__ == '__main__':
    exit(main())