#!/usr/bin/env python3
"""
PACER Import Migration Script
Migrates all imports from src.pacer to src.pacer
CRITICAL PATH: PACER-003
"""
import os
import re
from pathlib import Path
from typing import List, Tuple
import shutil
from datetime import datetime

class PacerImportMigrator:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / f"backups/import_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.files_processed = 0
        self.imports_updated = 0
        self.errors = []
        
    def migrate(self):
        """Execute the complete import migration"""
        print("🚀 Starting PACER Import Migration (CRITICAL PATH)")
        print(f"Project root: {self.project_root}")
        
        # Create backup directory
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        print(f"✅ Backup directory created: {self.backup_dir}")
        
        # Find all Python files in src/, tests/, and scripts/ only
        src_files = list((self.project_root / "src").rglob("*.py")) if (self.project_root / "src").exists() else []
        test_files = list((self.project_root / "tests").rglob("*.py")) if (self.project_root / "tests").exists() else []
        script_files = list((self.project_root / "scripts").rglob("*.py")) if (self.project_root / "scripts").exists() else []
        
        py_files = src_files + test_files + script_files
        print(f"📁 Found {len(py_files)} Python files to process")
        
        # Process each file
        for py_file in py_files:
            # Skip backup directories, node_modules, and venv
            if any(skip in str(py_file) for skip in ["backup", "node_modules", ".venv", "__pycache__"]):
                continue
                
            self._process_file(py_file)
        
        # Print summary
        self._print_summary()
        
        return self.errors == []
    
    def _process_file(self, file_path: Path):
        """Process a single file for import updates"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            original_content = content
            
            # Pattern replacements
            replacements = [
                # Direct imports
                (r'from src\.services\.pacer', 'from src.pacer'),
                (r'import src\.services\.pacer', 'import src.pacer'),
                
                # Relative imports in src/services/pacer files
                (r'from \.\.services\.pacer', 'from ..pacer'),
                (r'from services\.pacer', 'from pacer'),
                
                # String references (configs, dynamic imports)
                (r'"src\.services\.pacer', '"src.pacer'),
                (r"'src\.services\.pacer", "'src.pacer"),
                
                # Component imports
                (r'from src\.services\.pacer\._(\w+)_components', r'from src.pacer.components.\1'),
                (r'from src\.services\.pacer\.(\w+)_facade_service', r'from src.pacer.facades.\1_service'),
                
                # Core services
                (r'from src\.services\.pacer\._core_services', 'from src.pacer._core_services'),
                
                # Jobs
                (r'from src\.services\.pacer\.jobs', 'from src.pacer.jobs'),
            ]
            
            # Apply replacements
            for pattern, replacement in replacements:
                new_content = re.sub(pattern, replacement, content)
                if new_content != content:
                    self.imports_updated += re.subn(pattern, replacement, content)[1]
                    content = new_content
            
            # If content changed, backup and update file
            if content != original_content:
                # Create backup
                backup_path = file_path.with_suffix('.py.bak')
                shutil.copy2(file_path, backup_path)
                
                # Write updated content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                self.files_processed += 1
                print(f"✅ Updated: {file_path.relative_to(self.project_root)}")
                
        except Exception as e:
            self.errors.append((file_path, str(e)))
            print(f"❌ Error processing {file_path}: {e}")
    
    def _print_summary(self):
        """Print migration summary"""
        print("\n" + "="*60)
        print("📊 PACER IMPORT MIGRATION SUMMARY")
        print("="*60)
        print(f"✅ Files processed: {self.files_processed}")
        print(f"✅ Imports updated: {self.imports_updated}")
        print(f"❌ Errors encountered: {len(self.errors)}")
        
        if self.errors:
            print("\n⚠️ ERRORS:")
            for file_path, error in self.errors[:10]:
                print(f"  - {file_path}: {error}")
        
        print("\n🎯 Next steps:")
        print("1. Run tests to verify imports work")
        print("2. Delete src/services/pacer directory")
        print("3. Remove .bak files after verification")
        print("="*60)

def main():
    """Main execution"""
    project_root = "/Users/<USER>/PycharmProjects/lexgenius"
    
    migrator = PacerImportMigrator(project_root)
    success = migrator.migrate()
    
    if success:
        print("\n✅ MIGRATION SUCCESSFUL - CRITICAL PATH CLEARED!")
        print("🚀 You can now safely delete src/services/pacer directory")
    else:
        print("\n❌ MIGRATION ENCOUNTERED ERRORS - Review and fix before proceeding")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())