#!/usr/bin/env python3
"""
Transformer Import Migration Script

This script migrates all transformer imports from:
    src.services.transformer.* -> src.transformer.*
    
Mapping:
    - src.services.transformer._*_components/* -> src.transformer.components.*/
    - src.services.transformer.* -> src.transformer.facades.* (for service files)
    - Registry and factory imports handled properly
"""

import os
import re
import shutil
from pathlib import Path
from typing import Dict, List, Tuple


class TransformerImportMigrator:
    """Handles migration of transformer imports and directory structure."""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.old_path = self.project_root / "src" / "services" / "transformer"
        self.new_path = self.project_root / "src" / "transformer"
        
        # Track files processed for reporting
        self.files_processed = 0
        self.imports_updated = 0
        self.errors = []
        
    def get_import_mappings(self) -> Dict[str, str]:
        """Define import path mappings."""
        mappings = {
            # Component directories
            'src.services.transformer._data_cleaning_components': 'src.transformer.components.data_cleaning',
            'src.services.transformer._data_upload_components': 'src.transformer.components.data_upload',
            'src.services.transformer._case_classification_components': 'src.transformer.components.case_classification',
            'src.services.transformer._mdl_components': 'src.transformer.components.mdl',
            'src.services.transformer._file_components': 'src.transformer.components.file',
            'src.services.transformer._docket_components': 'src.transformer.components.docket',
            'src.services.transformer._law_firm_components': 'src.transformer.components.law_firm',
            
            # Core services become facades
            'src.services.transformer.data_cleaning_service': 'src.transformer.facades.data_cleaning_service',
            'src.services.transformer.data_upload_service': 'src.transformer.facades.data_upload_service',
            'src.services.transformer.case_classification_service': 'src.transformer.facades.case_classification_service',
            'src.services.transformer.mdl_processing_service': 'src.transformer.facades.mdl_processing_service',
            'src.services.transformer.file_processing_service': 'src.transformer.facades.file_processing_service',
            'src.services.transformer.docket_processing_service': 'src.transformer.facades.docket_processing_service',
            'src.services.transformer.law_firm_processing_service': 'src.transformer.facades.law_firm_processing_service',
            
            # Core files stay at root level
            'src.services.transformer.data_transformer': 'src.transformer.data_transformer',
            'src.services.transformer.component_factory': 'src.transformer.component_factory',
            'src.services.transformer.data_processing_engine': 'src.transformer.data_processing_engine',
            'src.services.transformer.error_handler': 'src.transformer.error_handler',
            'src.services.transformer.specialized_workflows': 'src.transformer.specialized_workflows',
            'src.services.transformer.html_integration_service': 'src.transformer.html_integration_service',
            
            # Jobs directory
            'src.services.transformer.jobs': 'src.transformer.jobs',
            
            # Utilities and constants
            'src.services.transformer.constants': 'src.transformer.constants',
            'src.services.transformer.exceptions': 'src.transformer.exceptions',
            'src.services.transformer.validation_utils': 'src.transformer.validation_utils',
            'src.services.transformer.resource_utils': 'src.transformer.resource_utils',
            'src.services.transformer.performance_utils': 'src.transformer.performance_utils',
            'src.services.transformer.config': 'src.transformer.config',
            'src.services.transformer.builders': 'src.transformer.builders',
            
            # Registry files
            'src.services.transformer.action_registry': 'src.transformer.registries.action_registry',
            'src.services.transformer.data_transformer_registry': 'src.transformer.registries.data_transformer_registry',
            'src.services.transformer.component_factory_registry': 'src.transformer.registries.component_factory_registry',
            'src.services.transformer.specialized_workflows_registry': 'src.transformer.registries.specialized_workflows_registry',
            'src.services.transformer.error_handler_registry': 'src.transformer.registries.error_handler_registry',
            'src.services.transformer.config_registry': 'src.transformer.registries.config_registry',
            'src.services.transformer.afff_calculator_registry': 'src.transformer.registries.afff_calculator_registry',
            
            # Specialized components
            'src.services.transformer.afff_calculator': 'src.transformer.afff_calculator',
            'src.services.transformer.resource_managers': 'src.transformer.resource_managers',
            'src.services.transformer.resource_pools': 'src.transformer.resource_pools',
            'src.services.transformer.resource_managed_service': 'src.transformer.resource_managed_service',
            
            # Examples and documentation
            'src.services.transformer.example_decorated_service': 'src.transformer.examples.example_decorated_service',
            'src.services.transformer.lifecycle_example': 'src.transformer.examples.lifecycle_example',
            'src.services.transformer.docstring_example': 'src.transformer.examples.docstring_example',
            
            # Factory
            'src.services.transformer.transformer_factory': 'src.transformer.transformer_factory',
            'src.services.transformer.transformer_orchestrator': 'src.transformer.transformer_orchestrator',
        }
        
        return mappings
    
    def find_python_files(self) -> List[Path]:
        """Find all Python files that might contain transformer imports."""
        python_files = []
        
        # Search in common directories
        search_dirs = [
            self.project_root / "src",
            self.project_root / "tests",
            self.project_root / "scripts",
            self.project_root / "archive",
        ]
        
        # Also check root level test files
        for file in self.project_root.glob("test_*.py"):
            python_files.append(file)
            
        for file in self.project_root.glob("debug_*.py"):
            python_files.append(file)
        
        for search_dir in search_dirs:
            if search_dir.exists():
                python_files.extend(search_dir.rglob("*.py"))
        
        return python_files
    
    def update_imports_in_file(self, file_path: Path) -> int:
        """Update imports in a single file. Returns number of changes made."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            mappings = self.get_import_mappings()
            changes_made = 0
            
            # Sort mappings by length (longest first) to avoid partial replacements
            sorted_mappings = sorted(mappings.items(), key=lambda x: len(x[0]), reverse=True)
            
            for old_import, new_import in sorted_mappings:
                # Pattern for 'from X import Y'
                from_pattern = rf'from {re.escape(old_import)}'
                if re.search(from_pattern, content):
                    content = re.sub(from_pattern, f'from {new_import}', content)
                    changes_made += 1
                
                # Pattern for 'import X'
                import_pattern = rf'import {re.escape(old_import)}'
                if re.search(import_pattern, content):
                    content = re.sub(import_pattern, f'import {new_import}', content)
                    changes_made += 1
            
            # Special handling for services.transformer (without src prefix)
            content = re.sub(r'from services\.transformer', 'from src.transformer', content)
            content = re.sub(r'import services\.transformer', 'import src.transformer', content)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return changes_made
            
            return 0
            
        except Exception as e:
            self.errors.append(f"Error processing {file_path}: {e}")
            return 0
    
    def create_new_directory_structure(self):
        """Create the new transformer directory structure."""
        directories_to_create = [
            self.new_path,
            self.new_path / "components",
            self.new_path / "components" / "data_cleaning",
            self.new_path / "components" / "data_upload", 
            self.new_path / "components" / "case_classification",
            self.new_path / "components" / "mdl",
            self.new_path / "components" / "file",
            self.new_path / "components" / "docket",
            self.new_path / "components" / "law_firm",
            self.new_path / "facades",
            self.new_path / "jobs",
            self.new_path / "registries",
            self.new_path / "examples",
        ]
        
        for directory in directories_to_create:
            directory.mkdir(parents=True, exist_ok=True)
            # Create __init__.py files
            (directory / "__init__.py").touch(exist_ok=True)
    
    def move_files_to_new_structure(self):
        """Move files from old structure to new structure."""
        if not self.old_path.exists():
            print(f"Warning: Source directory {self.old_path} does not exist")
            return
        
        # File movement mappings
        file_mappings = {
            # Component directories
            "_data_cleaning_components": "components/data_cleaning",
            "_data_upload_components": "components/data_upload",
            "_case_classification_components": "components/case_classification",
            "_mdl_components": "components/mdl",
            "_file_components": "components/file",
            "_docket_components": "components/docket",
            "_law_firm_components": "components/law_firm",
            
            # Services to facades
            "data_cleaning_service.py": "facades/data_cleaning_service.py",
            "data_upload_service.py": "facades/data_upload_service.py",
            "case_classification_service.py": "facades/case_classification_service.py",
            "mdl_processing_service.py": "facades/mdl_processing_service.py",
            "file_processing_service.py": "facades/file_processing_service.py",
            "docket_processing_service.py": "facades/docket_processing_service.py",
            "law_firm_processing_service.py": "facades/law_firm_processing_service.py",
            
            # Registry files
            "action_registry.py": "registries/action_registry.py",
            "data_transformer_registry.py": "registries/data_transformer_registry.py",
            "component_factory_registry.py": "registries/component_factory_registry.py",
            "specialized_workflows_registry.py": "registries/specialized_workflows_registry.py",
            "error_handler_registry.py": "registries/error_handler_registry.py",
            "config_registry.py": "registries/config_registry.py",
            "afff_calculator_registry.py": "registries/afff_calculator_registry.py",
            
            # Examples
            "example_decorated_service.py": "examples/example_decorated_service.py",
            "lifecycle_example.py": "examples/lifecycle_example.py",
            "docstring_example.py": "examples/docstring_example.py",
        }
        
        # Move files/directories
        for old_name, new_path in file_mappings.items():
            old_item = self.old_path / old_name
            new_item = self.new_path / new_path
            
            if old_item.exists():
                new_item.parent.mkdir(parents=True, exist_ok=True)
                try:
                    if old_item.is_dir():
                        if new_item.exists():
                            shutil.rmtree(new_item)
                        shutil.move(str(old_item), str(new_item))
                    else:
                        shutil.move(str(old_item), str(new_item))
                    print(f"Moved {old_item} -> {new_item}")
                except Exception as e:
                    self.errors.append(f"Error moving {old_item} to {new_item}: {e}")
        
        # Move remaining root-level files (core components)
        root_level_files = [
            "data_transformer.py", "component_factory.py", "data_processing_engine.py",
            "error_handler.py", "specialized_workflows.py", "html_integration_service.py",
            "constants.py", "exceptions.py", "validation_utils.py", "resource_utils.py",
            "performance_utils.py", "config.py", "builders.py", "afff_calculator.py",
            "resource_managers.py", "resource_pools.py", "resource_managed_service.py",
            "transformer_factory.py", "transformer_orchestrator.py", "__init__.py"
        ]
        
        for file_name in root_level_files:
            old_file = self.old_path / file_name
            new_file = self.new_path / file_name
            
            if old_file.exists():
                try:
                    shutil.move(str(old_file), str(new_file))
                    print(f"Moved {old_file} -> {new_file}")
                except Exception as e:
                    self.errors.append(f"Error moving {old_file} to {new_file}: {e}")
        
        # Move jobs directory
        jobs_dir = self.old_path / "jobs"
        if jobs_dir.exists():
            new_jobs_dir = self.new_path / "jobs"
            try:
                if new_jobs_dir.exists():
                    shutil.rmtree(new_jobs_dir)
                shutil.move(str(jobs_dir), str(new_jobs_dir))
                print(f"Moved {jobs_dir} -> {new_jobs_dir}")
            except Exception as e:
                self.errors.append(f"Error moving jobs directory: {e}")
    
    def run_migration(self):
        """Run the complete migration process."""
        print("Starting transformer import migration...")
        
        # Step 1: Create new directory structure
        print("\n1. Creating new directory structure...")
        self.create_new_directory_structure()
        
        # Step 2: Move files (commented out for safety - run manually)
        print("\n2. Moving files to new structure...")
        # Uncomment the next line when ready to move files
        # self.move_files_to_new_structure()
        print("   [SKIPPED] File moving disabled for safety. Uncomment in script to enable.")
        
        # Step 3: Update imports in all Python files
        print("\n3. Updating import statements...")
        python_files = self.find_python_files()
        
        for file_path in python_files:
            # Skip files in the old transformer directory during migration
            if str(self.old_path) in str(file_path):
                continue
                
            changes = self.update_imports_in_file(file_path)
            if changes > 0:
                self.files_processed += 1
                self.imports_updated += changes
                print(f"   Updated {changes} imports in {file_path}")
        
        # Step 4: Report results
        self.print_migration_report()
    
    def print_migration_report(self):
        """Print migration summary report."""
        print("\n" + "="*60)
        print("TRANSFORMER IMPORT MIGRATION REPORT")
        print("="*60)
        print(f"Files processed: {self.files_processed}")
        print(f"Total imports updated: {self.imports_updated}")
        
        if self.errors:
            print(f"\nErrors encountered ({len(self.errors)}):")
            for error in self.errors:
                print(f"  - {error}")
        else:
            print("\nNo errors encountered.")
        
        print("\nNext steps:")
        print("1. Review the updated import statements")
        print("2. Uncomment file moving code and run again if needed")
        print("3. Run tests to ensure everything works correctly")
        print("4. Clean up old transformer directory when satisfied")


if __name__ == "__main__":
    # Run migration from project root
    project_root = "/Users/<USER>/PycharmProjects/lexgenius"
    
    migrator = TransformerImportMigrator(project_root)
    migrator.run_migration()
