#!/usr/bin/env python3
"""
PACER Import Verification Script - CRITICAL PATH
Test that all migrated imports are working correctly
"""

import sys
import importlib.util
from pathlib import Path

def test_import(module_path):
    """Test if a module can be imported"""
    try:
        spec = importlib.util.find_spec(module_path)
        if spec is None:
            return False
        
        # Try to load the module
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return True
    except Exception as e:
        print(f"    Error: {e}")
        return False

def main():
    """Main verification function"""
    print("🔍 Verifying migrated PACER imports...")
    
    # Test actual existing PACER modules based on directory structure
    test_modules = [
        # Core interfaces
        'src.pacer.interfaces.base_interfaces',
        'src.pacer._core_services.shared.base_interfaces',
        
        # Core services
        'src.pacer._core_services.browser.browser_service',
        'src.pacer._core_services.verification.verification_service',
        'src.pacer._core_services.case_processing.case_processing_service',
        'src.pacer._core_services.file_operations.file_operations_service',
        'src.pacer._core_services.download_orchestration.download_orchestration_service',
        
        # Orchestrator
        'src.pacer.pacer_orchestrator_service',
        
        # Components
        'src.pacer._authentication_components',
        'src.pacer._browser_components',
        'src.pacer._case_processing_components',
        'src.pacer._classification_components',
        'src.pacer._config_components',
        'src.pacer._download_components',
        'src.pacer._export_components',
        'src.pacer._file_components',
        'src.pacer._navigation_components',
        'src.pacer._processing_components',
        'src.pacer._query_components',
        'src.pacer._report_components',
        'src.pacer._transfer_components',
        'src.pacer._verification_components',
        
        # Factories and registries
        'src.pacer.factories.service_factory',
        'src.pacer._core_services.registry.service_registry',
        'src.pacer._core_services.registry.simplified_factory'
    ]

    failed_imports = []
    success_count = 0
    
    for module in test_modules:
        print(f"Testing: {module}")
        if test_import(module):
            print(f"✅ Success: {module}")
            success_count += 1
        else:
            print(f"❌ Failed: {module}")
            failed_imports.append(module)

    print(f"\n📊 Import Verification Results:")
    print(f"  • Total modules tested: {len(test_modules)}")
    print(f"  • Successful imports: {success_count}")
    print(f"  • Failed imports: {len(failed_imports)}")

    if failed_imports:
        print(f"\n⚠️ {len(failed_imports)} imports failed verification:")
        for module in failed_imports:
            print(f"    - {module}")
        return False
    else:
        print("\n🎉 All imports verified successfully!")
        return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)