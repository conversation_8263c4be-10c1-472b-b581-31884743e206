#!/usr/bin/env python3
'''
PACER Import Migration Rollback Script
Restores .bak files created during migration
'''

import os
from pathlib import Path

root_path = Path("/Users/<USER>/PycharmProjects/lexgenius")
backup_files = list(root_path.rglob("*.py.bak"))

print(f"Found {len(backup_files)} backup files")

for backup in backup_files:
    original = backup.with_suffix('')
    if original.exists():
        print(f"Restoring: {original.relative_to(root_path)}")
        original.write_text(backup.read_text())
        backup.unlink()  # Remove backup
    else:
        print(f"Warning: Original not found for {backup}")

print("Rollback complete!")
