#!/usr/bin/env python3
"""
Fix any remaining imports pointing to the deleted src.services.pacer
"""
import os
import re
from pathlib import Path

def fix_imports():
    project_root = Path("/Users/<USER>/PycharmProjects/lexgenius")
    
    # Find all .bak files and regular Python files
    all_files = list(project_root.rglob("*.py")) + list(project_root.rglob("*.py.bak"))
    
    replacements = [
        # Main import patterns
        (r'from src\.services\.pacer', 'from src.pacer'),
        (r'import src\.services\.pacer', 'import src.pacer'),
        
        # Component mapping
        (r'from src\.services\.pacer\._(\w+)_components', r'from src.pacer.components.\1'),
        (r'from src\.services\.pacer\.(\w+)_facade_service', r'from src.pacer.facades.\1_service'),
        
        # Specific services
        (r'from src\.services\.pacer\.browser_facade_service import BrowserFacadeService', 
         'from src.pacer._core_services.browser.browser_service import BrowserService'),
        (r'from src\.services\.pacer\.pacer_orchestrator_service', 
         'from src.pacer._core_services.docket_processing_orchestrator'),
        (r'from src\.services\.pacer\.ignore_download_service',
         'from src.pacer.components.download.download_manager'),
        (r'from src\.services\.pacer\.query_service',
         'from src.pacer.components.query.query_builder'),
         
        # Jobs
        (r'from src\.services\.pacer\.jobs', 'from src.pacer.jobs'),
        
        # Core services
        (r'from src\.services\.pacer\._core_services', 'from src.pacer._core_services'),
    ]
    
    fixed_count = 0
    for file_path in all_files:
        # Skip node_modules and venv
        if any(skip in str(file_path) for skip in ["node_modules", ".venv", "__pycache__", "backups"]):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            original = content
            for pattern, replacement in replacements:
                content = re.sub(pattern, replacement, content)
            
            if content != original:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"Fixed: {file_path.relative_to(project_root)}")
                fixed_count += 1
                
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
    
    print(f"\n✅ Fixed {fixed_count} files")
    return fixed_count > 0

if __name__ == "__main__":
    fix_imports()