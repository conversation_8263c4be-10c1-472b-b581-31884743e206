#!/usr/bin/env python3
"""
Remove Legacy Files and Verify Architecture Compliance

This script removes legacy files from PACER and Transformer services
and verifies that both domains conform to the proper architecture.
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict, Tuple
import json
from datetime import datetime

# Project root
PROJECT_ROOT = Path(__file__).parent.parent.parent


class ArchitectureValidator:
    """Validates architecture compliance and removes legacy files."""
    
    def __init__(self):
        self.project_root = PROJECT_ROOT
        self.issues = []
        self.files_to_remove = []
        self.compliance_report = {
            "timestamp": datetime.now().isoformat(),
            "pacer": {},
            "transformer": {},
            "infrastructure": {},
            "issues": [],
            "files_removed": []
        }
    
    def validate_and_clean(self):
        """Main validation and cleanup process."""
        print("🔍 Starting Architecture Validation and Cleanup...")
        print("=" * 60)
        
        # 1. Identify legacy files in Transformer
        self.identify_transformer_legacy_files()
        
        # 2. Identify legacy files in PACER
        self.identify_pacer_legacy_files()
        
        # 3. Check for duplicate pattern implementations
        self.check_duplicate_patterns()
        
        # 4. Verify domain boundaries
        self.verify_domain_boundaries()
        
        # 5. Check service layer thickness
        self.check_service_layer_thickness()
        
        # 6. Generate compliance report
        self.generate_compliance_report()
        
        # 7. Remove legacy files (if confirmed)
        self.remove_legacy_files()
        
        # 8. Save report
        self.save_report()
    
    def identify_transformer_legacy_files(self):
        """Identify legacy files in Transformer service."""
        print("\n📦 Checking Transformer service...")
        
        # Legacy files to remove
        legacy_paths = [
            "src/services/transformer/old_files_backup",
            "src/services/transformer/data_transformer_legacy.py",
        ]
        
        for path in legacy_paths:
            full_path = self.project_root / path
            if full_path.exists():
                if full_path.is_dir():
                    # Count files in directory
                    file_count = sum(1 for _ in full_path.rglob("*") if _.is_file())
                    self.files_to_remove.append(str(full_path))
                    self.issues.append(f"Legacy directory found: {path} ({file_count} files)")
                    print(f"  ❌ Legacy directory: {path} ({file_count} files)")
                else:
                    self.files_to_remove.append(str(full_path))
                    self.issues.append(f"Legacy file found: {path}")
                    print(f"  ❌ Legacy file: {path}")
        
        # Check for files that should be in domain layer
        services_transformer = self.project_root / "src/services/transformer"
        if services_transformer.exists():
            # Count non-orchestration files
            allowed_files = {
                "__init__.py",
                "transformer_orchestrator.py",
                "transformer_factory.py",
                "CLAUDE.md"
            }
            
            extra_files = []
            for file in services_transformer.glob("*.py"):
                if file.name not in allowed_files:
                    extra_files.append(file.name)
            
            if extra_files:
                self.issues.append(f"Extra files in services/transformer: {extra_files}")
                print(f"  ⚠️  Extra files that may need migration: {extra_files}")
    
    def identify_pacer_legacy_files(self):
        """Identify legacy files in PACER service."""
        print("\n📦 Checking PACER service...")
        
        # Check for _core_services in services/pacer (should not exist)
        legacy_core = self.project_root / "src/services/pacer/_core_services"
        if legacy_core.exists():
            file_count = sum(1 for _ in legacy_core.rglob("*") if _.is_file())
            self.issues.append(f"Legacy _core_services in services/pacer: {file_count} files")
            print(f"  ⚠️  Legacy _core_services directory found in services/pacer ({file_count} files)")
            print("      This should be moved to src/pacer/_core_services")
        
        # Check for proper structure in domain layer
        pacer_domain = self.project_root / "src/pacer"
        if pacer_domain.exists():
            required_dirs = ["components", "core", "models", "interfaces", "facades", "factories"]
            missing_dirs = []
            for dir_name in required_dirs:
                if not (pacer_domain / dir_name).exists():
                    missing_dirs.append(dir_name)
            
            if missing_dirs:
                self.issues.append(f"Missing directories in src/pacer: {missing_dirs}")
                print(f"  ⚠️  Missing required directories: {missing_dirs}")
            else:
                print("  ✅ All required directories present in src/pacer")
    
    def check_duplicate_patterns(self):
        """Check for duplicate pattern implementations."""
        print("\n🔍 Checking for duplicate pattern implementations...")
        
        # Pattern files should only exist in infrastructure/patterns
        pattern_names = [
            "component_base.py",
            "registry.py",
            "factory.py",
            "builder.py",
            "observer.py",
            "strategy.py",
            "command.py",
            "resource_pool.py"
        ]
        
        # Check if patterns exist in infrastructure
        infra_patterns = self.project_root / "src/infrastructure/patterns"
        if not infra_patterns.exists():
            self.issues.append("Infrastructure patterns directory missing!")
            print("  ❌ Infrastructure patterns directory missing!")
        else:
            missing_patterns = []
            for pattern in pattern_names:
                if not (infra_patterns / pattern).exists():
                    missing_patterns.append(pattern)
            
            if missing_patterns:
                print(f"  ⚠️  Missing patterns in infrastructure: {missing_patterns}")
            else:
                print("  ✅ All required patterns present in infrastructure")
        
        # Check for duplicate AsyncServiceBase
        async_base_locations = []
        for root, dirs, files in os.walk(self.project_root / "src"):
            if "async_service_base.py" in files:
                rel_path = os.path.relpath(os.path.join(root, "async_service_base.py"), self.project_root)
                async_base_locations.append(rel_path)
        
        if len(async_base_locations) > 1:
            self.issues.append(f"Duplicate AsyncServiceBase found: {async_base_locations}")
            print(f"  ❌ Duplicate AsyncServiceBase implementations found:")
            for loc in async_base_locations:
                print(f"      - {loc}")
    
    def verify_domain_boundaries(self):
        """Verify no cross-domain dependencies."""
        print("\n🔍 Verifying domain boundaries...")
        
        # Check for cross-domain imports
        domains = ["pacer", "transformer", "reports", "fb_ads"]
        violations = []
        
        for domain in domains:
            domain_path = self.project_root / f"src/{domain}"
            if domain_path.exists():
                # Check for imports from other domains
                for py_file in domain_path.rglob("*.py"):
                    try:
                        with open(py_file, 'r') as f:
                            content = f.read()
                            for other_domain in domains:
                                if other_domain != domain:
                                    if f"from src.{other_domain}" in content or f"import src.{other_domain}" in content:
                                        violations.append(f"{domain} imports from {other_domain} in {py_file.name}")
                    except Exception:
                        pass
        
        if violations:
            self.issues.extend(violations)
            print("  ❌ Cross-domain dependency violations found:")
            for v in violations[:5]:  # Show first 5
                print(f"      - {v}")
            if len(violations) > 5:
                print(f"      ... and {len(violations) - 5} more")
        else:
            print("  ✅ No cross-domain dependencies detected")
    
    def check_service_layer_thickness(self):
        """Check if service layer is appropriately thin."""
        print("\n📏 Checking service layer thickness...")
        
        service_dirs = {
            "pacer": 10,  # Max files allowed
            "transformer": 10,
            "reports": 10,
            "fb_ads": 10
        }
        
        for service, max_files in service_dirs.items():
            service_path = self.project_root / f"src/services/{service}"
            if service_path.exists():
                # Count Python files (excluding __pycache__)
                py_files = [f for f in service_path.glob("*.py") if f.is_file()]
                file_count = len(py_files)
                
                if file_count > max_files:
                    self.issues.append(f"Service layer too thick: {service} has {file_count} files (max: {max_files})")
                    print(f"  ❌ {service}: {file_count} files (exceeds limit of {max_files})")
                else:
                    print(f"  ✅ {service}: {file_count} files (within limit)")
    
    def generate_compliance_report(self):
        """Generate detailed compliance report."""
        print("\n📊 Generating Compliance Report...")
        
        # PACER compliance
        pacer_domain = self.project_root / "src/pacer"
        if pacer_domain.exists():
            self.compliance_report["pacer"] = {
                "domain_exists": True,
                "component_count": len(list((pacer_domain / "components").rglob("*.py"))) if (pacer_domain / "components").exists() else 0,
                "core_services": len(list((pacer_domain / "core").glob("*.py"))) if (pacer_domain / "core").exists() else 0,
                "models": len(list((pacer_domain / "models").glob("*.py"))) if (pacer_domain / "models").exists() else 0,
            }
        
        # Transformer compliance
        transformer_domain = self.project_root / "src/transformer"
        if transformer_domain.exists():
            self.compliance_report["transformer"] = {
                "domain_exists": True,
                "component_count": len(list((transformer_domain / "components").rglob("*.py"))) if (transformer_domain / "components").exists() else 0,
                "core_services": len(list((transformer_domain / "core").glob("*.py"))) if (transformer_domain / "core").exists() else 0,
                "models": len(list((transformer_domain / "models").glob("*.py"))) if (transformer_domain / "models").exists() else 0,
            }
        
        self.compliance_report["issues"] = self.issues
        self.compliance_report["total_issues"] = len(self.issues)
    
    def remove_legacy_files(self):
        """Remove identified legacy files after confirmation."""
        if not self.files_to_remove:
            print("\n✅ No legacy files to remove")
            return
        
        print(f"\n🗑️  Found {len(self.files_to_remove)} legacy items to remove:")
        for file in self.files_to_remove[:10]:  # Show first 10
            print(f"  - {file}")
        if len(self.files_to_remove) > 10:
            print(f"  ... and {len(self.files_to_remove) - 10} more")
        
        # Auto-remove for CI/CD
        auto_remove = os.environ.get('AUTO_REMOVE', 'yes').lower() == 'yes'
        
        if not auto_remove:
            try:
                response = input("\nRemove these legacy files? (y/n): ")
                proceed = response.lower() == 'y'
            except EOFError:
                print("\n⚠️  Non-interactive mode detected, auto-removing legacy files...")
                proceed = True
        else:
            print("\n🤖 Auto-removing legacy files...")
            proceed = True
        
        if proceed:
            removed_count = 0
            for file_path in self.files_to_remove:
                try:
                    path = Path(file_path)
                    if path.exists():
                        if path.is_dir():
                            shutil.rmtree(path)
                            print(f"  ✅ Removed directory: {path.name}")
                        else:
                            path.unlink()
                            print(f"  ✅ Removed file: {path.name}")
                        removed_count += 1
                        self.compliance_report["files_removed"].append(str(path))
                except Exception as e:
                    print(f"  ❌ Error removing {file_path}: {e}")
            
            print(f"\n✅ Removed {removed_count} legacy items")
        else:
            print("\n⏭️  Skipped file removal")
    
    def save_report(self):
        """Save compliance report to file."""
        report_dir = self.project_root / "docs" / "compliance_reports"
        report_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = report_dir / f"architecture_compliance_{timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump(self.compliance_report, f, indent=2)
        
        print(f"\n📄 Report saved to: {report_file}")
        
        # Print summary
        print("\n" + "=" * 60)
        print("COMPLIANCE SUMMARY")
        print("=" * 60)
        print(f"Total Issues Found: {len(self.issues)}")
        print(f"Files Removed: {len(self.compliance_report['files_removed'])}")
        
        if self.issues:
            print("\n⚠️  Top Issues to Address:")
            for issue in self.issues[:5]:
                print(f"  • {issue}")
        else:
            print("\n✅ Architecture is fully compliant!")


def main():
    """Main entry point."""
    validator = ArchitectureValidator()
    validator.validate_and_clean()


if __name__ == "__main__":
    main()