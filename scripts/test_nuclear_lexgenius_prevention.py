#!/usr/bin/env python3
"""
Test script to demonstrate nuclear lexgenius.log prevention.

This script tests all the nuclear prevention mechanisms to ensure
lexgenius.log files cannot be created under any circumstances.
"""

import os
import sys
import logging
import time
import tempfile
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from pacer.utils.court_logger import (
    nuclear_lexgenius_prevention,
    activate_nuclear_lexgenius_prevention,
    emergency_delete_all_lexgenius_logs,
    CourtLogger
)


def create_test_lexgenius_logs(test_dir: str):
    """Create some test lexgenius.log files to test deletion."""
    test_files = []
    
    # Create various lexgenius log files
    test_paths = [
        os.path.join(test_dir, "lexgenius.log"),
        os.path.join(test_dir, "lexgenius-2025.log"),
        os.path.join(test_dir, "logs", "lexgenius.log"),
        os.path.join(test_dir, "data", "20250810", "logs", "lexgenius.log"),
    ]
    
    for path in test_paths:
        try:
            os.makedirs(os.path.dirname(path), exist_ok=True)
            with open(path, 'w') as f:
                f.write("Test lexgenius log content\n")
            test_files.append(path)
            print(f"Created test file: {path}")
        except Exception as e:
            print(f"Failed to create {path}: {e}")
    
    return test_files


def test_lexgenius_logger_creation():
    """Test that lexgenius loggers are properly intercepted."""
    print("\n=== Testing lexgenius logger creation ===")
    
    # Try to create various lexgenius loggers
    lexgenius_loggers = [
        "lexgenius",
        "lexgenius.main",
        "lexgenius.fallback", 
        "lexgenius.core",
        "lexgenius.services",
    ]
    
    for logger_name in lexgenius_loggers:
        try:
            logger = logging.getLogger(logger_name)
            logger.info("This should be intercepted and not create a log file")
            logger.error("This error should also be intercepted")
            logger.warning("This warning should be intercepted too")
            
            # Check if logger has been neutered
            if not logger.handlers or all(isinstance(h, logging.NullHandler) for h in logger.handlers):
                print(f"✅ Logger {logger_name} properly intercepted (no handlers)")
            else:
                print(f"⚠️ Logger {logger_name} may not be fully intercepted")
                
        except Exception as e:
            print(f"❌ Error testing logger {logger_name}: {e}")


def test_nuclear_context_manager():
    """Test the nuclear context manager."""
    print("\n=== Testing Nuclear Context Manager ===")
    
    with tempfile.TemporaryDirectory() as test_dir:
        print(f"Test directory: {test_dir}")
        
        # Create some test lexgenius files first
        test_files = create_test_lexgenius_logs(test_dir)
        
        # Use nuclear prevention
        with nuclear_lexgenius_prevention(base_path=test_dir, continuous_monitoring=False):
            print("Inside nuclear prevention context...")
            
            # Try to create lexgenius loggers
            test_lexgenius_logger_creation()
            
            # Try to manually create log files
            try:
                manual_log = os.path.join(test_dir, "manual_lexgenius.log")
                with open(manual_log, 'w') as f:
                    f.write("Manually created lexgenius log")
                print(f"Created manual log: {manual_log}")
            except Exception as e:
                print(f"Failed to create manual log: {e}")
        
        # Check which files still exist after nuclear prevention
        print(f"\nFiles remaining in test directory:")
        for root, dirs, files in os.walk(test_dir):
            for file in files:
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, test_dir)
                print(f"  {relative_path}")


def test_emergency_deletion():
    """Test emergency deletion function."""
    print("\n=== Testing Emergency Deletion ===")
    
    with tempfile.TemporaryDirectory() as test_dir:
        print(f"Test directory: {test_dir}")
        
        # Create test files
        test_files = create_test_lexgenius_logs(test_dir)
        print(f"Created {len(test_files)} test files")
        
        # Test emergency deletion
        deleted_files = emergency_delete_all_lexgenius_logs(test_dir)
        print(f"Emergency deletion removed {len(deleted_files)} files:")
        for file in deleted_files:
            print(f"  - {file}")
        
        # Verify files are gone
        remaining_files = []
        for root, dirs, files in os.walk(test_dir):
            for file in files:
                if 'lexgenius' in file.lower():
                    remaining_files.append(os.path.join(root, file))
        
        if remaining_files:
            print(f"⚠️ {len(remaining_files)} lexgenius files still remain:")
            for file in remaining_files:
                print(f"  - {file}")
        else:
            print("✅ All lexgenius log files successfully deleted")


def test_handler_removal():
    """Test that lexgenius handlers are properly removed."""
    print("\n=== Testing Handler Removal ===")
    
    # Create a logger with a file handler pointing to lexgenius.log
    test_logger = logging.getLogger("test.lexgenius.handler")
    
    with tempfile.TemporaryDirectory() as test_dir:
        lexgenius_log_path = os.path.join(test_dir, "lexgenius.log")
        
        # Add a file handler
        file_handler = logging.FileHandler(lexgenius_log_path)
        test_logger.addHandler(file_handler)
        
        print(f"Added file handler pointing to: {lexgenius_log_path}")
        print(f"Logger has {len(test_logger.handlers)} handlers before nuclear prevention")
        
        # Apply nuclear prevention
        result = CourtLogger.nuclear_prevent_lexgenius_log(test_dir)
        
        print(f"Nuclear prevention result: {result}")
        print(f"Logger has {len(test_logger.handlers)} handlers after nuclear prevention")
        
        # Try logging
        test_logger.info("This should not create a lexgenius.log file")
        
        # Check if file was created
        if os.path.exists(lexgenius_log_path):
            print(f"⚠️ lexgenius.log file was created despite prevention: {lexgenius_log_path}")
        else:
            print("✅ lexgenius.log file was not created - prevention successful")


def test_continuous_monitoring():
    """Test continuous monitoring of lexgenius.log creation."""
    print("\n=== Testing Continuous Monitoring ===")
    
    with tempfile.TemporaryDirectory() as test_dir:
        print(f"Test directory: {test_dir}")
        
        # Start continuous monitoring
        monitor_thread = CourtLogger.continuously_delete_lexgenius_logs(test_dir, interval=1)
        
        print("Started continuous monitoring thread...")
        
        # Create lexgenius files every second for 5 seconds
        for i in range(3):
            try:
                test_file = os.path.join(test_dir, f"lexgenius-{i}.log")
                with open(test_file, 'w') as f:
                    f.write(f"Test content {i}")
                print(f"Created: {test_file}")
                
                # Wait for monitoring to delete it
                time.sleep(1.5)
                
                if os.path.exists(test_file):
                    print(f"⚠️ File still exists: {test_file}")
                else:
                    print(f"✅ File deleted by monitoring: {test_file}")
                    
            except Exception as e:
                print(f"Error in continuous monitoring test: {e}")
        
        print("Continuous monitoring test complete")


def main():
    """Run all nuclear prevention tests."""
    print("🚨 NUCLEAR LEXGENIUS PREVENTION TESTS 🚨")
    print("=" * 50)
    
    try:
        # Test 1: Emergency deletion
        test_emergency_deletion()
        
        # Test 2: Nuclear context manager
        test_nuclear_context_manager()
        
        # Test 3: Handler removal
        test_handler_removal()
        
        # Test 4: Continuous monitoring
        test_continuous_monitoring()
        
        print("\n" + "=" * 50)
        print("🎉 ALL NUCLEAR PREVENTION TESTS COMPLETED")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()