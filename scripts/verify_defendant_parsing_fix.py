#!/usr/bin/env python
"""
Verify the defendant parsing fix produces correct JSON output format.
"""

import json
from src.pacer.components.report.report_generator import ReportGenerator


def test_json_output_format():
    """Test that the JSON output matches the expected format."""
    
    # Initialize report generator
    report_gen = ReportGenerator()
    
    # Test the specific case from the user's example
    raw_cells = [
        {
            "text": "1:25-cv-09294 Mandujano v. Avlon Industries, Inc.",
            "links": [{
                "text": "1:25-cv-09294",
                "href": "/cgi-bin/DktRpt.pl?484004"
            }]
        },
        {"text": "", "links": []},
        {"text": "08/06/2025", "links": []},
        {"text": "Cause: 28:1332 Diversity-Product Liability\nNOS: 365 Personal Inj. Prod. Liability", "links": []}
    ]
    
    # Parse the case data
    result = report_gen._parse_case_data_from_cells(
        raw_cells, "ilnd", "2025-08-08T20:21:54.307060", "[ilnd]"
    )
    
    # Expected output format
    expected = {
        "court_id": "ilnd",
        "docket_num": "1:25-cv-09294",
        "versus": "Mandujano v. Avlon Industries, Inc.",
        "filing_date": "08/06/2025",
        "docket_link": "/cgi-bin/DktRpt.pl?484004",
        "extracted_at": "2025-08-08T20:21:54.307060",
        "source": "PACER Case Report",
        "defendants": [
            {
                "name": "Avlon Industries, Inc."
            }
        ],
        "defendant": [
            {
                "name": "Avlon Industries, Inc."
            }
        ],
        "cause": "28:1332 Diversity-Product Liability",
        "nos": "365 Personal Inj. Prod. Liability"
    }
    
    print("🔍 Verifying JSON Output Format")
    print("=" * 60)
    
    # Verify each field
    success = True
    for key, expected_value in expected.items():
        actual_value = result.get(key)
        
        if actual_value == expected_value:
            print(f"✅ {key}: Match")
        else:
            print(f"❌ {key}: Mismatch")
            print(f"   Expected: {json.dumps(expected_value, indent=2)}")
            print(f"   Actual:   {json.dumps(actual_value, indent=2)}")
            success = False
    
    print("\n" + "=" * 60)
    print("\n📋 ACTUAL JSON OUTPUT:")
    print(json.dumps(result, indent=2))
    
    print("\n" + "=" * 60)
    if success:
        print("\n✅ SUCCESS: JSON output format is correct!")
        print("\n🎯 KEY FIX CONFIRMED:")
        print("   - Defendant name 'Avlon Industries, Inc.' is preserved as single entity")
        print("   - No incorrect splitting at commas")
        print("   - Both 'defendants' and 'defendant' fields updated correctly")
    else:
        print("\n❌ FAILED: JSON output format has issues")
    
    return success


def test_additional_cases():
    """Test additional defendant name formats."""
    
    report_gen = ReportGenerator()
    test_cases = [
        {
            "input": "Jones v. Monsanto Inc. et al",
            "expected_defendant": "Monsanto Inc."
        },
        {
            "input": "Smith V. Johnson & Johnson, LLC",
            "expected_defendant": "Johnson & Johnson, LLC"
        },
        {
            "input": "Doe v ABC Corporation, a Delaware Company",
            "expected_defendant": "ABC Corporation, a Delaware Company"
        }
    ]
    
    print("\n\n🧪 TESTING ADDITIONAL CASES")
    print("=" * 60)
    
    all_passed = True
    for test_case in test_cases:
        raw_cells = [
            {
                "text": f"1:25-cv-00000 {test_case['input']}",
                "links": [{
                    "text": "1:25-cv-00000",
                    "href": "/cgi-bin/DktRpt.pl?000000"
                }]
            },
            {"text": "", "links": []},
            {"text": "01/01/2025", "links": []},
            {"text": "Cause: Test\nNOS: Test", "links": []}
        ]
        
        result = report_gen._parse_case_data_from_cells(
            raw_cells, "test", "2025-01-01T00:00:00", "[test]"
        )
        
        actual_defendant = result["defendants"][0]["name"] if result["defendants"] else "None"
        
        if actual_defendant == test_case["expected_defendant"]:
            print(f"✅ '{test_case['input']}' → '{actual_defendant}'")
        else:
            print(f"❌ '{test_case['input']}'")
            print(f"   Expected: '{test_case['expected_defendant']}'")
            print(f"   Actual:   '{actual_defendant}'")
            all_passed = False
    
    return all_passed


if __name__ == "__main__":
    # Run verification
    format_ok = test_json_output_format()
    additional_ok = test_additional_cases()
    
    print("\n\n" + "=" * 60)
    print("📊 FINAL VERIFICATION RESULTS")
    print("=" * 60)
    
    if format_ok and additional_ok:
        print("✅ ALL TESTS PASSED - Defendant parsing fix verified!")
        print("\n🎉 The fix successfully:")
        print("   1. Preserves company names with commas (e.g., 'Inc.', 'LLC')")
        print("   2. Handles 'et al' correctly")
        print("   3. Works with various 'v.' formats")
        print("   4. Maintains correct JSON structure")
    else:
        print("❌ Some tests failed - please review the output above")