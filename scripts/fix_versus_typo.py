#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the 'versIus' typo in JSON files and rename them to correct filenames.
This script will:
1. Find all JSON files with 'versIus' instead of 'versus'
2. Fix the key name without removing any data
3. Rename files from Unknown_Case to the correct case title
"""

import json
import os
import sys
import shutil
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from typing import Tuple, Optional, Dict
from src.transformer.components.file.file_handler_core import FileHandlerCore as FileHandler


def fix_versus_typo_in_json(json_path: Path, dry_run: bool = False) -> Tuple[bool, str]:
    """
    Fix the versIus typo in a JSON file.
    
    Returns:
        Tuple of (success, message)
    """
    try:
        # Read the JSON file
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Check if the typo exists
        if 'versIus' not in data:
            return True, "No typo found"
        
        # Fix the typo
        versus_value = data['versIus']
        del data['versIus']
        data['versus'] = versus_value
        
        if not dry_run:
            # Save the corrected JSON
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
        
        return True, f"Fixed versIus -> versus: '{versus_value}'"
        
    except Exception as e:
        return False, f"Error: {str(e)}"


def rename_file_if_unknown(json_path: Path, dry_run: bool = False, fixed_data: Optional[Dict] = None) -> Tuple[bool, str, Optional[Path]]:
    """
    Rename file if it's named Unknown_Case but has valid case information.
    
    Args:
        json_path: Path to the JSON file
        dry_run: If True, don't actually rename
        fixed_data: Optional pre-fixed data (for dry run after typo fix)
    
    Returns:
        Tuple of (success, message, new_path)
    """
    try:
        # Skip if not Unknown_Case
        if "Unknown_Case" not in json_path.name:
            return True, "Not an Unknown_Case file", None
        
        # Use provided data or read from file
        if fixed_data:
            data = fixed_data
        else:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        
        # Check if we have valid case info (check both versus and versIus for compatibility)
        versus = data.get('versus', data.get('versIus', ''))
        if not versus or versus == 'unknown':
            return True, "No valid versus field for renaming", None
        
        # Get the base filename from the data
        base_filename = data.get('base_filename')
        if base_filename and base_filename != json_path.stem:
            # Use the base_filename if it exists and is different
            new_filename = f"{base_filename}.json"
        else:
            # Generate filename using FileHandler logic
            config = {
                'iso_date': json_path.parent.parent.name,  # Get date from path
                'project_root': str(project_root),
                'directories': {
                    'base_dir': str(project_root)
                }
            }
            file_handler = FileHandler(config)
            
            # Create filename from data
            new_base = file_handler.create_filename(data)
            new_filename = f"{new_base}.json"
        
        new_path = json_path.parent / new_filename
        
        # Check if target already exists
        if new_path.exists() and new_path != json_path:
            return False, f"Target file already exists: {new_filename}", None
        
        if not dry_run and new_path != json_path:
            # Rename the JSON file
            shutil.move(str(json_path), str(new_path))
            
            # Also rename associated PDF and MD files if they exist
            for ext in ['.pdf', '.md']:
                old_file = json_path.with_suffix(ext)
                if old_file.exists():
                    new_file = new_path.with_suffix(ext)
                    shutil.move(str(old_file), str(new_file))
        
        return True, f"Renamed to: {new_filename}", new_path
        
    except Exception as e:
        return False, f"Rename error: {str(e)}", None


def process_date_range(start_date: str, end_date: str, dry_run: bool = False):
    """
    Process all JSON files in the date range to fix typos and rename files.
    
    Args:
        start_date: Start date in YYYYMMDD format
        end_date: End date in YYYYMMDD format
        dry_run: If True, only show what would be changed
    """
    data_dir = project_root / "data"
    
    # Convert dates to integers for comparison
    start_int = int(start_date)
    end_int = int(end_date)
    
    total_files = 0
    fixed_typos = 0
    renamed_files = 0
    errors = 0
    
    print(f"Processing JSON files from {start_date} to {end_date}")
    if dry_run:
        print("DRY RUN - No files will be modified\n")
    else:
        print("UPDATING FILES\n")
    
    # Process each date directory
    for date_dir in sorted(data_dir.iterdir()):
        if not date_dir.is_dir() or not date_dir.name.isdigit():
            continue
        
        date_int = int(date_dir.name)
        if date_int < start_int or date_int > end_int:
            continue
        
        dockets_dir = date_dir / "dockets"
        if not dockets_dir.exists():
            continue
        
        print(f"\nProcessing {date_dir.name}:")
        date_total = 0
        date_fixed = 0
        date_renamed = 0
        
        # Process all JSON files in this date
        for json_file in sorted(dockets_dir.glob("*.json")):
            total_files += 1
            date_total += 1
            
            # Fix versIus typo
            success, message = fix_versus_typo_in_json(json_file, dry_run)
            typo_was_fixed = False
            if success and "Fixed versIus" in message:
                fixed_typos += 1
                date_fixed += 1
                typo_was_fixed = True
                print(f"  ✓ {json_file.name}: {message}")
            elif not success:
                errors += 1
                print(f"  ✗ {json_file.name}: {message}")
                continue
            
            # For rename check, prepare data with typo fixed if needed
            if typo_was_fixed and dry_run:
                # Load data and simulate the fix for rename check
                with open(json_file, 'r', encoding='utf-8') as f:
                    temp_data = json.load(f)
                if 'versIus' in temp_data:
                    temp_data['versus'] = temp_data['versIus']
                    del temp_data['versIus']
                success, rename_msg, new_path = rename_file_if_unknown(json_file, dry_run, fixed_data=temp_data)
            else:
                # Rename if Unknown_Case
                success, rename_msg, new_path = rename_file_if_unknown(json_file, dry_run)
            if success and new_path:
                renamed_files += 1
                date_renamed += 1
                print(f"    → {rename_msg}")
            elif not success:
                errors += 1
                print(f"    ✗ {rename_msg}")
        
        if date_total > 0:
            print(f"  Date summary: {date_total} files, {date_fixed} typos fixed, {date_renamed} renamed")
    
    # Final summary
    print(f"\n{'='*60}")
    print(f"SUMMARY:")
    print(f"  Total files processed: {total_files}")
    print(f"  Typos fixed (versIus → versus): {fixed_typos}")
    print(f"  Files renamed from Unknown_Case: {renamed_files}")
    print(f"  Errors: {errors}")
    
    if dry_run:
        print(f"\nThis was a DRY RUN. Run without --dry-run to apply changes.")


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Fix versIus typo and Unknown_Case filenames in docket JSON files"
    )
    parser.add_argument(
        "start_date",
        help="Start date in YYYYMMDD format (e.g., 20250601)"
    )
    parser.add_argument(
        "end_date",
        nargs="?",
        help="End date in YYYYMMDD format (default: same as start_date)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be changed without modifying files"
    )
    
    args = parser.parse_args()
    
    # Validate dates
    if len(args.start_date) != 8 or not args.start_date.isdigit():
        print(f"Error: Invalid start date format '{args.start_date}'. Must be YYYYMMDD.")
        sys.exit(1)
    
    end_date = args.end_date or args.start_date
    if len(end_date) != 8 or not end_date.isdigit():
        print(f"Error: Invalid end date format '{end_date}'. Must be YYYYMMDD.")
        sys.exit(1)
    
    # Process the date range
    process_date_range(args.start_date, end_date, dry_run=args.dry_run)


if __name__ == "__main__":
    main()