#!/usr/bin/env python3
"""
Navigation Fix Validation Runner

This script runs both unit tests and integration tests to comprehensively
validate that the navigation fix is working correctly.

The navigation fix ensures:
1. When docket_report_log EXISTS with cases -> Goes to Query page (resume mode)
2. When docket_report_log DOES NOT EXIST -> Goes to Civil Cases Report page (new session)
3. When docket_report_log EXISTS but empty -> Goes to Civil Cases Report page (treat as new)

Usage:
    python scripts/validate_navigation_fix.py
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/Users/<USER>/PycharmProjects/lexgenius')
sys.path.insert(0, '/Users/<USER>/PycharmProjects/lexgenius/src')

try:
    from tests.test_navigation_logic_unit import TestNavigationLogicUnit
    from tests.test_navigation_fix_integration import TestNavigationFixIntegration
    
    async def run_comprehensive_validation():
        """Run comprehensive navigation fix validation."""
        print("🔍 COMPREHENSIVE NAVIGATION FIX VALIDATION")
        print("=" * 60)
        print("This validation ensures the PACER navigation fix works correctly:")
        print("• Resume from existing docket logs -> Query page")
        print("• New sessions -> Civil Cases Report page")
        print("• Proper parameter passing between components")
        print("=" * 60)
        
        all_tests_passed = True
        
        # Run Unit Tests
        print("\n📋 PHASE 1: UNIT TESTS")
        print("-" * 30)
        
        unit_test_instance = TestNavigationLogicUnit()
        unit_test_instance.setUp()
        
        try:
            unit_success = await unit_test_instance.run_all_tests()
            if unit_success:
                print("✅ Unit tests PASSED")
            else:
                print("❌ Unit tests FAILED")
                all_tests_passed = False
        except Exception as e:
            print(f"❌ Unit tests FAILED with error: {e}")
            all_tests_passed = False
        finally:
            unit_test_instance.tearDown()
        
        # Run Integration Tests
        print("\n🔗 PHASE 2: INTEGRATION TESTS")
        print("-" * 30)
        
        integration_test_instance = TestNavigationFixIntegration()
        integration_test_instance.setUp()
        
        try:
            integration_success = await integration_test_instance.run_all_integration_tests()
            if integration_success:
                print("✅ Integration tests PASSED")
            else:
                print("❌ Integration tests FAILED")
                all_tests_passed = False
        except Exception as e:
            print(f"❌ Integration tests FAILED with error: {e}")
            all_tests_passed = False
        finally:
            integration_test_instance.tearDown()
        
        # Final Report
        print("\n" + "=" * 80)
        if all_tests_passed:
            print("🎉 NAVIGATION FIX VALIDATION: ALL TESTS PASSED!")
            print("=" * 80)
            print("✅ The navigation fix is working correctly:")
            print("  • docket_report_log exists with cases -> Query page (resume_mode=True)")
            print("  • docket_report_log missing -> Civil Cases Report (resume_mode=False)")
            print("  • docket_report_log empty -> Civil Cases Report (resume_mode=False)")
            print("  • Proper navigation facade method calls")
            print("  • Correct parameter passing")
            print("\n🚀 The PACER workflow will navigate correctly based on session state!")
        else:
            print("❌ NAVIGATION FIX VALIDATION: SOME TESTS FAILED!")
            print("=" * 80)
            print("⚠️  The navigation fix may not be working correctly.")
            print("   Please review the test output above for details.")
        
        return all_tests_passed
    
    if __name__ == "__main__":
        success = asyncio.run(run_comprehensive_validation())
        sys.exit(0 if success else 1)
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)