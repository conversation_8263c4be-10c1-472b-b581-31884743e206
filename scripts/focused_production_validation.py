#!/usr/bin/env python3
"""
Focused Production Validation Script for DocketArtifactChecker

This script addresses the specific issues found in the comprehensive validation
and focuses on the critical production readiness checks.
"""

import asyncio
import json
import logging
import os
import sys
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Dict, Any
from unittest.mock import Mock, AsyncMock

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class FocusedProductionValidator:
    """Focused validator for critical production issues."""
    
    def __init__(self):
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        logger = logging.getLogger('FocusedValidator')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter('%(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def validate_critical_functionality(self) -> bool:
        """Test the core functionality that must work in production."""
        self.logger.info("🔍 Validating critical functionality...")
        
        try:
            from src.pacer.components.download.artifact_checker import DocketArtifactChecker
            from src.pacer.components.processing.docket_filter_service import DocketFilterService
            from src.pacer.components.download.download_validator import DownloadValidator
            
            mock_logger = Mock()
            mock_repo = AsyncMock()
            mock_repo.check_docket_exists.return_value = True
            
            config = {'iso_date': '2025-01-01'}
            
            # Test 1: Basic component initialization
            artifact_checker = DocketArtifactChecker(mock_logger, config, mock_repo)
            filter_service = DocketFilterService(mock_logger, config, mock_repo)
            download_validator = DownloadValidator(mock_logger, config, mock_repo)
            
            self.logger.info("✅ All components initialize successfully")
            
            # Test 2: Core action routing
            test_data = {
                'action': 'should_download_docket',
                'court_id': 'cand',
                'docket_num': '3:25-cv-00001',
                'versus': 'Test v. Case'
            }
            
            result = await artifact_checker._execute_action(test_data)
            
            if not isinstance(result, tuple) or len(result) != 2:
                raise ValueError(f"Invalid result format: {result}")
            
            self.logger.info("✅ Core action routing works")
            
            # Test 3: File path generation
            with tempfile.TemporaryDirectory() as temp_dir:
                config_with_data = {
                    'iso_date': '2025-01-01',
                    'DATA_DIR': temp_dir
                }
                
                artifact_checker = DocketArtifactChecker(mock_logger, config_with_data, mock_repo)
                
                # Create test JSON with html_only flag
                data_dir = Path(temp_dir) / '2025-01-01' / 'dockets'
                data_dir.mkdir(parents=True, exist_ok=True)
                
                test_json = {
                    'court_id': 'cand',
                    'docket_num': '3:25-cv-12345',
                    'versus': 'Test v. Case',
                    'html_only': True
                }
                
                json_file = data_dir / 'cand_25_12345_Test_v_Case.json'
                with open(json_file, 'w') as f:
                    json.dump(test_json, f)
                
                # Test artifact checking logic
                should_download, reason = await artifact_checker.should_download_docket(
                    'cand', '3:25-cv-12345', 'Test v. Case'
                )
                
                if should_download:
                    self.logger.warning(f"⚠️ HTML-only logic may not be working: {reason}")
                else:
                    self.logger.info("✅ HTML-only logic works correctly")
                
                # Test batch processing
                batch_items = [
                    {
                        'court_id': 'cand',
                        'docket_num': '3:25-cv-12345',
                        'versus': 'Test v. Case'
                    }
                ]
                
                batch_results = await filter_service.filter_docket_report_log(batch_items)
                
                if not isinstance(batch_results, dict) or 'total_items' not in batch_results:
                    raise ValueError(f"Invalid batch results: {batch_results}")
                
                self.logger.info("✅ Batch processing works")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Critical functionality failed: {e}")
            return False
    
    async def validate_integration_points(self) -> bool:
        """Test integration with existing components."""
        self.logger.info("🔗 Validating integration points...")
        
        try:
            from src.pacer.components.download.artifact_checker import DocketArtifactChecker
            from src.pacer.components.processing.docket_filter_service import DocketFilterService
            from src.pacer.components.download.download_validator import DownloadValidator
            
            mock_logger = Mock()
            mock_repo = AsyncMock()
            config = {'iso_date': '2025-01-01'}
            
            # Test DocketFilterService integration
            filter_service = DocketFilterService(mock_logger, config, mock_repo)
            
            if not hasattr(filter_service, 'artifact_checker'):
                raise AttributeError("DocketFilterService missing artifact_checker")
            
            if not isinstance(filter_service.artifact_checker, DocketArtifactChecker):
                raise TypeError("artifact_checker wrong type")
            
            self.logger.info("✅ DocketFilterService integration works")
            
            # Test DownloadValidator integration
            download_validator = DownloadValidator(mock_logger, config, mock_repo)
            
            if not hasattr(download_validator, 'artifact_checker'):
                raise AttributeError("DownloadValidator missing artifact_checker")
            
            if not isinstance(download_validator.artifact_checker, DocketArtifactChecker):
                raise TypeError("artifact_checker wrong type")
            
            # Test enhanced validation method
            case_details = {
                'court_id': 'cand',
                'docket_num': '3:25-cv-00001',
                'versus': 'Test v. Case'
            }
            
            result = await download_validator.validate_case_for_download(case_details)
            
            if not isinstance(result, dict) or 'should_download' not in result:
                raise ValueError(f"Invalid validation result: {result}")
            
            self.logger.info("✅ DownloadValidator integration works")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Integration validation failed: {e}")
            return False
    
    async def validate_error_resilience(self) -> bool:
        """Test error handling and resilience."""
        self.logger.info("⚠️ Validating error resilience...")
        
        try:
            from src.pacer.components.download.artifact_checker import DocketArtifactChecker
            
            mock_logger = Mock()
            config = {'iso_date': '2025-01-01'}
            
            # Test with failing repository
            failing_repo = AsyncMock()
            failing_repo.check_docket_exists.side_effect = Exception("DB failure")
            
            artifact_checker = DocketArtifactChecker(mock_logger, config, failing_repo)
            
            # Should handle repo failures gracefully
            exists = await artifact_checker.check_docket_exists('cand', '3:25-cv-00001')
            
            if exists is not False:
                raise ValueError("Should return False when repo fails")
            
            self.logger.info("✅ Repository failure handling works")
            
            # Test invalid action handling
            try:
                await artifact_checker._execute_action({'action': 'invalid_action'})
                raise AssertionError("Should have raised ValueError")
            except ValueError as e:
                if "Unknown action" not in str(e):
                    raise AssertionError(f"Wrong error message: {e}")
            
            self.logger.info("✅ Invalid action handling works")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error resilience validation failed: {e}")
            return False
    
    async def run_validation(self) -> bool:
        """Run focused validation suite."""
        self.logger.info("🚀 Starting focused production validation...")
        
        validations = [
            ("Critical Functionality", self.validate_critical_functionality),
            ("Integration Points", self.validate_integration_points),
            ("Error Resilience", self.validate_error_resilience)
        ]
        
        all_passed = True
        
        for name, validation_func in validations:
            self.logger.info(f"\n📋 {name}")
            passed = await validation_func()
            if not passed:
                all_passed = False
        
        self.logger.info("\n" + "="*60)
        if all_passed:
            self.logger.info("✅ ALL FOCUSED VALIDATIONS PASSED")
            self.logger.info("🚀 DocketArtifactChecker integration is production ready!")
        else:
            self.logger.error("❌ FOCUSED VALIDATION FAILED")
            self.logger.error("Issues must be resolved before deployment!")
        
        return all_passed


async def main():
    """Main entry point."""
    validator = FocusedProductionValidator()
    
    try:
        success = await validator.run_validation()
        sys.exit(0 if success else 1)
    except Exception as e:
        validator.logger.error(f"Validation failed: {e}")
        sys.exit(2)


if __name__ == "__main__":
    asyncio.run(main())