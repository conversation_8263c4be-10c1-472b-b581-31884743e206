#!/usr/bin/env python3
"""
Backfill MDL Inheritance Script

This script retroactively applies mdl_num inheritance to existing JSON files that lack this field
but should match ignore_download configuration entries. It processes HTML-only cases (JSON files
without matching PDF/ZIP artifacts) and applies the same matching logic used in live processing.

Usage:
    python scripts/utils/backfill_mdl_inheritance.py --date 20250715 [--dry-run] [--verbose]

Requirements:
    - Must be run from project root directory
    - Requires access to ignore_download configuration
    - Requires conda environment 'lexgenius' to be activated
"""

import argparse
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

# Add project root to Python path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import required services
from pacer.services.ignore_download_service import PacerIgnoreDownloadService


class SimpleLogger:
    """Simple logger implementation for the script"""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        logging.basicConfig(
            level=logging.DEBUG if verbose else logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def log_info(self, message: str):
        self.logger.info(message)
    
    def log_warning(self, message: str):
        self.logger.warning(message)
    
    def log_error(self, message: str):
        self.logger.error(message)
    
    def log_debug(self, message: str):
        if self.verbose:
            self.logger.debug(message)
    
    # Add missing methods from LoggerProtocol with proper signature handling
    def info(self, message: str, *args, **kwargs):
        self.log_info(message)
    
    def warning(self, message: str, *args, **kwargs):
        self.log_warning(message)
    
    def error(self, message: str, *args, **kwargs):
        self.log_error(message)
    
    def debug(self, message: str, *args, **kwargs):
        self.log_debug(message)


class BackfillStats:
    """Statistics tracking for the backfill operation"""
    
    def __init__(self):
        self.total_json_files = 0
        self.html_only_files = 0
        self.files_missing_mdl = 0
        self.files_with_matches = 0
        self.files_updated = 0
        self.errors = 0
        self.updated_files: List[Tuple[str, str]] = []  # (filename, mdl_num)
        self.error_files: List[Tuple[str, str]] = []  # (filename, error)
    
    def print_summary(self, logger: SimpleLogger):
        """Print comprehensive statistics summary"""
        logger.log_info("=" * 60)
        logger.log_info("BACKFILL MDL INHERITANCE SUMMARY")
        logger.log_info("=" * 60)
        logger.log_info(f"Total JSON files found: {self.total_json_files}")
        logger.log_info(f"HTML-only files (no PDF/ZIP): {self.html_only_files}")
        logger.log_info(f"Files missing mdl_num: {self.files_missing_mdl}")
        logger.log_info(f"Files with ignore_download matches: {self.files_with_matches}")
        logger.log_info(f"Files successfully updated: {self.files_updated}")
        logger.log_info(f"Errors encountered: {self.errors}")
        
        if self.updated_files:
            logger.log_info("\nUpdated files:")
            for filename, mdl_num in self.updated_files:
                logger.log_info(f"  {filename} -> mdl_num: {mdl_num}")
        
        if self.error_files:
            logger.log_info("\nFiles with errors:")
            for filename, error in self.error_files:
                logger.log_info(f"  {filename}: {error}")


class MDLInheritanceBackfill:
    """Main class for MDL inheritance backfill operations"""
    
    def __init__(self, date: str, dry_run: bool = False, verbose: bool = False):
        self.date = date
        self.dry_run = dry_run
        self.verbose = verbose
        self.logger = SimpleLogger(verbose)
        self.stats = BackfillStats()
        
        # Initialize ignore_download service
        self.ignore_service = PacerIgnoreDownloadService(
            logger=self.logger,
            config={}
        )
        
        # Validate date format
        self._validate_date_format()
        
        # Setup data directory path
        self.data_dir = self._get_data_directory()
        self.date_dir = self.data_dir / self.date
        
        self.logger.log_info(f"Initialized backfill for date: {self.date}")
        self.logger.log_info(f"Data directory: {self.data_dir}")
        self.logger.log_info(f"Date directory: {self.date_dir}")
        self.logger.log_info(f"Dry run mode: {self.dry_run}")
    
    def _validate_date_format(self):
        """Validate date format is YYYYMMDD"""
        try:
            datetime.strptime(self.date, '%Y%m%d')
        except ValueError:
            raise ValueError(f"Invalid date format: {self.date}. Expected YYYYMMDD format.")
    
    def _get_data_directory(self) -> Path:
        """Get the data directory path"""
        # Try environment variable first
        env_data_dir = os.getenv('LEXGENIUS_DATA_DIR')
        if env_data_dir:
            return Path(env_data_dir)
        
        # Default fallback
        return Path.cwd() / 'data'
    
    def find_html_only_json_files(self) -> List[Path]:
        """Find JSON files without matching PDF/ZIP artifacts"""
        if not self.date_dir.exists():
            self.logger.log_error(f"Date directory does not exist: {self.date_dir}")
            return []
        
        html_only_files = []
        
        # Check if dockets subdirectory exists
        dockets_dir = self.date_dir / "dockets"
        if not dockets_dir.exists():
            self.logger.log_error(f"Dockets directory does not exist: {dockets_dir}")
            return []
        
        # Look for JSON files in the dockets subdirectory
        json_files = list(dockets_dir.glob("*.json"))
        
        self.stats.total_json_files = len(json_files)
        self.logger.log_info(f"Found {len(json_files)} JSON files in {dockets_dir}")
        
        for json_file in json_files:
            # Extract base filename without extension
            base_name = json_file.stem
            
            # Since JSON files are in dockets/, check for PDF/ZIP files in the same directory
            pdf_file = dockets_dir / f"{base_name}.pdf"
            zip_file = dockets_dir / f"{base_name}.zip"
            
            # If no artifacts found, it's HTML-only
            has_artifacts = pdf_file.exists() or zip_file.exists()
            
            if not has_artifacts:
                html_only_files.append(json_file)
                self.logger.log_debug(f"HTML-only file: {json_file.name}")
        
        self.stats.html_only_files = len(html_only_files)
        self.logger.log_info(f"Found {len(html_only_files)} HTML-only JSON files")
        
        return html_only_files
    
    def load_case_data(self, json_file: Path) -> Optional[Dict[str, Any]]:
        """Load case data from JSON file"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                case_data = json.load(f)
            
            self.logger.log_debug(f"Loaded case data from: {json_file.name}")
            return case_data
            
        except Exception as e:
            error_msg = f"Failed to load JSON: {str(e)}"
            self.logger.log_error(f"Error loading {json_file.name}: {error_msg}")
            self.stats.errors += 1
            self.stats.error_files.append((json_file.name, error_msg))
            return None
    
    def apply_mdl_inheritance(self, case_details: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Apply MDL inheritance using ignore_download matching rules"""
        try:
            # Check if case already has both mdl_num and html_only
            has_mdl = case_details.get('mdl_num')
            has_html_only = case_details.get('html_only')
            
            # If it has mdl_num but missing html_only, we still need to update
            if has_mdl and not has_html_only:
                self.logger.log_debug(f"Case has mdl_num but missing html_only: {has_mdl}")
                return True, has_mdl
            
            # If case already has both, skip
            if has_mdl and has_html_only:
                self.logger.log_debug(f"Case already has mdl_num: {has_mdl} and html_only: {has_html_only}")
                return False, None
            
            # Use ignore_download service to check for matches
            matched_entry = self.ignore_service.check_ignore_download(case_details)
            
            if matched_entry:
                config_mdl_num = matched_entry.get('mdl_num')
                if config_mdl_num:
                    self.logger.log_info(
                        f"Found ignore_download match for {case_details.get('court_id')}:"
                        f"{case_details.get('docket_num')} -> MDL: {config_mdl_num}"
                    )
                    return True, config_mdl_num
                else:
                    self.logger.log_warning(
                        f"Matched entry found but no mdl_num: {matched_entry}"
                    )
            
            return False, None
            
        except Exception as e:
            self.logger.log_error(f"Error in MDL inheritance: {str(e)}")
            return False, None
    
    def save_updated_case_data(self, json_file: Path, case_data: Dict[str, Any], mdl_num: str) -> bool:
        """Save updated case data with new mdl_num"""
        try:
            if self.dry_run:
                self.logger.log_info(f"DRY RUN: Would update {json_file.name} with mdl_num: {mdl_num} and html_only: true")
                return True
            
            # Skip backup creation per user request
            
            # Update case data
            case_data['mdl_num'] = mdl_num
            case_data['html_only'] = True
            case_data['_mdl_backfilled'] = True
            case_data['_mdl_backfilled_timestamp'] = datetime.now().isoformat()
            
            # Save updated data
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(case_data, f, indent=2, ensure_ascii=False)
            
            self.logger.log_info(f"Updated {json_file.name} with mdl_num: {mdl_num}")
            return True
            
        except Exception as e:
            error_msg = f"Failed to save updated data: {str(e)}"
            self.logger.log_error(f"Error saving {json_file.name}: {error_msg}")
            self.stats.errors += 1
            self.stats.error_files.append((json_file.name, error_msg))
            return False
    
    def process_date_directory(self):
        """Main processing logic for the specified date directory"""
        self.logger.log_info(f"Starting MDL inheritance backfill for date: {self.date}")
        
        # Find HTML-only JSON files
        html_only_files = self.find_html_only_json_files()
        
        if not html_only_files:
            self.logger.log_info("No HTML-only JSON files found. Nothing to process.")
            return
        
        # Process each file
        for json_file in html_only_files:
            self.logger.log_debug(f"Processing: {json_file.name}")
            
            # Load case data
            case_data = self.load_case_data(json_file)
            if not case_data:
                continue
            
            # Check if both mdl_num and html_only are present
            has_mdl = case_data.get('mdl_num')
            has_html_only = case_data.get('html_only')
            
            if has_mdl and has_html_only:
                self.logger.log_debug(f"Skipping {json_file.name} - already has mdl_num: {has_mdl} and html_only: {has_html_only}")
                continue
            
            # Count files that need updates
            if not has_mdl or not has_html_only:
                self.stats.files_missing_mdl += 1
            
            # Apply MDL inheritance
            needs_update, mdl_num = self.apply_mdl_inheritance(case_data)
            
            if needs_update and mdl_num:
                self.stats.files_with_matches += 1
                
                # Save updated data
                if self.save_updated_case_data(json_file, case_data, mdl_num):
                    self.stats.files_updated += 1
                    self.stats.updated_files.append((json_file.name, mdl_num))
        
        # Print summary
        self.stats.print_summary(self.logger)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Backfill MDL inheritance for HTML-only JSON files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Process files for July 15, 2025
    python scripts/utils/backfill_mdl_inheritance.py --date 20250715
    
    # Dry run to see what would be changed
    python scripts/utils/backfill_mdl_inheritance.py --date 20250715 --dry-run
    
    # Verbose output with detailed logging
    python scripts/utils/backfill_mdl_inheritance.py --date 20250715 --verbose
        """
    )
    
    parser.add_argument(
        '--date',
        required=True,
        help='Date in YYYYMMDD format to process'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be changed without making actual modifications'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging for detailed output'
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize and run backfill
        backfill = MDLInheritanceBackfill(
            date=args.date,
            dry_run=args.dry_run,
            verbose=args.verbose
        )
        
        backfill.process_date_directory()
        
    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()