#!/usr/bin/env python3
"""
Test script to validate authentication rollback fix.
This script tests the minimal authentication flow to ensure login works.
"""

import asyncio
import sys
import os
from typing import Dict, Any

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

async def test_basic_authentication():
    """Test the basic authentication flow to identify what's broken."""
    
    # Import required components
    import sys
    import os
    
    # Add the project root to the path
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    from src.containers.core import CoreContainer
    from src.pacer.components.browser.navigator import PacerNavigator
    from src.pacer.components.authentication.login_handler import LoginHandler
    from src.pacer.components.authentication.ecf_login_handler import ECFLoginHandler
    from playwright.async_api import async_playwright
    
    print("🔍 Testing basic authentication flow...")
    
    # Initialize core container
    container = CoreContainer()
    container.config.from_dict({
        'headless': False,  # Show browser for debugging
        'username_prod': os.getenv('PACER_USERNAME_PROD', 'test'),
        'password_prod': os.getenv('PACER_PASSWORD_PROD', 'test'),
    })
    container.wire(packages=["src"])
    
    logger = container.logger()
    
    try:
        # Create browser context manually
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=1000)
            context = await browser.new_browser_context()
            page = await context.new_page()
            
            # Create navigator
            navigator = PacerNavigator(page=page, logger=logger)
            
            # Test main PACER login
            print("🔐 Testing main PACER login...")
            login_handler = LoginHandler(logger=logger, config=container.config())
            
            main_login_result = await login_handler.perform_main_pacer_login(navigator)
            print(f"📊 Main login result: {main_login_result}")
            
            if main_login_result:
                # Test ECF login for a court
                print("🏛️ Testing ECF login for court...")
                court_id = "ilnd"  # Test with Illinois Northern District
                
                ecf_handler = ECFLoginHandler(logger=logger, config=container.config())
                ecf_result = await ecf_handler.perform_ecf_login_sequence(navigator, court_id)
                print(f"📊 ECF login result: {ecf_result}")
                
                if ecf_result:
                    print("✅ Authentication flow working!")
                    
                    # Test navigator handoff - create a new page and pass navigator
                    print("🔄 Testing navigator handoff...")
                    new_page = await context.new_page()
                    new_navigator = PacerNavigator(page=new_page, logger=logger)
                    
                    # Navigate to a simple page to test navigator functionality
                    await new_navigator.goto("https://ecf.ilnd.uscourts.gov/")
                    current_url = new_navigator.page.url
                    print(f"📍 Navigator handoff test URL: {current_url}")
                    
                    if "ecf.ilnd.uscourts.gov" in current_url:
                        print("✅ Navigator handoff working!")
                        return True
                    else:
                        print("❌ Navigator handoff failed!")
                        return False
                else:
                    print("❌ ECF login failed!")
                    return False
            else:
                print("❌ Main PACER login failed!")
                return False
                
    except Exception as e:
        print(f"❌ Authentication test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'browser' in locals():
            await browser.close()

if __name__ == "__main__":
    print("🚀 Starting authentication rollback test...")
    
    # Check environment variables
    if not os.getenv('PACER_USERNAME_PROD') or not os.getenv('PACER_PASSWORD_PROD'):
        print("⚠️  Warning: PACER credentials not found in environment")
        print("   Set PACER_USERNAME_PROD and PACER_PASSWORD_PROD")
    
    result = asyncio.run(test_basic_authentication())
    
    if result:
        print("\n✅ Authentication test PASSED!")
        sys.exit(0)
    else:
        print("\n❌ Authentication test FAILED!")
        sys.exit(1)