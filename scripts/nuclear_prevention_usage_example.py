#!/usr/bin/env python3
"""
Nuclear Lexgenius Prevention - Usage Example

This script demonstrates how to use the nuclear prevention system
in your own scripts to ensure NO lexgenius.log files are created.
"""

import os
import sys

# Setup proper imports
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)
os.environ['PYTHONPATH'] = src_path


def example_1_simple_activation():
    """Example 1: Simple nuclear activation at script start."""
    print("📋 EXAMPLE 1: Simple Activation")
    print("-" * 30)
    
    from pacer.utils.court_logger import activate_nuclear_lexgenius_prevention
    
    # Activate nuclear prevention at the start of your script
    result = activate_nuclear_lexgenius_prevention()
    print(f"✅ Nuclear prevention activated: {result['status']}")
    
    # Now any lexgenius loggers will be neutered
    import logging
    test_logger = logging.getLogger("lexgenius.my_script")
    test_logger.info("This will NOT create a lexgenius.log file!")
    
    print("✅ Logging intercepted - no lexgenius.log file created")


def example_2_context_manager():
    """Example 2: Using the nuclear context manager."""
    print("\\n📋 EXAMPLE 2: Context Manager")
    print("-" * 30)
    
    from pacer.utils.court_logger import nuclear_lexgenius_prevention
    
    # Use context manager for complete protection
    with nuclear_lexgenius_prevention():
        print("🚨 Inside nuclear prevention context")
        
        # All logging within this block is intercepted
        import logging
        
        loggers_to_test = [
            "lexgenius",
            "lexgenius.processing", 
            "lexgenius.main",
            "some.other.logger"  # Non-lexgenius loggers are not affected
        ]
        
        for logger_name in loggers_to_test:
            logger = logging.getLogger(logger_name)
            logger.info(f"Test message from {logger_name}")
            
            if 'lexgenius' in logger_name and (not logger.handlers or 
                all(isinstance(h, logging.NullHandler) for h in logger.handlers)):
                print(f"  ✅ {logger_name}: Intercepted")
            elif 'lexgenius' not in logger_name:
                print(f"  ℹ️ {logger_name}: Not a lexgenius logger (unaffected)")
            else:
                print(f"  ⚠️ {logger_name}: May not be fully intercepted")
    
    print("🔓 Exited nuclear prevention context")


def example_3_emergency_cleanup():
    """Example 3: Emergency cleanup of existing files."""
    print("\\n📋 EXAMPLE 3: Emergency Cleanup")
    print("-" * 30)
    
    from pacer.utils.court_logger import emergency_delete_all_lexgenius_logs
    
    # Delete any existing lexgenius.log files
    deleted_files = emergency_delete_all_lexgenius_logs()
    
    if deleted_files:
        print(f"🗑️ Deleted {len(deleted_files)} lexgenius log files:")
        for file in deleted_files:
            print(f"  - {file}")
    else:
        print("✅ No lexgenius.log files found to delete")


def example_4_pacer_integration():
    """Example 4: Integration with PACER processing."""
    print("\\n📋 EXAMPLE 4: PACER Integration")
    print("-" * 30)
    
    from pacer.utils.court_logger import (
        nuclear_lexgenius_prevention,
        create_court_logger
    )
    
    # Example PACER processing with nuclear prevention
    court_id = "ilnd"
    iso_date = "20250810"
    config = {"DATA_DIR": "data"}
    
    # Use nuclear prevention context for PACER operations
    with nuclear_lexgenius_prevention():
        print(f"🏛️ Processing court {court_id.upper()}")
        
        # Create court-specific logger (this will work normally)
        court_logger = create_court_logger(court_id, iso_date, config)
        
        # Court logger works normally - logs go to court-specific file
        court_logger.log_phase_start("Demo Processing", {"court": court_id})
        court_logger.get_logger().info("This goes to court-specific log file")
        court_logger.log_phase_complete("Demo Processing")
        
        print(f"✅ Court processing logged to: data/{iso_date}/logs/pacer/{court_id}.log")
        
        # Any lexgenius loggers are still intercepted
        import logging
        lexgenius_logger = logging.getLogger("lexgenius.pacer.processing")
        lexgenius_logger.error("This is intercepted and will NOT create lexgenius.log")
        
        print("✅ All lexgenius logging intercepted during court processing")


def example_5_import_time_activation():
    """Example 5: Automatic activation on import."""
    print("\\n📋 EXAMPLE 5: Auto-Activation")
    print("-" * 30)
    
    # You can create a module that auto-activates nuclear prevention
    print("💡 To auto-activate nuclear prevention in your scripts:")
    print("   1. Import: from scripts.activate_nuclear_prevention import activate")
    print("   2. Or run: python scripts/activate_nuclear_prevention.py")
    print("   3. Or set env var to avoid auto-activation: NO_AUTO_ACTIVATE=1")
    
    # Demonstrate auto-activation
    try:
        # This import will auto-activate nuclear prevention
        sys.modules.pop('scripts.activate_nuclear_prevention', None)  # Force reimport
        from scripts.activate_nuclear_prevention import activate
        print("✅ Auto-activation imported successfully")
        
        # Test that it worked
        import logging
        test_logger = logging.getLogger("lexgenius.auto.test")
        test_logger.info("Auto-activation test")
        
        if not test_logger.handlers or all(isinstance(h, logging.NullHandler) for h in test_logger.handlers):
            print("✅ Auto-activation working - logger intercepted")
        else:
            print("⚠️ Auto-activation may not be fully working")
            
    except ImportError as e:
        print(f"⚠️ Auto-activation import failed: {e}")


def main():
    """Run all usage examples."""
    print("🚨 NUCLEAR LEXGENIUS PREVENTION - USAGE EXAMPLES 🚨")
    print("=" * 60)
    
    try:
        example_1_simple_activation()
        example_2_context_manager()
        example_3_emergency_cleanup()
        example_4_pacer_integration()
        example_5_import_time_activation()
        
        print("\\n" + "=" * 60)
        print("🎉 ALL USAGE EXAMPLES COMPLETED!")
        print("=" * 60)
        
        print("\\n💡 QUICK REFERENCE:")
        print("  🚨 Nuclear activation:     activate_nuclear_lexgenius_prevention()")
        print("  🔒 Context manager:        with nuclear_lexgenius_prevention():")
        print("  🗑️ Emergency cleanup:      emergency_delete_all_lexgenius_logs()")
        print("  🔍 Continuous monitoring:  CourtLogger.continuously_delete_lexgenius_logs()")
        print("  📋 Court logging:          create_court_logger(court_id, iso_date, config)")
        
        print("\\n🛡️ PROTECTION GUARANTEED: lexgenius.log files CANNOT be created!")
        
    except Exception as e:
        print(f"❌ Example failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()