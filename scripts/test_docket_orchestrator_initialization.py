#!/usr/bin/env python3
"""
Test script to verify DocketOrchestrator service initialization fix.

This script tests that the _setup_dependencies method properly initializes services
and handles the _initialized flag correctly.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.pacer.facades.docket_orchestrator import DocketOrchestrator
from src.pacer.facades.case_processing_service import CaseProcessingService
from src.pacer.facades.relevance_service import RelevanceService
from src.pacer.facades.classification_service import ClassificationService
from src.pacer.facades.verification_service import VerificationService
from src.pacer.facades.download_service import DownloadService
from src.pacer.facades.file_operations_service import FileOperationsService
from src.pacer.facades.configuration_service import ConfigurationService
from src.pacer.facades.browser_service import BrowserService


def setup_logging():
    """Setup basic logging for testing."""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


async def test_docket_orchestrator_initialization():
    """Test that DocketOrchestrator properly initializes services."""
    logger = setup_logging()
    logger.info("Starting DocketOrchestrator initialization test")
    
    try:
        # Create service instances (without initializing them)
        config = {"data_path": "./data"}
        
        case_processing = CaseProcessingService(logger=logger, config=config)
        relevance = RelevanceService(logger=logger, config=config)
        classification = ClassificationService(logger=logger, config=config)
        verification = VerificationService(logger=logger, config=config)
        download = DownloadService(logger=logger, config=config)
        file_ops = FileOperationsService(logger=logger)
        configuration = ConfigurationService(logger=logger, config=config)
        browser = BrowserService(logger=logger, config=config)
        
        # Verify services are not initialized yet
        services_to_check = [
            ("case_processing", case_processing),
            ("relevance", relevance),
            ("classification", classification), 
            ("verification", verification),
            ("download", download),
            ("file_ops", file_ops),
            ("configuration", configuration),
            ("browser", browser),
        ]
        
        logger.info("Checking initial service states:")
        for name, service in services_to_check:
            initialized = getattr(service, '_initialized', None)
            has_flag = hasattr(service, '_initialized')
            has_method = hasattr(service, 'initialize')
            logger.info(f"  {name}: _initialized={initialized}, has_flag={has_flag}, has_method={has_method}")
        
        # Create DocketOrchestrator with all services
        orchestrator = DocketOrchestrator(
            case_processing_service=case_processing,
            relevance_service=relevance,
            classification_service=classification,
            verification_service=verification,
            download_service=download,
            file_operations_service=file_ops,
            configuration_service=configuration,
            browser_service=browser,
            logger=logger,
            config=config
        )
        
        # Check orchestrator initial state
        logger.info(f"DocketOrchestrator initial state: _initialized={getattr(orchestrator, '_initialized', None)}")
        
        # Initialize the orchestrator (this should call _setup_dependencies)
        logger.info("Initializing DocketOrchestrator...")
        await orchestrator.initialize()
        
        # Check orchestrator after initialization
        logger.info(f"DocketOrchestrator after init: _initialized={getattr(orchestrator, '_initialized', None)}")
        
        # Verify all services are now initialized
        logger.info("Checking service states after orchestrator initialization:")
        for name, service in services_to_check:
            initialized = getattr(service, '_initialized', None)
            logger.info(f"  {name}: _initialized={initialized}")
            
            if hasattr(service, '_initialized') and not service._initialized:
                logger.error(f"  ERROR: Service {name} was not properly initialized!")
                return False
            elif not hasattr(service, '_initialized'):
                logger.warning(f"  WARNING: Service {name} doesn't have _initialized flag")
        
        # Test double initialization (should be safe)
        logger.info("Testing double initialization...")
        await orchestrator.initialize()
        logger.info("Double initialization completed successfully")
        
        # Cleanup
        await orchestrator.cleanup()
        logger.info("Test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Test failed with exception: {e}", exc_info=True)
        return False


async def main():
    """Main test function."""
    success = await test_docket_orchestrator_initialization()
    if success:
        print("✅ DocketOrchestrator initialization test PASSED")
        return 0
    else:
        print("❌ DocketOrchestrator initialization test FAILED")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)