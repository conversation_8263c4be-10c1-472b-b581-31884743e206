#!/usr/bin/env python3
"""
Nuclear Lexgenius Prevention Demonstration

This script demonstrates the complete nuclear prevention system for lexgenius.log.
"""

import os
import sys
import logging
import tempfile
import time

# Set proper PYTHONPATH for imports
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)
os.environ['PYTHONPATH'] = src_path

print("🚨 NUCLEAR LEXGENIUS PREVENTION DEMONSTRATION 🚨")
print("=" * 60)

# Import nuclear prevention functions
try:
    from pacer.utils.court_logger import (
        nuclear_lexgenius_prevention,
        activate_nuclear_lexgenius_prevention,
        emergency_delete_all_lexgenius_logs,
        CourtLogger,
    )
    print("✅ Successfully imported nuclear prevention functions")
except ImportError as e:
    print(f"❌ Import failed: {e}")
    sys.exit(1)


def create_test_lexgenius_files(base_dir: str) -> list:
    """Create test lexgenius.log files for demonstration."""
    test_files = []
    test_paths = [
        os.path.join(base_dir, "lexgenius.log"),
        os.path.join(base_dir, "lexgenius-2025.log"),
        os.path.join(base_dir, "logs", "lexgenius.log"),
        os.path.join(base_dir, "data", "logs", "lexgenius.log"),
    ]
    
    for path in test_paths:
        try:
            os.makedirs(os.path.dirname(path), exist_ok=True)
            with open(path, 'w') as f:
                f.write(f"Test lexgenius log content - {time.time()}\\n")
            test_files.append(path)
            print(f"  Created: {path}")
        except Exception as e:
            print(f"  Failed to create {path}: {e}")
    
    return test_files


def demo_emergency_deletion():
    """Demonstrate emergency deletion of lexgenius.log files."""
    print("\\n📋 DEMO 1: Emergency Deletion")
    print("-" * 40)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Test directory: {temp_dir}")
        
        # Create test files
        test_files = create_test_lexgenius_files(temp_dir)
        print(f"Created {len(test_files)} test files")
        
        # Demonstrate emergency deletion
        deleted_files = emergency_delete_all_lexgenius_logs(temp_dir)
        print(f"\\n🗑️ Emergency deletion removed {len(deleted_files)} files:")
        for file in deleted_files:
            rel_path = os.path.relpath(file, temp_dir)
            print(f"  ✅ Deleted: {rel_path}")
        
        # Verify files are gone
        remaining = []
        for file in test_files:
            if os.path.exists(file):
                remaining.append(file)
        
        if remaining:
            print(f"⚠️ {len(remaining)} files still exist")
        else:
            print("🎉 All lexgenius log files successfully deleted!")


def demo_nuclear_activation():
    """Demonstrate nuclear prevention activation."""
    print("\\n🚀 DEMO 2: Nuclear Prevention Activation")
    print("-" * 40)
    
    # Demonstrate nuclear activation
    result = activate_nuclear_lexgenius_prevention(project_root)
    
    print("Nuclear prevention results:")
    print(f"  Status: {result['status']}")
    print(f"  Removed handlers: {len(result['removed_handlers'])}")
    print(f"  Deleted files: {len(result['deleted_files'])}")
    
    if result['removed_handlers']:
        print("  Removed handlers:")
        for logger_name, handler in result['removed_handlers']:
            print(f"    - {logger_name}: {handler}")
    
    if result['deleted_files']:
        print("  Deleted files:")
        for file in result['deleted_files']:
            print(f"    - {file}")


def demo_logger_interception():
    """Demonstrate lexgenius logger interception."""
    print("\\n🛡️ DEMO 3: Logger Interception")
    print("-" * 40)
    
    # Create various lexgenius loggers and test them
    test_loggers = [
        "lexgenius",
        "lexgenius.main",
        "lexgenius.fallback",
        "lexgenius.core",
        "lexgenius.services.test",
    ]
    
    print("Testing logger interception:")
    for logger_name in test_loggers:
        logger = logging.getLogger(logger_name)
        
        # Test logging
        logger.info(f"Test message from {logger_name}")
        logger.error(f"Test error from {logger_name}")
        
        # Check if intercepted
        if not logger.handlers:
            status = "✅ NO HANDLERS (intercepted)"
        elif all(isinstance(h, logging.NullHandler) for h in logger.handlers):
            status = "✅ NULL HANDLERS (intercepted)"
        else:
            status = f"⚠️ HAS HANDLERS: {[type(h).__name__ for h in logger.handlers]}"
        
        print(f"  {logger_name}: {status}")


def demo_context_manager():
    """Demonstrate nuclear prevention context manager."""
    print("\\n🔒 DEMO 4: Nuclear Prevention Context Manager")
    print("-" * 40)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Test directory: {temp_dir}")
        
        # Create some test files first
        test_files = create_test_lexgenius_files(temp_dir)
        print(f"Created {len(test_files)} test files before context")
        
        # Use nuclear prevention context
        with nuclear_lexgenius_prevention(base_path=temp_dir, continuous_monitoring=False):
            print("\\n🚨 Inside nuclear prevention context:")
            
            # Try to create loggers
            test_logger = logging.getLogger("lexgenius.context.test")
            test_logger.info("This message should be intercepted")
            test_logger.warning("This warning should be intercepted")
            
            # Try to manually create a log file (should be deleted)
            try:
                manual_log = os.path.join(temp_dir, "manual_lexgenius.log")
                with open(manual_log, 'w') as f:
                    f.write("Manually created lexgenius log\\n")
                print(f"  Created manual log: {manual_log}")
            except Exception as e:
                print(f"  Failed to create manual log: {e}")
            
            # Check logger state
            if not test_logger.handlers:
                print("  ✅ Context logger has no handlers (intercepted)")
            else:
                print(f"  ⚠️ Context logger has handlers: {test_logger.handlers}")
        
        print("\\n🔓 Exited nuclear prevention context")
        
        # Check what files remain
        remaining_files = []
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                if 'lexgenius' in file.lower():
                    remaining_files.append(os.path.join(root, file))
        
        if remaining_files:
            print(f"Files remaining after context:")
            for file in remaining_files:
                rel_path = os.path.relpath(file, temp_dir)
                print(f"  - {rel_path}")
        else:
            print("✅ No lexgenius files remaining after context!")


def demo_continuous_monitoring():
    """Demonstrate continuous monitoring (brief demo)."""
    print("\\n🔍 DEMO 5: Continuous Monitoring (Brief)")
    print("-" * 40)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Test directory: {temp_dir}")
        
        # Start monitoring
        monitor_thread = CourtLogger.continuously_delete_lexgenius_logs(temp_dir, interval=1)
        print("Started continuous monitoring thread...")
        
        # Create files and see them get deleted
        for i in range(3):
            test_file = os.path.join(temp_dir, f"lexgenius-monitor-{i}.log")
            
            try:
                with open(test_file, 'w') as f:
                    f.write(f"Monitor test file {i}\\n")
                print(f"  Created: {os.path.basename(test_file)}")
                
                # Wait a moment for monitoring to catch it
                time.sleep(1.5)
                
                if os.path.exists(test_file):
                    print(f"  ⚠️ File still exists: {os.path.basename(test_file)}")
                else:
                    print(f"  ✅ File deleted by monitor: {os.path.basename(test_file)}")
                    
            except Exception as e:
                print(f"  Error in monitoring test: {e}")
        
        print("Continuous monitoring demo complete")


def main():
    """Run the complete nuclear prevention demonstration."""
    try:
        # Demo 1: Emergency deletion
        demo_emergency_deletion()
        
        # Demo 2: Nuclear activation
        demo_nuclear_activation()
        
        # Demo 3: Logger interception
        demo_logger_interception()
        
        # Demo 4: Context manager
        demo_context_manager()
        
        # Demo 5: Continuous monitoring
        demo_continuous_monitoring()
        
        print("\\n" + "=" * 60)
        print("🎉 NUCLEAR PREVENTION DEMONSTRATION COMPLETE!")
        print("=" * 60)
        print("\\n💡 Summary of Nuclear Prevention Features:")
        print("  1. ✅ Emergency deletion of all lexgenius.log files")
        print("  2. ✅ Handler removal from all lexgenius loggers")
        print("  3. ✅ Logger interception and neutering")
        print("  4. ✅ Context manager for complete prevention")
        print("  5. ✅ Continuous monitoring and deletion")
        print("  6. ✅ Patched getLogger to prevent new lexgenius loggers")
        print("\\n🚨 Result: lexgenius.log creation is IMPOSSIBLE! 🚨")
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()