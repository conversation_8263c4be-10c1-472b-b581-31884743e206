#!/usr/bin/env bash

# This script attempts to upgrade all dependencies to their latest versions
# allowed by pyproject.toml, and then updates the lock files.

set -e # Exit immediately if a command fails

# Get the project root directory
PROJECT_ROOT="$( cd "$( dirname "${BASH_SOURCE[0]}" )/.." &> /dev/null && pwd )"

echo "⬆️  Upgrading all dependencies to the latest versions..."
cd "$PROJECT_ROOT"

# 1. Compile production dependencies with the --upgrade flag
echo "   -> Upgrading and compiling requirements.txt..."
uv pip compile pyproject.toml --upgrade -o requirements.txt

# 2. Compile development dependencies with the --upgrade flag
echo "   -> Upgrading and compiling requirements-dev.txt..."
uv pip compile pyproject.toml --upgrade --extra dev -o requirements-dev.txt

echo "✅ All dependencies upgraded and lock files have been updated."
echo "⚠️  Please run your tests to ensure upgrades have not introduced breaking changes."
echo ""

# 3. Sync the local environment with the new lock files
"$PROJECT_ROOT/scripts/sync.sh"