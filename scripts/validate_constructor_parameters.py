#!/usr/bin/env python3
"""
Script to validate that all service constructors are called with correct parameter names.
"""

import re
import os
from pathlib import Path

def find_constructor_issues():
    """Find service constructor calls with incorrect parameters."""
    src_path = Path("/Users/<USER>/PycharmProjects/lexgenius/src")
    issues = []
    
    # Pattern to match service constructors with court_logger=court_logger
    service_pattern = r'(\w*Service)\([^)]*court_logger=court_logger[^)]*\)'
    
    for py_file in src_path.rglob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                # Check for service constructor calls with court_logger=court_logger
                matches = re.findall(service_pattern, line)
                if matches:
                    issues.append({
                        'file': str(py_file),
                        'line': line_num,
                        'content': line.strip(),
                        'service': matches[0],
                        'issue': 'Service constructor called with court_logger=court_logger instead of logger=court_logger'
                    })
                    
        except Exception as e:
            print(f"Error reading {py_file}: {e}")
    
    return issues

def validate_specific_services():
    """Validate specific services mentioned in the issue."""
    src_path = Path("/Users/<USER>/PycharmProjects/lexgenius/src")
    target_services = ['RelevanceService', 'ConfigurationService']
    issues = []
    
    for py_file in src_path.rglob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                for service in target_services:
                    # Look for service constructor calls
                    if f"{service}(" in line:
                        # Check if it has court_logger=court_logger
                        if "court_logger=court_logger" in line:
                            issues.append({
                                'file': str(py_file),
                                'line': line_num,
                                'content': line.strip(),
                                'service': service,
                                'issue': f'{service} constructor called with court_logger=court_logger instead of logger=court_logger'
                            })
                            
        except Exception as e:
            print(f"Error reading {py_file}: {e}")
    
    return issues

def main():
    print("🔍 Validating service constructor parameters...")
    
    # Find general constructor issues
    print("\n1. Checking for service constructors with court_logger=court_logger...")
    general_issues = find_constructor_issues()
    
    if general_issues:
        print(f"❌ Found {len(general_issues)} general constructor issues:")
        for issue in general_issues:
            print(f"  - {issue['file']}:{issue['line']}")
            print(f"    {issue['content']}")
            print(f"    Issue: {issue['issue']}")
            print()
    else:
        print("✅ No general constructor issues found")
    
    # Check specific services
    print("2. Checking RelevanceService and ConfigurationService specifically...")
    specific_issues = validate_specific_services()
    
    if specific_issues:
        print(f"❌ Found {len(specific_issues)} specific service issues:")
        for issue in specific_issues:
            print(f"  - {issue['file']}:{issue['line']}")
            print(f"    {issue['content']}")
            print(f"    Issue: {issue['issue']}")
            print()
    else:
        print("✅ No specific service constructor issues found")
    
    total_issues = len(general_issues) + len(specific_issues)
    if total_issues == 0:
        print("\n🎉 All service constructors appear to be called correctly!")
        print("   Services are using logger= parameter as expected.")
    else:
        print(f"\n⚠️  Found {total_issues} constructor issues that need fixing.")
        
    return total_issues == 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)