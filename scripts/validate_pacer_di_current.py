#!/usr/bin/env python3
"""
PACER Dependency Injection Current State Validation Script

This script analyzes the current PACER component architecture to identify
specific dependency injection inconsistencies that need to be fixed.
"""

import os
import sys
import ast
import inspect
import importlib.util
from pathlib import Path
from typing import Dict, List, Set, Tuple, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

class PacerDIAnalyzer:
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.src_path = project_root / "src"
        self.issues: List[Dict[str, Any]] = []
        
    def analyze(self) -> Dict[str, Any]:
        """Run complete analysis of PACER DI architecture."""
        print("🔍 Analyzing PACER Dependency Injection Architecture...")
        
        results = {
            "component_issues": self._analyze_component_constructors(),
            "container_issues": self._analyze_container_providers(),
            "import_issues": self._analyze_imports(),
            "pattern_inconsistencies": self._analyze_patterns(),
            "summary": {}
        }
        
        # Generate summary
        total_issues = sum(len(issues) for issues in results.values() if isinstance(issues, list))
        results["summary"] = {
            "total_issues": total_issues,
            "critical_issues": len([i for i in self.issues if i.get("severity") == "critical"]),
            "high_issues": len([i for i in self.issues if i.get("severity") == "high"]),
            "medium_issues": len([i for i in self.issues if i.get("severity") == "medium"]),
        }
        
        return results
    
    def _analyze_component_constructors(self) -> List[Dict[str, str]]:
        """Analyze ComponentImplementation subclass constructors."""
        print("  📝 Analyzing component constructors...")
        issues = []
        
        # Find all PACER component files
        pacer_components = list(self.src_path.glob("**/pacer/components/**/*.py"))
        
        for component_file in pacer_components:
            if component_file.name == "__init__.py":
                continue
                
            try:
                issues.extend(self._analyze_single_component(component_file))
            except Exception as e:
                issues.append({
                    "file": str(component_file),
                    "issue": f"Failed to analyze: {e}",
                    "severity": "medium"
                })
        
        return issues
    
    def _analyze_single_component(self, file_path: Path) -> List[Dict[str, str]]:
        """Analyze a single component file."""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    # Check if inherits from ComponentImplementation
                    inherits_component = any(
                        isinstance(base, ast.Name) and base.id == "ComponentImplementation"
                        or isinstance(base, ast.Attribute) and base.attr == "ComponentImplementation"
                        for base in node.bases
                    )
                    
                    if inherits_component:
                        issues.extend(self._analyze_class_constructor(node, file_path))
        
        except Exception as e:
            issues.append({
                "file": str(file_path),
                "class": "unknown",
                "issue": f"Parse error: {e}",
                "severity": "medium"
            })
        
        return issues
    
    def _analyze_class_constructor(self, class_node: ast.ClassDef, file_path: Path) -> List[Dict[str, str]]:
        """Analyze constructor of a ComponentImplementation class."""
        issues = []
        
        # Find __init__ method
        init_method = None
        for node in class_node.body:
            if isinstance(node, ast.FunctionDef) and node.name == "__init__":
                init_method = node
                break
        
        if not init_method:
            issues.append({
                "file": str(file_path),
                "class": class_node.name,
                "issue": "No __init__ method found",
                "severity": "high"
            })
            return issues
        
        # Analyze parameters
        params = [arg.arg for arg in init_method.args.args[1:]]  # Skip 'self'
        
        # Check for required parameters
        if "logger" not in params:
            issues.append({
                "file": str(file_path),
                "class": class_node.name,
                "issue": "Missing 'logger' parameter in constructor",
                "severity": "critical",
                "fix": "Add 'logger: LoggerProtocol' parameter"
            })
        
        if "config" not in params:
            issues.append({
                "file": str(file_path),
                "class": class_node.name,
                "issue": "Missing 'config' parameter in constructor",
                "severity": "critical",
                "fix": "Add 'config: Dict[str, Any]' parameter"
            })
        
        # Check parameter order (logger and config should be last)
        if "logger" in params and "config" in params:
            logger_idx = params.index("logger")
            config_idx = params.index("config")
            
            if logger_idx < len(params) - 2 or config_idx < len(params) - 2:
                issues.append({
                    "file": str(file_path),
                    "class": class_node.name,
                    "issue": "logger and config should be last parameters",
                    "severity": "medium",
                    "fix": "Move logger and config to end of parameter list"
                })
        
        # Check for super().__init__ call
        has_super_init = False
        for node in ast.walk(init_method):
            if isinstance(node, ast.Call) and isinstance(node.func, ast.Call):
                if (isinstance(node.func.func, ast.Name) and node.func.func.id == "super"):
                    has_super_init = True
                    break
        
        if not has_super_init:
            issues.append({
                "file": str(file_path),
                "class": class_node.name,
                "issue": "Missing super().__init__() call",
                "severity": "critical",
                "fix": "Add super().__init__(logger, config) call"
            })
        
        return issues
    
    def _analyze_container_providers(self) -> List[Dict[str, str]]:
        """Analyze container provider definitions."""
        print("  🏗️  Analyzing container providers...")
        issues = []
        
        # Analyze both PACER containers
        container_files = [
            self.src_path / "containers" / "pacer.py",
            self.src_path / "containers" / "pacer_core.py"
        ]
        
        for container_file in container_files:
            if container_file.exists():
                issues.extend(self._analyze_container_file(container_file))
        
        return issues
    
    def _analyze_container_file(self, file_path: Path) -> List[Dict[str, str]]:
        """Analyze a single container file."""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for provider definitions that might be missing logger/config
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                if "= providers." in line and "Singleton" in line:
                    # Check if this is a ComponentImplementation provider
                    provider_name = line.split('=')[0].strip()
                    
                    # Look for logger/config in the provider definition
                    provider_block = self._extract_provider_block(lines, i)
                    
                    if "logger=" not in provider_block:
                        issues.append({
                            "file": str(file_path),
                            "provider": provider_name,
                            "issue": "Provider missing logger injection",
                            "severity": "high",
                            "line": i + 1,
                            "fix": "Add logger=logger to provider arguments"
                        })
                    
                    if "config=" not in provider_block:
                        issues.append({
                            "file": str(file_path),
                            "provider": provider_name,
                            "issue": "Provider missing config injection",
                            "severity": "high", 
                            "line": i + 1,
                            "fix": "Add config=config to provider arguments"
                        })
        
        except Exception as e:
            issues.append({
                "file": str(file_path),
                "issue": f"Failed to analyze container: {e}",
                "severity": "medium"
            })
        
        return issues
    
    def _extract_provider_block(self, lines: List[str], start_idx: int) -> str:
        """Extract the full provider definition block."""
        block = lines[start_idx]
        
        # If the line doesn't end with ), keep reading
        if not block.rstrip().endswith(')'):
            for i in range(start_idx + 1, min(start_idx + 10, len(lines))):
                block += lines[i]
                if lines[i].rstrip().endswith(')'):
                    break
        
        return block
    
    def _analyze_imports(self) -> List[Dict[str, str]]:
        """Analyze import patterns for containers."""
        print("  📦 Analyzing import patterns...")
        issues = []
        
        # Check for duplicate container imports
        container_files = [
            self.src_path / "containers" / "pacer.py",
            self.src_path / "containers" / "pacer_core.py"
        ]
        
        both_exist = all(f.exists() for f in container_files)
        if both_exist:
            issues.append({
                "issue": "Both PacerContainer and PacerCoreContainer exist",
                "severity": "critical",
                "fix": "Merge PacerCoreContainer into PacerContainer and remove duplicate"
            })
        
        # Check for inconsistent import usage
        import_files = list(self.src_path.glob("**/*.py"))
        pacer_core_imports = []
        pacer_imports = []
        
        for file_path in import_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if "from src.containers.pacer_core import" in content:
                    pacer_core_imports.append(str(file_path))
                if "from src.containers.pacer import" in content:
                    pacer_imports.append(str(file_path))
            except:
                pass
        
        if pacer_core_imports and pacer_imports:
            issues.append({
                "issue": f"Mixed container imports: {len(pacer_imports)} files import pacer, {len(pacer_core_imports)} import pacer_core",
                "severity": "high",
                "pacer_imports": len(pacer_imports),
                "pacer_core_imports": len(pacer_core_imports),
                "fix": "Standardize on single container"
            })
        
        return issues
    
    def _analyze_patterns(self) -> List[Dict[str, str]]:
        """Analyze architectural patterns and consistency."""
        print("  🏛️  Analyzing architectural patterns...")
        issues = []
        
        # Check for mixed DI patterns
        factory_files = list(self.src_path.glob("**/factories/**/*.py"))
        
        for factory_file in factory_files:
            try:
                with open(factory_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Look for manual component instantiation
                if "ComponentImplementation(" in content:
                    issues.append({
                        "file": str(factory_file),
                        "issue": "Manual ComponentImplementation instantiation in factory",
                        "severity": "medium",
                        "fix": "Use container-based component creation"
                    })
                
            except:
                pass
        
        return issues
    
    def generate_report(self, results: Dict[str, Any]) -> None:
        """Generate human-readable report."""
        print("\n" + "="*80)
        print("🔍 PACER DEPENDENCY INJECTION ANALYSIS REPORT")
        print("="*80)
        
        summary = results["summary"]
        print(f"\n📊 SUMMARY:")
        print(f"  Total Issues: {summary['total_issues']}")
        print(f"  Critical:     {summary['critical_issues']}")  
        print(f"  High:         {summary['high_issues']}")
        print(f"  Medium:       {summary['medium_issues']}")
        
        # Component Issues
        if results["component_issues"]:
            print(f"\n❌ COMPONENT CONSTRUCTOR ISSUES ({len(results['component_issues'])}):")
            for issue in results["component_issues"][:10]:  # Show top 10
                print(f"  • {issue['class']} in {Path(issue['file']).name}")
                print(f"    Issue: {issue['issue']}")
                if "fix" in issue:
                    print(f"    Fix: {issue['fix']}")
                print()
        
        # Container Issues
        if results["container_issues"]:
            print(f"\n🏗️  CONTAINER PROVIDER ISSUES ({len(results['container_issues'])}):")
            for issue in results["container_issues"][:10]:  # Show top 10
                container_name = Path(issue['file']).name
                print(f"  • {issue.get('provider', 'Unknown')} in {container_name}")
                print(f"    Issue: {issue['issue']}")
                if "fix" in issue:
                    print(f"    Fix: {issue['fix']}")
                print()
        
        # Import Issues
        if results["import_issues"]:
            print(f"\n📦 IMPORT PATTERN ISSUES ({len(results['import_issues'])}):")
            for issue in results["import_issues"]:
                print(f"  • {issue['issue']}")
                if "fix" in issue:
                    print(f"    Fix: {issue['fix']}")
                print()
        
        # Recommendations
        print(f"\n💡 IMMEDIATE ACTION ITEMS:")
        critical_issues = [i for sublist in results.values() if isinstance(sublist, list) 
                          for i in sublist if isinstance(i, dict) and i.get("severity") == "critical"]
        
        if critical_issues:
            print("  CRITICAL (Fix Immediately):")
            for issue in critical_issues[:5]:
                if "class" in issue:
                    print(f"    - Fix {issue['class']} constructor")
                else:
                    print(f"    - {issue['issue']}")
        
        high_issues = [i for sublist in results.values() if isinstance(sublist, list)
                      for i in sublist if isinstance(i, dict) and i.get("severity") == "high"]
        
        if high_issues:
            print("  HIGH PRIORITY (Fix This Week):")
            for issue in high_issues[:5]:
                if "provider" in issue:
                    print(f"    - Update {issue['provider']} container provider")
                else:
                    print(f"    - {issue['issue']}")
        
        print(f"\n✅ NEXT STEPS:")
        print("  1. Fix critical ComponentImplementation constructor issues")
        print("  2. Update container providers to inject logger/config consistently") 
        print("  3. Merge PacerCoreContainer into PacerContainer")
        print("  4. Standardize component creation patterns in factories")
        print("  5. Run validation again to confirm fixes")


def main():
    """Run the PACER DI analysis."""
    project_root = Path(__file__).parent.parent
    analyzer = PacerDIAnalyzer(project_root)
    
    try:
        results = analyzer.analyze()
        analyzer.generate_report(results)
        
        # Exit with error code if critical issues found
        if results["summary"]["critical_issues"] > 0:
            print("\n🚨 CRITICAL ISSUES FOUND - Architecture needs immediate attention!")
            sys.exit(1)
        elif results["summary"]["total_issues"] > 0:
            print("\n⚠️  Issues found - See report above for details")
            sys.exit(1)  
        else:
            print("\n✅ No DI architecture issues found!")
            
    except Exception as e:
        print(f"\n💥 Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()