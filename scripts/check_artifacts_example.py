#!/usr/bin/env python3
"""
Example script demonstrating DocketArtifactChecker usage.
This script checks a list of dockets and reports which need downloading.
"""
import asyncio
import json
import logging
import os
from datetime import datetime
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add project root to path
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.pacer.components.download.artifact_checker import DocketArtifactChecker
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Sample docket data (replace with actual data)
SAMPLE_DOCKETS = [
    {
        "court_id": "cand",
        "docket_num": "3:25-cv-12345",
        "versus": "Smith v. Jones Corporation",
        "filing_date": "2025-07-15"
    },
    {
        "court_id": "njd",
        "docket_num": "2:25-cv-54321",
        "versus": "Johnson v. Johnson & Johnson",
        "filing_date": "2025-07-14"
    },
    {
        "court_id": "nysd",
        "docket_num": "1:25-cv-98765",
        "versus": "Doe v. Tech Corp et al",
        "filing_date": "2025-07-13"
    }
]


async def check_artifacts_main():
    """Main function to check docket artifacts."""
    console = Console()
    
    # Configuration
    iso_date = datetime.now().strftime("%Y%m%d")
    config = {
        'iso_date': iso_date,
        'aws_region': os.getenv('AWS_DEFAULT_REGION', 'us-east-1')
    }
    
    console.print(f"[bold blue]Docket Artifact Checker[/bold blue]")
    console.print(f"ISO Date: {iso_date}")
    console.print(f"Data Directory: data/{iso_date}/dockets/\n")
    
    # Initialize repository (optional - for transfer checking)
    repository = None
    try:
        storage = AsyncDynamoDBStorage(config, logger=logger)
        async with storage:
            repository = PacerRepository(storage, logger=logger)
            console.print("[green]✓[/green] Repository connected for transfer checking\n")
    except Exception as e:
        console.print(f"[yellow]⚠[/yellow] Repository unavailable: {e}")
        console.print("[yellow]  Transfer checking will be skipped[/yellow]\n")
        repository = None
    
    # Create artifact checker
    checker = DocketArtifactChecker(logger, config, repository)
    
    # Process dockets with progress indicator
    results = {
        'to_download': [],
        'to_skip': [],
        'errors': []
    }
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task(
            f"Checking {len(SAMPLE_DOCKETS)} dockets...",
            total=len(SAMPLE_DOCKETS)
        )
        
        for docket in SAMPLE_DOCKETS:
            progress.update(
                task,
                description=f"Checking {docket['docket_num']}..."
            )
            
            try:
                should_download, reason = await checker.should_download_docket(
                    docket['court_id'],
                    docket['docket_num'],
                    docket['versus']
                )
                
                result_entry = {
                    'court_id': docket['court_id'],
                    'docket_num': docket['docket_num'],
                    'versus': docket['versus'][:40] + "..." if len(docket['versus']) > 40 else docket['versus'],
                    'reason': reason
                }
                
                if should_download:
                    results['to_download'].append(result_entry)
                else:
                    results['to_skip'].append(result_entry)
                    
            except Exception as e:
                results['errors'].append({
                    'docket_num': docket['docket_num'],
                    'error': str(e)
                })
                logger.error(f"Error checking {docket['docket_num']}: {e}")
            
            progress.advance(task)
    
    # Display results in tables
    console.print("\n[bold green]Results Summary[/bold green]")
    console.print(f"Total Checked: {len(SAMPLE_DOCKETS)}")
    console.print(f"Need Download: {len(results['to_download'])}")
    console.print(f"Can Skip: {len(results['to_skip'])}")
    console.print(f"Errors: {len(results['errors'])}\n")
    
    # Table for dockets needing download
    if results['to_download']:
        console.print("[bold yellow]Dockets Needing Download:[/bold yellow]")
        download_table = Table(show_header=True, header_style="bold magenta")
        download_table.add_column("Court", style="cyan", width=8)
        download_table.add_column("Docket #", style="cyan", width=15)
        download_table.add_column("Case", style="white", width=40)
        download_table.add_column("Reason", style="yellow")
        
        for item in results['to_download']:
            download_table.add_row(
                item['court_id'],
                item['docket_num'],
                item['versus'],
                item['reason']
            )
        
        console.print(download_table)
        console.print()
    
    # Table for dockets to skip
    if results['to_skip']:
        console.print("[bold green]Dockets to Skip (Already Have Artifacts):[/bold green]")
        skip_table = Table(show_header=True, header_style="bold magenta")
        skip_table.add_column("Court", style="cyan", width=8)
        skip_table.add_column("Docket #", style="cyan", width=15)
        skip_table.add_column("Case", style="white", width=40)
        skip_table.add_column("Skip Reason", style="green")
        
        for item in results['to_skip']:
            skip_table.add_row(
                item['court_id'],
                item['docket_num'],
                item['versus'],
                item['reason']
            )
        
        console.print(skip_table)
        console.print()
    
    # Show errors if any
    if results['errors']:
        console.print("[bold red]Errors:[/bold red]")
        for error in results['errors']:
            console.print(f"  • {error['docket_num']}: {error['error']}")
        console.print()
    
    # Save detailed results to JSON
    output_file = f"artifact_check_results_{iso_date}.json"
    with open(output_file, 'w') as f:
        json.dump({
            'check_date': datetime.now().isoformat(),
            'iso_date': iso_date,
            'total_checked': len(SAMPLE_DOCKETS),
            'need_download': len(results['to_download']),
            'can_skip': len(results['to_skip']),
            'errors': len(results['errors']),
            'details': results
        }, f, indent=2)
    
    console.print(f"[blue]Detailed results saved to:[/blue] {output_file}")
    
    # Generate download script if needed
    if results['to_download']:
        script_file = f"download_needed_{iso_date}.sh"
        with open(script_file, 'w') as f:
            f.write("#!/bin/bash\n")
            f.write("# Auto-generated script to download needed dockets\n\n")
            
            for item in results['to_download']:
                f.write(f"# Download: {item['versus']}\n")
                f.write(f"echo 'Processing {item['docket_num']}...'\n")
                f.write(f"# python download_docket.py --court {item['court_id']} --docket '{item['docket_num']}'\n\n")
        
        os.chmod(script_file, 0o755)
        console.print(f"[blue]Download script generated:[/blue] {script_file}\n")
    
    return results


async def batch_check_example():
    """Example of batch checking multiple dockets at once."""
    console = Console()
    
    # Setup
    config = {'iso_date': datetime.now().strftime("%Y%m%d")}
    checker = DocketArtifactChecker(logger, config, None)
    
    # Large batch of dockets
    large_batch = [
        {'court_id': 'cand', 'docket_num': f'3:25-cv-{12000+i:05d}', 'versus': f'Case_{i} v. Defendant_{i}'}
        for i in range(100)  # 100 dockets
    ]
    
    console.print("\n[bold blue]Batch Processing Example[/bold blue]")
    console.print(f"Processing {len(large_batch)} dockets in batch...\n")
    
    # Process batch
    start_time = datetime.now()
    results = await checker.check_batch(large_batch)
    elapsed = (datetime.now() - start_time).total_seconds()
    
    # Display results
    console.print(f"[green]✓[/green] Processed {results['total']} dockets in {elapsed:.2f} seconds")
    console.print(f"  • Downloads needed: {len(results['to_download'])}")
    console.print(f"  • Can skip: {len(results['to_skip'])}")
    
    # Breakdown by skip reason
    console.print("\n[bold]Skip Reasons:[/bold]")
    for reason, items in results['skip_reasons'].items():
        if items:
            console.print(f"  • {reason}: {len(items)} dockets")


if __name__ == "__main__":
    # Run main check
    asyncio.run(check_artifacts_main())
    
    # Optionally run batch example
    # asyncio.run(batch_check_example())