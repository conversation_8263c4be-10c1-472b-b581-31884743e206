#!/usr/bin/env python3
"""
Docket Extraction Refactor Validation Script

Validates that the refactored docket extraction logic is properly integrated
into the PACER workflow and meets all requirements.
"""

import sys
import os

# Add the project root to the Python path
sys.path.append('/Users/<USER>/PycharmProjects/lexgenius')

import logging
from src.utils.docket_utils import (
    extract_docket_digits,
    normalize_docket_number,
    validate_docket_format
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def validate_extract_docket_digits_requirements():
    """Validate that extract_docket_digits meets all specified requirements."""
    logger.info("Validating extract_docket_digits requirements...")
    
    # Requirement 1: Extract ONLY first 13 digits
    test_cases = [
        ("3:25-cv-06587-SK", "3:25-cv-06587"),
        ("3:25-cv-06587-SK-ABC-123", "3:25-cv-06587"),  # Multiple suffixes
        ("2:25-cv-2364-AMS-DEF", "2:25-cv-02364"),
    ]
    
    for input_val, expected in test_cases:
        result = extract_docket_digits(input_val)
        assert result == expected, f"Failed to extract only 13 digits: {input_val} -> {result}"
        assert len(result) == 13, f"Result not 13 characters: {result} ({len(result)} chars)"
        logger.info(f"✓ Extract only 13 digits: '{input_val}' -> '{result}'")
    
    # Requirement 2: Zero-pad if less than 5 numbers (but >= 1)
    zero_pad_cases = [
        ("1:25-cv-123", "1:25-cv-00123"),
        ("2:24-cv-45", "2:24-cv-00045"),
        ("3:23-cv-5", "3:23-cv-00005"),
        ("4:22-cv-1", "4:22-cv-00001"),
    ]
    
    for input_val, expected in zero_pad_cases:
        result = extract_docket_digits(input_val)
        assert result == expected, f"Failed zero-padding: {input_val} -> {result}"
        logger.info(f"✓ Zero-pad validation: '{input_val}' -> '{result}'")
    
    # Requirement 3: Stop after number, no hyphens/judge initials
    stop_after_cases = [
        ("3:25-cv-06587-SK-Additional-Info", "3:25-cv-06587"),
        ("2:25-cv-2364-AMS-Extra-Data-Here", "2:25-cv-02364"),
    ]
    
    for input_val, expected in stop_after_cases:
        result = extract_docket_digits(input_val)
        assert result == expected, f"Failed to stop after numbers: {input_val} -> {result}"
        logger.info(f"✓ Stop after numbers: '{input_val}' -> '{result}'")
    
    # Requirement 4: Format compliance N:YY-{2 alphabetical chars}-NNNNN
    format_cases = [
        "3:25-cv-06587",
        "1:24-cr-12345", 
        "2:23-bk-98765",
        "4:22-md-54321",
    ]
    
    for case in format_cases:
        result = extract_docket_digits(case + "-SK")
        assert validate_docket_format(result), f"Result doesn't match required format: {result}"
        logger.info(f"✓ Format compliance: '{case}' -> '{result}'")
    
    logger.info("✅ All extract_docket_digits requirements validated successfully!")


def validate_integration_with_existing_workflow():
    """Validate integration with existing PACER workflow components."""
    logger.info("Validating integration with existing workflow...")
    
    # Test 1: Verify imports work in key files
    try:
        from src.pacer.facades.docket_orchestrator import DocketOrchestrator
        logger.info("✓ DocketOrchestrator import successful")
    except ImportError as e:
        logger.error(f"❌ DocketOrchestrator import failed: {e}")
        return False
    
    try:
        from src.pacer.components.case_processing.case_validator import CaseValidator
        logger.info("✓ CaseValidator import successful")
    except ImportError as e:
        logger.error(f"❌ CaseValidator import failed: {e}")
        return False
    
    # Test 2: Verify backward compatibility with normalize_docket_number
    test_cases = [
        "3:25-cv-06587-SK",
        "2:25-cv-2364-AMS",
        "1:25-cv-123"
    ]
    
    for case in test_cases:
        old_result = normalize_docket_number(case)
        new_result = extract_docket_digits(case)
        assert old_result == new_result, f"Backward compatibility broken: {old_result} != {new_result}"
        logger.info(f"✓ Backward compatibility: '{case}' -> '{old_result}'")
    
    # Test 3: Verify enhanced regex patterns work
    import re
    
    enhanced_patterns = [
        r'\d+:\d{2}-cv-\d+(?:-\w+)?',
        r'\d+:\d{2}-cr-\d+(?:-\w+)?',
        r'\d+:\d{2}-bk-\d+(?:-\w+)?',
        r'\d+:\d{2}-md-\d+(?:-\w+)?',
        r'\d+:\d{2}-[a-zA-Z]{2}-\d+(?:-\w+)?'
    ]
    
    test_text = "Cases found: 3:25-cv-06587-SK and 2:24-cr-12345-ABC and 1:23-bk-98765"
    
    all_matches = []
    for pattern in enhanced_patterns:
        matches = re.findall(pattern, test_text, re.IGNORECASE)
        all_matches.extend(matches)
    
    expected_matches = ["3:25-cv-06587-SK", "2:24-cr-12345-ABC", "1:23-bk-98765"]
    for expected in expected_matches:
        assert expected in all_matches, f"Enhanced pattern missed: {expected}"
        logger.info(f"✓ Enhanced pattern matched: '{expected}'")
    
    logger.info("✅ Integration with existing workflow validated successfully!")
    return True


def validate_format_compliance():
    """Validate format compliance with examples."""
    logger.info("Validating format compliance with provided examples...")
    
    # Test against the exact example provided
    example_input = "3:25-cv-06587-SK"
    expected_output = "3:25-cv-06587"
    
    result = extract_docket_digits(example_input)
    assert result == expected_output, f"Example failed: {example_input} -> {result}, expected {expected_output}"
    
    # Verify it's exactly 13 characters
    assert len(result) == 13, f"Result not 13 characters: {result} ({len(result)} chars)"
    
    # Verify format components
    components = result.split(':')
    assert len(components) == 2, f"Invalid colon structure: {result}"
    
    prefix = components[0]  # Should be single digit
    suffix = components[1]  # Should be YY-XX-NNNNN
    
    suffix_parts = suffix.split('-')
    assert len(suffix_parts) == 3, f"Invalid suffix structure: {suffix}"
    
    year = suffix_parts[0]    # YY
    case_type = suffix_parts[1]  # XX
    case_num = suffix_parts[2]   # NNNNN
    
    assert len(year) == 2, f"Year not 2 digits: {year}"
    assert len(case_type) == 2, f"Case type not 2 chars: {case_type}"
    assert len(case_num) == 5, f"Case number not 5 digits: {case_num}"
    assert case_num.isdigit(), f"Case number not all digits: {case_num}"
    
    logger.info(f"✓ Format compliance validated: '{example_input}' -> '{result}'")
    logger.info(f"  - Court prefix: '{prefix}'")
    logger.info(f"  - Year: '{year}'")
    logger.info(f"  - Case type: '{case_type}'")
    logger.info(f"  - Case number: '{case_num}'")
    
    logger.info("✅ Format compliance validation successful!")


def run_validation():
    """Run complete validation of the docket extraction refactor."""
    print("=" * 80)
    print("DOCKET EXTRACTION REFACTOR VALIDATION")
    print("=" * 80)
    
    try:
        # Run all validations
        validate_extract_docket_digits_requirements()
        print()
        
        validate_integration_with_existing_workflow()
        print()
        
        validate_format_compliance()
        print()
        
        print("=" * 80)
        print("🎉 ALL VALIDATIONS PASSED!")
        print("=" * 80)
        print()
        print("✅ Docket extraction refactor is complete and working correctly.")
        print("✅ Integration with existing PACER workflow verified.")
        print("✅ All format requirements met.")
        print("✅ Backward compatibility maintained.")
        print()
        print("DELIVERABLE STATUS: ✅ COMPLETE")
        
        return True
        
    except Exception as e:
        print("=" * 80)
        print(f"❌ VALIDATION FAILED: {e}")
        print("=" * 80)
        logger.error(f"Validation error: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    success = run_validation()
    sys.exit(0 if success else 1)
