#!/usr/bin/env python3
"""
Test script to verify browser context fix in unified docket processing workflow.

This script tests the fix that ensures browser context is properly passed from:
PacerOrchestratorService -> WorkflowOrchestrator -> DocketOrchestrator -> process_dockets_for_court

The fix addresses the error: "Browser context is REQUIRED for unified docket processing in court ilnd"
"""

import asyncio
import sys
import os

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from unittest.mock import MagicMock, AsyncMock
from src.pacer.components.processing.workflow_orchestrator_di import WorkflowOrchestrator


async def test_browser_context_flow():
    """Test that browser context flows correctly through the workflow chain."""
    print("🔄 Testing browser context flow in unified docket processing...")
    
    # Create a mock DocketOrchestrator that expects browser context
    mock_docket_orchestrator = AsyncMock()
    mock_docket_orchestrator.process_courts = AsyncMock()
    
    # Create WorkflowOrchestrator with the mock
    workflow_orchestrator = WorkflowOrchestrator(
        docket_orchestrator=mock_docket_orchestrator,
        logger=MagicMock(),
        config={}
    )
    
    # Test data simulating the parallel processing workflow
    test_court_ids = ['ilnd', 'cand']
    test_browser_context = MagicMock()  # Simulates isolated browser context per court
    test_browser_context.pages = []  # Mock pages property
    
    # Test kwargs that would come from the parallel processing fix
    test_kwargs = {
        'browser_context': test_browser_context,  # This is the critical context from parallel processing
        'iso_date': '20250109',
        'start_date': None,
        'end_date': None,
        'workflow_config': {'test': True}
    }
    
    print(f"📥 Input: court_ids={test_court_ids}, browser_context={type(test_browser_context).__name__}")
    
    # Execute the workflow that was previously failing
    try:
        await workflow_orchestrator.execute_court_processing_workflow(
            court_ids=test_court_ids,
            **test_kwargs
        )
        
        # Verify the DocketOrchestrator.process_courts was called with the browser context
        mock_docket_orchestrator.process_courts.assert_called_once()
        
        # Get the actual call arguments
        call_args = mock_docket_orchestrator.process_courts.call_args
        called_args, called_kwargs = call_args.args if call_args.args else [], call_args.kwargs
        
        print("✅ WorkflowOrchestrator.execute_court_processing_workflow() completed successfully")
        print(f"📤 DocketOrchestrator.process_courts() called with:")
        print(f"   - court_ids: {called_kwargs.get('court_ids', 'NOT_FOUND')}")
        print(f"   - context: {type(called_kwargs.get('context', 'NOT_FOUND')).__name__}")
        print(f"   - iso_date: {called_kwargs.get('iso_date', 'NOT_FOUND')}")
        
        # CRITICAL TEST: Verify browser context was passed as 'context' parameter
        if called_kwargs.get('context') is test_browser_context:
            print("✅ BROWSER CONTEXT CORRECTLY PASSED: The isolated browser context from parallel processing is now properly passed to DocketOrchestrator")
            print("✅ This fixes the error: 'Browser context is REQUIRED for unified docket processing in court ilnd'")
            return True
        else:
            print("❌ BROWSER CONTEXT NOT PASSED: The browser context is still being lost in the workflow chain")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False


async def test_multiple_context_scenarios():
    """Test different browser context parameter scenarios."""
    print("\n🔄 Testing multiple browser context parameter scenarios...")
    
    mock_docket_orchestrator = AsyncMock()
    mock_docket_orchestrator.process_courts = AsyncMock()
    
    workflow_orchestrator = WorkflowOrchestrator(
        docket_orchestrator=mock_docket_orchestrator,
        logger=MagicMock(),
        config={}
    )
    
    test_context = MagicMock()
    
    # Scenario 1: browser_context in kwargs
    print("📋 Scenario 1: browser_context parameter")
    await workflow_orchestrator.execute_court_processing_workflow(
        court_ids=['ilnd'],
        browser_context=test_context
    )
    
    call_kwargs = mock_docket_orchestrator.process_courts.call_args.kwargs
    assert call_kwargs.get('context') is test_context, "browser_context not passed correctly"
    print("✅ browser_context parameter handled correctly")
    
    # Reset mock
    mock_docket_orchestrator.reset_mock()
    
    # Scenario 2: context in kwargs  
    print("📋 Scenario 2: context parameter")
    await workflow_orchestrator.execute_court_processing_workflow(
        court_ids=['ilnd'], 
        context=test_context
    )
    
    call_kwargs = mock_docket_orchestrator.process_courts.call_args.kwargs
    assert call_kwargs.get('context') is test_context, "context not passed correctly"
    print("✅ context parameter handled correctly")
    
    # Reset mock
    mock_docket_orchestrator.reset_mock()
    
    # Scenario 3: browser_context takes precedence over context
    print("📋 Scenario 3: browser_context precedence test")
    test_context2 = MagicMock()
    await workflow_orchestrator.execute_court_processing_workflow(
        court_ids=['ilnd'],
        browser_context=test_context,  # This should take precedence
        context=test_context2
    )
    
    call_kwargs = mock_docket_orchestrator.process_courts.call_args.kwargs
    assert call_kwargs.get('context') is test_context, "browser_context precedence not working"
    print("✅ browser_context precedence working correctly")
    
    print("✅ All browser context scenarios passed!")
    return True


async def main():
    """Run all tests."""
    print("🚀 Browser Context Fix Verification Test")
    print("=" * 60)
    
    success = True
    
    # Test 1: Basic browser context flow
    test1_result = await test_browser_context_flow()
    success = success and test1_result
    
    # Test 2: Multiple parameter scenarios
    test2_result = await test_multiple_context_scenarios()
    success = success and test2_result
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Browser context fix is working correctly")
        print("✅ The error 'Browser context is REQUIRED for unified docket processing' should be resolved")
        print("\nThe fix ensures that:")
        print("  1. Browser contexts from parallel processing are preserved")  
        print("  2. WorkflowOrchestrator properly extracts browser context from kwargs")
        print("  3. DocketOrchestrator receives the required browser context parameter")
    else:
        print("❌ SOME TESTS FAILED!")
        print("❌ Browser context issue may not be fully resolved")
    
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)