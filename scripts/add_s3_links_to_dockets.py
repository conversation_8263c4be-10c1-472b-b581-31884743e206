#!/usr/bin/env python3
"""
Script to add missing s3_links to docket JSON files.
This generates the s3_link field for PDFs that should be uploaded to S3.
"""

import json
import os
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.transformer.components.file.file_handler_core import FileHandlerCore as FileHandler


def add_s3_links_to_dockets(date_str: str, dry_run: bool = False):
    """
    Add s3_links to docket JSON files for a given date.
    
    Args:
        date_str: Date in YYYYMMDD format
        dry_run: If True, only show what would be changed without modifying files
    """
    # Validate date format
    if len(date_str) != 8 or not date_str.isdigit():
        print(f"Error: Invalid date format '{date_str}'. Must be YYYYMMDD.")
        return
    
    # Set up paths
    dockets_dir = project_root / "data" / date_str / "dockets"
    if not dockets_dir.exists():
        print(f"Error: Directory {dockets_dir} does not exist.")
        return
    
    # Create minimal config for FileHandler
    config = {
        'iso_date': date_str,
        'project_root': str(project_root),
        'directories': {
            'base_dir': str(project_root)
        }
    }
    
    # Initialize FileHandler
    file_handler = FileHandler(config)
    
    # Process all JSON files
    json_files = list(dockets_dir.glob("*.json"))
    print(f"Found {len(json_files)} JSON files in {dockets_dir}")
    
    updated_count = 0
    skipped_count = 0
    error_count = 0
    
    for json_path in json_files:
        try:
            # Load JSON data
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Skip if s3_link already exists
            if data.get('s3_link'):
                print(f"Skipping {json_path.name} - s3_link already exists: {data['s3_link']}")
                skipped_count += 1
                continue
            
            # Skip if it's a transferred case (they inherit s3_link from transferor)
            if data.get('is_transferred'):
                print(f"Skipping {json_path.name} - is a transferred case")
                skipped_count += 1
                continue
            
            # Generate s3_link
            s3_key_base = file_handler.get_s3_key_base(data)
            if not s3_key_base:
                print(f"Error: Could not generate S3 key base for {json_path.name}")
                error_count += 1
                continue
            
            # Create the S3 link
            s3_link = f"https://cdn.lexgenius.ai/{s3_key_base}.pdf"
            
            if dry_run:
                print(f"Would add s3_link to {json_path.name}: {s3_link}")
            else:
                # Update the data
                data['s3_link'] = s3_link
                
                # Save the updated JSON
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=4)
                
                print(f"Added s3_link to {json_path.name}: {s3_link}")
            
            updated_count += 1
            
        except Exception as e:
            print(f"Error processing {json_path.name}: {e}")
            error_count += 1
    
    # Summary
    print(f"\nSummary:")
    print(f"  Total files: {len(json_files)}")
    print(f"  Updated: {updated_count}")
    print(f"  Skipped: {skipped_count}")
    print(f"  Errors: {error_count}")
    
    if dry_run:
        print("\nThis was a dry run. No files were modified.")
        print("Run without --dry-run to actually update the files.")


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Add missing s3_links to docket JSON files"
    )
    parser.add_argument(
        "date",
        nargs="?",
        default=datetime.now().strftime("%Y%m%d"),
        help="Date in YYYYMMDD format (default: today)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be changed without modifying files"
    )
    
    args = parser.parse_args()
    
    print(f"Adding s3_links to dockets for date: {args.date}")
    add_s3_links_to_dockets(args.date, dry_run=args.dry_run)


if __name__ == "__main__":
    main()