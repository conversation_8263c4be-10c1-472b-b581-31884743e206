#!/usr/bin/env python3
"""
Standalone test script for nuclear lexgenius.log prevention.

This script demonstrates the nuclear prevention functionality without
complex imports or dependencies.
"""

import os
import sys
import logging
import time
import tempfile
import glob
from pathlib import Path

# Change to the project root to ensure proper imports
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
os.chdir(project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

try:
    from pacer.utils.court_logger import (
        nuclear_lexgenius_prevention,
        activate_nuclear_lexgenius_prevention,
        emergency_delete_all_lexgenius_logs,
    )
    IMPORTS_AVAILABLE = True
except ImportError as e:
    print(f"Import failed: {e}")
    IMPORTS_AVAILABLE = False


def test_emergency_deletion():
    """Test emergency deletion of lexgenius.log files."""
    print("\n=== Testing Emergency Deletion ===")
    
    if not IMPORTS_AVAILABLE:
        print("❌ Skipping - imports not available")
        return
    
    # Create test files in current directory
    test_files = []
    test_paths = [
        "lexgenius.log",
        "logs/lexgenius.log", 
        "data/20250810/logs/lexgenius.log",
    ]
    
    for path in test_paths:
        try:
            os.makedirs(os.path.dirname(path), exist_ok=True)
            with open(path, 'w') as f:
                f.write("Test lexgenius log content\n")
            test_files.append(path)
            print(f"Created test file: {path}")
        except Exception as e:
            print(f"Failed to create {path}: {e}")
    
    print(f"Created {len(test_files)} test files")
    
    # Test emergency deletion
    deleted_files = emergency_delete_all_lexgenius_logs(project_root)
    print(f"Emergency deletion removed {len(deleted_files)} files:")
    for file in deleted_files:
        print(f"  - {file}")
    
    # Clean up any remaining test files
    for path in test_paths:
        try:
            if os.path.exists(path):
                os.remove(path)
        except Exception:
            pass
        try:
            # Also try to remove directories if empty
            if os.path.exists(os.path.dirname(path)):
                os.rmdir(os.path.dirname(path))
        except Exception:
            pass


def test_logger_interception():
    """Test that lexgenius loggers get intercepted."""
    print("\n=== Testing Logger Interception ===")
    
    if not IMPORTS_AVAILABLE:
        print("❌ Skipping - imports not available")
        return
    
    # Activate nuclear prevention
    result = activate_nuclear_lexgenius_prevention(project_root)
    print(f"Nuclear prevention result: {result}")
    
    # Try to create various lexgenius loggers
    lexgenius_loggers = [
        "lexgenius",
        "lexgenius.main",
        "lexgenius.fallback", 
        "lexgenius.core",
    ]
    
    for logger_name in lexgenius_loggers:
        try:
            logger = logging.getLogger(logger_name)
            logger.info(f"Test message from {logger_name}")
            
            # Check if logger has been neutered
            if not logger.handlers or all(isinstance(h, logging.NullHandler) for h in logger.handlers):
                print(f"✅ Logger {logger_name} properly intercepted")
            else:
                print(f"⚠️ Logger {logger_name} may not be fully intercepted")
                
        except Exception as e:
            print(f"❌ Error testing logger {logger_name}: {e}")


def test_file_monitoring():
    """Test that lexgenius.log files get detected and can be deleted."""
    print("\n=== Testing File Monitoring ===")
    
    # Look for any existing lexgenius.log files
    search_patterns = [
        "**/lexgenius*.log",
        "logs/lexgenius*.log",
        "data/**/lexgenius*.log"
    ]
    
    found_files = []
    for pattern in search_patterns:
        matching_files = glob.glob(pattern, recursive=True)
        found_files.extend(matching_files)
    
    if found_files:
        print(f"Found {len(found_files)} existing lexgenius log files:")
        for file in found_files:
            print(f"  - {file}")
            
        # Show file sizes and modification times
        for file in found_files:
            try:
                stat = os.stat(file)
                size = stat.st_size
                mtime = time.ctime(stat.st_mtime)
                print(f"    Size: {size} bytes, Modified: {mtime}")
            except Exception as e:
                print(f"    Error getting stats: {e}")
    else:
        print("✅ No lexgenius log files found")


def test_context_manager():
    """Test the nuclear prevention context manager."""
    print("\n=== Testing Context Manager ===")
    
    if not IMPORTS_AVAILABLE:
        print("❌ Skipping - imports not available")
        return
        
    try:
        with nuclear_lexgenius_prevention(base_path=project_root, continuous_monitoring=False):
            print("Inside nuclear prevention context...")
            
            # Try to create a lexgenius logger
            test_logger = logging.getLogger("lexgenius.test.context")
            test_logger.info("This should be intercepted")
            
            # Check if it was intercepted
            if not test_logger.handlers or all(isinstance(h, logging.NullHandler) for h in test_logger.handlers):
                print("✅ Context manager successfully intercepted logger")
            else:
                print("⚠️ Context manager may not have fully intercepted logger")
                
    except Exception as e:
        print(f"❌ Context manager test failed: {e}")


def main():
    """Run all nuclear prevention tests."""
    print("🚨 NUCLEAR LEXGENIUS PREVENTION TESTS 🚨")
    print("=" * 50)
    print(f"Working directory: {os.getcwd()}")
    print(f"Project root: {project_root}")
    print(f"Imports available: {IMPORTS_AVAILABLE}")
    
    try:
        # Test 1: File monitoring
        test_file_monitoring()
        
        # Test 2: Emergency deletion
        test_emergency_deletion()
        
        # Test 3: Logger interception
        test_logger_interception()
        
        # Test 4: Context manager
        test_context_manager()
        
        print("\n" + "=" * 50)
        print("🎉 ALL NUCLEAR PREVENTION TESTS COMPLETED")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()