#!/usr/bin/env python3
"""
Test script for the enhanced CourtLogger implementation.
Validates that ALL logs are intercepted and go only to court-specific files.
"""

import os
import sys
import tempfile
import shutil
import logging
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_enhanced_court_logger():
    """Test the enhanced court logger with comprehensive log interception."""
    print("🧪 Testing Enhanced Court Logger")
    print("=" * 60)
    
    # Setup temporary test environment
    test_data_dir = tempfile.mkdtemp(prefix="enhanced_court_log_test_")
    iso_date = datetime.now().strftime('%Y-%m-%d')
    config = {"DATA_DIR": test_data_dir}
    
    try:
        from src.pacer.utils.court_logger import setup_court_logging_context
        
        # Test comprehensive log interception
        print("\n🔧 Test 1: Comprehensive log interception for NYSD")
        court_id = 'nysd'
        
        with setup_court_logging_context(court_id, iso_date, config) as court_logger:
            # Test various logging sources
            court_logger.info("Direct court logger message")
            
            # Test root logger
            root_logger = logging.getLogger("")
            root_logger.info("Root logger message should be intercepted")
            
            # Test src logger  
            src_logger = logging.getLogger("src")
            src_logger.warning("Src logger message should be intercepted")
            
            # Test lexgenius logger (problematic one)
            lex_logger = logging.getLogger(f"lexgenius-{datetime.now().strftime('%Y%m%d')}")
            lex_logger.error("Lexgenius logger message should be intercepted")
            
            # Test component logger
            comp_logger = logging.getLogger("ComponentService")
            comp_logger.debug("Component logger message should be intercepted")
            
        # Verify all logs went to court file
        expected_path = os.path.join(test_data_dir, iso_date, "logs", "pacer", f"{court_id}.log")
        
        if os.path.exists(expected_path):
            with open(expected_path, 'r') as f:
                content = f.read()
                
            # Check for all expected messages
            expected_messages = [
                "Direct court logger message",
                "Root logger message should be intercepted", 
                "Src logger message should be intercepted",
                "Lexgenius logger message should be intercepted",
                "Component logger message should be intercepted"
            ]
            
            all_found = True
            for msg in expected_messages:
                if msg not in content:
                    print(f"❌ Missing message: {msg}")
                    all_found = False
                else:
                    print(f"✅ Found intercepted message: {msg}")
            
            if all_found:
                print(f"✅ ALL logs successfully intercepted to: {expected_path}")
            else:
                print(f"❌ Some logs were not intercepted")
                return False
        else:
            print(f"❌ Court log file not found at: {expected_path}")
            return False
        
        # Test 2: Ensure no other log files were created
        print(f"\n🔧 Test 2: Verify no other log files created")
        
        # Check for any lexgenius-*.log files
        found_escape_logs = []
        for root, dirs, files in os.walk(test_data_dir):
            for file in files:
                if file.startswith('lexgenius') and file.endswith('.log'):
                    found_escape_logs.append(os.path.join(root, file))
        
        # Also check current directory
        cwd_escape_logs = []
        for file in os.listdir('.'):
            if file.startswith('lexgenius') and file.endswith('.log'):
                cwd_escape_logs.append(file)
        
        if found_escape_logs or cwd_escape_logs:
            print("❌ Found escaped log files:")
            for log_file in found_escape_logs + cwd_escape_logs:
                print(f"   - {log_file}")
            return False
        else:
            print("✅ No escaped log files found - all logs properly contained")
        
        # Test 3: Multi-court isolation
        print(f"\n🔧 Test 3: Multi-court isolation test")
        
        court1 = 'cand'
        court2 = 'ilnd'
        
        # Process court1
        with setup_court_logging_context(court1, iso_date, config) as logger1:
            logger1.info(f"Message from {court1.upper()}")
            logging.getLogger("").info(f"Root message during {court1.upper()} processing")
        
        # Process court2  
        with setup_court_logging_context(court2, iso_date, config) as logger2:
            logger2.info(f"Message from {court2.upper()}")
            logging.getLogger("").info(f"Root message during {court2.upper()} processing")
        
        # Verify isolation
        court1_path = os.path.join(test_data_dir, iso_date, "logs", "pacer", f"{court1}.log")
        court2_path = os.path.join(test_data_dir, iso_date, "logs", "pacer", f"{court2}.log")
        
        if os.path.exists(court1_path) and os.path.exists(court2_path):
            with open(court1_path, 'r') as f:
                court1_content = f.read()
            with open(court2_path, 'r') as f:
                court2_content = f.read()
            
            # Check isolation
            court1_has_court2 = court2.upper() in court1_content
            court2_has_court1 = court1.upper() in court2_content
            
            if not court1_has_court2 and not court2_has_court1:
                print("✅ Court log isolation working correctly")
            else:
                print("❌ Court log isolation failed - cross-contamination detected")
                return False
        else:
            print("❌ Multi-court log files not created properly")
            return False
        
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"Enhanced court logger is working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        try:
            shutil.rmtree(test_data_dir)
        except Exception:
            pass

if __name__ == "__main__":
    success = test_enhanced_court_logger()
    sys.exit(0 if success else 1)