diff --git a/src/pacer/components/processing/workflow_orchestrator.py b/src/pacer/components/processing/workflow_orchestrator.py
index 1234567..abcdef9 100644
--- a/src/pacer/components/processing/workflow_orchestrator.py
+++ b/src/pacer/components/processing/workflow_orchestrator.py
@@ -1,6 +1,8 @@
 # /src/services/pacer/_processing_components/workflow_orchestrator.py
 import logging
+import json
 import os
+from pathlib import Path
 import threading
 from typing import Any, Dict
 
@@ -54,6 +56,58 @@ class WorkflowOrchestrator(ComponentImplementation):
         else:
             raise ValueError(f"Unknown action for WorkflowOrchestrator: {action}")
 
+    def _ensure_directories_exist(self, iso_date: str) -> None:
+        """Ensure required data directories exist for the given date."""
+        directories = [
+            f"data/{iso_date}",
+            f"data/{iso_date}/logs",
+            f"data/{iso_date}/courts",
+            f"data/{iso_date}/downloads"
+        ]
+        for directory in directories:
+            os.makedirs(directory, exist_ok=True)
+            self.log_debug(f"Ensured directory exists: {directory}")
+
+    def _get_docket_log_path(self, court_id: str, iso_date: str) -> str:
+        """Get the path to the docket report log file for a specific court and date."""
+        return f"data/{iso_date}/logs/docket_report_list_{court_id}.json"
+
+    async def _check_log_exists(self, path: str) -> bool:
+        """Check if a docket report log file exists."""
+        return os.path.exists(path)
+
+    async def _load_docket_log(self, path: str) -> Dict[str, Any]:
+        """Load existing docket report log from file."""
+        try:
+            with open(path, 'r') as f:
+                data = json.load(f)
+                self.log_info(f"Loaded docket log with {len(data.get('cases', []))} cases")
+                return data
+        except Exception as e:
+            self.log_error(f"Failed to load docket log: {e}")
+            return None
+
+    async def _save_docket_log(self, path: str, data: Dict[str, Any]) -> None:
+        """Save docket report log to file."""
+        try:
+            os.makedirs(os.path.dirname(path), exist_ok=True)
+            with open(path, 'w') as f:
+                json.dump(data, f, indent=2)
+            self.log_info(f"Saved docket log with {len(data.get('cases', []))} cases")
+        except Exception as e:
+            self.log_error(f"Failed to save docket log: {e}")
+
+    async def _resume_from_log(self, log_data: Dict[str, Any], context: Any, kwargs: Dict[str, Any]) -> Dict[str, Any]:
+        """Resume processing from existing docket log."""
+        court_id = kwargs.get("court_id")
+        log_prefix = f"[{court_id}] CourtTask:"
+        
+        self.log_info(f"{log_prefix} Resuming from existing docket log")
+        
+        # Process the cases from the log
+        cases = log_data.get('cases', [])
+        self.log_info(f"{log_prefix} Found {len(cases)} cases to process from log")
+        
+        # TODO: Implement actual resume processing logic here
+        return {'court_id': court_id, 'status': 'resumed', 'cases_processed': len(cases)}
+
     async def process_single_court_task(self, **kwargs) -> Dict[str, Any]:
         """
         Processes a single court, handling login, navigation, and report processing.
@@ -95,17 +149,63 @@ class WorkflowOrchestrator(ComponentImplementation):
                 raise Exception("ECF login sequence failed.")
             self.log_info(f"{log_prefix} Login successful.")
 
-            # Navigate to Query Page
-            if not self.nav_facade:
-                self.log_warning(f"{log_prefix} NavigationFacade not available - skipping navigation")
-            else:
-                await self.nav_facade.execute({
-                    "action": "go_to_query_page", "navigator": navigator, "court_id": court_id
-                })
-            self.log_info(f"{log_prefix} Navigation to query page successful.")
+            # ===== CRITICAL FIX 1: Ensure directories exist =====
+            self._ensure_directories_exist(iso_date)
+            
+            # ===== CRITICAL FIX 2: Check for existing docket_report_log =====
+            log_file_path = self._get_docket_log_path(court_id, iso_date)
+            self.log_info(f"{log_prefix} Checking for existing docket log at: {log_file_path}")
+            
+            docket_log_data = None
+            has_cases = False
+            
+            if await self._check_log_exists(log_file_path):
+                # PATH A: Resume from existing log
+                self.log_info(f"{log_prefix} 📋 Found existing docket_report_log - RESUMING from previous session")
+                docket_log_data = await self._load_docket_log(log_file_path)
+                
+                if docket_log_data and docket_log_data.get('cases'):
+                    has_cases = True
+                    # Skip report generation, we already have the cases list
+                    self.log_info(f"{log_prefix} Loaded {len(docket_log_data['cases'])} cases from existing log")
+                else:
+                    self.log_warning(f"{log_prefix} Log file exists but is empty or invalid, will generate new report")
+                    docket_log_data = None
+            
+            # Only generate new report if we don't have valid log data
+            if not docket_log_data:
+                # PATH B: Start new - Generate civil cases report
+                self.log_info(f"{log_prefix} 🆕 No existing docket_report_log - STARTING NEW session")
+                
+                # Navigate to Query Page
+                if not self.nav_facade:
+                    self.log_warning(f"{log_prefix} NavigationFacade not available - skipping navigation")
+                else:
+                    await self.nav_facade.execute({
+                        "action": "go_to_query_page", "navigator": navigator, "court_id": court_id
+                    })
+                self.log_info(f"{log_prefix} Navigation to query page successful.")
 
-            # Report Generation
-            if not self.report_facade:
+                # Report Generation
+                if not self.report_facade:
+                    self.log_warning(f"{log_prefix} ReportFacade not available - marking as completed without report")
+                    result['status'] = 'completed'
+                    result['error'] = 'No report facade available'
+                    return result
+                
+                # Generate the civil cases report
+                has_cases = await self.report_facade.execute({
+                    "action": "generate_civil_cases_report",
+                    "navigator": navigator,
+                    "court_id": court_id,
+                    "from_date_str": start_date_obj.strftime('%m/%d/%y'),
+                    "to_date_str": end_date_obj.strftime('%m/%d/%y'),
+                    "ignore_download_service": self.ignore_download_service,
+                })
+                
+                if has_cases:
+                    self.log_info(f"{log_prefix} Report generated successfully, cases found.")
+                    # TODO: Save the generated report data to docket_log_path for future resumption
+            
+            # Check if we should continue processing
+            if not has_cases and not docket_log_data:
                 self.log_warning(f"{log_prefix} ReportFacade not available - marking as completed without report")
                 result['status'] = 'completed'
                 result['error'] = 'No report facade available'
                 return result
             
-            has_cases = await self.report_facade.execute({
-                "action": "generate_civil_cases_report",
-                "navigator": navigator,
-                "court_id": court_id,
-                "from_date_str": start_date_obj.strftime('%m/%d/%y'),
-                "to_date_str": end_date_obj.strftime('%m/%d/%y'),
-                "ignore_download_service": self.ignore_download_service,
-            })
             if not has_cases:
                 result['status'] = 'success_no_cases'
                 return result
-            self.log_info(f"{log_prefix} Report generated, cases found.")
 
             # Process Rows
-            row_counts = await self.row_facade.execute({
-                "action": "process_report_rows", "original_page": page, "court_id": court_id,
-                "iso_date": iso_date, "start_date_obj": start_date_obj, "end_date_obj": end_date_obj,
-                "context": context, "processor_config": processor_config,
-                "relevance_engine": relevance_engine, "court_logger": court_logger
-            })
+            if docket_log_data:
+                # Process from log data
+                self.log_info(f"{log_prefix} Processing rows from loaded docket log")
+                row_counts = await self.row_facade.execute({
+                    "action": "process_report_rows_from_log",
+                    "docket_log_data": docket_log_data,
+                    "original_page": page,
+                    "court_id": court_id,
+                    "iso_date": iso_date,
+                    "start_date_obj": start_date_obj,
+                    "end_date_obj": end_date_obj,
+                    "context": context,
+                    "processor_config": processor_config,
+                    "relevance_engine": relevance_engine,
+                    "court_logger": court_logger
+                })
+            else:
+                # Process from fresh report
+                self.log_info(f"{log_prefix} Processing rows from fresh report")
+                row_counts = await self.row_facade.execute({
+                    "action": "process_report_rows",
+                    "original_page": page,
+                    "court_id": court_id,
+                    "iso_date": iso_date,
+                    "start_date_obj": start_date_obj,
+                    "end_date_obj": end_date_obj,
+                    "context": context,
+                    "processor_config": processor_config,
+                    "relevance_engine": relevance_engine,
+                    "court_logger": court_logger
+                })
 
             result.update({'status': 'success', **row_counts})
             return result