7d5b95fe docs(pacer): add PACER service consolidation completion report
2ef0f95f fix(pacer): add __init__.py files to component directories
9f78e33e feat(pacer): implement SimplifiedPacerServiceFactory with auto-discovery
7448a990 refactor(pacer): integrate BrowserService into orchestrator and registry
2140d2e1 feat(pacer): add BrowserService as 9th core service
df895bb9 test: add comprehensive test suite for PACER core services
42113d8a docs: add PACER refactoring documentation
d371267c test: remove facade service tests
82aa187b refactor: update PACER package exports and utilities
fe4a718b refactor: update dependency injection containers for core services
4494c4a6 refactor: update PACER job processing for core services
26898d79 refactor: update PACER components to use core services
49cceef2 refactor: remove PACER facade services
6a83fd7c feat: implement PACER core services architecture
4491f7ea chore: Stop tracking Claude Flow generated files
e93f5f14 chore: Update .gitignore to exclude Claude Flow metrics and database files
e0b15e3a refactor: Update dependencies and remove unused packages (uv-20250804)
42cc6084 fix: critical parsing fixes for civil case reports
d698011d Fix missing court_logger parameter in DocketProcessingJob instantiation
cd69a4e6 refactor: Update dependencies in requirements.txt (uv-20250804)
