On branch uv-20250804-pacer
Your branch is ahead of 'origin/uv-20250804-pacer' by 19 commits.
  (use "git push" to publish your local commits)

Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   .hive-mind/hive.db-shm
	modified:   .hive-mind/hive.db-wal

Untracked files:
  (use "git add <file>..." to include in what will be committed)
	.roo/
	.roomodes
	CLAUDE.md
	PACER_COURT_PROCESSING_ANALYSIS.md
	backups/
	consolidate-services-facades.md
	docs/ADRs/
	docs/ARCHITECTURAL_CONSISTENCY_PLAN.md
	docs/ARCHITECTURE_COMPLIANCE_SUMMARY.md
	docs/ARCHITECTURE_CONSISTENCY.md
	docs/COURT_PROCESSING_FLOWCHART_COMPARISON.md
	docs/PACER_CODEBASE_AUDIT_REPORT.md
	docs/PACER_DESIGN_PATTERNS_CONSOLIDATION_COMPLETE.md
	docs/PACER_DESIGN_PATTERNS_IMPLEMENTATION_PLAN.md
	docs/PACER_Facade_Consolidation_Plan.md
	docs/PACER_MIGRATION_COMPLETE.md
	docs/PACER_MIGRATION_COMPREHENSIVE_ANALYSIS.md
	docs/PACER_MIGRATION_EXECUTION_PLAN.md
	docs/PACER_MIGRATION_PHASE_6_PLAN.md
	docs/PACER_MIGRATION_PLAN.md
	docs/PACER_MIGRATION_TASK_DECOMPOSITION.md
	docs/PACER_SCRAPING_PIPELINE_ANALYSIS.md
	docs/REPORTS_MIGRATION_PLAN.md
	docs/TRANSFORMER_MIGRATION_PLAN.md
	docs/TRANSFORMER_Service_Consolidation_Plan.md
	docs/branch-comparison-summary.md
	docs/browser_service_authenticate_pacer_usage.md
	docs/compliance_reports/
	docs/critical_paths_test_report.md
	docs/current_workflow_analysis.md
	docs/discrepancy_report.md
	docs/feature-restoration-plan.md
	docs/functionality-removed-in-pacer-branch.md
	docs/import_errors_analysis_report.md
	docs/match_case_conversion_summary.md
	docs/mdl_loading_trace_analysis.py
	docs/pacer_core_services_example.py
	docs/pacer_core_services_migration.md
	docs/transformer_consolidation_report.md
	docs/transformer_service_boundaries.md
	fix-s3-link.md
	fix_workflow.py
	memory/
	node_modules/
	package-lock.json
	package.json
	prompt2.md
	report_service.md
	scripts/analysis/AFFF/output/screenshots/20250806_005709_login_username_verification_fail.png
	scripts/analysis/AFFF/output/screenshots/20250806_005710_login_field_corruption.png
	scripts/migration/
	src/containers/CLAUDE.md
	src/containers/pacer_old.py.bak
	src/fb_ads/
	src/infrastructure/CLAUDE.md
	src/infrastructure/di/base_container.py
	src/infrastructure/di/service_registry.py
	src/infrastructure/factories/__init__.py
	src/infrastructure/health/__init__.py
	src/infrastructure/patterns/__init__.py
	src/infrastructure/patterns/builder.py
	src/infrastructure/patterns/command.py
	src/infrastructure/patterns/factory.py
	src/infrastructure/patterns/observer.py
	src/infrastructure/patterns/registry.py
	src/infrastructure/patterns/resource_pool.py
	src/infrastructure/patterns/strategy.py
	src/infrastructure/registry/__init__.py
	src/pacer/
	src/reports/
	src/services/infrastructure/__init__.py
	src/services/monitoring/__init__.py
	src/services/orchestration/CLAUDE.md
	src/services/pacer/CLAUDE.md
	src/services/pacer/pacer_factory.py
	src/services/transformer/CLAUDE.md
	src/services/transformer/transformer_factory.py
	src/services/transformer/transformer_orchestrator_v2.py
	src/transformer/
	test_authenticate_pacer.py
	test_ecf_login_fix.py
	test_pacer_working_branch.py
	tests/integration/test_pacer_workflow_fix.py
	tests/services/pacer/PACER_MIGRATION_VALIDATION_FRAMEWORK.md
	tests/services/pacer/_core_services/
	tests/test_async_fix.py
	tests/test_classification_fix.py
	tests/test_docket_filename_fix.py
	tests/test_pacer_fix.py
	tests/test_pacer_integration.py
	tests/test_proper_filename_format.py
	tests/test_regex_debug.py
	tests/test_relevance_fix.py
	tests/test_s3_link_fix.py
	tests/unit/services/transformer/test_document_processing_service.py
	tests/unit/services/transformer/test_enrichment_service.py
	tests/unit/services/transformer/test_persistence_service.py
	tests/unit/services/transformer/test_transformer_orchestrator.py
	uv-20250804.patch
	verify_pacer_fix.py

no changes added to commit (use "git add" and/or "git commit -a")
