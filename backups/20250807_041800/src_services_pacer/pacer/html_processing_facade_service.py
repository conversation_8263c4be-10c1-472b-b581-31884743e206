# /src/services/pacer/html_processing_facade_service.py
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer._case_processing_components.field_consistency_manager import \
    FieldConsistencyManager
from src.pacer._case_processing_components.html_parser import HtmlParser
from src.pacer._case_processing_components.law_firm_corrector import \
    LawFirmCorrector
from src.pacer._case_processing_components.transfer_info_processor import \
    TransferInfoProcessor
from src.pacer._download_components.s3_manager import S3Manager


class HtmlProcessingFacadeService(AsyncServiceBase):
    """
    Facade service for orchestrating all HTML processing operations for PACER cases.
    It coordinates various components to parse, enrich, and manage HTML data.
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        html_parser: Optional[HtmlParser] = None,
        law_firm_corrector: Optional[LawFirmCorrector] = None,
        s3_manager: Optional[S3Manager] = None,
        field_consistency_manager: Optional[FieldConsistencyManager] = None,
        transfer_info_processor: Optional[TransferInfoProcessor] = None,
    ):
        super().__init__(logger, config)
        self._html_parser = html_parser
        self._law_firm_corrector = law_firm_corrector
        self._s3_manager = s3_manager
        self._field_consistency_manager = field_consistency_manager
        self._transfer_info_processor = transfer_info_processor

    async def _execute_action(self, data: Any) -> Any:
        """
        Routes actions to the appropriate methods. The primary action is
        'process_html_content'.
        """
        action = data.get("action")
        if action == "process_html_content":
            return await self.process_html_content(
                case_details=data.get("case_details", {}),
                html_content=data.get("html_content", ""),
                json_path=data.get("json_path", ""),
            )
        else:
            raise ValueError(f"Unknown action for HtmlProcessingFacadeService: {action}")

    async def process_html_content(
        self, case_details: Dict[str, Any], html_content: str, json_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Orchestrates the end-to-end process of handling PACER HTML content.

        Args:
            case_details: The case data dictionary to be updated.
            html_content: The raw HTML content of the case page.
            json_path: The path to the source JSON file for context.

        Returns:
            The updated case details dictionary.
        """
        try:
            self.log_info("Starting comprehensive HTML processing workflow.")

            # Step 1: Parse HTML to extract attorneys
            attorneys = await self._html_parser.execute({
                "action": "extract_attorneys", "html_content": html_content
            })

            # Step 2: Correct law firm names that are street addresses
            if attorneys:
                attorneys = await self._law_firm_corrector.execute({
                    "action": "correct_law_firms", "attorneys": attorneys
                })
                case_details["attorney"] = attorneys
                self.log_info(f"Extracted and corrected {len(attorneys)} attorneys.")

            # Step 3: Upload HTML to S3
            if json_path and html_content:
                upload_success = await self._s3_manager.execute({
                    "action": "upload_html",
                    "case_details": case_details,
                    "html_content": html_content,
                    "json_path": json_path,
                })
                if not upload_success:
                    self.log_warning("HTML upload to S3 failed, but continuing processing.")

            # Step 4: Find the verified S3 link for the uploaded HTML
            if json_path:
                s3_html_link = await self._s3_manager.execute({
                    "action": "find_html_link",
                    "case_details": case_details,
                    "json_path": json_path,
                })
                if s3_html_link:
                    case_details["s3_html"] = s3_html_link
                    self.log_info(f"Constructed S3 HTML link: {s3_html_link}")

            # Step 5: Process transfer information for compatibility
            if case_details.get("case_in_other_court"):
                case_details = await self._transfer_info_processor.execute({
                    "action": "process_transfer_info", "case_details": case_details
                })
                self.log_info("Transfer compatibility processing completed.")

            # Step 6: Ensure field consistency (plaintiffs vs plaintiff, etc.)
            case_details = await self._field_consistency_manager.execute({
                "action": "ensure_consistency", "case_details": case_details
            })
            self.log_info("Field consistency check completed.")

            self.log_info("HTML processing workflow completed successfully.")
            return case_details

        except Exception as e:
            self.log_error(f"Error in HTML processing workflow: {e}", exc_info=True)
            case_details["_html_processing_error"] = str(e)
            return case_details
