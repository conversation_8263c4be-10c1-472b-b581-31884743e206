# /src/services/pacer/relevance_facade_service.py
from __future__ import annotations
import re
from typing import Any, Dict, List, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer.ignore_download_facade_service import IgnoreDownloadFacadeService
    from src.infrastructure.protocols.logger import LoggerProtocol

class RelevanceFacadeService(AsyncServiceBase):
    """Facade for determining case relevance."""

    def __init__(self,
                 ignore_download_facade: IgnoreDownloadFacadeService,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        self._ignore_facade = ignore_download_facade
        self.relevance_config = self.config.get('relevance_config', {})
        self.relevant_defendants_lower = [d.lower() for d in self.relevance_config.get('relevant_defendants', [])]

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        if action == 'is_relevant':
            return await self.is_relevant(data['case_details'])
        else:
            raise PacerServiceError(f"Unknown action for RelevanceFacadeService: {action}")

    async def is_relevant(self, case_details: Dict[str, Any]) -> bool:
        """
        Determines if a case is relevant based on a set of rules.
        This is a simplified version of the logic in the original service.
        """
        if await self._ignore_facade.should_ignore_download(case_details):
            return False

        # Check for MDL number
        if case_details.get('mdl_num'):
            return True

        # Check for relevant defendants
        defendants = case_details.get('defendant', [])
        if isinstance(defendants, str):
            defendants = [defendants]

        defendant_text = ' '.join(d.lower() for d in defendants)
        for relevant_defendant in self.relevant_defendants_lower:
            if relevant_defendant in defendant_text:
                return True

        return False
