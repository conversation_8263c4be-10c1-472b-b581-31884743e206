# /src/services/pacer/file_management_facade_service.py
from __future__ import annotations
from typing import Any, Dict, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer._file_components.path_builder import PathBuilder
    from src.pacer._file_components.directory_manager import DirectoryManager
    from src.pacer._file_components.file_manager import FileManager
    from src.infrastructure.protocols.logger import LoggerProtocol

class FileManagementFacadeService(AsyncServiceBase):
    """Unified interface for PACER file management operations."""

    def __init__(self,
                 path_builder: PathBuilder,
                 directory_manager: DirectoryManager,
                 file_manager: FileManager,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        self._path_builder = path_builder
        self._dir_manager = directory_manager
        self._file_manager = file_manager

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        iso_date = data.get('iso_date')
        if not iso_date:
            raise PacerServiceError("iso_date is required for FileManagementFacadeService actions.")

        if action == 'setup_directories':
            await self._dir_manager.setup_directories(iso_date)
        elif action == 'save_report':
            await self._file_manager.save_report(iso_date, data['court_id'], data['report_data'])
        elif action == 'load_report':
            return await self._file_manager.load_report(iso_date, data['court_id'])
        # Add other high-level file management actions here
        else:
            raise PacerServiceError(f"Unknown action for FileManagementFacadeService: {action}")
