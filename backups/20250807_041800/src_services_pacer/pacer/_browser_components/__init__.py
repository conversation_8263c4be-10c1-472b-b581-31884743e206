# /src/services/pacer/_browser_components/__init__.py

"""
Browser management components for PACER service.

This package contains components for browser lifecycle management,
context creation, and Playwright integration.
"""

from src.pacer._browser_components.playwright_manager import PlaywrightManager
from src.pacer._browser_components.context_factory import ContextFactory
from src.pacer._browser_components.navigator import PacerNavigator

__all__ = [
    'PlaywrightManager',
    'ContextFactory',
    'PacerNavigator'
]