# /src/services/pacer/_browser_components/context_factory.py
import json
import os
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional

from playwright.async_api import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Playwright, StorageState

from src.infrastructure.patterns.component_base import ComponentImplementation


class ContextFactory(ComponentImplementation):
    """
    Factory component for creating new, isolated browser contexts.
    It encapsulates the complex setup of user profiles, preferences,
    and launch arguments.
    """

    def __init__(self, logger: Any = None, config: Optional[Dict] = None, playwright: Playwright = None, headless: bool = False):
        super().__init__(logger, config)
        self.playwright = playwright
        self.headless = headless
        self._managed_temp_dirs: List[tempfile.TemporaryDirectory] = []

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a context creation action.
        """
        action = data.get("action")
        if action == "create_context":
            return await self.create_new_context(**data)
        elif action == "cleanup":
            self.cleanup_temp_dirs()
            return None
        else:
            raise ValueError(f"Unknown action for ContextFactory: {action}")

    async def create_context(self, **kwargs) -> BrowserContext:
        """
        Creates a new persistent browser context with specific preferences.
        """
        return await self.create_new_context(**kwargs)
    
    async def create_new_context(self, **kwargs) -> BrowserContext:
        """
        Creates a new persistent browser context with specific preferences.
        """
        download_path = kwargs.get("download_path")
        storage_state = kwargs.get("storage_state") # Not used but kept for API compatibility

        if not self.playwright:
            raise ConnectionError("Playwright instance not provided to ContextFactory.")

        temp_dir_obj = tempfile.TemporaryDirectory()
        self._managed_temp_dirs.append(temp_dir_obj)
        user_data_dir = temp_dir_obj.name
        self.log_info(f"Creating new context with user data dir: {user_data_dir}")

        self._setup_profile_preferences(user_data_dir, download_path)

        launch_args = self._get_launch_args()
        context_options = {
            "user_data_dir": user_data_dir,
            "headless": self.headless,
            "args": launch_args,
            "accept_downloads": True,
            "downloads_path": download_path,
        }

        try:
            # Get the actual playwright instance if playwright is a manager
            playwright_instance = self.playwright
            if hasattr(self.playwright, 'playwright') and self.playwright.playwright:
                playwright_instance = self.playwright.playwright
            elif hasattr(self.playwright, 'connect'):
                # It's a PlaywrightManager, get the playwright instance
                playwright_instance = await self.playwright.connect()
            
            context = await playwright_instance.chromium.launch_persistent_context(**context_options)
            context.set_default_timeout(self.config.get("timeout_ms", 60000))
            self.log_info(f"Persistent context launched successfully from {user_data_dir}.")
            return context
        except Exception as e:
            self.log_error(f"Failed to launch persistent context: {e}", exc_info=True)
            self.cleanup_temp_dirs() # Clean up if launch fails
            raise ConnectionError("Failed to create browser context") from e

    def _setup_profile_preferences(self, user_data_dir: str, download_path: Optional[str]):
        """Sets up the user profile preferences and security policies."""
        try:
            default_prefs_dir = Path(user_data_dir) / "Default"
            default_prefs_dir.mkdir(parents=True, exist_ok=True)

            # Preferences file
            prefs_file_path = default_prefs_dir / "Preferences"
            preferences = {
                'plugins': {'always_open_pdf_externally': True},
                'download': {'prompt_for_download': False, 'directory_upgrade': True},
                'credentials_enable_service': False,
                'password_manager_enabled': False
            }
            if download_path:
                preferences['download']['default_directory'] = download_path
            with open(prefs_file_path, 'w') as f:
                json.dump(preferences, f)

            # Local State file to disable credential storage
            local_state_path = Path(user_data_dir) / "Local State"
            with open(local_state_path, 'w') as f:
                json.dump({"os_crypt": {"encrypted_key": ""}}, f)

            self.log_debug(f"Setup profile preferences in {user_data_dir}")
        except Exception as e:
            self.log_error(f"Failed to setup profile preferences: {e}", exc_info=True)
            raise IOError("Failed to setup profile preferences") from e

    def _get_launch_args(self) -> List[str]:
        """Returns the appropriate list of launch arguments based on headless mode."""
        base_args = [
            "--window-size=1400,2500", "--no-sandbox",
            '--disable-pdf-extension', '--disable-extensions',
            '--disable-features=PDFViewer', '--use-mock-keychain',
            '--password-store=basic', '--disable-sync'
        ]
        headless_args = [
            "--disable-gpu", "--disable-dev-shm-usage",
            '--disable-web-security', '--test-type'
        ]
        return base_args + headless_args if self.headless else base_args

    async def close_context(self, context, **kwargs):
        """Close a browser context."""
        try:
            if context and not context.is_closed():
                await context.close()
                self.log_info("Browser context closed successfully")
        except Exception as e:
            self.log_error(f"Error closing context: {e}")
        
        # Also cleanup temp directories when closing context
        self.cleanup_temp_dirs()

    def cleanup_temp_dirs(self):
        """Cleans up all managed temporary directories."""
        for temp_dir in self._managed_temp_dirs:
            try:
                temp_dir.cleanup()
                self.log_info(f"Cleaned up temporary directory: {temp_dir.name}")
            except Exception as e:
                self.log_error(f"Error cleaning up temp dir {temp_dir.name}: {e}")
        self._managed_temp_dirs.clear()
