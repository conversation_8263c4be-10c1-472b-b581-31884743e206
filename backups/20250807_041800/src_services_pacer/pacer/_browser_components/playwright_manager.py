# /src/services/pacer/_browser_components/playwright_manager.py
from typing import Any, Dict, Optional

from playwright.async_api import Playwright, async_playwright

from src.infrastructure.patterns.component_base import ComponentImplementation


class PlaywrightManager(ComponentImplementation):
    """
    Component for managing the global Playwright instance lifecycle.
    It handles starting and stopping the Playwright service.
    """

    def __init__(self, logger: Any = None, config: Optional[Dict] = None):
        super().__init__(logger, config)
        self.playwright: Optional[Playwright] = None

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a Playwright management action.
        """
        action = data.get("action")
        if action == "connect":
            await self.connect()
            return self.playwright
        elif action == "close":
            await self.close()
            return None
        elif action == "get_status":
            return {"connected": self.playwright is not None}
        else:
            raise ValueError(f"Unknown action for PlaywrightManager: {action}")

    async def connect(self) -> Playwright:
        """
        Initializes the Playwright connection if not already active.
        """
        if self.playwright:
            self.log_info("Playwright connection is already active.")
            return self.playwright

        self.log_info("Starting Playwright...")
        try:
            self.playwright = await async_playwright().start()
            self.log_info("Playwright started successfully.")
            return self.playwright
        except Exception as e:
            self.log_error(f"Failed to start Playwright: {e}", exc_info=True)
            raise ConnectionError("Failed to start Playwright") from e

    async def close(self):
        """
        Stops the Playwright instance if it is running.
        """
        if self.playwright:
            try:
                await self.playwright.stop()
                self.log_info("Playwright stopped successfully.")
            except Exception as e:
                self.log_error(f"Error stopping Playwright: {e}", exc_info=True)
            finally:
                self.playwright = None
        else:
            self.log_info("Playwright was not running or already stopped.")

    async def __aenter__(self):
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
