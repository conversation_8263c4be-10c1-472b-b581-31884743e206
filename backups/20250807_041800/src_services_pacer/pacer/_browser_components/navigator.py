# /src/services/pacer/browser/navigator.py
import logging
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any

from playwright.async_api import Page, Error as PlaywrightError, Locator

from src.infrastructure.decorators.delay_decorators import with_delay, DelayConfig
# Direct async imports - no compatibility layer needed
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError


class PacerNavigator(AsyncServiceBase):
    """Handles low-level browser interactions for PACER pages."""

    # Reduce default timeout slightly, let specific actions override if needed
    DEFAULT_TIMEOUT_MS = 25000
    def __init__(self,
                 page: Page =None,
                 config: Dict[str, Any] =None,
                 screenshot_dir: str =None,
                 timeout_ms: Optional[int] =None,
                 logger: Optional[logging.Logger] =None):
        if page.is_closed():
            raise ValueError("Cannot initialize PacerNavigator with a closed page.")

        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(f"{__name__}.PacerNavigator")
        super().__init__(logger_instance, config)

        self.page = page
        self._delay_config = DelayConfig(self.config)
        self._screenshot_dir = screenshot_dir
        # Handle DI injection issues by providing defaults if Provide objects are passed
        self.timeout = timeout_ms if timeout_ms is not None else self.DEFAULT_TIMEOUT_MS
        self.logger = logging.getLogger(f"{__name__}.PacerNavigator")

        self.log_info("PacerNavigator initialized with ComponentImplementation pattern")

    async def _execute_action(self, data: Any) -> Any:
        """Execute PacerNavigator actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'goto':
                url = action_data.get('url', '')
                wait_until = action_data.get('wait_until', 'domcontentloaded')
                timeout_override_ms = action_data.get('timeout_override_ms')
                response = await self.goto(url, wait_until, timeout_override_ms)
                return {'status': 'navigated', 'response_ok': response.ok if response else None}
            elif action == 'click':
                selector = action_data.get('selector', '')
                wait_for_nav = action_data.get('wait_for_nav', False)
                force_click = action_data.get('force_click', False)
                timeout_override_ms = action_data.get('timeout_override_ms')
                await self.click(selector, wait_for_nav, force_click, timeout_override_ms)
                return {'status': 'clicked'}
            elif action == 'fill':
                selector = action_data.get('selector', '')
                value = action_data.get('value', '')
                timeout_override_ms = action_data.get('timeout_override_ms')
                await self.fill(selector, value, timeout_override_ms)
                return {'status': 'filled'}
            elif action == 'select_option':
                selector = action_data.get('selector', '')
                values = action_data.get('values', [])
                timeout_override_ms = action_data.get('timeout_override_ms')
                await self.select_option(selector, values, timeout_override_ms)
                return {'status': 'selected'}
            elif action == 'check':
                selector = action_data.get('selector', '')
                timeout_override_ms = action_data.get('timeout_override_ms')
                await self.check(selector, timeout_override_ms)
                return {'status': 'checked'}
            elif action == 'get_text':
                selector = action_data.get('selector', '')
                text = await self.get_text(selector)
                return {'text': text}
            elif action == 'get_attribute':
                selector = action_data.get('selector', '')
                attribute = action_data.get('attribute', '')
                attr_value = await self.get_attribute(selector, attribute)
                return {'attribute_value': attr_value}
            elif action == 'get_inner_html':
                selector = action_data.get('selector', '')
                html = await self.get_inner_html(selector)
                return {'inner_html': html}
            elif action == 'locator':
                selector = action_data.get('selector', '')
                locator = await self.locator(selector)
                return {'locator_available': locator is not None}
            elif action == 'save_screenshot':
                filename_suffix = action_data.get('filename_suffix', 'action_screenshot')
                await self.save_screenshot(filename_suffix)
                return {'status': 'screenshot_saved'}
            elif action == 'set_screenshot_dir':
                path = action_data.get('path', '')
                self.set_screenshot_dir(path)
                return {'screenshot_dir': self._screenshot_dir}
            elif action == 'ensure_ready':
                await self._ensure_ready()
                return {'page_ready': self.is_ready}
            elif action == 'get_page_status':
                return self.get_page_status()
            elif action == 'validate_page':
                return self.validate_page()
            elif action == 'get_navigator_info':
                return self.get_navigator_info()
        raise PacerServiceError("Invalid action data provided to PacerNavigator")

    @property
    def is_ready(self) -> bool:
        """Check if the page is open and usable."""
        return not self.page.is_closed()

    async def _ensure_ready(self):
        """Raise an error if the page is not ready for interaction."""
        if not self.is_ready:
            self.log_error("Page is closed. Cannot perform navigation action.")
            raise PlaywrightError("Page is closed")  # Use PlaywrightError or a custom one

    @with_delay('inter_operation')
    async def goto(self, url: str, wait_until: str = 'domcontentloaded', timeout_override_ms: Optional[int] = None):
        await self._ensure_ready()
        nav_timeout = timeout_override_ms if timeout_override_ms is not None else self.timeout + 10000  # Longer for initial nav
        self.log_info(f"Navigating to: {url} (Timeout: {nav_timeout}ms)")
        try:
            response = await self.page.goto(url, wait_until=wait_until, timeout=nav_timeout)
            if response and not response.ok:
                self.log_warning(f"Navigation to {url} resulted in status {response.status}: {response.status_text}")
                # Optionally raise an error for non-2xx/3xx statuses if critical
                # raise PlaywrightError(f"Navigation failed with status {response.status}")
            self.log_debug(f"Navigation successful to {self.page.url}")
            return response  # Return response object
        except PlaywrightError as e:
            self.log_error(f"Failed navigating to {url}: {type(e).__name__} - {e}")
            await self.save_screenshot(f"nav_fail_{url.split('/')[-1].split('?')[0]}")  # Sanitize filename
            raise

    # @with_delay('inter_operation')
    async def click(self, selector: str, wait_for_nav: bool = False, force_click: bool = False,
                    timeout_override_ms: Optional[int] = None, **kwargs):
        await self._ensure_ready()
        click_timeout = timeout_override_ms if timeout_override_ms is not None else self.timeout
        self.log_debug(
            f"Clicking selector: {selector} (WaitNav: {wait_for_nav}, Force: {force_click}, Timeout: {click_timeout}ms)")
        try:
            locator = self.page.locator(selector)
            # Ensure locator finds at least one element before proceeding
            if await locator.count() == 0:
                raise PlaywrightError(f"Locator '{selector}' did not find any elements.")
            # Wait for the first element matching the selector to be visible
            await locator.first.wait_for(state='visible', timeout=click_timeout)

            click_options = {'timeout': click_timeout, **kwargs}

            if wait_for_nav:
                # Use slightly longer timeout for navigation expectation
                nav_timeout = click_timeout + 15000
                self.log_debug(f"Expecting navigation with timeout {nav_timeout}ms")
                async with self.page.expect_navigation(wait_until='domcontentloaded', timeout=nav_timeout):
                    if force_click:
                        await locator.first.dispatch_event('click', timeout=click_timeout)  # Try event dispatch first
                        # await locator.first.click(force=True, **click_options) # force=True bypasses checks
                    else:
                        await locator.first.click(**click_options)
                # Check URL after navigation
                if self.is_ready:  # Check page state again after potential nav
                    self.log_debug(f"Clicked {selector} and navigated to {self.page.url}")
                else:
                    self.log_warning(f"Clicked {selector}, expected navigation, but page closed.")

            else:
                if force_click:
                    await locator.first.dispatch_event('click', timeout=click_timeout)  # Try event dispatch first
                    # await locator.first.click(force=True, **click_options)
                else:
                    await locator.first.click(**click_options)
                await self.page.wait_for_timeout(500)  # Small delay after click without navigation
                self.log_debug(f"Clicked {selector} (no navigation expected)")
        except PlaywrightError as e:
            self.log_error(f"Failed to click {selector}: {type(e).__name__} - {e}")
            await self.save_screenshot(f"click_fail_{selector[:20]}")
            raise
        except Exception as e:  # Catch unexpected errors
            self.log_error(f"Unexpected error clicking {selector}: {type(e).__name__} - {e}")
            await self.save_screenshot(f"click_unexpected_fail_{selector[:20]}")
            raise

    # @with_delay('inter_operation')
    async def fill(self, selector: str, value: str, timeout_override_ms: Optional[int] = None, **kwargs):
        await self._ensure_ready()
        fill_timeout = timeout_override_ms if timeout_override_ms is not None else self.timeout
        # Avoid logging sensitive values like passwords
        log_value = "********" if "password" in selector.lower() else value[:50]  # Truncate long values
        self.log_debug(f"Filling selector '{selector}' with value '{log_value}' (Timeout: {fill_timeout}ms)")
        try:
            locator = self.page.locator(selector)
            if await locator.count() == 0:
                raise PlaywrightError(f"Locator '{selector}' did not find any elements.")
            await locator.first.wait_for(state='visible', timeout=fill_timeout)
            await locator.first.fill(value, timeout=fill_timeout, **kwargs)
            self.log_debug(f"Filled {selector}")
        except PlaywrightError as e:
            self.log_error(f"Failed to fill {selector}: {type(e).__name__} - {e}")
            await self.save_screenshot(f"fill_fail_{selector[:20]}")
            raise
        except Exception as e:  # Catch unexpected errors
            self.log_error(f"Unexpected error filling {selector}: {type(e).__name__} - {e}")
            await self.save_screenshot(f"fill_unexpected_fail_{selector[:20]}")
            raise

    @with_delay('inter_operation')
    async def select_option(self, selector: str, values: List[str], timeout_override_ms: Optional[int] = None,
                            **kwargs):
        await self._ensure_ready()
        select_timeout = timeout_override_ms if timeout_override_ms is not None else self.timeout
        self.log_debug(f"Selecting options {values} for selector: {selector} (Timeout: {select_timeout}ms)")
        try:
            locator = self.page.locator(selector)
            if await locator.count() == 0:
                raise PlaywrightError(f"Locator '{selector}' did not find any elements.")
            await locator.first.wait_for(state='visible', timeout=select_timeout)

            # Select options by value attribute
            result = await locator.first.select_option(value=values, timeout=select_timeout, **kwargs)

            # Check which options were actually selected (Playwright returns the selected values)
            selected_options = result if result is not None else []
            if set(selected_options) == set(values):
                self.log_debug(f"Selected all requested options for {selector}")
            else:
                missing = list(set(values) - set(selected_options))
                extra = list(set(selected_options) - set(values))  # Should be empty if select_option worked correctly
                if missing:
                    self.log_warning(f"Could not select some options for {selector}. Missing: {missing}")
                if extra:  # This case indicates a potential issue or unexpected behavior
                    self.log_warning(f"Selected unexpected extra options for {selector}: {extra}")

        except PlaywrightError as e:
            # Handle gracefully if some options don't exist, but log clearly
            if "options not found" in str(e).lower():
                self.log_warning(
                    f"Some options not found for {selector} via direct select: {e}. Playwright might have selected available ones.")
                # You could add logic here to verify which ones *were* selected if needed
            else:
                self.log_error(f"Failed to select options for {selector}: {type(e).__name__} - {e}")
                await self.save_screenshot(f"select_fail_{selector[:20]}")
                raise  # Re-raise other Playwright errors
        except Exception as e:  # Catch unexpected errors
            self.log_error(f"Unexpected error selecting options for {selector}: {type(e).__name__} - {e}")
            await self.save_screenshot(f"select_unexpected_fail_{selector[:20]}")
            raise

    @with_delay('inter_operation')  # Assuming 'inter_operation' is suitable, adjust if a shorter delay type is needed
    async def check(self, selector: str, timeout_override_ms: Optional[int] = None, **kwargs):
        await self._ensure_ready()
        check_timeout = timeout_override_ms if timeout_override_ms is not None else self.timeout
        self.log_debug(f"Ensuring checkbox '{selector}' is checked (Timeout: {check_timeout}ms)")
        try:
            locator = self.page.locator(selector)
            if await locator.count() == 0:
                raise PlaywrightError(f"Locator '{selector}' did not find any elements.")
            await locator.first.wait_for(state='visible', timeout=check_timeout)
            await locator.first.check(timeout=check_timeout, **kwargs)  # Ensures checked state idempotently
            self.log_debug(f"Checked {selector}")
        except PlaywrightError as e:
            self.log_error(f"Failed to check {selector}: {type(e).__name__} - {e}")
            await self.save_screenshot(f"check_fail_{selector[:20]}")
            raise
        except Exception as e:  # Catch unexpected errors
            self.log_error(f"Unexpected error checking {selector}: {type(e).__name__} - {e}")
            await self.save_screenshot(f"check_unexpected_fail_{selector[:20]}")
            raise

    # --- Getters (use shorter timeouts as they shouldn't block long) ---
    GETTER_TIMEOUT_MS = 5000

    async def get_text(self, selector: str, timeout_override_ms: int = None, **kwargs) -> Optional[str]:
        await self._ensure_ready()
        try:
            timeout = timeout_override_ms if timeout_override_ms is not None else self.GETTER_TIMEOUT_MS
            locator = self.page.locator(selector)
            # Wait briefly for presence, not necessarily visibility
            await locator.first.wait_for(state='attached', timeout=timeout)
            return await locator.first.text_content(timeout=timeout)
        except PlaywrightError as e:
            self.log_debug(f"Could not get text for {selector}: {type(e).__name__} - {e}")
            return None
        except Exception as e:
            self.log_warning(f"Unexpected error in get_text for {selector}: {type(e).__name__} - {e}")
            return None

    async def get_attribute(self, selector: str, attribute: str, **kwargs) -> Optional[str]:
        await self._ensure_ready()
        try:
            locator = self.page.locator(selector)
            await locator.first.wait_for(state='attached', timeout=self.GETTER_TIMEOUT_MS)
            return await locator.first.get_attribute(attribute, timeout=self.GETTER_TIMEOUT_MS, **kwargs)
        except PlaywrightError as e:
            self.log_debug(f"Could not get attribute '{attribute}' for {selector}: {type(e).__name__} - {e}")
            return None
        except Exception as e:
            self.log_warning(f"Unexpected error in get_attribute for {selector}: {type(e).__name__} - {e}")
            return None

    async def get_inner_html(self, selector: str, **kwargs) -> Optional[str]:
        await self._ensure_ready()
        try:
            locator = self.page.locator(selector)
            await locator.first.wait_for(state='attached', timeout=self.GETTER_TIMEOUT_MS)
            return await locator.first.inner_html(timeout=self.GETTER_TIMEOUT_MS, **kwargs)
        except PlaywrightError as e:
            self.log_debug(f"Could not get inner HTML for {selector}: {type(e).__name__} - {e}")
            return None
        except Exception as e:
            self.log_warning(f"Unexpected error in get_inner_html for {selector}: {type(e).__name__} - {e}")
            return None

    async def locator(self, selector: str, **kwargs) -> Locator:
        """Provides direct access to locator for more complex checks/interactions."""
        await self._ensure_ready()
        return self.page.locator(selector, **kwargs)

    async def save_screenshot(self, filename_suffix: str):
        if not self.is_ready:
            self.log_warning("Cannot save screenshot, page is closed.")
            return

        # Ensure screenshot directory exists
        try:
            Path(self._screenshot_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            self.log_error(f"Failed to create screenshot directory {self._screenshot_dir}: {e}")
            return  # Cannot save if directory fails

        # Sanitize suffix: replace non-alphanumeric with underscore, limit length
        safe_suffix = re.sub(r'[^\w\-_\.]', '_', filename_suffix)
        safe_suffix = safe_suffix[:50]  # Limit length
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{safe_suffix}.png"
        screenshot_path = os.path.join(self._screenshot_dir, filename)

        try:
            await self.page.screenshot(path=screenshot_path, full_page=True, timeout=15000)  # Add timeout
            self.log_info(f"Screenshot saved: {screenshot_path}")
        except PlaywrightError as e:
            self.log_error(f"Failed to save screenshot {screenshot_path}: {type(e).__name__} - {e}")
        except Exception as e:
            self.log_error(f"Unexpected error saving screenshot {screenshot_path}: {type(e).__name__} - {e}")

    def set_screenshot_dir(self, path: str):
        """Sets the directory for saving screenshots."""
        self._screenshot_dir = path
        self.log_debug(f"Screenshot directory set to: {path}")

    async def __aenter__(self):
        # Could add setup logic here if needed, e.g., wait for ready state
        await self._ensure_ready()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # No specific cleanup in navigator needed, page closure handled elsewhere
        pass

    # === ComponentImplementation Helper Methods ===

    def get_page_status(self) -> Dict[str, Any]:
        """Get page status information."""
        try:
            current_url = self.page.url if not self.page.is_closed() else 'page_closed'
        except Exception:
            current_url = 'unable_to_determine'

        return {
            'is_ready': self.is_ready,
            'page_closed': self.page.is_closed() if hasattr(self.page, 'is_closed') else True,
            'current_url': current_url,
            'timeout_ms': self.timeout,
            'screenshot_dir': self._screenshot_dir,
            'delay_config_available': self._delay_config is not None
        }

    def validate_page(self) -> Dict[str, Any]:
        """Validate page state and configuration."""
        validation_results = {
            'page_available': self.page is not None,
            'page_ready': self.is_ready,
            'config_valid': isinstance(self.config, dict),
            'screenshot_dir_exists': os.path.exists(self._screenshot_dir) if self._screenshot_dir else False,
            'delay_config_valid': self._delay_config is not None,
            'status': 'valid'
        }

        # Check if page has required methods
        required_methods = ['goto', 'locator', 'screenshot', 'url']
        method_availability = {}
        for method in required_methods:
            method_availability[method] = hasattr(self.page, method) if self.page else False

        validation_results['page_methods'] = method_availability

        # Determine overall validity
        if not all([
            validation_results['page_available'],
            validation_results['page_ready'],
            validation_results['config_valid'],
            all(method_availability.values())
        ]):
            validation_results['status'] = 'invalid'

        return validation_results

    def get_navigator_info(self) -> Dict[str, Any]:
        """Get navigator configuration and capability information."""
        return {
            'service_type': 'PacerNavigator',
            'configuration': {
                'timeout_ms': self.timeout,
                'default_timeout_ms': self.DEFAULT_TIMEOUT_MS,
                'getter_timeout_ms': self.GETTER_TIMEOUT_MS,
                'screenshot_dir': self._screenshot_dir
            },
            'page_status': {
                'available': self.page is not None,
                'ready': self.is_ready,
                'url': self.page.url if self.is_ready else 'unavailable'
            },
            'capabilities': [
                'navigation',
                'element_interaction',
                'form_filling',
                'screenshot_capture',
                'delay_management',
                'text_extraction',
                'attribute_extraction'
            ],
            'delay_config': {
                'available': self._delay_config is not None,
                'type': type(self._delay_config).__name__ if self._delay_config else None
            }
        }
