# /src/services/pacer/navigation_facade_service.py
from __future__ import annotations
from typing import Any, Dict, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer._browser_components.navigator import PacerNavigator
    from src.pacer._navigation_components.url_builder import UrlBuilder
    from src.pacer._navigation_components.element_locator import ElementLocator
    from src.pacer._navigation_components.page_navigator import PageNavigator
    from src.infrastructure.protocols.logger import LoggerProtocol

class NavigationFacadeService(AsyncServiceBase):
    """Unified interface for PACER navigation operations."""

    def __init__(self,
                 url_builder: UrlBuilder,
                 element_locator: ElementLocator,
                 page_navigator: PageNavigator,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        self._url_builder = url_builder
        self._locator = element_locator
        self._navigator = page_navigator

    async def execute(self, data: Any) -> Any:
        """Main execution method for navigation facade."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        if action == 'go_to_query_page':
            await self.go_to_query_page(data['navigator'])
        elif action == 'query_docket':
            await self.query_docket(data['navigator'], data['docket_num'])
        else:
            raise PacerServiceError(f"Unknown action for NavigationFacadeService: {action}")

    async def go_to_query_page(self, navigator: PacerNavigator):
        """Navigates to the main query page."""
        self.log_info("Navigating to query page.")
        
        # Check if we're already in a court's ECF system (don't navigate away if we are)
        current_url = navigator.page.url
        if 'ecf.' in current_url and 'uscourts.gov' in current_url:
            self.log_info(f"Already in court ECF system ({current_url}), staying on current page.")
            return
            
        query_url = self._url_builder.get_lookup_url()  # Remove await - this is synchronous
        await self._navigator.go_to_url(navigator, query_url)
        # In a real scenario, we might need to click a 'Query' link after this.
        # This is a simplified flow.

    async def query_docket(self, navigator: PacerNavigator, docket_num: str):
        """
        Performs a full docket query workflow.
        This is a simplified version of the logic in the original service.
        """
        self.log_info(f"Starting docket query for {docket_num}.")

        # 1. Navigate to query page (or assume we are there)
        # For this example, we assume we are already on the query page.

        # 2. Fill in docket number
        await self._navigator.fill_element(navigator, 'case_num_input', docket_num)

        # 3. Click "Run Query"
        await self._navigator.click_element(navigator, 'run_query_button')

        # 4. Handle intermediate page and click "Docket Report"
        # This often opens a new page. The logic for that is complex and omitted here for brevity.
        # await self._navigator.click_element(navigator, 'docket_report_link')

        # 5. Click final "Run Report"
        # await self._navigator.click_element(navigator, 'run_report_button')

        self.log_info(f"Docket query workflow for {docket_num} initiated.")
