# /src/services/pacer/transfer_facade_service.py
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer._transfer_components.transfer_processor import TransferProcessor


class TransferFacadeService(AsyncServiceBase):
    """
    Facade service for processing case transfers. It coordinates the
    TransferProcessor component to handle the workflow.
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        transfer_processor: Optional[TransferProcessor] = None,
    ):
        super().__init__(logger, config)
        self.transfer_processor = transfer_processor

    async def _execute_action(self, data: Any) -> Any:
        """
        Routes actions to the transfer processing component.
        """
        action = data.get("action")
        if action == "process_transfer_case":
            if not self.transfer_processor:
                raise ValueError("TransferProcessor is not initialized.")

            # The component expects a different action name
            data["action"] = "process_transfer"
            return await self.transfer_processor.execute(data)
        else:
            raise ValueError(f"Unknown action for TransferFacadeService: {action}")
