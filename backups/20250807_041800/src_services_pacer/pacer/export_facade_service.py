# /src/services/pacer/export_facade_service.py
from __future__ import annotations
from typing import Any, Dict, List, Optional, TYPE_CHECKING
import pandas as pd
from rich.console import Console
from rich.table import Table

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer._export_components.csv_exporter import CsvExporter
    from src.infrastructure.protocols.logger import LoggerProtocol

class ExportFacadeService(AsyncServiceBase):
    """Unified interface for PACER data export operations."""

    def __init__(self,
                 csv_exporter: CsvExporter,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        self._csv_exporter = csv_exporter
        self.console = Console()

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        if action == 'export_to_csv':
            await self._csv_exporter.export_to_csv(data['items'], data['filename'], data.get('fields'))
        elif action == 'export_to_excel':
            self.export_to_excel(data['items'], data['filename'], data.get('sheet_name', 'PACER Data'))
        elif action == 'display_results':
            self.display_results(data['items'])
        else:
            raise PacerServiceError(f"Unknown action for ExportFacadeService: {action}")

    def export_to_excel(self, items: List[Dict[str, Any]], filename: str, sheet_name: str = "PACER Data"):
        """Exports a list of dictionaries to an Excel file."""
        if not items:
            self.log_warning("No items to export to Excel.")
            return
        try:
            df = pd.DataFrame(items)
            df.to_excel(filename, sheet_name=sheet_name, index=False)
            self.log_info(f"Successfully exported {len(items)} items to {filename}")
        except Exception as e:
            self.log_error(f"Error exporting to Excel: {e}")
            raise PacerServiceError(f"Failed to export to Excel: {e}") from e

    def display_results(self, items: List[Dict[str, Any]]):
        """Displays a list of dictionaries in a rich table."""
        if not items:
            self.console.print("[yellow]No results to display.[/yellow]")
            return

        table = Table(show_header=True, header_style="bold magenta")

        # Use a sample of the data to determine columns
        if items:
            sample = items[0]
            for key in sample.keys():
                table.add_column(key)

        for item in items:
            table.add_row(*[str(v) for v in item.values()])

        self.console.print(table)
