# /src/services/pacer/pacer_factory.py
"""
PACER Factory for creating and wiring PACER orchestrator dependencies.

This factory handles the creation and dependency injection of all PACER facades
and creates a fully configured orchestrator instance.
"""

import logging
from typing import Any, Dict, List, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from src.pacer.facades.browser_service import BrowserService
    from src.pacer.facades.case_processing_service import CaseProcessingService
    from src.pacer.facades.classification_service import ClassificationService
    from src.pacer.facades.configuration_service import ConfigurationService
    from src.pacer.facades.docket_orchestrator import DocketOrchestrator
    from src.pacer.facades.download_service import DownloadService
    from src.pacer.facades.file_operations_service import FileOperationsService
    from src.pacer.facades.metrics_service import MetricsService
    from src.pacer.facades.relevance_service import RelevanceService
    from src.pacer.facades.s3_service import S3Service
    from src.pacer.facades.verification_service import VerificationService

from .pacer_orchestrator_service import PacerOrchestratorService


class PacerFactory:
    """
    Factory for creating and configuring PACER orchestrator with all dependencies.
    
    This factory handles:
    - Creation of all facade services
    - Dependency injection and wiring
    - Configuration management
    - Service lifecycle coordination
    """
    
    def __init__(self, logger: Optional[Any] = None, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the factory.
        
        Args:
            logger: Logger instance to use for all services
            config: Configuration dictionary for all services
        """
        self._logger = logger or logging.getLogger(__name__)
        self._config = config or {}
        self._facades: Dict[str, Any] = {}
    
    async def create_orchestrator(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict[str, Any]] = None,
        facade_config: Optional[Dict[str, Any]] = None
    ) -> PacerOrchestratorService:
        """
        Create a fully configured PACER orchestrator with all dependencies.
        
        Args:
            logger: Override logger for the orchestrator
            config: Override config for the orchestrator
            facade_config: Specific configuration for facades
        
        Returns:
            PacerOrchestratorService: Fully configured orchestrator
        """
        # Use provided logger/config or factory defaults
        orchestrator_logger = logger or self._logger
        orchestrator_config = config or self._config
        
        # Create all facade services
        facades = await self._create_facades(orchestrator_logger, facade_config)
        
        # Create and configure the orchestrator
        orchestrator = PacerOrchestratorService(
            logger=orchestrator_logger,
            config=orchestrator_config,
            **facades
        )
        
        # Initialize the orchestrator
        await orchestrator.initialize()
        
        return orchestrator
    
    async def _create_facades(
        self, 
        logger: Any, 
        facade_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create all facade services with proper dependency injection.
        
        Args:
            logger: Logger to use for all facades
            facade_config: Configuration for facades
        
        Returns:
            Dict containing all created facade services
        """
        config = facade_config or self._config
        facades = {}
        
        # Import and create facades dynamically to handle import errors gracefully
        facade_configs = [
            ('configuration_service', 'src.pacer.facades.configuration_service', 'ConfigurationService'),
            ('browser_service', 'src.pacer.facades.browser_service', 'BrowserService'),
            ('file_operations_service', 'src.pacer.facades.file_operations_service', 'FileOperationsService'),
            ('s3_service', 'src.pacer.facades.s3_service', 'S3Service'),
            ('download_service', 'src.pacer.facades.download_service', 'DownloadService'),
            ('verification_service', 'src.pacer.facades.verification_service', 'VerificationService'),
            ('relevance_service', 'src.pacer.facades.relevance_service', 'RelevanceService'),
            ('classification_service', 'src.pacer.facades.classification_service', 'ClassificationService'),
            ('case_processing_service', 'src.pacer.facades.case_processing_service', 'CaseProcessingService'),
            ('metrics_service', 'src.pacer.facades.metrics_service', 'MetricsService'),
            ('docket_orchestrator', 'src.pacer.facades.docket_orchestrator', 'DocketOrchestrator'),
        ]
        
        for facade_name, module_path, class_name in facade_configs:
            try:
                # Dynamic import to handle missing modules gracefully
                import importlib
                module = importlib.import_module(module_path)
                facade_class = getattr(module, class_name)
                
                # Create and initialize facade
                facade_instance = facade_class(logger=logger, config=config)
                await facade_instance.initialize()
                
                facades[facade_name] = facade_instance
                logger.info(f"Successfully created {facade_name}")
                
            except (ImportError, AttributeError, Exception) as e:
                logger.warning(f"Could not create {facade_name}: {e}")
                facades[facade_name] = None
        
        # Cache facades for cleanup
        self._facades = {k: v for k, v in facades.items() if v is not None}
        
        return facades
    
    async def create_minimal_orchestrator(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict[str, Any]] = None,
        required_facades: Optional[List[str]] = None
    ) -> PacerOrchestratorService:
        """
        Create a minimal orchestrator with only the required facades.
        
        This is useful for testing or when only specific functionality is needed.
        
        Args:
            logger: Logger instance
            config: Configuration dictionary
            required_facades: List of facade names to create (defaults to core facades)
        
        Returns:
            PacerOrchestratorService: Minimal orchestrator instance
        """
        orchestrator_logger = logger or self._logger
        orchestrator_config = config or self._config
        
        # Default to core required facades
        if required_facades is None:
            required_facades = ['configuration_service', 'browser_service']
        
        facades = {}
        
        # Create only the required facades
        facade_configs = {
            'configuration_service': ('src.pacer.facades.configuration_service', 'ConfigurationService'),
            'browser_service': ('src.pacer.facades.browser_service', 'BrowserService'),
            'docket_orchestrator': ('src.pacer.facades.docket_orchestrator', 'DocketOrchestrator'),
            'file_operations_service': ('src.pacer.facades.file_operations_service', 'FileOperationsService'),
            'case_processing_service': ('src.pacer.facades.case_processing_service', 'CaseProcessingService'),
        }
        
        for facade_name in required_facades:
            if facade_name in facade_configs:
                module_path, class_name = facade_configs[facade_name]
                try:
                    import importlib
                    module = importlib.import_module(module_path)
                    facade_class = getattr(module, class_name)
                    
                    facade_instance = facade_class(logger=orchestrator_logger, config=orchestrator_config)
                    await facade_instance.initialize()
                    
                    facades[facade_name] = facade_instance
                    orchestrator_logger.info(f"Successfully created {facade_name} for minimal orchestrator")
                    
                except Exception as e:
                    orchestrator_logger.warning(f"Could not create {facade_name} for minimal orchestrator: {e}")
                    facades[facade_name] = None
            else:
                orchestrator_logger.warning(f"Unknown facade {facade_name} requested")
                facades[facade_name] = None
        
        # Create orchestrator with minimal facades
        orchestrator = PacerOrchestratorService(
            logger=orchestrator_logger,
            config=orchestrator_config,
            **facades
        )
        
        await orchestrator.initialize()
        return orchestrator
    
    async def cleanup(self) -> None:
        """Clean up all created facade services."""
        if not self._facades:
            return
        
        # Clean up facades in reverse order of creation
        facade_names = list(self._facades.keys())
        facade_names.reverse()
        
        for facade_name in facade_names:
            facade = self._facades.get(facade_name)
            if facade and hasattr(facade, 'cleanup'):
                try:
                    await facade.cleanup()
                except Exception as e:
                    self._logger.warning(f"Error cleaning up {facade_name}: {e}")
        
        self._facades.clear()


# Convenience functions for common use cases

async def create_pacer_orchestrator(
    logger: Optional[Any] = None,
    config: Optional[Dict[str, Any]] = None
) -> PacerOrchestratorService:
    """
    Convenience function to create a fully configured PACER orchestrator.
    
    Args:
        logger: Logger instance
        config: Configuration dictionary
    
    Returns:
        PacerOrchestratorService: Fully configured orchestrator
    """
    factory = PacerFactory(logger, config)
    try:
        return await factory.create_orchestrator()
    except Exception as e:
        # If full orchestrator creation fails, try minimal
        if logger:
            logger.warning(f"Full orchestrator creation failed, trying minimal: {e}")
        return await factory.create_minimal_orchestrator()


async def create_minimal_pacer_orchestrator(
    logger: Optional[Any] = None,
    config: Optional[Dict[str, Any]] = None,
    required_facades: Optional[List[str]] = None
) -> PacerOrchestratorService:
    """
    Convenience function to create a minimal PACER orchestrator.
    
    Args:
        logger: Logger instance
        config: Configuration dictionary
        required_facades: List of required facade names
    
    Returns:
        PacerOrchestratorService: Minimal orchestrator
    """
    factory = PacerFactory(logger, config)
    return await factory.create_minimal_orchestrator(
        logger, config, required_facades
    )