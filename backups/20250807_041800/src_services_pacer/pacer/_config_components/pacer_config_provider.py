# /src/services/pacer/_config_components/pacer_config_provider.py
from __future__ import annotations
from pathlib import Path
from typing import Any, Dict, List, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer._config_components.json_config_loader import JsonConfigLoader
    from src.infrastructure.protocols.logger import LoggerProtocol

class PacerConfigProvider(ComponentImplementation):
    """Provides PACER-specific configurations."""

    def __init__(self,
                 json_loader: JsonConfigLoader,
                 logger: LoggerProtocol = None,
                 config: Dict[str, Any] = None):
        super().__init__(logger, config)
        self._loader = json_loader
        self.config_dir = Path(self.config.get('config_dir', 'src/config/pacer'))

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'get_all_configs':
            return self.get_all_configs()
        else:
            raise PacerServiceError(f"Unknown action for PacerConfigProvider: {action}")

    def get_all_configs(self) -> Dict[str, Any]:
        """Loads and returns all PACER configurations."""
        self.log_info("Loading all PACER configurations.")
        return {
            "relevance": self._loader.load_config(self.config_dir / 'relevance_config.json'),
            "stability": self._loader.load_config(self.config_dir / 'stability_config.json'),
            "paths": self._loader.load_config(self.config_dir / 'paths_config.json'),
            "ignore_download": self._loader.load_config(self.config_dir / 'ignore_download/ignore_download.json'),
            "relevant_defendants": self._loader.load_config(self.config_dir / 'defendants/relevant_defendants.json'),
        }
