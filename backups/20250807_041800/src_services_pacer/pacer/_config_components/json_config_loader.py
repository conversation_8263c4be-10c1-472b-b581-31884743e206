# /src/services/pacer/_config_components/json_config_loader.py
from __future__ import annotations
import json
from pathlib import Path
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class JsonConfigLoader(ComponentImplementation):
    """Loads configuration from a JSON file."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'load_config':
            return self.load_config(data['file_path'])
        else:
            raise PacerServiceError(f"Unknown action for JsonConfigLoader: {action}")

    def load_config(self, file_path: Path) -> Dict[str, Any]:
        """Loads a JSON configuration file."""
        self.log_info(f"Loading configuration from {file_path}")
        try:
            with file_path.open('r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            self.log_error(f"Configuration file not found: {file_path}")
            return {}
        except json.JSONDecodeError as e:
            self.log_error(f"Error decoding JSON from {file_path}: {e}")
            return {}
        except Exception as e:
            self.log_error(f"Unexpected error loading config file {file_path}: {e}")
            return {}
