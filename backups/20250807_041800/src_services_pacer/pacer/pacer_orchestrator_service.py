# /src/services/pacer/pacer_orchestrator_service.py
import asyncio
from datetime import date as DateType, datetime
from typing import Any, Dict, List

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer._core_services import (
    ConfigurationService,
    CaseProcessingService,
    RelevanceService,
    VerificationService,
    DownloadOrchestrationService,
    FileOperationsService,
    MetricsReportingService,
    S3ManagementService
)
from src.pacer._core_services.docket_processing_orchestrator import DocketProcessingOrchestrator
# Browser facade service removed during refactoring - using try/except for compatibility
try:
    from src.pacer.browser_facade_service import BrowserFacadeService
except ImportError:
    # Placeholder for when BrowserFacadeService is removed
    BrowserFacadeService = None


class PacerOrchestratorService(AsyncServiceBase):
    """
    Main orchestrator for all PACER operations. Transformed to use the 8 core services
    through the DocketProcessingOrchestrator instead of facade services.
    
    This orchestrator coordinates high-level court processing workflows using
    the new core services architecture.
    """

    def __init__(
        self,
        logger: Any = None,
        config: Dict[str, Any] = None,
        browser_facade: Any = None,  # Temporary: Browser management (type depends on availability)
        # 8 Core Services
        configuration_service: ConfigurationService = None,
        case_processing_service: CaseProcessingService = None,
        relevance_service: RelevanceService = None,
        verification_service: VerificationService = None,
        download_orchestration_service: DownloadOrchestrationService = None,
        file_operations_service: FileOperationsService = None,
        metrics_reporting_service: MetricsReportingService = None,
        s3_management_service: S3ManagementService = None,
    ):
        super().__init__(logger, config)
        
        # Temporary: Browser facade still needed until we create BrowserService  
        self.browser_facade = browser_facade
        
        # Initialize 8 core services
        self._configuration_service = configuration_service
        self._case_processing_service = case_processing_service
        self._relevance_service = relevance_service
        self._verification_service = verification_service
        self._download_orchestration_service = download_orchestration_service
        self._file_operations_service = file_operations_service
        self._metrics_reporting_service = metrics_reporting_service
        self._s3_management_service = s3_management_service
        
        # Create the core services orchestrator
        self._docket_orchestrator = DocketProcessingOrchestrator(
            configuration_service=self._configuration_service,
            case_processing_service=self._case_processing_service,
            relevance_service=self._relevance_service,
            verification_service=self._verification_service,
            download_orchestration_service=self._download_orchestration_service,
            file_operations_service=self._file_operations_service,
            metrics_reporting_service=self._metrics_reporting_service,
            s3_management_service=self._s3_management_service,
            logger=logger,
            config=config
        )
        
        self.run_parallel = self.config.get("run_parallel", False)

    async def _initialize_service(self) -> None:
        """Initialize the orchestrator and all core services."""
        self.log_info("Initializing PacerOrchestratorService with core services")
        
        # Initialize the docket orchestrator (which will initialize all 8 core services)
        await self._docket_orchestrator.initialize()
        
        self.log_info("PacerOrchestratorService initialized successfully")

    async def _execute_action(self, data: Any) -> Any:
        """Executes a high-level orchestration action."""
        action = data.get("action")
        if action == "run_main_workflow":
            return await self.run_main_workflow()
        else:
            raise ValueError(f"Unknown action for PacerOrchestratorService: {action}")

    async def run_main_workflow(self) -> Dict[str, Any]:
        """
        Runs the main PACER processing workflow using core services.
        """
        self.log_info("Starting main PACER workflow with core services.")
        
        # Load configuration using ConfigurationService
        config_data = await self._configuration_service.execute({"action": "get_all_configs"})
        courts_to_process = config_data.get("courts_to_process", [])

        if not courts_to_process:
            self.log_warning("No courts to process.")
            return {"status": "no_courts", "results": {}}

        # Main processing logic
        if self.run_parallel:
            results = await self._process_courts_parallel(courts_to_process, config_data)
        else:
            results = await self._process_courts_sequential(courts_to_process, config_data)

        self.log_info("Main PACER workflow finished.")
        return {"status": "complete", "results": results}

    async def _process_courts_parallel(self, courts: List[str], config_data: Dict) -> Dict:
        """Processes multiple courts in parallel, with isolated browser contexts."""
        self.log_info(f"Processing {len(courts)} courts in parallel with core services.")
        tasks = []
        for court_id in courts:
            tasks.append(self._process_single_court_isolated(court_id, config_data))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        processed_results = {}
        for i, res in enumerate(results):
            court_id = courts[i]
            if isinstance(res, Exception):
                self.log_error(f"Error processing court {court_id}: {res}", exc_info=res)
                processed_results[court_id] = {"status": "failed", "error": str(res)}
            else:
                processed_results[court_id] = res

        return processed_results

    async def _process_courts_sequential(self, courts: List[str], config_data: Dict) -> Dict:
        """Processes multiple courts sequentially in a single browser context."""
        self.log_info(f"Processing {len(courts)} courts sequentially with core services.")
        results = {}
        
        if self.browser_facade:
            async with self.browser_facade as browser:
                context = await browser.get_new_context()
                try:
                    for court_id in courts:
                        self.log_info(f"Processing court: {court_id}")
                        result = await self._process_single_court_with_context(court_id, config_data, context)
                        results[court_id] = result
                finally:
                    try:
                        if context:
                            await context.close()
                    except Exception as e:
                        self.log_warning(f"Error closing context: {e}")
        else:
            self.log_error("Browser facade not available for sequential processing")
            for court_id in courts:
                results[court_id] = {"status": "failed", "error": "Browser facade not available"}
        
        return results

    async def _process_single_court_isolated(self, court_id: str, config_data: Dict) -> Dict:
        """Processes a single court in its own isolated browser context using core services."""
        self.log_info(f"Creating isolated context for court: {court_id}")
        
        if not self.browser_facade:
            return {"status": "failed", "error": "Browser facade not available"}
        
        try:
            async with self.browser_facade as browser:
                # Create a unique download path using FileOperationsService
                iso_date = config_data.get("iso_date", datetime.now().strftime('%Y-%m-%d'))
                await self._file_operations_service.execute({
                    "action": "setup_directories",
                    "iso_date": iso_date
                })

                context = await browser.get_new_context()
                try:
                    return await self._process_single_court_with_context(court_id, config_data, context)
                finally:
                    try:
                        if context:
                            await context.close()
                    except Exception as e:
                        self.log_warning(f"Error closing context: {e}")
        except Exception as e:
            self.log_error(f"Error processing court {court_id}: {str(e)}", exc_info=True)
            return {"status": "failed", "error": str(e)}

    async def _process_single_court_with_context(self, court_id: str, config_data: Dict, context) -> Dict:
        """
        Process a single court using the DocketProcessingOrchestrator with core services.
        
        This method delegates to the DocketProcessingOrchestrator which implements the 
        exact workflow from docket_processing.md using all 8 core services.
        """
        try:
            self.log_info(f"Processing court {court_id} using DocketProcessingOrchestrator")
            
            # This would typically involve:
            # 1. Navigating to the court's civil report page
            # 2. Processing each docket found in the report using the DocketProcessingOrchestrator
            
            # For now, return a placeholder - the actual implementation would integrate
            # with the court processing workflow that gets dockets from civil reports
            # and processes them through the DocketProcessingOrchestrator.process_docket_page()
            
            result = {
                "status": "success",
                "court_id": court_id,
                "message": f"Court {court_id} processed using core services",
                "processed_dockets": 0,
                "iso_date": config_data.get("iso_date")
            }
            
            self.log_info(f"Court {court_id} processing completed")
            return result
            
        except Exception as e:
            self.log_error(f"Error processing court {court_id}: {str(e)}", exc_info=True)
            return {
                "status": "failed", 
                "court_id": court_id,
                "error": str(e)
            }

    async def process_courts(self, court_ids: List[str], context=None, iso_date: str = None, 
                           start_date=None, end_date=None, docket_list_input=None) -> Dict[str, Any]:
        """
        Public method to process courts - called by ScrapingOrchestrator.
        
        Args:
            court_ids: List of court IDs to process
            context: Browser context (optional, can be None)
            iso_date: ISO date string
            start_date: Start date for processing
            end_date: End date for processing
            docket_list_input: Optional list of specific dockets to process
        
        Returns:
            Dictionary containing processing results
        """
        self.log_info(f"process_courts called with {len(court_ids)} courts using core services")
        
        # Build config data from parameters
        config_data = {
            "iso_date": iso_date,
            "start_date": start_date,
            "end_date": end_date,
            "courts_to_process": court_ids,
            "docket_list_input": docket_list_input
        }
        
        # If specific docket list provided, handle that workflow
        if docket_list_input and isinstance(docket_list_input, list):
            self.log_info(f"Processing specific docket list with {len(docket_list_input)} dockets")
            return await self._process_specific_dockets(docket_list_input, config_data, context)
        
        # Process the courts using existing workflow
        if self.run_parallel and len(court_ids) > 1:
            return await self._process_courts_parallel(court_ids, config_data)
        else:
            return await self._process_courts_sequential(court_ids, config_data)

    async def _process_specific_dockets(self, docket_list: List[Dict], config_data: Dict, context) -> Dict[str, Any]:
        """
        Process a specific list of dockets using the DocketProcessingOrchestrator.
        
        This is where individual dockets get processed through the complete
        docket_processing.md workflow using all 8 core services.
        """
        self.log_info(f"Processing {len(docket_list)} specific dockets using DocketProcessingOrchestrator")
        
        results = {
            "status": "success",
            "total_dockets": len(docket_list),
            "processed_dockets": 0,
            "successful_dockets": 0,
            "failed_dockets": 0,
            "docket_results": []
        }
        
        for docket_info in docket_list:
            try:
                # Extract docket information
                court_id = docket_info.get('court_id')
                docket_num = docket_info.get('docket_num')
                
                if not court_id or not docket_num:
                    self.log_warning(f"Invalid docket info: {docket_info}")
                    continue
                
                # Create initial details from docket info
                initial_details = {
                    "court_id": court_id,
                    "docket_num": docket_num,
                    "versus": docket_info.get('versus', ''),
                    "filing_date": docket_info.get('filing_date', ''),
                    "docket_link": docket_info.get('docket_link', ''),
                    "extracted_at": datetime.now().isoformat(),
                    "source": "explicit_docket_list"
                }
                
                # Process this docket using the DocketProcessingOrchestrator
                # Note: This would need a page object for the actual docket page
                # For now, we'll log the action that would be taken
                
                self.log_info(f"Would process docket: {court_id} {docket_num}")
                
                # TODO: Implement actual docket page navigation and processing
                # docket_result = await self._docket_orchestrator.execute({
                #     "action": "process_docket_page",
                #     "page": docket_page,
                #     "initial_details": initial_details,
                #     "is_explicitly_requested": True
                # })
                
                results["processed_dockets"] += 1
                results["successful_dockets"] += 1
                
            except Exception as e:
                self.log_error(f"Error processing docket {docket_info}: {str(e)}", exc_info=True)
                results["failed_dockets"] += 1
                results["docket_results"].append({
                    "docket_info": docket_info,
                    "status": "failed",
                    "error": str(e)
                })
        
        return results

    async def health_check(self) -> Dict[str, Any]:
        """Health check for the orchestrator and all core services."""
        health_status = {
            "service": "PacerOrchestratorService", 
            "status": "healthy" if self._initialized else "unhealthy",
            "browser_facade_available": self.browser_facade is not None
        }
        
        # Get health status from DocketProcessingOrchestrator (includes all 8 core services)
        try:
            orchestrator_health = await self._docket_orchestrator.health_check()
            health_status["docket_orchestrator"] = orchestrator_health
            
            # Overall status based on core services health
            if orchestrator_health.get("overall_status") != "healthy":
                health_status["status"] = "degraded"
                
        except Exception as e:
            health_status["docket_orchestrator"] = {"status": "error", "error": str(e)}
            health_status["status"] = "unhealthy"
        
        return health_status

    async def _cleanup_service(self) -> None:
        """Clean up orchestrator and all core services."""
        self.log_info("Cleaning up PacerOrchestratorService")
        
        # Clean up the docket orchestrator (which will clean up all 8 core services)
        if self._docket_orchestrator:
            try:
                await self._docket_orchestrator.cleanup()
            except Exception as e:
                self.log_warning(f"Error cleaning up docket orchestrator: {str(e)}")
        
        self.log_info("PacerOrchestratorService cleanup completed")