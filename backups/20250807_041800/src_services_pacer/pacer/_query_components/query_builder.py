# /src/services/pacer/_query_components/query_builder.py
from __future__ import annotations
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class QueryBuilder(ComponentImplementation):
    """Builds complex query criteria for the repository."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'build_complex_query':
            return self.build_complex_query(data.get('criteria', {}))
        else:
            raise PacerServiceError(f"Unknown action for QueryBuilder: {action}")

    def build_complex_query(self, criteria: Dict[str, Any]) -> Dict[str, Any]:
        """
        Builds a normalized query dictionary from various search criteria.
        In a real scenario, this could involve more complex logic, like
        translating natural language queries or building complex filter expressions.
        """
        self.log_info(f"Building complex query from criteria: {criteria}")

        # For now, this is a simple pass-through, but it establishes the pattern.
        # It could be extended to validate, clean, or transform the criteria.

        query = {
            "title": criteria.get('title'),
            "law_firm": criteria.get('law_firm'),
            "mdl_num": criteria.get('mdl_num'),
            "court_id": criteria.get('court_id'),
            "start_date": criteria.get('start_date'),
            "end_date": criteria.get('end_date'),
        }

        # Remove None values to keep the query clean
        return {k: v for k, v in query.items() if v is not None}
