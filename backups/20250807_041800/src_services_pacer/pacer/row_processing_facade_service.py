# /src/services/pacer/row_processing_facade_service.py
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.jobs.job_processor import JobProcessor
from src.pacer._processing_components.report_processor import ReportProcessor
from src.pacer._processing_components.single_docket_processor import \
    SingleDocketProcessor


class RowProcessingFacadeService(AsyncServiceBase):
    """
    Facade service for orchestrating the processing of PACER report rows
    and single dockets. It coordinates various components to manage the
    entire workflow from row extraction to document download.
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        report_processor: Optional[ReportProcessor] = None,
        single_docket_processor: Optional[SingleDocketProcessor] = None,
        # Note: JobProcessor is a dependency of ReportProcessor, not this facade.
    ):
        super().__init__(logger, config)
        self._report_processor = report_processor
        self._single_docket_processor = single_docket_processor

    async def execute(self, data: Any) -> Any:
        """Main execution method for the facade."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """
        Routes actions to the appropriate processing components.
        """
        action = data.get("action")
        if action == "process_report_rows":
            if not self._report_processor:
                raise ValueError("ReportProcessor is not initialized.")
            # The action name for the component is different from the facade
            data["action"] = "process_report"
            return await self._report_processor.execute(data)

        elif action == "process_single_row_docket":
            if not self._single_docket_processor:
                raise ValueError("SingleDocketProcessor is not initialized.")
            # The action name for the component is different from the facade
            data["action"] = "process_docket"
            return await self._single_docket_processor.execute(data)

        else:
            raise ValueError(f"Unknown action for RowProcessingFacadeService: {action}")
