# /src/services/pacer/pacer_orchestrator_service.py
import asyncio
from datetime import date as DateType
from typing import Any, Dict, List

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer._core_services.configuration.configuration_service import ConfigurationService
from src.pacer._browser_components.navigator import PacerNavigator
from src.pacer._processing_components.single_docket_processor import SingleDocketProcessor


class PacerOrchestratorService(AsyncServiceBase):
    """
    Main orchestrator for all PACER operations. It coordinates high-level
    facades to execute scraping and processing workflows.
    """

    def __init__(
        self,
        logger: Any = None,
        config: Dict[str, Any] = None,
        browser_navigator: PacerNavigator = None,
        docket_processor: SingleDocketProcessor = None,
        config_service: ConfigurationService = None,
    ):
        super().__init__(logger, config)
        self.browser_navigator = browser_navigator
        self.docket_processor = docket_processor
        self.config_service = config_service
        self.run_parallel = self.config.get("run_parallel", False)

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a high-level orchestration action.
        """
        action = data.get("action")
        if action == "run_main_workflow":
            return await self.run_main_workflow()
        else:
            raise ValueError(f"Unknown action for PacerOrchestratorService: {action}")

    async def run_main_workflow(self) -> Dict[str, Any]:
        """
        Runs the main PACER processing workflow.
        """
        self.log_info("Starting main PACER workflow.")
        
        # Load configuration
        config_data = await self.config_service.execute({"action": "get_all_configs"})
        courts_to_process = config_data.get("courts_to_process", [])

        if not courts_to_process:
            self.log_warning("No courts to process.")
            return {"status": "no_courts", "results": {}}

        # Main processing logic
        if self.run_parallel:
            results = await self._process_courts_parallel(courts_to_process, config_data)
        else:
            results = await self._process_courts_sequential(courts_to_process, config_data)

        self.log_info("Main PACER workflow finished.")
        return {"status": "complete", "results": results}

    async def _process_courts_parallel(self, courts: List[str], config_data: Dict) -> Dict:
        """Processes multiple courts in parallel, with isolated browser contexts."""
        self.log_info(f"Processing {len(courts)} courts in parallel.")
        tasks = []
        for court_id in courts:
            tasks.append(self._process_single_court_isolated(court_id, config_data))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        processed_results = {}
        for i, res in enumerate(results):
            court_id = courts[i]
            if isinstance(res, Exception):
                self.log_error(f"Error processing court {court_id}: {res}", exc_info=res)
                processed_results[court_id] = {"status": "failed", "error": str(res)}
            else:
                processed_results[court_id] = res

        return processed_results

    async def _process_courts_sequential(self, courts: List[str], config_data: Dict) -> Dict:
        """Processes multiple courts sequentially in a single browser context."""
        self.log_info(f"Processing {len(courts)} courts sequentially.")
        results = {}
        async with self.browser_facade as browser:
            context = await browser.get_new_context()
            try:
                for court_id in courts:
                    self.log_info(f"Processing court: {court_id}")
                    workflow_config = self._create_workflow_config(court_id, config_data, context)
                    result = await self.court_facade.execute(workflow_config)
                    results[court_id] = result
            finally:
                try:
                    if context:
                        await context.close()
                except Exception as e:
                    self.log_warning(f"Error closing context: {e}")
        return results

    async def _process_single_court_isolated(self, court_id: str, config_data: Dict) -> Dict:
        """Processes a single court in its own isolated browser context."""
        self.log_info(f"Creating isolated context for court: {court_id}")
        async with self.browser_facade as browser:
            # Create a unique download path for this court task
            download_path = await self.court_facade.execute({
                "action": "setup_download_path",
                "court_id": court_id,
                "iso_date": config_data.get("iso_date"),
                "mode": "report"
            })

            context = await browser.get_new_context(download_path=download_path)
            try:
                workflow_config = self._create_workflow_config(court_id, config_data, context)
                return await self.court_facade.execute(workflow_config)
            finally:
                try:
                    if context:
                        await context.close()
                except Exception as e:
                    self.log_warning(f"Error closing context: {e}")

    async def process_courts(self, court_ids: List[str], context=None, iso_date: str = None, 
                           start_date=None, end_date=None, docket_list_input=None) -> Dict[str, Any]:
        """
        Public method to process courts - called by ScrapingOrchestrator.
        
        Args:
            court_ids: List of court IDs to process
            context: Browser context (optional, can be None)
            iso_date: ISO date string
            start_date: Start date for processing
            end_date: End date for processing
            docket_list_input: Optional list of specific dockets to process
        
        Returns:
            Dictionary containing processing results
        """
        self.log_info(f"process_courts called with {len(court_ids)} courts")
        
        # Build config data from parameters
        config_data = {
            "iso_date": iso_date,
            "start_date": start_date,
            "end_date": end_date,
            "courts_to_process": court_ids,
            "docket_list_input": docket_list_input
        }
        
        # If specific docket list provided, handle that workflow
        if docket_list_input and isinstance(docket_list_input, list):
            self.log_info(f"Processing specific docket list with {len(docket_list_input)} dockets")
            # For now, delegate to the main workflow - this can be enhanced later
            return await self.run_main_workflow()
        
        # Process the courts using existing workflow
        if self.run_parallel and len(court_ids) > 1:
            return await self._process_courts_parallel(court_ids, config_data)
        else:
            return await self._process_courts_sequential(court_ids, config_data)

    def _create_workflow_config(self, court_id: str, config_data: Dict, context) -> Dict:
        """Creates the workflow configuration dictionary for a court processing task."""
        from datetime import datetime
        
        # Parse date strings to datetime objects
        start_date_str = config_data.get("start_date")
        end_date_str = config_data.get("end_date")  # This comes from 'date' in config
        
        start_date_obj = None
        end_date_obj = None
        
        if start_date_str:
            try:
                # Handle both string and datetime object inputs
                if isinstance(start_date_str, datetime):
                    start_date_obj = start_date_str
                else:
                    start_date_obj = datetime.strptime(start_date_str, '%m/%d/%y')
            except (ValueError, TypeError) as e:
                self.log_error(f"Failed to parse start_date '{start_date_str}': {e}")
                
        if end_date_str:
            try:
                # Handle both string and datetime object inputs
                if isinstance(end_date_str, datetime):
                    end_date_obj = end_date_str
                else:
                    end_date_obj = datetime.strptime(end_date_str, '%m/%d/%y')
            except (ValueError, TypeError) as e:
                self.log_error(f"Failed to parse end_date '{end_date_str}': {e}")
        
        return {
            "action": "handle_court_workflow",
            "processing_mode": "date_range",
            "court_id": court_id,
            "context": context,
            "iso_date": config_data.get("iso_date"),
            "start_date_obj": start_date_obj,  # Pass as datetime objects
            "end_date_obj": end_date_obj,      # Pass as datetime objects
            "workflow_config": {
                "iso_date": config_data.get("iso_date"),
                "start_date": start_date_str,     # Keep original strings too
                "end_date": end_date_str,
                # Other configs can be added here
            }
        }
