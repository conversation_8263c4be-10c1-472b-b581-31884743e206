# /src/services/pacer/_core_services/docket_processing_orchestrator.py

"""
Docket Processing Orchestrator - Core Services Implementation

This orchestrator implements the exact workflow from docket_processing.md using 
the 9 core services, eliminating all facade service dependencies.

PHASE 1: HTML Content Processing -> Case Processing Service
PHASE 2: Relevance & Classification -> Relevance Service  
PHASE 3: Case Verification -> Verification Service
PHASE 4: Download Workflow -> Download Orchestration Service
FINAL_SAVE: File Operations -> File Operations Service
"""

from __future__ import annotations
from typing import Any, Dict, Optional, TYPE_CHECKING
from datetime import datetime

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol
    from playwright.async_api import Page
    from src.pacer._core_services.configuration.configuration_service import ConfigurationService
    from src.pacer._core_services.browser.browser_service import BrowserService
    from src.pacer._core_services.case_processing.case_processing_service import CaseProcessingService
    from src.pacer._core_services.relevance.relevance_service import RelevanceService
    from src.pacer._core_services.verification.verification_service import VerificationService
    from src.pacer._core_services.download_orchestration.download_orchestration_service import DownloadOrchestrationService
    from src.pacer._core_services.file_operations.file_operations_service import FileOperationsService
    from src.pacer._core_services.metrics_reporting.metrics_reporting_service import MetricsReportingService
    from src.pacer._core_services.s3_management.s3_management_service import S3ManagementService


class DocketProcessingOrchestrator(AsyncServiceBase):
    """
    Core service-based docket processing orchestrator.
    
    Implements the complete docket processing lifecycle using the 9 core services:
    1. Configuration Service - Configuration management
    2. Browser Service - Browser management, authentication, navigation
    3. Case Processing Service - PHASE 1: HTML Content Processing  
    4. Relevance Service - PHASE 2: Relevance & Classification
    5. Verification Service - PHASE 3: Case Verification
    6. Download Orchestration Service - PHASE 4: Download Workflow
    7. File Operations Service - FINAL_SAVE: File Operations
    8. Metrics Reporting Service - Performance tracking
    9. S3 Management Service - Cloud storage operations
    """

    def __init__(self,
                 configuration_service: ConfigurationService,
                 browser_service: BrowserService,
                 case_processing_service: CaseProcessingService,
                 relevance_service: RelevanceService,
                 verification_service: VerificationService,
                 download_orchestration_service: DownloadOrchestrationService,
                 file_operations_service: FileOperationsService,
                 metrics_reporting_service: MetricsReportingService,
                 s3_management_service: S3ManagementService,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        
        super().__init__(logger, config)
        
        # Initialize all 9 core services
        self._configuration_service = configuration_service
        self._browser_service = browser_service
        self._case_processing_service = case_processing_service
        self._relevance_service = relevance_service
        self._verification_service = verification_service
        self._download_orchestration_service = download_orchestration_service
        self._file_operations_service = file_operations_service
        self._metrics_reporting_service = metrics_reporting_service
        self._s3_management_service = s3_management_service

    async def _initialize_service(self) -> None:
        """Initialize the docket processing orchestrator and all 9 services."""
        self.log_info("Initializing Docket Processing Orchestrator with 9 core services")
        
        # Initialize all 9 services in dependency order
        services_to_initialize = [
            ("Configuration", self._configuration_service),
            ("Browser", self._browser_service),
            ("Case Processing", self._case_processing_service),
            ("Relevance", self._relevance_service),
            ("Verification", self._verification_service),
            ("Download Orchestration", self._download_orchestration_service),
            ("File Operations", self._file_operations_service),
            ("Metrics Reporting", self._metrics_reporting_service),
            ("S3 Management", self._s3_management_service)
        ]
        
        for service_name, service in services_to_initialize:
            try:
                await service.initialize()
                self.log_info(f"{service_name} Service initialized successfully")
            except Exception as e:
                self.log_error(f"Failed to initialize {service_name} Service: {str(e)}", exc_info=True)
                raise PacerServiceError(f"Service initialization failed: {service_name}")
        
        self.log_info("Docket Processing Orchestrator initialized with all 9 core services")

    def _get_logger_context(self, court_id: str = "", docket_num: str = "") -> Dict[str, Any]:
        """Get logger context with consistent [court_id][docket_num] prefix format."""
        context = {"service": "DocketProcessingOrchestrator"}
        if court_id or docket_num:
            context["case_prefix"] = f"[{court_id}][{docket_num}]"
        return context

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        
        if action == 'process_docket_page':
            return await self.process_docket_page(
                data['page'],
                data['initial_details'],
                data.get('is_explicitly_requested', False)
            )
        elif action == 'health_check':
            return await self.health_check()
        else:
            raise PacerServiceError(f"Unknown action for DocketProcessingOrchestrator: {action}")

    async def process_docket_page(self, 
                                page: Page,
                                initial_details: Dict[str, Any],
                                is_explicitly_requested: bool = False) -> Optional[Dict[str, Any]]:
        """
        Complete docket processing lifecycle using only the 8 core services.
        
        Implements the exact workflow from docket_processing.md:
        - PHASE 1: HTML Content Processing
        - PHASE 2: Relevance & Classification  
        - PHASE 3: Case Verification
        - PHASE 4: Download Workflow
        - FINAL_SAVE: File Operations
        
        Args:
            page: Playwright page object
            initial_details: Initial case details from civil report
            is_explicitly_requested: Whether this is an explicit request or report-scraped
            
        Returns:
            Final processed case details or None if processing fails
        """
        court_id = initial_details.get('court_id', 'unknown')
        docket_num = initial_details.get('docket_num', 'unknown')
        context = self._get_logger_context(court_id, docket_num)
        
        # Start processing timer
        processing_start_time = datetime.now()
        operation_id = f"{court_id}_{docket_num}_{processing_start_time.strftime('%Y%m%d_%H%M%S')}"
        
        await self._metrics_reporting_service.execute({
            "action": "record_processing_start",
            "operation_id": operation_id,
            "operation_type": "docket_processing",
            "metadata": {
                "court_id": court_id,
                "docket_num": docket_num,
                "is_explicitly_requested": is_explicitly_requested
            }
        })
        
        try:
            self.log_info("=== Starting Single Docket Processing Lifecycle ===", context)
            
            # =================================================================
            # PHASE 1: HTML Content Processing
            # =================================================================
            self.log_info("PHASE 1: HTML Content Processing", context)
            
            # P1_1: Case Processing Service.process_case_html
            case_details = await self._case_processing_service.execute({
                "action": "process_case",
                "page": page,
                "initial_details": initial_details
            })
            
            # P1_4: HTML Valid?
            if not case_details:
                # P1_FAIL: Return None - Invalid HTML
                self.log_error("P1_FAIL: Return None - Invalid HTML", context)
                await self._record_processing_failure(operation_id, "invalid_html")
                return None
            
            # Check for special conditions
            if case_details.get("_no_proceedings_detected"):
                self.log_info("P1: No proceedings found - case will not be saved", context)
                await self._record_processing_failure(operation_id, "no_proceedings_detected")
                return None
            
            # =================================================================
            # PHASE 2: Relevance & Classification  
            # =================================================================
            self.log_info("PHASE 2: Relevance & Classification", context)
            
            # P2_1: Relevance Service.determine_case_relevance
            relevance_result = await self._relevance_service.execute({
                "action": "determine_case_relevance",
                "case_details": case_details,
                "court_id": court_id,
                "docket_num": docket_num
            })
            
            # Add relevance information to case details
            case_details.update(relevance_result)
            
            # Continue to classification regardless of relevance (as per workflow)
            # P2_CLASSIFY: Classification Service.classify_case (via Case Processing Service)
            classification_result = await self._case_processing_service.execute({
                "action": "classify_case",
                "case_details": case_details
            })
            case_details.update(classification_result)
            
            # =================================================================
            # PHASE 3: Case Verification
            # =================================================================
            self.log_info("PHASE 3: Case Verification", context)
            
            # P3_1: Verification Service.verify_case
            verification_result = await self._verification_service.execute({
                "action": "verify_case",
                "case_details": case_details,
                "is_explicitly_requested": is_explicitly_requested,
                "court_id": court_id,
                "docket_num": docket_num
            })
            
            if not verification_result.get('should_process', True):
                # P3_SKIP: Save Metadata Only - Skip Download
                self.log_info(f"P3_SKIP: Save Metadata Only - {verification_result['skip_reason']}", context)
                case_details['_skip_reason'] = verification_result['skip_reason']
                case_details['_verification_skipped'] = True
                
                # Save only metadata without download
                final_case_details = await self._save_case_data_only(case_details, court_id)
                await self._record_processing_success(operation_id, processing_start_time, "metadata_only")
                return final_case_details
            
            # =================================================================
            # PHASE 4: Download Workflow
            # =================================================================
            self.log_info("PHASE 4: Download Workflow", context)
            
            # P4_1: Download Orchestration Service.process_download_workflow
            case_details = await self._download_orchestration_service.execute({
                "action": "process_download_workflow",
                "case_details": case_details,
                "is_explicitly_requested": is_explicitly_requested,
                "page": page,
                "court_id": court_id,
                "docket_num": docket_num
            })
            
            # =================================================================
            # FINAL_SAVE: File Operations Service.save_and_upload_case_data
            # =================================================================
            self.log_info("FINAL_SAVE: File Operations Service.save_and_upload_case_data", context)
            
            iso_date = self.config.get('iso_date', datetime.now().strftime('%Y-%m-%d'))
            save_result = await self._file_operations_service.execute({
                "action": "save_and_upload_case_data",
                "case_data": case_details,
                "court_id": court_id,
                "iso_date": iso_date,
                "upload_enabled": self.config.get('s3_upload_enabled', True)
            })
            
            # Add save results to case details
            case_details['_save_result'] = save_result
            
            # Determine final status
            if save_result.get('local_success', False):
                if save_result.get('upload_success', False):
                    self.log_info("SUCCESS: Complete Success", context)
                    await self._record_processing_success(operation_id, processing_start_time, "complete_success")
                else:
                    self.log_warning("PARTIAL: Local Success + Upload Error", context) 
                    await self._record_processing_success(operation_id, processing_start_time, "partial_success")
            else:
                self.log_error("FAIL: Save operation failed", context)
                await self._record_processing_failure(operation_id, "save_failed")
                return None
            
            self.log_info("=== Single Docket Processing Lifecycle Completed ===", context)
            return case_details
            
        except Exception as e:
            self.log_error(f"Docket processing failed: {str(e)}", exc_info=True)
            await self._record_processing_failure(operation_id, f"exception: {str(e)}")
            return None

    async def _save_case_data_only(self, case_details: Dict[str, Any], court_id: str) -> Dict[str, Any]:
        """Save case metadata only without download."""
        try:
            iso_date = self.config.get('iso_date', datetime.now().strftime('%Y-%m-%d'))
            save_result = await self._file_operations_service.execute({
                "action": "save_and_upload_case_data",
                "case_data": case_details,
                "court_id": court_id,
                "iso_date": iso_date,
                "upload_enabled": self.config.get('s3_upload_enabled', True)
            })
            
            case_details['_save_result'] = save_result
            return case_details
            
        except Exception as e:
            self.log_error(f"Failed to save case metadata: {str(e)}", exc_info=True)
            case_details['_save_error'] = str(e)
            return case_details

    async def _record_processing_success(self, 
                                       operation_id: str, 
                                       start_time: datetime, 
                                       success_type: str) -> None:
        """Record successful processing metrics."""
        processing_time = (datetime.now() - start_time).total_seconds()
        
        await self._metrics_reporting_service.execute({
            "action": "record_case_processed",
            "success": True,
            "processing_time": processing_time,
            "metadata": {
                "success_type": success_type,
                "operation_id": operation_id
            }
        })

    async def _record_processing_failure(self, operation_id: str, failure_reason: str) -> None:
        """Record failed processing metrics."""
        await self._metrics_reporting_service.execute({
            "action": "record_case_processed", 
            "success": False,
            "processing_time": 0.0,
            "metadata": {
                "failure_reason": failure_reason,
                "operation_id": operation_id
            }
        })

    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check of orchestrator and all 8 core services."""
        health_status = {
            "service": "DocketProcessingOrchestrator",
            "status": "healthy" if self._initialized else "unhealthy",
            "core_services_health": {}
        }
        
        # Check all 8 core services
        services_to_check = [
            ("configuration", self._configuration_service),
            ("case_processing", self._case_processing_service),
            ("relevance", self._relevance_service),
            ("verification", self._verification_service),
            ("download_orchestration", self._download_orchestration_service),
            ("file_operations", self._file_operations_service),
            ("metrics_reporting", self._metrics_reporting_service),
            ("s3_management", self._s3_management_service)
        ]
        
        all_healthy = True
        for service_name, service in services_to_check:
            try:
                service_health = await service.health_check()
                health_status["core_services_health"][service_name] = service_health
                
                if service_health.get("status") != "healthy":
                    all_healthy = False
                    
            except Exception as e:
                health_status["core_services_health"][service_name] = {
                    "status": "error",
                    "error": str(e)
                }
                all_healthy = False
        
        health_status["overall_status"] = "healthy" if all_healthy else "degraded"
        return health_status

    async def _cleanup_service(self) -> None:
        """Clean up orchestrator and all core services."""
        self.log_info("Cleaning up Docket Processing Orchestrator and all core services")
        
        # Clean up all services
        services_to_cleanup = [
            self._metrics_reporting_service,
            self._s3_management_service, 
            self._file_operations_service,
            self._download_orchestration_service,
            self._verification_service,
            self._relevance_service,
            self._case_processing_service,
            self._configuration_service
        ]
        
        for service in services_to_cleanup:
            try:
                if hasattr(service, 'cleanup'):
                    await service.cleanup()
            except Exception as e:
                self.log_warning(f"Error cleaning up service: {str(e)}")
        
        self.log_info("Docket Processing Orchestrator cleanup completed")