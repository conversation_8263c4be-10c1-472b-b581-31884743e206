# /src/services/pacer/_core_services/case_processing/case_processing_service.py

"""
Core Case Processing Service - Consolidated facade for all case processing operations.

This service consolidates the functionality of:
- CaseProcessingFacadeService
- CaseClassificationFacadeService  
- HtmlProcessingFacadeService
- RowProcessingFacadeService
- TransferFacadeService
- CourtProcessingFacadeService
"""

from __future__ import annotations
from typing import Any, Dict, List, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.pacer._core_services.shared.base_interfaces import (
    CaseProcessingServiceInterface,
    CaseDetails,
    ProcessingResult
)

if TYPE_CHECKING:
    from playwright.async_api import Page
    from src.infrastructure.protocols.logger import LoggerProtocol
    from src.pacer._case_processing_components.case_validator import CaseValidator
    from src.pacer._case_processing_components.case_parser import CaseParser
    from src.pacer._case_processing_components.case_enricher import <PERSON>E<PERSON><PERSON>er
    from src.pacer._case_processing_components.case_transformer import CaseTransformer
    from src.pacer._classification_components.case_classifier import CaseClassifier
    from src.pacer._case_processing_components.html_parser import HtmlParser
    from src.pacer._transfer_components.transfer_processor import TransferProcessor


class CaseProcessingService(AsyncServiceBase, CaseProcessingServiceInterface):
    """
    Core service for all PACER case processing operations.
    
    Provides a unified interface for:
    - Case validation and processing
    - HTML parsing and content extraction
    - Case classification and categorization
    - Data enrichment and transformation
    - Transfer case handling
    - Row data processing
    """

    def __init__(self,
                 case_validator: CaseValidator,
                 case_parser: CaseParser,
                 case_enricher: CaseEnricher,
                 case_transformer: CaseTransformer,
                 case_classifier: Optional[CaseClassifier] = None,
                 html_parser: Optional[HtmlParser] = None,
                 transfer_processor: Optional[TransferProcessor] = None,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        
        # Core processing components
        self._validator = case_validator
        self._parser = case_parser
        self._enricher = case_enricher
        self._transformer = case_transformer
        
        # Optional specialized components
        self._classifier = case_classifier
        self._html_parser = html_parser
        self._transfer_processor = transfer_processor
        
        # Processing statistics
        self._stats = {
            "cases_processed": 0,
            "cases_classified": 0,
            "validation_failures": 0,
            "parsing_errors": 0
        }

    async def _initialize_service(self) -> None:
        """Initialize the case processing service and all components."""
        self.log_info("Initializing Case Processing Service")
        
        # Initialize all components
        components = [
            self._validator,
            self._parser, 
            self._enricher,
            self._transformer
        ]
        
        # Initialize optional components if available
        if self._classifier:
            components.append(self._classifier)
        if self._html_parser:
            components.append(self._html_parser)
        if self._transfer_processor:
            components.append(self._transfer_processor)
        
        for component in components:
            if hasattr(component, 'initialize'):
                await component.initialize()
        
        self.log_info("Case Processing Service initialized successfully", {
            "components_initialized": len(components),
            "classifier_available": self._classifier is not None,
            "html_parser_available": self._html_parser is not None,
            "transfer_processor_available": self._transfer_processor is not None
        })

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        
        if action == 'process_case':
            return await self.process_case(data['page'], data['initial_details'])
        elif action == 'classify_case':
            return await self.classify_case(data['case_details'])
        elif action == 'parse_html_content':
            return await self.parse_html_content(data['html_content'])
        elif action == 'process_transfer_case':
            return await self.process_transfer_case(data['case_details'])
        elif action == 'process_row_data':
            return await self.process_row_data(data['row_data'])
        elif action == 'update_case_details':
            return await self.update_case_details(data['html_content'], data['initial_details'])
        elif action == 'create_base_filename':
            return await self.create_base_filename(data['case_details'])
        elif action == 'enrich_case_data':
            return await self.enrich_case_data(data['case_details'], data.get('html_content', ''))
        elif action == 'validate_page_content':
            return await self.validate_page_content(data['page'], data['case_details'])
        elif action == 'get_processing_stats':
            return self.get_processing_stats()
        else:
            raise PacerServiceError(f"Unknown action for CaseProcessingService: {action}")

    async def process_case(self, page: Page, initial_details: CaseDetails) -> Optional[CaseDetails]:
        """
        Process a complete case workflow including validation, parsing, enrichment, and transformation.
        """
        log_prefix = f"[{initial_details.get('court_id', 'N/A')}][{initial_details.get('docket_num', 'N/A')}]"
        self.log_info(f"{log_prefix} Starting comprehensive case processing workflow")

        try:
            # 1. Validate page content
            html_content = await self._validator.validate_page_content(page, initial_details)
            if not html_content:
                self.log_error(f"{log_prefix} Case validation failed - aborting processing")
                self._stats["validation_failures"] += 1
                return None

            # 2. Parse HTML to extract case details
            parsed_details = await self._parser.execute({
                "action": "parse_html",
                "html_content": html_content
            })

            # Check for special parsing conditions
            if parsed_details.get("_no_proceedings_detected"):
                self.log_info(f"{log_prefix} No proceedings found - case will not be saved")
                return None

            # 3. Merge initial details with parsed details
            merged_details = await self._merge_case_details(initial_details, parsed_details)

            # 4. Enrich case data with additional information
            enriched_details = await self.enrich_case_data(merged_details, html_content)

            # 5. Transform case data to final format
            final_details = await self._transformer.execute({
                "action": "transform_case",
                "case_details": enriched_details
            })

            # 6. Optional: Classify case if classifier is available
            if self._classifier:
                classification_data = await self.classify_case(final_details)
                final_details.update(classification_data)
                self._stats["cases_classified"] += 1

            self._stats["cases_processed"] += 1
            self.log_info(f"{log_prefix} Case processing workflow completed successfully", {
                "final_fields_count": len(final_details),
                "has_proceedings": not parsed_details.get("_no_proceedings_detected", False)
            })

            return final_details

        except Exception as e:
            self._stats["parsing_errors"] += 1
            self.log_error(f"{log_prefix} Case processing failed: {str(e)}", exc_info=True)
            raise PacerServiceError(f"Case processing failed for {log_prefix}: {str(e)}")

    async def classify_case(self, case_details: CaseDetails) -> Dict[str, Any]:
        """Classify case using available classification components."""
        if not self._classifier:
            self.log_warning("Case classifier not available - returning empty classification")
            return {}

        try:
            classification_result = await self._classifier.execute({
                "action": "classify_case",
                "case_details": case_details
            })
            
            self.log_debug("Case classified successfully", {
                "docket_num": case_details.get('docket_num'),
                "classification_keys": list(classification_result.keys())
            })
            
            return classification_result
            
        except Exception as e:
            self.log_error(f"Case classification failed: {str(e)}", exc_info=True)
            return {"classification_error": str(e)}

    async def parse_html_content(self, html_content: str) -> Dict[str, Any]:
        """Parse HTML content to extract case information."""
        try:
            if self._html_parser:
                # Use specialized HTML parser if available
                parsed_data = await self._html_parser.execute({
                    "action": "parse_html",
                    "html_content": html_content
                })
            else:
                # Fall back to standard case parser
                parsed_data = await self._parser.execute({
                    "action": "parse_html", 
                    "html_content": html_content
                })
            
            self.log_debug("HTML content parsed successfully", {
                "extracted_fields": list(parsed_data.keys()),
                "content_length": len(html_content)
            })
            
            return parsed_data
            
        except Exception as e:
            self.log_error(f"HTML parsing failed: {str(e)}", exc_info=True)
            raise PacerServiceError(f"HTML parsing failed: {str(e)}")

    async def process_transfer_case(self, case_details: CaseDetails) -> CaseDetails:
        """Process transfer case with specialized logic."""
        if not self._transfer_processor:
            self.log_warning("Transfer processor not available - processing as regular case")
            return case_details

        try:
            processed_details = await self._transfer_processor.execute({
                "action": "process_transfer",
                "case_details": case_details
            })
            
            self.log_info("Transfer case processed successfully", {
                "docket_num": case_details.get('docket_num'),
                "court_id": case_details.get('court_id')
            })
            
            return processed_details
            
        except Exception as e:
            self.log_error(f"Transfer case processing failed: {str(e)}", exc_info=True)
            raise PacerServiceError(f"Transfer case processing failed: {str(e)}")

    async def process_row_data(self, row_data: Dict[str, Any]) -> CaseDetails:
        """Process row data from civil reports or similar sources."""
        try:
            # Use case parser to process row data
            processed_data = await self._parser.execute({
                "action": "parse_row_data",
                "row_data": row_data
            })
            
            # Enrich the processed row data
            enriched_data = await self.enrich_case_data(processed_data)
            
            # Transform to final format
            final_data = await self._transformer.execute({
                "action": "transform_case",
                "case_details": enriched_data
            })
            
            self.log_debug("Row data processed successfully", {
                "initial_fields": len(row_data),
                "final_fields": len(final_data)
            })
            
            return final_data
            
        except Exception as e:
            self.log_error(f"Row data processing failed: {str(e)}", exc_info=True)
            raise PacerServiceError(f"Row data processing failed: {str(e)}")

    async def update_case_details(self, html_content: str, initial_details: CaseDetails) -> CaseDetails:
        """
        Update case details by parsing HTML content and merging with initial details.
        Preserves initial details to avoid parsing UI elements.
        """
        log_prefix = f"[{initial_details.get('court_id', 'N/A')}][{initial_details.get('docket_num', 'N/A')}]"
        
        try:
            # Parse HTML content
            parsed_details = await self.parse_html_content(html_content)
            
            # Check for special conditions
            if parsed_details.get("_no_proceedings_detected"):
                self.log_info(f"{log_prefix} No proceedings found in HTML content")
                return {**initial_details, "_no_proceedings_detected": True}
                
            if parsed_details.get("_transaction_receipt_detected"):
                self.log_warning(f"{log_prefix} Transaction receipt detected instead of case details")
                return {**initial_details, "_transaction_receipt_detected": True}
            
            # Merge details preserving initial data priority
            merged_details = await self._merge_case_details(initial_details, parsed_details)
            
            # Enrich merged details
            enriched_details = await self.enrich_case_data(merged_details, html_content)
            
            # Transform to final format
            final_details = await self._transformer.execute({
                "action": "transform_case",
                "case_details": enriched_details
            })
            
            self.log_info(f"{log_prefix} Case details updated successfully")
            return final_details
            
        except Exception as e:
            self.log_error(f"{log_prefix} Case details update failed: {str(e)}", exc_info=True)
            raise PacerServiceError(f"Case details update failed: {str(e)}")

    async def enrich_case_data(self, case_details: CaseDetails, html_content: str = "") -> CaseDetails:
        """Enrich case data with additional information and processing."""
        try:
            enriched_details = await self._enricher.execute({
                "action": "enrich_case",
                "case_details": case_details,
                "html_content": html_content
            })
            
            self.log_debug("Case data enriched successfully", {
                "docket_num": case_details.get('docket_num'),
                "enrichment_added": len(enriched_details) - len(case_details)
            })
            
            return enriched_details
            
        except Exception as e:
            self.log_error(f"Case data enrichment failed: {str(e)}", exc_info=True)
            # Return original details if enrichment fails
            return case_details

    async def create_base_filename(self, case_details: CaseDetails) -> str:
        """Create standardized base filename for case."""
        try:
            transformed_details = await self._transformer.execute({
                "action": "transform_case",
                "case_details": case_details
            })
            
            filename = transformed_details.get('base_filename', 'unknown_case')
            
            self.log_debug("Base filename created", {
                "filename": filename,
                "docket_num": case_details.get('docket_num')
            })
            
            return filename
            
        except Exception as e:
            self.log_error(f"Base filename creation failed: {str(e)}", exc_info=True)
            # Generate fallback filename
            court_id = case_details.get('court_id', 'unknown')
            docket_num = case_details.get('docket_num', 'unknown')
            return f"{court_id}_{docket_num}".replace(':', '_').replace('/', '_')

    async def validate_page_content(self, page: Page, case_details: CaseDetails) -> Optional[str]:
        """Validate page content and return HTML if valid."""
        try:
            html_content = await self._validator.validate_page_content(page, case_details)
            return html_content
        except Exception as e:
            self.log_error(f"Page content validation failed: {str(e)}", exc_info=True)
            return None

    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics for monitoring."""
        return {
            **self._stats,
            "service_status": "operational" if self._initialized else "not_initialized",
            "components_available": {
                "classifier": self._classifier is not None,
                "html_parser": self._html_parser is not None,
                "transfer_processor": self._transfer_processor is not None
            }
        }

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the service and components."""
        health_status = {
            "service": "CaseProcessingService",
            "status": "healthy" if self._initialized else "unhealthy",
            "components": {},
            "stats": self.get_processing_stats()
        }
        
        # Check core components
        core_components = {
            "validator": self._validator,
            "parser": self._parser,
            "enricher": self._enricher,
            "transformer": self._transformer
        }
        
        for name, component in core_components.items():
            try:
                if hasattr(component, 'health_check'):
                    health_status["components"][name] = await component.health_check()
                else:
                    health_status["components"][name] = {"status": "available"}
            except Exception as e:
                health_status["components"][name] = {"status": "error", "error": str(e)}
        
        return health_status

    async def _merge_case_details(self, initial_details: CaseDetails, parsed_details: Dict[str, Any]) -> CaseDetails:
        """
        Merge initial case details with parsed details, preserving initial data priority.
        """
        # Start with initial details (trusted from civil report)
        merged_details = {**initial_details}
        
        # Add parsed case_info fields if available
        if parsed_details.get('case_info'):
            for key, value in parsed_details['case_info'].items():
                if key not in merged_details:
                    merged_details[key] = value
        
        # Add parsed fields only if they don't exist or are empty in initial details
        important_fields = [
            'plaintiffs', 'defendants', 'attorney', 'plaintiff', 'defendant',
            'court_name', 'office', 'assigned_to', 'referred_to', 'lead_case',
            'case_in_other_court', 'jury_demand', 'jurisdiction', 'cause', 'nos',
            'date_filed', 'date_terminated', 'demand', 'nature_of_suit'
        ]
        
        for field in important_fields:
            if field in parsed_details:
                if field in ['plaintiffs', 'defendants', 'attorney', 'plaintiff', 'defendant']:
                    # Only override if initial details don't have this field or it's empty
                    if field not in merged_details or not merged_details.get(field):
                        merged_details[field] = parsed_details[field]
                else:
                    # Add other fields if not present in initial details
                    if field not in merged_details:
                        merged_details[field] = parsed_details[field]
        
        return merged_details

    async def _cleanup_service(self) -> None:
        """Clean up service resources."""
        self.log_info("Cleaning up Case Processing Service")
        
        # Reset statistics
        self._stats = {
            "cases_processed": 0,
            "cases_classified": 0,
            "validation_failures": 0,
            "parsing_errors": 0
        }