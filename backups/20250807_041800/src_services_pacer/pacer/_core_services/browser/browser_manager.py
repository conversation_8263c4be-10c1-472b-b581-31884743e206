# /src/services/pacer/_core_services/browser/browser_manager.py

"""
Browser Manager for Browser Service.

Consolidates browser management components (playwright_manager, context_factory, navigator)
into a unified browser management module within the Browser Service.
"""

from __future__ import annotations
import asyncio
from typing import Any, Dict, List, Optional, TYPE_CHECKING
from datetime import datetime

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError, BrowserError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol
    from playwright.async_api import <PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page, Playwright


class BrowserManager(ComponentImplementation):
    """
    Manages browser lifecycle and operations for the Browser Service.
    
    Consolidates functionality from:
    - PlaywrightManager: Core Playwright browser management
    - ContextFactory: Browser context creation and configuration
    - Navigator: Page navigation operations
    """
    
    # Browser configuration defaults
    DEFAULT_TIMEOUT = 30000
    DEFAULT_NAVIGATION_TIMEOUT = 30000
    DEFAULT_VIEWPORT = {'width': 1920, 'height': 1080}
    DEFAULT_USER_AGENT = None
    
    # Browser launch arguments
    BROWSER_ARGS = [
        '--disable-blink-features=AutomationControlled',
        '--disable-dev-shm-usage',
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-web-security',
        '--disable-features=IsolateOrigins,site-per-process'
    ]
    
    def __init__(self, logger: Optional[LoggerProtocol] = None, config: Optional[Dict] = None):
        """
        Initialize the Browser Manager.
        
        Args:
            logger: Logger instance for structured logging
            config: Configuration dictionary
        """
        super().__init__(logger, config)
        
        # Playwright and browser instances
        self._playwright: Optional[Playwright] = None
        self._browser: Optional[Browser] = None
        
        # Context and page management
        self._contexts: Dict[str, BrowserContext] = {}
        self._pages: Dict[str, Page] = {}
        self._page_to_context: Dict[str, str] = {}
        
        # Configuration
        self._browser_type = self.config.get('browser_type', 'chromium')
        self._headless = self.config.get('headless', True)
        self._timeout = self.config.get('timeout', self.DEFAULT_TIMEOUT)
        self._navigation_timeout = self.config.get('navigation_timeout', self.DEFAULT_NAVIGATION_TIMEOUT)
        self._viewport = self.config.get('viewport', self.DEFAULT_VIEWPORT)
        self._user_agent = self.config.get('user_agent', self.DEFAULT_USER_AGENT)
        
        # State tracking
        self._initialized = False
        self._shutdown_requested = False

    async def _execute_action(self, data: Any) -> Any:
        """Execute browser management actions."""
        action = data.get('action')
        
        action_map = {
            # Lifecycle management
            'initialize': self.initialize_browser,
            'shutdown': self.shutdown_browser,
            'restart': self.restart_browser,
            'check_health': self.check_health,
            
            # Context management
            'create_context': self.create_context,
            'get_context': self.get_context,
            'close_context': self.close_context,
            'list_contexts': self.list_contexts,
            
            # Page management
            'create_page': self.create_page,
            'get_page': self.get_page,
            'close_page': self.close_page,
            'list_pages': self.list_pages,
            
            # Navigation operations
            'navigate': self.navigate,
            'reload': self.reload_page,
            'go_back': self.go_back,
            'go_forward': self.go_forward,
            'wait_for_selector': self.wait_for_selector,
            'wait_for_navigation': self.wait_for_navigation,
            
            # Interaction operations
            'click': self.click,
            'fill': self.fill,
            'select_option': self.select_option,
            'check': self.check,
            'uncheck': self.uncheck,
            'hover': self.hover,
            
            # Content operations
            'get_content': self.get_content,
            'get_text': self.get_text,
            'get_attribute': self.get_attribute,
            'screenshot': self.screenshot,
            'pdf': self.pdf
        }
        
        handler = action_map.get(action)
        if not handler:
            raise PacerServiceError(f"Unknown action for BrowserManager: {action}")
        
        return await handler(data)

    async def initialize_browser(self, data: Dict[str, Any] = None) -> bool:
        """
        Initialize Playwright and launch browser.
        
        Returns:
            Initialization success status
        """
        if self._initialized:
            self.log_warning("Browser already initialized")
            return True
        
        try:
            # Import and start Playwright
            from playwright.async_api import async_playwright
            
            self._playwright = await async_playwright().start()
            self.log_info("Playwright started successfully")
            
            # Select browser type
            browser_launcher = getattr(self._playwright, self._browser_type)
            
            # Launch browser
            self._browser = await browser_launcher.launch(
                headless=self._headless,
                args=self.BROWSER_ARGS
            )
            
            self._initialized = True
            self.log_info(f"Browser launched successfully (type: {self._browser_type}, headless: {self._headless})")
            
            return True
            
        except Exception as e:
            self.log_error(f"Browser initialization failed: {str(e)}", exc_info=True)
            raise BrowserError(f"Failed to initialize browser: {str(e)}")

    async def shutdown_browser(self, data: Dict[str, Any] = None) -> bool:
        """
        Shutdown browser and cleanup resources.
        
        Returns:
            Shutdown success status
        """
        self._shutdown_requested = True
        self.log_info("Browser shutdown requested")
        
        # Close all pages
        for page_id in list(self._pages.keys()):
            await self.close_page({'page_id': page_id})
        
        # Close all contexts
        for context_id in list(self._contexts.keys()):
            await self.close_context({'context_id': context_id})
        
        # Close browser
        if self._browser:
            try:
                await self._browser.close()
                self.log_info("Browser closed successfully")
            except Exception as e:
                self.log_warning(f"Error closing browser: {str(e)}")
        
        # Stop Playwright
        if self._playwright:
            try:
                await self._playwright.stop()
                self.log_info("Playwright stopped successfully")
            except Exception as e:
                self.log_warning(f"Error stopping Playwright: {str(e)}")
        
        # Reset state
        self._browser = None
        self._playwright = None
        self._initialized = False
        self._shutdown_requested = False
        
        return True

    async def restart_browser(self, data: Dict[str, Any] = None) -> bool:
        """
        Restart the browser.
        
        Returns:
            Restart success status
        """
        self.log_info("Restarting browser")
        
        # Shutdown
        await self.shutdown_browser()
        
        # Wait a moment
        await asyncio.sleep(1)
        
        # Reinitialize
        return await self.initialize_browser()

    async def check_health(self, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Check browser health status.
        
        Returns:
            Health status dictionary
        """
        health = {
            'status': 'unknown',
            'initialized': self._initialized,
            'browser_connected': False,
            'contexts': len(self._contexts),
            'pages': len(self._pages),
            'issues': []
        }
        
        if not self._initialized:
            health['status'] = 'not_initialized'
            health['issues'].append('Browser not initialized')
        elif self._shutdown_requested:
            health['status'] = 'shutting_down'
            health['issues'].append('Shutdown in progress')
        elif self._browser:
            try:
                health['browser_connected'] = self._browser.is_connected()
                if health['browser_connected']:
                    health['status'] = 'healthy'
                else:
                    health['status'] = 'disconnected'
                    health['issues'].append('Browser disconnected')
            except:
                health['status'] = 'error'
                health['issues'].append('Cannot check browser status')
        else:
            health['status'] = 'error'
            health['issues'].append('Browser instance missing')
        
        return health

    async def create_context(self, data: Dict[str, Any]) -> str:
        """
        Create a new browser context.
        
        Args:
            data: Context configuration
            
        Returns:
            Context ID
        """
        if not self._initialized:
            await self.initialize_browser()
        
        context_id = data.get('context_id', f"context_{len(self._contexts)}")
        
        if context_id in self._contexts:
            self.log_warning(f"Context {context_id} already exists")
            return context_id
        
        try:
            # Context configuration
            context_config = {
                'viewport': data.get('viewport', self._viewport),
                'user_agent': data.get('user_agent', self._user_agent),
                'ignore_https_errors': data.get('ignore_https_errors', True),
                'java_script_enabled': data.get('javascript_enabled', True),
                'bypass_csp': data.get('bypass_csp', False),
                'locale': data.get('locale', 'en-US'),
                'timezone_id': data.get('timezone', 'America/New_York')
            }
            
            # Remove None values
            context_config = {k: v for k, v in context_config.items() if v is not None}
            
            # Create context
            context = await self._browser.new_context(**context_config)
            
            # Set default timeouts
            context.set_default_timeout(self._timeout)
            context.set_default_navigation_timeout(self._navigation_timeout)
            
            self._contexts[context_id] = context
            self.log_info(f"Created browser context: {context_id}")
            
            return context_id
            
        except Exception as e:
            self.log_error(f"Failed to create context: {str(e)}")
            raise BrowserError(f"Context creation failed: {str(e)}")

    async def get_context(self, data: Dict[str, Any]) -> Optional[BrowserContext]:
        """Get an existing browser context."""
        context_id = data.get('context_id')
        return self._contexts.get(context_id)

    async def close_context(self, data: Dict[str, Any]) -> bool:
        """
        Close a browser context and its pages.
        
        Args:
            data: Contains context_id
            
        Returns:
            Close success status
        """
        context_id = data.get('context_id')
        
        if context_id not in self._contexts:
            self.log_warning(f"Context {context_id} not found")
            return False
        
        # Close all pages in this context
        pages_to_close = [
            pid for pid, cid in self._page_to_context.items() 
            if cid == context_id
        ]
        
        for page_id in pages_to_close:
            await self.close_page({'page_id': page_id})
        
        # Close context
        try:
            await self._contexts[context_id].close()
            del self._contexts[context_id]
            self.log_info(f"Closed context: {context_id}")
            return True
        except Exception as e:
            self.log_error(f"Error closing context: {str(e)}")
            return False

    async def list_contexts(self, data: Dict[str, Any] = None) -> List[str]:
        """List all active context IDs."""
        return list(self._contexts.keys())

    async def create_page(self, data: Dict[str, Any]) -> str:
        """
        Create a new page in a context.
        
        Args:
            data: Page configuration
            
        Returns:
            Page ID
        """
        context_id = data.get('context_id', 'default')
        page_id = data.get('page_id', f"page_{len(self._pages)}")
        
        # Ensure context exists
        if context_id not in self._contexts:
            await self.create_context({'context_id': context_id})
        
        context = self._contexts[context_id]
        
        try:
            # Create page
            page = await context.new_page()
            
            # Set default timeouts
            page.set_default_timeout(self._timeout)
            page.set_default_navigation_timeout(self._navigation_timeout)
            
            # Store page
            self._pages[page_id] = page
            self._page_to_context[page_id] = context_id
            
            self.log_info(f"Created page {page_id} in context {context_id}")
            return page_id
            
        except Exception as e:
            self.log_error(f"Failed to create page: {str(e)}")
            raise BrowserError(f"Page creation failed: {str(e)}")

    async def get_page(self, data: Dict[str, Any]) -> Optional[Page]:
        """Get an existing page."""
        page_id = data.get('page_id')
        return self._pages.get(page_id)

    async def close_page(self, data: Dict[str, Any]) -> bool:
        """
        Close a page.
        
        Args:
            data: Contains page_id
            
        Returns:
            Close success status
        """
        page_id = data.get('page_id')
        
        if page_id not in self._pages:
            self.log_warning(f"Page {page_id} not found")
            return False
        
        try:
            await self._pages[page_id].close()
            del self._pages[page_id]
            
            if page_id in self._page_to_context:
                del self._page_to_context[page_id]
            
            self.log_info(f"Closed page: {page_id}")
            return True
            
        except Exception as e:
            self.log_error(f"Error closing page: {str(e)}")
            return False

    async def list_pages(self, data: Dict[str, Any] = None) -> List[str]:
        """List all active page IDs."""
        return list(self._pages.keys())

    async def navigate(self, data: Dict[str, Any]) -> bool:
        """
        Navigate to a URL.
        
        Args:
            data: Contains page_id, url, and optional wait_until
            
        Returns:
            Navigation success status
        """
        page_id = data.get('page_id')
        url = data.get('url')
        wait_until = data.get('wait_until', 'networkidle')
        
        if not page_id or not url:
            raise ValueError("page_id and url required for navigation")
        
        page = self._pages.get(page_id)
        if not page:
            raise ValueError(f"Page {page_id} not found")
        
        try:
            response = await page.goto(url, wait_until=wait_until)
            
            if response and response.ok:
                self.log_info(f"Navigated to {url}")
                return True
            else:
                self.log_warning(f"Navigation response not OK: {response.status if response else 'No response'}")
                return False
                
        except Exception as e:
            self.log_error(f"Navigation error: {str(e)}")
            return False

    async def reload_page(self, data: Dict[str, Any]) -> bool:
        """Reload the current page."""
        page_id = data.get('page_id')
        page = self._pages.get(page_id)
        
        if not page:
            return False
        
        try:
            await page.reload(wait_until='networkidle')
            return True
        except Exception as e:
            self.log_error(f"Reload error: {str(e)}")
            return False

    async def go_back(self, data: Dict[str, Any]) -> bool:
        """Navigate back in history."""
        page_id = data.get('page_id')
        page = self._pages.get(page_id)
        
        if not page:
            return False
        
        try:
            await page.go_back(wait_until='networkidle')
            return True
        except:
            return False

    async def go_forward(self, data: Dict[str, Any]) -> bool:
        """Navigate forward in history."""
        page_id = data.get('page_id')
        page = self._pages.get(page_id)
        
        if not page:
            return False
        
        try:
            await page.go_forward(wait_until='networkidle')
            return True
        except:
            return False

    async def wait_for_selector(self, data: Dict[str, Any]) -> bool:
        """Wait for a selector to appear."""
        page_id = data.get('page_id')
        selector = data.get('selector')
        timeout = data.get('timeout', self._timeout)
        
        page = self._pages.get(page_id)
        if not page or not selector:
            return False
        
        try:
            await page.wait_for_selector(selector, timeout=timeout)
            return True
        except:
            return False

    async def wait_for_navigation(self, data: Dict[str, Any]) -> bool:
        """Wait for navigation to complete."""
        page_id = data.get('page_id')
        wait_until = data.get('wait_until', 'networkidle')
        
        page = self._pages.get(page_id)
        if not page:
            return False
        
        try:
            await page.wait_for_load_state(wait_until)
            return True
        except:
            return False

    async def click(self, data: Dict[str, Any]) -> bool:
        """Click an element."""
        page_id = data.get('page_id')
        selector = data.get('selector')
        
        page = self._pages.get(page_id)
        if not page or not selector:
            return False
        
        try:
            await page.click(selector)
            return True
        except Exception as e:
            self.log_warning(f"Click error: {str(e)}")
            return False

    async def fill(self, data: Dict[str, Any]) -> bool:
        """Fill an input field."""
        page_id = data.get('page_id')
        selector = data.get('selector')
        value = data.get('value', '')
        
        page = self._pages.get(page_id)
        if not page or not selector:
            return False
        
        try:
            await page.fill(selector, value)
            return True
        except Exception as e:
            self.log_warning(f"Fill error: {str(e)}")
            return False

    async def select_option(self, data: Dict[str, Any]) -> bool:
        """Select an option from a dropdown."""
        page_id = data.get('page_id')
        selector = data.get('selector')
        value = data.get('value')
        
        page = self._pages.get(page_id)
        if not page or not selector:
            return False
        
        try:
            await page.select_option(selector, value)
            return True
        except:
            return False

    async def check(self, data: Dict[str, Any]) -> bool:
        """Check a checkbox."""
        page_id = data.get('page_id')
        selector = data.get('selector')
        
        page = self._pages.get(page_id)
        if not page or not selector:
            return False
        
        try:
            await page.check(selector)
            return True
        except:
            return False

    async def uncheck(self, data: Dict[str, Any]) -> bool:
        """Uncheck a checkbox."""
        page_id = data.get('page_id')
        selector = data.get('selector')
        
        page = self._pages.get(page_id)
        if not page or not selector:
            return False
        
        try:
            await page.uncheck(selector)
            return True
        except:
            return False

    async def hover(self, data: Dict[str, Any]) -> bool:
        """Hover over an element."""
        page_id = data.get('page_id')
        selector = data.get('selector')
        
        page = self._pages.get(page_id)
        if not page or not selector:
            return False
        
        try:
            await page.hover(selector)
            return True
        except:
            return False

    async def get_content(self, data: Dict[str, Any]) -> Optional[str]:
        """Get page HTML content."""
        page_id = data.get('page_id')
        page = self._pages.get(page_id)
        
        if not page:
            return None
        
        try:
            return await page.content()
        except:
            return None

    async def get_text(self, data: Dict[str, Any]) -> Optional[str]:
        """Get text content of an element."""
        page_id = data.get('page_id')
        selector = data.get('selector')
        
        page = self._pages.get(page_id)
        if not page or not selector:
            return None
        
        try:
            return await page.text_content(selector)
        except:
            return None

    async def get_attribute(self, data: Dict[str, Any]) -> Optional[str]:
        """Get attribute value of an element."""
        page_id = data.get('page_id')
        selector = data.get('selector')
        attribute = data.get('attribute')
        
        page = self._pages.get(page_id)
        if not page or not selector or not attribute:
            return None
        
        try:
            return await page.get_attribute(selector, attribute)
        except:
            return None

    async def screenshot(self, data: Dict[str, Any]) -> Optional[bytes]:
        """Take a screenshot."""
        page_id = data.get('page_id')
        full_page = data.get('full_page', False)
        
        page = self._pages.get(page_id)
        if not page:
            return None
        
        try:
            return await page.screenshot(full_page=full_page)
        except:
            return None

    async def pdf(self, data: Dict[str, Any]) -> Optional[bytes]:
        """Generate PDF of the page."""
        page_id = data.get('page_id')
        
        page = self._pages.get(page_id)
        if not page:
            return None
        
        try:
            return await page.pdf()
        except:
            return None