# /src/services/pacer/_core_services/browser/browser_service.py

"""
Browser Service for PACER processing.

The 9th core service that consolidates browser management, authentication,
and navigation functionality into a single unified service.
"""

from __future__ import annotations
import asyncio
from typing import Any, Dict, Optional, TYPE_CHECKING
from datetime import datetime, timedelta

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError, BrowserError

# Import consolidated manager components
from src.pacer._core_services.browser.auth_manager import AuthenticationManager
from src.pacer._core_services.browser.browser_manager import BrowserManager
from src.pacer._core_services.browser.navigation_manager import NavigationManager

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol
    from playwright.async_api import Browser, BrowserContext, Page, Playwright


class BrowserService(AsyncServiceBase):
    """
    Consolidated browser service for PACER processing.
    
    This service combines the functionality of BrowserFacadeService and related
    browser components (authentication, navigation, session management) into a
    unified interface following SOLID principles.
    
    Responsibilities:
    - Browser lifecycle management (launch, close, cleanup)
    - Context and page creation with proper configuration
    - Authentication workflows (login, session validation)
    - Navigation operations (URL building, page navigation)
    - Session management (cookies, storage, timeouts)
    - Browser health monitoring and recovery
    """

    def __init__(self,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        """
        Initialize the Browser Service with consolidated manager components.
        
        Args:
            logger: Optional logger instance for structured logging
            config: Optional configuration dictionary
        """
        super().__init__(logger, config)
        
        # Initialize manager components
        self._auth_manager = AuthenticationManager(logger, config)
        self._browser_manager = BrowserManager(logger, config)
        self._navigation_manager = NavigationManager(logger, config)
        
        # Legacy compatibility attributes (will delegate to managers)
        self._playwright: Optional[Playwright] = None
        self._browser: Optional[Browser] = None
        self._contexts: Dict[str, BrowserContext] = {}
        self._pages: Dict[str, Page] = {}
        
        # Session management (delegated to auth_manager)
        self._session_timeout = timedelta(minutes=self.config.get('session_timeout', 30))
        self._last_activity: Dict[str, datetime] = {}
        self._authenticated_sessions: Dict[str, bool] = {}
        
        # Configuration
        self._browser_config = self._get_browser_config()
        self._auth_config = self._get_auth_config()
        self._navigation_config = self._get_navigation_config()

    async def _initialize_service(self) -> None:
        """Initialize the browser service and playwright."""
        self.log_info("Initializing Browser Service (9th Core Service)")
        
        try:
            # Import playwright dynamically to avoid import issues
            from playwright.async_api import async_playwright
            
            # Launch playwright
            self._playwright = await async_playwright().start()
            
            # Launch browser with configuration
            await self._launch_browser()
            
            self.log_info("Browser Service initialized successfully")
            
        except Exception as e:
            self.log_error(f"Failed to initialize Browser Service: {str(e)}", exc_info=True)
            raise PacerServiceError(f"Browser Service initialization failed: {str(e)}")

    async def _cleanup_service(self) -> None:
        """Clean up browser resources."""
        self.log_info("Cleaning up Browser Service resources")
        
        # Close all pages
        for page_id, page in self._pages.items():
            try:
                await page.close()
                self.log_debug(f"Closed page: {page_id}")
            except Exception as e:
                self.log_warning(f"Error closing page {page_id}: {str(e)}")
        
        # Close all contexts
        for context_id, context in self._contexts.items():
            try:
                await context.close()
                self.log_debug(f"Closed context: {context_id}")
            except Exception as e:
                self.log_warning(f"Error closing context {context_id}: {str(e)}")
        
        # Close browser
        if self._browser:
            try:
                await self._browser.close()
                self.log_debug("Browser closed successfully")
            except Exception as e:
                self.log_warning(f"Error closing browser: {str(e)}")
        
        # Stop playwright
        if self._playwright:
            try:
                await self._playwright.stop()
                self.log_debug("Playwright stopped successfully")
            except Exception as e:
                self.log_warning(f"Error stopping playwright: {str(e)}")
        
        # Clear references
        self._pages.clear()
        self._contexts.clear()
        self._authenticated_sessions.clear()
        self._last_activity.clear()
        
        self.log_info("Browser Service cleanup completed")

    def _get_browser_config(self) -> Dict[str, Any]:
        """Get browser configuration settings."""
        return {
            'headless': self.config.get('browser_headless', True),
            'timeout': self.config.get('browser_timeout', 30000),
            'viewport': {
                'width': self.config.get('viewport_width', 1920),
                'height': self.config.get('viewport_height', 1080)
            },
            'user_agent': self.config.get('user_agent', None),
            'args': self.config.get('browser_args', [
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--no-sandbox'
            ])
        }

    def _get_auth_config(self) -> Dict[str, Any]:
        """Get authentication configuration settings."""
        return {
            'login_url': self.config.get('pacer_login_url', 'https://pacer.uscourts.gov/'),
            'username_selector': self.config.get('username_selector', 'input[name="username"]'),
            'password_selector': self.config.get('password_selector', 'input[name="password"]'),
            'submit_selector': self.config.get('submit_selector', 'button[type="submit"]'),
            'login_timeout': self.config.get('login_timeout', 30000),
            'retry_attempts': self.config.get('auth_retry_attempts', 3)
        }

    def _get_navigation_config(self) -> Dict[str, Any]:
        """Get navigation configuration settings."""
        return {
            'wait_until': self.config.get('wait_until', 'networkidle'),
            'navigation_timeout': self.config.get('navigation_timeout', 30000),
            'retry_on_failure': self.config.get('retry_navigation', True),
            'max_retries': self.config.get('max_navigation_retries', 3)
        }

    async def _launch_browser(self) -> None:
        """Launch the browser with configured settings."""
        if not self._playwright:
            raise BrowserError("Playwright not initialized")
        
        self.log_debug("Launching browser with configuration", self._browser_config)
        
        # Select browser type (chromium by default)
        browser_type = getattr(self._playwright, 
                              self.config.get('browser_type', 'chromium'))
        
        # Launch browser
        self._browser = await browser_type.launch(
            headless=self._browser_config['headless'],
            args=self._browser_config['args']
        )
        
        self.log_info("Browser launched successfully")

    async def _execute_action(self, data: Any) -> Any:
        """
        Execute browser service actions.
        
        Args:
            data: Action data containing 'action' and parameters
            
        Returns:
            Action result
        """
        action = data.get('action')
        
        self.log_debug(f"Executing browser action: {action}")
        
        action_map = {
            # Context management
            'create_context': self.create_context,
            'get_context': self.get_context,
            'close_context': self.close_context,
            
            # Page management
            'create_page': self.create_page,
            'get_page': self.get_page,
            'close_page': self.close_page,
            
            # Authentication
            'login': self.login,
            'logout': self.logout,
            'is_authenticated': self.is_authenticated,
            'validate_session': self.validate_session,
            
            # Navigation
            'navigate': self.navigate,
            'get_current_url': self.get_current_url,
            'wait_for_selector': self.wait_for_selector,
            'click_element': self.click_element,
            'fill_input': self.fill_input,
            
            # Session management
            'save_session': self.save_session,
            'restore_session': self.restore_session,
            'clear_session': self.clear_session,
            
            # Health monitoring
            'check_health': self.check_health,
            'restart_browser': self.restart_browser
        }
        
        handler = action_map.get(action)
        if not handler:
            raise PacerServiceError(f"Unknown action for BrowserService: {action}")
        
        return await handler(data)

    # Context Management Methods
    async def create_context(self, data: Dict[str, Any]) -> str:
        """
        Create a new browser context.
        
        Args:
            data: Configuration for the context
            
        Returns:
            Context ID
        """
        context_id = data.get('context_id', f"context_{len(self._contexts)}")
        
        if context_id in self._contexts:
            self.log_warning(f"Context {context_id} already exists")
            return context_id
        
        if not self._browser:
            await self._launch_browser()
        
        # Create context with configuration
        context = await self._browser.new_context(
            viewport=self._browser_config['viewport'],
            user_agent=self._browser_config['user_agent'] if self._browser_config['user_agent'] else None,
            ignore_https_errors=data.get('ignore_https_errors', False),
            java_script_enabled=data.get('javascript_enabled', True)
        )
        
        self._contexts[context_id] = context
        self._last_activity[context_id] = datetime.now()
        
        self.log_info(f"Created browser context: {context_id}")
        return context_id

    async def get_context(self, data: Dict[str, Any]) -> Optional[BrowserContext]:
        """Get an existing browser context."""
        context_id = data.get('context_id')
        return self._contexts.get(context_id)

    async def close_context(self, data: Dict[str, Any]) -> bool:
        """Close a browser context and its pages."""
        context_id = data.get('context_id')
        
        if context_id not in self._contexts:
            self.log_warning(f"Context {context_id} not found")
            return False
        
        # Close all pages in this context
        pages_to_close = [pid for pid, page in self._pages.items() 
                         if page.context == self._contexts[context_id]]
        
        for page_id in pages_to_close:
            await self.close_page({'page_id': page_id})
        
        # Close context
        await self._contexts[context_id].close()
        del self._contexts[context_id]
        
        # Clean up related data
        if context_id in self._last_activity:
            del self._last_activity[context_id]
        if context_id in self._authenticated_sessions:
            del self._authenticated_sessions[context_id]
        
        self.log_info(f"Closed browser context: {context_id}")
        return True

    # Page Management Methods
    async def create_page(self, data: Dict[str, Any]) -> str:
        """
        Create a new page in a context.
        
        Args:
            data: Configuration including context_id
            
        Returns:
            Page ID
        """
        context_id = data.get('context_id', 'default')
        page_id = data.get('page_id', f"page_{len(self._pages)}")
        
        # Ensure context exists
        if context_id not in self._contexts:
            await self.create_context({'context_id': context_id})
        
        context = self._contexts[context_id]
        page = await context.new_page()
        
        # Set default timeout
        page.set_default_timeout(self._browser_config['timeout'])
        page.set_default_navigation_timeout(self._navigation_config['navigation_timeout'])
        
        self._pages[page_id] = page
        self._last_activity[context_id] = datetime.now()
        
        self.log_info(f"Created page {page_id} in context {context_id}")
        return page_id

    async def get_page(self, data: Dict[str, Any]) -> Optional[Page]:
        """Get an existing page."""
        page_id = data.get('page_id')
        return self._pages.get(page_id)

    async def close_page(self, data: Dict[str, Any]) -> bool:
        """Close a page."""
        page_id = data.get('page_id')
        
        if page_id not in self._pages:
            self.log_warning(f"Page {page_id} not found")
            return False
        
        await self._pages[page_id].close()
        del self._pages[page_id]
        
        self.log_info(f"Closed page: {page_id}")
        return True

    # Authentication Methods
    async def login(self, data: Dict[str, Any]) -> bool:
        """
        Perform login authentication.
        
        Args:
            data: Login credentials and page information
            
        Returns:
            Success status
        """
        page_id = data.get('page_id')
        username = data.get('username')
        password = data.get('password')
        context_id = data.get('context_id', 'default')
        
        if not all([page_id, username, password]):
            raise ValueError("Missing required login parameters")
        
        page = self._pages.get(page_id)
        if not page:
            raise ValueError(f"Page {page_id} not found")
        
        try:
            # Navigate to login page
            await page.goto(self._auth_config['login_url'], 
                          wait_until=self._navigation_config['wait_until'])
            
            # Fill credentials
            await page.fill(self._auth_config['username_selector'], username)
            await page.fill(self._auth_config['password_selector'], password)
            
            # Submit form
            await page.click(self._auth_config['submit_selector'])
            
            # Wait for navigation
            await page.wait_for_load_state('networkidle')
            
            # Verify login success (check for logout button or user indicator)
            success = await self._verify_login_success(page)
            
            if success:
                self._authenticated_sessions[context_id] = True
                self._last_activity[context_id] = datetime.now()
                self.log_info(f"Successfully logged in for context {context_id}")
            else:
                self.log_warning(f"Login failed for context {context_id}")
            
            return success
            
        except Exception as e:
            self.log_error(f"Login error: {str(e)}", exc_info=True)
            return False

    async def _verify_login_success(self, page: Page) -> bool:
        """Verify if login was successful."""
        try:
            # Check for common login success indicators
            indicators = [
                'a[href*="logout"]',
                'button:has-text("Logout")',
                'span.username',
                'div.user-info'
            ]
            
            for selector in indicators:
                try:
                    element = await page.wait_for_selector(selector, timeout=5000)
                    if element:
                        return True
                except:
                    continue
            
            # Check URL changed from login page
            current_url = page.url
            if 'login' not in current_url.lower():
                return True
            
            return False
            
        except Exception as e:
            self.log_warning(f"Error verifying login success: {str(e)}")
            return False

    async def logout(self, data: Dict[str, Any]) -> bool:
        """Perform logout."""
        context_id = data.get('context_id', 'default')
        
        if context_id in self._authenticated_sessions:
            del self._authenticated_sessions[context_id]
        
        self.log_info(f"Logged out context {context_id}")
        return True

    async def is_authenticated(self, data: Dict[str, Any]) -> bool:
        """Check if a context is authenticated."""
        context_id = data.get('context_id', 'default')
        return self._authenticated_sessions.get(context_id, False)

    async def validate_session(self, data: Dict[str, Any]) -> bool:
        """Validate if session is still active."""
        context_id = data.get('context_id', 'default')
        
        if context_id not in self._authenticated_sessions:
            return False
        
        # Check session timeout
        last_activity = self._last_activity.get(context_id)
        if last_activity:
            if datetime.now() - last_activity > self._session_timeout:
                self.log_warning(f"Session timeout for context {context_id}")
                del self._authenticated_sessions[context_id]
                return False
        
        return True

    # Navigation Methods
    async def navigate(self, data: Dict[str, Any]) -> bool:
        """
        Navigate to a URL.
        
        Args:
            data: Navigation parameters including page_id and url
            
        Returns:
            Success status
        """
        page_id = data.get('page_id')
        url = data.get('url')
        wait_until = data.get('wait_until', self._navigation_config['wait_until'])
        
        if not all([page_id, url]):
            raise ValueError("Missing required navigation parameters")
        
        page = self._pages.get(page_id)
        if not page:
            raise ValueError(f"Page {page_id} not found")
        
        retries = 0
        max_retries = self._navigation_config['max_retries']
        
        while retries <= max_retries:
            try:
                response = await page.goto(url, wait_until=wait_until)
                
                if response and response.ok:
                    self.log_info(f"Successfully navigated to {url}")
                    
                    # Update last activity
                    for context_id, context in self._contexts.items():
                        if page.context == context:
                            self._last_activity[context_id] = datetime.now()
                            break
                    
                    return True
                
                self.log_warning(f"Navigation response not OK: {response.status if response else 'No response'}")
                
            except Exception as e:
                self.log_warning(f"Navigation attempt {retries + 1} failed: {str(e)}")
                
            retries += 1
            if retries <= max_retries:
                await asyncio.sleep(2 ** retries)  # Exponential backoff
        
        return False

    async def get_current_url(self, data: Dict[str, Any]) -> Optional[str]:
        """Get the current URL of a page."""
        page_id = data.get('page_id')
        page = self._pages.get(page_id)
        
        if page:
            return page.url
        return None

    async def wait_for_selector(self, data: Dict[str, Any]) -> bool:
        """Wait for a selector to appear on the page."""
        page_id = data.get('page_id')
        selector = data.get('selector')
        timeout = data.get('timeout', 30000)
        
        page = self._pages.get(page_id)
        if not page:
            return False
        
        try:
            await page.wait_for_selector(selector, timeout=timeout)
            return True
        except Exception as e:
            self.log_warning(f"Selector wait failed: {str(e)}")
            return False

    async def click_element(self, data: Dict[str, Any]) -> bool:
        """Click an element on the page."""
        page_id = data.get('page_id')
        selector = data.get('selector')
        
        page = self._pages.get(page_id)
        if not page:
            return False
        
        try:
            await page.click(selector)
            return True
        except Exception as e:
            self.log_warning(f"Click failed: {str(e)}")
            return False

    async def fill_input(self, data: Dict[str, Any]) -> bool:
        """Fill an input field on the page."""
        page_id = data.get('page_id')
        selector = data.get('selector')
        value = data.get('value', '')
        
        page = self._pages.get(page_id)
        if not page:
            return False
        
        try:
            await page.fill(selector, value)
            return True
        except Exception as e:
            self.log_warning(f"Fill input failed: {str(e)}")
            return False

    # Session Management Methods
    async def save_session(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Save the current session state."""
        context_id = data.get('context_id', 'default')
        context = self._contexts.get(context_id)
        
        if not context:
            return {}
        
        storage_state = await context.storage_state()
        
        return {
            'context_id': context_id,
            'storage_state': storage_state,
            'authenticated': self._authenticated_sessions.get(context_id, False),
            'last_activity': self._last_activity.get(context_id, datetime.now()).isoformat()
        }

    async def restore_session(self, data: Dict[str, Any]) -> bool:
        """Restore a saved session state."""
        context_id = data.get('context_id', 'default')
        storage_state = data.get('storage_state')
        
        if not storage_state:
            return False
        
        try:
            # Close existing context if present
            if context_id in self._contexts:
                await self.close_context({'context_id': context_id})
            
            # Create new context with storage state
            if not self._browser:
                await self._launch_browser()
            
            context = await self._browser.new_context(
                storage_state=storage_state,
                viewport=self._browser_config['viewport']
            )
            
            self._contexts[context_id] = context
            self._authenticated_sessions[context_id] = data.get('authenticated', False)
            self._last_activity[context_id] = datetime.now()
            
            self.log_info(f"Restored session for context {context_id}")
            return True
            
        except Exception as e:
            self.log_error(f"Session restore failed: {str(e)}", exc_info=True)
            return False

    async def clear_session(self, data: Dict[str, Any]) -> bool:
        """Clear session data for a context."""
        context_id = data.get('context_id', 'default')
        
        if context_id in self._authenticated_sessions:
            del self._authenticated_sessions[context_id]
        
        if context_id in self._contexts:
            context = self._contexts[context_id]
            await context.clear_cookies()
            await context.clear_permissions()
        
        self.log_info(f"Cleared session for context {context_id}")
        return True

    # Health Monitoring Methods
    async def check_health(self, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Check the health status of the browser service."""
        health_status = {
            'service': 'BrowserService',
            'status': 'healthy',
            'browser_running': self._browser is not None and self._browser.is_connected(),
            'contexts': len(self._contexts),
            'pages': len(self._pages),
            'authenticated_sessions': len(self._authenticated_sessions),
            'issues': []
        }
        
        # Check browser connection
        if not health_status['browser_running']:
            health_status['status'] = 'unhealthy'
            health_status['issues'].append('Browser not connected')
        
        # Check for stale sessions
        now = datetime.now()
        for context_id, last_activity in self._last_activity.items():
            if now - last_activity > self._session_timeout:
                health_status['issues'].append(f"Stale session: {context_id}")
        
        if health_status['issues']:
            health_status['status'] = 'degraded' if health_status['browser_running'] else 'unhealthy'
        
        return health_status

    async def restart_browser(self, data: Dict[str, Any] = None) -> bool:
        """Restart the browser and restore sessions if needed."""
        self.log_info("Restarting browser service")
        
        # Save current sessions
        saved_sessions = {}
        for context_id in self._contexts:
            session_data = await self.save_session({'context_id': context_id})
            if session_data:
                saved_sessions[context_id] = session_data
        
        # Clean up
        await self._cleanup_service()
        
        # Reinitialize
        await self._initialize_service()
        
        # Restore sessions
        restore_sessions = data.get('restore_sessions', True) if data else True
        if restore_sessions:
            for context_id, session_data in saved_sessions.items():
                await self.restore_session(session_data)
        
        self.log_info("Browser service restarted successfully")
        return True