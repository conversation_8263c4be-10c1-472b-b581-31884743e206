"""
S3 Management Service

Handles AWS S3 operations including upload, download, and bucket management.
Provides secure and efficient file storage in the cloud.
"""

import os
from typing import Any, Dict, List, Optional
from pathlib import Path

from src.infrastructure.patterns.component_base import AsyncServiceBase


class S3ManagementService(AsyncServiceBase):
    """Service for managing AWS S3 operations."""
    
    def __init__(self, config_service=None, logger=None):
        super().__init__(logger=logger)
        self.config_service = config_service
        self.s3_client = None
        self.bucket_name = None
        self.aws_access_key_id = None
        self.aws_secret_access_key = None
        self.aws_region = None
        self.enabled = False
        
    async def initialize(self) -> None:
        """Initialize the S3 management service."""
        await super().initialize()
        
        # Load configuration
        if self.config_service:
            s3_config = await self.config_service.get_config_value('s3', 'settings', {})
            self.bucket_name = s3_config.get('bucket_name')
            self.aws_region = s3_config.get('region', 'us-east-1')
            self.enabled = s3_config.get('enabled', False)
        
        # Get credentials from environment or config
        self.aws_access_key_id = os.environ.get('AWS_ACCESS_KEY_ID')
        self.aws_secret_access_key = os.environ.get('AWS_SECRET_ACCESS_KEY')
        
        if self.enabled and self.bucket_name and self.aws_access_key_id:
            try:
                # Try to import boto3
                import boto3
                self.s3_client = boto3.client(
                    's3',
                    aws_access_key_id=self.aws_access_key_id,
                    aws_secret_access_key=self.aws_secret_access_key,
                    region_name=self.aws_region
                )
                
                # Test connection
                await self._test_connection()
                self.logger.info("S3ManagementService initialized successfully")
                
            except ImportError:
                self.logger.warning("boto3 not available - S3 functionality disabled")
                self.enabled = False
            except Exception as e:
                self.logger.error(f"Failed to initialize S3 client: {str(e)}")
                self.enabled = False
        else:
            self.logger.info("S3ManagementService initialized but disabled (missing config or credentials)")
    
    async def upload_file(self, local_file_path: str, s3_key: str, 
                         metadata: Dict[str, str] = None) -> str:
        """Upload a file to S3 and return the S3 URL."""
        if not self.enabled or not self.s3_client:
            raise ValueError("S3 service is not enabled or properly configured")
        
        try:
            local_path = Path(local_file_path)
            if not local_path.exists():
                raise FileNotFoundError(f"Local file not found: {local_file_path}")
            
            # Prepare upload arguments
            upload_args = {
                'Bucket': self.bucket_name,
                'Key': s3_key,
                'Filename': str(local_path)
            }
            
            if metadata:
                upload_args['ExtraArgs'] = {'Metadata': metadata}
            
            # Upload file
            self.s3_client.upload_file(**upload_args)
            
            # Generate S3 URL
            s3_url = f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{s3_key}"
            
            self.logger.info(f"File uploaded to S3: {s3_url}")
            return s3_url
            
        except Exception as e:
            self.logger.error(f"Error uploading file to S3: {str(e)}")
            raise
    
    async def download_file(self, s3_key: str, local_file_path: str) -> bool:
        """Download a file from S3 to local path."""
        if not self.enabled or not self.s3_client:
            raise ValueError("S3 service is not enabled or properly configured")
        
        try:
            local_path = Path(local_file_path)
            local_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Download file
            self.s3_client.download_file(
                Bucket=self.bucket_name,
                Key=s3_key,
                Filename=str(local_path)
            )
            
            self.logger.info(f"File downloaded from S3: {s3_key} -> {local_file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error downloading file from S3: {str(e)}")
            return False
    
    async def file_exists(self, s3_key: str) -> bool:
        """Check if a file exists in S3."""
        if not self.enabled or not self.s3_client:
            return False
        
        try:
            self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
            return True
        except Exception:
            return False
    
    async def delete_file(self, s3_key: str) -> bool:
        """Delete a file from S3."""
        if not self.enabled or not self.s3_client:
            raise ValueError("S3 service is not enabled or properly configured")
        
        try:
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=s3_key)
            self.logger.info(f"File deleted from S3: {s3_key}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting file from S3: {str(e)}")
            return False
    
    async def list_files(self, prefix: str = "", max_keys: int = 1000) -> List[Dict[str, Any]]:
        """List files in S3 with optional prefix filter."""
        if not self.enabled or not self.s3_client:
            return []
        
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix,
                MaxKeys=max_keys
            )
            
            files = []
            for obj in response.get('Contents', []):
                files.append({
                    'key': obj['Key'],
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'].isoformat(),
                    'etag': obj['ETag'].strip('"')
                })
            
            return files
            
        except Exception as e:
            self.logger.error(f"Error listing S3 files: {str(e)}")
            return []
    
    async def get_file_metadata(self, s3_key: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a file in S3."""
        if not self.enabled or not self.s3_client:
            return None
        
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
            
            return {
                'key': s3_key,
                'size': response.get('ContentLength'),
                'last_modified': response.get('LastModified').isoformat() if response.get('LastModified') else None,
                'content_type': response.get('ContentType'),
                'metadata': response.get('Metadata', {}),
                'etag': response.get('ETag', '').strip('"')
            }
            
        except Exception as e:
            self.logger.error(f"Error getting S3 file metadata: {str(e)}")
            return None
    
    async def generate_presigned_url(self, s3_key: str, expiration: int = 3600) -> Optional[str]:
        """Generate a presigned URL for temporary access to a file."""
        if not self.enabled or not self.s3_client:
            return None
        
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': s3_key},
                ExpiresIn=expiration
            )
            
            self.logger.info(f"Generated presigned URL for {s3_key} (expires in {expiration}s)")
            return url
            
        except Exception as e:
            self.logger.error(f"Error generating presigned URL: {str(e)}")
            return None
    
    async def get_bucket_info(self) -> Dict[str, Any]:
        """Get information about the configured S3 bucket."""
        if not self.enabled or not self.s3_client:
            return {'enabled': False}
        
        try:
            # Get bucket location
            location = self.s3_client.get_bucket_location(Bucket=self.bucket_name)
            
            # Count objects (sample)
            objects_response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                MaxKeys=1000
            )
            
            return {
                'enabled': True,
                'bucket_name': self.bucket_name,
                'region': location.get('LocationConstraint', 'us-east-1'),
                'sample_object_count': len(objects_response.get('Contents', [])),
                'is_truncated': objects_response.get('IsTruncated', False)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting bucket info: {str(e)}")
            return {
                'enabled': True,
                'bucket_name': self.bucket_name,
                'error': str(e)
            }
    
    async def _test_connection(self) -> bool:
        """Test S3 connection by listing bucket contents."""
        try:
            self.s3_client.list_objects_v2(Bucket=self.bucket_name, MaxKeys=1)
            return True
        except Exception as e:
            self.logger.warning(f"S3 connection test failed: {str(e)}")
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """Return service health status."""
        status = {
            'service': 'S3ManagementService',
            'status': 'healthy' if self.is_initialized() else 'not_initialized',
            'enabled': self.enabled,
            'bucket_name': self.bucket_name,
            'aws_region': self.aws_region,
            'has_credentials': bool(self.aws_access_key_id and self.aws_secret_access_key)
        }
        
        if self.enabled and self.s3_client:
            status['connection_test'] = await self._test_connection()
        
        return status