# /src/services/pacer/_report_components/report_generator.py
from typing import Any, Dict

from playwright.async_api import <PERSON>rro<PERSON> as PlaywrightError

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.pacer._browser_components.navigator import PacerNavigator
# TODO: Replace with proper ignore download service when implemented


class ReportGenerator(ComponentImplementation):
    """
    Stateless component for generating reports from PACER. It handles
    navigation, configuration, and execution of reports.
    """

    async def execute(self, data: Any) -> Any:
        """Main execution method for the report generator."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a report generation action.
        """
        action = data.get("action")
        if action == "generate_civil_cases_report":
            return await self.generate_civil_cases_report(**data)
        else:
            raise ValueError(f"Unknown action for ReportGenerator: {action}")

    async def generate_civil_cases_report(self, **kwargs) -> bool:
        """
        Navigates to, configures, and runs the Civil Cases Filed report.

        Args:
            **kwargs: A dictionary containing:
                - navigator (PacerNavigator)
                - court_id (str)
                - from_date_str (str)
                - to_date_str (str)
                - ignore_download_service (PacerIgnoreDownloadService)

        Returns:
            bool: True if cases were found, False otherwise.
        """
        navigator: PacerNavigator = kwargs.get("navigator")
        court_id: str = kwargs.get("court_id")
        from_date_str: str = kwargs.get("from_date_str")
        to_date_str: str = kwargs.get("to_date_str")
        ignore_download_service: PacerIgnoreDownloadService = kwargs.get("ignore_download_service")

        log_prefix = f"[{court_id}] ReportGen:"
        self.log_info(f"{log_prefix} Starting civil cases report generation.")

        try:
            # Debug: Check current page state before navigation
            current_url = navigator.page.url
            page_title = await navigator.page.title()
            self.log_info(f"{log_prefix} Current page URL: {current_url}")
            self.log_info(f"{log_prefix} Current page title: {page_title}")
            
            # Check if we're in the right ECF system
            if 'ecf.' not in current_url or 'uscourts.gov' not in current_url:
                self.log_error(f"{log_prefix} Not in ECF system! URL: {current_url}")
                raise Exception("Not in court ECF system")
            
            # Check if Reports link exists
            reports_locator = navigator.page.locator("a:has-text('Reports')")
            reports_count = await reports_locator.count()
            self.log_info(f"{log_prefix} Found {reports_count} 'Reports' links on page")
            
            if reports_count == 0:
                # Try different variations of Reports links
                report_variations = [
                    "a:has-text('Report')",
                    "a[href*='reports']", 
                    "a[href*='Reports']",
                    "a[href*='rptmenu']",
                    "a[href*='qryReports']"
                ]
                
                for variation in report_variations:
                    try:
                        var_locator = navigator.page.locator(variation)
                        var_count = await var_locator.count()
                        if var_count > 0:
                            self.log_info(f"{log_prefix} Found {var_count} links with selector: {variation}")
                            # Try to use this variation
                            await navigator.click(variation)
                            self.log_info(f"{log_prefix} Successfully clicked {variation}")
                            break
                    except Exception as e:
                        self.log_debug(f"{log_prefix} Failed to click {variation}: {e}")
                        continue
                else:
                    # Log available links for debugging
                    all_links = navigator.page.locator("a")
                    link_count = await all_links.count()
                    self.log_info(f"{log_prefix} Total links on page: {link_count}")
                    
                    if link_count < 50:  # Only log if reasonable number
                        for i in range(min(link_count, 15)):  # Log first 15 links
                            link = all_links.nth(i)
                            href = await link.get_attribute("href")
                            text = await link.text_content()
                            self.log_info(f"{log_prefix} Link {i}: text='{text}' href='{href}'")
                    
                    raise Exception("Could not find any Reports link variation")
            else:
                # Use the standard Reports link
                await navigator.click("a:has-text('Reports')")
                self.log_info(f"{log_prefix} Clicked standard Reports link")
            
            # Step 1: Click on "Civil Cases" (category link)
            civil_category_variations = [
                "a:has-text('Civil Cases')",
                "a:has-text('Civil')",
                "a[href*='civil']",
                "a[href*='Civil']"
            ]
            
            civil_category_found = False
            for variation in civil_category_variations:
                try:
                    var_locator = navigator.page.locator(variation)
                    var_count = await var_locator.count()
                    if var_count > 0:
                        self.log_info(f"{log_prefix} Found {var_count} Civil Cases category links with selector: {variation}")
                        await navigator.click(variation)
                        self.log_info(f"{log_prefix} Successfully clicked Civil Cases category: {variation}")
                        civil_category_found = True
                        break
                except Exception as e:
                    self.log_debug(f"{log_prefix} Failed to click {variation}: {e}")
                    continue
            
            if not civil_category_found:
                # Log available links on the reports page
                self.log_info(f"{log_prefix} Could not find Civil Cases category link. Available links:")
                all_links = navigator.page.locator("a")
                link_count = await all_links.count()
                self.log_info(f"{log_prefix} Total links on reports page: {link_count}")
                
                for i in range(min(link_count, 20)):  # Log first 20 links
                    link = all_links.nth(i)
                    href = await link.get_attribute("href")
                    text = await link.text_content()
                    self.log_info(f"{log_prefix} Reports Link {i}: text='{text}' href='{href}'")
                
                raise Exception("Could not find Civil Cases category link")
            
            # We're already on the civil cases report page after clicking Civil Cases
            self.log_info(f"{log_prefix} Already on civil cases report page after clicking Civil Cases.")

            # Configure
            await navigator.fill('input[name="filed_from"]', from_date_str)
            await navigator.fill('input[name="filed_to"]', to_date_str)
            self.log_info(f"{log_prefix} Configured date range: {from_date_str} to {to_date_str}.")

            # Find and click the submit button
            submit_variations = [
                'input[type="submit"][value="Run Report"]',
                'input[type="submit"]',
                'button[type="submit"]',
                'input[value*="Run"]',
                'input[value*="Submit"]',
                'button:has-text("Run")',
                'button:has-text("Submit")'
            ]
            
            submit_found = False
            for variation in submit_variations:
                try:
                    var_locator = navigator.page.locator(variation)
                    var_count = await var_locator.count()
                    if var_count > 0:
                        self.log_info(f"{log_prefix} Found {var_count} submit buttons with selector: {variation}")
                        await navigator.click(variation)
                        self.log_info(f"{log_prefix} Successfully clicked submit button: {variation}")
                        submit_found = True
                        break
                except Exception as e:
                    self.log_debug(f"{log_prefix} Failed to click {variation}: {e}")
                    continue
            
            if not submit_found:
                # Log all input and button elements
                self.log_info(f"{log_prefix} Could not find submit button. Available form elements:")
                all_inputs = navigator.page.locator("input, button")
                input_count = await all_inputs.count()
                for i in range(min(input_count, 20)):
                    elem = all_inputs.nth(i)
                    tag = await elem.evaluate("el => el.tagName")
                    input_type = await elem.get_attribute("type")
                    value = await elem.get_attribute("value")
                    text = await elem.text_content()
                    self.log_info(f"{log_prefix} Form Element {i}: {tag} type='{input_type}' value='{value}' text='{text}'")
                
                raise Exception("Could not find submit button to run report")
            
            self.log_info(f"{log_prefix} 'Run Report' clicked.")

            # Verify
            no_cases_locator = navigator.page.locator("text=No cases found matching the criteria")
            if await no_cases_locator.is_visible():
                self.log_info(f"{log_prefix} No cases found.")
                return False

            self.log_info(f"{log_prefix} Report generated successfully, cases found.")
            return True

        except PlaywrightError as e:
            self.log_error(f"{log_prefix} Report generation failed: {e}", exc_info=True)
            raise

    async def extract_report_data(self, **kwargs) -> list:
        # Placeholder for extracting data from a report page.
        # In the original design, this was part of the row processing service.
        # This component focuses only on generating the report page itself.
        return []
