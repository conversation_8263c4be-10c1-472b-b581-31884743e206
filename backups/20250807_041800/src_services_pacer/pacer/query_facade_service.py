# /src/services/pacer/query_facade_service.py
from __future__ import annotations
from typing import Any, Dict, List, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.repositories.pacer_repository import PacerRepository
    from src.pacer._query_components.query_builder import QueryBuilder
    from src.pacer._query_components.result_parser import ResultParser
    from src.infrastructure.protocols.logger import LoggerProtocol

class QueryFacadeService(AsyncServiceBase):
    """Unified interface for PACER data query operations."""

    def __init__(self,
                 query_builder: QueryBuilder,
                 result_parser: ResultParser,
                 repository: PacerRepository,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        self._builder = query_builder
        self._parser = result_parser
        self._repo = repository

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        if action == 'search_by_title':
            return await self.search_by_title(data['search_string'])
        elif action == 'search_complex':
            return await self.search_complex(data.get('criteria', {}))
        elif action == 'aggregate_by_court':
            records = await self._repo.scan_all()
            return self._parser.aggregate_by_court(records)
        else:
            raise PacerServiceError(f"Unknown action for QueryFacadeService: {action}")

    async def search_by_title(self, search_string: str) -> List[Dict[str, Any]]:
        """Searches for records by title."""
        self.log_info(f"Searching for records with title: {search_string}")
        all_records = await self._repo.scan_all()
        search_lower = search_string.lower()
        results = [r for r in all_records if search_lower in r.get('Title', '').lower()]
        return sorted(results, key=lambda x: x.get('FilingDate', ''), reverse=True)

    async def search_complex(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Performs a complex search using multiple criteria."""
        self.log_info(f"Performing complex search with criteria: {criteria}")
        query = self._builder.build_complex_query(criteria)

        # This is a simplified implementation. A real implementation would
        # pass the structured query to the repository.
        all_records = await self._repo.scan_all()
        results = all_records

        if query.get('title'):
            results = [r for r in results if query['title'].lower() in r.get('Title', '').lower()]
        if query.get('mdl_num'):
            results = [r for r in results if query['mdl_num'] == r.get('MdlNum')]

        return sorted(results, key=lambda x: x.get('FilingDate', ''), reverse=True)
