# /src/services/pacer/report_facade_service.py
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer._report_components.report_generator import ReportGenerator


class ReportFacadeService(AsyncServiceBase):
    """
    Facade service for generating reports from PACER. It coordinates the
    ReportGenerator component to perform the necessary actions.
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        report_generator: Optional[ReportGenerator] = None,
    ):
        super().__init__(logger, config)
        self.report_generator = report_generator

    async def execute(self, data: Any) -> Any:
        """Main execution method for report facade."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """
        Routes actions to the appropriate report generation methods.
        """
        action = data.get("action")
        if action == "generate_civil_cases_report":
            if not self.report_generator:
                raise ValueError("ReportGenerator is not initialized.")

            # The component expects the same action name
            return await self.report_generator.execute(data)
        else:
            raise ValueError(f"Unknown action for ReportFacadeService: {action}")
