# /src/services/pacer/authentication_facade_service.py
from __future__ import annotations
from typing import Any, Dict, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer._authentication_components.login_handler import <PERSON><PERSON><PERSON>andler
    from src.pacer._authentication_components.session_manager import SessionManager
    from src.pacer._authentication_components.credential_validator import CredentialValidator
    from src.pacer._authentication_components.ecf_login_handler import ECFLoginHandler
    from src.pacer._browser_components.navigator import PacerNavigator
    from src.infrastructure.protocols.logger import LoggerProtocol

class AuthenticationFacadeService(AsyncServiceBase):
    """Unified interface for PACER authentication operations."""

    def __init__(self,
                 login_handler: <PERSON><PERSON><PERSON><PERSON><PERSON>,
                 session_manager: SessionManager,
                 credential_validator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                 ecf_login_handler: EC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        self._login_handler = login_handler
        self._session_manager = session_manager
        self._credential_validator = credential_validator
        self._ecf_login_handler = ecf_login_handler

    async def execute(self, data: Any) -> Any:
        """Main execution method for authentication facade."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        if action == 'authenticate':
            return await self.authenticate(data['navigator'])
        elif action == 'perform_ecf_login':
            return await self.perform_ecf_login(data['navigator'], data['court_id'])
        elif action == 'login':
            # Support 'login' action for backwards compatibility - performs ECF login
            return await self.perform_ecf_login(data['navigator'], data['court_id'])
        else:
            raise PacerServiceError(f"Unknown action for AuthenticationFacadeService: {action}")

    async def perform_ecf_login(self, navigator: PacerNavigator, court_id: str) -> bool:
        """
        Performs the full ECF login sequence for a given court.
        Ensures main PACER login is complete before attempting ECF login.
        """
        log_prefix = f"[{court_id}]"
        self.log_info(f"{log_prefix} Starting full ECF login flow via facade.")

        # 1. Ensure we are logged into the main PACER site
        main_auth_success = await self.authenticate(navigator)
        if not main_auth_success:
            self.log_error(f"{log_prefix} Main PACER authentication failed. Aborting ECF login.")
            return False

        self.log_info(f"{log_prefix} Main PACER authentication successful. Proceeding to ECF login.")

        # 2. Perform the ECF-specific login sequence
        ecf_login_success = await self._ecf_login_handler.perform_ecf_login_sequence(navigator, court_id)

        if ecf_login_success:
            self.log_info(f"{log_prefix} ECF login flow completed successfully.")
        else:
            self.log_error(f"{log_prefix} ECF login flow failed.")

        return ecf_login_success

    async def authenticate(self, navigator: PacerNavigator) -> bool:
        """
        Ensures the user is logged into the main PACER site.

        Args:
            navigator: The PacerNavigator instance to use.

        Returns:
            True if login is successful or already logged in, False otherwise.
        """
        log_prefix = "[AuthFacade]"
        self.log_info(f"{log_prefix} Starting authentication process.")
        
        # Debug: Check current page state
        current_url = navigator.page.url
        page_title = await navigator.page.title()
        self.log_info(f"{log_prefix} Current URL: {current_url}")
        self.log_info(f"{log_prefix} Current page title: {page_title}")        

        # 1. Check if already logged in
        is_logged_in = await self._session_manager.is_logged_in(navigator)
        if is_logged_in:
            self.log_info(f"{log_prefix} Authentication successful (already logged in).")
            return True

        # 2. If not logged in, perform login
        # The credential validation is implicitly handled by the login handler now,
        # but the validator component is there for future extension.
        self.log_info(f"{log_prefix} Not logged in. Proceeding with login.")
        login_successful = await self._login_handler.perform_login(navigator)

        if login_successful:
            self.log_info(f"{log_prefix} Authentication successful (login performed).")
            # Verify login was successful by checking again
            post_login_check = await self._session_manager.is_logged_in(navigator)
            self.log_info(f"{log_prefix} Post-login verification: {post_login_check}")
        else:
            self.log_error(f"{log_prefix} Authentication failed.")

        return login_successful
