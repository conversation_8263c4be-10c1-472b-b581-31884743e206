# /src/services/pacer/docket_orchestrator_facade_service.py
from typing import Any, Dict, Optional

from playwright.async_api import Page

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.case_classification_facade_service import CaseClassificationFacadeService as PacerCaseClassificationService
from src.pacer.case_verification_facade_service import CaseVerificationFacadeService as PacerCaseVerificationService
from src.pacer.download_orchestration_facade_service import \
    DownloadOrchestrationFacadeService
from src.pacer.html_processing_facade_service import HtmlProcessingFacadeService
from src.pacer.relevance_facade_service import RelevanceFacadeService as RelevanceService


class DocketOrchestratorFacadeService(AsyncServiceBase):
    """
    Facade service that orchestrates the entire workflow for processing a single
    docket case. It coordinates other high-level facades to execute the process.
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        html_processing_facade: Optional[HtmlProcessingFacadeService] = None,
        relevance_service: Optional[RelevanceService] = None,
        case_classification_service: Optional[PacerCaseClassificationService] = None,
        case_verification_service: Optional[PacerCaseVerificationService] = None,
        download_orchestration_facade: Optional[DownloadOrchestrationFacadeService] = None,
    ):
        super().__init__(logger, config)
        self.html_facade = html_processing_facade
        self.relevance_service = relevance_service
        self.classification_service = case_classification_service
        self.verification_service = case_verification_service
        self.download_facade = download_orchestration_facade

    async def _execute_action(self, data: Any) -> Any:
        """
        Routes actions to the main processing workflow.
        """
        action = data.get("action")
        if action == "process_docket_case":
            return await self.process_docket_case(
                page=data.get("page"),
                initial_details=data.get("initial_details", {}),
            )
        else:
            raise ValueError(f"Unknown action for DocketOrchestratorFacadeService: {action}")

    async def process_docket_case(
        self, page: Page, initial_details: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Orchestrates the end-to-end processing of a single docket case.

        Args:
            page: The Playwright page object for the docket.
            initial_details: A dictionary with initial data about the case.

        Returns:
            A dictionary with the fully processed case details, or None on failure.
        """
        docket_num = initial_details.get("docket_num", "UNKNOWN")
        log_prefix = f"[{self.config.get('court_id', 'N/A')}][{docket_num}] DocketOrch:"
        self.log_info(f"{log_prefix} Starting docket processing workflow.")

        try:
            # Step 1: Process HTML content
            case_details = await self.html_facade.execute({
                "action": "process_html_content",
                "case_details": initial_details,
                "html_content": await page.content(),
                "json_path": initial_details.get("json_path"),
            })
            if not case_details:
                raise Exception("HTML processing failed to return case details.")
            self.log_info(f"{log_prefix} HTML processing complete.")

            # Step 2: Apply relevance and classification
            case_details = await self.relevance_service.apply_relevance_filters(case_details)
            case_details = self.classification_service.classify_case_initial(case_details)
            if self.relevance_service.should_skip_processing(case_details):
                self.log_info(f"{log_prefix} Case skipped after relevance filters.")
                # Save to review log if needed
                return case_details
            self.log_info(f"{log_prefix} Relevance and classification complete.")

            # Step 3: Verify if case should be processed further
            should_process = await self.verification_service.verify_case(case_details)
            if not should_process:
                self.log_info(f"{log_prefix} Case skipped after verification.")
                return case_details
            self.log_info(f"{log_prefix} Case verification complete.")

            # Step 4: Execute download workflow
            is_explicitly_requested = initial_details.get("_is_explicitly_requested", False)
            case_details = await self.download_facade.execute({
                "action": "execute_download_workflow",
                "case_details": case_details,
                "is_explicitly_requested": is_explicitly_requested,
                "page": page,
            })
            self.log_info(f"{log_prefix} Download workflow complete.")

            self.log_info(f"{log_prefix} Docket processing workflow finished successfully.")
            return case_details

        except Exception as e:
            self.log_error(f"{log_prefix} Docket processing workflow failed: {e}", exc_info=True)
            # Optionally save error state here
            return None
