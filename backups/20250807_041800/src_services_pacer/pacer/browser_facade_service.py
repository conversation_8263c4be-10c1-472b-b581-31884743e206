from __future__ import annotations
from typing import Optional, Dict, Any, TYPE_CHECKING
from src.infrastructure.patterns.component_base import AsyncServiceBase

if TYPE_CHECKING:
    from ._browser_components.context_factory import ContextFactory
    from ._browser_components.playwright_manager import PlaywrightManager

class BrowserFacadeService(AsyncServiceBase):
    """Facade for browser-related operations."""
    def __init__(self, context_factory: "ContextFactory", playwright_manager: "PlaywrightManager", config: Optional[Dict[str, Any]] = None, logger: Optional[Any] = None, timeout_ms: int = 60000):
        super().__init__(logger, config)
        self.context_factory = context_factory
        self.playwright_manager = playwright_manager
        self.timeout_ms = timeout_ms

    async def get_new_context(self, **kwargs):
        """Create a new browser context."""
        return await self._execute_action({"action": "new_context", **kwargs})
    
    async def close_context(self, context, **kwargs):
        """Close a browser context."""
        return await self._execute_action({"action": "close_context", "context": context, **kwargs})
    
    async def get_playwright(self):
        """Get the playwright instance."""
        return await self._execute_action({"action": "get_playwright"})

    async def _execute_action(self, data: Dict[str, Any]) -> Any:
        action = data.get("action")
        if action == "new_context":
            return await self.context_factory.create_context(**data)
        elif action == "close_context":
            return await self.context_factory.close_context(**data)
        elif action == "get_playwright":
            return await self.playwright_manager.get_playwright()
        else:
            raise ValueError(f"Unknown action: {action}")
