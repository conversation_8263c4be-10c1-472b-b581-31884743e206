# /src/services/pacer/_transfer_components/transfer_processor.py
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.pacer._transfer_components.court_identifier import CourtIdentifier
# TODO: Replace with proper file management service when implemented
from src.pacer._core_services.relevance.relevance_service import RelevanceService


class TransferProcessor(ComponentImplementation):
    """
    Component for processing case transfers. It identifies the transferor court,
    checks for MDL status, and handles S3 link inheritance.
    """

    def __init__(
        self,
        logger: Any = None,
        config: Optional[Dict] = None,
        court_identifier: Optional[CourtIdentifier] = None,
        file_manager: Optional[Any] = None,
        relevance_service: Optional[RelevanceService] = None,
    ):
        super().__init__(logger, config)
        self.court_identifier = court_identifier
        self.file_manager = file_manager
        self.relevance_service = relevance_service

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a transfer processing action.
        """
        action = data.get("action")
        if action == "process_transfer":
            case_details = data.get("case_details")
            if not case_details:
                raise ValueError("case_details must be provided.")
            return await self.process_transfer_case(case_details)
        else:
            raise ValueError(f"Unknown action for TransferProcessor: {action}")

    async def process_transfer_case(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes a case that has been transferred from another court.
        """
        log_prefix = f"[{case_details.get('court_id', 'N/A')}]"
        self.log_info(f"{log_prefix} Starting transfer case processing.")

        # Identify transferor court
        transferor_court_name = case_details.get("transferor_court")
        if transferor_court_name:
            transferor_court_id = await self.court_identifier.execute({
                "action": "get_court_id", "court_name": transferor_court_name
            })
            case_details["transferor_court_id"] = transferor_court_id
            self.log_info(f"{log_prefix} Identified transferor court: {transferor_court_id}")

        # Check for MDL and inherit S3 link
        is_mdl = await self.relevance_service.is_mdl_case(case_details)
        if is_mdl:
            self.log_info(f"{log_prefix} MDL case detected, attempting to inherit S3 link.")
            case_details["is_transferred"] = True
            case_details["is_mdl_case"] = True

            target_case = await self.file_manager.get_target_case_details(case_details)
            if target_case and target_case.get("s3_link"):
                case_details["s3_link"] = target_case["s3_link"]
                self.log_info(f"{log_prefix} Inherited S3 link: {target_case['s3_link']}")
        else:
            case_details["is_transferred"] = False

        return case_details
