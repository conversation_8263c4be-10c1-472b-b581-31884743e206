# PACER Service Orchestration Layer

## ⚠️ MIGRATION WARNING
This directory is being refactored. Business logic is moving to `src/pacer/` domain layer.

## Target State
After migration, this directory should contain ONLY:
- `pacer_orchestrator.py` - Main orchestration service
- `pacer_factory.py` - Service factory
- `interactive_service.py` - Interactive mode
- Maximum 5-10 files total

## Current State
Currently contains 102+ files with mixed responsibilities. These are being migrated to:
- Business logic → `src/pacer/components/`
- Core services → `src/pacer/core/`
- Domain models → `src/pacer/models/`

## Migration Checklist
- [ ] Move `_analytics_components/` → `src/pacer/components/analytics/`
- [ ] Move `_authentication_components/` → `src/pacer/components/authentication/`
- [ ] Move `_browser_components/` → `src/pacer/components/browser/`
- [ ] Move `_case_processing_components/` → `src/pacer/components/case_processing/`
- [ ] Move `_classification_components/` → `src/pacer/components/classification/`
- [ ] Move `_config_components/` → `src/pacer/components/configuration/`
- [ ] Move `_core_services/` → `src/pacer/core/`
- [ ] Move `_download_components/` → `src/pacer/components/download/`
- [ ] Move `_export_components/` → `src/pacer/components/export/`
- [ ] Move `_file_components/` → `src/pacer/components/file_operations/`
- [ ] Move `_navigation_components/` → `src/pacer/components/navigation/`
- [ ] Move `_processing_components/` → `src/pacer/components/processing/`
- [ ] Move `_query_components/` → `src/pacer/components/query/`
- [ ] Move `_report_components/` → `src/pacer/components/report/`
- [ ] Move `_transfer_components/` → `src/pacer/components/transfer/`
- [ ] Move `_verification_components/` → `src/pacer/components/verification/`
- [ ] Delete duplicate `async_service_base.py` - use infrastructure version

## Final Structure
```
services/pacer/
├── __init__.py
├── pacer_orchestrator.py    # Thin orchestration only
├── pacer_factory.py         # DI factory
├── interactive_service.py   # Interactive mode
└── CLAUDE.md               # This file
```

## Rules
1. **NO business logic** - Only orchestration
2. **NO domain logic** - Lives in `src/pacer/`
3. **Thin layer** - Coordinates domain components
4. **Use DI** - All dependencies injected

## See Also
- `docs/ARCHITECTURAL_CONSISTENCY_PLAN.md`
- `src/pacer/CLAUDE.md`