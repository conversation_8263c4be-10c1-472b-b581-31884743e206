# /src/services/pacer/case_classification_facade_service.py
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer._classification_components.case_classifier import CaseClassifier


class CaseClassificationFacadeService(AsyncServiceBase):
    """
    Facade service for classifying PACER cases. It coordinates the
    CaseClassifier component to perform the necessary actions.
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        case_classifier: Optional[CaseClassifier] = None,
    ):
        super().__init__(logger, config)
        self.case_classifier = case_classifier

    async def _execute_action(self, data: Any) -> Any:
        """
        Routes actions to the case classification component.
        """
        action = data.get("action")
        if action == "classify_case":
            if not self.case_classifier:
                raise ValueError("CaseClassifier is not initialized.")
            return await self.case_classifier.execute(data)
        else:
            raise ValueError(f"Unknown action for CaseClassificationFacadeService: {action}")
