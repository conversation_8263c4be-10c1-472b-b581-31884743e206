# /src/services/pacer/download_orchestration_facade_service.py
from __future__ import annotations
from pathlib import Path
from typing import Any, Dict, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer._browser_components.navigator import PacerNavigator
    from src.pacer._download_components.download_validator import DownloadValidator
    from src.pacer._download_components.file_downloader import FileDownloader
    from src.pacer._download_components.download_manager import DownloadManager
    from src.infrastructure.protocols.logger import LoggerProtocol

class DownloadOrchestrationFacadeService(AsyncServiceBase):
    """Unified interface for PACER download orchestration."""

    def __init__(self,
                 download_validator: DownloadValidator,
                 file_downloader: FileDownloader,
                 download_manager: DownloadManager,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        self._validator = download_validator
        self._downloader = file_downloader
        self._manager = download_manager

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        if action == 'execute_download_workflow':
            return await self.execute_download_workflow(data['navigator'], data['case_details'], data['html_content'])
        else:
            raise PacerServiceError(f"Unknown action for DownloadOrchestrationFacadeService: {action}")

    async def execute_download_workflow(self, navigator: PacerNavigator, case_details: Dict[str, Any], html_content: str) -> Dict[str, Any]:
        """Orchestrates the entire download workflow for a case."""
        log_prefix = f"[{case_details.get('court_id', 'N/A')}][{case_details.get('docket_num', 'N/A')}]"
        self.log_info(f"{log_prefix} Starting download workflow.")

        should_skip, reason = self._validator.should_skip_download(case_details)

        if should_skip:
            self.log_info(f"{log_prefix} Skipping download: {reason}")
            s3_key = f"{case_details.get('iso_date', 'unknown_date')}/html/{case_details.get('base_filename', 'unknown')}.html"
            s3_link = await self._manager.process_html_only(html_content, s3_key)
            case_details['s3_html'] = s3_link
            case_details['is_downloaded'] = False
        else:
            self.log_info(f"{log_prefix} Proceeding with file download.")
            downloaded_file_path = await self._downloader.download_file(navigator, case_details)

            if downloaded_file_path:
                s3_key = f"{case_details.get('iso_date', 'unknown_date')}/dockets/{Path(downloaded_file_path).name}"
                s3_link = await self._manager.upload_to_s3(downloaded_file_path, s3_key)
                case_details['s3_link'] = s3_link
                case_details['is_downloaded'] = True
            else:
                case_details['is_downloaded'] = False
                case_details['download_error'] = "File download failed."

        return case_details
