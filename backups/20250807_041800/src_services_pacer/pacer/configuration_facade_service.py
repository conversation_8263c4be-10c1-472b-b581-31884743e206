# /src/services/pacer/configuration_facade_service.py
from __future__ import annotations
from typing import Any, Dict, List, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer._config_components.pacer_config_provider import PacerConfigProvider
    from src.infrastructure.protocols.logger import LoggerProtocol

class ConfigurationFacadeService(AsyncServiceBase):
    """Unified interface for PACER configuration."""

    def __init__(self,
                 config_provider: PacerConfigProvider,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        self._provider = config_provider
        self._all_configs = None

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        if action == 'get_all_configs':
            return await self.get_all_configs()
        elif action == 'get_relevance_config':
            return await self.get_relevance_config()
        # Add other config getters here
        else:
            raise PacerServiceError(f"Unknown action for ConfigurationFacadeService: {action}")

    async def get_all_configs(self) -> Dict[str, Any]:
        """Returns all PACER configurations."""
        if not self._all_configs:
            self._all_configs = await self._provider.get_all_configs()
        return self._all_configs

    async def get_relevance_config(self) -> Dict[str, Any]:
        """Returns the relevance configuration."""
        configs = await self.get_all_configs()
        return configs.get("relevance", {})

    async def get_relevant_defendants(self) -> List[str]:
        """Returns the list of relevant defendants."""
        configs = await self.get_all_configs()
        defendants_data = configs.get("relevant_defendants", {})
        return defendants_data.get("defendants", [])
