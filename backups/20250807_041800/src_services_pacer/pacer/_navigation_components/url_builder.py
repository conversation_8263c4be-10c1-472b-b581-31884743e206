# /src/services/pacer/_navigation_components/url_builder.py
from __future__ import annotations
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class UrlBuilder(ComponentImplementation):
    """Builds URLs for PACER pages."""

    BASE_URLS = {
        "main": "https://pacer.login.uscourts.gov",
        "lookup": "https://pacer.uscourts.gov/file-case/court-cmecf-lookup",
    }

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'get_login_url':
            return self.get_login_url()
        elif action == 'get_query_page_url':
            # In a real scenario, this might take a court_id to build a court-specific URL
            return self.get_lookup_url()
        else:
            raise PacerServiceError(f"Unknown action for UrlBuilder: {action}")

    def get_login_url(self) -> str:
        """Returns the main PACER login URL."""
        return f"{self.BASE_URLS['main']}/csologin/login.jsf"

    def get_lookup_url(self) -> str:
        """Returns the court CM/ECF lookup URL."""
        return self.BASE_URLS['lookup']
