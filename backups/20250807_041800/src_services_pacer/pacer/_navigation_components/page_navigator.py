# /src/services/pacer/_navigation_components/page_navigator.py
from __future__ import annotations
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer._browser_components.navigator import PacerNavigator
    from src.pacer._navigation_components.element_locator import ElementLocator
    from src.infrastructure.protocols.logger import LoggerProtocol

class PageNavigator(ComponentImplementation):
    """Handles core page navigation actions like clicking and filling forms."""

    def __init__(self,
                 element_locator: ElementLocator,
                 logger: LoggerProtocol = None,
                 config: Dict[str, Any] = None):
        super().__init__(logger, config)
        self._locator = element_locator

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'go_to_url':
            await self.go_to_url(data['navigator'], data['url'])
        elif action == 'click_element':
            await self.click_element(data['navigator'], data['element_name'])
        elif action == 'fill_element':
            await self.fill_element(data['navigator'], data['element_name'], data['value'])
        else:
            raise PacerServiceError(f"Unknown action for PageNavigator: {action}")

    async def go_to_url(self, navigator: PacerNavigator, url: str):
        """Navigates to a given URL."""
        self.log_info(f"Navigating to {url}")
        await navigator.goto(url)

    async def click_element(self, navigator: PacerNavigator, element_name: str):
        """Clicks a UI element."""
        selector = self._locator.get_selector(element_name)
        self.log_info(f"Clicking element '{element_name}' with selector: {selector}")
        element = await navigator.locator(selector)
        await element.first.click()

    async def fill_element(self, navigator: PacerNavigator, element_name: str, value: str):
        """Fills a form element with a value."""
        selector = self._locator.get_selector(element_name)
        self.log_info(f"Filling element '{element_name}' with value: {value}")
        element = await navigator.locator(selector)
        await element.first.fill(value)
