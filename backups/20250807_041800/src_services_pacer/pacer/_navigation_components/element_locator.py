# /src/services/pacer/_navigation_components/element_locator.py
from __future__ import annotations
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class ElementLocator(ComponentImplementation):
    """Provides CSS selectors for PACER UI elements."""

    SELECTORS = {
        "query_link": "a:text('Query')",
        "case_num_input": "input[name='case_num']",
        "run_query_button": "input[name='button1'][value='Run Query']",
        "docket_report_link": "a:text-matches('/Docket Report/i')",
        "run_report_button": "input[value='Run Report'][type='submit']",
    }

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'get_selector':
            return self.get_selector(data['element_name'])
        else:
            raise PacerServiceError(f"Unknown action for ElementLocator: {action}")

    def get_selector(self, element_name: str) -> str:
        """Returns the selector for a given UI element."""
        if element_name not in self.SELECTORS:
            raise PacerServiceError(f"Selector for '{element_name}' not found.")
        return self.SELECTORS[element_name]
