# /src/services/pacer/court_processing_facade_service.py
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer._processing_components.download_path_manager import \
    DownloadPathManager
from src.pacer._processing_components.workflow_orchestrator import \
    WorkflowOrchestrator


class CourtProcessingFacadeService(AsyncServiceBase):
    """
    Facade service for orchestrating all court-level processing workflows.
    It acts as the main entry point for processing a court, routing tasks
    to the appropriate components.
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        download_path_manager: Optional[DownloadPathManager] = None,
        workflow_orchestrator: Optional[WorkflowOrchestrator] = None,
    ):
        super().__init__(logger, config)
        self.download_path_manager = download_path_manager
        self.workflow_orchestrator = workflow_orchestrator

    async def execute(self, data: Any) -> Any:
        """
        Main execution method for the facade.
        """
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """
        Routes actions to the appropriate components based on processing mode.
        """
        action = data.get("action")
        if action == "handle_court_workflow":
            return await self.handle_court_processing_workflow(**data)
        elif action == "setup_download_path":
            # Expose download path setup directly if needed
            data["action"] = "setup_download_path"
            return await self.download_path_manager.execute(data)
        else:
            raise ValueError(f"Unknown action for CourtProcessingFacadeService: {action}")

    async def handle_court_processing_workflow(self, **kwargs) -> Dict[str, Any]:
        """
        Handles the overall workflow for processing a court based on the mode.
        """
        processing_mode = kwargs.get("processing_mode")
        court_id = kwargs.get("court_id")

        log_prefix = f"[{court_id}] Workflow({processing_mode}):"
        self.log_info(f"{log_prefix} Starting court workflow via facade.")

        if not self.workflow_orchestrator:
            raise ValueError("WorkflowOrchestrator is not initialized.")

        # The processor_config needs the download path.
        # We can create it here and pass it down.
        processor_config = kwargs.get("processor_config", {})
        iso_date = kwargs.get("workflow_config", {}).get("iso_date")

        if iso_date:
             download_path = await self.download_path_manager.execute({
                 "action": "setup_download_path", "court_id": court_id,
                 "iso_date": iso_date, "mode": "report" # default mode for now
             })
             processor_config['context_download_path'] = download_path
             kwargs["processor_config"] = processor_config

        try:
            if processing_mode == 'date_range':
                kwargs["action"] = "process_court_task"
                return await self.workflow_orchestrator.execute(kwargs)
            elif processing_mode == 'specific_dockets':
                kwargs["action"] = "process_multi_docket_task"
                return await self.workflow_orchestrator.execute(kwargs)
            else:
                raise ValueError(f"Unknown processing mode: {processing_mode}")
        except Exception as e:
            self.log_error(f"{log_prefix} Workflow failed in facade: {e}", exc_info=True)
            return {'court_id': court_id, 'status': 'failed', 'error': str(e)}
