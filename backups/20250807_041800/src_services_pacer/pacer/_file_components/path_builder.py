# /src/services/pacer/_file_components/path_builder.py
from __future__ import annotations
from pathlib import Path
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.utils.pacer_standalone import get_default_data_path

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class PathBuilder(ComponentImplementation):
    """Builds paths for various PACER files and directories."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)
        default_data_path = get_default_data_path(self.config)
        self.base_path = Path(self.config.get('data_path', default_data_path))

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        iso_date = data.get('iso_date')
        if not iso_date:
            raise PacerServiceError("iso_date is required for all PathBuilder actions.")

        if action == 'get_date_path':
            return self.get_date_path(iso_date)
        elif action == 'get_screenshots_path':
            return self.get_screenshots_path(iso_date)
        # Add other path building actions here
        else:
            raise PacerServiceError(f"Unknown action for PathBuilder: {action}")

    def get_date_path(self, iso_date: str) -> Path:
        """Returns the base data path for a given date."""
        return self.base_path / iso_date

    def get_screenshots_path(self, iso_date: str) -> Path:
        """Returns the screenshots path for a given date."""
        return self.get_date_path(iso_date) / 'screenshots'

    def get_logs_path(self, iso_date: str) -> Path:
        """Returns the logs path for a given date."""
        return self.get_date_path(iso_date) / 'logs' / 'pacer'

    def get_dockets_path(self, iso_date: str) -> Path:
        """Returns the dockets path for a given date."""
        return self.get_date_path(iso_date) / 'dockets'

    def get_html_path(self, iso_date: str) -> Path:
        """Returns the html path for a given date."""
        return self.get_date_path(iso_date) / 'html'

    def get_docket_report_path(self, iso_date: str) -> Path:
        """Returns the docket report path for a given date."""
        return self.get_date_path(iso_date) / 'logs' / 'docket_report'

    def get_temp_path(self, iso_date: str) -> Path:
        """Returns the temp path for a given date."""
        return self.get_dockets_path(iso_date) / 'temp'
