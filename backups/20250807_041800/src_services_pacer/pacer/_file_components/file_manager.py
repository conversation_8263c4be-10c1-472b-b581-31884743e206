from typing import Any, Dict, List, Optional
import os
import json
from src.infrastructure.patterns.component_base import ComponentImplementation

class FileManager(ComponentImplementation):
    """Handles file operations like reading, writing, and checking existence."""

    async def _execute_action(self, data: Dict[str, Any]) -> Any:
        action = data.get("action")
        if action == "read":
            return self.read(data["filepath"])
        elif action == "write":
            return self.write(data["filepath"], data["content"])
        elif action == "exists":
            return self.exists(data["filepath"])
        else:
            raise ValueError(f"Unknown action: {action}")

    def read(self, filepath: str) -> str:
        """Reads content from a file."""
        with open(filepath, 'r') as f:
            return f.read()

    def write(self, filepath: str, content: str):
        with open(filepath, 'w') as f:
            f.write(content)

    def exists(self, filepath: str) -> bool:
        """Checks if a file exists."""
        return os.path.exists(filepath)

    def read_json(self, filepath: str) -> Dict:
        """Reads a JSON file and returns a dictionary."""
        with open(filepath, 'r') as f:
            return json.load(f)

    def _write_json(self, filepath: str, data: Dict):
        """Writes a dictionary to a JSON file."""
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=4)
