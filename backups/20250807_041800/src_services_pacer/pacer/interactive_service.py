# /src/services/pacer/interactive_service.py
from typing import Any, Dict, List

from rich.console import Console
from rich.prompt import Confirm, Prompt
from rich.table import Table

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError
# TODO: Replace with proper export service when implemented
# TODO: Replace with proper query service when implemented


class PacerInteractiveService(AsyncServiceBase):
    """Service for interactive PACER operations"""

    def __init__(self,
                 logger: Any,
                 query_service: QueryFacadeService,
                 export_facade: ExportFacadeService,
                 config: Dict[str, Any]):
        super().__init__(logger, config or {})
        self.query_service = query_service
        self.export_facade = export_facade
        self.console = Console()

    async def _execute_action(self, data: Any) -> Any:
        """Execute PacerInteractiveService actions."""
        action = data.get('action')
        if action == 'interactive_law_firm_search':
            await self.interactive_law_firm_search()
        elif action == 'interactive_duplicate_scan':
            await self.interactive_duplicate_scan()
        else:
            raise PacerServiceError(f"Unknown action: {action}")
        return None

    async def interactive_law_firm_search(self) -> None:
        """Interactive law firm search"""
        self.console.print("\n[bold cyan]Interactive Law Firm Search[/bold cyan]")
        law_firm = Prompt.ask("Enter law firm name to search for")

        self.console.print(f"\nSearching for law firm '{law_firm}'...")
        result = await self.query_service.search_by_law_firm_with_totals(law_firm)

        self.console.print(f"\n[green]Total filings: {result['total_count']}[/green]")
        if result['mdl_breakdown']:
            self._display_mdl_breakdown(result['mdl_breakdown'])

        if result['results'] and Confirm.ask("\nExport results to CSV?"):
            filename = Prompt.ask("Enter filename", default=f"law_firm_{law_firm.replace(' ', '_')}.csv")
            await self.export_facade.execute({
                'action': 'export_to_csv',
                'data': result['results'],
                'filename': filename
            })
            self.console.print(f"Results exported to [bold]{filename}[/bold]")

    async def interactive_duplicate_scan(self) -> None:
        """Interactive duplicate scan"""
        self.console.print("\n[bold cyan]Scanning for Duplicate Records[/bold cyan]")
        duplicates = await self.query_service.scan_for_duplicates()

        if not duplicates:
            self.console.print("[green]No duplicates found![/green]")
            return

        self.console.print(f"\n[yellow]Found {len(duplicates)} docket(s) with duplicates[/yellow]")
        self._display_duplicates_summary(duplicates)

        if Confirm.ask("\nExport duplicate report to CSV?"):
            filename = Prompt.ask("Enter filename", default="duplicate_report.csv")
            export_data = self._flatten_duplicates_for_export(duplicates)
            await self.export_facade.execute({
                'action': 'export_to_csv',
                'data': export_data,
                'filename': filename
            })
            self.console.print(f"Report exported to [bold]{filename}[/bold]")

    def _display_mdl_breakdown(self, mdl_breakdown: Dict[str, int]):
        self.console.print("\n[bold]Breakdown by MDL:[/bold]")
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("MDL Number")
        table.add_column("Filing Count")
        for mdl, count in sorted(mdl_breakdown.items(), key=lambda x: x[1], reverse=True):
            table.add_row(str(mdl), str(count))
        self.console.print(table)

    def _display_duplicates_summary(self, duplicates: List[Dict]):
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Court ID")
        table.add_column("Docket Number")
        table.add_column("Count")
        table.add_column("Filing Dates")
        for dup in duplicates[:20]:
            filing_dates = ", ".join(dup['filing_dates'][:3])
            if len(dup['filing_dates']) > 3:
                filing_dates += "..."
            table.add_row(dup['court_id'], dup['docket_num'], str(dup['count']), filing_dates)
        self.console.print(table)
        if len(duplicates) > 20:
            self.console.print(f"\n[yellow]... and {len(duplicates) - 20} more.[/yellow]")

    def _flatten_duplicates_for_export(self, duplicates: List[Dict]) -> List[Dict]:
        export_data = []
        for dup in duplicates:
            for i, record in enumerate(dup['records']):
                export_data.append({
                    'CourtId': dup['court_id'],
                    'DocketNum': dup['docket_num'],
                    'DuplicateIndex': i + 1,
                    'FilingDate': record.get('FilingDate'),
                    'Title': record.get('Title'),
                    'AddedOn': record.get('AddedOn')
                })
        return export_data
