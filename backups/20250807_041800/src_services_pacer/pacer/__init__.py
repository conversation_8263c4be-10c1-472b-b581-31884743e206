"""
PACER Services Module

This module provides the new service-oriented architecture for PACER processing,
replacing the old facade pattern with core services.
"""

# Core Services (New Architecture)
from ._core_services.configuration.configuration_service import ConfigurationService
from ._core_services.case_processing.case_processing_service import CaseProcessingService
from ._core_services.relevance.relevance_service import RelevanceService
from ._core_services.verification.verification_service import VerificationService
from ._core_services.download_orchestration.download_orchestration_service import DownloadOrchestrationService
from ._core_services.file_operations.file_operations_service import FileOperationsService
from ._core_services.metrics_reporting.metrics_reporting_service import MetricsReportingService
from ._core_services.s3_management.s3_management_service import S3ManagementService
from ._core_services.registry.service_registry import PacerCoreServiceRegistry
# from ._core_services.registry.service_factory import ServiceFactory  # TODO: Re-enable after fixing dependencies

# Orchestration Services (temporarily disabled for cleanup)
# from .pacer_orchestrator_service import PacerOrchestratorService
# from .interactive_service import InteractiveService

# Job Services (temporarily disabled for cleanup)
# from .jobs.job_orchestration_service import JobOrchestrationService
# from .jobs.job_runner_service import JobRunnerService
# from .jobs.docket_job import DocketProcessingJob

__all__ = [
    # Core Services (New Architecture)
    'ConfigurationService',
    'CaseProcessingService',
    'RelevanceService',
    'VerificationService',
    'DownloadOrchestrationService',
    'FileOperationsService',
    'MetricsReportingService',
    'S3ManagementService',
    'PacerCoreServiceRegistry',
    # 'ServiceFactory',  # TODO: Re-enable after fixing dependencies
    
    # Orchestration Services (temporarily disabled for cleanup)
    # 'PacerOrchestratorService',
    # 'InteractiveService',
    
    # Job Services (temporarily disabled for cleanup)
    # 'JobOrchestrationService',
    # 'JobRunnerService',
    # 'DocketProcessingJob',
]