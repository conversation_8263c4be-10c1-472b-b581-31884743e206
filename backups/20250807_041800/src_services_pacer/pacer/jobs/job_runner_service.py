import asyncio
import logging
from typing import Any, Dict, TYPE_CHECKING

from playwright.async_api import Browser<PERSON>ontext

from src.pacer.jobs.jobs_models import Pacer<PERSON>ob
# TODO: Replace with proper browser service when implemented

if TYPE_CHECKING:
    from src.pacer.pacer_orchestrator_service import PacerOrchestratorService

class PacerJobRunnerService:
    """
    Service responsible for executing a single PacerJob.
    It encapsulates the logic for processing a single court within an isolated browser context.
    
    Follows dependency injection pattern - dependencies are injected via constructor.
    """

    def __init__(
        self, 
        config: Dict[str, Any], 
        logger: logging.Logger, 
        pacer_orchestrator: 'PacerOrchestratorService',
        browser_service_factory: Any = None
    ):
        """
        Initializes the PacerJobRunnerService using dependency injection.

        Args:
            config: Application configuration dictionary.
            logger: Logger instance.
            pacer_orchestrator: An instance of the main PacerOrchestratorService.
            browser_service_factory: Factory for creating browser services (injected via DI).
        """
        self.config = config
        self.logger = logger
        self.pacer_orchestrator = pacer_orchestrator
        self.browser_service_factory = browser_service_factory

    async def run_job(self, job: PacerJob) -> PacerJob:
        """
        Executes a PacerJob, processing a single court.

        Args:
            job: The PacerJob instance to run.

        Returns:
            The job instance, updated with its final status, metrics, and results.
        """
        self.logger.info(f"JobRunner: Starting job {job.job_id} for court {job.court_id}")
        job.start_timer()

        browser_service = None
        context = None
        try:
            # Create a new BrowserService using the injected factory for proper DI
            if self.browser_service_factory:
                browser_service = self.browser_service_factory(
                    headless=job.config_snapshot.get("headless", True),
                    timeout_ms=job.config_snapshot.get("timeout_ms", 60000),
                    config=job.config_snapshot,
                    logger=self.logger,
                )
            else:
                # Fallback to manual creation if factory not available
                browser_service = BrowserService(
                    headless=job.config_snapshot.get("headless", True),
                    timeout_ms=job.config_snapshot.get("timeout_ms", 60000),
                    config=job.config_snapshot,
                    logger=self.logger,
                )
            
            # The orchestrator is now responsible for creating the context with the correct download path
            context = await self.pacer_orchestrator.create_browser_context_with_download_path(
                browser_service,
                job.court_id,
                job.iso_date,
                "docket_log" if job.docket_list_input else "report"
            )

            # The core processing logic is now encapsulated in a new method in the orchestrator
            result = await self.pacer_orchestrator.process_single_court_job(job, context)

            job.results = result
            job.metrics.update(result.get('metrics', {}))
            job.update_status("COMPLETED")
            self.logger.info(f"JobRunner: Successfully completed job {job.job_id} for court {job.court_id}")

        except Exception as e:
            self.logger.error(f"JobRunner: Unhandled exception in job {job.job_id} for court {job.court_id}: {e}", exc_info=True)
            job.set_error(f"Unhandled job execution error: {str(e)}")
        finally:
            if context:
                await context.close()
            if browser_service:
                await browser_service.close()
            job.end_timer()
            self.logger.info(f"JobRunner: Finished job {job.job_id} for court {job.court_id}. Status: {job.status}, Duration: {job.metrics.get('duration_sec', 0):.2f}s")

        return job
