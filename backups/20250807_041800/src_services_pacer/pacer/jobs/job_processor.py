# /src/services/pacer/jobs/job_processor.py
import asyncio
from datetime import datetime
from typing import Any, Dict

from playwright.async_api import Browser<PERSON>ontext, <PERSON><PERSON><PERSON> as Play<PERSON><PERSON><PERSON>r, TimeoutError as PlaywrightTimeoutError

from src.infrastructure.decorators.delay_decorators import with_case_delay
from src.infrastructure.patterns.component_base import ComponentImplementation
from src.pacer.jobs.docket_job import DocketProcessingJob
from src.pacer._core_services.docket_processing_orchestrator import DocketProcessingOrchestrator


class JobProcessor(ComponentImplementation):
    """
    Component responsible for processing a single DocketProcessingJob using core services.
    
    This processor has been transformed to use the DocketProcessingOrchestrator which
    implements the exact workflow from docket_processing.md using all 8 core services.
    """
    
    def __init__(self, 
                 docket_orchestrator: DocketProcessingOrchestrator = None,
                 *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._docket_orchestrator = docket_orchestrator

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a job processing action.

        Args:
            data (Dict): A dictionary containing:
                - job (DocketProcessingJob): The job to process.
                - context (BrowserContext): The browser context to use.

        Returns:
            str: The final status of the job.
        """
        action = data.get("action")
        if action == "process_job":
            job = data.get("job")
            context = data.get("context")
            if not job or not context:
                raise ValueError("Missing 'job' or 'context' for process_job action.")
            return await self._process_job_with_retry(job, context)
        else:
            raise ValueError(f"Unknown action for JobProcessor: {action}")

    async def _process_job_with_retry(
        self, job: DocketProcessingJob, context: BrowserContext
    ) -> str:
        """
        Processes a docket job with a retry mechanism.

        Args:
            job: The DocketProcessingJob to process.
            context: The Playwright BrowserContext.

        Returns:
            The final status of the job as a string.
        """
        log_prefix = f"[{job.court_id}][Job-{job.row_num}]"
        max_retries = self.config.get("max_job_retries", 2)
        retry_delay = self.config.get("job_retry_delay_seconds", 5)

        job.court_logger.info(f"{log_prefix} Starting job processing.")
        for attempt in range(max_retries + 1):
            try:
                await self._process_docket_job(job, context)
                if job.status != "failed":
                    break  # Success, exit retry loop

                if attempt < max_retries:
                    job.court_logger.warning(
                        f"{log_prefix} Attempt {attempt + 1} failed. Retrying in {retry_delay}s..."
                    )
                    await asyncio.sleep(retry_delay)
                else:
                    job.court_logger.error(
                        f"{log_prefix} Job failed after {max_retries + 1} attempts."
                    )
            except Exception as e:
                job.status = "failed"
                job.error_message = str(e)
                job.court_logger.error(f"{log_prefix} Exception on attempt {attempt + 1}: {e}", exc_info=True)
                if attempt >= max_retries:
                    job.court_logger.error(f"{log_prefix} Final attempt failed with exception.")
                    break
                await asyncio.sleep(retry_delay)

        job.court_logger.info(f"{log_prefix} Job finished with final status: {job.status}")
        return job.status

    @with_case_delay(0.1)
    async def _process_docket_job(
        self, job: DocketProcessingJob, context: BrowserContext
    ) -> None:
        """
        Processes a single docket job using the DocketProcessingOrchestrator.
        
        This method has been transformed to use the core services architecture
        through the DocketProcessingOrchestrator which implements the exact
        workflow from docket_processing.md.
        """
        log_prefix = f"[{job.court_id}][Job-{job.row_num}]"
        job.court_logger.info(f"{log_prefix} --- Starting Docket Job with Core Services ---")

        try:
            # Navigate to docket page
            job.page = await context.new_page()
            from urllib.parse import urljoin
            base_pacer_url = f"https://ecf.{job.court_id}.uscourts.gov"
            absolute_href = urljoin(base_pacer_url, job.docket_link_href)
            await job.page.goto(absolute_href, wait_until="domcontentloaded", timeout=30000)
            await job.page.wait_for_load_state("networkidle", timeout=10000)

            # Prepare initial details from the civil report
            initial_details = {
                "court_id": job.court_id,
                "docket_num": job.docket_num,
                "versus": job.initial_versus_text,
                "filing_date": job.initial_filing_date,
                "docket_link": job.docket_link_href,
                "extracted_at": datetime.now().isoformat(),
                "source": "docket_report_log"
            }
            
            # Process the docket using the DocketProcessingOrchestrator
            # This implements the complete workflow from docket_processing.md
            is_explicitly_requested = job.config.get("_processing_explicit_dockets", False)
            
            if self._docket_orchestrator:
                job.court_logger.info(f"{log_prefix} Processing with DocketProcessingOrchestrator")
                
                final_case_details = await self._docket_orchestrator.execute({
                    "action": "process_docket_page",
                    "page": job.page,
                    "initial_details": initial_details,
                    "is_explicitly_requested": is_explicitly_requested
                })
                
                # Update job status based on processing results
                if final_case_details:
                    job.case_details = final_case_details
                    job.base_filename = final_case_details.get('base_filename', 'unknown')
                    
                    # Determine final status
                    if final_case_details.get('_verification_skipped'):
                        job.status = "skipped_verification" 
                    elif final_case_details.get('is_downloaded'):
                        job.status = "success_downloaded"
                    elif final_case_details.get('html_only'):
                        job.status = "success_html_only"
                    else:
                        job.status = "success_no_download"
                        
                    job.court_logger.info(f"{log_prefix} Core services processing completed: {job.status}")
                else:
                    job.status = "failed"
                    job.error_message = "DocketProcessingOrchestrator returned None"
                    job.court_logger.error(f"{log_prefix} Core services processing failed")
            else:
                # Fallback if orchestrator not available
                job.status = "failed"
                job.error_message = "DocketProcessingOrchestrator not available"
                job.court_logger.error(f"{log_prefix} DocketProcessingOrchestrator not available")

        except Exception as e:
            job.status = "failed"
            job.error_message = str(e)
            job.court_logger.error(f"{log_prefix} Job failed: {e}", exc_info=True)
        finally:
            if job.page and not job.page.is_closed():
                await job.page.close()
            job.court_logger.info(f"{log_prefix} --- Finished Docket Job (Status: {job.status}) ---")
