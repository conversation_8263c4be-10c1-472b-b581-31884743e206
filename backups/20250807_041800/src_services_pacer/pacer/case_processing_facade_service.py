# /src/services/pacer/case_processing_facade_service.py
from __future__ import annotations
from typing import Any, Dict, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from playwright.async_api import Page
    from src.infrastructure.protocols.logger import LoggerProtocol
    from src.pacer._case_processing_components.case_validator import CaseValidator
    from src.pacer._case_processing_components.case_parser import CaseParser
    from src.pacer._case_processing_components.case_enricher import CaseEnricher
    from src.pacer._case_processing_components.case_transformer import CaseTransformer

class CaseProcessingFacadeService(AsyncServiceBase):
    """Unified interface for PACER case processing operations."""

    def __init__(self,
                 case_validator: CaseValidator,
                 case_parser: CaseParser,
                 case_enricher: CaseEnricher,
                 case_transformer: CaseTransformer,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        self._validator = case_validator
        self._parser = case_parser
        self._enricher = case_enricher
        self._transformer = case_transformer

    async def execute(self, data: Any) -> Any:
        """Main execution method for the case processing facade."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        if action == 'process_case':
            return await self.process_case(data['page'], data['initial_details'])
        elif action == 'validate_page_content':
            return await self._validator.execute({
                "action": "validate_page_content",
                "page": data['page'],
                "case_details": data['case_details']
            })
        elif action == 'update_case_details':
            return await self.update_case_details(data['html_content'], data['initial_details'])
        elif action == 'create_base_filename':
            return self.create_base_filename(data['case_details'])
        else:
            raise PacerServiceError(f"Unknown action for CaseProcessingFacadeService: {action}")

    async def process_case(self, page: Page, initial_details: Dict[str, Any]) -> Dict[str, Any] | None:
        """
        Orchestrates the entire case processing workflow.
        """
        log_prefix = f"[{initial_details.get('court_id', 'N/A')}][{initial_details.get('docket_num', 'N/A')}]"
        self.log_info(f"{log_prefix} Starting case processing workflow.")

        # 1. Validate page content
        html_content = await self._validator.validate_page_content(page, initial_details)
        if not html_content:
            self.log_error(f"{log_prefix} Case validation failed. Aborting processing.")
            return None

        # 2. Parse HTML
        parsed_details = self._parser.parse_html(html_content)
        if parsed_details.get("_no_proceedings_detected"):
            self.log_info(f"{log_prefix} No proceedings found. Case will not be saved.")
            return None

        # Merge initial details with parsed details - include ALL parsed fields
        case_details = {**initial_details}
        
        # Add case_info fields
        if parsed_details.get('case_info'):
            case_details.update(parsed_details['case_info'])
        
        # Add all parsed fields (plaintiffs, defendants, attorneys, etc.)
        for key in ['plaintiffs', 'defendants', 'attorney', 'plaintiff', 'defendant',
                   'court_name', 'office', 'assigned_to', 'referred_to', 'lead_case',
                   'case_in_other_court', 'jury_demand', 'jurisdiction', 'cause', 'nos',
                   'date_filed', 'date_terminated', 'demand', 'nature_of_suit']:
            if key in parsed_details:
                case_details[key] = parsed_details[key]


        # 3. Enrich case data
        enriched_details = self._enricher.enrich_case(case_details, html_content)

        # 4. Transform case data
        final_details = self._transformer.transform_case(enriched_details)

        self.log_info(f"{log_prefix} Case processing workflow completed successfully.")
        return final_details

    async def update_case_details(self, html_content: str, initial_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Updates case details by parsing HTML content and combining with initial details.
        CRITICAL: Preserves initial details from civil report to avoid parsing UI elements.
        """
        log_prefix = f"[{initial_details.get('court_id', 'N/A')}][{initial_details.get('docket_num', 'N/A')}]"
        
        # Parse the HTML content to extract case details
        parsed_details = await self._parser.execute({
            "action": "parse_html",
            "html_content": html_content
        })
        
        # Check for special cases
        if parsed_details.get("_no_proceedings_detected"):
            self.log_info(f"{log_prefix} No proceedings found in parsed HTML.")
            return {**initial_details, "_no_proceedings_detected": True}
        
        if parsed_details.get("_transaction_receipt_detected"):
            self.log_warning(f"{log_prefix} Transaction receipt detected instead of case details.")
            return {**initial_details, "_transaction_receipt_detected": True}
        
        # Start with initial details from civil report (these are trusted)
        merged_details = {**initial_details}
        
        # Add parsed case_info fields (but preserve initial details)
        if parsed_details.get('case_info'):
            for key, value in parsed_details['case_info'].items():
                # Only add if not already in initial_details
                if key not in merged_details:
                    merged_details[key] = value
        
        # Add parsed fields only if they are not empty and valid
        # This prevents UI elements from overriding good data
        for key in ['plaintiffs', 'defendants', 'attorney', 'plaintiff', 'defendant']:
            if key in parsed_details and parsed_details[key]:
                # Only override if initial_details doesn't have this field
                if key not in merged_details or not merged_details.get(key):
                    merged_details[key] = parsed_details[key]
        
        # Add other parsed fields
        for key in ['court_name', 'office', 'assigned_to', 'referred_to', 'lead_case',
                   'case_in_other_court', 'jury_demand', 'jurisdiction', 'cause', 'nos',
                   'date_filed', 'date_terminated', 'demand', 'nature_of_suit']:
            if key in parsed_details and key not in merged_details:
                merged_details[key] = parsed_details[key]
        
        # Enrich the merged details
        enriched_details = await self._enricher.execute({
            "action": "enrich_case",
            "case_details": merged_details,
            "html_content": html_content
        })
        
        # Transform the enriched details
        final_details = await self._transformer.execute({
            "action": "transform_case",
            "case_details": enriched_details
        })
        
        self.log_info(f"{log_prefix} Case details updated and enriched successfully.")
        return final_details

    def create_base_filename(self, case_details: Dict[str, Any]) -> str:
        """
        Creates a base filename for the case using the transformer.
        """
        transformed_details = self._transformer.transform_case(case_details)
        return transformed_details.get('base_filename', 'unknown_case')