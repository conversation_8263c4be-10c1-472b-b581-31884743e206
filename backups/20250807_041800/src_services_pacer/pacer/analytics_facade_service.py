# /src/services/pacer/analytics_facade_service.py
from __future__ import annotations
from typing import Any, Dict, List, Optional, TYPE_CHECKING
import pandas as pd

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer._analytics_components.mdl_summarizer import MdlSummarizer
    from src.repositories.pacer_repository import PacerRepository
    from src.infrastructure.protocols.logger import LoggerProtocol

class AnalyticsFacadeService(AsyncServiceBase):
    """Unified interface for PACER data analytics."""

    def __init__(self,
                 mdl_summarizer: MdlSummarizer,
                 repository: PacerRepository,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        self._summarizer = mdl_summarizer
        self._repo = repository

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        if action == 'get_mdl_summary':
            records = await self._repo.query_by_filing_date(data['date_str'])
            return self._summarizer.summarize_mdl(records)
        elif action == 'get_unique_law_firms':
            return await self.get_unique_law_firms()
        else:
            raise PacerServiceError(f"Unknown action for AnalyticsFacadeService: {action}")

    async def get_unique_law_firms(self) -> List[str]:
        """Extracts all unique law firm names from the database."""
        records = await self._repo.scan_all()
        unique_firms = set()
        for record in records:
            law_firm_fields = ['LawFirm', 'LawFirms', 'PlaintiffAttorneys']
            for field in law_firm_fields:
                value = record.get(field)
                if isinstance(value, str) and value:
                    unique_firms.add(value.strip())
                elif isinstance(value, list):
                    for firm in value:
                        if isinstance(firm, str) and firm:
                            unique_firms.add(firm.strip())
        return sorted(list(unique_firms))
