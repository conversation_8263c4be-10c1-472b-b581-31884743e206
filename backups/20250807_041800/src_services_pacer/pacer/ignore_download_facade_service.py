# /src/services/pacer/ignore_download_facade_service.py
from __future__ import annotations
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class IgnoreDownloadFacadeService(AsyncServiceBase):
    """Facade for managing ignore_download configuration and logic."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)
        self.ignore_download_config: List[Dict[str, Any]] = []
        self._courts_with_ignore_download: Set[str] = set()
        self._load_ignore_download_config()

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'should_ignore_download':
            return self.should_ignore_download(data['case_details'])
        else:
            raise PacerServiceError(f"Unknown action for IgnoreDownloadFacadeService: {action}")

    def should_ignore_download(self, case_details: Dict[str, Any]) -> bool:
        """Checks if a case should be ignored for download."""
        if not self.ignore_download_config:
            return False

        court_id = case_details.get('court_id', '')
        if court_id not in self._courts_with_ignore_download:
            return False

        # Simplified logic for demonstration. A full implementation would check flags, defendants, etc.
        for entry in self.ignore_download_config:
            if entry.get('court_id') == court_id:
                # In a real scenario, more complex matching logic would be here.
                # For now, if there's any entry for the court, we'll consider it a match.
                self.log_info(f"Case in court {court_id} matches an ignore_download rule.")
                return True
        return False

    def _load_ignore_download_config(self):
        """Loads the ignore_download.json configuration file."""
        try:
            # Assuming config dir is provided in the main config
            config_dir = Path(self.config.get('config_dir', 'src/config/pacer'))
            ignore_download_path = config_dir / 'ignore_download' / 'ignore_download.json'

            if not ignore_download_path.exists():
                self.log_warning(f"ignore_download.json not found at: {ignore_download_path}")
                return

            with open(ignore_download_path, 'r', encoding='utf-8') as f:
                self.ignore_download_config = json.load(f)

            self._courts_with_ignore_download = {
                entry.get('court_id') for entry in self.ignore_download_config if entry.get('court_id')
            }
            self.log_info(f"Loaded {len(self.ignore_download_config)} ignore_download entries.")
        except Exception as e:
            self.log_error(f"Error loading ignore_download configuration: {e}")
            self.ignore_download_config = []
            self._courts_with_ignore_download = set()
