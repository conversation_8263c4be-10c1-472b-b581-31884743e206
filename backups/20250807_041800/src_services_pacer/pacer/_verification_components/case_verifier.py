# /src/services/pacer/_verification_components/case_verifier.py
from __future__ import annotations
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.utils.docket_utils import normalize_docket_number

if TYPE_CHECKING:
    from src.repositories.pacer_repository import PacerRepository
    from src.pacer._file_components.file_manager import FileManager
    from src.infrastructure.protocols.logger import LoggerProtocol

class CaseVerifier(ComponentImplementation):
    """Verifies if a case should be processed."""

    def __init__(self,
                 repository: PacerRepository,
                 file_manager: FileManager,
                 logger: LoggerProtocol = None,
                 config: Dict[str, Any] = None):
        super().__init__(logger, config)
        self._repo = repository
        self._file_manager = file_manager

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'verify_case':
            return await self.verify_case(data['case_details'])
        else:
            raise PacerServiceError(f"Unknown action for CaseVerifier: {action}")

    async def verify_case(self, case_details: Dict[str, Any]) -> bool:
        """
        Verifies if a case should be processed by checking for its existence.
        Returns True if the case should be processed, False otherwise.
        """
        court_id = case_details.get('court_id')
        docket_num = case_details.get('docket_num')

        if not court_id or not docket_num:
            self.log_warning("Missing court_id or docket_num for verification.")
            return False

        # 1. Check database
        db_docket_num = normalize_docket_number(docket_num)
        if await self._repo.check_docket_exists(court_id, db_docket_num):
            self.log_info(f"Case {court_id}:{docket_num} already exists in the database.")
            return False

        # 2. Check local files (simplified for this example)
        # In a real implementation, this would be more robust.
        # For now, we assume if it's not in the DB, it needs processing.

        self.log_info(f"Case {court_id}:{docket_num} does not exist. Needs processing.")

    async def check_database_gsi(self, case_details: Dict[str, Any]) -> bool:
        """Check if case exists in database using GSI."""
        court_id = case_details.get('court_id')
        docket_num = case_details.get('docket_num')

        if not court_id or not docket_num:
            self.log_warning('Missing court_id or docket_num for GSI check.')
            return False

        # Use existing repository check
        from src.utils.docket_utils import normalize_docket_number
        db_docket_num = normalize_docket_number(docket_num)
        return await self._repo.check_docket_exists(court_id, db_docket_num)

    async def check_local_artifacts_pdf_zip(self, case_details: Dict[str, Any]) -> bool:
        """Check for local PDF/ZIP artifacts only (for explicitly requested cases)."""
        # This is a simplified implementation - in practice would check specific file types
        return await self._file_manager.check_local_files(case_details, file_types=['pdf', 'zip'])
    
    async def check_all_local_files(self, case_details: Dict[str, Any]) -> bool:
        """Check for any local files (for report-scraped cases)."""
        # This checks for any existing files
        return await self._file_manager.check_local_files(case_details, file_types=None)

        return True
