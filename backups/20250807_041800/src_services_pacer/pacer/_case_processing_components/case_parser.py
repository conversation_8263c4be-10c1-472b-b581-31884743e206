# /src/services/pacer/_case_processing_components/case_parser.py
from __future__ import annotations
import re
from typing import Any, Dict, List, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.services.html.case_parser_service import CaseParserService

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class CaseParser(ComponentImplementation):
    """Parses case details from PACER HTML content using the full CaseParserService."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def execute(self, data: Any) -> Any:
        """Main execution method for the case parser."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'parse_html':
            return self.parse_html(data['html_content'])
        else:
            raise PacerServiceError(f"Unknown action for CaseParser: {action}")
    
    def _is_ui_element(self, text: str) -> bool:
        """Check if the text is a UI element that should not be extracted as a party name."""
        ui_elements = [
            "docket sheet", "case number", "document options", "sort by",
            "date filed", "date range", "document type", "run report",
            "pacer service center", "transaction receipt", "client code",
            "search", "clear", "submit", "reset", "print", "download",
            "view", "close", "next", "previous", "first", "last"
        ]
        text_lower = text.lower().strip()
        return any(ui in text_lower for ui in ui_elements)
    
    def _filter_ui_elements(self, items: List) -> List:
        """Filter out UI elements from extracted data."""
        filtered = []
        for item in items:
            if isinstance(item, dict):
                name = item.get("name", "")
                if name and not self._is_ui_element(name):
                    filtered.append(item)
            elif isinstance(item, str):
                if item and not self._is_ui_element(item):
                    filtered.append(item)
        return filtered

    def parse_html(self, html_content: str) -> Dict[str, Any]:
        """
        Parses the HTML content using the full CaseParserService to extract all case details.
        
        Returns:
            Dictionary containing:
            - case_info: Court metadata and case details
            - plaintiffs: List of plaintiff information
            - defendants: List of defendant information  
            - attorney: List of attorney details
            - Additional fields from the parser
        """
        try:
            # Use the full CaseParserService for comprehensive extraction
            parser = CaseParserService(self.logger, html_content)
            
            # Parse with preprocessing and fallback enabled for better extraction
            parsed_data = parser.parse(enable_preprocessing=True, enable_fallback=True)
            
            # Check for special cases
            if parsed_data.get("case_info", {}).get("no_proceedings", False):
                return {"_no_proceedings_detected": True}
            
            if parsed_data.get("case_info", {}).get("transaction_receipt", False):
                self.log_warning("Transaction receipt page detected instead of case details")
                return {"_transaction_receipt_detected": True}
            
            # Extract and normalize key fields that are needed
            # CRITICAL: Filter out UI elements that might be incorrectly parsed as party names
            result = {
                "case_info": parsed_data.get("case_info", {}),
                "plaintiffs": self._filter_ui_elements(parsed_data.get("plaintiffs", [])),
                "defendants": self._filter_ui_elements(parsed_data.get("defendants", [])),
                "attorney": parsed_data.get("attorney", []),
            }
            
            # Extract plaintiff and defendant names as simple lists if available
            if result["plaintiffs"]:
                result["plaintiff"] = [p.get("name", "") for p in result["plaintiffs"] if p.get("name") and not self._is_ui_element(p.get("name"))]
            
            if result["defendants"]:
                result["defendant"] = [d.get("name", "") for d in result["defendants"] if isinstance(d, dict) and d.get("name") and not self._is_ui_element(d.get("name"))]
                if not result["defendant"] and result["defendants"]:
                    # Handle case where defendants might be strings
                    result["defendant"] = [d for d in result["defendants"] if isinstance(d, str) and not self._is_ui_element(d)]
            
            # Add any additional fields from the parser
            for key in ["court_name", "office", "assigned_to", "referred_to", "lead_case", 
                       "case_in_other_court", "jury_demand", "jurisdiction", "cause", "nos",
                       "date_filed", "date_terminated", "demand", "nature_of_suit"]:
                if key in parsed_data:
                    result[key] = parsed_data[key]
            
            self.log_debug(f"Parsed case details: {len(result.get('plaintiffs', []))} plaintiffs, "
                          f"{len(result.get('defendants', []))} defendants, "
                          f"{len(result.get('attorney', []))} attorneys")
            
            return result
            
        except Exception as e:
            self.log_error(f"Error parsing HTML content: {e}", exc_info=True)
            # Return minimal structure on error
            return {
                "case_info": {},
                "plaintiffs": [],
                "defendants": [],
                "attorney": [],
                "_parse_error": str(e)
            }