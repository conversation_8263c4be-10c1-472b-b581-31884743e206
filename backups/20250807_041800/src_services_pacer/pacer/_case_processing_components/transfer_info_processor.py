# /src/services/pacer/_case_processing_components/transfer_info_processor.py
from typing import Any, Dict

from src.infrastructure.patterns.component_base import ComponentImplementation


class TransferInfoProcessor(ComponentImplementation):
    """
    Component for processing case transfer information to ensure compatibility
    with case transfer handlers.
    """

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes the transfer info processing action.

        Args:
            data (Dict): A dictionary containing:
                - case_details (Dict): The case data to process for transfer info.

        Returns:
            Dict[str, Any]: The updated case data with transfer compatibility fields.
        """
        action = data.get("action")
        if action == "process_transfer_info":
            case_details = data.get("case_details")
            if not case_details:
                self.log_warning("No case_details provided for transfer info processing.")
                return {}
            return self._process_transfer_compatibility(case_details)
        else:
            raise ValueError(f"Unknown action: {action}")

    def _process_transfer_compatibility(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processes transfer information for compatibility with the case transfer handler.

        Maps `case_in_other_court` to the `transferred_from` field and extracts
        transferor court and docket information.

        Args:
            case_details: Case details to update.

        Returns:
            Updated case details.
        """
        try:
            case_in_other_court = case_details.get('case_in_other_court', '')

            if case_in_other_court and case_in_other_court.strip():
                # Set transferred_from field for handler compatibility
                case_details['transferred_from'] = case_in_other_court

                # Process detailed transfer information
                if ',' in case_in_other_court:
                    parts = case_in_other_court.split(',', 1)
                    transferor_court_name = parts[0].strip()
                    transferor_docket_num = parts[1].strip()

                    case_details['transferor_court'] = transferor_court_name
                    case_details['transferor_court_docket_num'] = transferor_docket_num

                    self.log_info(
                        "Processed transfer info: "
                        f"Court='{transferor_court_name}', "
                        f"Docket='{transferor_docket_num}'"
                    )
            return case_details
        except Exception as e:
            self.log_error(f"Error processing transfer compatibility: {e}")
            # Return original details on error to avoid data corruption
            return case_details
