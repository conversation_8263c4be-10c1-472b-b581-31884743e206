# /src/services/pacer/_case_processing_components/case_validator.py
from __future__ import annotations
import hashlib
import re
from typing import Any, Dict, TYPE_CHECKING

from playwright.async_api import TimeoutError as PlaywrightTimeoutError

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.utils.docket_utils import normalize_docket_number

if TYPE_CHECKING:
    from playwright.async_api import Page
    from src.infrastructure.protocols.logger import LoggerProtocol

class CaseValidator(ComponentImplementation):
    """Validates that the page content is a valid case docket sheet."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'validate_page_content':
            return await self.validate_page_content(data['page'], data['case_details'])
        else:
            raise PacerServiceError(f"Unknown action for CaseValidator: {action}")

    async def validate_page_content(self, page: Page, case_details: dict[str, Any]) -> str | None:
        """
        Waits for page content to load and validates it contains expected case data.
        Returns the HTML content if successful, None otherwise.
        """
        log_prefix = f"[{case_details.get('court_id', 'N/A')}][{case_details.get('docket_num', 'N/A')}]"
        self.log_info(f"{log_prefix} Validating page content.")

        try:
            await page.locator("body").first.wait_for(state="visible", timeout=15000)
            await page.wait_for_load_state("domcontentloaded", timeout=5000)
        except PlaywrightTimeoutError as e:
            self.log_error(f"{log_prefix} Timeout waiting for page to load: {e}")
            return None

        html_content = await page.content()

        # Basic validation
        if not self._is_content_valid(html_content, case_details):
             self.log_error(f"{log_prefix} Page content validation failed.")
             return None

        self.log_info(f"{log_prefix} Page content validation successful.")
        return html_content

    def _is_content_valid(self, html_content: str, case_details: dict[str, Any]) -> bool:
        """Performs a series of checks to validate the HTML content."""
        expected_docket_num = case_details.get('docket_num', '').strip()
        if not expected_docket_num:
            self.log_warning("No expected docket number provided for validation.")
            return False

        docket_pattern = r'(\d+:\d+-[a-zA-Z]{2}-\d{5})'
        found_dockets = re.findall(docket_pattern, html_content)

        if not found_dockets:
            self.log_warning("No docket numbers found in HTML content.")
            return False

        expected_normalized = normalize_docket_number(expected_docket_num)
        found_normalized = [normalize_docket_number(d) for d in found_dockets]

        if expected_normalized not in found_normalized:
            self.log_error(f"DOCKET MISMATCH: Expected {expected_normalized}, found {found_normalized}")
            return False

        return True
