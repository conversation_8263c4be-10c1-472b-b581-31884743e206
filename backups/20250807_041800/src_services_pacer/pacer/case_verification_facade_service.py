# /src/services/pacer/case_verification_facade_service.py
from __future__ import annotations
from typing import Any, Dict, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer._verification_components.case_verifier import CaseVerifier
    from src.infrastructure.protocols.logger import LoggerProtocol

class CaseVerificationFacadeService(AsyncServiceBase):
    """Unified interface for PACER case verification."""

    def __init__(self,
                 case_verifier: CaseVerifier,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        self._verifier = case_verifier

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        if action == 'verify_case':
            return await self.verify_case(data['case_details'])
        else:
            raise PacerServiceError(f"Unknown action for CaseVerificationFacadeService: {action}")

    async def verify_case(self, case_details: Dict[str, Any]) -> bool:
        """
        Verifies if a case should be processed.
        Returns True if the case should be processed, False otherwise.
        """
        self.log_info(f"Verifying case: {case_details.get('docket_num')}")
        return await self._verifier.verify_case(case_details)
