# /src/services/pacer/_processing_components/report_processor.py
import asyncio
import logging
from datetime import date as DateType
from typing import Any, Dict, List, Optional

from playwright.async_api import B<PERSON>er<PERSON><PERSON>x<PERSON>, <PERSON>, Error as PlaywrightError

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.pacer.jobs.job_processor import JobProcessor
from src.pacer.jobs.docket_job import DocketProcessingJob
from src.pacer._core_services.relevance.relevance_service import RelevanceService
from src.utils.date import DateUtils


class ReportProcessor(ComponentImplementation):
    """
    Component for processing a report page from PACER. It iterates through
    report rows, creates processing jobs, and manages their execution.
    """

    def __init__(
        self,
        logger: Any = None,
        config: Dict[str, Any] = None,
        job_processor: Any = None,
        pacer_repository: Any = None,
        file_manager: Any = None,
        case_processing_service: Any = None,
        download_orchestration_service: Any = None,
        file_operations_service: Any = None,
    ):
        super().__init__(logger, config)
        self.job_processor = job_processor
        self.pacer_repository = pacer_repository
        self.file_manager = file_manager
        # These services are passed down to the job
        self.case_processing_service = case_processing_service
        self.download_orchestration_service = download_orchestration_service
        self.file_operations_service = file_operations_service

    async def execute(self, data: Any) -> Any:
        """Main execution method for the report processor."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a report processing action.

        Args:
            data (Dict): A dictionary containing action parameters.
                - original_page (Page)
                - court_id (str)
                - iso_date (str)
                - start_date_obj (DateType)
                - end_date_obj (DateType)
                - context (BrowserContext)
                - processor_config (Dict)
                - relevance_engine (RelevanceService)
                - court_logger (logging.Logger)

        Returns:
            Dict[str, int]: A dictionary with counts of job outcomes.
        """
        action = data.get("action")
        if action == "process_report":
            return await self.process_report_rows(**data)
        else:
            raise ValueError(f"Unknown action for ReportProcessor: {action}")

    async def process_report_rows(self, **kwargs) -> Dict[str, int]:
        """
        Processes all rows from a PACER report by creating and dispatching
        isolated DocketProcessingJob objects for each row.
        """
        original_page = kwargs.get("original_page")
        court_id = kwargs.get("court_id")
        iso_date = kwargs.get("iso_date")
        start_date_obj = kwargs.get("start_date_obj")
        end_date_obj = kwargs.get("end_date_obj")
        context = kwargs.get("context")
        processor_config = kwargs.get("processor_config")
        relevance_engine = kwargs.get("relevance_engine")
        court_logger = kwargs.get("court_logger")

        log_prefix = f"[{court_id}] ReportProc:"
        self.log_info(f"{log_prefix} Starting report row processing.")

        job_config = self.config.copy()
        job_config["iso_date"] = iso_date
        if processor_config:
            job_config.update(processor_config)

        tasks = []
        row_counts = self._initialize_row_counts()

        rows_selector = "//*[@id='cmecfMainContent']/table[@border='1']/tbody/tr[position() > 1]"
        all_row_locators = await original_page.locator(rows_selector).all()
        row_counts["total_rows"] = len(all_row_locators)
        self.log_info(f"{log_prefix} Found {row_counts['total_rows']} rows to process.")

        for i, row_locator in enumerate(all_row_locators):
            try:
                job = await self._create_job_from_row(
                    row_locator, i + 1, row_counts, court_id,
                    start_date_obj, end_date_obj, iso_date,
                    relevance_engine, job_config, processor_config
                )
                if job:
                    row_counts["jobs_created"] += 1
                    tasks.append(
                        asyncio.create_task(
                            self.job_processor.execute({"action": "process_job", "job": job, "context": context})
                        )
                    )
            except Exception as e:
                self.log_error(f"{log_prefix} Critical error creating job for row {i+1}: {e}", exc_info=True)
                row_counts["failed"] += 1

        self.log_info(f"{log_prefix} All {len(tasks)} job tasks created. Awaiting completion.")

        if tasks:
            job_statuses = await asyncio.gather(*tasks, return_exceptions=True)
            self._tally_job_statuses(job_statuses, row_counts)

        self.log_info(f"{log_prefix} Finished processing all rows. Final counts: {row_counts}")
        return row_counts

    def _initialize_row_counts(self) -> Dict[str, int]:
        return {
            "total_rows": 0, "jobs_created": 0, "success_downloaded": 0,
            "success_html_only": 0, "success_no_download": 0,
            "skipped_relevance": 0, "skipped_exists": 0, "failed": 0,
            "skipped_date_range": 0,
        }

    async def _create_job_from_row(
        self, row_locator, row_num, row_counts, court_id,
        start_date_obj, end_date_obj, iso_date,
        relevance_engine, job_config, processor_config
    ) -> Optional[DocketProcessingJob]:
        log_prefix = f"[{court_id}] Row-{row_num}:"

        docket_link_loc = row_locator.locator("a[href*='DktRpt.pl']")
        if await docket_link_loc.count() == 0:
            self.log_warning(f"{log_prefix} Missing DktRpt.pl link, skipping.")
            return None

        docket_link_href = await docket_link_loc.first.get_attribute("href")
        versus_text = (await row_locator.locator("b").all_text_contents() or ["N/A"])[0]
        date_text = (await row_locator.locator("td").nth(1).all_text_contents() or ["N/A"])[0]
        filing_date_str = date_text.replace("Case filed:", "").strip()

        if not self._is_date_in_range(filing_date_str, start_date_obj, end_date_obj, log_prefix):
            row_counts["skipped_date_range"] += 1
            return None

        docket_num = (await docket_link_loc.first.all_text_contents() or [""])[0].strip()
        if not docket_num:
            self.log_warning(f"{log_prefix} Missing docket number, skipping.")
            return None

        if self.pacer_repository and await self.pacer_repository.check_docket_exists(court_id, docket_num):
            self.log_info(f"{log_prefix} Docket {docket_num} already exists, skipping.")
            row_counts["skipped_exists"] += 1
            return None

        cause_text = (await row_locator.locator("td").nth(2).all_text_contents() or [""])[0].lower()
        is_removal = any(term in cause_text for term in ["petition for removal", "notice of removal"])

        prelim_details = {
            "court_id": court_id, "docket_num": docket_num, "versus": versus_text,
            "filing_date": filing_date_str, "cause": cause_text, "is_removal": is_removal,
            "added_date_iso": iso_date
        }

        if relevance_engine and relevance_engine._determine_case_review_status(prelim_details):
            self.log_info(f"{log_prefix} Irrelevant based on prelim data. Reason: {prelim_details.get('_reason_review')}")
            if self.file_manager:
                await self.file_manager.save_review_case_details(prelim_details)
            row_counts["skipped_relevance"] += 1
            return None

        return DocketProcessingJob(
            court_id=court_id, row_num=row_num, total_rows=row_counts["total_rows"],
            docket_num=docket_num,  # CRITICAL: Pass docket number to job
            docket_link_href=docket_link_href, initial_versus_text=versus_text,
            initial_filing_date=filing_date_str, relevance_engine=relevance_engine,
            config=job_config, processor_config=processor_config or {},
            file_management_service=None,  # This seems unused in the job itself
            download_orchestration_service=self.download_orchestration_service,
            case_processing_service=self.case_processing_service,
            file_operations_service=self.file_operations_service,
            file_manager=self.file_manager,
            is_removal_case=is_removal
        )

    def _is_date_in_range(self, date_str, start_date, end_date, log_prefix):
        if not date_str:
            self.log_warning(f"{log_prefix} Missing filing date, proceeding but may be invalid.")
            return True
        try:
            filing_date = DateUtils.parse_date(date_str)
            if not filing_date: return True # Assume valid if unparsable
            # Convert datetime objects to dates for comparison
            start_date_cmp = start_date.date() if hasattr(start_date, 'date') else start_date
            end_date_cmp = end_date.date() if hasattr(end_date, 'date') else end_date
            filing_date_cmp = filing_date.date() if hasattr(filing_date, 'date') else filing_date
            return start_date_cmp <= filing_date_cmp <= end_date_cmp
        except ValueError:
            self.log_warning(f"{log_prefix} Invalid date format '{date_str}', proceeding.")
            return True

    def _tally_job_statuses(self, statuses: List[Any], counts: Dict[str, int]):
        for status in statuses:
            if isinstance(status, Exception):
                self.log_error(f"A job task failed with an unhandled exception: {status}", exc_info=True)
                counts["failed"] += 1
            elif isinstance(status, str) and status in counts:
                counts[status] += 1
            else:
                self.log_error(f"Unknown or invalid job status '{status}' received. Counting as failed.")
                counts["failed"] += 1
