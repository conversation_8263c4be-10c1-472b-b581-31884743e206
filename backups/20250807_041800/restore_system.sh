#\!/bin/bash
# Restoration script for PACER system backup
# Created: 20250807_041800

set -e

BACKUP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$BACKUP_DIR/../../.." && pwd)"

echo "Restoring PACER system from backup..."
echo "Backup location: $BACKUP_DIR"
echo "Project root: $PROJECT_ROOT"

# Restore git state
cd "$PROJECT_ROOT"
git checkout $(cat "$BACKUP_DIR/git_branch/current_branch.txt" | grep '*' | awk '{print $2}')

# Restore PACER directories
rm -rf src/services/pacer src/pacer
cp -r "$BACKUP_DIR/src_services_pacer/pacer" src/services/
cp -r "$BACKUP_DIR/src_pacer/pacer" src/

echo "System restoration completed successfully"
