# /src/services/pacer/_core_services/browser/auth_manager.py

"""
Authentication Manager for Browser Service.

Consolidates authentication components (login_handler, session_manager, ecf_login_handler)
into a unified authentication management module within the Browser Service.
"""

from __future__ import annotations
import asyncio
from typing import Any, Dict, Optional, TYPE_CHECKING
from datetime import datetime, timedelta

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError, AuthenticationError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol
    from playwright.async_api import Page


class AuthenticationManager(ComponentImplementation):
    """
    Manages all authentication operations for the Browser Service.
    
    Consolidates functionality from:
    - LoginHandler: Core PACER login operations
    - SessionManager: Session state management
    - ECFLoginHandler: ECF-specific login workflows
    - CredentialValidator: Credential validation
    """
    
    # Login configuration
    LOGIN_URL = 'https://pacer.login.uscourts.gov/csologin/login.jsf'
    USERNAME_SELECTOR = "#loginForm\\:loginName"
    PASSWORD_SELECTOR = "#loginForm\\:password"
    LOGIN_BUTTON_SELECTOR = "#loginForm\\:fbtnLogin"
    LOGIN_SUCCESS_INDICATOR_SELECTOR = "#logoutForm\\:courtId"
    
    # ECF login configuration
    ECF_USERNAME_SELECTOR = 'input[name="login"]'
    ECF_PASSWORD_SELECTOR = 'input[name="key"]'
    ECF_SUBMIT_SELECTOR = 'input[type="submit"][name="button1"]'
    
    # Session configuration
    SESSION_TIMEOUT = timedelta(minutes=30)
    
    def __init__(self, logger: Optional[LoggerProtocol] = None, config: Optional[Dict] = None):
        """
        Initialize the Authentication Manager.
        
        Args:
            logger: Logger instance for structured logging
            config: Configuration dictionary
        """
        super().__init__(logger, config)
        
        # Session state management
        self._authenticated_sessions: Dict[str, bool] = {}
        self._session_cookies: Dict[str, Any] = {}
        self._last_activity: Dict[str, datetime] = {}
        
        # Login synchronization
        self._main_login_lock = asyncio.Lock()
        self._ecf_login_locks: Dict[str, asyncio.Lock] = {}
        
        # Credential caching
        self._credentials_cache: Dict[str, Dict[str, str]] = {}

    async def _execute_action(self, data: Any) -> Any:
        """Execute authentication-related actions."""
        action = data.get('action')
        
        action_map = {
            'perform_login': self.perform_login,
            'perform_ecf_login': self.perform_ecf_login,
            'validate_session': self.validate_session,
            'refresh_session': self.refresh_session,
            'logout': self.logout,
            'get_credentials': self.get_credentials,
            'validate_credentials': self.validate_credentials,
            'save_session_state': self.save_session_state,
            'restore_session_state': self.restore_session_state,
            'clear_session': self.clear_session
        }
        
        handler = action_map.get(action)
        if not handler:
            raise PacerServiceError(f"Unknown action for AuthenticationManager: {action}")
        
        return await handler(data)

    async def perform_login(self, data: Dict[str, Any]) -> bool:
        """
        Perform main PACER login with synchronization.
        
        Args:
            data: Contains page instance and optional credentials
            
        Returns:
            Success status
        """
        page = data.get('page')
        if not page:
            raise ValueError("Page instance required for login")
        
        log_prefix = "[MainPACERLogin]"
        context_id = data.get('context_id', 'default')
        
        async with self._main_login_lock:
            self.log_info(f"{log_prefix} Acquired main login lock for context {context_id}")
            
            # Get credentials
            credentials = await self.get_credentials(data)
            username = credentials.get('username')
            password = credentials.get('password')
            
            if not username or not password:
                self.log_error(f"{log_prefix} PACER credentials not found")
                return False
            
            try:
                # Navigate to login page
                await page.goto(self.LOGIN_URL, wait_until='networkidle')
                
                # Check if already logged in
                if await self._check_login_status(page):
                    self.log_info(f"{log_prefix} Already logged in")
                    self._authenticated_sessions[context_id] = True
                    self._last_activity[context_id] = datetime.now()
                    return True
                
                self.log_info(f"{log_prefix} Performing main PACER login...")
                
                # Clear and fill login fields with delays to prevent form conflicts
                await page.fill(self.USERNAME_SELECTOR, "")
                await asyncio.sleep(0.5)
                await page.fill(self.USERNAME_SELECTOR, username)
                await asyncio.sleep(0.5)
                
                await page.fill(self.PASSWORD_SELECTOR, "")
                await asyncio.sleep(0.5)
                await page.fill(self.PASSWORD_SELECTOR, password)
                await asyncio.sleep(0.5)
                
                # Submit login form
                await page.click(self.LOGIN_BUTTON_SELECTOR)
                await page.wait_for_load_state('networkidle')
                
                # Verify login success
                try:
                    await page.wait_for_selector(self.LOGIN_SUCCESS_INDICATOR_SELECTOR, timeout=10000)
                    
                    # Store session information
                    self._authenticated_sessions[context_id] = True
                    self._last_activity[context_id] = datetime.now()
                    await self._store_session_cookies(page, context_id)
                    
                    self.log_info(f"{log_prefix} Main PACER login successful")
                    return True
                    
                except Exception as e:
                    self.log_error(f"{log_prefix} Main PACER login failed: {e}")
                    await self._save_debug_screenshot(page, "main_pacer_login_fail")
                    return False
                    
            except Exception as e:
                self.log_error(f"{log_prefix} Login error: {str(e)}", exc_info=True)
                return False

    async def perform_ecf_login(self, data: Dict[str, Any]) -> bool:
        """
        Perform ECF-specific login for a court.
        
        Args:
            data: Contains page, court_id, and optional credentials
            
        Returns:
            Success status
        """
        page = data.get('page')
        court_id = data.get('court_id')
        
        if not page or not court_id:
            raise ValueError("Page and court_id required for ECF login")
        
        log_prefix = f"[ECF-{court_id}]"
        
        # Get or create court-specific lock
        if court_id not in self._ecf_login_locks:
            self._ecf_login_locks[court_id] = asyncio.Lock()
        
        async with self._ecf_login_locks[court_id]:
            self.log_info(f"{log_prefix} Performing ECF login")
            
            # Get ECF credentials
            credentials = await self.get_credentials({
                **data,
                'credential_type': 'ecf',
                'court_id': court_id
            })
            
            username = credentials.get('username')
            password = credentials.get('password')
            
            if not username or not password:
                self.log_error(f"{log_prefix} ECF credentials not found")
                return False
            
            try:
                # Fill ECF login form
                await page.fill(self.ECF_USERNAME_SELECTOR, username)
                await asyncio.sleep(0.3)
                await page.fill(self.ECF_PASSWORD_SELECTOR, password)
                await asyncio.sleep(0.3)
                
                # Submit form
                await page.click(self.ECF_SUBMIT_SELECTOR)
                await page.wait_for_load_state('networkidle')
                
                # Verify ECF login success
                if await self._verify_ecf_login(page, court_id):
                    self.log_info(f"{log_prefix} ECF login successful")
                    return True
                else:
                    self.log_error(f"{log_prefix} ECF login verification failed")
                    await self._save_debug_screenshot(page, f"ecf_login_fail_{court_id}")
                    return False
                    
            except Exception as e:
                self.log_error(f"{log_prefix} ECF login error: {str(e)}", exc_info=True)
                return False

    async def validate_session(self, data: Dict[str, Any]) -> bool:
        """
        Validate if a session is still active.
        
        Args:
            data: Contains context_id
            
        Returns:
            Session validity status
        """
        context_id = data.get('context_id', 'default')
        
        # Check if authenticated
        if context_id not in self._authenticated_sessions:
            return False
        
        if not self._authenticated_sessions[context_id]:
            return False
        
        # Check session timeout
        last_activity = self._last_activity.get(context_id)
        if last_activity:
            if datetime.now() - last_activity > self.SESSION_TIMEOUT:
                self.log_warning(f"Session timeout for context {context_id}")
                await self.clear_session({'context_id': context_id})
                return False
        
        # Update last activity
        self._last_activity[context_id] = datetime.now()
        return True

    async def refresh_session(self, data: Dict[str, Any]) -> bool:
        """
        Refresh an existing session to prevent timeout.
        
        Args:
            data: Contains page and context_id
            
        Returns:
            Refresh success status
        """
        page = data.get('page')
        context_id = data.get('context_id', 'default')
        
        if not page:
            return False
        
        try:
            # Navigate to a simple page to refresh session
            await page.goto('https://pacer.uscourts.gov/pacer/findcase.jsf', 
                          wait_until='networkidle')
            
            # Check if still logged in
            if await self._check_login_status(page):
                self._last_activity[context_id] = datetime.now()
                self.log_info(f"Session refreshed for context {context_id}")
                return True
            else:
                # Session expired, need to re-login
                self.log_warning(f"Session expired for context {context_id}, re-login required")
                return await self.perform_login(data)
                
        except Exception as e:
            self.log_error(f"Session refresh error: {str(e)}")
            return False

    async def logout(self, data: Dict[str, Any]) -> bool:
        """
        Perform logout operation.
        
        Args:
            data: Contains page and context_id
            
        Returns:
            Logout success status
        """
        page = data.get('page')
        context_id = data.get('context_id', 'default')
        
        try:
            if page:
                # Navigate to logout URL
                await page.goto('https://pacer.login.uscourts.gov/csologin/logout.jsf')
                await page.wait_for_load_state('networkidle')
            
            # Clear session data
            await self.clear_session({'context_id': context_id})
            
            self.log_info(f"Logged out successfully for context {context_id}")
            return True
            
        except Exception as e:
            self.log_error(f"Logout error: {str(e)}")
            return False

    async def get_credentials(self, data: Dict[str, Any]) -> Dict[str, str]:
        """
        Get credentials for authentication.
        
        Args:
            data: Contains credential_type and optional court_id
            
        Returns:
            Dictionary with username and password
        """
        credential_type = data.get('credential_type', 'main')
        court_id = data.get('court_id')
        
        # Check cache first
        cache_key = f"{credential_type}_{court_id}" if court_id else credential_type
        if cache_key in self._credentials_cache:
            return self._credentials_cache[cache_key]
        
        # Get from configuration
        if credential_type == 'ecf' and court_id:
            username = self.config.get(f'ecf_username_{court_id}') or self.config.get('ecf_username')
            password = self.config.get(f'ecf_password_{court_id}') or self.config.get('ecf_password')
        else:
            username = self.config.get('username_prod') or self.config.get('pacer_username')
            password = self.config.get('password_prod') or self.config.get('pacer_password')
        
        credentials = {
            'username': username,
            'password': password
        }
        
        # Cache credentials
        self._credentials_cache[cache_key] = credentials
        
        return credentials

    async def validate_credentials(self, data: Dict[str, Any]) -> bool:
        """
        Validate if credentials are available and properly formatted.
        
        Args:
            data: Contains credential_type and optional court_id
            
        Returns:
            Validation status
        """
        credentials = await self.get_credentials(data)
        
        username = credentials.get('username')
        password = credentials.get('password')
        
        if not username or not password:
            self.log_error("Missing credentials")
            return False
        
        # Basic validation
        if len(username) < 3 or len(password) < 3:
            self.log_error("Invalid credential format")
            return False
        
        return True

    async def save_session_state(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Save current session state for persistence.
        
        Args:
            data: Contains context_id
            
        Returns:
            Session state dictionary
        """
        context_id = data.get('context_id', 'default')
        
        state = {
            'context_id': context_id,
            'authenticated': self._authenticated_sessions.get(context_id, False),
            'last_activity': self._last_activity.get(context_id, datetime.now()).isoformat(),
            'cookies': self._session_cookies.get(context_id, {})
        }
        
        return state

    async def restore_session_state(self, data: Dict[str, Any]) -> bool:
        """
        Restore a saved session state.
        
        Args:
            data: Session state dictionary
            
        Returns:
            Restoration success status
        """
        context_id = data.get('context_id', 'default')
        
        try:
            self._authenticated_sessions[context_id] = data.get('authenticated', False)
            
            last_activity_str = data.get('last_activity')
            if last_activity_str:
                self._last_activity[context_id] = datetime.fromisoformat(last_activity_str)
            
            cookies = data.get('cookies')
            if cookies:
                self._session_cookies[context_id] = cookies
            
            self.log_info(f"Session state restored for context {context_id}")
            return True
            
        except Exception as e:
            self.log_error(f"Session restoration error: {str(e)}")
            return False

    async def clear_session(self, data: Dict[str, Any]) -> bool:
        """
        Clear session data for a context.
        
        Args:
            data: Contains context_id
            
        Returns:
            Clear success status
        """
        context_id = data.get('context_id', 'default')
        
        # Clear all session data
        if context_id in self._authenticated_sessions:
            del self._authenticated_sessions[context_id]
        
        if context_id in self._session_cookies:
            del self._session_cookies[context_id]
        
        if context_id in self._last_activity:
            del self._last_activity[context_id]
        
        self.log_info(f"Session cleared for context {context_id}")
        return True

    # Helper methods
    async def _check_login_status(self, page: Page) -> bool:
        """Check if currently logged in."""
        try:
            # Check for logout button or user indicator
            await page.wait_for_selector(self.LOGIN_SUCCESS_INDICATOR_SELECTOR, timeout=3000)
            return True
        except:
            return False

    async def _verify_ecf_login(self, page: Page, court_id: str) -> bool:
        """Verify ECF login success."""
        try:
            # Check for ECF-specific success indicators
            current_url = page.url
            
            # Check if redirected away from login page
            if 'login' not in current_url.lower():
                return True
            
            # Check for error messages
            error_text = await page.text_content('body')
            if 'invalid' in error_text.lower() or 'error' in error_text.lower():
                return False
            
            return True
            
        except Exception as e:
            self.log_warning(f"ECF login verification error: {str(e)}")
            return False

    async def _store_session_cookies(self, page: Page, context_id: str) -> None:
        """Store session cookies for later use."""
        try:
            cookies = await page.context.cookies()
            self._session_cookies[context_id] = cookies
        except Exception as e:
            self.log_warning(f"Could not store session cookies: {str(e)}")

    async def _save_debug_screenshot(self, page: Page, name: str) -> None:
        """Save a debug screenshot."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"debug_{name}_{timestamp}.png"
            await page.screenshot(path=f"/tmp/{filename}")
            self.log_debug(f"Debug screenshot saved: {filename}")
        except Exception as e:
            self.log_warning(f"Could not save debug screenshot: {str(e)}")