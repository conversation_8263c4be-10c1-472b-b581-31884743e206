# /src/services/pacer/_core_services/browser/navigation_manager.py

"""
Navigation Manager for Browser Service.

Consolidates navigation components (page_navigator, element_locator, url_builder)
into a unified navigation management module within the Browser Service.
"""

from __future__ import annotations
import asyncio
from typing import Any, Dict, List, Optional, TYPE_CHECKING
from urllib.parse import urljoin, urlparse, parse_qs, urlencode

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError, NavigationError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol
    from playwright.async_api import Page, Locator


class NavigationManager(ComponentImplementation):
    """
    Manages navigation operations for the Browser Service.
    
    Consolidates functionality from:
    - PageNavigator: Page navigation and interaction
    - ElementLocator: Element finding and waiting
    - URLBuilder: URL construction and manipulation
    """
    
    # PACER URL patterns
    PACER_BASE_URL = "https://pacer.uscourts.gov"
    ECF_URL_PATTERN = "https://ecf.{court}.uscourts.gov"
    
    # Common PACER paths
    PATHS = {
        'login': '/csologin/login.jsf',
        'find_case': '/pacer/findcase.jsf',
        'docket_report': '/cgi-bin/DktRpt.pl',
        'query': '/cgi-bin/qryDocument.pl',
        'history': '/cgi-bin/HistDocQry.pl',
        'case_search': '/cgi-bin/iquery.pl'
    }
    
    # Wait strategies
    WAIT_STRATEGIES = {
        'load': 'load',
        'domcontentloaded': 'domcontentloaded',
        'networkidle': 'networkidle',
        'commit': 'commit'
    }
    
    def __init__(self, logger: Optional[LoggerProtocol] = None, config: Optional[Dict] = None):
        """
        Initialize the Navigation Manager.
        
        Args:
            logger: Logger instance for structured logging
            config: Configuration dictionary
        """
        super().__init__(logger, config)
        
        # Navigation configuration
        self._default_timeout = self.config.get('navigation_timeout', 30000)
        self._default_wait_strategy = self.config.get('wait_strategy', 'networkidle')
        self._retry_enabled = self.config.get('retry_navigation', True)
        self._max_retries = self.config.get('max_navigation_retries', 3)
        
        # URL cache for optimization
        self._url_cache: Dict[str, str] = {}

    async def _execute_action(self, data: Any) -> Any:
        """Execute navigation-related actions."""
        action = data.get('action')
        
        action_map = {
            # URL operations
            'build_url': self.build_url,
            'build_ecf_url': self.build_ecf_url,
            'parse_url': self.parse_url,
            'get_current_url': self.get_current_url,
            
            # Navigation operations
            'navigate_to': self.navigate_to,
            'navigate_to_docket': self.navigate_to_docket,
            'navigate_to_query': self.navigate_to_query,
            'navigate_to_case_search': self.navigate_to_case_search,
            
            # Element location
            'find_element': self.find_element,
            'find_elements': self.find_elements,
            'wait_for_element': self.wait_for_element,
            'element_exists': self.element_exists,
            'element_visible': self.element_visible,
            
            # Element interaction
            'click_and_wait': self.click_and_wait,
            'fill_and_submit': self.fill_and_submit,
            'select_and_continue': self.select_and_continue,
            
            # Page state
            'wait_for_load': self.wait_for_load,
            'wait_for_url': self.wait_for_url,
            'is_error_page': self.is_error_page,
            'extract_error_message': self.extract_error_message
        }
        
        handler = action_map.get(action)
        if not handler:
            raise PacerServiceError(f"Unknown action for NavigationManager: {action}")
        
        return await handler(data)

    # URL Operations
    async def build_url(self, data: Dict[str, Any]) -> str:
        """
        Build a PACER URL.
        
        Args:
            data: Contains path and optional params
            
        Returns:
            Complete URL
        """
        path = data.get('path', '')
        params = data.get('params', {})
        base_url = data.get('base_url', self.PACER_BASE_URL)
        
        # Build base URL
        if path.startswith('http'):
            url = path
        else:
            url = urljoin(base_url, path)
        
        # Add parameters
        if params:
            parsed = urlparse(url)
            existing_params = parse_qs(parsed.query)
            existing_params.update(params)
            
            query_string = urlencode(existing_params, doseq=True)
            url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
            if query_string:
                url = f"{url}?{query_string}"
        
        # Cache URL
        cache_key = f"{path}_{str(params)}"
        self._url_cache[cache_key] = url
        
        return url

    async def build_ecf_url(self, data: Dict[str, Any]) -> str:
        """
        Build an ECF court-specific URL.
        
        Args:
            data: Contains court_id and path
            
        Returns:
            ECF URL
        """
        court_id = data.get('court_id')
        path = data.get('path', '')
        params = data.get('params', {})
        
        if not court_id:
            raise ValueError("court_id required for ECF URL")
        
        # Build ECF base URL
        base_url = self.ECF_URL_PATTERN.format(court=court_id)
        
        # Use build_url for the rest
        return await self.build_url({
            'base_url': base_url,
            'path': path,
            'params': params
        })

    async def parse_url(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse a URL into components.
        
        Args:
            data: Contains url
            
        Returns:
            Parsed URL components
        """
        url = data.get('url')
        if not url:
            return {}
        
        parsed = urlparse(url)
        params = parse_qs(parsed.query)
        
        return {
            'scheme': parsed.scheme,
            'netloc': parsed.netloc,
            'path': parsed.path,
            'params': params,
            'fragment': parsed.fragment
        }

    async def get_current_url(self, data: Dict[str, Any]) -> Optional[str]:
        """
        Get the current page URL.
        
        Args:
            data: Contains page
            
        Returns:
            Current URL
        """
        page = data.get('page')
        if not page:
            return None
        
        return page.url

    # Navigation Operations
    async def navigate_to(self, data: Dict[str, Any]) -> bool:
        """
        Navigate to a URL with retry logic.
        
        Args:
            data: Contains page, url, and optional wait_strategy
            
        Returns:
            Navigation success status
        """
        page = data.get('page')
        url = data.get('url')
        wait_strategy = data.get('wait_strategy', self._default_wait_strategy)
        timeout = data.get('timeout', self._default_timeout)
        
        if not page or not url:
            raise ValueError("page and url required for navigation")
        
        retries = 0
        while retries <= self._max_retries:
            try:
                self.log_info(f"Navigating to {url} (attempt {retries + 1})")
                
                response = await page.goto(
                    url,
                    wait_until=wait_strategy,
                    timeout=timeout
                )
                
                if response and response.ok:
                    self.log_info(f"Successfully navigated to {url}")
                    return True
                
                self.log_warning(f"Navigation response not OK: {response.status if response else 'No response'}")
                
                # Check for specific error conditions
                if await self.is_error_page({'page': page}):
                    error_msg = await self.extract_error_message({'page': page})
                    self.log_error(f"Error page detected: {error_msg}")
                    return False
                
            except asyncio.TimeoutError:
                self.log_warning(f"Navigation timeout (attempt {retries + 1})")
            except Exception as e:
                self.log_warning(f"Navigation error (attempt {retries + 1}): {str(e)}")
            
            retries += 1
            if retries <= self._max_retries and self._retry_enabled:
                wait_time = 2 ** retries  # Exponential backoff
                self.log_info(f"Retrying navigation in {wait_time} seconds")
                await asyncio.sleep(wait_time)
        
        self.log_error(f"Navigation failed after {self._max_retries} attempts")
        return False

    async def navigate_to_docket(self, data: Dict[str, Any]) -> bool:
        """
        Navigate to docket report page.
        
        Args:
            data: Contains page, court_id, case_number
            
        Returns:
            Navigation success status
        """
        page = data.get('page')
        court_id = data.get('court_id')
        case_number = data.get('case_number')
        
        if not all([page, court_id, case_number]):
            raise ValueError("page, court_id, and case_number required")
        
        # Build docket URL
        url = await self.build_ecf_url({
            'court_id': court_id,
            'path': self.PATHS['docket_report'],
            'params': {
                'case_num': case_number
            }
        })
        
        return await self.navigate_to({
            'page': page,
            'url': url
        })

    async def navigate_to_query(self, data: Dict[str, Any]) -> bool:
        """
        Navigate to query page.
        
        Args:
            data: Contains page, court_id, and query parameters
            
        Returns:
            Navigation success status
        """
        page = data.get('page')
        court_id = data.get('court_id')
        query_params = data.get('query_params', {})
        
        if not all([page, court_id]):
            raise ValueError("page and court_id required")
        
        # Build query URL
        url = await self.build_ecf_url({
            'court_id': court_id,
            'path': self.PATHS['query'],
            'params': query_params
        })
        
        return await self.navigate_to({
            'page': page,
            'url': url
        })

    async def navigate_to_case_search(self, data: Dict[str, Any]) -> bool:
        """
        Navigate to case search page.
        
        Args:
            data: Contains page and court_id
            
        Returns:
            Navigation success status
        """
        page = data.get('page')
        court_id = data.get('court_id')
        
        if not all([page, court_id]):
            raise ValueError("page and court_id required")
        
        # Build case search URL
        url = await self.build_ecf_url({
            'court_id': court_id,
            'path': self.PATHS['case_search']
        })
        
        return await self.navigate_to({
            'page': page,
            'url': url
        })

    # Element Location
    async def find_element(self, data: Dict[str, Any]) -> Optional[Locator]:
        """
        Find a single element on the page.
        
        Args:
            data: Contains page and selector
            
        Returns:
            Element locator or None
        """
        page = data.get('page')
        selector = data.get('selector')
        
        if not page or not selector:
            return None
        
        try:
            return page.locator(selector).first
        except:
            return None

    async def find_elements(self, data: Dict[str, Any]) -> List[Locator]:
        """
        Find multiple elements on the page.
        
        Args:
            data: Contains page and selector
            
        Returns:
            List of element locators
        """
        page = data.get('page')
        selector = data.get('selector')
        
        if not page or not selector:
            return []
        
        try:
            locator = page.locator(selector)
            count = await locator.count()
            return [locator.nth(i) for i in range(count)]
        except:
            return []

    async def wait_for_element(self, data: Dict[str, Any]) -> bool:
        """
        Wait for an element to appear.
        
        Args:
            data: Contains page, selector, and optional timeout
            
        Returns:
            Element found status
        """
        page = data.get('page')
        selector = data.get('selector')
        timeout = data.get('timeout', self._default_timeout)
        state = data.get('state', 'visible')  # visible, attached, detached, hidden
        
        if not page or not selector:
            return False
        
        try:
            await page.wait_for_selector(selector, timeout=timeout, state=state)
            return True
        except:
            return False

    async def element_exists(self, data: Dict[str, Any]) -> bool:
        """
        Check if an element exists on the page.
        
        Args:
            data: Contains page and selector
            
        Returns:
            Element existence status
        """
        page = data.get('page')
        selector = data.get('selector')
        
        if not page or not selector:
            return False
        
        try:
            count = await page.locator(selector).count()
            return count > 0
        except:
            return False

    async def element_visible(self, data: Dict[str, Any]) -> bool:
        """
        Check if an element is visible.
        
        Args:
            data: Contains page and selector
            
        Returns:
            Element visibility status
        """
        page = data.get('page')
        selector = data.get('selector')
        
        if not page or not selector:
            return False
        
        try:
            element = page.locator(selector).first
            return await element.is_visible()
        except:
            return False

    # Element Interaction
    async def click_and_wait(self, data: Dict[str, Any]) -> bool:
        """
        Click an element and wait for navigation.
        
        Args:
            data: Contains page, selector, and optional wait_strategy
            
        Returns:
            Click success status
        """
        page = data.get('page')
        selector = data.get('selector')
        wait_strategy = data.get('wait_strategy', self._default_wait_strategy)
        
        if not page or not selector:
            return False
        
        try:
            # Click and wait for navigation
            async with page.expect_navigation(wait_until=wait_strategy):
                await page.click(selector)
            
            self.log_info(f"Clicked {selector} and waited for navigation")
            return True
            
        except Exception as e:
            self.log_warning(f"Click and wait error: {str(e)}")
            return False

    async def fill_and_submit(self, data: Dict[str, Any]) -> bool:
        """
        Fill a form field and submit the form.
        
        Args:
            data: Contains page, selector, value, and submit_selector
            
        Returns:
            Submit success status
        """
        page = data.get('page')
        selector = data.get('selector')
        value = data.get('value', '')
        submit_selector = data.get('submit_selector')
        
        if not all([page, selector, submit_selector]):
            return False
        
        try:
            # Fill the field
            await page.fill(selector, value)
            await asyncio.sleep(0.3)  # Small delay for form validation
            
            # Submit the form
            async with page.expect_navigation():
                await page.click(submit_selector)
            
            self.log_info(f"Filled {selector} and submitted form")
            return True
            
        except Exception as e:
            self.log_warning(f"Fill and submit error: {str(e)}")
            return False

    async def select_and_continue(self, data: Dict[str, Any]) -> bool:
        """
        Select an option and continue.
        
        Args:
            data: Contains page, selector, value, and continue_selector
            
        Returns:
            Continue success status
        """
        page = data.get('page')
        selector = data.get('selector')
        value = data.get('value')
        continue_selector = data.get('continue_selector')
        
        if not all([page, selector, value, continue_selector]):
            return False
        
        try:
            # Select option
            await page.select_option(selector, value)
            await asyncio.sleep(0.3)
            
            # Click continue
            async with page.expect_navigation():
                await page.click(continue_selector)
            
            self.log_info(f"Selected {value} and continued")
            return True
            
        except Exception as e:
            self.log_warning(f"Select and continue error: {str(e)}")
            return False

    # Page State
    async def wait_for_load(self, data: Dict[str, Any]) -> bool:
        """
        Wait for page to load.
        
        Args:
            data: Contains page and optional wait_strategy
            
        Returns:
            Load complete status
        """
        page = data.get('page')
        wait_strategy = data.get('wait_strategy', self._default_wait_strategy)
        
        if not page:
            return False
        
        try:
            await page.wait_for_load_state(wait_strategy)
            return True
        except:
            return False

    async def wait_for_url(self, data: Dict[str, Any]) -> bool:
        """
        Wait for URL to match a pattern.
        
        Args:
            data: Contains page, url_pattern, and optional timeout
            
        Returns:
            URL match status
        """
        page = data.get('page')
        url_pattern = data.get('url_pattern')
        timeout = data.get('timeout', self._default_timeout)
        
        if not page or not url_pattern:
            return False
        
        try:
            await page.wait_for_url(url_pattern, timeout=timeout)
            return True
        except:
            return False

    async def is_error_page(self, data: Dict[str, Any]) -> bool:
        """
        Check if the current page is an error page.
        
        Args:
            data: Contains page
            
        Returns:
            Error page status
        """
        page = data.get('page')
        if not page:
            return False
        
        try:
            # Check for common error indicators
            error_indicators = [
                'text=Error',
                'text=Access Denied',
                'text=Session Expired',
                'text=Not Found',
                'text=Internal Server Error',
                '.error-message',
                '#error-content',
                '[class*="error"]'
            ]
            
            for indicator in error_indicators:
                if await page.locator(indicator).count() > 0:
                    return True
            
            # Check page title
            title = await page.title()
            if any(err in title.lower() for err in ['error', 'denied', 'not found']):
                return True
            
            return False
            
        except:
            return False

    async def extract_error_message(self, data: Dict[str, Any]) -> Optional[str]:
        """
        Extract error message from the page.
        
        Args:
            data: Contains page
            
        Returns:
            Error message or None
        """
        page = data.get('page')
        if not page:
            return None
        
        try:
            # Try common error message selectors
            error_selectors = [
                '.error-message',
                '#error-message',
                '[class*="error-text"]',
                '.alert-danger',
                '.message-error'
            ]
            
            for selector in error_selectors:
                if await page.locator(selector).count() > 0:
                    return await page.locator(selector).first.text_content()
            
            # Fall back to looking for text containing "error"
            error_elements = await page.locator('text=/error/i').all_text_contents()
            if error_elements:
                return error_elements[0]
            
            return None
            
        except:
            return None