"""
File Operations Service

Handles file operations, directory management, and data persistence for PACER processing.
Implements the FileOperationsServiceInterface from base_interfaces.py
"""

import json
import csv
from pathlib import Path
from typing import Any, Dict, List
from datetime import datetime

from src.infrastructure.patterns.component_base import AsyncServiceBase
from ..shared.base_interfaces import FileOperationsServiceInterface


class FileOperationsService(AsyncServiceBase, FileOperationsServiceInterface):
    """Service for handling file operations and data persistence."""
    
    def __init__(self, config_service=None, s3_service=None, logger=None):
        super().__init__(logger=logger)
        self.config_service = config_service
        self.s3_service = s3_service
        self.base_directory = None
        self.s3_enabled = False
    
    async def initialize(self) -> None:
        """Initialize the file operations service."""
        await super().initialize()
        
        if self.config_service:
            file_config = await self.config_service.get_config_value(
                'file_operations', 'storage', {}
            )
            self.base_directory = Path(file_config.get('base_directory', './data'))
            self.s3_enabled = file_config.get('s3_enabled', False)
        else:
            self.base_directory = Path('./data')
        
        # Ensure base directory exists
        self.base_directory.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"FileOperationsService initialized with base directory: {self.base_directory}")
    
    async def setup_directories(self, iso_date: str) -> None:
        """Setup required directory structure for a given date."""
        try:
            date_dir = self.base_directory / iso_date
            date_dir.mkdir(parents=True, exist_ok=True)
            
            # Create court-specific subdirectories
            courts_dir = date_dir / 'courts'
            courts_dir.mkdir(exist_ok=True)
            
            # Create reports directory
            reports_dir = date_dir / 'reports'
            reports_dir.mkdir(exist_ok=True)
            
            # Create exports directory
            exports_dir = date_dir / 'exports'
            exports_dir.mkdir(exist_ok=True)
            
            self.logger.info(f"Directory structure created for date: {iso_date}")
            
        except Exception as e:
            self.logger.error(f"Error setting up directories for {iso_date}: {str(e)}")
            raise
    
    async def save_case_data(self, case_data: Dict[str, Any], iso_date: str) -> str:
        """Save case data to appropriate location."""
        try:
            # Ensure directories exist
            await self.setup_directories(iso_date)
            
            # Extract case details for filename generation
            case_number = case_data.get('case_number', 'unknown_case')
            court_code = case_data.get('court_code', 'unknown_court')
            
            # Clean filename components
            clean_case_number = self._clean_filename_component(case_number)
            clean_court_code = self._clean_filename_component(court_code)
            
            # Create court-specific directory
            court_dir = self.base_directory / iso_date / 'courts' / clean_court_code
            court_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime('%H%M%S')
            filename = f"{clean_case_number}_{timestamp}.json"
            file_path = court_dir / filename
            
            # Save data as JSON
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(case_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"Case data saved to: {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"Error saving case data: {str(e)}")
            raise
    
    async def upload_to_s3(self, file_path: str, s3_key: str) -> str:
        """Upload file to S3 and return URL."""
        try:
            if not self.s3_enabled:
                self.logger.warning("S3 upload requested but S3 is not enabled")
                return ""
            
            if not self.s3_service:
                self.logger.error("S3 service not available for upload")
                raise ValueError("S3 service not configured")
            
            # Use S3 service to upload
            s3_url = await self.s3_service.upload_file(file_path, s3_key)
            
            self.logger.info(f"File uploaded to S3: {s3_url}")
            return s3_url
            
        except Exception as e:
            self.logger.error(f"Error uploading to S3: {str(e)}")
            raise
    
    async def export_to_csv(self, data: List[Dict[str, Any]], output_path: str) -> str:
        """Export data to CSV format."""
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            if not data:
                self.logger.warning("No data to export to CSV")
                # Create empty CSV with headers
                with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['No data available'])
                return str(output_file)
            
            # Get all unique keys from all records
            all_keys = set()
            for record in data:
                all_keys.update(record.keys())
            
            fieldnames = sorted(list(all_keys))
            
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for record in data:
                    # Flatten nested dictionaries and handle special types
                    flat_record = self._flatten_record(record)
                    writer.writerow(flat_record)
            
            self.logger.info(f"Data exported to CSV: {output_file}")
            return str(output_file)
            
        except Exception as e:
            self.logger.error(f"Error exporting to CSV: {str(e)}")
            raise
    
    async def generate_report(self, report_type: str, data: List[Dict[str, Any]]) -> str:
        """Generate formatted report."""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_filename = f"{report_type}_report_{timestamp}.json"
            
            reports_dir = self.base_directory / 'reports'
            reports_dir.mkdir(parents=True, exist_ok=True)
            report_path = reports_dir / report_filename
            
            # Generate report metadata
            report = {
                'report_type': report_type,
                'generated_at': datetime.now().isoformat(),
                'record_count': len(data),
                'data': data
            }
            
            # Add report-specific metadata
            if report_type == 'processing_summary':
                report['summary'] = self._generate_processing_summary(data)
            elif report_type == 'error_analysis':
                report['error_analysis'] = self._generate_error_analysis(data)
            
            # Save report
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"Report generated: {report_path}")
            return str(report_path)
            
        except Exception as e:
            self.logger.error(f"Error generating report: {str(e)}")
            raise
    
    # Private helper methods
    def _clean_filename_component(self, component: str) -> str:
        """Clean a filename component to be filesystem-safe."""
        if not component:
            return "unknown"
        
        # Remove or replace problematic characters
        import re
        clean = re.sub(r'[<>:"/\\|?*]', '_', str(component))
        clean = re.sub(r'[^\w\-_.]', '_', clean)
        return clean[:50]  # Limit length
    
    def _flatten_record(self, record: Dict[str, Any]) -> Dict[str, str]:
        """Flatten nested dictionaries for CSV export."""
        flat = {}
        
        for key, value in record.items():
            if isinstance(value, dict):
                # Flatten nested dictionary
                for nested_key, nested_value in value.items():
                    flat_key = f"{key}.{nested_key}"
                    flat[flat_key] = str(nested_value)
            elif isinstance(value, list):
                # Convert list to string
                flat[key] = '; '.join(str(item) for item in value)
            else:
                flat[key] = str(value) if value is not None else ''
        
        return flat
    
    def _generate_processing_summary(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate processing summary statistics."""
        total_cases = len(data)
        successful_cases = len([d for d in data if d.get('status') == 'success'])
        failed_cases = total_cases - successful_cases
        
        return {
            'total_cases': total_cases,
            'successful_cases': successful_cases,
            'failed_cases': failed_cases,
            'success_rate': (successful_cases / total_cases * 100) if total_cases > 0 else 0
        }
    
    def _generate_error_analysis(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate error analysis from data."""
        errors = []
        error_counts = {}
        
        for record in data:
            if record.get('status') == 'error' or record.get('errors'):
                record_errors = record.get('errors', [])
                errors.extend(record_errors)
                
                for error in record_errors:
                    error_type = str(error).split(':')[0] if ':' in str(error) else str(error)
                    error_counts[error_type] = error_counts.get(error_type, 0) + 1
        
        return {
            'total_errors': len(errors),
            'unique_error_types': len(error_counts),
            'error_frequency': error_counts
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Return service health status."""
        return {
            'service': 'FileOperationsService',
            'status': 'healthy' if self.is_initialized() else 'not_initialized',
            'base_directory': str(self.base_directory),
            'base_directory_exists': self.base_directory.exists() if self.base_directory else False,
            's3_enabled': self.s3_enabled,
            'has_s3_service': self.s3_service is not None
        }