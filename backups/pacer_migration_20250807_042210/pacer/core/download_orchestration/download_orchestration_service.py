# /src/services/pacer/_core_services/download_orchestration/download_orchestration_service.py

"""
Download Orchestration Service for PACER processing.

Consolidates DownloadOrchestrationFacadeService into a unified service that handles 
all download workflow orchestration following PHASE 4: Download Workflow from docket_processing.md.
"""

from __future__ import annotations
from typing import Any, Dict, List, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError
# Interface removed for simplicity - TODO: Add back when interfaces are properly organized

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol
    from playwright.async_api import Page


class DownloadOrchestrationService(AsyncServiceBase):
    """
    Consolidated download orchestration service for PACER processing.
    
    This service implements PHASE 4 of the docket processing lifecycle:
    Download Workflow - process_download_workflow method that:
    1. Validates case for download
    2. Determines if download should be attempted
    3. Prepares download context
    4. Executes document download
    5. Handles success/failure scenarios
    """

    def __init__(self,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        
        # Dependencies will be injected
        self._download_validator = None
        self._file_downloader = None
        self._download_manager = None
        self._s3_manager = None
        self._authentication_service = None
        self._navigation_service = None

    async def _initialize_service(self) -> None:
        """Initialize the download orchestration service."""
        # Get injected dependencies
        self._download_validator = self.get_dependency('download_validator')
        self._file_downloader = self.get_dependency('file_downloader')
        self._download_manager = self.get_dependency('download_manager')
        self._s3_manager = self.get_dependency('s3_manager')
        self._authentication_service = self.get_dependency('authentication_service')
        self._navigation_service = self.get_dependency('navigation_service')
        
        self.log_info("Download orchestration service initialized successfully")

    def _get_logger_context(self, court_id: str = "", docket_num: str = "") -> Dict[str, Any]:
        """Get logger context with consistent [court_id][docket_num] prefix format."""
        context = {"service": "DownloadOrchestrationService"}
        if court_id or docket_num:
            context["case_prefix"] = f"[{court_id}][{docket_num}]"
        return context

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        court_id = data.get('court_id', '')
        docket_num = data.get('docket_num', '')
        
        context = self._get_logger_context(court_id, docket_num)
        self.log_debug(f"Executing download orchestration action: {action}", context)
        
        if action == 'process_download_workflow':
            return await self.process_download_workflow(
                data['case_details'],
                data.get('is_explicitly_requested', False),
                data.get('page'),
                court_id=court_id,
                docket_num=docket_num
            )
        elif action == 'execute_download_workflow':
            return await self.execute_download_workflow(data['case_details'])
        elif action == 'authenticate_session':
            return await self.authenticate_session(data['credentials'])
        elif action == 'navigate_to_case':
            return await self.navigate_to_case(data['case_details'])
        elif action == 'execute_query':
            return await self.execute_query(data['query_params'])
        elif action == 'upload_html_to_s3':
            return await self.upload_html_to_s3(
                data['base_filename'], 
                data['html_content'], 
                data['iso_date']
            )
        else:
            raise PacerServiceError(f"Unknown action for DownloadOrchestrationService: {action}")

    async def process_download_workflow(self, 
                                      case_details: Dict[str, Any],
                                      is_explicitly_requested: bool = False,
                                      page: Optional[Page] = None,
                                      court_id: str = "",
                                      docket_num: str = "") -> Dict[str, Any]:
        """
        PHASE 4: Download Workflow
        
        Implements the download workflow from docket_processing.md:
        P4_1: Download Orchestration Service.process_download_workflow
        P4_2: Validate Case for Download
        P4_3: Should Attempt Download?
        P4_4: Prepare Download Context
        P4_5: Execute Document Download
        P4_6: Handle Success/Failure
        
        Args:
            case_details: Case details dictionary
            is_explicitly_requested: Whether this is an explicit request
            page: Browser page object
            court_id: Court identifier for logging
            docket_num: Docket number for logging
            
        Returns:
            Updated case details with download results
        """
        context = self._get_logger_context(court_id, docket_num)
        self.log_info("Starting PHASE 4: Download Workflow", context)
        
        # Make a copy to avoid modifying the original
        updated_case_details = case_details.copy()
        
        # P4_2: Validate Case for Download
        self.log_debug("P4_2: Validate Case for Download", context)
        should_download, skip_reason = await self._validate_case_for_download(case_details, is_explicitly_requested)
        
        # P4_3: Should Attempt Download?
        if not should_download:
            # P4_SKIP: Skip Download with Reason
            self.log_debug(f"P4_SKIP: Skip Download - {skip_reason}", context)
            updated_case_details.update({
                'is_downloaded': False,
                'download_skipped': True,
                'skip_reason': skip_reason,
                'html_only': True
            })
            return updated_case_details
        
        # P4_4: Prepare Download Context
        self.log_debug("P4_4: Prepare Download Context", context)
        download_context = await self._prepare_download_context(case_details, page)
        
        # P4_5: Execute Document Download
        self.log_debug("P4_5: Execute Document Download", context)
        download_result = await self._execute_document_download(case_details, download_context)
        
        # P4_6: Handle Success/Failure
        if download_result.get('success', False):
            # P4_SUCCESS: Handle Download Success
            self.log_debug("P4_SUCCESS: Handle Download Success", context)
            updated_case_details.update({
                'is_downloaded': True,
                'download_success': True,
                'download_files': download_result.get('files', []),
                'download_metadata': download_result.get('metadata', {}),
                'html_only': False
            })
        else:
            # P4_FAIL: Handle Download Failure
            self.log_debug("P4_FAIL: Handle Download Failure", context)
            updated_case_details.update({
                'is_downloaded': False,
                'download_failed': True,
                'download_error': download_result.get('error', 'Unknown download error'),
                'html_only': True
            })
        
        self.log_info("PHASE 4: Download Workflow completed", context)
        return updated_case_details

    async def _validate_case_for_download(self, 
                                        case_details: Dict[str, Any], 
                                        is_explicitly_requested: bool) -> tuple[bool, Optional[str]]:
        """P4_2: Validate Case for Download."""
        try:
            if self._download_validator:
                result = await self._download_validator.execute({
                    "action": "validate_case_for_download",
                    "case_details": case_details,
                    "is_explicitly_requested": is_explicitly_requested
                })
                return result.get('should_download', False), result.get('skip_reason')
            
            # Fallback validation logic
            if case_details.get('_no_proceedings_detected'):
                return False, "no_proceedings_detected"
            
            if case_details.get('_transaction_receipt_detected'):
                return False, "transaction_receipt_detected"
                
            return True, None
            
        except Exception as e:
            self.log_error(f"Case validation failed: {str(e)}", exc_info=True)
            return False, f"validation_error: {str(e)}"

    async def _prepare_download_context(self, 
                                      case_details: Dict[str, Any], 
                                      page: Optional[Page]) -> Dict[str, Any]:
        """P4_4: Prepare Download Context."""
        download_context = {
            'case_details': case_details,
            'page': page,
            'download_path': case_details.get('download_path'),
            'court_id': case_details.get('court_id'),
            'docket_num': case_details.get('docket_num'),
            'base_filename': case_details.get('base_filename')
        }
        
        # Add any additional context needed for download
        if self._download_manager:
            try:
                additional_context = await self._download_manager.execute({
                    "action": "prepare_context",
                    "case_details": case_details
                })
                download_context.update(additional_context)
            except Exception as e:
                self.log_warning(f"Failed to prepare additional download context: {str(e)}")
        
        return download_context

    async def _execute_document_download(self, 
                                       case_details: Dict[str, Any], 
                                       download_context: Dict[str, Any]) -> Dict[str, Any]:
        """P4_5: Execute Document Download."""
        try:
            if self._file_downloader:
                download_result = await self._file_downloader.execute({
                    "action": "download_documents",
                    "case_details": case_details,
                    "download_context": download_context
                })
                return download_result
            
            # Fallback if no downloader available
            return {
                'success': False,
                'error': 'File downloader not available',
                'files': [],
                'metadata': {}
            }
            
        except Exception as e:
            self.log_error(f"Document download failed: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'files': [],
                'metadata': {}
            }

    async def upload_html_to_s3(self, base_filename: str, html_content: str, iso_date: str) -> bool:
        """Upload HTML content to S3."""
        try:
            if self._s3_manager:
                result = await self._s3_manager.execute({
                    "action": "upload_html",
                    "base_filename": base_filename,
                    "html_content": html_content,
                    "iso_date": iso_date
                })
                return result.get('success', False)
            return False
        except Exception as e:
            self.log_error(f"HTML upload to S3 failed: {str(e)}", exc_info=True)
            return False

    # Interface implementation methods
    async def execute_download_workflow(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Execute complete download workflow for a case."""
        return await self.process_download_workflow(case_details)

    async def authenticate_session(self, credentials: Dict[str, Any]) -> bool:
        """Authenticate PACER session."""
        try:
            if self._authentication_service:
                result = await self._authentication_service.execute({
                    "action": "authenticate",
                    "credentials": credentials
                })
                return result.get('authenticated', False)
            return False
        except Exception as e:
            self.log_error(f"Authentication failed: {str(e)}", exc_info=True)
            return False

    async def navigate_to_case(self, case_details: Dict[str, Any]) -> bool:
        """Navigate browser to case page."""
        try:
            if self._navigation_service:
                result = await self._navigation_service.execute({
                    "action": "navigate_to_case",
                    "case_details": case_details
                })
                return result.get('success', False)
            return False
        except Exception as e:
            self.log_error(f"Navigation failed: {str(e)}", exc_info=True)
            return False

    async def execute_query(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute PACER query and return results."""
        try:
            if self._navigation_service:
                result = await self._navigation_service.execute({
                    "action": "execute_query",
                    "query_params": query_params
                })
                return result.get('results', [])
            return []
        except Exception as e:
            self.log_error(f"Query execution failed: {str(e)}", exc_info=True)
            return []

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the service and components."""
        return {
            "service": "DownloadOrchestrationService",
            "status": "healthy" if self._initialized else "unhealthy",
            "components": {
                "download_validator": self._download_validator is not None,
                "file_downloader": self._file_downloader is not None,
                "download_manager": self._download_manager is not None,
                "s3_manager": self._s3_manager is not None,
                "authentication_service": self._authentication_service is not None,
                "navigation_service": self._navigation_service is not None
            }
        }