# /src/services/pacer/_download_components/s3_manager.py
import asyncio
import os
import re
from typing import Any, Dict, List, Optional

from src.infrastructure.patterns.component_base import ComponentImplementation


class S3Manager(ComponentImplementation):
    """
    Component for managing interactions with S3, including uploading HTML,
    constructing links, and verifying file existence.
    """

    def __init__(self, logger: Any = None, config: Optional[Dict] = None, s3_async_storage: Optional[Any] = None):
        super().__init__(logger, config)
        self.s3_async_storage = s3_async_storage

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes an S3 management action.

        Args:
            data (Dict): A dictionary containing action and parameters.
                - action: 'upload_html', 'find_html_link'
                - ... other params based on action

        Returns:
            Depends on the action.
            - upload_html: bool (success)
            - find_html_link: Optional[str] (the verified S3 URL)
        """
        action = data.get("action")
        if action == "upload_html":
            return await self.upload_html_to_s3(
                case_details=data.get("case_details", {}),
                html_content=data.get("html_content", ""),
                json_path=data.get("json_path", ""),
            )
        elif action == "find_html_link":
            return await self.find_s3_html_link(
                case_details=data.get("case_details", {}),
                json_path=data.get("json_path", ""),
            )
        else:
            raise ValueError(f"Unknown S3Manager action: {action}")

    async def upload_html_to_s3(
        self, case_details: Dict[str, Any], html_content: str, json_path: str
    ) -> bool:
        """
        Uploads HTML content to an S3 bucket with the correct key format.

        Args:
            case_details: Dictionary containing case details like 'base_filename'.
            html_content: The HTML content string to upload.
            json_path: The path to the JSON file, used to extract the date.

        Returns:
            True if upload was successful, False otherwise.
        """
        if not self.s3_async_storage:
            self.log_error("S3 async storage is not available for upload.")
            return False

        try:
            # Extract date from json_path (e.g., 'data/YYYYMMDD/dockets/file.json')
            date_match = re.search(r"data/(\d{8})/dockets/", json_path)
            if not date_match:
                self.log_error(f"Cannot extract date from json_path: {json_path}")
                return False
            iso_date = date_match.group(1)

            base_filename = case_details.get("base_filename")
            if not base_filename:
                self.log_error("No base_filename in case_details for S3 upload.")
                return False

            # Construct the S3 key
            s3_key = f"{iso_date}/html/{base_filename}.html"
            self.log_info(f"Uploading HTML to S3 key: {s3_key}")

            # Upload using the appropriate async method
            if hasattr(self.s3_async_storage, "upload_html_string_async"):
                success = await self.s3_async_storage.upload_html_string_async(
                    html_content, s3_key
                )
            else:
                # Fallback to run synchronous method in an executor
                loop = asyncio.get_running_loop()
                success = await loop.run_in_executor(
                    None, self.s3_async_storage.upload_html_string, html_content, s3_key
                )

            if success:
                self.log_info(f"HTML successfully uploaded to S3: {s3_key}")
                return True
            else:
                self.log_error(f"S3 upload failed for HTML: {s3_key}")
                return False
        except Exception as e:
            self.log_error(f"Exception during HTML upload: {e}", exc_info=True)
            return False

    async def find_s3_html_link(
        self, case_details: Dict[str, Any], json_path: str
    ) -> Optional[str]:
        """
        Finds and verifies the S3 HTML link for a case. It first tries to construct
        a URL and then verifies it by searching S3.

        This combines logic from PacerHTMLProcessingService and DataUpdaterService.

        Args:
            case_details: Case details for building search patterns.
            json_path: Path to the JSON file for context.

        Returns:
            A verified S3 CDN URL, or None if not found.
        """
        if not self.s3_async_storage:
            self.log_error("S3 async storage is not available for finding links.")
            return None

        try:
            # Build the search pattern from court_id and docket_num
            court_id = case_details.get("court_id", "").strip()
            docket_num = case_details.get("docket_num", "").strip()

            if not court_id or not docket_num:
                self.log_warning("Missing court_id or docket_num for S3 HTML search.")
                return None

            # Extract YY: two digits after the colon
            year_match = re.search(r":(\d{2})", docket_num)
            # Extract NNNNN: 5 consecutive digits after the case type
            num_match = re.search(r"-[a-zA-Z]{2}-(\d{5})", docket_num)

            if not year_match or not num_match:
                self.log_warning(f"Could not extract YY/NNNNN from docket_num: {docket_num}")
                return None

            search_pattern = f"{court_id}_{year_match.group(1)}_{num_match.group(2)}"
            self.log_info(f"S3 HTML search pattern: {search_pattern}")

            # Determine the directory to search in S3
            config_date = self.config.get("iso_date")
            if not config_date:
                self.log_error("Config 'iso_date' is not set; cannot search for HTML file.")
                return None
            s3_html_dir_path = f"{config_date}/html/"

            # List files in the directory
            self.log_info(f"Searching for HTML files in S3 directory: {s3_html_dir_path}")
            if hasattr(self.s3_async_storage, "list_existing_files_async"):
                s3_keys = await self.s3_async_storage.list_existing_files_async(prefix=s3_html_dir_path)
            else:
                loop = asyncio.get_running_loop()
                s3_keys = await loop.run_in_executor(
                    None, self.s3_async_storage.list_existing_files, s3_html_dir_path
                )

            # Find a file matching the pattern (case-insensitive)
            search_pattern_lower = search_pattern.lower()
            for s3_key in s3_keys:
                s3_filename = os.path.basename(s3_key)
                if s3_filename.lower().startswith(search_pattern_lower):
                    self.log_info(f"Found matching S3 HTML file: {s3_filename}")
                    # Construct the full CDN URL
                    return f"https://cdn.lexgenius.ai/{s3_key}"

            self.log_warning(
                f"No S3 HTML file found with pattern '{search_pattern}' in '{s3_html_dir_path}'"
            )
            return None

        except Exception as e:
            self.log_error(f"Error finding S3 HTML link: {e}", exc_info=True)
            return None
