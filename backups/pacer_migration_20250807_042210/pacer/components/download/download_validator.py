# /src/services/pacer/_download_components/download_validator.py
from __future__ import annotations
from typing import Any, Dict, <PERSON><PERSON>, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class DownloadValidator(ComponentImplementation):
    """Validates if a download should be skipped based on case details and config."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'should_skip_download':
            return self.should_skip_download(data['case_details'])
        else:
            raise PacerServiceError(f"Unknown action for DownloadValidator: {action}")

    def should_skip_download(self, case_details: Dict[str, Any]) -> <PERSON><PERSON>[bool, str]:
        """
        Determines if a case should skip PDF download.
        """
        # This is a simplified version of the logic in PacerDownloadOrchestrationService.
        # A full implementation would also check ignore_download service and other configs.

        self.log_info(f"Validating download for docket: {case_details.get('docket_num')}")

        if self.config.get("html_only", False):
            return True, "HTML_ONLY mode is enabled in the configuration."

        mdl_flags_config = self.config.get("mdl_flags")
        if mdl_flags_config:
            case_flags = case_details.get("flags", [])
            for config_flag in mdl_flags_config:
                if str(config_flag).strip() in [str(cf).strip() for cf in case_flags]:
                    return True, f"MDL flag match: {config_flag}"

        return False, "Full download required."
