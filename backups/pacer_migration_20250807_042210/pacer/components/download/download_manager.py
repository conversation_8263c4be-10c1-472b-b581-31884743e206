# /src/services/pacer/_download_components/download_manager.py
from __future__ import annotations
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol
    from src.infrastructure.storage.s3_async_storage import S3AsyncStorage

class DownloadManager(ComponentImplementation):
    """Manages the download process, including S3 uploads."""

    def __init__(self,
                 s3_storage: S3AsyncStorage,
                 logger: LoggerProtocol = None,
                 config: Dict[str, Any] = None):
        super().__init__(logger, config)
        self._s3 = s3_storage

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'upload_to_s3':
            return await self.upload_to_s3(data['file_path'], data['s3_key'])
        elif action == 'process_html_only':
            return await self.process_html_only(data['html_content'], data['s3_key'])
        else:
            raise PacerServiceError(f"Unknown action for DownloadManager: {action}")

    async def upload_to_s3(self, file_path: str, s3_key: str) -> str | None:
        """Uploads a file to S3 and returns the CDN link."""
        self.log_info(f"Uploading {file_path} to S3 at {s3_key}")
        try:
            success, message = await self._s3.upload_file(file_path, s3_key)
            if success:
                cdn_base = self.config.get("cdn_base_url", "https://cdn.example.com")
                cdn_link = f"{cdn_base}/{s3_key}"
                self.log_info(f"Upload successful. CDN link: {cdn_link}")
                return cdn_link
            else:
                self.log_error(f"S3 upload failed: {message}")
                return None
        except Exception as e:
            self.log_error(f"Exception during S3 upload: {e}")
            return None

    async def process_html_only(self, html_content: str, s3_key: str) -> str | None:
        """Processes an HTML-only case by uploading the HTML to S3."""
        self.log_info(f"Processing HTML-only case. Uploading to {s3_key}")
        try:
            success = await self._s3.upload_html_string_async(html_content, s3_key)
            if success:
                cdn_base = self.config.get("cdn_base_url", "https://cdn.example.com")
                cdn_link = f"{cdn_base}/{s3_key}"
                self.log_info(f"HTML upload successful. CDN link: {cdn_link}")
                return cdn_link
            else:
                self.log_error("HTML upload to S3 failed.")
                return None
        except Exception as e:
            self.log_error(f"Exception during HTML upload: {e}")
            return None
