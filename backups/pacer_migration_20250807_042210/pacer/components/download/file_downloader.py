# /src/services/pacer/_download_components/file_downloader.py
from __future__ import annotations
import async<PERSON>
from pathlib import Path
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer.components.navigator import PacerNavigator
    from src.infrastructure.protocols.logger import LoggerProtocol
    from playwright.async_api import Download as PlaywrightDownload

class FileDownloader(ComponentImplementation):
    """Handles the actual file download process."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'download_file':
            return await self.download_file(data['navigator'], data['case_details'])
        else:
            raise PacerServiceError(f"Unknown action for FileDownloader: {action}")

    async def download_file(self, navigator: PacerNavigator, case_details: Dict[str, Any]) -> str | None:
        """
        Handles the PDF download process.
        This is a simplified version of the logic in PacerDownloadOrchestrationService.
        """
        log_prefix = f"[{case_details.get('court_id', 'N/A')}][{case_details.get('docket_num', 'N/A')}]"
        self.log_info(f"{log_prefix} Starting file download.")

        page = navigator.page
        download_path = Path(self.config.get("temp_download_dir", "/tmp/downloads"))
        download_path.mkdir(parents=True, exist_ok=True)

        try:
            # Simplified download trigger: click the first link that looks like a document.
            doc_link_selector = "a[href*='doc1']"
            doc_link = await navigator.locator(doc_link_selector)

            if await doc_link.count() == 0:
                self.log_warning(f"{log_prefix} No document link found with selector: {doc_link_selector}")
                return None

            async with page.expect_download() as download_info:
                await doc_link.first.click()

            download = await download_info.value

            # Wait for the download to complete
            download_file_path = download_path / download.suggested_filename
            await download.save_as(download_file_path)

            self.log_info(f"{log_prefix} File downloaded to: {download_file_path}")
            return str(download_file_path)

        except Exception as e:
            self.log_error(f"{log_prefix} File download failed: {e}")
            await navigator.save_screenshot("file_download_fail")
            return None
