# /src/services/pacer/_case_processing_components/html_parser.py
from typing import Any, Dict, List

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.services.html.case_parser_service import CaseParserService


class HtmlParser(ComponentImplementation):
    """
    Component for parsing HTML content to extract case-related information,
    specifically attorneys. It acts as a wrapper around the shared
    CaseParserService.
    """

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes the HTML parsing action.

        Args:
            data (Dict): A dictionary containing:
                - html_content (str): The HTML content to parse.

        Returns:
            List[Dict[str, str]]: A list of unique attorney dictionaries.
        """
        action = data.get("action")
        if action == "extract_attorneys":
            html_content = data.get("html_content")
            if not html_content:
                self.log_warning("No HTML content provided for attorney extraction.")
                return []
            return await self._extract_and_deduplicate_attorneys(html_content)
        else:
            raise ValueError(f"Unknown action: {action}")

    async def _extract_and_deduplicate_attorneys(
        self, html_content: str
    ) -> List[Dict[str, str]]:
        """
        Extracts attorneys from HTML content and deduplicates them.

        This method uses the centralized logic in the shared CaseParserService.

        Args:
            html_content: HTML content to parse.

        Returns:
            List of unique attorney dictionaries.
        """
        try:
            # CaseParserService is synchronous, so we don't need to await it,
            # but we keep this method async to conform to the Component pattern.
            parser = CaseParserService(self.logger, html_content)
            parsed_content = parser.parse()

            # Get attorneys directly from parser result
            attorneys = parsed_content.get("attorney", [])
            self.log_debug(f"Extracted {len(attorneys)} attorneys from HTML.")
            return attorneys
        except Exception as e:
            self.log_error(f"Error extracting attorneys from HTML: {e}")
            return []
