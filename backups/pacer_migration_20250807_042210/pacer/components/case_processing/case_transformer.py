# /src/services/pacer/_case_processing_components/case_transformer.py
from __future__ import annotations
import re
from datetime import datetime
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class CaseTransformer(ComponentImplementation):
    """Transforms case data into the final format."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'transform_case':
            return self.transform_case(data['case_details'])
        else:
            raise PacerServiceError(f"Unknown action for CaseTransformer: {action}")

    def transform_case(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Performs all transformation steps on the case details.
        """
        transformed_details = self._create_base_filename(case_details)
        transformed_details = self._add_processing_metadata(transformed_details)
        transformed_details = self._clean_dict_values(transformed_details)
        return transformed_details

    def _create_base_filename(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Creates a base filename for the case."""
        try:
            court_id = case_details.get("court_id", "unknown")
            docket_num = case_details.get("docket_num", "unknown")
            versus = case_details.get("versus", "unknown_versus")

            clean_docket = re.sub(r'[^\w-]', '_', docket_num)
            clean_versus = re.sub(r'[^\w\s-]', '', versus)
            clean_versus = re.sub(r'\s+', '_', clean_versus).strip('_-')
            clean_versus = clean_versus[:60]

            base_filename = f"{court_id.lower()}_{clean_docket}_{clean_versus}"
            base_filename = re.sub(r'[^\\w_-]+', '', base_filename)
            case_details['base_filename'] = base_filename
            return case_details
        except Exception as e:
            self.log_error(f"Error creating base filename: {e}")
            case_details['base_filename'] = f"{case_details.get('court_id', 'unknown')}_error"
            return case_details

    def _add_processing_metadata(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Adds processing metadata to the case details."""
        case_details["_processed_timestamp"] = datetime.now().isoformat()
        case_details["_processed_by"] = "CaseProcessingFacadeService"
        return case_details

    def _clean_dict_values(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively cleans dictionary values."""
        cleaned = {}
        for key, value in data.items():
            if isinstance(value, dict):
                cleaned[key] = self._clean_dict_values(value)
            elif isinstance(value, str):
                cleaned[key] = value.strip()
            else:
                cleaned[key] = value
        return cleaned
