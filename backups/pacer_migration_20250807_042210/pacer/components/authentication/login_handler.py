# /src/services/pacer/_authentication_components/login_handler.py
from __future__ import annotations
import asyncio
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer.components.browser.navigator import PacerNavigator
    from src.infrastructure.protocols.logger import LoggerProtocol

class LoginHandler(ComponentImplementation):
    """Handles the core PACER login operations."""

    LOGIN_URL = 'https://pacer.login.uscourts.gov/csologin/login.jsf'
    USERNAME_SELECTOR = "#loginForm\\:loginName"
    PASSWORD_SELECTOR = "#loginForm\\:password"
    LOGIN_BUTTON_SELECTOR = "#loginForm\\:fbtnLogin"
    LOGIN_SUCCESS_INDICATOR_SELECTOR = "#logoutForm\\:courtId"

    _main_login_lock = asyncio.Lock()

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        """Execute login-related actions."""
        action = data.get('action')
        if action == 'perform_login':
            return await self.perform_login(data['navigator'])
        else:
            raise PacerServiceError(f"Unknown action for LoginHandler: {action}")

    async def perform_login(self, navigator: PacerNavigator) -> bool:
        """
        Performs the login process for the main PACER site with synchronization.
        """
        log_prefix = "[MainPACERLogin]"
        async with self._main_login_lock:
            self.log_info(f"{log_prefix} Acquired main login lock.")

            # Try multiple config key formats for credentials
            username = self.config.get('username_prod') or self.config.get('PACER_USERNAME_PROD') or self.config.get('PACER_USERNAME')
            password = self.config.get('password_prod') or self.config.get('PACER_PASSWORD_PROD') or self.config.get('PACER_PASSWORD')

            if not username or not password:
                self.log_error(f"{log_prefix} PACER username or password not found.")
                return False

            await navigator.goto(self.LOGIN_URL)

            # NOTE: Assuming a separate component will check if already logged in.
            # This component's responsibility is just to perform the login action.

            self.log_info(f"{log_prefix} Performing main PACER login...")
            
            # Clear fields first and add delay to prevent form conflicts
            await navigator.fill(self.USERNAME_SELECTOR, "")
            await asyncio.sleep(0.5)
            await navigator.fill(self.USERNAME_SELECTOR, username)
            await asyncio.sleep(0.5)
            
            await navigator.fill(self.PASSWORD_SELECTOR, "")
            await asyncio.sleep(0.5)
            await navigator.fill(self.PASSWORD_SELECTOR, password)
            await asyncio.sleep(0.5)
            
            await navigator.click(self.LOGIN_BUTTON_SELECTOR, wait_for_nav=True)

            try:
                success_indicator = await navigator.locator(self.LOGIN_SUCCESS_INDICATOR_SELECTOR)
                await success_indicator.wait_for(state="visible", timeout=navigator.timeout)
                self.log_info(f"{log_prefix} Main PACER login successful.")
                return True
            except Exception as e:
                self.log_error(f"{log_prefix} Main PACER login failed: {e}")
                await navigator.save_screenshot("main_pacer_login_fail")
                return False
