# /src/services/pacer/_authentication_components/ecf_login_handler.py
from __future__ import annotations
import async<PERSON>
from typing import Any, Dict, TYPE_CHECKING

from playwright.async_api import (
    TimeoutError as PlaywrightTimeoutError,
    <PERSON>rror as PlaywrightError
)

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer.components.navigator import Pacer<PERSON>avigator
    from src.infrastructure.protocols.logger import LoggerProtocol

class ECFLoginHandler(ComponentImplementation):
    """Handles court-specific ECF login sequences."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'perform_ecf_login':
            return await self.perform_ecf_login_sequence(
                data['navigator'],
                data['court_id'],
            )
        else:
            raise PacerServiceError(f"Unknown action for ECFLoginHandler: {action}")

    async def perform_ecf_login_sequence(self, navigator: PacerNavigator, court_id: str) -> bool:
        """
        Performs the full login sequence for a court's ECF system.
        NOTE: This assumes main PACER login has already been handled by the facade.
        """
        log_prefix = f"[{court_id}] ECFLoginSeq:"
        self.log_info(f"{log_prefix} Starting ECF login sequence.")

        try:
            if not await self._navigate_to_court_ecf(navigator, court_id):
                self.log_error(f"{log_prefix} Navigation to court ECF page failed.")
                return False

            await self._handle_court_specific_ui(navigator, court_id)

            if not await self._login_to_court_ecf(navigator, court_id):
                self.log_error(f"{log_prefix} Court ECF login failed.")
                return False

            if not await self._handle_client_code_page(navigator, court_id):
                self.log_error(f"{log_prefix} Client code page handling failed.")
                return False

            self.log_info(f"{log_prefix} Full ECF login sequence successful.")
            return True

        except Exception as e:
            self.log_error(f"{log_prefix} Exception during ECF login sequence: {e}")
            await navigator.save_screenshot("ecf_login_sequence_exception")
            return False

    async def _navigate_to_court_ecf(self, navigator: PacerNavigator, court_id: str) -> bool:
        """Navigates to the specific court's ECF login page."""
        log_prefix = f"[{court_id}]"
        lookup_url = "https://pacer.uscourts.gov/file-case/court-cmecf-lookup"
        self.log_info(f"{log_prefix} Navigating to Court CM/ECF Lookup page: {lookup_url}")
        await navigator.goto(lookup_url)

        court_link_selector = f"a[href*='//ecf.{court_id.lower()}.uscourts.gov']"
        try:
            link_locator = await navigator.locator(court_link_selector)
            await link_locator.first.click()
            await navigator.page.wait_for_load_state('domcontentloaded')
            self.log_info(f"{log_prefix} Successfully navigated to court ECF page.")
            return True
        except Exception as e:
            self.log_error(f"{log_prefix} Failed to navigate to court ECF page: {e}")
            return False

    async def _login_to_court_ecf(self, navigator: PacerNavigator, court_id: str) -> bool:
        """Clicks the ECF login button on the specific court's page."""
        log_prefix = f"[{court_id}]"
        self.log_info(f"{log_prefix} Attempting to log into court ECF.")

        login_selectors = [
            "a:has-text('Document Filing System')",
            "a:has-text('Login')",
        ]

        for selector in login_selectors:
            try:
                login_element = navigator.page.locator(selector).first
                if await login_element.is_visible():
                    await login_element.click()
                    await navigator.page.wait_for_load_state('domcontentloaded')
                    self.log_info(f"{log_prefix} Clicked ECF login button.")
                    return True
            except Exception:
                continue

        self.log_error(f"{log_prefix} Could not find ECF login button.")
        return False


    async def _handle_court_specific_ui(self, navigator: PacerNavigator, court_id: str) -> None:
        """Handles court-specific UI elements like popups."""
        log_prefix = f"[{court_id}]"
        popup_selectors = [
            "#closePopupAlert",
            "div[role='dialog'] button:has-text('OK')",
        ]
        for selector in popup_selectors:
            try:
                popup_element = navigator.page.locator(selector).first
                if await popup_element.is_visible(timeout=1000):
                    self.log_info(f"{log_prefix} Handling court-specific UI: {selector}")
                    await popup_element.click()
            except Exception:
                continue

    async def _handle_client_code_page(self, navigator: PacerNavigator, court_id: str) -> bool:
        """Handles the client code page that appears for some courts."""
        log_prefix = f"[{court_id}]"
        client_code_input_selector = "input[name='logoutForm:clientCode']"
        try:
            client_input = navigator.page.locator(client_code_input_selector).first
            if await client_input.is_visible(timeout=2000):
                self.log_info(f"{log_prefix} Handling client code page.")
                await client_input.fill("007")
                await navigator.page.locator("input[type='submit']").first.click()
                await navigator.page.wait_for_load_state('domcontentloaded')
            return True
        except Exception:
            self.log_info(f"{log_prefix} No client code page found.")
            return True
