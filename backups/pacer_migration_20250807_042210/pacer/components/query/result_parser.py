# /src/services/pacer/_query_components/result_parser.py
from __future__ import annotations
from typing import Any, Dict, List, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class ResultParser(ComponentImplementation):
    """Parses and formats query results from the repository."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'aggregate_by_court':
            return self.aggregate_by_court(data['records'])
        else:
            raise PacerServiceError(f"Unknown action for ResultParser: {action}")

    def aggregate_by_court(self, records: List[Dict[str, Any]]) -> Dict[str, int]:
        """Aggregates a list of records by court_id."""
        self.log_info(f"Aggregating {len(records)} records by court.")
        court_counts = {}
        for record in records:
            court_id = record.get('CourtId', 'Unknown')
            court_counts[court_id] = court_counts.get(court_id, 0) + 1
        return court_counts
