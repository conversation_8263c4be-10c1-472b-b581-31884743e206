# /src/services/pacer/_browser_components/__init__.py

"""
Browser management components for PACER service.

This package contains components for browser lifecycle management,
context creation, and Playwright integration.
"""

from .playwright_manager import PlaywrightManager
from .context_factory import ContextFactory
from .navigator import PacerNavigator

__all__ = [
    'PlaywrightManager',
    'ContextFactory',
    'PacerNavigator'
]