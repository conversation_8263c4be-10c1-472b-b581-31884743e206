# /src/services/pacer/_export_components/csv_exporter.py
from __future__ import annotations
import csv
from typing import Any, Dict, List, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class CsvExporter(ComponentImplementation):
    """Handles exporting data to CSV files."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'export_to_csv':
            self.export_to_csv(data['items'], data['filename'], data.get('fields'))
        else:
            raise PacerServiceError(f"Unknown action for CsvExporter: {action}")

    def export_to_csv(self, items: List[Dict[str, Any]], filename: str, fields: Optional[List[str]] = None):
        """Exports a list of dictionaries to a CSV file."""
        if not items:
            self.log_warning("No items to export to CSV.")
            return

        if not fields:
            all_fields = set()
            for item in items:
                all_fields.update(item.keys())
            fields = sorted(list(all_fields))

        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fields, extrasaction='ignore')
                writer.writeheader()
                writer.writerows(items)
            self.log_info(f"Successfully exported {len(items)} items to {filename}")
        except IOError as e:
            self.log_error(f"Error writing to CSV file {filename}: {e}")
            raise PacerServiceError(f"Failed to export to CSV: {e}") from e
