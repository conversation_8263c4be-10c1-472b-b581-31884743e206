# /src/services/pacer/_processing_components/single_docket_processor.py
import logging
from datetime import date as DateType
from typing import Any, Dict, Optional

from playwright.async_api import Page

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.pacer.jobs.docket_job import DocketProcessingJob
from src.pacer.core_services.relevance.relevance_service import RelevanceService


class SingleDocketProcessor(ComponentImplementation):
    """
    Component for processing a single, specific docket page. This is used
    for workflows that start directly from a case page, not a report.
    """

    def __init__(
        self,
        logger: Any = None,
        config: Dict[str, Any] = None,
        pacer_repository: Any = None,
        file_manager: Any = None,
        case_processing_service: Any = None,
        download_orchestration_service: Any = None,
        file_operations_service: Any = None,
        case_classification_service: Any = None,
    ):
        super().__init__(logger, config)
        self.pacer_repository = pacer_repository
        self.file_manager = file_manager
        self.case_processing_service = case_processing_service
        self.download_orchestration_service = download_orchestration_service
        self.file_operations_service = file_operations_service
        self.case_classification_service = case_classification_service

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a single docket processing action.

        Args:
            data (Dict): A dictionary containing action parameters.
                - case_page (Page)
                - initial_details (Dict)
                - court_id (str)
                - court_logger (logging.Logger)
                - processor_config (Optional[Dict])

        Returns:
            Optional[Dict[str, Any]]: The processed case details or None on failure.
        """
        action = data.get("action")
        if action == "process_docket":
            return await self.process_single_docket(**data)
        else:
            raise ValueError(f"Unknown action for SingleDocketProcessor: {action}")

    async def process_single_docket(self, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Processes a single docket page to extract and download case details.
        """
        case_page = kwargs.get("case_page")
        initial_details = kwargs.get("initial_details")
        court_id = kwargs.get("court_id")
        court_logger = kwargs.get("court_logger")
        processor_config = kwargs.get("processor_config")

        log_prefix = f"[{court_id}] SingleDocketProc:"
        court_logger.info(f"{log_prefix} Starting single docket processing for URL: {case_page.url}")

        job = self._create_job_for_single_docket(
            case_page, initial_details, court_id, court_logger, processor_config
        )

        try:
            # Phase 1: Parse
            job.html_content = await job.page.content()
            current_details = {"court_id": court_id, "filing_date": job.initial_filing_date, **initial_details}
            job.case_details = self.case_processing_service.update_case_details(job.html_content, current_details)

            # Phase 1.5: Classify
            job.case_details = self.case_classification_service.classify_case_from_html(
                job.case_details, job.html_content
            )
            job.base_filename = self.case_processing_service.create_base_filename(job.case_details)
            job.case_details["base_filename"] = job.base_filename
            court_logger.info(f"{log_prefix} Generated base_filename: {job.base_filename}")

            # Phase 2: Save HTML
            iso_date = job.config.get("iso_date", DateType.today().strftime("%Y%m%d"))
            await self.download_orchestration_service.upload_html_to_s3(
                job.base_filename, job.html_content, iso_date
            )
            await self.file_operations_service.save_case_data_to_json(
                job.case_details, court_id, iso_date
            )

            # Phase 3: Relevance & Download
            if job.case_details.get("_html_indicates_removal") or job.case_details.get("is_removal"):
                court_logger.info(f"{log_prefix} Removal case detected, saving to review list and skipping download.")
                if self.file_manager:
                    await self.file_manager.save_review_case_details(job.case_details)
                return job.case_details

            is_explicitly_requested = job.config.get("_processing_explicit_dockets", False)
            if not is_explicitly_requested and job.relevance_engine._determine_case_review_status(job.case_details):
                court_logger.info(f"{log_prefix} Case irrelevant, saving to review list.")
                if self.file_manager:
                    await self.file_manager.save_review_case_details(job.case_details)
                return job.case_details

            job.case_details = await self.download_orchestration_service.execute_download_workflow(
                case_details=job.case_details,
                is_explicitly_requested=is_explicitly_requested,
                page=job.page,
            )

            court_logger.info(f"{log_prefix} Single docket processing completed.")
            return job.case_details

        except Exception as e:
            court_logger.error(f"{log_prefix} Single docket processing failed: {e}", exc_info=True)
            return None

    def _create_job_for_single_docket(
        self, case_page, initial_details, court_id, court_logger, processor_config
    ) -> DocketProcessingJob:

        relevance_config = self.config.get("relevance_config", {})
        relevance_engine = RelevanceService(
            config=self.config,
            relevance_config_dict=relevance_config,
            relevant_defendants_list=[d.lower() for d in relevance_config.get("relevant_defendants", [])],
            usa_defendant_regex_str="|".join(relevance_config.get("usa_defendant_patterns", [])),
            court_id=court_id,
            logger=court_logger,
        )

        job_config = self.config.copy()
        job_config["iso_date"] = job_config.get("iso_date", DateType.today().strftime("%Y%m%d"))
        if processor_config:
            job_config.update(processor_config)

        job = DocketProcessingJob(
            court_id=court_id,
            row_num=1,
            total_rows=1,
            docket_link_href=case_page.url,
            initial_versus_text=initial_details.get("versus", ""),
            initial_filing_date=initial_details.get("filing_date", ""),
            relevance_engine=relevance_engine,
            config=job_config,
            processor_config=processor_config or {},
            file_management_service=None,
            download_orchestration_service=self.download_orchestration_service,
            case_processing_service=self.case_processing_service,
            file_operations_service=self.file_operations_service,
            file_manager=self.file_manager,
            court_logger=court_logger,
        )
        job.page = case_page
        return job
