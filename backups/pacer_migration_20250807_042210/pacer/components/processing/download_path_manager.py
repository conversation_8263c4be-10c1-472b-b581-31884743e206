# /src/services/pacer/_processing_components/download_path_manager.py
import os
import tempfile
import uuid
from pathlib import Path
from typing import Any, Dict

from src.infrastructure.patterns.component_base import ComponentImplementation


class DownloadPathManager(ComponentImplementation):
    """
    Component responsible for creating and managing the download path
    infrastructure, which is critical for preventing file corruption during
    parallel processing.
    """

    def __init__(self, logger: Any = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)
        data_path = self.config.get('data_path', './data')
        self.base_data_dir = self._get_base_data_path(data_path)

    def _get_base_data_path(self, data_path: str) -> Path:
        """Strips nested date structure from data_path if present."""
        path_parts = str(data_path).split('/')
        if len(path_parts) >= 4:
            try:
                year, month, day = map(int, path_parts[-3:])
                if 2020 <= year <= 2030 and 1 <= month <= 12 and 1 <= day <= 31:
                    stripped_path = '/'.join(path_parts[:-3])
                    self.log_info(f"Stripped nested date from data_path: '{data_path}' -> '{stripped_path}'")
                    return Path(stripped_path)
            except (ValueError, IndexError):
                pass  # Not a date pattern
        return Path(data_path)

    async def execute(self, data: Any) -> Any:
        """Main execution method for the download path manager."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a download path management action.

        Args:
            data (Dict): A dictionary containing:
                - court_id (str)
                - iso_date (str)
                - mode (str): 'multi_docket', 'report', 'single_docket', etc.

        Returns:
            str: The created download path.
        """
        action = data.get("action")
        if action == "setup_download_path":
            return self.setup_download_paths(**data)
        else:
            raise ValueError(f"Unknown action for DownloadPathManager: {action}")

    def setup_download_paths(self, **kwargs) -> str:
        """
        Creates the download path infrastructure based on the processing mode.
        This preserves the temp -> staging -> final rename pattern.
        """
        court_id = kwargs.get("court_id")
        iso_date = kwargs.get("iso_date")
        mode = kwargs.get("mode")

        try:
            path_patterns = {
                'multi_docket': f"{court_id}_multidocket_dl_{uuid.uuid4().hex[:4]}",
                'report': f"{court_id}_ctx_dl_report/attempt_1_{uuid.uuid4().hex[:8]}",
                'single_docket': f"{court_id}_single_dl_{uuid.uuid4().hex[:4]}",
                'docket_log': f"{court_id}_docket_log_dl_{uuid.uuid4().hex[:4]}",
            }

            pattern = path_patterns.get(mode)
            if not pattern:
                self.log_warning(f"Unknown mode '{mode}', using fallback download path.")
                pattern = f"{court_id}_general_dl_{uuid.uuid4().hex[:4]}"

            download_path = self.base_data_dir / iso_date / "dockets" / "temp" / pattern
            download_path.mkdir(parents=True, exist_ok=True)

            self.log_info(f"[{court_id}] Download path for mode '{mode}' established: {download_path}")
            return str(download_path)

        except Exception as e:
            self.log_error(f"[{court_id}] CRITICAL: Failed to setup download path: {e}", exc_info=True)
            return self._get_fallback_path(court_id, iso_date)

    def _get_fallback_path(self, court_id: str, iso_date: str) -> str:
        """Provides a fallback download path in case of errors."""
        try:
            fallback_path = self.base_data_dir / iso_date / "dockets" / "temp" / f"{court_id}_fallback"
            fallback_path.mkdir(parents=True, exist_ok=True)
            self.log_warning(f"[{court_id}] Using fallback download path: {fallback_path}")
            return str(fallback_path)
        except Exception as fallback_error:
            self.log_error(f"[{court_id}] CRITICAL: Fallback path creation also failed: {fallback_error}")
            system_temp = Path(tempfile.gettempdir()) / "lexgenius_fallback" / f"{court_id}_{iso_date}"
            try:
                system_temp.mkdir(parents=True, exist_ok=True)
                self.log_warning(f"[{court_id}] Using system temp fallback: {system_temp}")
                return str(system_temp)
            except Exception as final_error:
                self.log_error(f"[{court_id}] CRITICAL: All fallback attempts failed: {final_error}")
                return str(Path(tempfile.gettempdir()))
