# /src/services/pacer/_transfer_components/court_identifier.py
import re
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import ComponentImplementation


class CourtIdentifier(ComponentImplementation):
    """
    Component for identifying a court's ID from its name.
    It uses a lookup table and fuzzy matching to identify courts.
    """

    def __init__(
        self,
        logger: Any = None,
        config: Optional[Dict] = None,
        court_lookup: Optional[Dict[str, str]] = None,
    ):
        super().__init__(logger, config)
        self.court_lookup = court_lookup or {}

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a court identification action.
        """
        action = data.get("action")
        if action == "get_court_id":
            court_name = data.get("court_name")
            if not court_name:
                raise ValueError("court_name must be provided.")
            return self.get_court_id_from_name(court_name)
        else:
            raise ValueError(f"Unknown action for CourtIdentifier: {action}")

    def get_court_id_from_name(self, court_name: str) -> str:
        """
        Gets the court ID for a given court name.

        Args:
            court_name: The name of the court.

        Returns:
            The court ID, or an 'UNKNOWN' string if not found.
        """
        if not court_name:
            return "UNKNOWN_NO_NAME"

        # Normalize court name for lookup
        normalized_name = (
            court_name.replace(" District Court", "")
            .replace("U. S.", "")
            .replace("US ", "")
            .strip()
        )

        # 1. Exact match
        if normalized_name in self.court_lookup:
            return self.court_lookup[normalized_name]

        # 2. Fuzzy match (simple version)
        # A more sophisticated fuzzy match could be added here if needed.
        for lookup_name, court_id in self.court_lookup.items():
            if normalized_name.lower() in lookup_name.lower() or lookup_name.lower() in normalized_name.lower():
                 self.log_info(f"Fuzzy matched '{court_name}' to '{lookup_name}' -> '{court_id}'")
                 return court_id

        self.log_warning(f"Court ID not found for court: {court_name}")
        return f"UNKNOWN_{normalized_name.replace(' ', '_').upper()}"
