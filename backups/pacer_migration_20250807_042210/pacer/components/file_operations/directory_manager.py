# /src/services/pacer/_file_components/directory_manager.py
from __future__ import annotations
import asyncio
import shutil
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer.components.path_builder import PathBuilder
    from src.infrastructure.protocols.logger import LoggerProtocol

class DirectoryManager(ComponentImplementation):
    """Manages directory operations for PACER."""

    def __init__(self,
                 path_builder: PathBuilder,
                 logger: LoggerProtocol = None,
                 config: Dict[str, Any] = None):
        super().__init__(logger, config)
        self._path_builder = path_builder

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        iso_date = data.get('iso_date')
        if not iso_date:
            raise PacerServiceError("iso_date is required for DirectoryManager actions.")

        if action == 'setup_directories':
            self.setup_directories(iso_date)
        elif action == 'cleanup_old_directories':
            await self.cleanup_old_directories(data.get('days_to_keep', 30))
        elif action == 'create_temp_download_dir':
            return self.create_temp_download_dir(iso_date, data['court_id'], data['operation_type'])
        else:
            raise PacerServiceError(f"Unknown action for DirectoryManager: {action}")

    def setup_directories(self, iso_date: str):
        """Creates essential directories for a given date."""
        self.log_info(f"Setting up directories for {iso_date}")
        date_path = self._path_builder.get_date_path(iso_date)
        subdirs = [
            self._path_builder.get_dockets_path(iso_date),
            self._path_builder.get_logs_path(iso_date),
            self._path_builder.get_html_path(iso_date),
            self._path_builder.get_screenshots_path(iso_date),
            self._path_builder.get_temp_path(iso_date),
            self._path_builder.get_docket_report_path(iso_date),
        ]
        for subdir in subdirs:
            subdir.mkdir(parents=True, exist_ok=True)

    async def cleanup_old_directories(self, days_to_keep: int):
        """Removes data directories older than a specified number of days."""
        self.log_info(f"Cleaning up directories older than {days_to_keep} days.")
        cutoff_date = datetime.now().date() - timedelta(days=days_to_keep)
        for date_dir in self._path_builder.base_path.iterdir():
            if date_dir.is_dir() and date_dir.name.isdigit():
                try:
                    dir_date = datetime.strptime(date_dir.name, '%Y%m%d').date()
                    if dir_date < cutoff_date:
                        await asyncio.to_thread(shutil.rmtree, date_dir)
                        self.log_info(f"Removed old directory: {date_dir}")
                except (ValueError, OSError) as e:
                    self.log_warning(f"Could not process or remove directory {date_dir}: {e}")

    def create_temp_download_dir(self, iso_date: str, court_id: str, operation_type: str) -> Path:
        """Creates a unique temporary download directory."""
        temp_base = self._path_builder.get_temp_path(iso_date)
        temp_dir_name = f"{court_id}_{operation_type}_{uuid.uuid4().hex[:8]}"
        temp_dir = temp_base / temp_dir_name
        temp_dir.mkdir(parents=True, exist_ok=True)
        self.log_info(f"Created temp download directory: {temp_dir}")
        return temp_dir
