# /src/services/pacer/_analytics_components/mdl_summarizer.py
from __future__ import annotations
from collections import defaultdict
from typing import Any, Dict, List, TYPE_CHECKING
import pandas as pd

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class MdlSummarizer(ComponentImplementation):
    """Summarizes MDL data from a list of records."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'summarize_mdl':
            return self.summarize_mdl(data['records'])
        else:
            raise PacerServiceError(f"Unknown action for MdlSummarizer: {action}")

    def summarize_mdl(self, records: List[Dict[str, Any]]) -> pd.DataFrame:
        """Summarizes MDL data from a list of records."""
        if not records:
            return pd.DataFrame()

        mdl_groups = defaultdict(list)
        for record in records:
            mdl_num = record.get('MdlNum', 'Unknown')
            mdl_groups[mdl_num].append(record)

        summaries = []
        for mdl_num, mdl_records in mdl_groups.items():
            court_counts = defaultdict(int)
            for record in mdl_records:
                court_counts[record.get('CourtId', 'Unknown')] += 1

            summaries.append({
                'MDL': mdl_num,
                'TotalFilings': len(mdl_records),
                'UniqueCourtCount': len(court_counts),
                'TopCourt': max(court_counts.items(), key=lambda x: x[1])[0] if court_counts else 'N/A',
            })

        return pd.DataFrame(summaries).sort_values('TotalFilings', ascending=False)
