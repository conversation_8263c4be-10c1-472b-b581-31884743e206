# /src/services/pacer/_classification_components/case_classifier.py
from typing import Any, Dict

from src.infrastructure.patterns.component_base import ComponentImplementation
# TODO: Replace with proper service dependency when transfer service is implemented
from src.utils.date import DateUtils


class CaseClassifier(ComponentImplementation):
    """
    Component for classifying PACER cases, including removal detection
    and transfer processing logic.
    """

    def __init__(self, *args, transfer_processor=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.transfer_processor = transfer_processor

    async def _execute_action(self, data: Any) -> Any:
        action = data.get("action")
        case_details = data.get("case_details", {})

        if action == "classify_case":
            html_content = data.get("html_content", "")
            # Full classification workflow
            classified_details = self.classify_case_initial(case_details)
            classified_details = self.classify_case_from_html(classified_details, html_content)
            if self.should_process_as_transfer(classified_details):
                classified_details = await self.process_transfer_case(classified_details)
            return self.apply_special_case_rules(classified_details)
        else:
            raise ValueError(f"Unknown action for CaseClassifier: {action}")

    def classify_case_initial(self, initial_details: Dict[str, Any]) -> Dict[str, Any]:
        """Performs initial case classification based on report data."""
        case_details = initial_details.copy()
        cause = str(case_details.get('cause_from_report', '')).lower()
        removal_keywords = ["petition for removal", "notice of removal", "28:1441", "28:1446"]
        case_details['_initial_removal_status'] = any(kw in cause for kw in removal_keywords)
        return case_details

    def classify_case_from_html(self, case_details: Dict[str, Any], html_content: str) -> Dict[str, Any]:
        """Performs detailed classification based on HTML content."""
        from src.services.html import CaseParserService
        parser = CaseParserService(self.logger, html_content)

        # Removal info
        removal_info = parser.is_removal()
        if removal_info and removal_info.get('is_removal'):
            case_details['_html_indicates_removal'] = True
            if removal_date := removal_info.get('removal_date'):
                try:
                    dt_obj = DateUtils.parse_date(removal_date)
                    if dt_obj:
                        case_details['removal_date'] = dt_obj.strftime('%Y-%m-%d')
                except ValueError:
                    self.log_warning(f"Could not parse removal date: {removal_date}")
        else:
            case_details['_html_indicates_removal'] = False

        # Final determination
        is_removal = case_details.get('_initial_removal_status') or case_details.get('_html_indicates_removal')
        case_details['is_removal'] = is_removal

        # Transfer info
        case_details['_requires_transfer_processing'] = 'transferred_from' in case_details or 'case_in_other_court' in case_details

        return case_details

    async def process_transfer_case(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Processes a case that requires transfer handling."""
        if not self.transfer_processor:
            self.log_warning("Transfer processor not available, skipping transfer processing.")
            return case_details

        return await self.transfer_processor.execute({
            "action": "process_transfer_case",
            "case_details": case_details
        })

    def apply_special_case_rules(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Applies special classification rules for specific courts or case types."""
        court_id = case_details.get("court_id")
        flags = str(case_details.get("flags", [])).lower()

        if court_id == 'ilnd' and "mdl 3060" in flags:
            case_details.update({'is_mdl_related_ilnd_3060': True, 'transferred_in': False, 'is_removal': False})
        elif court_id == 'cand' and "mdl 3047" in flags:
            case_details.update({'is_mdl_related_cand_3047': True, 'transferred_in': False, 'is_removal': False})

        return case_details

    def should_process_as_transfer(self, case_details: Dict[str, Any]) -> bool:
        """Determines if a case should be processed as a transfer."""
        return case_details.get('_requires_transfer_processing', False)
