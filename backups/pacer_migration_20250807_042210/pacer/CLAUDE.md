# PACER Domain Module

## Overview
This is the PACER domain module containing all business logic for legal document processing and case management. This is a COMPLEX domain with 80-100 files organized in a component architecture.

## Architecture Status
🚧 **MIGRATION IN PROGRESS**: Moving components from `src/services/pacer/` to this domain layer.

## Directory Structure
```
pacer/
├── models/              # Domain models (case, docket, document)
├── components/          # Component architecture (15+ subsystems)
│   ├── analytics/       # MDL analytics and summarization
│   ├── authentication/  # ECF, NextGen, PACER auth
│   ├── browser/        # Playwright browser management
│   ├── case_processing/ # Case parsing and transformation
│   ├── classification/  # Case classification logic
│   ├── configuration/   # Config management
│   ├── download/       # Download orchestration
│   ├── export/         # CSV and other exports
│   ├── file_operations/ # File and directory management
│   ├── navigation/     # Page navigation logic
│   ├── processing/     # Processing workflows
│   ├── query/          # Query building and parsing
│   ├── report/         # Report generation
│   ├── transfer/       # Transfer case processing
│   └── verification/   # Case verification
├── core/               # Core service implementations
├── factories/          # Domain factories and builders
│   ├── service_factory.py
│   ├── component_factory.py
│   └── builder.py
├── interfaces/         # Domain interfaces and protocols
└── exceptions.py       # Domain-specific exceptions
```

## Key Principles
1. **NO cross-domain dependencies** - PACER cannot import from transformer, reports, or fb_ads
2. **Use infrastructure patterns** - Inherit from `src.infrastructure.patterns.component_base.AsyncServiceBase`
3. **Component isolation** - Each component should be independently testable
4. **Domain-driven design** - Business logic lives here, not in service layer

## Migration Notes
- Components being moved from `src/services/pacer/_*_components/`
- Core services being moved from `src/services/pacer/_core_services/`
- Service layer (`src/services/pacer/`) will only contain thin orchestration

## Base Classes
```python
# ALWAYS use this base class, NEVER create domain-specific base classes
from src.infrastructure.patterns.component_base import AsyncServiceBase
```

## Testing
All components should have corresponding tests in `tests/unit/pacer/`

## Dependencies
- Infrastructure patterns
- External: S3, Database, Playwright
- NO dependencies on other domains

## Contact
Architecture questions: See docs/ARCHITECTURAL_CONSISTENCY_PLAN.md