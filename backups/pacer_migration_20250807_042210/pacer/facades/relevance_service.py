# /src/services/pacer/_core_services/relevance/relevance_service.py

"""
Relevance Service for PACER processing.

Consolidates RelevanceFacadeService and IgnoreDownloadFacadeService into a single
unified service that handles all relevance determination logic following the workflow
phases from docket_processing.md.
"""

from __future__ import annotations
import re
from typing import Any, Dict, List, Optional, Set, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol


class RelevanceService(AsyncServiceBase):
    """
    Consolidated relevance service for PACER processing.
    
    This service implements PHASE 2 of the docket processing lifecycle:
    Relevance & Classification - determine_case_relevance method that:
    1. Checks for MDL Flag (if yes, set relevant - MDL Flag Override)
    2. Checks ignore_download patterns (if yes, set not relevant - Ignore Download)
    3. Checks exclusion by statute (if yes, set not relevant - Excluded)
    4. Checks explicitly relevant patterns (if yes, set relevant - Explicit Match)
    5. Default to not relevant
    
    Combines functionality from RelevanceFacadeService and IgnoreDownloadFacadeService.
    """

    def __init__(self,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        
        # Configuration will be injected from ConfigurationService
        self._configuration_service = None
        
        # Cached configuration data
        self._relevance_config: Optional[Dict[str, Any]] = None
        self._ignore_download_config: Optional[List[Dict[str, Any]]] = None
        self._relevant_defendants_lower: List[str] = []
        self._courts_with_ignore_download: Set[str] = set()

    async def _initialize_service(self) -> None:
        """Initialize the relevance service."""
        # Get injected dependencies
        self._configuration_service = self.get_dependency('configuration_service')
        
        # Load configurations
        await self._load_relevance_configs()
        
        self.log_info("Relevance service initialized successfully")

    def _get_logger_context(self, court_id: str = "", docket_num: str = "") -> Dict[str, Any]:
        """Get logger context with consistent [court_id][docket_num] prefix format."""
        context = {"service": "RelevanceService"}
        if court_id or docket_num:
            context["case_prefix"] = f"[{court_id}][{docket_num}]"
        return context

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        court_id = data.get('court_id', '')
        docket_num = data.get('docket_num', '')
        
        context = self._get_logger_context(court_id, docket_num)
        self.log_debug(f"Executing relevance action: {action}", context)
        
        if action == 'determine_case_relevance':
            return await self.determine_case_relevance(
                data['case_details'],
                court_id=court_id,
                docket_num=docket_num
            )
        elif action == 'is_relevant':
            # Legacy method name support
            return await self.determine_case_relevance(
                data['case_details'],
                court_id=court_id,
                docket_num=docket_num
            )
        elif action == 'should_ignore_download':
            return await self.should_ignore_download(
                data['case_details'],
                court_id=court_id,
                docket_num=docket_num
            )
        elif action == 'reload_configs':
            return await self._load_relevance_configs()
        else:
            raise PacerServiceError(f"Unknown action for RelevanceService: {action}")

    async def _load_relevance_configs(self) -> None:
        """Load relevance and ignore download configurations."""
        self.log_info("Loading relevance configurations")
        
        if not self._configuration_service:
            self.log_warning("Configuration service not available")
            return
        
        # Load relevance config
        self._relevance_config = await self._configuration_service.execute({
            'action': 'get_relevance_config'
        })
        
        # Load ignore download config
        self._ignore_download_config = await self._configuration_service.execute({
            'action': 'get_ignore_download_config'
        })
        
        # Load relevant defendants
        relevant_defendants = await self._configuration_service.execute({
            'action': 'get_relevant_defendants'
        })
        self._relevant_defendants_lower = [d.lower() for d in relevant_defendants]
        
        # Build courts with ignore download set
        if self._ignore_download_config:
            self._courts_with_ignore_download = {
                entry.get('court_id') for entry in self._ignore_download_config 
                if entry.get('court_id')
            }
        else:
            self._courts_with_ignore_download = set()
        
        self.log_info("Relevance configurations loaded successfully", {
            "relevance_rules": len(self._relevance_config) if self._relevance_config else 0,
            "ignore_rules": len(self._ignore_download_config) if self._ignore_download_config else 0,
            "relevant_defendants": len(self._relevant_defendants_lower),
            "courts_with_ignore": len(self._courts_with_ignore_download)
        })

    async def determine_case_relevance(self, 
                                     case_details: Dict[str, Any],
                                     court_id: str = "",
                                     docket_num: str = "") -> Dict[str, Any]:
        """
        PHASE 2: Relevance & Classification
        
        Determines case relevance following the exact workflow from docket_processing.md:
        P2_2: Has MDL Flag? -> Yes: Set Relevant - MDL Flag Override
        P2_4: Matches ignore_download Pattern? -> Yes: Set Not Relevant - Ignore Download
        P2_6: Excluded by Statute? -> Yes: Set Not Relevant - Excluded  
        P2_8: Explicitly Relevant? -> Yes: Set Relevant - Explicit Match
        P2_10: Default: Set Not Relevant
        
        Args:
            case_details: Case details dictionary
            court_id: Court identifier for logging
            docket_num: Docket number for logging
            
        Returns:
            Dictionary with relevance determination results
        """
        context = self._get_logger_context(court_id, docket_num)
        self.log_info("Starting PHASE 2: Relevance & Classification", context)
        
        relevance_result = {
            'is_relevant': False,
            'relevance_reason': 'default_not_relevant',
            'mdl_override': False,
            'ignore_download_match': False,
            'statute_excluded': False,
            'explicit_match': False
        }
        
        # P2_2: Has MDL Flag?
        if case_details.get('mdl_num') or case_details.get('mdl_flag'):
            self.log_debug("P2_3: Set Relevant - MDL Flag Override", context)
            relevance_result.update({
                'is_relevant': True,
                'relevance_reason': 'mdl_flag_override',
                'mdl_override': True
            })
            return relevance_result
        
        # P2_4: Matches ignore_download Pattern?
        if await self.should_ignore_download(case_details, court_id, docket_num):
            self.log_debug("P2_5: Set Not Relevant - Ignore Download", context)
            relevance_result.update({
                'is_relevant': False,
                'relevance_reason': 'ignore_download_match',
                'ignore_download_match': True
            })
            return relevance_result
        
        # P2_6: Excluded by Statute?
        if await self._is_excluded_by_statute(case_details, court_id, docket_num):
            self.log_debug("P2_7: Set Not Relevant - Excluded", context)
            relevance_result.update({
                'is_relevant': False,
                'relevance_reason': 'statute_excluded',
                'statute_excluded': True
            })
            return relevance_result
        
        # P2_8: Explicitly Relevant?
        if await self._is_explicitly_relevant(case_details, court_id, docket_num):
            self.log_debug("P2_9: Set Relevant - Explicit Match", context)
            relevance_result.update({
                'is_relevant': True,
                'relevance_reason': 'explicit_match',
                'explicit_match': True
            })
            return relevance_result
        
        # P2_10: Set Not Relevant - Default
        self.log_debug("P2_10: Set Not Relevant - Default", context)
        self.log_info("PHASE 2: Relevance & Classification completed", context)
        
        return relevance_result

    async def should_ignore_download(self, 
                                   case_details: Dict[str, Any],
                                   court_id: str = "",
                                   docket_num: str = "") -> bool:
        """
        Check if a case should be ignored for download based on ignore_download patterns.
        
        Args:
            case_details: Case details dictionary
            court_id: Court identifier for logging  
            docket_num: Docket number for logging
            
        Returns:
            True if case should be ignored for download
        """
        context = self._get_logger_context(court_id, docket_num)
        
        if not self._ignore_download_config:
            return False

        case_court_id = case_details.get('court_id', court_id)
        if case_court_id not in self._courts_with_ignore_download:
            return False

        # Check ignore download patterns for this court
        for entry in self._ignore_download_config:
            if entry.get('court_id') != case_court_id:
                continue
            
            # Check various ignore patterns
            if self._matches_ignore_pattern(entry, case_details):
                self.log_info(f"Case matches ignore_download pattern", context)
                return True
                
        return False

    def _matches_ignore_pattern(self, ignore_entry: Dict[str, Any], case_details: Dict[str, Any]) -> bool:
        """Check if case details match an ignore download pattern."""
        # Check defendant patterns
        defendant_patterns = ignore_entry.get('defendant_patterns', [])
        case_defendants = case_details.get('defendant', [])
        
        if isinstance(case_defendants, str):
            case_defendants = [case_defendants]
        
        defendant_text = ' '.join(d.lower() for d in case_defendants)
        
        for pattern in defendant_patterns:
            if isinstance(pattern, str) and pattern.lower() in defendant_text:
                return True
            elif isinstance(pattern, dict) and pattern.get('regex'):
                try:
                    if re.search(pattern['regex'], defendant_text, re.IGNORECASE):
                        return True
                except re.error:
                    self.log_warning(f"Invalid regex pattern: {pattern['regex']}")
        
        # Check cause patterns
        cause_patterns = ignore_entry.get('cause_patterns', [])
        case_cause = case_details.get('cause', '').lower()
        
        for pattern in cause_patterns:
            if isinstance(pattern, str) and pattern.lower() in case_cause:
                return True
            elif isinstance(pattern, dict) and pattern.get('regex'):
                try:
                    if re.search(pattern['regex'], case_cause, re.IGNORECASE):
                        return True
                except re.error:
                    self.log_warning(f"Invalid regex pattern: {pattern['regex']}")
        
        # Check nature of suit patterns
        nos_patterns = ignore_entry.get('nos_patterns', [])
        case_nos = case_details.get('nos', '').lower()
        
        for pattern in nos_patterns:
            if isinstance(pattern, str) and pattern.lower() in case_nos:
                return True
        
        return False

    async def _is_excluded_by_statute(self, 
                                    case_details: Dict[str, Any],
                                    court_id: str = "",
                                    docket_num: str = "") -> bool:
        """Check if case is excluded by statute."""
        # Implement statute exclusion logic based on relevance config
        if not self._relevance_config:
            return False
        
        excluded_statutes = self._relevance_config.get('excluded_statutes', [])
        case_cause = case_details.get('cause', '').lower()
        case_nos = case_details.get('nos', '').lower()
        
        for statute in excluded_statutes:
            statute_lower = statute.lower()
            if statute_lower in case_cause or statute_lower in case_nos:
                return True
        
        return False

    async def _is_explicitly_relevant(self, 
                                    case_details: Dict[str, Any],
                                    court_id: str = "",
                                    docket_num: str = "") -> bool:
        """Check if case is explicitly relevant."""
        context = self._get_logger_context(court_id, docket_num)
        
        # Check for relevant defendants
        defendants = case_details.get('defendant', [])
        if isinstance(defendants, str):
            defendants = [defendants]

        defendant_text = ' '.join(d.lower() for d in defendants)
        
        for relevant_defendant in self._relevant_defendants_lower:
            if relevant_defendant in defendant_text:
                self.log_debug(f"Found relevant defendant match: {relevant_defendant}", context)
                return True

        # Check other explicit relevance patterns from config
        if self._relevance_config:
            relevant_patterns = self._relevance_config.get('relevant_patterns', [])
            
            for pattern in relevant_patterns:
                if self._matches_relevant_pattern(pattern, case_details):
                    self.log_debug(f"Found relevant pattern match", context)
                    return True

        return False

    def _matches_relevant_pattern(self, pattern: Dict[str, Any], case_details: Dict[str, Any]) -> bool:
        """Check if case details match a relevant pattern."""
        # Check pattern type and apply appropriate matching logic
        pattern_type = pattern.get('type', 'defendant')
        pattern_value = pattern.get('value', '')
        field = pattern.get('field', 'defendant')
        
        case_value = case_details.get(field, '')
        if isinstance(case_value, list):
            case_value = ' '.join(str(v) for v in case_value)
        
        case_value = str(case_value).lower()
        pattern_value = str(pattern_value).lower()
        
        if pattern.get('regex', False):
            try:
                return bool(re.search(pattern_value, case_value, re.IGNORECASE))
            except re.error:
                self.log_warning(f"Invalid regex pattern: {pattern_value}")
                return False
        else:
            return pattern_value in case_value

    async def is_relevant(self, case_details: Dict[str, Any]) -> bool:
        """
        Legacy method for backwards compatibility.
        Returns boolean result of relevance determination.
        """
        result = await self.determine_case_relevance(case_details)
        return result['is_relevant']