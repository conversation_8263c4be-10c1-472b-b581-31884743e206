"""
Docket Processing Orchestrator for PACER Core Services.

This service is the main workflow coordinator that orchestrates the complete
docket processing lifecycle using all other core services.
"""

from typing import Any, Dict, Optional, List
from playwright.async_api import Page

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.facades.case_processing_service import CaseProcessingService
from src.pacer.facades.relevance_service import RelevanceService
from src.pacer.facades.classification_service import ClassificationService
from src.pacer.facades.verification_service import VerificationService
from src.pacer.facades.download_service import DownloadService
from src.pacer.facades.file_operations_service import FileOperationsService
from src.pacer.facades.configuration_service import ConfigurationService
from src.pacer.facades.browser_service import BrowserService


class DocketOrchestrator(AsyncServiceBase):
    """
    Main Docket Processing Orchestrator for PACER workflow coordination.
    
    This service coordinates the complete docket processing lifecycle:
    - Phase 1: HTML Content Processing (Case Processing Service)
    - Phase 2: Relevance & Classification (Relevance + Classification Services)  
    - Phase 3: Case Verification (Verification Service)
    - Phase 4: Download Workflow (Download Orchestration Service)
    - Phase 5: File Operations & S3 Upload (File Operations Service)
    
    Additional capabilities:
    - Court-level processing with automatic PACER docket discovery
    - Browser-based navigation and authentication via Browser Service
    - Date-range queries to discover available dockets in courts
    - Robust parsing of PACER search results
    - Comprehensive error handling and resource cleanup
    
    Follows the exact workflow from docket_processing.md.
    """

    def __init__(
        self,
        case_processing_service: Optional[CaseProcessingService] = None,
        relevance_service: Optional[RelevanceService] = None,
        classification_service: Optional[ClassificationService] = None,
        verification_service: Optional[VerificationService] = None,
        download_service: Optional[DownloadService] = None,
        file_operations_service: Optional[FileOperationsService] = None,
        configuration_service: Optional[ConfigurationService] = None,
        browser_service: Optional[BrowserService] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
    ):
        super().__init__(logger, config)
        self._case_processing = case_processing_service
        self._relevance = relevance_service
        self._classification = classification_service
        self._verification = verification_service
        self._download_service = download_service
        self._file_operations = file_operations_service
        self._configuration = configuration_service
        self._browser_service = browser_service

    async def _initialize_service(self) -> None:
        """Initialize the orchestrator and setup dependencies."""
        await self._setup_dependencies()
        await self._validate_configuration()
        self.log_info("Docket Orchestrator initialized successfully")

    async def _setup_dependencies(self) -> None:
        """Setup orchestrator dependencies."""
        services = {
            "case_processing": self._case_processing,
            "relevance": self._relevance,
            "classification": self._classification,
            "verification": self._verification,
            "download_service": self._download_service,
            "file_operations": self._file_operations,
            "configuration": self._configuration,
            "browser_service": self._browser_service,
        }
        
        # Initialize all services
        for name, service in services.items():
            if service and not service._initialized:
                await service.initialize()

    async def _validate_configuration(self) -> None:
        """Validate orchestrator configuration."""
        required_services = [
            ("case_processing", self._case_processing),
            ("relevance", self._relevance),
            ("classification", self._classification),
            ("verification", self._verification),
            ("download_service", self._download_service),
            ("file_operations", self._file_operations),
            ("browser_service", self._browser_service),
        ]
        
        missing = [name for name, service in required_services if not service]
        if missing:
            raise ValueError(f"Missing required services for DocketOrchestrator: {missing}")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate orchestration methods."""
        action = data.get("action")
        
        if action == "process_docket":
            return await self.process_docket_comprehensive(
                page=data.get("page"),
                initial_details=data.get("initial_details", {})
            )
        elif action == "process_docket_case":
            return await self.process_docket_case(
                page=data.get("page"),
                initial_details=data.get("initial_details", {})
            )
        elif action == "process_court":
            return await self.process_court(data)
        else:
            raise ValueError(f"Unknown action for DocketOrchestrator: {action}")

    async def process_docket_comprehensive(
        self, page: Page, initial_details: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Execute the complete docket processing workflow.
        
        This is the main orchestration method that follows the exact phases
        from docket_processing.md Section 4.
        """
        court_id = initial_details.get("court_id", "N/A")
        docket_num = initial_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Starting comprehensive docket processing")
        
        try:
            # PHASE 1: HTML Content Processing
            case_details = await self._execute_phase_1_html_processing(page, initial_details)
            if not case_details:
                self.log_error(f"[{court_id}][{docket_num}] Phase 1 (HTML Processing) failed")
                return None
            
            # PHASE 2: Relevance & Classification
            case_details = await self._execute_phase_2_relevance_classification(case_details)
            
            # Check if case should be skipped after relevance filters
            if case_details.get("_skip_processing", False):
                self.log_info(f"[{court_id}][{docket_num}] Case skipped after relevance filters")
                # Still save metadata
                return await self._save_metadata_only(case_details)
            
            # PHASE 3: Case Verification  
            should_process = await self._execute_phase_3_verification(case_details)
            if not should_process:
                self.log_info(f"[{court_id}][{docket_num}] Case skipped after verification")
                # Save metadata only
                return await self._save_metadata_only(case_details)
            
            # PHASE 4: Download Workflow
            case_details = await self._execute_phase_4_download_workflow(page, case_details)
            
            # PHASE 5: File Operations & S3 Upload
            result = await self._execute_phase_5_save_and_upload(case_details)
            
            self.log_info(f"[{court_id}][{docket_num}] Comprehensive docket processing completed successfully")
            return result
            
        except Exception as e:
            self.log_error(f"[{court_id}][{docket_num}] Comprehensive docket processing failed: {e}")
            return None

    async def _execute_phase_1_html_processing(
        self, page: Page, initial_details: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Execute Phase 1: HTML Content Processing."""
        court_id = initial_details.get("court_id", "N/A")
        docket_num = initial_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 1: HTML Content Processing")
        
        # Get HTML content
        html_content = await page.content()
        
        # Process case HTML
        case_details = await self._case_processing.execute({
            "action": "process_case",
            "page": page,
            "initial_details": initial_details,
            "html_content": html_content
        })
        
        if not case_details:
            self.log_error(f"[{court_id}][{docket_num}] HTML processing returned no case details")
            return None
        
        # Add processing metadata
        case_details.update({
            "_processing_phase": "html_completed",
            "_html_content": html_content,  # Store for later phases
            "base_filename": case_details.get("base_filename") or f"{court_id}_{docket_num}"
        })
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 1 completed successfully")
        return case_details

    async def _execute_phase_2_relevance_classification(
        self, case_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute Phase 2: Relevance & Classification."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 2: Relevance & Classification")
        
        # Step 1: Determine case relevance
        relevance_result = await self._relevance.execute({
            "action": "determine_case_relevance",
            "case_details": case_details
        })
        
        case_details["is_relevant"] = relevance_result.get('is_relevant', False)
        case_details["relevance_reason"] = relevance_result.get('relevance_reason', 'unknown')
        
        # Step 2: Classify case (regardless of relevance for metadata)
        html_content = case_details.get("_html_content", "")
        classification_result = await self._classification.execute({
            "action": "classify_case",
            "case_details": case_details,
            "html_content": html_content
        })
        
        case_details.update(classification_result)
        
        # Step 3: Apply relevance-based processing decisions
        if not case_details["is_relevant"]:
            case_details["_skip_processing"] = True
            case_details["_skip_reason"] = "Not relevant"
        
        case_details["_processing_phase"] = "relevance_classification_completed"
        
        self.log_info(f"[{court_id}][{docket_num}] "
                     f"Phase 2 completed: relevant={case_details['is_relevant']}")
        return case_details

    async def _execute_phase_3_verification(
        self, case_details: Dict[str, Any]
    ) -> bool:
        """Execute Phase 3: Case Verification."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 3: Case Verification")
        
        # Check if explicitly requested
        is_explicitly_requested = case_details.get("_is_explicitly_requested", False)
        
        # Verify case should be processed
        verification_result = await self._verification.execute({
            "action": "verify_case",
            "case_details": case_details,
            "is_explicitly_requested": is_explicitly_requested
        })
        
        should_process = verification_result.get('should_process', True)
        case_details["_verification_result"] = verification_result
        case_details["_processing_phase"] = "verification_completed"
        
        self.log_info(f"[{court_id}][{docket_num}] "
                     f"Phase 3 completed: should_process={should_process}")
        return should_process

    async def _execute_phase_4_download_workflow(
        self, page: Page, case_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute Phase 4: Download Workflow."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 4: Download Workflow")
        
        # Execute download orchestration
        download_result = await self._download_service.execute({
            "action": "process_download_workflow",
            "case_details": case_details,
            "page": page,
            "is_explicitly_requested": case_details.get("_is_explicitly_requested", False)
        })
        
        case_details.update(download_result)
        case_details["_processing_phase"] = "download_completed"
        
        download_status = case_details.get("processing_status", "unknown")
        self.log_info(f"[{court_id}][{docket_num}] "
                     f"Phase 4 completed: status={download_status}")
        return case_details

    async def _execute_phase_5_save_and_upload(
        self, case_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute Phase 5: File Operations & S3 Upload."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 5: File Operations & S3 Upload")
        
        # Get ISO date from config or case details
        iso_date = case_details.get("iso_date") or self.config.get("iso_date")
        if not iso_date:
            raise ValueError("ISO date not available for file operations")
        
        # Clean up temporary fields before saving
        case_details = await self._cleanup_temporary_fields(case_details)
        
        # Save case data
        local_path = await self._file_operations.save_case_data(case_details, iso_date)
        
        # Try to upload to S3 if configured
        s3_url = ""
        try:
            s3_key = f"{iso_date}/{court_id}/{case_details.get('base_filename', 'unknown')}.json"
            s3_url = await self._file_operations.upload_to_s3(local_path, s3_key)
        except Exception as e:
            self.log_warning(f"S3 upload failed: {str(e)}")
        
        # Merge result with case details
        case_details.update({
            "_processing_phase": "completed",
            "_local_path": local_path,
            "_s3_url": s3_url,
            "_processing_status": "success"
        })
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 5 completed successfully")
        return case_details

    async def _save_metadata_only(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Save case metadata without full processing."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Saving metadata only (skipped processing)")
        
        # Clean up temporary fields
        case_details = await self._cleanup_temporary_fields(case_details)
        case_details["_processing_status"] = "metadata_only"
        
        # Get ISO date
        iso_date = case_details.get("iso_date") or self.config.get("iso_date")
        if not iso_date:
            raise ValueError("ISO date not available for file operations")
        
        # Save metadata
        local_path = await self._file_operations.save_case_data(case_details, iso_date)
        
        case_details.update({
            "_local_path": local_path,
            "_s3_url": ""
        })
        
        return case_details

    async def _cleanup_temporary_fields(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Remove temporary processing fields before final save."""
        cleaned_data = case_details.copy()
        
        # Remove HTML content (too large for storage)
        cleaned_data.pop("_html_content", None)
        
        # Remove other temporary fields but keep important processing metadata
        fields_to_remove = [
            field for field in cleaned_data.keys() 
            if field.startswith('_') and field not in [
                '_processing_phase', '_processing_status', '_local_path', '_s3_url',
                '_verification_result', '_is_explicitly_requested'
            ]
        ]
        
        for field in fields_to_remove:
            cleaned_data.pop(field, None)
        
        return cleaned_data

    # Interface implementation methods
    async def process_docket(self, docket_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process docket using new core services workflow."""
        page = docket_data.get("page")
        initial_details = docket_data.get("initial_details", {})
        
        if not page:
            raise ValueError("Page object required for docket processing")
        
        return await self.process_docket_comprehensive(page, initial_details)

    async def process_docket_case(
        self, page: Page, initial_details: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Alias for comprehensive processing - maintains compatibility."""
        return await self.process_docket_comprehensive(page, initial_details)

    async def orchestrate_job(self, job_data: Dict[str, Any]) -> str:
        """Orchestrate a processing job."""
        court_id = job_data.get("court_id", "N/A")
        self.log_info(f"Orchestrating job for court: {court_id}")
        
        # This would integrate with the job processing system
        # For now, return a job ID
        import uuid
        job_id = str(uuid.uuid4())
        
        self.log_info(f"Job orchestrated with ID: {job_id}")
        return job_id

    async def execute_bulk_processing(self, jobs: list[Dict[str, Any]]) -> list[Dict[str, Any]]:
        """Execute bulk processing of multiple jobs."""
        self.log_info(f"Executing bulk processing for {len(jobs)} jobs")
        
        results = []
        for job in jobs:
            try:
                result = await self.process_docket(job)
                if result:
                    results.append(result)
            except Exception as e:
                self.log_error(f"Bulk processing job failed: {e}")
                # Continue with other jobs
        
        self.log_info(f"Bulk processing completed: {len(results)}/{len(jobs)} successful")
        return results

    async def handle_legacy_workflows(self, workflow_type: str, data: Dict[str, Any]) -> Any:
        """Handle legacy workflow patterns."""
        self.log_info(f"Handling legacy workflow: {workflow_type}")
        
        if workflow_type == "single_docket":
            # Route to comprehensive processing
            return await self.process_docket(data)
        elif workflow_type == "bulk_docket":
            # Route to bulk processing  
            return await self.execute_bulk_processing(data.get("jobs", []))
        else:
            raise ValueError(f"Unsupported legacy workflow type: {workflow_type}")

    async def get_processing_status(self, job_id: str) -> Dict[str, Any]:
        """Get status of a processing job."""
        # Placeholder for job status tracking
        return {
            "job_id": job_id,
            "status": "completed",
            "message": "Job processing status tracking not yet implemented"
        }

    async def process_court(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a specific court by loading existing dockets or generating new civil cases report.
        
        This method implements the EXACT court processing logic from the working branch:
        1. Check for existing docket_report_log files
        2. If exists: Load cases from the log and process them
        3. If missing: Generate civil cases report first, then process
        4. Process each docket through the comprehensive docket processing pipeline
        5. Return aggregated results with proper status tracking
        """
        court_id = request_data.get("court_id", "unknown")
        config = request_data.get("config", {})
        browser_context = request_data.get("browser_context")
        iso_date = request_data.get("iso_date")
        start_date = request_data.get("start_date") 
        end_date = request_data.get("end_date")
        docket_list_input = request_data.get("docket_list_input")
        
        self.log_info(f"Processing court: {court_id} for date range {start_date} to {end_date}")
        
        if not all([court_id, start_date, end_date]):
            return {
                "status": "failed",
                "court_id": court_id,
                "error": "Missing required parameters: court_id, start_date, end_date",
                "processed_dockets": 0
            }
        
        try:
            # Step 1: Setup directories if file operations available
            if self._file_operations and hasattr(self._file_operations, 'setup_directories') and iso_date:
                try:
                    await self._file_operations.setup_directories(iso_date)
                    self.log_info(f"Setup directories for court {court_id} processing")
                except Exception as e:
                    self.log_warning(f"Could not setup directories: {e}")
            
            # Step 2: Determine if we have existing docket reports or need to generate them
            cases_to_process = []
            
            # Check for special docket list input first (highest priority)
            if docket_list_input:
                self.log_info(f"Using provided docket list for court {court_id}: {len(docket_list_input)} dockets")
                # Convert docket list input to cases format
                for docket_item in docket_list_input:
                    if docket_item.get("court_id") == court_id:  # Only process matching court
                        case = {
                            "docket_num": docket_item.get("docket_num"),
                            "court_id": court_id,
                            "case_title": docket_item.get("case_title", ""),
                            "filed_date": docket_item.get("filed_date", ""),
                            **docket_item  # Include all fields from docket item
                        }
                        cases_to_process.append(case)
            else:
                # Check for existing docket report log using parent orchestrator's method
                # We need to access the parent orchestrator that has the load_docket_report_log method
                existing_cases = None
                if hasattr(self, '_parent_orchestrator') and hasattr(self._parent_orchestrator, 'load_docket_report_log'):
                    existing_cases = await self._parent_orchestrator.load_docket_report_log(iso_date, court_id)
                elif iso_date:
                    # Try direct method call if we have the functionality
                    existing_cases = await self._load_docket_report_log_direct(iso_date, court_id)
                
                if existing_cases:
                    # Use existing docket report log
                    self.log_info(f"Found existing docket report log for court {court_id}: {len(existing_cases)} cases")
                    cases_to_process = existing_cases
                else:
                    # Civil cases report should already be generated by orchestrator service
                    # This is a fallback that should rarely be needed
                    self.log_warning(f"No existing docket report found for court {court_id} - this should have been generated by orchestrator service")
                    
                    # Try one more time to load the report in case it was just created
                    existing_cases_retry = await self._load_docket_report_log_retry(court_id, iso_date)
                    
                    if existing_cases_retry:
                        cases_to_process = existing_cases_retry
                        self.log_info(f"Found docket report on retry for court {court_id}: {len(cases_to_process)} cases")
                    else:
                        self.log_error(f"Still no docket report found for court {court_id} after orchestrator should have generated it")
                        return {
                            "status": "failed",
                            "court_id": court_id,
                            "error": f"Docket report missing - orchestrator service should have generated it",
                            "processed_dockets": 0,
                            "source": "missing_report"
                        }
            
            # Step 3: Process each case through the comprehensive docket processing pipeline
            if not cases_to_process:
                self.log_info(f"No cases to process for court {court_id}")
                return {
                    "status": "success_no_cases",
                    "court_id": court_id,
                    "processed_dockets": 0,
                    "cases_found": 0,
                    "message": f"No cases found for processing in court {court_id}",
                    "source": "no_cases"
                }
            
            # Get or create browser context for processing
            context_id = f"court_{court_id}_processing"
            page_id = f"court_{court_id}_docket_page"
            page = None
            
            if not browser_context and self._browser_service:
                try:
                    browser_context = await self._browser_service.create_context({
                        "context_id": context_id,
                        "ignore_https_errors": True
                    })
                    self.log_info(f"Created browser context for court {court_id} processing")
                except Exception as e:
                    self.log_error(f"Failed to create browser context: {e}")
                    browser_context = None
            
            if browser_context and self._browser_service:
                try:
                    await self._browser_service.create_page({
                        "context_id": browser_context,
                        "page_id": page_id
                    })
                    page = await self._browser_service.get_page({"page_id": page_id})
                except Exception as e:
                    self.log_warning(f"Could not create page for docket processing: {e}")
            
            # Process each case
            processed_count = 0
            failed_count = 0
            processed_dockets = []
            
            for case in cases_to_process:
                try:
                    # Prepare initial details for comprehensive docket processing
                    initial_details = {
                        "court_id": court_id,
                        "docket_num": case.get("docket_num"),
                        "case_title": case.get("case_title", ""),
                        "filed_date": case.get("filed_date", ""),
                        "iso_date": iso_date,
                        "_is_explicitly_requested": True,
                        **case  # Include all case fields
                    }
                    
                    # Process the docket using comprehensive processing
                    if page:
                        # Navigate to docket if we have a page
                        try:
                            docket_url = f"https://ecf.{court_id}.uscourts.gov/cgi-bin/DktRpt.pl?{case.get('docket_num', '')}"
                            await self._browser_service.navigate({
                                "page_id": page_id,
                                "url": docket_url,
                                "wait_until": "networkidle"
                            })
                        except Exception as e:
                            self.log_warning(f"Could not navigate to docket {case.get('docket_num')}: {e}")
                    
                    # Process through the comprehensive workflow
                    result = await self.process_docket_comprehensive(page, initial_details)
                    
                    if result:
                        processed_dockets.append(result)
                        processed_count += 1
                        self.log_info(f"Successfully processed docket {case.get('docket_num')}")
                    else:
                        failed_count += 1
                        self.log_warning(f"Failed to process docket {case.get('docket_num')}")
                        
                except Exception as e:
                    failed_count += 1
                    self.log_error(f"Error processing docket {case.get('docket_num', 'unknown')}: {e}")
            
            # Step 4: Clean up browser resources
            if self._browser_service:
                try:
                    if page:
                        await self._browser_service.close_page({"page_id": page_id})
                    if browser_context and not request_data.get("browser_context"):  # Only close if we created it
                        await self._browser_service.close_context({"context_id": browser_context})
                except Exception as e:
                    self.log_warning(f"Error cleaning up browser resources: {e}")
            
            # Step 5: Return aggregated results
            self.log_info(f"Court {court_id} processing completed: {processed_count} processed, {failed_count} failed from {len(cases_to_process)} total cases")
            
            return {
                "status": "success" if processed_count > 0 else "success_no_cases",
                "court_id": court_id,
                "processed_dockets": processed_count,
                "failed_dockets": failed_count,
                "cases_loaded": len(cases_to_process),
                "cases_found": len(cases_to_process),
                "docket_results": processed_dockets,
                "message": f"Court processing completed for {court_id}: {processed_count}/{len(cases_to_process)} successful",
                "source": "docket_report_log" if not docket_list_input else "docket_list_input"
            }
            
        except Exception as e:
            self.log_error(f"Error processing court {court_id}: {str(e)}", exc_info=True)
            
            # Clean up resources on error
            try:
                if self._browser_service and 'page_id' in locals():
                    await self._browser_service.close_page({"page_id": page_id})
                if self._browser_service and 'context_id' in locals() and not request_data.get("browser_context"):
                    await self._browser_service.close_context({"context_id": context_id})
            except:
                pass  # Ignore cleanup errors
            
            return {
                "status": "failed",
                "court_id": court_id,
                "error": str(e),
                "processed_dockets": 0,
                "source": "processing_error"
            }
    
    async def _discover_court_dockets(
        self, page: Page, court_id: str, start_date: str, end_date: str
    ) -> List[Dict[str, Any]]:
        """
        Discover available dockets for a court within the specified date range.
        
        This method performs the actual PACER query to find dockets.
        """
        self.log_info(f"Discovering dockets for court {court_id} from {start_date} to {end_date}")
        
        discovered_dockets = []
        
        try:
            # STEP 1: First, authenticate with PACER BEFORE navigating to civil reports
            self.log_info(f"Authenticating with PACER for court {court_id}")
            
            # Always perform PACER authentication first to establish session
            if self._browser_service and hasattr(self._browser_service, 'authenticate_pacer'):
                try:
                    # Get the context_id from the page
                    context_id = None
                    for cid, ctx in self._browser_service._contexts.items():
                        if page.context == ctx:
                            context_id = cid
                            break
                    
                    if context_id:
                        self.log_info(f"Performing PACER authentication for context {context_id}")
                        auth_page = await self._browser_service.authenticate_pacer({'context_id': context_id})
                        if auth_page:
                            self.log_info(f"Successfully authenticated with PACER for court {court_id}")
                            page = auth_page  # Use the authenticated page
                        else:
                            self.log_error(f"PACER authentication failed for court {court_id}")
                            return discovered_dockets
                    else:
                        self.log_error(f"Could not find context ID for authentication for court {court_id}")
                        return discovered_dockets
                except Exception as e:
                    self.log_error(f"PACER authentication error for court {court_id}: {e}")
                    return discovered_dockets
            else:
                self.log_error(f"Browser service authentication not available for court {court_id}")
                return discovered_dockets
            
            # STEP 2: Navigate directly to the civil cases filed report page
            # Use the correct URL pattern for civil cases filed reports
            # Georgia courts have a different URL pattern
            if court_id.startswith('ga'):  # Georgia courts: gand, gamd, gasd
                civil_reports_url = f"https://ecf.{court_id}.uscourts.gov/cgi-bin/{court_id.upper()}c_CaseFiled-Rpt.pl"
            else:
                civil_reports_url = f"https://ecf.{court_id}.uscourts.gov/cgi-bin/CaseFiled-Rpt.pl"
            
            self.log_info(f"Navigating directly to civil cases filed reports page: {civil_reports_url}")
            try:
                response = await page.goto(civil_reports_url, wait_until="networkidle", timeout=30000)
                if not response or not response.ok:
                    self.log_warning(f"Navigation to civil reports failed for court {court_id}, status: {response.status if response else 'No response'}")
                    # Try alternative URLs as fallback
                    alternative_urls = [
                        f"https://ecf.{court_id}.uscourts.gov/cgi-bin/rss_outside.pl",
                        f"https://ecf.{court_id}.uscourts.gov/cgi-bin/NewCasesRpt.pl",
                        f"https://ecf.{court_id}.uscourts.gov/cgi-bin/CivCasesSummary.pl"
                    ]
                    
                    for alt_url in alternative_urls:
                        try:
                            self.log_info(f"Trying alternative civil reports URL: {alt_url}")
                            response = await page.goto(alt_url, wait_until="networkidle", timeout=30000)
                            if response and response.ok:
                                self.log_info(f"Successfully reached alternative reports page at: {alt_url}")
                                break
                        except:
                            continue
                    else:
                        self.log_error(f"All civil reports URLs failed for court {court_id}")
                        return discovered_dockets
                        
            except Exception as e:
                self.log_error(f"Error navigating to civil reports for court {court_id}: {e}")
                return discovered_dockets
            
            # Check if we're on the correct civil cases reports page
            page_title = await page.title()
            page_content = await page.content()
            
            # If still on login page, authentication failed
            if "login" in page_title.lower():
                self.log_error(f"Still on login page after authentication attempt for court {court_id}")
                return discovered_dockets
            
            if "civil" not in page_title.lower() and "civil" not in page_content.lower():
                # Try alternative civil reports URL
                alternative_url = f"https://ecf.{court_id}.uscourts.gov/cgi-bin/iquery.pl"
                self.log_info(f"Trying alternative civil reports URL: {alternative_url}")
                await page.goto(alternative_url, wait_until="networkidle")
                page_title = await page.title()
                
                # Check again if still on login page
                if "login" in page_title.lower():
                    self.log_error(f"Alternative URL also requires login for court {court_id}")
                    return discovered_dockets
            
            self.log_info(f"On civil reports page for court {court_id}: {page_title}")
            
            # Enhanced debugging - capture current URL and page content info  
            current_url = page.url
            self.log_info(f"Current URL for court {court_id}: {current_url}")
            
            # Debug: Get all form elements on the page
            try:
                all_forms = await page.query_selector_all('form')
                self.log_info(f"Found {len(all_forms)} forms on page for court {court_id}")
                
                all_inputs = await page.query_selector_all('input')
                all_selects = await page.query_selector_all('select')
                all_buttons = await page.query_selector_all('button')
                
                self.log_info(f"Found {len(all_inputs)} inputs, {len(all_selects)} selects, {len(all_buttons)} buttons for court {court_id}")
                
                # Get form field details
                form_details = []
                for inp in all_inputs[:10]:  # First 10 inputs only
                    name = await inp.get_attribute('name') or 'no-name'
                    input_type = await inp.get_attribute('type') or 'text'
                    value = await inp.get_attribute('value') or ''
                    form_details.append(f"{name}({input_type})='{value}'")
                
                self.log_info(f"Sample form inputs for court {court_id}: {', '.join(form_details)}")
                
            except Exception as e:
                self.log_warning(f"Error debugging page structure for court {court_id}: {e}")
            
            # Look for civil cases report form elements
            # Try to find date range input fields for civil cases filed reports
            date_fields_found = False
            
            # Try different common selectors for date fields
            start_date_selectors = [
                'input[name="date_from"]',
                'input[name="start_date"]',
                'input[name="from_date"]',
                'input[id*="date_from"]',
                'input[id*="start"]'
            ]
            
            end_date_selectors = [
                'input[name="date_to"]',
                'input[name="end_date"]', 
                'input[name="to_date"]',
                'input[id*="date_to"]',
                'input[id*="end"]'
            ]
            
            # Try to fill date fields
            for start_selector in start_date_selectors:
                try:
                    start_element = await page.wait_for_selector(start_selector, timeout=2000)
                    if start_element:
                        await start_element.fill(start_date)
                        date_fields_found = True
                        self.log_info(f"Found and filled start date field: {start_selector}")
                        break
                except:
                    continue
            
            for end_selector in end_date_selectors:
                try:
                    end_element = await page.wait_for_selector(end_selector, timeout=2000)
                    if end_element:
                        await end_element.fill(end_date)
                        self.log_info(f"Found and filled end date field: {end_selector}")
                        break
                except:
                    continue
            
            if not date_fields_found:
                self.log_warning(f"Could not find date fields on civil cases reports page for court {court_id}")
                # Try to dump available form fields for debugging
                try:
                    form_inputs = await page.query_selector_all('input')
                    input_names = []
                    for inp in form_inputs:
                        name = await inp.get_attribute('name')
                        if name:
                            input_names.append(name)
                    self.log_info(f"Available form inputs for court {court_id}: {input_names}")
                except:
                    pass
                # Try to submit anyway or look for alternative query methods
            
            # Look for and click submit button (civil reports typically have "Run Report" or similar)
            submit_selectors = [
                'input[type="submit"]',
                'button[type="submit"]',
                'input[value*="Submit"]',
                'input[value*="Search"]',
                'input[value*="Run"]',        # Common in civil reports
                'input[value*="Report"]',     # "Run Report" buttons  
                'input[value*="Generate"]',   # "Generate Report" buttons
                'button:has-text("Submit")',
                'button:has-text("Search")',
                'button:has-text("Run")',
                'button:has-text("Report")'
            ]
            
            submit_clicked = False
            for submit_selector in submit_selectors:
                try:
                    submit_element = await page.wait_for_selector(submit_selector, timeout=2000)
                    if submit_element:
                        await submit_element.click()
                        submit_clicked = True
                        self.log_info(f"Clicked submit button: {submit_selector}")
                        break
                except:
                    continue
            
            if not submit_clicked:
                self.log_warning(f"Could not find submit button for court {court_id}")
                return discovered_dockets
            
            # Wait for results to load
            await page.wait_for_load_state("networkidle")
            
            # Parse docket results from the page
            discovered_dockets = await self._parse_docket_results(page, court_id)
            
        except Exception as e:
            self.log_error(f"Error during docket discovery for court {court_id}: {e}")
            
        return discovered_dockets
    
    async def _parse_docket_results(self, page: Page, court_id: str) -> List[Dict[str, Any]]:
        """
        Parse docket results from the PACER search results page.
        """
        dockets = []
        
        try:
            # Look for common table patterns in PACER results
            # Most PACER results are in tables with specific patterns
            
            # Try to find result tables
            table_selectors = [
                'table[summary*="case"]',
                'table[summary*="docket"]', 
                'table.results',
                'table[border="1"]',
                'table:has(tr:has(td:has(a[href*="DktRpt"])))'
            ]
            
            results_table = None
            for selector in table_selectors:
                try:
                    table = await page.wait_for_selector(selector, timeout=3000)
                    if table:
                        results_table = table
                        self.log_info(f"Found results table with selector: {selector}")
                        break
                except:
                    continue
            
            if not results_table:
                self.log_warning(f"Could not find results table for court {court_id}")
                # Try to extract from page text as fallback
                return await self._parse_docket_results_from_text(page, court_id)
            
            # Extract docket rows from the table
            rows = await page.query_selector_all(f'{table_selectors[0]} tr')
            
            for row in rows:
                try:
                    # Look for links that contain case/docket information
                    links = await row.query_selector_all('a[href*="DktRpt"]')
                    
                    if links:
                        for link in links:
                            # Extract docket number and case title
                            link_text = await link.inner_text()
                            href = await link.get_attribute('href')
                            
                            # Parse docket number from link text or href
                            docket_num = self._extract_docket_number(link_text, href)
                            
                            if docket_num:
                                # Get additional case details from the row
                                row_text = await row.inner_text()
                                case_title = self._extract_case_title(row_text, link_text)
                                filed_date = self._extract_filed_date(row_text)
                                
                                docket_metadata = {
                                    "docket_num": docket_num,
                                    "case_title": case_title,
                                    "filed_date": filed_date,
                                    "docket_url": href,
                                    "court_id": court_id
                                }
                                
                                dockets.append(docket_metadata)
                                self.log_debug(f"Parsed docket: {docket_num}")
                                
                except Exception as e:
                    self.log_debug(f"Error parsing row for court {court_id}: {e}")
                    continue
            
        except Exception as e:
            self.log_error(f"Error parsing docket results for court {court_id}: {e}")
        
        self.log_info(f"Parsed {len(dockets)} dockets from results for court {court_id}")
        return dockets
    
    async def _parse_docket_results_from_text(self, page: Page, court_id: str) -> List[Dict[str, Any]]:
        """
        Fallback method to parse docket information from page text when table parsing fails.
        """
        dockets = []
        
        try:
            page_text = await page.inner_text('body')
            
            # Look for docket number patterns in the text
            import re
            
            # Common docket number patterns (e.g., 1:23-cv-12345, 2:22-cr-00123)
            docket_patterns = [
                r'\d+:\d{2}-cv-\d+',  # Civil cases
                r'\d+:\d{2}-cr-\d+',  # Criminal cases  
                r'\d+:\d{2}-bk-\d+',  # Bankruptcy cases
                r'\d+:\d{2}-md-\d+',  # MDL cases
                r'\d+-\d+-\d+',       # Alternative format
            ]
            
            for pattern in docket_patterns:
                matches = re.finditer(pattern, page_text, re.IGNORECASE)
                
                for match in matches:
                    docket_num = match.group()
                    
                    # Try to extract surrounding context for case title
                    start = max(0, match.start() - 100)
                    end = min(len(page_text), match.end() + 100)
                    context = page_text[start:end]
                    
                    case_title = self._extract_case_title_from_context(context, docket_num)
                    
                    docket_metadata = {
                        "docket_num": docket_num,
                        "case_title": case_title,
                        "filed_date": "",
                        "docket_url": "",
                        "court_id": court_id
                    }
                    
                    dockets.append(docket_metadata)
            
        except Exception as e:
            self.log_error(f"Error in text-based docket parsing for court {court_id}: {e}")
        
        return dockets
    
    def _extract_docket_number(self, link_text: str, href: str) -> str:
        """Extract docket number from link text or URL."""
        import re
        
        # Try link text first
        docket_patterns = [
            r'\d+:\d{2}-cv-\d+',
            r'\d+:\d{2}-cr-\d+',
            r'\d+:\d{2}-bk-\d+',
            r'\d+:\d{2}-md-\d+'
        ]
        
        for pattern in docket_patterns:
            match = re.search(pattern, link_text, re.IGNORECASE)
            if match:
                return match.group()
        
        # Try href as fallback
        for pattern in docket_patterns:
            match = re.search(pattern, href, re.IGNORECASE)
            if match:
                return match.group()
        
        return ""
    
    def _extract_case_title(self, row_text: str, link_text: str) -> str:
        """Extract case title from row text."""
        # Remove the docket number and URL components to get case title
        import re
        
        # Clean up the text
        cleaned_text = row_text.replace(link_text, "").strip()
        
        # Remove common PACER artifacts
        artifacts_to_remove = [
            r'\d+:\d{2}-\w{2}-\d+',  # Docket numbers
            r'https?://[^\s]+',       # URLs
            r'\d{1,2}/\d{1,2}/\d{4}', # Dates
            r'Judge:?\s*\w+',         # Judge names
            r'Filed:?\s*\d+/\d+/\d+', # Filed dates
        ]
        
        for pattern in artifacts_to_remove:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.IGNORECASE)
        
        # Take the first substantial text as case title
        parts = [part.strip() for part in cleaned_text.split() if len(part.strip()) > 2]
        case_title = ' '.join(parts[:10])  # Limit to first 10 words
        
        return case_title.strip()
    
    def _extract_filed_date(self, row_text: str) -> str:
        """Extract filed date from row text."""
        import re
        
        # Look for date patterns
        date_patterns = [
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'\d{4}-\d{2}-\d{2}',
            r'\w{3}\s+\d{1,2},?\s+\d{4}'  # Mar 15, 2024
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, row_text)
            if match:
                return match.group()
        
        return ""
    
    def _extract_case_title_from_context(self, context: str, docket_num: str) -> str:
        """Extract case title from surrounding context."""
        # Simple extraction - take text after docket number
        parts = context.split(docket_num)
        if len(parts) > 1:
            after_docket = parts[1].strip()
            # Take first line or up to 100 characters
            case_title = after_docket.split('\n')[0][:100].strip()
            return case_title
        
        return ""

    async def _load_docket_report_log_direct(self, iso_date: str, court_id: str) -> Optional[List[Dict[str, Any]]]:
        """
        Direct method to load docket report log when parent orchestrator is not available.
        """
        try:
            from pathlib import Path
            import json
            
            # Get data path from config or use default
            data_path = self.config.get("data_path", "./data") if self.config else "./data"
            
            # Construct the docket report file path
            docket_report_path = (
                Path(data_path)
                / iso_date
                / "logs"
                / "docket_report" 
                / f"{court_id}.json"
            )
            
            self.log_info(f"Checking for docket report log: {docket_report_path}")
            
            # Check if file exists
            if not docket_report_path.exists():
                self.log_info(f"No docket report log found for court {court_id}")
                return None
                
            # Load and parse JSON content
            with open(docket_report_path, 'r', encoding='utf-8') as f:
                log_data = json.load(f)
            
            # Extract cases from the log data
            cases = log_data.get('cases', [])
            metadata = log_data.get('metadata', {})
            
            self.log_info(f"Loaded {len(cases)} cases from docket report log for court {court_id}")
            if metadata:
                self.log_info(f"Report metadata: generated_at={metadata.get('generated_at', 'unknown')}")
            
            return cases
            
        except Exception as e:
            self.log_error(f"Error loading docket report log for court {court_id}: {str(e)}", exc_info=True)
            return None

    async def _load_docket_report_log_retry(self, court_id: str, iso_date: str) -> Optional[List[Dict[str, Any]]]:
        """
        Retry method to load docket report log with proper error handling.
        This method implements retry logic for loading docket report logs.
        
        Args:
            court_id: Court identifier
            iso_date: ISO date for the report
            
        Returns:
            List of cases if successful, None if not found
        """
        try:
            from pathlib import Path
            import json
            import asyncio
            
            # Get data path from config or use default
            data_path = self.config.get("data_path", "./data") if self.config else "./data"
            
            # Construct the docket report file path
            docket_report_path = (
                Path(data_path)
                / iso_date
                / "logs"
                / "docket_report" 
                / f"{court_id}.json"
            )
            
            self.log_info(f"[RETRY] Checking for docket report log: {docket_report_path}")
            
            # Try multiple times with small delays
            max_attempts = 3
            for attempt in range(max_attempts):
                # Check if file exists
                if docket_report_path.exists():
                    try:
                        # Load and parse JSON content
                        with open(docket_report_path, 'r', encoding='utf-8') as f:
                            log_data = json.load(f)
                        
                        # Extract cases from the log data
                        cases = log_data.get('cases', [])
                        metadata = log_data.get('metadata', {})
                        
                        self.log_info(f"[RETRY] Loaded {len(cases)} cases from docket report log for court {court_id}")
                        if metadata:
                            self.log_info(f"[RETRY] Report metadata: generated_at={metadata.get('generated_at', 'unknown')}")
                        
                        return cases
                        
                    except (json.JSONDecodeError, KeyError, FileNotFoundError) as e:
                        self.log_warning(f"[RETRY] Error reading docket report log attempt {attempt + 1}: {str(e)}")
                        
                        # Wait a bit before retrying
                        if attempt < max_attempts - 1:
                            await asyncio.sleep(1.0)
                        continue
                else:
                    self.log_info(f"[RETRY] Docket report log not found (attempt {attempt + 1}): {docket_report_path}")
                    
                    # Wait a bit before retrying
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(2.0)
            
            self.log_info(f"[RETRY] No docket report log found for court {court_id} after {max_attempts} attempts")
            return None
            
        except Exception as e:
            self.log_error(f"[RETRY] Error loading docket report log for court {court_id}: {str(e)}", exc_info=True)
            return None

    async def _generate_civil_cases_report_for_court(
        self, 
        court_id: str, 
        iso_date: str, 
        start_date: str, 
        end_date: str,
        config: Dict[str, Any],
        browser_context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate civil cases report for a specific court.
        
        This method creates a new docket report by discovering dockets and saving them
        to the proper docket_report_log format.
        """
        self.log_info(f"Generating civil cases report for court {court_id}")
        
        try:
            # Get or create browser context for report generation
            context_id = f"civil_report_{court_id}"
            page_id = f"civil_report_page_{court_id}"
            page = None
            
            if not browser_context and self._browser_service:
                try:
                    browser_context = await self._browser_service.create_context({
                        "context_id": context_id,
                        "ignore_https_errors": True
                    })
                    self.log_info(f"Created browser context for civil cases report: {court_id}")
                except Exception as e:
                    self.log_error(f"Failed to create browser context: {e}")
                    return {
                        "status": "failed",
                        "error": f"Browser setup failed: {e}",
                        "cases": []
                    }
            
            if browser_context and self._browser_service:
                try:
                    await self._browser_service.create_page({
                        "context_id": browser_context,
                        "page_id": page_id
                    })
                    page = await self._browser_service.get_page({"page_id": page_id})
                    
                    if not page:
                        raise ValueError(f"Failed to create page for court {court_id}")
                        
                    # Navigate to court PACER page
                    court_url = f"https://ecf.{court_id}.uscourts.gov/"
                    navigation_success = await self._browser_service.navigate({
                        "page_id": page_id,
                        "url": court_url,
                        "wait_until": "networkidle"
                    })
                    
                    if not navigation_success:
                        raise ValueError(f"Failed to navigate to court {court_id} PACER page")
                    
                    # Discover dockets using existing discovery method
                    discovered_dockets = await self._discover_court_dockets(
                        page, court_id, start_date, end_date
                    )
                    
                    # Convert discovered dockets to cases format
                    cases = []
                    for docket_metadata in discovered_dockets:
                        case = {
                            "docket_num": docket_metadata.get("docket_num"),
                            "court_id": court_id,
                            "case_title": docket_metadata.get("case_title", ""),
                            "filed_date": docket_metadata.get("filed_date", ""),
                            "docket_url": docket_metadata.get("docket_url", "")
                        }
                        cases.append(case)
                    
                    # Save the report to docket_report format
                    if cases:
                        await self._save_docket_report_log_direct(court_id, iso_date, cases)
                    
                    self.log_info(f"Generated civil cases report for court {court_id}: {len(cases)} cases")
                    
                    return {
                        "status": "success",
                        "cases": cases,
                        "cases_found": len(cases),
                        "message": f"Civil cases report generated successfully for court {court_id}"
                    }
                    
                except Exception as e:
                    self.log_error(f"Error during civil cases report generation: {e}")
                    return {
                        "status": "failed", 
                        "error": f"Report generation failed: {e}",
                        "cases": []
                    }
            else:
                return {
                    "status": "failed",
                    "error": "Browser service not available",
                    "cases": []
                }
                
        except Exception as e:
            self.log_error(f"Failed to generate civil cases report for court {court_id}: {str(e)}", exc_info=True)
            return {
                "status": "failed",
                "error": str(e),
                "cases": []
            }
        finally:
            # Clean up browser resources
            if self._browser_service:
                try:
                    if page:
                        await self._browser_service.close_page({"page_id": page_id})
                    if browser_context and context_id:
                        await self._browser_service.close_context({"context_id": browser_context})
                except Exception as e:
                    self.log_warning(f"Error cleaning up browser resources: {e}")

    async def _save_docket_report_log_direct(self, court_id: str, iso_date: str, cases: List[Dict[str, Any]]) -> None:
        """
        Save cases to docket report log format directly.
        
        Saves to: data/{iso_date}/logs/docket_report/{court_id}.json
        """
        try:
            from pathlib import Path
            import json
            from datetime import datetime
            
            # Get data path from config or use default
            data_path = self.config.get("data_path", "./data") if self.config else "./data"
            
            # Construct the docket report file path
            docket_report_path = (
                Path(data_path)
                / iso_date
                / "logs"
                / "docket_report" 
                / f"{court_id}.json"
            )
            
            # Ensure directory exists
            docket_report_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create the report structure
            report_data = {
                "cases": cases,
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "total_cases": len(cases),
                    "court_id": court_id,
                    "iso_date": iso_date,
                    "source": "docket_orchestrator"
                }
            }
            
            # Save to file
            with open(docket_report_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.log_info(f"Saved docket report log for court {court_id}: {len(cases)} cases to {docket_report_path}")
            
        except Exception as e:
            self.log_error(f"Error saving docket report log for court {court_id}: {str(e)}", exc_info=True)
            raise