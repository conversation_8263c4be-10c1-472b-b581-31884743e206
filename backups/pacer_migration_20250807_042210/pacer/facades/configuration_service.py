# /src/services/pacer/_core_services/configuration/configuration_service.py

"""
Configuration Service for PACER processing.

Consolidates ConfigurationFacadeService and PacerConfigProvider into a single
unified service that manages all PACER configuration needs.
"""

from __future__ import annotations
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError, ConfigurationError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol


class ConfigurationService(AsyncServiceBase):
    """
    Consolidated configuration service for PACER processing.
    
    This service combines the functionality of ConfigurationFacadeService and 
    PacerConfigProvider to provide a unified interface for all PACER configuration
    management, following the workflow phases from docket_processing.md.
    """

    def __init__(self,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        
        # Configuration paths
        self._config_dir = Path(self.config.get('config_dir', 'src/config/pacer'))
        
        # Cached configurations
        self._all_configs: Optional[Dict[str, Any]] = None
        self._configs_loaded = False

    async def _initialize_service(self) -> None:
        """Initialize the configuration service."""
        await self._load_all_configs()
        self.log_info("Configuration service initialized successfully")

    def _get_logger_context(self, court_id: str = "", docket_num: str = "") -> Dict[str, Any]:
        """Get logger context with consistent [court_id][docket_num] prefix format."""
        context = {"service": "ConfigurationService"}
        if court_id or docket_num:
            context["case_prefix"] = f"[{court_id}][{docket_num}]"
        return context

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        court_id = data.get('court_id', '')
        docket_num = data.get('docket_num', '')
        
        context = self._get_logger_context(court_id, docket_num)
        self.log_debug(f"Executing configuration action: {action}", context)
        
        if action == 'get_all_configs':
            return await self.get_all_configs()
        elif action == 'get_relevance_config':
            return await self.get_relevance_config()
        elif action == 'get_relevant_defendants':
            return await self.get_relevant_defendants()
        elif action == 'get_ignore_download_config':
            return await self.get_ignore_download_config()
        elif action == 'get_stability_config':
            return await self.get_stability_config()
        elif action == 'get_paths_config':
            return await self.get_paths_config()
        elif action == 'reload_configs':
            return await self.reload_configs()
        else:
            raise PacerServiceError(f"Unknown action for ConfigurationService: {action}")

    async def _load_all_configs(self) -> Dict[str, Any]:
        """Load all PACER configurations from JSON files."""
        self.log_info("Loading all PACER configurations")
        
        configs = {}
        config_files = {
            "relevance": self._config_dir / 'relevance_config.json',
            "stability": self._config_dir / 'stability_config.json', 
            "paths": self._config_dir / 'paths_config.json',
            "ignore_download": self._config_dir / 'ignore_download/ignore_download.json',
            "relevant_defendants": self._config_dir / 'defendants/relevant_defendants.json',
        }
        
        for config_name, file_path in config_files.items():
            try:
                configs[config_name] = self._load_json_config(file_path)
                self.log_debug(f"Loaded {config_name} configuration", 
                              {"config_file": str(file_path)})
            except Exception as e:
                self.log_warning(f"Failed to load {config_name} config: {str(e)}", 
                               {"config_file": str(file_path)})
                configs[config_name] = {}
        
        self._all_configs = configs
        self._configs_loaded = True
        
        self.log_info("All PACER configurations loaded successfully", 
                     {"loaded_configs": list(configs.keys())})
        
        return configs

    def _load_json_config(self, file_path: Path) -> Dict[str, Any]:
        """Load a single JSON configuration file."""
        if not file_path.exists():
            self.log_warning(f"Configuration file not found: {file_path}")
            return {}
            
        try:
            with file_path.open('r', encoding='utf-8') as f:
                content = json.load(f)
                if not isinstance(content, dict):
                    self.log_warning(f"Configuration file contains non-dict content: {file_path}")
                    return {}
                return content
        except json.JSONDecodeError as e:
            self.log_error(f"Invalid JSON in configuration file {file_path}: {str(e)}")
            raise ConfigurationError(f"Invalid JSON in {file_path}: {str(e)}")
        except Exception as e:
            self.log_error(f"Error loading configuration file {file_path}: {str(e)}")
            raise ConfigurationError(f"Error loading {file_path}: {str(e)}")

    async def get_all_configs(self) -> Dict[str, Any]:
        """Get all PACER configurations."""
        if not self._configs_loaded or self._all_configs is None:
            await self._load_all_configs()
        return self._all_configs.copy()

    async def get_relevance_config(self) -> Dict[str, Any]:
        """Get the relevance configuration for case processing."""
        configs = await self.get_all_configs()
        return configs.get("relevance", {})

    async def get_relevant_defendants(self) -> List[str]:
        """Get the list of relevant defendants."""
        configs = await self.get_all_configs()
        defendants_data = configs.get("relevant_defendants", {})
        return defendants_data.get("defendants", [])

    async def get_ignore_download_config(self) -> Dict[str, Any]:
        """Get the ignore download configuration."""
        configs = await self.get_all_configs()
        return configs.get("ignore_download", {})

    async def get_stability_config(self) -> Dict[str, Any]:
        """Get the stability configuration for page processing."""
        configs = await self.get_all_configs()
        return configs.get("stability", {})

    async def get_paths_config(self) -> Dict[str, Any]:
        """Get the paths configuration."""
        configs = await self.get_all_configs()
        return configs.get("paths", {})

    async def reload_configs(self) -> Dict[str, Any]:
        """Reload all configurations from disk."""
        self.log_info("Reloading all PACER configurations")
        self._configs_loaded = False
        self._all_configs = None
        return await self._load_all_configs()

    async def get_config_value(self, config_name: str, key_path: str, default: Any = None) -> Any:
        """
        Get a specific configuration value using dot notation.
        
        Args:
            config_name: Name of the configuration (e.g., 'relevance', 'stability')
            key_path: Dot-separated path to the value (e.g., 'timeouts.page_load')
            default: Default value if key is not found
            
        Returns:
            The configuration value or default
        """
        configs = await self.get_all_configs()
        config = configs.get(config_name, {})
        
        keys = key_path.split('.')
        current = config
        
        for key in keys:
            if not isinstance(current, dict) or key not in current:
                return default
            current = current[key]
            
        return current

    async def is_config_loaded(self) -> bool:
        """Check if configurations are loaded."""
        return self._configs_loaded and self._all_configs is not None