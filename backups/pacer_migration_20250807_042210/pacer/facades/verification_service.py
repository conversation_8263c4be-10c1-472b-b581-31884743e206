# /src/services/pacer/_core_services/verification/verification_service.py

"""
Verification Service for PACER processing.

Consolidates CaseVerificationFacadeService into a unified service that handles 
all case verification logic following PHASE 3: Case Verification from docket_processing.md.
"""

from __future__ import annotations
from typing import Any, Dict, List, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol


class VerificationService(AsyncServiceBase):
    """
    Consolidated verification service for PACER processing.
    
    This service implements PHASE 3 of the docket processing lifecycle:
    Case Verification - verify_case method that:
    1. Checks is_explicitly_requested flag
    2. For explicit requests: Check Database GSI, check local artifacts (PDF/ZIP only)
    3. For report-scraped: Check Database GSI, check all local files
    4. Return skip or process decision
    """

    def __init__(self,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        
        # Dependencies will be injected
        self._case_verifier = None
        self._pacer_repository = None
        self._file_manager = None

    async def _initialize_service(self) -> None:
        """Initialize the verification service."""
        # Get injected dependencies
        self._case_verifier = self.get_dependency('case_verifier')
        self._pacer_repository = self.get_dependency('pacer_repository')
        self._file_manager = self.get_dependency('file_manager')
        
        self.log_info("Verification service initialized successfully")

    def _get_logger_context(self, court_id: str = "", docket_num: str = "") -> Dict[str, Any]:
        """Get logger context with consistent [court_id][docket_num] prefix format."""
        context = {"service": "VerificationService"}
        if court_id or docket_num:
            context["case_prefix"] = f"[{court_id}][{docket_num}]"
        return context

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        court_id = data.get('court_id', '')
        docket_num = data.get('docket_num', '')
        
        context = self._get_logger_context(court_id, docket_num)
        self.log_debug(f"Executing verification action: {action}", context)
        
        if action == 'verify_case':
            return await self.verify_case(
                data['case_details'],
                data.get('is_explicitly_requested', False),
                court_id=court_id,
                docket_num=docket_num
            )
        elif action == 'verify_case_completeness':
            return await self.verify_case_completeness(data['case_details'])
        elif action == 'verify_data_integrity':
            return await self.verify_data_integrity(data['data'])
        elif action == 'validate_case_format':
            return await self.validate_case_format(data['case_details'])
        elif action == 'verify_processing_eligibility':
            return await self.verify_processing_eligibility(data['case_details'])
        else:
            raise PacerServiceError(f"Unknown action for VerificationService: {action}")

    async def verify_case(self, 
                         case_details: Dict[str, Any],
                         is_explicitly_requested: bool = False,
                         court_id: str = "",
                         docket_num: str = "") -> Dict[str, Any]:
        """
        PHASE 3: Case Verification
        
        Implements the verification workflow from docket_processing.md:
        P3_2: is_explicitly_requested? 
        - Yes: P3_3 Explicitly Requested Verification
        - No: P3_4 Report-Scraped Verification
        
        Args:
            case_details: Case details dictionary
            is_explicitly_requested: Whether this is an explicit request or report-scraped
            court_id: Court identifier for logging
            docket_num: Docket number for logging
            
        Returns:
            Dictionary with verification results
        """
        context = self._get_logger_context(court_id, docket_num)
        self.log_info("Starting PHASE 3: Case Verification", context)
        
        verification_result = {
            'should_process': True,
            'skip_reason': None,
            'database_exists': False,
            'local_files_exist': False,
            'verification_type': 'explicitly_requested' if is_explicitly_requested else 'report_scraped'
        }
        
        # P3_2: is_explicitly_requested?
        if is_explicitly_requested:
            # P3_3: Explicitly Requested Verification
            self.log_debug("P3_3: Explicitly Requested Verification", context)
            verification_result = await self._verify_explicitly_requested(case_details, context)
        else:
            # P3_4: Report-Scraped Verification
            self.log_debug("P3_4: Report-Scraped Verification", context)
            verification_result = await self._verify_report_scraped(case_details, context)
        
        if verification_result['should_process']:
            self.log_info("PHASE 3: Case Verification - Proceeding to Download Phase", context)
        else:
            self.log_info(f"PHASE 3: Case Verification - Skipping: {verification_result['skip_reason']}", context)
        
        return verification_result

    async def _verify_explicitly_requested(self, 
                                         case_details: Dict[str, Any], 
                                         context: Dict[str, Any]) -> Dict[str, Any]:
        """
        P3_3: Explicitly Requested Verification
        P3_5: Check Database GSI
        P3_7: Check Local Artifacts Only PDF/ZIP
        """
        verification_result = {
            'should_process': True,
            'skip_reason': None,
            'database_exists': False,
            'local_files_exist': False,
            'verification_type': 'explicitly_requested'
        }
        
        # P3_5: Check Database GSI
        self.log_debug("P3_5: Check Database GSI", context)
        database_exists = await self._check_database_gsi(case_details)
        verification_result['database_exists'] = database_exists
        
        if database_exists:
            # P3_6: Exists in DB? -> Yes: P3_SKIP
            self.log_debug("P3_SKIP: Skip - Already in Database", context)
            verification_result.update({
                'should_process': False,
                'skip_reason': 'exists_in_database'
            })
            return verification_result
        
        # P3_7: Check Local Artifacts Only PDF/ZIP
        self.log_debug("P3_7: Check Local Artifacts Only PDF/ZIP", context)
        local_artifacts_exist = await self._check_local_artifacts_pdf_zip_only(case_details)
        verification_result['local_files_exist'] = local_artifacts_exist
        
        if local_artifacts_exist:
            # P3_8: Artifacts Exist? -> Yes: P3_SKIP
            self.log_debug("P3_SKIP: Skip - Local Artifacts Exist", context)
            verification_result.update({
                'should_process': False,
                'skip_reason': 'local_artifacts_exist'
            })
            return verification_result
        
        # P3_PROCESS: Proceed to Download Phase
        self.log_debug("P3_PROCESS: Proceed to Download Phase", context)
        return verification_result

    async def _verify_report_scraped(self, 
                                   case_details: Dict[str, Any], 
                                   context: Dict[str, Any]) -> Dict[str, Any]:
        """
        P3_4: Report-Scraped Verification
        P3_9: Check Database GSI
        P3_11: Check All Local Files
        """
        verification_result = {
            'should_process': True,
            'skip_reason': None,
            'database_exists': False,
            'local_files_exist': False,
            'verification_type': 'report_scraped'
        }
        
        # P3_9: Check Database GSI
        self.log_debug("P3_9: Check Database GSI", context)
        database_exists = await self._check_database_gsi(case_details)
        verification_result['database_exists'] = database_exists
        
        if database_exists:
            # P3_10: Exists in DB? -> Yes: P3_SKIP
            self.log_debug("P3_SKIP: Skip - Already in Database", context)
            verification_result.update({
                'should_process': False,
                'skip_reason': 'exists_in_database'
            })
            return verification_result
        
        # P3_11: Check All Local Files
        self.log_debug("P3_11: Check All Local Files", context)
        local_files_exist = await self._check_all_local_files(case_details)
        verification_result['local_files_exist'] = local_files_exist
        
        if local_files_exist:
            # P3_12: Any Files Exist? -> Yes: P3_SKIP
            self.log_debug("P3_SKIP: Skip - Local Files Exist", context)
            verification_result.update({
                'should_process': False,
                'skip_reason': 'local_files_exist'
            })
            return verification_result
        
        # P3_PROCESS: Proceed to Download Phase
        self.log_debug("P3_PROCESS: Proceed to Download Phase", context)
        return verification_result

    async def _check_database_gsi(self, case_details: Dict[str, Any]) -> bool:
        """Check if case exists in database using GSI (Global Secondary Index)."""
        try:
            if self._case_verifier:
                result = await self._case_verifier.execute({
                    "action": "check_database",
                    "case_details": case_details
                })
                return result.get('exists', False)
            return False
        except Exception as e:
            self.log_error(f"Database GSI check failed: {str(e)}", exc_info=True)
            return False

    async def _check_local_artifacts_pdf_zip_only(self, case_details: Dict[str, Any]) -> bool:
        """Check for local PDF/ZIP artifacts only (for explicitly requested cases)."""
        try:
            if self._file_manager:
                result = await self._file_manager.execute({
                    "action": "check_artifacts_pdf_zip",
                    "case_details": case_details
                })
                return result.get('exists', False)
            return False
        except Exception as e:
            self.log_error(f"Local artifacts check failed: {str(e)}", exc_info=True)
            return False

    async def _check_all_local_files(self, case_details: Dict[str, Any]) -> bool:
        """Check for any local files (for report-scraped cases)."""
        try:
            if self._file_manager:
                result = await self._file_manager.execute({
                    "action": "check_all_files",
                    "case_details": case_details
                })
                return result.get('exists', False)
            return False
        except Exception as e:
            self.log_error(f"All local files check failed: {str(e)}", exc_info=True)
            return False

    # Interface implementation methods
    async def verify_case_completeness(self, case_details: Dict[str, Any]) -> bool:
        """Verify case data completeness."""
        required_fields = ['court_id', 'docket_num', 'versus']
        return all(case_details.get(field) for field in required_fields)

    async def verify_data_integrity(self, data: Dict[str, Any]) -> bool:
        """Verify data integrity and consistency."""
        # Basic integrity checks
        if not isinstance(data, dict):
            return False
        
        # Check for corrupt data markers
        corrupt_markers = ['_transaction_receipt_detected', '_no_proceedings_detected']
        return not any(data.get(marker, False) for marker in corrupt_markers)

    async def validate_case_format(self, case_details: Dict[str, Any]) -> List[str]:
        """Validate case data format and return any errors."""
        errors = []
        
        # Check required fields
        required_fields = ['court_id', 'docket_num']
        for field in required_fields:
            if not case_details.get(field):
                errors.append(f"Missing required field: {field}")
        
        # Validate court_id format
        court_id = case_details.get('court_id', '')
        if court_id and not court_id.isalnum():
            errors.append("Invalid court_id format")
        
        # Validate docket_num format
        docket_num = case_details.get('docket_num', '')
        if docket_num and ':' not in docket_num:
            errors.append("Invalid docket_num format (missing colon)")
        
        return errors

    async def verify_processing_eligibility(self, case_details: Dict[str, Any]) -> bool:
        """Verify if case is eligible for processing."""
        # Check format validation
        format_errors = await self.validate_case_format(case_details)
        if format_errors:
            return False
        
        # Check data integrity
        integrity_check = await self.verify_data_integrity(case_details)
        if not integrity_check:
            return False
        
        # Check completeness
        completeness_check = await self.verify_case_completeness(case_details)
        return completeness_check

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the service and components."""
        return {
            "service": "VerificationService",
            "status": "healthy" if self._initialized else "unhealthy",
            "components": {
                "case_verifier": self._case_verifier is not None,
                "pacer_repository": self._pacer_repository is not None,
                "file_manager": self._file_manager is not None
            }
        }