"""
Docket domain model for PACER.

This module defines the Docket and DocketEntry entities which represent
docket sheets and their individual entries.
"""

from dataclasses import dataclass, field
from datetime import datetime, date
from enum import Enum
from typing import Optional, List, Dict, Any


class EntryType(Enum):
    """Enumeration of docket entry types."""
    MOTION = "motion"
    ORDER = "order"
    NOTICE = "notice"
    COMPLAINT = "complaint"
    ANSWER = "answer"
    BRIEF = "brief"
    EXHIBIT = "exhibit"
    TRANSCRIPT = "transcript"
    JUDGMENT = "judgment"
    OTHER = "other"


@dataclass
class DocketEntry:
    """
    Represents a single entry in a docket sheet.
    
    Each entry corresponds to a filed document or court action.
    """
    entry_number: int
    date_filed: date
    description: str
    entry_type: EntryType = EntryType.OTHER
    
    # Document references
    document_ids: List[str] = field(default_factory=list)
    page_count: Optional[int] = None
    
    # Filing party information
    filed_by: Optional[str] = None
    attorney_id: Optional[str] = None
    
    # Processing metadata
    is_sealed: bool = False
    is_restricted: bool = False
    download_status: Optional[str] = None
    
    # Additional metadata
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_downloadable(self) -> bool:
        """Check if this entry's documents are downloadable."""
        return not self.is_sealed and not self.is_restricted and len(self.document_ids) > 0
    
    def get_filing_party(self) -> str:
        """Get the name of the filing party."""
        return self.filed_by or "Unknown"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert docket entry to dictionary representation."""
        return {
            "entry_number": self.entry_number,
            "date_filed": self.date_filed.isoformat() if self.date_filed else None,
            "description": self.description,
            "entry_type": self.entry_type.value,
            "document_ids": self.document_ids,
            "page_count": self.page_count,
            "filed_by": self.filed_by,
            "attorney_id": self.attorney_id,
            "is_sealed": self.is_sealed,
            "is_restricted": self.is_restricted,
            "download_status": self.download_status,
            "tags": self.tags,
            "metadata": self.metadata,
        }


@dataclass
class Docket:
    """
    Represents a complete docket sheet for a case.
    
    Contains all docket entries and provides methods for docket analysis.
    """
    docket_id: str
    case_id: str
    court_id: str
    docket_number: str
    
    # Docket metadata
    case_title: str
    date_filed: date
    date_terminated: Optional[date] = None
    date_last_filing: Optional[date] = None
    
    # Entries
    entries: List[DocketEntry] = field(default_factory=list)
    total_entries: int = 0
    
    # Processing metadata
    retrieved_date: datetime = field(default_factory=datetime.utcnow)
    is_complete: bool = True
    missing_entries: List[int] = field(default_factory=list)
    
    # Statistics
    total_documents: int = 0
    total_pages: Optional[int] = None
    downloadable_count: int = 0
    
    # Additional metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Calculate statistics after initialization."""
        self.update_statistics()
    
    def update_statistics(self):
        """Update docket statistics based on entries."""
        self.total_entries = len(self.entries)
        self.total_documents = sum(len(e.document_ids) for e in self.entries)
        self.downloadable_count = sum(1 for e in self.entries if e.is_downloadable())
        
        if self.entries:
            # Update last filing date
            latest_entry = max(self.entries, key=lambda e: e.date_filed)
            self.date_last_filing = latest_entry.date_filed
            
            # Calculate total pages if available
            pages = [e.page_count for e in self.entries if e.page_count]
            self.total_pages = sum(pages) if pages else None
    
    def add_entry(self, entry: DocketEntry):
        """Add a new entry to the docket."""
        self.entries.append(entry)
        self.update_statistics()
    
    def get_entries_by_type(self, entry_type: EntryType) -> List[DocketEntry]:
        """Get all entries of a specific type."""
        return [e for e in self.entries if e.entry_type == entry_type]
    
    def get_entries_by_date_range(self, start_date: date, end_date: date) -> List[DocketEntry]:
        """Get entries within a date range."""
        return [
            e for e in self.entries 
            if start_date <= e.date_filed <= end_date
        ]
    
    def get_recent_entries(self, days: int = 30) -> List[DocketEntry]:
        """Get entries from the last N days."""
        from datetime import timedelta
        cutoff_date = date.today() - timedelta(days=days)
        return [e for e in self.entries if e.date_filed >= cutoff_date]
    
    def has_critical_entries(self) -> bool:
        """Check if docket contains critical entry types."""
        critical_types = [EntryType.JUDGMENT, EntryType.ORDER]
        return any(e.entry_type in critical_types for e in self.entries)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert docket to dictionary representation."""
        return {
            "docket_id": self.docket_id,
            "case_id": self.case_id,
            "court_id": self.court_id,
            "docket_number": self.docket_number,
            "case_title": self.case_title,
            "date_filed": self.date_filed.isoformat() if self.date_filed else None,
            "date_terminated": self.date_terminated.isoformat() if self.date_terminated else None,
            "date_last_filing": self.date_last_filing.isoformat() if self.date_last_filing else None,
            "entries": [e.to_dict() for e in self.entries],
            "total_entries": self.total_entries,
            "retrieved_date": self.retrieved_date.isoformat() if self.retrieved_date else None,
            "is_complete": self.is_complete,
            "missing_entries": self.missing_entries,
            "total_documents": self.total_documents,
            "total_pages": self.total_pages,
            "downloadable_count": self.downloadable_count,
            "metadata": self.metadata,
        }