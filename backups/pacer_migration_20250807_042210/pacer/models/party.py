"""
Party domain model for PACER.

This module defines the Party entity which represents
plaintiffs, defendants, and other parties in legal cases.
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Optional, List, Dict, Any


class PartyType(Enum):
    """Enumeration of party types in a case."""
    PLAINTIFF = "plaintiff"
    DEFENDANT = "defendant"
    APPELLANT = "appellant"
    APPELLEE = "appellee"
    PETITIONER = "petitioner"
    RESPONDENT = "respondent"
    INTERVENOR = "intervenor"
    THIRD_PARTY_PLAINTIFF = "third_party_plaintiff"
    THIRD_PARTY_DEFENDANT = "third_party_defendant"
    CREDITOR = "creditor"
    DEBTOR = "debtor"
    OTHER = "other"


class PartyRole(Enum):
    """Enumeration of party roles."""
    INDIVIDUAL = "individual"
    CORPORATION = "corporation"
    GOVERNMENT = "government"
    NON_PROFIT = "non_profit"
    PARTNERSHIP = "partnership"
    LLC = "llc"
    TRUST = "trust"
    ESTATE = "estate"
    OTHER_ENTITY = "other_entity"


@dataclass
class Party:
    """
    Represents a party (plaintiff, defendant, etc.) in a legal case.
    
    Parties are individuals or entities involved in litigation.
    """
    # Core identifiers
    party_id: str
    case_id: str
    
    # Party information
    name: str
    party_type: PartyType
    party_role: PartyRole = PartyRole.INDIVIDUAL
    
    # Additional names
    aliases: List[str] = field(default_factory=list)
    doing_business_as: Optional[str] = None
    formerly_known_as: Optional[str] = None
    
    # Contact information
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    country: str = "USA"
    phone: Optional[str] = None
    email: Optional[str] = None
    
    # Legal representation
    attorney_ids: List[str] = field(default_factory=list)
    pro_se: bool = False  # Representing themselves
    
    # Corporate information (if applicable)
    ein: Optional[str] = None  # Employer ID Number
    corporate_status: Optional[str] = None
    jurisdiction_of_incorporation: Optional[str] = None
    
    # Case-specific information
    date_added: Optional[str] = None
    date_terminated: Optional[str] = None
    is_active: bool = True
    termination_reason: Optional[str] = None
    
    # Financial information (for bankruptcy cases)
    claimed_amount: Optional[float] = None
    secured_claim: bool = False
    priority_claim: bool = False
    
    # Classification flags
    is_class_representative: bool = False
    is_government_entity: bool = False
    is_foreign_entity: bool = False
    
    # Additional metadata
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate and normalize party data."""
        if not self.party_id:
            raise ValueError("Party ID is required")
        if not self.case_id:
            raise ValueError("Case ID is required")
        if not self.name:
            raise ValueError("Party name is required")
        
        # Auto-detect government entities
        gov_keywords = ['United States', 'State of', 'County of', 'City of', 'Department', 'Agency']
        if any(keyword in self.name for keyword in gov_keywords):
            self.is_government_entity = True
            if self.party_role == PartyRole.INDIVIDUAL:
                self.party_role = PartyRole.GOVERNMENT
    
    def get_display_name(self) -> str:
        """Get the display name for the party."""
        name = self.name
        if self.doing_business_as:
            name += f" d/b/a {self.doing_business_as}"
        if self.formerly_known_as:
            name += f" f/k/a {self.formerly_known_as}"
        return name
    
    def get_full_address(self) -> Optional[str]:
        """Get the complete formatted address."""
        if not self.address:
            return None
        
        parts = [self.address]
        if self.city:
            parts.append(self.city)
        if self.state:
            parts.append(self.state)
        if self.zip_code:
            parts.append(self.zip_code)
        if self.country != "USA":
            parts.append(self.country)
        
        return ", ".join(parts)
    
    def has_legal_representation(self) -> bool:
        """Check if the party has legal representation."""
        return len(self.attorney_ids) > 0 and not self.pro_se
    
    def is_corporate_entity(self) -> bool:
        """Check if the party is a corporate entity."""
        corporate_roles = [
            PartyRole.CORPORATION,
            PartyRole.LLC,
            PartyRole.PARTNERSHIP,
            PartyRole.TRUST
        ]
        return self.party_role in corporate_roles
    
    def get_party_category(self) -> str:
        """Get a high-level category for the party."""
        if self.is_government_entity:
            return "Government"
        elif self.is_corporate_entity():
            return "Corporate"
        elif self.party_role == PartyRole.INDIVIDUAL:
            return "Individual"
        else:
            return "Other Entity"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert party to dictionary representation."""
        return {
            "party_id": self.party_id,
            "case_id": self.case_id,
            "name": self.name,
            "party_type": self.party_type.value,
            "party_role": self.party_role.value,
            "aliases": self.aliases,
            "doing_business_as": self.doing_business_as,
            "formerly_known_as": self.formerly_known_as,
            "address": self.address,
            "city": self.city,
            "state": self.state,
            "zip_code": self.zip_code,
            "country": self.country,
            "phone": self.phone,
            "email": self.email,
            "attorney_ids": self.attorney_ids,
            "pro_se": self.pro_se,
            "ein": self.ein,
            "corporate_status": self.corporate_status,
            "jurisdiction_of_incorporation": self.jurisdiction_of_incorporation,
            "date_added": self.date_added,
            "date_terminated": self.date_terminated,
            "is_active": self.is_active,
            "termination_reason": self.termination_reason,
            "claimed_amount": self.claimed_amount,
            "secured_claim": self.secured_claim,
            "priority_claim": self.priority_claim,
            "is_class_representative": self.is_class_representative,
            "is_government_entity": self.is_government_entity,
            "is_foreign_entity": self.is_foreign_entity,
            "tags": self.tags,
            "metadata": self.metadata,
        }