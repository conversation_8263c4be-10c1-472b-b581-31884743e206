"""
Document domain model for PACER.

This module defines the Document entity which represents
legal documents filed in PACER cases.
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any


class DocumentType(Enum):
    """Enumeration of document types."""
    PDF = "pdf"
    HTML = "html"
    TEXT = "text"
    IMAGE = "image"
    TRANSCRIPT = "transcript"
    EXHIBIT = "exhibit"
    ATTACHMENT = "attachment"
    OTHER = "other"


class DocumentStatus(Enum):
    """Enumeration of document processing statuses."""
    PENDING = "pending"
    DOWNLOADING = "downloading"
    DOWNLOADED = "downloaded"
    FAILED = "failed"
    SKIPPED = "skipped"
    RESTRICTED = "restricted"


@dataclass
class Document:
    """
    Represents a legal document in the PACER system.
    
    Documents are associated with docket entries and cases.
    """
    # Core identifiers
    document_id: str
    case_id: str
    docket_entry_number: Optional[int] = None
    
    # Document metadata
    title: str
    document_type: DocumentType = DocumentType.PDF
    file_size: Optional[int] = None  # Size in bytes
    page_count: Optional[int] = None
    
    # URLs and paths
    pacer_url: Optional[str] = None
    local_path: Optional[str] = None
    s3_url: Optional[str] = None
    
    # Processing status
    status: DocumentStatus = DocumentStatus.PENDING
    download_timestamp: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    
    # Access restrictions
    is_sealed: bool = False
    is_restricted: bool = False
    requires_payment: bool = False
    cost_estimate: Optional[float] = None  # Cost in dollars
    
    # Content metadata
    content_hash: Optional[str] = None
    text_extracted: bool = False
    ocr_performed: bool = False
    
    # Filing information
    filed_date: Optional[datetime] = None
    filed_by: Optional[str] = None
    
    # Additional metadata
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_downloadable(self) -> bool:
        """Check if the document can be downloaded."""
        return (
            not self.is_sealed and 
            not self.is_restricted and 
            self.status not in [DocumentStatus.DOWNLOADED, DocumentStatus.RESTRICTED]
        )
    
    def is_available_locally(self) -> bool:
        """Check if the document is available locally."""
        return self.local_path is not None and self.status == DocumentStatus.DOWNLOADED
    
    def is_available_on_s3(self) -> bool:
        """Check if the document is available on S3."""
        return self.s3_url is not None
    
    def mark_as_downloaded(self, local_path: str, s3_url: Optional[str] = None):
        """Mark the document as successfully downloaded."""
        self.status = DocumentStatus.DOWNLOADED
        self.local_path = local_path
        self.s3_url = s3_url
        self.download_timestamp = datetime.utcnow()
        self.error_message = None
    
    def mark_as_failed(self, error_message: str):
        """Mark the document download as failed."""
        self.status = DocumentStatus.FAILED
        self.error_message = error_message
        self.retry_count += 1
    
    def should_retry(self, max_retries: int = 3) -> bool:
        """Determine if download should be retried."""
        return (
            self.status == DocumentStatus.FAILED and 
            self.retry_count < max_retries and
            not self.is_restricted
        )
    
    def get_filename(self) -> str:
        """Generate a standardized filename for the document."""
        # Remove invalid characters from title
        safe_title = "".join(c for c in self.title if c.isalnum() or c in (' ', '-', '_'))
        safe_title = safe_title[:100]  # Limit length
        
        # Build filename
        parts = [self.case_id]
        if self.docket_entry_number:
            parts.append(f"entry_{self.docket_entry_number}")
        parts.append(safe_title)
        
        extension = ".pdf" if self.document_type == DocumentType.PDF else ".html"
        return "_".join(parts) + extension
    
    def calculate_download_priority(self) -> int:
        """Calculate download priority (higher = more important)."""
        priority = 0
        
        # Prioritize main documents over attachments
        if self.document_type != DocumentType.ATTACHMENT:
            priority += 10
        
        # Prioritize smaller documents
        if self.file_size and self.file_size < 1_000_000:  # Less than 1MB
            priority += 5
        
        # Deprioritize failed documents
        priority -= self.retry_count * 2
        
        return max(priority, 0)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary representation."""
        return {
            "document_id": self.document_id,
            "case_id": self.case_id,
            "docket_entry_number": self.docket_entry_number,
            "title": self.title,
            "document_type": self.document_type.value,
            "file_size": self.file_size,
            "page_count": self.page_count,
            "pacer_url": self.pacer_url,
            "local_path": self.local_path,
            "s3_url": self.s3_url,
            "status": self.status.value,
            "download_timestamp": self.download_timestamp.isoformat() if self.download_timestamp else None,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "is_sealed": self.is_sealed,
            "is_restricted": self.is_restricted,
            "requires_payment": self.requires_payment,
            "cost_estimate": self.cost_estimate,
            "content_hash": self.content_hash,
            "text_extracted": self.text_extracted,
            "ocr_performed": self.ocr_performed,
            "filed_date": self.filed_date.isoformat() if self.filed_date else None,
            "filed_by": self.filed_by,
            "tags": self.tags,
            "metadata": self.metadata,
        }