"""
Court domain model for PACER.

This module defines the Court entity which represents
federal courts in the PACER system.
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Optional, List, Dict, Any


class CourtType(Enum):
    """Enumeration of federal court types."""
    DISTRICT = "district"
    BANKRUPTCY = "bankruptcy"
    APPELLATE = "appellate"
    SUPREME = "supreme"
    MAGISTRATE = "magistrate"
    SPECIAL = "special"


class CourtStatus(Enum):
    """Enumeration of court operational statuses."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    MAINTENANCE = "maintenance"
    MERGED = "merged"


@dataclass
class Court:
    """
    Represents a federal court in the PACER system.
    
    Contains court information, configuration, and access details.
    """
    # Core identifiers
    court_id: str  # e.g., 'njd', 'cand', 'ilnd'
    full_name: str  # e.g., 'District of New Jersey'
    short_name: str  # e.g., 'D.N.J.'
    
    # Court classification
    court_type: CourtType
    circuit: Optional[int] = None  # Circuit number (1-11, or None for special courts)
    district_code: Optional[str] = None
    
    # Location information
    state: Optional[str] = None
    city: Optional[str] = None
    timezone: str = "America/New_York"
    
    # PACER access information
    pacer_url: Optional[str] = None
    ecf_version: Optional[str] = None  # e.g., 'NextGen', 'CM/ECF 6.3'
    login_type: str = "standard"  # 'standard', 'nextgen', 'special'
    
    # Operational status
    status: CourtStatus = CourtStatus.ACTIVE
    maintenance_message: Optional[str] = None
    
    # Court-specific configuration
    supports_mdl: bool = False
    supports_electronic_filing: bool = True
    requires_special_access: bool = False
    max_query_days: int = 180  # Maximum date range for queries
    
    # Fee information
    document_fee: float = 0.10  # Per page fee
    search_fee: float = 0.10  # Per search fee
    max_document_fee: float = 3.00  # Maximum fee per document
    
    # Processing rules
    business_hours_start: str = "09:00"
    business_hours_end: str = "17:00"
    excluded_holidays: List[str] = field(default_factory=list)
    
    # Statistics
    total_cases: Optional[int] = None
    active_cases: Optional[int] = None
    last_sync_date: Optional[str] = None
    
    # Additional metadata
    aliases: List[str] = field(default_factory=list)
    notes: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate court data after initialization."""
        if not self.court_id:
            raise ValueError("Court ID is required")
        if not self.full_name:
            raise ValueError("Court full name is required")
        
        # Normalize court_id to lowercase
        self.court_id = self.court_id.lower()
    
    def is_operational(self) -> bool:
        """Check if the court is operational."""
        return self.status == CourtStatus.ACTIVE
    
    def is_mdl_court(self) -> bool:
        """Check if this is an MDL (Multi-District Litigation) court."""
        # Special MDL courts
        mdl_courts = ['jpml', 'ilnd', 'cand', 'ohnd']
        return self.court_id in mdl_courts or self.supports_mdl
    
    def get_pacer_login_url(self) -> str:
        """Get the PACER login URL for this court."""
        if self.pacer_url:
            return f"{self.pacer_url}/cgi-bin/login.pl"
        
        # Generate standard URL
        if self.court_type == CourtType.APPELLATE:
            return f"https://ecf.ca{self.circuit}.uscourts.gov/cgi-bin/login.pl"
        else:
            return f"https://ecf.{self.court_id}.uscourts.gov/cgi-bin/login.pl"
    
    def get_query_url(self) -> str:
        """Get the PACER query URL for this court."""
        base_url = self.pacer_url or f"https://ecf.{self.court_id}.uscourts.gov"
        
        if self.ecf_version and "NextGen" in self.ecf_version:
            return f"{base_url}/cgi-bin/iquery.pl"
        else:
            return f"{base_url}/cgi-bin/CivilCaseFinder.pl"
    
    def calculate_document_cost(self, pages: int) -> float:
        """Calculate the cost for downloading a document."""
        if pages <= 0:
            return 0.0
        
        cost = pages * self.document_fee
        return min(cost, self.max_document_fee)
    
    def is_in_business_hours(self, timestamp: Any) -> bool:
        """Check if a given time is within business hours."""
        # This would need proper timezone handling in production
        # Simplified version for demonstration
        return True
    
    def get_display_name(self) -> str:
        """Get a display-friendly name for the court."""
        if self.short_name:
            return f"{self.short_name} - {self.full_name}"
        return self.full_name
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert court to dictionary representation."""
        return {
            "court_id": self.court_id,
            "full_name": self.full_name,
            "short_name": self.short_name,
            "court_type": self.court_type.value,
            "circuit": self.circuit,
            "district_code": self.district_code,
            "state": self.state,
            "city": self.city,
            "timezone": self.timezone,
            "pacer_url": self.pacer_url,
            "ecf_version": self.ecf_version,
            "login_type": self.login_type,
            "status": self.status.value,
            "maintenance_message": self.maintenance_message,
            "supports_mdl": self.supports_mdl,
            "supports_electronic_filing": self.supports_electronic_filing,
            "requires_special_access": self.requires_special_access,
            "max_query_days": self.max_query_days,
            "document_fee": self.document_fee,
            "search_fee": self.search_fee,
            "max_document_fee": self.max_document_fee,
            "business_hours_start": self.business_hours_start,
            "business_hours_end": self.business_hours_end,
            "excluded_holidays": self.excluded_holidays,
            "total_cases": self.total_cases,
            "active_cases": self.active_cases,
            "last_sync_date": self.last_sync_date,
            "aliases": self.aliases,
            "notes": self.notes,
            "metadata": self.metadata,
        }