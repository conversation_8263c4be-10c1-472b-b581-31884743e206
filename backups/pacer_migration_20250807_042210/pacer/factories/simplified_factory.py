# /src/services/pacer/_core_services/registry/simplified_factory.py

"""
Simplified Service Factory for PACER Core Services.

Phase 2C implementation providing auto-discovery and simplified dependency injection
for the 9 core PACER services.
"""

from __future__ import annotations
import inspect
import importlib
from typing import Any, Dict, List, Optional, Type, TYPE_CHECKING
from pathlib import Path

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import ConfigurationError, ServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol


class SimplifiedPacerServiceFactory:
    """
    Simplified factory for creating and managing PACER core services.
    
    Features:
    - Auto-discovery of core services
    - Automatic dependency resolution
    - Simplified configuration management
    - Lazy initialization
    - Singleton pattern for services
    """
    
    # Core service mapping (9 services)
    CORE_SERVICES = {
        'configuration': {
            'module': 'src.pacer._core_services.configuration.configuration_service',
            'class': 'ConfigurationService',
            'dependencies': []
        },
        'browser': {
            'module': 'src.pacer._core_services.browser.browser_service',
            'class': 'BrowserService',
            'dependencies': []
        },
        'case_processing': {
            'module': 'src.pacer._core_services.case_processing.case_processing_service',
            'class': 'CaseProcessingService',
            'dependencies': ['configuration']
        },
        'relevance': {
            'module': 'src.pacer._core_services.relevance.relevance_service',
            'class': 'RelevanceService',
            'dependencies': ['configuration']
        },
        'verification': {
            'module': 'src.pacer._core_services.verification.verification_service',
            'class': 'VerificationService',
            'dependencies': ['configuration']
        },
        'download_orchestration': {
            'module': 'src.pacer._core_services.download_orchestration.download_orchestration_service',
            'class': 'DownloadOrchestrationService',
            'dependencies': ['configuration', 'browser', 'verification']
        },
        'file_operations': {
            'module': 'src.pacer._core_services.file_operations.file_operations_service',
            'class': 'FileOperationsService',
            'dependencies': ['configuration']
        },
        'metrics_reporting': {
            'module': 'src.pacer._core_services.metrics_reporting.metrics_reporting_service',
            'class': 'MetricsReportingService',
            'dependencies': ['configuration']
        },
        's3_management': {
            'module': 'src.pacer._core_services.s3_management.s3_management_service',
            'class': 'S3ManagementService',
            'dependencies': ['configuration']
        }
    }
    
    def __init__(self,
                 config: Optional[Dict[str, Any]] = None,
                 logger: Optional[LoggerProtocol] = None):
        """
        Initialize the simplified service factory.
        
        Args:
            config: Configuration dictionary for services
            logger: Logger instance for the factory
        """
        self._config = config or {}
        self._logger = logger
        self._instances: Dict[str, AsyncServiceBase] = {}
        self._initialized = False
        
        # Auto-discovery flag
        self._auto_discover = self._config.get('auto_discover', True)
        
        # Service overrides for testing
        self._service_overrides: Dict[str, Type[AsyncServiceBase]] = {}

    async def initialize(self) -> None:
        """Initialize the factory and optionally auto-discover services."""
        if self._initialized:
            return
        
        if self._auto_discover:
            await self._discover_services()
        
        self._initialized = True
        
        if self._logger:
            self._logger.info("Simplified PACER Service Factory initialized")

    async def _discover_services(self) -> None:
        """
        Auto-discover available core services.
        
        This method scans the _core_services directory to find all available
        service implementations and registers them automatically.
        """
        if self._logger:
            self._logger.info("Auto-discovering core services...")
        
        core_services_path = Path(__file__).parent.parent  # _core_services directory
        
        # Scan for service modules
        discovered = 0
        for service_dir in core_services_path.iterdir():
            if service_dir.is_dir() and not service_dir.name.startswith('_'):
                # Look for service implementation
                service_files = list(service_dir.glob('*_service.py'))
                if service_files:
                    discovered += 1
                    if self._logger:
                        self._logger.debug(f"Discovered service in {service_dir.name}")
        
        if self._logger:
            self._logger.info(f"Auto-discovered {discovered} core services")

    async def get_service(self, service_name: str) -> AsyncServiceBase:
        """
        Get a service instance by name with automatic dependency resolution.
        
        Args:
            service_name: Name of the service (e.g., 'configuration', 'browser')
            
        Returns:
            Service instance
            
        Raises:
            ConfigurationError: If service is not found
            ServiceError: If service creation fails
        """
        if not self._initialized:
            await self.initialize()
        
        # Check if already instantiated
        if service_name in self._instances:
            return self._instances[service_name]
        
        # Check for overrides (useful for testing)
        if service_name in self._service_overrides:
            return await self._create_override_instance(service_name)
        
        # Get service definition
        if service_name not in self.CORE_SERVICES:
            raise ConfigurationError(f"Unknown service: {service_name}")
        
        # Create service with dependency resolution
        return await self._create_service_instance(service_name)

    async def _create_service_instance(self, service_name: str) -> AsyncServiceBase:
        """
        Create a service instance with automatic dependency resolution.
        
        Args:
            service_name: Name of the service to create
            
        Returns:
            Service instance
        """
        service_def = self.CORE_SERVICES[service_name]
        
        if self._logger:
            self._logger.debug(f"Creating service: {service_name}")
        
        # Resolve dependencies first
        dependencies = {}
        for dep_name in service_def['dependencies']:
            dependencies[dep_name + '_service'] = await self.get_service(dep_name)
        
        # Import service class
        try:
            module = importlib.import_module(service_def['module'])
            service_class = getattr(module, service_def['class'])
        except (ImportError, AttributeError) as e:
            raise ServiceError(f"Failed to import service {service_name}: {str(e)}")
        
        # Get service-specific configuration
        service_config = self._config.get(service_name, {})
        
        # Create instance with dependencies
        try:
            # Build constructor arguments
            kwargs = {
                'logger': self._logger,
                'config': service_config
            }
            kwargs.update(dependencies)
            
            # Create instance
            instance = service_class(**kwargs)
            
            # Initialize if needed
            if hasattr(instance, 'initialize'):
                await instance.initialize()
            
            # Cache instance
            self._instances[service_name] = instance
            
            if self._logger:
                self._logger.info(f"Service {service_name} created successfully")
            
            return instance
            
        except Exception as e:
            raise ServiceError(f"Failed to create service {service_name}: {str(e)}")

    async def _create_override_instance(self, service_name: str) -> AsyncServiceBase:
        """Create an override service instance (for testing)."""
        service_class = self._service_overrides[service_name]
        
        # Get configuration
        service_config = self._config.get(service_name, {})
        
        # Create instance
        instance = service_class(logger=self._logger, config=service_config)
        
        # Initialize if needed
        if hasattr(instance, 'initialize'):
            await instance.initialize()
        
        # Cache instance
        self._instances[service_name] = instance
        
        return instance

    def override_service(self, service_name: str, service_class: Type[AsyncServiceBase]) -> None:
        """
        Override a service implementation (useful for testing).
        
        Args:
            service_name: Name of the service to override
            service_class: Replacement service class
        """
        self._service_overrides[service_name] = service_class
        
        # Clear cached instance if exists
        if service_name in self._instances:
            del self._instances[service_name]

    async def get_all_services(self) -> Dict[str, AsyncServiceBase]:
        """
        Get all core service instances.
        
        Returns:
            Dictionary mapping service names to instances
        """
        if not self._initialized:
            await self.initialize()
        
        services = {}
        for service_name in self.CORE_SERVICES:
            services[service_name] = await self.get_service(service_name)
        
        return services

    async def create_orchestrator(self) -> Any:
        """
        Create the DocketProcessingOrchestrator with all required services.
        
        Returns:
            DocketProcessingOrchestrator instance
        """
        # Import orchestrator
        from src.pacer._core_services.docket_processing_orchestrator import (
            DocketProcessingOrchestrator
        )
        
        # Get all required services
        services = {
            'configuration_service': await self.get_service('configuration'),
            'browser_service': await self.get_service('browser'),
            'case_processing_service': await self.get_service('case_processing'),
            'relevance_service': await self.get_service('relevance'),
            'verification_service': await self.get_service('verification'),
            'download_orchestration_service': await self.get_service('download_orchestration'),
            'file_operations_service': await self.get_service('file_operations'),
            'metrics_reporting_service': await self.get_service('metrics_reporting'),
            's3_management_service': await self.get_service('s3_management')
        }
        
        # Create orchestrator
        orchestrator = DocketProcessingOrchestrator(
            logger=self._logger,
            config=self._config.get('orchestrator', {}),
            **services
        )
        
        # Initialize
        await orchestrator.initialize()
        
        return orchestrator

    async def shutdown(self) -> None:
        """
        Shutdown all services gracefully.
        """
        if self._logger:
            self._logger.info("Shutting down services...")
        
        # Shutdown in reverse dependency order
        shutdown_order = [
            's3_management',
            'metrics_reporting',
            'file_operations',
            'download_orchestration',
            'verification',
            'relevance',
            'case_processing',
            'browser',
            'configuration'
        ]
        
        for service_name in shutdown_order:
            if service_name in self._instances:
                service = self._instances[service_name]
                if hasattr(service, 'cleanup'):
                    try:
                        await service.cleanup()
                        if self._logger:
                            self._logger.debug(f"Service {service_name} shut down")
                    except Exception as e:
                        if self._logger:
                            self._logger.warning(f"Error shutting down {service_name}: {str(e)}")
        
        # Clear instances
        self._instances.clear()
        self._initialized = False
        
        if self._logger:
            self._logger.info("All services shut down successfully")

    def get_service_status(self) -> Dict[str, str]:
        """
        Get the status of all services.
        
        Returns:
            Dictionary mapping service names to status
        """
        status = {}
        
        for service_name in self.CORE_SERVICES:
            if service_name in self._instances:
                status[service_name] = 'initialized'
            else:
                status[service_name] = 'not_initialized'
        
        return status

    def get_dependency_graph(self) -> Dict[str, List[str]]:
        """
        Get the dependency graph for all services.
        
        Returns:
            Dictionary mapping service names to their dependencies
        """
        return {
            name: info['dependencies']
            for name, info in self.CORE_SERVICES.items()
        }


# Convenience function for creating factory
async def create_pacer_factory(
    config: Optional[Dict[str, Any]] = None,
    logger: Optional[LoggerProtocol] = None
) -> SimplifiedPacerServiceFactory:
    """
    Create and initialize a PACER service factory.
    
    Args:
        config: Configuration dictionary
        logger: Logger instance
        
    Returns:
        Initialized factory instance
    """
    factory = SimplifiedPacerServiceFactory(config, logger)
    await factory.initialize()
    return factory