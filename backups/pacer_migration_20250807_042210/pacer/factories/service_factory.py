"""
Service Factory and Dependency Injection Container for PACER Core Services.

This module provides the dependency injection container and factory patterns
for creating and managing the 9 core PACER services, following the DI pattern
from DESIGN_PATTERNS.md.
Moved from src/services/pacer/_core_services/registry/service_factory.py
"""

from __future__ import annotations
from typing import Any, Dict, Optional, TYPE_CHECKING

from dependency_injector import containers, providers
from src.pacer.registries.service_registry import PacerCoreServiceRegistry

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol


class PacerCoreContainer(containers.DeclarativeContainer):
    """
    Dependency injection container for PACER core services.
    
    Manages the creation and lifecycle of all core services and their dependencies,
    following the consolidation from 23+ facade services into 8 core services.
    """

    # Configuration providers
    config = providers.Configuration()
    logger = providers.Dependency()

    # Component providers (existing components from _components directories)
    # Configuration components
    json_config_loader = providers.Factory(
        "src.pacer._config_components.json_config_loader.JsonConfigLoader"
    )

    # Case processing components
    case_validator = providers.Factory(
        "src.pacer._case_processing_components.case_validator.CaseValidator",
        logger=logger
    )
    
    case_parser = providers.Factory(
        "src.pacer._case_processing_components.case_parser.CaseParser",
        logger=logger
    )
    
    case_enricher = providers.Factory(
        "src.pacer._case_processing_components.case_enricher.CaseEnricher",
        logger=logger
    )
    
    case_transformer = providers.Factory(
        "src.pacer._case_processing_components.case_transformer.CaseTransformer",
        logger=logger
    )
    
    html_parser = providers.Factory(
        "src.pacer._case_processing_components.html_parser.HtmlParser",
        logger=logger
    )

    # Classification components
    case_classifier = providers.Factory(
        "src.pacer._classification_components.case_classifier.CaseClassifier",
        logger=logger
    )

    # Transfer components
    transfer_processor = providers.Factory(
        "src.pacer._transfer_components.transfer_processor.TransferProcessor",
        logger=logger
    )

    # Verification components  
    case_verifier = providers.Factory(
        "src.pacer._verification_components.case_verifier.CaseVerifier",
        logger=logger
    )

    # Browser components
    playwright_manager = providers.Factory(
        "src.pacer._browser_components.playwright_manager.PlaywrightManager",
        logger=logger
    )
    
    navigator = providers.Factory(
        "src.pacer._browser_components.navigator.PacerNavigator",
        playwright_manager=playwright_manager,
        logger=logger
    )

    # Authentication components
    login_handler = providers.Factory(
        "src.pacer._authentication_components.login_handler.LoginHandler",
        logger=logger
    )
    
    session_manager = providers.Factory(
        "src.pacer._authentication_components.session_manager.SessionManager",
        logger=logger
    )

    # Download components
    download_validator = providers.Factory(
        "src.pacer._download_components.download_validator.DownloadValidator",
        logger=logger
    )
    
    file_downloader = providers.Factory(
        "src.pacer._download_components.file_downloader.FileDownloader",
        logger=logger
    )
    
    download_manager = providers.Factory(
        "src.pacer._download_components.download_manager.DownloadManager",
        logger=logger
    )

    # File components
    path_builder = providers.Factory(
        "src.pacer._file_components.path_builder.PathBuilder",
        logger=logger
    )
    
    directory_manager = providers.Factory(
        "src.pacer._file_components.directory_manager.DirectoryManager",
        logger=logger
    )
    
    file_manager = providers.Factory(
        "src.pacer._file_components.file_manager.FileManager",
        logger=logger
    )

    # Export components
    csv_exporter = providers.Factory(
        "src.pacer._export_components.csv_exporter.CSVExporter",
        logger=logger
    )

    # S3 components
    s3_manager = providers.Factory(
        "src.pacer._download_components.s3_manager.S3Manager",
        logger=logger
    )

    # Query components
    query_builder = providers.Factory(
        "src.pacer._query_components.query_builder.QueryBuilder",
        logger=logger
    )

    # Navigation components
    page_navigator = providers.Factory(
        "src.pacer._navigation_components.page_navigator.PageNavigator",
        logger=logger
    )

    # Report components
    report_generator = providers.Factory(
        "src.pacer._report_components.report_generator.ReportGenerator",
        logger=logger
    )

    # Processing components
    workflow_orchestrator = providers.Factory(
        "src.pacer._processing_components.workflow_orchestrator.WorkflowOrchestrator",
        logger=logger
    )

    # CORE SERVICE PROVIDERS - The 9 consolidated services

    # 1. Configuration Service
    configuration_service = providers.Singleton(
        "src.pacer._core_services.configuration.configuration_service.ConfigurationService",
        json_loader=json_config_loader,
        logger=logger,
        config=config.configuration
    )

    # 2. Browser Service (NEW - 9th core service)
    browser_service = providers.Singleton(
        "src.pacer._core_services.browser.browser_service.BrowserService",
        logger=logger,
        config=config.browser
    )

    # 3. Case Processing Service
    case_processing_service = providers.Singleton(
        "src.pacer._core_services.case_processing.case_processing_service.CaseProcessingService",
        case_validator=case_validator,
        case_parser=case_parser,
        case_enricher=case_enricher,
        case_transformer=case_transformer,
        case_classifier=case_classifier,
        html_parser=html_parser,
        transfer_processor=transfer_processor,
        logger=logger,
        config=config.case_processing
    )

    # 4. Relevance Service
    relevance_service = providers.Singleton(
        "src.pacer._core_services.relevance.relevance_service.RelevanceService",
        configuration_service=configuration_service,
        logger=logger,
        config=config.relevance
    )

    # 5. Classification Service
    classification_service = providers.Singleton(
        "src.pacer._core_services.classification.classification_service.ClassificationService",
        case_classifier=case_classifier,
        configuration_service=configuration_service,
        logger=logger,
        config=config.classification
    )

    # 6. Verification Service
    verification_service = providers.Singleton(
        "src.pacer._core_services.verification.verification_service.VerificationService",
        case_verifier=case_verifier,
        configuration_service=configuration_service,
        logger=logger,
        config=config.verification
    )

    # 7. Download Orchestration Service
    download_orchestration_service = providers.Singleton(
        "src.pacer._core_services.download_orchestration.download_orchestration_service.DownloadOrchestrationService",
        browser_service=browser_service,  # Use browser service instead of individual components
        download_validator=download_validator,
        file_downloader=file_downloader,
        download_manager=download_manager,
        query_builder=query_builder,
        page_navigator=page_navigator,
        verification_service=verification_service,
        logger=logger,
        config=config.download_orchestration
    )

    # 8. File Operations Service  
    file_operations_service = providers.Singleton(
        "src.pacer._core_services.file_operations.file_operations_service.FileOperationsService",
        path_builder=path_builder,
        directory_manager=directory_manager,
        file_manager=file_manager,
        s3_manager=s3_manager,
        csv_exporter=csv_exporter,
        report_generator=report_generator,
        configuration_service=configuration_service,
        logger=logger,
        config=config.file_operations
    )
    
    # 9. Metrics Reporting Service
    metrics_reporting_service = providers.Singleton(
        "src.pacer._core_services.metrics_reporting.metrics_reporting_service.MetricsReportingService",
        configuration_service=configuration_service,
        logger=logger,
        config=config.metrics_reporting
    )
    
    # 10. S3 Management Service
    s3_management_service = providers.Singleton(
        "src.pacer._core_services.s3_management.s3_management_service.S3ManagementService",
        configuration_service=configuration_service,
        s3_manager=s3_manager,
        logger=logger,
        config=config.s3_management
    )

    # 8. Docket Processor (Legacy)
    docket_processor = providers.Singleton(
        "src.pacer._core_services.docket_processor.docket_processor.DocketProcessor",
        workflow_orchestrator=workflow_orchestrator,
        configuration_service=configuration_service,
        case_processing_service=case_processing_service,
        download_orchestration_service=download_orchestration_service,
        file_operations_service=file_operations_service,
        logger=logger,
        config=config.docket_processor
    )

    # Service Registry
    service_registry = providers.Singleton(
        PacerCoreServiceRegistry,
        container=providers.Self(),
        logger=logger
    )


class PacerServiceFactory:
    """
    Factory for creating and managing PACER core services.
    
    Provides high-level interface for service creation and lifecycle management,
    wrapping the dependency injection container.
    """

    def __init__(self, 
                 config: Optional[Dict[str, Any]] = None,
                 logger: Optional[LoggerProtocol] = None):
        """
        Initialize the service factory.
        
        Args:
            config: Configuration dictionary for services
            logger: Logger instance for the factory
        """
        self._container = PacerCoreContainer()
        self._logger = logger
        self._initialized = False
        
        # Configure the container
        if config:
            self._container.config.update(config)
        
        if logger:
            self._container.logger.override(logger)

    async def initialize(self) -> None:
        """Initialize the service factory and container."""
        if self._initialized:
            return
        
        try:
            # Wire the container (9 core services)
            self._container.wire(modules=[
                "src.pacer._core_services.configuration.configuration_service",
                "src.pacer._core_services.browser.browser_service",
                "src.pacer._core_services.case_processing.case_processing_service",
                "src.pacer._core_services.relevance.relevance_service", 
                "src.pacer._core_services.classification.classification_service",
                "src.pacer._core_services.verification.verification_service",
                "src.pacer._core_services.download_orchestration.download_orchestration_service",
                "src.pacer._core_services.file_operations.file_operations_service",
                "src.pacer._core_services.metrics_reporting.metrics_reporting_service",
                "src.pacer._core_services.s3_management.s3_management_service",
                "src.pacer._core_services.docket_processing_orchestrator"
            ])
            
            self._initialized = True
            
            if self._logger:
                self._logger.info("PACER Service Factory initialized successfully")
                
        except Exception as e:
            if self._logger:
                self._logger.error(f"Failed to initialize PACER Service Factory: {str(e)}")
            raise

    async def get_service_registry(self) -> PacerCoreServiceRegistry:
        """Get the service registry instance."""
        if not self._initialized:
            await self.initialize()
        
        return self._container.service_registry()

    async def get_configuration_service(self):
        """Get the Configuration Service instance."""
        if not self._initialized:
            await self.initialize()
        
        return self._container.configuration_service()
    
    async def get_browser_service(self):
        """Get the Browser Service instance (9th core service)."""
        if not self._initialized:
            await self.initialize()
        
        return self._container.browser_service()

    async def get_case_processing_service(self):
        """Get the Case Processing Service instance."""
        if not self._initialized:
            await self.initialize()
        
        return self._container.case_processing_service()

    async def get_relevance_service(self):
        """Get the Relevance Service instance."""
        if not self._initialized:
            await self.initialize()
        
        return self._container.relevance_service()

    async def get_classification_service(self):
        """Get the Classification Service instance."""
        if not self._initialized:
            await self.initialize()
        
        return self._container.classification_service()

    async def get_verification_service(self):
        """Get the Verification Service instance."""
        if not self._initialized:
            await self.initialize()
        
        return self._container.verification_service()

    async def get_download_orchestration_service(self):
        """Get the Download Orchestration Service instance."""
        if not self._initialized:
            await self.initialize()
        
        return self._container.download_orchestration_service()

    async def get_file_operations_service(self):
        """Get the File Operations Service instance."""
        if not self._initialized:
            await self.initialize()
        
        return self._container.file_operations_service()
    
    async def get_metrics_reporting_service(self):
        """Get the Metrics Reporting Service instance."""
        if not self._initialized:
            await self.initialize()
        
        return self._container.metrics_reporting_service()
    
    async def get_s3_management_service(self):
        """Get the S3 Management Service instance."""
        if not self._initialized:
            await self.initialize()
        
        return self._container.s3_management_service()

    async def get_docket_processor(self):
        """Get the Docket Processor instance."""
        if not self._initialized:
            await self.initialize()
        
        return self._container.docket_processor()

    async def create_all_services(self) -> Dict[str, Any]:
        """Create all core services and return them as a dictionary."""
        if not self._initialized:
            await self.initialize()
        
        services = {
            "configuration_service": await self.get_configuration_service(),
            "case_processing_service": await self.get_case_processing_service(),
            "relevance_service": await self.get_relevance_service(),
            "classification_service": await self.get_classification_service(),
            "verification_service": await self.get_verification_service(),
            "download_orchestration_service": await self.get_download_orchestration_service(),
            "file_operations_service": await self.get_file_operations_service(),
            "docket_processor": await self.get_docket_processor()
        }
        
        if self._logger:
            self._logger.info("All PACER core services created successfully", extra={
                "services_created": list(services.keys())
            })
        
        return services

    async def shutdown(self) -> None:
        """Shutdown the factory and cleanup resources."""
        try:
            # Get service registry and shutdown all services
            registry = await self.get_service_registry()
            await registry.shutdown()
            
            # Unwire the container
            self._container.unwire()
            
            self._initialized = False
            
            if self._logger:
                self._logger.info("PACER Service Factory shutdown completed")
                
        except Exception as e:
            if self._logger:
                self._logger.error(f"Error during factory shutdown: {str(e)}")
            raise

    def get_container(self) -> PacerCoreContainer:
        """Get the underlying DI container."""
        return self._container

    def is_initialized(self) -> bool:
        """Check if the factory is initialized."""
        return self._initialized


# Convenience function for creating a fully configured service factory
async def create_pacer_service_factory(
    config: Optional[Dict[str, Any]] = None,
    logger: Optional[LoggerProtocol] = None
) -> PacerServiceFactory:
    """
    Create and initialize a PACER service factory with all core services.
    
    Args:
        config: Configuration for services
        logger: Logger instance
        
    Returns:
        Initialized service factory
    """
    factory = PacerServiceFactory(config=config, logger=logger)
    await factory.initialize()
    return factory