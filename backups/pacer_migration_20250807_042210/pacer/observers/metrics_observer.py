"""
Metrics observer implementations for PACER services.

Provides performance monitoring and metrics collection.
"""

from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import statistics
import asyncio

from src.infrastructure.protocols.logger import LoggerProtocol
from .event_bus import Event, EventType, Observer, AsyncObserver


class MetricType(Enum):
    """Types of metrics."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"
    RATE = "rate"


@dataclass
class MetricEvent(Event):
    """Event for metric recording."""
    metric_name: str = ""
    metric_type: MetricType = MetricType.COUNTER
    value: float = 0.0
    unit: str = ""
    tags: Dict[str, str] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.event_type:
            self.event_type = EventType.METRIC_RECORDED


@dataclass
class MetricData:
    """Container for metric data points."""
    name: str
    metric_type: MetricType
    unit: str = ""
    values: List[float] = field(default_factory=list)
    timestamps: List[datetime] = field(default_factory=list)
    tags: Dict[str, str] = field(default_factory=dict)
    
    def add_value(self, value: float, timestamp: Optional[datetime] = None) -> None:
        """Add a new value to the metric."""
        self.values.append(value)
        self.timestamps.append(timestamp or datetime.now())
        
        # Keep only recent values (last 1000)
        if len(self.values) > 1000:
            self.values = self.values[-1000:]
            self.timestamps = self.timestamps[-1000:]
    
    def get_statistics(self) -> Dict[str, float]:
        """Calculate statistics for the metric."""
        if not self.values:
            return {}
        
        stats = {
            'count': len(self.values),
            'sum': sum(self.values),
            'min': min(self.values),
            'max': max(self.values),
            'mean': statistics.mean(self.values),
        }
        
        if len(self.values) > 1:
            stats['median'] = statistics.median(self.values)
            stats['stdev'] = statistics.stdev(self.values)
        
        # Calculate rate for recent values
        if len(self.timestamps) > 1:
            time_diff = (self.timestamps[-1] - self.timestamps[0]).total_seconds()
            if time_diff > 0:
                stats['rate'] = len(self.values) / time_diff
        
        return stats
    
    def get_recent_values(self, seconds: int = 60) -> List[float]:
        """Get values from the last N seconds."""
        cutoff = datetime.now() - timedelta(seconds=seconds)
        recent = []
        
        for value, timestamp in zip(self.values, self.timestamps):
            if timestamp >= cutoff:
                recent.append(value)
        
        return recent


class MetricsObserver(Observer):
    """
    Observer for collecting and aggregating metrics.
    """
    
    def __init__(self, logger: LoggerProtocol):
        self.logger = logger
        self._metrics: Dict[str, MetricData] = {}
        self._thresholds: Dict[str, float] = {}
        self._alert_callbacks: List[Callable] = []
    
    def handle(self, event: Event) -> None:
        """Handle metric events."""
        if isinstance(event, MetricEvent):
            self._record_metric(event)
    
    def _record_metric(self, metric_event: MetricEvent) -> None:
        """Record a metric value."""
        key = self._get_metric_key(metric_event)
        
        if key not in self._metrics:
            self._metrics[key] = MetricData(
                name=metric_event.metric_name,
                metric_type=metric_event.metric_type,
                unit=metric_event.unit,
                tags=metric_event.tags
            )
        
        self._metrics[key].add_value(metric_event.value, metric_event.timestamp)
        
        # Check thresholds
        self._check_threshold(key, metric_event.value)
        
        # Log if configured
        if self.logger.level <= 10:  # DEBUG level
            self.logger.debug(
                f"Metric: {metric_event.metric_name} = {metric_event.value} {metric_event.unit}"
            )
    
    def _get_metric_key(self, metric_event: MetricEvent) -> str:
        """Generate unique key for metric."""
        tag_str = ",".join(f"{k}={v}" for k, v in sorted(metric_event.tags.items()))
        return f"{metric_event.metric_name}:{tag_str}" if tag_str else metric_event.metric_name
    
    def _check_threshold(self, key: str, value: float) -> None:
        """Check if metric exceeds threshold."""
        if key in self._thresholds:
            threshold = self._thresholds[key]
            if value > threshold:
                self._trigger_threshold_alert(key, value, threshold)
    
    def _trigger_threshold_alert(self, metric_key: str, value: float, threshold: float) -> None:
        """Trigger alert for threshold breach."""
        self.logger.warning(
            f"Metric threshold exceeded: {metric_key} = {value} (threshold: {threshold})"
        )
        
        # Notify callbacks
        for callback in self._alert_callbacks:
            try:
                callback(metric_key, value, threshold)
            except Exception as e:
                self.logger.error(f"Alert callback failed: {str(e)}")
    
    def set_threshold(self, metric_name: str, threshold: float) -> None:
        """Set threshold for a metric."""
        self._thresholds[metric_name] = threshold
    
    def add_alert_callback(self, callback: Callable) -> None:
        """Add callback for threshold alerts."""
        self._alert_callbacks.append(callback)
    
    def get_metric(self, metric_name: str) -> Optional[MetricData]:
        """Get a specific metric."""
        return self._metrics.get(metric_name)
    
    def get_all_metrics(self) -> Dict[str, MetricData]:
        """Get all metrics."""
        return self._metrics.copy()
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of all metrics."""
        summary = {}
        
        for key, metric in self._metrics.items():
            stats = metric.get_statistics()
            summary[key] = {
                'type': metric.metric_type.value,
                'unit': metric.unit,
                'stats': stats,
                'tags': metric.tags
            }
        
        return summary


class MetricsCollector(AsyncObserver):
    """
    Async observer for advanced metrics collection.
    """
    
    def __init__(self, logger: LoggerProtocol):
        self.logger = logger
        self._metrics: Dict[str, MetricData] = {}
        self._lock = asyncio.Lock()
        self._aggregation_interval = 60  # seconds
        self._aggregation_task: Optional[asyncio.Task] = None
    
    async def handle(self, event: Event) -> None:
        """Handle metric events asynchronously."""
        if isinstance(event, MetricEvent):
            await self._collect_metric(event)
    
    async def _collect_metric(self, metric_event: MetricEvent) -> None:
        """Collect metric data."""
        async with self._lock:
            key = self._get_metric_key(metric_event)
            
            if key not in self._metrics:
                self._metrics[key] = MetricData(
                    name=metric_event.metric_name,
                    metric_type=metric_event.metric_type,
                    unit=metric_event.unit,
                    tags=metric_event.tags
                )
            
            self._metrics[key].add_value(metric_event.value, metric_event.timestamp)
    
    def _get_metric_key(self, metric_event: MetricEvent) -> str:
        """Generate unique key for metric."""
        tag_str = ",".join(f"{k}={v}" for k, v in sorted(metric_event.tags.items()))
        return f"{metric_event.metric_name}:{tag_str}" if tag_str else metric_event.metric_name
    
    async def start_aggregation(self) -> None:
        """Start periodic aggregation task."""
        if not self._aggregation_task:
            self._aggregation_task = asyncio.create_task(self._aggregate_periodically())
    
    async def stop_aggregation(self) -> None:
        """Stop aggregation task."""
        if self._aggregation_task:
            self._aggregation_task.cancel()
            try:
                await self._aggregation_task
            except asyncio.CancelledError:
                pass
            self._aggregation_task = None
    
    async def _aggregate_periodically(self) -> None:
        """Periodically aggregate metrics."""
        while True:
            await asyncio.sleep(self._aggregation_interval)
            await self._aggregate_metrics()
    
    async def _aggregate_metrics(self) -> None:
        """Aggregate current metrics."""
        async with self._lock:
            for key, metric in self._metrics.items():
                stats = metric.get_statistics()
                
                if stats:
                    self.logger.info(
                        f"Metric aggregate - {metric.name}: "
                        f"mean={stats.get('mean', 0):.2f}, "
                        f"count={stats.get('count', 0)}"
                    )
    
    async def get_metrics_report(self) -> Dict[str, Any]:
        """Generate comprehensive metrics report."""
        async with self._lock:
            report = {
                'timestamp': datetime.now().isoformat(),
                'metrics': {},
                'summary': {
                    'total_metrics': len(self._metrics),
                    'total_data_points': sum(len(m.values) for m in self._metrics.values())
                }
            }
            
            for key, metric in self._metrics.items():
                stats = metric.get_statistics()
                recent_values = metric.get_recent_values(60)
                
                report['metrics'][key] = {
                    'name': metric.name,
                    'type': metric.metric_type.value,
                    'unit': metric.unit,
                    'statistics': stats,
                    'recent_count': len(recent_values),
                    'recent_mean': statistics.mean(recent_values) if recent_values else 0,
                    'tags': metric.tags
                }
            
            return report


class PerformanceMonitor(Observer):
    """
    Monitor for tracking performance metrics.
    """
    
    def __init__(self, logger: LoggerProtocol):
        self.logger = logger
        self._timers: Dict[str, datetime] = {}
        self._performance_data: Dict[str, List[float]] = {}
    
    def start_timer(self, operation: str) -> None:
        """Start timing an operation."""
        self._timers[operation] = datetime.now()
    
    def end_timer(self, operation: str) -> float:
        """End timing and record duration."""
        if operation not in self._timers:
            self.logger.warning(f"Timer not started for operation: {operation}")
            return 0.0
        
        start_time = self._timers.pop(operation)
        duration = (datetime.now() - start_time).total_seconds()
        
        # Record performance data
        if operation not in self._performance_data:
            self._performance_data[operation] = []
        
        self._performance_data[operation].append(duration)
        
        # Keep only recent measurements
        if len(self._performance_data[operation]) > 100:
            self._performance_data[operation] = self._performance_data[operation][-100:]
        
        return duration
    
    def handle(self, event: Event) -> None:
        """Handle performance-related events."""
        # Handle specific performance events
        if event.event_type == EventType.PROCESSING_STARTED:
            operation = event.data.get('operation', 'unknown')
            self.start_timer(operation)
        
        elif event.event_type == EventType.PROCESSING_COMPLETED:
            operation = event.data.get('operation', 'unknown')
            duration = self.end_timer(operation)
            
            if duration > 0:
                self.logger.info(f"Operation '{operation}' completed in {duration:.2f}s")
    
    def get_performance_stats(self, operation: Optional[str] = None) -> Dict[str, Any]:
        """Get performance statistics."""
        if operation:
            if operation not in self._performance_data:
                return {}
            
            values = self._performance_data[operation]
            return {
                'operation': operation,
                'count': len(values),
                'min': min(values) if values else 0,
                'max': max(values) if values else 0,
                'mean': statistics.mean(values) if values else 0,
                'median': statistics.median(values) if len(values) > 1 else 0
            }
        
        # Return stats for all operations
        all_stats = {}
        for op, values in self._performance_data.items():
            all_stats[op] = {
                'count': len(values),
                'mean': statistics.mean(values) if values else 0
            }
        
        return all_stats