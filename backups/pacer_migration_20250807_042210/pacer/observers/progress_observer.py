"""
Progress observer implementations for PACER services.

Provides observers for tracking progress of various operations.
"""

from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from datetime import datetime
import asyncio
from enum import Enum

from src.infrastructure.protocols.logger import LoggerProtocol
from .event_bus import Event, EventType, Observer, AsyncObserver


class ProgressStatus(Enum):
    """Progress status types."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ProgressEvent(Event):
    """Event for progress updates."""
    task_id: str = ""
    task_type: str = ""
    current: int = 0
    total: int = 0
    percentage: float = 0.0
    status: ProgressStatus = ProgressStatus.NOT_STARTED
    message: str = ""
    
    def __post_init__(self):
        # Calculate percentage if not provided
        if self.total > 0 and self.percentage == 0.0:
            self.percentage = (self.current / self.total) * 100
        
        # Set event type if not set
        if not self.event_type:
            if self.status == ProgressStatus.COMPLETED:
                self.event_type = EventType.PROCESSING_COMPLETED
            elif self.status == ProgressStatus.FAILED:
                self.event_type = EventType.PROCESSING_FAILED
            else:
                self.event_type = EventType.PROCESSING_STARTED


@dataclass
class TaskProgress:
    """Tracks progress for a single task."""
    task_id: str
    task_type: str
    total: int
    current: int = 0
    status: ProgressStatus = ProgressStatus.NOT_STARTED
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    last_updated: Optional[datetime] = None
    errors: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def percentage(self) -> float:
        """Calculate completion percentage."""
        if self.total == 0:
            return 0.0
        return (self.current / self.total) * 100
    
    @property
    def is_complete(self) -> bool:
        """Check if task is complete."""
        return self.status in [ProgressStatus.COMPLETED, ProgressStatus.FAILED, ProgressStatus.CANCELLED]
    
    @property
    def duration(self) -> Optional[float]:
        """Get task duration in seconds."""
        if self.started_at:
            end_time = self.completed_at or datetime.now()
            return (end_time - self.started_at).total_seconds()
        return None
    
    def update(self, current: int, message: str = "") -> None:
        """Update progress."""
        self.current = min(current, self.total)
        self.last_updated = datetime.now()
        
        if self.status == ProgressStatus.NOT_STARTED:
            self.status = ProgressStatus.IN_PROGRESS
            self.started_at = datetime.now()
        
        if self.current >= self.total:
            self.status = ProgressStatus.COMPLETED
            self.completed_at = datetime.now()
    
    def fail(self, error: str) -> None:
        """Mark task as failed."""
        self.status = ProgressStatus.FAILED
        self.errors.append(error)
        self.completed_at = datetime.now()
    
    def cancel(self) -> None:
        """Cancel the task."""
        self.status = ProgressStatus.CANCELLED
        self.completed_at = datetime.now()


class ProgressObserver(Observer):
    """
    Observer for tracking progress events.
    """
    
    def __init__(self, logger: LoggerProtocol, 
                 callback: Optional[Callable[[ProgressEvent], None]] = None):
        self.logger = logger
        self.callback = callback
        self._tasks: Dict[str, TaskProgress] = {}
    
    def handle(self, event: Event) -> None:
        """Handle progress events."""
        if isinstance(event, ProgressEvent):
            self._update_task_progress(event)
            
            # Log progress
            self.logger.info(
                f"Progress: {event.task_type} [{event.task_id}] - "
                f"{event.current}/{event.total} ({event.percentage:.1f}%) - {event.status.value}"
            )
            
            # Call callback if provided
            if self.callback:
                self.callback(event)
    
    def _update_task_progress(self, event: ProgressEvent) -> None:
        """Update internal task tracking."""
        if event.task_id not in self._tasks:
            self._tasks[event.task_id] = TaskProgress(
                task_id=event.task_id,
                task_type=event.task_type,
                total=event.total
            )
        
        task = self._tasks[event.task_id]
        
        if event.status == ProgressStatus.FAILED:
            task.fail(event.message)
        elif event.status == ProgressStatus.CANCELLED:
            task.cancel()
        else:
            task.update(event.current, event.message)
            task.status = event.status
    
    def get_task_progress(self, task_id: str) -> Optional[TaskProgress]:
        """Get progress for a specific task."""
        return self._tasks.get(task_id)
    
    def get_all_progress(self) -> Dict[str, TaskProgress]:
        """Get all task progress."""
        return self._tasks.copy()
    
    def get_active_tasks(self) -> List[TaskProgress]:
        """Get all active tasks."""
        return [
            task for task in self._tasks.values()
            if task.status == ProgressStatus.IN_PROGRESS
        ]
    
    def clear_completed(self) -> None:
        """Clear completed tasks from tracking."""
        self._tasks = {
            task_id: task
            for task_id, task in self._tasks.items()
            if not task.is_complete
        }


class ProgressTracker(AsyncObserver):
    """
    Async observer for tracking and aggregating progress.
    """
    
    def __init__(self, logger: LoggerProtocol):
        self.logger = logger
        self._tasks: Dict[str, TaskProgress] = {}
        self._subscribers: List[Callable] = []
        self._lock = asyncio.Lock()
    
    async def handle(self, event: Event) -> None:
        """Handle progress events asynchronously."""
        if isinstance(event, ProgressEvent):
            async with self._lock:
                self._update_task_progress(event)
            
            # Notify subscribers
            await self._notify_subscribers(event)
    
    def _update_task_progress(self, event: ProgressEvent) -> None:
        """Update task progress tracking."""
        if event.task_id not in self._tasks:
            self._tasks[event.task_id] = TaskProgress(
                task_id=event.task_id,
                task_type=event.task_type,
                total=event.total
            )
        
        task = self._tasks[event.task_id]
        task.update(event.current, event.message)
        task.status = event.status
    
    async def _notify_subscribers(self, event: ProgressEvent) -> None:
        """Notify all subscribers of progress update."""
        notifications = []
        
        for subscriber in self._subscribers:
            if asyncio.iscoroutinefunction(subscriber):
                notifications.append(subscriber(event))
            else:
                # Run sync callbacks in executor
                loop = asyncio.get_event_loop()
                notifications.append(
                    loop.run_in_executor(None, subscriber, event)
                )
        
        if notifications:
            await asyncio.gather(*notifications, return_exceptions=True)
    
    def subscribe(self, callback: Callable) -> None:
        """Subscribe to progress updates."""
        self._subscribers.append(callback)
    
    def unsubscribe(self, callback: Callable) -> None:
        """Unsubscribe from progress updates."""
        if callback in self._subscribers:
            self._subscribers.remove(callback)
    
    async def get_overall_progress(self) -> Dict[str, Any]:
        """Get overall progress statistics."""
        async with self._lock:
            total_tasks = len(self._tasks)
            completed_tasks = sum(1 for t in self._tasks.values() if t.is_complete)
            active_tasks = sum(1 for t in self._tasks.values() if t.status == ProgressStatus.IN_PROGRESS)
            failed_tasks = sum(1 for t in self._tasks.values() if t.status == ProgressStatus.FAILED)
            
            # Calculate overall percentage
            if total_tasks > 0:
                overall_percentage = (completed_tasks / total_tasks) * 100
            else:
                overall_percentage = 0.0
            
            return {
                'total_tasks': total_tasks,
                'completed_tasks': completed_tasks,
                'active_tasks': active_tasks,
                'failed_tasks': failed_tasks,
                'overall_percentage': overall_percentage,
                'tasks': {
                    task_id: {
                        'type': task.task_type,
                        'percentage': task.percentage,
                        'status': task.status.value,
                        'duration': task.duration
                    }
                    for task_id, task in self._tasks.items()
                }
            }


class BatchProgressObserver(Observer):
    """
    Observer for tracking progress of batch operations.
    """
    
    def __init__(self, logger: LoggerProtocol, batch_size: int):
        self.logger = logger
        self.batch_size = batch_size
        self._batches: Dict[str, List[TaskProgress]] = {}
        self._batch_start_times: Dict[str, datetime] = {}
    
    def handle(self, event: Event) -> None:
        """Handle batch progress events."""
        if isinstance(event, ProgressEvent):
            batch_id = event.metadata.get('batch_id')
            
            if batch_id:
                self._track_batch_progress(batch_id, event)
    
    def _track_batch_progress(self, batch_id: str, event: ProgressEvent) -> None:
        """Track progress for a batch."""
        if batch_id not in self._batches:
            self._batches[batch_id] = []
            self._batch_start_times[batch_id] = datetime.now()
        
        # Create or update task progress
        task_progress = TaskProgress(
            task_id=event.task_id,
            task_type=event.task_type,
            total=event.total,
            current=event.current,
            status=event.status
        )
        
        # Add to batch
        self._batches[batch_id].append(task_progress)
        
        # Check if batch is complete
        if len(self._batches[batch_id]) >= self.batch_size:
            self._report_batch_completion(batch_id)
    
    def _report_batch_completion(self, batch_id: str) -> None:
        """Report batch completion statistics."""
        batch_tasks = self._batches[batch_id]
        start_time = self._batch_start_times[batch_id]
        duration = (datetime.now() - start_time).total_seconds()
        
        completed = sum(1 for t in batch_tasks if t.status == ProgressStatus.COMPLETED)
        failed = sum(1 for t in batch_tasks if t.status == ProgressStatus.FAILED)
        
        self.logger.info(
            f"Batch {batch_id} completed: "
            f"{completed}/{len(batch_tasks)} successful, "
            f"{failed} failed, "
            f"duration: {duration:.2f}s"
        )
        
        # Clear batch data
        del self._batches[batch_id]
        del self._batch_start_times[batch_id]
    
    def get_batch_status(self, batch_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific batch."""
        if batch_id not in self._batches:
            return None
        
        batch_tasks = self._batches[batch_id]
        start_time = self._batch_start_times[batch_id]
        
        return {
            'batch_id': batch_id,
            'size': len(batch_tasks),
            'target_size': self.batch_size,
            'completed': sum(1 for t in batch_tasks if t.is_complete),
            'duration': (datetime.now() - start_time).total_seconds(),
            'tasks': batch_tasks
        }