# /src/services/pacer/jobs/docket_job.py
import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict

from playwright.async_api import Page

from src.pacer._core_services.case_processing.case_processing_service import CaseProcessingService
from src.pacer._core_services.relevance.relevance_service import RelevanceService


@dataclass
class DocketProcessingJob:
    """A self-contained unit of work for processing a single docket."""

    # Inputs
    court_id: str
    row_num: int
    total_rows: int
    docket_num: str  # CRITICAL: Docket number from civil report
    docket_link_href: str
    initial_versus_text: str
    initial_filing_date: str
    relevance_engine: RelevanceService
    config: Dict[str, Any]
    file_management_service: Any
    download_orchestration_service: Any
    case_processing_service: CaseProcessingService
    file_operations_service: Any
    file_manager: Any
    court_logger: logging.Logger
    processor_config: Dict[str, Any] = field(default_factory=dict)

    # State
    page: Page | None = None
    html_content: str | None = None
    case_details: Dict[str, Any] = field(default_factory=dict)
    base_filename: str | None = None
    status: str = "pending"  # e.g., pending, success, failed, skipped
    error_message: str | None = None
    download_path: Path | None = None
    artifacts_downloaded: list[str] = field(default_factory=list)

    # Removal-specific fields
    is_removal_case: bool = False
    removal_link_number: str | None = None
