"""
Authentication strategy implementations for PACER services.

Provides different authentication strategies for various PACER systems:
- ECF (Electronic Court Filing)
- NextGen PACER
- Legacy PACER
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass
import asyncio
from datetime import datetime, timedelta

from src.infrastructure.protocols.logger import LoggerProtocol


@dataclass
class Session:
    """Authentication session data."""
    token: str
    court_id: str
    username: str
    expires_at: datetime
    session_type: str
    metadata: Dict[str, Any] = None
    
    def is_valid(self) -> bool:
        """Check if session is still valid."""
        return datetime.now() < self.expires_at


class AuthenticationStrategy(ABC):
    """Base authentication strategy interface."""
    
    def __init__(self, logger: LoggerProtocol, config: Dict[str, Any]):
        self.logger = logger
        self.config = config
        self._session_cache: Dict[str, Session] = {}
    
    @abstractmethod
    async def authenticate(self, credentials: Dict[str, Any]) -> Session:
        """
        Authenticate with the specific PACER system.
        
        Args:
            credentials: Authentication credentials
            
        Returns:
            Session object with authentication details
        """
        pass
    
    @abstractmethod
    async def refresh_session(self, session: Session) -> Session:
        """Refresh an existing session."""
        pass
    
    @abstractmethod
    async def logout(self, session: Session) -> None:
        """Logout and invalidate session."""
        pass
    
    def get_cached_session(self, court_id: str) -> Optional[Session]:
        """Get cached session if valid."""
        session = self._session_cache.get(court_id)
        if session and session.is_valid():
            return session
        return None
    
    def cache_session(self, session: Session) -> None:
        """Cache a valid session."""
        self._session_cache[session.court_id] = session


class ECFAuthStrategy(AuthenticationStrategy):
    """Authentication strategy for ECF (Electronic Court Filing) system."""
    
    async def authenticate(self, credentials: Dict[str, Any]) -> Session:
        """Authenticate with ECF system."""
        self.logger.info(f"Authenticating with ECF for court: {credentials.get('court_id')}")
        
        # Check cache first
        cached = self.get_cached_session(credentials['court_id'])
        if cached:
            self.logger.debug("Using cached ECF session")
            return cached
        
        try:
            # ECF-specific authentication logic
            username = credentials['username']
            password = credentials['password']
            court_id = credentials['court_id']
            
            # Simulate ECF authentication (replace with actual implementation)
            await asyncio.sleep(0.5)  # Simulate network delay
            
            # Create session
            session = Session(
                token=f"ecf_token_{court_id}_{username}",
                court_id=court_id,
                username=username,
                expires_at=datetime.now() + timedelta(hours=8),
                session_type="ECF",
                metadata={
                    'login_time': datetime.now().isoformat(),
                    'court_name': credentials.get('court_name', ''),
                    'ecf_version': '5.0'
                }
            )
            
            # Cache the session
            self.cache_session(session)
            
            self.logger.info(f"ECF authentication successful for {username} at {court_id}")
            return session
            
        except Exception as e:
            self.logger.error(f"ECF authentication failed: {str(e)}")
            raise
    
    async def refresh_session(self, session: Session) -> Session:
        """Refresh ECF session."""
        self.logger.debug(f"Refreshing ECF session for {session.court_id}")
        
        # ECF session refresh logic
        await asyncio.sleep(0.2)
        
        # Extend expiration
        session.expires_at = datetime.now() + timedelta(hours=8)
        self.cache_session(session)
        
        return session
    
    async def logout(self, session: Session) -> None:
        """Logout from ECF."""
        self.logger.info(f"Logging out from ECF: {session.court_id}")
        
        # Remove from cache
        if session.court_id in self._session_cache:
            del self._session_cache[session.court_id]
        
        # ECF logout logic
        await asyncio.sleep(0.1)


class NextGenAuthStrategy(AuthenticationStrategy):
    """Authentication strategy for NextGen PACER system."""
    
    async def authenticate(self, credentials: Dict[str, Any]) -> Session:
        """Authenticate with NextGen PACER."""
        self.logger.info("Authenticating with NextGen PACER")
        
        # Check for cached session
        court_id = credentials.get('court_id', 'nextgen')
        cached = self.get_cached_session(court_id)
        if cached:
            self.logger.debug("Using cached NextGen session")
            return cached
        
        try:
            # NextGen specific authentication
            username = credentials['username']
            password = credentials['password']
            
            # NextGen uses OAuth-like flow
            await asyncio.sleep(0.7)  # Simulate OAuth flow
            
            # Create session with NextGen specifics
            session = Session(
                token=f"nextgen_bearer_{username}",
                court_id=court_id,
                username=username,
                expires_at=datetime.now() + timedelta(hours=12),
                session_type="NextGen",
                metadata={
                    'login_time': datetime.now().isoformat(),
                    'api_version': '2.0',
                    'refresh_token': f"refresh_{username}",
                    'scopes': ['read', 'write', 'download']
                }
            )
            
            self.cache_session(session)
            
            self.logger.info(f"NextGen authentication successful for {username}")
            return session
            
        except Exception as e:
            self.logger.error(f"NextGen authentication failed: {str(e)}")
            raise
    
    async def refresh_session(self, session: Session) -> Session:
        """Refresh NextGen session using refresh token."""
        self.logger.debug("Refreshing NextGen session")
        
        # Use refresh token
        refresh_token = session.metadata.get('refresh_token')
        if not refresh_token:
            raise ValueError("No refresh token available")
        
        await asyncio.sleep(0.3)
        
        # Update session
        session.expires_at = datetime.now() + timedelta(hours=12)
        session.token = f"nextgen_bearer_refreshed_{session.username}"
        
        self.cache_session(session)
        return session
    
    async def logout(self, session: Session) -> None:
        """Logout from NextGen PACER."""
        self.logger.info("Logging out from NextGen PACER")
        
        # Revoke tokens
        await asyncio.sleep(0.2)
        
        # Clear cache
        if session.court_id in self._session_cache:
            del self._session_cache[session.court_id]


class PacerAuthStrategy(AuthenticationStrategy):
    """Authentication strategy for legacy PACER system."""
    
    async def authenticate(self, credentials: Dict[str, Any]) -> Session:
        """Authenticate with legacy PACER."""
        self.logger.info(f"Authenticating with legacy PACER for court: {credentials.get('court_id')}")
        
        court_id = credentials.get('court_id', 'pacer')
        cached = self.get_cached_session(court_id)
        if cached:
            self.logger.debug("Using cached PACER session")
            return cached
        
        try:
            # Legacy PACER authentication
            username = credentials['username']
            password = credentials['password']
            
            # Simple session-based auth
            await asyncio.sleep(0.4)
            
            session = Session(
                token=f"pacer_session_{username}",
                court_id=court_id,
                username=username,
                expires_at=datetime.now() + timedelta(hours=4),
                session_type="PACER",
                metadata={
                    'login_time': datetime.now().isoformat(),
                    'session_id': f"sess_{court_id}_{username}"
                }
            )
            
            self.cache_session(session)
            
            self.logger.info(f"PACER authentication successful for {username}")
            return session
            
        except Exception as e:
            self.logger.error(f"PACER authentication failed: {str(e)}")
            raise
    
    async def refresh_session(self, session: Session) -> Session:
        """Refresh PACER session."""
        self.logger.debug("Refreshing PACER session")
        
        # Legacy PACER requires re-authentication
        self.logger.warning("Legacy PACER requires re-authentication for session refresh")
        
        # For now, extend the session
        session.expires_at = datetime.now() + timedelta(hours=4)
        self.cache_session(session)
        
        return session
    
    async def logout(self, session: Session) -> None:
        """Logout from legacy PACER."""
        self.logger.info("Logging out from legacy PACER")
        
        await asyncio.sleep(0.1)
        
        if session.court_id in self._session_cache:
            del self._session_cache[session.court_id]


class AuthenticationContext:
    """Context for managing authentication strategies."""
    
    def __init__(self, logger: LoggerProtocol, config: Dict[str, Any]):
        self.logger = logger
        self.config = config
        self._strategy: Optional[AuthenticationStrategy] = None
        self._current_session: Optional[Session] = None
        
        # Initialize available strategies
        self._strategies = {
            'ecf': ECFAuthStrategy(logger, config),
            'nextgen': NextGenAuthStrategy(logger, config),
            'pacer': PacerAuthStrategy(logger, config)
        }
    
    def set_strategy(self, strategy_type: str) -> None:
        """Set the authentication strategy."""
        if strategy_type not in self._strategies:
            raise ValueError(f"Unknown authentication strategy: {strategy_type}")
        
        self._strategy = self._strategies[strategy_type]
        self.logger.debug(f"Authentication strategy set to: {strategy_type}")
    
    async def authenticate(self, credentials: Dict[str, Any]) -> Session:
        """Authenticate using the current strategy."""
        if not self._strategy:
            # Auto-detect strategy based on credentials
            self._auto_select_strategy(credentials)
        
        self._current_session = await self._strategy.authenticate(credentials)
        return self._current_session
    
    async def refresh_session(self) -> Session:
        """Refresh the current session."""
        if not self._current_session:
            raise ValueError("No active session to refresh")
        
        self._current_session = await self._strategy.refresh_session(self._current_session)
        return self._current_session
    
    async def logout(self) -> None:
        """Logout current session."""
        if self._current_session and self._strategy:
            await self._strategy.logout(self._current_session)
            self._current_session = None
    
    def _auto_select_strategy(self, credentials: Dict[str, Any]) -> None:
        """Auto-select strategy based on credentials."""
        court_id = credentials.get('court_id', '')
        
        if 'ecf' in court_id.lower() or credentials.get('use_ecf'):
            self.set_strategy('ecf')
        elif credentials.get('use_nextgen') or credentials.get('api_version') == '2.0':
            self.set_strategy('nextgen')
        else:
            self.set_strategy('pacer')
        
        self.logger.info(f"Auto-selected authentication strategy: {self._strategy.__class__.__name__}")
    
    def get_current_session(self) -> Optional[Session]:
        """Get the current active session."""
        return self._current_session
    
    def is_authenticated(self) -> bool:
        """Check if currently authenticated."""
        return self._current_session is not None and self._current_session.is_valid()