"""
Builder pattern implementations for PACER services.

This module provides builder classes to simplify the construction of PACER services
with many parameters, making the code more readable and maintainable.
Follows the same pattern as src/services/transformer/builders.py
"""
from typing import Any, Dict, Optional, TypeVar, Generic, Union
from abc import ABC

from src.infrastructure.protocols.logger import LoggerProtocol
from src.infrastructure.patterns.component_base import AsyncServiceBase


T = TypeVar('T')


class ServiceBuilder(Generic[T]):
    """
    Base builder class for PACER services.
    
    Provides a fluent interface for building services with many parameters.
    """
    
    def __init__(self, service_class: type[T]):
        self.service_class = service_class
        self._config: Dict[str, Any] = {}
        self._logger: Optional[LoggerProtocol] = None
        self._params: Dict[str, Any] = {}
    
    def with_config(self, config: Union[Dict[str, Any], Any]) -> 'ServiceBuilder[T]':
        """Set the configuration."""
        if hasattr(config, 'to_dict'):
            self._config = config.to_dict()
        else:
            self._config = config
        return self
    
    def with_logger(self, logger: LoggerProtocol) -> 'ServiceBuilder[T]':
        """Set the logger."""
        self._logger = logger
        return self
    
    def with_param(self, name: str, value: Any) -> 'ServiceBuilder[T]':
        """Set a single parameter."""
        self._params[name] = value
        return self
    
    def with_params(self, **params) -> 'ServiceBuilder[T]':
        """Set multiple parameters."""
        self._params.update(params)
        return self
    
    def build(self) -> T:
        """Build the service instance."""
        # Merge all parameters
        all_params = {
            'config': self._config,
            'logger': self._logger,
            **self._params
        }
        
        # Remove None values
        all_params = {k: v for k, v in all_params.items() if v is not None}
        
        return self.service_class(**all_params)


class DocketProcessingOrchestratorBuilder(ServiceBuilder):
    """
    Builder for DocketProcessingOrchestrator service.
    
    Example:
        orchestrator = (DocketProcessingOrchestratorBuilder()
            .with_config(config)
            .with_logger(logger)
            .with_configuration_service(config_service)
            .with_browser_service(browser_service)
            .with_case_processing_service(case_service)
            .with_verification_service(verification_service)
            .enable_parallel_processing(max_workers=4)
            .build())
    """
    
    def __init__(self):
        from src.pacer._core_services.docket_processor.docket_processing_orchestrator import DocketProcessingOrchestrator
        super().__init__(DocketProcessingOrchestrator)
        self._parallel_enabled = False
    
    def with_configuration_service(self, service: Any) -> 'DocketProcessingOrchestratorBuilder':
        """Set the configuration service."""
        return self.with_param('configuration_service', service)
    
    def with_browser_service(self, service: Any) -> 'DocketProcessingOrchestratorBuilder':
        """Set the browser service."""
        return self.with_param('browser_service', service)
    
    def with_case_processing_service(self, service: Any) -> 'DocketProcessingOrchestratorBuilder':
        """Set the case processing service."""
        return self.with_param('case_processing_service', service)
    
    def with_relevance_service(self, service: Any) -> 'DocketProcessingOrchestratorBuilder':
        """Set the relevance service."""
        return self.with_param('relevance_service', service)
    
    def with_verification_service(self, service: Any) -> 'DocketProcessingOrchestratorBuilder':
        """Set the verification service."""
        return self.with_param('verification_service', service)
    
    def with_download_orchestration_service(self, service: Any) -> 'DocketProcessingOrchestratorBuilder':
        """Set the download orchestration service."""
        return self.with_param('download_orchestration_service', service)
    
    def with_file_operations_service(self, service: Any) -> 'DocketProcessingOrchestratorBuilder':
        """Set the file operations service."""
        return self.with_param('file_operations_service', service)
    
    def with_metrics_reporting_service(self, service: Any) -> 'DocketProcessingOrchestratorBuilder':
        """Set the metrics reporting service."""
        return self.with_param('metrics_reporting_service', service)
    
    def with_s3_management_service(self, service: Any) -> 'DocketProcessingOrchestratorBuilder':
        """Set the S3 management service."""
        return self.with_param('s3_management_service', service)
    
    def enable_parallel_processing(self, max_workers: int = 4) -> 'DocketProcessingOrchestratorBuilder':
        """Enable parallel processing with specified workers."""
        self._parallel_enabled = True
        return self.with_param('parallel_enabled', True).with_param('max_workers', max_workers)
    
    def with_all_services(self, **services) -> 'DocketProcessingOrchestratorBuilder':
        """Set all services at once."""
        for key, value in services.items():
            self.with_param(key, value)
        return self


class BrowserServiceBuilder(ServiceBuilder):
    """
    Builder for BrowserService.
    
    Example:
        browser_service = (BrowserServiceBuilder()
            .with_config(config)
            .with_logger(logger)
            .with_playwright_manager(playwright)
            .with_context_factory(context_factory)
            .enable_headless_mode()
            .with_timeout(30000)
            .build())
    """
    
    def __init__(self):
        from src.pacer.facades.browser_service import BrowserService
        super().__init__(BrowserService)
    
    def with_playwright_manager(self, manager: Any) -> 'BrowserServiceBuilder':
        """Set the playwright manager."""
        return self.with_param('playwright_manager', manager)
    
    def with_context_factory(self, factory: Any) -> 'BrowserServiceBuilder':
        """Set the context factory."""
        return self.with_param('context_factory', factory)
    
    def with_auth_manager(self, manager: Any) -> 'BrowserServiceBuilder':
        """Set the authentication manager."""
        return self.with_param('auth_manager', manager)
    
    def with_navigation_manager(self, manager: Any) -> 'BrowserServiceBuilder':
        """Set the navigation manager."""
        return self.with_param('navigation_manager', manager)
    
    def enable_headless_mode(self, headless: bool = True) -> 'BrowserServiceBuilder':
        """Enable or disable headless mode."""
        return self.with_param('headless', headless)
    
    def with_timeout(self, timeout: int) -> 'BrowserServiceBuilder':
        """Set the default timeout in milliseconds."""
        return self.with_param('timeout', timeout)
    
    def with_viewport(self, width: int, height: int) -> 'BrowserServiceBuilder':
        """Set the viewport dimensions."""
        return self.with_param('viewport', {'width': width, 'height': height})


class CaseProcessingServiceBuilder(ServiceBuilder):
    """
    Builder for CaseProcessingService.
    
    Example:
        case_service = (CaseProcessingServiceBuilder()
            .with_config(config)
            .with_logger(logger)
            .with_case_validator(validator)
            .with_case_parser(parser)
            .with_case_enricher(enricher)
            .with_case_transformer(transformer)
            .enable_field_consistency_check()
            .build())
    """
    
    def __init__(self):
        from src.pacer.facades.case_processing_service import CaseProcessingService
        super().__init__(CaseProcessingService)
    
    def with_case_validator(self, validator: Any) -> 'CaseProcessingServiceBuilder':
        """Set the case validator."""
        return self.with_param('case_validator', validator)
    
    def with_case_parser(self, parser: Any) -> 'CaseProcessingServiceBuilder':
        """Set the case parser."""
        return self.with_param('case_parser', parser)
    
    def with_case_enricher(self, enricher: Any) -> 'CaseProcessingServiceBuilder':
        """Set the case enricher."""
        return self.with_param('case_enricher', enricher)
    
    def with_case_transformer(self, transformer: Any) -> 'CaseProcessingServiceBuilder':
        """Set the case transformer."""
        return self.with_param('case_transformer', transformer)
    
    def enable_field_consistency_check(self, enabled: bool = True) -> 'CaseProcessingServiceBuilder':
        """Enable field consistency checking."""
        return self.with_param('field_consistency_enabled', enabled)
    
    def with_law_firm_corrector(self, corrector: Any) -> 'CaseProcessingServiceBuilder':
        """Set the law firm corrector."""
        return self.with_param('law_firm_corrector', corrector)


class RelevanceServiceBuilder(ServiceBuilder):
    """
    Builder for RelevanceService.
    
    Example:
        relevance_service = (RelevanceServiceBuilder()
            .with_config(config)
            .with_logger(logger)
            .with_relevance_config(relevance_config)
            .with_mdl_classifier(classifier)
            .enable_ai_classification()
            .build())
    """
    
    def __init__(self):
        from src.pacer.facades.relevance_service import RelevanceService
        super().__init__(RelevanceService)
    
    def with_relevance_config(self, config: Dict[str, Any]) -> 'RelevanceServiceBuilder':
        """Set the relevance configuration."""
        return self.with_param('relevance_config', config)
    
    def with_mdl_classifier(self, classifier: Any) -> 'RelevanceServiceBuilder':
        """Set the MDL classifier."""
        return self.with_param('mdl_classifier', classifier)
    
    def enable_ai_classification(self, enabled: bool = True) -> 'RelevanceServiceBuilder':
        """Enable AI-based classification."""
        return self.with_param('ai_classification_enabled', enabled)
    
    def with_threshold(self, threshold: float) -> 'RelevanceServiceBuilder':
        """Set the relevance threshold."""
        return self.with_param('relevance_threshold', threshold)


class VerificationServiceBuilder(ServiceBuilder):
    """
    Builder for VerificationService.
    
    Example:
        verification_service = (VerificationServiceBuilder()
            .with_config(config)
            .with_logger(logger)
            .with_repository(repository)
            .with_case_verifier(verifier)
            .enable_duplicate_check()
            .build())
    """
    
    def __init__(self):
        from src.pacer.facades.verification_service import VerificationService
        super().__init__(VerificationService)
    
    def with_repository(self, repository: Any) -> 'VerificationServiceBuilder':
        """Set the repository."""
        return self.with_param('repository', repository)
    
    def with_case_verifier(self, verifier: Any) -> 'VerificationServiceBuilder':
        """Set the case verifier."""
        return self.with_param('case_verifier', verifier)
    
    def with_file_manager(self, manager: Any) -> 'VerificationServiceBuilder':
        """Set the file manager."""
        return self.with_param('file_manager', manager)
    
    def enable_duplicate_check(self, enabled: bool = True) -> 'VerificationServiceBuilder':
        """Enable duplicate checking."""
        return self.with_param('duplicate_check_enabled', enabled)
    
    def with_verification_rules(self, rules: Dict[str, Any]) -> 'VerificationServiceBuilder':
        """Set custom verification rules."""
        return self.with_param('verification_rules', rules)


# Convenience functions for quick builder creation
def build_orchestrator(**kwargs) -> Any:
    """Quick function to build a DocketProcessingOrchestrator."""
    builder = DocketProcessingOrchestratorBuilder()
    for key, value in kwargs.items():
        if hasattr(builder, f'with_{key}'):
            getattr(builder, f'with_{key}')(value)
        else:
            builder.with_param(key, value)
    return builder.build()


def build_browser_service(**kwargs) -> Any:
    """Quick function to build a BrowserService."""
    builder = BrowserServiceBuilder()
    for key, value in kwargs.items():
        if hasattr(builder, f'with_{key}'):
            getattr(builder, f'with_{key}')(value)
        else:
            builder.with_param(key, value)
    return builder.build()


def build_case_service(**kwargs) -> Any:
    """Quick function to build a CaseProcessingService."""
    builder = CaseProcessingServiceBuilder()
    for key, value in kwargs.items():
        if hasattr(builder, f'with_{key}'):
            getattr(builder, f'with_{key}')(value)
        else:
            builder.with_param(key, value)
    return builder.build()