"""
PACER Domain Module.

This module provides the consolidated PACER domain with 9 core services,
design pattern implementations, and component-based architecture.

Architecture:
- Core Services (9): Configuration, Browser, Case Processing, Relevance, 
  Classification, Verification, Download Orchestration, File Operations, 
  Metrics Reporting, S3 Management
- Design Patterns: Strategy, Observer, Builder, Factory, Registry
- Components: Authentication, Navigation, File Management, etc.
- Facades: Simplified interfaces for complex subsystems
"""

# Import base class from infrastructure
from src.infrastructure.patterns.component_base import AsyncServiceBase

# Import core services from business logic layer
from ._core_services import (
    ClassificationService,
    ConfigurationService,
    CaseProcessingService,
    RelevanceService,
    DownloadOrchestrationService,
    FileOperationsService,
    MetricsReportingService,
    S3ManagementService,
    VerificationService,
)
from ._core_services.docket_processing_orchestrator import DocketProcessingOrchestrator

# Import design patterns
from .strategies import *
from .observers import *
from .builders import *
from .factories import *
from .registries import *

# Import interfaces and exceptions
from .interfaces import *
from .exceptions import *

__all__ = [
    # Base classes
    "AsyncServiceBase",
    
    # Core services
    "CaseProcessingService",
    "ClassificationService", 
    "ConfigurationService",
    "DownloadOrchestrationService",
    "DocketProcessingOrchestrator",
    "FileOperationsService",
    "MetricsReportingService",
    "RelevanceService",
    "S3ManagementService",
    "VerificationService",
    
    # Interfaces
    "PacerCoreServiceProtocol",
    "ConfigurationServiceInterface",
    "CaseProcessingServiceInterface", 
    "RelevanceServiceInterface",
    "ClassificationServiceInterface",
    "VerificationServiceInterface",
    "DownloadOrchestrationServiceInterface",
    "FileOperationsServiceInterface",
    "DocketProcessorInterface",
    
    # Exceptions
    "PacerException",
    "ValidationError",
    "ProcessingError",
    "AuthenticationError",
    "NavigationError",
    "DownloadError",
    "ConfigurationError",
    "ServiceError",
    "PatternError",
]