"""
Verification Service for PACER Core Services.

This service merges functionality from CaseVerificationFacadeService and case_verifier
to provide comprehensive case verification capabilities.
"""

from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
# Interface removed for simplicity - TODO: Add back when interfaces are properly organized
from src.pacer.components.verification.case_verifier import CaseVerifier
from src.utils.docket_utils import normalize_docket_number


class VerificationService(AsyncServiceBase):
    """
    Core Verification Service for PACER case verification.
    
    Provides comprehensive case verification including:
    - Database existence checks using GSI
    - Local file artifact verification
    - Processing eligibility determination
    - Data integrity validation
    """

    def __init__(
        self,
        repository: Optional[Any] = None,
        file_manager: Optional[Any] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        case_verifier: Optional[CaseVerifier] = None,
    ):
        super().__init__(logger, config)
        self._repository = repository
        self._file_manager = file_manager
        self.case_verifier = case_verifier

    async def _setup_dependencies(self) -> None:
        """Setup verification dependencies."""
        if not self.case_verifier and self._repository and self._file_manager:
            # Initialize with provided dependencies
            self.case_verifier = CaseVerifier(
                repository=self._repository,
                file_manager=self._file_manager,
                logger=self.logger,
                config=self.config
            )
        
        if self.case_verifier:
            self._dependencies["case_verifier"] = self.case_verifier
        if self._repository:
            self._dependencies["repository"] = self._repository
        if self._file_manager:
            self._dependencies["file_manager"] = self._file_manager

    async def _validate_configuration(self) -> None:
        """Validate verification configuration."""
        if not self.case_verifier:
            raise ValueError("CaseVerifier is required for VerificationService")
        if not self._repository:
            raise ValueError("Repository is required for VerificationService")
        if not self._file_manager:
            raise ValueError("FileManager is required for VerificationService")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate verification methods."""
        if not self._initialized:
            await self.initialize()
            
        action = data.get("action")
        case_details = data.get("case_details", {})
        
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, f"Processing verification action: {action}")
        
        if action == "verify_case":
            is_explicitly_requested = data.get("is_explicitly_requested", False)
            return await self.verify_case_comprehensive(case_details, is_explicitly_requested)
        elif action == "verify_processing_eligibility":
            return await self.verify_processing_eligibility(case_details)
        elif action == "check_database":
            return await self.check_database_gsi(case_details)
        elif action == "check_local_files":
            file_types = data.get("file_types")
            return await self.check_local_files(case_details, file_types)
        else:
            raise ValueError(f"Unknown action for VerificationService: {action}")

    async def verify_case_comprehensive(
        self, case_details: Dict[str, Any], is_explicitly_requested: bool = False
    ) -> bool:
        """
        Comprehensive case verification workflow.
        
        This follows the exact logic from docket_processing.md Phase 3.
        """
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, 
                           f"Starting verification (explicitly_requested={is_explicitly_requested})")
        
        if not court_id or not docket_num:
            self.log_with_prefix("warning", court_id, docket_num, 
                               "Missing court_id or docket_num for verification")
            return False

        if is_explicitly_requested:
            return await self._verify_explicitly_requested(case_details)
        else:
            return await self._verify_report_scraped(case_details)

    async def _verify_explicitly_requested(self, case_details: Dict[str, Any]) -> bool:
        """Verification workflow for explicitly requested cases."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("debug", court_id, docket_num, "Explicitly requested verification")
        
        # Step 1: Check Database GSI
        if await self.check_database_gsi(case_details):
            self.log_with_prefix("info", court_id, docket_num, 
                               "Case exists in database - skipping processing")
            return False
        
        # Step 2: Check Local Artifacts (PDF/ZIP only)
        if await self.check_local_artifacts_pdf_zip(case_details):
            self.log_with_prefix("info", court_id, docket_num, 
                               "Case artifacts exist locally - skipping processing")
            return False
        
        # If neither exists, proceed with processing
        self.log_with_prefix("info", court_id, docket_num, 
                           "Case not found in database or locally - proceeding with processing")
        return True

    async def _verify_report_scraped(self, case_details: Dict[str, Any]) -> bool:
        """Verification workflow for report-scraped cases."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("debug", court_id, docket_num, "Report-scraped verification")
        
        # Step 1: Check Database GSI
        if await self.check_database_gsi(case_details):
            self.log_with_prefix("info", court_id, docket_num, 
                               "Case exists in database - skipping processing")
            return False
        
        # Step 2: Check All Local Files
        if await self.check_all_local_files(case_details):
            self.log_with_prefix("info", court_id, docket_num, 
                               "Case files exist locally - skipping processing")
            return False
        
        # If neither exists, proceed with processing
        self.log_with_prefix("info", court_id, docket_num, 
                           "Case not found in database or locally - proceeding with processing")
        return True

    async def check_database_gsi(self, case_details: Dict[str, Any]) -> bool:
        """Check if case exists in database using GSI."""
        court_id = case_details.get('court_id')
        docket_num = case_details.get('docket_num')

        if not court_id or not docket_num:
            self.log_with_prefix("warning", court_id or "N/A", docket_num or "N/A", 
                               'Missing court_id or docket_num for GSI check')
            return False

        # Normalize docket number for database lookup
        db_docket_num = normalize_docket_number(docket_num)
        exists = await self._repository.check_docket_exists(court_id, db_docket_num)
        
        self.log_with_prefix("debug", court_id, docket_num, 
                           f"Database GSI check: exists={exists}")
        return exists

    async def check_local_artifacts_pdf_zip(self, case_details: Dict[str, Any]) -> bool:
        """Check for local PDF/ZIP artifacts only (for explicitly requested cases)."""
        return await self.check_local_files(case_details, file_types=['pdf', 'zip'])

    async def check_all_local_files(self, case_details: Dict[str, Any]) -> bool:
        """Check for any local files (for report-scraped cases)."""
        return await self.check_local_files(case_details, file_types=None)

    async def check_local_files(self, case_details: Dict[str, Any], file_types: Optional[list] = None) -> bool:
        """Check for local files with optional type filtering."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        try:
            exists = await self._file_manager.check_local_files(case_details, file_types=file_types)
            file_type_desc = f" ({file_types})" if file_types else " (any type)"
            self.log_with_prefix("debug", court_id, docket_num, 
                               f"Local files check{file_type_desc}: exists={exists}")
            return exists
        except Exception as e:
            self.log_with_prefix("warning", court_id, docket_num, 
                               f"Error checking local files: {e}")
            return False

    # VerificationServiceInterface implementation
    async def verify_case_completeness(self, case_details: Dict[str, Any]) -> bool:
        """Verify case data completeness."""
        required_fields = ['court_id', 'docket_num', 'case_name']
        missing_fields = [field for field in required_fields if not case_details.get(field)]
        
        if missing_fields:
            court_id = case_details.get("court_id", "N/A")
            docket_num = case_details.get("docket_num", "N/A")
            self.log_with_prefix("warning", court_id, docket_num, 
                               f"Case data incomplete, missing: {missing_fields}")
            return False
        
        return True

    async def verify_data_integrity(self, data: Dict[str, Any]) -> bool:
        """Verify data integrity and consistency."""
        # Basic data integrity checks
        if not isinstance(data, dict):
            return False
        
        # Check for required structure
        case_details = data.get('case_details')
        if not case_details:
            return False
        
        # Verify court_id format (basic validation)
        court_id = case_details.get('court_id')
        if court_id and len(court_id) < 2:
            return False
        
        return True

    async def validate_case_format(self, case_details: Dict[str, Any]) -> list[str]:
        """Validate case data format and return any errors."""
        errors = []
        
        # Validate docket number format
        docket_num = case_details.get('docket_num', '')
        if not docket_num:
            errors.append("Missing docket_num")
        elif not any(char.isdigit() for char in docket_num):
            errors.append("Invalid docket_num format (no digits found)")
        
        # Validate court_id format
        court_id = case_details.get('court_id', '')
        if not court_id:
            errors.append("Missing court_id")
        elif len(court_id) < 2:
            errors.append("Invalid court_id format (too short)")
        
        # Validate case_name
        case_name = case_details.get('case_name', '')
        if not case_name or len(case_name.strip()) < 3:
            errors.append("Missing or invalid case_name")
        
        return errors

    async def verify_processing_eligibility(self, case_details: Dict[str, Any]) -> bool:
        """Verify if case is eligible for processing."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        # Check completeness first
        if not await self.verify_case_completeness(case_details):
            self.log_with_prefix("info", court_id, docket_num, 
                               "Case not eligible: incomplete data")
            return False
        
        # Check format validation
        format_errors = await self.validate_case_format(case_details)
        if format_errors:
            self.log_with_prefix("info", court_id, docket_num, 
                               f"Case not eligible: format errors: {format_errors}")
            return False
        
        # Additional business logic checks can be added here
        self.log_with_prefix("debug", court_id, docket_num, "Case is eligible for processing")
        return True