"""
Classification Service for PACER Core Services.

This service merges functionality from CaseClassificationFacadeService and case_classifier
to provide comprehensive case classification capabilities.
"""

from typing import Any, Dict, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.utils.date import DateUtils

if TYPE_CHECKING:
    from src.pacer.components.classification.case_classifier import CaseClassifier
    from src.pacer.components.transfer.transfer_processor import TransferProcessor


class ClassificationService(AsyncServiceBase):
    """
    Core Classification Service for PACER case classification.
    
    Provides comprehensive case classification including:
    - Initial classification from report data
    - HTML-based detailed classification
    - Removal detection
    - Transfer processing
    - Special case rules
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        case_classifier: Optional["CaseClassifier"] = None,
        transfer_processor: Optional["TransferProcessor"] = None,
    ):
        super().__init__(logger, config)
        self.case_classifier = case_classifier
        self.transfer_processor = transfer_processor

    async def _setup_dependencies(self) -> None:
        """Setup classification dependencies."""
        if not self.case_classifier:
            # Initialize with default dependencies if not provided
            self.case_classifier = CaseClassifier(
                logger=self.logger,
                config=self.config,
                transfer_processor=self.transfer_processor
            )
        self._dependencies["case_classifier"] = self.case_classifier
        if self.transfer_processor:
            self._dependencies["transfer_processor"] = self.transfer_processor

    async def _validate_configuration(self) -> None:
        """Validate classification configuration."""
        if not self.case_classifier:
            raise ValueError("CaseClassifier is required for ClassificationService")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate classification methods."""
        if not self._initialized:
            await self.initialize()
            
        action = data.get("action")
        case_details = data.get("case_details", {})
        
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, f"Processing {action}")
        
        if action == "classify_case":
            html_content = data.get("html_content", "")
            return await self.classify_case_comprehensive(case_details, html_content)
        elif action == "classify_initial":
            return await self.classify_case_initial(case_details)
        elif action == "classify_from_html":
            html_content = data.get("html_content", "")
            return await self.classify_case_from_html(case_details, html_content)
        elif action == "process_transfer":
            return await self.process_transfer_case(case_details)
        else:
            raise ValueError(f"Unknown action for ClassificationService: {action}")

    async def classify_case_comprehensive(
        self, case_details: Dict[str, Any], html_content: str = ""
    ) -> Dict[str, Any]:
        """
        Perform comprehensive case classification workflow.
        
        This is the main entry point that coordinates all classification phases.
        """
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, "Starting comprehensive classification")
        
        # Phase 1: Initial classification from report data
        classified_details = await self.classify_case_initial(case_details)
        
        # Phase 2: HTML-based detailed classification
        if html_content:
            classified_details = await self.classify_case_from_html(classified_details, html_content)
        
        # Phase 3: Transfer processing if required
        if self.should_process_as_transfer(classified_details):
            classified_details = await self.process_transfer_case(classified_details)
        
        # Phase 4: Apply special case rules
        classified_details = await self.apply_special_case_rules(classified_details)
        
        self.log_with_prefix("info", court_id, docket_num, "Comprehensive classification completed")
        return classified_details

    async def classify_case_initial(self, initial_details: Dict[str, Any]) -> Dict[str, Any]:
        """Perform initial case classification based on report data."""
        case_details = initial_details.copy()
        cause = str(case_details.get('cause_from_report', '')).lower()
        
        removal_keywords = ["petition for removal", "notice of removal", "28:1441", "28:1446"]
        case_details['_initial_removal_status'] = any(kw in cause for kw in removal_keywords)
        
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        self.log_with_prefix("debug", court_id, docket_num, 
                           f"Initial classification: removal_status={case_details['_initial_removal_status']}")
        
        return case_details

    async def classify_case_from_html(
        self, case_details: Dict[str, Any], html_content: str
    ) -> Dict[str, Any]:
        """Perform detailed classification based on HTML content."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("debug", court_id, docket_num, "Performing HTML-based classification")
        
        from src.services.html import CaseParserService
        parser = CaseParserService(self.logger, html_content)

        # Removal info detection
        removal_info = parser.is_removal()
        if removal_info and removal_info.get('is_removal'):
            case_details['_html_indicates_removal'] = True
            if removal_date := removal_info.get('removal_date'):
                try:
                    dt_obj = DateUtils.parse_date(removal_date)
                    if dt_obj:
                        case_details['removal_date'] = dt_obj.strftime('%Y-%m-%d')
                        self.log_with_prefix("debug", court_id, docket_num, 
                                           f"Detected removal date: {case_details['removal_date']}")
                except ValueError:
                    self.log_with_prefix("warning", court_id, docket_num, 
                                       f"Could not parse removal date: {removal_date}")
        else:
            case_details['_html_indicates_removal'] = False

        # Final removal determination
        is_removal = case_details.get('_initial_removal_status') or case_details.get('_html_indicates_removal')
        case_details['is_removal'] = is_removal

        # Transfer info detection
        case_details['_requires_transfer_processing'] = (
            'transferred_from' in case_details or 'case_in_other_court' in case_details
        )

        self.log_with_prefix("debug", court_id, docket_num, 
                           f"HTML classification: is_removal={is_removal}, "
                           f"requires_transfer_processing={case_details['_requires_transfer_processing']}")

        return case_details

    async def process_transfer_case(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Process a case that requires transfer handling."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        if not self.transfer_processor:
            self.log_with_prefix("warning", court_id, docket_num, 
                               "Transfer facade not available, skipping transfer processing")
            return case_details

        self.log_with_prefix("info", court_id, docket_num, "Processing transfer case")
        
        return await self.transfer_processor.execute({
            "action": "process_transfer_case",
            "case_details": case_details
        })

    async def apply_special_case_rules(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Apply special classification rules for specific courts or case types."""
        court_id = case_details.get("court_id")
        flags = str(case_details.get("flags", [])).lower()
        docket_num = case_details.get("docket_num", "N/A")

        # ILND MDL 3060 special handling
        if court_id == 'ilnd' and "mdl 3060" in flags:
            case_details.update({
                'is_mdl_related_ilnd_3060': True, 
                'transferred_in': False, 
                'is_removal': False
            })
            self.log_with_prefix("info", court_id, docket_num, "Applied ILND MDL 3060 special rules")
        
        # CAND MDL 3047 special handling
        elif court_id == 'cand' and "mdl 3047" in flags:
            case_details.update({
                'is_mdl_related_cand_3047': True, 
                'transferred_in': False, 
                'is_removal': False
            })
            self.log_with_prefix("info", court_id, docket_num, "Applied CAND MDL 3047 special rules")

        return case_details

    def should_process_as_transfer(self, case_details: Dict[str, Any]) -> bool:
        """Determine if a case should be processed as a transfer."""
        return case_details.get('_requires_transfer_processing', False)

    # ClassificationServiceInterface implementation
    async def classify_case_type(self, case_details: Dict[str, Any]) -> str:
        """Classify the type of legal case."""
        cause = case_details.get('cause_from_report', '').lower()
        
        # Basic classification logic
        if any(kw in cause for kw in ['removal', '28:1441', '28:1446']):
            return 'removal'
        elif 'mdl' in str(case_details.get('flags', [])).lower():
            return 'mdl'
        elif 'transfer' in cause:
            return 'transfer'
        else:
            return 'standard'

    async def classify_court_jurisdiction(self, case_details: Dict[str, Any]) -> str:
        """Classify court jurisdiction level."""
        court_id = case_details.get('court_id', '').lower()
        
        if 'fed' in court_id or 'dc' in court_id:
            return 'federal'
        elif 'appellate' in court_id or 'app' in court_id:
            return 'appellate'
        else:
            return 'district'

    async def classify_case_complexity(self, case_details: Dict[str, Any]) -> str:
        """Classify case complexity level."""
        # Simple heuristic based on available information
        flags = str(case_details.get('flags', [])).lower()
        
        if 'mdl' in flags:
            return 'high'
        elif case_details.get('is_removal'):
            return 'medium'
        else:
            return 'low'

    async def get_classification_confidence(self, classification: str, case_details: Dict[str, Any]) -> float:
        """Get confidence score for classification."""
        # Simple confidence scoring - can be enhanced with ML models
        cause = case_details.get('cause_from_report', '').lower()
        
        if classification == 'removal':
            return 0.9 if any(kw in cause for kw in ['28:1441', '28:1446']) else 0.7
        elif classification == 'mdl':
            return 0.95 if 'mdl' in str(case_details.get('flags', [])).lower() else 0.6
        else:
            return 0.8