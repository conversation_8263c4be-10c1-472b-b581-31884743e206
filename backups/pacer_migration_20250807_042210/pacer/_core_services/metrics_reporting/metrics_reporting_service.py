"""
Metrics Reporting Service

Handles collection, analysis, and reporting of processing metrics.
Provides insights into system performance and processing statistics.
"""

from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict, Counter

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.logger import LoggerProtocol


class MetricsReportingService(AsyncServiceBase):
    """Service for collecting and reporting processing metrics."""
    
    def __init__(self, logger: Optional[LoggerProtocol] = None, config: Optional[Dict[str, Any]] = None):
        super().__init__(logger, config)
        # For backward compatibility, we'll create a mock config_service interface
        self.config_service = None
        self.metrics_storage = defaultdict(list)
        self.metric_counters = defaultdict(int)
        self.processing_times = defaultdict(list)
        self.error_tracking = defaultdict(int)
        
    async def initialize(self) -> None:
        """Initialize the metrics reporting service."""
        await super().initialize()
        
        # Get retention config from the config dict
        metrics_config = self.config.get('metrics', {}).get('reporting', {})
        self.retention_days = metrics_config.get('retention_days', 7)
        
        self.logger.info("MetricsReportingService initialized successfully")
    
    async def record_processing_start(self, operation_id: str, operation_type: str, metadata: Dict[str, Any] = None) -> None:
        """Record the start of a processing operation."""
        record = {
            'operation_id': operation_id,
            'operation_type': operation_type,
            'start_time': datetime.now(),
            'status': 'started',
            'metadata': metadata or {}
        }
        
        self.metrics_storage[operation_id].append(record)
        self.metric_counters[f"{operation_type}_started"] += 1
        
        self.logger.debug(f"Recording processing start for {operation_id} ({operation_type})")
    
    async def record_processing_completion(self, operation_id: str, success: bool = True, 
                                         result_metadata: Dict[str, Any] = None, 
                                         error_message: str = None) -> None:
        """Record the completion of a processing operation."""
        if operation_id not in self.metrics_storage:
            self.logger.warning(f"No start record found for operation {operation_id}")
            return
        
        records = self.metrics_storage[operation_id]
        start_record = records[-1] if records else None
        
        if start_record and start_record.get('status') == 'started':
            completion_time = datetime.now()
            processing_duration = (completion_time - start_record['start_time']).total_seconds()
            
            completion_record = {
                'operation_id': operation_id,
                'operation_type': start_record['operation_type'],
                'completion_time': completion_time,
                'processing_duration_seconds': processing_duration,
                'status': 'completed' if success else 'failed',
                'result_metadata': result_metadata or {},
                'error_message': error_message
            }
            
            records.append(completion_record)
            
            # Update counters and tracking
            operation_type = start_record['operation_type']
            if success:
                self.metric_counters[f"{operation_type}_completed"] += 1
            else:
                self.metric_counters[f"{operation_type}_failed"] += 1
                if error_message:
                    self.error_tracking[error_message] += 1
            
            # Track processing times
            self.processing_times[operation_type].append(processing_duration)
            
            self.logger.debug(f"Recording processing completion for {operation_id}: {success}")
    
    async def record_custom_metric(self, metric_name: str, value: Any, 
                                 tags: Dict[str, str] = None) -> None:
        """Record a custom metric value."""
        metric_record = {
            'timestamp': datetime.now(),
            'metric_name': metric_name,
            'value': value,
            'tags': tags or {}
        }
        
        self.metrics_storage[f"custom_{metric_name}"].append(metric_record)
        self.logger.debug(f"Recording custom metric {metric_name}: {value}")
    
    async def get_processing_summary(self, operation_type: Optional[str] = None, 
                                   hours_back: int = 24) -> Dict[str, Any]:
        """Get processing summary for the specified time period."""
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        
        summary = {
            'time_period_hours': hours_back,
            'summary_generated_at': datetime.now().isoformat(),
            'operations': {}
        }
        
        # Collect operations within time period
        relevant_operations = []
        for operation_id, records in self.metrics_storage.items():
            if operation_id.startswith('custom_'):
                continue
                
            for record in records:
                if record.get('start_time', record.get('completion_time', datetime.min)) >= cutoff_time:
                    if operation_type is None or record.get('operation_type') == operation_type:
                        relevant_operations.append(record)
        
        # Group by operation type
        by_operation_type = defaultdict(list)
        for op in relevant_operations:
            op_type = op.get('operation_type', 'unknown')
            by_operation_type[op_type].append(op)
        
        # Generate summary for each operation type
        for op_type, operations in by_operation_type.items():
            started = len([op for op in operations if op.get('status') == 'started'])
            completed = len([op for op in operations if op.get('status') == 'completed'])
            failed = len([op for op in operations if op.get('status') == 'failed'])
            
            # Calculate processing times for completed operations
            durations = [op.get('processing_duration_seconds') for op in operations 
                        if op.get('processing_duration_seconds') is not None]
            
            avg_duration = sum(durations) / len(durations) if durations else 0
            min_duration = min(durations) if durations else 0
            max_duration = max(durations) if durations else 0
            
            summary['operations'][op_type] = {
                'started': started,
                'completed': completed,
                'failed': failed,
                'success_rate': (completed / (completed + failed) * 100) if (completed + failed) > 0 else 0,
                'average_duration_seconds': round(avg_duration, 2),
                'min_duration_seconds': round(min_duration, 2),
                'max_duration_seconds': round(max_duration, 2)
            }
        
        return summary
    
    async def get_error_analysis(self, hours_back: int = 24) -> Dict[str, Any]:
        """Get error analysis for the specified time period."""
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        
        # Collect errors within time period
        errors = []
        for operation_id, records in self.metrics_storage.items():
            if operation_id.startswith('custom_'):
                continue
                
            for record in records:
                record_time = record.get('completion_time', record.get('start_time', datetime.min))
                if (record_time >= cutoff_time and 
                    record.get('status') == 'failed' and 
                    record.get('error_message')):
                    errors.append({
                        'operation_type': record.get('operation_type'),
                        'error_message': record.get('error_message'),
                        'timestamp': record_time
                    })
        
        # Analyze errors
        error_counts = Counter(error['error_message'] for error in errors)
        operation_error_counts = defaultdict(int)
        for error in errors:
            operation_error_counts[error['operation_type']] += 1
        
        return {
            'time_period_hours': hours_back,
            'analysis_generated_at': datetime.now().isoformat(),
            'total_errors': len(errors),
            'unique_error_messages': len(error_counts),
            'most_common_errors': error_counts.most_common(10),
            'errors_by_operation_type': dict(operation_error_counts)
        }
    
    async def get_performance_metrics(self, operation_type: str, hours_back: int = 24) -> Dict[str, Any]:
        """Get performance metrics for a specific operation type."""
        if operation_type not in self.processing_times:
            return {
                'operation_type': operation_type,
                'error': 'No data available for this operation type'
            }
        
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        recent_times = []
        
        # Get recent processing times
        for operation_id, records in self.metrics_storage.items():
            if operation_id.startswith('custom_'):
                continue
                
            for record in records:
                if (record.get('operation_type') == operation_type and 
                    record.get('completion_time', datetime.min) >= cutoff_time and
                    record.get('processing_duration_seconds') is not None):
                    recent_times.append(record['processing_duration_seconds'])
        
        if not recent_times:
            return {
                'operation_type': operation_type,
                'time_period_hours': hours_back,
                'message': 'No recent completed operations found'
            }
        
        # Calculate statistics
        avg_time = sum(recent_times) / len(recent_times)
        min_time = min(recent_times)
        max_time = max(recent_times)
        
        # Calculate percentiles
        sorted_times = sorted(recent_times)
        n = len(sorted_times)
        p50 = sorted_times[n // 2] if n > 0 else 0
        p95 = sorted_times[int(n * 0.95)] if n > 0 else 0
        p99 = sorted_times[int(n * 0.99)] if n > 0 else 0
        
        return {
            'operation_type': operation_type,
            'time_period_hours': hours_back,
            'sample_size': len(recent_times),
            'average_duration_seconds': round(avg_time, 2),
            'min_duration_seconds': round(min_time, 2),
            'max_duration_seconds': round(max_time, 2),
            'percentile_50_seconds': round(p50, 2),
            'percentile_95_seconds': round(p95, 2),
            'percentile_99_seconds': round(p99, 2)
        }
    
    async def cleanup_old_metrics(self) -> None:
        """Clean up old metrics beyond retention period."""
        cutoff_time = datetime.now() - timedelta(days=self.retention_days)
        cleaned_count = 0
        
        for operation_id in list(self.metrics_storage.keys()):
            records = self.metrics_storage[operation_id]
            original_count = len(records)
            
            # Keep only recent records
            recent_records = [
                record for record in records 
                if record.get('start_time', record.get('completion_time', 
                    record.get('timestamp', datetime.max))) >= cutoff_time
            ]
            
            if recent_records:
                self.metrics_storage[operation_id] = recent_records
            else:
                del self.metrics_storage[operation_id]
            
            cleaned_count += original_count - len(recent_records)
        
        if cleaned_count > 0:
            self.logger.info(f"Cleaned up {cleaned_count} old metric records")
    
    async def health_check(self) -> Dict[str, Any]:
        """Return service health status."""
        total_operations = len(self.metrics_storage)
        total_records = sum(len(records) for records in self.metrics_storage.values())
        
        return {
            'service': 'MetricsReportingService',
            'status': 'healthy' if self.is_initialized() else 'not_initialized',
            'total_operations_tracked': total_operations,
            'total_records': total_records,
            'retention_days': self.retention_days,
            'operation_types_tracked': len(self.processing_times)
        }
    
    async def _execute_action(self, data: Any) -> Any:
        """Execute specific metrics action based on the action type in data."""
        action = data.get('action') if isinstance(data, dict) else None
        
        if action == 'record_processing_start':
            return await self.record_processing_start(
                data['operation_id'], 
                data['operation_type'], 
                data.get('metadata')
            )
        elif action == 'record_processing_completion':
            return await self.record_processing_completion(
                data['operation_id'], 
                data.get('result'),
                data.get('processing_duration_seconds')
            )
        elif action == 'record_processing_failure':
            return await self.record_processing_failure(
                data['operation_id'], 
                data['error_type'], 
                data.get('error_details')
            )
        elif action == 'get_metrics_summary':
            return await self.get_metrics_summary(
                data.get('hours_back', 24),
                data.get('operation_type')
            )
        elif action == 'get_error_analysis':
            return await self.get_error_analysis(data.get('hours_back', 24))
        elif action == 'get_performance_metrics':
            return await self.get_performance_metrics(
                data['operation_type'],
                data.get('hours_back', 24)
            )
        elif action == 'health_check':
            return await self.health_check()
        else:
            raise NotImplementedError(f"Action '{action}' not supported by MetricsReportingService")