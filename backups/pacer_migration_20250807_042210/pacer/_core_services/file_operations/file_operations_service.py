"""
File Operations Service for PACER Core Services.

This service merges functionality from FileManagementFacadeService, file components,
and S3Manager to provide comprehensive file operations and data persistence.
"""

import asyncio
import json
from pathlib import Path
from typing import Any, Dict, List, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
# Interface removed for simplicity - TODO: Add back when interfaces are properly organized
from src.pacer.components.file_operations.directory_manager import DirectoryManager
from src.pacer.components.file_operations.file_manager import FileManager
from src.pacer.components.file_operations.path_builder import PathBuilder
from src.pacer.components.download.s3_manager import S3Manager
from src.pacer.components.export.csv_exporter import CsvExporter


class FileOperationsService(AsyncServiceBase):
    """
    Core File Operations Service for PACER file management and data persistence.
    
    Provides comprehensive file operations including:
    - Directory structure management
    - File I/O operations
    - S3 upload and storage management
    - CSV export functionality
    - Case data persistence
    - Report generation
    """

    def __init__(
        self,
        directory_manager: Optional[DirectoryManager] = None,
        file_manager: Optional[FileManager] = None,
        path_builder: Optional[PathBuilder] = None,
        s3_manager: Optional[S3Manager] = None,
        csv_exporter: Optional[CsvExporter] = None,
        s3_async_storage: Optional[Any] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
    ):
        super().__init__(logger, config)
        self._directory_manager = directory_manager
        self._file_manager = file_manager
        self._path_builder = path_builder
        self._s3_manager = s3_manager
        self._csv_exporter = csv_exporter
        self._s3_async_storage = s3_async_storage

    async def _initialize_service(self) -> None:
        """Initialize the file operations service."""
        await self._setup_dependencies()
        await self._validate_configuration()

    async def _setup_dependencies(self) -> None:
        """Setup file operations dependencies."""
        # Initialize path_builder first as it's needed by others
        if not self._path_builder:
            self._path_builder = PathBuilder(logger=self.logger, config=self.config)
        self._dependencies["path_builder"] = self._path_builder

        # Initialize directory_manager with path_builder
        if not self._directory_manager:
            self._directory_manager = DirectoryManager(
                path_builder=self._path_builder,
                logger=self.logger,
                config=self.config
            )
        self._dependencies["directory_manager"] = self._directory_manager

        # Initialize file_manager
        if not self._file_manager:
            self._file_manager = FileManager(logger=self.logger, config=self.config)
        self._dependencies["file_manager"] = self._file_manager

        # Initialize S3 manager if storage is available
        if self._s3_async_storage:
            if not self._s3_manager:
                self._s3_manager = S3Manager(
                    logger=self.logger,
                    config=self.config,
                    s3_async_storage=self._s3_async_storage
                )
            self._dependencies["s3_manager"] = self._s3_manager

        # Initialize CSV exporter
        if not self._csv_exporter:
            self._csv_exporter = CsvExporter(logger=self.logger, config=self.config)
        self._dependencies["csv_exporter"] = self._csv_exporter

    async def _validate_configuration(self) -> None:
        """Validate file operations configuration."""
        required_components = [
            ("path_builder", self._path_builder),
            ("directory_manager", self._directory_manager),
            ("file_manager", self._file_manager),
            ("csv_exporter", self._csv_exporter)
        ]
        
        missing = [name for name, component in required_components if not component]
        if missing:
            raise ValueError(f"Missing required components for FileOperationsService: {missing}")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate file operations methods."""
        if not self._initialized:
            await self.initialize()
            
        action = data.get("action")
        
        # Log action without case details for file operations
        self.log_info(f"Processing file operations action: {action}")
        
        if action == "save_and_upload_case_data":
            return await self.save_and_upload_case_data(data["case_data"], data["iso_date"])
        elif action == "setup_directories":
            return await self.setup_directories(data["iso_date"])
        elif action == "save_case_data":
            return await self.save_case_data(data["case_data"], data["iso_date"])
        elif action == "upload_to_s3":
            return await self.upload_to_s3(data["file_path"], data["s3_key"])
        elif action == "export_to_csv":
            return await self.export_to_csv(data["data"], data["output_path"])
        elif action == "generate_report":
            return await self.generate_report(data["report_type"], data["data"])
        else:
            raise ValueError(f"Unknown action for FileOperationsService: {action}")

    async def save_and_upload_case_data(self, case_data: Dict[str, Any], iso_date: str) -> str:
        """
        Complete case data persistence workflow.
        
        This implements the final phase from docket_processing.md.
        """
        court_id = case_data.get("court_id", "N/A")
        docket_num = case_data.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, "Starting case data save and upload")
        
        try:
            # Phase 1: Create court/date directory structure
            await self.setup_directories(iso_date)
            
            # Phase 2: Generate filename
            filename = await self.generate_case_filename(case_data, iso_date)
            
            # Phase 3: Clean and prepare case data
            cleaned_data = await self.clean_and_prepare_case_data(case_data)
            
            # Phase 4: Save to local JSON
            local_path = await self.save_case_data(cleaned_data, iso_date)
            
            # Phase 5: Upload to S3 (if enabled and available)
            s3_url = None
            upload_enabled = self.config.get("s3_upload_enabled", True)
            
            if upload_enabled and self._s3_manager:
                s3_key = f"{iso_date}/dockets/{filename}"
                s3_url = await self.upload_to_s3(local_path, s3_key)
                
                if s3_url:
                    self.log_with_prefix("info", court_id, docket_num, f"Case data uploaded to S3: {s3_url}")
                else:
                    self.log_with_prefix("warning", court_id, docket_num, "S3 upload failed, saved locally only")
            
            self.log_with_prefix("info", court_id, docket_num, f"Case data saved successfully: {local_path}")
            
            # Return result information
            result = {
                "local_path": local_path,
                "s3_url": s3_url,
                "filename": filename,
                "status": "success" if s3_url else "local_only"
            }
            
            return result
            
        except Exception as e:
            self.log_with_prefix("error", court_id, docket_num, f"Failed to save case data: {e}")
            raise

    async def generate_case_filename(self, case_data: Dict[str, Any], iso_date: str) -> str:
        """Generate standardized case filename."""
        base_filename = case_data.get("base_filename")
        if base_filename:
            return f"{base_filename}.json"
        
        # Fallback filename generation
        court_id = case_data.get("court_id", "unknown")
        docket_num = case_data.get("docket_num", "unknown").replace(":", "_").replace("-", "_")
        return f"{court_id}_{docket_num}_{iso_date}.json"

    async def clean_and_prepare_case_data(self, case_data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and prepare case data for persistence."""
        # Create a clean copy
        cleaned_data = case_data.copy()
        
        # Remove temporary processing fields
        temp_fields = [field for field in cleaned_data.keys() if field.startswith('_')]
        for field in temp_fields:
            cleaned_data.pop(field, None)
        
        # Ensure required metadata
        if "processed_at" not in cleaned_data:
            from datetime import datetime
            cleaned_data["processed_at"] = datetime.utcnow().isoformat()
        
        return cleaned_data

    # FileOperationsServiceInterface implementation
    async def setup_directories(self, iso_date: str) -> None:
        """Setup required directory structure."""
        self.log_info(f"Setting up directories for {iso_date}")
        
        if not self._directory_manager:
            # Ensure service is initialized first
            if not self._initialized:
                await self.initialize()
            
            # Check again after initialization
            if not self._directory_manager:
                raise ValueError("DirectoryManager not initialized")
        
        # Use directory manager to setup all required directories
        self._directory_manager.setup_directories(iso_date)
        
        self.log_info(f"Directory setup completed for {iso_date}")

    async def save_case_data(self, case_data: Dict[str, Any], iso_date: str) -> str:
        """Save case data to appropriate local location."""
        if not self._path_builder or not self._file_manager:
            raise ValueError("Required components not initialized")
        
        # Generate filename
        filename = await self.generate_case_filename(case_data, iso_date)
        
        # Get dockets path
        dockets_path = self._path_builder.get_dockets_path(iso_date)
        file_path = dockets_path / filename
        
        # Save JSON data
        try:
            json_content = json.dumps(case_data, indent=2, ensure_ascii=False)
            await self._file_manager.execute({
                "action": "write",
                "filepath": str(file_path),
                "content": json_content
            })
            
            return str(file_path)
            
        except Exception as e:
            self.log_error(f"Failed to save case data to {file_path}: {e}")
            raise

    async def upload_to_s3(self, file_path: str, s3_key: str) -> str:
        """Upload file to S3 and return URL."""
        if not self._s3_manager:
            self.log_warning("S3Manager not available for upload")
            return ""
        
        try:
            self.log_info(f"Uploading {file_path} to S3 key: {s3_key}")
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Upload via S3 manager
            success = await self._s3_manager.execute({
                "action": "upload_html",  # Using existing method
                "case_details": {"base_filename": Path(file_path).stem},
                "html_content": content,
                "json_path": file_path
            })
            
            if success:
                # Construct CDN URL
                cdn_base = self.config.get("cdn_base_url", "https://cdn.lexgenius.ai")
                return f"{cdn_base}/{s3_key}"
            else:
                return ""
                
        except Exception as e:
            self.log_error(f"Failed to upload to S3: {e}")
            return ""

    async def export_to_csv(self, data: List[Dict[str, Any]], output_path: str) -> str:
        """Export data to CSV format."""
        if not self._csv_exporter:
            raise ValueError("CsvExporter not initialized")
        
        self.log_info(f"Exporting {len(data)} records to CSV: {output_path}")
        
        try:
            await self._csv_exporter.execute({
                "action": "export_to_csv",
                "items": data,
                "filename": output_path
            })
            
            self.log_info(f"CSV export completed: {output_path}")
            return output_path
            
        except Exception as e:
            self.log_error(f"CSV export failed: {e}")
            raise

    async def generate_report(self, report_type: str, data: List[Dict[str, Any]]) -> str:
        """Generate formatted report."""
        self.log_info(f"Generating {report_type} report with {len(data)} records")
        
        if report_type == "csv":
            # Generate temporary CSV report
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"/tmp/pacer_report_{report_type}_{timestamp}.csv"
            return await self.export_to_csv(data, output_path)
        
        elif report_type == "json":
            # Generate JSON report
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"/tmp/pacer_report_{report_type}_{timestamp}.json"
            
            report_data = {
                "generated_at": datetime.utcnow().isoformat(),
                "report_type": report_type,
                "record_count": len(data),
                "records": data
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            self.log_info(f"JSON report generated: {output_path}")
            return output_path
        
        else:
            raise ValueError(f"Unsupported report type: {report_type}")

    async def cleanup_old_files(self, iso_date: str, days_to_keep: int = 30) -> None:
        """Clean up old files and directories."""
        if not self._directory_manager:
            return
        
        self.log_info(f"Cleaning up files older than {days_to_keep} days")
        
        await self._directory_manager.execute({
            "action": "cleanup_old_directories",
            "iso_date": iso_date,
            "days_to_keep": days_to_keep
        })

    async def create_temp_directory(self, iso_date: str, court_id: str, operation_type: str) -> str:
        """Create temporary directory for operations."""
        if not self._directory_manager:
            # Ensure service is initialized first
            if not self._initialized:
                await self.initialize()
            
            # Check again after initialization
            if not self._directory_manager:
                raise ValueError("DirectoryManager not initialized")
        
        temp_dir = await self._directory_manager.execute({
            "action": "create_temp_download_dir",
            "iso_date": iso_date,
            "court_id": court_id,
            "operation_type": operation_type
        })
        
        return str(temp_dir)

    async def verify_file_integrity(self, file_path: str) -> bool:
        """Verify file exists and is readable."""
        if not self._file_manager:
            return False
        
        try:
            exists = await self._file_manager.execute({
                "action": "exists",
                "filepath": file_path
            })
            
            if exists and Path(file_path).suffix.lower() == '.json':
                # Additional JSON validation
                content = await self._file_manager.execute({
                    "action": "read",
                    "filepath": file_path
                })
                json.loads(content)  # Validate JSON format
            
            return exists
            
        except Exception as e:
            self.log_warning(f"File integrity check failed for {file_path}: {e}")
            return False