"""Core consolidated services for PACER processing.

This package contains the 9 core services that replace the facade pattern:
1. Configuration Service
2. Case Processing Service
3. Classification Service
4. Relevance Service
5. Verification Service
6. Download Orchestration Service
7. File Operations Service
8. Metrics Reporting Service
9. S3 Management Service

All services follow the AsyncServiceBase pattern and use dependency injection.
"""

from .configuration.configuration_service import ConfigurationService
from .case_processing.case_processing_service import CaseProcessingService
from .classification.classification_service import ClassificationService
from .relevance.relevance_service import RelevanceService
from .verification.verification_service import VerificationService
from .download_orchestration.download_orchestration_service import DownloadOrchestrationService
from .file_operations.file_operations_service import FileOperationsService
from .metrics_reporting.metrics_reporting_service import MetricsReportingService
from .s3_management.s3_management_service import S3ManagementService

__all__ = [
    'ConfigurationService',
    'CaseProcessingService',
    'ClassificationService',
    'RelevanceService',
    'VerificationService',
    'DownloadOrchestrationService',
    'FileOperationsService',
    'MetricsReportingService',
    'S3ManagementService',
]