"""
Download Orchestration Service for PACER Core Services.

This service merges functionality from DownloadOrchestrationFacadeService and related
download components to provide comprehensive download workflow orchestration.
"""

from pathlib import Path
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
# Interface removed for simplicity - TODO: Add back when interfaces are properly organized


class DownloadOrchestrationService(AsyncServiceBase):
    """
    Core Download Orchestration Service for PACER download workflows.
    
    Provides comprehensive download orchestration including:
    - Download workflow validation
    - File download execution
    - S3 upload management
    - HTML-only processing
    - Authentication and navigation coordination
    """

    def __init__(
        self,
        download_validator: Optional[Any] = None,
        file_downloader: Optional[Any] = None,
        download_manager: Optional[Any] = None,
        browser_navigator: Optional[Any] = None,
        authentication_service: Optional[Any] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
    ):
        super().__init__(logger, config)
        self._download_validator = download_validator
        self._file_downloader = file_downloader
        self._download_manager = download_manager
        self._browser_navigator = browser_navigator
        self._authentication_service = authentication_service

    async def _setup_dependencies(self) -> None:
        """Setup download orchestration dependencies."""
        if self._download_validator:
            self._dependencies["download_validator"] = self._download_validator
        if self._file_downloader:
            self._dependencies["file_downloader"] = self._file_downloader
        if self._download_manager:
            self._dependencies["download_manager"] = self._download_manager
        if self._browser_navigator:
            self._dependencies["browser_navigator"] = self._browser_navigator
        if self._authentication_service:
            self._dependencies["authentication_service"] = self._authentication_service

    async def _validate_configuration(self) -> None:
        """Validate download orchestration configuration."""
        required_components = [
            ("download_validator", self._download_validator),
            ("file_downloader", self._file_downloader),
            ("download_manager", self._download_manager)
        ]
        
        missing = [name for name, component in required_components if not component]
        if missing:
            raise ValueError(f"Missing required components for DownloadOrchestrationService: {missing}")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate download methods."""
        if not self._initialized:
            await self.initialize()
            
        action = data.get("action")
        case_details = data.get("case_details", {})
        
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, f"Processing download action: {action}")
        
        if action == "execute_download_workflow":
            navigator = data.get("navigator")
            html_content = data.get("html_content", "")
            return await self.execute_download_workflow_comprehensive(navigator, case_details, html_content)
        elif action == "process_download_workflow":
            return await self.process_download_workflow(case_details)
        elif action == "validate_download":
            return await self.validate_case_for_download(case_details)
        elif action == "execute_document_download":
            navigator = data.get("navigator")
            return await self.execute_document_download(navigator, case_details)
        else:
            raise ValueError(f"Unknown action for DownloadOrchestrationService: {action}")

    async def execute_download_workflow_comprehensive(
        self, navigator: Any, case_details: Dict[str, Any], html_content: str
    ) -> Dict[str, Any]:
        """
        Execute comprehensive download workflow for a case.
        
        This follows the Phase 4 workflow from docket_processing.md.
        """
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, "Starting download workflow")
        
        # Step 1: Validate case for download
        should_attempt, reason = await self.validate_case_for_download(case_details)
        
        if not should_attempt:
            self.log_with_prefix("info", court_id, docket_num, f"Skipping download: {reason}")
            case_details = await self._handle_download_skip(case_details, html_content, reason)
        else:
            self.log_with_prefix("info", court_id, docket_num, "Proceeding with document download")
            case_details = await self._handle_document_download(navigator, case_details, html_content)
        
        self.log_with_prefix("info", court_id, docket_num, "Download workflow completed")
        return case_details

    async def _handle_download_skip(
        self, case_details: Dict[str, Any], html_content: str, reason: str
    ) -> Dict[str, Any]:
        """Handle cases where download is skipped."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("debug", court_id, docket_num, "Processing HTML-only case")
        
        # Create S3 key for HTML
        s3_key = f"{case_details.get('iso_date', 'unknown_date')}/html/{case_details.get('base_filename', 'unknown')}.html"
        
        # Upload HTML to S3
        s3_link = await self._download_manager.process_html_only(html_content, s3_key)
        
        # Update case details
        case_details.update({
            's3_html': s3_link,
            'is_downloaded': False,
            'download_skip_reason': reason,
            'processing_status': 'html_only'
        })
        
        return case_details

    async def _handle_document_download(
        self, navigator: Any, case_details: Dict[str, Any], html_content: str
    ) -> Dict[str, Any]:
        """Handle full document download workflow."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        try:
            # Execute document download
            downloaded_file_path = await self.execute_document_download(navigator, case_details)
            
            if downloaded_file_path:
                self.log_with_prefix("info", court_id, docket_num, 
                                   f"Document downloaded successfully: {downloaded_file_path}")
                case_details = await self._handle_download_success(case_details, downloaded_file_path, html_content)
            else:
                self.log_with_prefix("warning", court_id, docket_num, "Document download failed")
                case_details = await self._handle_download_failure(case_details, html_content, "Download failed")
                
        except Exception as e:
            self.log_with_prefix("error", court_id, docket_num, f"Download error: {e}")
            case_details = await self._handle_download_failure(case_details, html_content, str(e))
        
        return case_details

    async def _handle_download_success(
        self, case_details: Dict[str, Any], downloaded_file_path: str, html_content: str
    ) -> Dict[str, Any]:
        """Handle successful download."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        # Create S3 key for document
        s3_key = f"{case_details.get('iso_date', 'unknown_date')}/dockets/{Path(downloaded_file_path).name}"
        
        # Upload document to S3
        s3_link = await self._download_manager.upload_to_s3(downloaded_file_path, s3_key)
        
        # Also upload HTML for reference
        html_s3_key = f"{case_details.get('iso_date', 'unknown_date')}/html/{case_details.get('base_filename', 'unknown')}.html"
        s3_html_link = await self._download_manager.process_html_only(html_content, html_s3_key)
        
        # Update case details
        case_details.update({
            's3_link': s3_link,
            's3_html': s3_html_link,
            'is_downloaded': True,
            'downloaded_file_path': downloaded_file_path,
            'processing_status': 'download_success'
        })
        
        self.log_with_prefix("info", court_id, docket_num, f"Document uploaded to S3: {s3_link}")
        return case_details

    async def _handle_download_failure(
        self, case_details: Dict[str, Any], html_content: str, error_message: str
    ) -> Dict[str, Any]:
        """Handle failed download."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        # Still upload HTML for reference
        html_s3_key = f"{case_details.get('iso_date', 'unknown_date')}/html/{case_details.get('base_filename', 'unknown')}.html"
        s3_html_link = await self._download_manager.process_html_only(html_content, html_s3_key)
        
        # Update case details
        case_details.update({
            's3_html': s3_html_link,
            'is_downloaded': False,
            'download_error': error_message,
            'processing_status': 'download_failed'
        })
        
        self.log_with_prefix("warning", court_id, docket_num, 
                           f"Download failed, HTML saved: {s3_html_link}")
        return case_details

    async def validate_case_for_download(self, case_details: Dict[str, Any]) -> tuple[bool, str]:
        """Validate if case should proceed with download."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        if not self._download_validator:
            return True, "No validator configured"
        
        should_skip, reason = self._download_validator.should_skip_download(case_details)
        
        if should_skip:
            self.log_with_prefix("debug", court_id, docket_num, f"Download validation failed: {reason}")
            return False, reason
        
        self.log_with_prefix("debug", court_id, docket_num, "Download validation passed")
        return True, "Validation passed"

    # DownloadOrchestrationServiceInterface implementation
    async def execute_download_workflow(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Execute complete download workflow for a case."""
        return await self.process_download_workflow(case_details)

    async def process_download_workflow(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Process the download workflow phases."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, "Processing download workflow")
        
        # Phase 1: Validate case for download
        should_attempt, reason = await self.validate_case_for_download(case_details)
        
        if not should_attempt:
            case_details.update({
                'download_status': 'skipped',
                'download_skip_reason': reason,
                'is_downloaded': False
            })
            return case_details
        
        # Phase 2: Prepare download context (placeholder for future enhancement)
        case_details = await self._prepare_download_context(case_details)
        
        # Phase 3: Set status for download execution (actual download requires navigator)
        case_details.update({
            'download_status': 'ready_for_download',
            'is_downloaded': False
        })
        
        return case_details

    async def _prepare_download_context(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare download context with required metadata."""
        # Add download-specific metadata
        if 'download_metadata' not in case_details:
            case_details['download_metadata'] = {}
        
        case_details['download_metadata'].update({
            'prepared_at': self.config.get('iso_date', 'unknown'),
            'download_ready': True
        })
        
        return case_details

    async def authenticate_session(self, credentials: Dict[str, Any]) -> bool:
        """Authenticate PACER session."""
        if not self._authentication_service:
            self.log_warning("Authentication service not configured")
            return False
        
        return await self._authentication_service.authenticate(credentials)

    async def navigate_to_case(self, case_details: Dict[str, Any]) -> bool:
        """Navigate browser to case page."""
        if not self._browser_navigator:
            self.log_warning("Browser navigator not configured")
            return False
        
        return await self._browser_navigator.navigate_to_case(case_details)

    async def execute_query(self, query_params: Dict[str, Any]) -> list[Dict[str, Any]]:
        """Execute PACER query and return results."""
        if not self._browser_navigator:
            self.log_warning("Browser navigator not configured")
            return []
        
        return await self._browser_navigator.execute_query(query_params)

    async def execute_document_download(self, navigator: Any, case_details: Dict[str, Any]) -> Optional[str]:
        """Execute the actual document download."""
        if not self._file_downloader:
            raise ValueError("File downloader not configured")
        
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, "Executing document download")
        
        try:
            downloaded_path = await self._file_downloader.download_file(navigator, case_details)
            if downloaded_path:
                self.log_with_prefix("info", court_id, docket_num, f"Download successful: {downloaded_path}")
            return downloaded_path
        except Exception as e:
            self.log_with_prefix("error", court_id, docket_num, f"Download execution failed: {e}")
            return None