"""
Docket Processing Orchestrator for PACER Core Services.

This service is the main workflow coordinator that orchestrates the complete
docket processing lifecycle using all other core services.
"""

from typing import Any, Dict, Optional
from playwright.async_api import Page

from src.infrastructure.patterns.component_base import AsyncServiceBase
# Interface removed for simplicity - TODO: Add back when interfaces are properly organized
from src.pacer._core_services.case_processing.case_processing_service import CaseProcessingService
from src.pacer._core_services.relevance.relevance_service import RelevanceService
from src.pacer._core_services.classification.classification_service import ClassificationService
from src.pacer._core_services.verification.verification_service import VerificationService
from src.pacer._core_services.download_orchestration.download_orchestration_service import DownloadOrchestrationService
from src.pacer._core_services.file_operations.file_operations_service import FileOperationsService
from src.pacer._core_services.configuration.configuration_service import ConfigurationService


class DocketProcessingOrchestrator(AsyncServiceBase):
    """
    Main Docket Processing Orchestrator for PACER workflow coordination.
    
    This service coordinates the complete docket processing lifecycle:
    - Phase 1: HTML Content Processing (Case Processing Service)
    - Phase 2: Relevance & Classification (Relevance + Classification Services)  
    - Phase 3: Case Verification (Verification Service)
    - Phase 4: Download Workflow (Download Orchestration Service)
    - Phase 5: File Operations & S3 Upload (File Operations Service)
    
    Follows the exact workflow from docket_processing.md.
    """

    def __init__(
        self,
        case_processing_service: Optional[CaseProcessingService] = None,
        relevance_service: Optional[RelevanceService] = None,
        classification_service: Optional[ClassificationService] = None,
        verification_service: Optional[VerificationService] = None,
        download_orchestration_service: Optional[DownloadOrchestrationService] = None,
        file_operations_service: Optional[FileOperationsService] = None,
        configuration_service: Optional[ConfigurationService] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
    ):
        super().__init__(logger, config)
        self._case_processing = case_processing_service
        self._relevance = relevance_service
        self._classification = classification_service
        self._verification = verification_service
        self._download_orchestration = download_orchestration_service
        self._file_operations = file_operations_service
        self._configuration = configuration_service

    async def _setup_dependencies(self) -> None:
        """Setup orchestrator dependencies."""
        services = {
            "case_processing": self._case_processing,
            "relevance": self._relevance,
            "classification": self._classification,
            "verification": self._verification,
            "download_orchestration": self._download_orchestration,
            "file_operations": self._file_operations,
            "configuration": self._configuration,
        }
        
        # Initialize all services
        for name, service in services.items():
            if service:
                if not service.is_initialized():
                    await service.initialize()
                self._dependencies[name] = service

    async def _validate_configuration(self) -> None:
        """Validate orchestrator configuration."""
        required_services = [
            ("case_processing", self._case_processing),
            ("relevance", self._relevance),
            ("classification", self._classification),
            ("verification", self._verification),
            ("download_orchestration", self._download_orchestration),
            ("file_operations", self._file_operations),
        ]
        
        missing = [name for name, service in required_services if not service]
        if missing:
            raise ValueError(f"Missing required services for DocketProcessingOrchestrator: {missing}")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate orchestration methods."""
        if not self._initialized:
            await self.initialize()
            
        action = data.get("action")
        
        if action == "process_docket":
            return await self.process_docket_comprehensive(
                page=data.get("page"),
                initial_details=data.get("initial_details", {})
            )
        elif action == "process_docket_case":
            return await self.process_docket_case(
                page=data.get("page"),
                initial_details=data.get("initial_details", {})
            )
        else:
            raise ValueError(f"Unknown action for DocketProcessingOrchestrator: {action}")

    async def process_docket_comprehensive(
        self, page: Page, initial_details: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Execute the complete docket processing workflow.
        
        This is the main orchestration method that follows the exact phases
        from docket_processing.md Section 4.
        """
        court_id = initial_details.get("court_id", "N/A")
        docket_num = initial_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, "Starting comprehensive docket processing")
        
        try:
            # PHASE 1: HTML Content Processing
            case_details = await self._execute_phase_1_html_processing(page, initial_details)
            if not case_details:
                self.log_with_prefix("error", court_id, docket_num, "Phase 1 (HTML Processing) failed")
                return None
            
            # PHASE 2: Relevance & Classification
            case_details = await self._execute_phase_2_relevance_classification(case_details)
            
            # Check if case should be skipped after relevance filters
            if case_details.get("_skip_processing", False):
                self.log_with_prefix("info", court_id, docket_num, "Case skipped after relevance filters")
                # Still save metadata
                return await self._save_metadata_only(case_details)
            
            # PHASE 3: Case Verification  
            should_process = await self._execute_phase_3_verification(case_details)
            if not should_process:
                self.log_with_prefix("info", court_id, docket_num, "Case skipped after verification")
                # Save metadata only
                return await self._save_metadata_only(case_details)
            
            # PHASE 4: Download Workflow
            case_details = await self._execute_phase_4_download_workflow(page, case_details)
            
            # PHASE 5: File Operations & S3 Upload
            result = await self._execute_phase_5_save_and_upload(case_details)
            
            self.log_with_prefix("info", court_id, docket_num, "Comprehensive docket processing completed successfully")
            return result
            
        except Exception as e:
            self.log_with_prefix("error", court_id, docket_num, f"Comprehensive docket processing failed: {e}")
            return None

    async def _execute_phase_1_html_processing(
        self, page: Page, initial_details: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Execute Phase 1: HTML Content Processing."""
        court_id = initial_details.get("court_id", "N/A")
        docket_num = initial_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, "Phase 1: HTML Content Processing")
        
        # Get HTML content
        html_content = await page.content()
        
        # Process case HTML
        case_details = await self._case_processing.execute({
            "action": "process_case",
            "page": page,
            "initial_details": initial_details,
            "html_content": html_content
        })
        
        if not case_details:
            self.log_with_prefix("error", court_id, docket_num, "HTML processing returned no case details")
            return None
        
        # Add processing metadata
        case_details.update({
            "_processing_phase": "html_completed",
            "_html_content": html_content,  # Store for later phases
            "base_filename": case_details.get("base_filename") or f"{court_id}_{docket_num}"
        })
        
        self.log_with_prefix("info", court_id, docket_num, "Phase 1 completed successfully")
        return case_details

    async def _execute_phase_2_relevance_classification(
        self, case_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute Phase 2: Relevance & Classification."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, "Phase 2: Relevance & Classification")
        
        # Step 1: Determine case relevance
        is_relevant = await self._relevance.execute({
            "action": "determine_relevance",
            "case_details": case_details
        })
        
        case_details["is_relevant"] = is_relevant
        
        # Step 2: Classify case (regardless of relevance for metadata)
        html_content = case_details.get("_html_content", "")
        case_details = await self._classification.execute({
            "action": "classify_case",
            "case_details": case_details,
            "html_content": html_content
        })
        
        # Step 3: Apply relevance-based processing decisions
        if not is_relevant:
            case_details["_skip_processing"] = True
            case_details["_skip_reason"] = "Not relevant"
        
        case_details["_processing_phase"] = "relevance_classification_completed"
        
        self.log_with_prefix("info", court_id, docket_num, 
                           f"Phase 2 completed: relevant={is_relevant}")
        return case_details

    async def _execute_phase_3_verification(
        self, case_details: Dict[str, Any]
    ) -> bool:
        """Execute Phase 3: Case Verification."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, "Phase 3: Case Verification")
        
        # Check if explicitly requested
        is_explicitly_requested = case_details.get("_is_explicitly_requested", False)
        
        # Verify case should be processed
        should_process = await self._verification.execute({
            "action": "verify_case",
            "case_details": case_details,
            "is_explicitly_requested": is_explicitly_requested
        })
        
        case_details["_verification_result"] = should_process
        case_details["_processing_phase"] = "verification_completed"
        
        self.log_with_prefix("info", court_id, docket_num, 
                           f"Phase 3 completed: should_process={should_process}")
        return should_process

    async def _execute_phase_4_download_workflow(
        self, page: Page, case_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute Phase 4: Download Workflow."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, "Phase 4: Download Workflow")
        
        # Execute download orchestration
        html_content = case_details.get("_html_content", "")
        case_details = await self._download_orchestration.execute({
            "action": "execute_download_workflow",
            "navigator": page,  # Using page as navigator for now
            "case_details": case_details,
            "html_content": html_content
        })
        
        case_details["_processing_phase"] = "download_completed"
        
        download_status = case_details.get("processing_status", "unknown")
        self.log_with_prefix("info", court_id, docket_num, 
                           f"Phase 4 completed: status={download_status}")
        return case_details

    async def _execute_phase_5_save_and_upload(
        self, case_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute Phase 5: File Operations & S3 Upload."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, "Phase 5: File Operations & S3 Upload")
        
        # Get ISO date from config or case details
        iso_date = case_details.get("iso_date") or self.config.get("iso_date")
        if not iso_date:
            raise ValueError("ISO date not available for file operations")
        
        # Clean up temporary fields before saving
        case_details = await self._cleanup_temporary_fields(case_details)
        
        # Save and upload case data
        result = await self._file_operations.execute({
            "action": "save_and_upload_case_data",
            "case_data": case_details,
            "iso_date": iso_date
        })
        
        # Merge result with case details
        case_details.update({
            "_processing_phase": "completed",
            "_local_path": result.get("local_path"),
            "_s3_url": result.get("s3_url"),
            "_processing_status": result.get("status")
        })
        
        self.log_with_prefix("info", court_id, docket_num, 
                           f"Phase 5 completed: status={result.get('status')}")
        return case_details

    async def _save_metadata_only(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Save case metadata without full processing."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_with_prefix("info", court_id, docket_num, "Saving metadata only (skipped processing)")
        
        # Clean up temporary fields
        case_details = await self._cleanup_temporary_fields(case_details)
        case_details["_processing_status"] = "metadata_only"
        
        # Get ISO date
        iso_date = case_details.get("iso_date") or self.config.get("iso_date")
        if not iso_date:
            raise ValueError("ISO date not available for file operations")
        
        # Save metadata
        result = await self._file_operations.execute({
            "action": "save_and_upload_case_data",
            "case_data": case_details,
            "iso_date": iso_date
        })
        
        case_details.update({
            "_local_path": result.get("local_path"),
            "_s3_url": result.get("s3_url")
        })
        
        return case_details

    async def _cleanup_temporary_fields(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Remove temporary processing fields before final save."""
        cleaned_data = case_details.copy()
        
        # Remove HTML content (too large for storage)
        cleaned_data.pop("_html_content", None)
        
        # Remove other temporary fields but keep important processing metadata
        fields_to_remove = [
            field for field in cleaned_data.keys() 
            if field.startswith('_') and field not in [
                '_processing_phase', '_processing_status', '_local_path', '_s3_url',
                '_verification_result', '_is_explicitly_requested'
            ]
        ]
        
        for field in fields_to_remove:
            cleaned_data.pop(field, None)
        
        return cleaned_data

    # DocketProcessorInterface implementation
    async def process_docket(self, docket_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process docket using new core services workflow."""
        page = docket_data.get("page")
        initial_details = docket_data.get("initial_details", {})
        
        if not page:
            raise ValueError("Page object required for docket processing")
        
        return await self.process_docket_comprehensive(page, initial_details)

    async def process_docket_case(
        self, page: Page, initial_details: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Alias for comprehensive processing - maintains compatibility."""
        return await self.process_docket_comprehensive(page, initial_details)

    async def orchestrate_job(self, job_data: Dict[str, Any]) -> str:
        """Orchestrate a processing job."""
        court_id = job_data.get("court_id", "N/A")
        self.log_info(f"Orchestrating job for court: {court_id}")
        
        # This would integrate with the job processing system
        # For now, return a job ID
        import uuid
        job_id = str(uuid.uuid4())
        
        self.log_info(f"Job orchestrated with ID: {job_id}")
        return job_id

    async def execute_bulk_processing(self, jobs: list[Dict[str, Any]]) -> list[Dict[str, Any]]:
        """Execute bulk processing of multiple jobs."""
        self.log_info(f"Executing bulk processing for {len(jobs)} jobs")
        
        results = []
        for job in jobs:
            try:
                result = await self.process_docket(job)
                if result:
                    results.append(result)
            except Exception as e:
                self.log_error(f"Bulk processing job failed: {e}")
                # Continue with other jobs
        
        self.log_info(f"Bulk processing completed: {len(results)}/{len(jobs)} successful")
        return results

    async def handle_legacy_workflows(self, workflow_type: str, data: Dict[str, Any]) -> Any:
        """Handle legacy workflow patterns."""
        self.log_info(f"Handling legacy workflow: {workflow_type}")
        
        if workflow_type == "single_docket":
            # Route to comprehensive processing
            return await self.process_docket(data)
        elif workflow_type == "bulk_docket":
            # Route to bulk processing  
            return await self.execute_bulk_processing(data.get("jobs", []))
        else:
            raise ValueError(f"Unsupported legacy workflow type: {workflow_type}")

    async def get_processing_status(self, job_id: str) -> Dict[str, Any]:
        """Get status of a processing job."""
        # Placeholder for job status tracking
        return {
            "job_id": job_id,
            "status": "completed",
            "message": "Job processing status tracking not yet implemented"
        }