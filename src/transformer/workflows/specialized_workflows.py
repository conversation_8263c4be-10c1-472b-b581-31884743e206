# /src/services/transformer/specialized_workflows.py
"""
# Removed dependency_injector imports - using container-based injection
Specialized workflows for DataTransformer.

This module handles specific processing modes and specialized workflows
extracted from transformer.py as part of Phase 3 refactoring.
"""
import asyncio
import logging
import os
from typing import Dict, List, Any, Optional

# Removed dependency_injector imports - using container-based injection
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.transformer.facades.file_processing_service import FileProcessingService
from src.transformer.jobs.job_orchestration_service import JobOrchestrationService


class SpecializedWorkflows(AsyncServiceBase):
    """Handles specialized processing workflows."""

    def __init__(self,
                 file_processing_service: FileProcessingService,
                 job_orchestration_service: JobOrchestrationService,
                 logger: Optional[logging.Logger] = None,
                 config: Optional[Dict] = None,
                 docket_processor=None,
                 mdl_processor=None):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.file_processing_service = file_processing_service
        self.job_orchestration_service = job_orchestration_service
        self.docket_processor = docket_processor
        self.mdl_processor = mdl_processor

    async def _execute_action(self, data: Any) -> Any:
        """Execute SpecializedWorkflows actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'run_law_firm_normalization_only':
                num_workers = action_data.get('num_workers', 4)
                return await self.run_law_firm_normalization_only(num_workers)
            elif action == 'run_mdl_title_update_only':
                num_workers = action_data.get('num_workers', 4)
                return await self.run_mdl_title_update_only(num_workers)
            elif action == 'run_custom_workflow':
                workflow_name = action_data.get('workflow_name', '')
                kwargs = action_data.get('kwargs', {})
                return await self.run_custom_workflow(workflow_name, **kwargs)
            elif action == 'normalize_single_file_law_firms':
                json_path = action_data.get('json_path', '')
                return await self._normalize_single_file_law_firms(json_path)
            elif action == 'update_single_file_mdl_title':
                json_path = action_data.get('json_path', '')
                return await self._update_single_file_mdl_title(json_path)
            elif action == 'validate_workflow_configuration':
                workflow_name = action_data.get('workflow_name', '')
                return await self._validate_workflow_configuration(workflow_name)
            elif action == 'get_available_workflows':
                return self._get_available_workflows()
            elif action == 'get_workflow_summary':
                return self.get_workflow_summary()
            elif action == 'validate_docket_processor':
                return await self._validate_docket_processor()
            elif action == 'validate_mdl_processor':
                return await self._validate_mdl_processor()
            elif action == 'get_workflow_configuration':
                workflow_name = action_data.get('workflow_name', '')
                return self._get_workflow_configuration(workflow_name)
            elif action == 'process_files_with_semaphore':
                file_paths = action_data.get('file_paths', [])
                processor_func = action_data.get('processor_func')
                num_workers = action_data.get('num_workers', 4)
                return await self._process_files_with_semaphore(file_paths, processor_func, num_workers)
        raise TransformerServiceError("Invalid action data provided to SpecializedWorkflows")

    async def run_law_firm_normalization_only(self, num_workers: int = 4) -> List[Dict]:
        """
        Run law firm normalization workflow only.
        """
        self.log_info("Running Law Firm Normalization ONLY mode")
        return await self.job_orchestration_service.run_jobs_async(
            reprocess_files=True,
            start_from_incomplete=False,
            skip_files=set(),
            force_reprocess=True,
            num_workers=num_workers,
            transformer=self,
            job_type='law_firm_normalization'
        )

    async def run_mdl_title_update_only(self, num_workers: int = 4) -> List[Dict]:
        """
        Run MDL title update workflow only.
        """
        self.log_info("Running MDL Title Update ONLY mode")
        return await self.job_orchestration_service.run_jobs_async(
            reprocess_files=True,
            start_from_incomplete=False,
            skip_files=set(),
            force_reprocess=True,
            num_workers=num_workers,
            transformer=self,
            job_type='mdl_title_update'
        )

    async def run_custom_workflow(self, workflow_name: str, **kwargs) -> List[Dict]:
        """
        Run a custom workflow by name.
        
        Args:
            workflow_name: Name of the workflow to run
            **kwargs: Additional arguments for the workflow
            
        Returns:
            List of processing results
        """
        self.log_info(f"Running custom workflow: {workflow_name}")

        # Map workflow names to methods
        workflow_map = {
            'law_firm_normalization': self.run_law_firm_normalization_only,
            'mdl_title_update': self.run_mdl_title_update_only
        }

        if workflow_name not in workflow_map:
            self.log_error(f"Unknown workflow: {workflow_name}")
            return []

        workflow_method = workflow_map[workflow_name]

        try:
            return await workflow_method(**kwargs)
        except Exception as e:
            self.log_error(f"Error running workflow {workflow_name}: {e}")
            return []

    async def _normalize_single_file_law_firms(self, json_path: str) -> Dict:
        """Normalize law firms for a single file (internal helper)."""
        base_filename = os.path.splitext(os.path.basename(json_path))[0]

        try:
            # Load data
            data = self.file_handler.load_json(json_path)
            if not data:
                return {
                    'filename': base_filename,
                    'status': 'error',
                    'message': 'Failed to load JSON data'
                }

            # Run law firm processing
            if self.docket_processor:
                await self.docket_processor.process_law_firms(data)

                # Save updated data
                self.file_handler.save_json(json_path, data)

                return {
                    'filename': base_filename,
                    'status': 'success',
                    'message': 'Law firm normalization completed'
                }
            else:
                return {
                    'filename': base_filename,
                    'status': 'error',
                    'message': 'DocketProcessor not available'
                }

        except Exception as e:
            self.log_error(f"Error normalizing law firms for {base_filename}: {e}")
            return {
                'filename': base_filename,
                'status': 'error',
                'message': str(e)
            }

    async def _update_single_file_mdl_title(self, json_path: str) -> Dict:
        """Update MDL title for a single file (internal helper)."""
        base_filename = os.path.splitext(os.path.basename(json_path))[0]

        try:
            # Load data
            data = self.file_handler.load_json(json_path)
            if not data:
                return {
                    'filename': base_filename,
                    'status': 'error',
                    'message': 'Failed to load JSON data'
                }

            # Run MDL processing
            if self.mdl_processor:
                original_title = data.get('title')
                original_allegations = data.get('allegations')

                # Add MDL info (includes title and allegations from lookup)
                await self.mdl_processor.add_mdl_info_async(data)

                # Check if anything was updated
                title_updated = data.get('title') != original_title
                allegations_updated = data.get('allegations') != original_allegations

                if title_updated or allegations_updated:
                    # Save updated data
                    self.file_handler.save_json(json_path, data)

                    update_details = []
                    if title_updated:
                        update_details.append('title')
                    if allegations_updated:
                        update_details.append('allegations')

                    return {
                        'filename': base_filename,
                        'status': 'success',
                        'message': f'Updated: {", ".join(update_details)}',
                        'mdl_num': data.get('mdl_num'),
                        'title_updated': title_updated,
                        'allegations_updated': allegations_updated
                    }
                else:
                    return {
                        'filename': base_filename,
                        'status': 'no_change',
                        'message': 'No MDL updates needed',
                        'mdl_num': data.get('mdl_num')
                    }
            else:
                return {
                    'filename': base_filename,
                    'status': 'error',
                    'message': 'MDLProcessor not available'
                }

        except Exception as e:
            self.log_error(f"Error updating MDL title for {base_filename}: {e}")
            return {
                'filename': base_filename,
                'status': 'error',
                'message': str(e)
            }

    async def _validate_workflow_configuration(self, workflow_name: str) -> Dict[str, Any]:
        """Validate workflow configuration."""
        validation_result = {
            'workflow_name': workflow_name,
            'is_valid': True,
            'errors': [],
            'warnings': []
        }

        workflow_map = {
            'law_firm_normalization': self.run_law_firm_normalization_only,
            'mdl_title_update': self.run_mdl_title_update_only
        }

        if workflow_name not in workflow_map:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"Unknown workflow: {workflow_name}")
            return validation_result

        # Validate dependencies
        if workflow_name == 'law_firm_normalization':
            if not self.docket_processor:
                validation_result['errors'].append("DocketProcessor not available for law firm normalization")
                validation_result['is_valid'] = False

        elif workflow_name == 'mdl_title_update':
            if not self.mdl_processor:
                validation_result['errors'].append("MDLProcessor not available for MDL title update")
                validation_result['is_valid'] = False

        # Validate file handler
        if not self.file_handler:
            validation_result['errors'].append("FileHandler not available")
            validation_result['is_valid'] = False

        return validation_result

    def _get_available_workflows(self) -> List[str]:
        """Get list of available workflow names."""
        return ['law_firm_normalization', 'mdl_title_update']

    def get_workflow_summary(self) -> str:
        """Get summary of available workflows and their status."""
        workflows = self._get_available_workflows()
        status_parts = []

        for workflow in workflows:
            if workflow == 'law_firm_normalization':
                status = "available" if self.docket_processor else "unavailable (no DocketProcessor)"
            elif workflow == 'mdl_title_update':
                status = "available" if self.mdl_processor else "unavailable (no MDLProcessor)"
            else:
                status = "unknown"

            status_parts.append(f"{workflow}: {status}")

        return f"SpecializedWorkflows with {len(workflows)} workflows: {', '.join(status_parts)}"

    async def _validate_docket_processor(self) -> Dict[str, Any]:
        """Validate docket processor availability and configuration."""
        validation_result = {
            'is_available': self.docket_processor is not None,
            'processor_type': type(self.docket_processor).__name__ if self.docket_processor else None,
            'methods_available': []
        }

        if self.docket_processor:
            # Check for required methods
            required_methods = ['process_law_firms']
            for method_name in required_methods:
                if hasattr(self.docket_processor, method_name):
                    validation_result['methods_available'].append(method_name)

        return validation_result

    async def _validate_mdl_processor(self) -> Dict[str, Any]:
        """Validate MDL processor availability and configuration."""
        validation_result = {
            'is_available': self.mdl_processor is not None,
            'processor_type': type(self.mdl_processor).__name__ if self.mdl_processor else None,
            'methods_available': []
        }

        if self.mdl_processor:
            # Check for required methods
            required_methods = ['add_mdl_info_async']
            for method_name in required_methods:
                if hasattr(self.mdl_processor, method_name):
                    validation_result['methods_available'].append(method_name)

        return validation_result

    def _get_workflow_configuration(self, workflow_name: str) -> Dict[str, Any]:
        """Get configuration details for a specific workflow."""
        configurations = {
            'law_firm_normalization': {
                'description': 'Normalize law firm names in docket files',
                'required_dependencies': ['docket_processor', 'file_handler'],
                'default_workers': 4,
                'file_types': ['.json'],
                'parameters': ['num_workers']
            },
            'mdl_title_update': {
                'description': 'Update MDL titles and allegations in docket files',
                'required_dependencies': ['mdl_processor', 'file_handler'],
                'default_workers': 4,
                'file_types': ['.json'],
                'parameters': ['num_workers']
            }
        }

        return configurations.get(workflow_name, {})

    async def _process_files_with_semaphore(self, file_paths: List[str],
                                            processor_func, num_workers: int = 4) -> List[Dict]:
        """Process files with semaphore-controlled concurrency."""
        if not file_paths:
            return []

        semaphore = asyncio.Semaphore(num_workers)

        async def process_with_semaphore(file_path: str) -> Dict:
            async with semaphore:
                return await processor_func(file_path)

        # Run processing
        tasks = [process_with_semaphore(file_path) for file_path in file_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'filename': os.path.basename(file_paths[i]),
                    'status': 'error',
                    'message': str(result)
                })
            elif isinstance(result, dict):
                processed_results.append(result)

        return processed_results
