# /src/services/transformer/component_factory.py
"""
Component factory for DataTransformer.

This module handles initialization and management of components used by DataTransformer.
Extracted from transformer.py as part of Phase 3 refactoring.
"""
import os
from typing import Dict, Optional, Union, Any, List

import pandas as pd
# Removed dependency_injector imports - using container-based injection
from src.infrastructure.external.openai_client import OpenAIClient as GPTClient
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.services.ai.deepseek_service import DeepSeekService
from src.services.ai.mistral_service import MistralService
from src.transformer.config.constants import (
    ComponentFactoryActions, ErrorMessages, LLMProvider
)


class ComponentFactory(AsyncServiceBase):
    """Factory for creating and managing DataTransformer components."""

    def __init__(self,
                 config: Dict ,
                 logger: LoggerProtocol ,
                 openai_client: Optional[GPTClient] ,
                 deepseek_service: Optional[DeepSeekService] ,
                 mistral_service: Optional[MistralService] ):
        # Initialize AsyncServiceBase  
        super().__init__(logger, config)

        # Store pre-configured clients
        self._openai_client = openai_client
        self._deepseek_service = deepseek_service
        self._mistral_service = mistral_service

    async def _execute_action(self, data: Any) -> Any:
        """Execute ComponentFactory actions."""
        if isinstance(data, dict):
            action = data.get('action')
            match action:
                case ComponentFactoryActions.LOAD_MDL_DATA.value:
                    return self.load_mdl_data(data['mdl_path'])
                case ComponentFactoryActions.INITIALIZE_PDF_PROCESSOR.value:
                    return self.initialize_pdf_processor(data.get('config', self.config))
                case ComponentFactoryActions.GET_LLM_CLIENT.value:
                    return self.get_llm_client(data['provider'])
                case ComponentFactoryActions.GET_SUPPORTED_PROVIDERS.value:
                    return self.get_supported_providers()
                case ComponentFactoryActions.VALIDATE_CONFIG.value:
                    return self.validate_config(data.get('config', self.config))
                case ComponentFactoryActions.GET_COMPONENT_STATUS.value:
                    return self.get_component_status()
                case _:
                    raise TransformerServiceError(f"{ErrorMessages.INVALID_ACTION}: '{action}'")
        raise TransformerServiceError(ErrorMessages.INVALID_ACTION)

    def load_mdl_data(self, mdl_path: str) -> pd.DataFrame:
        """
        Load MDL litigation data from JSON file.
        
        Args:
            mdl_path: Path to MDL lookup JSON file
            
        Returns:
            DataFrame with MDL litigation data
        """
        default_columns = ['mdl_num', 'litigation', 'description', 'short_summary', 'summary']

        try:
            if os.path.exists(mdl_path):
                self.log_info(f"Loading MDL litigation data from {mdl_path}")
                df = pd.read_json(mdl_path)

                # Ensure mdl_num is string type for consistency
                if 'mdl_num' in df.columns:
                    df['mdl_num'] = df['mdl_num'].astype(str)

                # Ensure all expected columns exist
                for col in default_columns:
                    if col not in df.columns:
                        self.log_debug(f"Adding missing column '{col}' to MDL data")
                        df[col] = None
                return df
            else:
                self.log_warning(f"MDL file not found at {mdl_path}, creating empty DataFrame")
                return pd.DataFrame(columns=default_columns)
        except Exception as e:
            self.log_error(f"Error loading MDL data from {mdl_path}: {e}")
            return pd.DataFrame(columns=default_columns)

    def initialize_pdf_processor(self, config: Dict) -> Optional[MistralService]:
        """
        Initialize PDF processor using Mistral service.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            MistralService instance or None if initialization fails
        """
        try:
            mistral_config = config.get('mistral', {})
            if not mistral_config:
                self.log_warning("No Mistral config found, PDF processing may be limited")
                return None

            # Return the injected mistral service
            if self._mistral_service is None:
                self.log_warning("Mistral service not available - must be injected via dependency injection")
                return None

            self.log_info("PDF processor (Mistral) initialized successfully")
            return self._mistral_service

        except Exception as e:
            self.log_error(f"Failed to initialize PDF processor: {e}")
            return None

    def get_llm_client(self, provider: str) -> Union[GPTClient, DeepSeekService]:
        """
        Get pre-configured LLM client based on provider.
        
        Args:
            provider: LLM provider name ('openai' or 'deepseek')
            
        Returns:
            Pre-configured LLM client/service
            
        Raises:
            TransformerServiceError: If requested client was not injected
        """
        match provider.lower():
            case LLMProvider.OPENAI.value:
                if self._openai_client is None:
                    raise TransformerServiceError(
                        f"OpenAI client not available - must be injected via dependency injection")
                self.log_info("Using injected OpenAI client")
                return self._openai_client
            case LLMProvider.DEEPSEEK.value:
                if self._deepseek_service is None:
                    raise TransformerServiceError(
                        f"DeepSeek service not available - must be injected via dependency injection")
                self.log_info("Using injected DeepSeek service")
                return self._deepseek_service
            case _:
                self.log_warning(f"Unknown llm_provider '{provider}', trying DeepSeek fallback")
                if self._deepseek_service is None:
                    raise TransformerServiceError(
                        f"DeepSeek service not available for fallback - must be injected via dependency injection")
                return self._deepseek_service

    def get_supported_providers(self) -> List[str]:
        """
        Get list of supported LLM providers.
        
        Returns:
            List of supported provider names
        """
        return ['openai', 'deepseek']

    def validate_config(self, config: Dict) -> Dict[str, Any]:
        """
        Validate configuration for component initialization.
        
        Args:
            config: Configuration dictionary to validate
            
        Returns:
            Validation result dictionary
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'components': {}
        }

        # Check for required API keys
        if not config.get('openai_api_key') and not config.get('deepseek_api_key'):
            validation_result['errors'].append("No LLM API keys found (openai_api_key or deepseek_api_key)")
            validation_result['is_valid'] = False

        # Check Mistral config
        mistral_config = config.get('mistral', {})
        if not mistral_config.get('api_key'):
            validation_result['warnings'].append("No Mistral API key found, PDF processing will be limited")

        validation_result['components']['mistral'] = bool(mistral_config.get('api_key'))
        validation_result['components']['openai'] = bool(config.get('openai_api_key'))
        validation_result['components']['deepseek'] = bool(config.get('deepseek_api_key'))

        return validation_result

    def get_component_status(self) -> Dict[str, Any]:
        """
        Get status of all components.
        
        Returns:
            Component status dictionary
        """
        return {
            'factory_initialized': True,
            'supported_providers': self.get_supported_providers(),
            'config_validation': self.validate_config(self.config),
            'default_columns': ['mdl_num', 'litigation', 'description', 'short_summary', 'summary']
        }
