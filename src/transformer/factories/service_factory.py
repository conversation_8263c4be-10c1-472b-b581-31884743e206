"""
Domain Service Factory

Creates domain services with proper dependency injection.
"""
import logging
from typing import Dict, Optional, Any

from src.infrastructure.patterns.component_base import AsyncServiceBase

# Import domain facades
from ..facades.document_processing_service import DocumentProcessingService
from ..facades.enrichment_service import EnrichmentService
from ..facades.persistence_service import PersistenceService
from ..facades.job_execution_service import JobExecutionService


class TransformerServiceFactory(AsyncServiceBase):
    """
    Domain factory for creating transformer services.
    
    Creates and configures domain services with proper dependency injection.
    """
    
    def __init__(self, 
                 config: Dict[str, Any],
                 logger: Optional[logging.Logger] = None):
        """Initialize domain factory."""
        super().__init__(logger, config)
        
        self.log_info("TransformerServiceFactory (domain) initialized")
    
    async def create_document_processing_service(self, **kwargs) -> DocumentProcessingService:
        """Create DocumentProcessingService."""
        self.log_info("Creating DocumentProcessingService")
        
        return DocumentProcessingService(
            config=self.config,
            logger=self.logger,
            **kwargs
        )
    
    async def create_enrichment_service(self, **kwargs) -> EnrichmentService:
        """Create EnrichmentService."""
        self.log_info("Creating EnrichmentService")
        
        return EnrichmentService(
            config=self.config,
            logger=self.logger,
            **kwargs
        )
    
    async def create_persistence_service(self, **kwargs) -> PersistenceService:
        """Create PersistenceService."""
        self.log_info("Creating PersistenceService")
        
        return PersistenceService(
            config=self.config,
            logger=self.logger,
            **kwargs
        )
    
    async def create_job_execution_service(self, **kwargs) -> JobExecutionService:
        """Create JobExecutionService."""
        self.log_info("Creating JobExecutionService")
        
        return JobExecutionService(
            config=self.config,
            logger=self.logger,
            **kwargs
        )
    
    async def health_check(self) -> Dict[str, Any]:
        """Check factory health."""
        return {
            'status': 'healthy',
            'architecture': 'domain',
            'factory_type': 'TransformerServiceFactory',
        }
    
    async def cleanup(self) -> None:
        """Clean up factory resources."""
        self.log_info("TransformerServiceFactory (domain) cleaned up")