# /src/services/transformer/jobs/job_runner_service.py
# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
Job runner service for the transformer service.
"""

import asyncio
import logging
import os
import time
from typing import Dict

import aiofiles

from src.transformer.jobs.job_models import TransformationJob
from src.transformer.exceptions import (
    JobExecutionException, FileOperationException, ProcessingException,
    wrap_file_error, wrap_processing_error
)


class JobRunnerService:
    """
    Runs a single transformation job.
    """

    def __init__(self,
                 file_handler: "FileHandler",
                 docket_processor: "DocketProcessor",
                 file_operations_manager: "FileOperationsManager",
                 data_processing_engine: "DataProcessingEngine",
                 error_handler: "ErrorHandler",
                 shutdown_event: asyncio.Event,
                 logger: logging.Logger,
                 data_upload_service: 'PersistenceService' = None,
                 config: dict = None,
                 ):
        self.file_handler = file_handler
        self.docket_processor = docket_processor
        self.file_operations_manager = file_operations_manager
        self.data_processing_engine = data_processing_engine
        self.error_handler = error_handler
        self.shutdown_event = shutdown_event
        self.logger = logger
        self.data_upload_service = data_upload_service
        self.config = config or {}

    async def run_job_async(self, job: TransformationJob) -> Dict:
        """
        Runs a single transformation job.
        """
        job.logger.info(f"[Job {os.path.basename(job.json_path)}] Starting processing...")
        try:
            should_continue = await self._job_phase_1_load_and_prepare(job)
            if not should_continue:
                job.logger.info(
                    f"[{job.original_filename or os.path.basename(job.json_path)}] Skipped or failed in Phase 1. Status: {job.status}")
                job.processing_time = time.time() - job.start_time
                return job.__dict__

            if self._check_shutdown():
                job.status = "interrupted"
                job.message = "Processing interrupted by shutdown request before Phase 2"
                job.logger.info(
                    f"[{job.original_filename or os.path.basename(job.json_path)}] Interrupted before Phase 2")
                job.processing_time = time.time() - job.start_time
                return job.__dict__

            await self._job_phase_2_enrich(job)

            if self._check_shutdown():
                job.status = "interrupted"
                job.message = "Processing interrupted by shutdown request before Phase 3"
                job.logger.info(
                    f"[{job.original_filename or os.path.basename(job.json_path)}] Interrupted before Phase 3")
                job.processing_time = time.time() - job.start_time
                return job.__dict__

            await self._job_phase_3_finalize_and_persist(job)

            job.status = "success"
            job.message = "Processing completed successfully."
            job.logger.info(f"[{job.original_filename}] -> [{job.final_filename}] Success.")

        except (JobExecutionException, FileOperationException, ProcessingException):
            # Re-raise known exceptions
            raise
        except Exception as e:
            job_error = JobExecutionException(
                f"Job failed: {str(e)}",
                job_name=os.path.basename(job.json_path),
                job_id=getattr(job, 'id', None),
                phase="execution",
                can_retry=True
            )
            job.status = "error"
            job.message = str(job_error)
            job.logger.error(f"[{job.original_filename or os.path.basename(job.json_path)}] {job_error}")
            if job.working_data and self.error_handler:
                try:
                    await self.error_handler.update_error_status_and_save(job.working_data, job.json_path, str(job_error))
                except Exception as eh_e:
                    job.logger.error(
                        f"[{job.original_filename or os.path.basename(job.json_path)}] Error handler failed: {eh_e}")
            raise job_error
        finally:
            job.processing_time = time.time() - job.start_time

        return job.__dict__

    async def _job_phase_1_load_and_prepare(self, job: TransformationJob) -> bool:
        job.logger.debug(f"[{os.path.basename(job.json_path)}] Phase 1: Load & Prepare")
        current_dir = os.path.dirname(job.json_path)

        try:
            job.working_data = await self.file_handler.load_json_async(job.json_path)
        except FileNotFoundError as e:
            file_error = wrap_file_error(e, job.json_path, "read")
            job.status = "error"
            job.message = str(file_error)
            job.logger.error(f"[{os.path.basename(job.json_path)}] {file_error}")
            raise file_error
        except Exception as e:
            file_error = wrap_file_error(e, job.json_path, "load")
            job.status = "error"
            job.message = str(file_error)
            job.logger.error(f"[{os.path.basename(job.json_path)}] {file_error}")
            raise file_error

        if not job.working_data:
            job.status = "error"
            job.message = "Failed to load JSON data (empty)."
            job.logger.error(f"[{os.path.basename(job.json_path)}] Error: {job.message}")
            return False

        job.original_filename = os.path.splitext(os.path.basename(job.json_path))[0]
        job.working_data['original_filename'] = job.original_filename
        job.working_data['json_path'] = job.json_path
        job.working_data['base_filename'] = job.original_filename

        validator = self.docket_processor.validator
        is_complete, _ = validator.is_data_complete(job.working_data)

        if is_complete and not job.force_reprocess:
            job.status = "skipped"
            job.message = "Already processed and force_reprocess is False."
            job.logger.info(f"[{job.original_filename}] Skipped: {job.message}")
            return False

        try:
            job.pdf_file_path = await self.file_operations_manager.ensure_pdf_available(
                job.working_data, job.original_filename, current_dir, job.force_reprocess
            )
            job.logger.debug(f"[{job.original_filename}] PDF available status: {bool(job.pdf_file_path)}")

            job.md_file_path = os.path.join(current_dir, f"{job.original_filename}.md")

            if os.path.exists(job.md_file_path):
                async with aiofiles.open(job.md_file_path, 'r', encoding='utf-8') as f:
                    job.pdf_text = await f.read()
                job.logger.info(f"[{job.original_filename}] MD content loaded for job.pdf_text.")
            else:
                job.pdf_text = None
                job.logger.info(
                    f"[{job.original_filename}] No MD file found at {job.md_file_path}. job.pdf_text is None.")

        except FileNotFoundError as e:
            file_error = wrap_file_error(e, job.md_file_path, "read")
            job.logger.error(f"[{job.original_filename}] {file_error}")
            job.status = "error"
            job.message = str(file_error)
            raise file_error
        except Exception as e:
            processing_error = wrap_processing_error(
                e, "file_preparation", "md_reading", job.original_filename
            )
            job.logger.error(f"[{job.original_filename}] {processing_error}")
            job.status = "error"
            job.message = str(processing_error)
            raise processing_error

        job.logger.info(f"[{job.original_filename}] Phase 1: Load & Prepare completed successfully.")
        return True

    async def _job_phase_2_enrich(self, job: TransformationJob) -> None:
        job.logger.debug(f"[{job.original_filename}] Phase 2: Enrich Data")

        if job.working_data is None:
            job.logger.error(
                f"[{job.original_filename}] working_data is None at the start of Phase 2. Skipping enrichment.")
            raise RuntimeError("Cannot perform enrichment: working_data is None.")

        try:
            self.data_processing_engine.clean_duplicate_fields(job.working_data)
            job.logger.info(f"[{job.original_filename}] Duplicate fields cleaned.")
        except Exception as e:
            processing_error = ProcessingException(
                f"Error during clean_duplicate_fields: {str(e)}",
                operation="clean_duplicate_fields",
                stage="phase_2_enrich",
                item_id=job.original_filename
            )
            job.logger.error(f"[{job.original_filename}] {processing_error}")
            raise processing_error

        has_mdl_info = bool(job.working_data.get('mdl_num')) and job.working_data.get('mdl_num') != 'NA'
        
        # Check if case info fields are already enriched - only skip LLM if they're complete
        has_enriched_case_info = bool(
            job.working_data.get('title') and 
            job.working_data.get('allegations') and
            job.working_data.get('title') != '' and 
            job.working_data.get('allegations') != ''
        )
        
        # Only skip LLM processing if we have both MDL info AND already enriched case details
        skip_case_info_llm = has_mdl_info and has_enriched_case_info

        job.logger.info(
            f"[{job.original_filename}] LLM case info processing decision: "
            f"has_mdl_info={has_mdl_info}, has_enriched_case_info={has_enriched_case_info}, skip_case_info_llm={skip_case_info_llm}"
        )

        job.logger.debug(
            f"[{job.original_filename}] Calling DataProcessingEngine.enrich_data. PDF text available: {job.pdf_text is not None and len(job.pdf_text) > 0}")

        enrichment_success = await self.data_processing_engine.enrich_data(
            job.working_data,
            job.pdf_text,
            job.json_path,
            skip_case_info_llm,
            job.force_reprocess
        )

        if not enrichment_success:
            job.logger.error(f"[{job.original_filename}] Data enrichment failed as reported by DataProcessingEngine.")
            raise RuntimeError("Data enrichment failed.")

        # Log MDL status after enrichment
        job.logger.info(f"[{job.original_filename}] After enrichment - mdl_num: {job.working_data.get('mdl_num')}, html_only: {job.working_data.get('html_only')}")
        
        job.logger.info(f"[{job.original_filename}] Phase 2: Enrich Data completed successfully.")

    async def _job_phase_3_finalize_and_persist(self, job: TransformationJob) -> None:
        job.logger.debug(f"[{job.original_filename}] Phase 3: Finalize & Persist")

        if job.working_data is None:
            job.logger.error(
                f"[{job.original_filename}] working_data is None at the start of Phase 3. Skipping finalize and persist.")
            raise RuntimeError("Cannot finalize and persist: working_data is None.")

        current_dir = os.path.dirname(job.json_path)
        zip_path = os.path.join(current_dir, f"{job.original_filename}.zip")

        final_working_data = await self.file_operations_manager.finalize_and_rename_files(
            job.working_data,
            job.original_filename,
            job.json_path,
            job.pdf_file_path or "",
            job.md_file_path or "",
            zip_path
        )

        job.working_data = final_working_data

        if 'new_filename' not in job.working_data:
            job.logger.error(
                f"[{job.original_filename}] 'new_filename' not found in data after finalize_and_rename_files.")
            raise RuntimeError("'new_filename' missing after file finalization.")

        job.final_filename = job.working_data['new_filename']
        job.logger.info(f"[{job.original_filename}] Renamed to [{job.final_filename}]")

        # Use the proper docket directory instead of the current directory
        target_docket_dir = self.file_handler.get_target_docket_directory()
        if not target_docket_dir:
            job.logger.error(f"[{job.final_filename}] Could not determine target docket directory")
            raise RuntimeError("Target docket directory not available")
        
        # Ensure the dockets directory exists
        os.makedirs(target_docket_dir, exist_ok=True)
        job.logger.info(f"[{job.final_filename}] Using target docket directory: {target_docket_dir}")
        
        final_json_path = os.path.join(target_docket_dir, f"{job.final_filename}.json")

        # Remove pdf_text before saving to prevent large text blobs in JSON
        data_to_save = job.working_data.copy()
        if 'pdf_text' in data_to_save:
            pdf_text_size = len(data_to_save.get('pdf_text', ''))
            data_to_save.pop('pdf_text')
            job.logger.info(f"[{job.final_filename}] Removed pdf_text from data before saving (was {pdf_text_size} chars)")
        
        # Log MDL status before saving
        job.logger.info(f"[{job.final_filename}] Before save_json_async - mdl_num: {data_to_save.get('mdl_num')}, html_only: {data_to_save.get('html_only')}")
        
        await self.file_handler.save_json_async(final_json_path, data_to_save)
        job.logger.info(f"[{job.final_filename}] Final JSON data saved to {final_json_path}")

        # Note: Upload is handled by UploadOrchestrator, not here
        # The ProcessingOrchestrator explicitly sets upload=False when calling the transformer
        # This ensures proper batch upload handling in the UploadOrchestrator phase
        job.logger.debug(f"[{job.final_filename}] Upload will be handled by UploadOrchestrator in separate phase")

        await self.file_operations_manager.perform_cleanup(job.working_data, zip_path)
        job.logger.info(f"[{job.final_filename}] Cleanup performed.")

        job.logger.info(f"[{job.original_filename}] Phase 3: Finalize & Persist completed successfully.")

    def _check_shutdown(self) -> bool:
        """
        Check if a shutdown has been requested.
        """
        if self.shutdown_event and self.shutdown_event.is_set():
            self.logger.info("Shutdown requested, stopping processing")
            return True
        return False
