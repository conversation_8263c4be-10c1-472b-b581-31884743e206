# /src/services/transformer/jobs/job_models.py
# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
Job models for the transformer service.
"""
import time
from dataclasses import dataclass, field
from typing import Dict, Optional, Any


@dataclass
class TransformationJob:
    """
    Represents a job for transforming a single JSON file.
    """
    # Inputs
    json_path: str
    force_reprocess: bool

    # Dependencies (passed from parent)
    transformer: Any  # Reference to the parent DataTransformer instance

    # State
    status: str = "pending"  # pending, success, skipped, error
    message: str = ""
    start_time: float = field(default_factory=time.time)
    processing_time: float = 0.0

    # Data payload
    working_data: Optional[Dict] = None
    pdf_text: Optional[str] = None
    original_filename: Optional[str] = None
    final_filename: Optional[str] = None
    pdf_file_path: Optional[str] = None  # New
    md_file_path: Optional[str] = None  # New

    @property
    def logger(self):
        """
        Get the logger from the parent transformer instance.
        """
        if not hasattr(self.transformer, 'logger'):
            raise AttributeError("Transformer instance in TransformationJob is missing a 'logger' attribute.")
        return self.transformer.logger
