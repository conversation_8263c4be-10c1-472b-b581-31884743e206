# /src/services/transformer/afff_calculator.py
"""
# Removed dependency_injector imports - using container-based injection
AFFF-specific calculations and metrics for MDL 2873.

This module handles AFFF (Aqueous Film-Forming Foam) case-specific calculations
extracted from mdl_processor.py as part of Phase 3.3 refactoring.
"""
import ast
import logging
from typing import Dict, List, Optional, Any

from src.transformer.config.constants import NULL_CONDITIONS

# Removed dependency_injector imports - using container-based injection
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError


class AfffCalculator(AsyncServiceBase):
    """Handles AFFF-specific calculations and metrics for MDL 2873."""


    AFFF_MDL_NUMBER = '2873'

    def __init__(self,
                 config: Optional[Dict] ,
                 logger: Optional[logging.Logger] ):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

    async def _execute_action(self, data: Any) -> Any:
        """Execute AfffCalculator actions."""
        if isinstance(data, dict):
            action = data.get('action')
            match action:
                case 'calculate_afff_num_plaintiffs':
                    return await self.calculate_afff_num_plaintiffs(data['data'])
                case 'calculate_afff_num_plaintiffs_sync':
                    return self.calculate_afff_num_plaintiffs_sync(data['data'])
                case 'validate_afff_data':
                    return await self.validate_afff_data(data['data'])
                case 'get_afff_summary':
                    return self.get_afff_summary(data['data'])
                case 'extract_plaintiffs_list':
                    return await self._extract_plaintiffs_list(data['plaintiffs_data'])
                case 'parse_plaintiffs_string':
                    return await self._parse_plaintiffs_string(data['plaintiffs_str'])
                case 'filter_valid_plaintiffs':
                    return await self._filter_valid_plaintiffs(data['plaintiffs'])
                case 'is_valid_plaintiff':
                    return await self._is_valid_plaintiff(data['plaintiff'])
                case _:
                    raise TransformerServiceError(f"Invalid action '{action}' provided to AfffCalculator")
        raise TransformerServiceError("Invalid action data provided to AfffCalculator")

    async def calculate_afff_num_plaintiffs(self, data: Dict) -> str:
        """
        Calculate number of plaintiffs for AFFF cases (MDL 2873).
        
        Args:
            data: Docket data dictionary (modified in place)
            
        Returns:
            Number of plaintiffs as string, or "NA" if not applicable
        """
        if data is None:
            self.log_error("Input data is None for calculate_afff_num_plaintiffs.")
            return "NA"

        num_plaintiffs = "NA"

        try:
            # Ensure mdl_num is treated as string for comparison and strip whitespace
            mdl_num_str = str(data.get('mdl_num', '')).strip()

            if mdl_num_str == self.AFFF_MDL_NUMBER:
                self.log_debug(f"Processing AFFF case (MDL {self.AFFF_MDL_NUMBER}) for plaintiff calculation")

                # Get plaintiffs data with priority: 'plaintiff' > 'Plaintiff'
                plaintiffs_data = data.get('plaintiff', data.get('Plaintiff', []))

                # Process plaintiffs data based on type
                plaintiffs = await self._extract_plaintiffs_list(plaintiffs_data)

                # Count valid plaintiffs
                valid_plaintiffs = await self._filter_valid_plaintiffs(plaintiffs)
                count = len(valid_plaintiffs)

                if count > 0:
                    num_plaintiffs = str(count)
                    self.log_debug(f"Calculated num_plaintiffs = {num_plaintiffs} for MDL {self.AFFF_MDL_NUMBER}")
                    # Add the calculated count to the data
                    data['num_plaintiffs'] = num_plaintiffs
                else:
                    self.log_debug(f"No valid plaintiffs found for MDL {self.AFFF_MDL_NUMBER}.")
                    # Don't set the field if there's no valid data
            else:
                self.log_debug(f"Skipping plaintiff calculation for non-AFFF case (MDL {mdl_num_str})")

        except Exception as e:
            self.log_error(f"Error calculating num_plaintiffs for MDL {self.AFFF_MDL_NUMBER}: {e}")
            if mdl_num_str == self.AFFF_MDL_NUMBER:
                data['num_plaintiffs'] = "NA"

        return num_plaintiffs

    def calculate_afff_num_plaintiffs_sync(self, data: Dict) -> str:
        """
        Synchronous version of AFFF plaintiff calculation.
        
        This method is deprecated and will be removed in Phase 4.
        Use calculate_afff_num_plaintiffs() instead.
        
        Args:
            data: Docket data dictionary (modified in place)
            
        Returns:
            Number of plaintiffs as string, or "NA" if not applicable
        """
        self.log_warning("Using deprecated sync AFFF calculation method. Please migrate to async version.")

        if data is None:
            self.log_error("Input data is None for calculate_afff_num_plaintiffs.")
            return "NA"

        num_plaintiffs = "NA"

        try:
            # Ensure mdl_num is treated as string for comparison and strip whitespace
            mdl_num_str = str(data.get('mdl_num', '')).strip()

            if mdl_num_str == self.AFFF_MDL_NUMBER:
                # Prioritize 'plaintiff' key, fallback to 'Plaintiff'
                plaintiffs_data = data.get('plaintiff', data.get('Plaintiff', []))

                plaintiffs = []
                if isinstance(plaintiffs_data, str):
                    # Safely evaluate string representation of list
                    try:
                        evaluated_list = ast.literal_eval(plaintiffs_data)
                        if isinstance(evaluated_list, list):
                            plaintiffs = evaluated_list
                        else:
                            self.log_warning(f"Evaluated plaintiff string is not a list: {type(evaluated_list)}")
                    except (ValueError, SyntaxError, TypeError) as e:
                        self.log_warning(
                            f"Could not evaluate plaintiff string '{plaintiffs_data[:50]}...': {e}. Treating as single plaintiff if non-empty.")
                        # If it's a non-empty string that doesn't parse as a list, treat as one plaintiff name
                        if plaintiffs_data.strip() and plaintiffs_data not in NULL_CONDITIONS:
                            plaintiffs = [plaintiffs_data]
                    except Exception as e:
                        self.log_error(f"Unexpected error evaluating plaintiff string: {e}")
                        plaintiffs = []
                elif isinstance(plaintiffs_data, list):
                    plaintiffs = plaintiffs_data
                else:
                    self.log_warning(f"Plaintiff data is neither string nor list: {type(plaintiffs_data)}")

                # Count non-null entries
                valid_plaintiffs = [p for p in plaintiffs if p not in NULL_CONDITIONS]
                count = len(valid_plaintiffs)

                if count > 0:
                    num_plaintiffs = str(count)
                    self.log_debug(f"Calculated num_plaintiffs = {num_plaintiffs} for MDL {self.AFFF_MDL_NUMBER}")
                    # Only add the key if this is MDL 2873
                    data['num_plaintiffs'] = num_plaintiffs
                else:
                    self.log_debug(f"No valid plaintiffs found for MDL {self.AFFF_MDL_NUMBER}.")
                    data['num_plaintiffs'] = "NA"

        except Exception as e:
            self.log_error(f"Error calculating num_plaintiffs for MDL {self.AFFF_MDL_NUMBER}: {e}")
            if mdl_num_str == self.AFFF_MDL_NUMBER:
                data['num_plaintiffs'] = "NA"

        return num_plaintiffs

    async def _extract_plaintiffs_list(self, plaintiffs_data: Any) -> List[str]:
        """
        Extract plaintiffs list from various data formats.
        
        Args:
            plaintiffs_data: Raw plaintiffs data (string, list, or other)
            
        Returns:
            List of plaintiff names
        """
        plaintiffs = []

        if isinstance(plaintiffs_data, str):
            # Handle string representation of list
            plaintiffs = await self._parse_plaintiffs_string(plaintiffs_data)
        elif isinstance(plaintiffs_data, list):
            plaintiffs = plaintiffs_data
        elif plaintiffs_data is not None:
            self.log_warning(f"Unexpected plaintiff data type: {type(plaintiffs_data)}")
            # Try to convert to string and then parse
            try:
                plaintiffs_str = str(plaintiffs_data)
                plaintiffs = await self._parse_plaintiffs_string(plaintiffs_str)
            except Exception as e:
                self.log_error(f"Failed to convert plaintiff data to string: {e}")
                plaintiffs = []

        return plaintiffs

    async def _parse_plaintiffs_string(self, plaintiffs_str: str) -> List[str]:
        """
        Parse plaintiffs from string representation.
        
        Args:
            plaintiffs_str: String representation of plaintiffs
            
        Returns:
            List of plaintiff names
        """
        if not plaintiffs_str or plaintiffs_str in NULL_CONDITIONS:
            return []

        try:
            # Try to evaluate as literal Python list
            evaluated_list = ast.literal_eval(plaintiffs_str)
            if isinstance(evaluated_list, list):
                return evaluated_list
            else:
                self.log_warning(f"Evaluated plaintiff string is not a list: {type(evaluated_list)}")
                return []
        except (ValueError, SyntaxError, TypeError) as e:
            self.log_warning(
                f"Could not evaluate plaintiff string '{plaintiffs_str[:50]}...': {e}. Treating as single plaintiff if non-empty.")

            # If it's a non-empty string that doesn't parse as a list, treat as one plaintiff name
            if plaintiffs_str.strip() and plaintiffs_str not in NULL_CONDITIONS:
                return [plaintiffs_str]
            return []
        except Exception as e:
            self.log_error(f"Unexpected error evaluating plaintiff string: {e}")
            return []

    async def _filter_valid_plaintiffs(self, plaintiffs: List[str]) -> List[str]:
        """
        Filter out invalid plaintiff entries.
        
        Args:
            plaintiffs: List of plaintiff names
            
        Returns:
            List of valid plaintiff names
        """
        if not plaintiffs:
            return []

        valid_plaintiffs = []

        for plaintiff in plaintiffs:
            if await self._is_valid_plaintiff(plaintiff):
                valid_plaintiffs.append(plaintiff)

        return valid_plaintiffs

    async def _is_valid_plaintiff(self, plaintiff: Any) -> bool:
        """
        Check if a plaintiff entry is valid.
        
        Args:
            plaintiff: Plaintiff entry to validate
            
        Returns:
            True if valid, False otherwise
        """
        if plaintiff in NULL_CONDITIONS:
            return False

        # Check for empty or whitespace-only strings
        if isinstance(plaintiff, str) and not plaintiff.strip():
            return False

        # Additional validation rules can be added here
        # For example, minimum length, format checks, etc.

        return True

    async def validate_afff_data(self, data: Dict) -> Dict[str, Any]:
        """
        Validate AFFF-specific data and return validation report.
        
        Args:
            data: Data dictionary to validate
            
        Returns:
            Validation report with status and details
        """
        report = {
            'status': 'success',
            'warnings': [],
            'errors': [],
            'is_afff_case': False,
            'plaintiff_count': 0,
            'validation_details': {}
        }

        # Check if this is an AFFF case
        mdl_num_str = str(data.get('mdl_num', ''))
        report['is_afff_case'] = mdl_num_str == self.AFFF_MDL_NUMBER

        if not report['is_afff_case']:
            report['validation_details']['message'] = f"Not an AFFF case (MDL {mdl_num_str})"
            return report

        # Validate AFFF-specific fields
        plaintiffs_data = data.get('plaintiff', data.get('Plaintiff', []))

        if not plaintiffs_data or plaintiffs_data in NULL_CONDITIONS:
            report['warnings'].append("No plaintiff data found for AFFF case")
            return report

        # Extract and validate plaintiffs
        try:
            plaintiffs = await self._extract_plaintiffs_list(plaintiffs_data)
            valid_plaintiffs = await self._filter_valid_plaintiffs(plaintiffs)

            report['plaintiff_count'] = len(valid_plaintiffs)
            report['validation_details']['total_plaintiffs_found'] = len(plaintiffs)
            report['validation_details']['valid_plaintiffs'] = len(valid_plaintiffs)

            if len(valid_plaintiffs) == 0:
                report['warnings'].append("No valid plaintiffs found in AFFF case")
            elif len(valid_plaintiffs) != len(plaintiffs):
                report['warnings'].append(
                    f"Some plaintiff entries were invalid ({len(plaintiffs) - len(valid_plaintiffs)} filtered out)")

            # Check num_plaintiffs field consistency
            num_plaintiffs_field = data.get('num_plaintiffs')
            if num_plaintiffs_field:
                try:
                    field_count = int(num_plaintiffs_field)
                    if field_count != len(valid_plaintiffs):
                        report['warnings'].append(
                            f"num_plaintiffs field ({field_count}) doesn't match calculated count ({len(valid_plaintiffs)})")
                except (ValueError, TypeError):
                    report['warnings'].append(f"Invalid num_plaintiffs field value: {num_plaintiffs_field}")

        except Exception as e:
            report['errors'].append(f"Error validating plaintiff data: {str(e)}")
            report['status'] = 'error'

        # Set overall status
        if report['errors']:
            report['status'] = 'error'
        elif report['warnings']:
            report['status'] = 'warning'

        return report

    def get_afff_summary(self, data: Dict) -> str:
        """
        Get summary of AFFF case processing.
        
        Args:
            data: Processed data dictionary
            
        Returns:
            Human-readable summary
        """
        mdl_num_str = str(data.get('mdl_num', ''))

        if mdl_num_str != self.AFFF_MDL_NUMBER:
            return f"Not an AFFF case (MDL {mdl_num_str})"

        num_plaintiffs = data.get('num_plaintiffs', 'NA')
        docket_num = data.get('docket_num', 'unknown')

        return f"AFFF case {docket_num}: {num_plaintiffs} plaintiffs"
