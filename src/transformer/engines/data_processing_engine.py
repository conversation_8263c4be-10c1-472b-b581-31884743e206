# /src/services/transformer/data_processing_engine.py
"""
# Removed dependency_injector imports - using container-based injection
Data processing engine for DataTransformer.

This module handles core data processing and enrichment logic extracted from transformer.py
as part of Phase 3 refactoring.
"""
import asyncio
import os
from datetime import datetime
from typing import Dict, Optional, Tuple, Any

# Removed dependency_injector imports - using container-based injection
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.transformer.components.docket.docket_processor import DocketProcessor
from src.transformer.components.case_classification.classifier import LitigationClassifier
from src.transformer.components.mdl.mdl_processor import MDLProcessor


class DataProcessingEngine(AsyncServiceBase):
    """Core data processing and enrichment engine."""

    def __init__(self,
                 docket_processing_service,
                 mdl_processing_service,
                 file_processing_service,
                 law_firm_processing_service,
                 case_classification_service,
                 data_cleaning_service,
                 data_upload_service,
                 llm_client=None,
                 logger=None,
                 config=None,
                 html_integration_service=None,
                 deepseek_service=None):
        # Initialize AsyncServiceBase
        super().__init__(logger, config)

        self.docket_processing_service = docket_processing_service
        self.mdl_processing_service = mdl_processing_service
        self.file_processing_service = file_processing_service
        self.law_firm_processing_service = law_firm_processing_service
        self.case_classification_service = case_classification_service
        self.data_cleaning_service = data_cleaning_service
        self.data_upload_service = data_upload_service
        self.llm_client = llm_client
        self.html_integration_service = html_integration_service
        self.deepseek_service = deepseek_service

    async def _execute_action(self, data: Any) -> Any:
        """Execute DataProcessingEngine actions."""
        if isinstance(data, dict):
            action = data.get('action')

            if action == 'load_and_validate_data':
                return await self.load_and_validate_initial_data(
                    data['json_path'],
                    data.get('force_reprocess', False)
                )
            elif action == 'enrich_data':
                return await self.enrich_data(
                    data['working_data'],
                    data.get('pdf_text_content'),
                    data['current_json_path'],
                    data.get('skip_case_info_llm', False),
                    data.get('force_reprocess', True)
                )
            elif action == 'clean_duplicate_fields':
                self.clean_duplicate_fields(data['data'])
                return data['data']
            elif action == 'extract_mdl_from_flags':
                return self.extract_mdl_from_flags(data['data'])
            elif action == 'validate_data_structure':
                return self._validate_data_structure(data['data'], data['base_filename'])
            elif action == 'is_already_processed':
                return self._is_already_processed(data['data'], data['base_filename'])
            elif action == 'should_skip_file':
                return self._should_skip_file(data['data'], data['base_filename'])
            elif action == 'load_json_data':
                return await self._load_json_data(data['json_path'])
        raise TransformerServiceError("Invalid action data provided to DataProcessingEngine")

    async def process_job(self, job: Dict) -> Dict:
        """
        Processes a single job by delegating to the appropriate facade services.
        """
        self.log_info(f"Processing job: {job.get('json_path')}")
        data = job.get('working_data', {})
        json_path = job.get('json_path')

        # 1. Data Cleaning
        await self.data_cleaning_service.clean_docket_data(data)

        # 2. Law Firm Processing
        await self.law_firm_processing_service.process_law_firms(data)

        # 3. Case Classification and Transfer Handling
        await self.case_classification_service.classify_case(data)

        # 4. MDL Processing
        await self.mdl_processing_service.add_mdl_info_to_docket(data)

        # 5. File Processing (saving the updated data)
        # Remove pdf_text before saving to prevent large text blobs in JSON
        data_to_save = data.copy()
        if 'pdf_text' in data_to_save:
            data_to_save.pop('pdf_text')
            self.log_debug(f"Removed pdf_text from data before saving (was {len(data.get('pdf_text', ''))} chars)")
        
        # Log MDL status before saving
        self.log_info(f"Before save_json_async - mdl_num: {data_to_save.get('mdl_num')}, docket: {data_to_save.get('docket_num')}")
        
        await self.file_processing_service.save_json_async(json_path, data_to_save)

        # 6. Data Upload
        # Pass the JSON path to the upload service
        # Determine upload types based on config
        upload_types = []
        if self.config.get('upload_json_to_dynamodb', True):
            upload_types.append('dynamodb')
        if self.config.get('upload_pdfs_to_s3', False) or self.config.get('upload', False):
            upload_types.append('s3')
        
        upload_data = {
            'json_paths': [json_path],  # Include the JSON file path for upload
            'upload_types': upload_types,
            'force_s3_upload': self.config.get('force_s3_upload', False)
        }
        await self.data_upload_service.upload_docket(upload_data)

        job['working_data'] = data
        job['status'] = 'success'
        return job

    async def enrich_data(self, 
                         working_data: Dict, 
                         pdf_text_content: str = None, 
                         current_json_path: str = None, 
                         skip_case_info_llm: bool = False, 
                         force_reprocess: bool = True) -> bool:
        """
        Enrich docket data by processing it through various services.
        
        Args:
            working_data: The docket data to enrich
            pdf_text_content: PDF text content if available
            current_json_path: Path to the JSON file
            skip_case_info_llm: Whether to skip LLM processing for case info
            force_reprocess: Whether to force reprocessing
            
        Returns:
            bool: True if enrichment was successful, False otherwise
        """
        try:
            self.log_info(f"Enriching data for: {current_json_path}")
            
            # Check if this is an html_only case for specialized enrichment
            is_html_only = working_data.get('html_only', False)
            self.log_info(f"Checking html_only status: {is_html_only} for {current_json_path}")
            
            if is_html_only:
                self.log_info(f"🔧 Detected html_only=True case - using specialized HTML-only enrichment for: {current_json_path}")
                return await self._enrich_html_only_data(working_data, current_json_path)
            
            # Regular enrichment flow for non-html_only cases
            # Debug: Check which services are None
            services_status = {
                'data_cleaning_service': self.data_cleaning_service is not None,
                'law_firm_processing_service': self.law_firm_processing_service is not None,
                'case_classification_service': self.case_classification_service is not None,
                'mdl_processing_service': self.mdl_processing_service is not None,
            }
            self.log_info(f"Services status: {services_status}")
            
            # 1. Data Cleaning
            if self.data_cleaning_service is None:
                self.log_error("data_cleaning_service is None!")
                return False
            
            # Add pdf_text_content to working_data for LLM extraction
            if pdf_text_content:
                working_data['pdf_text'] = pdf_text_content
                self.log_info(f"Added pdf_text to working_data, length: {len(pdf_text_content)}")
            
            await self.data_cleaning_service.clean_docket_data(working_data)

            # 2. Law Firm Processing
            await self.law_firm_processing_service.process_law_firms(working_data)

            # 3. Case Classification and Transfer Handling
            await self.case_classification_service.classify_case(working_data)
            
            # 3.5. Force s3_link update based on MD file existence
            # This is critical - after classification, we need to ensure s3_link is correct
            if self.case_classification_service and hasattr(self.case_classification_service, '_transfer_handler'):
                transfer_handler = self.case_classification_service._transfer_handler
                if hasattr(transfer_handler, 'utils') and transfer_handler.utils:
                    # Force check and convert s3_link - run even if s3_link is missing
                    current_s3_link = working_data.get('s3_link')
                    self.log_info(f"Checking s3_link (current: {current_s3_link})")
                    
                    # Always run the check to ensure proper s3_link generation
                    transfer_handler.utils._check_and_convert_s3_link(working_data)
                    
                    new_s3_link = working_data.get('s3_link')
                    if new_s3_link != current_s3_link:
                        self.log_info(f"Updated s3_link from '{current_s3_link}' to '{new_s3_link}'")

            # 4. MDL Processing
            # First, try to extract MDL number from flags if not already present
            if not working_data.get('mdl_num'):
                extracted_mdl = self.extract_mdl_from_flags(working_data)
                if extracted_mdl:
                    working_data['mdl_num'] = extracted_mdl
                    self.log_info(f"Set mdl_num from flags: {extracted_mdl}")
            
            # Get mdl_litigations from the mdl_processor if available
            mdl_litigations = None
            
            # Debug logging to understand the structure
            self.log_debug(f"Checking for MDL data - mdl_processing_service exists: {self.mdl_processing_service is not None}")
            
            if hasattr(self.mdl_processing_service, '_processor') and self.mdl_processing_service._processor:
                self.log_debug(f"mdl_processing_service._processor exists: True")
                if hasattr(self.mdl_processing_service._processor, 'utils') and self.mdl_processing_service._processor.utils:
                    self.log_debug(f"mdl_processing_service._processor.utils exists: True")
                    mdl_litigations = getattr(self.mdl_processing_service._processor.utils, 'mdl_litigations', None)
                    self.log_debug(f"mdl_litigations from utils: {type(mdl_litigations) if mdl_litigations is not None else 'None'}")
                    if mdl_litigations is not None:
                        self.log_debug(f"MDL data found with {len(mdl_litigations)} entries")
                else:
                    self.log_debug(f"mdl_processing_service._processor.utils does not exist or is None")
            else:
                self.log_debug(f"mdl_processing_service._processor does not exist or is None")
            
            # Try to add MDL info - the service will handle getting the data internally
            mdl_added = await self.mdl_processing_service.add_mdl_info_to_docket(working_data)
            if not mdl_added:
                self.log_warning("MDL litigations data not available for enrichment")

            # 5. LLM Case Info Processing (versus formatting, etc.)
            if not skip_case_info_llm and self.deepseek_service:
                self.log_info("🔧 Running LLM case info processing for versus formatting")
                await self.deepseek_service.format_html_only_fields(working_data)

            self.log_info(f"Data enrichment completed successfully for: {current_json_path}")
            return True
            
        except Exception as e:
            self.log_error(f"Data enrichment failed for {current_json_path}: {str(e)}")
            return False

    async def _enrich_html_only_data(self, working_data: Dict, current_json_path: str = None) -> bool:
        """
        Specialized enrichment for HTML-only processed cases.
        Uses DeepSeek service for versus extraction and field formatting.
        """
        try:
            if not self.deepseek_service:
                self.log_error("DeepSeek service not available for HTML-only enrichment")
                return False
            
            self.log_info(f"🚀 Starting HTML-only data enrichment for: {current_json_path}")
            
            # Use DeepSeek service's comprehensive HTML-only enrichment
            await self.deepseek_service.enrich_html_only_data(working_data)
            
            # Basic data cleaning (safe for HTML-only cases)
            if self.data_cleaning_service:
                # Only run basic cleaning operations that don't require PDF content
                self.clean_duplicate_fields(working_data)
            
            # Law Firm Processing - CRITICAL for extracting law firms from attorneys_gpt
            if self.law_firm_processing_service:
                self.log_info("🏢 Running law firm processing for HTML-only case")
                await self.law_firm_processing_service.process_law_firms(working_data)
                self.log_info(f"Law firms extracted: {working_data.get('law_firms', [])}")
            else:
                self.log_warning("⚠️ Law firm processing service not available for HTML-only case!")
            
            # Case Classification - important for MDL identification
            if self.case_classification_service:
                self.log_info("Running case classification for HTML-only case")
                self.log_info(f"Before case classification - mdl_num: {working_data.get('mdl_num')}")
                await self.case_classification_service.classify_case(working_data)
                self.log_info(f"After case classification - mdl_num: {working_data.get('mdl_num')}")
            
            # Basic MDL processing (safe for HTML-only cases)
            if self.mdl_processing_service:
                self.log_info(f"MDL processing for HTML-only case - Current mdl_num: {working_data.get('mdl_num')}")
                
                # First, try to extract MDL number from flags if not already present
                if not working_data.get('mdl_num'):
                    extracted_mdl = self.extract_mdl_from_flags(working_data)
                    if extracted_mdl:
                        working_data['mdl_num'] = extracted_mdl
                        self.log_info(f"Set mdl_num from flags for HTML-only case: {extracted_mdl}")
                
                # Try to add MDL info - the service will handle getting the data internally
                mdl_added = await self.mdl_processing_service.add_mdl_info_to_docket(working_data)
                if mdl_added:
                    self.log_info(f"After MDL processing - mdl_num: {working_data.get('mdl_num')}")
                else:
                    self.log_warning("MDL litigations data not available for HTML-only enrichment")
            
            self.log_info(f"✅ HTML-only data enrichment completed successfully for: {current_json_path}")
            return True
            
        except Exception as e:
            self.log_error(f"HTML-only data enrichment failed for {current_json_path}: {str(e)}")
            return False

    def clean_duplicate_fields(self, data: Dict):
        """
        Clean duplicate fields from data dictionary.
        
        Args:
            data: Data dictionary to clean
        """
        # Map of standardized field names to their possible duplicates
        field_mappings = {
            'court_id': ['CourtId', 'courtId'],
            'docket_num': ['DocketNum', 'docketNum'],
            'date_filed': ['DateFiled', 'dateFiled'],
            'case_title': ['CaseTitle', 'caseTitle'],
            'law_firm': ['LawFirm', 'lawFirm'],
            'mdl_num': ['MdlNum', 'mdlNum'],
            's3_link': ['S3Link', 's3Link']
        }

        cleaned_count = 0
        for standard_field, duplicate_fields in field_mappings.items():
            # Keep the standard field if it exists
            if standard_field in data:
                for dup_field in duplicate_fields:
                    if dup_field in data:
                        del data[dup_field]
                        cleaned_count += 1
                        self.log_debug(f"Removed duplicate field '{dup_field}' (kept '{standard_field}')")
            else:
                # If standard field doesn't exist, promote the first duplicate found
                for dup_field in duplicate_fields:
                    if dup_field in data:
                        data[standard_field] = data[dup_field]
                        del data[dup_field]
                        cleaned_count += 1
                        self.log_debug(f"Promoted '{dup_field}' to '{standard_field}'")
                        break

        if cleaned_count > 0:
            self.log_debug(f"Cleaned {cleaned_count} duplicate fields from data")

    def extract_mdl_from_flags(self, data: Dict) -> Optional[str]:
        """
        Extract MDL number from case flags and lead_case field.
        
        Args:
            data: Data dictionary containing case flags and lead_case
            
        Returns:
            MDL number if found, None otherwise
        """
        import re
        
        # Check both 'flags' and 'case_flags' for compatibility
        flags_to_check = data.get('flags', []) or data.get('case_flags', [])
        if isinstance(flags_to_check, list):
            for flag in flags_to_check:
                if isinstance(flag, str) and 'MDL' in flag.upper():
                    # Try to extract number from flag
                    mdl_match = re.search(r'MDL[:\s]*(\d+)', flag, re.IGNORECASE)
                    if mdl_match:
                        mdl_num = mdl_match.group(1)
                        self.log_debug(f"Extracted MDL number {mdl_num} from flag: {flag}")
                        return mdl_num
        
        # Check lead_case field for MDL format: "2:18-md-02846-EAS-KAJ"
        lead_case = data.get('lead_case', '')
        if isinstance(lead_case, str) and lead_case:
            # Pattern for lead_case format: X:XX-md-XXXXX-XXX-XXX
            lead_case_match = re.search(r'\d+:\d+-md-(\d+)', lead_case, re.IGNORECASE)
            if lead_case_match:
                mdl_num = lead_case_match.group(1).lstrip('0')  # Remove leading zeros
                self.log_debug(f"Extracted MDL number {mdl_num} from lead_case: {lead_case}")
                return mdl_num

        return None
