"""
Concrete resource pool implementations for transformer services.

Provides connection pooling for DynamoDB, S3, and LLM clients.
"""
import asyncio
import logging
import time
from typing import Any, Dict, Optional

import aioboto3
from botocore.exceptions import ClientError

from src.transformer.pools.resource_utils import ResourcePool, PoolConfig
from src.transformer.exceptions import ResourceException


class DynamoDBConnectionPool(ResourcePool[Any]):
    """
    Connection pool for DynamoDB clients.
    
    Manages aioboto3 DynamoDB client connections with health checking
    and automatic reconnection.
    """
    
    def __init__(self, 
                 table_name: str,
                 region: str = 'us-west-2',
                 config: Optional[PoolConfig] = None,
                 logger: Optional[logging.Logger] = None):
        super().__init__(config or PoolConfig(), logger)
        self.table_name = table_name
        self.region = region
        self._session = aioboto3.Session()
    
    async def _create_resource(self) -> Any:
        """Create a new DynamoDB client."""
        try:
            # Create async DynamoDB client
            async with self._session.client('dynamodb', region_name=self.region) as client:
                # Verify table exists
                await client.describe_table(TableName=self.table_name)
            
            # Return a new client context manager
            return self._session.client('dynamodb', region_name=self.region)
            
        except ClientError as e:
            raise ResourceException(
                f"Failed to create DynamoDB client: {str(e)}",
                resource_type="dynamodb",
                resource_path=self.table_name,
                operation="create_client"
            )
    
    async def _destroy_resource(self, resource: Any) -> None:
        """Close DynamoDB client."""
        try:
            # aioboto3 clients are context managers
            if hasattr(resource, '__aexit__'):
                await resource.__aexit__(None, None, None)
        except Exception as e:
            self.logger.error(f"Error closing DynamoDB client: {e}")
    
    async def _is_healthy(self, resource: Any) -> bool:
        """Check if DynamoDB client is healthy."""
        try:
            async with resource as client:
                # Simple health check - describe table
                await asyncio.wait_for(
                    client.describe_table(TableName=self.table_name),
                    timeout=5.0
                )
            return True
        except Exception as e:
            self.logger.debug(f"DynamoDB client health check failed: {e}")
            return False


class S3ConnectionPool(ResourcePool[Any]):
    """
    Connection pool for S3 clients.
    
    Manages aioboto3 S3 client connections with request rate limiting.
    """
    
    def __init__(self,
                 bucket_name: str,
                 region: str = 'us-west-2', 
                 config: Optional[PoolConfig] = None,
                 logger: Optional[logging.Logger] = None):
        super().__init__(config or PoolConfig(), logger)
        self.bucket_name = bucket_name
        self.region = region
        self._session = aioboto3.Session()
        
        # Rate limiting
        self._request_semaphore = asyncio.Semaphore(100)  # Max concurrent requests
        self._request_times = []
        self._rate_limit = 1000  # Requests per second
    
    async def _create_resource(self) -> Any:
        """Create a new S3 client."""
        try:
            # Create async S3 client
            async with self._session.client('s3', region_name=self.region) as client:
                # Verify bucket exists
                await client.head_bucket(Bucket=self.bucket_name)
            
            # Return a new client context manager
            return self._session.client('s3', region_name=self.region)
            
        except ClientError as e:
            raise ResourceException(
                f"Failed to create S3 client: {str(e)}",
                resource_type="s3",
                resource_path=self.bucket_name,
                operation="create_client"
            )
    
    async def _destroy_resource(self, resource: Any) -> None:
        """Close S3 client."""
        try:
            if hasattr(resource, '__aexit__'):
                await resource.__aexit__(None, None, None)
        except Exception as e:
            self.logger.error(f"Error closing S3 client: {e}")
    
    async def _is_healthy(self, resource: Any) -> bool:
        """Check if S3 client is healthy."""
        try:
            async with resource as client:
                # Simple health check - head bucket
                await asyncio.wait_for(
                    client.head_bucket(Bucket=self.bucket_name),
                    timeout=5.0
                )
            return True
        except Exception as e:
            self.logger.debug(f"S3 client health check failed: {e}")
            return False
    
    async def rate_limited_request(self, func, *args, **kwargs):
        """Execute S3 request with rate limiting."""
        async with self._request_semaphore:
            # Check rate limit
            current_time = time.time()
            self._request_times = [t for t in self._request_times if current_time - t < 1.0]
            
            if len(self._request_times) >= self._rate_limit:
                # Rate limit exceeded, wait
                sleep_time = 1.0 - (current_time - self._request_times[0])
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
            
            # Execute request
            self._request_times.append(time.time())
            return await func(*args, **kwargs)


class LLMClientPool(ResourcePool[Any]):
    """
    Connection pool for LLM API clients.
    
    Manages LLM client connections with token rate limiting and
    request queuing.
    """
    
    def __init__(self,
                 provider: str,
                 api_key: str,
                 model: str,
                 config: Optional[PoolConfig] = None,
                 logger: Optional[logging.Logger] = None):
        # LLM pools typically need smaller sizes
        llm_config = config or PoolConfig(min_size=1, max_size=5)
        super().__init__(llm_config, logger)
        
        self.provider = provider
        self.api_key = api_key
        self.model = model
        
        # Rate limiting
        self._token_bucket = TokenBucket(
            tokens_per_minute=90000,  # GPT-4 default
            burst_size=10000
        )
        self._request_queue = asyncio.Queue()
        self._processing_task: Optional[asyncio.Task] = None
    
    async def _create_resource(self) -> Any:
        """Create a new LLM client."""
        try:
            if self.provider == "openai":
                from openai import AsyncOpenAI
                return AsyncOpenAI(api_key=self.api_key)
            elif self.provider == "anthropic":
                from anthropic import AsyncAnthropic
                return AsyncAnthropic(api_key=self.api_key)
            else:
                raise ValueError(f"Unknown LLM provider: {self.provider}")
                
        except Exception as e:
            raise ResourceException(
                f"Failed to create LLM client: {str(e)}",
                resource_type="llm",
                resource_path=self.provider,
                operation="create_client"
            )
    
    async def _destroy_resource(self, resource: Any) -> None:
        """Close LLM client."""
        try:
            if hasattr(resource, 'close'):
                await resource.close()
        except Exception as e:
            self.logger.error(f"Error closing LLM client: {e}")
    
    async def _is_healthy(self, resource: Any) -> bool:
        """Check if LLM client is healthy."""
        # LLM clients don't typically have health checks
        # Could implement a simple API call if needed
        return True
    
    async def initialize(self) -> None:
        """Initialize pool and start request processor."""
        await super().initialize()
        self._processing_task = asyncio.create_task(self._process_requests())
    
    async def close(self) -> None:
        """Close pool and stop request processor."""
        if self._processing_task:
            self._processing_task.cancel()
            try:
                await self._processing_task
            except asyncio.CancelledError:
                pass
        
        await super().close()
    
    async def submit_request(self, request_func, estimated_tokens: int = 1000) -> Any:
        """
        Submit LLM request with rate limiting.
        
        Args:
            request_func: Async function to execute
            estimated_tokens: Estimated token usage
            
        Returns:
            Result from request_func
        """
        future = asyncio.Future()
        await self._request_queue.put((request_func, estimated_tokens, future))
        return await future
    
    async def _process_requests(self) -> None:
        """Process queued requests with rate limiting."""
        while True:
            try:
                request_func, estimated_tokens, future = await self._request_queue.get()
                
                # Wait for tokens
                await self._token_bucket.acquire(estimated_tokens)
                
                # Get client and execute
                async with self.acquire_context() as client:
                    try:
                        result = await request_func(client)
                        future.set_result(result)
                    except Exception as e:
                        future.set_exception(e)
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error processing LLM request: {e}")


class TokenBucket:
    """Token bucket for rate limiting."""
    
    def __init__(self, tokens_per_minute: int, burst_size: int):
        self.tokens_per_minute = tokens_per_minute
        self.burst_size = burst_size
        self.tokens = burst_size
        self.last_update = time.time()
        self._lock = asyncio.Lock()
    
    async def acquire(self, tokens: int) -> None:
        """Acquire tokens, waiting if necessary."""
        async with self._lock:
            while True:
                # Refill bucket
                current_time = time.time()
                elapsed = current_time - self.last_update
                refill = elapsed * (self.tokens_per_minute / 60.0)
                self.tokens = min(self.burst_size, self.tokens + refill)
                self.last_update = current_time
                
                # Check if we have enough tokens
                if self.tokens >= tokens:
                    self.tokens -= tokens
                    return
                
                # Calculate wait time
                needed = tokens - self.tokens
                wait_time = needed / (self.tokens_per_minute / 60.0)
                await asyncio.sleep(wait_time)


class FileHandlePool:
    """
    Pool for managing file handles with automatic cleanup.
    
    Tracks open files and ensures they are properly closed.
    """
    
    def __init__(self, max_open_files: int = 100, logger: Optional[logging.Logger] = None):
        self.max_open_files = max_open_files
        self.logger = logger or logging.getLogger(__name__)
        self._open_files: Dict[str, Any] = {}
        self._lock = asyncio.Lock()
        self._lru_order: List[str] = []
    
    async def open_file(self, path: str, mode: str = 'r', **kwargs) -> Any:
        """Open a file with automatic tracking."""
        async with self._lock:
            # Check if already open
            if path in self._open_files:
                # Move to end of LRU
                self._lru_order.remove(path)
                self._lru_order.append(path)
                return self._open_files[path]
            
            # Check limit
            if len(self._open_files) >= self.max_open_files:
                # Close LRU file
                lru_path = self._lru_order.pop(0)
                await self._close_file(lru_path)
            
            # Open new file
            try:
                import aiofiles
                file_handle = await aiofiles.open(path, mode, **kwargs)
                self._open_files[path] = file_handle
                self._lru_order.append(path)
                return file_handle
            except Exception as e:
                raise ResourceException(
                    f"Failed to open file: {str(e)}",
                    resource_type="file",
                    resource_path=path,
                    operation="open"
                )
    
    async def _close_file(self, path: str) -> None:
        """Close a tracked file."""
        if path in self._open_files:
            try:
                await self._open_files[path].close()
            except Exception as e:
                self.logger.error(f"Error closing file {path}: {e}")
            finally:
                del self._open_files[path]
    
    async def close_all(self) -> None:
        """Close all tracked files."""
        async with self._lock:
            paths = list(self._open_files.keys())
            for path in paths:
                await self._close_file(path)
            self._lru_order.clear()
    
    def get_open_count(self) -> int:
        """Get number of open files."""
        return len(self._open_files)
    
    async def __aenter__(self):
        """Context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        await self.close_all()