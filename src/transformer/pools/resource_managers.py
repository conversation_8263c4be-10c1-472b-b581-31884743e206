"""
Resource management context managers for transformer services.

This module provides context managers for handling resources like files,
temporary directories, and processing contexts with automatic cleanup.
"""
import asyncio
import os
import shutil
import tempfile
from contextlib import asynccontextmanager, contextmanager
from datetime import datetime
from pathlib import Path
from typing import Any, Async<PERSON>tera<PERSON>, Dict, Iterator, Optional, Union

from src.infrastructure.protocols.logger import LoggerProtocol
from src.models.transformer.processing_models import ProcessingMetadata
from src.transformer.config.constants import ProcessingStatus


class FileOperationContext:
    """
    Context manager for file operations with automatic cleanup.
    
    Example:
        async with FileOperationContext(logger) as ctx:
            temp_file = ctx.create_temp_file(suffix='.json')
            # Use temp_file
            # File is automatically cleaned up on exit
    """
    
    def __init__(self, logger: Optional[LoggerProtocol] = None, 
                 cleanup: bool = True):
        self.logger = logger
        self.cleanup = cleanup
        self._temp_files: list[Path] = []
        self._temp_dirs: list[Path] = []
        self._open_files: list[Any] = []
    
    async def __aenter__(self) -> 'FileOperationContext':
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with cleanup."""
        await self._cleanup()
    
    def __enter__(self) -> 'FileOperationContext':
        """Sync context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Sync context manager exit with cleanup."""
        asyncio.create_task(self._cleanup())
    
    def create_temp_file(self, suffix: str = '', prefix: str = 'transformer_',
                        dir: Optional[Path] = None) -> Path:
        """
        Create a temporary file that will be cleaned up on exit.
        
        Args:
            suffix: File suffix
            prefix: File prefix
            dir: Directory for temp file
            
        Returns:
            Path to temporary file
        """
        fd, path = tempfile.mkstemp(suffix=suffix, prefix=prefix, 
                                   dir=str(dir) if dir else None)
        os.close(fd)  # Close the file descriptor
        
        temp_path = Path(path)
        self._temp_files.append(temp_path)
        
        if self.logger:
            self.logger.debug(f"Created temp file: {temp_path}")
        
        return temp_path
    
    def create_temp_dir(self, suffix: str = '', prefix: str = 'transformer_') -> Path:
        """
        Create a temporary directory that will be cleaned up on exit.
        
        Args:
            suffix: Directory suffix
            prefix: Directory prefix
            
        Returns:
            Path to temporary directory
        """
        temp_dir = Path(tempfile.mkdtemp(suffix=suffix, prefix=prefix))
        self._temp_dirs.append(temp_dir)
        
        if self.logger:
            self.logger.debug(f"Created temp directory: {temp_dir}")
        
        return temp_dir
    
    async def open_file(self, path: Union[str, Path], mode: str = 'r', **kwargs):
        """
        Open a file that will be automatically closed on exit.
        
        Args:
            path: File path
            mode: Open mode
            **kwargs: Additional arguments for open()
            
        Returns:
            Opened file object
        """
        import aiofiles
        
        file_obj = await aiofiles.open(path, mode, **kwargs)
        self._open_files.append(file_obj)
        
        return file_obj
    
    async def _cleanup(self):
        """Clean up all resources."""
        # Close open files
        for file_obj in self._open_files:
            try:
                await file_obj.close()
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"Error closing file: {e}")
        
        if self.cleanup:
            # Remove temporary files
            for temp_file in self._temp_files:
                try:
                    if temp_file.exists():
                        temp_file.unlink()
                        if self.logger:
                            self.logger.debug(f"Removed temp file: {temp_file}")
                except Exception as e:
                    if self.logger:
                        self.logger.warning(f"Error removing temp file {temp_file}: {e}")
            
            # Remove temporary directories
            for temp_dir in self._temp_dirs:
                try:
                    if temp_dir.exists():
                        shutil.rmtree(temp_dir)
                        if self.logger:
                            self.logger.debug(f"Removed temp directory: {temp_dir}")
                except Exception as e:
                    if self.logger:
                        self.logger.warning(f"Error removing temp directory {temp_dir}: {e}")


class ProcessingContext:
    """
    Context manager for tracking processing state and metadata.
    
    Example:
        async with ProcessingContext("operation_name", logger) as ctx:
            ctx.update_metadata(court_id="njd")
            # Do processing
            ctx.mark_success()
    """
    
    def __init__(self, operation_name: str, logger: Optional[LoggerProtocol] = None):
        self.operation_name = operation_name
        self.logger = logger
        self.metadata = ProcessingMetadata()
        self.status = ProcessingStatus.PENDING
        self.errors: list[str] = []
        self.warnings: list[str] = []
    
    async def __aenter__(self) -> 'ProcessingContext':
        """Async context manager entry."""
        self.metadata.start_time = datetime.utcnow()
        self.status = ProcessingStatus.PROCESSING
        
        if self.logger:
            self.logger.info(f"Starting {self.operation_name}")
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        self.metadata.end_time = datetime.utcnow()
        self.metadata.calculate_duration()
        
        if exc_type:
            self.status = ProcessingStatus.ERROR
            self.errors.append(str(exc_val))
            
            if self.logger:
                self.logger.error(
                    f"{self.operation_name} failed after "
                    f"{self.metadata.duration_seconds:.2f}s: {exc_val}"
                )
        else:
            if self.status == ProcessingStatus.PROCESSING:
                self.status = ProcessingStatus.COMPLETED
            
            if self.logger:
                self.logger.info(
                    f"{self.operation_name} completed in "
                    f"{self.metadata.duration_seconds:.2f}s"
                )
    
    def update_metadata(self, **kwargs):
        """Update processing metadata."""
        for key, value in kwargs.items():
            if hasattr(self.metadata, key):
                setattr(self.metadata, key, value)
    
    def mark_success(self):
        """Mark the processing as successful."""
        self.status = ProcessingStatus.COMPLETED
    
    def mark_error(self, error: Union[str, Exception]):
        """Mark the processing as failed."""
        self.status = ProcessingStatus.ERROR
        self.errors.append(str(error))
    
    def add_warning(self, warning: str):
        """Add a warning message."""
        self.warnings.append(warning)
        if self.status == ProcessingStatus.PENDING:
            self.status = ProcessingStatus.WARNING


class ValidationContext:
    """
    Context manager for validation operations.
    
    Example:
        with ValidationContext("field_validation", logger) as ctx:
            if not validate_field(data):
                ctx.add_error("field", "Invalid value")
            # Validation report available at ctx.report
    """
    
    def __init__(self, validation_name: str, logger: Optional[LoggerProtocol] = None):
        self.validation_name = validation_name
        self.logger = logger
        self.errors: Dict[str, list[str]] = {}
        self.warnings: Dict[str, list[str]] = {}
        self.validated_fields: set[str] = set()
        self.is_valid = True
    
    def __enter__(self) -> 'ValidationContext':
        """Context manager entry."""
        if self.logger:
            self.logger.debug(f"Starting {self.validation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        if self.logger:
            if self.is_valid:
                self.logger.debug(f"{self.validation_name} passed")
            else:
                self.logger.warning(
                    f"{self.validation_name} failed with "
                    f"{sum(len(errs) for errs in self.errors.values())} errors"
                )
    
    def add_error(self, field: str, message: str):
        """Add a validation error."""
        if field not in self.errors:
            self.errors[field] = []
        self.errors[field].append(message)
        self.is_valid = False
    
    def add_warning(self, field: str, message: str):
        """Add a validation warning."""
        if field not in self.warnings:
            self.warnings[field] = []
        self.warnings[field].append(message)
    
    def mark_validated(self, field: str):
        """Mark a field as validated."""
        self.validated_fields.add(field)
    
    @property
    def report(self) -> Dict[str, Any]:
        """Get validation report."""
        return {
            'is_valid': self.is_valid,
            'errors': self.errors,
            'warnings': self.warnings,
            'validated_fields': list(self.validated_fields),
            'error_count': sum(len(errs) for errs in self.errors.values()),
            'warning_count': sum(len(warns) for warns in self.warnings.values())
        }


class BatchContext:
    """
    Context manager for batch operations with progress tracking.
    
    Example:
        async with BatchContext(items, "processing", logger) as batch:
            async for item in batch:
                try:
                    process(item)
                    batch.mark_success()
                except Exception as e:
                    batch.mark_error(e)
    """
    
    def __init__(self, items: list[Any], operation: str, 
                 logger: Optional[LoggerProtocol] = None):
        self.items = items
        self.operation = operation
        self.logger = logger
        self.total = len(items)
        self.current = 0
        self.successes = 0
        self.errors = 0
        self.start_time: Optional[datetime] = None
    
    async def __aenter__(self) -> 'BatchContext':
        """Async context manager entry."""
        self.start_time = datetime.utcnow()
        if self.logger:
            self.logger.info(f"Starting batch {self.operation} for {self.total} items")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        duration = (datetime.utcnow() - self.start_time).total_seconds()
        
        if self.logger:
            self.logger.info(
                f"Batch {self.operation} completed in {duration:.2f}s: "
                f"{self.successes}/{self.total} successful, {self.errors} errors"
            )
    
    async def __aiter__(self):
        """Async iterator for batch items."""
        for item in self.items:
            self.current += 1
            if self.logger and self.current % 10 == 0:
                self.logger.debug(f"Processing item {self.current}/{self.total}")
            yield item
    
    def mark_success(self):
        """Mark current item as successfully processed."""
        self.successes += 1
    
    def mark_error(self, error: Union[str, Exception]):
        """Mark current item as failed."""
        self.errors += 1
        if self.logger:
            self.logger.warning(f"Error processing item {self.current}: {error}")
    
    @property
    def progress(self) -> float:
        """Get progress percentage."""
        return (self.current / self.total * 100) if self.total > 0 else 0


# Convenience functions
@asynccontextmanager
async def temp_processing_dir(prefix: str = "transformer_", 
                             logger: Optional[LoggerProtocol] = None) -> AsyncIterator[Path]:
    """
    Async context manager for a temporary processing directory.
    
    Example:
        async with temp_processing_dir("myprocess_", logger) as temp_dir:
            # Use temp_dir
            # Directory is automatically cleaned up
    """
    temp_dir = Path(tempfile.mkdtemp(prefix=prefix))
    
    if logger:
        logger.debug(f"Created temp directory: {temp_dir}")
    
    try:
        yield temp_dir
    finally:
        try:
            shutil.rmtree(temp_dir)
            if logger:
                logger.debug(f"Removed temp directory: {temp_dir}")
        except Exception as e:
            if logger:
                logger.warning(f"Error removing temp directory {temp_dir}: {e}")


@contextmanager
def safe_file_operation(path: Union[str, Path], mode: str = 'r',
                       logger: Optional[LoggerProtocol] = None) -> Iterator:
    """
    Context manager for safe file operations with error handling.
    
    Example:
        with safe_file_operation("data.json", "r", logger) as f:
            data = json.load(f)
    """
    file_obj = None
    try:
        file_obj = open(path, mode)
        yield file_obj
    except Exception as e:
        if logger:
            logger.error(f"Error accessing file {path}: {e}")
        raise
    finally:
        if file_obj:
            file_obj.close()