# /src/services/transformer/error_handler.py
"""
# Removed dependency_injector imports - using container-based injection
Error handling utilities for DataTransformer.

This module centralizes error handling logic extracted from transformer.py
as part of Phase 3 refactoring.
"""
import logging
from typing import TYPE_CHECKING, Any, Dict, Optional

# Removed dependency_injector imports - using container-based injection
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError

if TYPE_CHECKING:
    from src.transformer.components.file import FileHandler


class Error<PERSON>andler(AsyncServiceBase):
    """Centralized error handling for DataTransformer operations."""

    def __init__(self,
                 file_handler: 'FileHandler',
                 config: Optional[Dict],
                 logger: Optional[logging.Logger]):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.file_handler = file_handler

    async def _execute_action(self, data: Any) -> Any:
        """Execute E<PERSON><PERSON><PERSON><PERSON><PERSON> actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            match action:
                case 'update_error_status_and_save':
                    error_data = action_data.get('data')
                    json_path = action_data.get('json_path', '')
                    error_message = action_data.get('error_message', '')
                    await self.update_error_status_and_save(error_data, json_path, error_message)
                    return {'status': 'saved'}
                case 'update_error_status':
                    error_data = action_data.get('data')
                    json_path = action_data.get('json_path', '')
                    error_message = action_data.get('error_message', '')
                    self.update_error_status(error_data, json_path, error_message)
                    return error_data
                case 'clear_processing_errors':
                    self.clear_processing_errors(action_data)
                    return action_data
                case 'has_processing_errors':
                    return self.has_processing_errors(action_data)
                case 'get_error_summary':
                    return self.get_error_summary(action_data)
                case 'validate_error_data':
                    return self.validate_error_data(action_data)
                case 'get_error_count':
                    return self.get_error_count(action_data)
                case 'add_error':
                    error_message = action_data.get('error_message', '')
                    self.add_error(action_data, error_message)
                    return action_data
                case _:
                    raise TransformerServiceError(f"Invalid action '{action}' provided to ErrorHandler")
        raise TransformerServiceError("Invalid action data provided to ErrorHandler")

    async def update_error_status_and_save(self, data: Optional[Dict], json_path: str, error_message: str):
        """
        Update error status in data and save to file.

        Args:
            data: Data dictionary to update (can be None)
            json_path: Path to JSON file to save
            error_message: Error message to record
        """
        self.update_error_status(data, json_path, error_message)

        if data:
            try:
                await self.file_handler.save_json_async(json_path, data)
                self.log_debug(f"Saved error status to {json_path}")
            except Exception as e:
                self.log_error(f"Failed to save error status to {json_path}: {e}")

    def update_error_status(self, data: Optional[Dict], json_path: str, error_message: str):
        """
        Update error status in data dictionary.

        Args:
            data: Data dictionary to update
            json_path: Path context for logging
            error_message: Error message to record
        """
        if data:
            data['processing_errors'] = data.get('processing_errors', [])
            if isinstance(data['processing_errors'], list):
                data['processing_errors'].append(error_message)
            else:
                data['processing_errors'] = [str(data['processing_errors']), error_message]

            data['processing_status'] = 'error'
            self.log_error(f"Updated error status for {json_path}: {error_message}")
        else:
            self.log_error(f"Cannot update error status (data is None) for {json_path}: {error_message}")

    def clear_processing_errors(self, data: Dict):
        """
        Clear processing errors from data dictionary.

        Args:
            data: Data dictionary to clear errors from
        """
        if 'processing_errors' in data:
            del data['processing_errors']
        if 'processing_status' in data:
            del data['processing_status']

        self.log_debug("Cleared processing errors from data")

    def has_processing_errors(self, data: Dict) -> bool:
        """
        Check if data has processing errors.

        Args:
            data: Data dictionary to check

        Returns:
            True if data has processing errors
        """
        return bool(data.get('processing_errors')) or data.get('processing_status') == 'error'

    def get_error_summary(self, data: Dict) -> str:
        """
        Get summary of processing errors.

        Args:
            data: Data dictionary to summarize

        Returns:
            String summary of errors
        """
        errors = data.get('processing_errors', [])
        status = data.get('processing_status', 'unknown')

        if not errors:
            return f"Status: {status}"

        if isinstance(errors, list):
            return f"Status: {status}, Errors: {len(errors)} - {'; '.join(errors[-3:])}"
        else:
            return f"Status: {status}, Error: {str(errors)}"

    def validate_error_data(self, data: Dict) -> Dict[str, Any]:
        """
        Validate error data structure and return validation report.

        Args:
            data: Data dictionary to validate

        Returns:
            Validation report with status and details
        """
        report = {
            'status': 'success',
            'warnings': [],
            'errors': []
        }

        # Check for error fields
        if 'processing_errors' in data:
            errors = data['processing_errors']
            if not isinstance(errors, list):
                report['warnings'].append('processing_errors should be a list')
            elif len(errors) > 10:
                report['warnings'].append(f'Large number of processing errors: {len(errors)}')

        if 'processing_status' in data:
            status = data['processing_status']
            if status not in ['error', 'success', 'pending', 'warning']:
                report['warnings'].append(f'Unknown processing status: {status}')

        # Set overall status
        if report['errors']:
            report['status'] = 'error'
        elif report['warnings']:
            report['status'] = 'warning'

        return report

    def get_error_count(self, data: Dict) -> int:
        """
        Get count of processing errors.

        Args:
            data: Data dictionary to count errors in

        Returns:
            Number of processing errors
        """
        errors = data.get('processing_errors', [])
        if isinstance(errors, list):
            return len(errors)
        elif errors:
            return 1
        else:
            return 0

    def add_error(self, data: Dict, error_message: str):
        """
        Add a single error to the data dictionary.

        Args:
            data: Data dictionary to add error to
            error_message: Error message to add
        """
        if not data:
            self.log_warning("Cannot add error to None data")
            return

        # Initialize error list if needed
        if 'processing_errors' not in data:
            data['processing_errors'] = []

        # Ensure it's a list
        if not isinstance(data['processing_errors'], list):
            data['processing_errors'] = [str(data['processing_errors'])]

        # Add the new error
        data['processing_errors'].append(error_message)
        data['processing_status'] = 'error'

        self.log_debug(f"Added error to data: {error_message}")

    def get_recent_errors(self, data: Dict, count: int = 5) -> list:
        """
        Get the most recent errors.

        Args:
            data: Data dictionary to get errors from
            count: Number of recent errors to return

        Returns:
            List of recent error messages
        """
        errors = data.get('processing_errors', [])
        if isinstance(errors, list):
            return errors[-count:] if len(errors) > count else errors
        elif errors:
            return [str(errors)]
        else:
            return []
