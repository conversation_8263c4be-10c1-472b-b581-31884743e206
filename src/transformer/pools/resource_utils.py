"""
Resource management utilities for transformer services.

This module provides connection pooling, resource tracking, and cleanup
utilities to optimize resource usage across transformer services.
"""
import asyncio
import logging
import time
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, TypeVar, Generic, Callable
from weakref import WeakSet

from src.transformer.exceptions import ResourceException

T = TypeVar('T')


@dataclass
class ResourceMetrics:
    """Metrics for resource usage tracking."""
    created_count: int = 0
    destroyed_count: int = 0
    acquired_count: int = 0
    released_count: int = 0
    failed_count: int = 0
    wait_time_total: float = 0.0
    usage_time_total: float = 0.0
    current_active: int = 0
    peak_active: int = 0
    last_used: Optional[datetime] = None


@dataclass
class PoolConfig:
    """Configuration for resource pools."""
    min_size: int = 1
    max_size: int = 10
    acquire_timeout: float = 30.0
    idle_timeout: float = 300.0
    health_check_interval: float = 60.0
    max_lifetime: float = 3600.0
    retry_attempts: int = 3
    retry_delay: float = 1.0


class ResourcePool(Generic[T], ABC):
    """
    Generic resource pool implementation with health checking and metrics.
    
    Provides connection pooling, automatic cleanup, and usage tracking
    for any type of resource.
    """
    
    def __init__(self, config: PoolConfig, logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        
        # Pool state
        self._pool: asyncio.Queue[T] = asyncio.Queue(maxsize=config.max_size)
        self._active: Set[T] = set()
        self._creating: Set[asyncio.Task] = set()
        self._metrics = ResourceMetrics()
        
        # Lifecycle tracking
        self._resource_created_at: Dict[T, float] = {}
        self._resource_last_used: Dict[T, float] = {}
        
        # Pool control
        self._closed = False
        self._lock = asyncio.Lock()
        self._health_check_task: Optional[asyncio.Task] = None
    
    @abstractmethod
    async def _create_resource(self) -> T:
        """Create a new resource instance."""
        pass
    
    @abstractmethod
    async def _destroy_resource(self, resource: T) -> None:
        """Destroy a resource instance."""
        pass
    
    @abstractmethod
    async def _is_healthy(self, resource: T) -> bool:
        """Check if a resource is healthy and usable."""
        pass
    
    async def initialize(self) -> None:
        """Initialize the pool with minimum resources."""
        if self._closed:
            raise ResourceException(
                "Cannot initialize closed pool",
                resource_type="pool",
                resource_path=self.__class__.__name__,
                operation="initialize"
            )
        
        # Create initial resources
        create_tasks = []
        for _ in range(self.config.min_size):
            task = asyncio.create_task(self._create_and_add_resource())
            create_tasks.append(task)
            self._creating.add(task)
        
        # Wait for initial resources
        results = await asyncio.gather(*create_tasks, return_exceptions=True)
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"Failed to create initial resource: {result}")
        
        # Start health check task
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        
        self.logger.info(
            f"Pool initialized with {self._pool.qsize()} resources "
            f"(min: {self.config.min_size}, max: {self.config.max_size})"
        )
    
    async def acquire(self) -> T:
        """
        Acquire a resource from the pool.
        
        Returns:
            A healthy resource instance
            
        Raises:
            ResourceException: If unable to acquire resource
        """
        if self._closed:
            raise ResourceException(
                "Pool is closed",
                resource_type="pool",
                resource_path=self.__class__.__name__,
                operation="acquire"
            )
        
        start_time = time.time()
        retry_count = 0
        
        while retry_count < self.config.retry_attempts:
            try:
                resource = await self._acquire_with_timeout()
                
                # Update metrics
                wait_time = time.time() - start_time
                self._metrics.wait_time_total += wait_time
                self._metrics.acquired_count += 1
                self._metrics.current_active += 1
                self._metrics.peak_active = max(
                    self._metrics.peak_active,
                    self._metrics.current_active
                )
                self._metrics.last_used = datetime.now()
                
                # Track usage
                self._resource_last_used[resource] = time.time()
                
                return resource
                
            except asyncio.TimeoutError:
                retry_count += 1
                if retry_count >= self.config.retry_attempts:
                    raise ResourceException(
                        f"Failed to acquire resource after {retry_count} attempts",
                        resource_type="pool",
                        resource_path=self.__class__.__name__,
                        operation="acquire"
                    )
                await asyncio.sleep(self.config.retry_delay * retry_count)
            except Exception as e:
                self._metrics.failed_count += 1
                raise ResourceException(
                    f"Error acquiring resource: {str(e)}",
                    resource_type="pool",
                    resource_path=self.__class__.__name__,
                    operation="acquire"
                )
    
    async def _acquire_with_timeout(self) -> T:
        """Acquire resource with timeout handling."""
        while True:
            # Try to get from pool
            try:
                resource = self._pool.get_nowait()
                if await self._is_healthy(resource):
                    self._active.add(resource)
                    return resource
                else:
                    # Unhealthy resource, destroy it
                    await self._destroy_and_remove(resource)
                    continue
            except asyncio.QueueEmpty:
                pass
            
            # Check if we can create new resource
            async with self._lock:
                total_resources = len(self._active) + self._pool.qsize() + len(self._creating)
                if total_resources < self.config.max_size:
                    # Create new resource
                    task = asyncio.create_task(self._create_and_add_resource())
                    self._creating.add(task)
                    
                    try:
                        resource = await asyncio.wait_for(
                            task,
                            timeout=self.config.acquire_timeout
                        )
                        if resource:
                            self._active.add(resource)
                            return resource
                    finally:
                        self._creating.discard(task)
            
            # Wait for resource to become available
            try:
                resource = await asyncio.wait_for(
                    self._pool.get(),
                    timeout=self.config.acquire_timeout
                )
                if await self._is_healthy(resource):
                    self._active.add(resource)
                    return resource
                else:
                    await self._destroy_and_remove(resource)
            except asyncio.TimeoutError:
                raise
    
    async def release(self, resource: T) -> None:
        """
        Release a resource back to the pool.
        
        Args:
            resource: The resource to release
        """
        if resource not in self._active:
            self.logger.warning("Attempting to release untracked resource")
            return
        
        try:
            # Update metrics
            if resource in self._resource_last_used:
                usage_time = time.time() - self._resource_last_used[resource]
                self._metrics.usage_time_total += usage_time
            
            self._metrics.released_count += 1
            self._metrics.current_active -= 1
            
            # Remove from active set
            self._active.discard(resource)
            
            # Check if resource should be recycled
            if self._should_recycle(resource):
                await self._destroy_and_remove(resource)
            elif await self._is_healthy(resource):
                # Return to pool
                await self._pool.put(resource)
                self._resource_last_used[resource] = time.time()
            else:
                # Unhealthy resource
                await self._destroy_and_remove(resource)
                
        except Exception as e:
            self.logger.error(f"Error releasing resource: {e}")
            # Ensure resource is cleaned up
            await self._destroy_and_remove(resource)
    
    def _should_recycle(self, resource: T) -> bool:
        """Check if resource should be recycled based on age or usage."""
        if resource not in self._resource_created_at:
            return True
        
        age = time.time() - self._resource_created_at[resource]
        return age > self.config.max_lifetime
    
    async def _create_and_add_resource(self) -> Optional[T]:
        """Create a new resource and add to pool."""
        try:
            resource = await self._create_resource()
            self._resource_created_at[resource] = time.time()
            self._resource_last_used[resource] = time.time()
            self._metrics.created_count += 1
            return resource
        except Exception as e:
            self._metrics.failed_count += 1
            self.logger.error(f"Failed to create resource: {e}")
            return None
    
    async def _destroy_and_remove(self, resource: T) -> None:
        """Destroy resource and remove from tracking."""
        try:
            await self._destroy_resource(resource)
            self._metrics.destroyed_count += 1
        except Exception as e:
            self.logger.error(f"Error destroying resource: {e}")
        finally:
            # Clean up tracking
            self._resource_created_at.pop(resource, None)
            self._resource_last_used.pop(resource, None)
            self._active.discard(resource)
    
    async def _health_check_loop(self) -> None:
        """Periodic health check of idle resources."""
        while not self._closed:
            try:
                await asyncio.sleep(self.config.health_check_interval)
                await self._check_idle_resources()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in health check loop: {e}")
    
    async def _check_idle_resources(self) -> None:
        """Check and clean up idle resources."""
        current_time = time.time()
        resources_to_check = []
        
        # Get resources to check
        while not self._pool.empty():
            try:
                resource = self._pool.get_nowait()
                resources_to_check.append(resource)
            except asyncio.QueueEmpty:
                break
        
        # Check each resource
        for resource in resources_to_check:
            try:
                last_used = self._resource_last_used.get(resource, 0)
                idle_time = current_time - last_used
                
                if idle_time > self.config.idle_timeout:
                    # Resource idle too long
                    await self._destroy_and_remove(resource)
                elif not await self._is_healthy(resource):
                    # Unhealthy resource
                    await self._destroy_and_remove(resource)
                else:
                    # Healthy resource, return to pool
                    await self._pool.put(resource)
            except Exception as e:
                self.logger.error(f"Error checking resource health: {e}")
                await self._destroy_and_remove(resource)
    
    async def close(self) -> None:
        """Close the pool and clean up all resources."""
        if self._closed:
            return
        
        self._closed = True
        
        # Cancel health check
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # Cancel creating tasks
        for task in self._creating:
            task.cancel()
        
        # Destroy active resources
        for resource in list(self._active):
            await self._destroy_and_remove(resource)
        
        # Destroy pooled resources
        while not self._pool.empty():
            try:
                resource = self._pool.get_nowait()
                await self._destroy_and_remove(resource)
            except asyncio.QueueEmpty:
                break
        
        self.logger.info(
            f"Pool closed. Metrics: created={self._metrics.created_count}, "
            f"destroyed={self._metrics.destroyed_count}, "
            f"peak_active={self._metrics.peak_active}"
        )
    
    def get_metrics(self) -> ResourceMetrics:
        """Get current pool metrics."""
        return self._metrics
    
    @asynccontextmanager
    async def acquire_context(self):
        """Context manager for resource acquisition."""
        resource = await self.acquire()
        try:
            yield resource
        finally:
            await self.release(resource)


class ResourceTracker:
    """
    Central resource tracking and monitoring.
    
    Tracks all active resources, detects leaks, and provides
    usage statistics.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self._resources: Dict[str, WeakSet] = {}
        self._metrics: Dict[str, ResourceMetrics] = {}
        self._lock = asyncio.Lock()
    
    async def register_resource(self, resource_type: str, resource: Any) -> None:
        """Register a new resource for tracking."""
        async with self._lock:
            if resource_type not in self._resources:
                self._resources[resource_type] = WeakSet()
                self._metrics[resource_type] = ResourceMetrics()
            
            self._resources[resource_type].add(resource)
            self._metrics[resource_type].created_count += 1
            self._metrics[resource_type].current_active = len(self._resources[resource_type])
            self._metrics[resource_type].peak_active = max(
                self._metrics[resource_type].peak_active,
                self._metrics[resource_type].current_active
            )
    
    async def unregister_resource(self, resource_type: str, resource: Any) -> None:
        """Unregister a resource from tracking."""
        async with self._lock:
            if resource_type in self._resources:
                self._resources[resource_type].discard(resource)
                self._metrics[resource_type].destroyed_count += 1
                self._metrics[resource_type].current_active = len(self._resources[resource_type])
    
    def get_active_resources(self, resource_type: Optional[str] = None) -> Dict[str, int]:
        """Get count of active resources by type."""
        if resource_type:
            return {resource_type: len(self._resources.get(resource_type, set()))}
        else:
            return {rt: len(resources) for rt, resources in self._resources.items()}
    
    def get_metrics(self, resource_type: Optional[str] = None) -> Dict[str, ResourceMetrics]:
        """Get resource metrics."""
        if resource_type:
            return {resource_type: self._metrics.get(resource_type, ResourceMetrics())}
        else:
            return dict(self._metrics)
    
    def detect_leaks(self) -> Dict[str, List[Any]]:
        """Detect potential resource leaks."""
        leaks = {}
        for resource_type, resources in self._resources.items():
            active_resources = list(resources)
            if active_resources:
                leaks[resource_type] = active_resources
        return leaks
    
    def generate_report(self) -> str:
        """Generate resource usage report."""
        lines = ["Resource Usage Report", "=" * 50]
        
        for resource_type, metrics in self._metrics.items():
            lines.append(f"\n{resource_type}:")
            lines.append(f"  Created: {metrics.created_count}")
            lines.append(f"  Destroyed: {metrics.destroyed_count}")
            lines.append(f"  Currently Active: {metrics.current_active}")
            lines.append(f"  Peak Active: {metrics.peak_active}")
            lines.append(f"  Failed: {metrics.failed_count}")
            
            if metrics.acquired_count > 0:
                avg_wait = metrics.wait_time_total / metrics.acquired_count
                lines.append(f"  Avg Wait Time: {avg_wait:.3f}s")
            
            if metrics.released_count > 0:
                avg_usage = metrics.usage_time_total / metrics.released_count
                lines.append(f"  Avg Usage Time: {avg_usage:.3f}s")
        
        # Check for leaks
        leaks = self.detect_leaks()
        if leaks:
            lines.append("\nPotential Resource Leaks:")
            for resource_type, resources in leaks.items():
                lines.append(f"  {resource_type}: {len(resources)} active")
        
        return "\n".join(lines)


class ResourceCleanupChain:
    """
    Manages cleanup of multiple resources in reverse order.
    
    Ensures all resources are cleaned up even if some fail.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self._cleanup_tasks: List[Callable] = []
        self._cleanup_names: List[str] = []
    
    def register(self, cleanup_func: Callable, name: str = "unnamed") -> None:
        """Register a cleanup function."""
        self._cleanup_tasks.append(cleanup_func)
        self._cleanup_names.append(name)
    
    async def cleanup(self) -> List[Exception]:
        """
        Execute all cleanup tasks in reverse order.
        
        Returns:
            List of exceptions from failed cleanups
        """
        errors = []
        
        for task, name in zip(reversed(self._cleanup_tasks), reversed(self._cleanup_names)):
            try:
                if asyncio.iscoroutinefunction(task):
                    await task()
                else:
                    task()
            except Exception as e:
                self.logger.error(f"Cleanup failed for {name}: {e}")
                errors.append(
                    ResourceException(
                        f"Cleanup failed for {name}: {str(e)}",
                        resource_type="cleanup",
                        resource_path=name,
                        operation="cleanup"
                    )
                )
        
        return errors
    
    def __len__(self) -> int:
        """Get number of registered cleanup tasks."""
        return len(self._cleanup_tasks)
    
    async def __aenter__(self):
        """Context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        await self.cleanup()


# Global resource tracker instance
_global_tracker = ResourceTracker()


def get_resource_tracker() -> ResourceTracker:
    """Get the global resource tracker instance."""
    return _global_tracker