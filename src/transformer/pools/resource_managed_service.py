"""
Resource-managed service base class for transformer services.

Extends AsyncServiceBase with resource pooling, tracking, and cleanup capabilities.
"""
import asyncio
import logging
from abc import ABC
from typing import Any, Dict, Optional, Type

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.transformer.pools.resource_utils import (
    ResourceCleanup<PERSON>hain, ResourceTracker, get_resource_tracker
)
from src.transformer.pools.resource_pools import (
    DynamoDBConnectionPool, S3ConnectionPool, LLMClientPool, FileHandlePool
)
from src.transformer.exceptions import ResourceException


class ResourceManagedService(AsyncServiceBase, ABC):
    """
    Base service class with integrated resource management.
    
    Provides connection pooling, resource tracking, and automatic cleanup
    for transformer services.
    """
    
    def __init__(self, logger: logging.Logger, config: Dict[str, Any]):
        super().__init__(logger, config)
        
        # Resource management
        self._resource_tracker = get_resource_tracker()
        self._cleanup_chain = ResourceCleanupChain(logger)
        self._resource_pools: Dict[str, Any] = {}
        self._managed_resources: Dict[str, Any] = {}
        
        # Configuration
        self._pool_configs = {
            'dynamodb': config.get('dynamodb_pool', {}),
            's3': config.get('s3_pool', {}),
            'llm': config.get('llm_pool', {}),
            'file': config.get('file_pool', {})
        }
    
    async def _initialize_service(self) -> None:
        """Initialize service with resource management."""
        try:
            # Initialize base service
            await super()._initialize_service()
            
            # Initialize resource pools
            await self._initialize_resource_pools()
            
            # Register cleanup
            self._cleanup_chain.register(
                self._cleanup_resource_pools,
                "resource_pools"
            )
            
            self.log_info("Resource-managed service initialized")
            
        except Exception as e:
            self.log_error(f"Failed to initialize resource-managed service: {e}")
            raise ResourceException(
                f"Initialization failed: {str(e)}",
                resource_type="service",
                resource_path=self.__class__.__name__,
                operation="initialize"
            )
    
    async def _cleanup_service(self) -> None:
        """Cleanup service resources."""
        try:
            # Run cleanup chain
            errors = await self._cleanup_chain.cleanup()
            if errors:
                self.log_error(f"Cleanup errors: {errors}")
            
            # Cleanup base service
            await super()._cleanup_service()
            
            # Check for resource leaks
            leaks = self._resource_tracker.detect_leaks()
            if leaks:
                self.log_warning(f"Potential resource leaks detected: {leaks}")
            
            self.log_info("Resource-managed service cleaned up")
            
        except Exception as e:
            self.log_error(f"Error during service cleanup: {e}")
    
    async def _initialize_resource_pools(self) -> None:
        """Initialize configured resource pools."""
        # Override in subclasses to initialize specific pools
        pass
    
    async def _cleanup_resource_pools(self) -> None:
        """Cleanup all resource pools."""
        for pool_name, pool in self._resource_pools.items():
            try:
                if hasattr(pool, 'close'):
                    await pool.close()
                    self.log_debug(f"Closed resource pool: {pool_name}")
            except Exception as e:
                self.log_error(f"Error closing pool {pool_name}: {e}")
    
    async def get_dynamodb_pool(self, table_name: str) -> DynamoDBConnectionPool:
        """
        Get or create DynamoDB connection pool.
        
        Args:
            table_name: DynamoDB table name
            
        Returns:
            DynamoDBConnectionPool instance
        """
        pool_key = f"dynamodb_{table_name}"
        
        if pool_key not in self._resource_pools:
            # Create new pool
            pool_config = self._pool_configs.get('dynamodb', {})
            pool = DynamoDBConnectionPool(
                table_name=table_name,
                region=self.config.get('aws_region', 'us-west-2'),
                config=pool_config,
                logger=self.logger
            )
            
            # Initialize pool
            await pool.initialize()
            
            # Register pool
            self._resource_pools[pool_key] = pool
            await self._resource_tracker.register_resource('dynamodb_pool', pool)
            
            # Register cleanup
            self._cleanup_chain.register(
                lambda: pool.close(),
                f"dynamodb_pool_{table_name}"
            )
            
            self.log_info(f"Created DynamoDB pool for table: {table_name}")
        
        return self._resource_pools[pool_key]
    
    async def get_s3_pool(self, bucket_name: str) -> S3ConnectionPool:
        """
        Get or create S3 connection pool.
        
        Args:
            bucket_name: S3 bucket name
            
        Returns:
            S3ConnectionPool instance
        """
        pool_key = f"s3_{bucket_name}"
        
        if pool_key not in self._resource_pools:
            # Create new pool
            pool_config = self._pool_configs.get('s3', {})
            pool = S3ConnectionPool(
                bucket_name=bucket_name,
                region=self.config.get('aws_region', 'us-west-2'),
                config=pool_config,
                logger=self.logger
            )
            
            # Initialize pool
            await pool.initialize()
            
            # Register pool
            self._resource_pools[pool_key] = pool
            await self._resource_tracker.register_resource('s3_pool', pool)
            
            # Register cleanup
            self._cleanup_chain.register(
                lambda: pool.close(),
                f"s3_pool_{bucket_name}"
            )
            
            self.log_info(f"Created S3 pool for bucket: {bucket_name}")
        
        return self._resource_pools[pool_key]
    
    async def get_llm_pool(self, provider: str) -> LLMClientPool:
        """
        Get or create LLM client pool.
        
        Args:
            provider: LLM provider name
            
        Returns:
            LLMClientPool instance
        """
        pool_key = f"llm_{provider}"
        
        if pool_key not in self._resource_pools:
            # Get LLM configuration
            llm_config = self.config.get('llm', {}).get(provider, {})
            if not llm_config:
                raise ResourceException(
                    f"LLM provider {provider} not configured",
                    resource_type="llm",
                    resource_path=provider,
                    operation="get_pool"
                )
            
            # Create new pool
            pool_config = self._pool_configs.get('llm', {})
            pool = LLMClientPool(
                provider=provider,
                api_key=llm_config.get('api_key'),
                model=llm_config.get('model'),
                config=pool_config,
                logger=self.logger
            )
            
            # Initialize pool
            await pool.initialize()
            
            # Register pool
            self._resource_pools[pool_key] = pool
            await self._resource_tracker.register_resource('llm_pool', pool)
            
            # Register cleanup
            self._cleanup_chain.register(
                lambda: pool.close(),
                f"llm_pool_{provider}"
            )
            
            self.log_info(f"Created LLM pool for provider: {provider}")
        
        return self._resource_pools[pool_key]
    
    async def get_file_handle_pool(self) -> FileHandlePool:
        """
        Get or create file handle pool.
        
        Returns:
            FileHandlePool instance
        """
        pool_key = "file_handles"
        
        if pool_key not in self._resource_pools:
            # Create new pool
            pool_config = self._pool_configs.get('file', {})
            max_files = pool_config.get('max_open_files', 100)
            
            pool = FileHandlePool(
                max_open_files=max_files,
                logger=self.logger
            )
            
            # Register pool
            self._resource_pools[pool_key] = pool
            await self._resource_tracker.register_resource('file_pool', pool)
            
            # Register cleanup
            self._cleanup_chain.register(
                lambda: pool.close_all(),
                "file_handle_pool"
            )
            
            self.log_info(f"Created file handle pool (max files: {max_files})")
        
        return self._resource_pools[pool_key]
    
    async def track_resource(self, resource_type: str, resource: Any) -> None:
        """
        Track a managed resource.
        
        Args:
            resource_type: Type of resource
            resource: Resource instance
        """
        await self._resource_tracker.register_resource(resource_type, resource)
        self._managed_resources[f"{resource_type}_{id(resource)}"] = resource
    
    async def untrack_resource(self, resource_type: str, resource: Any) -> None:
        """
        Untrack a managed resource.
        
        Args:
            resource_type: Type of resource
            resource: Resource instance
        """
        await self._resource_tracker.unregister_resource(resource_type, resource)
        self._managed_resources.pop(f"{resource_type}_{id(resource)}", None)
    
    def register_cleanup(self, cleanup_func: Any, name: str = "unnamed") -> None:
        """
        Register a cleanup function.
        
        Args:
            cleanup_func: Cleanup function (sync or async)
            name: Name for debugging
        """
        self._cleanup_chain.register(cleanup_func, name)
    
    def get_resource_metrics(self) -> Dict[str, Any]:
        """Get resource usage metrics."""
        metrics = {
            'pools': {},
            'tracked_resources': self._resource_tracker.get_active_resources(),
            'managed_resources': len(self._managed_resources),
            'cleanup_tasks': len(self._cleanup_chain)
        }
        
        # Add pool metrics
        for pool_name, pool in self._resource_pools.items():
            if hasattr(pool, 'get_metrics'):
                metrics['pools'][pool_name] = pool.get_metrics()
        
        return metrics
    
    def log_resource_report(self) -> None:
        """Log resource usage report."""
        report = self._resource_tracker.generate_report()
        self.log_info(f"Resource Usage Report:\n{report}")
        
        # Log pool metrics
        for pool_name, pool in self._resource_pools.items():
            if hasattr(pool, 'get_metrics'):
                metrics = pool.get_metrics()
                self.log_info(
                    f"Pool {pool_name}: active={metrics.current_active}, "
                    f"peak={metrics.peak_active}, created={metrics.created_count}"
                )