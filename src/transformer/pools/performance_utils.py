"""
Performance optimization utilities for transformer services.

Provides batch processing, caching, and async coordination utilities.
"""
import asyncio
import functools
import hashlib
import json
import logging
import time
from collections import OrderedDict
from dataclasses import dataclass
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union

from src.transformer.exceptions import ProcessingException

T = TypeVar('T')
R = TypeVar('R')


@dataclass
class BatchConfig:
    """Configuration for batch processing."""
    batch_size: int = 100
    max_concurrent_batches: int = 10
    timeout_per_batch: float = 300.0
    retry_failed_items: bool = True
    error_threshold: float = 0.5  # Fail if >50% of items fail


class BatchProcessor:
    """
    Optimized batch processing with error handling and metrics.
    
    Processes items in batches with configurable concurrency and error handling.
    """
    
    def __init__(self, 
                 config: Optional[BatchConfig] = None,
                 logger: Optional[logging.Logger] = None):
        self.config = config or BatchConfig()
        self.logger = logger or logging.getLogger(__name__)
        self._metrics = {
            'total_items': 0,
            'processed_items': 0,
            'failed_items': 0,
            'total_batches': 0,
            'failed_batches': 0,
            'processing_time': 0.0
        }
    
    async def process_batch(self,
                           items: List[T],
                           process_func: Callable[[List[T]], R],
                           item_key_func: Optional[Callable[[T], str]] = None) -> Dict[str, Any]:
        """
        Process items in optimized batches.
        
        Args:
            items: Items to process
            process_func: Async function to process a batch
            item_key_func: Optional function to get item key for results
            
        Returns:
            Results dictionary with processed items and metrics
        """
        start_time = time.time()
        self._metrics['total_items'] = len(items)
        
        # Split into batches
        batches = self._create_batches(items)
        self._metrics['total_batches'] = len(batches)
        
        # Process batches concurrently
        semaphore = asyncio.Semaphore(self.config.max_concurrent_batches)
        tasks = []
        
        for batch_idx, batch in enumerate(batches):
            task = self._process_single_batch(
                batch, batch_idx, process_func, semaphore
            )
            tasks.append(task)
        
        # Gather results
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Aggregate results
        results = self._aggregate_results(
            items, batch_results, item_key_func
        )
        
        # Update metrics
        self._metrics['processing_time'] = time.time() - start_time
        results['metrics'] = dict(self._metrics)
        
        # Log summary
        self.logger.info(
            f"Batch processing complete: {self._metrics['processed_items']}/{self._metrics['total_items']} "
            f"items in {self._metrics['processing_time']:.2f}s"
        )
        
        return results
    
    def _create_batches(self, items: List[T]) -> List[List[T]]:
        """Split items into batches."""
        batches = []
        for i in range(0, len(items), self.config.batch_size):
            batch = items[i:i + self.config.batch_size]
            batches.append(batch)
        return batches
    
    async def _process_single_batch(self,
                                   batch: List[T],
                                   batch_idx: int,
                                   process_func: Callable,
                                   semaphore: asyncio.Semaphore) -> Dict[str, Any]:
        """Process a single batch with error handling."""
        async with semaphore:
            try:
                # Process with timeout
                result = await asyncio.wait_for(
                    process_func(batch),
                    timeout=self.config.timeout_per_batch
                )
                
                self.logger.debug(f"Batch {batch_idx} processed successfully")
                return {
                    'status': 'success',
                    'batch_idx': batch_idx,
                    'items': batch,
                    'result': result
                }
                
            except asyncio.TimeoutError:
                self._metrics['failed_batches'] += 1
                self.logger.error(f"Batch {batch_idx} timed out")
                return {
                    'status': 'timeout',
                    'batch_idx': batch_idx,
                    'items': batch,
                    'error': 'Batch processing timeout'
                }
                
            except Exception as e:
                self._metrics['failed_batches'] += 1
                self.logger.error(f"Batch {batch_idx} failed: {e}")
                return {
                    'status': 'error',
                    'batch_idx': batch_idx,
                    'items': batch,
                    'error': str(e)
                }
    
    def _aggregate_results(self,
                          original_items: List[T],
                          batch_results: List[Dict],
                          item_key_func: Optional[Callable]) -> Dict[str, Any]:
        """Aggregate batch results."""
        successful_items = []
        failed_items = []
        results_by_key = {}
        
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                # Entire task failed
                self._metrics['failed_batches'] += 1
                continue
            
            if batch_result['status'] == 'success':
                # Process successful results
                batch_items = batch_result['items']
                batch_data = batch_result.get('result', {})
                
                for item in batch_items:
                    successful_items.append(item)
                    self._metrics['processed_items'] += 1
                    
                    if item_key_func:
                        key = item_key_func(item)
                        results_by_key[key] = batch_data.get(key)
            else:
                # Handle failed batch
                for item in batch_result['items']:
                    failed_items.append({
                        'item': item,
                        'error': batch_result.get('error', 'Unknown error')
                    })
                    self._metrics['failed_items'] += 1
        
        # Check error threshold
        error_rate = self._metrics['failed_items'] / self._metrics['total_items']
        if error_rate > self.config.error_threshold:
            raise ProcessingException(
                f"Batch processing failed: {error_rate:.1%} error rate exceeds threshold",
                operation="batch_processing",
                stage="aggregation",
                item_id=None
            )
        
        return {
            'successful_items': successful_items,
            'failed_items': failed_items,
            'results_by_key': results_by_key,
            'success_rate': 1 - error_rate
        }
    
    def reset_metrics(self) -> None:
        """Reset processing metrics."""
        self._metrics = {
            'total_items': 0,
            'processed_items': 0,
            'failed_items': 0,
            'total_batches': 0,
            'failed_batches': 0,
            'processing_time': 0.0
        }


class LRUCache:
    """
    Thread-safe LRU cache implementation for async operations.
    
    Provides in-memory caching with TTL support.
    """
    
    def __init__(self, max_size: int = 1000, ttl: float = 3600.0):
        self.max_size = max_size
        self.ttl = ttl
        self._cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()
        self._lock = asyncio.Lock()
        self._hits = 0
        self._misses = 0
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        async with self._lock:
            if key in self._cache:
                # Check TTL
                entry = self._cache[key]
                if time.time() - entry['timestamp'] > self.ttl:
                    # Expired
                    del self._cache[key]
                    self._misses += 1
                    return None
                
                # Move to end (most recently used)
                self._cache.move_to_end(key)
                self._hits += 1
                return entry['value']
            
            self._misses += 1
            return None
    
    async def set(self, key: str, value: Any) -> None:
        """Set value in cache."""
        async with self._lock:
            # Remove oldest if at capacity
            if len(self._cache) >= self.max_size:
                self._cache.popitem(last=False)
            
            # Add new entry
            self._cache[key] = {
                'value': value,
                'timestamp': time.time()
            }
            self._cache.move_to_end(key)
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        async with self._lock:
            self._cache.clear()
            self._hits = 0
            self._misses = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self._hits + self._misses
        hit_rate = self._hits / total_requests if total_requests > 0 else 0
        
        return {
            'size': len(self._cache),
            'max_size': self.max_size,
            'hits': self._hits,
            'misses': self._misses,
            'hit_rate': hit_rate,
            'ttl': self.ttl
        }


def cached(cache: Optional[LRUCache] = None, 
          key_func: Optional[Callable] = None,
          ttl: Optional[float] = None):
    """
    Decorator for caching async function results.
    
    Args:
        cache: LRUCache instance (creates new if None)
        key_func: Function to generate cache key from args
        ttl: Override cache TTL for this function
    """
    if cache is None:
        cache = LRUCache(ttl=ttl or 3600.0)
    
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # Default key generation
                key_parts = [func.__name__]
                key_parts.extend(str(arg) for arg in args)
                key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                cache_key = hashlib.md5(
                    json.dumps(key_parts).encode()
                ).hexdigest()
            
            # Check cache
            cached_value = await cache.get(cache_key)
            if cached_value is not None:
                return cached_value
            
            # Call function
            result = await func(*args, **kwargs)
            
            # Cache result
            await cache.set(cache_key, result)
            
            return result
        
        # Attach cache instance for access
        wrapper.cache = cache
        return wrapper
    
    return decorator


class PriorityQueue:
    """
    Async priority queue for managing operations by priority.
    
    Higher priority items are processed first.
    """
    
    def __init__(self, maxsize: int = 0):
        self._queue: List[tuple[int, Any]] = []
        self._lock = asyncio.Lock()
        self._not_empty = asyncio.Condition(self._lock)
        self._not_full = asyncio.Condition(self._lock)
        self._maxsize = maxsize
        self._counter = 0  # For stable sorting
    
    async def put(self, item: Any, priority: int = 0) -> None:
        """Add item with priority (higher = more important)."""
        async with self._not_full:
            while self._maxsize > 0 and len(self._queue) >= self._maxsize:
                await self._not_full.wait()
            
            # Use negative priority for max heap behavior
            self._counter += 1
            heapq.heappush(self._queue, (-priority, self._counter, item))
            self._not_empty.notify()
    
    async def get(self) -> Any:
        """Get highest priority item."""
        async with self._not_empty:
            while not self._queue:
                await self._not_empty.wait()
            
            _, _, item = heapq.heappop(self._queue)
            self._not_full.notify()
            return item
    
    def qsize(self) -> int:
        """Get current queue size."""
        return len(self._queue)
    
    def empty(self) -> bool:
        """Check if queue is empty."""
        return len(self._queue) == 0
    
    def full(self) -> bool:
        """Check if queue is full."""
        return self._maxsize > 0 and len(self._queue) >= self._maxsize


class CircuitBreaker:
    """
    Circuit breaker pattern for handling failing services.
    
    Prevents cascading failures by temporarily disabling failing operations.
    """
    
    def __init__(self,
                 failure_threshold: int = 5,
                 recovery_timeout: float = 60.0,
                 expected_exception: type = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self._failure_count = 0
        self._last_failure_time = None
        self._state = 'closed'  # closed, open, half-open
        self._lock = asyncio.Lock()
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Call function with circuit breaker protection.
        
        Args:
            func: Async function to call
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            ProcessingException: If circuit is open
        """
        async with self._lock:
            if self._state == 'open':
                # Check if we should try half-open
                if (self._last_failure_time and 
                    time.time() - self._last_failure_time > self.recovery_timeout):
                    self._state = 'half-open'
                else:
                    raise ProcessingException(
                        "Circuit breaker is open",
                        operation="circuit_breaker",
                        stage="call",
                        item_id=None
                    )
        
        try:
            # Try to call function
            result = await func(*args, **kwargs)
            
            # Success - reset on half-open
            async with self._lock:
                if self._state == 'half-open':
                    self._state = 'closed'
                    self._failure_count = 0
            
            return result
            
        except self.expected_exception as e:
            # Record failure
            async with self._lock:
                self._failure_count += 1
                self._last_failure_time = time.time()
                
                if self._failure_count >= self.failure_threshold:
                    self._state = 'open'
                    raise ProcessingException(
                        f"Circuit breaker opened after {self._failure_count} failures",
                        operation="circuit_breaker",
                        stage="failure_threshold",
                        item_id=None
                    )
            
            raise
    
    @property
    def state(self) -> str:
        """Get current circuit breaker state."""
        return self._state
    
    @property
    def failure_count(self) -> int:
        """Get current failure count."""
        return self._failure_count
    
    async def reset(self) -> None:
        """Manually reset circuit breaker."""
        async with self._lock:
            self._state = 'closed'
            self._failure_count = 0
            self._last_failure_time = None


# Import heapq for priority queue
import heapq


async def gather_with_semaphore(tasks: List[Callable],
                               max_concurrent: int = 10,
                               return_exceptions: bool = True) -> List[Any]:
    """
    Execute tasks with concurrency limit.
    
    Args:
        tasks: List of async callables
        max_concurrent: Maximum concurrent executions
        return_exceptions: Whether to return exceptions or raise
        
    Returns:
        List of results
    """
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def run_with_semaphore(task):
        async with semaphore:
            return await task()
    
    wrapped_tasks = [run_with_semaphore(task) for task in tasks]
    return await asyncio.gather(*wrapped_tasks, return_exceptions=return_exceptions)