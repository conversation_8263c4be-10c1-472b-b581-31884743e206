# /src/services/transformer/components.data_cleaning/cleaner.py
"""
# Removed dependency_injector imports - using container-based injection
Docket data cleaning and formatting utilities.

This module handles data cleaning, formatting, and validation extracted from 
docket_processor.py as part of Phase 3.2 refactoring.
"""
import logging
import re
from datetime import datetime
from typing import Dict, List, Any, Optional

# Removed dependency_injector imports - using container-based injection
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.transformer.config.constants import (
    NULL_CONDITIONS, DataCleanerActions, DocketFields, 
    ErrorMessages, DATE_PATTERNS, ProcessingStatus
)


class DocketDataCleaner(AsyncServiceBase):
    """Handles data cleaning, formatting, and validation for docket data."""

    def __init__(self,
                 config: Optional[Dict] ,
                 logger: Optional[logging.Logger] ):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

    async def _execute_action(self, data: Any) -> Any:
        """Execute DocketDataCleaner actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            match action:
                case DataCleanerActions.PROCESS_FILING_DATE.value:
                    self.process_filing_date(action_data)
                    return action_data
                case DataCleanerActions.CLEAN_AND_FLATTEN.value:
                    self.clean_and_flatten(action_data)
                    return action_data
                case DataCleanerActions.CLEAR_PROCESSING_ERRORS.value:
                    self.clear_processing_errors(action_data)
                    return action_data
                case DataCleanerActions.VALIDATE_REQUIRED_FIELDS.value:
                    return self.validate_required_fields(action_data)
                case DataCleanerActions.NORMALIZE_FIELD_NAMES.value:
                    self.normalize_field_names(action_data)
                    return action_data
                case DataCleanerActions.CLEAN_TEXT_FIELDS.value:
                    self.clean_text_fields(action_data)
                    return action_data
                case DataCleanerActions.FORMAT_DATE_TO_YYYYMMDD.value:
                    return self._format_date_to_yyyymmdd(
                        action_data.get('date_str', ''),
                        action_data.get('log_filename', '')
                    )
                case DataCleanerActions.CLEAN_PARTY_LIST.value:
                    return self._clean_party_list(
                        action_data.get('party_data'),
                        action_data.get('party_type', 'unknown')
                    )
                case DataCleanerActions.NORMALIZE_DEFENDANT_NAME.value:
                    return self._normalize_defendant_name(action_data.get('name', ''))
                case DataCleanerActions.COMPREHENSIVE_CLEAN.value:
                    # Perform all cleaning operations in sequence
                    self.process_filing_date(action_data)
                    self.clean_and_flatten(action_data)
                    self.normalize_field_names(action_data)
                    self.clean_text_fields(action_data)
                    self.clear_processing_errors(action_data)
                    return action_data
                case _:
                    raise TransformerServiceError(f"{ErrorMessages.INVALID_ACTION}: '{action}'")

    def process_filing_date(self, data: Dict):
        """
        Process, format, and validate the filing date within the data dictionary.
        Ensures 'date_filed' and 'filing_date' are present and in YYYYMMDD format if possible.
        
        Args:
            data: Data dictionary to process (modified in place)
        """
        if not isinstance(data, dict):
            self.log_warning("Invalid data provided to process_filing_date.")
            return

        # Determine the source of the date
        date_filed_raw = data.get(DocketFields.DATE_FILED, '')
        filing_date_raw = data.get(DocketFields.FILING_DATE, '')
        log_filename = data.get(DocketFields.NEW_FILENAME, data.get(DocketFields.DOCKET_NUM, 'unknown'))

        # Get the 'best' date - prefer date_filed, fallback to filing_date
        raw_date_str = date_filed_raw if date_filed_raw and date_filed_raw not in NULL_CONDITIONS else filing_date_raw

        if not raw_date_str or raw_date_str in NULL_CONDITIONS:
            self.log_debug(f"No valid date found for {log_filename}. Setting defaults.")
            data['date_filed'] = 'NA'
            data['filing_date'] = 'NA'
            return

        # Clean and format the date
        formatted_date = self._format_date_to_yyyymmdd(raw_date_str, log_filename)

        if formatted_date:
            data['date_filed'] = formatted_date
            data['filing_date'] = formatted_date
            self.log_debug(f"Processed filing date for {log_filename}: {raw_date_str} -> {formatted_date}")
        else:
            self.log_warning(f"Could not parse date '{raw_date_str}' for {log_filename}. Setting to original value.")
            data['date_filed'] = str(raw_date_str)
            data['filing_date'] = str(raw_date_str)

    def _format_date_to_yyyymmdd(self, date_str: str, log_filename: str = '') -> Optional[str]:
        """
        Convert various date formats to YYYYMMDD format.
        
        Args:
            date_str: Input date string
            log_filename: Filename for logging context
            
        Returns:
            Formatted date string or None if parsing fails
        """
        if not date_str or not isinstance(date_str, str):
            return None

        date_str = date_str.strip()

        # If already in YYYYMMDD format, validate and return
        if re.match(r'^\d{8}$', date_str):
            try:
                datetime.strptime(date_str, '%Y%m%d')
                return date_str
            except ValueError:
                pass

        # Use date patterns from constants
        for pattern, description in DATE_PATTERNS:
            try:
                parsed_date = datetime.strptime(date_str, pattern)

                # Handle two-digit years (assume 2000s if < 50, 1900s if >= 50)
                if pattern.endswith('%y'):
                    if parsed_date.year < 1950:
                        parsed_date = parsed_date.replace(year=parsed_date.year + 100)

                formatted = parsed_date.strftime('%Y%m%d')
                self.log_debug(f"Parsed date '{date_str}' as {description} -> {formatted} for {log_filename}")
                return formatted

            except ValueError:
                continue

        self.log_warning(f"Could not parse date format: '{date_str}' for {log_filename}")
        return None

    def clean_and_flatten(self, data: Dict):
        """
        Remove duplicates from plaintiff/defendant lists and flatten 'case_info'.
        
        Ensures plaintiff/defendant are lists and removes duplicates while preserving order.
        Also flattens case_info dictionary into the main data dictionary.
        
        Args:
            data: Data dictionary to process (modified in place)
        """
        if not isinstance(data, dict):
            self.log_warning("Invalid data provided to clean_and_flatten.")
            return

        # Ensure plaintiff/defendant are lists and remove duplicates
        for key in ['plaintiff', 'defendant']:
            if key in data:
                cleaned_list = self._clean_party_list(data[key], key)
                data[key] = cleaned_list

        # Handle 'plaintiffs_gpt' specifically for uniqueness
        if 'plaintiffs_gpt' in data:
            if isinstance(data['plaintiffs_gpt'], list):
                # Remove duplicates while preserving order
                seen = set()
                unique_plaintiffs = []
                for p in data['plaintiffs_gpt']:
                    if isinstance(p, str):
                        cleaned = p.strip()
                        if cleaned and cleaned not in seen and cleaned not in NULL_CONDITIONS:
                            unique_plaintiffs.append(cleaned)
                            seen.add(cleaned)
                data['plaintiffs_gpt'] = unique_plaintiffs
            else:
                # Ensure it's a list
                data['plaintiffs_gpt'] = []

        # Flatten case_info if it exists
        self._flatten_case_info(data)

    def _clean_party_list(self, party_data: Any, party_type: str) -> List[str]:
        """
        Clean and deduplicate a list of plaintiff or defendant names.
        
        Args:
            party_data: Raw party data (could be list, string, or other)
            party_type: Type of party ('plaintiff' or 'defendant') for logging
            
        Returns:
            Cleaned list of party names
        """
        if not party_data:
            return []

        # Ensure it's a list
        if isinstance(party_data, str):
            party_list = [party_data]
        elif isinstance(party_data, list):
            party_list = party_data
        else:
            self.log_warning(f"Unexpected {party_type} data type: {type(party_data)}")
            return []

        # Clean and deduplicate
        seen = set()
        cleaned_parties = []

        for party in party_list:
            if not isinstance(party, str):
                continue

            cleaned = party.strip()
            if not cleaned or cleaned in NULL_CONDITIONS:
                continue

            # For defendants, use more aggressive deduplication
            if party_type == 'defendant':
                # Normalize common variations
                normalized = self._normalize_defendant_name(cleaned)
                if normalized not in seen:
                    cleaned_parties.append(cleaned)  # Keep original format
                    seen.add(normalized)
            else:
                # For plaintiffs, simpler deduplication
                if cleaned not in seen:
                    cleaned_parties.append(cleaned)
                    seen.add(cleaned)

        return cleaned_parties

    def _normalize_defendant_name(self, name: str) -> str:
        """
        Normalize defendant name for deduplication purposes.
        
        Args:
            name: Original defendant name
            
        Returns:
            Normalized name for comparison
        """
        # Convert to lowercase and remove extra whitespace
        normalized = re.sub(r'\s+', ' ', name.lower().strip())

        # Remove common suffixes that might vary
        suffixes_to_remove = [
            r'\s+inc\.?$', r'\s+corp\.?$', r'\s+llc\.?$', r'\s+ltd\.?$',
            r'\s+co\.?$', r'\s+company$', r'\s+corporation$'
        ]

        for suffix in suffixes_to_remove:
            normalized = re.sub(suffix, '', normalized)

        return normalized.strip()

    def _flatten_case_info(self, data: Dict):
        """
        Flatten case_info dictionary into main data dictionary.
        
        Args:
            data: Data dictionary to process (modified in place)
        """
        case_info = data.get('case_info')
        if not isinstance(case_info, dict):
            return

        # Fields to flatten from case_info
        flatten_fields = [
            'nature_of_suit', 'jurisdiction', 'cause_of_action',
            'case_flags', 'case_type', 'judge', 'court_name'
        ]

        for field in flatten_fields:
            if field in case_info and field not in data:
                data[field] = case_info[field]
                self.log_debug(f"Flattened case_info.{field} to main data")

        # Remove case_info after flattening to avoid duplication
        if any(field in case_info for field in flatten_fields):
            data.pop('case_info', None)

    def clear_processing_errors(self, data: Dict):
        """
        Clear processing errors and status from data dictionary.
        
        Args:
            data: Data dictionary to clean (modified in place)
        """
        if not isinstance(data, dict):
            return

        error_fields = [
            'processing_errors', 'processing_status', 'last_error_date',
            'error_message', 'processing_error'
        ]

        for field in error_fields:
            if field in data:
                del data[field]

        self.log_debug("Cleared processing error fields from data")

    def validate_required_fields(self, data: Dict) -> List[str]:
        """
        Validate that required fields are present and valid.
        
        Args:
            data: Data dictionary to validate
            
        Returns:
            List of validation error messages
        """
        errors = []

        # Required fields
        required_fields = ['docket_num', 'court_id']

        for field in required_fields:
            if field not in data or data[field] in NULL_CONDITIONS:
                errors.append(f"Missing required field: {field}")

        # Date validation - ensure YYYYMMDD format for database consistency
        date_fields = ['date_filed', 'filing_date']
        for field in date_fields:
            if field in data and data[field] not in NULL_CONDITIONS:
                date_value = str(data[field])
                if not re.match(r'^\d{8}$', date_value):
                    errors.append(f"Invalid date format for {field}: {date_value} (must be YYYYMMDD)")
                    # Attempt to auto-fix MM/DD/YYYY format
                    if re.match(r'^\d{1,2}/\d{1,2}/\d{4}$', date_value):
                        try:
                            from datetime import datetime
                            fixed_date = datetime.strptime(date_value, '%m/%d/%Y').strftime('%Y%m%d')
                            data[field] = fixed_date
                            self.log_warning(f"Auto-fixed {field} from {date_value} to {fixed_date}")
                        except ValueError:
                            errors.append(f"Could not auto-fix invalid date format for {field}: {date_value}")

        # List field validation
        list_fields = ['plaintiff', 'defendant']
        for field in list_fields:
            if field in data and not isinstance(data[field], list):
                errors.append(f"Field {field} should be a list, got {type(data[field])}")

        return errors

    def normalize_field_names(self, data: Dict):
        """
        Normalize field names to consistent format.
        
        Args:
            data: Data dictionary to normalize (modified in place)
        """
        # Field name mappings (alternative_name -> standard_name)
        field_mappings = {
            'CourtId': 'court_id',
            'DocketNum': 'docket_num',
            'DateFiled': 'date_filed',
            'FilingDate': 'filing_date',
            'CaseTitle': 'case_title',
            'LawFirm': 'law_firm',
            'MdlNum': 'mdl_num',
            'S3Link': 's3_link'
        }

        for old_name, new_name in field_mappings.items():
            if old_name in data:
                if new_name not in data:
                    data[new_name] = data.pop(old_name)
                    self.log_debug(f"Normalized field name: {old_name} -> {new_name}")
                else:
                    # Remove the old field to avoid conflicts, keeping the existing new field
                    data.pop(old_name)
                    self.log_debug(f"Removed duplicate field name: {old_name} (keeping existing {new_name})")

    def clean_text_fields(self, data: Dict):
        """
        Clean and normalize text fields.
        
        Args:
            data: Data dictionary to clean (modified in place)
        """
        text_fields = ['case_title', 'allegations', 'summary', 'title']

        for field in text_fields:
            if field in data and isinstance(data[field], str):
                # Clean extra whitespace
                cleaned = re.sub(r'\s+', ' ', data[field].strip())

                # Remove null conditions
                if cleaned not in NULL_CONDITIONS:
                    data[field] = cleaned
                else:
                    data[field] = None
