# /src/services/transformer/components.data_cleaning/cleaner_typed.py
"""
Example of comprehensive type hints for transformer services.

This demonstrates proper type annotations throughout the service,
including method signatures, return types, and internal variables.
"""
import logging
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Union, Tuple, Final, cast

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.transformer.config.constants import (
    NULL_CONDITIONS, DataCleanerActions, DocketFields, 
    ErrorMessages, DATE_PATTERNS, ProcessingStatus
)
from src.transformer.exceptions import (
    ValidationException, ProcessingException, raise_if_validation_failed
)
from src.models.transformer.docket_data import DocketData
from src.models.transformer.processing_models import ValidationResult
from src.transformer.validation_utils import (
    validate_date_format as validate_date_util,
    validate_list_field,
    auto_fix_date_format,
    is_null_or_empty,
    ValidationMixin
)


# Type aliases for clarity
DateString = str  # Format: YYYYMMDD
RawDateString = str  # Any date format
PartyName = str
FieldName = str
ErrorMessage = str


class DocketDataCleanerTyped(AsyncServiceBase):
    """
    Handles data cleaning, formatting, and validation for docket data.
    
    This service provides comprehensive data cleaning operations including:
    - Date formatting and validation
    - Party name deduplication
    - Field normalization
    - Text cleaning
    
    Attributes:
        NULL_CONDITIONS: List of values considered null/empty
        DATE_PATTERNS: Supported date format patterns
    """
    
    # Class-level constants with proper typing
    NULL_CONDITIONS: Final[List[Union[str, None]]] = NULL_CONDITIONS
    DATE_PATTERNS: Final[List[Tuple[str, str]]] = DATE_PATTERNS

    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        logger: Optional[Union[logging.Logger, LoggerProtocol]] = None
    ) -> None:
        """
        Initialize the DocketDataCleaner service.
        
        Args:
            config: Service configuration dictionary
            logger: Logger instance (Logger or LoggerProtocol)
        """
        logger_instance: Union[logging.Logger, LoggerProtocol] = (
            logger if logger else logging.getLogger(__name__)
        )
        super().__init__(logger_instance, config or {})

    async def _execute_action(self, data: Any) -> Any:
        """
        Execute DocketDataCleaner actions.
        
        Args:
            data: Action request data containing 'action' and 'data' fields
            
        Returns:
            Processed data based on the requested action
            
        Raises:
            TransformerServiceError: If action is invalid or processing fails
            ValidationException: If data validation fails
        """
        if not isinstance(data, dict):
            raise ValidationException(
                "Invalid data type for action execution",
                field="data",
                value=type(data).__name__,
                suggestion="Provide a dictionary with 'action' and 'data' fields"
            )
            
        action: Optional[str] = data.get('action')
        action_data: Dict[str, Any] = data.get('data', {})

        match action:
            case DataCleanerActions.PROCESS_FILING_DATE.value:
                self.process_filing_date(action_data)
                return action_data
            case DataCleanerActions.CLEAN_AND_FLATTEN.value:
                self.clean_and_flatten(action_data)
                return action_data
            case DataCleanerActions.CLEAR_PROCESSING_ERRORS.value:
                self.clear_processing_errors(action_data)
                return action_data
            case DataCleanerActions.VALIDATE_REQUIRED_FIELDS.value:
                errors: List[ErrorMessage] = self.validate_required_fields(action_data)
                return ValidationResult(
                    is_valid=len(errors) == 0,
                    errors=errors,
                    field_validations={field: field not in errors for field in action_data}
                )
            case DataCleanerActions.NORMALIZE_FIELD_NAMES.value:
                self.normalize_field_names(action_data)
                return action_data
            case DataCleanerActions.CLEAN_TEXT_FIELDS.value:
                self.clean_text_fields(action_data)
                return action_data
            case DataCleanerActions.FORMAT_DATE_TO_YYYYMMDD.value:
                date_str: RawDateString = action_data.get('date_str', '')
                log_filename: str = action_data.get('log_filename', '')
                return self._format_date_to_yyyymmdd(date_str, log_filename)
            case DataCleanerActions.CLEAN_PARTY_LIST.value:
                party_data: Any = action_data.get('party_data')
                party_type: str = action_data.get('party_type', 'unknown')
                return self._clean_party_list(party_data, party_type)
            case DataCleanerActions.NORMALIZE_DEFENDANT_NAME.value:
                name: str = action_data.get('name', '')
                return self._normalize_defendant_name(name)
            case DataCleanerActions.COMPREHENSIVE_CLEAN.value:
                # Perform all cleaning operations in sequence
                self.process_filing_date(action_data)
                self.clean_and_flatten(action_data)
                self.normalize_field_names(action_data)
                self.clean_text_fields(action_data)
                self.clear_processing_errors(action_data)
                return action_data
            case _:
                raise TransformerServiceError(
                    f"{ErrorMessages.INVALID_ACTION}: '{action}'"
                )

    def process_filing_date(self, data: Dict[str, Any]) -> None:
        """
        Process, format, and validate the filing date within the data dictionary.
        
        Ensures 'date_filed' and 'filing_date' are present and in YYYYMMDD format.
        Modifies the data dictionary in place.
        
        Args:
            data: Data dictionary to process (modified in place)
            
        Raises:
            ValidationException: If data is not a dictionary
        """
        raise_if_validation_failed(
            isinstance(data, dict),
            "Data must be a dictionary",
            field="data",
            value=type(data).__name__
        )

        # Determine the source of the date
        date_filed_raw: Any = data.get(DocketFields.DATE_FILED, '')
        filing_date_raw: Any = data.get(DocketFields.FILING_DATE, '')
        log_filename: str = cast(
            str,
            data.get(DocketFields.NEW_FILENAME, 
                     data.get(DocketFields.DOCKET_NUM, 'unknown'))
        )

        # Get the 'best' date - prefer date_filed, fallback to filing_date
        raw_date_str: RawDateString = (
            date_filed_raw 
            if date_filed_raw and date_filed_raw not in self.NULL_CONDITIONS 
            else filing_date_raw
        )

        if not raw_date_str or raw_date_str in self.NULL_CONDITIONS:
            self.log_debug(f"No valid date found for {log_filename}. Setting defaults.")
            data[DocketFields.DATE_FILED] = 'NA'
            data[DocketFields.FILING_DATE] = 'NA'
            return

        # Clean and format the date
        formatted_date: Optional[DateString] = self._format_date_to_yyyymmdd(
            raw_date_str, log_filename
        )

        if formatted_date:
            data[DocketFields.DATE_FILED] = formatted_date
            data[DocketFields.FILING_DATE] = formatted_date
            self.log_debug(
                f"Processed filing date for {log_filename}: "
                f"{raw_date_str} -> {formatted_date}"
            )
        else:
            self.log_warning(
                f"Could not parse date '{raw_date_str}' for {log_filename}. "
                f"Setting to original value."
            )
            data[DocketFields.DATE_FILED] = str(raw_date_str)
            data[DocketFields.FILING_DATE] = str(raw_date_str)

    def _format_date_to_yyyymmdd(
        self, 
        date_str: RawDateString, 
        log_filename: str = ''
    ) -> Optional[DateString]:
        """
        Convert various date formats to YYYYMMDD format.
        
        Args:
            date_str: Input date string in any supported format
            log_filename: Filename for logging context
            
        Returns:
            Formatted date string in YYYYMMDD format or None if parsing fails
        """
        # Use the validation utility function
        formatted = auto_fix_date_format(date_str)
        
        if formatted:
            self.log_debug(
                f"Parsed date '{date_str}' -> {formatted} for {log_filename}"
            )
            return cast(DateString, formatted)
        else:
            self.log_warning(
                f"Could not parse date format: '{date_str}' for {log_filename}"
            )
            return None

    def clean_and_flatten(self, data: Dict[str, Any]) -> None:
        """
        Remove duplicates from plaintiff/defendant lists and flatten 'case_info'.
        
        Ensures plaintiff/defendant are lists and removes duplicates while
        preserving order. Also flattens case_info dictionary into the main
        data dictionary.
        
        Args:
            data: Data dictionary to process (modified in place)
        """
        if not isinstance(data, dict):
            self.log_warning("Invalid data provided to clean_and_flatten.")
            return

        # Ensure plaintiff/defendant are lists and remove duplicates
        key: str
        for key in ['plaintiff', 'defendant']:
            if key in data:
                cleaned_list: List[PartyName] = self._clean_party_list(
                    data[key], key
                )
                data[key] = cleaned_list

        # Handle 'plaintiffs_gpt' specifically for uniqueness
        if 'plaintiffs_gpt' in data:
            if isinstance(data['plaintiffs_gpt'], list):
                # Remove duplicates while preserving order
                seen: set[PartyName] = set()
                unique_plaintiffs: List[PartyName] = []
                
                p: Any
                for p in data['plaintiffs_gpt']:
                    if isinstance(p, str):
                        cleaned: PartyName = p.strip()
                        if (cleaned and 
                            cleaned not in seen and 
                            cleaned not in self.NULL_CONDITIONS):
                            unique_plaintiffs.append(cleaned)
                            seen.add(cleaned)
                data['plaintiffs_gpt'] = unique_plaintiffs
            else:
                # Ensure it's a list
                data['plaintiffs_gpt'] = []

        # Flatten case_info if it exists
        self._flatten_case_info(data)

    def _clean_party_list(
        self, 
        party_data: Any, 
        party_type: str
    ) -> List[PartyName]:
        """
        Clean and deduplicate a list of plaintiff or defendant names.
        
        Args:
            party_data: Raw party data (could be list, string, or other)
            party_type: Type of party ('plaintiff' or 'defendant') for logging
            
        Returns:
            Cleaned list of party names with duplicates removed
        """
        if not party_data:
            return []

        # Ensure it's a list
        party_list: List[Any]
        if isinstance(party_data, str):
            party_list = [party_data]
        elif isinstance(party_data, list):
            party_list = party_data
        else:
            self.log_warning(
                f"Unexpected {party_type} data type: {type(party_data)}"
            )
            return []

        # Clean and deduplicate
        seen: set[PartyName] = set()
        cleaned_parties: List[PartyName] = []

        party: Any
        for party in party_list:
            if not isinstance(party, str):
                continue

            cleaned: PartyName = party.strip()
            if not cleaned or cleaned in self.NULL_CONDITIONS:
                continue

            # For defendants, use more aggressive deduplication
            if party_type == 'defendant':
                # Normalize common variations
                normalized: PartyName = self._normalize_defendant_name(cleaned)
                if normalized not in seen:
                    cleaned_parties.append(cleaned)  # Keep original format
                    seen.add(normalized)
            else:
                # For plaintiffs, simpler deduplication
                if cleaned not in seen:
                    cleaned_parties.append(cleaned)
                    seen.add(cleaned)

        return cleaned_parties

    def _normalize_defendant_name(self, name: PartyName) -> PartyName:
        """
        Normalize defendant name for deduplication purposes.
        
        Removes common suffixes and normalizes formatting to identify
        duplicate entries with slight variations.
        
        Args:
            name: Original defendant name
            
        Returns:
            Normalized name for comparison
        """
        # Convert to lowercase and remove extra whitespace
        normalized: PartyName = re.sub(r'\s+', ' ', name.lower().strip())

        # Remove common suffixes that might vary
        suffixes_to_remove: List[str] = [
            r'\s+inc\.?$', r'\s+corp\.?$', r'\s+llc\.?$', r'\s+ltd\.?$',
            r'\s+co\.?$', r'\s+company$', r'\s+corporation$'
        ]

        suffix: str
        for suffix in suffixes_to_remove:
            normalized = re.sub(suffix, '', normalized)

        return normalized.strip()

    def _flatten_case_info(self, data: Dict[str, Any]) -> None:
        """
        Flatten case_info dictionary into main data dictionary.
        
        Promotes nested case_info fields to the top level and removes
        the case_info key to avoid duplication.
        
        Args:
            data: Data dictionary to process (modified in place)
        """
        case_info: Any = data.get('case_info')
        if not isinstance(case_info, dict):
            return

        # Fields to flatten from case_info
        flatten_fields: List[FieldName] = [
            'nature_of_suit', 'jurisdiction', 'cause_of_action',
            'case_flags', 'case_type', 'judge', 'court_name'
        ]

        field: FieldName
        for field in flatten_fields:
            if field in case_info and field not in data:
                data[field] = case_info[field]
                self.log_debug(f"Flattened case_info.{field} to main data")

        # Remove case_info after flattening to avoid duplication
        if any(field in case_info for field in flatten_fields):
            data.pop('case_info', None)

    def clear_processing_errors(self, data: Dict[str, Any]) -> None:
        """
        Clear processing errors and status from data dictionary.
        
        Removes all error-related fields to ensure clean data for
        reprocessing attempts.
        
        Args:
            data: Data dictionary to clean (modified in place)
        """
        if not isinstance(data, dict):
            return

        error_fields: List[FieldName] = [
            'processing_errors', 'processing_status', 'last_error_date',
            'error_message', 'processing_error'
        ]

        field: FieldName
        for field in error_fields:
            if field in data:
                del data[field]

        self.log_debug("Cleared processing error fields from data")

    def validate_required_fields(self, data: Dict[str, Any]) -> List[ErrorMessage]:
        """
        Validate that required fields are present and valid.
        
        Checks for required fields and validates their format. Attempts
        to auto-fix common formatting issues where possible.
        
        Args:
            data: Data dictionary to validate
            
        Returns:
            List of validation error messages (empty if all valid)
        """
        errors: List[ErrorMessage] = []

        # Required fields validation
        required_fields: List[FieldName] = ['docket_num', 'court_id']
        for field in required_fields:
            if field not in data or is_null_or_empty(data[field]):
                errors.append(f"Missing required field: {field}")

        # Date validation using utility functions
        date_fields: List[FieldName] = ['date_filed', 'filing_date']
        for field in date_fields:
            if field in data and not is_null_or_empty(data[field]):
                date_value: str = str(data[field])
                
                # Use validation utility
                date_result = validate_date_util(date_value)
                if not date_result.is_valid:
                    errors.append(
                        f"Invalid date format for {field}: {date_value} "
                        f"(must be YYYYMMDD)"
                    )
                    
                    # Attempt auto-fix using utility
                    fixed_date = auto_fix_date_format(date_value)
                    if fixed_date:
                        data[field] = fixed_date
                        self.log_warning(
                            f"Auto-fixed {field} from {date_value} "
                            f"to {fixed_date}"
                        )
                    else:
                        errors.append(
                            f"Could not auto-fix invalid date format "
                            f"for {field}: {date_value}"
                        )

        # List field validation using utility
        list_fields: List[FieldName] = ['plaintiff', 'defendant']
        for field in list_fields:
            if field in data:
                list_result = validate_list_field(field, data[field], str)
                if not list_result.is_valid:
                    errors.append(list_result.error_message)

        return errors

    def normalize_field_names(self, data: Dict[str, Any]) -> None:
        """
        Normalize field names to consistent format.
        
        Converts PascalCase or other variations to snake_case for
        consistency across the system.
        
        Args:
            data: Data dictionary to normalize (modified in place)
        """
        # Field name mappings (alternative_name -> standard_name)
        field_mappings: Dict[str, FieldName] = {
            'CourtId': 'court_id',
            'DocketNum': 'docket_num',
            'DateFiled': 'date_filed',
            'FilingDate': 'filing_date',
            'CaseTitle': 'case_title',
            'LawFirm': 'law_firm',
            'MdlNum': 'mdl_num',
            'S3Link': 's3_link'
        }

        old_name: str
        new_name: FieldName
        for old_name, new_name in field_mappings.items():
            if old_name in data:
                if new_name not in data:
                    data[new_name] = data.pop(old_name)
                    self.log_debug(f"Normalized field name: {old_name} -> {new_name}")
                else:
                    # Remove the old field to avoid conflicts
                    data.pop(old_name)
                    self.log_debug(
                        f"Removed duplicate field name: {old_name} "
                        f"(keeping existing {new_name})"
                    )

    def clean_text_fields(self, data: Dict[str, Any]) -> None:
        """
        Clean and normalize text fields.
        
        Removes extra whitespace, normalizes null values, and ensures
        consistent text formatting.
        
        Args:
            data: Data dictionary to clean (modified in place)
        """
        text_fields: List[FieldName] = [
            'case_title', 'allegations', 'summary', 'title'
        ]

        field: FieldName
        for field in text_fields:
            if field in data and isinstance(data[field], str):
                # Clean extra whitespace
                cleaned: str = re.sub(r'\s+', ' ', data[field].strip())

                # Remove null conditions
                if cleaned not in self.NULL_CONDITIONS:
                    data[field] = cleaned
                else:
                    data[field] = None

    # Additional type-safe helper methods
    
    def to_docket_data(self, data: Dict[str, Any]) -> DocketData:
        """
        Convert cleaned dictionary to typed DocketData model.
        
        Args:
            data: Cleaned data dictionary
            
        Returns:
            Validated DocketData instance
            
        Raises:
            ValidationException: If data fails Pydantic validation
        """
        try:
            return DocketData(**data)
        except Exception as e:
            raise ValidationException(
                f"Failed to create DocketData: {str(e)}",
                value=data
            )

    async def process_batch(
        self, 
        items: List[Dict[str, Any]]
    ) -> Tuple[List[DocketData], List[ValidationException]]:
        """
        Process a batch of docket items with comprehensive cleaning.
        
        Args:
            items: List of raw docket data dictionaries
            
        Returns:
            Tuple of (successfully processed items, validation errors)
        """
        processed: List[DocketData] = []
        errors: List[ValidationException] = []
        
        item: Dict[str, Any]
        for item in items:
            try:
                # Execute comprehensive cleaning
                cleaned_data = await self._execute_action({
                    'action': DataCleanerActions.COMPREHENSIVE_CLEAN.value,
                    'data': item.copy()
                })
                
                # Convert to typed model
                docket_data = self.to_docket_data(cleaned_data)
                processed.append(docket_data)
                
            except ValidationException as e:
                errors.append(e)
                self.log_error(f"Validation failed for item: {e.details}")
            except Exception as e:
                errors.append(ValidationException(
                    f"Processing failed: {str(e)}",
                    value=item
                ))
                
        return processed, errors