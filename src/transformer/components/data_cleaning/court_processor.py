# /src/services/transformer/components.data_cleaning/court_processor.py
"""
# Removed dependency_injector imports - using container-based injection
Court-specific data processing for docket data.

This module handles court-specific data processing and validation extracted from 
docket_processor.py as part of Phase 3.2 refactoring.
"""
import logging
import re
from typing import Dict, List, Optional, Any

from src.transformer.config.constants import NULL_CONDITIONS
from src.transformer.validation_utils import (
    validate_court_id,
    is_null_or_empty,
    ValidationMixin
)

# Removed dependency_injector imports - using container-based injection
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.transformer.exceptions import (
    CourtDataException, ValidationException, ProcessingException,
    wrap_processing_error
)


class CourtDataProcessor(AsyncServiceBase):
    """Handles court-specific data processing and validation for docket data."""



    async def _execute_action(self, data: Any) -> Any:
        """Execute CourtDataProcessor actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'process_nature_of_suit':
                return await self.process_nature_of_suit(action_data)
            elif action == 'process_case_flags':
                return await self.process_case_flags(action_data)
            elif action == 'process_court_info':
                return await self.process_court_info(action_data)
            elif action == 'validate_court_data':
                return self.validate_court_data(action_data)
            elif action == 'get_processing_summary':
                return self.get_court_processing_summary(action_data)
            elif action == 'clean_nature_of_suit':
                return self._clean_nature_of_suit(action_data.get('nature_of_suit'))
            elif action == 'process_case_flags_data':
                return self._process_case_flags_data(action_data.get('case_flags'))
            elif action == 'extract_court_metadata':
                court_id = action_data.get('court_id')
                if court_id:
                    result_data = action_data.copy()
                    self._extract_court_metadata(result_data, court_id)
                    return result_data
                return action_data
        raise ValidationException(
            "Invalid action data provided to CourtDataProcessor",
            field="action",
            value=data.get('action') if isinstance(data, dict) else str(data),
            suggestion="Provide a valid action dictionary with 'action' and 'data' fields"
        )

    def __init__(self,
                 config: Optional[Dict] ,
                 logger: Optional[logging.Logger] ):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

    async def process_nature_of_suit(self, data: Dict) -> bool:
        """
        Process and validate nature of suit information.
        
        Args:
            data: Docket data dictionary (modified in place)
            
        Returns:
            True if nature of suit was processed, False otherwise
        """
        if not isinstance(data, dict):
            self.log_error("Invalid data dictionary provided to process_nature_of_suit")
            return False

        filename = data.get('new_filename', data.get('docket_num', 'unknown'))

        try:
            # Get raw nature of suit data (check both field names)
            nature_of_suit = data.get('nature_of_suit') or data.get('nos')

            if not nature_of_suit or nature_of_suit in NULL_CONDITIONS:
                self.log_debug(f"No nature of suit data for {filename}")
                return False

            # Clean and validate nature of suit
            cleaned_nature = self._clean_nature_of_suit(nature_of_suit)

            if cleaned_nature:
                data['nature_of_suit'] = cleaned_nature

                # Extract additional metadata if available
                self._extract_nature_metadata(data, cleaned_nature)

                self.log_debug(f"Processed nature of suit for {filename}: {cleaned_nature}")
                return True
            else:
                self.log_warning(f"Invalid nature of suit data for {filename}: {nature_of_suit}")
                return False

        except Exception as e:
            court_error = CourtDataException(
                f"Error processing nature of suit for {filename}: {str(e)}",
                court_id=data.get('court_id'),
                docket_num=data.get('docket_num'),
                field="nature_of_suit"
            )
            self.log_error(str(court_error))
            raise court_error

    async def process_case_flags(self, data: Dict) -> bool:
        """
        Process and validate case flags information.
        
        Args:
            data: Docket data dictionary (modified in place)
            
        Returns:
            True if case flags were processed, False otherwise
        """
        if not isinstance(data, dict):
            self.log_error("Invalid data dictionary provided to process_case_flags")
            return False

        filename = data.get('new_filename', data.get('docket_num', 'unknown'))

        try:
            # Get raw case flags data (check both field names)
            case_flags = data.get('case_flags') or data.get('flags')

            if not case_flags or case_flags in NULL_CONDITIONS:
                self.log_debug(f"No case flags data for {filename}")
                return False

            # Process case flags
            processed_flags = self._process_case_flags_data(case_flags)

            if processed_flags:
                data['case_flags'] = processed_flags

                # Note: Removed derived flags to match original transformer behavior
                # self._set_derived_flags(data, processed_flags)

                self.log_debug(f"Processed case flags for {filename}: {processed_flags}")
                return True
            else:
                self.log_warning(f"Invalid case flags data for {filename}: {case_flags}")
                return False

        except Exception as e:
            court_error = CourtDataException(
                f"Error processing case flags for {filename}: {str(e)}",
                court_id=data.get('court_id'),
                docket_num=data.get('docket_num'),
                field="case_flags"
            )
            self.log_error(str(court_error))
            raise court_error

    async def process_court_info(self, data: Dict) -> bool:
        """
        Process and validate court information.
        
        Args:
            data: Docket data dictionary (modified in place)
            
        Returns:
            True if court info was processed, False otherwise
        """
        if not isinstance(data, dict):
            self.log_error("Invalid data dictionary provided to process_court_info")
            return False

        filename = data.get('new_filename', data.get('docket_num', 'unknown'))

        try:
            # Process court ID
            court_id_processed = await self._process_court_id(data, filename)

            # Process jurisdiction
            jurisdiction_processed = await self._process_jurisdiction(data, filename)

            # Process judge information
            judge_processed = await self._process_judge_info(data, filename)

            # Process court name
            court_name_processed = await self._process_court_name(data, filename)

            # Return True if any processing was successful
            return any([court_id_processed, jurisdiction_processed, judge_processed, court_name_processed])

        except Exception as e:
            court_error = CourtDataException(
                f"Error processing court info for {filename}: {str(e)}",
                court_id=data.get('court_id'),
                docket_num=data.get('docket_num'),
                field="court_info"
            )
            self.log_error(str(court_error))
            raise court_error

    def _clean_nature_of_suit(self, nature_of_suit: Any) -> Optional[str]:
        """
        Clean and validate nature of suit data.
        
        Args:
            nature_of_suit: Raw nature of suit data
            
        Returns:
            Cleaned nature of suit string or None
        """
        if not nature_of_suit or nature_of_suit in NULL_CONDITIONS:
            return None

        # Convert to string and clean
        if isinstance(nature_of_suit, (int, float)):
            cleaned = str(nature_of_suit).strip()
        elif isinstance(nature_of_suit, str):
            cleaned = nature_of_suit.strip()
        else:
            return None

        # Remove common prefixes
        cleaned = re.sub(r'^(nature of suit|nos):\s*', '', cleaned, flags=re.IGNORECASE)

        # Validate minimum length
        if len(cleaned) < 2:
            return None

        return cleaned

    def _extract_nature_metadata(self, data: Dict, nature_of_suit: str):
        """
        Extract metadata from nature of suit field.
        
        Args:
            data: Data dictionary to update (modified in place)
            nature_of_suit: Cleaned nature of suit string
        """
        # Extract numeric code if present (e.g., "190 Other Contract")
        code_match = re.match(r'^(\d+)\s+(.+)', nature_of_suit)
        if code_match:
            data['nature_of_suit_code'] = code_match.group(1)
            data['nature_of_suit_description'] = code_match.group(2).strip()

        # Check for specific categories
        nature_lower = nature_of_suit.lower()

        if any(term in nature_lower for term in ['contract', 'breach', 'agreement']):
            data['case_category'] = 'contract'
        elif any(term in nature_lower for term in ['tort', 'negligence', 'liability']):
            data['case_category'] = 'tort'
        elif any(term in nature_lower for term in ['civil rights', 'discrimination']):
            data['case_category'] = 'civil_rights'
        elif any(term in nature_lower for term in ['intellectual property', 'patent', 'trademark']):
            data['case_category'] = 'intellectual_property'

    def _process_case_flags_data(self, case_flags: Any) -> Optional[List[str]]:
        """
        Process case flags data into standardized format.
        
        Args:
            case_flags: Raw case flags data
            
        Returns:
            List of processed flags or None
        """
        if not case_flags or case_flags in NULL_CONDITIONS:
            return None

        # Handle different input types
        if isinstance(case_flags, str):
            # Split by common delimiters
            flags = re.split(r'[,;|]', case_flags)
        elif isinstance(case_flags, list):
            flags = case_flags
        else:
            return None

        # Clean and validate flags
        processed_flags = []
        for flag in flags:
            if isinstance(flag, str):
                cleaned_flag = flag.strip().upper()
                if cleaned_flag and cleaned_flag not in NULL_CONDITIONS:
                    processed_flags.append(cleaned_flag)

        return processed_flags if processed_flags else None

    def _set_derived_flags(self, data: Dict, case_flags: List[str]):
        """
        Set derived boolean flags based on case flags.
        
        Args:
            data: Data dictionary to update (modified in place)
            case_flags: List of case flags
        """
        # Common flag mappings
        flag_mappings = {
            'is_class_action': ['CLASS', 'CLASS_ACTION', 'CA'],
            'is_mdl': ['MDL', 'MULTIDISTRICT'],
            'is_mass_tort': ['MASS_TORT', 'MT'],
            'is_consolidated': ['CONSOLIDATED', 'CONS'],
            'is_sealed': ['SEALED', 'CONFIDENTIAL']
        }

        for derived_flag, flag_patterns in flag_mappings.items():
            data[derived_flag] = any(
                any(pattern in flag for pattern in flag_patterns)
                for flag in case_flags
            )

    async def _process_court_id(self, data: Dict, filename: str) -> bool:
        """
        Process court ID (no validation - court IDs are already correct).
        
        Args:
            data: Data dictionary to process (modified in place)
            filename: Filename for logging context
            
        Returns:
            True if court ID exists
        """
        court_id = data.get('court_id')
        if not court_id or court_id in NULL_CONDITIONS:
            return False

        # Just clean and store - no validation needed
        if isinstance(court_id, str):
            cleaned_court_id = court_id.strip().lower()
            data['court_id'] = cleaned_court_id

            # Extract district and circuit info
            self._extract_court_metadata(data, cleaned_court_id)

            self.log_debug(f"Processed court ID for {filename}: {cleaned_court_id}")
            return True

        return False

    async def _process_jurisdiction(self, data: Dict, filename: str) -> bool:
        """
        Process and validate jurisdiction information.
        
        Args:
            data: Data dictionary to process (modified in place)
            filename: Filename for logging context
            
        Returns:
            True if jurisdiction was processed
        """
        jurisdiction = data.get('jurisdiction')
        if not jurisdiction or jurisdiction in NULL_CONDITIONS:
            return False

        if isinstance(jurisdiction, str):
            cleaned_jurisdiction = jurisdiction.strip()

            # Standardize common jurisdiction values  
            # Order matters - more specific patterns first
            jurisdiction_mappings = [
                ('Federal Question', ['federal question', 'fed question', 'fq']),
                ('Diversity', ['diversity', 'div']),
                ('Federal', ['federal', 'fed', 'us', 'united states']),
                ('State', ['state', 'st'])
            ]

            jurisdiction_lower = cleaned_jurisdiction.lower()
            for standard_jurisdiction, variations in jurisdiction_mappings:
                if any(var in jurisdiction_lower for var in variations):
                    data['jurisdiction'] = standard_jurisdiction
                    self.log_debug(f"Standardized jurisdiction for {filename}: {standard_jurisdiction}")
                    return True

            # Keep original if no mapping found
            data['jurisdiction'] = cleaned_jurisdiction
            return True

        return False

    async def _process_judge_info(self, data: Dict, filename: str) -> bool:
        """
        Process and validate judge information.
        
        Args:
            data: Data dictionary to process (modified in place)
            filename: Filename for logging context
            
        Returns:
            True if judge info was processed
        """
        judge = data.get('judge')
        if not judge or judge in NULL_CONDITIONS:
            return False

        if isinstance(judge, str):
            cleaned_judge = judge.strip()

            # Clean common prefixes/suffixes
            cleaned_judge = re.sub(r'^(judge|hon\.?|honorable)\s+', '', cleaned_judge, flags=re.IGNORECASE)
            cleaned_judge = re.sub(r'\s+(jr\.?|sr\.?|iii?|iv)$', '', cleaned_judge, flags=re.IGNORECASE)

            if cleaned_judge and len(cleaned_judge) > 2:
                data['judge'] = cleaned_judge
                self.log_debug(f"Processed judge info for {filename}: {cleaned_judge}")
                return True

        return False

    async def _process_court_name(self, data: Dict, filename: str) -> bool:
        """
        Process and validate court name.
        
        Args:
            data: Data dictionary to process (modified in place)
            filename: Filename for logging context
            
        Returns:
            True if court name was processed
        """
        court_name = data.get('court_name')
        if not court_name or court_name in NULL_CONDITIONS:
            return False

        if isinstance(court_name, str):
            cleaned_court_name = court_name.strip()

            # Standardize common abbreviations
            abbreviations = {
                'U.S. District Court': ['us district court', 'usdc', 'district court'],
                'U.S. Court of Appeals': ['us court of appeals', 'court of appeals'],
                'U.S. Bankruptcy Court': ['us bankruptcy court', 'bankruptcy court']
            }

            court_name_lower = cleaned_court_name.lower()
            for standard_name, variations in abbreviations.items():
                if any(var in court_name_lower for var in variations):
                    data['court_name'] = standard_name
                    self.log_debug(f"Standardized court name for {filename}: {standard_name}")
                    return True

            # Keep original if no mapping found
            data['court_name'] = cleaned_court_name
            return True

        return False

    def _extract_court_metadata(self, data: Dict, court_id: str):
        """
        Extract metadata from court ID.
        
        Args:
            data: Data dictionary to update (modified in place)
            court_id: Cleaned court ID
        """
        if len(court_id) >= 4:
            # Extract state and district info (e.g., 'cacd' -> 'ca' + 'cd')
            state_code = court_id[:2].upper()
            district_code = court_id[2:].upper()

            data['state_code'] = state_code
            data['district_code'] = district_code

            # Map to full state names if available
            state_mappings = {
                'CA': 'California', 'NY': 'New York', 'TX': 'Texas',
                'FL': 'Florida', 'IL': 'Illinois', 'PA': 'Pennsylvania'
            }

            if state_code in state_mappings:
                data['state_name'] = state_mappings[state_code]

    def validate_court_data(self, data: Dict) -> Dict[str, Any]:
        """
        Validate court data and return validation report.
        
        Args:
            data: Data dictionary to validate
            
        Returns:
            Validation report with status and details
        """
        report = {
            'status': 'success',
            'warnings': [],
            'errors': [],
            'validated_fields': []
        }

        # Check required court fields
        required_fields = ['court_id']
        for field in required_fields:
            if field not in data or is_null_or_empty(data[field]):
                report['errors'].append(f"Missing required field: {field}")
            else:
                report['validated_fields'].append(field)

        # Validate court ID format using utility
        court_id = data.get('court_id')
        if court_id and not is_null_or_empty(court_id):
            court_result = validate_court_id(court_id)
            if not court_result.is_valid:
                report['warnings'].append(court_result.error_message)
                if court_result.suggestion:
                    report['warnings'].append(f"Suggestion: {court_result.suggestion}")

        # Check optional fields
        optional_fields = ['jurisdiction', 'judge', 'court_name', 'nature_of_suit']
        for field in optional_fields:
            if field in data and not is_null_or_empty(data[field]):
                report['validated_fields'].append(field)

        # Set overall status
        if report['errors']:
            report['status'] = 'error'
        elif report['warnings']:
            report['status'] = 'warning'
        elif not report['validated_fields']:
            report['status'] = 'no_data'

        return report

    def get_court_processing_summary(self, data: Dict) -> str:
        """
        Get summary of court data processing results.
        
        Args:
            data: Processed data dictionary
            
        Returns:
            Human-readable summary
        """
        summary_parts = []

        # Court identification
        court_id = data.get('court_id')
        if court_id:
            summary_parts.append(f"Court: {court_id.upper()}")

        # Jurisdiction
        jurisdiction = data.get('jurisdiction')
        if jurisdiction:
            summary_parts.append(f"Jurisdiction: {jurisdiction}")

        # Judge
        judge = data.get('judge')
        if judge:
            summary_parts.append(f"Judge: {judge}")

        # Nature of suit
        nature_of_suit = data.get('nature_of_suit')
        if nature_of_suit:
            summary_parts.append(f"Nature: {nature_of_suit[:50]}...")

        # Case flags
        case_flags = data.get('case_flags')
        if case_flags and isinstance(case_flags, list):
            summary_parts.append(f"{len(case_flags)} flags")

        if summary_parts:
            return f"Court data: {', '.join(summary_parts)}"
        else:
            return "No court data processed"
