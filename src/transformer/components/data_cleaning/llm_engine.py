# /src/services/transformer/components.data_cleaning/llm_engine.py
"""

LLM extraction engine for docket processing.

This module handles all LLM-related processing extracted from docket_processor.py
as part of Phase 3.2 refactoring.
"""
import logging
from typing import Dict, Optional, Any

from src.transformer.config.constants import NULL_CONDITIONS
from src.transformer.validation_utils import (
    is_null_or_empty,
    validate_text_quality,
    validate_list_field,
    ValidationMixin
)


from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.transformer.exceptions import (
    LLMException, ValidationException, DependencyException,
    ProcessingException, wrap_processing_error
)


class DocketLLMEngine(AsyncServiceBase):
    """Handles LLM extraction and processing for docket data."""


    def __init__(self,
                 llm_client: Any =None,
                 afff_calculator: Any =None,
                 config: Optional[Dict] =None,
                 logger: Optional[logging.Logger] =None):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.llm_client = llm_client
        self.afff_calculator = afff_calculator

    async def _execute_action(self, data: Any) -> Any:
        """Execute DocketLLMEngine actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'run_llm_extraction':
                docket_data = action_data.get('docket_data', {})
                pdf_text = action_data.get('pdf_text', '')
                return await self.run_llm_extraction(docket_data, pdf_text)
            elif action == 'prepare_data_for_llm':
                self._prepare_data_for_llm(action_data)
                return action_data
            elif action == 'execute_llm_extraction':
                docket_data = action_data.get('docket_data', {})
                pdf_text = action_data.get('pdf_text', '')
                await self._execute_llm_extraction(docket_data, pdf_text)
                return docket_data
            elif action == 'post_process_llm_results':
                self._post_process_llm_results(action_data)
                return action_data
            elif action == 'validate_party_lists':
                self._validate_party_lists(action_data)
                return action_data
            elif action == 'validate_text_fields':
                self._validate_text_fields(action_data)
                return action_data
            elif action == 'clear_processing_errors':
                self._clear_processing_errors(action_data)
                return action_data
            elif action == 'handle_llm_error':
                error_message = action_data.get('error_message', 'Unknown error')
                docket_data = action_data.get('docket_data', {})
                self._handle_llm_error(docket_data, error_message)
                return docket_data
            elif action == 'validate_llm_results':
                return self.validate_llm_results(action_data)
            elif action == 'get_extraction_summary':
                return self.get_extraction_summary(action_data)
        raise ValidationException(
            "Invalid action data provided to DocketLLMEngine",
            field="action",
            value=data.get('action') if isinstance(data, dict) else str(data),
            suggestion="Provide a valid action dictionary with 'action' and 'data' fields"
        )

    async def run_llm_extraction(self, data: Dict, pdf_text: Optional[str]) -> bool:
        """
        Run the configured LLM client's extraction process on the provided text,
        updating the data dictionary with the results.

        Args:
            data: The docket data dictionary (will be updated in-place)
            pdf_text: The text content extracted from the document

        Returns:
            bool: True if extraction was attempted, False otherwise
        """
        if not isinstance(data, dict):
            self.log_error("Invalid data dictionary provided to run_llm_extraction.")
            return False

        if not pdf_text or not isinstance(pdf_text, str) or len(pdf_text.strip()) < 10:
            text_length = len(pdf_text) if isinstance(pdf_text, str) else 0
            self.log_warning(
                f"Insufficient text content for LLM extraction (length: {text_length}). "
                f"Skipping LLM for docket {data.get('docket_num', 'NA')}."
            )
            return False

        # Prepare data for LLM extraction
        self._prepare_data_for_llm(data)

        try:
            # Run LLM extraction
            await self._execute_llm_extraction(data, pdf_text)

            # Post-process LLM results
            self._post_process_llm_results(data)

            self.log_info(
                f"LLM extraction complete. Data dictionary updated directly by LLM client "
                f"for {data.get('new_filename', 'NA')}."
            )
            return True

        except Exception as e:
            llm_error = LLMException(
                f"Error during LLM extraction: {str(e)}",
                provider=type(self.llm_client).__name__ if self.llm_client else "Unknown",
                model=getattr(self.llm_client, 'model', None) if self.llm_client else None,
                prompt_tokens=None  # Could be extracted from llm_client if available
            )
            self.log_error(str(llm_error))
            self._handle_llm_error(data, str(llm_error))
            raise llm_error

    def _prepare_data_for_llm(self, data: Dict):
        """
        Prepare data dictionary for LLM extraction.
        
        Args:
            data: Data dictionary to prepare (modified in place)
        """
        # Ensure essential keys exist before LLM processing
        data.setdefault('plaintiff', [])
        data.setdefault('defendant', [])
        data.setdefault('original_filename', data.get('new_filename'))

        # Ensure plaintiff/defendant are lists
        if not isinstance(data.get('plaintiff'), list):
            data['plaintiff'] = []
        if not isinstance(data.get('defendant'), list):
            data['defendant'] = []

        # Clear any previous processing errors before LLM
        self._clear_processing_errors(data)

    async def _execute_llm_extraction(self, data: Dict, pdf_text: str):
        """
        Execute the LLM extraction process.
        
        Args:
            data: Data dictionary for extraction (modified in place)
            pdf_text: PDF text content for extraction
        """
        if not self.llm_client:
            raise DependencyException(
                "LLM client not available for extraction",
                dependency_name="llm_client",
                dependency_type="service",
                required_by="DocketLLMEngine"
            )

        # Store the original docket_num if it exists and is valid (13 characters)
        original_docket_num = data.get('docket_num')
        preserve_docket_num = False
        if original_docket_num and isinstance(original_docket_num, str) and len(original_docket_num) == 13:
            preserve_docket_num = True
            self.log_info(f"Preserving existing valid docket_num: {original_docket_num}")

        # Pass the entire data dictionary directly to LLM client
        # The LLM client's extract_all_information method modifies data in-place
        await self.llm_client.extract_all_information(data, pdf_text)

        # Restore the original docket_num if it was valid
        if preserve_docket_num:
            data['docket_num'] = original_docket_num
            self.log_info(f"Restored original docket_num: {original_docket_num}")

    def _post_process_llm_results(self, data: Dict):
        """
        Post-process LLM extraction results.
        
        Args:
            data: Data dictionary to post-process (modified in place)
        """
        # Validate and clean extracted plaintiff/defendant lists
        self._validate_party_lists(data)

        # Ensure extracted text fields are properly formatted
        self._validate_text_fields(data)

        # Handle attorneys_gpt population for HTML-only non-removal cases
        self._handle_attorneys_gpt_population(data)

        # Run AFFF calculation for MDL 2873 cases
        self._run_afff_calculation(data)

        # Set processing metadata
        data['llm_extraction_completed'] = True
        data['llm_extraction_timestamp'] = self._get_current_timestamp()

    def _validate_party_lists(self, data: Dict):
        """
        Validate and clean plaintiff/defendant lists from LLM extraction.
        
        Args:
            data: Data dictionary to validate (modified in place)
        """
        for party_type in ['plaintiff', 'defendant']:
            if party_type in data:
                party_list = data[party_type]

                # Ensure it's a list
                if not isinstance(party_list, list):
                    if isinstance(party_list, str) and party_list.strip():
                        data[party_type] = [party_list.strip()]
                    else:
                        data[party_type] = []
                    continue

                # Clean and validate list items
                cleaned_list = []
                for party in party_list:
                    if isinstance(party, str):
                        cleaned = party.strip()
                        if cleaned and not is_null_or_empty(cleaned):
                            cleaned_list.append(cleaned)

                data[party_type] = cleaned_list

    def _validate_text_fields(self, data: Dict):
        """
        Validate and clean text fields from LLM extraction.
        
        Args:
            data: Data dictionary to validate (modified in place)
        """
        text_fields = ['title', 'summary', 'allegations', 'case_title']

        for field in text_fields:
            if field in data:
                value = data[field]

                if isinstance(value, str):
                    cleaned = value.strip()
                    if cleaned and not is_null_or_empty(cleaned):
                        # Also validate text quality
                        text_result = validate_text_quality(cleaned)
                        if text_result.is_valid:
                            data[field] = cleaned
                        else:
                            data[field] = cleaned  # Keep it but log the warning
                            self.log_warning(f"Text quality issue in {field}: {text_result.error_message}")
                    else:
                        data[field] = None
                elif is_null_or_empty(value):
                    data[field] = None

    def _clear_processing_errors(self, data: Dict):
        """
        Clear processing errors before LLM extraction.
        
        Args:
            data: Data dictionary to clean (modified in place)
        """
        error_fields = ['processing_errors', 'processing_status', 'last_error_date']

        for field in error_fields:
            if field in data:
                del data[field]

    def _handle_llm_error(self, data: Dict, error_message: str):
        """
        Handle LLM extraction errors.
        
        Args:
            data: Data dictionary to update with error info (modified in place)
            error_message: Error message to record
        """
        data['llm_extraction_error'] = error_message
        data['llm_extraction_completed'] = False
        data['llm_extraction_timestamp'] = self._get_current_timestamp()

        self.log_error(f"LLM extraction failed for {data.get('docket_num', 'unknown')}: {error_message}")

    def _get_current_timestamp(self) -> str:
        """Get current timestamp for metadata."""
        from datetime import datetime
        return datetime.now().isoformat()

    def validate_llm_results(self, data: Dict) -> Dict[str, Any]:
        """
        Validate LLM extraction results and return validation report.
        
        Args:
            data: Data dictionary to validate
            
        Returns:
            Validation report with status and details
        """
        report = {
            'status': 'success',
            'warnings': [],
            'errors': [],
            'extracted_fields': []
        }

        # Check if LLM extraction was completed
        if not data.get('llm_extraction_completed'):
            report['status'] = 'error'
            report['errors'].append('LLM extraction not completed')
            return report

        # Check for extraction errors
        if 'llm_extraction_error' in data:
            report['status'] = 'error'
            report['errors'].append(f"LLM extraction error: {data['llm_extraction_error']}")

        # Validate extracted content
        expected_fields = ['plaintiff', 'defendant', 'title', 'summary', 'allegations']

        for field in expected_fields:
            if field in data and not is_null_or_empty(data[field]):
                report['extracted_fields'].append(field)
            else:
                report['warnings'].append(f"Field '{field}' not extracted or empty")

        # Check plaintiff/defendant quality
        for party_type in ['plaintiff', 'defendant']:
            if party_type in data and isinstance(data[party_type], list):
                if len(data[party_type]) == 0:
                    report['warnings'].append(f"No {party_type}s extracted")
                elif len(data[party_type]) > 20:
                    report['warnings'].append(f"Unusually large number of {party_type}s ({len(data[party_type])})")

        # Set overall status based on errors and warnings
        if report['errors']:
            report['status'] = 'error'
        elif len(report['warnings']) > len(expected_fields) // 2:
            report['status'] = 'warning'

        return report

    def _handle_attorneys_gpt_population(self, data: Dict):
        """
        Handle attorneys_gpt population when attorneys_gpt doesn't exist or is empty.
        This applies universally, not just for HTML-only cases.
        
        Args:
            data: Data dictionary to process (modified in place)
        """
        attorneys_gpt = data.get('attorneys_gpt', [])
        
        # Check if attorneys_gpt is missing or empty
        if not attorneys_gpt or len(attorneys_gpt) == 0:
            # Try to populate from attorney field
            attorney_data = data.get('attorney', [])
            
            if attorney_data and isinstance(attorney_data, list) and len(attorney_data) > 0:
                # Convert attorney data to attorneys_gpt format (extract only attorney_name and law_firm)
                attorneys_gpt_list = []
                seen_combinations = set()
                
                for attorney in attorney_data:
                    if isinstance(attorney, dict):
                        attorney_name = attorney.get('attorney_name', '').strip()
                        law_firm = attorney.get('law_firm', '').strip()
                        
                        if attorney_name and law_firm:
                            # Create unique combination key
                            combo_key = f"{attorney_name}|{law_firm}"
                            
                            if combo_key not in seen_combinations:
                                attorneys_gpt_list.append({
                                    'attorney_name': attorney_name,
                                    'law_firm': law_firm
                                })
                                seen_combinations.add(combo_key)
                
                if attorneys_gpt_list:
                    data['attorneys_gpt'] = attorneys_gpt_list
                    self.log_info(
                        f"Populated attorneys_gpt from attorney field: "
                        f"{len(attorneys_gpt_list)} unique attorney/law firm combinations"
                    )

    def _run_afff_calculation(self, data: Dict):
        """
        Run AFFF calculation for MDL 2873 cases during LLM extraction.
        
        Args:
            data: Data dictionary to process (modified in place)
        """
        # Check if this is an AFFF case (MDL 2873)
        mdl_num_str = str(data.get('mdl_num', '')).strip()
        docket_num = data.get('docket_num', 'unknown')
        
        if mdl_num_str == '2873' and self.afff_calculator:
            try:
                self.log_info(f"🧯 AFFF: Starting calculation for MDL 2873 case {docket_num}")
                
                # Log available plaintiff data for debugging
                plaintiff_data = data.get('plaintiff', data.get('Plaintiff', []))
                self.log_info(f"🧯 AFFF: Plaintiff data type: {type(plaintiff_data)}, length/value: {len(plaintiff_data) if isinstance(plaintiff_data, (list, str)) else plaintiff_data}")
                
                # Run the async AFFF calculation
                # Note: We can't use await here since this is not an async method
                # but the afff_calculator has a sync version available
                if hasattr(self.afff_calculator, 'calculate_afff_num_plaintiffs_sync'):
                    result = self.afff_calculator.calculate_afff_num_plaintiffs_sync(data)
                    self.log_info(f"🧯 AFFF: Calculation completed for {docket_num}: num_plaintiffs = {result}")
                    
                    # Verify the result was stored in data
                    stored_value = data.get('num_plaintiffs')
                    self.log_info(f"🧯 AFFF: Stored num_plaintiffs in data: {stored_value}")
                else:
                    self.log_warning(f"🧯 AFFF: Calculator does not have sync method available for {docket_num}")
                    
            except Exception as e:
                self.log_error(f"🧯 AFFF: Error running calculation for {docket_num}: {e}")
        elif mdl_num_str == '2873':
            self.log_warning(f"🧯 AFFF: Case {docket_num} detected as MDL 2873 but no AFFF calculator available")
        else:
            self.log_debug(f"Skipping AFFF calculation for non-2873 case {docket_num} (MDL: {mdl_num_str})")

    def get_extraction_summary(self, data: Dict) -> str:
        """
        Get a summary of LLM extraction results.
        
        Args:
            data: Data dictionary to summarize
            
        Returns:
            Human-readable summary string
        """
        if not data.get('llm_extraction_completed'):
            return "LLM extraction not completed"

        if 'llm_extraction_error' in data:
            return f"LLM extraction failed: {data['llm_extraction_error']}"

        summary_parts = []

        # Count extracted parties
        plaintiff_count = len(data.get('plaintiff', []))
        defendant_count = len(data.get('defendant', []))

        if plaintiff_count > 0:
            summary_parts.append(f"{plaintiff_count} plaintiff(s)")
        if defendant_count > 0:
            summary_parts.append(f"{defendant_count} defendant(s)")

        # Check for AFFF calculation
        num_plaintiffs = data.get('num_plaintiffs')
        if num_plaintiffs and num_plaintiffs != 'NA':
            summary_parts.append(f"AFFF: {num_plaintiffs} plaintiffs calculated")

        # Check for text fields
        text_fields = ['title', 'summary', 'allegations']
        extracted_text_fields = [f for f in text_fields if not is_null_or_empty(data.get(f))]

        if extracted_text_fields:
            summary_parts.append(f"{len(extracted_text_fields)} text fields")

        if summary_parts:
            return f"Extracted: {', '.join(summary_parts)}"
        else:
            return "LLM extraction completed but no significant content extracted"
