import asyncio
import logging
import os
from typing import Any, Dict, List, Optional, Set

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.infrastructure.storage.s3_async import S3AsyncStorage
from src.repositories.pacer_repository import PacerRepository
from src.transformer.components.file.file_handler_core import FileHandlerCore


class UploaderUtils(AsyncServiceBase):
    def __init__(
        self,
        config: dict,
        s3_manager: S3AsyncStorage,
        pacer_db: PacerRepository,
        file_handler: FileHandlerCore,
        logger: logging.Logger | None,
    ):
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config)
        self.s3_manager = s3_manager
        self.pacer_db = pacer_db
        self.file_handler = file_handler
        self.bucket_name = (
            config.get("bucket_name")
            or config.get("aws_s3", {}).get("bucket_name")
            or os.getenv("S3_BUCKET_NAME")
            or os.getenv("AWS_S3_BUCKET")
            or os.getenv("LEXGENIUS_AWS_S3_BUCKET")
            or "lexgenius-dockets"
        )

    async def _execute_action(self, data: Any) -> Any:
        """Execute UploaderUtils actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'upload_batch_to_aws':
                json_paths = action_data.get('json_paths', [])
                upload_types = set(action_data.get('upload_types', []))
                force_s3_upload = action_data.get('force_s3_upload', False)
                return await self.upload_batch_to_aws_async(json_paths, upload_types, force_s3_upload)
            else:
                raise TransformerServiceError(f"Unknown action: {action}")
        else:
            raise TransformerServiceError(f"Invalid data format for UploaderUtils: {type(data)}")
        
        return None

    async def upload_batch_to_aws_async(
        self,
        json_paths: list[str],
        upload_types: set[str],
        force_s3_upload: bool = False,
    ) -> dict[str, list[str]]:
        self.log_info(
            f"Starting batch upload for {len(json_paths)} files. Targets: {upload_types}, Force S3: {force_s3_upload}"
        )
        upload_summary = {"uploaded": [], "skipped": [], "failed": []}
        if not json_paths:
            self.log_warning("No JSON paths provided for upload.")
            return upload_summary
        if not self.bucket_name and "s3" in upload_types:
            self.log_error(
                "Cannot perform S3 upload because S3 bucket name is not configured."
            )
            for json_path in json_paths:
                upload_summary["failed"].append(os.path.basename(json_path))
            return upload_summary

        use_batch_dynamodb = self.config.get("use_batch_dynamodb_uploads", True)
        batch_size = self.config.get("dynamodb_batch_size", 25)

        if use_batch_dynamodb and "dynamodb" in upload_types and len(upload_types) == 1:
            self.log_info(f"Using batch DynamoDB upload for {len(json_paths)} files")
            return await self._upload_batch_dynamodb_only(
                json_paths, batch_size, upload_summary
            )

        max_concurrent_uploads = self.config.get("uploader_workers", 10)
        semaphore = asyncio.Semaphore(max_concurrent_uploads)

        if "s3" in upload_types:
            if (
                hasattr(self.s3_manager, "_client")
                and self.s3_manager._client is not None
            ):
                tasks = []
                for json_path in json_paths:
                    tasks.append(
                        self._upload_single_file_with_semaphore(
                            semaphore,
                            json_path,
                            upload_types,
                            force_s3_upload,
                            upload_summary,
                            self.s3_manager,
                        )
                    )
                await asyncio.gather(*tasks)
            else:
                async with self.s3_manager as s3:
                    tasks = []
                    for json_path in json_paths:
                        tasks.append(
                            self._upload_single_file_with_semaphore(
                                semaphore,
                                json_path,
                                upload_types,
                                force_s3_upload,
                                upload_summary,
                                s3,
                            )
                        )
                    await asyncio.gather(
                        *tasks
                    )
        else:
            tasks = []
            for json_path in json_paths:
                tasks.append(
                    self._upload_single_file_with_semaphore(
                        semaphore,
                        json_path,
                        upload_types,
                        force_s3_upload,
                        upload_summary,
                        None,
                    )
                )
            await asyncio.gather(*tasks)

        self.log_info("--- Upload Batch Summary ---")
        self.log_info(
            f"Successfully uploaded (or verified exists): {len(upload_summary['uploaded'])} files"
        )
        self.log_info(
            f"Skipped (e.g., S3 already exists and force=False): {len(upload_summary['skipped'])} files"
        )
        self.log_info(f"Failed uploads: {len(upload_summary['failed'])} files")
        if upload_summary["failed"]:
            self.log_error(
                f"Failed file list (first 5): {upload_summary['failed'][:5]}"
            )
        self.log_info("--- End Upload Batch Summary ---")

        return upload_summary

    async def _upload_single_file_with_semaphore(
        self,
        semaphore: asyncio.Semaphore,
        json_path: str,
        upload_types: set[str],
        force_s3_upload: bool,
        upload_summary: dict[str, list[str]],
        s3_client=None,
    ):
        async with semaphore:
            await self._upload_single_file_to_aws(
                json_path, upload_types, force_s3_upload, upload_summary, s3_client
            )

    async def _upload_single_file_to_aws(
        self,
        json_path: str,
        upload_types: set[str],
        force_s3_upload: bool,
        upload_summary: dict[str, list[str]],
        s3_client=None,
    ) -> None:
        base_filename = os.path.splitext(os.path.basename(json_path))[0]
        current_dir = os.path.dirname(json_path)
        pdf_path = os.path.join(current_dir, f"{base_filename}.pdf")
        md_path = os.path.join(current_dir, f"{base_filename}.md")

        s3_upload_attempted = False
        s3_upload_succeeded = False
        dynamodb_upload_attempted = False
        dynamodb_upload_succeeded = False
        s3_skipped = False

        file_basename_for_log = os.path.basename(json_path)
        item_data: dict | None = None

        try:
            try:
                item_data = await self.file_handler.load_json_async(json_path)
                if item_data is None:
                    raise ValueError(
                        f"Failed to load JSON data from {json_path}"
                    )
            except Exception as load_err:
                self.log_error(
                    f"Upload Task: Critical - Failed to load JSON data for {file_basename_for_log}, cannot proceed with S3 or DynamoDB. Error: {load_err}"
                )
                upload_summary["failed"].append(file_basename_for_log)
                return

            if "s3" in upload_types:
                s3_upload_attempted = True
                if not self.bucket_name:
                    self.log_error(
                        f"S3 Upload: Skipping {file_basename_for_log} - Bucket name not configured."
                    )
                    s3_upload_succeeded = False
                else:
                    self.log_debug(
                        f"S3 Upload: Starting for {file_basename_for_log} (Force={force_s3_upload})"
                    )

                    s3_key_base = self.file_handler.get_s3_key_base(item_data)
                    if not s3_key_base:
                        self.log_error(
                            f"S3 Upload: Failed to generate S3 key base for {file_basename_for_log}. Check FileHandler logs."
                        )
                        raise ValueError(
                            f"Could not determine S3 key base for {json_path}"
                        )

                    s3_json_key = f"{s3_key_base}.json"

                    if s3_client is None:
                        self.log_error(
                            f"S3 Upload: No S3 client provided for {file_basename_for_log}"
                        )
                        s3_upload_succeeded = False
                    else:
                        s3_exists = False
                        if not force_s3_upload:
                            s3_exists = await s3_client.check_s3_existence_async(
                                self.bucket_name, s3_json_key
                            )
                            self.log_debug(
                                f"S3 Upload: Existence check for {s3_json_key}: {s3_exists}"
                            )

                        if s3_exists and not force_s3_upload:
                            self.log_info(
                                f"S3 Upload: Skipping {file_basename_for_log} - already exists on S3 and force_s3_upload=False."
                            )
                            s3_skipped = True
                            s3_upload_succeeded = True
                        else:
                            if force_s3_upload and s3_exists:
                                self.log_info(
                                    f"S3 Upload: Forcing upload/overwrite for {file_basename_for_log}"
                                )

                            self.log_debug(
                                f"S3 Upload: Uploading JSON {file_basename_for_log} to {s3_json_key}"
                            )
                            json_uploaded = await s3_client.upload_file_async(
                                json_path,
                                self.bucket_name,
                                s3_json_key,
                                force_upload=force_s3_upload,
                            )
                            if not json_uploaded:
                                raise OSError(
                                    f"Failed to upload JSON to S3: {s3_json_key}"
                                )

                            if await asyncio.to_thread(os.path.exists, pdf_path):
                                s3_pdf_key = f"{s3_key_base}.pdf"
                                self.log_info(
                                    f"S3 Upload: Found PDF at {pdf_path}, uploading to {s3_pdf_key} (force={force_s3_upload})"
                                )
                                pdf_uploaded = await s3_client.upload_file_async(
                                    pdf_path,
                                    self.bucket_name,
                                    s3_pdf_key,
                                    force_upload=force_s3_upload,
                                )
                                if not pdf_uploaded:
                                    self.log_error(
                                        f"S3 Upload: Failed to upload PDF {s3_pdf_key} for {file_basename_for_log}"
                                    )
                                else:
                                    self.log_info(
                                        f"S3 Upload: Successfully uploaded PDF {s3_pdf_key}"
                                    )
                            else:
                                self.log_debug(f"S3 Upload: No PDF found at {pdf_path}")

                            if await asyncio.to_thread(os.path.exists, md_path):
                                s3_md_key = f"{s3_key_base}.md"
                                self.log_debug(
                                    f"S3 Upload: Uploading MD {os.path.basename(md_path)} to {s3_md_key}"
                                )
                                md_uploaded = await s3_client.upload_file_async(
                                    md_path,
                                    self.bucket_name,
                                    s3_md_key,
                                    force_upload=force_s3_upload,
                                )
                                if not md_uploaded:
                                    self.log_error(
                                        f"S3 Upload: Failed to upload MD {s3_md_key} for {file_basename_for_log}"
                                    )

                            s3_upload_succeeded = json_uploaded
                            if s3_upload_succeeded:
                                self.log_info(
                                    f"S3 Upload: Successfully uploaded artifacts for {file_basename_for_log}"
                                )

            if "dynamodb" in upload_types:
                is_transfer_source = (
                    item_data and (
                        item_data.get("_save_json_only") == True or
                        item_data.get("is_transfer_source") == True or
                        item_data.get("IsTransferSource") == True
                    )
                )
                if is_transfer_source:
                    self.log_info(
                        f"DynamoDB Upload: Skipping {file_basename_for_log} - transfer source record (_save_json_only or is_transfer_source flag)"
                    )
                    dynamodb_upload_attempted = False
                    dynamodb_upload_succeeded = (
                        True
                    )
                else:
                    dynamodb_upload_attempted = True
                    self.log_debug(
                        f"DynamoDB Upload: Starting for {file_basename_for_log}"
                    )
                    try:
                        if (
                            not item_data
                        ):
                            raise ValueError(
                                "item_data is None, cannot proceed with DynamoDB upload"
                            )

                        self.log_debug(
                            f"DynamoDB Upload: Data for {file_basename_for_log}: {item_data}"
                        )

                        if "num_plaintiffs" in item_data:
                            self.log_info(
                                f"DynamoDB Upload: num_plaintiffs field is present with value: {item_data['num_plaintiffs']}"
                            )
                        else:
                            self.log_warning(
                                f"DynamoDB Upload: num_plaintiffs field is NOT present in item_data. Available keys: {list(item_data.keys())}"
                            )

                        if not self.pacer_db:
                            raise ValueError(
                                "PacerRepository is not available for DynamoDB upload"
                            )

                        # Filter out error fields before uploading
                        filtered_data = self._filter_error_fields(item_data)

                        dynamodb_upload_succeeded = (
                            await self.pacer_db.add_or_update_record(filtered_data)
                        )

                        if dynamodb_upload_succeeded:
                            self.log_info(
                                f"DynamoDB Upload: Successfully triggered add/update for {file_basename_for_log}"
                            )
                        else:
                            filing_date = item_data.get(
                                "filing_date", item_data.get("FilingDate", "unknown")
                            )
                            docket_num = item_data.get(
                                "docket_num", item_data.get("DocketNum", "unknown")
                            )

                            self.log_error(
                                f"DynamoDB Upload: Failed to add/update record for {file_basename_for_log}",
                                {
                                    "filing_date": filing_date,
                                    "docket_num": docket_num,
                                    "file_path": json_path,
                                    "note": "Check PACER Repository logs for detailed AWS error information",
                                },
                            )

                    except Exception as db_err:
                        self.log_error(
                            f"DynamoDB Upload: Error during DB operation for {file_basename_for_log}: {db_err}",
                            exc_info=True,
                        )
                        dynamodb_upload_succeeded = False

            overall_success = True
            if s3_upload_attempted and not s3_upload_succeeded:
                overall_success = False
            if dynamodb_upload_attempted and not dynamodb_upload_succeeded:
                overall_success = False

            targeted_s3 = "s3" in upload_types
            targeted_dynamodb = "dynamodb" in upload_types

            final_status = "unknown"

            if targeted_s3 and targeted_dynamodb:
                if s3_upload_succeeded and dynamodb_upload_succeeded:
                    final_status = "uploaded"
                elif (
                    s3_skipped and dynamodb_upload_succeeded
                ):
                    final_status = "uploaded"
                else:
                    final_status = "failed"
            elif targeted_s3:
                if s3_upload_succeeded:
                    final_status = "uploaded"
                elif s3_skipped:
                    final_status = "skipped"
                else:
                    final_status = "failed"
            elif targeted_dynamodb:
                if dynamodb_upload_succeeded:
                    final_status = "uploaded"
                else:
                    final_status = "failed"
            else:
                self.log_warning(
                    f"Upload called for {file_basename_for_log} but no valid targets in {upload_types}."
                )
                final_status = "skipped"

            match final_status:
                case "uploaded":
                    upload_summary["uploaded"].append(file_basename_for_log)
                case "skipped":
                    upload_summary["skipped"].append(file_basename_for_log)
                case "failed":
                    upload_summary["failed"].append(file_basename_for_log)
                    if s3_upload_attempted and not s3_upload_succeeded:
                        self.log_debug(
                            f"Upload Result: S3 portion failed for {file_basename_for_log}"
                        )
                    if dynamodb_upload_attempted and not dynamodb_upload_succeeded:
                        self.log_debug(
                            f"Upload Result: DynamoDB portion failed for {file_basename_for_log}"
                        )
                case _:
                    self.log_error(
                        f"Upload Task: Unknown final status '{final_status}' for {file_basename_for_log}"
                    )
                    upload_summary["failed"].append(file_basename_for_log)

        except Exception as e:
            self.log_error(
                f"Upload Task Failed: Unhandled error processing file {file_basename_for_log}: {e}"
            )
            if file_basename_for_log not in upload_summary["failed"]:
                upload_summary["failed"].append(file_basename_for_log)

    async def _upload_batch_dynamodb_only(
        self,
        json_paths: list[str],
        batch_size: int,
        upload_summary: dict[str, list[str]],
    ) -> dict[str, list[str]]:
        self.log_info(
            f"Starting batch DynamoDB upload for {len(json_paths)} files in batches of {batch_size}"
        )

        items_to_upload = []
        file_to_item_map = {}

        for json_path in json_paths:
            file_basename = os.path.basename(json_path)
            try:
                item_data = await self.file_handler.load_json_async(json_path)
                if item_data is None:
                    self.log_error(f"Failed to load JSON data from {file_basename}")
                    upload_summary["failed"].append(file_basename)
                    continue

                if "filing_date" not in item_data or "docket_num" not in item_data:
                    self.log_error(f"Missing required fields in {file_basename}")
                    upload_summary["failed"].append(file_basename)
                    continue

                items_to_upload.append(item_data)
                file_to_item_map[json_path] = item_data

            except Exception as e:
                self.log_error(f"Error loading {file_basename}: {e}")
                upload_summary["failed"].append(file_basename)

        if not items_to_upload:
            self.log_warning("No valid items to upload to DynamoDB")
            return upload_summary

        total_batches = (len(items_to_upload) + batch_size - 1) // batch_size
        self.log_info(
            f"Processing {len(items_to_upload)} items in {total_batches} batches"
        )

        for batch_num, i in enumerate(range(0, len(items_to_upload), batch_size)):
            batch = items_to_upload[i : i + batch_size]
            batch_files = list(file_to_item_map.keys())[i : i + batch_size]

            self.log_debug(
                f"Processing batch {batch_num + 1}/{total_batches} with {len(batch)} items"
            )

            try:
                # Filter error fields from batch items
                filtered_batch = [self._filter_error_fields(item) for item in batch]
                result = await self.pacer_db.batch_add_or_update_records(filtered_batch)

                if result["successful"] > 0:
                    successful_files = batch_files[: result["successful"]]
                    for file_path in successful_files:
                        upload_summary["uploaded"].append(os.path.basename(file_path))

                    self.log_info(
                        f"Successfully uploaded {result['successful']}/{len(batch)} items in batch {batch_num + 1}/{total_batches}"
                    )

                if result["failed"] > 0:
                    failed_files = batch_files[result["successful"] :]
                    for file_path in failed_files:
                        upload_summary["failed"].append(os.path.basename(file_path))

                    self.log_error(
                        f"Failed to upload {result['failed']}/{len(batch)} items in batch {batch_num + 1}"
                    )
                    for error in result["errors"]:
                        self.log_error(f"  - {error}")

            except Exception as e:
                self.log_error(f"Failed to upload batch {batch_num + 1}: {e}")
                for file_path in batch_files:
                    upload_summary["failed"].append(os.path.basename(file_path))

        self.log_info("Batch DynamoDB upload complete:")
        self.log_info(
            f"  - Successfully uploaded: {len(upload_summary['uploaded'])} files"
        )
        self.log_info(f"  - Failed: {len(upload_summary['failed'])} files")

        return upload_summary

    def _filter_error_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter out fields that contain '_error' or '_errors' in the key name.
        
        Args:
            data: Original data dictionary
            
        Returns:
            Filtered data dictionary without error fields
        """
        if not isinstance(data, dict):
            return data
            
        filtered_data = {}
        removed_fields = []
        
        for key, value in data.items():
            if '_error' in key or '_errors' in key:
                removed_fields.append(key)
                continue
            filtered_data[key] = value
            
        if removed_fields:
            self.log_debug(f"Filtered out error fields from upload: {removed_fields}")
            
        return filtered_data
