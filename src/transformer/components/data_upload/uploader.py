# /src/services/transformer/components.data_upload/uploader_main.py
import logging
from typing import Any, Dict, List, Optional, Set

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.infrastructure.storage.s3_async import S3AsyncStorage
from src.repositories.pacer_repository import PacerRepository
from src.transformer.components.file.file_handler_core import FileHandlerCore
from .uploader_utils import UploaderUtils


class Uploader(AsyncServiceBase):
    def __init__(
        self,
        config: dict,
        s3_manager: S3AsyncStorage,
        pacer_db: PacerRepository,
        file_handler: FileHandlerCore,
        logger: logging.Logger | None,
    ):
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config)
        self.utils = UploaderUtils(config, s3_manager, pacer_db, file_handler, logger)

    async def _execute_action(self, data: Any) -> Any:
        """Execute Uploader actions."""
        if isinstance(data, dict):
            action = data.get("action")
            action_data = data.get("data", {})

            if action == "upload_batch_to_aws_async":
                json_paths = action_data.get("json_paths", [])
                upload_types = set(action_data.get("upload_types", []))
                force_s3_upload = action_data.get("force_s3_upload", False)
                return await self.upload_batch_to_aws_async(
                    json_paths, upload_types, force_s3_upload
                )
        raise TransformerServiceError("Invalid action data provided to Uploader")

    async def upload_batch_to_aws_async(
        self,
        json_paths: list[str],
        upload_types: set[str],
        force_s3_upload: bool = False,
    ) -> dict[str, list[str]]:
        """
        Uploads a batch of JSON files (and associated PDF/MD) asynchronously
        to specified AWS services (S3, DynamoDB).
        """
        return await self.utils.upload_batch_to_aws_async(
            json_paths, upload_types, force_s3_upload
        )
