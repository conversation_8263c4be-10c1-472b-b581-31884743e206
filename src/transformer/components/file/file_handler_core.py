import asyncio
import json
import logging
import os
import shutil
from typing import Dict, Any, Optional, List

import aiofiles
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.utils.file_utils import try_remove


class FileHandlerCore(AsyncServiceBase):
    """Manages core file operations like load and save."""

    def __init__(self,
                 logger: LoggerProtocol,
                 config: Dict,
                 s3_manager: Optional[Any]):
        super().__init__(logger, config or {})
        if s3_manager is None:
            self.log_warning("S3 manager not properly injected - S3 operations will be disabled")
        self.s3_manager = s3_manager
        
        # Initialize download_dir from config
        self.download_dir = self._determine_download_dir()

    async def _execute_action(self, data: Any) -> Any:
        """Execute FileHandlerCore actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'load_json':
                json_path = action_data.get('json_path')
                return await self.load_json_async(json_path)
            elif action == 'save_json':
                json_path = action_data.get('json_path')
                json_data = action_data.get('json_data')
                return await self.save_json_async(json_data, json_path)
            else:
                raise TransformerServiceError(f"Unknown action: {action}")
        else:
            raise TransformerServiceError(f"Invalid data format for FileHandlerCore: {type(data)}")
        
        return None

    async def load_json_async(self, json_path: str) -> Optional[Dict]:
        if not isinstance(json_path, str) or not json_path.endswith('.json'):
            self.log_error(f"Invalid json_path provided for async load: {json_path}")
            return None
        json_path = os.path.normpath(json_path)
        try:
            exists = await asyncio.to_thread(os.path.isfile, json_path)
            if not exists:
                self.log_error(f"File does not exist (async check): {json_path}")
                return None
        except Exception as e:
            self.log_error(f"Error checking file existence for {json_path}: {e}")
            return None
        try:
            async with aiofiles.open(json_path, mode='r', encoding='utf-8') as f:
                content = await f.read()
                data = json.loads(content)
                return data
        except json.JSONDecodeError as e:
            self.log_error(f"JSON Decode Error loading async {os.path.basename(json_path)}: {str(e)}")
            try:
                async with aiofiles.open(json_path, mode='r', encoding='utf-8') as f_err:
                    content_preview = await f_err.read(500)
                self.log_debug(f"Async content preview (first 500 chars):\n{content_preview}")
            except Exception as read_err:
                self.log_error(f"Could not read file content after async decode error: {read_err}")
            return None
        except FileNotFoundError:
            self.log_error(f"File not found during async open (race condition?): {json_path}")
            return None
        except Exception as e:
            self.log_error(f"Error loading JSON asynchronously {os.path.basename(json_path)}: {str(e)}",
                           exc_info=True)
            return None

    async def save_json_async(self, json_path: str, data: Dict) -> bool:
        if not isinstance(json_path, str) or not json_path.endswith('.json'):
            self.log_error(f"Invalid json_path provided for async save: {json_path}")
            return False
        if not data or not isinstance(data, dict):
            self.log_error(f"Attempted to save empty/invalid data async to {os.path.basename(json_path)}")
            return False
        try:
            loop = asyncio.get_running_loop()
            result = await loop.run_in_executor(None, self.save_json, json_path, data)
            return result
        except Exception as e:
            self.log_error(f"Error in save_json_async wrapper for {os.path.basename(json_path)}: {str(e)}",
                           exc_info=True)
            return False

    def save_json(self, json_path: str, data: Dict) -> bool:
        if not data or not isinstance(data, dict) or len(data) == 0:
            self.log_error(
                f"CRITICAL SAFETY CHECK FAILED: Attempted to save empty/invalid data to {os.path.basename(json_path)}")
            return False
        if not isinstance(json_path, str) or not json_path.endswith('.json'):
            self.log_error(f"Invalid json_path provided for save: {json_path}")
            return False

        json_path = os.path.normpath(json_path)
        target_dir = os.path.dirname(json_path)
        try:
            os.makedirs(target_dir, exist_ok=True)
        except OSError as e:
            self.log_error(f"Failed to create directory {target_dir} for saving {os.path.basename(json_path)}: {e}")
            return False

        backup_path = f"{json_path}.bak"
        temp_path = f"{json_path}.tmp"

        try:
            if os.path.exists(json_path):
                try:
                    shutil.copy2(json_path, backup_path)
                except Exception as backup_error:
                    self.log_warning(f"Could not create backup of {os.path.basename(json_path)}: {backup_error}")

            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4)
                f.flush()
                os.fsync(f.fileno())

            os.replace(temp_path, json_path)

            if os.path.exists(backup_path):
                try_remove(backup_path, self.logger, "backup file")
            return True

        except Exception as e:
            self.log_error(f"Unexpected error during save operation for {os.path.basename(json_path)}: {str(e)}",
                           exc_info=True)
            if os.path.exists(temp_path):
                try_remove(temp_path, self.logger, "temp file after general save error")
            if not os.path.exists(json_path) and os.path.exists(backup_path):
                try:
                    shutil.move(backup_path, json_path)
                except Exception as restore_error:
                    self.log_error(
                        f"Could not restore {os.path.basename(json_path)} from backup: {restore_error}")
            return False

    def _determine_download_dir(self) -> Optional[str]:
        """
        Determine download directory from config using the same logic as other components.
        Should return the path to data/{iso_date}/ directory.
        """
        try:
            # First try to get from config directories
            if self.config and 'directories' in self.config:
                data_dir_config = self.config['directories'].get('data_dir')
                if data_dir_config and os.path.isabs(data_dir_config):
                    self.log_debug(f"FileHandlerCore: Using data_dir from config: '{data_dir_config}'")
                    return data_dir_config
            
            # Fallback: use project_root/data/{iso_date} pattern
            project_root = self.config.get('directories', {}).get('base_dir') if self.config else None
            if not project_root:
                project_root = os.getcwd()  # Ultimate fallback
            
            # Get iso_date from config - this is REQUIRED
            iso_date = None
            if self.config and 'iso_date' in self.config:
                iso_date = self.config['iso_date']
                self.log_debug(f"FileHandlerCore: Using iso_date from config: '{iso_date}'")
            
            if not iso_date:
                self.log_error("FileHandlerCore: iso_date not found in config - this is required for determining download directory")
                return None
            
            download_dir_path = os.path.join(project_root, 'data', iso_date)
            self.log_debug(f"FileHandlerCore: Determined download directory: '{download_dir_path}'")
            return download_dir_path
            
        except Exception as e:
            self.log_error(f"Error determining download directory: {e}")
            return None

    def get_target_docket_directory(self) -> Optional[str]:
        """
        Get the target docket directory (download_dir/dockets).
        """
        if not self.download_dir:
            self.log_error("Download directory not initialized. Cannot determine target docket directory.")
            return None
        
        target_docket_dir = os.path.join(self.download_dir, 'dockets')
        return target_docket_dir

    async def get_json_files(self) -> List[str]:
        """Get all JSON files from the target docket directory."""
        target_dir = self.get_target_docket_directory()
        if not target_dir:
            self.log_error("Could not determine target docket directory")
            return []
            
        if not await asyncio.to_thread(os.path.isdir, target_dir):
            self.log_error(f"Target directory not found: {target_dir}")
            return []

        try:
            import glob
            json_files = await asyncio.to_thread(glob.glob, os.path.join(target_dir, "*.json"))
            self.log_debug(f"Found {len(json_files)} JSON files in {target_dir}")
            return json_files
        except Exception as e:
            self.log_error(f"Error scanning JSON files: {e}")
            return []

    def get_s3_key_base(self, data: Dict[str, Any]) -> Optional[str]:
        """Generate S3 key base using FileHandlerUtils."""
        try:
            from .file_handler_utils import FileHandlerUtils
            utils = FileHandlerUtils(self.logger, self.config)
            return utils.get_s3_key_base(data)
        except Exception as e:
            self.log_error(f"Error generating S3 key base: {e}")
            return None
