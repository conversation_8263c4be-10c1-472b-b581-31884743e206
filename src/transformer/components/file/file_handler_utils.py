import logging
import re
from datetime import datetime
from typing import Dict, Any, Optional

from src.transformer.config.constants import NULL_CONDITIONS


class FileHandlerUtils:
    """Manages file utility operations including filename generation."""



    def __init__(self, logger: logging.Logger, config: Dict):
        self.logger = logger
        self.config = config

    @staticmethod
    def clean_docket_num(docket_num: str) -> str:
        """
        Clean docket number to proper 13-character format: N:NN-XX-NNNNN
        """
        if not docket_num or not isinstance(docket_num, str):
            return docket_num
        pattern = r'^(\d+:\d{2}-[a-zA-Z]{2}-\d{5})'
        match = re.match(pattern, docket_num)
        if match:
            return match.group(1)
        return docket_num

    @staticmethod
    def _normalize_filename(name: str, preserve_case: bool = False) -> str:
        if not isinstance(name, str): return ""
        cleaned_name = re.sub(r'[^\w\s.-]', '', name)
        cleaned_name = re.sub(r'[\s_-]+', '_', cleaned_name)
        cleaned_name = cleaned_name.strip('_')
        max_len = 100
        if len(cleaned_name) > max_len:
            name_base = cleaned_name[:max_len]
            last_underscore = name_base.rfind('_')
            if last_underscore > max_len / 2:
                cleaned_name = name_base[:last_underscore]
            else:
                cleaned_name = name_base
        if preserve_case:
            return cleaned_name
        else:
            return cleaned_name.lower()

    def create_filename(self, data: Dict[str, Any]) -> str:
        if not isinstance(data, dict):
            self.logger.error("Input to create_filename must be a dictionary")
            raise TypeError("Input must be a dictionary")

        court_id = str(data.get('court_id', '')).strip()
        docket_num = str(data.get('docket_num', '')).strip()
        versus = data.get('versus')
        if not versus or versus in NULL_CONDITIONS:
            versus = data.get('title', '')
        versus = str(versus).strip()

        if not court_id or court_id in NULL_CONDITIONS:
            raise ValueError("Missing/Invalid court_id")
        if not docket_num or docket_num in NULL_CONDITIONS:
            raise ValueError("Missing/Invalid docket_num")
        # Check if we have defendants list first, then fall back to versus field
        defendants = data.get('defendants', [])
        if defendants and isinstance(defendants, list) and len(defendants) > 0:
            # We have a defendants list, try to construct versus from it
            self.logger.debug(f"Found defendants list with {len(defendants)} defendants, constructing versus field")
            if not versus or versus in NULL_CONDITIONS:
                # Try to construct versus from plaintiffs and defendants
                plaintiffs = data.get('plaintiffs', [])
                if plaintiffs and isinstance(plaintiffs, list) and len(plaintiffs) > 0:
                    plaintiff_name = plaintiffs[0].get('name', 'Unknown Plaintiff') if isinstance(plaintiffs[0], dict) else str(plaintiffs[0])
                else:
                    plaintiff_name = "Unknown Plaintiff"
                
                defendant_name = defendants[0].get('name', 'Unknown Defendant') if isinstance(defendants[0], dict) else str(defendants[0])
                versus = f"{plaintiff_name} v. {defendant_name}"
                if len(defendants) > 1:
                    versus += " et al"
                self.logger.info(f"Constructed versus from defendants list: '{versus}'")
        elif not versus or versus in NULL_CONDITIONS:
            self.logger.error("Missing/Invalid 'versus' or 'title' and no defendants list. This should not happen in normal operation.")
            # If this happens, create a minimal fallback without Case_ prefix
            # Extract just the case number part for versus
            case_match = re.search(r'(\d{5})', docket_num)
            if case_match:
                versus = f"CaseNum_{case_match.group(1)}"
            else:
                versus = "NoVersusProvided"

        year_str = "YY"
        colon_match = re.search(r':(\d{2})', docket_num)
        if colon_match:
            year_str = colon_match.group(1)
        else:
            self.logger.warning(f"Could not extract YY (2 digits after colon) from docket_num '{docket_num}'")

        # Extract the case number - could be 3, 4, or 5 digits after cv-
        # Examples: 1:25-cv-354 (3 digits), 1:25-cv-9354 (4 digits), 1:25-cv-09354 (5 digits)
        num_str = "NNNNN"
        
        # Try to find digits after cv- or similar pattern
        case_num_match = re.search(r'-([a-zA-Z]{2})-(\d+)', docket_num)
        if case_num_match:
            case_digits = case_num_match.group(2)
            # Zero-pad to 5 digits
            num_str = case_digits.zfill(5)
            self.logger.debug(f"Extracted and padded case number: {case_digits} -> {num_str}")
        else:
            # Fallback: try to find any sequence of 3-5 digits
            digit_match = re.search(r'(\d{3,5})', docket_num)
            if digit_match:
                num_str = digit_match.group(1).zfill(5)
                self.logger.debug(f"Extracted digits via fallback: {digit_match.group(1)} -> {num_str}")
            else:
                self.logger.warning(f"Could not extract case number from docket_num '{docket_num}', using NNNNN")

        versus_clean = versus.replace('.', '').replace(',', '').replace(' ', '_').replace('/', '_')
        versus_normalized = self._normalize_filename(versus_clean, preserve_case=True)
        filename = f"{court_id}_{year_str}_{num_str}_{versus_normalized}"
        self.logger.info(f"Generated filename: {filename}")
        return filename

    def get_s3_key_base(self, data: Dict[str, Any]) -> Optional[str]:
        iso_date = self.config.get('iso_date')
        if not iso_date:
            self.logger.error("Cannot generate S3 key base: 'iso_date' missing from config.")
            return None

        try:
            base_filename = self.create_filename(data)
            if not base_filename:
                raise ValueError("create_filename returned an empty string")
        except (ValueError, KeyError, TypeError) as e:
            self.logger.error(
                f"Cannot generate S3 key base: Failed to create base filename from provided data. Error: {e}")
            return None

        s3_key_base = f"{iso_date}/dockets/{base_filename}"
        return s3_key_base
