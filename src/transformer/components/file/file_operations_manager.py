# /src/services/transformer/file_operations.py
"""
# Removed dependency_injector imports - using container-based injection

File operations manager for DataTransformer.

This module handles all file-related operations extracted from transformer.py
as part of Phase 3 refactoring.
"""
import asyncio
import logging
import os
import shutil
from typing import Dict, List, Optional, Set, Union, Any

# Removed dependency_injector imports - using container-based injection

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.utils import try_remove
from src.utils.docket_utils import normalize_and_sanitize_docket, extract_docket_components
from .file_handler_core import FileHandlerCore as FileHandler


class FileOperationsManager(AsyncServiceBase):
    """Manages file operations for DataTransformer."""

    def __init__(self,
                 file_handler: FileHandler = None,
                 config: Optional[Dict] = None,
                 logger: Optional[logging.Logger] = None):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.file_handler = file_handler

    async def _execute_action(self, data: Any) -> Any:
        """Execute FileOperationsManager actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'get_files_to_process':
                reprocess_files = action_data.get('reprocess_files', False)
                start_from_incomplete = action_data.get('start_from_incomplete', False)
                skip_files = action_data.get('skip_files', set())
                return await self.get_files_to_process(reprocess_files, start_from_incomplete, skip_files)
            elif action == 'finalize_and_rename_files':
                final_data = action_data.get('final_data', {})
                base_filename = action_data.get('base_filename', '')
                current_json_path = action_data.get('current_json_path', '')
                pdf_path = action_data.get('pdf_path', '')
                md_path = action_data.get('md_path', '')
                zip_path = action_data.get('zip_path', '')
                return await self.finalize_and_rename_files(
                    final_data, base_filename, current_json_path, pdf_path, md_path, zip_path
                )
            elif action == 'perform_cleanup':
                cleanup_data = action_data.get('data_for_cleanup')
                original_zip_path = action_data.get('original_zip_path', '')
                await self.perform_cleanup(cleanup_data, original_zip_path)
                return {'status': 'cleaned'}
            elif action == 'ensure_pdf_available':
                working_data = action_data.get('working_data', {})
                base_filename = action_data.get('base_filename', '')
                current_dir = action_data.get('current_dir', '')
                force_reprocess = action_data.get('force_reprocess', False)
                return await self.ensure_pdf_available(working_data, base_filename, current_dir, force_reprocess)
            elif action == 'ensure_md_available':
                pdf_path = action_data.get('pdf_path', '')
                md_path = action_data.get('md_path', '')
                base_filename = action_data.get('base_filename', '')
                reprocess_md_list = action_data.get('reprocess_md_list', [])
                return await self.ensure_md_available(pdf_path, md_path, base_filename, reprocess_md_list)
            elif action == 'process_specific_file_list':
                reprocess_files = action_data.get('reprocess_files', [])
                normalized_target_dir = action_data.get('normalized_target_dir', '')
                skip_set = action_data.get('skip_set', set())
                return await self._process_specific_file_list(reprocess_files, normalized_target_dir, skip_set)
            elif action == 'process_incomplete_files':
                normalized_target_dir = action_data.get('normalized_target_dir', '')
                skip_set = action_data.get('skip_set', set())
                return await self._process_incomplete_files(normalized_target_dir, skip_set)
            elif action == 'process_all_files':
                normalized_target_dir = action_data.get('normalized_target_dir', '')
                skip_set = action_data.get('skip_set', set())
                return await self._process_all_files(normalized_target_dir, skip_set)
        raise TransformerServiceError("Invalid action data provided to FileOperationsManager")

    async def get_files_to_process(
            self,
            reprocess_files: Union[bool, List[str], Set[str]],
            start_from_incomplete: bool,
            skip_files: Set[str]
    ) -> List[str]:
        """
        Determine the list of JSON file paths to process based on flags.
        
        Args:
            reprocess_files: Files to reprocess (bool, list, or set)
            start_from_incomplete: Whether to start from incomplete files
            skip_files: Set of files to skip
            
        Returns:
            List of file paths to process
        """
        self.log_info("Determining files to process...")
        self.log_debug(
            f"Params: reprocess_files type={type(reprocess_files).__name__} "
            f"({len(reprocess_files) if isinstance(reprocess_files, (list, set)) else reprocess_files}), "
            f"start_from_incomplete={start_from_incomplete}, skip_files count={len(skip_files)}"
        )

        target_docket_dir = self.file_handler.get_target_docket_directory()
        if not target_docket_dir:
            self.log_error("Target docket directory not found. Cannot get files to process.")
            return []

        normalized_target_dir = os.path.normpath(target_docket_dir)
        self.log_info(f"🔍 Target directory path: {normalized_target_dir}")

        # Check if target directory exists
        dir_exists = os.path.exists(normalized_target_dir)
        self.log_info(f"🔍 Directory exists: {dir_exists}")

        if dir_exists:
            try:
                all_items = os.listdir(normalized_target_dir)
                json_files = [f for f in all_items if f.endswith('.json')]
                self.log_info(f"🔍 Total items in directory: {len(all_items)}")
                self.log_info(f"🔍 JSON files found: {len(json_files)}")
                if len(json_files) > 0:
                    self.log_info(f"🔍 First few JSON files: {json_files[:5]}")
            except OSError as e:
                self.log_error(f"🔍 Error reading directory: {e}")

        self.log_debug(f"Normalized target directory: {normalized_target_dir}")

        files_to_process = []
        skip_set = set(os.path.basename(f) for f in skip_files) if skip_files else set()
        self.log_debug(f"Skip set (basenames): {skip_set}")

        # Strategy 1: Specific List Provided (Non-Empty)
        if isinstance(reprocess_files, (list, set)) and reprocess_files:
            self.log_info(f"🔍 Using Strategy 1: Specific file list ({len(reprocess_files)} files)")
            files_to_process = await self._process_specific_file_list(
                reprocess_files, normalized_target_dir, skip_set
            )

        # Strategy 2: Incomplete Files Mode
        elif start_from_incomplete:
            self.log_info("🔍 Using Strategy 2: Incomplete files mode")
            files_to_process = await self._process_incomplete_files(
                normalized_target_dir, skip_set
            )

        # Strategy 3: When reprocess_files is False - only process incomplete files
        elif reprocess_files is False:
            self.log_info("🔍 Using Strategy 3: reprocess_files=False - processing only incomplete files")
            files_to_process = await self._process_incomplete_files(
                normalized_target_dir, skip_set
            )

        # Strategy 4: All Files Mode (when reprocess_files is True or other)
        else:
            self.log_info("🔍 Using Strategy 4: All files mode (reprocess_files=True or other)")
            files_to_process = await self._process_all_files(
                normalized_target_dir, skip_set
            )

        self.log_info(f"Final file list contains {len(files_to_process)} files to process.")
        return files_to_process

    async def _process_specific_file_list(
            self,
            reprocess_files: Union[List[str], Set[str]],
            normalized_target_dir: str,
            skip_set: Set[str]
    ) -> List[str]:
        """Process specific list of files provided."""
        self.log_info(f"Processing specific list of {len(reprocess_files)} files provided via 'reprocess_files'.")
        valid_files_from_list = []

        for file_path_or_name in reprocess_files:
            if not isinstance(file_path_or_name, str):
                self.log_warning(f"Skipping non-string item in reprocess_files list: {file_path_or_name}")
                continue

            # Check if it's just a filename (basename) vs a full/relative path
            if os.path.dirname(file_path_or_name) == "":
                # It's just a filename - construct path relative to target directory
                normalized_path = os.path.normpath(os.path.join(normalized_target_dir, file_path_or_name))
            else:
                # It's a full or relative path - use absolute path
                abs_path_input = os.path.abspath(file_path_or_name)
                normalized_path = os.path.normpath(abs_path_input)

            base_name = os.path.basename(normalized_path)
            self.log_debug(
                f"Checking file from list: '{file_path_or_name}' -> "
                f"Normalized path: '{normalized_path}', Basename: '{base_name}'"
            )

            if base_name in skip_set:
                self.log_debug(f"Skipping specified file due to skip_files list: {base_name}")
                continue

            if await asyncio.to_thread(os.path.exists, normalized_path):
                file_dir_normalized = os.path.normpath(os.path.dirname(normalized_path))
                if file_dir_normalized == normalized_target_dir:
                    valid_files_from_list.append(normalized_path)
                    self.log_debug(f"  -> Found and validated: {normalized_path}")
                else:
                    self.log_warning(
                        f"File '{normalized_path}' from reprocess_files list exists but is not in the target directory "
                        f"'{normalized_target_dir}'. Skipping."
                    )
            else:
                self.log_warning(f"File '{normalized_path}' from reprocess_files list does not exist. Skipping.")

        return valid_files_from_list

    async def _process_incomplete_files(self, normalized_target_dir: str, skip_set: Set[str]) -> List[str]:
        """Process incomplete files mode."""
        self.log_info("Processing incomplete files mode - finding JSON files without corresponding ZIP files.")

        try:
            all_files = await asyncio.to_thread(os.listdir, normalized_target_dir)
        except OSError as e:
            self.log_error(f"Error listing files in {normalized_target_dir}: {e}")
            return []

        json_files = [f for f in all_files if f.endswith('.json')]
        zip_basenames = {os.path.splitext(f)[0] for f in all_files if f.endswith('.zip')}

        incomplete_files = []
        for json_file in json_files:
            json_basename = os.path.splitext(json_file)[0]
            if json_basename not in zip_basenames and json_file not in skip_set:
                full_path = os.path.join(normalized_target_dir, json_file)
                incomplete_files.append(full_path)

        self.log_info(f"Found {len(incomplete_files)} incomplete files (JSON without ZIP).")
        return incomplete_files

    async def _process_all_files(self, normalized_target_dir: str, skip_set: Set[str]) -> List[str]:
        """Process all files mode."""
        self.log_info("Processing all JSON files in target directory.")

        try:
            all_files = await asyncio.to_thread(os.listdir, normalized_target_dir)
        except OSError as e:
            self.log_error(f"Error listing files in {normalized_target_dir}: {e}")
            return []

        all_json_files = []
        for file in all_files:
            if file.endswith('.json') and file not in skip_set:
                full_path = os.path.join(normalized_target_dir, file)
                all_json_files.append(full_path)

        self.log_info(f"Found {len(all_json_files)} JSON files to process.")
        return all_json_files

    async def finalize_and_rename_files(
            self,
            final_data: Dict,
            base_filename: str,
            current_json_path: str,
            pdf_path: str,
            md_path: str,
            zip_path: str
    ) -> Dict:
        """
        Finalize processing by renaming files and updating data.
        
        Args:
            final_data: Final processed data
            base_filename: Base filename for files
            current_json_path: Current JSON file path
            pdf_path: PDF file path
            md_path: Markdown file path
            zip_path: ZIP file path
            
        Returns:
            Updated data dictionary with final filenames
        """

        def _sync_perform_rename_ops(
                final_data_inner: Dict,
                base_filename_inner: str,
                current_json_path_inner: str,
                pdf_path_inner: str,
                md_path_inner: str,
                zip_path_inner: str
        ) -> Dict:
            """Synchronous file rename operations."""
            try:
                # Determine new filename based on data - following format: court_case_title
                court_id = final_data_inner.get('court_id', 'unknown')
                docket_num = final_data_inner.get('docket_num', 'unknown')
                versus = final_data_inner.get('versus', 'unknown')

                # Clean docket number using centralized utilities
                docket_num_clean = normalize_and_sanitize_docket(docket_num)

                # Extract case number from docket (e.g., "3:25-cv-00411" -> "25_00411")
                components = extractcomponents.docket(docket_num)
                if components:
                    case_num = f"{components['year']}_{components['case_number']}"
                else:
                    # Fallback to cleaned docket number
                    case_num = docket_num_clean

                # Sanitize case title from versus field using original transformer logic
                case_title = "Unknown_Case"
                if versus and versus != 'unknown':
                    # Step 1: Initial cleaning (remove periods/commas, replace spaces/slashes with underscores)
                    title = versus.replace('.', '').replace(',', '').replace(' ', '_').replace('/', '_')

                    # Step 2: Normalization - remove special characters except letters, numbers, underscore, hyphen, period
                    import re
                    # Allow letters, numbers, underscore, hyphen, period, space (will be replaced)
                    title = re.sub(r'[^\w\s.-]', '', title)
                    # Replace one or more whitespace/hyphen/underscore chars with a single underscore
                    title = re.sub(r'[\s_-]+', '_', title)
                    # Remove leading/trailing underscores
                    title = title.strip('_')

                    # Step 3: Truncation with smart handling
                    max_len = 100
                    if len(title) > max_len:
                        title_base = title[:max_len]
                        last_underscore = title_base.rfind('_')
                        if last_underscore > max_len / 2:
                            title = title_base[:last_underscore]
                        else:
                            title = title_base

                    case_title = title if title else "Unknown_Case"

                new_filename = f"{court_id}_{case_num}_{case_title}"

                # Paths for new files
                current_dir = os.path.dirname(current_json_path_inner)
                new_json_path = os.path.join(current_dir, f"{new_filename}.json")
                new_pdf_path = os.path.join(current_dir, f"{new_filename}.pdf")
                new_md_path = os.path.join(current_dir, f"{new_filename}.md")
                new_zip_path = os.path.join(current_dir, f"{new_filename}.zip")

                # Perform renames
                if os.path.exists(current_json_path_inner) and current_json_path_inner != new_json_path:
                    shutil.move(current_json_path_inner, new_json_path)
                    self.log_debug(f"Renamed JSON: {base_filename_inner}.json -> {new_filename}.json")

                if os.path.exists(pdf_path_inner) and pdf_path_inner != new_pdf_path:
                    shutil.move(pdf_path_inner, new_pdf_path)
                    self.log_debug(f"Renamed PDF: {base_filename_inner}.pdf -> {new_filename}.pdf")

                if os.path.exists(md_path_inner) and md_path_inner != new_md_path:
                    shutil.move(md_path_inner, new_md_path)
                    self.log_debug(f"Renamed MD: {base_filename_inner}.md -> {new_filename}.md")

                if os.path.exists(zip_path_inner) and zip_path_inner != new_zip_path:
                    shutil.move(zip_path_inner, new_zip_path)
                    self.log_debug(f"Renamed ZIP: {base_filename_inner}.zip -> {new_filename}.zip")

                # Update data with new filename and added_on date
                final_data_inner['new_filename'] = new_filename
                final_data_inner['original_filename'] = base_filename_inner

                # Set added_on date from config (required for DynamoDB upload)
                current_added_on = final_data_inner.get('added_on')
                skip_values = ['00000000', '0', False]
                if current_added_on in skip_values:
                    # Preserve manual skip flags - do NOT overwrite
                    self.log_info(
                        f"Preserving manual skip flag: added_on='{current_added_on}' for {base_filename_inner}")
                else:
                    # Set added_on from config's iso_date
                    iso_date = self.file_handler.config.get('iso_date')
                    self.log_debug(f"Config iso_date: {iso_date}, filing_date: {final_data_inner.get('filing_date')}")

                    if not iso_date or not isinstance(iso_date, str) or len(iso_date) != 8 or not iso_date.isdigit():
                        self.log_error(
                            f"Invalid or missing iso_date ('{iso_date}') in config for {base_filename_inner}. Using fallback.")
                        iso_date = "00000000"  # Use invalid date as fallback

                    # Store the previous added_on value before updating
                    previous_added_on = current_added_on
                    final_data_inner['added_on'] = iso_date
                    self.log_info(f"Set added_on = {iso_date} for {base_filename_inner}")

                    # Only warn if there's a potential data integrity issue
                    filing_date = final_data_inner.get('filing_date')
                    if filing_date and filing_date == iso_date and previous_added_on and previous_added_on != iso_date:
                        # Only warn if added_on is being changed FROM a different value TO match filing_date
                        self.log_warning(
                            f"⚠️ WARNING: added_on is being changed from '{previous_added_on}' to '{iso_date}' which matches filing_date for {base_filename_inner}")

                    # No need for the "CRITICAL BUG" check since we're intentionally setting added_on to iso_date

                return final_data_inner

            except Exception as e:
                self.log_error(f"Error during file rename operations: {e}")
                # Ensure we have the filename fields even on error
                final_data_inner['new_filename'] = base_filename_inner
                final_data_inner['original_filename'] = base_filename_inner

                # Still need to set added_on even on error
                current_added_on = final_data_inner.get('added_on')
                skip_values = ['00000000', '0', False]
                if current_added_on not in skip_values:
                    iso_date = self.file_handler.config.get('iso_date', '00000000')
                    final_data_inner['added_on'] = iso_date

                return final_data_inner

        # Run rename operations in thread to avoid blocking
        updated_data = await asyncio.to_thread(
            _sync_perform_rename_ops,
            final_data, base_filename, current_json_path, pdf_path, md_path, zip_path
        )

        return updated_data

    async def perform_cleanup(self, data_for_cleanup: Optional[Dict], original_zip_path: str):
        """
        Perform cleanup operations after processing.
        
        Args:
            data_for_cleanup: Data dictionary for cleanup context
            original_zip_path: Path to original ZIP file
        """
        try:
            # Clean up extracted ZIP directory if it exists
            if original_zip_path and await asyncio.to_thread(os.path.exists, original_zip_path):
                zip_dir = os.path.splitext(original_zip_path)[0]
                if await asyncio.to_thread(os.path.isdir, zip_dir):
                    await asyncio.to_thread(shutil.rmtree, zip_dir, ignore_errors=True)
                    self.log_debug(f"Cleaned up extracted directory: {zip_dir}")

            # Remove temporary files
            if data_for_cleanup:
                temp_files = data_for_cleanup.get('temp_files', [])
                for temp_file in temp_files:
                    await asyncio.to_thread(try_remove, temp_file)

        except Exception as e:
            self.log_warning(f"Error during cleanup: {e}")

    async def ensure_pdf_available(
            self,
            working_data: Dict,
            base_filename: str,
            current_dir: str,
            force_reprocess: bool = False
    ) -> Optional[str]:
        """
        Ensure PDF file is available for processing.
        
        Args:
            working_data: Working data dictionary
            base_filename: Base filename
            current_dir: Current directory
            force_reprocess: Whether to force reprocessing
            
        Returns:
            Path to PDF file if available, None otherwise
        """
        pdf_path = os.path.join(current_dir, f"{base_filename}.pdf")

        self.log_debug(f"[{base_filename}] Looking for PDF: {pdf_path}")

        # Check if PDF already exists
        if await asyncio.to_thread(os.path.exists, pdf_path):
            self.log_debug(f"PDF found: {pdf_path}")
            return pdf_path

        # Try alternative naming patterns - use comprehensive character substitutions
        self.log_debug(f"[{base_filename}] Trying alternative PDF naming patterns...")

        # Try common character variations
        alt_patterns = []

        # Hyphen vs underscore substitution
        if '-' in base_filename:
            alt_patterns.append(base_filename.replace('-', '_'))
        if '_' in base_filename:
            alt_patterns.append(base_filename.replace('_', '-'))

        # Try removing special characters that might have been cleaned differently
        import re
        # Create version with all special chars removed except letters, numbers, underscores
        cleaned_version = re.sub(r'[^\w]', '_', base_filename)
        cleaned_version = re.sub(r'_+', '_', cleaned_version).strip('_')
        if cleaned_version != base_filename:
            alt_patterns.append(cleaned_version)

        for alt_filename in alt_patterns:
            if alt_filename != base_filename:  # Don't retry the same name
                alt_pdf_path = os.path.join(current_dir, f"{alt_filename}.pdf")
                self.log_debug(f"[{base_filename}] Trying alternative: {alt_pdf_path}")

                if await asyncio.to_thread(os.path.exists, alt_pdf_path):
                    self.log_debug(f"Alternative PDF found: {alt_pdf_path}")
                    return alt_pdf_path

        # Try to extract from ZIP
        zip_path = os.path.join(current_dir, f"{base_filename}.zip")
        if await asyncio.to_thread(os.path.exists, zip_path):
            try:
                from src.utils import extract_from_zip
                extracted_files = await asyncio.to_thread(extract_from_zip, zip_path, current_dir)

                for extracted_file in extracted_files:
                    if extracted_file.endswith('.pdf'):
                        # Rename to expected filename
                        expected_pdf_path = os.path.join(current_dir, f"{base_filename}.pdf")
                        if extracted_file != expected_pdf_path:
                            await asyncio.to_thread(shutil.move, extracted_file, expected_pdf_path)

                        self.log_debug(f"Extracted PDF from ZIP: {expected_pdf_path}")
                        return expected_pdf_path

            except Exception as e:
                self.log_error(f"Error extracting PDF from ZIP {zip_path}: {e}")

        self.log_warning(f"PDF not available for {base_filename}")
        return None

    async def ensure_md_available(
            self,
            pdf_path: str,
            md_path: str,
            base_filename: str,
            reprocess_md_list: List[str] = None
    ) -> Optional[str]:
        """
        Ensure Markdown file is available, extract from PDF if needed.
        
        Args:
            pdf_path: Path to PDF file
            md_path: Path to Markdown file
            base_filename: Base filename for logging
            reprocess_md_list: List of files that should have MD regenerated
            
        Returns:
            Path to Markdown file if available, None otherwise
        """

        def _sync_read_text_file(path: str) -> Optional[str]:
            """Synchronously read text file."""
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return content if content.strip() else None
            except Exception as e:
                self.log_debug(f"Could not read {path}: {e}")
                return None

        reprocess_md_list = reprocess_md_list or []
        should_regenerate_md = base_filename in reprocess_md_list

        # Check if MD file already exists with content
        self.log_debug(f"[{base_filename}] Looking for MD: {md_path}")

        md_exists = await asyncio.to_thread(os.path.exists, md_path)
        if md_exists and not should_regenerate_md:
            existing_content = await asyncio.to_thread(_sync_read_text_file, md_path)
            if existing_content:
                self.log_debug(f"MD file found with content: {md_path}")
                return md_path

        # Try alternative naming patterns - use comprehensive character substitutions
        if not md_exists:
            self.log_debug(f"[{base_filename}] Trying alternative MD naming patterns...")

            # Try common character variations
            alt_patterns = []

            # Hyphen vs underscore substitution
            if '-' in base_filename:
                alt_patterns.append(base_filename.replace('-', '_'))
            if '_' in base_filename:
                alt_patterns.append(base_filename.replace('_', '-'))

            # Try removing special characters that might have been cleaned differently
            import re
            # Create version with all special chars removed except letters, numbers, underscores
            cleaned_version = re.sub(r'[^\w]', '_', base_filename)
            cleaned_version = re.sub(r'_+', '_', cleaned_version).strip('_')
            if cleaned_version != base_filename:
                alt_patterns.append(cleaned_version)

            for alt_filename in alt_patterns:
                if alt_filename != base_filename:  # Don't retry the same name
                    alt_md_path = os.path.join(os.path.dirname(md_path), f"{alt_filename}.md")
                    self.log_debug(f"[{base_filename}] Trying alternative: {alt_md_path}")

                    alt_md_exists = await asyncio.to_thread(os.path.exists, alt_md_path)
                    if alt_md_exists and not should_regenerate_md:
                        existing_content = await asyncio.to_thread(_sync_read_text_file, alt_md_path)
                        if existing_content:
                            self.log_debug(f"Alternative MD file found with content: {alt_md_path}")
                            return alt_md_path

        # Extract MD from PDF if needed (MD doesn't exist OR should regenerate)
        if pdf_path and await asyncio.to_thread(os.path.exists, pdf_path):
            if not md_exists:
                self.log_debug(f"MD file doesn't exist, extracting from PDF: {base_filename}")
            elif should_regenerate_md:
                self.log_debug(f"Regenerating MD file (in reprocess_md list): {base_filename}")

            try:
                # This would need to be implemented with actual PDF text extraction
                # For now, we'll just check if the file exists
                self.log_warning(f"PDF text extraction not implemented for {pdf_path}")
                return None

            except Exception as e:
                self.log_error(f"Error extracting text from PDF {pdf_path}: {e}")

        self.log_debug(f"MD file not available for {base_filename}")
        return None
