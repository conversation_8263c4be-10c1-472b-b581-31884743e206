# /src/services/transformer/components.file/docket_file_manager.py
import asyncio
import glob
import json
import logging
import os
from typing import Union, List, Set, Optional, Dict, Any

# Removed dependency_injector imports - using container-based injection
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.utils import try_remove


class DocketFileManager(AsyncServiceBase):
    def __init__(self,
                 config: Optional[Dict],
                 logger: Optional[logging.Logger],
                 file_handler=None):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.file_handler = file_handler
        # Get download_dir from file_handler if available, otherwise calculate it
        if file_handler and hasattr(file_handler, 'download_dir'):
            self.download_dir = file_handler.download_dir
        else:
            # Fallback: calculate download_dir using the same logic as FileHandler
            self.download_dir = self._determine_download_dir()
            if not self.download_dir:
                self.log_error("CRITICAL: Could not determine download directory for DocketFileManager.")
                raise ValueError("Could not determine download directory for DocketFileManager.")

    async def _execute_action(self, data: Any) -> Any:
        """Execute DocketFileManager actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'process_zip_files':
                self.process_zip_files()
                return {'status': 'completed'}
            elif action == 'check_for_error_key':
                json_path = action_data.get('json_path', '')
                return await self._check_for_error_key(json_path)
            elif action == 'get_filtered_json_files_async':
                files_to_process = action_data.get('files_to_process', False)
                start_from_incomplete = action_data.get('start_from_incomplete', False)
                return await self.get_filtered_json_files_async(files_to_process, start_from_incomplete)
            elif action == 'get_target_directory':
                return self.file_handler.get_target_docket_directory()
            elif action == 'get_download_directory':
                return self.download_dir
            elif action == 'scan_json_files':
                target_dir = action_data.get('target_dir', self.file_handler.get_target_docket_directory())
                return await self._scan_json_files(target_dir)
            elif action == 'recovery_workflow':
                target_dir = action_data.get('target_dir', self.file_handler.get_target_docket_directory())
                return await self._recovery_workflow(target_dir)
            elif action == 'validate_file_structure':
                file_path = action_data.get('file_path', '')
                return await self._validate_file_structure(file_path)
        raise TransformerServiceError("Invalid action data provided to DocketFileManager")

    def process_zip_files(self):
        """Refactored: Process all zip files in the dockets directory."""
        docket_dir = os.path.join(self.download_dir, 'dockets')
        self.log_debug(f"Processing zip files in directory: {docket_dir}")
        zip_filenames = glob.glob(os.path.join(docket_dir, '*.zip'))
        if zip_filenames:
            self.log_info(f"Found {len(zip_filenames)} zip files. Extracting...")
            self.file_handler.process_all_zip_files()
        else:
            self.log_info("No zip files to process.")

    async def _check_for_error_key(self, json_path: str) -> bool:
        """
        Helper method to asynchronously check if a JSON file contains the 'processing_error' key.
        Leverages FileHandler for loading if possible.
        """
        # --- This method remains unchanged ---
        try:
            # Assuming self.file_handler exists and has an async load method
            # If not, implement direct async file reading and JSON parsing here.
            data = await self.file_handler.load_json_async(json_path)
            return data is not None and 'processing_error' in data
        except FileNotFoundError:
            self.log_warning(f"File not found while checking for error key: {json_path}")
            return False
        except json.JSONDecodeError:
            self.log_warning(
                f"Invalid JSON structure in {os.path.basename(json_path)} while checking for error key.")
            return False
        except Exception as e:
            self.log_warning(f"Error reading {os.path.basename(json_path)} to check for error key: {e}")
            return False

    async def get_filtered_json_files_async(self,
                                            files_to_process: Optional[Union[List[str], Set[str], bool]] = False,
                                            start_from_incomplete: bool = False) -> List[str]:
        """
        Identifies JSON files in the target directory based on filtering criteria.
        Includes recovery logic for '.bak' and '.tmp' files.

        Args:
            files_to_process:
                - List/Set[str]: Process only these specific files (full paths).
                - True: Process all standard files found/recovered (subject to start_from_incomplete).
                - False/None: Process standard files found/recovered (subject to start_from_incomplete).
            start_from_incomplete: If True, prioritizes selecting only files with a 'processing_error' key
                                   from the found/recovered files.

        Returns:
            List[str]: A list of absolute paths to the JSON files selected for processing.
        """
        target_dir = self.file_handler.get_target_docket_directory()
        if not await asyncio.to_thread(os.path.isdir, target_dir):
            self.log_error(f"Target directory for filtering not found: {target_dir}")
            return []

        self.log_info(f"Scanning for JSON files (including recovery) in: {target_dir}")
        self.log_debug(
            f"Filtering params: files_to_process type={type(files_to_process).__name__}, start_from_incomplete={start_from_incomplete}")

        initial_candidates = []  # Files found initially (.json, .bak, .tmp)
        recovered_files_map = {}  # Track original names of recovered files base -> recovered_json_path

        # --- Step 1: Glob for all potentially relevant files ---
        try:
            def glob_sync_multiple(patterns):
                all_files = set()
                for pattern in patterns:
                    all_files.update(glob.glob(pattern))
                return list(all_files)

            glob_patterns = [
                os.path.join(target_dir, "*.json"),
                os.path.join(target_dir, "*.json.bak"),
                os.path.join(target_dir, "*.json.tmp")
            ]
            initial_candidates = await asyncio.to_thread(glob_sync_multiple, glob_patterns)
            self.log_debug(f"Initial scan found {len(initial_candidates)} potential files (.json, .bak, .tmp).")

        except Exception as e:
            self.log_error(f"Error during initial glob scan: {e}")
            return []

        # --- Step 2: Perform Recovery ---
        # Group files by their base name (without .json/.bak/.tmp)
        files_by_base = {}
        for f_path in initial_candidates:
            # Robustly get base name: remove .json, then .bak or .tmp if present
            base_name = os.path.basename(f_path)
            if base_name.endswith(".json.bak"):
                base_name = base_name[:-len(".json.bak")]
            elif base_name.endswith(".json.tmp"):
                base_name = base_name[:-len(".json.tmp")]
            elif base_name.endswith(".json"):
                base_name = base_name[:-len(".json")]
            else:
                self.log_warning(f"Skipping unexpected file during recovery grouping: {f_path}")
                continue  # Skip files not matching expected patterns

            if base_name not in files_by_base:
                files_by_base[base_name] = {}
            if f_path.endswith(".json"): files_by_base[base_name]['json'] = f_path
            if f_path.endswith(".json.bak"): files_by_base[base_name]['bak'] = f_path
            if f_path.endswith(".json.tmp"): files_by_base[base_name]['tmp'] = f_path

        # Process each group to recover if necessary
        recovered_or_existing_jsons = []
        for base_name, paths in files_by_base.items():
            target_json_path = os.path.join(target_dir, f"{base_name}.json")
            bak_path = paths.get('bak')
            tmp_path = paths.get('tmp')
            json_path = paths.get('json')

            if json_path:  # If .json exists, prioritize it
                recovered_or_existing_jsons.append(json_path)
                # Clean up potential leftover .bak or .tmp for this base
                if bak_path: await asyncio.to_thread(try_remove, bak_path, self.logger,
                                                     f"redundant backup for existing {base_name}.json")
                if tmp_path: await asyncio.to_thread(try_remove, tmp_path, self.logger,
                                                     f"redundant temp for existing {base_name}.json")
            elif bak_path:  # If no .json, but .bak exists, recover from .bak
                self.log_info(
                    f"[RECOVERY] Found '{os.path.basename(bak_path)}', attempting recovery to '{os.path.basename(target_json_path)}'.")
                try:
                    await asyncio.to_thread(os.rename, bak_path, target_json_path)
                    recovered_or_existing_jsons.append(target_json_path)
                    recovered_files_map[base_name] = target_json_path
                    # Clean up potential leftover .tmp
                    if tmp_path: await asyncio.to_thread(try_remove, tmp_path, self.logger,
                                                         f"redundant temp after recovering {base_name}.json from bak")
                except OSError as e:
                    self.log_error(f"Failed to recover from {os.path.basename(bak_path)}: {e}")
            elif tmp_path:  # If no .json or .bak, but .tmp exists, recover from .tmp
                self.log_info(
                    f"[RECOVERY] Found '{os.path.basename(tmp_path)}', attempting recovery to '{os.path.basename(target_json_path)}'.")
                # Optional: Add validation for .tmp file (e.g., check size > 0) before rename
                try:
                    # Example validation (optional):
                    # tmp_size = await asyncio.to_thread(os.path.getsize, tmp_path)
                    # if tmp_size == 0:
                    #    self.log_warning(f"Temporary file {tmp_path} is empty, removing instead of recovering.")
                    #    await asyncio.to_thread(try_remove, tmp_path, self.logger, "empty temp file")
                    #    continue # Skip to next base name
                    await asyncio.to_thread(os.rename, tmp_path, target_json_path)
                    recovered_or_existing_jsons.append(target_json_path)
                    recovered_files_map[base_name] = target_json_path
                except OSError as e:
                    self.log_error(f"Failed to recover from {os.path.basename(tmp_path)}: {e}")
            # else: No .json, .bak, or .tmp found for this base name - nothing to do

        self.log_info(f"Found/Recovered {len(recovered_or_existing_jsons)} total JSON files after recovery phase.")

        # --- Step 3: Apply Filtering Logic based on parameters ---
        candidate_files = []  # This will hold the files selected by the input parameters

        # --- Filtering Logic based on files_to_process and start_from_incomplete ---
        if isinstance(files_to_process, (list, set)):
            # Filter the recovered/existing list based on the specific input list
            self.log_info(
                f"Filtering found/recovered files based on specific list of {len(files_to_process)} files.")
            requested_files_set = {os.path.normpath(f) for f in files_to_process}
            candidate_files = [f for f in recovered_or_existing_jsons if os.path.normpath(f) in requested_files_set]

            # Check for requested files that weren't found/recovered
            found_set = {os.path.normpath(f) for f in candidate_files}
            missing_requested = requested_files_set - found_set
            if missing_requested:
                self.log_warning(f"Specified files not found or recovered: {list(missing_requested)}")

            # Note: start_from_incomplete is generally ignored when a specific list is given for selection
            if start_from_incomplete:
                self.log_warning(
                    "`start_from_incomplete=True` ignored for file *selection* because a specific file list was provided.")

        elif start_from_incomplete:
            # Filter the recovered/existing list for those containing the error key
            self.log_info("Filtering found/recovered files for incomplete ('processing_error' key).")
            check_tasks = {f: self._check_for_error_key(f) for f in recovered_or_existing_jsons}
            results = await asyncio.gather(*check_tasks.values())
            candidate_files = [f for f, has_error in zip(check_tasks.keys(), results) if has_error]
            self.log_info(
                f"Found {len(candidate_files)} incomplete files out of {len(recovered_or_existing_jsons)} found/recovered JSONs.")

        else:  # files_to_process is True, False, or None - use all recovered/existing
            self.log_info("Selecting all standard found/recovered JSON files.")
            candidate_files = recovered_or_existing_jsons

        # --- Final Filtering: Apply skip_files ---
        if not candidate_files:
            self.log_info("No candidate files found after initial filtering and recovery.")
            return []

        skip_files_set = self.file_handler.get_skip_files()
        if skip_files_set:
            final_files = [f for f in candidate_files if os.path.realpath(f) not in skip_files_set]
            skipped_count = len(candidate_files) - len(final_files)
            if skipped_count > 0:
                self.log_info(f"Excluded {skipped_count} files based on the skip list.")
        else:
            final_files = candidate_files

        self.log_info(f"Final selection: Returning {len(final_files)} JSON files for processing.")
        return final_files

    # def batch_process_pdfs(self, json_files: List[str], config: Dict):
    #     """Refactored: Delegate the batch processing (wrapping existing batch_process_json_files)."""
    #     from .pdf_extractor2 import batch_process_json_files
    #     reprocess_md = config.get('reprocess_md', False)
    #     num_workers = config.get('num_workers', 4)
    #     self.log_info(f"Batch processing {len(json_files)} PDFs with {num_workers} workers")
    #     stats = batch_process_json_files(
    #         json_files,
    #         config,
    #         num_workers=num_workers,
    #         reprocess_md=reprocess_md,
    #         batch_size=10
    #     )
    #     self.log_info(f"Batch processing complete: {stats['successful']} successful, {stats['failed']} failed")
    #
    # async def batch_process_pdfs_async(self, json_files: List[str], config: Dict):
    #     """Asynchronous version of batch processing PDFs."""
    #     # Configure to use tqdm for progress tracking
    #     config['use_tqdm'] = True
    #
    #     # Import the async batch processor
    #     from .mistral_pdf_processor import batch_process_json_files_async
    #     reprocess_md = config.get('reprocess_md', False)
    #     num_workers = config.get('num_workers', 4)
    #     rate_limit = config.get('api_rate_limit', 2)
    #
    #     self.log_info(
    #         f"Async batch processing {len(json_files)} PDFs with {num_workers} workers and API rate limit {rate_limit}"
    #     )
    #
    #     # Process files with progress tracking
    #     stats = await batch_process_json_files_async(
    #         json_files,
    #         config,
    #         num_workers=num_workers,
    #         reprocess_md=reprocess_md,
    #         batch_size=10,
    #         rate_limit=rate_limit
    #     )
    #
    #     return stats

    # TODO: Add pdf_extractor as backup.

    async def _scan_json_files(self, target_dir: str) -> List[str]:
        """Scan for JSON files in target directory."""
        if not await asyncio.to_thread(os.path.isdir, target_dir):
            self.log_error(f"Target directory not found: {target_dir}")
            return []

        try:
            json_files = await asyncio.to_thread(glob.glob, os.path.join(target_dir, "*.json"))
            self.log_debug(f"Found {len(json_files)} JSON files in {target_dir}")
            return json_files
        except Exception as e:
            self.log_error(f"Error scanning JSON files: {e}")
            return []

    async def _recovery_workflow(self, target_dir: str) -> Dict[str, Any]:
        """Perform file recovery workflow."""
        recovery_stats = {'recovered': 0, 'cleaned': 0, 'errors': 0}

        try:
            # Scan for backup and temp files
            bak_files = await asyncio.to_thread(glob.glob, os.path.join(target_dir, "*.json.bak"))
            tmp_files = await asyncio.to_thread(glob.glob, os.path.join(target_dir, "*.json.tmp"))

            # Attempt recovery from backup files
            for bak_file in bak_files:
                base_name = os.path.basename(bak_file)[:-len(".json.bak")]
                json_path = os.path.join(target_dir, f"{base_name}.json")

                if not os.path.exists(json_path):
                    try:
                        await asyncio.to_thread(os.rename, bak_file, json_path)
                        recovery_stats['recovered'] += 1
                        self.log_info(f"Recovered {base_name}.json from backup")
                    except OSError as e:
                        self.log_error(f"Failed to recover {base_name}.json: {e}")
                        recovery_stats['errors'] += 1
                else:
                    await asyncio.to_thread(try_remove, bak_file, self.logger, f"redundant backup for {base_name}.json")
                    recovery_stats['cleaned'] += 1

            # Clean up temp files
            for tmp_file in tmp_files:
                await asyncio.to_thread(try_remove, tmp_file, self.logger, "orphaned temp file")
                recovery_stats['cleaned'] += 1

        except Exception as e:
            self.log_error(f"Error during recovery workflow: {e}")
            recovery_stats['errors'] += 1

        return recovery_stats

    async def _validate_file_structure(self, file_path: str) -> Dict[str, Any]:
        """Validate JSON file structure."""
        validation_result = {'valid': False, 'errors': [], 'size': 0}

        try:
            if not os.path.exists(file_path):
                validation_result['errors'].append("File does not exist")
                return validation_result

            file_size = await asyncio.to_thread(os.path.getsize, file_path)
            validation_result['size'] = file_size

            if file_size == 0:
                validation_result['errors'].append("File is empty")
                return validation_result

            # Try to load JSON to validate structure
            data = await self.file_handler.load_json_async(file_path)
            if data is None:
                validation_result['errors'].append("Invalid JSON structure")
                return validation_result

            validation_result['valid'] = True

        except Exception as e:
            validation_result['errors'].append(f"Validation error: {e}")

        return validation_result

    def _determine_download_dir(self) -> Optional[str]:
        """
        Fallback method to determine download directory using the same logic as FileHandler.
        This should only be used if file_handler is not available.
        """
        project_root = self.config.get('directories', {}).get('base_dir') or \
                       self.config.get('project_root') or \
                       self.config.get('DATA_DIR') or \
                       self.config.get('data_path')

        if not project_root:
            project_root = os.getcwd()
            self.log_warning(
                f"No project_root found in config for DocketFileManager, using current directory: {project_root}")

        iso_date = self.config.get('iso_date')
        if not iso_date:
            self.log_error("CRITICAL: 'iso_date' not found in config for DocketFileManager.")
            return None
        elif not (isinstance(iso_date, str) and len(iso_date) == 8 and iso_date.isdigit()):
            self.log_error(f"CRITICAL: Invalid 'iso_date' format ('{iso_date}') found in config.")
            return None
        else:
            data_dir = self.config.get('data_dir', 'data')
            data_dir_config = self.config.get('DATA_DIR') or self.config.get('data_path')
            
            if data_dir_config and os.path.isabs(data_dir_config):
                download_dir_path = os.path.join(data_dir_config, iso_date)
            else:
                download_dir_path = os.path.join(project_root, data_dir, iso_date)
            
            self.log_debug(f"DocketFileManager: Determined download directory path: '{download_dir_path}'")
            return download_dir_path
