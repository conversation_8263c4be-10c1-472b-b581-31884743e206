# /src/services/transformer/mdl_persistence_manager.py
"""

MDL file and data persistence operations.

This module handles MDL data saving, loading, and file management operations
extracted from mdl_processor.py as part of Phase 3.3 refactoring.
"""
import logging
import os
from typing import Dict, Optional, Any, Tuple

import pandas as pd

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError


class MDLPersistenceManager(AsyncServiceBase):
    """Manages MDL data persistence and file operations."""
    def __init__(self,
                 mdl_path: str =None,
                 config: Optional[Dict] =None,
                 logger: Optional[logging.Logger] =None):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.mdl_path = mdl_path

    async def _execute_action(self, data: Any) -> Any:
        """Execute MDLPersistenceManager actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'load_mdl_litigation_async':
                return await self.load_mdl_litigation_async()
            elif action == 'load_mdl_litigation_sync':
                return self.load_mdl_litigation_sync()
            elif action == 'save_mdl_litigation_async':
                mdl_litigations = action_data.get('mdl_litigations')
                return await self.save_mdl_litigation_async(mdl_litigations)
            elif action == 'save_mdl_litigation_sync':
                mdl_litigations = action_data.get('mdl_litigations')
                self.save_mdl_litigation_sync(mdl_litigations)
                return {'status': 'completed'}
            elif action == 'update_and_save_mdl_entry':
                mdl_number = action_data.get('mdl_number', '')
                litigation_name = action_data.get('litigation_name', '')
                additional_data = action_data.get('additional_data')
                mdl_litigations = action_data.get('mdl_litigations')
                return await self.update_and_save_mdl_entry(mdl_number, litigation_name, additional_data,
                                                            mdl_litigations)
            elif action == 'validate_persistence_data':
                data_to_validate = action_data.get('data')
                return await self.validate_persistence_data(data_to_validate)
            elif action == 'cleanup_old_backups':
                max_backups = action_data.get('max_backups', 10)
                return await self.cleanup_old_backups(max_backups)
            elif action == 'get_file_info':
                return self.get_file_info()
            elif action == 'get_file_info_async':
                return await self.get_file_info_async()
            elif action == 'validate_file_path':
                return self.validate_file_path()
            elif action == 'get_persistence_summary':
                return self.get_persistence_summary()
            elif action == 'create_backup':
                return await self._create_backup_if_needed()
            elif action == 'verify_saved_file':
                original_df = action_data.get('original_df')
                return await self._verify_saved_file(original_df)
            elif action == 'save_dataframe_to_json':
                df = action_data.get('dataframe')
                file_path = action_data.get('file_path', '')
                return await self._save_dataframe_to_json(df, file_path)
            elif action == 'validate_entry_data':
                entry_data = action_data.get('entry_data', {})
                return await self._validate_entry_data(entry_data)
        raise TransformerServiceError("Invalid action data provided to MDLPersistenceManager")

    async def load_mdl_litigation_async(self) -> pd.DataFrame:
        """
        Load MDL litigation data from file asynchronously.
        
        Returns:
            DataFrame containing MDL litigation data
        """
        self.log_info('Loading active MDL Litigation...')

        try:
            if not os.path.exists(self.mdl_path):
                self.log_warning(f"MDL file not found: {self.mdl_path}")
                return self._create_empty_mdl_dataframe()

            # Load the JSON file
            df = pd.read_json(self.mdl_path)

            # Validate and clean the data
            df = await self._validate_and_clean_mdl_data(df)

            self.log_info(f"Successfully loaded {len(df)} MDL litigation entries")
            return df

        except ValueError as e:
            self.log_error(f"Error reading JSON from {self.mdl_path}: {e}")
            return self._create_empty_mdl_dataframe()
        except Exception as e:
            self.log_error(f"Unexpected error loading MDL data: {e}")
            return self._create_empty_mdl_dataframe()

    def load_mdl_litigation_sync(self) -> pd.DataFrame:
        """
        Load MDL litigation data from file synchronously.
        
        This method is deprecated and will be removed in Phase 4.
        Use load_mdl_litigation_async() instead.
        
        Returns:
            DataFrame containing MDL litigation data
        """
        self.log_warning("Using deprecated sync MDL loading method. Please migrate to async version.")

        self.log_info('Loading active MDL Litigation...')

        try:
            if not os.path.exists(self.mdl_path):
                self.log_warning(f"MDL file not found: {self.mdl_path}")
                return self._create_empty_mdl_dataframe()

            df = pd.read_json(self.mdl_path)

            # Validate mdl_num column
            if 'mdl_num' in df.columns:
                df['mdl_num'] = df['mdl_num'].astype(str)
            else:
                self.log_warning(f"Column 'mdl_num' not found in {self.mdl_path}")
                return self._create_empty_mdl_dataframe()

            return df

        except ValueError as e:
            self.log_error(f"Error reading JSON from {self.mdl_path}: {e}")
            return self._create_empty_mdl_dataframe()
        except FileNotFoundError:
            self.log_error(f"MDL lookup file not found: {self.mdl_path}")
            return self._create_empty_mdl_dataframe()

    async def save_mdl_litigation_async(self, mdl_litigations: pd.DataFrame) -> bool:
        """
        Save MDL litigation DataFrame to file asynchronously.
        
        Args:
            mdl_litigations: DataFrame to save
            
        Returns:
            True if save was successful, False otherwise
        """
        if mdl_litigations is None:
            self.log_error("MDL litigation data is None. Cannot save.")
            return False

        if not self.mdl_path:
            self.log_error("MDL save path (mdl_path) is not configured. Cannot save.")
            return False

        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(self.mdl_path), exist_ok=True)

            # Validate data before saving
            validation_result = await self._validate_mdl_data_for_save(mdl_litigations)
            if not validation_result['is_valid']:
                self.log_error(f"MDL data validation failed: {validation_result['errors']}")
                return False

            # Create backup if file exists
            backup_created = await self._create_backup_if_needed()
            if backup_created:
                self.log_debug("Created backup of existing MDL file")

            # Save the DataFrame
            mdl_litigations.to_json(
                self.mdl_path,
                orient='records',
                indent=4,
                date_format='iso'
            )

            self.log_info(f"MDL litigation data saved successfully to {self.mdl_path}")

            # Verify the saved file
            verification_result = await self._verify_saved_file(mdl_litigations)
            if not verification_result:
                self.log_warning("Saved file verification failed")
                return False

            return True

        except Exception as e:
            self.log_error(f"Failed to save MDL litigation data to {self.mdl_path}: {e}")
            return False

    def save_mdl_litigation_sync(self, mdl_litigations: pd.DataFrame) -> None:
        """
        Save MDL litigation DataFrame to file synchronously.
        
        This method is deprecated and will be removed in Phase 4.
        Use save_mdl_litigation_async() instead.
        
        Args:
            mdl_litigations: DataFrame to save
        """
        self.log_warning("Using deprecated sync MDL saving method. Please migrate to async version.")

        if mdl_litigations is None:
            self.log_error("MDL litigation data is None. Cannot save.")
            return

        if not self.mdl_path:
            self.log_error("MDL save path (mdl_path) is not configured. Cannot save.")
            return

        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(self.mdl_path), exist_ok=True)

            # Save the DataFrame
            mdl_litigations.to_json(self.mdl_path, orient='records', indent=4, date_format='iso')
            self.log_info(f"MDL litigation data saved successfully to {self.mdl_path}")

        except Exception as e:
            self.log_error(f"Failed to save MDL litigation data to {self.mdl_path}: {e}")

    async def update_and_save_mdl_entry(self, mdl_number: str, litigation_name: str,
                                        additional_data: Optional[Dict] = None,
                                        mdl_litigations: Optional[pd.DataFrame] = None) -> Tuple[pd.DataFrame, bool]:
        """
        Add or update an entry in the MDL litigation data and save it.
        
        Args:
            mdl_number: The MDL number (as string)
            litigation_name: The name of the litigation
            additional_data: Optional dictionary of other fields to add/update
            mdl_litigations: Optional existing DataFrame (will load if not provided)
            
        Returns:
            Tuple of (updated DataFrame, success status)
        """
        self.log_info(f"Updating MDL litigation data for MDL {mdl_number} ({litigation_name})")
        mdl_number_str = str(mdl_number)

        # Load existing data if not provided
        if mdl_litigations is None:
            mdl_litigations = await self.load_mdl_litigation_async()

        # Prepare the new/updated entry data
        entry_data = {"mdl_num": mdl_number_str, "litigation": litigation_name}
        if additional_data:
            entry_data.update(additional_data)

        # Update or add the entry
        try:
            mdl_litigations = await self._update_or_add_entry(mdl_litigations, mdl_number_str, entry_data)

            # Save the updated DataFrame
            save_success = await self.save_mdl_litigation_async(mdl_litigations)

            if save_success:
                self.log_info(f"Successfully updated and saved MDL {mdl_number_str}")
            else:
                self.log_error(f"Failed to save updated MDL data for {mdl_number_str}")

            return mdl_litigations, save_success

        except Exception as e:
            self.log_error(f"Error updating MDL entry {mdl_number_str}: {e}")
            return mdl_litigations, False

    async def _validate_and_clean_mdl_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Validate and clean loaded MDL data."""
        if df.empty:
            return df

        # Ensure mdl_num column exists and is properly formatted
        if 'mdl_num' in df.columns:
            df['mdl_num'] = df['mdl_num'].astype(str)
        else:
            self.log_warning(f"Column 'mdl_num' not found in {self.mdl_path}")
            # Create an empty DataFrame with expected columns if necessary
            return self._create_empty_mdl_dataframe()

        # Remove any completely empty rows
        df = df.dropna(how='all')

        # Validate required columns
        required_columns = ['mdl_num']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            self.log_warning(f"Missing required columns in MDL data: {missing_columns}")

        # Ensure consistent data types
        if 'mdl_num' in df.columns:
            df['mdl_num'] = df['mdl_num'].astype(str)

        self.log_debug(f"Validated and cleaned MDL data: {len(df)} entries")
        return df

    def _create_empty_mdl_dataframe(self) -> pd.DataFrame:
        """Create an empty DataFrame with expected MDL columns."""
        return pd.DataFrame(columns=['mdl_num', 'litigation', 'description', 'short_summary'])

    async def _validate_mdl_data_for_save(self, mdl_litigations: pd.DataFrame) -> Dict[str, Any]:
        """Validate MDL data before saving."""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }

        if mdl_litigations.empty:
            validation_result['warnings'].append("DataFrame is empty")
            return validation_result

        # Check for required columns
        required_columns = ['mdl_num']
        missing_columns = [col for col in required_columns if col not in mdl_litigations.columns]

        if missing_columns:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"Missing required columns: {missing_columns}")

        # Check for duplicate MDL numbers
        if 'mdl_num' in mdl_litigations.columns:
            duplicate_mdls = mdl_litigations[mdl_litigations.duplicated('mdl_num', keep=False)]['mdl_num'].unique()
            if len(duplicate_mdls) > 0:
                validation_result['warnings'].append(f"Duplicate MDL numbers found: {duplicate_mdls.tolist()}")

        # Check for empty MDL numbers
        if 'mdl_num' in mdl_litigations.columns:
            empty_mdl_count = mdl_litigations['mdl_num'].isna().sum()
            if empty_mdl_count > 0:
                validation_result['warnings'].append(f"{empty_mdl_count} entries have empty MDL numbers")

        return validation_result

    async def _create_backup_if_needed(self) -> bool:
        """Create a backup of the existing MDL file if it exists."""
        if not os.path.exists(self.mdl_path):
            return False

        try:
            # Create backup filename with timestamp
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{self.mdl_path}.backup_{timestamp}"

            # Copy the existing file
            import shutil
            shutil.copy2(self.mdl_path, backup_path)

            self.log_debug(f"Created backup: {backup_path}")
            return True

        except Exception as e:
            self.log_warning(f"Failed to create backup: {e}")
            return False

    async def _verify_saved_file(self, original_df: pd.DataFrame) -> bool:
        """Verify that the saved file can be loaded and matches the original data."""
        try:
            # Try to load the saved file
            loaded_df = pd.read_json(self.mdl_path)

            # Basic validation
            if len(loaded_df) != len(original_df):
                self.log_warning(f"Saved file has different number of entries: {len(loaded_df)} vs {len(original_df)}")
                return False

            # Check that mdl_num column exists and has the same values
            if 'mdl_num' in original_df.columns and 'mdl_num' in loaded_df.columns:
                original_mdls = set(original_df['mdl_num'].astype(str))
                loaded_mdls = set(loaded_df['mdl_num'].astype(str))

                if original_mdls != loaded_mdls:
                    self.log_warning("Saved file has different MDL numbers than original")
                    return False

            return True

        except Exception as e:
            self.log_error(f"Error verifying saved file: {e}")
            return False

    async def _update_or_add_entry(self, mdl_litigations: pd.DataFrame,
                                   mdl_number_str: str, entry_data: Dict) -> pd.DataFrame:
        """Update existing entry or add new entry to DataFrame."""
        # Check if the MDL number already exists
        if 'mdl_num' in mdl_litigations.columns and not mdl_litigations.empty:
            existing_indices = mdl_litigations[mdl_litigations['mdl_num'] == mdl_number_str].index

            if not existing_indices.empty:
                # Update existing entry/entries
                idx_to_update = existing_indices[0]  # Update the first match
                self.log_debug(f"MDL {mdl_number_str} exists. Updating entry at index {idx_to_update}.")

                for key, value in entry_data.items():
                    mdl_litigations.loc[idx_to_update, key] = value

                if len(existing_indices) > 1:
                    self.log_warning(f"Multiple entries found for MDL {mdl_number_str}. Only the first was updated.")
            else:
                # Add as new entry
                self.log_debug(f"MDL {mdl_number_str} not found. Adding as new entry.")
                new_entry_df = pd.DataFrame([entry_data])
                mdl_litigations = pd.concat([mdl_litigations, new_entry_df], ignore_index=True)
        else:
            # If DataFrame is empty or lacks 'mdl_num', create/replace it
            self.log_debug("MDL DataFrame empty or missing 'mdl_num'. Creating/replacing with new entry.")
            mdl_litigations = pd.DataFrame([entry_data])

        return mdl_litigations

    def get_file_info(self) -> Dict[str, Any]:
        """
        Get information about the MDL file.
        
        Returns:
            Dictionary with file information
        """
        file_info = {
            'path': self.mdl_path,
            'exists': False,
            'size_bytes': 0,
            'modified_time': None,
            'is_readable': False,
            'is_writable': False
        }

        try:
            if os.path.exists(self.mdl_path):
                file_info['exists'] = True

                # Get file size
                file_info['size_bytes'] = os.path.getsize(self.mdl_path)

                # Get modification time
                import datetime
                mod_time = os.path.getmtime(self.mdl_path)
                file_info['modified_time'] = datetime.datetime.fromtimestamp(mod_time).isoformat()

                # Check permissions
                file_info['is_readable'] = os.access(self.mdl_path, os.R_OK)
                file_info['is_writable'] = os.access(self.mdl_path, os.W_OK)

        except Exception as e:
            self.log_error(f"Error getting file info for {self.mdl_path}: {e}")

        return file_info

    def validate_file_path(self) -> Dict[str, Any]:
        """
        Validate the MDL file path configuration.
        
        Returns:
            Validation report with status and details
        """
        report = {
            'status': 'success',
            'warnings': [],
            'errors': [],
            'path_info': {}
        }

        if not self.mdl_path:
            report['status'] = 'error'
            report['errors'].append("MDL path is not configured")
            return report

        # Check if path is absolute
        if not os.path.isabs(self.mdl_path):
            report['warnings'].append("MDL path is not absolute")

        # Check directory existence and permissions
        directory = os.path.dirname(self.mdl_path)

        if not os.path.exists(directory):
            report['errors'].append(f"Directory does not exist: {directory}")
            report['status'] = 'error'
        elif not os.access(directory, os.W_OK):
            report['errors'].append(f"Directory is not writable: {directory}")
            report['status'] = 'error'

        # Get file info
        report['path_info'] = self.get_file_info()

        # Set overall status
        if report['errors']:
            report['status'] = 'error'
        elif report['warnings']:
            report['status'] = 'warning'

        return report

    def get_persistence_summary(self) -> str:
        """
        Get summary of MDL persistence configuration and status.
        
        Returns:
            Human-readable summary
        """
        file_info = self.get_file_info()

        if file_info['exists']:
            size_kb = file_info['size_bytes'] / 1024
            return f"MDL file: {os.path.basename(self.mdl_path)} ({size_kb:.1f}KB, accessible: {file_info['is_readable']})"
        else:
            return f"MDL file: {os.path.basename(self.mdl_path)} (not found)"

    # === Missing methods for test compatibility ===

    async def _save_dataframe_to_json(self, df: pd.DataFrame, file_path: str) -> bool:
        """Save DataFrame to JSON file."""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Convert DataFrame to JSON and save
            df.to_json(file_path, orient='records', indent=2)

            self.log_info(f"Successfully saved DataFrame to {file_path}")
            return True

        except Exception as e:
            self.log_error(f"Error saving DataFrame to JSON: {e}")
            return False

    def _get_backup_path(self, timestamp: Optional[str] = None) -> str:
        """Generate backup file path."""
        if not timestamp:
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        base_name = os.path.splitext(os.path.basename(self.mdl_path))[0]
        backup_name = f"{base_name}_backup_{timestamp}.json"

        return os.path.join(os.path.dirname(self.mdl_path), backup_name)

    async def _validate_entry_data(self, entry_data: Dict) -> bool:
        """Validate entry data before saving (async version for test compatibility)."""
        try:
            if not isinstance(entry_data, dict):
                return False

            # Validate MDL number
            mdl_number = entry_data.get('mdl_num')
            if not mdl_number or mdl_number in ['', 'NA', 'None']:
                return False

            # Validate litigation name
            litigation_name = entry_data.get('litigation')
            if not litigation_name or litigation_name in ['', 'NA', 'None']:
                return False

            return True

        except Exception as e:
            self.log_error(f"Error validating entry data: {e}")
            return False

    async def validate_persistence_data(self, data: Any) -> Dict[str, Any]:
        """Validate persistence data structure."""
        report = {
            'status': 'success',
            'is_valid': True,
            'errors': [],
            'warnings': []
        }

        try:
            if data is None:
                report['errors'].append("Data is None")
                report['is_valid'] = False
                report['status'] = 'error'
                return report

            if isinstance(data, pd.DataFrame):
                if data.empty:
                    report['warnings'].append("DataFrame is empty")

                # Check for required columns
                required_columns = ['mdl_num', 'litigation']
                missing_columns = [col for col in required_columns if col not in data.columns]
                if missing_columns:
                    report['errors'].append(f"Missing required columns: {missing_columns}")
                    report['is_valid'] = False
                    report['status'] = 'error'

            elif isinstance(data, dict):
                # Validate dictionary structure
                if not data:
                    report['warnings'].append("Dictionary is empty")

            else:
                report['errors'].append(f"Unsupported data type: {type(data)}")
                report['is_valid'] = False
                report['status'] = 'error'

        except Exception as e:
            report['errors'].append(f"Error validating data: {str(e)}")
            report['is_valid'] = False
            report['status'] = 'error'

        return report

    async def cleanup_old_backups(self, max_backups: int = 10) -> Dict[str, Any]:
        """Clean up old backup files."""
        report = {
            'removed_count': 0,
            'errors': [],
            'backup_files_found': 0
        }

        try:
            backup_dir = os.path.dirname(self.mdl_path)
            base_name = os.path.splitext(os.path.basename(self.mdl_path))[0]

            # Find all backup files
            backup_files = []
            for filename in os.listdir(backup_dir):
                if filename.startswith(f"{base_name}_backup_") and filename.endswith('.json'):
                    full_path = os.path.join(backup_dir, filename)
                    mtime = os.path.getmtime(full_path)
                    backup_files.append((full_path, mtime))

            report['backup_files_found'] = len(backup_files)

            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x[1], reverse=True)

            # Remove old backups if we exceed max_backups
            if len(backup_files) > max_backups:
                files_to_remove = backup_files[max_backups:]

                for file_path, _ in files_to_remove:
                    try:
                        os.remove(file_path)
                        report['removed_count'] += 1
                        self.log_debug(f"Removed old backup: {file_path}")
                    except Exception as e:
                        report['errors'].append(f"Error removing {file_path}: {str(e)}")

        except Exception as e:
            report['errors'].append(f"Error during cleanup: {str(e)}")

        return report

    async def get_file_info_async(self) -> Dict[str, Any]:
        """Get file information asynchronously."""
        return self.get_file_info()

    def _create_backup_if_needed_sync(self) -> bool:
        """Create backup of existing file if it exists."""
        try:
            if not os.path.exists(self.mdl_path):
                return True  # No file to backup

            backup_path = self._get_backup_path()

            # Copy existing file to backup location
            import shutil
            shutil.copy2(self.mdl_path, backup_path)

            self.log_info(f"Created backup: {backup_path}")
            return True

        except Exception as e:
            self.log_error(f"Error creating backup: {e}")
            return False
