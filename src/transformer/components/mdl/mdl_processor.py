# /src/services/transformer/components.mdl/mdl_processor_main.py
import logging
from typing import Dict, Any, Optional

import pandas as pd

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.transformer.config.constants import NULL_CONDITIONS
from .mdl_processor_utils import MDLProcessorUtils


class MDLProcessor(AsyncServiceBase):
    """
    Orchestrates MDL-related operations using modular components.
    """


    def __init__(self,
                 mdl_litigations: pd.DataFrame = None,
                 mdl_path: str = None,
                 file_handler=None,
                 gpt=None,
                 config: Optional[Dict[str, Any]] = None,
                 litigation_classifier=None,
                 pdf_cache=None,
                 download_dir: Optional[str] = None,
                 district_court_db=None,
                 logger: Optional[logging.Logger] = None):
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.utils = MDLProcessorUtils(
            mdl_litigations, mdl_path, file_handler, gpt, config,
            litigation_classifier, pdf_cache, download_dir, district_court_db, logger
        )
        
        # Add a temporary validator attribute for compatibility with JobRunnerService
        # This is a compatibility layer until the DI is properly configured
        from src.transformer.components.docket.validator import DocketValidator
        self.validator = DocketValidator(config=config or {}, logger=logger)

    async def _execute_action(self, data: Any) -> Any:
        """Execute MDLProcessor actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'add_mdl_info_async':
                docket_data = action_data.get('docket_data', {})
                return await self.add_mdl_info_async(docket_data)
            elif action == 'calculate_afff_num_plaintiffs_async':
                docket_data = action_data.get('docket_data', {})
                return await self.calculate_afff_num_plaintiffs_async(docket_data)
        raise TransformerServiceError("Invalid action data provided to MDLProcessor")

    async def add_mdl_info_async(self, data: Dict) -> bool:
        """
        Add MDL details asynchronously using data processor.
        """
        return await self.utils.add_mdl_info_async(data)

    async def calculate_afff_num_plaintiffs_async(self, data: Dict) -> str:
        """
        Calculate number of plaintiffs for AFFF cases (MDL 2873) asynchronously.
        """
        return await self.utils.calculate_afff_num_plaintiffs_async(data)
    
    async def _create_mdl_lookup_async(self) -> Dict[str, str]:
        """
        Create MDL lookup dictionary asynchronously.
        This is a compatibility method that delegates to the lookup manager.
        """
        # Use the lookup manager if available
        if hasattr(self.utils, 'lookup_manager') and self.utils.lookup_manager:
            return await self.utils.lookup_manager.get_mdl_lookup_async()
        
        # Fallback to empty dict if no lookup manager
        self.log_warning("No lookup manager available for MDL lookup")
        return {}
    
    @property
    def mdl_litigations(self):
        """Property to access mdl_litigations from utils for backward compatibility."""
        return self.utils.mdl_litigations if self.utils else None
    
    @mdl_litigations.setter
    def mdl_litigations(self, value):
        """Property setter to set mdl_litigations in utils for backward compatibility."""
        if self.utils:
            self.utils.mdl_litigations = value
        else:
            self.log_warning("Cannot set mdl_litigations - utils not available")

    async def add_mdl_info_to_docket(self, data: Dict, mdl_litigations: pd.DataFrame = None) -> bool:
        """
        Add MDL information to a docket.
        This is a compatibility method that delegates to add_mdl_info_async.
        """
        # Use provided mdl_litigations or fall back to instance attribute
        if mdl_litigations is None:
            mdl_litigations = self.mdl_litigations
        
        # Store temporarily for the operation
        old_mdl_litigations = self.utils.mdl_litigations
        self.utils.mdl_litigations = mdl_litigations
        
        try:
            result = await self.add_mdl_info_async(data)
            return result
        finally:
            # Restore original
            self.utils.mdl_litigations = old_mdl_litigations
