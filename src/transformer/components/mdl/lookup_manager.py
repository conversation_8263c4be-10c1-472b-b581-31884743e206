# /src/services/transformer/mdl_lookup_manager.py
"""

MDL lookup table management for async database operations.

This module handles MDL lookup table creation and caching extracted from
mdl_processor.py as part of Phase 3.3 refactoring.
"""
import logging
import time
from typing import Dict, Optional, Any

import pandas as pd

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError


class MDLLookupManager(AsyncServiceBase):
    """Manages MDL lookup table operations with async database access."""
    def __init__(self,
                 district_court_db=None,
                 config: Optional[Dict] =None,
                 logger: Optional[logging.Logger] =None):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.district_court_db = district_court_db

        # Cache management
        self._mdl_lookup_cache = None
        self._mdl_lookup_loaded = False

    async def _execute_action(self, data: Any) -> Any:
        """Execute MDLLookupManager actions."""
        if isinstance(data, dict):
            action = data.get('action')

            if action == 'get_mdl_lookup_async':
                return await self.get_mdl_lookup_async()
            elif action == 'get_mdl_lookup_sync':
                return self.get_mdl_lookup_sync()
            elif action == 'clear_cache':
                self.clear_cache()
                return {'status': 'cache_cleared'}
            elif action == 'validate_lookup_data':
                return self.validate_lookup_data(data['lookup_dict'])
            elif action == 'get_lookup_summary':
                return self.get_lookup_summary(data['lookup_dict'])
            elif action == 'process_district_court_data':
                return await self._process_district_court_data(data['dc_items'])
            elif action == 'process_district_court_data_sync':
                return self._process_district_court_data_sync(data['dc_items'])
            elif action == 'get_cache_status':
                return {
                    'loaded': self._mdl_lookup_loaded,
                    'cache_size': len(self._mdl_lookup_cache) if self._mdl_lookup_cache else 0,
                    'has_cache': self._mdl_lookup_cache is not None
                }
        raise TransformerServiceError("Invalid action data provided to MDLLookupManager")

    async def get_mdl_lookup_async(self) -> Dict[str, str]:
        """
        Get MDL lookup table using async database operations.

        Returns:
            Dict mapping MDL numbers to court IDs
        """
        # Return cached version if already loaded
        if self._mdl_lookup_loaded:
            self.log_debug("Returning cached MDL lookup from DistrictCourts data.")
            return self._mdl_lookup_cache if self._mdl_lookup_cache is not None else {}

        # Check dependencies
        if not self.district_court_db:
            self.log_error("District court database not available for MDL lookup.")
            self._mdl_lookup_cache = {}
            self._mdl_lookup_loaded = True
            return {}

        self.log_debug("Creating MDL lookup table from DistrictCourts data (first time)...")

        try:
            start_scan_time = time.time()

            self.log_info("Scanning DistrictCourts async repository for MDL lookup...")
            # Use the new async repository pattern - scan_all() returns all items
            dc_items = await self.district_court_db.scan_all()

            scan_duration = time.time() - start_scan_time
            self.log_info(f"DistrictCourts async scan complete in {scan_duration:.2f}s, found {len(dc_items)} items.")

            if not dc_items:
                self.log_warning("DistrictCourts scan for MDL lookup returned no items.")
                self._mdl_lookup_cache = {}
            else:
                # Process the retrieved data
                self._mdl_lookup_cache = await self._process_district_court_data(dc_items)

        except Exception as e:
            self.log_error(f"Exception during async MDL lookup creation: {str(e)}")
            self._mdl_lookup_cache = {}

        # Mark as loaded and return
        self._mdl_lookup_loaded = True
        return self._mdl_lookup_cache if self._mdl_lookup_cache is not None else {}

    def get_mdl_lookup_sync(self) -> Dict[str, str]:
        """
        Get MDL lookup table using sync database operations.

        This method is deprecated and will be removed in Phase 4.
        Use get_mdl_lookup_async() instead.

        Returns:
            Dict mapping MDL numbers to court IDs
        """
        self.log_warning("Using deprecated sync MDL lookup method. Please migrate to async version.")

        # Return cached version if already loaded
        if self._mdl_lookup_loaded:
            self.log_debug("Returning cached MDL lookup from DistrictCourts data.")
            return self._mdl_lookup_cache if self._mdl_lookup_cache is not None else {}

        # Check dependencies
        if not self.district_court_db:
            self.log_error("District court database not available for MDL lookup.")
            self._mdl_lookup_cache = {}
            self._mdl_lookup_loaded = True
            return {}

        self.log_debug("Creating MDL lookup table from DistrictCourts data (first time)...")

        try:
            start_scan_time = time.time()

            # Check if this is the new async repository
            if hasattr(self.district_court_db, 'scan_all'):
                # This is an async repository, we can't use it in sync context
                self.log_error("Cannot use async repository in sync context. Please use _create_mdl_lookup_async().")
                self._mdl_lookup_cache = {}
                self._mdl_lookup_loaded = True
                return {}

            # Legacy sync repository pattern (if it exists)
            projection = "#mn, #ci"
            attr_names = {'#mn': 'MdlNum', '#ci': 'CourtId'}

            self.log_info(f"Scanning DistrictCourts table '{self.district_court_db.table.name}' for MDL lookup...")
            dc_items = list(self.district_court_db.scan_table(
                ProjectionExpression=projection,
                ExpressionAttributeNames=attr_names
            ))

            scan_duration = time.time() - start_scan_time
            self.log_info(f"DistrictCourts scan complete in {scan_duration:.2f}s, found {len(dc_items)} items.")

            if not dc_items:
                self.log_warning("DistrictCourts scan for MDL lookup returned no items.")
                self._mdl_lookup_cache = {}
            else:
                # Process the retrieved data synchronously
                self._mdl_lookup_cache = self._process_district_court_data_sync(dc_items)

        except Exception as e:
            self.log_error(f"Exception during sync MDL lookup creation: {str(e)}")
            self._mdl_lookup_cache = {}

        # Mark as loaded and return
        self._mdl_lookup_loaded = True
        return self._mdl_lookup_cache if self._mdl_lookup_cache is not None else {}

    async def _process_district_court_data(self, dc_items: list) -> Dict[str, str]:
        """
        Process district court data to create MDL lookup table.

        Args:
            dc_items: List of district court items from database

        Returns:
            Dict mapping MDL numbers to court IDs
        """
        if not dc_items:
            return {}

        df = pd.DataFrame(dc_items)

        if 'mdl_num' not in df.columns or 'court_id' not in df.columns:
            self.log_warning("Projected columns 'mdl_num' or 'court_id' missing in DistrictCourts scan results.")
            self.log_warning(f"Available columns: {list(df.columns)}")
            return {}

        # Convert mdl_num to string for consistent handling
        df['mdl_num'] = df['mdl_num'].astype(str)

        # Filter out invalid MDL numbers
        filtered_df = df[
            (df['mdl_num'] != 'NA') &
            (df['mdl_num'] != 'None') &
            (df['mdl_num'].notna()) &
            (df['mdl_num'] != '')
            ].copy()

        if filtered_df.empty:
            self.log_warning("No valid mdl_num entries found after filtering.")
            return {}

        # Ensure court_id is also valid
        filtered_df = filtered_df[
            filtered_df['court_id'].notna() & (filtered_df['court_id'] != '')
            ]

        if filtered_df.empty:
            self.log_warning("No valid court_ids found after filtering.")
            return {}

        # Create the lookup dictionary
        mdl_lookup_dict = filtered_df.drop_duplicates(subset=['mdl_num']).set_index('mdl_num')['court_id'].to_dict()

        self.log_debug(f"MDL lookup created with {len(mdl_lookup_dict)} entries.")
        return mdl_lookup_dict

    def _process_district_court_data_sync(self, dc_items: list) -> Dict[str, str]:
        """
        Synchronous version of district court data processing.

        Args:
            dc_items: List of district court items from database

        Returns:
            Dict mapping MDL numbers to court IDs
        """
        # Same logic as async version but without async/await
        return self._process_district_court_data_sync_impl(dc_items)

    def _process_district_court_data_sync_impl(self, dc_items: list) -> Dict[str, str]:
        """Implementation of sync district court data processing."""
        if not dc_items:
            return {}

        df = pd.DataFrame(dc_items)

        if 'mdl_num' not in df.columns or 'court_id' not in df.columns:
            self.log_warning("Projected columns 'mdl_num' or 'court_id' missing in DistrictCourts scan results.")
            self.log_warning(f"Available columns: {list(df.columns)}")
            return {}

        df['mdl_num'] = df['mdl_num'].astype(str)

        # Filter out invalid MDL numbers
        filtered_df = df[
            (df['mdl_num'] != 'NA') &
            (df['mdl_num'] != 'None') &
            (df['mdl_num'].notna()) &
            (df['mdl_num'] != '')
            ].copy()

        if filtered_df.empty:
            self.log_warning("No valid mdl_num entries found after filtering.")
            return {}

        # Ensure court_id is also valid
        filtered_df = filtered_df[
            filtered_df['court_id'].notna() & (filtered_df['court_id'] != '')
            ]

        if filtered_df.empty:
            self.log_warning("No valid court_ids found after filtering.")
            return {}

        # Create the lookup dictionary
        mdl_lookup_dict = filtered_df.drop_duplicates(subset=['mdl_num']).set_index('mdl_num')['court_id'].to_dict()

        self.log_debug(f"MDL lookup created with {len(mdl_lookup_dict)} entries.")
        return mdl_lookup_dict

    def clear_cache(self):
        """Clear the MDL lookup cache."""
        self._mdl_lookup_cache = None
        self._mdl_lookup_loaded = False
        self.log_debug("MDL lookup cache cleared.")

    def validate_lookup_data(self, lookup_dict: Dict[str, str]) -> Dict[str, Any]:
        """
        Validate MDL lookup data and return validation report.

        Args:
            lookup_dict: MDL lookup dictionary to validate

        Returns:
            Validation report with status and details
        """
        report = {
            'status': 'success',
            'warnings': [],
            'errors': [],
            'entry_count': len(lookup_dict),
            'sample_entries': {}
        }

        if not lookup_dict:
            report['status'] = 'error'
            report['errors'].append("MDL lookup dictionary is empty")
            return report

        # Validate a sample of entries
        sample_size = min(5, len(lookup_dict))
        sample_items = list(lookup_dict.items())[:sample_size]

        for mdl_num, court_id in sample_items:
            # Validate MDL number format
            if not isinstance(mdl_num, str) or not mdl_num.isdigit():
                report['warnings'].append(f"Non-numeric MDL number: {mdl_num}")

            # Validate court ID format
            if not isinstance(court_id, str) or len(court_id) != 4:
                report['warnings'].append(f"Invalid court ID format: {court_id}")

            report['sample_entries'][mdl_num] = court_id

        # Set overall status
        if report['errors']:
            report['status'] = 'error'
        elif report['warnings']:
            report['status'] = 'warning'

        return report

    def get_lookup_summary(self, lookup_dict: Dict[str, str]) -> str:
        """
        Get summary of MDL lookup table.

        Args:
            lookup_dict: MDL lookup dictionary

        Returns:
            Human-readable summary
        """
        if not lookup_dict:
            return "No MDL lookup data available"

        entry_count = len(lookup_dict)

        # Count unique court IDs
        unique_courts = len(set(lookup_dict.values()))

        # Get sample entries for display
        sample_entries = list(lookup_dict.items())[:3]
        sample_text = ", ".join([f"MDL {k}→{v}" for k, v in sample_entries])

        return f"MDL lookup: {entry_count} entries, {unique_courts} courts (e.g., {sample_text})"
