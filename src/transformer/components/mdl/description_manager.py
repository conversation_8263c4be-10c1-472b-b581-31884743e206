# /src/services/transformer/components.mdl/description_manager_main.py
import logging
from typing import Dict, List, Optional, Any, Tuple

from src.transformer.config.constants import NULL_CONDITIONS

import pandas as pd
from tqdm import tqdm

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from .description_manager_utils import MDLDescriptionManagerUtils


class MDLDescriptionManager(AsyncServiceBase):
    """Manages MDL descriptions and summaries with LLM integration."""



    def __init__(self,
                 file_handler=None,
                 gpt_client=None,
                 config: Optional[Dict] = None,
                 download_dir: Optional[str] = None,
                 logger: Optional[logging.Logger] = None):
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.utils = MDLDescriptionManagerUtils(file_handler, gpt_client, config, download_dir, logger)

    async def _execute_action(self, data: Any) -> Any:
        """Execute MDLDescriptionManager actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'update_mdl_descriptions_from_dockets':
                mdl_litigations = action_data.get('mdl_litigations')
                json_paths = action_data.get('json_paths')
                return await self.update_mdl_descriptions_from_dockets(mdl_litigations, json_paths)
            elif action == 'match_titles_to_mdl_litigation':
                mdl_litigations = action_data.get('mdl_litigations')
                return await self.match_titles_to_mdl_litigation(mdl_litigations)
            elif action == 'summarize_existing_descriptions':
                mdl_litigations = action_data.get('mdl_litigations')
                return await self.summarize_existing_descriptions(mdl_litigations)
        raise TransformerServiceError("Invalid action data provided to MDLDescriptionManager")

    async def update_mdl_descriptions_from_dockets(self, mdl_litigations: pd.DataFrame,
                                                   json_paths: Optional[List[str]] = None) -> Tuple[pd.DataFrame, int]:
        """
        Update MDL descriptions by processing docket files and generating summaries.
        """
        return await self.utils.update_mdl_descriptions_from_dockets(mdl_litigations, json_paths)

    async def match_titles_to_mdl_litigation(self, mdl_litigations: pd.DataFrame,
                                             json_paths: List[str]) -> Tuple[pd.DataFrame, int]:
        """
        Match titles from specific JSON files to MDL numbers and update summaries.
        """
        return await self.utils.match_titles_to_mdl_litigation(mdl_litigations, json_paths)

    async def summarize_existing_descriptions(self, mdl_litigations: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
        """
        Summarize existing MDL descriptions using LLM where short_summary is missing.
        """
        return await self.utils.summarize_existing_descriptions(mdl_litigations)
