import logging
import os
from typing import List, Dict, Any, Optional

from src.transformer.config.constants import NULL_CONDITIONS

import pandas as pd

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.transformer.engines.afff_calculator import AfffCalculator
from .data_processor import MDLDataProcessor
from .description_manager import MDLDescriptionManager
from .lookup_manager import MDLLookupManager
from .persistence_manager import MDLPersistenceManager


class MDLProcessorUtils(AsyncServiceBase):
    """Utility functions for the MDLProcessor."""

    async def _execute_action(self, data: Any) -> Any:
        """Execute MDLProcessorUtils actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'process_mdl_data':
                return await self.process_mdl_data(action_data)
            elif action == 'validate_mdl':
                return self.validate_mdl_data(action_data)
            else:
                raise TransformerServiceError(f"Unknown action: {action}")
        else:
            raise TransformerServiceError(f"Invalid data format for MDLProcessorUtils: {type(data)}")
        
        return None


    def __init__(self,
                 mdl_litigations: pd.DataFrame = None,
                 mdl_path: str = None,
                 file_handler=None,
                 gpt=None,
                 config: Optional[Dict[str, Any]] = None,
                 litigation_classifier=None,
                 pdf_cache=None,
                 download_dir: Optional[str] = None,
                 district_court_db=None,
                 logger: Optional[logging.Logger] = None):
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.mdl_litigations = mdl_litigations
        self.mdl_path = mdl_path
        self.file_handler = file_handler
        self.gpt = gpt
        self.litigation_classifier = litigation_classifier
        self.pdf_cache = pdf_cache
        self.download_dir = download_dir
        self.district_court_db = district_court_db

        self._initialize_components()

        self._mdl_lookup_cache = None
        self._mdl_lookup_loaded = False

    def _initialize_components(self):
        """Initialize the modular components for MDL processing."""
        try:
            self.lookup_manager = MDLLookupManager(
                district_court_db=self.district_court_db,
                config=self.config,
                logger=self.logger
            )
            self.afff_calculator = AfffCalculator(
                config=self.config,
                logger=self.logger
            )
            self.description_manager = MDLDescriptionManager(
                file_handler=self.file_handler,
                gpt_client=self.gpt,
                config=self.config,
                download_dir=self.download_dir,
                logger=self.logger
            )
            self.persistence_manager = MDLPersistenceManager(
                mdl_path=self.mdl_path,
                config=self.config,
                logger=self.logger
            )
            self.data_processor = MDLDataProcessor(
                config=self.config,
                logger=self.logger
            )
            self.log_debug("Initialized all MDL processor components")
        except Exception as e:
            self.log_error(f"Error initializing MDL processor components: {e}")
            raise

    async def add_mdl_info_async(self, data: Dict) -> bool:
        """
        Add MDL details asynchronously using data processor.
        """
        return await self.data_processor.add_mdl_info_to_docket(data, self.mdl_litigations)

    async def calculate_afff_num_plaintiffs_async(self, data: Dict) -> str:
        """
        Calculate number of plaintiffs for AFFF cases (MDL 2873) asynchronously.
        """
        return await self.afff_calculator.calculate_afff_num_plaintiffs(data)
