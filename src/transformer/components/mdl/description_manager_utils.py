import asyncio
import logging
import os
from typing import Dict, List, Optional, Any, Tuple

from src.transformer.config.constants import NULL_CONDITIONS

import pandas as pd
from tqdm import tqdm

from src.infrastructure.patterns.component_base import AsyncServiceBase


class MDLDescriptionManagerUtils(AsyncServiceBase):
    """Utility functions for the MDLDescriptionManager."""

    def __init__(self,
                 file_handler=None,
                 gpt_client=None,
                 config: Optional[Dict] = None,
                 download_dir: Optional[str] = None,
                 logger: Optional[logging.Logger] = None):
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.file_handler = file_handler
        self.gpt = gpt_client
        self.download_dir = download_dir

    async def _execute_action(self, data: Any) -> Any:
        """
        Execute action - not used for utility class.
        
        This class is a utility class and actions are handled by MDLDescriptionManager.
        """
        from src.infrastructure.protocols.exceptions import TransformerServiceError
        raise TransformerServiceError(
            "MDLDescriptionManagerUtils is a utility class and does not handle actions directly. "
            "Use MDLDescriptionManager for action-based operations."
        )

    async def update_mdl_descriptions_from_dockets(self, mdl_litigations: pd.DataFrame,
                                                   json_paths: Optional[List[str]] = None) -> Tuple[pd.DataFrame, int]:
        self.log_info("Starting MDL description update from dockets...")

        if json_paths:
            df = await self._read_specific_jsons(json_paths)
        else:
            df = await self._read_all_jsons()

        if df.empty:
            self.log_warning("No docket data read, cannot update MDL descriptions.")
            return mdl_litigations, 0

        unique_mdl_nums = await self._extract_unique_mdl_numbers(df)

        if not unique_mdl_nums:
            self.log_warning("No valid MDL numbers found in docket data.")
            return mdl_litigations, 0

        self.log_info(f"Found {len(unique_mdl_nums)} unique MDL numbers in dockets to check for updates.")

        updated_count = 0

        for mdl_num in tqdm(unique_mdl_nums, desc='Updating MDL Descriptions'):
            try:
                if await self._should_update_mdl_summary(mdl_litigations, mdl_num):
                    docket_data = await self._get_docket_data_for_mdl(df, mdl_num)

                    if docket_data:
                        target_idx = await self._find_mdl_index(mdl_litigations, mdl_num)

                        if target_idx is not None:
                            if await self._update_mdl_summary_from_docket(
                                    mdl_litigations, target_idx, mdl_num, docket_data
                            ):
                                updated_count += 1

            except Exception as e:
                self.log_error(f"Error processing MDL {mdl_num}: {e}")
                continue

        self.log_info(f"Updated descriptions for {updated_count} MDLs from docket data.")
        return mdl_litigations, updated_count

    async def match_titles_to_mdl_litigation(self, mdl_litigations: pd.DataFrame,
                                             json_paths: List[str]) -> Tuple[pd.DataFrame, int]:
        if not json_paths:
            self.log_info("No JSON paths provided for MDL title matching.")
            return mdl_litigations, 0

        df = await self._read_specific_jsons(json_paths)
        if df.empty:
            self.log_info("No data read from the provided JSON paths for MDL matching.")
            return mdl_litigations, 0

        self.log_info(f"Attempting to match titles for {len(json_paths)} files against MDL data.")
        updated_count = 0

        for json_path in tqdm(json_paths, desc="Matching Titles to MDL"):
            try:
                base_filename = os.path.basename(json_path)
                docket_data = await self._find_docket_data_by_filename(df, base_filename, json_path)

                if not docket_data:
                    continue

                mdl_num = await self._extract_mdl_number_from_data(docket_data)

                if not mdl_num:
                    self.log_debug(f"Skipping {base_filename}: No valid MDL number.")
                    continue

                target_idx = await self._find_mdl_index(mdl_litigations, mdl_num)

                if target_idx is None:
                    self.log_debug(f"MDL {mdl_num} from {base_filename} not found in lookup table.")
                    continue

                if await self._should_update_mdl_summary_at_index(mdl_litigations, target_idx):
                    self.log_info(f"MDL {mdl_num} needs summary update. Processing data from {base_filename}.")

                    if await self._update_mdl_summary_from_docket(
                            mdl_litigations, target_idx, mdl_num, docket_data
                    ):
                        updated_count += 1

            except Exception as e:
                self.log_error(f"Error processing {json_path}: {e}")
                continue

        self.log_info(f"Attempted updates for {updated_count} MDL summaries from title matching.")
        return mdl_litigations, updated_count

    async def summarize_existing_descriptions(self, mdl_litigations: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
        self.log_info("Starting summarization of existing MDL descriptions...")

        needs_summary_df = await self._filter_entries_needing_summary(mdl_litigations)

        if needs_summary_df.empty:
            self.log_info("No MDL entries found requiring description summarization.")
            return mdl_litigations, 0

        self.log_info(f"Found {len(needs_summary_df)} MDL entries requiring summarization.")
        updated_count = 0

        for index, row in tqdm(needs_summary_df.iterrows(), total=len(needs_summary_df), desc="Summarizing MDLs"):
            try:
                mdl_num = row['mdl_num']
                description = row['description']

                self.log_debug(f"Summarizing MDL No {mdl_num}")

                summary = await self._generate_summary_with_llm(description)

                if summary and summary not in NULL_CONDITIONS:
                    await self._update_summary_in_dataframe(mdl_litigations, mdl_num, summary)
                    updated_count += 1
                else:
                    self.log_warning(f"LLM failed to generate a valid summary for MDL No {mdl_num}")

            except Exception as e:
                self.log_error(f"Error summarizing description for MDL {row.get('mdl_num', 'unknown')}: {e}")

        self.log_info(f"Generated summaries for {updated_count} MDLs.")
        return mdl_litigations, updated_count

    async def _read_specific_jsons(self, json_paths: List[str]) -> pd.DataFrame:
        records = []
        if not json_paths:
            return pd.DataFrame()

        self.log_info(f"Reading data from {len(json_paths)} specified JSON files.")
        for file_path in tqdm(json_paths, desc="Reading JSONs"):
            try:
                data = self.file_handler.load_json(file_path)
                if data:
                    data['_source_json_path'] = file_path
                    records.append(data)
                else:
                    self.log_warning(f"Could not load data from {os.path.basename(file_path)}")
            except Exception as e:
                self.log_error(f"Error reading {file_path}: {e}")

        return pd.DataFrame(records) if records else pd.DataFrame()

    async def _read_all_jsons(self) -> pd.DataFrame:
        target_docket_dir = await self._get_dockets_directory()

        if not target_docket_dir or not os.path.isdir(target_docket_dir):
            self.log_error(f"Target dockets directory does not exist: {target_docket_dir}")
            return pd.DataFrame()

        try:
            json_files = [
                os.path.join(target_docket_dir, f)
                for f in os.listdir(target_docket_dir)
                if f.endswith('.json')
            ]
        except OSError as e:
            self.log_error(f"Error listing files in {target_docket_dir}: {e}")
            return pd.DataFrame()

        records = []
        self.log_info(f"Reading all {len(json_files)} JSON files from {target_docket_dir}.")

        for file_path in tqdm(json_files, desc="Reading all JSONs"):
            try:
                data = self.file_handler.load_json(file_path)
                if data:
                    records.append(data)
            except Exception as e:
                self.log_error(f"Error reading {file_path}: {e}")

        return pd.DataFrame(records)

    async def _get_dockets_directory(self) -> Optional[str]:
        if self.download_dir:
            return os.path.join(self.download_dir, 'dockets')
        elif hasattr(self.file_handler, 'download_dir') and self.file_handler.download_dir:
            return os.path.join(self.file_handler.download_dir, 'dockets')
        else:
            self.log_error("Cannot determine target dockets directory.")
            return None

    async def _extract_unique_mdl_numbers(self, df: pd.DataFrame) -> List[str]:
        if df.empty or 'mdl_num' not in df.columns:
            return []

        unique_mdl_nums = (
            df['mdl_num']
            .dropna()
            .astype(str)
            .str.replace(r'\.0$', '', regex=True)
            .unique()
            .tolist()
        )

        valid_mdl_nums = [
            num for num in unique_mdl_nums
            if num not in ["NA", '9000', '', 'nan'] and num.strip()
        ]

        return valid_mdl_nums

    async def _should_update_mdl_summary(self, mdl_litigations: pd.DataFrame, mdl_num: str) -> bool:
        try:
            if 'mdl_num' not in mdl_litigations.columns:
                self.log_error("MDL lookup table missing 'mdl_num' column.")
                return False

            match_index = mdl_litigations[mdl_litigations['mdl_num'] == mdl_num].index

            if match_index.empty:
                self.log_debug(f"MDL {mdl_num} not found in the active MDL lookup table.")
                return False

            target_idx = match_index[0]
            return await self._should_update_mdl_summary_at_index(mdl_litigations, target_idx)

        except Exception as e:
            self.log_error(f"Error checking if MDL {mdl_num} needs update: {e}")
            return False

    async def _should_update_mdl_summary_at_index(self, mdl_litigations: pd.DataFrame, target_idx: int) -> bool:
        try:
            short_summary_value = mdl_litigations.at[target_idx, 'short_summary']

            summary_missing = (
                    pd.isna(short_summary_value) or
                    (isinstance(short_summary_value, str) and not short_summary_value.strip()) or
                    short_summary_value in NULL_CONDITIONS
            )

            return summary_missing

        except Exception as e:
            self.log_error(f"Error checking summary at index {target_idx}: {e}")
            return False

    async def _get_docket_data_for_mdl(self, df: pd.DataFrame, mdl_num: str) -> Optional[Dict]:
        try:
            df['mdl_num_str'] = df['mdl_num'].astype(str).str.replace(r'\.0$', '', regex=True)
            match_row_df = df[df['mdl_num_str'] == mdl_num]

            if match_row_df.empty:
                self.log_warning(f"No docket data found for MDL {mdl_num}")
                return None

            return match_row_df.iloc[0].to_dict()

        except Exception as e:
            self.log_error(f"Error getting docket data for MDL {mdl_num}: {e}")
            return None
        finally:
            if 'mdl_num_str' in df.columns:
                df.drop(columns=['mdl_num_str'], inplace=True)

    async def _find_mdl_index(self, mdl_litigations: pd.DataFrame, mdl_num: str) -> Optional[int]:
        try:
            if 'mdl_num' not in mdl_litigations.columns:
                return None

            match_index = mdl_litigations[mdl_litigations['mdl_num'] == mdl_num].index
            return match_index[0] if not match_index.empty else None

        except Exception as e:
            self.log_error(f"Error finding index for MDL {mdl_num}: {e}")
            return None

    async def _update_mdl_summary_from_docket(self, mdl_litigations: pd.DataFrame,
                                              target_idx: int, mdl_num: str,
                                              docket_data: Dict) -> bool:
        try:
            full_text = await self._extract_text_from_docket(docket_data, mdl_num)

            if not full_text:
                self.log_warning(f"No text content could be obtained for MDL {mdl_num}")
                return False

            self.log_info(f"Generating summary for MDL {mdl_num} from text ({len(full_text)} chars).")
            summary = await self._generate_summary_with_llm(full_text)

            if summary and summary not in NULL_CONDITIONS:
                mdl_litigations.at[target_idx, 'short_summary'] = summary
                self.log_info(f"Successfully updated MDL {mdl_num} with summary: '{summary[:100]}...'")
                return True
            else:
                self.log_warning(f"LLM did not generate a valid summary for MDL {mdl_num}")
                return False

        except Exception as e:
            self.log_error(f"Error updating MDL summary for {mdl_num}: {e}")
            return False

    async def _extract_text_from_docket(self, docket_data: Dict, mdl_num: str) -> str:
        try:
            docket_dir = await self._get_dockets_directory()
            if not docket_dir:
                return ""

            base_filename = docket_data.get('new_filename') or docket_data.get('original_filename')
            if not base_filename:
                self.log_error(f"Could not determine filename for MDL {mdl_num}")
                return ""

            md_path = os.path.join(docket_dir, f'{base_filename}.md')
            pdf_path = os.path.join(docket_dir, f'{base_filename}.pdf')

            if os.path.exists(md_path):
                try:
                    with open(md_path, 'r', encoding='utf-8') as f:
                        full_text = f.read()
                    if full_text and len(full_text.strip()) > 10:
                        self.log_debug(f"Loaded text from {os.path.basename(md_path)} for MDL {mdl_num}")
                        return full_text
                except Exception as e:
                    self.log_warning(f"Error reading .md file {os.path.basename(md_path)}: {e}")

            if os.path.exists(pdf_path):
                self.log_warning(f"PDF text extraction not yet implemented for {pdf_path}")

            return ""

        except Exception as e:
            self.log_error(f"Error extracting text for MDL {mdl_num}: {e}")
            return ""

    async def _generate_summary_with_llm(self, text: str) -> Optional[str]:
        try:
            if not hasattr(self.gpt, 'extract_summary'):
                self.log_error("LLM client does not have 'extract_summary' method.")
                return None

            if asyncio.iscoroutinefunction(self.gpt.extract_summary):
                summary = await self.gpt.extract_summary(text, 'short')
            else:
                summary = self.gpt.extract_summary(text, 'short')

            return summary if summary and summary not in NULL_CONDITIONS else None

        except Exception as e:
            self.log_error(f"Error during LLM summary generation: {e}")
            return None

    async def _find_docket_data_by_filename(self, df: pd.DataFrame, base_filename: str,
                                            json_path: str) -> Optional[Dict]:
        try:
            file_identifier = os.path.splitext(base_filename)[0]

            match_row_df = df[
                (df.get('new_filename') == file_identifier) |
                (df.get('original_filename') == file_identifier)
                ]

            if not match_row_df.empty:
                return match_row_df.iloc[0].to_dict()

            self.log_debug(f"No data found in pre-read DataFrame for {base_filename}, loading directly")
            data = self.file_handler.load_json(json_path)
            return data if data else None

        except Exception as e:
            self.log_error(f"Error finding docket data for {base_filename}: {e}")
            return None

    async def _extract_mdl_number_from_data(self, docket_data: Dict) -> Optional[str]:
        try:
            mdl_num_raw = docket_data.get('mdl_num')

            if mdl_num_raw in NULL_CONDITIONS or pd.isna(mdl_num_raw):
                return None

            mdl_num = str(mdl_num_raw).lstrip('0').replace('.0', '')

            return mdl_num if mdl_num else None

        except Exception as e:
            self.log_error(f"Error extracting MDL number: {e}")
            return None

    async def _filter_entries_needing_summary(self, mdl_litigations: pd.DataFrame) -> pd.DataFrame:
        if mdl_litigations.empty:
            return pd.DataFrame()

        required_cols = ['description', 'short_summary', 'mdl_num']
        missing_cols = [col for col in required_cols if col not in mdl_litigations.columns]

        if missing_cols:
            self.log_error(f"MDL DataFrame missing required columns: {missing_cols}")
            return pd.DataFrame()

        mdl_litigations_unique = mdl_litigations.drop_duplicates(subset=['mdl_num'], keep='first')

        needs_summary_mask = (
                mdl_litigations_unique['description'].notna() &
                (mdl_litigations_unique['description'].astype(str).str.strip().str.len() > 10) &
                (
                        mdl_litigations_unique['short_summary'].isna() |
                        (mdl_litigations_unique['short_summary'].astype(str).str.strip().str.len() <= 3) |
                        (mdl_litigations_unique['short_summary'].isin(NULL_CONDITIONS))
                )
        )

        return mdl_litigations_unique[needs_summary_mask]

    async def _update_summary_in_dataframe(self, mdl_litigations: pd.DataFrame,
                                           mdl_num: str, summary: str):
        try:
            original_indices = mdl_litigations[mdl_litigations['mdl_num'] == mdl_num].index

            if not original_indices.empty:
                mdl_litigations.loc[original_indices, 'short_summary'] = summary
                self.log_debug(
                    f"Generated summary for MDL {mdl_num}: '{summary[:100]}...' "
                    f"(updated {len(original_indices)} rows)"
                )
            else:
                self.log_warning(
                    f"MDL {mdl_num} was found for summarization but original index not found?"
                )

        except Exception as e:
            self.log_error(f"Error updating summary for MDL {mdl_num}: {e}")
