# /src/services/transformer/components.mdl/data_processor.py
"""

Core MDL data processing and validation operations.

This module handles core MDL data processing, validation, and information
addition extracted from mdl_processor.py as part of Phase 3.3 refactoring.
Moved to internal components as part of facade pattern implementation.
"""
import logging
import os
import re
from typing import Dict, List, Optional, Any

import pandas as pd

from src.transformer.config.constants import NULL_CONDITIONS
from src.transformer.validation_utils import (
    validate_mdl_number,
    is_null_or_empty,
    ValidationMixin
)


class MDLDataProcessor:
    """Internal component for core MDL data processing and validation operations."""


    
    def __init__(self,
                 config: Optional[Dict] = None,
                 logger: Optional[logging.Logger] = None):
        self.config = config or {}
        self.logger = logger if logger else logging.getLogger(__name__)

    def log_debug(self, message: str, **kwargs):
        """Log debug message."""
        self.logger.debug(message, **kwargs)

    def log_info(self, message: str, **kwargs):
        """Log info message."""
        self.logger.info(message, **kwargs)

    def log_warning(self, message: str, **kwargs):
        """Log warning message."""
        self.logger.warning(message, **kwargs)

    def log_error(self, message: str, **kwargs):
        """Log error message."""
        self.logger.error(message, **kwargs)

    async def add_mdl_info_to_docket(self, data: Dict, mdl_litigations: pd.DataFrame) -> bool:
        """
        Add MDL details (litigation name as title, short_summary as allegations)
        from the loaded MDL lookup table into the provided docket data dictionary.
        
        Args:
            data: The docket data dictionary to update (modified in place)
            mdl_litigations: DataFrame with MDL litigation data
            
        Returns:
            True if MDL info was added, False otherwise
        """
        try:
            if mdl_litigations is None or mdl_litigations.empty:
                self.log_debug("MDL litigation data not loaded. Cannot add MDL info.")
                # Ensure keys exist even if no MDL data is available
                data.setdefault('title', '')
                data.setdefault('allegations', '')
                return False

            # Extract and validate MDL number
            mdl_num = await self._extract_and_validate_mdl_number(data)
            if not mdl_num:
                data.setdefault('title', '')
                data.setdefault('allegations', '')
                return False

            # Find matching MDL entry
            mdl_info = await self._find_mdl_info(mdl_litigations, mdl_num)
            if not mdl_info:
                docket_num_for_log = data.get('docket_num', 'unknown docket')
                self.log_warning(
                    f"No matching MDL entry found for number {mdl_num} (from docket {docket_num_for_log})."
                )
                data.setdefault('title', '')
                data.setdefault('allegations', '')
                return False

            # Update title and allegations
            title_updated = await self._update_title_from_mdl(data, mdl_info, mdl_num)
            allegations_updated = await self._update_allegations_from_mdl(data, mdl_info, mdl_num)

            # Log the update results
            await self._log_mdl_info_update(data, mdl_num, title_updated, allegations_updated)

            return title_updated or allegations_updated

        except Exception as e:
            self.log_error(f"Error adding MDL info for docket {data.get('docket_num', 'unknown')}: {str(e)}",
                           exc_info=True)
            # Ensure keys exist on error too
            data.setdefault('title', '')
            data.setdefault('allegations', '')
            return False

    async def filter_mdl_docket_patterns(self, json_paths: List[str]) -> List[str]:
        """
        Filter out specific MDL docket patterns from a list of JSON file paths.
        
        Args:
            json_paths: List of JSON file paths to filter
            
        Returns:
            List of filtered file paths (non-MDL pattern files)
        """
        if not json_paths:
            return []

        filtered_paths = []
        filtered_count = 0

        for path in json_paths:
            base_filename = os.path.basename(path)
            if self._is_mdl_pattern_file(base_filename):
                filtered_count += 1
                self.log_debug(f"Filtered out MDL pattern file: {base_filename}")
            else:
                filtered_paths.append(path)

        self.log_info(f"Filtered {filtered_count} MDL pattern files, {len(filtered_paths)} files remain")
        return filtered_paths

    async def validate_docket_mdl_data(self, data: Dict) -> Dict[str, Any]:
        """
        Validate MDL-related data in a docket entry.
        
        Args:
            data: Docket data dictionary to validate
            
        Returns:
            Validation report with status and details
        """
        report = {
            'status': 'success',
            'warnings': [],
            'errors': [],
            'mdl_info_complete': False,
            'has_mdl_data': False
        }

        try:
            # Check for docket number
            docket_num = data.get('docket_num')
            if not docket_num or is_null_or_empty(docket_num):
                report['warnings'].append("No docket number found")

            # Check for MDL number
            mdl_num_raw = data.get('mdl_num')

            if is_null_or_empty(mdl_num_raw) or pd.isna(mdl_num_raw):
                return report

            # Validate MDL number format
            mdl_num = await self._extract_and_validate_mdl_number(data)
            if not mdl_num:
                report['warnings'].append(f"Invalid MDL number format: {mdl_num_raw}")
                return report

            report['has_mdl_data'] = True

            # Check for title and allegations
            title = data.get('title', '')
            allegations = data.get('allegations', '')

            has_title = bool(title and not is_null_or_empty(title))
            has_allegations = bool(allegations and not is_null_or_empty(allegations))

            if not has_title:
                report['warnings'].append("MDL case missing title")

            if not has_allegations:
                report['warnings'].append("MDL case missing allegations")

            # Set complete flag based on having both title and allegations
            report['mdl_info_complete'] = has_title and has_allegations

            # Validate MDL number is numeric
            if not mdl_num.isdigit():
                report['warnings'].append(f"MDL number is not numeric: {mdl_num}")

            # Check for common invalid MDL numbers
            invalid_mdl_numbers = ['9000', '0', '']
            if mdl_num in invalid_mdl_numbers:
                report['warnings'].append(f"Potentially invalid MDL number: {mdl_num}")

        except Exception as e:
            report['errors'].append(f"Error validating MDL data: {str(e)}")
            report['status'] = 'error'

        # Set overall status
        if report['errors']:
            report['status'] = 'error'
        elif report['warnings']:
            report['status'] = 'warning'

        return report

    async def process_mdl_file_list(self, file_list: List[str],
                                    filter_mdl_patterns: bool = True) -> Dict[str, Any]:
        """
        Process a list of files for MDL-related operations.
        
        Args:
            file_list: List of file paths to process
            filter_mdl_patterns: Whether to filter out MDL pattern files
            
        Returns:
            Processing report with statistics and filtered file list
        """
        report = {
            'total_files': len(file_list),
            'filtered_files': [],
            'excluded_files': [],
            'processing_stats': {
                'json_files': 0,
                'mdl_pattern_files': 0,
                'other_files': 0
            }
        }

        if not file_list:
            self.log_info("No files provided for MDL processing")
            return report

        self.log_info(f"Processing {len(file_list)} files for MDL operations")

        # Define MDL pattern
        mdl_pattern = r'.*[a-zA-Z]{3,5}_\d{2}_\d{5}_M\d+\.json$'

        for file_path in file_list:
            try:
                base_filename = os.path.basename(file_path)

                # Check file type
                if file_path.endswith('.json'):
                    report['processing_stats']['json_files'] += 1

                    # Check for MDL pattern
                    if re.match(mdl_pattern, base_filename):
                        report['processing_stats']['mdl_pattern_files'] += 1
                        if filter_mdl_patterns:
                            report['excluded_files'].append({
                                'path': file_path,
                                'reason': 'MDL pattern file'
                            })
                            continue

                    report['filtered_files'].append(file_path)
                else:
                    report['processing_stats']['other_files'] += 1
                    if not filter_mdl_patterns:
                        report['filtered_files'].append(file_path)
                    else:
                        report['excluded_files'].append({
                            'path': file_path,
                            'reason': 'Non-JSON file'
                        })

            except Exception as e:
                self.log_error(f"Error processing file {file_path}: {e}")
                report['excluded_files'].append({
                    'path': file_path,
                    'reason': f'Processing error: {str(e)}'
                })

        self.log_info(
            f"File processing complete: {len(report['filtered_files'])} files to process, "
            f"{len(report['excluded_files'])} excluded"
        )

        return report

    async def _extract_and_validate_mdl_number(self, data: Dict) -> Optional[str]:
        """Extract and validate MDL number from docket data."""
        mdl_num_raw = data.get('mdl_num')
        docket_num_for_log = data.get('docket_num', 'unknown docket')

        if is_null_or_empty(mdl_num_raw) or pd.isna(mdl_num_raw):
            return None

        # Use validation utility
        mdl_result = validate_mdl_number(mdl_num_raw)
        if not mdl_result.is_valid:
            self.log_debug(
                f"{mdl_result.error_message} for docket {docket_num_for_log}."
            )
            return None

        return mdl_result.value  # Return the cleaned value from validation

    async def _find_mdl_info(self, mdl_litigations: pd.DataFrame, mdl_num: str) -> Optional[Dict]:
        """Find MDL information in the litigation DataFrame."""
        try:
            if 'mdl_num' not in mdl_litigations.columns:
                self.log_error("MDL lookup DataFrame missing 'mdl_num' column.")
                return None

            match_index = mdl_litigations[mdl_litigations['mdl_num'] == mdl_num].index

            if match_index.empty:
                return None

            return mdl_litigations.loc[match_index[0]].to_dict()

        except Exception as e:
            self.log_error(f"Error finding MDL info for {mdl_num}: {e}")
            return None

    async def _update_title_from_mdl(self, data: Dict, mdl_info: Dict, mdl_num: str) -> bool:
        """Update title from MDL litigation name."""
        current_title_for_logging = data.get('title', '')
        mdl_litigation_name = mdl_info.get('litigation')

        if pd.notna(mdl_litigation_name) and isinstance(mdl_litigation_name, str) and mdl_litigation_name.strip():
            new_title = mdl_litigation_name.strip()
            if data.get('title') != new_title:
                data['title'] = new_title
                self.log_info(
                    f"Updated title for MDL {mdl_num} from MDL lookup ('litigation' field) to: '{new_title[:100]}...'"
                )
                return True
            else:
                self.log_debug(
                    f"Title for MDL {mdl_num} from MDL lookup ('{new_title[:100]}...') matches existing. No change to title."
                )
                return True  # Considered "set" as it matched
        else:
            self.log_debug(
                f"MDL lookup ('litigation' field) did not provide a valid name for MDL {mdl_num}. "
                f"Title ('{str(current_title_for_logging)[:100]}...') remains unchanged."
            )
            # Ensure 'title' key exists if it was missing
            data.setdefault('title', '')
            return False

    async def _update_allegations_from_mdl(self, data: Dict, mdl_info: Dict, mdl_num: str) -> bool:
        """Update allegations from MDL summary/description."""
        current_allegations_for_logging = data.get('allegations', '')
        mdl_allegations_source_content = None
        mdl_allegations_source_field = "None"

        # Try different source fields in order of preference
        for source_field_candidate in ['short_summary', 'description', 'summary']:
            potential_content = mdl_info.get(source_field_candidate)
            if pd.notna(potential_content) and isinstance(potential_content, str) and potential_content.strip():
                mdl_allegations_source_content = potential_content.strip()
                mdl_allegations_source_field = source_field_candidate
                self.log_debug(
                    f"Found allegations from MDL lookup ('{mdl_allegations_source_field}' field) for {mdl_num}"
                )
                break

        if mdl_allegations_source_content:
            if data.get('allegations') != mdl_allegations_source_content:
                data['allegations'] = mdl_allegations_source_content
                self.log_info(
                    f"Updated allegations for MDL {mdl_num} from MDL lookup ('{mdl_allegations_source_field}' field)."
                )
                return True
            else:
                self.log_debug(
                    f"Allegations for MDL {mdl_num} from MDL lookup ('{mdl_allegations_source_field}') match existing. "
                    "No change to allegations."
                )
                return True  # Considered "set" as it matched
        else:
            self.log_debug(
                f"MDL lookup did not provide valid allegations content (from short_summary/description/summary) "
                f"for {mdl_num}. Allegations ('{str(current_allegations_for_logging)[:100]}...') remain unchanged."
            )
            # Ensure 'allegations' key exists if it was missing
            data.setdefault('allegations', '')
            return False

    async def _log_mdl_info_update(self, data: Dict, mdl_num: str,
                                   title_updated: bool, allegations_updated: bool):
        """Log the results of MDL info update."""
        docket_num_for_log = data.get('docket_num', 'unknown docket')

        # Determine status messages
        title_status = 'Set from MDL litigation name' if title_updated else 'Kept Existing or Missing'
        allegations_status = 'Set from MDL data' if allegations_updated else 'Kept Existing or Missing'

        self.log_debug(
            f"Applied MDL info (Title: {title_status}, Allegations: {allegations_status}) "
            f"for MDL {mdl_num} to docket {docket_num_for_log}."
        )

    def _clean_mdl_number(self, mdl_num_raw: str) -> str:
        """Clean MDL number by removing leading zeros and float artifacts."""
        if not mdl_num_raw:
            return ''
        return str(mdl_num_raw).lstrip('0').replace('.0', '')

    def _is_valid_mdl_content(self, content: str) -> bool:
        """Check if MDL content is valid (not null/empty)."""
        if is_null_or_empty(content):
            return False
        if isinstance(content, str) and not content.strip():
            return False
        return True

    def _get_allegations_source_field(self, mdl_info: Dict) -> tuple:
        """Get allegations source field content and field name."""
        for field in ['short_summary', 'description', 'summary']:
            content = mdl_info.get(field)
            if content and not is_null_or_empty(content) and str(content).strip():
                return content.strip(), field
        return None, "None"

    def _is_mdl_pattern_file(self, filename: str) -> bool:
        """Check if filename matches MDL pattern."""
        import re
        # Pattern matching the test cases: mdl_2873_transfer.json, MDL3019_motion.json, etc.
        mdl_pattern = r'.*[mM][dD][lL][_-]?\d+.*\.json$'
        return bool(re.match(mdl_pattern, filename))

    def validate_mdl_processing_data(self, data: Dict) -> Dict[str, Any]:
        """
        Validate MDL processing data and return validation report.
        
        Args:
            data: Data dictionary to validate
            
        Returns:
            Validation report with status and details
        """
        report = {
            'status': 'success',
            'warnings': [],
            'errors': [],
            'has_docket_num': False,
            'has_mdl_num': False,
            'mdl_num_valid': False,
            'has_title': False,
            'has_allegations': False
        }

        # Check for docket number (optional)
        docket_num = data.get('docket_num')
        if docket_num and not is_null_or_empty(docket_num):
            report['has_docket_num'] = True

        # Check for MDL number
        mdl_num_raw = data.get('mdl_num')
        if mdl_num_raw and not is_null_or_empty(mdl_num_raw):
            report['has_mdl_num'] = True

            # Validate MDL number format using utility
            mdl_result = validate_mdl_number(mdl_num_raw)
            if mdl_result.is_valid:
                report['mdl_num_valid'] = True
            else:
                report['warnings'].append(mdl_result.error_message)
                if mdl_result.suggestion:
                    report['warnings'].append(f"Suggestion: {mdl_result.suggestion}")
        else:
            # If we're validating MDL processing data, missing MDL number should be a warning
            report['warnings'].append("Missing or invalid MDL number for MDL processing")

        # Check for title
        title = data.get('title')
        if title and not is_null_or_empty(title) and str(title).strip():
            report['has_title'] = True
        else:
            # Only warn about missing title if we have an MDL number
            if report['has_mdl_num']:
                report['warnings'].append("Missing title for MDL case")

        # Check for allegations
        allegations = data.get('allegations')
        if allegations and not is_null_or_empty(allegations) and str(allegations).strip():
            report['has_allegations'] = True
        else:
            # Only warn about missing allegations if we have an MDL number
            if report['has_mdl_num']:
                report['warnings'].append("Missing allegations for MDL case")

        # Set overall status
        if report['errors']:
            report['status'] = 'error'
        elif report['warnings']:
            report['status'] = 'warning'

        return report

    def get_mdl_processing_summary(self, data: Dict) -> str:
        """
        Get summary of MDL data processing results.
        
        Args:
            data: Processed data dictionary
            
        Returns:
            Human-readable summary
        """
        # Check if we have MDL data
        mdl_num = data.get('mdl_num')
        if not mdl_num or is_null_or_empty(mdl_num):
            return "No MDL processing data available"

        # Get docket number for context
        docket_num = data.get('docket_num', 'unknown')

        # Check completeness
        title = data.get('title', '')
        allegations = data.get('allegations', '')

        has_title = title and not is_null_or_empty(title)
        has_allegations = allegations and not is_null_or_empty(allegations)

        if has_title and has_allegations:
            status = "complete"
        elif has_title or has_allegations:
            status = "incomplete"
        else:
            status = "incomplete"

        return f"Docket {docket_num}: MDL {mdl_num} processing {status}"