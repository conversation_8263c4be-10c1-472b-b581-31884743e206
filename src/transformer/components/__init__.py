"""
Transformer Components Module

Component-based architecture organized by functional areas:
- case_classification/: Classification logic and transfer handling
- data_cleaning/: Cleaning, validation, court processing, LLM integration
- data_upload/: S3 upload and utilities
- docket/: HTML processing, text handling, validation
- file/: Caching, file operations, PDF handling
- law_firm/: Integration and processing
- mdl/: MDL processing, lookup, persistence
"""
