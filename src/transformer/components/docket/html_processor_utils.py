import logging
import re
from typing import Dict, List, Optional, Any

from src.transformer.config.constants import NULL_CONDITIONS

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError


class HTMLProcessorUtils(AsyncServiceBase):
    """Utility functions for the HTMLProcessor."""



    def __init__(self,
                 html_data_updater: Any = None,
                 config: Optional[Dict] = None,
                 logger: Optional[logging.Logger] = None,
                 html_processing_service: Optional[Any] = None):
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.html_data_updater = html_data_updater
        self.html_processing_service = html_processing_service

    async def _execute_action(self, data: Any) -> Any:
        """Execute HTMLProcessorUtils actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'process_s3_html':
                docket_data = action_data.get('docket_data', {})
                json_path = action_data.get('json_path', '')
                return await self.process_s3_html(docket_data, json_path)
            else:
                raise TransformerServiceError(f"Unknown action: {action}")
        else:
            raise TransformerServiceError(f"Invalid data format for HTMLProcessorUtils: {type(data)}")
        
        return None

    async def process_s3_html(self, data: Dict, json_path: str) -> bool:
        if not isinstance(data, dict):
            self.log_error("Invalid data dictionary provided to process_s3_html")
            return False

        filename = data.get('new_filename', data.get('docket_num', 'unknown'))

        self.log_info(f"[{filename}] Starting HTML processing. s3_html field present: {'s3_html' in data}")
        if 's3_html' in data:
            self.log_info(f"[{filename}] s3_html value: {data['s3_html']}")

        try:
            if self.html_processing_service:
                self.log_debug(f"Using enhanced HTML processing service for {filename}")
                
                # Check if this is the integration service that needs court_id
                if hasattr(self.html_processing_service, 'process_docket_html'):
                    # Use the integration service's process_docket_html method
                    court_id = data.get('court_id')
                    if not court_id:
                        self.log_warning(f"[{filename}] No court_id found in data, cannot process HTML with integration service")
                        return False
                    
                    self.log_info(f"[{filename}] Using integration service with court_id: {court_id}")
                    return await self.html_processing_service.process_docket_html(data, json_path, court_id)
                    
                # Check if this service has process_html_content method
                elif hasattr(self.html_processing_service, 'process_html_content'):
                    s3_html_link = self._extract_s3_html_link(data)
                    if not s3_html_link:
                        self.log_debug(f"No S3 HTML link found for {filename}")
                        return False

                    html_content = await self._download_html_content(s3_html_link, filename)
                    if not html_content:
                        self.log_error(f"[{filename}] Failed to download HTML content - aborting HTML processing")
                        return False

                    self.log_info(f"[{filename}] Successfully downloaded HTML content: {len(html_content)} chars")
                    original_data = data.copy()
                    updated_data = await self.html_processing_service.process_html_content(
                        data, html_content, json_path
                    )

                    if updated_data.get('_html_processing_failed') or updated_data.get('_html_processing_error'):
                        self.log_warning(
                            f"HTML processing failed for {filename}, not merging fields. "
                            f"Error: {updated_data.get('_html_processing_error', 'Unknown error')}"
                        )
                        data['_html_processing_error'] = updated_data.get('_html_processing_error')
                        data['_html_processing_failed'] = True
                        if 's3_html_error' in updated_data:
                            data['s3_html_error'] = updated_data['s3_html_error']
                        if 'html_update_error' in updated_data:
                            data['html_update_error'] = updated_data['html_update_error']
                        return False

                    data.update(updated_data)

                    if hasattr(self.html_processing_service, 'validate_html_processing_results'):
                        validation_report = self.html_processing_service.validate_html_processing_results(
                            data, original_data
                        )

                        if validation_report['changes']:
                            self.log_info(f"Enhanced HTML processing for {filename}: {', '.join(validation_report['changes'])}")
                            return True
                        else:
                            self.log_debug(f"No updates from enhanced HTML processing for {filename}")
                            return False
                    else:
                        self.log_info(f"Enhanced HTML processing completed for {filename}")
                        return True
                else:
                    self.log_error(f"[{filename}] HTML processing service does not have required methods")
                    return False
            else:
                self.log_info(f"[{filename}] Using fallback HTML processing (no enhanced service available)")
                s3_html_link = self._extract_s3_html_link(data)
                if not s3_html_link:
                    self.log_warning(f"[{filename}] No S3 HTML link found - cannot process HTML")
                    return False

                self.log_info(f"[{filename}] Found S3 HTML link: {s3_html_link}")
                parsed_content = await self._parse_html_content(s3_html_link, filename)
                if not parsed_content:
                    self.log_error(f"[{filename}] Failed to parse HTML content")
                    return False

                self.log_info(f"[{filename}] Successfully parsed HTML content: {type(parsed_content)}")
                parties_updated = self._extract_party_information(data, parsed_content, filename)
                self.log_info(f"[{filename}] Party information extraction result: {parties_updated}")
                case_info_updated = self._extract_case_information(data, parsed_content, filename)
                self.log_info(f"[{filename}] Case information extraction result: {case_info_updated}")
                self._cleanup_plural_fields(data)

                if parties_updated or case_info_updated:
                    self.log_info(f"[{filename}] HTML processing SUCCESS - Updated docket data from HTML")
                    return True
                else:
                    self.log_warning(f"[{filename}] HTML processing completed but NO DATA was extracted")
                    return False

        except Exception as e:
            self.log_error(f"Error processing S3 HTML for {filename}: {e}")
            return False

    def _extract_s3_html_link(self, data: Dict) -> Optional[str]:
        html_link_fields = ['s3_html', 's3_html_link', 's3_link_html', 'html_link', 's3_link']
        available_fields = [f for f in html_link_fields if f in data]
        self.log_info(f"[HTML Link Extraction] Looking for HTML link. Available fields: {available_fields}")

        for field in available_fields:
            value = data.get(field)
            self.log_info(f"[HTML Link Extraction] Field '{field}' contains: {value}")

        for field in html_link_fields:
            link = data.get(field)
            if link and isinstance(link, str):
                self.log_debug(f"[HTML Link Extraction] Checking field '{field}' with value: {link}")
                if '.html' in link.lower() or (field == 's3_html' and '/html/' in link):
                    self.log_info(f"[HTML Link Extraction] Found valid HTML link in field '{field}'")
                    if not link.startswith('http'):
                        original_link = link
                        link = link.lstrip('/')
                        link = f"https://cdn.lexgenius.ai/{link}"
                        self.log_info(
                            f"[HTML Link Extraction] Converted relative path '{original_link}' to full URL: {link}")
                    self.log_info(f"[HTML Link Extraction] RETURNING HTML link from field '{field}': {link}")
                    return link
                else:
                    self.log_debug(f"[HTML Link Extraction] Field '{field}' does not contain valid HTML link pattern")

        s3_link = data.get('s3_link')
        if s3_link and isinstance(s3_link, str):
            if s3_link.lower().endswith('.html'):
                return s3_link
            elif s3_link.lower().endswith('.pdf'):
                html_link = s3_link.replace('.pdf', '.html')
                if '/dockets/' in html_link:
                    html_link = html_link.replace('/dockets/', '/html/')
                return html_link

        self.log_warning(f"[HTML Link Extraction] No HTML link found in any of the checked fields: {html_link_fields}")
        return None

    async def _parse_html_content(self, html_link: str, filename: str) -> Optional[Dict]:
        self.log_info(f"[HTML Parse] Starting HTML parsing for {filename}")
        try:
            self.log_info(f"[HTML Parse] Attempting direct HTML download and parse for {filename}")
            html_content = await self._download_html_content(html_link, filename)

            if html_content:
                direct_parsed = await self._direct_parse_html(html_content, filename)
                if direct_parsed:
                    self.log_info(f"[HTML Parse] Direct parsing successful for {filename}")
                    return direct_parsed

            if not self.html_data_updater:
                self.log_warning(
                    f"[HTML Parse] HTML data updater not available for {filename}, and direct parsing failed")
                return None

            self.log_info(f"[HTML Parse] Using HTML data updater for {filename}")

            if hasattr(self.html_data_updater, 'parse_html_async'):
                self.log_debug(f"[HTML Parse] Using parse_html_async method")
                parsed_content = await self.html_data_updater.parse_html_async(html_link)
            elif hasattr(self.html_data_updater, 'parse_html'):
                self.log_debug(f"[HTML Parse] Using parse_html method with asyncio.to_thread")
                import asyncio
                parsed_content = await asyncio.to_thread(self.html_data_updater.parse_html, html_link)
            else:
                self.log_error(f"[HTML Parse] HTML data updater missing parse methods for {filename}")
                return None

            if parsed_content and isinstance(parsed_content, dict):
                self.log_info(
                    f"[HTML Parse] Successfully parsed HTML content for {filename} - Keys: {list(parsed_content.keys())}")
                return parsed_content
            else:
                self.log_warning(
                    f"[HTML Parse] HTML parsing returned empty/invalid content for {filename}: {type(parsed_content)}")
                return None
        except Exception as e:
            self.log_error(f"[HTML Parse] Error parsing HTML content for {filename}: {e}")
            return None

    def _extract_party_information(self, data: Dict, parsed_content: Dict, filename: str) -> bool:
        updated_parties = False
        plaintiff_names = self._extract_plaintiff_names(parsed_content)
        if plaintiff_names:
            current_plaintiff = data.get('plaintiff', [])
            should_overwrite = (
                    current_plaintiff is None or
                    current_plaintiff == [] or
                    not isinstance(current_plaintiff, list) or
                    all(p in NULL_CONDITIONS for p in current_plaintiff)
            )
            if should_overwrite:
                data['plaintiff'] = plaintiff_names
                updated_parties = True
                self.log_info(f"Updated 'plaintiff' from HTML: {plaintiff_names} for {filename}")
            else:
                self.log_debug(f"Skipping plaintiff update - existing data: {current_plaintiff} for {filename}")

        defendant_names = self._extract_defendant_names(parsed_content)
        if defendant_names:
            current_defendant = data.get('defendant', [])
            current_defendants = data.get('defendants', [])
            should_overwrite_defendant = (
                    current_defendant is None or
                    current_defendant == [] or
                    not isinstance(current_defendant, list) or
                    all(d in NULL_CONDITIONS for d in current_defendant)
            )
            should_overwrite_defendants = (
                    current_defendants is None or
                    current_defendants == [] or
                    not isinstance(current_defendants, list) or
                    all(d in NULL_CONDITIONS for d in current_defendants)
            )
            if should_overwrite_defendant:
                data['defendant'] = defendant_names
                updated_parties = True
                self.log_info(f"Updated 'defendant' from HTML: {defendant_names} for {filename}")
            else:
                self.log_debug(f"Skipping defendant update - existing data: {current_defendant} for {filename}")

            if should_overwrite_defendants:
                data['defendants'] = defendant_names
                updated_parties = True
                self.log_info(f"Updated 'defendants' from HTML: {defendant_names} for {filename}")
            else:
                self.log_debug(f"Skipping defendants update - existing data: {current_defendants} for {filename}")
        return updated_parties

    def _extract_plaintiff_names(self, parsed_content: Dict) -> List[str]:
        plaintiff_names = []
        plaintiff_fields = ['plaintiffs', 'plaintiff', 'plaintiff_names']
        for field in plaintiff_fields:
            raw_plaintiffs = parsed_content.get(field, [])
            if isinstance(raw_plaintiffs, list):
                seen_plaintiffs = set()
                for p in raw_plaintiffs:
                    if isinstance(p, dict):
                        name = p.get('name') or p.get('plaintiff_name') or str(p)
                    elif isinstance(p, str):
                        name = p
                    else:
                        continue
                    cleaned_name = self._clean_party_name(name)
                    if cleaned_name and cleaned_name not in seen_plaintiffs:
                        plaintiff_names.append(cleaned_name)
                        seen_plaintiffs.add(cleaned_name)
            elif isinstance(raw_plaintiffs, str) and raw_plaintiffs.strip():
                cleaned_name = self._clean_party_name(raw_plaintiffs)
                if cleaned_name:
                    plaintiff_names.append(cleaned_name)
        return plaintiff_names

    def _extract_defendant_names(self, parsed_content: Dict) -> List[str]:
        defendant_names = []
        defendant_fields = ['defendants', 'defendant', 'defendant_names']
        for field in defendant_fields:
            raw_defendants = parsed_content.get(field, [])
            if isinstance(raw_defendants, list):
                seen_defendants = set()
                for d in raw_defendants:
                    if isinstance(d, dict):
                        name = d.get('name') or d.get('defendant_name') or str(d)
                    elif isinstance(d, str):
                        name = d
                    else:
                        continue
                    cleaned_name = self._clean_party_name(name)
                    if cleaned_name and cleaned_name not in seen_defendants:
                        defendant_names.append(cleaned_name)
                        seen_defendants.add(cleaned_name)
            elif isinstance(raw_defendants, str) and raw_defendants.strip():
                cleaned_name = self._clean_party_name(raw_defendants)
                if cleaned_name:
                    defendant_names.append(cleaned_name)
        return defendant_names

    def _clean_party_name(self, name: str) -> Optional[str]:
        if not isinstance(name, str):
            return None
        cleaned = name.strip()
        cleaned = re.sub(r'^(plaintiff|defendant):\s*', '', cleaned, flags=re.IGNORECASE)
        cleaned = re.sub(r'\s*\(.*?\)\s*$', '', cleaned)
        if not cleaned or cleaned in NULL_CONDITIONS:
            return None
        if len(cleaned) < 2:
            return None
        invalid_patterns = [
            r'^[0-9\-\s]+$',
            r'^[^a-zA-Z]*$',
            r'^\W+$'
        ]
        for pattern in invalid_patterns:
            if re.match(pattern, cleaned):
                return None
        return cleaned

    def _extract_case_information(self, data: Dict, parsed_content: Dict, filename: str) -> bool:
        updated_case_info = False
        case_info_fields = {
            'nature_of_suit': ['nature_of_suit', 'nature', 'cause'],
            'jurisdiction': ['jurisdiction', 'court_jurisdiction'],
            'case_type': ['case_type', 'type'],
            'judge': ['judge', 'assigned_judge', 'presiding_judge'],
            'case_flags': ['case_flags', 'flags', 'status_flags']
        }
        for target_field, source_fields in case_info_fields.items():
            for source_field in source_fields:
                if source_field in parsed_content:
                    value = parsed_content[source_field]
                    if target_field not in data or data[target_field] in NULL_CONDITIONS:
                        if value not in NULL_CONDITIONS:
                            data[target_field] = value
                            updated_case_info = True
                            self.log_debug(f"Updated '{target_field}' from HTML for {filename}")
                    break
        if 'case_info' in parsed_content and isinstance(parsed_content['case_info'], dict):
            case_info = parsed_content['case_info']
            for key, value in case_info.items():
                if key not in ['plaintiffs', 'defendants', 'plaintiff', 'defendant']:
                    if key not in data or data[key] in NULL_CONDITIONS:
                        if value not in NULL_CONDITIONS:
                            data[key] = value
                            updated_case_info = True
        return updated_case_info

    async def _download_html_content(self, html_link: str, filename: str) -> Optional[str]:
        self.log_info(f"[HTML Download] Starting download for {filename} from: {html_link}")
        try:
            import aiohttp
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                self.log_debug(f"[HTML Download] Created aiohttp session for {filename}")
                async with session.get(html_link) as response:
                    self.log_info(f"[HTML Download] Response status for {filename}: {response.status}")
                    if response.status == 200:
                        content = await response.text()
                        self.log_info(f"[HTML Download] SUCCESS - Downloaded {len(content)} chars for {filename}")
                        preview = content[:500] if content else ""
                        self.log_debug(f"[HTML Download] Content preview for {filename}: {preview}...")
                        return content
                    else:
                        self.log_error(
                            f"[HTML Download] FAILED - HTTP {response.status} for {filename} from {html_link}")
                        return None
        except Exception as e:
            self.log_error(f"[HTML Download] ERROR - Failed to download HTML for {filename}: {e}")
            return None

    def _cleanup_plural_fields(self, data: Dict) -> None:
        fields_to_remove = ['plaintiffs', 'defendants', 'plaintiffs_gpt']
        for field in fields_to_remove:
            if field in data:
                del data[field]
                self.log_debug(f"Removed plural field '{field}' for consistency")

    async def _direct_parse_html(self, html_content: str, filename: str) -> Optional[Dict]:
        self.log_info(f"[Direct Parse] Attempting direct HTML parsing for {filename}")
        try:
            from bs4 import BeautifulSoup
            import re
            soup = BeautifulSoup(html_content, 'html.parser')
            parsed_data = {}
            for row in soup.find_all('tr'):
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    label = cells[0].get_text(strip=True).lower()
                    value = cells[1].get_text(strip=True)
                    if 'plaintiff' in label and value:
                        if 'plaintiffs' not in parsed_data:
                            parsed_data['plaintiffs'] = []
                        parsed_data['plaintiffs'].append(value)
                        self.log_debug(f"[Direct Parse] Found plaintiff: {value}")
                    elif 'defendant' in label and value:
                        if 'defendants' not in parsed_data:
                            parsed_data['defendants'] = []
                        parsed_data['defendants'].append(value)
                        self.log_debug(f"[Direct Parse] Found defendant: {value}")
            party_sections = soup.find_all(['div', 'section'], string=re.compile(r'(Plaintiff|Defendant)', re.I))
            for section in party_sections:
                next_element = section.find_next_sibling()
                if next_element and next_element.name in ['ul', 'ol']:
                    items = next_element.find_all('li')
                    if 'plaintiff' in section.get_text(strip=True).lower():
                        if 'plaintiffs' not in parsed_data:
                            parsed_data['plaintiffs'] = []
                        for item in items:
                            parsed_data['plaintiffs'].append(item.get_text(strip=True))
                    elif 'defendant' in section.get_text(strip=True).lower():
                        if 'defendants' not in parsed_data:
                            parsed_data['defendants'] = []
                        for item in items:
                            parsed_data['defendants'].append(item.get_text(strip=True))
            for class_name in ['plaintiff', 'plaintiffs', 'defendant', 'defendants']:
                elements = soup.find_all(class_=re.compile(class_name, re.I))
                for elem in elements:
                    text = elem.get_text(strip=True)
                    if text and not text.lower().startswith(class_name):
                        if 'plaintiff' in class_name and 'plaintiffs' not in parsed_data:
                            parsed_data['plaintiffs'] = []
                        if 'defendant' in class_name and 'defendants' not in parsed_data:
                            parsed_data['defendants'] = []
                        if 'plaintiff' in class_name:
                            parsed_data['plaintiffs'].append(text)
                        else:
                            parsed_data['defendants'].append(text)
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for i, row in enumerate(rows):
                    row_text = row.get_text(strip=True).lower()
                    if 'plaintiff' in row_text and row.find('b') and row.find('u'):
                        j = i + 1
                        while j < len(rows) and 'defendant' not in rows[j].get_text(strip=True).lower():
                            bold_tags = rows[j].find_all('b')
                            for tag in bold_tags:
                                name = tag.get_text(strip=True)
                                if name and name not in ['V.', 'v.', '', 'plaintiff', 'PLAINTIFF']:
                                    if '    ' not in name:
                                        if 'plaintiffs' not in parsed_data:
                                            parsed_data['plaintiffs'] = []
                                        if name not in parsed_data['plaintiffs']:
                                            parsed_data['plaintiffs'].append(name)
                                            self.log_debug(f"[Direct Parse] Found plaintiff via table pattern: {name}")
                            j += 1
                    elif 'defendant' in row_text and row.find('b') and row.find('u'):
                        j = i + 1
                        while j < len(rows):
                            bold_tags = rows[j].find_all('b')
                            for tag in bold_tags:
                                name = tag.get_text(strip=True)
                                if name and name not in ['', 'V.', 'v.', 'defendant', 'DEFENDANT']:
                                    if '    ' not in name:
                                        if 'defendants' not in parsed_data:
                                            parsed_data['defendants'] = []
                                        if name not in parsed_data['defendants']:
                                            parsed_data['defendants'].append(name)
                                            self.log_debug(f"[Direct Parse] Found defendant via table pattern: {name}")
                            j += 1
            self.log_info(f"[Direct Parse] Parsed data summary for {filename}: "
                          f"plaintiffs={len(parsed_data.get('plaintiffs', []))}, "
                          f"defendants={len(parsed_data.get('defendants', []))}")
            if parsed_data:
                return parsed_data
            else:
                self.log_warning(f"[Direct Parse] No party data found in HTML for {filename}")
                return None
        except ImportError:
            self.log_error(f"[Direct Parse] BeautifulSoup not available for direct HTML parsing")
            return None
        except Exception as e:
            self.log_error(f"[Direct Parse] Error during direct HTML parsing for {filename}: {e}")
            return None
