# /src/services/transformer/components.law_firm/processor.py
import json
import logging
import os
from typing import Dict, List, Set, Any, Tuple, Optional

from src.transformer.config.constants import NULL_CONDITIONS
import re

# Removed dependency_injector imports - using container-based injection
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.utils.law_firm_normalizer import normalize_law_firm_name


class LawFirmProcessor(AsyncServiceBase):
    """
    Processes attorney and law firm information from docket data,
    including extraction and formatting.
    """


    def __init__(self,
                 config: Optional[Dict] ,
                 logger: Optional[logging.Logger] ,
                 s3_async_storage: Optional[Any] ):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})
        self.ACRONYMS = [  # Not actively used in cleaning, but kept for reference
            'PC', 'PSC', 'PLLC', 'LLC', 'PLC', 'LLP', 'PA', 'APLC', 'LP', 'Ltd', 'APC',
            'LPA', 'INC', 'INCORPORATED', 'PLLP', 'CO', 'CHARTERED', 'A LAW CORPORATION', 'ALC'
        ]

        # Store injected S3 storage service (if provided)
        self.s3_async_storage = s3_async_storage

        self._setup_attorney_lookup()

    async def _execute_action(self, data: Any) -> Any:
        """Execute LawFirmProcessor actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'process_law_firms':
                self.process_law_firms(action_data)
                return action_data
            elif action == 'extract_unique_attorneys':
                return self.extract_unique_attorneys(action_data)
            elif action == 'clean_law_firm_name':
                name = action_data.get('name', '')
                return self._clean_law_firm_name(name)
            elif action == 'extract_and_format_law_firms':
                self._extract_and_format_law_firms(action_data)
                return action_data
            elif action == 'remove_deprecated_keys':
                self.remove_deprecated_keys(action_data)
                return action_data
            elif action == 'load_attorney_lookup':
                self._load_attorney_lookup()
                return {'status': 'loaded'}
            elif action == 'get_attorney_lookup_stats':
                return {
                    'attorney_lookup_loaded': hasattr(self, 'attorney_lookup'),
                    'attorney_count': len(getattr(self, 'attorney_lookup', {})),
                    'config_project_root': self.config.get('project_root')
                }
        raise TransformerServiceError("Invalid action data provided to LawFirmProcessor")

    def _setup_attorney_lookup(self):
        """Setup attorney lookup data during initialization."""
        # Get project_root from config - should be injected through DI
        project_root = self.config.get('project_root')
        if not project_root:
            # Fall back to directories.base_dir
            project_root = self.config.get('directories', {}).get('base_dir')
        if not project_root:
            self.log_warning("No project_root found in config (should be injected through DI), using current directory")
            project_root = os.getcwd()

        self.attorney_lookup_path = os.path.join(project_root, "src", "config", "attorneys", "attorney_lookup.json")
        self.attorney_lookup = {}
        self._load_attorney_lookup()

    def _load_attorney_lookup(self):
        """Load attorney lookup from JSON file."""
        try:
            if os.path.exists(self.attorney_lookup_path):
                with open(self.attorney_lookup_path, 'r') as f:
                    lookup_data = json.load(f)
                    # Create a dictionary with attorney_name as key for quick lookup
                    self.attorney_lookup = {item["attorney_name"]: item["law_firm"]
                                            for item in lookup_data
                                            if "attorney_name" in item and "law_firm" in item}
                    self.log_info(f"Loaded {len(self.attorney_lookup)} attorney-law firm mappings")
            else:
                self.log_warning(f"Attorney lookup file not found: {self.attorney_lookup_path}")
        except Exception as e:
            self.log_error(f"Error loading attorney lookup: {str(e)}")

    def _clean_law_firm_name(self, name: Any) -> str:
        """
        Cleans and normalizes a law firm name.
        
        First performs basic cleaning, then applies normalization.
        
        Args:
            name: Raw law firm name
            
        Returns:
            Normalized law firm name
        """
        if not isinstance(name, str): return ""

        # First do basic cleaning as before
        cleaned_name = name.replace(',', '').replace('.', '')
        cleaned_name = cleaned_name.strip()

        # Now apply normalization
        normalized_name = normalize_law_firm_name(cleaned_name)

        return normalized_name

    def _extract_and_format_law_firms(self, data: Dict[str, Any]):
        """
        Extracts unique law firms from 'attorneys_gpt' or 'attorney', populates 'law_firms' list
        and 'law_firm' string. Filters out null values from attorney dictionaries.
        
        **AUTOMATIC FALLBACK BEHAVIOR:**
        
        1. **Primary Processing**: First attempts to extract law firms from 'attorneys_gpt' field
           - Normalizes string entries to dict format
           - Uses attorney lookup to populate missing law_firm fields
           - Applies cleaning and normalization to law firm names
           
        2. **Automatic Fallback**: When 'attorneys_gpt' is not available or contains no valid law firms:
           - Automatically processes the 'attorney' field instead
           - Handles multiple formats: list of dicts, single dict
           - Applies same cleaning, normalization, and deduplication
           - Produces identical output format regardless of source
           
        3. **Output Format**: Always generates both:
           - data['law_firms']: List of unique, cleaned law firm names
           - data['law_firm']: Semicolon-separated string of law firms
           
        **NULL_CONDITIONS Filtering**: Filters out null values like '', 'NA', 'N/A', None, 'Pro Se', etc.
        
        **Additional Processing**:
        - Removes 'bar_info' and 'phone' keys if their value is "N/A" from attorneys_gpt items
        - Handles ignore_download config overrides for law firm reporting
        - Uses attorney lookup table to populate missing law firms in attorneys_gpt processing
        """
        # First, correct any street addresses in the attorney data before processing
        attorney_data = data.get('attorney')
        if attorney_data:
            self._correct_street_address_law_firms(attorney_data)
        
        unique_firms_list: List[str] = []
        seen_firms: Set[str] = set()
        attorneys_gpt = data.get('attorneys_gpt')
        found_law_firm_in_attorneys_gpt = False

        # First try to process attorneys_gpt
        if isinstance(attorneys_gpt, list):
            self.log_info(f"Processing attorneys_gpt with {len(attorneys_gpt)} attorneys: {attorneys_gpt}")
            # Convert string entries to dict format for consistency
            normalized_attorneys_gpt = []
            for attorney_info in attorneys_gpt:
                if isinstance(attorney_info, str):
                    # Convert string to dict format
                    normalized_attorneys_gpt.append({
                        "attorney_name": attorney_info.strip(),
                        "law_firm": None  # Will be populated later if needed
                    })
                    self.log_debug(f"Converted string attorney to dict: {attorney_info}")
                elif isinstance(attorney_info, dict):
                    normalized_attorneys_gpt.append(attorney_info)
                else:
                    self.log_warning(f"Item in attorneys_gpt is neither string nor dict: {attorney_info}")
                    continue

            # Update the data with normalized structure
            data['attorneys_gpt'] = normalized_attorneys_gpt
            attorneys_gpt = normalized_attorneys_gpt

            # Iterate through the normalized list
            for attorney_info in attorneys_gpt:  # attorneys_gpt is now a list of dicts
                if not isinstance(attorney_info, dict):
                    self.log_warning(f"Item in attorneys_gpt is not a dict after normalization: {attorney_info}")
                    continue

                # --- START: Remove 'bar_info' and 'phone' if "N/A" ---
                if attorney_info.get('bar_info') == "N/A":
                    try:
                        del attorney_info['bar_info']
                        self.log_debug(
                            f"Removed 'bar_info': 'N/A' for attorney: {attorney_info.get('attorney_name', 'Unknown')}")
                    except KeyError:
                        pass  # Should not happen if .get() found it

                if attorney_info.get('phone') == "N/A":
                    try:
                        del attorney_info['phone']
                        self.log_debug(
                            f"Removed 'phone': 'N/A' for attorney: {attorney_info.get('attorney_name', 'Unknown')}")
                    except KeyError:
                        pass
                # --- END: Remove 'bar_info' and 'phone' if "N/A" ---

                # Check for law_firm and try to get law_firm from lookup if missing
                if not attorney_info.get('law_firm') and 'attorney_name' in attorney_info:
                    attorney_name = attorney_info['attorney_name']
                    if attorney_name in self.attorney_lookup:
                        law_firm_from_lookup = self.attorney_lookup[attorney_name]
                        attorney_info['law_firm'] = law_firm_from_lookup
                        self.log_info(
                            f"Updated attorney {attorney_name} with law_firm from lookup: {law_firm_from_lookup}")

                # Now process for law firm extraction
                raw_firm_name = attorney_info.get('law_firm')
                if raw_firm_name and raw_firm_name not in NULL_CONDITIONS:
                    found_law_firm_in_attorneys_gpt = True
                    cleaned_firm_name = self._clean_law_firm_name(raw_firm_name)
                    if cleaned_firm_name and cleaned_firm_name not in seen_firms:
                        unique_firms_list.append(cleaned_firm_name)
                        seen_firms.add(cleaned_firm_name)
                        self.log_debug(f"Added law firm from attorneys_gpt: {cleaned_firm_name}")

            if found_law_firm_in_attorneys_gpt:
                self.log_info(
                    f"Found {len(unique_firms_list)} unique law firms in attorneys_gpt after N/A field cleanup.")
            else:
                # This case would be hit if attorneys_gpt was empty or all items lacked a valid law_firm after processing.
                self.log_info(
                    "No valid law firms found in attorneys_gpt after N/A field cleanup. Will check 'attorney' key.")

        # If no law firm found in attorneys_gpt (i.e., found_law_firm_in_attorneys_gpt is still False)
        # or if attorneys_gpt itself was empty/None initially.
        if not found_law_firm_in_attorneys_gpt or not attorneys_gpt or (
                isinstance(attorneys_gpt, list) and len(attorneys_gpt) == 0):
            # Enhanced logging for fallback decision process
            if not attorneys_gpt or len(attorneys_gpt) == 0:
                self.log_info(
                    "📋 FALLBACK TRIGGER: attorneys_gpt is empty or not present. Processing 'attorney' key instead.")
            else:
                self.log_info(
                    "📋 FALLBACK TRIGGER: attorneys_gpt exists but contains no valid law firms. Processing 'attorney' key instead.")

            attorney_data = data.get('attorney')
            if attorney_data is None:
                self.log_info("⚠️  FALLBACK RESULT: No 'attorney' key present. No law firms can be extracted.")
            elif isinstance(attorney_data, list):
                self.log_info(
                    f"✅ FALLBACK PROCESSING: 'attorney' key is a list with {len(attorney_data)} items. Processing for law firms...")
                for attorney_item in attorney_data:
                    if isinstance(attorney_item, dict) and 'law_firm' in attorney_item:
                        law_firm_value = attorney_item.get('law_firm')
                        if law_firm_value and law_firm_value not in NULL_CONDITIONS:
                            cleaned_firm_name = self._clean_law_firm_name(law_firm_value)
                            if cleaned_firm_name and cleaned_firm_name not in seen_firms:
                                unique_firms_list.append(cleaned_firm_name)
                                seen_firms.add(cleaned_firm_name)
                                self.log_info(
                                    f"🏢 EXTRACTED: Added law firm from 'attorney' key: '{cleaned_firm_name}' (original: '{law_firm_value}')")
                    elif isinstance(attorney_item, dict):
                        self.log_debug(
                            f"Skipped attorney item (no law_firm field): {attorney_item.get('attorney_name', 'Unknown')}")
            elif isinstance(attorney_data, dict) and 'law_firm' in attorney_data:
                self.log_info(
                    f"✅ FALLBACK PROCESSING: 'attorney' key is a single attorney dict. Processing for law firms...")
                law_firm_value = attorney_data.get('law_firm')
                if law_firm_value and law_firm_value not in NULL_CONDITIONS:
                    cleaned_firm_name = self._clean_law_firm_name(law_firm_value)
                    if cleaned_firm_name and cleaned_firm_name not in seen_firms:
                        unique_firms_list.append(cleaned_firm_name)
                        seen_firms.add(cleaned_firm_name)
                        self.log_info(
                            f"🏢 EXTRACTED: Added law firm from 'attorney' key: '{cleaned_firm_name}' (original: '{law_firm_value}')")
            else:
                self.log_info(
                    "⚠️  FALLBACK RESULT: 'attorney' key exists but is not in expected format (list or dict with law_firm). No law firms extracted.")

            if len(unique_firms_list) > 0 and not found_law_firm_in_attorneys_gpt:
                self.log_info(
                    f"🎯 FALLBACK SUCCESS: Extracted {len(unique_firms_list)} unique law firm(s) from 'attorney' key: {unique_firms_list}")
            elif not found_law_firm_in_attorneys_gpt:
                self.log_info("❌ FALLBACK FAILED: No law firms could be extracted from 'attorney' key.")

        # Check if this case was matched by ignore_download config
        if 'ignore_download' in data.get('_processing_notes', ''):
            # This case should have law firm overrides from ignore_download config
            # We need to re-apply them here since they may have been lost
            self.log_info("Case was matched by ignore_download config, checking for law firm overrides")

            # Load ignore_download config to find the matching entry
            ignore_download_path = os.path.join(
                self.config.get('project_root', os.getcwd()),
                'src', 'config', 'pacer', 'ignore_download', 'ignore_download.json'
            )

            try:
                with open(ignore_download_path, 'r') as f:
                    ignore_download_config = json.load(f)

                # Find matching entry
                court_id = data.get('court_id')
                attorney_list = data.get('attorney', [])

                for entry in ignore_download_config:
                    if entry.get('court_id') != court_id:
                        continue

                    # Check attorney match
                    match_found = False
                    for attorney in attorney_list:
                        if isinstance(attorney, dict):
                            attorney_name = attorney.get('attorney_name', '').strip().upper()
                            law_firm = attorney.get('law_firm', '').strip().upper()

                            entry_attorney = entry.get('attorney_name', '').strip().upper()
                            entry_law_firm = entry.get('law_firm', '').strip().upper()

                            if (attorney_name == entry_attorney and law_firm == entry_law_firm):
                                match_found = True
                                break

                    if match_found and 'report_law_firm' in entry:
                        # Apply the override
                        report_law_firm = entry['report_law_firm']
                        law_firms_list = [firm.strip() for firm in report_law_firm.split(' ; ') if firm.strip()]
                        data['law_firms'] = law_firms_list
                        data['law_firm'] = report_law_firm
                        self.log_info(f"Applied ignore_download law firm override: '{report_law_firm}'")
                        return  # Exit early, we've set the values

            except Exception as e:
                self.log_error(f"Error loading ignore_download config: {e}")

        # Normal processing - update data dictionary with extracted law firms
        data['law_firms'] = unique_firms_list
        data['law_firm'] = " ; ".join(unique_firms_list)

        if unique_firms_list:
            self.log_debug(f"Final generated law_firms list: {data['law_firms']}")
            self.log_debug(f"Final generated law_firm string: '{data['law_firm']}'")
        else:
            self.log_info("No law firms ultimately found to populate 'law_firms' or 'law_firm'.")

        # The attorneys_gpt list within 'data' is modified in-place if applicable.

    def extract_unique_attorneys(self, data: Dict) -> List[Tuple[str, str, str]]:
        """
        Extract unique attorney combinations (name, email, cleaned_law_firm)
        from attorneys_gpt or attorney key.
        NOTE: This now benefits from the "N/A" filtering done in
              _extract_and_format_law_firms if process_law_firms is called first.
              If called independently, consider adding the filtering here too.
        """
        unique_attorneys: Set[Tuple[str, str, str]] = set()
        found_valid_attorney_in_attorneys_gpt = False

        # First try attorneys_gpt
        attorneys_gpt = data.get(
            'attorneys_gpt')  # Assumes 'attorneys_gpt' might already be cleaned by the other method
        if isinstance(attorneys_gpt, list):
            for attorney in attorneys_gpt:
                if not isinstance(attorney, dict): continue
                # No need to filter "N/A" here again if _extract_and_format_law_firms runs first and updates data['attorneys_gpt']
                name = attorney.get('attorney_name', '').strip()
                email = attorney.get('email', '').strip()
                raw_firm_name = attorney.get('law_firm')
                cleaned_firm_name = self._clean_law_firm_name(raw_firm_name)

                # Skip if name, email, or cleaned firm name is missing/null
                if (not name or name in NULL_CONDITIONS or
                        not email or email in NULL_CONDITIONS or
                        not cleaned_firm_name or cleaned_firm_name in NULL_CONDITIONS):
                    continue  # Skip incomplete entries silently for brevity

                found_valid_attorney_in_attorneys_gpt = True
                attorney_tuple = (name, email, cleaned_firm_name)
                unique_attorneys.add(attorney_tuple)  # Set handles duplicates

        # If no valid attorney found in attorneys_gpt, try attorney key
        if not found_valid_attorney_in_attorneys_gpt:
            self.log_info("No valid attorney found in attorneys_gpt, checking attorney key")
            attorney_data = data.get('attorney')

            if isinstance(attorney_data, list):
                for attorney in attorney_data:
                    if not isinstance(attorney, dict): continue
                    name = attorney.get('attorney_name', '').strip()
                    email = attorney.get('email', '').strip()
                    raw_firm_name = attorney.get('law_firm')
                    cleaned_firm_name = self._clean_law_firm_name(raw_firm_name)

                    # Skip if name, email, or cleaned firm name is missing/null
                    if (not name or name in NULL_CONDITIONS or
                            not email or email in NULL_CONDITIONS or
                            not cleaned_firm_name or cleaned_firm_name in NULL_CONDITIONS):
                        continue  # Skip incomplete entries silently for brevity

                    attorney_tuple = (name, email, cleaned_firm_name)
                    unique_attorneys.add(attorney_tuple)  # Set handles duplicates
                    self.log_debug(f"Added attorney tuple from attorney key: {attorney_tuple}")
            elif isinstance(attorney_data, dict):
                name = attorney_data.get('attorney_name', '').strip()
                email = attorney_data.get('email', '').strip()
                raw_firm_name = attorney_data.get('law_firm')
                cleaned_firm_name = self._clean_law_firm_name(raw_firm_name)

                # Check if name, email, and cleaned firm name are all valid
                if (name and name not in NULL_CONDITIONS and
                        email and email not in NULL_CONDITIONS and
                        cleaned_firm_name and cleaned_firm_name not in NULL_CONDITIONS):
                    attorney_tuple = (name, email, cleaned_firm_name)
                    unique_attorneys.add(attorney_tuple)  # Set handles duplicates
                    self.log_debug(f"Added attorney tuple from attorney key (dict): {attorney_tuple}")

        result_list = list(unique_attorneys)
        self.log_debug(f"Extracted {len(result_list)} unique attorney (name, email, firm) tuples.")
        return result_list

    def process_law_firms(self, data: Dict):
        """
        Processes attorney and law firm info. Populates 'law_firms' (list) and
        'law_firm' (string). Removes deprecated keys.
        If attorneys_gpt doesn't contain at least one item with a law firm key, processes attorney key instead.
        """
        docket_num = data.get('docket_num', 'NA')
        log_filename = data.get('new_filename', docket_num)  # Use new_filename for logging if available
        self.log_info(f"Starting law firm processing for: {log_filename}")

        # 1. Extract and format law firms list and string
        # This will check attorneys_gpt first, and if no law firm is found, it will check attorney key
        self._extract_and_format_law_firms(data)  # Populates data['law_firms'] and data['law_firm']

        # 2. Remove deprecated keys
        self.remove_deprecated_keys(data)  # This handles all deprecated keys

        # Log the results
        if data.get('law_firms') and len(data.get('law_firms', [])) > 0:
            self.log_info(
                f"Found {len(data.get('law_firms', []))} law firms for {log_filename}: {data.get('law_firm')}")
        else:
            self.log_warning(f"No law firms found for {log_filename} after processing")

        self.log_info(f"Finished law firm processing for: {log_filename}")

    @staticmethod
    def _extract_email_domain(email: str) -> Optional[str]:
        """
        Extract domain from email address.
        
        Args:
            email: Email address string
            
        Returns:
            Domain part of email (e.g., "gorilaw.com") or None
        """
        if not email or '@' not in email:
            return None
        
        try:
            # Split email and get domain part
            domain = email.strip().lower().split('@')[1]
            # Remove any trailing whitespace or special characters
            domain = re.sub(r'[^\w.-]', '', domain)
            return domain if domain else None
        except (IndexError, AttributeError):
            return None
    
    def _is_street_address(self, text: str) -> bool:
        """
        Detect if a text string is likely a street address rather than a law firm name.
        
        Args:
            text: Text to check
            
        Returns:
            True if text appears to be a street address
        """
        if not text:
            return False
            
        text_lower = text.lower().strip()
        
        # Common street address indicators
        street_indicators = [
            'street', 'st.', 'st ', 'avenue', 'ave.', 'ave ', 
            'road', 'rd.', 'rd ', 'drive', 'dr.', 'dr ',
            'boulevard', 'blvd.', 'blvd ', 'lane', 'ln.', 'ln ',
            'way', 'plaza', 'place', 'pl.', 'pl ', 'court', 'ct.', 'ct ',
            'suite', 'ste.', 'ste ', 'floor', 'fl.', 'fl '
        ]
        
        # Check for street indicators
        for indicator in street_indicators:
            if indicator in text_lower:
                # Additional check: if it starts with a number, it's very likely an address
                if re.match(r'^\d+\s+', text):
                    return True
                # If it has "suite" or "floor", it's likely an address
                if any(suite_word in text_lower for suite_word in ['suite', 'ste.', 'floor', 'fl.']):
                    return True
                # For other indicators, require a number somewhere in the text
                if re.search(r'\d', text):
                    return True
        
        # Check if it starts with a number followed by a word (common address pattern)
        if re.match(r'^\d+\s+[A-Za-z]', text):
            return True
            
        # Check for P.O. Box
        if re.search(r'p\.?o\.?\s*box', text_lower):
            return True
            
        return False
    
    def _correct_street_address_law_firms(self, attorneys: Any) -> None:
        """
        Correct law firm fields that contain street addresses by finding the proper
        law firm name from other attorneys with the same email domain.
        
        Args:
            attorneys: Attorney data (list or dict) - modified in place
        """
        # Ensure we have a list to work with
        if not attorneys:
            return
            
        # Convert single dict to list for uniform processing
        if isinstance(attorneys, dict):
            attorneys_list = [attorneys]
        elif isinstance(attorneys, list):
            attorneys_list = attorneys
        else:
            return
        
        # Build mapping of email domains to proper law firm names
        domain_to_law_firm = {}
        
        # First pass: collect all non-address law firms by domain
        for attorney in attorneys_list:
            if not isinstance(attorney, dict):
                continue
                
            email = attorney.get('email', '')
            law_firm = attorney.get('law_firm', '')
            
            if email and law_firm:
                domain = LawFirmProcessor._extract_email_domain(email)
                if domain and not self._is_street_address(law_firm):
                    # This is a proper law firm name
                    if domain not in domain_to_law_firm:
                        domain_to_law_firm[domain] = law_firm
                        self.log_debug(f"📧 Found law firm '{law_firm}' for domain '{domain}'")
        
        # Second pass: correct street addresses using domain mapping
        corrected_count = 0
        for attorney in attorneys_list:
            if not isinstance(attorney, dict):
                continue
                
            email = attorney.get('email', '')
            law_firm = attorney.get('law_firm', '')
            
            if email and law_firm and self._is_street_address(law_firm):
                domain = LawFirmProcessor._extract_email_domain(email)
                if domain and domain in domain_to_law_firm:
                    correct_firm = domain_to_law_firm[domain]
                    self.log_info(
                        f"🏢 Correcting law firm for {attorney.get('attorney_name', 'Unknown')}: "
                        f"'{law_firm}' → '{correct_firm}' (based on @{domain})"
                    )
                    attorney['law_firm'] = correct_firm
                    corrected_count += 1
                else:
                    self.log_warning(
                        f"⚠️ Detected street address '{law_firm}' for {attorney.get('attorney_name', 'Unknown')} "
                        f"but no other attorney found with domain @{domain or 'unknown'}"
                    )
        
        if corrected_count > 0:
            self.log_info(f"✅ Corrected {corrected_count} street addresses in law firm fields")

    @staticmethod
    def remove_deprecated_keys(data: Dict) -> None:
        """Remove deprecated keys from the data dictionary."""
        # Handle potential transferor_docket_law_firm update safely
        law_firm2_val = data.get('law_firm2')
        if law_firm2_val not in NULL_CONDITIONS and data.get('transferred_in', False):
            data['transferor_docket_law_firm'] = law_firm2_val
        # elif 'transferor_docket_law_firm' not in data:  # Ensure field exists even if law_firm2 was null
        #     data['transferor_docket_law_firm'] = ''

        deprecated_keys = {
            'attorney_emails',
            'law_firm_emails',
            'matched_law_firms',
            'matched_law_firms_list',
            'attorney_emails_gpt',
            'law_firms_gpt',
            'law_firms_gpt_list',
            'merged_law_firms',
            'merged_law_firms_list',
            'law_firm_stats',
            'normalized_filename',  # This specific key might be handled differently now
            # Keep law_firm_stats removal? Yes.
            'law_firm2'  # Remove after potentially using its value
        }

        keys_removed_count = 0
        for key in deprecated_keys:
            if key in data:
                try:
                    del data[key]
                    keys_removed_count += 1
                except KeyError:
                    pass  # Should not happen after check

        # if keys_removed_count > 0:
        #     logging.debug(f"Removed {keys_removed_count} deprecated keys.")

# TODO: Update process_law_firm to handle attorney matching case insensitvely and properly parse out law_firm name.
