# /src/services/transformer/law_firm_integration.py
"""
# Removed dependency_injector imports - using container-based injection
Law firm integration and processing for docket data.

This module handles law firm processing integration extracted from docket_processor.py
as part of Phase 3.2 refactoring.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any

from src.transformer.config.constants import NULL_CONDITIONS

# Removed dependency_injector imports - using container-based injection
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError


class LawFirmIntegration(AsyncServiceBase):
    """Handles law firm processing and integration for docket data."""



    def __init__(self,
                 law_firm_processor: Optional[Any] ,
                 config: Optional[Dict] ,
                 logger: Optional[logging.Logger] ):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.law_firm_processor = law_firm_processor

        # Configure processing parameters
        self.max_concurrent_requests = self.config.get('max_concurrent_law_firm_requests', 5)
        self.timeout_seconds = self.config.get('law_firm_timeout_seconds', 30)

    async def _execute_action(self, data: Any) -> Any:
        """Execute LawFirmIntegration actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'process_law_firms':
                return await self.process_law_firms(action_data)
            elif action == 'prepare_law_firm_data':
                self._prepare_law_firm_data(action_data)
                return action_data
            elif action == 'extract_attorney_information':
                return self._extract_attorney_information(action_data)
            elif action == 'process_law_firms_async':
                attorneys = action_data.get('attorneys', [])
                filename = action_data.get('filename', 'unknown')
                return await self._process_law_firms_async(attorneys, filename)
            elif action == 'update_data_with_law_firms':
                processed_firms = action_data.get('processed_firms', [])
                self._update_data_with_law_firms(action_data, processed_firms)
                return action_data
            elif action == 'validate_law_firm_results':
                self._validate_law_firm_results(action_data)
                return action_data
            elif action == 'handle_law_firm_error':
                error_message = action_data.get('error_message', 'Unknown error')
                self._handle_law_firm_error(action_data, error_message)
                return action_data
            elif action == 'validate_law_firm_integration':
                return self.validate_law_firm_integration(action_data)
            elif action == 'get_law_firm_summary':
                return self.get_law_firm_summary(action_data)
            elif action == 'get_processing_stats':
                return self.get_processing_stats(action_data)
        raise TransformerServiceError("Invalid action data provided to LawFirmIntegration")

    async def process_law_firms(self, data: Dict) -> bool:
        """
        Process law firm information for the docket data.
        
        This is the main async entry point for law firm processing.
        
        Args:
            data: Docket data dictionary (modified in place)
            
        Returns:
            True if law firm processing was attempted, False otherwise
        """
        if not isinstance(data, dict):
            self.log_error("Invalid data dictionary provided to process_law_firms")
            return False

        filename = data.get('new_filename', data.get('docket_num', 'unknown'))

        if not self.law_firm_processor:
            self.log_warning(f"Law firm processor not available for {filename}")
            return False

        try:
            # Prepare data for law firm processing
            self._prepare_law_firm_data(data)

            # Extract attorney information for processing
            attorneys = self._extract_attorney_information(data)

            if not attorneys:
                self.log_debug(f"No attorney information found for law firm processing: {filename}")
                return False

            # Process law firms using the correct method
            await asyncio.to_thread(self.law_firm_processor.process_law_firms, data)

            # Check if processing was successful
            law_firms = data.get('law_firms', [])
            if law_firms:
                # Set completion indicators
                data['law_firm_processed'] = True
                data['law_firm_normalized'] = True
                data['law_firm_processing_timestamp'] = self._get_current_timestamp()
                data['law_firm_count'] = len(law_firms)

                self.log_info(f"Law firm processing completed for {filename}. "
                              f"Processed {len(law_firms)} firms: {data.get('law_firm', '')}")
                return True
            else:
                self.log_warning(f"Law firm processing returned no results for {filename}")
                return False

        except Exception as e:
            self.log_error(f"Error during law firm processing for {filename}: {e}")
            self._handle_law_firm_error(data, str(e))
            return False

    def _prepare_law_firm_data(self, data: Dict):
        """
        Prepare data dictionary for law firm processing.
        
        Args:
            data: Data dictionary to prepare (modified in place)
        """
        # Ensure law firm related fields exist
        data.setdefault('law_firms', [])  # Array format
        data.setdefault('law_firm', '')  # String format (semicolon-separated)
        data.setdefault('attorneys', [])
        data.setdefault('law_firm_processed', False)

        # Clean existing law firm data - ensure proper format
        if not isinstance(data.get('law_firms'), list):
            if isinstance(data.get('law_firms'), str) and data['law_firms'].strip():
                # Convert string to list by splitting on semicolon
                data['law_firms'] = [firm.strip() for firm in data['law_firms'].split(';') if firm.strip()]
            else:
                data['law_firms'] = []

        # Ensure law_firm is string format (semicolon-separated)
        if not isinstance(data.get('law_firm'), str):
            if isinstance(data.get('law_firm'), list):
                # Convert list to semicolon-separated string
                data['law_firm'] = ' ; '.join(str(firm).strip() for firm in data['law_firm'] if str(firm).strip())
            else:
                data['law_firm'] = ''

        if not isinstance(data.get('attorneys'), list):
            if isinstance(data.get('attorneys'), str) and data['attorneys'].strip():
                data['attorneys'] = [data['attorneys'].strip()]
            else:
                data['attorneys'] = []

    def _extract_attorney_information(self, data: Dict) -> List[Dict[str, Any]]:
        """
        Extract attorney information from data for law firm processing.
        
        Args:
            data: Data dictionary containing attorney information
            
        Returns:
            List of attorney information dictionaries
        """
        attorneys = []

        # Extract from various possible fields
        attorney_sources = [
            ('attorney', 'attorney_name'),  # Original attorney data from HTML processing
            ('attorneys_gpt', 'attorney_name'),  # LLM extracted attorneys
            ('defendant', 'attorney_name'),  # Defendant data (if has attorney info)
            ('defendants_gpt', 'attorney_name')  # LLM extracted defendants (if has attorney info)
        ]

        for field_name, name_key in attorney_sources:
            field_data = data.get(field_name, [])

            if isinstance(field_data, list):
                for attorney in field_data:
                    if isinstance(attorney, dict):
                        # Attorney is already a dictionary
                        if attorney.get(name_key) and attorney[name_key] not in NULL_CONDITIONS:
                            attorneys.append(attorney)
                    elif isinstance(attorney, str) and attorney.strip():
                        # Attorney is a string, convert to dict
                        attorneys.append({name_key: attorney.strip()})
            elif isinstance(field_data, str) and field_data.strip():
                # Single attorney as string
                attorneys.append({name_key: field_data.strip()})

        # Deduplicate attorneys by name
        seen_names = set()
        unique_attorneys = []

        for attorney in attorneys:
            name = attorney.get('attorney_name', '').strip()
            if name and name not in seen_names and name not in NULL_CONDITIONS:
                unique_attorneys.append(attorney)
                seen_names.add(name)

        return unique_attorneys

    async def _process_law_firms_async(self, attorneys: List[Dict], filename: str) -> List[Dict[str, Any]]:
        """
        Process law firms asynchronously using the law firm processor.
        
        Args:
            attorneys: List of attorney information dictionaries
            filename: Filename for logging context
            
        Returns:
            List of processed law firm information
        """
        if not attorneys:
            return []

        try:
            # Create semaphore to limit concurrent requests
            semaphore = asyncio.Semaphore(self.max_concurrent_requests)

            async def process_single_attorney(attorney: Dict) -> Optional[Dict]:
                async with semaphore:
                    try:
                        # Use timeout for individual requests
                        return await asyncio.wait_for(
                            self._call_law_firm_processor(attorney),
                            timeout=self.timeout_seconds
                        )
                    except asyncio.TimeoutError:
                        self.log_warning(f"Law firm processing timeout for attorney: {attorney.get('attorney_name')}")
                        return None
                    except Exception as e:
                        self.log_error(f"Error processing attorney {attorney.get('attorney_name')}: {e}")
                        return None

            # Process all attorneys concurrently
            tasks = [process_single_attorney(attorney) for attorney in attorneys]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Filter successful results
            processed_firms = []
            for result in results:
                if isinstance(result, dict) and result:
                    processed_firms.append(result)
                elif isinstance(result, Exception):
                    self.log_error(f"Law firm processing exception: {result}")

            self.log_debug(f"Processed {len(processed_firms)} law firms from {len(attorneys)} attorneys for {filename}")
            return processed_firms

        except Exception as e:
            self.log_error(f"Error in async law firm processing for {filename}: {e}")
            return []

    async def _call_law_firm_processor(self, attorney: Dict) -> Optional[Dict]:
        """
        Call the law firm processor for a single attorney.
        
        Args:
            attorney: Attorney information dictionary
            
        Returns:
            Processed law firm information or None
        """
        if not self.law_firm_processor:
            return None

        # The law firm processor works on the entire data dict, not individual attorneys
        # Return a dummy result - the real processing happens in process_law_firms
        return {'processed': True}

    def _update_data_with_law_firms(self, data: Dict, processed_firms: List[Dict]):
        """
        Update data dictionary with processed law firm information.
        
        Args:
            data: Data dictionary to update (modified in place)
            processed_firms: List of processed law firm information
        """
        if not processed_firms:
            return

        # Extract law firm names
        law_firm_names = []
        law_firm_details = []

        for firm in processed_firms:
            if isinstance(firm, dict):
                # Extract firm name
                firm_name = (firm.get('law_firm_name') or
                             firm.get('firm_name') or
                             firm.get('name'))

                if firm_name and firm_name not in NULL_CONDITIONS:
                    law_firm_names.append(firm_name)
                    law_firm_details.append(firm)

        # Update law firm fields with proper format
        if law_firm_names:
            # Remove duplicates while preserving order
            unique_firms = []
            seen = set()
            for firm in law_firm_names:
                if firm not in seen:
                    unique_firms.append(firm)
                    seen.add(firm)

            # Set both formats correctly
            data['law_firms'] = unique_firms  # Array format
            data['law_firm'] = ' ; '.join(unique_firms)  # String format (semicolon-separated)

        # Store detailed law firm information
        if law_firm_details:
            data['law_firm_details'] = law_firm_details

        # Set processing metadata
        data['law_firm_processed'] = True
        data['law_firm_normalized'] = True  # Completion indicator
        data['law_firm_processing_timestamp'] = self._get_current_timestamp()
        data['law_firm_count'] = len(law_firm_names)

    def _validate_law_firm_results(self, data: Dict):
        """
        Validate and clean law firm processing results.
        
        Args:
            data: Data dictionary to validate (modified in place)
        """
        # Validate law_firms array
        law_firms = data.get('law_firms', [])
        if isinstance(law_firms, list):
            cleaned_firms = []
            for firm in law_firms:
                if isinstance(firm, str):
                    cleaned = firm.strip()
                    if cleaned and cleaned not in NULL_CONDITIONS:
                        cleaned_firms.append(cleaned)
            data['law_firms'] = cleaned_firms
            # Update law_firm string to match cleaned array
            data['law_firm'] = ' ; '.join(cleaned_firms)

        # Ensure law_firm is string format
        if not isinstance(data.get('law_firm'), str):
            data['law_firm'] = ''

        # Validate law_firm_details
        law_firm_details = data.get('law_firm_details', [])
        if isinstance(law_firm_details, list):
            validated_details = []
            for detail in law_firm_details:
                if isinstance(detail, dict) and detail:
                    validated_details.append(detail)
            data['law_firm_details'] = validated_details

    def _handle_law_firm_error(self, data: Dict, error_message: str):
        """
        Handle law firm processing errors.
        
        Args:
            data: Data dictionary to update with error info (modified in place)
            error_message: Error message to record
        """
        data['law_firm_processing_error'] = error_message
        data['law_firm_processed'] = False
        data['law_firm_processing_timestamp'] = self._get_current_timestamp()

        filename = data.get('new_filename', data.get('docket_num', 'unknown'))
        self.log_error(f"Law firm processing failed for {filename}: {error_message}")

    def _get_current_timestamp(self) -> str:
        """Get current timestamp for metadata."""
        from datetime import datetime
        return datetime.now().isoformat()

    def validate_law_firm_integration(self, data: Dict) -> Dict[str, Any]:
        """
        Validate law firm integration results and return validation report.
        
        Args:
            data: Data dictionary to validate
            
        Returns:
            Validation report with status and details
        """
        report = {
            'status': 'success',
            'warnings': [],
            'errors': [],
            'processed_firms': 0,
            'processed_attorneys': 0
        }

        # Check if processing was completed
        if not data.get('law_firm_processed'):
            report['status'] = 'not_processed'
            return report

        # Check for processing errors
        if 'law_firm_processing_error' in data:
            report['status'] = 'error'
            report['errors'].append(f"Processing error: {data['law_firm_processing_error']}")

        # Count processed firms and attorneys
        law_firms = data.get('law_firms', [])
        if isinstance(law_firms, list):
            report['processed_firms'] = len(law_firms)

        attorneys = data.get('attorneys', [])
        if isinstance(attorneys, list):
            report['processed_attorneys'] = len(attorneys)

        # Validate data quality
        if report['processed_firms'] == 0 and report['processed_attorneys'] > 0:
            report['warnings'].append("Attorneys found but no law firms processed")

        law_firm_details = data.get('law_firm_details', [])
        if isinstance(law_firm_details, list):
            if len(law_firm_details) != report['processed_firms']:
                report['warnings'].append("Mismatch between law firm count and detailed information")

        # Set overall status
        if report['errors']:
            report['status'] = 'error'
        elif report['warnings']:
            report['status'] = 'warning'
        elif report['processed_firms'] == 0:
            report['status'] = 'no_firms'

        return report

    def get_law_firm_summary(self, data: Dict) -> str:
        """
        Get summary of law firm processing results.
        
        Args:
            data: Data dictionary to summarize
            
        Returns:
            Human-readable summary string
        """
        if not data.get('law_firm_processed'):
            return "Law firm processing not completed"

        if 'law_firm_processing_error' in data:
            return f"Law firm processing failed: {data['law_firm_processing_error']}"

        law_firms = data.get('law_firms', [])
        attorneys = data.get('attorneys', [])

        firm_count = len(law_firms) if isinstance(law_firms, list) else 0
        attorney_count = len(attorneys) if isinstance(attorneys, list) else 0

        summary_parts = []

        if firm_count > 0:
            summary_parts.append(f"{firm_count} law firm(s)")
        if attorney_count > 0:
            summary_parts.append(f"{attorney_count} attorney(s)")

        if summary_parts:
            result = f"Processed: {', '.join(summary_parts)}"

            # Add sample firm names if available
            if firm_count > 0 and isinstance(law_firms, list):
                sample_firms = law_firms[:2]  # Show first 2 firms
                firm_sample = ", ".join(sample_firms)
                if firm_count > 2:
                    firm_sample += f" (+{firm_count - 2} more)"
                result += f" [{firm_sample}]"

            return result
        else:
            return "Law firm processing completed but no firms identified"

    def get_processing_stats(self, data: Dict) -> Dict[str, Any]:
        """
        Get detailed processing statistics.
        
        Args:
            data: Data dictionary to analyze
            
        Returns:
            Dictionary with processing statistics
        """
        stats = {
            'processed': data.get('law_firm_processed', False),
            'timestamp': data.get('law_firm_processing_timestamp'),
            'firm_count': 0,
            'attorney_count': 0,
            'has_details': False,
            'has_error': 'law_firm_processing_error' in data
        }

        law_firms = data.get('law_firms', [])
        if isinstance(law_firms, list):
            stats['firm_count'] = len(law_firms)

        attorneys = data.get('attorneys', [])
        if isinstance(attorneys, list):
            stats['attorney_count'] = len(attorneys)

        law_firm_details = data.get('law_firm_details', [])
        if isinstance(law_firm_details, list) and law_firm_details:
            stats['has_details'] = True

        return stats
