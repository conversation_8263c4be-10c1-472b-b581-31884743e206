"""
Transformer Domain Interfaces

Defines the core protocols and interfaces for the transformer domain.
"""
from typing import Protocol, Dict, Any, List, Optional
from abc import ABC, abstractmethod

from ..models.transformation_job import TransformationJob
from ..models.document import Document


class DocumentProcessorProtocol(Protocol):
    """Protocol for document processing components."""
    
    async def process_document(self, document: Document) -> Document:
        """Process a document through the processing pipeline."""
        ...
    
    async def health_check(self) -> Dict[str, Any]:
        """Check component health."""
        ...


class EnrichmentServiceProtocol(Protocol):
    """Protocol for data enrichment services."""
    
    async def enrich_document(self, document: Document) -> Document:
        """Enrich document with additional data."""
        ...


class PersistenceServiceProtocol(Protocol):
    """Protocol for persistence services."""
    
    async def save_document(self, document: Document, path: str, format: str = 'json') -> bool:
        """Save document to storage."""
        ...
    
    async def load_document(self, path: str) -> Optional[Document]:
        """Load document from storage."""
        ...
    
    async def upload_batch(self, paths: List[str], upload_types: List[str]) -> Dict[str, Any]:
        """Upload batch of files."""
        ...


class JobExecutionServiceProtocol(Protocol):
    """Protocol for job execution services."""
    
    async def execute_transformation_job(self, job: TransformationJob) -> Dict[str, Any]:
        """Execute a transformation job."""
        ...
    
    async def execute_batch_jobs(self, jobs: List[TransformationJob]) -> List[Dict[str, Any]]:
        """Execute batch of transformation jobs."""
        ...


class TransformerOrchestratorProtocol(Protocol):
    """Protocol for the main transformer orchestrator."""
    
    async def transform_document(self, job: TransformationJob) -> Dict[str, Any]:
        """Transform a document using the complete pipeline."""
        ...
    
    async def transform_batch(self, jobs: List[TransformationJob]) -> List[Dict[str, Any]]:
        """Transform multiple documents."""
        ...
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check."""
        ...