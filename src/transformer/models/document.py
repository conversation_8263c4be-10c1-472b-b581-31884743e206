"""
Document Domain Model

Represents a legal document in the transformer domain with metadata,
content, and processing state information.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Dict, List, Any
from enum import Enum
from pathlib import Path


class DocumentType(Enum):
    """Document type classification."""
    DOCKET = "docket"
    FILING = "filing"
    ORDER = "order"
    MOTION = "motion"
    BRIEF = "brief"
    EXHIBIT = "exhibit"
    UNKNOWN = "unknown"


class DocumentFormat(Enum):
    """Document format types."""
    PDF = "pdf"
    HTML = "html"
    TEXT = "text"
    JSON = "json"
    XML = "xml"
    UNSPECIFIED = "unspecified"


class ProcessingState(Enum):
    """Document processing state."""
    RAW = "raw"
    PARSED = "parsed"
    CLASSIFIED = "classified"
    CLEANED = "cleaned"
    ENRICHED = "enriched"
    FINALIZED = "finalized"


@dataclass
class DocumentMetadata:
    """Metadata associated with a document."""
    
    # File information
    file_size_bytes: Optional[int] = None
    file_hash: Optional[str] = None
    mime_type: Optional[str] = None
    
    # Legal metadata
    court_name: Optional[str] = None
    case_number: Optional[str] = None
    filing_date: Optional[datetime] = None
    document_number: Optional[str] = None
    
    # Processing metadata
    processing_flags: Dict[str, bool] = field(default_factory=dict)
    quality_score: Optional[float] = None
    confidence_score: Optional[float] = None
    
    # Source information
    source_system: Optional[str] = None
    source_url: Optional[str] = None
    retrieved_at: Optional[datetime] = None


@dataclass
class Document:
    """
    Domain model representing a legal document.
    
    This model encapsulates all information about a document including
    its content, metadata, processing state, and relationships to other
    domain entities.
    """
    
    # Identity
    document_id: str
    file_path: str
    
    # Classification
    document_type: DocumentType = DocumentType.UNKNOWN
    document_format: DocumentFormat = DocumentFormat.UNSPECIFIED
    
    # Content
    raw_content: Optional[str] = None
    processed_content: Optional[str] = None
    structured_data: Optional[Dict[str, Any]] = None
    
    # Processing state
    processing_state: ProcessingState = ProcessingState.RAW
    processing_history: List[Dict[str, Any]] = field(default_factory=list)
    
    # Metadata
    metadata: DocumentMetadata = field(default_factory=DocumentMetadata)
    
    # Relationships
    parent_document_id: Optional[str] = None
    child_document_ids: List[str] = field(default_factory=list)
    related_document_ids: List[str] = field(default_factory=list)
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    # Processing results
    classification_results: Optional[Dict[str, Any]] = None
    extraction_results: Optional[Dict[str, Any]] = None
    enrichment_results: Optional[Dict[str, Any]] = None
    
    # Validation
    validation_errors: List[str] = field(default_factory=list)
    validation_warnings: List[str] = field(default_factory=list)
    
    @property
    def file_name(self) -> str:
        """Get the file name from the path."""
        return Path(self.file_path).name
    
    @property
    def file_extension(self) -> str:
        """Get the file extension."""
        return Path(self.file_path).suffix.lower()
    
    @property
    def is_processed(self) -> bool:
        """Check if document has been processed."""
        return self.processing_state != ProcessingState.RAW
    
    @property
    def has_content(self) -> bool:
        """Check if document has content."""
        return bool(self.raw_content or self.processed_content)
    
    def update_processing_state(self, new_state: ProcessingState, 
                              notes: Optional[str] = None) -> None:
        """Update processing state with history tracking."""
        old_state = self.processing_state
        self.processing_state = new_state
        self.updated_at = datetime.utcnow()
        
        # Add to processing history
        history_entry = {
            'timestamp': self.updated_at.isoformat(),
            'from_state': old_state.value,
            'to_state': new_state.value,
            'notes': notes
        }
        self.processing_history.append(history_entry)
    
    def add_validation_error(self, error: str) -> None:
        """Add a validation error."""
        if error not in self.validation_errors:
            self.validation_errors.append(error)
            self.updated_at = datetime.utcnow()
    
    def add_validation_warning(self, warning: str) -> None:
        """Add a validation warning."""
        if warning not in self.validation_warnings:
            self.validation_warnings.append(warning)
            self.updated_at = datetime.utcnow()
    
    def clear_validation_issues(self) -> None:
        """Clear all validation errors and warnings."""
        self.validation_errors.clear()
        self.validation_warnings.clear()
        self.updated_at = datetime.utcnow()
    
    def is_valid(self) -> bool:
        """Check if document is valid (no validation errors)."""
        return len(self.validation_errors) == 0
    
    def set_classification_results(self, results: Dict[str, Any]) -> None:
        """Set classification results."""
        self.classification_results = results
        self.updated_at = datetime.utcnow()
    
    def set_extraction_results(self, results: Dict[str, Any]) -> None:
        """Set extraction results."""
        self.extraction_results = results
        self.updated_at = datetime.utcnow()
    
    def set_enrichment_results(self, results: Dict[str, Any]) -> None:
        """Set enrichment results."""
        self.enrichment_results = results
        self.updated_at = datetime.utcnow()
    
    def add_child_document(self, child_id: str) -> None:
        """Add a child document relationship."""
        if child_id not in self.child_document_ids:
            self.child_document_ids.append(child_id)
            self.updated_at = datetime.utcnow()
    
    def add_related_document(self, related_id: str) -> None:
        """Add a related document relationship."""
        if related_id not in self.related_document_ids:
            self.related_document_ids.append(related_id)
            self.updated_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'document_id': self.document_id,
            'file_path': self.file_path,
            'document_type': self.document_type.value,
            'document_format': self.document_format.value,
            'raw_content': self.raw_content,
            'processed_content': self.processed_content,
            'structured_data': self.structured_data,
            'processing_state': self.processing_state.value,
            'processing_history': self.processing_history,
            'metadata': {
                'file_size_bytes': self.metadata.file_size_bytes,
                'file_hash': self.metadata.file_hash,
                'mime_type': self.metadata.mime_type,
                'court_name': self.metadata.court_name,
                'case_number': self.metadata.case_number,
                'filing_date': self.metadata.filing_date.isoformat() if self.metadata.filing_date else None,
                'document_number': self.metadata.document_number,
                'processing_flags': self.metadata.processing_flags,
                'quality_score': self.metadata.quality_score,
                'confidence_score': self.metadata.confidence_score,
                'source_system': self.metadata.source_system,
                'source_url': self.metadata.source_url,
                'retrieved_at': self.metadata.retrieved_at.isoformat() if self.metadata.retrieved_at else None
            },
            'parent_document_id': self.parent_document_id,
            'child_document_ids': self.child_document_ids,
            'related_document_ids': self.related_document_ids,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'classification_results': self.classification_results,
            'extraction_results': self.extraction_results,
            'enrichment_results': self.enrichment_results,
            'validation_errors': self.validation_errors,
            'validation_warnings': self.validation_warnings
        }