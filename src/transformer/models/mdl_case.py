"""
MDL Case Domain Model

Represents a Multidistrict Litigation (MDL) case with metadata,
transferee court information, and related case data.
"""

from dataclasses import dataclass, field
from datetime import datetime, date
from typing import Optional, Dict, List, Any
from enum import Enum


class MdlStatus(Enum):
    """MDL case status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    CLOSED = "closed"
    TERMINATED = "terminated"
    UNKNOWN = "unknown"


class CaseType(Enum):
    """Type of case within MDL."""
    TAG_ALONG = "tag_along"
    MEMBER_CASE = "member_case"
    TRANSFEREE_CASE = "transferee_case"
    COORDINATED_CASE = "coordinated_case"
    UNKNOWN = "unknown"


@dataclass
class TransferorCourt:
    """Court from which case was transferred."""
    
    court_name: str
    district: str
    state: Optional[str] = None
    judge_name: Optional[str] = None
    original_case_number: Optional[str] = None
    transfer_date: Optional[date] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'court_name': self.court_name,
            'district': self.district,
            'state': self.state,
            'judge_name': self.judge_name,
            'original_case_number': self.original_case_number,
            'transfer_date': self.transfer_date.isoformat() if self.transfer_date else None
        }


@dataclass
class MdlStatistics:
    """Statistical information about MDL."""
    
    total_actions: int = 0
    pending_actions: int = 0
    terminated_actions: int = 0
    remanded_actions: int = 0
    
    # Case distribution
    member_cases: int = 0
    tag_along_actions: int = 0
    directly_filed_actions: int = 0
    
    # Timeline statistics
    average_case_duration_days: Optional[float] = None
    median_case_duration_days: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'total_actions': self.total_actions,
            'pending_actions': self.pending_actions,
            'terminated_actions': self.terminated_actions,
            'remanded_actions': self.remanded_actions,
            'member_cases': self.member_cases,
            'tag_along_actions': self.tag_along_actions,
            'directly_filed_actions': self.directly_filed_actions,
            'average_case_duration_days': self.average_case_duration_days,
            'median_case_duration_days': self.median_case_duration_days
        }


@dataclass 
class MdlCase:
    """
    Domain model representing an MDL (Multidistrict Litigation) case.
    
    MDLs are created when multiple federal cases involving common questions
    of fact are transferred to a single judge for coordinated pretrial
    proceedings.
    """
    
    # Identity
    mdl_number: str
    case_title: str
    
    # Court information
    transferee_court: str
    transferee_district: str
    transferee_judge: str
    
    # Case details
    docket_number: Optional[str] = None
    case_type: CaseType = CaseType.UNKNOWN
    mdl_status: MdlStatus = MdlStatus.UNKNOWN
    
    # Dates
    date_filed: Optional[date] = None
    date_centralized: Optional[date] = None
    date_terminated: Optional[date] = None
    last_update_date: Optional[date] = None
    
    # Description and classification
    short_title: Optional[str] = None
    nature_of_action: Optional[str] = None
    cause_of_action: Optional[str] = None
    case_description: Optional[str] = None
    
    # Transferor information
    transferor_courts: List[TransferorCourt] = field(default_factory=list)
    
    # Statistics
    statistics: MdlStatistics = field(default_factory=MdlStatistics)
    
    # Related cases
    member_case_numbers: List[str] = field(default_factory=list)
    tag_along_case_numbers: List[str] = field(default_factory=list)
    related_mdl_numbers: List[str] = field(default_factory=list)
    
    # Processing metadata
    data_source: Optional[str] = None
    source_url: Optional[str] = None
    last_scraped: Optional[datetime] = None
    processing_notes: List[str] = field(default_factory=list)
    
    # Legal categorization
    legal_categories: List[str] = field(default_factory=list)
    subject_matter: Optional[str] = None
    product_liability: bool = False
    mass_tort: bool = False
    
    # Settlement information
    settlement_status: Optional[str] = None
    settlement_amount: Optional[float] = None
    settlement_date: Optional[date] = None
    
    # Key documents
    consolidation_order_url: Optional[str] = None
    case_management_order_urls: List[str] = field(default_factory=list)
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    @property
    def is_active(self) -> bool:
        """Check if MDL is currently active."""
        return self.mdl_status == MdlStatus.ACTIVE
    
    @property
    def duration_days(self) -> Optional[int]:
        """Calculate MDL duration in days."""
        if not self.date_centralized:
            return None
        end_date = self.date_terminated or date.today()
        return (end_date - self.date_centralized).days
    
    @property
    def total_cases(self) -> int:
        """Get total number of related cases."""
        return len(self.member_case_numbers) + len(self.tag_along_case_numbers)
    
    def add_transferor_court(self, court: TransferorCourt) -> None:
        """Add a transferor court."""
        self.transferor_courts.append(court)
        self.updated_at = datetime.utcnow()
    
    def add_member_case(self, case_number: str) -> None:
        """Add a member case number."""
        if case_number not in self.member_case_numbers:
            self.member_case_numbers.append(case_number)
            self.statistics.member_cases = len(self.member_case_numbers)
            self.updated_at = datetime.utcnow()
    
    def add_tag_along_case(self, case_number: str) -> None:
        """Add a tag-along case number."""
        if case_number not in self.tag_along_case_numbers:
            self.tag_along_case_numbers.append(case_number)
            self.statistics.tag_along_actions = len(self.tag_along_case_numbers)
            self.updated_at = datetime.utcnow()
    
    def add_legal_category(self, category: str) -> None:
        """Add a legal category."""
        if category not in self.legal_categories:
            self.legal_categories.append(category)
            self.updated_at = datetime.utcnow()
    
    def update_status(self, status: MdlStatus, notes: Optional[str] = None) -> None:
        """Update MDL status."""
        self.mdl_status = status
        if notes:
            self.processing_notes.append(f"{datetime.utcnow().isoformat()}: {notes}")
        self.updated_at = datetime.utcnow()
    
    def update_statistics(self, stats: Dict[str, Any]) -> None:
        """Update statistics from dictionary."""
        for key, value in stats.items():
            if hasattr(self.statistics, key):
                setattr(self.statistics, key, value)
        self.updated_at = datetime.utcnow()
    
    def is_product_liability(self) -> bool:
        """Check if this is a product liability case."""
        return (self.product_liability or 
                'product liability' in self.case_title.lower() or
                'defective' in self.case_title.lower())
    
    def is_mass_tort(self) -> bool:
        """Check if this is a mass tort case."""
        return (self.mass_tort or
                'mass tort' in self.case_description.lower() if self.case_description else False)
    
    def get_district_abbreviation(self) -> str:
        """Get abbreviated district name."""
        # Common district abbreviations
        abbrev_map = {
            'Eastern District': 'E.D.',
            'Western District': 'W.D.',
            'Northern District': 'N.D.',
            'Southern District': 'S.D.',
            'Central District': 'C.D.',
            'Middle District': 'M.D.'
        }
        
        district = self.transferee_district
        for full, abbrev in abbrev_map.items():
            if full in district:
                return district.replace(full, abbrev)
        return district
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'mdl_number': self.mdl_number,
            'case_title': self.case_title,
            'transferee_court': self.transferee_court,
            'transferee_district': self.transferee_district,
            'transferee_judge': self.transferee_judge,
            'docket_number': self.docket_number,
            'case_type': self.case_type.value,
            'mdl_status': self.mdl_status.value,
            'date_filed': self.date_filed.isoformat() if self.date_filed else None,
            'date_centralized': self.date_centralized.isoformat() if self.date_centralized else None,
            'date_terminated': self.date_terminated.isoformat() if self.date_terminated else None,
            'last_update_date': self.last_update_date.isoformat() if self.last_update_date else None,
            'short_title': self.short_title,
            'nature_of_action': self.nature_of_action,
            'cause_of_action': self.cause_of_action,
            'case_description': self.case_description,
            'transferor_courts': [court.to_dict() for court in self.transferor_courts],
            'statistics': self.statistics.to_dict(),
            'member_case_numbers': self.member_case_numbers,
            'tag_along_case_numbers': self.tag_along_case_numbers,
            'related_mdl_numbers': self.related_mdl_numbers,
            'data_source': self.data_source,
            'source_url': self.source_url,
            'last_scraped': self.last_scraped.isoformat() if self.last_scraped else None,
            'processing_notes': self.processing_notes,
            'legal_categories': self.legal_categories,
            'subject_matter': self.subject_matter,
            'product_liability': self.product_liability,
            'mass_tort': self.mass_tort,
            'settlement_status': self.settlement_status,
            'settlement_amount': self.settlement_amount,
            'settlement_date': self.settlement_date.isoformat() if self.settlement_date else None,
            'consolidation_order_url': self.consolidation_order_url,
            'case_management_order_urls': self.case_management_order_urls,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MdlCase':
        """Create instance from dictionary."""
        # Convert string enums back to enum instances
        if isinstance(data.get('case_type'), str):
            data['case_type'] = CaseType(data['case_type'])
        if isinstance(data.get('mdl_status'), str):
            data['mdl_status'] = MdlStatus(data['mdl_status'])
        
        # Convert date strings back to date objects
        date_fields = ['date_filed', 'date_centralized', 'date_terminated', 
                      'last_update_date', 'settlement_date']
        for field in date_fields:
            if data.get(field):
                data[field] = date.fromisoformat(data[field])
        
        # Convert datetime strings back to datetime objects
        datetime_fields = ['last_scraped', 'created_at', 'updated_at']
        for field in datetime_fields:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        
        # Convert transferor courts
        if data.get('transferor_courts'):
            courts = []
            for court_data in data['transferor_courts']:
                if court_data.get('transfer_date'):
                    court_data['transfer_date'] = date.fromisoformat(court_data['transfer_date'])
                courts.append(TransferorCourt(**court_data))
            data['transferor_courts'] = courts
        
        # Convert statistics
        if data.get('statistics'):
            data['statistics'] = MdlStatistics(**data['statistics'])
        
        return cls(**data)