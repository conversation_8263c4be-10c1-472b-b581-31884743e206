"""
Law Firm Domain Model

Represents a law firm entity with contact information, practice areas,
and relationships to legal cases and attorneys.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Dict, List, Any
from enum import Enum


class FirmSize(Enum):
    """Law firm size categories."""
    SOLO = "solo"
    SMALL = "small"  # 2-10 attorneys
    MEDIUM = "medium"  # 11-50 attorneys
    LARGE = "large"  # 51-250 attorneys
    BIGLAW = "biglaw"  # 250+ attorneys
    UNKNOWN = "unknown"


class FirmType(Enum):
    """Type of law firm practice."""
    GENERAL_PRACTICE = "general_practice"
    BOUTIQUE = "boutique"
    CORPORATE = "corporate"
    LITIGATION = "litigation"
    PERSONAL_INJURY = "personal_injury"
    INSURANCE_DEFENSE = "insurance_defense"
    GOVERNMENT = "government"
    IN_HOUSE = "in_house"
    UNKNOWN = "unknown"


@dataclass
class ContactInformation:
    """Contact details for law firm."""
    
    # Address
    street_address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    country: str = "United States"
    
    # Communication
    phone: Optional[str] = None
    fax: Optional[str] = None
    email: Optional[str] = None
    website: Optional[str] = None
    
    # Business identifiers
    bar_number: Optional[str] = None
    tax_id: Optional[str] = None
    
    @property
    def full_address(self) -> Optional[str]:
        """Get formatted full address."""
        parts = [self.street_address, self.city, self.state, self.zip_code]
        address_parts = [part for part in parts if part]
        return ', '.join(address_parts) if address_parts else None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'street_address': self.street_address,
            'city': self.city,
            'state': self.state,
            'zip_code': self.zip_code,
            'country': self.country,
            'phone': self.phone,
            'fax': self.fax,
            'email': self.email,
            'website': self.website,
            'bar_number': self.bar_number,
            'tax_id': self.tax_id
        }


@dataclass
class AttorneyInfo:
    """Information about an attorney at the firm."""
    
    full_name: str
    title: Optional[str] = None  # Partner, Associate, Of Counsel, etc.
    bar_number: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    practice_areas: List[str] = field(default_factory=list)
    years_experience: Optional[int] = None
    law_school: Optional[str] = None
    admission_date: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'full_name': self.full_name,
            'title': self.title,
            'bar_number': self.bar_number,
            'email': self.email,
            'phone': self.phone,
            'practice_areas': self.practice_areas,
            'years_experience': self.years_experience,
            'law_school': self.law_school,
            'admission_date': self.admission_date.isoformat() if self.admission_date else None
        }


@dataclass
class LawFirm:
    """
    Domain model representing a law firm.
    
    Contains comprehensive information about law firms including
    contact details, attorneys, practice areas, and case relationships.
    """
    
    # Identity
    firm_name: str
    
    # Alternative names and identifiers
    alternate_names: List[str] = field(default_factory=list)
    firm_id: Optional[str] = None
    
    # Classification
    firm_size: FirmSize = FirmSize.UNKNOWN
    firm_type: FirmType = FirmType.UNKNOWN
    
    # Contact information
    contact_info: ContactInformation = field(default_factory=ContactInformation)
    
    # Practice information
    practice_areas: List[str] = field(default_factory=list)
    primary_practice_area: Optional[str] = None
    specializations: List[str] = field(default_factory=list)
    
    # Attorneys
    attorneys: List[AttorneyInfo] = field(default_factory=list)
    managing_partner: Optional[str] = None
    
    # Business information
    founded_year: Optional[int] = None
    employee_count: Optional[int] = None
    revenue: Optional[float] = None
    
    # Legal metrics
    martindale_hubbell_rating: Optional[str] = None
    super_lawyers_count: Optional[int] = None
    notable_recognitions: List[str] = field(default_factory=list)
    
    # Case relationships
    active_cases: List[str] = field(default_factory=list)
    past_cases: List[str] = field(default_factory=list)
    mdl_cases: List[str] = field(default_factory=list)
    
    # Geographic presence
    office_locations: List[ContactInformation] = field(default_factory=list)
    licensed_states: List[str] = field(default_factory=list)
    
    # Data quality and source
    data_source: Optional[str] = None
    data_quality_score: Optional[float] = None
    verification_status: str = "unverified"
    
    # Processing metadata
    normalization_notes: List[str] = field(default_factory=list)
    merge_history: List[str] = field(default_factory=list)
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    last_verified: Optional[datetime] = None
    
    @property
    def attorney_count(self) -> int:
        """Get count of attorneys."""
        return len(self.attorneys)
    
    @property
    def office_count(self) -> int:
        """Get count of office locations."""
        return len(self.office_locations)
    
    @property
    def total_cases(self) -> int:
        """Get total case count."""
        return len(self.active_cases) + len(self.past_cases)
    
    @property
    def primary_location(self) -> ContactInformation:
        """Get primary office location."""
        return self.contact_info
    
    def add_attorney(self, attorney: AttorneyInfo) -> None:
        """Add an attorney to the firm."""
        self.attorneys.append(attorney)
        self.updated_at = datetime.utcnow()
    
    def add_office_location(self, location: ContactInformation) -> None:
        """Add an office location."""
        self.office_locations.append(location)
        self.updated_at = datetime.utcnow()
    
    def add_practice_area(self, practice_area: str) -> None:
        """Add a practice area."""
        if practice_area not in self.practice_areas:
            self.practice_areas.append(practice_area)
            self.updated_at = datetime.utcnow()
    
    def add_case(self, case_number: str, is_active: bool = True) -> None:
        """Add a case to the firm's portfolio."""
        case_list = self.active_cases if is_active else self.past_cases
        if case_number not in case_list:
            case_list.append(case_number)
            self.updated_at = datetime.utcnow()
    
    def add_mdl_case(self, mdl_number: str) -> None:
        """Add an MDL case."""
        if mdl_number not in self.mdl_cases:
            self.mdl_cases.append(mdl_number)
            self.updated_at = datetime.utcnow()
    
    def add_alternate_name(self, name: str) -> None:
        """Add an alternate firm name."""
        if name not in self.alternate_names and name != self.firm_name:
            self.alternate_names.append(name)
            self.updated_at = datetime.utcnow()
    
    def get_attorneys_by_title(self, title: str) -> List[AttorneyInfo]:
        """Get attorneys by title."""
        return [attorney for attorney in self.attorneys 
                if attorney.title and attorney.title.lower() == title.lower()]
    
    def get_partners(self) -> List[AttorneyInfo]:
        """Get all partners."""
        partner_titles = ['partner', 'managing partner', 'senior partner', 'founding partner']
        return [attorney for attorney in self.attorneys
                if attorney.title and any(title in attorney.title.lower() 
                                        for title in partner_titles)]
    
    def update_firm_size_from_attorney_count(self) -> None:
        """Update firm size based on attorney count."""
        count = self.attorney_count
        if count == 1:
            self.firm_size = FirmSize.SOLO
        elif count <= 10:
            self.firm_size = FirmSize.SMALL
        elif count <= 50:
            self.firm_size = FirmSize.MEDIUM
        elif count <= 250:
            self.firm_size = FirmSize.LARGE
        else:
            self.firm_size = FirmSize.BIGLAW
        
        self.updated_at = datetime.utcnow()
    
    def is_multi_state_firm(self) -> bool:
        """Check if firm operates in multiple states."""
        return len(self.licensed_states) > 1
    
    def has_office_in_state(self, state: str) -> bool:
        """Check if firm has office in specific state."""
        if self.contact_info.state and self.contact_info.state.upper() == state.upper():
            return True
        return any(office.state and office.state.upper() == state.upper() 
                  for office in self.office_locations)
    
    def mark_verified(self, verification_notes: Optional[str] = None) -> None:
        """Mark firm as verified."""
        self.verification_status = "verified"
        self.last_verified = datetime.utcnow()
        if verification_notes:
            self.normalization_notes.append(f"Verified: {verification_notes}")
        self.updated_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'firm_name': self.firm_name,
            'alternate_names': self.alternate_names,
            'firm_id': self.firm_id,
            'firm_size': self.firm_size.value,
            'firm_type': self.firm_type.value,
            'contact_info': self.contact_info.to_dict(),
            'practice_areas': self.practice_areas,
            'primary_practice_area': self.primary_practice_area,
            'specializations': self.specializations,
            'attorneys': [attorney.to_dict() for attorney in self.attorneys],
            'managing_partner': self.managing_partner,
            'founded_year': self.founded_year,
            'employee_count': self.employee_count,
            'revenue': self.revenue,
            'martindale_hubbell_rating': self.martindale_hubbell_rating,
            'super_lawyers_count': self.super_lawyers_count,
            'notable_recognitions': self.notable_recognitions,
            'active_cases': self.active_cases,
            'past_cases': self.past_cases,
            'mdl_cases': self.mdl_cases,
            'office_locations': [office.to_dict() for office in self.office_locations],
            'licensed_states': self.licensed_states,
            'data_source': self.data_source,
            'data_quality_score': self.data_quality_score,
            'verification_status': self.verification_status,
            'normalization_notes': self.normalization_notes,
            'merge_history': self.merge_history,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'last_verified': self.last_verified.isoformat() if self.last_verified else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LawFirm':
        """Create instance from dictionary."""
        # Convert string enums back to enum instances
        if isinstance(data.get('firm_size'), str):
            data['firm_size'] = FirmSize(data['firm_size'])
        if isinstance(data.get('firm_type'), str):
            data['firm_type'] = FirmType(data['firm_type'])
        
        # Convert datetime strings back to datetime objects
        datetime_fields = ['created_at', 'updated_at', 'last_verified']
        for field in datetime_fields:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        
        # Convert contact info
        if data.get('contact_info'):
            data['contact_info'] = ContactInformation(**data['contact_info'])
        
        # Convert attorneys
        if data.get('attorneys'):
            attorneys = []
            for attorney_data in data['attorneys']:
                if attorney_data.get('admission_date'):
                    attorney_data['admission_date'] = datetime.fromisoformat(attorney_data['admission_date'])
                attorneys.append(AttorneyInfo(**attorney_data))
            data['attorneys'] = attorneys
        
        # Convert office locations
        if data.get('office_locations'):
            offices = [ContactInformation(**office_data) 
                      for office_data in data['office_locations']]
            data['office_locations'] = offices
        
        return cls(**data)