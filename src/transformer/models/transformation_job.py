"""
Transformation Job Domain Model

Command pattern implementation - encapsulates transformation request with
all necessary data and metadata for processing.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Any, Dict, List
from enum import Enum


class JobStatus(Enum):
    """Job execution status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class JobPriority(Enum):
    """Job priority levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class TransformationJob:
    """
    Command pattern - encapsulates transformation request.
    
    This model represents a unit of work for the transformer service,
    containing all necessary information to execute a transformation
    operation on legal document data.
    """
    
    # Required fields
    json_path: str
    
    # Optional configuration
    force_reprocess: bool = False
    transformer: Optional[Any] = None
    
    # Execution metadata
    status: JobStatus = JobStatus.PENDING
    priority: JobPriority = JobPriority.MEDIUM
    
    # Results and tracking
    result: Optional[Any] = None
    error_message: Optional[str] = None
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Processing metadata
    processing_context: Dict[str, Any] = field(default_factory=dict)
    retry_count: int = 0
    max_retries: int = 3
    
    # Dependencies and relationships
    dependencies: List[str] = field(default_factory=list)
    related_jobs: List[str] = field(default_factory=list)
    
    # Performance tracking
    execution_time_seconds: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    
    def is_completed(self) -> bool:
        """Check if job has completed successfully."""
        return self.status == JobStatus.COMPLETED
    
    def is_failed(self) -> bool:
        """Check if job has failed."""
        return self.status == JobStatus.FAILED
    
    def can_retry(self) -> bool:
        """Check if job can be retried."""
        return (self.status == JobStatus.FAILED and 
                self.retry_count < self.max_retries)
    
    def mark_started(self) -> None:
        """Mark job as started."""
        self.status = JobStatus.IN_PROGRESS
        self.started_at = datetime.utcnow()
    
    def mark_completed(self, result: Any = None) -> None:
        """Mark job as completed with optional result."""
        self.status = JobStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        if result is not None:
            self.result = result
        self._calculate_execution_time()
    
    def mark_failed(self, error_message: str) -> None:
        """Mark job as failed with error message."""
        self.status = JobStatus.FAILED
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
        self._calculate_execution_time()
    
    def increment_retry(self) -> None:
        """Increment retry counter and reset status."""
        self.retry_count += 1
        self.status = JobStatus.PENDING
        self.started_at = None
        self.completed_at = None
        self.error_message = None
    
    def _calculate_execution_time(self) -> None:
        """Calculate and store execution time."""
        if self.started_at and self.completed_at:
            delta = self.completed_at - self.started_at
            self.execution_time_seconds = delta.total_seconds()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'json_path': self.json_path,
            'force_reprocess': self.force_reprocess,
            'status': self.status.value,
            'priority': self.priority.value,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'processing_context': self.processing_context,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'dependencies': self.dependencies,
            'related_jobs': self.related_jobs,
            'execution_time_seconds': self.execution_time_seconds,
            'memory_usage_mb': self.memory_usage_mb
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TransformationJob':
        """Create instance from dictionary."""
        # Convert string enums back to enum instances
        if isinstance(data.get('status'), str):
            data['status'] = JobStatus(data['status'])
        if isinstance(data.get('priority'), str):
            data['priority'] = JobPriority(data['priority'])
        
        # Convert datetime strings back to datetime objects
        for field_name in ['created_at', 'started_at', 'completed_at']:
            if data.get(field_name):
                data[field_name] = datetime.fromisoformat(data[field_name])
        
        return cls(**data)