"""
Classification Result Domain Model

Represents the result of case classification operations including
confidence scores, categories, and processing metadata.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Dict, List, Any, Union
from enum import Enum


class ClassificationAlgorithm(Enum):
    """Algorithm used for classification."""
    RULE_BASED = "rule_based"
    MACHINE_LEARNING = "machine_learning" 
    NEURAL_NETWORK = "neural_network"
    HYBRID = "hybrid"
    MANUAL = "manual"
    LLM = "llm"
    UNKNOWN = "unknown"


class ClassificationStatus(Enum):
    """Status of classification operation."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    REQUIRES_REVIEW = "requires_review"
    MANUALLY_OVERRIDDEN = "manually_overridden"


class ConfidenceLevel(Enum):
    """Confidence level categories."""
    VERY_LOW = "very_low"      # < 0.3
    LOW = "low"                # 0.3 - 0.5
    MEDIUM = "medium"          # 0.5 - 0.7
    HIGH = "high"              # 0.7 - 0.9
    VERY_HIGH = "very_high"    # > 0.9


@dataclass
class ClassificationScore:
    """Individual classification score for a category."""
    
    category: str
    score: float
    confidence_level: ConfidenceLevel
    
    # Supporting evidence
    matching_keywords: List[str] = field(default_factory=list)
    matching_patterns: List[str] = field(default_factory=list)
    rule_matches: List[str] = field(default_factory=list)
    
    # Algorithm details
    algorithm_details: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Set confidence level based on score."""
        if self.score < 0.3:
            self.confidence_level = ConfidenceLevel.VERY_LOW
        elif self.score < 0.5:
            self.confidence_level = ConfidenceLevel.LOW
        elif self.score < 0.7:
            self.confidence_level = ConfidenceLevel.MEDIUM
        elif self.score < 0.9:
            self.confidence_level = ConfidenceLevel.HIGH
        else:
            self.confidence_level = ConfidenceLevel.VERY_HIGH
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'category': self.category,
            'score': self.score,
            'confidence_level': self.confidence_level.value,
            'matching_keywords': self.matching_keywords,
            'matching_patterns': self.matching_patterns,
            'rule_matches': self.rule_matches,
            'algorithm_details': self.algorithm_details
        }


@dataclass
class ClassificationMetadata:
    """Metadata about the classification process."""
    
    # Processing information
    processing_time_seconds: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    
    # Input characteristics
    input_text_length: Optional[int] = None
    input_token_count: Optional[int] = None
    preprocessed_text_length: Optional[int] = None
    
    # Model/algorithm information
    model_version: Optional[str] = None
    model_config: Dict[str, Any] = field(default_factory=dict)
    training_data_version: Optional[str] = None
    
    # Feature extraction
    feature_count: Optional[int] = None
    feature_extraction_method: Optional[str] = None
    
    # Quality indicators
    text_quality_score: Optional[float] = None
    completeness_score: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'processing_time_seconds': self.processing_time_seconds,
            'memory_usage_mb': self.memory_usage_mb,
            'cpu_usage_percent': self.cpu_usage_percent,
            'input_text_length': self.input_text_length,
            'input_token_count': self.input_token_count,
            'preprocessed_text_length': self.preprocessed_text_length,
            'model_version': self.model_version,
            'model_config': self.model_config,
            'training_data_version': self.training_data_version,
            'feature_count': self.feature_count,
            'feature_extraction_method': self.feature_extraction_method,
            'text_quality_score': self.text_quality_score,
            'completeness_score': self.completeness_score
        }


@dataclass
class ClassificationResult:
    """
    Domain model representing the result of case classification.
    
    Contains comprehensive information about classification outcomes
    including scores, confidence levels, and processing metadata.
    """
    
    # Identity and context
    classification_id: str
    input_document_id: Optional[str] = None
    case_number: Optional[str] = None
    
    # Classification results
    primary_classification: Optional[str] = None
    primary_score: Optional[float] = None
    
    # All classification scores
    classification_scores: List[ClassificationScore] = field(default_factory=list)
    
    # Overall metrics
    overall_confidence: Optional[float] = None
    max_score: Optional[float] = None
    score_spread: Optional[float] = None  # Difference between top 2 scores
    
    # Algorithm information
    algorithm_used: ClassificationAlgorithm = ClassificationAlgorithm.UNKNOWN
    algorithm_version: Optional[str] = None
    
    # Status and quality
    status: ClassificationStatus = ClassificationStatus.PENDING
    requires_manual_review: bool = False
    quality_flags: List[str] = field(default_factory=list)
    
    # Processing metadata
    metadata: ClassificationMetadata = field(default_factory=ClassificationMetadata)
    
    # Additional categorizations
    secondary_classifications: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    
    # Transfer and routing information
    transfer_recommendation: Optional[str] = None
    routing_suggestion: Optional[str] = None
    jurisdiction_recommendation: Optional[str] = None
    
    # Review and audit
    reviewed_by: Optional[str] = None
    review_date: Optional[datetime] = None
    review_notes: Optional[str] = None
    
    # Error handling
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def __post_init__(self):
        """Post-initialization calculations."""
        self._calculate_derived_metrics()
    
    def _calculate_derived_metrics(self) -> None:
        """Calculate derived metrics from scores."""
        if not self.classification_scores:
            return
        
        # Sort scores by value
        sorted_scores = sorted(self.classification_scores, 
                             key=lambda x: x.score, reverse=True)
        
        # Set primary classification
        if sorted_scores:
            top_score = sorted_scores[0]
            self.primary_classification = top_score.category
            self.primary_score = top_score.score
            self.max_score = top_score.score
        
        # Calculate score spread
        if len(sorted_scores) >= 2:
            self.score_spread = sorted_scores[0].score - sorted_scores[1].score
        
        # Calculate overall confidence (average of top 3 scores)
        top_scores = [score.score for score in sorted_scores[:3]]
        if top_scores:
            self.overall_confidence = sum(top_scores) / len(top_scores)
    
    def add_classification_score(self, score: ClassificationScore) -> None:
        """Add a classification score."""
        self.classification_scores.append(score)
        self._calculate_derived_metrics()
        self.updated_at = datetime.utcnow()
    
    def get_top_classifications(self, n: int = 5) -> List[ClassificationScore]:
        """Get top N classifications by score."""
        return sorted(self.classification_scores, 
                     key=lambda x: x.score, reverse=True)[:n]
    
    def get_high_confidence_classifications(self, 
                                          min_confidence: float = 0.7) -> List[ClassificationScore]:
        """Get classifications above confidence threshold."""
        return [score for score in self.classification_scores 
                if score.score >= min_confidence]
    
    def is_confident_classification(self, min_confidence: float = 0.7) -> bool:
        """Check if primary classification is above confidence threshold."""
        return bool(self.primary_score and self.primary_score >= min_confidence)
    
    def is_ambiguous_classification(self, max_spread: float = 0.1) -> bool:
        """Check if classification is ambiguous (small score spread)."""
        return bool(self.score_spread is not None and self.score_spread <= max_spread)
    
    def add_quality_flag(self, flag: str) -> None:
        """Add a quality flag."""
        if flag not in self.quality_flags:
            self.quality_flags.append(flag)
            self.updated_at = datetime.utcnow()
    
    def add_error(self, error: str) -> None:
        """Add an error."""
        if error not in self.errors:
            self.errors.append(error)
            self.status = ClassificationStatus.FAILED
            self.updated_at = datetime.utcnow()
    
    def add_warning(self, warning: str) -> None:
        """Add a warning."""
        if warning not in self.warnings:
            self.warnings.append(warning)
            self.updated_at = datetime.utcnow()
    
    def mark_for_review(self, reason: str) -> None:
        """Mark classification for manual review."""
        self.requires_manual_review = True
        self.status = ClassificationStatus.REQUIRES_REVIEW
        self.add_quality_flag(f"Review required: {reason}")
        self.updated_at = datetime.utcnow()
    
    def complete_review(self, reviewer: str, notes: Optional[str] = None) -> None:
        """Mark review as complete."""
        self.reviewed_by = reviewer
        self.review_date = datetime.utcnow()
        self.review_notes = notes
        self.requires_manual_review = False
        self.status = ClassificationStatus.COMPLETED
        self.updated_at = datetime.utcnow()
    
    def override_classification(self, new_classification: str, 
                              reviewer: str, reason: str) -> None:
        """Manually override classification."""
        self.primary_classification = new_classification
        self.reviewed_by = reviewer
        self.review_date = datetime.utcnow()
        self.review_notes = f"Manual override: {reason}"
        self.status = ClassificationStatus.MANUALLY_OVERRIDDEN
        self.add_quality_flag("Manual override applied")
        self.updated_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'classification_id': self.classification_id,
            'input_document_id': self.input_document_id,
            'case_number': self.case_number,
            'primary_classification': self.primary_classification,
            'primary_score': self.primary_score,
            'classification_scores': [score.to_dict() for score in self.classification_scores],
            'overall_confidence': self.overall_confidence,
            'max_score': self.max_score,
            'score_spread': self.score_spread,
            'algorithm_used': self.algorithm_used.value,
            'algorithm_version': self.algorithm_version,
            'status': self.status.value,
            'requires_manual_review': self.requires_manual_review,
            'quality_flags': self.quality_flags,
            'metadata': self.metadata.to_dict(),
            'secondary_classifications': self.secondary_classifications,
            'tags': self.tags,
            'transfer_recommendation': self.transfer_recommendation,
            'routing_suggestion': self.routing_suggestion,
            'jurisdiction_recommendation': self.jurisdiction_recommendation,
            'reviewed_by': self.reviewed_by,
            'review_date': self.review_date.isoformat() if self.review_date else None,
            'review_notes': self.review_notes,
            'errors': self.errors,
            'warnings': self.warnings,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ClassificationResult':
        """Create instance from dictionary."""
        # Convert string enums back to enum instances
        if isinstance(data.get('algorithm_used'), str):
            data['algorithm_used'] = ClassificationAlgorithm(data['algorithm_used'])
        if isinstance(data.get('status'), str):
            data['status'] = ClassificationStatus(data['status'])
        
        # Convert datetime strings back to datetime objects
        datetime_fields = ['review_date', 'created_at', 'updated_at']
        for field in datetime_fields:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        
        # Convert classification scores
        if data.get('classification_scores'):
            scores = []
            for score_data in data['classification_scores']:
                if isinstance(score_data.get('confidence_level'), str):
                    score_data['confidence_level'] = ConfidenceLevel(score_data['confidence_level'])
                scores.append(ClassificationScore(**score_data))
            data['classification_scores'] = scores
        
        # Convert metadata
        if data.get('metadata'):
            data['metadata'] = ClassificationMetadata(**data['metadata'])
        
        return cls(**data)