"""
Docket Domain Model

Represents a legal case docket with entries, metadata, and processing state.
A docket contains the chronological record of all filings and proceedings
in a legal case.
"""

from dataclasses import dataclass, field
from datetime import datetime, date
from typing import Optional, Dict, List, Any
from enum import Enum


class DocketEntryType(Enum):
    """Types of docket entries."""
    FILING = "filing"
    ORDER = "order"
    NOTICE = "notice"
    HEARING = "hearing"
    MOTION = "motion"
    JUDGMENT = "judgment"
    ADMINISTRATIVE = "administrative"
    UNKNOWN = "unknown"


class DocketStatus(Enum):
    """Docket processing status."""
    RAW = "raw"
    PARSED = "parsed"
    VALIDATED = "validated"
    PROCESSED = "processed"
    ERROR = "error"


@dataclass
class DocketEntry:
    """Individual entry in a docket."""
    
    entry_number: int
    entry_date: date
    entry_type: DocketEntryType = DocketEntryType.UNKNOWN
    description: str = ""
    
    # Document references
    document_number: Optional[str] = None
    document_url: Optional[str] = None
    document_pages: Optional[int] = None
    
    # Party information
    filer: Optional[str] = None
    parties_involved: List[str] = field(default_factory=list)
    
    # Processing metadata
    raw_text: Optional[str] = None
    processed_text: Optional[str] = None
    extraction_confidence: Optional[float] = None
    
    # Financial information
    fee_amount: Optional[float] = None
    fee_status: Optional[str] = None
    
    def is_document_entry(self) -> bool:
        """Check if entry has associated document."""
        return bool(self.document_number or self.document_url)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'entry_number': self.entry_number,
            'entry_date': self.entry_date.isoformat(),
            'entry_type': self.entry_type.value,
            'description': self.description,
            'document_number': self.document_number,
            'document_url': self.document_url,
            'document_pages': self.document_pages,
            'filer': self.filer,
            'parties_involved': self.parties_involved,
            'raw_text': self.raw_text,
            'processed_text': self.processed_text,
            'extraction_confidence': self.extraction_confidence,
            'fee_amount': self.fee_amount,
            'fee_status': self.fee_status
        }


@dataclass
class DocketParty:
    """Party involved in the case."""
    
    name: str
    party_type: str  # plaintiff, defendant, attorney, etc.
    
    # Optional details
    attorney_name: Optional[str] = None
    attorney_firm: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    
    # Metadata
    party_number: Optional[str] = None
    role_description: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'name': self.name,
            'party_type': self.party_type,
            'attorney_name': self.attorney_name,
            'attorney_firm': self.attorney_firm,
            'address': self.address,
            'phone': self.phone,
            'email': self.email,
            'party_number': self.party_number,
            'role_description': self.role_description
        }


@dataclass
class Docket:
    """
    Domain model representing a legal case docket.
    
    A docket contains the complete chronological record of all proceedings,
    filings, and court actions for a specific legal case.
    """
    
    # Identity
    case_number: str
    court_name: str
    
    # Case information
    case_title: str = ""
    judge_name: Optional[str] = None
    case_type: Optional[str] = None
    jurisdiction: Optional[str] = None
    
    # Dates
    filed_date: Optional[date] = None
    closed_date: Optional[date] = None
    last_filing_date: Optional[date] = None
    
    # Case status
    case_status: str = "Open"
    nature_of_suit: Optional[str] = None
    cause_of_action: Optional[str] = None
    
    # Parties
    parties: List[DocketParty] = field(default_factory=list)
    
    # Docket entries
    entries: List[DocketEntry] = field(default_factory=list)
    
    # Processing state
    processing_status: DocketStatus = DocketStatus.RAW
    
    # Source information
    source_url: Optional[str] = None
    source_file: Optional[str] = None
    retrieval_date: Optional[datetime] = None
    
    # Processing metadata
    html_content: Optional[str] = None
    parsed_data: Optional[Dict[str, Any]] = None
    validation_errors: List[str] = field(default_factory=list)
    processing_notes: List[str] = field(default_factory=list)
    
    # Statistics
    total_entries: int = 0
    total_documents: int = 0
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def __post_init__(self):
        """Post-initialization to calculate derived fields."""
        self.total_entries = len(self.entries)
        self.total_documents = sum(1 for entry in self.entries if entry.is_document_entry())
    
    @property
    def is_active(self) -> bool:
        """Check if case is still active."""
        return self.case_status.lower() in ['open', 'active', 'pending']
    
    @property
    def case_age_days(self) -> Optional[int]:
        """Calculate case age in days."""
        if not self.filed_date:
            return None
        end_date = self.closed_date or date.today()
        return (end_date - self.filed_date).days
    
    def add_entry(self, entry: DocketEntry) -> None:
        """Add a docket entry."""
        self.entries.append(entry)
        self.total_entries = len(self.entries)
        self.total_documents = sum(1 for e in self.entries if e.is_document_entry())
        
        # Update last filing date
        if not self.last_filing_date or entry.entry_date > self.last_filing_date:
            self.last_filing_date = entry.entry_date
        
        self.updated_at = datetime.utcnow()
    
    def add_party(self, party: DocketParty) -> None:
        """Add a party to the case."""
        self.parties.append(party)
        self.updated_at = datetime.utcnow()
    
    def get_entries_by_type(self, entry_type: DocketEntryType) -> List[DocketEntry]:
        """Get all entries of a specific type."""
        return [entry for entry in self.entries if entry.entry_type == entry_type]
    
    def get_document_entries(self) -> List[DocketEntry]:
        """Get all entries that have associated documents."""
        return [entry for entry in self.entries if entry.is_document_entry()]
    
    def get_parties_by_type(self, party_type: str) -> List[DocketParty]:
        """Get all parties of a specific type."""
        return [party for party in self.parties if party.party_type.lower() == party_type.lower()]
    
    def update_processing_status(self, status: DocketStatus, notes: Optional[str] = None) -> None:
        """Update processing status."""
        self.processing_status = status
        if notes:
            self.processing_notes.append(f"{datetime.utcnow().isoformat()}: {notes}")
        self.updated_at = datetime.utcnow()
    
    def add_validation_error(self, error: str) -> None:
        """Add a validation error."""
        if error not in self.validation_errors:
            self.validation_errors.append(error)
            self.updated_at = datetime.utcnow()
    
    def is_valid(self) -> bool:
        """Check if docket is valid."""
        return len(self.validation_errors) == 0
    
    def get_chronological_entries(self) -> List[DocketEntry]:
        """Get entries sorted chronologically."""
        return sorted(self.entries, key=lambda e: e.entry_date)
    
    def get_entry_count_by_type(self) -> Dict[str, int]:
        """Get count of entries by type."""
        counts = {}
        for entry in self.entries:
            entry_type = entry.entry_type.value
            counts[entry_type] = counts.get(entry_type, 0) + 1
        return counts
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'case_number': self.case_number,
            'court_name': self.court_name,
            'case_title': self.case_title,
            'judge_name': self.judge_name,
            'case_type': self.case_type,
            'jurisdiction': self.jurisdiction,
            'filed_date': self.filed_date.isoformat() if self.filed_date else None,
            'closed_date': self.closed_date.isoformat() if self.closed_date else None,
            'last_filing_date': self.last_filing_date.isoformat() if self.last_filing_date else None,
            'case_status': self.case_status,
            'nature_of_suit': self.nature_of_suit,
            'cause_of_action': self.cause_of_action,
            'parties': [party.to_dict() for party in self.parties],
            'entries': [entry.to_dict() for entry in self.entries],
            'processing_status': self.processing_status.value,
            'source_url': self.source_url,
            'source_file': self.source_file,
            'retrieval_date': self.retrieval_date.isoformat() if self.retrieval_date else None,
            'html_content': self.html_content,
            'parsed_data': self.parsed_data,
            'validation_errors': self.validation_errors,
            'processing_notes': self.processing_notes,
            'total_entries': self.total_entries,
            'total_documents': self.total_documents,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }