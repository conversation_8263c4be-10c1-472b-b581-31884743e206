# /src/services/transformer/html_integration_service.py
"""
# Removed dependency_injector imports - using container-based injection

Transformer HTML Integration Service
Bridges the gap between new transformer services and HTML processing functionality.

This service ensures that the new transformer architecture has access to the same
HTML processing capabilities as the original implementation, including:
- DataUpdaterService integration
- Attorney extraction and deduplication
- S3 link construction and verification
- Transfer case processing
"""

import logging
from typing import Any

# Direct async imports - no compatibility layer needed
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError

# Removed improper logger_utils import
from src.services.html import CaseParserService, DataUpdaterService, HTMLProcessingOrchestrator

# Removed dependency_injector imports - using container-based injection


class TransformerHTMLIntegrationService(AsyncServiceBase):
    """Service for integrating HTML processing into the transformer architecture"""

    def __init__(self, config: dict[str, Any], logger: logging.Logger | None = None):
        logger_instance = logger or logging.getLogger(self.__class__.__name__)

        super().__init__(logger_instance, config)

        # Service instances
        self.html_data_updater = None
        self.html_processing_services = {}  # court_id -> HTMLProcessingOrchestrator
        self.docket_html_processor = None

        self.log_info("Transformer HTML integration service initialized")

    async def _execute_action(self, data: Any) -> Any:
        """Execute TransformerHTMLIntegrationService actions."""
        if isinstance(data, dict):
            action = data.get("action")
            action_data = data.get("data", {})

            match action:
                case "initialize":
                    s3_manager = action_data.get("s3_manager")
                    pacer_db = action_data.get("pacer_db")
                    await self.initialize(s3_manager, pacer_db)
                    return {"status": "initialized"}
                case "process_docket_html":
                    docket_data = action_data.get("docket_data", {})
                    json_path = action_data.get("json_path", "")
                    court_id = action_data.get("court_id")
                    return await self.process_docket_html(docket_data, json_path, court_id)
                case "extract_attorneys_from_html":
                    html_content = action_data.get("html_content", "")
                    court_id = action_data.get("court_id", "")
                    return await self.extract_attorneys_from_html(html_content, court_id)
                case "construct_s3_html_link":
                    json_path = action_data.get("json_path", "")
                    case_details = action_data.get("case_details", {})
                    court_id = action_data.get("court_id", "")
                    return await self.construct_s3_html_link(
                        json_path, case_details, court_id
                    )
                case "merge_party_data":
                    docket_data = action_data.get("docket_data", {})
                    return self.merge_party_data(docket_data)
                case "extract_law_firms_from_case_data":
                    case_data = action_data.get("case_data", {})
                    return self.extract_law_firms_from_case_data(case_data)
                case "validate_html_processing_integration":
                    return self.validate_html_processing_integration()
                case "get_integration_summary":
                    return self.get_integration_summary()
                case "get_or_create_html_processing_service":
                    court_id = action_data.get("court_id", "")
                    return self.get_or_create_html_processing_service(court_id)
                case "check_service_availability":
                    return self._check_service_availability()
                case "configure_html_processing":
                    html_config = action_data.get("html_config", {})
                    return await self._configure_html_processing(html_config)
                case "process_multiple_court_html":
                    court_data_list = action_data.get("court_data_list", [])
                    return await self._process_multiple_court_html(court_data_list)
                case "cleanup_html_services":
                    return self._cleanup_html_services()
                case _:
                    raise TransformerServiceError(
                        f"Invalid action '{action}' provided to TransformerHTMLIntegrationService"
                    )
        raise TransformerServiceError(
            "Invalid action data provided to TransformerHTMLIntegrationService"
        )

    async def initialize(self, s3_manager: Any, pacer_db: Any) -> None:
        """
        Initialize the HTML integration service with required dependencies.

        Args:
            s3_manager: S3 manager instance
            pacer_db: PACER database manager instance
        """
        try:
            self.log_info("Initializing HTML integration service")

            # Create CaseParserService for HTML parsing
            # Use validated logger from __init__ (already validated by validate_and_fix_logger)
            html_parser = CaseParserService(logger=self.logger)

            # Create DataUpdaterService with all required parameters
            self.html_data_updater = DataUpdaterService(
                logger=self.logger,
                config=self.config,
                s3_manager=s3_manager,
                pacer_db=pacer_db,
                html_parser=html_parser,
            )

            # Create DocketHTMLProcessor with DataUpdaterService
            self.docket_html_processor = None

            self.log_info("HTML integration service initialized successfully")

        except Exception as e:
            self.log_error(f"Error initializing HTML integration service: {e}")
            raise

    def get_or_create_html_processing_service(
        self, court_id: str
    ) -> HTMLProcessingOrchestrator:
        """
        Get or create HTML processing service for a court.

        Args:
            court_id: Court identifier

        Returns:
            HTML processing service for the court
        """
        if court_id not in self.html_processing_services:
            self.log_info(f"Creating HTML processing service for court: {court_id}")

            # Get S3 manager from DataUpdaterService for proper initialization
            s3_manager = None
            if self.html_data_updater and hasattr(self.html_data_updater, "s3_manager"):
                s3_manager = self.html_data_updater.s3_manager
                self.log_info(f"DEBUG: S3 manager available for court {court_id}: {s3_manager is not None}")
            else:
                self.log_warning(f"DEBUG: No S3 manager available for court {court_id}")

            # Use validated logger from __init__ (already validated by validate_and_fix_logger)
            service = HTMLProcessingOrchestrator(
                logger=self.logger,
                config=self.config,
                court_id=court_id,
                html_data_updater=self.html_data_updater,
                s3_async_storage=s3_manager  # CRITICAL FIX: Pass S3 storage during initialization
            )

            self.log_info(f"DEBUG: HTMLProcessingOrchestrator created for court {court_id} with S3 storage: {service.s3_async_storage is not None}")

            self.html_processing_services[court_id] = service

        return self.html_processing_services[court_id]

    async def process_docket_html(
        self, data: dict, json_path: str, court_id: str | None = None
    ) -> bool:
        """
        Process HTML for a docket using the enhanced processing pipeline.
        """
        try:
            filename = data.get("new_filename", data.get("docket_num", "unknown"))
            self.log_debug(f"Processing HTML for docket: {filename}")

            if court_id:
                html_processing_service = self.get_or_create_html_processing_service(
                    court_id
                )
                return await html_processing_service.process_s3_html(data, json_path)
            else:
                self.log_warning(f"No court_id provided for {filename}, cannot process HTML.")
                return False
        except Exception as e:
            self.log_error(f"Error processing docket HTML: {e}")
            return False

    async def extract_attorneys_from_html(
        self, html_content: str, court_id: str
    ) -> list[dict[str, str]]:
        """
        Extract and deduplicate attorneys from HTML content.

        Args:
            html_content: HTML content to process
            court_id: Court identifier

        Returns:
            List of unique attorney dictionaries
        """
        try:
            html_processing_service = self.get_or_create_html_processing_service(
                court_id
            )

            # Use the service to extract attorneys
            attorneys = (
                await html_processing_service.extract_and_deduplicate_attorneys(
                    html_content, {}
                )
            )

            self.log_debug(f"Extracted {len(attorneys)} attorneys for court {court_id}")
            return attorneys

        except Exception as e:
            self.log_error(f"Error extracting attorneys from HTML: {e}")
            return []

    async def construct_s3_html_link(
        self, json_path: str, case_details: dict[str, Any], court_id: str
    ) -> str | None:
        """
        Construct and verify S3 HTML link.

        Args:
            json_path: Path to JSON file
            case_details: Case details for filename construction
            court_id: Court identifier

        Returns:
            Verified S3 HTML URL or None
        """
        try:
            html_processing_service = self.get_or_create_html_processing_service(
                court_id
            )

            s3_link = await html_processing_service.construct_s3_html_link(
                json_path, case_details
            )

            if s3_link:
                self.log_debug(
                    f"Constructed S3 HTML link for court {court_id}: {s3_link}"
                )

            return s3_link

        except Exception as e:
            self.log_error(f"Error constructing S3 HTML link: {e}")
            return None

    async def process_html_content(self, case_details: dict[str, Any], 
                                   html_content: str, json_path: str) -> dict[str, Any]:
        """
        Process HTML content compatibility method for HTMLProcessorUtils.
        
        This method provides compatibility with HTMLProcessorUtils when process_docket_html
        is not available or when court_id is not provided.
        
        Args:
            case_details: Case details dictionary
            html_content: HTML content to process
            json_path: JSON file path
            
        Returns:
            Updated case details dictionary
        """
        try:
            court_id = case_details.get('court_id', 'UNKNOWN_COURT')
            self.log_info(f"Processing HTML content for court {court_id} via compatibility method")
            
            # Get or create the HTML processing service for this court
            html_processing_service = self.get_or_create_html_processing_service(court_id)
            
            # Use the HTMLProcessingOrchestrator's process_html_content method directly
            return await html_processing_service.process_html_content(
                case_details, html_content, json_path
            )
            
        except Exception as e:
            self.log_error(f"Error in compatibility process_html_content: {e}")
            case_details['_html_processing_error'] = str(e)
            return case_details

    def merge_party_data(self, data: dict[str, Any]) -> dict[str, Any]:
        """
        Merge and deduplicate party data (plaintiffs/defendants).

        Args:
            data: Data dictionary to process

        Returns:
            Data dictionary with merged party information
        """
        try:
            # Handle plaintiff merging
            if "plaintiffs" in data and isinstance(data["plaintiffs"], list):
                # Extract names from plaintiff objects
                plaintiff_names = []
                for p in data["plaintiffs"]:
                    if isinstance(p, dict):
                        name = p.get("name", str(p))
                    else:
                        name = str(p)

                    if name and name.strip():
                        plaintiff_names.append(name.strip())

                # Deduplicate while preserving order
                seen = set()
                data["plaintiff"] = [
                    x for x in plaintiff_names if not (x in seen or seen.add(x))
                ]

                # Remove plural form
                del data["plaintiffs"]

            # Handle defendant merging with deduplication
            if "defendants" in data and isinstance(data["defendants"], list):
                defendants = data["defendants"]
                clean_defendants = []
                seen_defendants = set()

                for d in defendants:
                    if isinstance(d, dict):
                        name = d.get("name", str(d))
                    else:
                        name = str(d)

                    name = name.strip()
                    if name and name.lower() not in seen_defendants:
                        clean_defendants.append(name)
                        seen_defendants.add(name.lower())

                data["defendant"] = clean_defendants
                # Remove plural form
                del data["defendants"]

            # Clean up other plural fields
            plural_fields_to_remove = ["plaintiffs_gpt"]
            for field in plural_fields_to_remove:
                if field in data:
                    del data[field]

            return data

        except Exception as e:
            self.log_error(f"Error merging party data: {e}")
            return data

    def extract_law_firms_from_case_data(self, data: dict[str, Any]) -> list[str]:
        """
        Extract law firm names from case data.

        Args:
            data: Case data dictionary

        Returns:
            List of unique law firm names
        """
        law_firms = []
        seen_firms = set()

        try:
            # Extract from attorney field
            attorneys = data.get("attorney", [])
            if isinstance(attorneys, list):
                for attorney in attorneys:
                    if isinstance(attorney, dict):
                        law_firm = attorney.get("law_firm", "").strip()
                        if law_firm and law_firm.lower() not in seen_firms:
                            law_firms.append(law_firm)
                            seen_firms.add(law_firm.lower())

            self.log_debug(f"Extracted {len(law_firms)} unique law firms")
            return law_firms

        except Exception as e:
            self.log_error(f"Error extracting law firms: {e}")
            return []

    def validate_html_processing_integration(self) -> dict[str, bool]:
        """
        Validate that HTML processing integration is properly configured.

        Returns:
            Validation results
        """
        validation_results = {
            "html_data_updater_available": self.html_data_updater is not None,
            "docket_html_processor_available": self.docket_html_processor is not None,
            "async_methods_available": False,
            "parse_methods_available": False,
        }

        # Check for async methods
        if self.html_data_updater:
            validation_results["async_methods_available"] = hasattr(
                self.html_data_updater, "parse_html_async"
            )
            validation_results["parse_methods_available"] = hasattr(
                self.html_data_updater, "parse_html"
            )

        # Log validation results
        self.log_info(f"HTML processing integration validation: {validation_results}")

        return validation_results

    def get_integration_summary(self) -> dict[str, Any]:
        """
        Get summary of HTML integration service status.

        Returns:
            Summary of service status
        """
        return {
            "initialized": self.html_data_updater is not None,
            "html_processing_services_count": len(self.html_processing_services),
            "available_courts": list(self.html_processing_services.keys()),
            "docket_processor_available": self.docket_html_processor is not None,
        }

    def _check_service_availability(self) -> dict[str, Any]:
        """Check availability of all HTML processing services."""
        return {
            "html_data_updater": {
                "available": self.html_data_updater is not None,
                "type": (
                    type(self.html_data_updater).__name__
                    if self.html_data_updater
                    else None
                ),
            },
            "docket_html_processor": {
                "available": self.docket_html_processor is not None,
                "type": (
                    type(self.docket_html_processor).__name__
                    if self.docket_html_processor
                    else None
                ),
            },
            "html_processing_services": {
                "count": len(self.html_processing_services),
                "courts": list(self.html_processing_services.keys()),
            },
            "service_status": (
                "ready"
                if self.html_data_updater and self.docket_html_processor
                else "not_initialized"
            ),
        }

    async def _configure_html_processing(
        self, html_config: dict[str, Any]
    ) -> dict[str, Any]:
        """Configure HTML processing with custom settings."""
        try:
            self.log_info(f"Configuring HTML processing with settings: {html_config}")

            # Update configuration
            for key, value in html_config.items():
                match key:
                    case "enhanced_html_processing" | "attorney_extraction_enabled":
                        self.config[key] = value

            # Reinitialize services if needed
            if html_config.get("reinitialize_services", False):
                # Clear existing services
                self.html_processing_services.clear()
                self.log_info("Cleared existing HTML processing services")

            return {
                "status": "configured",
                "applied_settings": html_config,
                "current_config": {key: self.config.get(key) for key in html_config},
            }

        except Exception as e:
            self.log_error(f"Error configuring HTML processing: {e}")
            return {"status": "error", "message": str(e)}

    async def _process_multiple_court_html(
        self, court_data_list: list[dict[str, Any]]
    ) -> list[dict[str, Any]]:
        """Process HTML for multiple courts in parallel."""
        try:
            self.log_info(f"Processing HTML for {len(court_data_list)} courts")

            results = []
            for court_data in court_data_list:
                court_id = court_data.get("court_id", "")
                docket_data = court_data.get("docket_data", {})
                json_path = court_data.get("json_path", "")

                try:
                    result = await self.process_docket_html(
                        docket_data, json_path, court_id
                    )
                    results.append(
                        {
                            "court_id": court_id,
                            "status": "success" if result else "failed",
                            "processed": result,
                        }
                    )
                except Exception as e:
                    self.log_error(f"Error processing HTML for court {court_id}: {e}")
                    results.append(
                        {"court_id": court_id, "status": "error", "error": str(e)}
                    )

            success_count = sum(1 for r in results if r["status"] == "success")
            self.log_info(
                f"Processed HTML for {success_count}/{len(court_data_list)} courts successfully"
            )

            return results
        
        except Exception as e:
            self.log_error(f"Error in bulk HTML processing: {e}")
            return []

    def validate_html_processing_results(self, current_data: dict, original_data: dict) -> dict:
        """
        Validate HTML processing results by comparing current data with original data.
        
        Args:
            current_data: Data after HTML processing
            original_data: Data before HTML processing
            
        Returns:
            Dictionary with validation results including list of changes
        """
        changes = []
        
        # Check for newly added or modified fields
        for key, value in current_data.items():
            if key not in original_data:
                changes.append(f"Added {key}")
            elif original_data.get(key) != value:
                changes.append(f"Updated {key}")
        
        # Check if attorney information was added/updated
        if 'attorney' in current_data and current_data.get('attorney'):
            if 'attorney' not in original_data or not original_data.get('attorney'):
                changes.append("Added attorney information")
            elif len(current_data['attorney']) != len(original_data.get('attorney', [])):
                changes.append("Updated attorney information")
        
        # Check for S3 HTML link construction
        if 's3_html' in current_data and 's3_html' not in original_data:
            changes.append("Added S3 HTML link")
            
        return {
            'changes': changes,
            'total_changes': len(changes),
            'processing_successful': not current_data.get('_html_processing_failed', False)
        }

    def _cleanup_html_services(self) -> dict[str, Any]:
        """Clean up HTML processing services and resources."""
        try:
            initial_count = len(self.html_processing_services)

            # Clear HTML processing services
            self.html_processing_services.clear()

            # Reset other services if needed
            cleanup_actions = []
            if self.html_data_updater:
                cleanup_actions.append("html_data_updater_preserved")
            if self.docket_html_processor:
                cleanup_actions.append("docket_html_processor_preserved")

            self.log_info(f"Cleaned up {initial_count} HTML processing services")

            return {
                "status": "cleaned",
                "services_removed": initial_count,
                "cleanup_actions": cleanup_actions,
                "remaining_services": {
                    "html_data_updater": self.html_data_updater is not None,
                    "docket_html_processor": self.docket_html_processor is not None,
                },
            }

        except Exception as e:
            self.log_error(f"Error during cleanup: {e}")
            return {"status": "error", "message": str(e)}
