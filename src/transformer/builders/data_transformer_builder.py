"""
Builder pattern implementations for complex transformer services.

This module provides builder classes to simplify the construction of services
with many parameters, making the code more readable and maintainable.
"""
from typing import Any, Dict, Optional, TypeVar, Generic, Union

from src.infrastructure.protocols.logger import LoggerProtocol
from src.transformer.config.config import DataTransformerConfig
from src.services.transformer.data_transformer import DataTransformer
from src.transformer.factories.component_factory import ComponentFactory


T = TypeVar('T')


class ServiceBuilder(Generic[T]):
    """
    Base builder class for services.
    
    Provides a fluent interface for building services with many parameters.
    """
    
    def __init__(self, service_class: type[T]):
        self.service_class = service_class
        self._config: Dict[str, Any] = {}
        self._logger: Optional[LoggerProtocol] = None
        self._params: Dict[str, Any] = {}
    
    def with_config(self, config: Union[Dict[str, Any], Any]) -> 'ServiceBuilder[T]':
        """Set the configuration."""
        if hasattr(config, 'to_dict'):
            self._config = config.to_dict()
        else:
            self._config = config
        return self
    
    def with_logger(self, logger: LoggerProtocol) -> 'ServiceBuilder[T]':
        """Set the logger."""
        self._logger = logger
        return self
    
    def with_param(self, name: str, value: Any) -> 'ServiceBuilder[T]':
        """Set a single parameter."""
        self._params[name] = value
        return self
    
    def with_params(self, **params) -> 'ServiceBuilder[T]':
        """Set multiple parameters."""
        self._params.update(params)
        return self
    
    def build(self) -> T:
        """Build the service instance."""
        # Merge all parameters
        all_params = {
            'config': self._config,
            'logger': self._logger,
            **self._params
        }
        
        # Remove None values
        all_params = {k: v for k, v in all_params.items() if v is not None}
        
        return self.service_class(**all_params)


class DataTransformerBuilder(ServiceBuilder[DataTransformer]):
    """
    Builder for DataTransformer service.
    
    Example:
        transformer = (DataTransformerBuilder()
            .with_config(config)
            .with_logger(logger)
            .with_ai_services(openai_client, deepseek_service, mistral_service)
            .with_shutdown_event(shutdown_event)
            .with_storage(s3_manager)
            .with_repositories(pacer_repo, courts_repo)
            .with_processors(file_handler, docket_processor, mdl_processor)
            .with_html_integration(html_service)
            .enable_monitoring()
            .build())
    """
    
    def __init__(self):
        super().__init__(DataTransformer)
        self._monitoring_enabled = False
    
    def with_openai_client(self, client: Any) -> 'DataTransformerBuilder':
        """Set the OpenAI client."""
        return self.with_param('openai_client', client)
    
    def with_deepseek_service(self, service: Any) -> 'DataTransformerBuilder':
        """Set the DeepSeek service."""
        return self.with_param('deepseek_service', service)
    
    def with_mistral_service(self, service: Any) -> 'DataTransformerBuilder':
        """Set the Mistral service."""
        return self.with_param('mistral_service', service)
    
    def with_ai_services(self, openai_client: Any, deepseek_service: Any, mistral_service: Any) -> 'DataTransformerBuilder':
        """Set all AI services in one call."""
        return (self
            .with_param('openai_client', openai_client)
            .with_param('deepseek_service', deepseek_service)
            .with_param('mistral_service', mistral_service))
    
    def with_s3_manager(self, manager: Any) -> 'DataTransformerBuilder':
        """Set the S3 manager."""
        return self.with_param('s3_manager', manager)
    
    def with_storage(self, s3_manager: Any) -> 'DataTransformerBuilder':
        """Set storage services (alias for with_s3_manager)."""
        return self.with_s3_manager(s3_manager)
    
    def with_shutdown_event(self, event: Any) -> 'DataTransformerBuilder':
        """Set the shutdown event."""
        return self.with_param('shutdown_event', event)
    
    def with_repositories(self, pacer_repo: Any, courts_repo: Any) -> 'DataTransformerBuilder':
        """Set the repositories."""
        return (self
            .with_param('pacer_repository', pacer_repo)
            .with_param('district_courts_repository', courts_repo))
    
    def with_pacer_repository(self, repo: Any) -> 'DataTransformerBuilder':
        """Set the PACER repository."""
        return self.with_param('pacer_repository', repo)
    
    def with_district_courts_repository(self, repo: Any) -> 'DataTransformerBuilder':
        """Set the district courts repository."""
        return self.with_param('district_courts_repository', repo)
    
    def with_html_integration_service(self, service: Any) -> 'DataTransformerBuilder':
        """Set the HTML integration service."""
        return self.with_param('html_integration_service', service)
    
    def with_html_integration(self, service: Any) -> 'DataTransformerBuilder':
        """Set the HTML integration service (alias)."""
        return self.with_html_integration_service(service)
    
    def with_file_handler(self, handler: Any) -> 'DataTransformerBuilder':
        """Set the file handler."""
        return self.with_param('file_handler', handler)
    
    def with_docket_processor(self, processor: Any) -> 'DataTransformerBuilder':
        """Set the docket processor."""
        return self.with_param('docket_processor', processor)
    
    def with_mdl_processor(self, processor: Any) -> 'DataTransformerBuilder':
        """Set the MDL processor."""
        return self.with_param('mdl_processor', processor)
    
    def with_processors(self, file_handler: Any, docket_processor: Any, mdl_processor: Any) -> 'DataTransformerBuilder':
        """Set all processors in one call."""
        return (self
            .with_param('file_handler', file_handler)
            .with_param('docket_processor', docket_processor)
            .with_param('mdl_processor', mdl_processor))
    
    def with_dynamodb_client(self, client: Any) -> 'DataTransformerBuilder':
        """Set the DynamoDB client."""
        return self.with_param('dynamodb_client', client)
    
    def enable_monitoring(self, monitor: Optional[Any] = None) -> 'DataTransformerBuilder':
        """Enable performance monitoring."""
        self._monitoring_enabled = True
        if monitor:
            return self.with_param('performance_monitor', monitor)
        return self
    
    def with_processing_mode(self, mode: str) -> 'DataTransformerBuilder':
        """Set the processing mode."""
        return self.with_param('processing_mode', mode)
    
    def with_workers(self, num_workers: int) -> 'DataTransformerBuilder':
        """Set the number of workers."""
        if self._config:
            self._config['num_workers'] = num_workers
        else:
            self._config = {'num_workers': num_workers}
        return self
    
    def with_all_dependencies(self, 
                             config: Any,
                             logger: Any,
                             openai_client: Any,
                             deepseek_service: Any,
                             mistral_service: Any,
                             shutdown_event: Any,
                             s3_manager: Any = None,
                             pacer_repository: Any = None,
                             district_courts_repository: Any = None,
                             html_integration_service: Any = None,
                             file_handler: Any = None,
                             docket_processor: Any = None,
                             mdl_processor: Any = None,
                             law_firm_processing_service: Any = None,
                             case_classification_service: Any = None,
                             data_cleaning_service: Any = None,
                             data_upload_service: Any = None) -> 'DataTransformerBuilder':
        """Set all dependencies in a single call (for container integration)."""
        builder = (self
            .with_config(config)
            .with_logger(logger)
            .with_ai_services(openai_client, deepseek_service, mistral_service)
            .with_shutdown_event(shutdown_event))
        
        if s3_manager:
            builder = builder.with_s3_manager(s3_manager)
        if pacer_repository:
            builder = builder.with_pacer_repository(pacer_repository)
        if district_courts_repository:
            builder = builder.with_district_courts_repository(district_courts_repository)
        if html_integration_service:
            builder = builder.with_html_integration_service(html_integration_service)
        if file_handler:
            builder = builder.with_file_handler(file_handler)
        if docket_processor:
            builder = builder.with_docket_processor(docket_processor)
        if mdl_processor:
            builder = builder.with_mdl_processor(mdl_processor)
        if law_firm_processing_service:
            builder = builder.with_param('law_firm_processing_service', law_firm_processing_service)
        if case_classification_service:
            builder = builder.with_param('case_classification_service', case_classification_service)
        if data_cleaning_service:
            builder = builder.with_param('data_cleaning_service', data_cleaning_service)
        if data_upload_service:
            builder = builder.with_param('data_upload_service', data_upload_service)
            
        return builder
    
    def validate(self) -> Dict[str, Any]:
        """
        Validate the builder configuration before building.
        
        Returns:
            Validation result with errors and warnings
        """
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'missing_required': [],
            'missing_optional': []
        }
        
        # Check required parameters - config and logger are stored separately
        if not self._config:
            result['missing_required'].append('config')
            result['errors'].append("Required parameter 'config' is missing")
        
        if self._logger is None:
            result['missing_required'].append('logger')
            result['errors'].append("Required parameter 'logger' is missing")
        
        # Check other required parameters in _params
        required_params = ['openai_client', 'deepseek_service', 'mistral_service', 'shutdown_event']
        for param in required_params:
            if param not in self._params:
                result['missing_required'].append(param)
                result['errors'].append(f"Required parameter '{param}' is missing")
        
        # Optional but recommended parameters
        recommended_params = ['s3_manager', 'pacer_repository', 'district_courts_repository', 'file_handler']
        for param in recommended_params:
            if param not in self._params:
                result['missing_optional'].append(param)
                result['warnings'].append(f"Optional parameter '{param}' is missing")
        
        if result['errors']:
            result['is_valid'] = False
        
        return result
    
    def build(self) -> T:
        """Build the DataTransformer instance with validation."""
        # Validate before building
        validation = self.validate()
        if not validation['is_valid']:
            raise ValueError(f"Builder validation failed: {', '.join(validation['errors'])}")
        
        # Log warnings if any
        if validation['warnings'] and hasattr(self._logger, 'warning'):
            for warning in validation['warnings']:
                self._logger.warning(f"DataTransformerBuilder: {warning}")
        
        return super().build()


class ComponentFactoryBuilder(ServiceBuilder[ComponentFactory]):
    """
    Builder for ComponentFactory service.
    
    Example:
        factory = (ComponentFactoryBuilder()
            .with_config(config)
            .with_logger(logger)
            .with_llm_clients(openai, deepseek, mistral)
            .build())
    """
    
    def __init__(self):
        super().__init__(ComponentFactory)
    
    def with_openai_client(self, client: Any) -> 'ComponentFactoryBuilder':
        """Set the OpenAI client."""
        return self.with_param('openai_client', client)
    
    def with_deepseek_service(self, service: Any) -> 'ComponentFactoryBuilder':
        """Set the DeepSeek service."""
        return self.with_param('deepseek_service', service)
    
    def with_mistral_service(self, service: Any) -> 'ComponentFactoryBuilder':
        """Set the Mistral service."""
        return self.with_param('mistral_service', service)
    
    def with_llm_clients(self, openai: Any = None, deepseek: Any = None, 
                         mistral: Any = None) -> 'ComponentFactoryBuilder':
        """Set all LLM clients at once."""
        if openai:
            self.with_openai_client(openai)
        if deepseek:
            self.with_deepseek_service(deepseek)
        if mistral:
            self.with_mistral_service(mistral)
        return self


class ConfigBuilder:
    """
    Builder for DataTransformerConfig.
    
    Example:
        config = (ConfigBuilder()
            .with_project_root("/path/to/project")
            .with_bucket_name("my-bucket")
            .with_llm_provider("deepseek")
            .with_workers(8)
            .enable_law_firm_normalization()
            .build())
    """
    
    def __init__(self):
        self._params: Dict[str, Any] = {}
    
    def with_project_root(self, path: str) -> 'ConfigBuilder':
        """Set the project root path."""
        self._params['PROJECT_ROOT'] = path
        return self
    
    def with_bucket_name(self, name: str) -> 'ConfigBuilder':
        """Set the S3 bucket name."""
        self._params['BUCKET_NAME'] = name
        return self
    
    def with_llm_provider(self, provider: str) -> 'ConfigBuilder':
        """Set the LLM provider."""
        self._params['llm_provider'] = provider
        return self
    
    def with_workers(self, num_workers: int) -> 'ConfigBuilder':
        """Set the number of workers."""
        self._params['num_workers'] = num_workers
        return self
    
    def with_iso_date(self, date: str) -> 'ConfigBuilder':
        """Set the ISO date for processing."""
        self._params['iso_date'] = date
        return self
    
    def enable_law_firm_normalization(self) -> 'ConfigBuilder':
        """Enable law firm name normalization."""
        self._params['normalize_law_firm_names'] = True
        return self
    
    def enable_mdl_reprocessing(self) -> 'ConfigBuilder':
        """Enable MDL number reprocessing."""
        self._params['reprocess_mdl_num'] = True
        return self
    
    def with_mdl_lookup_path(self, path: str) -> 'ConfigBuilder':
        """Set the MDL lookup file path."""
        self._params['mdl_lookup'] = path
        return self
    
    def build(self) -> DataTransformerConfig:
        """Build the configuration."""
        return DataTransformerConfig(**self._params)


# Convenience functions
def build_transformer(**kwargs) -> DataTransformer:
    """
    Convenience function to build a DataTransformer.
    
    Args:
        **kwargs: Parameters for the transformer
        
    Returns:
        Configured DataTransformer instance
        
    Example:
        transformer = build_transformer(
            config=config,
            logger=logger,
            openai_client=openai_client,
            deepseek_service=deepseek_service,
            mistral_service=mistral_service,
            shutdown_event=shutdown_event,
            s3_manager=s3_manager,
            pacer_repository=pacer_repo,
            district_courts_repository=courts_repo,
            html_integration_service=html_service,
            file_handler=file_handler,
            docket_processor=docket_processor,
            mdl_processor=mdl_processor
        )
    """
    builder = DataTransformerBuilder()
    
    # Handle common parameters with dedicated methods
    if 'config' in kwargs:
        builder = builder.with_config(kwargs.pop('config'))
    if 'logger' in kwargs:
        builder = builder.with_logger(kwargs.pop('logger'))
    
    # Handle AI services as a group
    ai_services = {}
    for ai_param in ['openai_client', 'deepseek_service', 'mistral_service']:
        if ai_param in kwargs:
            ai_services[ai_param] = kwargs.pop(ai_param)
    
    if len(ai_services) == 3:
        builder = builder.with_ai_services(
            ai_services['openai_client'],
            ai_services['deepseek_service'], 
            ai_services['mistral_service']
        )
    else:
        # Add individually
        for param, value in ai_services.items():
            builder = builder.with_param(param, value)
    
    # Handle repositories as a group
    pacer_repo = kwargs.pop('pacer_repository', None)
    courts_repo = kwargs.pop('district_courts_repository', None)
    if pacer_repo and courts_repo:
        builder = builder.with_repositories(pacer_repo, courts_repo)
    else:
        if pacer_repo:
            builder = builder.with_pacer_repository(pacer_repo)
        if courts_repo:
            builder = builder.with_district_courts_repository(courts_repo)
    
    # Handle processors as a group
    file_handler = kwargs.pop('file_handler', None)
    docket_processor = kwargs.pop('docket_processor', None)
    mdl_processor = kwargs.pop('mdl_processor', None)
    if file_handler and docket_processor and mdl_processor:
        builder = builder.with_processors(file_handler, docket_processor, mdl_processor)
    else:
        if file_handler:
            builder = builder.with_file_handler(file_handler)
        if docket_processor:
            builder = builder.with_docket_processor(docket_processor)
        if mdl_processor:
            builder = builder.with_mdl_processor(mdl_processor)
    
    # Handle specialized services
    if 'html_integration_service' in kwargs:
        builder = builder.with_html_integration_service(kwargs.pop('html_integration_service'))
    if 's3_manager' in kwargs:
        builder = builder.with_s3_manager(kwargs.pop('s3_manager'))
    if 'shutdown_event' in kwargs:
        builder = builder.with_shutdown_event(kwargs.pop('shutdown_event'))
    
    # Handle remaining parameters
    for key, value in kwargs.items():
        builder = builder.with_param(key, value)
    
    return builder.build()


def build_component_factory(**kwargs) -> ComponentFactory:
    """
    Convenience function to build a ComponentFactory.
    
    Args:
        **kwargs: Parameters for the factory
        
    Returns:
        Configured ComponentFactory instance
    """
    builder = ComponentFactoryBuilder()
    
    # Handle common parameters
    if 'config' in kwargs:
        builder.with_config(kwargs.pop('config'))
    if 'logger' in kwargs:
        builder.with_logger(kwargs.pop('logger'))
    
    # Handle LLM clients
    if 'openai_client' in kwargs:
        builder.with_openai_client(kwargs.pop('openai_client'))
    if 'deepseek_service' in kwargs:
        builder.with_deepseek_service(kwargs.pop('deepseek_service'))
    if 'mistral_service' in kwargs:
        builder.with_mistral_service(kwargs.pop('mistral_service'))
    
    return builder.build()