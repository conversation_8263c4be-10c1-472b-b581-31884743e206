# /src/services/fb_ads/session_manager.py
import asyncio
import json
import logging
import os
import random
import re
import time
import uuid
from typing import Any, Dict, List, Optional

import requests
from botocore.httpsession import create_urllib3_context
from bs4 import BeautifulSoup
from requests.adapters import HTT<PERSON>dapter
from requests.exceptions import RequestException

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import FBAdServiceError
from .bandwidth_logger import BandwidthLogger, calculate_request_size, calculate_response_size
from .logging_setup import FBAdsLogger
from .base.session_manager_base import SessionManagerBase


class SSLAdapter(HTTPAdapter):
    """An HTTP adapter that allows specifying SSL/TLS cipher suites."""

    # --- MOVED Constants to Class Level ---
    CIPHERS_SET_1 = (
        'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:'
        'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:'
        'ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:'
        'DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384'
    )
    CIPHERS_SET_2 = (
        'ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384:'
        'ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256:'
        'AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA'  # Include some non-ECDHE for variety if needed
    )
    CIPHERS_SET_3 = (  # More focused on ChaCha20
        'ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:'
        'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:'
        'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384'
    )

    # --- End Class Level Constants ---

    # --- MODIFIED: Accept cipher string in constructor, default to SET_1 ---
    def __init__(self, *args, ciphers: str = CIPHERS_SET_1, **kwargs):
        # Store the specific cipher string for this instance
        self.ciphers_to_use = ciphers
        super().__init__(*args, **kwargs)

    def init_poolmanager(self, *args, **kwargs):
        # Use the cipher string stored in the instance
        context = create_urllib3_context(ciphers=self.ciphers_to_use)
        # Optionally set minimum TLS version:
        # import ssl
        # context.minimum_version = ssl.TLSVersion.TLSv1_2
        kwargs['ssl_context'] = context
        return super().init_poolmanager(*args, **kwargs)


class FacebookSessionManager(SessionManagerBase, AsyncServiceBase):
    """Manages the requests session, headers, proxies, and authentication tokens."""

    # No longer banning proxies, just rotating
    # --- MOVED Constant Here ---
    DEFAULT_SESSION_REFRESH_INTERVAL = 25  # Refresh session every N firms

    @staticmethod
    def _extract_session_id_for_log(proxy_url: Optional[str]) -> str:
        """Safely extracts 'sessid-...' part and host for logging."""
        if not proxy_url:
            return "None"
        # Extract sessid-1234567890
        sess_match = re.search(r'(sessid-\d+)', proxy_url)
        sess_id = sess_match.group(1) if sess_match else "UnknownSessID"
        # Extract @pr.oxylabs.io:7777
        host_match = re.search(r'@([\w.-]+:\d+)', proxy_url)
        host = host_match.group(1) if host_match else "UnknownHost"
        return f"{sess_id}@{host}"

    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        # Initialize AsyncServiceBase
        super().__init__(logger, config)

        # ... (rest of __init__ remains the same) ...
        self.reqs: Optional[requests.Session] = None
        self.data: Dict[str, Any] = {}  # Holds session tokens like dtsg, lsd, jazoest, etc.
        self.session_id: Optional[str] = None  # Unique ID for a scraping session attempt
        self.request_counter: int = 1

        # Initialize bandwidth logger with periodic logging disabled by default
        bandwidth_config = self.config.copy() if self.config else {}
        bandwidth_config['disable_bandwidth_periodic_logging'] = bandwidth_config.get(
            'disable_bandwidth_periodic_logging', True)
        self.bandwidth_logger = BandwidthLogger(bandwidth_config, self.logger)
        self.log_info("Bandwidth logger initialized for session manager with periodic logging disabled")

        # Proxy related
        self.mobile_proxy = self.config.get('mobile_proxy', False)
        self.use_proxy = self.config.get('use_proxy', True)  # Default to True
        self.testing = self.config.get('testing', False)
        self.render_html = self.config.get('render_html', False)  # Oxylabs render

        # Log proxy configuration
        self.log_info(f"Session manager initialized with use_proxy={self.use_proxy}, mobile_proxy={self.mobile_proxy}")

        # Generate proxy list
        if self.use_proxy and not self.testing:
            self.log_info(f"Generating proxy list for {'MOBILE' if self.mobile_proxy else 'RESIDENTIAL'} proxies")
            self.proxies = self._generate_proxy_list()
            if not self.proxies:
                self.log_error("Failed to generate proxy list! Check credentials and configuration.")
            else:
                self.log_info(f"Successfully generated {len(self.proxies)} proxies")
        else:
            self.proxies = []
            if not self.use_proxy:
                self.log_info("Proxy use is disabled in configuration.")
            if self.testing:
                self.log_info("Testing mode: Not generating proxy list.")
        self.current_proxy_index: int = 0
        self.current_proxy_url: Optional[str] = None  # Store the currently active proxy URL

        # --- Proxy Tracking ---
        # No longer tracking failures or banning proxies
        # Just rotating on failures

        # --- ADDED: TLS Rotation ---
        self.airplane_mode = self.config.get('airplane_mode', False)  # If using custom SSL adapter
        # Access class attribute correctly using SSLAdapter.CIPHERS_SET_1 etc.
        self.cipher_sets: List[str] = [SSLAdapter.CIPHERS_SET_1, SSLAdapter.CIPHERS_SET_2, SSLAdapter.CIPHERS_SET_3]
        self.current_cipher_set_index: int = 0

        if not self.testing:
            self._initialize_session()
        else:
            self.log_info("Testing mode: Skipping session initialization.")

    async def _execute_action(self, data: Any) -> Any:
        """Execute FacebookSessionManager actions."""
        if isinstance(data, dict):
            action = data.get('action')
            if action == 'create_new_session':
                return await self.create_new_session(
                    max_retries=data.get('max_retries', 5),
                    backoff_base=data.get('backoff_base', 1.5)
                )
            elif action == 'get_session':
                return self.get_session()
            elif action == 'get_session_data':
                return self.get_session_data()
            elif action == 'get_current_session_id':
                return self.get_current_session_id()
            elif action == 'increment_request_counter':
                self.increment_request_counter()
                return None
            elif action == 'get_request_counter':
                return self.get_request_counter()
            elif action == 'rotate_proxy':
                self.rotate_proxy()
                return None
            elif action == 'get_current_proxy':
                return self.get_current_proxy()
            elif action == 'record_proxy_failure':
                self.record_proxy_failure(data.get('proxy_url'))
                return None
            elif action == 'handle_block_or_rate_limit':
                self.handle_block_or_rate_limit(data.get('reason', 'block/rate limit'))
                return None
            elif action == 'update_ajax_headers':
                self.update_ajax_headers()
                return None
            elif action == 'get_current_proxy_settings':
                return self.get_current_proxy_settings()
            elif action == 'random_sleep':
                await self.random_sleep(
                    short=data.get('short', False),
                    long_pause=data.get('long_pause', False)
                )
                return None
        raise FBAdServiceError("Invalid action data provided to FacebookSessionManager")

    def _initialize_session(self):
        """Creates and configures the requests.Session object."""
        self.reqs = requests.Session()
        self.reqs.timeout = int(self.config.get('request_timeout', 30))
        self._set_randomized_headers()  # Call header setup first

        if self.airplane_mode:
            self.log_info("Airplane mode active - setting initial custom SSLAdapter.")
            # --- MODIFIED: Use initial cipher set ---
            initial_ciphers = self.cipher_sets[self.current_cipher_set_index]
            self.reqs.mount('https://', SSLAdapter(ciphers=initial_ciphers))
        elif self.use_proxy:
            if self.proxies:
                self.log_info(f"Setting up proxy from list of {len(self.proxies)} proxies")
                success = self._set_proxy()  # This will try to set the first valid proxy
                if success:
                    self.log_info(f"Successfully set proxy: {self.current_proxy_url.split('@')[-1]}")
                else:
                    self.log_error("Failed to set initial proxy. Will try again on first request.")
            else:
                self.log_error("Proxy use enabled, but no proxy list generated. Attempting to regenerate...")
                # Try to regenerate the proxy list
                self.proxies = self._generate_proxy_list()
                if self.proxies:
                    self.log_info(f"Successfully regenerated proxy list with {len(self.proxies)} proxies")
                    success = self._set_proxy()
                    if success:
                        self.log_info(f"Successfully set proxy: {self.current_proxy_url.split('@')[-1]}")
                    else:
                        self.log_error("Failed to set proxy after regenerating list")
                        self.reqs.proxies = {}
                        self.current_proxy_url = None
                else:
                    self.log_error("Failed to regenerate proxy list. Check credentials and configuration.")
                    # Don't disable proxy use here - we'll try again on first request
                    self.reqs.proxies = {}
                    self.current_proxy_url = None
        else:
            self.log_info("Running without proxy.")
            self.reqs.proxies = {}
            self.current_proxy_url = None

    def get_session(self) -> Optional[requests.Session]:
        """Returns the managed requests session."""
        if self.testing:
            # In testing, you might return a mock session or None
            # self.log_debug("Testing mode: Returning None for session.")
            return None
        if not self.reqs:
            self.log_warning("Session not initialized. Attempting initialization.")
            self._initialize_session()
        return self.reqs

    def get_session_data(self) -> Dict[str, Any]:
        """Returns the dictionary containing session tokens."""
        return self.data

    def get_current_session_id(self) -> Optional[str]:
        """Returns the UUID generated for the current session attempt."""
        return self.session_id

    def increment_request_counter(self):
        """Increments the internal request counter."""
        self.request_counter += 1

    def get_request_counter(self) -> int:
        """Gets the current request counter value."""
        return self.request_counter

    def _generate_proxy_list(self) -> List[str]:
        """
        Generates a list of Oxylabs proxy URLs based on config.
        Prioritizes specific keys (mobile/residential) before falling back to generic keys.
        """
        username = None
        password = None
        proxy_type_log = "UNKNOWN"  # For logging purposes

        # 1. Determine Proxy Type and Attempt to Load Specific Credentials
        if self.mobile_proxy:
            proxy_type_log = "MOBILE"
            specific_user_key = 'oxy_labs_mobile_username'
            specific_pass_key = 'oxy_labs_mobile_password'
            specific_user_env = 'OXY_LABS_MOBILE_USERNAME'
            specific_pass_env = 'OXY_LABS_MOBILE_PASSWORD'
        else:
            proxy_type_log = "RESIDENTIAL"
            specific_user_key = 'oxy_labs_residential_username'
            specific_pass_key = 'oxy_labs_residential_password'
            specific_user_env = 'OXY_LABS_RESIDENTIAL_USERNAME'
            specific_pass_env = 'OXY_LABS_RESIDENTIAL_PASSWORD'

        self.log_info(f"Attempting to load credentials for {proxy_type_log} proxy...")
        
        # Debug log to check what's in config
        self.log_debug(f"Config contains {specific_user_key}: {'yes' if specific_user_key in self.config else 'no'}")
        self.log_debug(f"Config contains {specific_pass_key}: {'yes' if specific_pass_key in self.config else 'no'}")
        
        if specific_pass_key in self.config:
            config_pass_value = self.config.get(specific_pass_key)
            if config_pass_value and isinstance(config_pass_value, str) and config_pass_value.startswith('${'):
                self.log_error(f"❌ CRITICAL: Config contains unexpanded env var: {config_pass_value}")
            elif config_pass_value:
                self.log_debug(f"✅ Config password appears expanded (length: {len(config_pass_value)})")

        # Try specific config keys first, then specific environment variables
        username = self.config.get(specific_user_key) or os.environ.get(specific_user_env)
        password = self.config.get(specific_pass_key) or os.environ.get(specific_pass_env)

        if username and password:
            self.log_info(f"Using specific {proxy_type_log} credentials (from config or env).")
        else:
            # 2. Fallback to Generic Credentials if Specific Ones Weren't Found
            self.log_info(
                f"Specific {proxy_type_log} credentials not found. Falling back to generic keys (oxy_labs_username/password)...")
            generic_user_key = 'oxy_labs_username'
            generic_pass_key = 'oxy_labs_password'
            # Note: No generic environment variables specified, adjust if needed

            username = self.config.get(generic_user_key)
            password = self.config.get(generic_pass_key)

            if username and password:
                self.log_info(f"Using generic credentials found in config for {proxy_type_log} proxy.")
            else:
                # 3. Error if No Credentials Found After Fallback
                self.log_error(
                    f"Oxylabs username or password missing for {proxy_type_log} proxy. Checked specific and generic config keys/env vars.")
                return []

        # Log the final selected username (avoid logging password)
        self.log_info(f"Using username: {username} for {proxy_type_log} proxy list generation.")

        # 4. Generate Proxy URLs
        base_url = 'pr.oxylabs.io:7777'
        # Default num_proxies if not in config
        num_proxies = int(self.config.get('oxylabs_num_proxies', 500))  # Made configurable, default 500

        proxy_list = []
        proxy_type_log = "MOBILE" if self.mobile_proxy else "RESIDENTIAL"
        self.log_debug(f"Generating {proxy_type_log} proxy list for user: {username}")

        # Use the configured proxy count from config/fb_ads.yml
        self.log_info(f"Using configured proxy count: {num_proxies} {proxy_type_log} proxies")

        # Generate unique random 10-digit session IDs
        generated_sessids = set()
        while len(generated_sessids) < num_proxies:
            # Generate a random 10-digit number as a string
            random_sessid = str(random.randint(1000000000, 9999999999))
            generated_sessids.add(random_sessid)
            # Add a small sleep to prevent potential rapid exhaustion of randomness source, though unlikely needed
            # await asyncio.sleep(0.0001) # Consider if truly needed

        # Create proxy URLs using the generated session IDs
        for sessid in generated_sessids:
            proxy_url = f"http://customer-{username}-cc-us-sessid-{sessid}-sesstime-10:{password}@{base_url}"
            proxy_list.append(proxy_url)

        # Log a sample proxy URL for debugging
        if proxy_list:
            self.log_debug(f"Sample {proxy_type_log} proxy format: {proxy_list[0].split('@')[0].split(':')[0]}@...")

        self.log_info(f"Generated {len(proxy_list)} {proxy_type_log} proxies with random session IDs.")

        # Randomize the generated list order
        random.shuffle(proxy_list)
        self.log_info(f"Generated and shuffled {len(proxy_list)} proxy URLs.")
        return proxy_list

    def _rotate_ssl_adapter(self):
        """Rotates to the next cipher set and updates the session's adapter."""
        if not self.airplane_mode or not self.reqs or not self.cipher_sets:
            return  # Only rotate if in airplane mode and session exists

        self.current_cipher_set_index = (self.current_cipher_set_index + 1) % len(self.cipher_sets)
        new_ciphers = self.cipher_sets[self.current_cipher_set_index]
        new_adapter = SSLAdapter(ciphers=new_ciphers)
        self.reqs.mount('https://', new_adapter)
        self.log_info(f"Rotated TLS cipher set to index {self.current_cipher_set_index}.")
        # Log only a part of the cipher string for brevity if needed
        # self.log_debug(f"New ciphers: {new_ciphers[:50]}...")

    def get_current_proxy(self) -> Optional[str]:
        """Returns the URL of the currently active proxy, if any."""
        return self.current_proxy_url

    def _set_randomized_headers(self):
        """Sets realistic, randomized headers for the session based on a chosen profile."""
        if not self.reqs: return

        # --- MODIFIED: Generate a consistent profile first ---
        # Choose OS Profile
        os_choices = [
            ('Windows', ['10.0', '11.0'], ['Win64; x64', 'Win32; x86'], 'en-US'),
            ('Macintosh', ['10_15_7', '11_7_10', '12_6_5', '13_4_1', '14_1_1'], ['Intel Mac OS X'], 'en-US'),
            # Use Macintosh for UA platform string
            ('X11', ['Linux x86_64'], ['Linux x86_64'], 'en-GB'),  # Common Linux UA platform
        ]
        os_name_platform, os_versions, architectures, language = random.choice(os_choices)
        os_version = random.choice(os_versions)
        architecture = random.choice(architectures)

        # Generate platform string for UA
        if os_name_platform == 'Macintosh':
            ua_os_part = f'{architecture} {os_version}'
        elif os_name_platform == 'Windows':
            ua_os_part = f'Windows NT {os_version}; {architecture}'
        else:  # Linux
            ua_os_part = architecture  # e.g., Linux x86_64

        # Choose Browser Version (Chrome) - FIXED: Use realistic versions
        chrome_major = random.randint(115, 124)  # Recent Chrome versions
        chrome_build = f"{chrome_major}.0.{random.randint(5000, 6500)}.{random.randint(100, 199)}"
        # FIXED: Chrome always uses WebKit 537.36, not random versions
        webkit_ver = "537.36"  # Standard WebKit version for all Chrome browsers
        safari_ver = webkit_ver

        # Generate User Agent
        user_agent = (
            f'Mozilla/5.0 ({ua_os_part}) '
            f'AppleWebKit/{webkit_ver} (KHTML, like Gecko) '
            f'Chrome/{chrome_build} Safari/{safari_ver}'
        )

        # Generate Sec-CH-UA values based on chosen version
        brands = [
            {"brand": "Not/A)Brand", "version": "8"},
            {"brand": "Chromium", "version": str(chrome_major)},
            {"brand": "Google Chrome", "version": str(chrome_major)}
        ]
        random.shuffle(brands)  # Randomize order slightly
        sec_ch_ua = ", ".join([f'"{b["brand"]}";v="{b["version"]}"' for b in brands])
        sec_ch_ua_platform = f'"{os_name_platform}"'  # Use the UA platform string name

        # Use a common viewport
        viewport = random.choice(["1920x1080", "1536x864", "1440x900", "1366x768"])
        viewport_width = viewport.split('x')[0]

        # Assemble Headers
        headers = {
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': f"{language},{language.split('-')[0]};q=0.9,en;q=0.8",  # More realistic lang string
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'DNT': '1',  # Do Not Track often set
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Sec-Ch-Ua': sec_ch_ua,
            'Sec-Ch-Ua-Mobile': '?0',  # Assume desktop
            'Sec-Ch-Ua-Platform': sec_ch_ua_platform,
            'Viewport-Width': viewport_width,
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
        }
        # --- End Profile/Header Consistency ---

        self.reqs.headers.clear()
        self.reqs.headers.update(headers)
        self.log_debug(f"Set randomized headers based on profile ({os_name_platform}). UA: ...{user_agent[-50:]}")

    def update_ajax_headers(self):
        """Updates session headers specifically for AJAX POST requests."""
        if not self.reqs: return
        # Use the *current* randomized headers as a base
        ajax_headers = self.reqs.headers.copy()

        # Update/Add headers specific to AJAX calls
        ajax_headers.update({
            'Accept': '*/*',  # Common for AJAX
            'Content-Type': 'application/x-www-form-urlencoded',
            'Referer': 'https://www.facebook.com/ads/library/',  # Keep consistent referer
            'Origin': 'https://www.facebook.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',  # Important for cross-origin resource sharing behavior
            'Sec-Fetch-Site': 'same-origin',  # Request originates from the same site
            'X-Requested-With': 'XMLHttpRequest',  # Classic AJAX indicator
            'X-FB-Friendly-Name': 'AdsLibraryDataTableQuery',  # Example, adjust if needed for specific calls
        })
        # Remove headers less common/needed for AJAX
        ajax_headers.pop('Upgrade-Insecure-Requests', None)
        ajax_headers.pop('Sec-Fetch-User', None)

        # DO NOT clear and update; just update the existing session headers
        # This preserves potentially important base headers like User-Agent, Sec-CH-UA etc.
        # self.reqs.headers.clear() # <-- REMOVE THIS
        self.reqs.headers.update(ajax_headers)  # Update existing header dict
        self.log_debug("Updated/Added AJAX specific headers.")

    def _set_proxy(self, rotation_attempt=0) -> bool:
        """Sets the current proxy. Returns True if successful."""
        max_rotation_attempts = len(self.proxies) + 1  # Avoid infinite loop

        if not self.use_proxy or not self.proxies or not self.reqs:
            self.log_warning(
                f"Cannot set proxy: use_proxy={self.use_proxy}, proxies={len(self.proxies) if self.proxies else 0}, reqs={self.reqs is not None}")
            if self.reqs: self.reqs.proxies = {}
            self.current_proxy_url = None
            return False
        if rotation_attempt >= max_rotation_attempts:
            self.log_error(f"All proxies seem to be unavailable after {rotation_attempt} attempts. Cannot set proxy.")
            self.reqs.proxies = {}
            self.current_proxy_url = None
            return False

        try:
            proxy_url = self.proxies[self.current_proxy_index]

            # No longer checking for banned proxies - we're just rotating
            log_safe_proxy_info = self._extract_session_id_for_log(proxy_url)
            self.log_info(
                f'Setting proxy index {self.current_proxy_index}: {log_safe_proxy_info}'  # Log sessid@host
            )
            self.reqs.proxies = {"http": proxy_url, "https": proxy_url}
            self.current_proxy_url = proxy_url  # Store the active proxy URL

            # Handle Oxylabs rendering header
            if self.render_html:
                self.reqs.headers["X-Oxylabs-Render"] = "html"
            elif "X-Oxylabs-Render" in self.reqs.headers:
                del self.reqs.headers["X-Oxylabs-Render"]

            self.reqs.auth = None  # Ensure no basic auth set directly
            self._log_current_proxy_ip()
            return True  # Successfully set proxy

        except IndexError:
            self.log_error(f"Proxy index {self.current_proxy_index} out of bounds. Resetting to 0.")
            self.current_proxy_index = 0
            if self.proxies:
                return self._set_proxy(rotation_attempt + 1)  # Retry with index 0
            else:
                self.use_proxy = False
                self.reqs.proxies = {}
                self.current_proxy_url = None
                return False
        except Exception as e:
            self.log_error(f"Unexpected error setting proxy: {e}")
            self.use_proxy = False
            self.reqs.proxies = {}
            self.current_proxy_url = None
            return False

    def rotate_proxy(self):
        """Rotates to the next available (non-banned) proxy in the list."""
        if not self.use_proxy or not self.proxies:
            self.log_debug("Proxy rotation requested but not applicable.")
            return

        initial_index = self.current_proxy_index
        num_proxies = len(self.proxies)
        if num_proxies == 0: return  # Nothing to rotate

        self.log_info(f'Attempting to rotate proxy (current index: {initial_index}, total proxies: {num_proxies})...')
        # Loop to find the next valid proxy
        for i in range(num_proxies):
            next_index = (initial_index + 1 + i) % num_proxies
            next_proxy_url = self.proxies[next_index]
            log_safe_next_proxy = self._extract_session_id_for_log(next_proxy_url)

            # Always use the next proxy
            self.log_debug(f"Trying proxy at index {next_index}: {log_safe_next_proxy}")

            # Found a usable proxy
            self.current_proxy_index = next_index
            if self._set_proxy():  # Attempt to set the found proxy (logs sessid internally)
                self.log_info(f"Successfully rotated proxy to index {self.current_proxy_index}.")
                # --- ADDED: Rotate TLS adapter when proxy rotates ---
                self._rotate_ssl_adapter()
                time.sleep(random.uniform(1, 3))  # Small delay after successful rotation
                return
            else:
                # _set_proxy failed for some reason
                self.log_warning(f"Failed to set proxy at index {self.current_proxy_index}. Trying next.")
                # Continue loop to find another proxy

        # If loop completes without finding a usable proxy
        self.log_error(f"Proxy rotation failed: Could not find any usable proxy after trying {num_proxies} proxies.")
        # Reset proxy settings if none are usable
        if self.reqs:
            self.reqs.proxies = {}
            self.log_warning("Cleared proxy settings as no usable proxies were found")
        self.current_proxy_url = None

    def record_proxy_failure(self, proxy_url: Optional[str]):
        """Records a failure for the given proxy URL and rotates to the next proxy."""
        if not self.use_proxy:
            self.log_warning("Proxy failure reported but proxy use is disabled")
            return  # Ignore if proxy use is disabled

        # Check if we need to regenerate the proxy list
        if not self.proxies:
            self.log_warning("No proxies available. Attempting to regenerate proxy list.")
            self.proxies = self._generate_proxy_list()
            if self.proxies:
                self.log_info(f"Successfully regenerated proxy list with {len(self.proxies)} proxies.")
            else:
                self.log_error("Failed to regenerate proxy list. Check credentials and configuration.")
                return

        if not proxy_url:
            self.log_warning("Proxy failure reported but proxy_url is None. Attempting to rotate anyway.")
            self.rotate_proxy()
            return

        log_safe_failed_proxy = self._extract_session_id_for_log(proxy_url)
        self.log_info(f"Proxy failure detected for {log_safe_failed_proxy}. Rotating to next proxy.")
        # Always rotate on failure
        if proxy_url == self.current_proxy_url:
            self.rotate_proxy()
        else:
            log_safe_current_proxy = self._extract_session_id_for_log(self.current_proxy_url)
            self.log_warning(
                f"Failed proxy {log_safe_failed_proxy} is not the current proxy. Current: {log_safe_current_proxy}")
            # Rotate anyway since we had a failure
            self.rotate_proxy()

    def handle_block_or_rate_limit(self, reason: str = "block/rate limit"):
        """Handles IP blocks or rate limits, typically by rotating proxy or waiting."""
        self.log_warning(f'{reason.capitalize()} detected.')

        # Check if we need to regenerate the proxy list
        if self.use_proxy and not self.proxies:
            self.log_warning("No proxies available. Attempting to regenerate proxy list.")
            self.proxies = self._generate_proxy_list()
            if self.proxies:
                self.log_info(f"Successfully regenerated proxy list with {len(self.proxies)} proxies.")
            else:
                self.log_error("Failed to regenerate proxy list. Check credentials and configuration.")
                return  # Abort if regeneration fails

        # Proceed even if regeneration wasn't needed or succeeded
        failed_proxy = self.get_current_proxy()  # Get the proxy that likely caused the block
        log_safe_failed_proxy = self._extract_session_id_for_log(failed_proxy)
        self.log_info(f"Current proxy when block detected: {log_safe_failed_proxy}")

        if self.use_proxy and self.proxies:
            # Always rotate on failure
            if failed_proxy:
                self.log_info(f"Rotating away from blocked proxy: {log_safe_failed_proxy}")
                self.record_proxy_failure(failed_proxy)  # Record failure and rotate
            else:
                # If current proxy isn't set but we got blocked, still try rotating
                self.log_warning("No current proxy set but block detected. Attempting to set a proxy.")
                self.rotate_proxy()

            # Verify we have a proxy after rotation
            new_proxy = self.get_current_proxy()
            if new_proxy:
                # Use the helper function for safe logging
                log_safe_new_proxy = self._extract_session_id_for_log(new_proxy)
                self.log_info(f"New proxy after rotation: {log_safe_new_proxy}")
            else:
                self.log_error("Failed to set a new proxy after rotation.")
        else:
            # Wait if not using proxies or if proxies are exhausted/unavailable
            wait_time = random.uniform(30, 90)
            self.log_info(f"Not using proxy or proxy rotation failed. Waiting {wait_time:.2f} seconds.")
            time.sleep(wait_time)  # Keep time.sleep here as it's outside async context potentially

    def _log_current_proxy_ip(self):
        """Logs the IP address of the current proxy (best effort)."""
        if not self.use_proxy or not self.reqs or not self.reqs.proxies:
            # self.log_debug("Not using proxy or session not configured for IP check.")
            return

        ip_check_services = ['https://httpbin.org/ip', 'https://api.ipify.org?format=json', 'https://ipinfo.io/ip']
        # Use the main session timeout for checking IP
        check_timeout = getattr(self.reqs, 'timeout', 15)  # Use slightly shorter timeout?

        for service_url in ip_check_services:
            try:
                # Use the main session directly - it has the correct proxy & headers
                response = self.reqs.get(service_url, timeout=check_timeout)
                response.raise_for_status()

                ip_info = None
                content_type = response.headers.get('content-type', '').lower()
                if 'json' in content_type:
                    data = response.json()
                    ip_info = data.get("origin") or data.get("ip")
                elif 'text' in content_type:
                    ip_info = response.text.strip()

                if ip_info:
                    self.log_info(f'Current proxy reports IP: {ip_info}')
                    return
            except Exception as e:
                self.log_debug(f"Proxy IP check failed ({service_url}): {e}")
                # --- ADDED: Record proxy failure on IP check fail ---
                self.record_proxy_failure(self.get_current_proxy())

        self.log_warning("Failed to retrieve proxy IP from all check services.")

    def get_current_proxy_settings(self) -> Optional[Dict[str, str]]:
        """
        Returns the current proxy settings dictionary suitable for requests,
        including authentication, based on the current proxy index.
        """
        # Use the instance attributes for proxy state
        if not self.use_proxy or not self.proxies or not self.current_proxy_url:  # Check current_proxy_url
            self.log_debug("get_current_proxy_settings: Proxy disabled or no proxy currently set.")
            return None

        current_proxy = self.current_proxy_url  # Use the stored active proxy

        # --- Format Check (Keep as is) ---
        if "@" not in current_proxy and self.config.get('oxy_labs_username'):
            self.log_warning(
                f"Proxy string '{current_proxy}' seems to be missing credentials. Attempting to add from config.")
            username = self.config.get('oxy_labs_username')
            password = self.config.get('oxy_labs_password')
            host_port_match = re.search(r"([\w.-]+:\d+)$", current_proxy)
            if host_port_match and username and password:
                host_port = host_port_match.group(1)
                current_proxy = f"http://{username}:{password}@{host_port}"
            else:
                self.log_error(f"Could not reconstruct proxy '{current_proxy}' with credentials. Format error.")
                return None
        if "://" not in current_proxy:
            scheme = "https" if ":443" in current_proxy else "http"
            self.log_warning(f"Proxy string '{current_proxy}' is missing scheme. Assuming {scheme}://")
            current_proxy = f"{scheme}://{current_proxy}"
        elif not current_proxy.startswith("http://") and not current_proxy.startswith("https://"):
            self.log_error(f"Proxy string '{current_proxy}' has unknown scheme or format. Cannot use.")
            return None
        # --- End Format Check ---

        proxy_dict = {
            "http": current_proxy,
            "https": current_proxy,
        }
        self.log_debug(f"get_current_proxy_settings: Returning proxy dict for {self.current_proxy_url.split('@')[-1]}")
        return proxy_dict

    async def create_new_session(self, max_retries=5, backoff_base=1.5) -> bool:
        """Attempts to establish a new session and extract essential tokens."""
        # 🐛 DEBUG: Enhanced logging for session creation troubleshooting
        self.log_info(f"🔄 STARTING session creation - testing mode: {self.testing}")
        
        if self.testing:
            self.log_info("⚠️  Testing mode: Simulating session creation.")
            self.data = {'fb_dtsg': 'dummy_dtsg', 'lsd': 'dummy_lsd', 'jazoest': 'dummy_jazoest'}
            self.session_id = str(uuid.uuid4())
            self.reqs = None  # Ensure reqs is None or a mock in testing
            self.log_info("✅ Testing mode session created successfully")
            return True

        # Store existing cookies before potentially recreating session
        existing_cookies = None
        if self.reqs and hasattr(self.reqs, 'cookies'):
            existing_cookies = self.reqs.cookies.copy()
            cookie_names = list(existing_cookies.keys())
            self.log_info(f"🍪 Preserving {len(existing_cookies)} existing cookies: {cookie_names}")
        
        if not self.reqs:
            self.log_info("📋 Session object not found, initializing new one.")
            self._initialize_session()
            if not self.reqs:  # If initialization failed
                self.log_error("❌ Failed to initialize session object.")
                return False
                
            # Restore preserved cookies to new session
            if existing_cookies:
                self.reqs.cookies.update(existing_cookies)
                self.log_info(f"🍪 Restored {len(existing_cookies)} cookies to new session")

        # 🐛 DEBUG: Log current session and proxy state
        current_proxy = self.get_current_proxy()
        proxy_log = self._extract_session_id_for_log(current_proxy) if current_proxy else "No proxy"
        self.log_info(f"🌐 Current proxy state: {proxy_log}")
        self.log_info(f"🔧 Proxy configuration: use_proxy={self.use_proxy}, testing={self.testing}")
        
        self.log_info(f"🎯 Attempting to establish new session and extract tokens (Max Retries: {max_retries})...")
        self.session_id = str(uuid.uuid4())  # New UUID for this attempt sequence
        self.request_counter = random.randint(1, 5)  # Reset counter
        
        self.log_info(f"🆔 Generated session ID: ...{self.session_id[-8:]}")

        # --- FIXED: Set headers/TLS ONCE per session, not per retry ---
        self._set_randomized_headers()  # Consistent browser fingerprint for entire session
        if self.airplane_mode:
            self._rotate_ssl_adapter()  # Set TLS once per session in airplane mode
        
        # Log header consistency for debugging
        current_ua = self.reqs.headers.get('User-Agent', 'Unknown')
        self.log_info(f"🔒 Headers set for session: ...{current_ua[-50:]}")
        # --- End Session Setup ---

        for attempt in range(max_retries):
            self.log_info(f"Session attempt {attempt + 1}/{max_retries}...")
            self.data = {}  # Reset data for each attempt
            # Headers and SSL adapter now remain consistent across retries

            try:
                ads_lib_url = 'https://www.facebook.com/ads/library/'
                # Use await for potential async operations within requests or adapters in the future
                # For standard requests, this doesn't change behavior but prepares for async compatibility
                # Calculate request size before making the request
                request_size = calculate_request_size(self.reqs.headers)

                response = await asyncio.to_thread(
                    self.reqs.get,
                    ads_lib_url,
                    # headers are now set directly on self.reqs by _set_randomized_headers
                    timeout=self.reqs.timeout,
                    allow_redirects=True
                )
                response.raise_for_status()
                html = response.text
                if not html: raise ValueError("Received empty HTML from Ads Library")

                # Calculate response size and log bandwidth usage
                response_size = calculate_response_size(response)
                content_type = response.headers.get('content-type', '')
                self.bandwidth_logger.log_request(
                    url=ads_lib_url,
                    request_size=request_size,
                    response_size=response_size,
                    content_type=content_type
                )
                self.log_debug(f"Logged bandwidth for session creation: {response_size} bytes downloaded")

                self._update_cookies(response)
                self._extract_tokens_from_html(html)

                if self.data.get('fb_dtsg') and self.data.get('lsd') and self.data.get('jazoest'):
                    self.log_info(f"Session essentials acquired on attempt {attempt + 1}.")
                    self.log_debug(
                        f"Tokens: dtsg=...{self.data['fb_dtsg'][-6:]}, lsd={self.data['lsd']}, jazoest={self.data['jazoest']}, session_id=...{self.session_id[-6:]}")
                    self._populate_common_data_fields(html)
                    self.update_ajax_headers()
                    # --- ADDED: Reset proxy failures on successful session ---
                    self.proxy_failure_counts = {}
                    self.banned_proxies = {}  # Clear bans as well? Or let them expire naturally? Let them expire for now.
                    self.log_info("Reset proxy failure counts on successful session establishment.")
                    # --- End Reset ---
                    return True
                else:
                    missing = [t for t in ['fb_dtsg', 'lsd', 'jazoest'] if not self.data.get(t)]
                    self.log_warning(
                        f"Failed to acquire session essentials ({', '.join(missing)}) on attempt {attempt + 1}.")
                    # --- ADDED: Record proxy failure on token extraction fail ---
                    self.record_proxy_failure(self.get_current_proxy())

            except RequestException as e:
                self.log_error(f"Network error during session creation (Attempt {attempt + 1}): {e}")
                # --- ADDED: Record proxy failure on network error ---
                self.record_proxy_failure(self.get_current_proxy())
                if isinstance(e,
                              requests.exceptions.HTTPError) and e.response is not None and e.response.status_code in [
                    403, 429, 503]:
                    self.handle_block_or_rate_limit("Session creation block")  # This already rotates/waits
                else:
                    if self.use_proxy: self.rotate_proxy()  # Rotate on other network errors too
            except ValueError as e:
                self.log_error(f"Data error during session creation (Attempt {attempt + 1}): {e}")
                # --- ADDED: Record proxy failure on data error ---
                self.record_proxy_failure(self.get_current_proxy())
            except Exception as e:
                self.log_error(f"Unexpected error during session creation (Attempt {attempt + 1}): {e}",
                               exc_info=self.config.get('verbose', False))
                # --- ADDED: Record proxy failure on unexpected error ---
                self.record_proxy_failure(self.get_current_proxy())

            if attempt < max_retries - 1:
                wait_time = (backoff_base ** attempt) + random.uniform(0.5, 2)
                self.log_info(f"Waiting {wait_time:.2f} seconds before session retry...")
                # Use await for asyncio.sleep
                await asyncio.sleep(wait_time)
                # Rotate proxy *before* next session attempt
                if self.use_proxy: self.rotate_proxy()  # rotate_proxy now handles banned check

        self.log_error("Failed to establish session after multiple attempts.")
        self.data = {}
        self.session_id = None
        return False

    def _extract_tokens_from_html(self, html: str):
        """Extracts dtsg, lsd, jazoest, spin tokens, etc., from HTML."""
        # 🐛 DEBUG: Enhanced logging for token extraction troubleshooting
        self.log_info("🔍 STARTING token extraction from HTML")
        self.log_info(f"📄 HTML content length: {len(html) if html else 0} chars")
        
        if not html:
            self.log_error("❌ HTML content is empty - cannot extract tokens")
            return
            
        if not isinstance(self.data, dict): self.data = {}
        found_dtsg, found_lsd, found_jazoest = False, False, False

        # 🐛 DEBUG: Log HTML preview to verify content structure
        html_preview = html[:500] if html else "None"
        self.log_info(f"📋 HTML preview (first 500 chars): {html_preview}")

        # 1. Hidden Inputs (Most Reliable)
        self.log_info("🎯 Attempting token extraction from hidden input tags")
        try:
            soup = BeautifulSoup(html, 'html.parser')
            hidden_inputs = soup.find_all('input', {'type': 'hidden'})
            self.log_info(f"🔢 Found {len(hidden_inputs)} hidden input tags")
            
            for input_tag in hidden_inputs:
                name = input_tag.get('name')
                value = input_tag.get('value')
                if not value: continue
                
                # 🐛 DEBUG: Log each token as it's found
                if name == 'fb_dtsg' and not found_dtsg:
                    self.data['fb_dtsg'] = value; found_dtsg = True
                    self.log_info(f"✅ Found fb_dtsg token: {value[:20]}...")
                elif name == 'lsd' and not found_lsd:
                    self.data['lsd'] = value; found_lsd = True
                    self.log_info(f"✅ Found lsd token: {value[:20]}...")
                elif name == 'jazoest' and not found_jazoest:
                    self.data['jazoest'] = value; found_jazoest = True
                    self.log_info(f"✅ Found jazoest token: {value[:20]}...")
                elif name == '__spin_r':
                    self.data['__spin_r'] = value
                    self.log_info(f"✅ Found __spin_r token: {value[:20]}...")
                elif name == '__spin_b':
                    self.data['__spin_b'] = value
                    self.log_info(f"✅ Found __spin_b token: {value[:20]}...")
                elif name == '__spin_t':
                    self.data['__spin_t'] = value
                    self.log_info(f"✅ Found __spin_t token: {value[:20]}...")
        except Exception as e:
            self.log_error(f"❌ Error parsing input tags: {e}")
            
        # 🐛 DEBUG: Log extraction results so far
        self.log_info(f"📊 Hidden input extraction results - dtsg: {'✅' if found_dtsg else '❌'}, lsd: {'✅' if found_lsd else '❌'}, jazoest: {'✅' if found_jazoest else '❌'}")

        # 2. Regex Fallbacks (More Fragile)
        self.log_info("🔄 Attempting regex fallback for missing tokens")
        
        if not found_dtsg:
            self.log_info("🎯 Using regex to find fb_dtsg token")
            dtsg_match = re.search(r'["\']fb_dtsg["\']\s*:\s*["\']([^"\']+)["\']', html) or \
                         re.search(r'DTSGInitialData["\'].*?token["\']\s*:\s*["\']([^"\']+)["\']', html)
            if dtsg_match: 
                self.data['fb_dtsg'] = dtsg_match.group(1); found_dtsg = True
                self.log_info(f"✅ Found fb_dtsg via regex: {self.data['fb_dtsg'][:20]}...")
            else:
                self.log_warning("❌ Failed to find fb_dtsg via regex")

        if not found_lsd:
            self.log_info("🎯 Using regex to find lsd token")
            lsd_match = re.search(r'["\']LSD["\'].*?token["\']\s*:\s*["\']([^"\']+)["\']', html) or \
                        re.search(r'["\']LsdToken["\']\s*,\s*\[\s*\]\s*,\s*["\']([^"\']+)["\']', html)
            if lsd_match: 
                self.data['lsd'] = lsd_match.group(1); found_lsd = True
                self.log_info(f"✅ Found lsd via regex: {self.data['lsd'][:20]}...")
            else:
                self.log_warning("❌ Failed to find lsd via regex")

        # 3. SiteData JSON for Spin Tokens (if not found in inputs)
        # Using safer regex and json parsing
        if '__spin_r' not in self.data:
            raw_json_str = ""  # Initialize for potential error logging
            try:
                # Look for a pattern that reliably captures the JSON block for SiteData
                site_data_match = re.search(
                    r'(?=["\']SiteData["\']\s*,).*{.*}(?=,\s*\[\s*\]\s*,?\s*["\']ServerJS["\'])', html)
                if site_data_match:
                    # Extract the JSON-like string - needs cleaning
                    raw_json_str = site_data_match.group(0)
                    # Minimal cleaning: Find first '{' and last '}'
                    start_brace = raw_json_str.find('{')
                    end_brace = raw_json_str.rfind('}')
                    if start_brace != -1 and end_brace != -1:
                        json_str_cleaned = raw_json_str[start_brace:end_brace + 1]
                        # Attempt to parse the cleaned string
                        site_data = json.loads(json_str_cleaned)
                        self.data['__spin_r'] = site_data.get("__spin_r", self.data.get('__spin_r',
                                                                                        ''))  # Use existing if found earlier
                        self.data['__spin_b'] = site_data.get("__spin_b", self.data.get('__spin_b', ''))
                        self.data['__spin_t'] = site_data.get("__spin_t", self.data.get('__spin_t', ''))
                        self.data['__hsi'] = site_data.get("hsi", self.data.get('__hsi', ''))
                        self.data.setdefault('__rev', self.data.get('__spin_r'))  # Default __rev based on __spin_r
            except json.JSONDecodeError as json_e:
                self.log_warning(f"Failed to parse SiteData JSON: {json_e}. String sample: {raw_json_str[:200]}...")
            except Exception as e:
                self.log_warning(f"Failed during SiteData processing: {e}")

        # 4. Generate Jazoest if needed
        if found_dtsg and not found_jazoest:
            self.log_info("🔧 Generating jazoest token from fb_dtsg")
            self.data['jazoest'] = self._generate_jazoest(self.data['fb_dtsg'])
            if self.data['jazoest']: 
                found_jazoest = True
                self.log_info(f"✅ Generated jazoest token: {self.data['jazoest'][:20]}...")
            else:
                self.log_warning("❌ Failed to generate jazoest token")

        # Set defaults for spin tokens if still missing after all attempts
        self.log_info("🔧 Setting default values for missing tokens")
        self.data.setdefault('__spin_r', '')
        self.data.setdefault('__spin_b', '')
        self.data.setdefault('__spin_t', '')
        self.data.setdefault('__rev', self.data.get('__spin_r', ''))
        self.data.setdefault('__hsi', '')

        # 🐛 DEBUG: Final comprehensive token validation
        required_tokens = ['fb_dtsg', 'lsd', 'jazoest']
        missing_tokens = [token for token in required_tokens if not self.data.get(token)]
        
        if missing_tokens:
            self.log_error(f"❌ CRITICAL: Missing required tokens: {missing_tokens}")
            self.log_error("💡 This will cause API requests to fail")
        else:
            self.log_info("✅ All required tokens successfully extracted")
            
        # 🐛 DEBUG: Log final token summary
        token_summary = {
            'fb_dtsg': f"{self.data.get('fb_dtsg', 'MISSING')[:20]}..." if self.data.get('fb_dtsg') else 'MISSING',
            'lsd': f"{self.data.get('lsd', 'MISSING')[:20]}..." if self.data.get('lsd') else 'MISSING',
            'jazoest': f"{self.data.get('jazoest', 'MISSING')[:20]}..." if self.data.get('jazoest') else 'MISSING',
        }
        self.log_info(f"📋 Final token summary: {token_summary}")
        
        self.log_debug(
            f"Token extraction: dtsg={'OK' if found_dtsg else 'Fail'}, lsd={'OK' if found_lsd else 'Fail'}, jazoest={'OK' if found_jazoest else 'Fail'}")

    def _populate_common_data_fields(self, html: str):
        """Populates common __<xyz> fields in self.data after successful session setup."""
        if not isinstance(self.data, dict): self.data = {}
        self.data.setdefault('__user', '0')
        self.data.setdefault('__a', '1')
        self.data.setdefault('dpr', str(random.choice([1, 1.5, 2])))  # Randomize DPR
        self.data.setdefault('__comet_req', str(random.choice([0, 1, 15])))  # Values seen
        self.data.setdefault('locale', 'en_US')
        self.data.setdefault('__csr', '')

        # Try to extract dynamic fields if present
        # Use safer extraction with checks
        def extract_field(pattern, default=''):
            match = re.search(pattern, html)
            return match.group(1) if match else default

        self.data['__dyn'] = extract_field(r'["\']__dyn["\']\s*:\s*["\']([^"\']+)["\']')
        self.data['__s'] = extract_field(r'["\']__s["\']\s*:\s*["\']([^"\']+)["\']')
        self.data['__csr'] = extract_field(r'["\']__csr["\']\s*:\s*["\']([^"\']+)["\']',
                                           self.data['__csr'])  # Keep existing if not found
        self.data['__ccg'] = extract_field(r'["\']connectionClass["\']:\s*"([^"]+)"', 'EXCELLENT')
        self.data.setdefault('__hs', self.data.get('__spin_r', ''))

        self.data['__req'] = str(self.request_counter)  # Ensure __req uses current counter

    def _generate_jazoest(self, fb_dtsg: Optional[str]) -> str:
        """Calculates the 'jazoest' value from 'fb_dtsg'."""
        if not fb_dtsg or not isinstance(fb_dtsg, str):
            self.log_warning("Cannot generate jazoest without valid fb_dtsg.")
            return ''
        try:
            value = sum(ord(c) for c in fb_dtsg)
            jazoest = f"2{value}"
            return jazoest
        except Exception as e:
            self.log_error(f"Error generating jazoest: {e}")
            return ''

    def _update_cookies(self, response: requests.Response):
        """Updates session cookies, trying JS extraction for 'datr' if needed."""
        if not self.reqs: 
            return
            
        # Log all cookies currently in the session
        self.log_debug(f"🍪 Current session cookies: {list(self.reqs.cookies.keys())}")
        
        # Log cookies received in this response
        response_cookies = []
        for cookie in response.cookies:
            response_cookies.append(f"{cookie.name}={cookie.value[:10]}...")
        if response_cookies:
            self.log_info(f"🍪 Response cookies received: {', '.join(response_cookies)}")
        
        # Check for datr cookie specifically
        if 'datr' not in self.reqs.cookies:
            js_datr = self._extract_js_datr_value(response.text)
            if js_datr:
                self.log_debug(f"Found 'datr' via JS extraction: ...{js_datr[-6:]}. Adding to session.")
                self.reqs.cookies.set('datr', js_datr, domain='.facebook.com', path='/')
            else:
                self.log_warning("Failed to extract 'datr' cookie from JS block.")
                
        # Log final cookie state
        final_cookies = {name: f"{value[:10]}..." for name, value in self.reqs.cookies.items()}
        self.log_info(f"🍪 Final session cookies: {final_cookies}")

    @staticmethod
    def _extract_js_datr_value(html: str) -> Optional[str]:
        """Extracts the 'datr' cookie value from a JS block."""
        match = re.search(r'["\']_js_datr["\']\s*:\s*{\s*["\']value["\']\s*:\s*["\']([^"\']+)["\']', html)
        return match.group(1) if match else None

    @staticmethod
    async def random_sleep(short: bool = False, long_pause: bool = False):  # Added long_pause flag, made async
        """Pauses execution for a random duration (now asynchronous)."""
        if long_pause:
            delay = random.uniform(15, 35)  # Significantly longer pause
            reason = "long periodic pause"
        elif short:
            delay = random.uniform(0.7, 2.2)  # Shorter pauses between pages/API calls
            reason = "short"
        else:  # Default "between firms" pause
            delay = random.uniform(4, 11)
            # Increased chance of longer pause between firms
            if random.random() < 0.15: delay += random.uniform(5, 15)
            reason = "standard inter-firm"

        logging.getLogger(__name__).debug(f"Sleeping for {delay:.2f} seconds ({reason})...")
        await asyncio.sleep(delay)  # Use asyncio.sleep

    # Base class compatibility methods
    async def refresh_session(self) -> bool:
        """Refresh the current session - wrapper for create_new_session."""
        return await self.create_new_session()
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.reqs:
            self.reqs.close()
            self.reqs = None
        self.data = {}
        
    def get_session_headers(self) -> Dict[str, str]:
        """Get headers for API requests."""
        if not self.reqs:
            return {}
        return dict(self.reqs.headers)
    
    def is_session_valid(self) -> bool:
        """Check if current session is valid."""
        return (
            self.reqs is not None and 
            bool(self.data.get('fb_dtsg')) and 
            bool(self.data.get('lsd'))
        )
