# /src/services/fb_ads/image_handler.py

import os
from io import BytesIO
from typing import Any

import requests
from botocore.exceptions import ClientError
from PIL import Image

# Removed dependency_injector imports - using container-based injection
from requests.exceptions import RequestException

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import FBAdServiceError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.infrastructure.storage.s3_async import S3AsyncStorage

from .bandwidth_logger import BandwidthLogger, calculate_request_size
from .image_utils import FBImageHashManager, calculate_image_hash
from .session_manager import FacebookSessionManager


class ImageHandler(AsyncServiceBase):
    """Handles downloading, processing, and uploading of ad images."""

    def __init__(
        self,
        logger: LoggerProtocol,
        config: dict[str, Any],
        s3_manager: S3AsyncStorage,
        session_manager: FacebookSessionManager,
        hash_manager: FBImageHashManager | None,
        bandwidth_logger: BandwidthLogger,
    ):
        """
        Initializes the ImageHandler with injected dependencies.

        Args:
            logger: Logger instance from DI container.
            config: Application configuration from DI container.
            s3_manager: Instance of S3AsyncStorage from DI container.
            session_manager: Instance of FacebookSessionManager from DI container.
            hash_manager: Instance of FBImageHashManager from DI container.
            bandwidth_logger: Instance of BandwidthLogger from DI container.
        """
        super().__init__(logger, config)
        # Initialize S3 manager with fallback handling
        if s3_manager is None:
            self.log_warning(
                "S3 manager not properly injected - S3 operations will be disabled"
            )
        self.s3_manager = s3_manager
        self.session_manager = session_manager
        self.temp_download_dir = config.get("temp_image_dir", "./temp_images")

        # Store injected dependencies
        self.hash_manager = hash_manager
        self.bandwidth_logger = bandwidth_logger

        if self.hash_manager:
            self.log_info("Using injected FBImageHashManager")
        else:
            self.log_warning("FBImageHashManager not provided by DI container")

        self.log_debug("Using injected bandwidth logger instance")

        try:
            os.makedirs(self.temp_download_dir, exist_ok=True)
        except OSError as e:
            self.log_error(
                f"Failed to create temporary image directory {self.temp_download_dir}: {e}"
            )

    async def _execute_action(self, data: Any) -> Any:
        """Execute image handler actions."""
        if isinstance(data, dict):
            action = data.get("action")
            if action == "process_upload_image":
                return await self.process_and_upload_ad_image(
                    data["ad_archive_id"], data["creative_id"], data["image_url"]
                )
        raise FBAdServiceError("Invalid action data provided to ImageHandler")

    async def process_and_upload_ad_image(
        self, ad_archive_id: str, creative_id: str, image_url: str | None
    ) -> tuple[str | None, bool | None]:
        """
        Checks S3 existence, downloads if needed (with explicit proxy handling),
        processes, uploads, and CONSISTENTLY returns (s3_key|None, s3_exists_bool|None).
        - Returns (key, True) if file exists on S3.
        - Returns (key, False) if file did NOT exist but was successfully downloaded/uploaded.
        - Returns (None, False) if file did NOT exist and download/upload FAILED.
        - Returns (None, None) if S3 check failed or process was skipped early (e.g., bad URL).
        - Returns (None, check_result) if blocked or proxy error during download after check.
        """
        s3_exists_result: bool | None = None  # Variable to store the check result
        local_temp_path = (
            None  # Initialize to prevent potential UnboundLocalError in finally
        )

        if (
            not image_url
            or not isinstance(image_url, str)
            or not image_url.startswith("http")
        ):
            self.log_debug(
                f"IH: No valid image URL provided for ad {ad_archive_id}/{creative_id}. Skipping."
            )
            return None, None  # Return two values

        # Use the correct S3 key format: adarchive/{ad_archive_id}/{ad_creative_id}.jpg
        s3_key = f"adarchive/{ad_archive_id}/{creative_id}.jpg"
        # Define temp path using creative_id to avoid collisions in parallel runs
        local_temp_path = os.path.join(
            self.temp_download_dir, f"{creative_id}_temp_{os.getpid()}.jpg"
        )

        # --- Step 1: Check S3 Existence ---
        try:
            self.log_debug(f"IH_CHECK: Checking S3 existence for key: '{s3_key}'")
            s3_exists_result = await self.s3_manager.file_exists(s3_key)
            self.log_debug(
                f"IH_CHECK: s3_manager.file_exists reported: {s3_exists_result} for key: '{s3_key}'"
            )

            if s3_exists_result is True:
                self.log_info(f"IH: Image exists in S3, skipping download: {s3_key}")
                
                # Log bandwidth savings
                if self.bandwidth_logger:
                    # Estimate image size based on typical Facebook ad images
                    estimated_size = 150 * 1024  # 150KB average
                    self.bandwidth_logger.log_s3_skip(estimated_size)
                
                if os.path.exists(local_temp_path):
                    try:
                        os.remove(local_temp_path)
                    except OSError as e:
                        self.log_warning(
                            f"IH_CHECK: Could not remove pre-existing temp file {local_temp_path}: {e}"
                        )
                return s3_key, True  # Return key and True for exists

            self.log_info(
                f"IH: Image not found in S3 (or check failed: {s3_exists_result}), proceeding to download: {s3_key}"
            )

        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code")
            self.log_error(
                f"IH_CHECK: S3 ClientError during existence check for {s3_key} (Code: {error_code}): {e}. Skipping image."
            )
            return None, None
        except Exception as e_generic_s3_check:
            self.log_error(
                f"IH_CHECK: Generic error during S3 file existence check for {s3_key}: {e_generic_s3_check}. Skipping image."
            )
            return None, None

        # PHash handling has been moved to processor.py

        # --- Step 2, 3, 4: Download, Process, Upload (Only if S3 check didn't return True) ---
        try:
            # Check if this is a Facebook CDN URL that we should download directly via HTTP
            is_cdn_url = any(cdn in image_url for cdn in ['fbcdn.net', 'facebook.com', 'fbsbx.com'])
            
            # Check if session manager supports Playwright-based downloads (Camoufox)
            # BUT skip Playwright for CDN URLs to avoid double bandwidth usage
            if hasattr(self.session_manager, 'download_image') and not is_cdn_url:
                # Use Playwright-based download for Camoufox
                self.log_debug(f"IH_DOWNLOAD: Using Playwright-based download for Camoufox")
                timeout = int(self.config.get("image_download_timeout", 30))
                
                # Download image using Playwright
                image_content = await self.session_manager.download_image(image_url, timeout=timeout)
                
                if not image_content:
                    self.log_error(f"IH_DOWNLOAD: Failed to download image via Playwright: {image_url}")
                    return None, s3_exists_result
                
                # Save to local file
                image_content_buffer = BytesIO(image_content)
                content_length = len(image_content)
                
                self.log_debug(f"IH_DOWNLOAD: Downloaded {content_length} bytes for {s3_key}")
                
                # Log bandwidth usage
                if self.bandwidth_logger:
                    request_size = calculate_request_size({"User-Agent": "Mozilla/5.0"})
                    self.bandwidth_logger.log_request(
                        url=image_url,
                        request_size=request_size,
                        response_size=content_length,
                        content_type="image/jpeg",  # Assume JPEG for FB images
                    )
                    self.log_debug(
                        f"Logged bandwidth for Playwright image download: {content_length} bytes from {image_url}"
                    )
                    # Note: We don't log direct HTTP savings here because this IS using Playwright
                
                # Process the image
                saved_locally = self._save_image_to_jpeg(
                    image_content_buffer, local_temp_path
                )
                if not saved_locally:
                    self.log_error(
                        f"IH_DOWNLOAD: Failed to save/convert image locally: {local_temp_path} for {s3_key}"
                    )
                    return None, s3_exists_result
                
            else:
                # Use legacy requests-based download (or forced for CDN URLs)
                if is_cdn_url:
                    self.log_info(f"IH_DOWNLOAD: Detected Facebook CDN URL, using direct HTTP download to avoid double bandwidth: {image_url}")
                session = self.session_manager.get_session()
                if not session:
                    # Try to establish session if not available
                    self.log_warning(
                        f"IH_DOWNLOAD: Session not available for {s3_key}. Attempting to establish session..."
                    )
                    try:
                        # Check if session manager has async create_new_session method
                        if hasattr(self.session_manager, 'create_new_session'):
                            import asyncio
                            # Get or create event loop for async call
                            try:
                                loop = asyncio.get_running_loop()
                                session_created = await self.session_manager.create_new_session()
                            except RuntimeError:
                                # No running loop, create one
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)
                                session_created = loop.run_until_complete(
                                    self.session_manager.create_new_session()
                                )
                            
                            if session_created:
                                session = self.session_manager.get_session()
                                self.log_info("IH_DOWNLOAD: Successfully established new session")
                            else:
                                self.log_error("IH_DOWNLOAD: Failed to establish new session")
                    except Exception as e:
                        self.log_error(f"IH_DOWNLOAD: Error establishing session: {e}")
                    
                    # Final check after attempt
                    if not session:
                        self.log_error(
                            f"IH_DOWNLOAD: Cannot download image {s3_key}: Session not available after establishment attempt."
                        )
                        return None, s3_exists_result

                # --- Explicit Proxy Setup for Download ---
                proxies = None
                # ALWAYS use proxy for Facebook CDN downloads
                if self.session_manager:
                    # Get proxy settings dictionary WITH AUTHENTICATION from session manager
                    # ASSUMES session_manager has a method returning {'http': '*********************:port', 'https': '...'}
                    proxy_settings = self.session_manager.get_current_proxy_settings()
                    if proxy_settings:
                        proxies = proxy_settings
                        proxy_host = (
                            list(proxies.values())[0].split("@")[-1] if proxies else "N/A"
                        )  # Log host only
                        self.log_info(
                            f"IH_DOWNLOAD: Using proxy {proxy_host} for Facebook CDN image download (REQUIRED)."
                        )
                    else:
                        self.log_error(
                            "IH_DOWNLOAD: CRITICAL - Proxy is REQUIRED for Facebook CDN downloads but failed to get proxy settings from session manager."
                        )
                        # Fail fast - proxy is REQUIRED for Facebook CDN
                        return None, s3_exists_result
                else:
                    self.log_error(
                        "IH_DOWNLOAD: CRITICAL - Session manager not available. Proxy is REQUIRED for Facebook CDN downloads."
                    )
                    # Fail fast - cannot proceed without proxy
                    return None, s3_exists_result
                # --- End Explicit Proxy Setup ---

                self.log_debug(f"IH_DOWNLOAD: Attempting download: {image_url}")
                headers = {"User-Agent": "Mozilla/5.0"}  # Simple user agent
                timeout = int(self.config.get("image_download_timeout", 30))

                # Calculate request size before making the request
                request_size = calculate_request_size(headers)

                # Initialize variables for both paths
                image_content_buffer = None
                content_length = 0
                
                # Check if this is a CamoufoxSessionManager (has download_image method)
                if hasattr(session, 'download_image') and callable(getattr(session, 'download_image')):
                    # Use Camoufox's download_image method
                    self.log_debug(f"IH_DOWNLOAD: Using Camoufox download_image method")
                    try:
                        # Camoufox download_image is async
                        import asyncio
                        try:
                            loop = asyncio.get_running_loop()
                            image_content = await session.download_image(image_url, timeout=timeout)
                        except RuntimeError:
                            # No running loop, create one
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            image_content = loop.run_until_complete(
                                session.download_image(image_url, timeout=timeout)
                            )
                        
                        if not image_content:
                            self.log_error(f"IH_DOWNLOAD: Camoufox download_image returned None for {image_url}")
                            return None, s3_exists_result
                        
                        # Create a BytesIO buffer from the content
                        image_content_buffer = BytesIO(image_content)
                        content_length = len(image_content)
                        
                        self.log_debug(
                            f"IH_DOWNLOAD: Downloaded {content_length} bytes via Camoufox for {s3_key}"
                        )
                        
                        # Log bandwidth usage for Camoufox download
                        if self.bandwidth_logger:
                            self.bandwidth_logger.log_request(
                                url=image_url,
                                request_size=request_size,
                                response_size=content_length,
                                content_type="image/jpeg",  # Assume JPEG for FB images
                            )
                            self.log_debug(
                                f"Logged bandwidth for Camoufox image download: {content_length} bytes from {image_url}"
                            )
                        
                    except Exception as e:
                        self.log_error(f"IH_DOWNLOAD: Error using Camoufox download_image: {e}")
                        return None, s3_exists_result
                
                elif hasattr(session, 'get'):
                    # Standard requests.Session - use the get method
                    response = session.get(
                        image_url,
                        timeout=timeout,
                        stream=True,
                        headers=headers,
                        proxies=proxies,  # Pass the explicit proxy dict here
                    )
                    
                    # Check for FB blocks... (keep this logic)
                    response_text_preview = "[Binary Content]"
                    is_likely_text = "text" in response.headers.get("Content-Type", "").lower()
                    if is_likely_text:
                        try:
                            response_text_preview = next(
                                response.iter_content(chunk_size=512, decode_unicode=True), ""
                            )
                        except Exception:
                            pass
                    elif response.status_code != 200:
                        try:
                            response_text_preview = response.text[:500]
                        except Exception:
                            pass

                    if (
                        response.status_code in [403, 429, 503]
                        or "Temporarily Blocked" in response_text_preview
                        or "Try again later" in response_text_preview
                    ):
                        self.log_warning(
                            f"IH_DOWNLOAD: Block/Rate limit ({response.status_code}) downloading image: {image_url} for {s3_key}"
                        )
                        self.session_manager.handle_block_or_rate_limit("Image download block")
                        return None, s3_exists_result

                    response.raise_for_status()  # Check for other HTTP errors AFTER block checks

                    # --- Process downloaded content (keep this logic) ---
                    image_content_buffer = BytesIO()
                    content_length = 0
                    for chunk in response.iter_content(chunk_size=8192):
                        image_content_buffer.write(chunk)
                        content_length += len(chunk)
                    self.log_debug(
                        f"IH_DOWNLOAD: Downloaded {content_length} bytes for {s3_key}"
                    )

                    # Log bandwidth usage for image download
                    content_type = response.headers.get("content-type", "")
                    if self.bandwidth_logger:
                        self.bandwidth_logger.log_request(
                            url=image_url,
                            request_size=request_size,
                            response_size=content_length,
                            content_type=content_type,
                        )
                        self.log_debug(
                            f"Logged bandwidth for image download: {content_length} bytes from {image_url}"
                        )
                        
                        # If this was a CDN URL, log the savings from using direct HTTP
                        if is_cdn_url:
                            self.bandwidth_logger.log_direct_http_save(content_length)
                    else:
                        self.log_debug(
                            f"Downloaded {content_length} bytes from {image_url} (bandwidth logging disabled)"
                        )

                    if content_length == 0:
                        self.log_warning(
                            f"IH_DOWNLOAD: Downloaded empty content from {image_url} for {s3_key}. Skipping."
                        )
                        return None, s3_exists_result
                
                else:
                    self.log_error(f"IH_DOWNLOAD: Session object has neither 'get' nor 'download_image' method: {type(session).__name__}")
                    return None, s3_exists_result

                # Common processing for both paths (if we have content)
                if image_content_buffer and content_length > 0:
                    image_content_buffer.seek(0)
                saved_locally = self._save_image_to_jpeg(
                    image_content_buffer, local_temp_path
                )
                if not saved_locally:
                    self.log_error(
                        f"IH_DOWNLOAD: Failed to save/convert image locally: {local_temp_path} for {s3_key}"
                    )
                    return None, s3_exists_result

            # PHash calculation has been moved to processor.py after S3 upload

            # --- Upload to S3 (keep this logic) ---
            self.log_debug(
                f"IH_UPLOAD: Attempting upload to S3 for key: {s3_key} from {local_temp_path}"
            )
            # Assuming s3_manager.upload_file returns bool or similar indicating success
            upload_result = await self.s3_manager.upload_file(local_temp_path, s3_key)
            # Check the status string returned by upload_file
            if isinstance(upload_result, tuple) and len(upload_result) == 2:
                status = upload_result[1]
                if status in [
                    "uploaded",
                    "reuploaded",
                    "already exists",
                ]:  # Consider "already exists" a success here too? Adjust if needed.
                    self.log_info(
                        f"IH_UPLOAD: Upload successful (Status: {status}) to S3: {s3_key}"
                    )
                    
                    # Calculate and save PHash to FBImageHash table
                    if self.hash_manager and local_temp_path and os.path.exists(local_temp_path):
                        try:
                            # Read the image file to calculate PHash
                            with open(local_temp_path, 'rb') as f:
                                image_content = f.read()
                            
                            phash = calculate_image_hash(image_content)
                            if phash:
                                phash_str = str(phash)
                                
                                # Create FBImageHash record
                                hash_record = {
                                    "PHash": phash_str,
                                    "AdArchiveID": ad_archive_id,
                                    "ImageUrl": image_url,
                                    "S3Key": s3_key,  # Note: FBImageHashService expects 'S3Key' not 'S3ImageKey'
                                    "LastUpdated": self.config.get("iso_date", ""),  # Note: expects 'LastUpdated' not 'CreatedDate'/'LastSeen'
                                }
                                
                                # Save to FBImageHash table using the correct method name
                                await self.hash_manager.add_hash_record(hash_record)
                                self.log_info(
                                    f"IH_UPLOAD: Saved PHash {phash_str} for {ad_archive_id} to FBImageHash table"
                                )
                        except Exception as hash_e:
                            self.log_error(
                                f"IH_UPLOAD: Error saving PHash for {ad_archive_id}: {hash_e}"
                            )
                    
                    return (
                        s3_key,
                        False,
                    )  # Return key, and False (since it didn't exist initially)
                else:
                    self.log_error(
                        f"IH_UPLOAD: Upload FAILED to S3 for key: {s3_key}. Status: {status}"
                    )
                    return None, False
            else:  # Handle unexpected return type from upload_file
                self.log_error(
                    f"IH_UPLOAD: Upload to S3 for key {s3_key} returned unexpected result: {upload_result}"
                )
                return None, False

        # --- Exception Handling ---
        except requests.exceptions.ProxyError as pe:
            # Specific handling for ProxyError - includes the 407 Auth error
            self.log_error(
                f"IH_DOWNLOAD: ProxyError downloading image {image_url} (S3 key {s3_key}): {pe}"
            )
            if "407" in str(pe):
                self.log_error(
                    "IH_DOWNLOAD: *** Proxy Authentication Required (407). Check credentials used in proxy setup. ***"
                )
            return (
                None,
                s3_exists_result,
            )  # Return None key and the original check result

        except RequestException as e:
            # Handle other network/HTTP errors
            status_code = (
                e.response.status_code
                if hasattr(e, "response") and e.response is not None
                else "N/A"
            )
            self.log_error(
                f"IH_DOWNLOAD: Download failed for image {image_url} (S3 key {s3_key}): {e} (Status: {status_code})"
            )
            if (
                hasattr(e, "response")
                and e.response is not None
                and e.response.status_code in [403, 429]
            ):
                self.session_manager.handle_block_or_rate_limit(
                    "Image download RequestException"
                )
            return None, s3_exists_result

        except OSError as e:
            self.log_error(
                f"IH_DOWNLOAD: File/Image processing IO error for {s3_key} ({local_temp_path}): {e}"
            )
            return None, s3_exists_result
        except Exception as e:
            self.log_error(
                f"IH_DOWNLOAD: Unexpected error processing image {s3_key} from {image_url}: {e}"
            )
            return None, s3_exists_result
        finally:
            # Ensure temp file is removed
            if local_temp_path and os.path.exists(local_temp_path):
                try:
                    os.remove(local_temp_path)
                    self.log_debug(
                        f"IH_CLEANUP: Removed temporary file {local_temp_path}"
                    )
                except OSError as e:
                    self.log_warning(
                        f"IH_CLEANUP: Could not remove temporary image file {local_temp_path}: {e}"
                    )

        # _save_image_to_jpeg remains the same...

    def _save_image_to_jpeg(
        self, image_content_stream: BytesIO, save_path: str
    ) -> bool:
        # (Keep the implementation from the previous response)
        try:
            with Image.open(image_content_stream) as image:
                image_to_save = None
                self.log_debug(
                    f"IH_SAVE: Processing image {os.path.basename(save_path)} (Mode: {image.mode}, Size: {image.size})"
                )
                if image.mode == "P":
                    image = image.convert("RGBA")
                    self.log_debug(
                        f"IH_SAVE: Converted image {os.path.basename(save_path)} from P to RGBA."
                    )
                if image.mode in ("RGBA", "LA"):
                    self.log_debug(
                        f"IH_SAVE: Converting image {os.path.basename(save_path)} from {image.mode} to RGB with white background."
                    )
                    background = Image.new("RGB", image.size, (255, 255, 255))
                    try:
                        mask = image.getchannel("A")
                        background.paste(image, mask=mask)
                    except ValueError:
                        try:  # Try splitting as fallback
                            *_, mask = image.split()
                            background.paste(image, mask=mask)
                        except Exception:
                            self.log_warning(
                                f"IH_SAVE: Could not get or split alpha channel for {save_path}. Using simple convert."
                            )
                            image_to_save = image.convert("RGB")  # Fallback conversion

                    if image_to_save is None:
                        image_to_save = background
                elif image.mode != "RGB":
                    self.log_debug(
                        f"IH_SAVE: Converting image {os.path.basename(save_path)} from {image.mode} to RGB."
                    )
                    image_to_save = image.convert("RGB")
                else:
                    image_to_save = image
                if image_to_save:
                    os.makedirs(os.path.dirname(save_path), exist_ok=True)
                    image_to_save.save(save_path, format="JPEG", quality=85)
                    self.log_debug(
                        f"IH_SAVE: Saved image {os.path.basename(save_path)} as JPEG to {save_path}"
                    )
                    return True
                else:
                    self.log_error(
                        f"IH_SAVE: Image object became None during conversion for {save_path}"
                    )
                    return False
        except Exception as e:
            # Use PIL.UnidentifiedImageError if available/needed, else generic Exception
            # from PIL import UnidentifiedImageError
            # except UnidentifiedImageError: ...
            self.log_error(
                f"IH_SAVE: Pillow error processing/saving image to {save_path}: {e}"
            )
            return False

    # save_profile_picture remains the same...
    def save_profile_picture(
        self, law_firm_id: str, image_uri: str | None, local_dir: str
    ) -> bool | None:
        # (Keep the implementation from the previous response)
        if not law_firm_id or not image_uri:
            self.logger.warning(
                f"Missing ID ({law_firm_id}) or URI ({image_uri}) for profile pic."
            )
            return None
        local_img_dir = os.path.join(local_dir, "img")
        os.makedirs(local_img_dir, exist_ok=True)
        local_save_path = os.path.join(local_img_dir, f"{law_firm_id}.jpg")
        s3_save_path = f"assets/images/law-firm-logos/{law_firm_id}.jpg"
        s3_exists = None
        uploaded_to_s3 = False  # Track upload status
        try:
            self.logger.debug(f"PROFILE PIC: Checking S3 for {s3_save_path}")
            s3_exists = self.s3_manager.file_exists(s3_save_path)
            self.logger.debug(
                f"PROFILE PIC: s3_manager.file_exists returned: {s3_exists} for {s3_save_path}"
            )
            if s3_exists:
                self.logger.debug(
                    f"PROFILE PIC: Already exists in S3: {s3_save_path}. Skipping download."
                )
                return True
            self.logger.info(
                f"PROFILE PIC: Not found in S3 ({s3_save_path}). Proceeding with download."
            )
            
            # Check if this is a Facebook CDN URL
            is_cdn_url = any(cdn in image_uri for cdn in ['fbcdn.net', 'facebook.com', 'fbsbx.com'])
            
            # Check if session manager supports Playwright-based downloads (Camoufox)
            # BUT skip Playwright for CDN URLs to avoid double bandwidth usage
            if hasattr(self.session_manager, 'download_image') and not is_cdn_url:
                # Use Playwright-based download for Camoufox
                self.logger.debug(f"PROFILE PIC: Using Playwright-based download for profile picture")
                
                # Since save_profile_picture is not async, we need to run the async download in the event loop
                import asyncio
                try:
                    # Get or create event loop
                    try:
                        loop = asyncio.get_running_loop()
                    except RuntimeError:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                    
                    # Run the async download
                    content = loop.run_until_complete(
                        self.session_manager.download_image(image_uri, timeout=15)
                    )
                except Exception as e:
                    self.logger.error(f"PROFILE PIC: Error running async download: {e}")
                    content = None
                
                if not content:
                    self.logger.error(f"PROFILE PIC: Failed to download profile pic via Playwright: {image_uri}")
                    return False
                
                content_length = len(content)
                content_type = "image/jpeg"  # Assume JPEG for profile pics
                request_size = calculate_request_size({"User-Agent": "Mozilla/5.0"})
                
            else:
                # Use legacy requests-based download (or forced for CDN URLs)
                if is_cdn_url:
                    self.logger.info(f"PROFILE PIC: Detected Facebook CDN URL, using direct HTTP download to avoid double bandwidth: {image_uri}")
                session = self.session_manager.get_session()
                if not session:
                    self.logger.error(
                        f"PROFILE PIC: Cannot download profile pic for {law_firm_id}: Session not available."
                    )
                    return False
                self.logger.debug(
                    f"PROFILE PIC: Attempting download via session: {image_uri}"
                )

                # Calculate request size before making the request
                request_size = calculate_request_size(
                    {"User-Agent": session.headers.get("User-Agent", "Mozilla/5.0")}
                )

                response = session.get(image_uri, timeout=15, stream=True)
                response.raise_for_status()

                # Get content and log bandwidth usage
                content = response.content
                content_length = len(content)
                content_type = response.headers.get("content-type", "")

            if self.bandwidth_logger:
                self.bandwidth_logger.log_request(
                    url=image_uri,
                    request_size=request_size,
                    response_size=content_length,
                    content_type=content_type,
                )
                self.logger.debug(
                    f"Logged bandwidth for profile picture download: {content_length} bytes from {image_uri}"
                )
            else:
                self.logger.debug(
                    f"Downloaded profile picture {content_length} bytes from {image_uri} (bandwidth logging disabled)"
                )

            image_content_buffer = BytesIO(content)
            saved_locally = self._save_image_to_jpeg(
                image_content_buffer, local_save_path
            )
            if not saved_locally:
                self.logger.error(
                    f"PROFILE PIC: Failed to save profile pic locally to {local_save_path}"
                )
                return False
            self.logger.info(f"PROFILE PIC: Saved locally: {local_save_path}")
            self.logger.info(f"PROFILE PIC: Attempting upload to S3: {s3_save_path}")
            uploaded_to_s3 = self.s3_manager.upload_file(local_save_path, s3_save_path)
            if uploaded_to_s3:
                self.logger.info(f"PROFILE PIC: Uploaded to S3: {s3_save_path}")
                return True
            else:
                self.logger.error(
                    f"PROFILE PIC: Failed to upload profile pic to S3: {s3_save_path}"
                )
                return False
        except RequestException as e:
            err_msg = f"PROFILE PIC: Failed to download profile pic for {law_firm_id} from {image_uri}: {e}"
            if e.response is not None:
                err_msg += f" (Status: {e.response.status_code})"
            self.logger.error(err_msg)
            return False
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code")
            status_code = e.response.get("ResponseMetadata", {}).get("HTTPStatusCode")
            self.logger.error(
                f"PROFILE PIC: S3 ClientError during processing for {law_firm_id} (Code: {error_code}, Status: {status_code}): {e}"
            )
            return False
        except OSError as e:
            self.logger.error(
                f"PROFILE PIC: IOError saving profile picture {local_save_path} for {law_firm_id}: {e}"
            )
            return False
        except Exception as e:
            self.logger.error(
                f"PROFILE PIC: Unexpected error saving profile picture for {law_firm_id}: {e}",
                exc_info=self.config.get("verbose", False),
            )
            return False
        finally:
            if os.path.exists(local_save_path):
                # Remove if S3 check was True OR if upload failed after local save
                should_remove = s3_exists is True or not uploaded_to_s3
                if should_remove:
                    try:
                        os.remove(local_save_path)
                        self.logger.debug(
                            f"PROFILE PIC: Removed local file {local_save_path}"
                        )
                    except OSError as e:
                        self.logger.warning(
                            f"PROFILE PIC: Could not remove local profile pic file {local_save_path}: {e}"
                        )

    async def get_phash_and_existing_text(
        self,
        s3_key: str,
        ad_archive_id: str,
        ad_creative_id: str,
        current_process_date: str,
    ) -> tuple[str | None, str | None]:
        """
        Get perceptual hash and check for existing ImageText in FBImageHash table.

        Args:
            s3_key: S3 key of the image
            ad_archive_id: Ad archive ID
            ad_creative_id: Ad creative ID
            current_process_date: Current processing date

        Returns:
            Tuple of (phash_str, existing_image_text)
        """
        phash_str = None
        existing_image_text = None

        try:
            # Download image from S3 to calculate phash
            if s3_key:
                image_content = await self.s3_manager.download_content(s3_key)
                if image_content:
                    # Calculate perceptual hash
                    phash = calculate_image_hash(image_content)
                    if phash:
                        phash_str = str(phash)

                        # Check if we have FBImageHashManager configured
                        if self.hash_manager:
                            # Query for existing record by phash
                            existing_records = await self.hash_manager.query_by_hash(
                                phash_str
                            )
                            if existing_records:
                                # Look for ImageText in any of the records
                                for record in existing_records:
                                    if "ImageText" in record and record["ImageText"]:
                                        existing_image_text = record["ImageText"]
                                        self.logger.debug(
                                            f"Found existing ImageText for phash {phash_str}: "
                                            f"{existing_image_text[:50]}..."
                                        )
                                        break
                else:
                    self.logger.warning(f"Could not download image from S3: {s3_key}")
            else:
                self.logger.debug("No S3 key provided for phash calculation")

        except Exception as e:
            self.logger.error(
                f"Error getting phash and existing text for {ad_archive_id}: {e}",
                exc_info=self.config.get("verbose", False),
            )

        return phash_str, existing_image_text
