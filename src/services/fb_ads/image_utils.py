# /src/services/fb_ads/image_utils.py
# --- START OF FILE src/lib/fb_ads/image_utils.py ---
# !/usr/bin/env python3
"""
Utility functions and classes for Facebook image processing and hashing.

This module provides:
1. FBImageHashManager - A class for managing image hashes in DynamoDB
2. extract_ids_from_key - A function to extract AdArchiveID and AdCreativeID from S3 keys
3. process_single_image - Function to download, hash, and prepare an image record
4. Helper functions for image processing
"""

import argparse
import datetime  # <-- Ensure datetime is imported
import io
import logging
import os
import re
import sys
import time
from asyncio import as_completed
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from typing import Any

import boto3
import imagehash
from botocore.config import Config
from botocore.exceptions import ClientError, NoCredentialsError
from PIL import Image, UnidentifiedImageError
from rich.logging import RichHandler
from rich.progress import (
    BarColumn,
    MofNCompleteColumn,
    Progress,
    SpinnerColumn,
    TextColumn,
    TimeElapsedColumn,
    TimeRemainingColumn,
)

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import FBAdServiceError

# Config should be passed in, not loaded from module
# from src.lib.config import load_config
# REMOVED: from hash_fb_images import process_single_image
# Import the repository and base classes
from src.repositories.fb_image_hash_repository import FBImageHashRepository

# Configure logging
logger = logging.getLogger(__name__)

# Constants
BATCH_SIZE = 25  # DynamoDB BatchWriteItem limit


# --- Helper Functions ---


def calculate_image_hash(image_content: bytes) -> imagehash.ImageHash | None:
    """Calculates the perceptual hash of an image."""
    try:
        img = Image.open(io.BytesIO(image_content))
        img_hash = imagehash.phash(img)
        return img_hash
    except UnidentifiedImageError:
        logger.warning("Could not identify image format.")
        return None
    except Exception as e:
        logger.error(f"Error processing image for hashing: {e}", exc_info=True)
        return None


def extract_ids_from_key(
    s3_key: str, key_format: str = "original"
) -> tuple[str | None, str | None]:
    """
    Extract AdArchiveID and AdCreativeID from an S3 key based on the specified format.

    Args:
        s3_key: The S3 key to parse
        key_format: The format of the key ('original', 'archive_id_creative_id', etc.)

    Returns:
        Tuple of (AdArchiveID, AdCreativeID), either may be None if extraction fails
    """
    if not s3_key:
        logger.debug("Empty S3 key provided to extract_ids_from_key")
        return None, None

    # Default values
    ad_archive_id = None
    ad_creative_id = None

    try:
        # Handle different key formats
        if key_format == "original":
            # Example format: adarchive/fb/123456789/creative_12345.jpg
            parts = s3_key.split("/")
            logger.debug(
                f"Parsing key '{s3_key}' with format 'original', parts: {parts}"
            )

            if len(parts) >= 3:
                ad_archive_id = parts[2]  # Extract archive ID from path
                logger.debug(f"Extracted AdArchiveID: {ad_archive_id}")

                # Extract creative ID from filename if possible
                if len(parts) >= 4:
                    filename = parts[3]
                    # Try different patterns for creative ID
                    patterns = [
                        r"creative_([0-9]+)",  # Standard pattern
                        r"creative([0-9]+)",  # No underscore
                        r"([0-9]+)_creative",  # Reversed order
                        r"([0-9]+)",  # Just look for numbers
                    ]

                    for pattern in patterns:
                        creative_match = re.search(pattern, filename)
                        if creative_match:
                            ad_creative_id = creative_match.group(1)
                            logger.debug(
                                f"Extracted AdCreativeID: {ad_creative_id} using pattern {pattern}"
                            )
                            break

                    if not ad_creative_id:
                        # If no pattern matched, use a default creative ID based on the filename
                        ad_creative_id = f"default_{hash(filename) % 10000}"
                        logger.debug(
                            f"Using default AdCreativeID: {ad_creative_id} for filename {filename}"
                        )

        elif key_format == "archive_id_creative_id":
            # Example format: adarchive/fb/archive_123456789_creative_12345.jpg
            logger.debug(f"Parsing key '{s3_key}' with format 'archive_id_creative_id'")
            match = re.search(r"archive_([0-9]+)_creative_([0-9]+)", s3_key)
            if match:
                ad_archive_id = match.group(1)
                ad_creative_id = match.group(2)
                logger.debug(
                    f"Extracted AdArchiveID: {ad_archive_id}, AdCreativeID: {ad_creative_id}"
                )

        # Flexible format - try to extract any numeric IDs from the path
        if not ad_archive_id or not ad_creative_id:
            logger.debug(f"Trying flexible format extraction for '{s3_key}'")
            # Look for numeric sequences that could be IDs
            numeric_ids = re.findall(r"/([0-9]{5,})/?", s3_key)
            if numeric_ids:
                if not ad_archive_id and len(numeric_ids) > 0:
                    ad_archive_id = numeric_ids[0]
                    logger.debug(f"Flexibly extracted AdArchiveID: {ad_archive_id}")
                if not ad_creative_id and len(numeric_ids) > 1:
                    ad_creative_id = numeric_ids[1]
                    logger.debug(f"Flexibly extracted AdCreativeID: {ad_creative_id}")

    except Exception as e:
        logger.error(f"Error extracting IDs from S3 key '{s3_key}': {e}")
        return None, None

    # If we still don't have a creative ID but have an archive ID, generate a default one
    if ad_archive_id and not ad_creative_id:
        ad_creative_id = f"default_{hash(s3_key) % 10000}"
        logger.debug(
            f"Generated default AdCreativeID: {ad_creative_id} for key with AdArchiveID: {ad_archive_id}"
        )

    return ad_archive_id, ad_creative_id


# --- MOVED FUNCTION DEFINITION HERE ---
def process_single_image(
    s3_client,  # Use the passed client instance
    bucket_name: str,
    s3_key: str,
    ad_archive_id: str,
    ad_creative_id: str,
    hash_manager,  # hash_manager is passed but not used in this implementation - can be removed if not needed later
) -> dict[str, Any] | None:
    """
    Downloads an image from S3, calculates its hash, and returns a record dictionary.

    Args:
        s3_client: Initialized S3 client.
        bucket_name: S3 bucket name.
        s3_key: S3 key of the image.
        ad_archive_id: AdArchiveID associated with the image.
        ad_creative_id: AdCreativeID associated with the image.
        hash_manager: FBImageHashManager instance (currently unused).

    Returns:
        A dictionary representing the hash record, or None if processing fails.
    """
    image_content = None
    try:
        logger.debug(
            f"Processing S3 key: {s3_key} (Archive: {ad_archive_id}, Creative: {ad_creative_id})"
        )
        response = s3_client.get_object(Bucket=bucket_name, Key=s3_key)
        image_content = response["Body"].read()
        logger.debug(f"Downloaded {len(image_content)} bytes for {s3_key}")
    except ClientError as e:
        if e.response["Error"]["Code"] == "NoSuchKey":
            logger.warning(f"S3 key not found: {s3_key}")
        elif e.response["Error"]["Code"] == "AccessDenied":
            logger.error(f"S3 access denied for key: {s3_key}")
        else:
            logger.error(f"S3 ClientError downloading {s3_key}: {e}")
        return None
    except Exception as e:
        logger.error(f"Error downloading {s3_key}: {e}", exc_info=True)
        return None

    if not image_content:
        logger.warning(f"No image content downloaded for {s3_key}")
        return None

    # Calculate hash
    image_hash_val = calculate_image_hash(image_content)
    if image_hash_val is None:
        logger.warning(f"Failed to calculate hash for {s3_key}. Skipping record.")
        return None

    hash_str = str(image_hash_val)
    timestamp = datetime.datetime.utcnow().isoformat()

    # Create record
    record = {
        "PHash": hash_str,  # Partition Key (CORRECT NAME)
        "AdArchiveID": ad_archive_id,  # Sort Key
        "AdCreativeID": ad_creative_id,
        "S3Key": s3_key,
        "LastUpdated": timestamp,
        # Initialize ImageText as empty string - it will be populated later
        "ImageText": "",
        # Other fields can be added later if needed
    }
    logger.debug(
        f"Generated record for {s3_key}: PHash={hash_str}, AdArchiveID={ad_archive_id}"
    )
    return record


# --- END MOVED FUNCTION ---


class FBImageHashService(AsyncServiceBase):
    """
    Service for Facebook image hash operations using the repository pattern.
    """

    def __init__(
        self,
        repository: FBImageHashRepository = None,
        config: dict[str, Any] = None,
        logger: logging.Logger = None,
    ):
        """
        Initialize the FBImageHashService.

        Args:
            repository: FBImageHashRepository instance
            config: Configuration dictionary
            logger: Logger instance (optional)
        """
        logger_instance = logger or logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})
        self.repository = repository

    async def _execute_action(self, data: Any) -> Any:
        """Execute FBImageHashService actions."""
        if isinstance(data, dict):
            action = data.get("action")
            if action == "check_duplicate":
                return await self.check_duplicate_exists(data["phash"])
            elif action == "get_duplicate_info":
                return await self.get_duplicate_info(data["phash"])
            elif action == "query_by_hash":
                return await self.query_by_hash(data["hash_str"])
            elif action == "add_hash_record":
                return await self.add_hash_record(data["record"])
            elif action == "update_image_text":
                return await self.update_image_text(
                    data["phash"], data["ad_archive_id"], data["image_text"]
                )
            elif action == "batch_insert":
                return await self.batch_insert_items(data["items"])
        raise FBAdServiceError("Invalid action data provided to FBImageHashService")

    async def check_duplicate_exists(self, p_hash: str) -> bool:
        """Check if a PHash already exists in the database."""
        return await self.repository.check_duplicate_exists(p_hash)

    async def get_duplicate_info(self, p_hash: str) -> dict[str, Any] | None:
        """Get information about the first duplicate found for a PHash."""
        return await self.repository.get_duplicate_info(p_hash)

    async def query_by_hash(self, hash_str: str) -> list[dict[str, Any]]:
        """Query records by PHash value."""
        return await self.repository.query_by_p_hash(hash_str)

    async def add_hash_record(self, record: dict[str, Any]) -> bool:
        """Add a new hash record to the database."""
        # Convert PascalCase record to snake_case for repository
        snake_record = {
            "p_hash": record.get("PHash"),
            "ad_archive_id": record.get("AdArchiveID"),
            "ad_creative_id": record.get("AdCreativeID"),
            "s3_image_key": record.get("S3Key"),
            "image_url": record.get("ImageUrl", ""),
            "created_date": record.get("LastUpdated"),
            "last_seen": record.get("LastUpdated"),
            "image_text": record.get("ImageText", ""),
        }
        return await self.repository.add_or_update_record(snake_record)

    async def update_image_text(
        self, p_hash: str, ad_archive_id: str, image_text: str
    ) -> bool:
        """Update ImageText for a specific hash record."""
        updates = {"image_text": image_text or ""}
        key = {"p_hash": p_hash, "ad_archive_id": ad_archive_id}
        return await self.repository.update_record(key, updates)

    async def batch_insert_items(
        self, records: list[dict[str, Any]]
    ) -> tuple[int, int]:
        """Batch insert hash records."""
        if not records:
            return 0, 0

        # Convert records to snake_case format for repository
        snake_records = []
        for record in records:
            snake_record = {
                "p_hash": record.get("PHash"),
                "ad_archive_id": record.get("AdArchiveID"),
                "ad_creative_id": record.get("AdCreativeID"),
                "s3_image_key": record.get("S3Key"),
                "image_url": record.get("ImageUrl", ""),
                "created_date": record.get("LastUpdated"),
                "last_seen": record.get("LastUpdated"),
                "image_text": record.get("ImageText", ""),
            }
            snake_records.append(snake_record)

        return await self.repository.batch_create(snake_records)


# Legacy compatibility class - wrapper around the new service
class FBImageHashManager:
    """Legacy wrapper for backward compatibility."""

    def __init__(
        self,
        table_name: str,
        config: dict[str, Any] = None,
        region_name: str = None,
        use_local: bool = False,
        local_port: int = 8000,
        remove_empty_str: bool = True,
    ):
        """Initialize with legacy interface but use new service internally."""
        self.config = config or {}
        if region_name:
            self.config["aws_region"] = region_name

        # Create storage and repository (this would normally be done by factory)
        import logging

        from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage

        logger_instance = logging.getLogger(__name__)
        storage = AsyncDynamoDBStorage(config=self.config, logger=logger_instance)
        repository = FBImageHashRepository(storage)

        # Create the new service
        self.service = FBImageHashService(repository, self.config)

    def table_exists(self) -> bool:
        """Check if table exists (synchronous legacy method)."""
        import asyncio

        try:
            return asyncio.run(self._check_table_exists())
        except:
            return False

    async def _check_table_exists(self) -> bool:
        """Async helper for table existence check."""
        try:
            async with self.service.repository.storage:
                # Try to get table info
                await self.service.repository.storage.get_table(
                    self.service.repository.table_name
                )
                return True
        except:
            return False

    def query_by_hash(self, hash_str: str) -> list[dict[str, Any]]:
        """Legacy synchronous method."""
        import asyncio

        try:
            return asyncio.run(self._query_by_hash_async(hash_str))
        except:
            return []

    async def _query_by_hash_async(self, hash_str: str) -> list[dict[str, Any]]:
        """Async helper for hash query."""
        async with self.service.repository.storage:
            records = await self.service.query_by_hash(hash_str)
            # Convert back to PascalCase for legacy compatibility
            return [self._convert_to_pascal(record) for record in records]

    def _convert_to_pascal(self, record: dict[str, Any]) -> dict[str, Any]:
        """Convert snake_case record back to PascalCase for legacy compatibility."""
        return {
            "PHash": record.get("p_hash"),
            "AdArchiveID": record.get("ad_archive_id"),
            "AdCreativeID": record.get("ad_creative_id"),
            "S3Key": record.get("s3_image_key"),
            "ImageUrl": record.get("image_url"),
            "LastUpdated": record.get("last_seen"),
            "ImageText": record.get("image_text", ""),
        }

    def batch_insert_items(self, records: list[dict[str, Any]]) -> tuple[int, int]:
        """Legacy synchronous batch insert."""
        import asyncio

        try:
            return asyncio.run(self._batch_insert_async(records))
        except:
            return 0, len(records)

    async def _batch_insert_async(
        self, records: list[dict[str, Any]]
    ) -> tuple[int, int]:
        """Async helper for batch insert."""
        async with self.service.repository.storage:
            return await self.service.batch_insert_items(records)
    
    async def add_hash_record(self, record: dict[str, Any]) -> bool:
        """Async method to add a hash record."""
        async with self.service.repository.storage:
            return await self.service.add_hash_record(record)


def process_images(
    config: dict[str, Any],
    bucket_name: str,  # Bucket name is now passed in directly
    use_local_db: bool,
    local_db_port: int,
    max_workers: int,
    limit: int | None = None,
):
    """
    Lists images in the specified S3 prefix, calculates hashes,
    and stores them in DynamoDB. Focuses on images within multi-creative archives.
    """
    s3_prefix = config.get("s3_fb_ad_archive_prefix", "adarchive/fb/")
    expected_key_format = config.get(
        "s3_key_format", "original"
    )  # e.g., 'original' or 'archive_id_creative_id'

    logger.info(
        f"Starting processing for bucket='{bucket_name}', prefix='{s3_prefix}'"
    )  # Use the passed bucket_name
    logger.info(
        f"Using {'local' if use_local_db else 'AWS'} DynamoDB. Workers={max_workers}, BatchSize={BATCH_SIZE}"
    )
    logger.info(f"Expecting S3 key format: '{expected_key_format}'")

    # --- Initialize S3 and DynamoDB Clients/Managers ---
    s3_client = None
    hash_manager = None
    try:
        # *** Configure S3 client with increased connection pool ***
        s3_config = Config(
            max_pool_connections=max_workers + 5
        )  # Adjust pool size based on workers

        aws_profile = config.get("aws_profile")
        session = (
            boto3.Session(profile_name=aws_profile) if aws_profile else boto3.Session()
        )
        s3_client = session.client(
            "s3", region_name=config["aws_region"], config=s3_config
        )  # Pass config

        # Use legacy wrapper for backward compatibility
        hash_manager = FBImageHashManager(
            table_name=config["dynamodb"]["fb_image_hash_table_name"],
            config=config,
            use_local=use_local_db,
            local_port=local_db_port if use_local_db else 8000,
            remove_empty_str=False,
        )

        if use_local_db:
            logger.info(
                f"Initializing LOCAL mode for table '{config['dynamodb']['fb_image_hash_table_name']}' port {local_db_port}"
            )
        else:
            logger.info(
                f"Initializing AWS mode for table '{config['dynamodb']['fb_image_hash_table_name']}' region '{config['aws_region']}')"
            )

        # Ensure table exists (applies to both local and AWS)
        if not hash_manager.table_exists():
            table_name = config["dynamodb"]["fb_image_hash_table_name"]
            logger.error(f"DynamoDB table '{table_name}' not found or accessible.")
            return  # Exit processing if table isn't there

    except NoCredentialsError:
        logger.error(
            "AWS credentials not found. Configure credentials (e.g., ~/.aws/credentials, environment variables, IAM role)."
        )
        sys.exit(1)
    except ClientError as e:
        logger.error(f"AWS ClientError during initialization: {e}", exc_info=True)
        sys.exit(1)
    except Exception as e:
        logger.error(
            f"Failed to initialize AWS clients or DynamoDB manager: {e}", exc_info=True
        )
        sys.exit(1)

    # --- List S3 Objects and Identify Multi-Creative Archives ---
    logger.info("Listing S3 objects to identify archives...")
    paginator = s3_client.get_paginator("list_objects_v2")
    objects_by_archive_id = defaultdict(list)
    all_ad_archive_ids = set()
    processed_object_count = 0  # Renamed for clarity
    start_time = time.time()

    try:
        for page in paginator.paginate(Bucket=bucket_name, Prefix=s3_prefix):
            if "Contents" not in page:
                continue
            for obj in page["Contents"]:
                s3_key = obj["Key"]
                # Basic filtering for common image/video types
                if not any(
                    s3_key.lower().endswith(ext)
                    for ext in [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".mp4", ".mov"]
                ):
                    continue

                ad_archive_id, ad_creative_id = extract_ids_from_key(
                    s3_key, expected_key_format
                )

                # Stricter validation for extracted IDs
                is_valid_archive_id = (
                    ad_archive_id
                    and isinstance(ad_archive_id, str)
                    and len(ad_archive_id.strip()) > 0
                )
                is_valid_creative_id = (
                    ad_creative_id
                    and isinstance(ad_creative_id, str)
                    and len(ad_creative_id.strip()) > 0
                )

                if not is_valid_archive_id or not is_valid_creative_id:
                    logger.debug(  # Use debug level for potentially noisy logs
                        f"Could not extract valid, non-empty ArchiveID ('{ad_archive_id}') "
                        f"and CreativeID ('{ad_creative_id}') from S3 key: {s3_key}. Skipping."
                    )
                    continue

                all_ad_archive_ids.add(ad_archive_id)
                objects_by_archive_id[ad_archive_id].append(
                    {"key": s3_key, "creative_id": ad_creative_id}
                )
                processed_object_count += 1

                if limit and processed_object_count >= limit:
                    logger.info(f"Reached processing limit of {limit} S3 objects.")
                    break  # Stop processing objects
            if limit and processed_object_count >= limit:
                break  # Stop paginating

    except ClientError as e:
        logger.error(f"Error listing S3 objects: {e}", exc_info=True)
        return
    except Exception as e:
        logger.error(f"Unexpected error during S3 listing: {e}", exc_info=True)
        return

    elapsed_time = time.time() - start_time
    logger.info(
        f"Found {len(all_ad_archive_ids)} unique AdArchiveIDs from {processed_object_count} listed objects in {elapsed_time:.2f}s."
    )

    # Identify archives with more than one creative (potential for different hashes)
    multi_creative_archives = {
        archive_id
        for archive_id, objects in objects_by_archive_id.items()
        if len(objects) > 1
    }
    logger.info(
        f"Found {len(multi_creative_archives)} AdArchiveIDs with more than one creative."
    )

    # Create list of images to process (only those in multi-creative archives)
    images_to_process = []
    for archive_id in multi_creative_archives:
        images_to_process.extend(objects_by_archive_id[archive_id])

    if not images_to_process:
        logger.info("No images found belonging to multi-creative archives. Exiting.")
        return

    logger.info(
        f"Identified {len(images_to_process)} images belonging to multi-creative archives to process."
    )

    # --- Process Images Concurrently ---
    hashes_to_write = []
    processed_image_count = 0
    failed_image_count = 0
    future_to_info: dict[Any, str] = {}  # Map future to s3_key for logging

    # Setup Rich progress bar
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        MofNCompleteColumn(),
        TimeElapsedColumn(),
        TimeRemainingColumn(),
        transient=False,  # Keep progress bar after completion
    ) as progress:
        task_id = progress.add_task("Processing images", total=len(images_to_process))

        try:
            # Use ThreadPoolExecutor for I/O bound tasks (downloading from S3)
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit tasks
                for image_info in images_to_process:
                    s3_key = image_info["key"]
                    # Re-extract archive_id to ensure consistency, or retrieve if stored reliably
                    extracted_archive_id, _ = extract_ids_from_key(
                        s3_key, expected_key_format
                    )
                    ad_creative_id = image_info["creative_id"]

                    # Double check IDs before submitting (belt and suspenders)
                    if not extracted_archive_id or not ad_creative_id:
                        logger.warning(
                            f"Skipping submission for key {s3_key} due to missing IDs in image_info or re-extraction."
                        )
                        progress.update(
                            task_id, advance=1
                        )  # Advance progress even if skipped
                        failed_image_count += 1
                        continue

                    # *** UPDATED CALL: Pass hash_manager=None or adjust args if needed ***
                    future = executor.submit(
                        process_single_image,  # Function is now defined in this file
                        s3_client,
                        bucket_name,
                        s3_key,
                        extracted_archive_id,
                        ad_creative_id,
                        None,  # Pass None for hash_manager as it's not used by the moved function
                    )
                    future_to_info[future] = (
                        s3_key  # Store s3_key for context on error/completion
                    )

                # Process results as they complete
                for future in as_completed(future_to_info):
                    s3_key = future_to_info[future]
                    try:
                        image_hash_record = future.result()
                        if image_hash_record:
                            hashes_to_write.append(image_hash_record)
                            processed_image_count += 1

                            # Write to DynamoDB in batches
                            if len(hashes_to_write) >= BATCH_SIZE:
                                logger.debug(
                                    f"Writing batch of {len(hashes_to_write)} hashes to DynamoDB..."
                                )
                                success_count, failure_count = (
                                    hash_manager.batch_insert_items(hashes_to_write)
                                )
                                logger.info(
                                    f"Batch write result: Successful={success_count}, Failed/Skipped={failure_count}"
                                )
                                if failure_count > 0:
                                    logger.warning(
                                        f"{failure_count} items failed pre-processing or batch write."
                                    )
                                hashes_to_write = []  # Clear batch
                        else:
                            # Hash calculation or download failed (already logged in process_single_image)
                            failed_image_count += 1

                    except Exception as exc:
                        logger.error(
                            f"Error processing future for S3 key {s3_key}: {exc}",
                            exc_info=True,
                        )
                        failed_image_count += 1
                    finally:
                        progress.update(
                            task_id, advance=1
                        )  # Advance progress bar for each completed future

            # Write any remaining hashes after the loop finishes
            if hashes_to_write:
                logger.info(
                    f"Writing final batch of {len(hashes_to_write)} hashes to DynamoDB..."
                )
                success_count, failure_count = hash_manager.batch_insert_items(
                    hashes_to_write
                )
                logger.info(
                    f"Final batch write result: Successful={success_count}, Failed/Skipped={failure_count}"
                )
                if failure_count > 0:
                    logger.warning(
                        f"{failure_count} items failed pre-processing or batch write in final batch."
                    )

        except KeyboardInterrupt:
            logger.warning(
                "Keyboard interrupt received. Shutting down workers and attempting to write remaining hashes..."
            )
            # Executor shutdown happens automatically in __exit__
            if hashes_to_write:
                logger.info(
                    f"Writing final batch of {len(hashes_to_write)} hashes due to interrupt..."
                )
                success_count, failure_count = hash_manager.batch_insert_items(
                    hashes_to_write
                )
                logger.info(
                    f"Final batch write result: Successful={success_count}, Failed/Skipped={failure_count}"
                )
            logger.info("Processing stopped by user.")
            # Allow finally block to run
        except Exception as e:
            logger.error(
                f"An unexpected error occurred during concurrent processing: {e}",
                exc_info=True,
            )
            # Allow finally block to run
        finally:
            # Ensure progress bar stops cleanly
            if not progress.finished:
                progress.update(task_id, completed=progress.tasks[task_id].completed)

    logger.info("Finished processing.")
    logger.info(
        f"Successfully processed and attempted write for {processed_image_count} images."
    )
    logger.info(f"Failed to process/hash {failed_image_count} images.")


# --- Main Execution ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Calculate perceptual hashes for FB ad images in S3 and store in DynamoDB."
    )
    parser.add_argument(
        "--local-db", action="store_true", help="Use local DynamoDB instance."
    )
    parser.add_argument(
        "--db-port", type=int, default=8000, help="Port for local DynamoDB instance."
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=os.cpu_count() or 4,
        help="Number of concurrent workers for processing.",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limit the number of S3 objects listed for processing (for testing).",
    )
    parser.add_argument("--debug", action="store_true", help="Enable debug logging.")

    args = parser.parse_args()

    # Set up basic logging immediately for RichHandler compatibility
    logging.basicConfig(
        level="INFO",
        format="%(message)s",
        datefmt="[%X]",
        handlers=[RichHandler(rich_tracebacks=True, show_path=False)],
    )
    logger = logging.getLogger()  # Get root logger

    if args.debug:
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:
            if isinstance(handler, RichHandler):
                handler.setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled.")
    else:
        # Keep libraries quieter in INFO mode
        logging.getLogger("botocore").setLevel(logging.WARNING)
        logging.getLogger("boto3").setLevel(logging.WARNING)
        logging.getLogger("urllib3").setLevel(logging.WARNING)
        logging.getLogger("PIL").setLevel(logging.WARNING)

    # --- Load Configuration ---
    try:
        # Assuming load_config doesn't strictly need a date for this script's purpose
        # Config should be passed in or use defaults
        # config = load_config('01/01/70')  # Using default date
        # Get region from environment or use default
        config = {
            "aws_region": (
                os.getenv("AWS_REGION")
                or os.getenv("LEXGENIUS_AWS_REGION")
                or os.getenv("REGION_NAME")
                or "us-west-2"
            )
        }
        logger.warning("Using default config - load_config has been removed")
        if not config.get("dynamodb", {}).get("fb_image_hash_table_name"):
            raise ValueError("Missing 'dynamodb.fb_image_hash_table_name' in config.")
    except FileNotFoundError:
        logger.error(
            "Configuration file not found. Ensure config exists at expected location for load_config."
        )
        sys.exit(1)
    except ValueError as ve:
        logger.error(f"Configuration Error: {ve}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}", exc_info=True)
        sys.exit(1)

    # --- Determine Bucket Name (from config ONLY) ---
    config_bucket_key = "bucket_name"
    bucket_to_use = config.get(config_bucket_key)
    if not bucket_to_use:
        logger.error(
            f"S3 bucket name must be defined as '{config_bucket_key}' in the configuration file."
        )
        sys.exit(1)
    else:
        logger.info(
            f"Using bucket name '{bucket_to_use}' from configuration key '{config_bucket_key}'."
        )

    # --- Validate Worker Count ---
    if args.workers <= 0:
        logger.error("Number of workers must be positive.")
        sys.exit(1)
    if args.workers > 100:  # Example threshold
        logger.warning(
            f"High number of workers ({args.workers}) requested. Ensure sufficient system resources."
        )

    # --- Run Processing ---
    process_images(
        config=config,
        bucket_name=bucket_to_use,  # Use the bucket name from config
        use_local_db=args.local_db,
        local_db_port=args.db_port,
        max_workers=args.workers,
        limit=args.limit,
    )
