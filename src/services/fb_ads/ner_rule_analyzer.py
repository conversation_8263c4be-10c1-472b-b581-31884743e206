# /src/services/fb_ads/ner_rule_analyzer.py

import argparse
import asyncio
import logging
import os
import re
import sys
import time
from collections import Counter
from concurrent.futures import ProcessPoolExecutor
from pathlib import Path
from typing import List, Tuple, Dict, Optional, Any, Union

import aiohttp
import pandas as pd
import spacy
import torch  # For MPS check for spaCy
# Removed dependency_injector imports - using container-based injection
from rich.console import Console
from rich.logging import RichHandler
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeElapsedColumn, TimeRemainingColumn, track
from rich.table import Table

# --- Project imports ---
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import FBAdServiceError

try:
    # Try new migration helper first
    from src.migration import create_manager_replacement

    _using_new_architecture = True
except ImportError:
    _using_new_architecture = False

try:
    from src.repositories.fb_archive_repository import FBArchiveRepository as FBAdArchiveManager
except ImportError as e:
    print(f"CRITICAL ERROR: Could not import FBAdArchiveManager: {e}")
    sys.exit(1)

# --- Logger setup with Rich ---
logger = logging.getLogger("NerRuleAnalyzer")
if not logger.handlers:
    log_console_stderr = Console(stderr=True)
    rich_handler = RichHandler(
        rich_tracebacks=True, show_time=True, show_level=True, show_path=False,
        console=log_console_stderr, markup=True
    )
    logger.addHandler(rich_handler)
    logger.setLevel(logging.INFO)
    logger.propagate = False

# --- Top-level functions for multiprocessing worker ---
_worker_nlp_models_cache_analyzer = {}


def _is_entity_noisy(entity_text: str, entity_label: str) -> bool:
    """
    Determines if an entity is likely noise based on its text and label.
    You MUST curate the sets NOISY_LOWERCASE_TEXTS, KNOWN_LAW_FIRM_NAMES_LOWER,
    LAW_FIRM_INDICATORS, and KNOWN_SHORT_ACRONYMS based on your specific data and needs.
    """
    text_lower = entity_text.lower().strip()

    # 1. EXACT MATCHES FOR NOISY TEXTS:
    #    Add full, lowercased entity texts here that you ALWAYS consider noise,
    #    regardless of their NER label. Populate this from your CSV reviews.
    NOISY_LOWERCASE_TEXTS = {
        "today", "learn more", "free case evaluation", "submit a claim", "submit a claim today",
        "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten",
        "first", "second", "third", "fourth", "fifth",
        "✅", "🤝", "💰", "⚖", "👉", "🚨", "💥", "#", "click here", "learn now", "sign up",
        "advertisement", "attorney advertising", "attn",
        "january", "february", "march", "april", "may", "june", "july", "august",
        "september", "october", "november", "december",
        "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday",
        "minute", "minutes", "second", "seconds", "hour", "hours", "day", "days",
        "week", "weeks", "month", "months", "year", "years",
        "time", "date", "a free case review",  # Added this again as it appeared in your example list
        "get started", "find out", "act now", "limited time", "call now", "apply now",
        "us", "usa", "u.s.", "u.s.a", "the united states",
        "contact us", "to learn more", "don't wait to file a claim", "free guide",
        "book your free call today", "learn more below and see",
        "free & easy sign", "free online compensation review",
        "research", "qualifies your case",
        "fast & free online compensation review", "your well-being matters: secure",
        "dedicated advocacy: our", "your legal rights matter", "your legal rights matters",
        "our women's health attorneys", "federal court", "casefolio",
        "congressional", "casefoto", "ils", "vad", "justison street wilmington",
        # This might be too specific if it's a real address relevant to one campaign
        "de 19801",
        "the united states district court", "suite 347", "carle place", "ny", "new york", "new york, ny",
        # These GPEs might be too broad
        "los angeles", "tap \"learn more", "twitter", "facebook", "instagram", "tiktok", "pinterest", "youtube",
        "america's leading hair law firm", "america", "l", "register now",
        "we fight to get your money", "america's largest trial law", "potential lawsuit",
        "potential case", "american", "wv", "west virginia",
        "lee st. e. charleston", "wv 25301",
        "spanish", "english", "live(d",
        "nbc", "unite & justice", "unite & join us", "few minutes",
        "breaking news", "main st", "anytown usa", "residence inn", "s festival dr", "anaheim", "house",
        "😨 send", "✅sleep", "covid-19",
        "guardian legal network",
        # !!! IMPORTANT: CONTINUE POPULATING THIS SET BASED ON YOUR CSV REVIEW !!!
        # Add any other phrases you see in your 'EntityText' column that are noise.
    }
    if text_lower in NOISY_LOWERCASE_TEXTS:
        return True

    # 2. NOISY NER LABELS (These labels often indicate noise)
    NOISY_LABELS = {'DATE', 'CARDINAL', 'ORDINAL', 'TIME', 'PERCENT', 'QUANTITY', 'MONEY'}
    if entity_label in NOISY_LABELS:
        return True  # Filter all entities with these labels by default.

    # 3. FILTERING LAW FIRMS (Heuristic - needs careful curation of lists below)
    LAW_FIRM_INDICATORS = {  # Common words/suffixes in law firm names
        "law", "legal", "group", "associates", "attorney", "attorneys", "lawyer", "lawyers",
        "llp", "pllc", "p.a.", "p.c.", "esq", "firm", "counsel", "advocates", "chtd",
        "injury", "&", "partners", "trial lawyers", "center", "institute",
        "shkolnik", "konigsberg", "gentile", "borrelli", "schelkopf", "baum", "eisenhofer",
        "luxenberg", "jez", "nadrich", "shapiro", "salkow", "weiss", "seeger", "frankz", "bryant",
        "mcgonigle", "franklin", "vigna", "anapol", "anastopoulo", "kershaw talley barlow"
        # !!! Add more common law firm name fragments YOU observe !!!
    }
    KNOWN_LAW_FIRM_NAMES_LOWER = {  # Add FULL known firm names (lowercase)
        "morgan & morgan", "shamis & gentile", "leeds brown law", "sokolove law",
        "edelson pc", "edelson", "weitz & luxenberg", "grant & eisenhofer",
        "napoli shkolnik", "ben crump law", "levy konigsberg", "sieger weiss", "seeger weiss",
        "wisner baum", "clarkson law firm", "crosner legal", "sauder schelkopf",
        "vigna law group", "the webb law centre", "baron & budd", "milberg coleman bryant",
        "anapol weiss", "anastopoulo law firm", "hach & rose", "burg simpson", "shapiro legal group",
        "the yost legal group", "jason joy & associates", "grant & eisenhofer p.a."  # Added from your data
        # !!! Add more full law firm names YOU observe !!!
    }
    if text_lower in KNOWN_LAW_FIRM_NAMES_LOWER:
        return True
    if entity_label in {'ORG', 'PERSON'}:  # Law firms are often ORG or PERSON
        words_in_entity = set(re.findall(r'\b\w+\b', text_lower))
        # If the name contains multiple law firm indicators, or ends with one
        if len(words_in_entity.intersection(LAW_FIRM_INDICATORS)) >= 1:
            # Heuristic: if a name is short (e.g., <= 4 words) and contains an indicator, it's likely a firm.
            # Or if it clearly ends with a suffix like "llp", "p.a.", "law group", etc.
            if len(words_in_entity) <= 4 or \
                    any(text_lower.endswith(f" {suffix}") for suffix in
                        ["llp", "pllc", "p.a.", "p.c.", "group", "firm", "associates", "attorneys", "lawyers"]):
                return True
            if "&" in text_lower and len(words_in_entity) <= 4:  # Catches "X & Y" style firm names
                return True

    # 4. SHORT TEXTS (unless known acronyms)
    KNOWN_SHORT_ACRONYMS = {
        "bsa", "lds", "nec", "afff", "eto", "ivf", "pgt", "mdl", "cvs", "w&l", "sce", "cpsc",
        "ny", "ca", "tx", "fl", "il", "dc", "ga", "pa", "nj", "wv", "az", "wa", "de", "co", "nc", "va", "oh",
        # Common state abbrevs
        "gop", "dem", "fda", "epa", "fcc", "ftc", "sec", "doj", "va", "dod", "dhs", "hhs", "hud",
        "ssn", "tin", "ein", "nhtsa", "td", "pnc", "rbc", "ubs", "j&j", "3m", "gm", "ge", "at&t",
        "ors", "cll", "nhl", "voc", "pfoa", "pfos", "gyn", "dnc", "tcpa", "her2", "sars", "hiv", "fed"
        # !!! Add YOUR important short acronyms (3-4 chars usually) here !!!
    }
    if len(text_lower) <= 4:  # Consider texts of 4 chars or less
        if text_lower not in KNOWN_SHORT_ACRONYMS:
            # If it's purely non-alphanumeric (e.g. "!!", "$$", "##")
            if not any(char.isalnum() for char in text_lower):
                return True
            # If it's very short (1-2 chars) and not a known acronym, it's likely noise
            if len(text_lower) <= 2:
                return True
            # If it's 3-4 chars, not an acronym, and not a clearly recognized word (this part is harder to automate perfectly)
            # For now, we assume if it's not a known acronym and short, and not caught by other rules, it might be noise if too frequent.
            # This part might need more sophisticated dictionary checks if it's too aggressive or too lenient.
            # A simple check: if it contains no vowels (and not a known acronym), might be noise (e.g. "rglr")
            if not re.search(r'[aeiou]', text_lower, re.IGNORECASE):
                return True

    # 5. NOISY HASHTAGS
    if text_lower.startswith("#") and len(text_lower) > 1:
        hashtag_content = text_lower[1:]
        # Common noisy hashtag content
        if hashtag_content in NOISY_LOWERCASE_TEXTS or \
                any(generic_ht in hashtag_content for generic_ht in
                    ["lawsuit", "legal", "attorney", "news", "breaking", "update", "justice", "claim", "claims",
                     "compensation", "free", "now", "ad", "promo", "ad"]):
            return True
        # Very short hashtags unless they are known acronyms
        if len(hashtag_content) <= 3 and hashtag_content not in KNOWN_SHORT_ACRONYMS:
            return True

    return False


def _init_worker_spacy_model_for_analyzer(ner_model_name: str) -> spacy.language.Language:
    use_mps = False
    if torch.backends.mps.is_available() and torch.backends.mps.is_built():
        if "_trf" in ner_model_name.lower():  # Transformer models benefit more
            use_mps = True
            # print(f"Worker PID {os.getpid()}: Preferring GPU (MPS) for spaCy model '{ner_model_name}'.")

    if use_mps:
        spacy.prefer_gpu()
    else:
        spacy.require_cpu()
        # print(f"Worker PID {os.getpid()}: Requiring CPU for spaCy model '{ner_model_name}'.")

    try:
        # Exclude components not needed for NER to speed up loading and processing
        nlp = spacy.load(ner_model_name, exclude=["tagger", "parser", "lemmatizer", "attribute_ruler"])
    except ValueError:  # If exclude fails for some models, load normally
        nlp = spacy.load(ner_model_name)
    return nlp


def _worker_process_ner_chunk_analyzer(
        ad_id_text_campaign_chunk: List[Tuple[str, str, str]],  # AdArchiveID, CombinedText, RuleMatchedCampaign
        ner_model_name: str,
        spacy_pipe_batch_size: int
) -> List[Dict[str, Union[str, int]]]:
    worker_pid = os.getpid()
    cache_key = (worker_pid, ner_model_name)

    if cache_key not in _worker_nlp_models_cache_analyzer:
        _worker_nlp_models_cache_analyzer[cache_key] = _init_worker_spacy_model_for_analyzer(ner_model_name)

    nlp = _worker_nlp_models_cache_analyzer[cache_key]

    # Unpack: AdArchiveID, CombinedText, RuleMatchedCampaign
    texts_to_process = [item[1] for item in ad_id_text_campaign_chunk]
    ad_ids_for_texts = [item[0] for item in ad_id_text_campaign_chunk]
    campaigns_for_texts = [item[2] for item in ad_id_text_campaign_chunk]  # This is now RuleMatchedCampaign

    processed_ner_results = []
    for i, doc in enumerate(nlp.pipe(texts_to_process, batch_size=spacy_pipe_batch_size)):
        ad_id = ad_ids_for_texts[i]
        campaign_name = campaigns_for_texts[i]  # RuleMatchedCampaign
        for ent in doc.ents:
            ent_text_normalized = ent.text.lower().strip()
            if ent_text_normalized:
                processed_ner_results.append({
                    "AdArchiveID": ad_id,
                    "RuleMatchedCampaign": campaign_name,
                    "EntityText": ent_text_normalized,
                    "EntityLabel": ent.label_,
                })
    return processed_ner_results


class NerRuleAnalyzer(AsyncServiceBase):
    def __init__(self,
                 config: Dict[str, Any],
                 session: aiohttp.ClientSession,
                 ner_model_name: str,
                 spacy_pipe_batch_size: int,
                 use_local_dynamodb: bool,
                 dynamodb_scan_workers: Optional[int],
                 ner_processing_workers: Optional[int]):

        # Initialize AsyncServiceBase
        logger_instance = logging.getLogger(__name__)
        super().__init__(logger_instance, config)

        self.session = session
        self.ner_model_name = ner_model_name
        self.spacy_pipe_batch_size = spacy_pipe_batch_size
        self.use_local_dynamodb = use_local_dynamodb

        self.dynamodb_scan_workers = dynamodb_scan_workers if dynamodb_scan_workers is not None else (
                os.cpu_count() or 4)
        self.ner_processing_workers = ner_processing_workers if ner_processing_workers is not None else (
                os.cpu_count() or 4)

        self.console = Console()
        self.ad_archive: Optional[FBAdArchiveManager] = None
        # self.nlp_model is not initialized here; workers get their own.

        try:
            if _using_new_architecture:
                self.ad_archive = create_manager_replacement('FBArchiveManager', config)
            else:
                self.ad_archive = FBAdArchiveManager(self.config, use_local=self.use_local_dynamodb, no_filter=True)
                self.log_info(
                    f"FBAdArchiveManager initialized for table '[cyan]{self.ad_archive.table_name}[/cyan]' (local=[yellow]{self.use_local_dynamodb}[/yellow])")
        except Exception as e:
            self.log_error(f"FBAdArchiveManager initialization failed: {e}")
            self.console.print(
                Panel(f"[bold red]Error:[/bold red] Failed to initialize FBAdArchiveManager: {e}. Check config.",
                      border_style="red"))
            raise

        self.log_info(f"Using spaCy model for analysis: [bright_blue]{self.ner_model_name}[/bright_blue]")
        self.log_info(f"spaCy nlp.pipe batch size: [blue]{self.spacy_pipe_batch_size}[/blue]")
        self.log_info(f"DynamoDB scan workers for fetching details: [blue]{self.dynamodb_scan_workers}[/blue]")
        self.log_info(f"NER processing workers for analysis: [blue]{self.ner_processing_workers}[/blue]")

    async def _execute_action(self, data: Any) -> Any:
        """Execute NerRuleAnalyzer actions."""
        if isinstance(data, dict):
            action = data.get('action')
            if action == 'load_input_csv':
                return self.load_input_csv(data['csv_path'])
            elif action == 'fetch_additional_ad_details':
                return await self.fetch_additional_ad_details(data['ad_ids'])
            elif action == 'prepare_data_for_ner':
                return self._prepare_data_for_ner(data['input_df'], data['details_df'])
            elif action == 'perform_ner_on_ads':
                return await self.perform_ner_on_ads(data['ads_for_ner'])
            elif action == 'analyze_entities_by_category':
                self.analyze_entities_by_category(
                    data['ner_df'],
                    data.get('top_n_entities', 20),
                    data.get('output_dir', 'ner_analysis_reports')
                )
                return None
            elif action == 'run_analysis':
                await self.run_analysis(
                    data['input_csv_path'],
                    data['output_dir'],
                    data['top_n']
                )
                return None
        raise FBAdServiceError("Invalid action data provided to NerRuleAnalyzer")

    def load_input_csv(self, csv_path: str) -> pd.DataFrame:
        self.console.print(Panel(f"[bold cyan]Loading Input CSV: {csv_path}[/]", border_style="cyan"))
        try:
            df = pd.read_csv(csv_path, dtype={'AdArchiveID': str})
            required_cols = ['AdArchiveID', 'RuleMatchedCampaign']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                self.log_error(f"Input CSV missing required columns: {missing_cols}")
                self.console.print(f"[bold red]Error: Input CSV '{csv_path}' is missing columns: {missing_cols}.[/]")
                return pd.DataFrame()

            df = df[required_cols].copy()
            df.dropna(subset=['AdArchiveID', 'RuleMatchedCampaign'], inplace=True)
            df['RuleMatchedCampaign'] = df['RuleMatchedCampaign'].astype(str)
            df = df[df['RuleMatchedCampaign'] != '']

            if df.empty:
                self.log_warning(f"No valid rows with RuleMatchedCampaign found in {csv_path}.")
                self.console.print(
                    f"[yellow]Warning: No valid rows with RuleMatchedCampaign found in {csv_path}.[/yellow]")
                return pd.DataFrame()

            self.log_info(f"Loaded {len(df)} rows with valid RuleMatchedCampaign from {csv_path}. "
                          f"Unique AdArchiveIDs: {df['AdArchiveID'].nunique()}, "
                          f"Unique RuleMatchedCampaigns: {df['RuleMatchedCampaign'].nunique()}")
            self.console.print(f"[green]✓ Loaded {len(df):,} rows with RuleMatchedCampaign, "
                               f"{df['AdArchiveID'].nunique():,} unique AdArchiveIDs, "
                               f"{df['RuleMatchedCampaign'].nunique():,} unique RuleMatchedCampaigns.[/green]")
            return df
        except FileNotFoundError:
            self.log_error(f"Input CSV file not found: {csv_path}")
            self.console.print(f"[bold red]Error: Input CSV file not found: {csv_path}[/]")
            return pd.DataFrame()
        except Exception as e:
            self.log_error(f"Error loading input CSV {csv_path}: {e}")
            self.console.print(f"[bold red]Error loading input CSV {csv_path}: {e}[/]")
            return pd.DataFrame()

    async def fetch_additional_ad_details(self, ad_ids: List[str]) -> pd.DataFrame:
        if not ad_ids:
            return pd.DataFrame(columns=['AdArchiveID', 'Title', 'Body', 'ImageText'])

        self.console.print(f"[cyan]Fetching Title, Body, ImageText for {len(ad_ids):,} ads from DynamoDB...[/]")

        fields_to_project = ['AdArchiveID', 'Title', 'Body', 'ImageText']
        projection_expression = ", ".join(fields_to_project)

        all_items_from_db = []
        start_time = time.time()

        self.console.print(
            f"[dim]Attempting to fetch details using a full table scan (projection: {fields_to_project})...[/dim]")
        try:
            if self.ad_archive is None:  # Should be initialized in __init__
                self.log_error("FBAdArchiveManager not initialized. Cannot fetch details.")
                self.console.print("[bold red]Error: FBAdArchiveManager not available for fetching details.[/]")
                return pd.DataFrame(columns=['AdArchiveID'] + fields_to_project[1:])

            if self.use_local_dynamodb:
                from tqdm.asyncio import tqdm_asyncio as local_tqdm_asyncio
                total_segments = self.dynamodb_scan_workers

                async def scan_segment_local(segment, total_segments_val, projection_expr_val):
                    segment_items_list = []
                    last_evaluated_key = None
                    try:
                        while True:
                            if not self.ad_archive.table: return []  # type: ignore
                            scan_params = {'Segment': segment, 'TotalSegments': total_segments_val,
                                           'ProjectionExpression': projection_expr_val}
                            if last_evaluated_key: scan_params['ExclusiveStartKey'] = last_evaluated_key
                            response = await asyncio.to_thread(self.ad_archive.scan_segment,
                                                               **scan_params)
                            segment_items_list.extend(response.get('Items', []))
                            last_evaluated_key = response.get('LastEvaluatedKey')
                            if not last_evaluated_key: break
                        return segment_items_list
                    except Exception as e_scan:
                        self.log_error(f"DynamoDB scan worker {segment} error: {e_scan}")
                        return []

                scan_tasks = [scan_segment_local(i, total_segments, projection_expression) for i in
                              range(total_segments)]
                segment_results = await local_tqdm_asyncio.gather(*scan_tasks, desc="Scanning DynamoDB Segments",
                                                                  unit="segment", ncols=100)
                for result_list_val in segment_results: all_items_from_db.extend(result_list_val)
            else:  # AWS
                with self.console.status("[bold green]Scanning AWS DynamoDB table for details...", spinner="dots"):
                    all_items_from_db = await asyncio.to_thread(self.ad_archive.scan_all,
                                                                projection_expression=projection_expression)

            scan_duration = time.time() - start_time
            self.console.print(
                f"[green]✓ DynamoDB scan for details finished in {scan_duration:.2f}s, found {len(all_items_from_db):,} items raw.[/]")

            if not all_items_from_db:
                self.console.print("[yellow]Warning: No items found in DynamoDB scan for details.[/yellow]")
                return pd.DataFrame(columns=['AdArchiveID'] + fields_to_project[1:])

            details_df = pd.DataFrame(all_items_from_db)
            if 'AdArchiveID' not in details_df.columns:  # Should always be there due to projection
                self.console.print("[bold red]Critical: AdArchiveID missing from DynamoDB scan results.[/]")
                return pd.DataFrame(columns=['AdArchiveID'] + fields_to_project[1:])
            details_df['AdArchiveID'] = details_df['AdArchiveID'].astype(str)

            details_df = details_df[details_df['AdArchiveID'].isin(ad_ids)]

            for col in ['Title', 'Body', 'ImageText']:
                if col not in details_df.columns:
                    details_df[col] = ''
                else:
                    details_df[col] = details_df[col].fillna('').astype(str)

            self.log_info(f"Fetched details for {len(details_df)} of the requested {len(ad_ids)} ads.")
            self.console.print(
                f"[green]✓ Retrieved details for {len(details_df)}/{len(ad_ids)} ads from DynamoDB.[/green]")
            return details_df[['AdArchiveID'] + [col for col in fields_to_project if col != 'AdArchiveID']]

        except Exception as e:
            self.log_error(f"Error fetching ad details from DynamoDB: {e}")
            self.console.print(f"[bold red]Error fetching ad details: {e}[/]")
            return pd.DataFrame(columns=['AdArchiveID'] + fields_to_project[1:])

    def _prepare_data_for_ner(self, input_df: pd.DataFrame, details_df: pd.DataFrame) -> List[Tuple[str, str, str]]:
        self.console.print("[cyan]Combining text fields for NER processing...[/]")
        merged_df = pd.merge(input_df, details_df, on='AdArchiveID', how='left')

        for col in ['Title', 'Body', 'ImageText']:
            if col not in merged_df.columns:
                merged_df[col] = ''
            else:
                merged_df[col] = merged_df[col].fillna('').astype(str)

        ads_for_ner_processing = []
        if 'RuleMatchedCampaign' not in merged_df.columns:
            self.log_error("RuleMatchedCampaign column lost during merge. This should not happen.")
            self.console.print("[bold red]Critical error: RuleMatchedCampaign column lost during data preparation.[/]")
            return []

        merged_df.dropna(subset=['RuleMatchedCampaign'], inplace=True)

        for _, row in track(merged_df.iterrows(), description="Preparing texts", total=len(merged_df),
                            console=self.console):
            combined_text_parts = [
                str(row.get('Title', '')).strip(),
                str(row.get('Body', '')).strip(),
                str(row.get('ImageText', '')).strip()
            ]
            combined_text = " | ".join(part for part in combined_text_parts if part)

            if combined_text:
                ads_for_ner_processing.append((
                    str(row['AdArchiveID']),
                    combined_text,
                    str(row['RuleMatchedCampaign'])
                ))
        self.console.print(f"[green]✓ Prepared {len(ads_for_ner_processing):,} ads with combined text for NER.[/green]")
        return ads_for_ner_processing

    async def perform_ner_on_ads(self, ads_for_ner: List[Tuple[str, str, str]]) -> pd.DataFrame:
        if not ads_for_ner:
            self.console.print("[yellow]No ads to process for NER.[/yellow]")
            return pd.DataFrame(columns=["AdArchiveID", "RuleMatchedCampaign", "EntityText", "EntityLabel"])

        self.console.print(Panel(f"[bold cyan]Performing NER on {len(ads_for_ner):,} Ad Texts[/]", border_style="cyan"))

        all_ner_results_list = []
        num_total_items = len(ads_for_ner)
        # Adjusted chunking logic to avoid issues with very small num_total_items
        if num_total_items > 0:
            items_per_task_submission = min(
                max(50, num_total_items // (self.ner_processing_workers * 4 if self.ner_processing_workers > 0 else 1)),
                1000)
            if items_per_task_submission == 0: items_per_task_submission = num_total_items  # Ensure at least 1 task if items exist
        else:  # Should be caught by early return, but as safeguard
            items_per_task_submission = 1

        self.log_info(
            f"Distributing {num_total_items:,} ads to [blue]{self.ner_processing_workers}[/blue] NER workers, "
            f"approx. {items_per_task_submission:,} ads per submitted task chunk.")

        loop = asyncio.get_running_loop()
        progress_columns = [
            SpinnerColumn(), TextColumn("[progress.description]{task.description}"), BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
            TextColumn("({task.completed} of {task.total} chunks)"),
            TimeRemainingColumn(), TimeElapsedColumn()
        ]

        with ProcessPoolExecutor(max_workers=self.ner_processing_workers) as executor:
            futures = []
            if num_total_items > 0:  # Only create futures if there are items
                for i in range(0, num_total_items, items_per_task_submission):
                    chunk_to_submit = ads_for_ner[i:i + items_per_task_submission]
                    if chunk_to_submit:
                        futures.append(loop.run_in_executor(executor, _worker_process_ner_chunk_analyzer,
                                                            chunk_to_submit, self.ner_model_name,
                                                            self.spacy_pipe_batch_size))
            if not futures:
                self.console.print("[yellow]No NER tasks submitted to process pool (likely no data).[/yellow]")
            else:
                self.console.print(f"\n[cyan]Submitting {len(futures)} NER tasks to process pool...[/cyan]")
                with Progress(*progress_columns, console=self.console, transient=False) as progress_bar:
                    task_id = progress_bar.add_task("Processing NER chunks...", total=len(futures))
                    for future in asyncio.as_completed(futures):
                        try:
                            chunk_results = await future
                            if chunk_results:
                                all_ner_results_list.extend(chunk_results)
                        except Exception as e:
                            self.log_error(f"Error processing an NER chunk: {e}")
                            self.console.print(f"[bold red]Error in NER worker: {e}[/]")
                        progress_bar.update(task_id, advance=1)

        if not all_ner_results_list:
            self.console.print("[yellow]No NER entities found across all ads.[/yellow]")
            return pd.DataFrame(columns=["AdArchiveID", "RuleMatchedCampaign", "EntityText", "EntityLabel"])

        self.console.print(f"\n[cyan]Consolidating {len(all_ner_results_list):,} found NER entities...[/cyan]")
        ner_df = pd.DataFrame(all_ner_results_list)
        self.console.print(f"[green]✓ NER processing complete. Found {len(ner_df):,} total entities.[/green]")
        return ner_df

    def analyze_entities_by_category(self, ner_df: pd.DataFrame, top_n_entities: int = 20,
                                     output_dir: str = "ner_analysis_reports") -> None:
        if ner_df.empty:
            self.console.print("[yellow]NER results DataFrame is empty. No analysis to perform.[/yellow]")
            Path(output_dir).mkdir(parents=True, exist_ok=True)
            summary_report_path = Path(output_dir) / "ner_rule_matched_campaign_entity_summary.csv"
            pd.DataFrame(columns=[
                "RuleMatchedCampaign", "Rank", "EntityText", "EntityLabel", "Frequency",
                "NumAdsInCampaignWithMeaningfulEntities", "NormalizedFrequencyPerAd"
            ]).to_csv(summary_report_path, index=False, encoding='utf-8')
            self.console.print(f"\n[yellow]Empty summary report saved to: {summary_report_path}[/yellow]")
            return

        if 'RuleMatchedCampaign' not in ner_df.columns:
            self.console.print(
                "[bold red]Error: 'RuleMatchedCampaign' column missing from NER results. Cannot analyze.[/]")
            self.log_error("RuleMatchedCampaign column missing in ner_df for analysis.")
            return

        self.console.print(
            Panel("[bold cyan]Analyzing Entity Frequencies per RuleMatchedCampaign (Noise Filtered)[/]",
                  border_style="cyan"))

        Path(output_dir).mkdir(parents=True, exist_ok=True)
        summary_report_path = Path(output_dir) / "ner_rule_matched_campaign_entity_summary.csv"
        all_campaign_reports = []

        for campaign_name, group_df in ner_df.groupby('RuleMatchedCampaign'):
            self.console.print(f"\n[bold magenta]--- Campaign: {campaign_name} ---[/bold magenta]")

            if group_df.empty:
                self.console.print("[dim]No entities found for this campaign (empty group).[/dim]")
                continue

            try:
                # Ensure EntityText and EntityLabel are strings before passing to _is_entity_noisy
                is_noisy_series = group_df.apply(
                    lambda row: _is_entity_noisy(str(row.get('EntityText', '')), str(row.get('EntityLabel', ''))),
                    axis=1
                )
                filtered_group_df = group_df[~is_noisy_series]
            except Exception as e:
                self.log_error(f"Error applying noise filter for campaign '{campaign_name}': {e}")
                self.console.print(
                    f"[bold red]Error filtering noise for {campaign_name}. Skipping this campaign's detailed analysis.[/]")
                continue

            if filtered_group_df.empty:
                self.console.print(
                    "[dim]No meaningful (non-noise) entities found for this campaign after filtering.[/dim]")
                continue

            entity_counts = Counter(zip(filtered_group_df['EntityText'], filtered_group_df['EntityLabel']))

            if not entity_counts:
                self.console.print("[dim]No entities to count for this campaign after filtering.[/dim]")
                continue

            num_ads_in_campaign_with_meaningful_entities = filtered_group_df['AdArchiveID'].nunique()

            if num_ads_in_campaign_with_meaningful_entities == 0:
                self.console.print(
                    "[dim]Although entities were found, they were all filtered as noise, resulting in zero ads with meaningful entities.[/dim]")
                continue

            table = Table(
                title=f"Top {top_n_entities} Meaningful Entities for '{campaign_name}' (Total Ads with Meaningful Entities: {num_ads_in_campaign_with_meaningful_entities})")
            table.add_column("Rank", style="dim")
            table.add_column("Entity Text", style="green", no_wrap=False, max_width=50)
            table.add_column("Entity Label", style="blue")
            table.add_column("Frequency (Post-Filter)", style="magenta", justify="right")
            table.add_column("Normalized Freq. (per Ad with Meaningful Entities)", style="cyan", justify="right")

            for rank, ((text, label), count) in enumerate(entity_counts.most_common(top_n_entities), 1):
                normalized_freq = count / num_ads_in_campaign_with_meaningful_entities
                table.add_row(str(rank), text, label, str(count), f"{normalized_freq:.3f}")
                all_campaign_reports.append({
                    "RuleMatchedCampaign": campaign_name,
                    "Rank": rank,
                    "EntityText": text,
                    "EntityLabel": label,
                    "Frequency": count,
                    "NumAdsInCampaignWithMeaningfulEntities": num_ads_in_campaign_with_meaningful_entities,
                    "NormalizedFrequencyPerAd": normalized_freq
                })
            self.console.print(table)

        if all_campaign_reports:
            summary_df = pd.DataFrame(all_campaign_reports)
            summary_df.sort_values(by=["RuleMatchedCampaign", "Rank"], inplace=True)
            summary_df.to_csv(summary_report_path, index=False, encoding='utf-8')
            self.console.print(
                f"\n[green]✓ Comprehensive meaningful entity summary report saved to: {summary_report_path}[/green]")
        else:
            self.console.print(
                "\n[yellow]No meaningful entity data to save in the summary report after noise filtering.[/yellow]")
            pd.DataFrame(columns=[
                "RuleMatchedCampaign", "Rank", "EntityText", "EntityLabel", "Frequency",
                "NumAdsInCampaignWithMeaningfulEntities", "NormalizedFrequencyPerAd"
            ]).to_csv(summary_report_path, index=False, encoding='utf-8')

    async def run_analysis(self, input_csv_path: str, output_dir: str, top_n: int):
        self.console.print(Panel("[bold green]Starting NER Rule Analyzer Pipeline[/]", border_style="green"))

        input_df = self.load_input_csv(input_csv_path)
        if input_df.empty:
            self.console.print("[bold red]Pipeline aborted: Failed to load input CSV.[/]")
            return

        unique_ad_ids = input_df['AdArchiveID'].unique().tolist()
        if not unique_ad_ids:
            self.console.print("[yellow]No AdArchiveIDs found in the input CSV. Nothing to process.[/yellow]")
            empty_ner_df = pd.DataFrame(columns=["AdArchiveID", "RuleMatchedCampaign", "EntityText", "EntityLabel"])
            self.analyze_entities_by_category(empty_ner_df, top_n_entities=top_n, output_dir=output_dir)
            return

        details_df = await self.fetch_additional_ad_details(unique_ad_ids)

        ads_to_process_for_ner = self._prepare_data_for_ner(input_df, details_df)
        if not ads_to_process_for_ner:
            self.console.print(
                "[yellow]No data prepared for NER processing. Might be due to missing text or failed fetching.[/yellow]")
            empty_ner_df = pd.DataFrame(columns=["AdArchiveID", "RuleMatchedCampaign", "EntityText", "EntityLabel"])
            self.analyze_entities_by_category(empty_ner_df, top_n_entities=top_n, output_dir=output_dir)
            return

        ner_results_df = await self.perform_ner_on_ads(ads_to_process_for_ner)
        self.analyze_entities_by_category(ner_results_df, top_n_entities=top_n, output_dir=output_dir)
        self.console.print(Panel("[bold green]NER Rule Analyzer Pipeline Finished[/]", border_style="green"))


async def main_analyzer_async(args):
    console_main = Console()
    config = {}
    try:
        if args.local_dynamodb:
            config["dynamodb_endpoint_url"] = os.getenv("DYNAMODB_ENDPOINT_URL", "http://localhost:8000")
        config["aws_access_key_id"] = os.getenv("AWS_ACCESS_KEY_ID")
        config["aws_secret_access_key"] = os.getenv("AWS_SECRET_ACCESS_KEY")
        config["region_name"] = (
                os.getenv("AWS_REGION") or
                os.getenv("LEXGENIUS_AWS_REGION") or
                os.getenv("REGION_NAME") or
                "us-west-2"
        )
        config["fb_ad_archive_table_name"] = os.getenv("FB_AD_ARCHIVE_TABLE_NAME", "FBAdArchive")
    except Exception as e:
        logger.error(f"Config setup from environment variables failed: {e}.", exc_info=True)
        console_main.print(Panel(f"[bold red]Config setup failed:[/bold red] {e}", border_style="red"))
        return

    async with aiohttp.ClientSession() as session:
        try:
            analyzer = NerRuleAnalyzer(
                config=config,
                session=session,
                ner_model_name=args.ner_model,
                spacy_pipe_batch_size=args.spacy_batch_size,
                use_local_dynamodb=args.local_dynamodb,
                dynamodb_scan_workers=args.ddb_scan_workers,
                ner_processing_workers=args.ner_workers
            )
            await analyzer.run_analysis(
                input_csv_path=args.input_csv,
                output_dir=args.output_dir,
                top_n=args.top_n_entities
            )
        except Exception as e:
            logger.critical(f"Critical error during NER rule analysis pipeline: {e}", exc_info=True)
            console_main.print(Panel(f"[bold red]CRITICAL PIPELINE ERROR:[/bold red] {e}", border_style="red"))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Analyze NER entities in categorized ads to suggest rule improvements.")
    parser.add_argument('input_csv', type=str, help="Path to the input CSV file (e.g., 1_rules_categorized_full.csv).")
    parser.add_argument('--local-dynamodb', action='store_true', default=True,
                        help="Use local DynamoDB (default: True).")
    parser.add_argument('--aws-dynamodb', action='store_false', dest='local_dynamodb',
                        help="Use AWS DynamoDB.")
    parser.add_argument('--ddb-scan-workers', type=int, default=None,
                        help="Number of parallel workers for DynamoDB scan (local only feature in some setups). Default: CPU count.")
    parser.add_argument('--ner-model', type=str, default="en_core_web_lg",
                        help="SpaCy NER model to use (e.g., en_core_web_sm, en_core_web_lg, en_core_web_trf).")
    parser.add_argument('--spacy-batch-size', type=int, default=64,
                        help="Batch size for spaCy nlp.pipe() during NER.")
    parser.add_argument('--ner-workers', type=int, default=None,
                        help="Number of CPU workers for parallel NER processing. Default: CPU count.")
    parser.add_argument('--output-dir', type=str, default="ner_rule_analysis_reports",
                        help="Directory to save analysis reports.")
    parser.add_argument('--top-n-entities', type=int, default=20,
                        help="Number of top entities to show per category.")
    parser.add_argument('--log-level', type=str, default='INFO',
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='Set logging level for the script.')
    args = parser.parse_args()

    logger.setLevel(getattr(logging, args.log_level.upper()))
    if logger.handlers and isinstance(logger.handlers[0], RichHandler):
        logger.handlers[0].setLevel(getattr(logging, args.log_level.upper()))
    if args.log_level.upper() != 'DEBUG':
        for lib_logger_name in ["boto3", "botocore", "urllib3", "s3transfer", "aiohttp", "asyncio", "spacy"]:
            logging.getLogger(lib_logger_name).setLevel(logging.WARNING)

    console_main_startup = Console()
    try:
        spacy.load(args.ner_model)
    except OSError:
        console_main_startup.print(
            f"[yellow]SpaCy model '[bold]{args.ner_model}[/bold]' not found. Attempting to download...[/yellow]")
        try:
            spacy.cli.download(args.ner_model)
            console_main_startup.print(
                f"[green]✓ Successfully downloaded '[bold]{args.ner_model}[/bold]'. Please re-run the script if it exits now.[/green]")
        except SystemExit:
            console_main_startup.print(
                f"[red]✗ Failed to download '[bold]{args.ner_model}[/bold]'. Please install it manually: [code]python -m spacy download {args.ner_model}[/code][/red]")
            sys.exit(1)
        except Exception as e:
            console_main_startup.print(
                f"[red]✗ An error occurred during model download for '{args.ner_model}': {e}. Install manually.[/red]")
            sys.exit(1)

    if sys.platform == 'win32' and sys.version_info >= (3, 8):
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    asyncio.run(main_analyzer_async(args))
