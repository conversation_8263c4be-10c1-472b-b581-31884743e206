# Orchestration Service Layer

## Overview
This is the cross-domain orchestration layer that coordinates workflows across multiple domains. These orchestrators handle complex multi-domain workflows.

## Directory Structure
```
orchestration/
├── main_orchestrator.py        # Main workflow orchestrator
├── processing_orchestrator.py  # Processing workflow orchestrator
├── upload_orchestrator.py      # Upload workflow orchestrator
└── CLAUDE.md                   # This file
```

## Key Orchestrators

### MainOrchestrator
- Coordinates the entire pipeline
- Manages phases: Scraping → Processing → Upload → FB Ads → Reports
- Uses MainServiceFactory to create domain services
- Handles shutdown events and error recovery

### ProcessingOrchestrator
- Coordinates post-processing tasks
- Manages DataTransformer workflow
- Handles various processing modes (reprocess, cleanup, etc.)

### UploadOrchestrator
- Coordinates upload workflows
- Manages S3 uploads
- Handles batch processing

## Architecture Rules

1. **Cross-domain coordination ONLY** - These orchestrators can coordinate multiple domains
2. **No business logic** - Business logic lives in domains
3. **Thin orchestration** - Only workflow coordination
4. **Use factories** - All services created via factories
5. **Error handling** - Graceful degradation and recovery

## Dependencies
```python
# Can import from multiple domains (exception to domain isolation)
from src.services.pacer import PacerOrchestrator
from src.services.transformer import TransformerOrchestrator
from src.services.reports import ReportsOrchestrator
from src.services.fb_ads import FbAdsOrchestrator

# Uses infrastructure patterns
from src.infrastructure.patterns.component_base import AsyncServiceBase
```

## Workflow Example
```python
# MainOrchestrator coordinates cross-domain workflow
async def run(self):
    # 1. Scraping (PACER domain)
    if self.config.scraper:
        scraper = await self.factory.create_scraping_orchestrator()
        await scraper.execute()
    
    # 2. Processing (Transformer domain)
    if self.config.post_process:
        processor = await self.factory.create_processing_orchestrator()
        result = await processor.execute()
    
    # 3. Upload (cross-domain)
    if self.config.upload:
        uploader = await self.factory.create_upload_orchestrator()
        await uploader.execute(result)
    
    # 4. Reports (Reports domain)
    if self.config.report_generator:
        reporter = await self.factory.create_reports_orchestrator()
        await reporter.generate_report()
```

## Testing
All orchestrators should have integration tests in `tests/integration/orchestration/`

## Note
These are the ONLY services allowed to coordinate across domains. All other services must respect domain boundaries.

## Contact
Architecture questions: See docs/ARCHITECTURAL_CONSISTENCY_PLAN.md