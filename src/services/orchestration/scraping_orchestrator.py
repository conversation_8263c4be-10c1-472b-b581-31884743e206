# /src/services/orchestration/scraping_orchestrator.py

import asyncio
import logging
from typing import Any, Optional


from src.config_models.base import WorkflowConfig
from src.config_models.utils import convert_datetime_fields_to_strings
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import OrchestrationServiceError
from src.services.pacer.pacer_orchestrator_service import PacerOrchestratorService
from src.utils.date import FORMAT_US_SHORT, FORMAT_ISO


class ScrapingOrchestrator(AsyncServiceBase):
    def __init__(self,
                 config: WorkflowConfig =None,
                 pacer_service: PacerOrchestratorService =None,
                 shutdown_event: Optional[asyncio.Event] =None):
        logger = logging.getLogger(__name__)
        super().__init__(logger, config.model_dump() if hasattr(config, 'model_dump') else {})
        self.config = config
        self.pacer_service = pacer_service
        self.shutdown_event = shutdown_event
        self.log_info("ScrapingOrchestrator initialized")

    def _check_shutdown(self) -> bool:
        """Check if shutdown has been requested."""
        if self.shutdown_event and self.shutdown_event.is_set():
            self.log_info("Shutdown requested, stopping scraping")
            return True
        return False

    async def _execute_action(self, data: Any) -> Any:
        """Execute the scraping orchestration workflow."""
        return await self.execute()

    async def execute(self):
        """Execute the scraping orchestration workflow with proper async context management."""
        self.log_info("Executing scraping tasks")

        if self._check_shutdown():
            return

        # 1. Extract configuration parameters (court_ids, dates, docket_num)
        docket_num_param = self.config.docket_num
        court_ids_param = self.config.process_single_court  # List of courts
        html_only_param = self.config.html_only
        docket_list_input = self.config.docket_list_for_orchestrator  # List of dicts
        run_general_scraper = self.config.scraper

        # Log extracted parameters
        self.log_info("Configuration parameters extracted", {
            "docket_num": docket_num_param,
            "court_ids": court_ids_param,
            "html_only": html_only_param,
            "docket_list_count": len(docket_list_input) if docket_list_input else 0,
            "run_general_scraper": run_general_scraper
        })

        # 2. Handle datetime field conversions for compatibility
        start_date_obj = self.config.start_date
        end_date_obj = self.config.date  # 'date' is the run date (end_date for range)
        iso_date_str = end_date_obj.strftime(FORMAT_ISO) if end_date_obj else None
        
        # Convert datetime objects to strings for compatibility with services that expect strings
        config_dict = convert_datetime_fields_to_strings(self.config.model_dump())
        
        self.log_info("Datetime conversions complete", {
            "start_date": start_date_obj,
            "end_date": end_date_obj,
            "iso_date": iso_date_str
        })

        # 3. Create PacerOrchestratorService instance (already provided via DI)
        if not self.pacer_service:
            self.log_error("PacerOrchestratorService instance not available")
            raise OrchestrationServiceError("PacerOrchestratorService not initialized")

        # 4. ENTER async context manager for resource cleanup
        async with self.pacer_service:
            try:
                # 5. Determine processing mode and route appropriately
                processed = await self._route_processing_mode(
                    docket_list_input=docket_list_input,
                    docket_num_param=docket_num_param,
                    court_ids_param=court_ids_param,
                    html_only_param=html_only_param,
                    run_general_scraper=run_general_scraper,
                    iso_date_str=iso_date_str,
                    start_date_obj=start_date_obj,
                    end_date_obj=end_date_obj
                )
                
                if not processed:
                    self.log_info("Scraping skipped - no valid processing mode detected")

            except Exception as e:
                self.log_error("Error during scraping execution", {"error": str(e)})
                raise OrchestrationServiceError(f"ScrapingOrchestrator execution failed: {str(e)}",
                                                {"original_error": e})

        self.log_info("Scraping tasks completed")

    async def _route_processing_mode(self,
                                   docket_list_input,
                                   docket_num_param,
                                   court_ids_param,
                                   html_only_param,
                                   run_general_scraper,
                                   iso_date_str,
                                   start_date_obj,
                                   end_date_obj) -> bool:
        """
        Route to appropriate processing mode based on configuration.
        
        Returns:
            bool: True if processing was executed, False if skipped
        """
        
        # Mode 1: Specific docket list processing
        if docket_list_input and isinstance(docket_list_input, list):
            self.log_info("Processing Mode 1: Specific docket list", {
                "docket_count": len(docket_list_input)
            })
            # 6. Route to PacerOrchestratorService.process_courts()
            await self.pacer_service.process_courts(
                court_ids=[],  # Not used when docket_list_input is provided
                context=None,
                iso_date=iso_date_str,
                start_date=start_date_obj,
                end_date=end_date_obj,
                docket_list_input=docket_list_input
            )
            self.log_info("Specific docket list processing complete")
            return True

        # Mode 2: Single docket processing
        elif docket_num_param and court_ids_param:
            return await self._process_single_docket(
                docket_num_param=docket_num_param,
                court_ids_param=court_ids_param,
                html_only_param=html_only_param,
                iso_date_str=iso_date_str
            )

        # Mode 3: Multiple courts / review cases processing
        elif run_general_scraper:
            return await self._process_multiple_courts(
                court_ids_param=court_ids_param,
                iso_date_str=iso_date_str,
                start_date_obj=start_date_obj,
                end_date_obj=end_date_obj
            )

        return False

    async def _process_single_docket(self,
                                   docket_num_param: str,
                                   court_ids_param: list,
                                   html_only_param: bool,
                                   iso_date_str: str) -> bool:
        """Process a single docket."""
        if not isinstance(court_ids_param, list) or len(court_ids_param) == 0:
            self.log_error("Cannot process single docket: 'process_single_court' list is empty or not a list", {
                "docket_num": docket_num_param
            })
            return False

        target_court = court_ids_param[0]
        if len(court_ids_param) > 1:
            self.log_warning("Multiple courts provided but processing single docket for first court only", {
                "courts_provided": court_ids_param,
                "docket_num": docket_num_param,
                "target_court": target_court
            })

        self.log_info("Processing Mode 2: Single docket", {
            "court": target_court,
            "docket": docket_num_param,
            "html_only": html_only_param
        })

        # Route to PacerOrchestratorService for single docket processing
        await self.pacer_service.process_docket(
            docket_num=docket_num_param,
            court_ids=[target_court],
            iso_date=iso_date_str,
            context=None  # Let PacerOrchestratorService manage context
        )
        
        self.log_info("Single docket processing complete")
        return True

    async def _process_multiple_courts(self,
                                     court_ids_param: list,
                                     iso_date_str: str,
                                     start_date_obj,
                                     end_date_obj) -> bool:
        """Process multiple courts for report scraping."""
        target_courts_for_reports = court_ids_param
        
        if not target_courts_for_reports:  # Load all courts when list is empty
            self.log_info("process_single_court is empty, loading all available courts")
            # Import here to avoid circular imports
            from src.main import get_court_ids_async
            all_courts = await get_court_ids_async()
            self.log_info(f"Loaded {len(all_courts)} courts from district courts file")
            
            # Apply skip_courts filter
            skip_list = getattr(self.config, 'skip_courts', []) or []
            if skip_list:
                target_courts_for_reports = [c for c in all_courts if c not in skip_list]
                self.log_info(f"Applied skip_courts filter. Remaining: {len(target_courts_for_reports)} courts")
            else:
                target_courts_for_reports = all_courts
        
        if not target_courts_for_reports:
            self.log_error("No courts available to process for report scraping")
            return False

        self.log_info("Processing Mode 3: Multiple courts (report scraping)", {
            "court_count": len(target_courts_for_reports),
            "courts": target_courts_for_reports[:5]  # Log first 5 courts to avoid clutter
        })

        # 6. Route to PacerOrchestratorService.process_courts()
        await self.pacer_service.process_courts(
            court_ids=target_courts_for_reports,
            context=None,  # Let the service manage its own context
            iso_date=iso_date_str,
            start_date=start_date_obj,
            end_date=end_date_obj
        )
        
        self.log_info("Multiple courts report scraping complete")
        return True

    async def __aenter__(self):
        """Async context manager entry."""
        self.log_info("ScrapingOrchestrator entering async context")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with cleanup."""
        self.log_info("ScrapingOrchestrator exiting async context")

        # Perform cleanup operations
        try:
            # Clean up any resources if needed
            if hasattr(self, 'pacer_service') and self.pacer_service:
                # If pacer_service has cleanup methods, call them
                if hasattr(self.pacer_service, 'cleanup'):
                    await self.pacer_service.cleanup()

            # Log completion
            self.log_info("ScrapingOrchestrator async context cleanup completed")
        except Exception as e:
            self.log_error(f"Error during ScrapingOrchestrator async context cleanup: {e}")

        # Don't suppress exceptions by default
        return False
