import asyncio
import concurrent.futures
import logging
import re
from typing import Dict, Tuple, List, Optional, Any

import pandas as pd
# VectorClusterer has been deprecated
# try:
#     from src.fb_ads.vector_clusterer import VectorClusterer
# except ImportError:

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import ConfigurationError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.utils.date import DateUtils
from src.utils.law_firm import LawFirmNameHandler

VectorClusterer = None
logging.getLogger(__name__).info("VectorClusterer deprecated. Campaign tagging functionality disabled.")


class AdDataFrameProcessorService(AsyncServiceBase):
    def __init__(self,
                 logger: LoggerProtocol = None,
                 config: Optional[Dict[str, Any]] = None,
                 fb_ad_repository = None):
        super().__init__(logger, config)

        # Initialize service-specific attributes that are NOT set by validation
        self.fb_ad_db = fb_ad_repository
        # Note: iso_date, days_back, and start_date are set in _validate_config()

        self.log_info("VectorClusterer deprecated. Rule-based campaign tagging disabled.")

    def _validate_config(self) -> None:
        """Validate required configuration."""
        if not self.config:
            raise ConfigurationError("Configuration is required for AdDataFrameProcessorService")

        required_keys = ['iso_date']
        missing_keys = [key for key in required_keys if key not in self.config]
        if missing_keys:
            raise ConfigurationError(f"Missing required config keys: {missing_keys}")

        # Extract configuration values
        self.iso_date = self.config['iso_date']
        self.days_back = self.config.get('days_back', 14)

        try:
            self.start_date = DateUtils.get_date_before_n_days(self.days_back, self.iso_date)
            self.log_info(
                f"AdDataFrameProcessorService configured with {self.days_back} days lookback period: {self.start_date} to {self.iso_date}")
        except ValueError as e:
            raise ConfigurationError(f"Invalid iso_date format '{self.iso_date}'. Cannot calculate start_date: {e}")


    async def _execute_action(self, data: Any) -> Any:
        """Execute the main ad dataframe loading action."""
        return await self.load_ad_df_async()

    @staticmethod
    def _clean_ad_firm_name_final(name: str) -> str:
        if not isinstance(name, str):
            return ""
        exceptions_keep_periods = {"AllConsumer.com", "ClassAction.org", "AllVeteran.com"}
        if name in exceptions_keep_periods:
            return name
        name_no_commas = name.replace(',', '')
        parts = name_no_commas.split()
        cleaned_parts = []
        initial_pattern = re.compile(r"^[A-Z]\.$")
        for part in parts:
            if initial_pattern.match(part):
                cleaned_parts.append(part)
            else:
                cleaned_parts.append(part.replace('.', ''))
        cleaned_name = ' '.join(cleaned_parts)
        cleaned_name = re.sub(r'\s+', ' ', cleaned_name).strip()
        return cleaned_name

    def _log_and_capitalize(self, name):
        original_name = name
        try:
            name_str = str(name).strip() if pd.notna(name) and name is not None else ""
            if not name_str:
                return ""
            # Assuming LawFirmNameHandler and normalize_law_firm_name are correctly pathed or moved
            from src.utils.law_firm_normalizer import normalize_law_firm_name  # Ensure this path is valid
            if ';' in name_str:
                firms = [firm.strip() for firm in name_str.split(';')]
                normalized_firms = []
                for firm in firms:
                    if firm:
                        # Check for hyphen splitting within each semicolon-separated firm
                        if ' - ' in firm:
                            first_part = firm.split(' - ')[0].strip()
                            if first_part:
                                capitalized_firm = LawFirmNameHandler.capitalize_law_firm_names(first_part)
                                normalized_firm = normalize_law_firm_name(capitalized_firm)
                                if normalized_firm:
                                    normalized_firms.append(normalized_firm)
                        else:
                            capitalized_firm = LawFirmNameHandler.capitalize_law_firm_names(firm)
                            normalized_firm = normalize_law_firm_name(capitalized_firm)
                            if normalized_firm:
                                normalized_firms.append(normalized_firm)
                
                # Remove duplicates while preserving order
                unique_firms = []
                seen = set()
                for firm in normalized_firms:
                    if firm not in seen:
                        unique_firms.append(firm)
                        seen.add(firm)
                
                result = ' ; '.join(unique_firms)
                return result
            elif ' - ' in name_str:
                first_part = name_str.split(' - ')[0].strip()
                if first_part:
                    capitalized_name = LawFirmNameHandler.capitalize_law_firm_names(first_part)
                    normalized_name = normalize_law_firm_name(capitalized_name)
                    return normalized_name
                else:
                    capitalized_name = LawFirmNameHandler.capitalize_law_firm_names(name_str)
                    normalized_name = normalize_law_firm_name(capitalized_name)
                    return normalized_name
            else:
                capitalized_name = LawFirmNameHandler.capitalize_law_firm_names(name_str)
                normalized_name = normalize_law_firm_name(capitalized_name)
                return normalized_name
        except Exception as e:
            self.log_error(f"Error during law firm capitalization for Input='{original_name}': {e}",
                           extra={'original_name': original_name, 'error': str(e)})
            return original_name  # Return original on error to avoid data loss

    async def load_ad_df_async(self) -> pd.DataFrame:
        """Load and process ad dataframe asynchronously."""
        self.log_info("Starting Ad DataFrame load process (Service)...")
        df = await self._query_and_create_dataframe(self.start_date)
        if df.empty:
            self.log_info("Ad DataFrame is empty after query.")
            return df

        df = self._preprocess_dataframe(df, self.start_date)
        self.log_info(f"Ad DataFrame shape after preprocess: {df.shape}")
        if df.empty:
            self.log_warning("Ad DataFrame became empty after preprocessing.")
            return df

        if 'law_firm' in df.columns:
            self.log_info("Applying law firm name capitalization and cleaning to Ad DataFrame...")
            df['law_firm'] = df['law_firm'].fillna('').astype(str).apply(self._log_and_capitalize)
            df['law_firm'] = df['law_firm'].astype(str).apply(AdDataFrameProcessorService._clean_ad_firm_name_final)
            self.log_info("Finished law firm name processing.")
        else:
            self.log_warning("Ad DataFrame missing 'law_firm' column.")

        # VectorClusterer deprecated - skip rule-based campaign tagging
        self.log_info("VectorClusterer deprecated. Skipping rule-based campaign tagging.")
        if 'campaign' not in df.columns:
            df['campaign'] = 'N/A (VectorClusterer Deprecated)'

        df = self._filter_invalid_summaries(df)
        self.log_info(f"Ad DataFrame shape after filtering invalid summaries: {df.shape}")
        if df.empty:
            self.log_warning("Ad DataFrame became empty after filtering invalid summaries.")

        self.log_info("Finished Ad DataFrame load process (Service).")
        return df

    def _filter_invalid_summaries(self, df: pd.DataFrame) -> pd.DataFrame:
        if 'summary' not in df.columns:
            self.logger.warning("No 'summary' column found, skipping summary filtering.")
            return df

        initial_count = len(df)
        self.log_info(f"🔍 Starting summary filtering - Initial count: {initial_count}")

        df['summary'] = df['summary'].fillna('').astype(str)

        # Use exact match for invalid markers (not case-insensitive)
        invalid_markers = {'NA', 'na', 'N/A', 'n/a', 'Skipped', 'skipped', '', 'None', 'none'}

        # Log sample of summaries before filtering
        if len(df) > 0:
            sample_summaries = df['summary'].head(5).tolist()
            self.log_debug(f"📝 Sample summaries before filtering: {sample_summaries}")

        # Create mask for valid summaries
        valid_summary_mask = ~df['summary'].isin(invalid_markers)
        valid_summary_mask &= ~(df['summary'].str.lower() == 'summary generation failed')
        valid_summary_mask &= df['summary'].str.len() >= 3  # Minimum length check

        # Log which summaries are being filtered
        invalid_summaries = df[~valid_summary_mask][['ad_archive_id', 'summary']].head(10)
        if len(invalid_summaries) > 0:
            self.log_debug(f"🚫 Sample of filtered summaries: {invalid_summaries.to_dict('records')}")

        filtered_df = df[valid_summary_mask].copy()
        removed_count = len(df) - len(filtered_df)

        if removed_count > 0:
            self.log_info(f"📊 Removed {removed_count} entries with invalid summaries "
                          f"({removed_count / initial_count * 100:.1f}% of total)")

        self.log_info(f"✅ Summary filtering complete - Remaining: {len(filtered_df)}")

        return filtered_df

    async def _query_and_create_dataframe(self, start_date: str) -> pd.DataFrame:
        self.log_info(f"📊 Querying ad archive from {start_date} to {self.iso_date} (Service)...")
        self.log_info(f"📅 Date range: {start_date} to {self.iso_date} ({self.days_back} days lookback)")

        try:
            # Query the FB ad repository
            items = await self.fb_ad_db.query_ad_archive_by_date(start_date, self.iso_date)
            self.log_info(f"✅ Query returned {len(items)} ad items from database")

            if not items:
                self.log_warning("⚠️ No ad items returned from database query")
                return pd.DataFrame()

            # Log sample of returned items
            if len(items) > 0:
                sample_item = items[0]
                self.log_debug(f"📝 Sample ad item keys: {list(sample_item.keys())}")
                if 'StartDate' in sample_item:
                    self.log_debug(f"📅 Sample StartDate: {sample_item['StartDate']}")
                if 'Summary' in sample_item:
                    self.log_debug(f"📝 Sample Summary: {sample_item['Summary']}")

            df = pd.DataFrame(items)
            self.log_info(f"📊 Created DataFrame with shape: {df.shape}")
            return df
        except Exception as e:
            self.log_error(f"❌ Error querying or creating DataFrame: {e}", extra={'error': str(e)})
            return pd.DataFrame()

    def _preprocess_dataframe(self, df: pd.DataFrame, start_date: str) -> pd.DataFrame:
        if df.empty: return df

        self.log_info(f"📊 Starting preprocessing - Initial shape: {df.shape}")

        _, col_names_mapping = self._get_column_definitions()  # col_order not strictly needed if we fill missing

        df.rename(columns={k: v for k, v in col_names_mapping.items() if k in df.columns}, inplace=True)

        target_cols = list(col_names_mapping.values())
        for target_col in target_cols:
            if target_col not in df.columns:
                if target_col == 'is_active':
                    df[target_col] = False
                elif target_col in ['start_date', 'end_date']:
                    df[target_col] = ''
                else:
                    df[target_col] = ''

        try:
            if 'start_date' in df.columns: df['start_date'] = df['start_date'].astype(str)
            if 'end_date' in df.columns: df['end_date'] = df['end_date'].astype(str)

            start_date_dt_filter = pd.to_datetime(start_date, format='%Y%m%d', errors='coerce')

            df['start_date_dt'] = pd.to_datetime(df['start_date'], format='%Y%m%d', errors='coerce')
            if df['start_date_dt'].isna().all():
                df['start_date_dt'] = pd.to_datetime(df['start_date'], errors='coerce')

            df['end_date_dt'] = pd.to_datetime(df['end_date'], format='%Y%m%d', errors='coerce')
            if df['end_date_dt'].isna().all():
                df['end_date_dt'] = pd.to_datetime(df['end_date'], errors='coerce')

            if pd.notna(start_date_dt_filter):
                self.log_info(
                    f"📅 Applying date filter - keeping ads with start_date >= {start_date_dt_filter.strftime('%Y-%m-%d')}")

                # Log some sample dates before filtering
                sample_dates = df[df['start_date_dt'].notna()]['start_date_dt'].head(10)
                self.log_debug(f"📅 Sample start dates before filter: {[d.strftime('%Y-%m-%d') for d in sample_dates]}")

                before_filter = len(df)
                valid_date_mask = (df['start_date_dt'] >= start_date_dt_filter) | \
                                  (df['end_date_dt'] >= start_date_dt_filter)
                df = df[valid_date_mask].copy()
                after_filter = len(df)

                self.log_info(f"📊 Date filter applied - Before: {before_filter}, After: {after_filter}, "
                              f"Filtered out: {before_filter - after_filter}")
            else:  # If start_date_dt_filter is NaT, means start_date was invalid, log and proceed with all data
                self.log_warning(
                    f"Could not parse filter start_date '{start_date}'. Proceeding without date filtering.")

            df['start_date'] = df['start_date_dt'].dt.strftime('%m/%d/%y').fillna('')
            df['end_date'] = df['end_date_dt'].dt.strftime('%m/%d/%y').fillna('')
            df.drop(columns=['start_date_dt', 'end_date_dt'], inplace=True, errors='ignore')

        except Exception as date_err:
            self.log_error(f"Error processing ad date columns: {date_err}.", extra={'error': str(date_err)})
            # Ensure columns exist and are strings even if processing fails
            for col_date in ['start_date', 'end_date']:
                if col_date not in df.columns:
                    df[col_date] = ''
                else:
                    df[col_date] = df[col_date].fillna('').astype(str)

        for col in ['caption', 'body', 'title', 'law_firm', 'summary', 'page_name']:
            if col in df.columns:
                df[col] = df[col].fillna('').astype(str)
            elif col == 'law_firm':  # Ensure law_firm exists for later processing
                df[col] = ''
        return df

    @staticmethod
    def _get_column_definitions() -> Tuple[List[str], Dict[str, str]]:
        col_order = [
            'ad_archive_id', 'ad_creative_id', 'is_active', 'law_firm', 'page_name', 'page_id', 'start_date',
            'end_date',
            'summary', 'cta_text', 'title', 'body', 'link_description', 'caption', 'link_url', 'publisher_platform',
            'original_image_url', 'resized_image_url', 'video_preview_image_url', 'video_hd_url', 'video_sd_url'
        ]
        col_names_mapping = {col: col for col in col_order}  # Assuming snake_case from DB
        return col_order, col_names_mapping

    def load_ad_df(self) -> pd.DataFrame:
        """Synchronous wrapper for load_ad_df_async."""
        try:
            loop = asyncio.get_running_loop()
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(lambda: asyncio.run(self.load_ad_df_async()))
                return future.result()
        except RuntimeError:  # No event loop running
            return asyncio.run(self.load_ad_df_async())
        except Exception as e:
            self.log_error(f"Error in sync load_ad_df (Service) wrapper: {e}", extra={'error': str(e)})
            return pd.DataFrame()

    # Methods like _filter_and_clean_dataframe, _remove_duplicate_entries,
    # _load_unwanted_terms, _filter_out_unwanted_content, _sort_and_drop_duplicates
    # were part of the original class but not directly used by load_ad_df_async.
    # They can be added here if their functionality is needed by other service methods
    # or if the processing logic is expanded. For now, they are omitted to keep the
    # service focused on the logic moved from the original AdDataFrameProcessor.load_ad_df_async.
    # If these are general DataFrame utilities, they might belong in a separate utility module.
    # For example, _load_unwanted_terms might become part of a config service or a general utility.

    # Example of how one might be added if needed:
    # @staticmethod
    # def _load_unwanted_terms_from_config(config_path_str: str) -> List[str]:
    #     """Loads unwanted terms from a specified configuration file."""
    #     config_path = Path(config_path_str) # e.g., "config/reports/content_filters.json"
    #     try:
    #         with open(config_path, 'r') as f:
    #             config = json.load(f)
    #         return config.get('unwanted_terms', [])
    #     except Exception as e:
    #         logging.getLogger(__name__).error(f"Error loading unwanted terms from {config_path}: {e}")
    #         return []

    # And its usage:
    # unwanted_terms = AdDataFrameProcessorService._load_unwanted_terms_from_config("path/to/your/config.json")
    # df = self._filter_out_unwanted_content(df, unwanted_terms)
    # (Assuming _filter_out_unwanted_content is also defined in this class or a utility)

    # For now, keeping it aligned with the direct logic of load_ad_df_async.
    # The unused static methods from the original class are:
    # _remove_duplicate_entries
    # _load_unwanted_terms
    # _filter_out_unwanted_content
    # _sort_and_drop_duplicates (parts of its logic are in _preprocess_dataframe)
    # _filter_and_clean_dataframe (calls some of the above)
    # These can be reintegrated if the processing scope expands.

    # Legacy method for backward compatibility
    async def perform_action(self, data: Any = None) -> pd.DataFrame:
        """Execute ad dataframe loading - component pattern interface."""
        return await self.load_ad_df_async()
