import re
import traceback
from datetime import datetime
from pprint import pprint
from typing import Any

import requests
from bs4 import BeautifulSoup

# Removed dependency_injector imports - using container-based injection
from src.infrastructure.protocols.logger import LoggerProtocol
from src.infrastructure.patterns.component_base import ComponentImplementation


class CaseParserService(ComponentImplementation):
    """Service for parsing HTML content from PACER legal documents."""

    def __init__(self, logger: LoggerProtocol, html_content: str | None = None):
        """
        Initialize the CaseParserService with dependency injection.

        Args:
            logger: Logger instance from DI container
            html_content: Optional HTML content string. If provided, initializes parser with content.
        """
        # Initialize base class with logger
        super().__init__(logger)

        self.html = html_content
        self.soup = BeautifulSoup(html_content, "html.parser") if html_content else None

    async def _execute_action(self, data: Any) -> Any:
        """
        Required abstract method implementation for ComponentImplementation.
        
        Args:
            data: Input data for processing
            
        Returns:
            Processed result
        """
        # For CaseParserService, this could route to different parsing actions
        action = data.get('action', 'parse_html') if isinstance(data, dict) else 'parse_html'
        
        if action == 'parse_html':
            html_content = data.get('html_content') if isinstance(data, dict) else data
            return self.parse_html(html_content)
        else:
            raise ValueError(f"Unknown action for CaseParserService: {action}")

    def _safe_log(self, level: str, message: str) -> None:
        """Log message using standardized logger from ComponentImplementation."""
        if level == "info":
            self.log_info(message)
        elif level == "warning":
            self.log_warning(message)
        elif level == "error":
            self.log_error(message)
        elif level == "debug":
            self.log_debug(message)
        else:
            self.log_info(f"[{level.upper()}] {message}")

    def set_html(self, html_content: str) -> None:
        """
        Set or update the HTML content to parse.

        Args:
            html_content: HTML content string
        """
        self.html = html_content
        self.soup = BeautifulSoup(html_content, "html.parser")

    def _preprocess_html_content(self, html_content: str) -> tuple[str, dict[str, Any]]:
        """
        Preprocess HTML content to remove problematic elements and detect edge cases.
        
        Args:
            html_content: Raw HTML content
            
        Returns:
            Tuple of (cleaned_html, metadata_dict)
        """
        metadata = {
            "has_javascript": False,
            "has_transaction_receipt": False,
            "has_mixed_content": False,
            "scripts_removed": 0,
            "preprocessing_applied": True
        }
        
        try:
            # Parse the HTML
            soup = BeautifulSoup(html_content, "html.parser")
            
            # Remove all script tags and their content
            scripts = soup.find_all("script")
            if scripts:
                metadata["has_javascript"] = True
                metadata["scripts_removed"] = len(scripts)
                for script in scripts:
                    script.decompose()
                self._safe_log("info", f"🧹 Removed {len(scripts)} JavaScript blocks")
            
            # Remove style tags that might interfere with parsing
            styles = soup.find_all("style")
            for style in styles:
                style.decompose()
            
            # Detect transaction receipt pages - must have multiple strong indicators
            body_text = soup.get_text().lower()
            
            # CRITICAL FIX: Only mark as transaction receipt if it's an actual receipt page
            # A docket sheet may have "Transaction Receipt" and "Cost:" in docket entries
            # but is NOT a transaction receipt page
            
            # Check for actual receipt page structure (multiple indicators required)
            # A real receipt page will have specific combinations
            has_receipt_id = "receipt id:" in body_text
            has_billing_info = "billing information" in body_text
            has_pages_retrieved = "pages retrieved" in body_text
            
            # Also check for docket indicators (if present, it's NOT a receipt page)
            has_docket_indicators = (
                "civil docket" in body_text or 
                "docket text" in body_text or
                "case #:" in body_text or
                "assigned to:" in body_text
            )
            
            # It's a receipt page ONLY if it has multiple receipt indicators AND no docket indicators
            receipt_indicator_count = sum([has_receipt_id, has_billing_info, has_pages_retrieved])
            if receipt_indicator_count >= 2 and not has_docket_indicators:
                metadata["has_transaction_receipt"] = True
                self._safe_log("info", "🧾 Detected transaction receipt page")
            else:
                metadata["has_transaction_receipt"] = False
            
            # Detect mixed content (HTML + non-HTML elements)
            if re.search(r'<[^>]+>\s*[^<]*\s*<script[^>]*>', str(soup), re.IGNORECASE):
                metadata["has_mixed_content"] = True
                self._safe_log("info", "🔀 Detected mixed HTML/script content")
            
            # Clean up problematic Unicode characters
            cleaned_html = str(soup)
            cleaned_html = cleaned_html.replace('\u00a0', ' ')  # Non-breaking space
            cleaned_html = cleaned_html.replace('\u200b', '')   # Zero-width space
            cleaned_html = cleaned_html.replace('\ufeff', '')   # Byte order mark
            
            # Remove inline JavaScript event handlers
            cleaned_html = re.sub(r'\s*on\w+\s*=\s*["\'][^"\']*["\']', '', cleaned_html, flags=re.IGNORECASE)
            
            self._safe_log("info", f"✨ HTML preprocessing completed - Scripts removed: {metadata['scripts_removed']}")
            
            return cleaned_html, metadata
            
        except Exception as e:
            self._safe_log("warning", f"⚠️ HTML preprocessing failed: {str(e)}")
            # Return original content if preprocessing fails
            metadata["preprocessing_applied"] = False
            return html_content, metadata

    def _parse_alternative_structure(self) -> dict[str, Any]:
        """
        Alternative parsing method for edge cases like transaction receipts and JavaScript-heavy pages.
        
        Returns:
            Dictionary with parsed case information using alternative strategies
        """
        if not self.soup:
            return {"case_info": {}, "plaintiffs": [], "defendants": [], "attorney": []}
        
        self._safe_log("info", "🔄 Using alternative structure parsing")
        
        case_info = {}
        plaintiffs = []
        defendants = []
        attorneys = []
        
        try:
            # Strategy 1: Extract from any text content containing case patterns
            body_text = self.soup.get_text()
            
            # Try to extract docket number from anywhere in the text
            docket_pattern = r'(?:Case\s*[#:]?\s*|Docket\s*[#:]?\s*)?(\d+:\d{2}-[a-zA-Z]{2}-\d{5})'
            docket_match = re.search(docket_pattern, body_text, re.IGNORECASE)
            if docket_match:
                case_info["docket_num"] = self.normalize_docket_number(docket_match.group(1))
                self._safe_log("info", f"📋 Extracted docket number: {case_info['docket_num']}")
            
            # Try to extract versus information from text patterns
            versus_patterns = [
                r'([A-Z][a-zA-Z\s,]+)\s+v\.?\s+([A-Z][a-zA-Z\s,]+)',
                r'([A-Z][a-zA-Z\s,]+)\s+vs\.?\s+([A-Z][a-zA-Z\s,]+)',
                r'([A-Z][a-zA-Z\s,]+)\s+versus\s+([A-Z][a-zA-Z\s,]+)'
            ]
            
            for pattern in versus_patterns:
                versus_match = re.search(pattern, body_text)
                if versus_match:
                    plaintiff_name = versus_match.group(1).strip()
                    defendant_name = versus_match.group(2).strip()
                    case_info["versus"] = f"{plaintiff_name} v. {defendant_name}"
                    
                    # Add to plaintiffs and defendants
                    plaintiffs.append({"name": plaintiff_name, "attorneys": []})
                    defendants.append(defendant_name)
                    
                    self._safe_log("info", f"⚖️ Extracted versus: {case_info['versus']}")
                    break
            
            # Strategy 2: Look for any table structure that might contain case info
            tables = self.soup.find_all("table")
            for table in tables:
                table_text = table.get_text()
                
                # Extract date filed if present
                date_pattern = r'Date\s+Filed:\s*(\d{1,2}/\d{1,2}/\d{4})'
                date_match = re.search(date_pattern, table_text, re.IGNORECASE)
                if date_match:
                    case_info["date_filed"] = date_match.group(1)
                    self._safe_log("info", f"📅 Extracted date filed: {case_info['date_filed']}")
                
                # Extract assigned judge
                judge_pattern = r'Assigned\s+to:\s*([A-Z][a-zA-Z\s,\.]+)'
                judge_match = re.search(judge_pattern, table_text, re.IGNORECASE)
                if judge_match:
                    case_info["assigned_to"] = judge_match.group(1).strip()
                    self._safe_log("info", f"👨‍⚖️ Extracted assigned judge: {case_info['assigned_to']}")
            
            # Strategy 3: Extract any bold text that might be names
            bold_elements = self.soup.find_all("b")
            potential_names = []
            
            for bold in bold_elements:
                text = bold.get_text().strip()
                # Filter for likely names (contains letters, not too long, not common legal terms)
                if (len(text) > 3 and len(text) < 100 and 
                    re.match(r'^[A-Z][a-zA-Z\s,\.&]+$', text) and
                    not any(term in text.lower() for term in ['plaintiff', 'defendant', 'attorney', 'court', 'case', 'docket'])):
                    potential_names.append(text)
            
            # If we don't have plaintiffs yet, try to use potential names
            if not plaintiffs and potential_names:
                # Take the first few as potential plaintiffs
                for name in potential_names[:3]:  # Limit to avoid noise
                    plaintiffs.append({"name": name, "attorneys": []})
                    self._safe_log("info", f"👤 Added potential plaintiff: {name}")
            
            # Mark as alternative parsing
            case_info["_alternative_parsing"] = True
            case_info["_parsing_method"] = "alternative_structure"
            
            result = {
                "case_info": case_info,
                "plaintiffs": plaintiffs,
                "defendants": defendants,
                "attorney": attorneys
            }
            
            self._safe_log("info", f"🎯 Alternative parsing completed - Plaintiffs: {len(plaintiffs)}, Defendants: {len(defendants)}")
            return result
            
        except Exception as e:
            self._safe_log("error", f"❌ Alternative parsing failed: {str(e)}")
            return {
                "case_info": {"_alternative_parsing_failed": True, "_error": str(e)},
                "plaintiffs": [],
                "defendants": [],
                "attorney": []
            }

    @staticmethod
    def strip_whitespace(text):
        return " ".join(text.strip().split()) if text else ""

    @staticmethod
    def format_phone_number(phone):
        # Convert (XXX) XXX-XXXX to XXX-XXX-XXXX format
        phone = re.sub(r"\((\d{3})\) (\d{3})-(\d{4})", r"\1-\2-\3", phone)
        # Convert XXX/XXX-XXXX to XXX-XXX-XXXX format
        phone = re.sub(r"(\d{3})/(\d{3})-(\d{4})", r"\1-\2-\3", phone)
        return phone

    @staticmethod
    def normalize_law_firm_name(law_firm: str) -> str:
        """
        Normalize law firm name for deduplication purposes.

        Args:
            law_firm: Raw law firm name string

        Returns:
            Normalized law firm name

        Examples:
            >>> normalize_law_firm_name("ENVIRONMENTAL LITIGATION GROUP, P.C.")
            "environmental litigation group pc"
            >>> normalize_law_firm_name("Smith & Jones LLC")
            "smith & jones llc"
        """
        if not law_firm or not isinstance(law_firm, str):
            return ""

        # Convert to lowercase
        normalized = law_firm.lower().strip()

        # Remove common punctuation but preserve ampersands
        normalized = re.sub(r"[,.]", "", normalized)

        # Standardize multiple spaces to single space
        normalized = " ".join(normalized.split())

        # Handle common abbreviations (but keep them for disambiguation)
        # P.C. -> pc, LLC -> llc, etc.
        normalized = re.sub(r"\bp\.?c\.?\b", "pc", normalized)
        normalized = re.sub(r"\bl\.?l\.?c\.?\b", "llc", normalized)
        normalized = re.sub(r"\bl\.?l\.?p\.?\b", "llp", normalized)
        normalized = re.sub(r"\binc\.?\b", "inc", normalized)
        normalized = re.sub(r"\bcorp\.?\b", "corp", normalized)

        # Final cleanup of multiple spaces
        normalized = " ".join(normalized.split())

        return normalized

    @staticmethod
    def normalize_docket_number(docket_num: str) -> str:
        """
        Normalize docket number to exactly 13 characters: N:NN-XX-NNNNN

        This function handles:
        - Truncating judge initials and suffixes
        - Zero-padding case numbers to 5 digits
        - Preserving the core docket format

        Args:
            docket_num: Raw docket number string

        Returns:
            Normalized 13-character docket number

        Examples:
            >>> normalize_docket_number("2:25-cv-02364-AMS")
            "2:25-cv-02364"
            >>> normalize_docket_number("2:25-cv-2364")
            "2:25-cv-02364"
            >>> normalize_docket_number("1:25-cv-123")
            "1:25-cv-00123"
        """
        if not docket_num or ":" not in docket_num:
            return docket_num

        try:
            prefix = docket_num.split(":")[0]  # N
            suffix_parts = docket_num.split(":")[1].split("-")

            if len(suffix_parts) >= 3:
                year = suffix_parts[0]  # YY
                case_type = suffix_parts[1]  # XX (cv, sf, etc.)
                docket_part = suffix_parts[2]  # NNNNN-...

                # Extract digits and zero-pad to exactly 5 digits
                num_match = re.search(r"(\d{1,5})", docket_part)
                if num_match:
                    digits = num_match.group(1)
                    # Zero-pad to exactly 5 digits
                    five_digits = digits.zfill(5)
                    normalized = f"{prefix}:{year}-{case_type}-{five_digits}"

                    # Validate the result is exactly 13 characters
                    if len(normalized) == 13:
                        return normalized
                    else:
                        # Return even if not exactly 13 characters
                        return normalized
                else:
                    # Fallback: use original if no digit match
                    return docket_num[:13] if len(docket_num) > 13 else docket_num
            else:
                # Fallback: truncate or return as-is
                return docket_num[:13] if len(docket_num) > 13 else docket_num

        except Exception:
            # On any error, return truncated version
            return docket_num[:13] if len(docket_num) > 13 else docket_num

    @staticmethod
    def _normalize_docket_to_13_chars(docket_num: str) -> str:
        """
        Normalize docket number to exactly 13 characters: N:NN-XX-NNNNN

        This method is maintained for backward compatibility.
        """
        return CaseParserService.normalize_docket_number(docket_num)

    def process_attorney_parts(
        self, input_parts: list[str], previous_attorneys: list[dict]
    ) -> dict[str, Any]:
        """Processes attorney information parts and returns a dictionary.

        Args:
            input_parts: A list of strings containing attorney information.
                         These strings are expected to be pre-stripped and have internal whitespace normalized.
            previous_attorneys: A list of previously processed attorney dictionaries.

        Returns:
            A dictionary containing attorney information.
        """
        # Initialize with default values for a consistent return structure.
        attorney_record = {
            "attorney_name": "",
            "law_firm": "",
            "phone": "",
            "fax": "",
            "email": "",
            "address1": "",
            "address2": "",
            "city": "",
            "state": "",
            "zip_code": "",
            "lead_attorney": False,
            "pro_hac_vice": False,
            "attorney_to_be_noticed": False,
        }

        # Handle empty input_parts list as per original behavior (return default structure, no append).
        if not input_parts:
            return attorney_record

        try:
            # Filter out parts containing "COUNSEL NOT..." or "Civil Litigation" (case-insensitive).
            # Assumes elements in input_parts are already stripped and whitespace-normalized by the caller.
            current_processing_list = [
                p
                for p in input_parts
                if "COUNSEL NOT" not in p.upper()
                and "CIVIL LITIGATION" not in p.upper()
            ]

            if not current_processing_list:
                # If filtering results in an empty list (e.g., input_parts only contained "COUNSEL NOT...").
                if (
                    input_parts and input_parts[0]
                ):  # Ensure input_parts[0] is not an empty string
                    attorney_record["attorney_name"] = input_parts[0]
                # Append to previous_attorneys in this fallback, then return.
                previous_attorneys.append(attorney_record.copy())
                return attorney_record

            # Step 1: Assign attorney name and law firm (based on fixed positions).
            attorney_record["attorney_name"] = current_processing_list[0]
            if len(current_processing_list) > 1:
                attorney_record["law_firm"] = current_processing_list[1]

            # Determine the starting index for parsing detailed information (flags, contact, address).
            details_start_index = 2 if len(current_processing_list) > 1 else 1
            parts_for_details_scan = current_processing_list[details_start_index:]

            # Step 2: Extract flags (lead_attorney, pro_hac_vice, attorney_to_be_noticed).
            parts_after_flags = []
            for text_part in parts_for_details_scan:
                text_part_lower = text_part.lower()
                if "lead attorney" in text_part_lower:
                    attorney_record["lead_attorney"] = True
                elif "pro hac vice" in text_part_lower:
                    attorney_record["pro_hac_vice"] = True
                elif "attorney to be noticed" in text_part_lower:
                    attorney_record["attorney_to_be_noticed"] = True
                else:
                    parts_after_flags.append(text_part)

            # Step 3: Extract email, phone, and fax from parts_after_flags.
            address_candidate_parts = []
            for text_part in parts_after_flags:
                if text_part.startswith("Email:"):
                    attorney_record["email"] = text_part.split("Email:", 1)[-1].strip()
                elif re.search(r"\d{3}-\d{3}-\d{4}|\(\d{3}\)\s*\d{3}-\d{4}", text_part):
                    formatted_number = self.format_phone_number(text_part)
                    if not attorney_record["phone"]:
                        attorney_record["phone"] = formatted_number
                    else:
                        attorney_record["fax"] = formatted_number
                else:
                    address_candidate_parts.append(text_part)

            # Step 4: Process address lines from address_candidate_parts.
            _current_address1 = ""
            _current_address2 = ""
            for addr_text_part in address_candidate_parts:
                if re.search(r"[A-Z]{2}\s+\d{5}", addr_text_part):
                    city_str_part, *state_zip_str_list = addr_text_part.split(",", 1)
                    attorney_record["city"] = city_str_part.strip()
                    if state_zip_str_list:
                        state_zip_full_text = state_zip_str_list[0]
                        state_zip_match_obj = re.search(
                            r"([A-Z]{2})\s*(\d{5}(?:-\d{4})?)", state_zip_full_text
                        )
                        if state_zip_match_obj:
                            attorney_record["state"] = state_zip_match_obj.group(1)
                            attorney_record["zip_code"] = state_zip_match_obj.group(2)
                    _current_address2 = ""  # Reset address2 when city/state/zip found
                elif not _current_address1:
                    _current_address1 = addr_text_part
                else:
                    _current_address2 = addr_text_part
            attorney_record["address1"] = _current_address1
            attorney_record["address2"] = _current_address2

            # Step 5: Handle "(See above for address)"
            # This logic attempts to fill in details if a previous record for the same attorney exists.
            if attorney_record.get("law_firm") == "(See above for address)":
                for prev_att in reversed(
                    previous_attorneys
                ):  # Search most recent first
                    if prev_att.get("attorney_name") == attorney_record.get(
                        "attorney_name"
                    ):
                        # Found a previous record for the same attorney.
                        # If this previous record has a valid law firm, use its details.
                        if (
                            prev_att.get("law_firm")
                            and prev_att["law_firm"] != "(See above for address)"
                        ):
                            attorney_record["law_firm"] = prev_att["law_firm"]
                            # If law firm is being copied, other details should also be copied if present in prev_att
                            # and not already set in current record from its specific "(See above...)" block.
                            # This assumes "(See above for address)" implies all address fields are from "above".
                            if prev_att.get("address1"):
                                attorney_record["address1"] = prev_att["address1"]
                            if prev_att.get("address2"):
                                attorney_record["address2"] = prev_att["address2"]
                            if prev_att.get("city"):
                                attorney_record["city"] = prev_att["city"]
                            if prev_att.get("state"):
                                attorney_record["state"] = prev_att["state"]
                            if prev_att.get("zip_code"):
                                attorney_record["zip_code"] = prev_att["zip_code"]
                            if prev_att.get("phone"):
                                attorney_record["phone"] = prev_att["phone"]
                            if prev_att.get("fax"):
                                attorney_record["fax"] = prev_att["fax"]
                            if prev_att.get("email"):
                                attorney_record["email"] = prev_att["email"]
                            # Once a suitable previous record is found and used, break.
                            break

            # Final cleanup for 'fax' field, removing "Fax:" prefix if present.
            if attorney_record["fax"]:
                attorney_record["fax"] = (
                    attorney_record["fax"].replace("Fax:", "").strip()
                )

            previous_attorneys.append(attorney_record.copy())
            return attorney_record

        except Exception as e:
            self._safe_log("error", f"Error processing attorney information: {e}")
            self._safe_log("error", f"Traceback: {traceback.format_exc()}")

            fallback_record = {
                "attorney_name": (
                    input_parts[0] if input_parts and input_parts[0] else ""
                ),
                "law_firm": "",
                "phone": "",
                "fax": "",
                "email": "",
                "address1": "",
                "address2": "",
                "city": "",
                "state": "",
                "zip_code": "",
                "lead_attorney": False,
                "pro_hac_vice": False,
                "attorney_to_be_noticed": False,
            }
            previous_attorneys.append(fallback_record.copy())
            return fallback_record

    def extract_attorneys(self, plaintiffs: list[dict]) -> list[dict[str, Any]]:
        """Extract and deduplicate attorneys from all plaintiffs.

        Args:
            plaintiffs: List of plaintiff dictionaries with attorneys

        Returns:
            List of unique attorney dictionaries
        """
        all_attorneys = []

        # Collect attorneys from all plaintiffs
        for plaintiff in plaintiffs:
            if isinstance(plaintiff, dict):
                plaintiff_attorneys = plaintiff.get("attorneys", [])
                all_attorneys.extend(plaintiff_attorneys)

        # Deduplicate attorneys based on (attorney_name, normalized_law_firm) tuple
        unique_attorneys = []
        seen_attorneys = set()

        for attorney in all_attorneys:
            if isinstance(attorney, dict):
                attorney_name = attorney.get("attorney_name", "").strip()
                law_firm = attorney.get("law_firm", "").strip()

                # Skip if either attorney name or law firm is empty
                if not attorney_name or not law_firm:
                    continue

                # Create unique key based on attorney name and normalized law firm
                normalized_law_firm = self.normalize_law_firm_name(law_firm)
                attorney_key = (attorney_name.lower(), normalized_law_firm)

                # Only add if we haven't seen this attorney/firm combination
                if attorney_key not in seen_attorneys:
                    seen_attorneys.add(attorney_key)
                    unique_attorneys.append(attorney)
                else:
                    self._safe_log(
                        "debug",
                        f"Duplicate attorney found: {attorney_name} at {law_firm} (normalized: {normalized_law_firm})",
                    )

        self._safe_log(
            "info",
            f"Attorney deduplication: {len(all_attorneys)} total -> {len(unique_attorneys)} unique attorneys",
        )
        return unique_attorneys

    def parse(self, enable_preprocessing: bool = False, enable_fallback: bool = True) -> dict:
        """
        Parses the HTML content and returns a dictionary containing case information.
        
        Args:
            enable_preprocessing: If True, applies HTML preprocessing to remove JavaScript and clean content
            enable_fallback: If True, attempts alternative parsing when standard parsing fails
            
        Returns:
            Dictionary containing parsed case information
        """
        if not self.soup:
            return {"case_info": {}, "plaintiffs": [], "defendants": [], "attorney": []}

        # Store original HTML for potential preprocessing
        original_html = self.html
        preprocessing_metadata = {}

        # Apply preprocessing if requested
        if enable_preprocessing and self.html:
            self._safe_log("info", "🔧 Applying HTML preprocessing...")
            cleaned_html, preprocessing_metadata = self._preprocess_html_content(self.html)
            
            # Update the soup with cleaned HTML
            self.html = cleaned_html
            self.soup = BeautifulSoup(cleaned_html, "html.parser")
            
            # CRITICAL FIX: Process the page regardless of transaction receipt
            # Transaction receipts are just footer info on paid docket sheets - NOT separate pages
            if preprocessing_metadata.get("has_transaction_receipt", False):
                self._safe_log("info", "📄 Page has transaction receipt footer - proceeding with parsing")
            
            # Continue with standard parsing regardless

        # Check for "Proceedings not available" page
        if self.html and (
            "proceedings for case" in self.html.lower()
            and "are not available" in self.html.lower()
        ):
            self._safe_log(
                "info", "Detected 'Proceedings for case are not available' page"
            )
            result = {
                "case_info": {"no_proceedings": True},
                "plaintiffs": [],
                "defendants": [],
                "attorney": [],
                "_no_proceedings_detected": True,
            }
            if preprocessing_metadata:
                result["case_info"]["_preprocessing_metadata"] = preprocessing_metadata
            return result

        case_info = {}
        plaintiffs = []
        defendants = []
        previous_attorneys = []
        case_details = []

        main_content = self.soup.find("div", id="cmecfMainContent")

        if not main_content:
            # If standard parsing fails and fallback is enabled, try alternative parsing
            if enable_fallback:
                self._safe_log("info", "⚠️ No main content found - trying fallback parsing")
                fallback_result = self._parse_alternative_structure()
                if preprocessing_metadata:
                    fallback_result["case_info"]["_preprocessing_metadata"] = preprocessing_metadata
                return fallback_result
            else:
                result = {"case_info": {}, "plaintiffs": [], "defendants": []}
                if preprocessing_metadata:
                    result["case_info"]["_preprocessing_metadata"] = preprocessing_metadata
                return result

        case_info["flags"] = []
        first_table = main_content.find("table")
        if first_table and first_table.find("td", align="right"):
            spans = first_table.find_all("span")
            case_info["flags"] = [self.strip_whitespace(span.text) for span in spans]

        h3_element = main_content.find("h3", align="center")
        if h3_element:
            h3_text = h3_element.decode_contents().replace("<br>", "").split("<br/>\n")
            if len(h3_text) > 0 and (
                "U.S. District Court" in h3_text[0]
                or "United States District Court" in h3_text[0]
            ):
                if len(h3_text) > 1:
                    court_and_office = h3_text[1].split("(")
                    case_info["court_name"] = self.strip_whitespace(court_and_office[0])
                    case_info["office"] = (
                        self.strip_whitespace(court_and_office[1].strip(")"))
                        if len(court_and_office) > 1
                        else ""
                    )
                if len(h3_text) > 2 and "CIVIL DOCKET FOR CASE #" in h3_text[2]:
                    # Extract full docket number first
                    full_docket = self.strip_whitespace(h3_text[2].split(":", 1)[-1])
                    # Normalize to exactly 13 characters: N:NN-XX-NNNNN
                    case_info["docket_num"] = self.normalize_docket_number(full_docket)

        # Initialize case_details
        case_details = []

        # Find case table - try relative to h3_element first, then fallback to main_content
        case_table = None
        if h3_element:
            case_table = h3_element.find_next("table")
        else:
            # Fallback: try to find table directly in main_content
            case_table = main_content.find(
                "table", {"cellpadding": "1"}
            )  # Look for specific table attributes

        if case_table:
            rows = case_table.find_all("td", valign="top")
            for row in rows:
                case_details.extend(row.text.split("\n"))

        # Only set versus if we have case details
        if case_details:
            case_info["versus"] = case_details[0]
            case_details.pop(0)
        else:
            case_info["versus"] = ""

        if len(case_details) >= 2:
            for detail in case_details:
                detail = detail.replace("\u00a0", " ")
                if "Assigned to:" in detail:
                    case_info["assigned_to"] = self.strip_whitespace(
                        detail.split(":")[1]
                    )
                elif "Referred to:" in detail:
                    case_info["referred_to"] = self.strip_whitespace(
                        detail.split(":")[1]
                    )
                elif "Case in other court:" in detail:
                    case_in_other_court = self.strip_whitespace(detail.split(":", 1)[1])
                    if len(case_in_other_court) != 0:
                        case_info["case_in_other_court"] = self.strip_whitespace(
                            detail.split(":", 1)[1]
                        )
                elif "Date Filed:" in detail:
                    case_info["date_filed"] = self.strip_whitespace(
                        detail.split(":")[1]
                    )
                elif "Jury Demand:" in detail:
                    case_info["jury_demand"] = self.strip_whitespace(
                        detail.split(":")[1]
                    )
                elif "Nature of Suit:" in detail:
                    case_info["nos"] = self.strip_whitespace(detail.split(":")[1])
                elif "Jurisdiction:" in detail:
                    case_info["jurisdiction"] = self.strip_whitespace(
                        detail.split(":")[1]
                    )
                elif "Demand:" in detail:
                    case_info["demand"] = self.strip_whitespace(detail.split(":")[1])
                elif "Lead case:" in detail:
                    case_info["lead_case"] = (
                        detail.split(":", 1)[1].strip() if ":" in detail else None
                    )
                elif "Cause:" in detail:
                    case_info["cause"] = (
                        detail.split(":", 1)[1].strip() if ":" in detail else None
                    )

        # CRITICAL FIX: Check if case_table exists before calling find_next
        if case_table:
            attorney_and_parties_table = case_table.find_next("table")
        else:
            # Enhanced fallback logic for removal cases and other edge cases
            attorney_and_parties_table = None

            # Strategy 1: Find table containing plaintiff/petitioner/defendant header
            for table in self.soup.find_all("table"):
                table_text = table.get_text().lower()
                if "plaintiff" in table_text or "petitioner" in table_text or "defendant" in table_text:
                    attorney_and_parties_table = table
                    break

            # Strategy 2: If still no table found, look for tables with specific structure
            if not attorney_and_parties_table:
                for table in self.soup.find_all(
                    "table", {"border": "0", "cellspacing": "5"}
                ):
                    if table.find("b"):  # Contains bold text (likely names)
                        attorney_and_parties_table = table
                        break

            # Strategy 3: Final fallback - any table with bold text in main content
            if not attorney_and_parties_table and main_content:
                for table in main_content.find_all("table"):
                    if table.find("b"):
                        attorney_and_parties_table = table
                        break

        while attorney_and_parties_table:
            first_row = attorney_and_parties_table.find("tr")
            while first_row:
                row_text = first_row.text
                if "plaintiff" in row_text.lower() or "petitioner" in row_text.lower():
                    first_row = first_row.find_next("tr")
                    if first_row:
                        if len(first_row.find_all("td")) == 0:
                            first_row = first_row.find_next("tr")
                            continue
                        tds = first_row.find_all("td")
                        if len(tds) < 3:
                            first_row = first_row.find_next("tr")
                            continue
                        plaintiff_td, _, attorney_td = tds
                        if not plaintiff_td.find("b"):
                            first_row = first_row.find_next("tr")
                            continue
                        plaintiff = {
                            "name": self.strip_whitespace(plaintiff_td.find("b").text),
                            "attorneys": [],
                        }
                        
                        # CRITICAL FIX: Extract attorneys from <b> tags within attorney_td
                        # The HTML structure has multiple <b> tags for attorney names
                        attorney_bold_tags = attorney_td.find_all("b")
                        if attorney_bold_tags:
                            self._safe_log("info", f"Found {len(attorney_bold_tags)} <b> tags with attorney names in plaintiff row")
                        
                        # Get full text and split into attorney blocks using "ATTORNEY TO BE NOTICED" as delimiter
                        full_attorney_text = attorney_td.text
                        
                        # Split by "ATTORNEY TO BE NOTICED" to separate attorney blocks
                        attorney_blocks = full_attorney_text.split("ATTORNEY TO BE NOTICED")
                        
                        self._safe_log("info", f"Split into {len(attorney_blocks)} attorney blocks using 'ATTORNEY TO BE NOTICED' delimiter")
                        
                        # Process each attorney block separately
                        for block_idx, attorney_block in enumerate(attorney_blocks):
                            if not attorney_block.strip():
                                continue
                                
                            # Split this block by newlines to get individual lines
                            block_lines = [line.strip() for line in attorney_block.strip().split("\n") if line.strip()]
                            
                            if block_lines:
                                # First line should be attorney name (usually in bold)
                                attorney_name = block_lines[0]
                                self._safe_log("info", f"Processing attorney block {block_idx + 1}: {attorney_name}")
                                
                                # Add "ATTORNEY TO BE NOTICED" back to the info if this wasn't the last block
                                if block_idx < len(attorney_blocks) - 1:
                                    block_lines.append("ATTORNEY TO BE NOTICED")
                                
                                # Process this attorney's information
                                try:
                                    attorney_record = self.process_attorney_parts(
                                        block_lines, previous_attorneys
                                    )
                                    plaintiff["attorneys"].append(attorney_record)
                                    self._safe_log("info", f"Successfully added attorney: {attorney_name}")
                                except Exception as e:
                                    self._safe_log("error", f"Error processing attorney {attorney_name}: {str(e)}")
                        
                        # Skip the old processing logic - we've handled everything above
                        attorneys = []
                        attorneys = [
                            re.sub(r"\s+", " ", item.strip())
                            for item in attorneys
                            if item.strip()
                            and not item.startswith("United Sta")
                            and "Civil Litigation" not in item
                        ]

                        # Old processing logic removed - handled by the new block-based approach above
                        
                        # Log final count
                        self._safe_log("info", f"Total attorneys for plaintiff '{plaintiff.get('name')}': {len(plaintiff['attorneys'])}")
                        plaintiffs.append(plaintiff)

                elif "defendant" in row_text.lower():
                    first_row = first_row.find_next("tr")
                    while first_row:
                        row_text = first_row.text.strip()
                        if "defendant" in row_text.lower():
                            first_row = first_row.find_next("tr")
                            continue
                        if not row_text:
                            first_row = first_row.find_next("tr")
                            continue
                        defendant_td = first_row.find("td")
                        if (
                            defendant_td
                            and defendant_td.find("b")
                            and defendant_td.find("b").text.strip()
                        ):
                            defendants.append(
                                self.strip_whitespace(defendant_td.find("b").text)
                            )
                        first_row = first_row.find_next("tr")

                first_row = first_row.find_next("tr") if first_row else None

            attorney_and_parties_table = attorney_and_parties_table.find_next("table")

        # REMOVAL CASE FALLBACK: If no plaintiffs found and case might be removal, try alternative parsing
        if not plaintiffs:
            self._safe_log(
                "info",
                "🔍 No plaintiffs found with standard parsing, trying removal case fallback...",
            )

            # Check if this is likely a removal case
            removal_result = self.is_removal()
            is_likely_removal = (
                removal_result.get("is_removal", False)
                if isinstance(removal_result, dict)
                else False
            )

            if is_likely_removal:
                self._safe_log(
                    "info",
                    "📋 REMOVAL CASE DETECTED - Using specialized plaintiff extraction",
                )
                plaintiffs_extracted = self._extract_plaintiffs_removal_fallback()
                plaintiffs.extend(plaintiffs_extracted)

                # Also try to extract attorneys from defendant sections for removal cases
                attorneys_extracted = self._extract_attorneys_removal_fallback()
                previous_attorneys.extend(attorneys_extracted)

                self._safe_log(
                    "info",
                    f"🎯 REMOVAL FALLBACK - Plaintiffs: {len(plaintiffs)}, Attorneys: {len(previous_attorneys)}",
                )
            else:
                self._safe_log(
                    "info", "ℹ️ Not a removal case, trying general fallback parsing"
                )
                # Try general fallback for other edge cases
                plaintiffs_extracted = self._extract_plaintiffs_general_fallback()
                plaintiffs.extend(plaintiffs_extracted)

        # Final fallback: If still no meaningful data and fallback is enabled, try alternative parsing
        if enable_fallback and not plaintiffs and not defendants and not case_info.get("versus"):
            self._safe_log("info", "🚨 Standard parsing yielded minimal results - trying alternative structure parsing")
            fallback_result = self._parse_alternative_structure()
            
            # Merge any existing case_info with fallback results
            if fallback_result.get("case_info"):
                case_info.update(fallback_result["case_info"])
            if fallback_result.get("plaintiffs"):
                plaintiffs.extend(fallback_result["plaintiffs"])
            if fallback_result.get("defendants"):
                defendants.extend(fallback_result["defendants"])
            
            # Mark that fallback was used
            case_info["_used_fallback_parsing"] = True

        # Find and extract 'Case in other court' using more specific logic
        case_in_other_court_table = self.soup.find(
            "td", string=re.compile(r"Case\s+in\s+other\s+court:")
        )
        if case_in_other_court_table:
            next_td = case_in_other_court_table.find_next("td")
            if next_td:
                case_info["case_in_other_court"] = self.strip_whitespace(next_td.text)

        # Extract unique attorneys from all plaintiffs using the new parsing method
        attorneys = self.parse_attorneys()
        
        # Fallback: If no attorneys found with new method, use old method
        if not attorneys:
            attorneys = self.extract_attorneys(plaintiffs)

        # Add attorneys from fallback extraction (from previous_attorneys)
        if previous_attorneys:
            self._safe_log(
                "info",
                f"📋 Adding {len(previous_attorneys)} attorneys from fallback extraction",
            )
            attorneys.extend(previous_attorneys)

        # Construct versus field from plaintiff/defendant information if available
        if plaintiffs and defendants:
            try:
                # Get first plaintiff and defendant names
                plaintiff_name = (
                    plaintiffs[0]
                    if isinstance(plaintiffs[0], str)
                    else plaintiffs[0].get("name", "")
                )
                defendant_name = (
                    defendants[0]
                    if isinstance(defendants[0], str)
                    else defendants[0].get("name", "")
                )

                if plaintiff_name and defendant_name:
                    constructed_versus = f"{plaintiff_name} v. {defendant_name}"
                    self._safe_log(
                        "info",
                        f"🏗️ Constructed versus from plaintiff/defendant: {constructed_versus}",
                    )
                    case_info["versus"] = constructed_versus
                elif plaintiff_name:
                    # If only plaintiff available, use it
                    case_info["versus"] = plaintiff_name
                    self._safe_log(
                        "info", f"🏗️ Using plaintiff name for versus: {plaintiff_name}"
                    )
            except Exception as e:
                self._safe_log(
                    "warning",
                    f"⚠️ Failed to construct versus from plaintiff/defendant: {e}",
                )
                # Keep original versus from case_details if construction fails

        # Add preprocessing metadata if it was applied
        if preprocessing_metadata:
            case_info["_preprocessing_metadata"] = preprocessing_metadata

        # Restore original HTML if preprocessing was applied
        if enable_preprocessing and original_html:
            self.html = original_html
            self.soup = BeautifulSoup(original_html, "html.parser")

        result = {
            "case_info": case_info,
            "plaintiffs": plaintiffs,
            "defendants": defendants,
            "attorney": attorneys,
        }
        return result

    def parse_with_enhancements(self, html_content: str = None) -> dict:
        """
        Convenience method that applies both preprocessing and fallback parsing.
        
        This method is specifically designed to handle problematic cases like:
        - JavaScript-heavy HTML content
        - Mixed content scenarios  
        - Transaction receipt pages
        - Cases where standard parsing fails
        
        Args:
            html_content: Optional HTML content. If provided, updates the parser's content.
            
        Returns:
            Dictionary containing parsed case information with enhanced capabilities
        """
        if html_content:
            self.set_html(html_content)
        
        self._safe_log("info", "🚀 Starting enhanced parsing with preprocessing and fallback enabled")
        
        # Use both preprocessing and fallback for maximum compatibility
        result = self.parse(enable_preprocessing=True, enable_fallback=True)
        
        # Log summary of enhancements used
        case_info = result.get("case_info", {})
        enhancements_used = []
        
        if case_info.get("_preprocessing_metadata", {}).get("preprocessing_applied"):
            enhancements_used.append("preprocessing")
        if case_info.get("_used_fallback_parsing"):
            enhancements_used.append("fallback parsing")
        if case_info.get("_alternative_parsing"):
            enhancements_used.append("alternative structure parsing")
        if case_info.get("transaction_receipt"):
            enhancements_used.append("transaction receipt detection")
            
        if enhancements_used:
            self._safe_log("info", f"✅ Enhanced parsing completed - Used: {', '.join(enhancements_used)}")
        else:
            self._safe_log("info", "✅ Standard parsing completed successfully")
            
        return result

    def _extract_plaintiffs_removal_fallback(self) -> list[dict]:
        """Extract plaintiffs using removal case-specific logic."""
        plaintiffs = []

        try:
            # Look for plaintiff section in removal cases - typically separate from defendant section
            # Pattern: <td><b><u>Plaintiff </u></b></td> followed by rows with plaintiff names

            # Find all tables and search for plaintiff sections
            for table in self.soup.find_all("table"):
                plaintiff_header_found = False
                rows = table.find_all("tr")

                for row in rows:
                    row_text = row.get_text().lower()

                    # Look for plaintiff header
                    if "plaintiff" in row_text and ("b" in str(row) or "u" in str(row)):
                        plaintiff_header_found = True
                        self._safe_log(
                            "info",
                            f"🎯 Found plaintiff header in removal case: {row_text.strip()}",
                        )
                        continue

                    # If we found plaintiff header, look for actual plaintiff names in subsequent rows
                    if plaintiff_header_found:
                        # Stop if we hit defendant section or empty rows
                        if "defendant" in row_text:
                            break
                        if "v." in row_text.lower() or row_text.strip() == "":
                            continue

                        # Look for bold names (plaintiff names are typically bold)
                        bold_elements = row.find_all("b")
                        for bold in bold_elements:
                            plaintiff_name = self.strip_whitespace(bold.get_text())
                            if (
                                plaintiff_name and len(plaintiff_name) > 2
                            ):  # Avoid single letters
                                plaintiff = {
                                    "name": plaintiff_name,
                                    "attorneys": [],  # Will be populated separately
                                }
                                plaintiffs.append(plaintiff)
                                self._safe_log(
                                    "info",
                                    f"✅ REMOVAL FALLBACK - Extracted plaintiff: {plaintiff_name}",
                                )

                # If we found plaintiffs in this table, we're done
                if plaintiffs:
                    break

        except Exception as e:
            self._safe_log("error", f"Error in removal plaintiff extraction: {str(e)}")

        return plaintiffs

    def _extract_attorneys_removal_fallback(self) -> list[dict]:
        """Extract attorney information from defendant sections in removal cases."""
        attorneys = []

        try:
            # In removal cases, attorneys are typically associated with defendants
            # Pattern: defendant name | "represented by" | attorney info

            for table in self.soup.find_all("table"):
                rows = table.find_all("tr")

                for row in rows:
                    tds = row.find_all("td")

                    # Look for 3-column structure: defendant | "represented by" | attorney
                    if len(tds) == 3:
                        middle_td = tds[1]
                        attorney_td = tds[2]

                        if "represented" in middle_td.get_text().lower():
                            # Extract attorney information
                            attorney_text = attorney_td.get_text()
                            attorney_lines = [
                                line.strip()
                                for line in attorney_text.split("\n")
                                if line.strip()
                            ]

                            current_attorney = []
                            for line in attorney_lines:
                                if (
                                    line == "ATTORNEY TO BE NOTICED"
                                    or line == "LEAD ATTORNEY"
                                ):
                                    if current_attorney:
                                        # Process accumulated attorney info
                                        attorney_name = (
                                            current_attorney[0]
                                            if current_attorney
                                            else ""
                                        )
                                        if attorney_name and len(attorney_name) > 2:
                                            attorney_record = {
                                                "name": self.strip_whitespace(
                                                    attorney_name
                                                ),
                                                "firm": "",
                                                "address": "",
                                                "phone": "",
                                                "email": "",
                                            }
                                            # Try to extract firm from second line
                                            if len(current_attorney) > 1:
                                                attorney_record["firm"] = (
                                                    self.strip_whitespace(
                                                        current_attorney[1]
                                                    )
                                                )

                                            attorneys.append(attorney_record)
                                            self._safe_log(
                                                "info",
                                                f"✅ REMOVAL FALLBACK - Extracted attorney: {attorney_name}",
                                            )

                                    current_attorney = []
                                else:
                                    current_attorney.append(line)

                            # Handle any remaining attorney info
                            if current_attorney:
                                attorney_name = (
                                    current_attorney[0] if current_attorney else ""
                                )
                                if attorney_name and len(attorney_name) > 2:
                                    attorney_record = {
                                        "name": self.strip_whitespace(attorney_name),
                                        "firm": "",
                                        "address": "",
                                        "phone": "",
                                        "email": "",
                                    }
                                    if len(current_attorney) > 1:
                                        attorney_record["firm"] = self.strip_whitespace(
                                            current_attorney[1]
                                        )

                                    attorneys.append(attorney_record)
                                    self._safe_log(
                                        "info",
                                        f"✅ REMOVAL FALLBACK - Extracted attorney: {attorney_name}",
                                    )

        except Exception as e:
            self._safe_log("error", f"Error in removal attorney extraction: {str(e)}")

        return attorneys

    def _extract_plaintiffs_general_fallback(self) -> list[dict]:
        """General fallback method for plaintiff extraction in edge cases."""
        plaintiffs = []

        try:
            # Look for any bold text that might be plaintiff names
            # This is a very general approach for edge cases

            main_content = self.soup.find("div", id="cmecfMainContent")
            if main_content:
                bold_elements = main_content.find_all("b")

                for bold in bold_elements:
                    text = self.strip_whitespace(bold.get_text())
                    # Simple heuristics: name-like text (contains space, not all caps system text)
                    if (
                        text
                        and " " in text
                        and len(text.split()) >= 2
                        and not text.isupper()
                        and "COURT" not in text.upper()
                        and "DISTRICT" not in text.upper()
                        and "JUDGE" not in text.upper()
                    ):
                        plaintiff = {"name": text, "attorneys": []}
                        plaintiffs.append(plaintiff)
                        self._safe_log(
                            "info",
                            f"✅ GENERAL FALLBACK - Extracted potential plaintiff: {text}",
                        )

        except Exception as e:
            self._safe_log("error", f"Error in general plaintiff extraction: {str(e)}")

        return plaintiffs

    def is_removal(self):
        """Checks if the case is a removal case based on the docket text."""

        # Check if soup is None
        if self.soup is None:
            self._safe_log(
                "warning", "BeautifulSoup object is None in is_removal method"
            )
            return {"is_removal": False, "error": "No HTML content to parse"}

        # Try to find the table with the most specific attributes first
        table = self.soup.find(
            "table", attrs={"align": "center", "width": "99%", "border": "1"}
        )
        if not table:
            table = self.soup.find("table", attrs={"align": "center"})
            if not table:
                table = self.soup.find("table")
                if not table:
                    return False

        # Find all rows in the table
        rows = table.find_all("tr")

        # Try to find the header row
        header_row = None
        header_index = -1
        for i, row in enumerate(rows):
            if "Date Filed" in row.get_text() and "Docket Text" in row.get_text():
                header_row = row
                header_index = i
                break

        if not header_row or header_index == -1:
            return False

        # Check ALL content rows after the header, not just the first one
        removal_pattern = r"(?:notice\s+of\s+removal|notice\s+removal|petition\s+for\s+removal|NOTICE\s+OF\s+REMOVAL)"

        # Iterate through all rows after the header
        for i in range(header_index + 1, len(rows)):
            content_row = rows[i]
            cells = content_row.find_all("td")

            if len(cells) < 3:
                continue

            date = cells[0].text.strip()
            # Check the second TD (index 1) for docket entry number
            docket_text = cells[2].text.strip() if len(cells) > 2 else ""

            # Also check the second column in case it contains the removal text
            second_col_text = cells[1].text.strip() if len(cells) > 1 else ""

            # Check both the docket text column and the second column
            combined_text = f"{docket_text} {second_col_text}"

            if re.search(removal_pattern, combined_text, re.IGNORECASE):
                try:
                    parsed_date = datetime.strptime(date, "%m/%d/%Y")
                    result = {
                        "removal_date": parsed_date.strftime("%Y%m%d"),
                        "is_removal": True,
                        "removal_text": combined_text.strip(),
                    }
                    self._safe_log("info", f"Found removal case: {result}")
                    return result
                except ValueError:
                    self._safe_log("debug", f"Could not parse date: {date}")
                    # Still return that it's a removal even if date parsing fails
                    return {"is_removal": True, "removal_text": combined_text.strip()}

        return False

    def get_initial_filing_date(self):
        """Gets the initial filing date of the case from the docket text."""

        # Try to find the table with the most specific attributes first
        table = self.soup.find(
            "table", attrs={"align": "center", "width": "99%", "border": "1"}
        )
        if not table:
            table = self.soup.find("table", attrs={"align": "center"})
            if not table:
                table = self.soup.find("table")
                if not table:
                    return False

        # Find all rows in the table
        rows = table.find_all("tr")

        # Try to find the header row
        header_row = None
        for row in rows:
            if "Date Filed" in row.get_text() and "Docket Text" in row.get_text():
                header_row = row
                break

        if not header_row:
            return False

        # Find the first row after the header row
        content_row = header_row.find_next_sibling("tr")
        if not content_row:
            return False

        # Extract date and docket text
        cells = content_row.find_all("td")
        if len(cells) < 3:
            self._safe_log(
                "debug", f"Not enough cells in content row. Found {len(cells)} cells."
            )
            return False

        date = cells[0].text.strip()
        # docket_text = cells[2].text.strip()  # Commented out as unused

        try:
            parsed_date = datetime.strptime(date, "%m/%d/%Y")
            result = {"initial_filing_date": parsed_date.strftime("%Y%m%d")}
            return result
        except ValueError:
            self._safe_log("debug", f"Could not parse date: {date}")

        return False

    def process_url(self, url: str):
        """Fetches HTML from a URL, parses it, and prints the results."""
        try:
            # Fetch the HTML from the URL
            response = requests.get(url)
            response.raise_for_status()  # Raise an error if the request failed

            # Parse the HTML content
            self.html = response.text
            self.soup = BeautifulSoup(self.html, "html.parser")

            # Parse and display the results
            parsed_result = self.parse()
            pprint(parsed_result)

        except requests.RequestException as e:
            self._safe_log("error", f"Error fetching the URL: {e}")
        except Exception:
            self._safe_log(
                "error",
                f"An error occurred while processing the URL: {traceback.format_exc()}",
            )

    def parse_html(self, html_content: str) -> dict:
        """
        Parse HTML content and return case information.

        Args:
            html_content: HTML content string to parse

        Returns:
            Dict containing parsed case information
        """
        self.html = html_content
        self.soup = BeautifulSoup(html_content, "html.parser")
        return self.parse()

    def extract_case_details(self) -> dict:
        """
        Extract case details from HTML content.

        Returns:
            Dict containing case information
        """
        return self.parse()

    def parse_case_details_from_html(
        self, html_content: str, court_id: str, docket_num: str
    ) -> dict:
        """
        Parse case details from HTML content.

        This method is called by PacerNavigationService to extract case details.

        Args:
            html_content: HTML content to parse
            court_id: Court identifier
            docket_num: Docket number

        Returns:
            Dict containing parsed case information
        """
        # Set the HTML content
        self.set_html(html_content)

        # Parse and return the case details
        case_details = self.parse()

        # Ensure court_id and docket_num are included
        if case_details:
            case_details["court_id"] = court_id
            case_details["docket_num"] = docket_num

        return case_details

    def _preprocess_html_content(self, html_content: str) -> tuple[str, dict[str, Any]]:
        """
        Preprocess HTML content to handle JavaScript blocks and mixed content.
        
        Args:
            html_content: Raw HTML content to preprocess
            
        Returns:
            Tuple of (cleaned_html, metadata_dict)
        """
        if not html_content:
            return html_content, {}
            
        metadata = {
            "has_javascript": False,
            "has_transaction_receipt": False,
            "scripts_removed": 0,
            "preprocessing_applied": True
        }
        
        try:
            # Remove JavaScript blocks that interfere with parsing
            import re
            
            # Count script blocks before removal
            script_matches = re.findall(r'<script[^>]*>.*?</script>', html_content, re.DOTALL | re.IGNORECASE)
            metadata["scripts_removed"] = len(script_matches)
            metadata["has_javascript"] = len(script_matches) > 0
            
            # Remove script tags and their content
            cleaned_html = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            
            # Remove JavaScript event handlers
            cleaned_html = re.sub(r'\s+on\w+\s*=\s*["\'][^"\']*["\']', '', cleaned_html, flags=re.IGNORECASE)
            
            # Detect transaction receipt pages
            if "PACER Service Center" in html_content or "Transaction Receipt" in html_content:
                metadata["has_transaction_receipt"] = True
            
            # Clean up problematic Unicode characters that can break parsing
            cleaned_html = cleaned_html.replace('\u00a0', ' ')  # Non-breaking space
            cleaned_html = re.sub(r'\s+', ' ', cleaned_html)  # Normalize whitespace
            
            self._safe_log("debug", f"HTML preprocessing: {metadata}")
            
            return cleaned_html, metadata
            
        except Exception as e:
            self._safe_log("error", f"Error in HTML preprocessing: {e}")
            return html_content, {"preprocessing_applied": False, "error": str(e)}

    def _parse_alternative_structure(self) -> dict[str, Any]:
        """
        Alternative parsing method for edge cases like transaction receipts or JavaScript-heavy pages.
        
        Returns:
            Dict containing extracted case information using fallback methods
        """
        result = {
            "case_info": {},
            "plaintiffs": [],
            "defendants": [],
            "attorney": []
        }
        
        try:
            if not self.html:
                return result
                
            # Use regex patterns to extract information from any text content
            html_text = self.html
            
            # Extract case number - look for various patterns
            case_patterns = [
                r'(?:Case|CASE)\s*#?\s*:?\s*(\d:\d{2}-[a-zA-Z]{2}-\d{4,5}(?:-[A-Z]{2,3})?)',
                r'(\d:\d{2}-[a-zA-Z]{2}-\d{4,5}(?:-[A-Z]{2,3})?)',
                r'Civil\s+Action\s+No\.?\s*(\d:\d{2}-[a-zA-Z]{2}-\d{4,5})',
            ]
            
            for pattern in case_patterns:
                match = re.search(pattern, html_text, re.IGNORECASE)
                if match:
                    docket_num = match.group(1)
                    result["case_info"]["docket_num"] = self.normalize_docket_number(docket_num)
                    break
            
            # Extract case name/versus - look for "v." pattern
            versus_patterns = [
                r'([A-Z][A-Za-z\s,\.]+)\s+v\.?\s+([A-Z][A-Za-z\s,\.]+)',
                r'([A-Z][A-Za-z\s,\.]{5,50})\s+v\.?\s+([A-Z][A-Za-z\s,\.]{5,50})',
            ]
            
            for pattern in versus_patterns:
                match = re.search(pattern, html_text)
                if match:
                    plaintiff_name = self.strip_whitespace(match.group(1))
                    defendant_name = self.strip_whitespace(match.group(2))
                    
                    result["case_info"]["versus"] = f"{plaintiff_name} v. {defendant_name}"
                    result["plaintiffs"] = [{"name": plaintiff_name, "attorneys": []}]
                    result["defendants"] = [defendant_name]
                    break
            
            # Extract court information
            court_patterns = [
                r'United States District Court\s+([A-Za-z\s]+District\s+of\s+[A-Za-z\s]+)',
                r'U\.S\. District Court\s+([A-Za-z\s]+)',
                r'District Court\s+([A-Za-z\s]+)',
            ]
            
            for pattern in court_patterns:
                match = re.search(pattern, html_text, re.IGNORECASE)
                if match:
                    result["case_info"]["court_name"] = self.strip_whitespace(match.group(1))
                    break
            
            # Extract filing date
            date_patterns = [
                r'Filed:\s*(\d{1,2}/\d{1,2}/\d{4})',
                r'Date Filed:\s*(\d{1,2}/\d{1,2}/\d{4})',
                r'(\d{1,2}/\d{1,2}/\d{4})',
            ]
            
            for pattern in date_patterns:
                match = re.search(pattern, html_text)
                if match:
                    date_str = match.group(1)
                    result["case_info"]["date_filed"] = date_str
                    break
                    
            # Try to extract attorney information using simple patterns
            attorney_patterns = [
                r'Attorney:\s*([A-Z][A-Za-z\s,\.]+)',
                r'([A-Z][A-Z\s]+)\s+Attorney',
                r'represented by\s+([A-Z][A-Za-z\s,\.]+)',
            ]
            
            attorneys_found = []
            for pattern in attorney_patterns:
                matches = re.findall(pattern, html_text, re.IGNORECASE)
                for match in matches:
                    attorney_name = self.strip_whitespace(match)
                    if attorney_name and len(attorney_name) > 3:
                        attorneys_found.append({
                            "attorney_name": attorney_name,
                            "law_firm": "",
                            "phone": "",
                            "email": "",
                            "address1": "",
                            "city": "",
                            "state": "",
                            "zip_code": ""
                        })
            
            if attorneys_found:
                result["attorney"] = attorneys_found
                
            # Add metadata about alternative parsing
            result["case_info"]["_alternative_parsing"] = True
            result["case_info"]["_parsing_method"] = "regex_fallback"
            
            self._safe_log("info", f"Alternative parsing extracted: {len(result['plaintiffs'])} plaintiffs, {len(result['defendants'])} defendants, {len(result['attorney'])} attorneys")
            
            return result
            
        except Exception as e:
            self._safe_log("error", f"Error in alternative structure parsing: {e}")
            result["case_info"]["_parsing_error"] = str(e)
            return result

    def parse_with_enhancements(self, html_content: str = None) -> dict:
        """
        Enhanced parsing method that handles JavaScript-heavy HTML and mixed content.
        
        This method provides a one-step solution for problematic HTML cases by:
        1. Preprocessing HTML to remove JavaScript interference
        2. Attempting standard parsing
        3. Falling back to alternative parsing if needed
        
        Args:
            html_content: Optional HTML content. If not provided, uses self.html
            
        Returns:
            Dict containing parsed case information with enhancement metadata
        """
        try:
            # Use provided HTML or existing HTML
            if html_content:
                original_html = self.html
                self.set_html(html_content)
            else:
                original_html = self.html
                
            if not self.html:
                return {"case_info": {}, "plaintiffs": [], "defendants": [], "attorney": []}
            
            # Step 1: Preprocess HTML to handle JavaScript and mixed content
            cleaned_html, preprocessing_metadata = self._preprocess_html_content(self.html)
            
            # Step 2: Attempt parsing with cleaned HTML
            if preprocessing_metadata.get("preprocessing_applied", False):
                self.set_html(cleaned_html)
                self._safe_log("info", f"🧹 Applied HTML preprocessing: {preprocessing_metadata}")
            
            # Step 3: Try standard parsing first
            result = self.parse()
            
            # Step 4: Check if standard parsing was successful
            parsing_successful = (
                result.get("case_info", {}).get("versus") or 
                (result.get("plaintiffs") and len(result.get("plaintiffs", [])) > 0) or 
                (result.get("defendants") and len(result.get("defendants", [])) > 0) or
                (result.get("attorney") and len(result.get("attorney", [])) > 0)
            )
            
            # Step 5: Use alternative parsing if standard parsing failed
            if not parsing_successful and preprocessing_metadata.get("has_javascript", False):
                self._safe_log("info", "🔄 Standard parsing unsuccessful, trying alternative structure parsing...")
                self.set_html(original_html)  # Use original HTML for alternative parsing
                fallback_result = self._parse_alternative_structure()
                
                # Merge results, prioritizing fallback data where it exists
                for key in ["case_info", "plaintiffs", "defendants", "attorney"]:
                    fallback_data = fallback_result.get(key)
                    current_data = result.get(key)
                    
                    if fallback_data:
                        if key == "case_info":
                            # Merge case_info dictionaries
                            if not current_data or not isinstance(current_data, dict):
                                result[key] = fallback_data
                            else:
                                # Merge dictionaries, fallback takes precedence for missing keys
                                for fb_key, fb_value in fallback_data.items():
                                    if fb_key not in current_data or not current_data[fb_key]:
                                        result[key][fb_key] = fb_value
                        elif key in ["plaintiffs", "defendants", "attorney"]:
                            # Replace if current is empty or None
                            if not current_data or (isinstance(current_data, list) and len(current_data) == 0):
                                result[key] = fallback_data
                
                result["case_info"]["_fallback_parsing_used"] = True
                self._safe_log("info", "✅ Alternative parsing applied successfully")
            
            # Step 6: Add preprocessing metadata to result
            if preprocessing_metadata:
                result["case_info"]["_preprocessing_metadata"] = preprocessing_metadata
            
            # Step 7: Restore original HTML
            if original_html:
                self.set_html(original_html)
            
            enhancement_summary = {
                "preprocessing_applied": preprocessing_metadata.get("preprocessing_applied", False),
                "javascript_removed": preprocessing_metadata.get("has_javascript", False),
                "alternative_parsing": result["case_info"].get("_fallback_parsing_used", False),
                "transaction_receipt": preprocessing_metadata.get("has_transaction_receipt", False)
            }
            
            self._safe_log("info", f"🎯 Enhanced parsing completed: {enhancement_summary}")
            
            return result
            
        except Exception as e:
            self._safe_log("error", f"Error in enhanced parsing: {e}")
            # Return basic parsing result on error
            try:
                return self.parse()
            except:
                return {"case_info": {"_parsing_error": str(e)}, "plaintiffs": [], "defendants": [], "attorney": []}

    def parse_attorneys(self) -> list[dict[str, Any]]:
        """Extract and parse attorney information from the HTML.
        
        Returns:
            List of attorney dictionaries with improved structure including party representation
        """
        attorneys = []
        
        try:
            if not self.soup:
                return attorneys
            
            # Check if this is a removal case
            is_removal_case = self._is_removal_case()
            
            # Find the main content area
            main_content = self.soup.find("div", id="cmecfMainContent")
            if not main_content:
                return attorneys
            
            # Look for attorney and parties table
            attorney_and_parties_table = None
            
            # Strategy 1: Find table containing plaintiff/petitioner/defendant header AND represented by
            for table in main_content.find_all("table"):
                table_text = table.get_text().lower()
                if "represented" in table_text and ("plaintiff" in table_text or "petitioner" in table_text or "defendant" in table_text):
                    attorney_and_parties_table = table
                    self._safe_log("info", "Found attorney and parties table")
                    break
            
            if not attorney_and_parties_table:
                return attorneys
            
            current_party_type = None
            current_party_name = None
            
            # Process each row in the table
            for row in attorney_and_parties_table.find_all("tr"):
                row_text = row.get_text().lower()
                
                # Check if this row indicates a new party type
                if "plaintiff" in row_text or "petitioner" in row_text:
                    current_party_type = "plaintiff"
                    continue
                elif "defendant" in row_text or "respondent" in row_text:
                    current_party_type = "defendant"
                    continue
                
                # Skip if we don't have a party type yet
                if not current_party_type:
                    continue
                
                # Look for rows with party name and attorney information
                tds = row.find_all("td")
                if len(tds) >= 3:
                    party_td = tds[0]
                    represented_td = tds[1]
                    attorney_td = tds[2]
                    
                    # Check if this is a party name row
                    party_bold = party_td.find("b")
                    if party_bold and "represented" in represented_td.get_text().lower():
                        current_party_name = self.strip_whitespace(party_bold.get_text())
                        
                        # Parse attorney information from the attorney column
                        attorney_info = self._parse_attorney_from_td(
                            attorney_td, current_party_type, current_party_name, is_removal_case
                        )
                        if attorney_info:
                            attorneys.extend(attorney_info)
            
            return attorneys
            
        except Exception as e:
            self._safe_log("error", f"Error parsing attorneys: {str(e)}")
            return attorneys

    def _is_removal_case(self) -> bool:
        """Check if this is a removal case based on various indicators."""
        try:
            if not self.soup:
                return False
            
            # Check case cause for removal indicators
            text = self.soup.get_text().lower()
            removal_indicators = [
                "notice of removal",
                "28:1442 notice of removal",
                "removal",
                "petition for removal"
            ]
            
            return any(indicator in text for indicator in removal_indicators)
            
        except Exception:
            return False
    
    def _parse_attorney_from_td(self, attorney_td, party_type: str, party_name: str, is_removal_case: bool = False) -> list[dict[str, Any]]:
        """Parse attorney information from a table cell.
        
        Args:
            attorney_td: BeautifulSoup element containing attorney information
            party_type: "plaintiff" or "defendant"
            party_name: Name of the party this attorney represents
            is_removal_case: Whether this is a removal case (affects PRO SE handling)
            
        Returns:
            List of attorney dictionaries
        """
        attorneys = []
        
        try:
            attorney_text = attorney_td.get_text()
            
            # Check for PRO SE representation
            if "PRO SE" in attorney_text.upper():
                # For removal cases, exclude PRO SE from attorney list
                if is_removal_case:
                    self._safe_log("info", f"Skipping PRO SE attorney for {party_name} in removal case")
                    return attorneys
                else:
                    # For standard cases, include PRO SE with special handling
                    pro_se_attorney = {
                        "attorney_name": party_name,  # PRO SE uses party name as attorney name
                        "law_firm": "PRO SE",
                        "represents": party_type,
                        "phone": "",
                        "fax": "",
                        "email": "",
                        "address1": "",
                        "address2": "",
                        "city": "",
                        "state": "",
                        "zip_code": "",
                        "lead_attorney": False,
                        "pro_hac_vice": False,
                        "attorney_to_be_noticed": False,
                        "is_pro_se": True
                    }
                    attorneys.append(pro_se_attorney)
                    return attorneys
            
            # Parse regular attorney information
            # Split by "ATTORNEY TO BE NOTICED" to separate multiple attorneys
            attorney_blocks = attorney_text.split("ATTORNEY TO BE NOTICED")
            
            for block_idx, attorney_block in enumerate(attorney_blocks):
                if not attorney_block.strip():
                    continue
                
                # Split this block by newlines to get individual lines
                block_lines = [line.strip() for line in attorney_block.strip().split("\n") if line.strip()]
                
                if not block_lines:
                    continue
                
                # Extract attorney name (first bold element or first line)
                attorney_name = ""
                law_firm = ""
                
                # Look for bold attorney name first
                bold_elements = attorney_td.find_all("b")
                if bold_elements and block_idx < len(bold_elements):
                    attorney_name = self.strip_whitespace(bold_elements[block_idx].get_text())
                elif block_lines:
                    attorney_name = block_lines[0]
                
                # Extract law firm (typically second line)
                if len(block_lines) > 1:
                    law_firm = block_lines[1]
                
                # Skip if attorney name is the same as party name (PRO SE case)
                if attorney_name.lower() == party_name.lower():
                    continue
                
                # Add "ATTORNEY TO BE NOTICED" back if this wasn't the last block
                if block_idx < len(attorney_blocks) - 1:
                    block_lines.append("ATTORNEY TO BE NOTICED")
                
                # Parse contact information and flags
                contact_info = self._extract_attorney_contact_info(block_lines[1:] if len(block_lines) > 1 else [])
                
                attorney_record = {
                    "attorney_name": attorney_name,
                    "law_firm": law_firm,
                    "represents": party_type,
                    "phone": contact_info.get("phone", ""),
                    "fax": contact_info.get("fax", ""),
                    "email": contact_info.get("email", ""),
                    "address1": contact_info.get("address1", ""),
                    "address2": contact_info.get("address2", ""),
                    "city": contact_info.get("city", ""),
                    "state": contact_info.get("state", ""),
                    "zip_code": contact_info.get("zip_code", ""),
                    "lead_attorney": contact_info.get("lead_attorney", False),
                    "pro_hac_vice": contact_info.get("pro_hac_vice", False),
                    "attorney_to_be_noticed": contact_info.get("attorney_to_be_noticed", False),
                    "is_pro_se": False
                }
                
                attorneys.append(attorney_record)
                self._safe_log("info", f"Parsed attorney: {attorney_name} representing {party_type} {party_name}")
            
            return attorneys
            
        except Exception as e:
            self._safe_log("error", f"Error parsing attorney from TD: {str(e)}")
            return attorneys
    
    def _extract_attorney_contact_info(self, lines: list[str]) -> dict[str, Any]:
        """Extract contact information from attorney text lines.
        
        Args:
            lines: List of text lines containing attorney contact information
            
        Returns:
            Dictionary with extracted contact information and flags
        """
        contact_info = {
            "phone": "",
            "fax": "",
            "email": "",
            "address1": "",
            "address2": "",
            "city": "",
            "state": "",
            "zip_code": "",
            "lead_attorney": False,
            "pro_hac_vice": False,
            "attorney_to_be_noticed": False
        }
        
        try:
            address_lines = []
            
            for line in lines:
                line_lower = line.lower()
                
                # Check for flags
                if "lead attorney" in line_lower:
                    contact_info["lead_attorney"] = True
                elif "pro hac vice" in line_lower:
                    contact_info["pro_hac_vice"] = True
                elif "attorney to be noticed" in line_lower:
                    contact_info["attorney_to_be_noticed"] = True
                # Check for email
                elif line.startswith("Email:"):
                    contact_info["email"] = line.split("Email:", 1)[-1].strip()
                # Check for phone/fax numbers
                elif re.search(r"\d{3}-\d{3}-\d{4}|\(\d{3}\)\s*\d{3}-\d{4}", line):
                    formatted_number = self.format_phone_number(line)
                    if not contact_info["phone"]:
                        contact_info["phone"] = formatted_number
                    else:
                        contact_info["fax"] = formatted_number
                # Check for fax specifically
                elif line.startswith("Fax:"):
                    contact_info["fax"] = line.replace("Fax:", "").strip()
                # Check for city/state/zip (contains state abbreviation and zip)
                elif re.search(r"[A-Z]{2}\s+\d{5}", line):
                    # This line contains city, state, zip
                    parts = line.split(",")
                    if len(parts) >= 2:
                        contact_info["city"] = parts[0].strip()
                        state_zip_part = parts[1].strip()
                        state_zip_match = re.search(r"([A-Z]{2})\s*(\d{5}(?:-\d{4})?)", state_zip_part)
                        if state_zip_match:
                            contact_info["state"] = state_zip_match.group(1)
                            contact_info["zip_code"] = state_zip_match.group(2)
                # Special handling for "(See above for address)"
                elif "see above for address" in line_lower:
                    # Don't add this to address lines, it's a reference
                    pass
                # Collect address lines
                else:
                    # Skip empty lines and special text
                    if line.strip() and not any(skip in line_lower for skip in ["not a member", "counsel not"]):
                        address_lines.append(line.strip())
            
            # Assign address lines
            if address_lines:
                contact_info["address1"] = address_lines[0] if len(address_lines) > 0 else ""
                contact_info["address2"] = address_lines[1] if len(address_lines) > 1 else ""
            
            return contact_info
            
        except Exception as e:
            self._safe_log("error", f"Error extracting contact info: {str(e)}")
            return contact_info
