"""
HTML Processing Orchestrator Service

Provides court-aware HTML processing functionality for transformer services.
This service orchestrates HTML parsing, attorney extraction, and S3 operations
without creating cross-domain dependencies.
"""
import logging
import os
import re
from typing import Any, Dict, List, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import HTMLProcessingError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.services.html import CaseParserService, DataUpdaterService


class HTMLProcessingOrchestrator(AsyncServiceBase):
    """Orchestrates HTML processing with court-aware functionality"""
    
    def __init__(self,
                 logger: LoggerProtocol = None,
                 config: Dict[str, Any] = None,
                 court_id: Optional[str] = None,
                 html_data_updater: Optional[DataUpdaterService] = None,
                 s3_async_storage: Optional[Any] = None):
        """
        Initialize the HTML Processing Orchestrator.
        
        Args:
            logger: Logger instance
            config: Configuration dictionary
            court_id: Optional court identifier for court-specific processing
            html_data_updater: Optional DataUpdaterService instance
            s3_async_storage: Optional S3 storage service
        """
        super().__init__(logger, config)
        
        self.court_id = court_id
        self.html_data_updater = html_data_updater
        self.s3_async_storage = s3_async_storage
        
        if self.court_id:
            self.log_info(f"HTML processing orchestrator initialized for court: {court_id}")
        else:
            self.log_debug("HTML processing orchestrator initialized without specific court_id")
    
    async def _execute_action(self, data: Any) -> Any:
        """Execute HTMLProcessingOrchestrator actions."""
        if isinstance(data, dict):
            action = data.get('action')
            if action == 'process_html_content':
                case_details = data.get('case_details', {})
                html_content = data.get('html_content', '')
                json_path = data.get('json_path')
                return await self.process_html_content(case_details, html_content, json_path)
            elif action == 'extract_attorneys':
                html_content = data.get('html_content', '')
                case_details = data.get('case_details', {})
                return await self.extract_and_deduplicate_attorneys(html_content, case_details)
            elif action == 'construct_s3_link':
                json_path = data.get('json_path', '')
                case_details = data.get('case_details', {})
                return await self.construct_s3_html_link(json_path, case_details)
        raise HTMLProcessingError("Invalid action data provided to HTMLProcessingOrchestrator")
    
    async def process_html_content(self, case_details: Dict[str, Any],
                                   html_content: str, json_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Comprehensive HTML content processing with attorney extraction and S3 integration.
        
        Args:
            case_details: Case details dictionary to update
            html_content: HTML content to process
            json_path: Optional JSON file path for S3 link construction
            
        Returns:
            Updated case details with HTML-extracted information
        """
        current_court_id = self.court_id or case_details.get('court_id', 'UNKNOWN_COURT')
        log_prefix = f"[{current_court_id}][{case_details.get('docket_num', 'UNKNOWN')}] HTMLProcessing:"
        
        try:
            self.log_info(f"{log_prefix} 🔄 Starting comprehensive HTML processing")
            
            # Step 1: Parse HTML to extract structured data
            parser = CaseParserService(self.logger, html_content)
            parsed_data = parser.parse()
            
            # Step 2: Deep merge parsed data with existing case_details
            # This ensures data from HTML is properly integrated, not overwritten
            self._deep_merge_case_data(case_details, parsed_data)
            self.log_info(f"{log_prefix} ✅ Merged HTML-extracted data into case details")
            
            # Step 3: Extract attorneys from all plaintiffs with deduplication
            attorneys = await self.extract_and_deduplicate_attorneys(html_content, case_details)
            if attorneys:
                # Merge attorneys with existing ones if any
                existing_attorneys = case_details.get('attorney', [])
                if existing_attorneys:
                    # Combine and deduplicate
                    all_attorneys = existing_attorneys + attorneys
                    case_details['attorney'] = self._deduplicate_attorneys_list(all_attorneys)
                else:
                    case_details['attorney'] = attorneys
                self.log_info(f"{log_prefix} ✅ Extracted {len(attorneys)} unique attorneys")
            
            # Step 4: Upload raw HTML to S3 (CRITICAL FIX)
            # DEBUG: Add detailed logging to trace S3 upload issues
            self.log_info(f"{log_prefix} DEBUG: About to upload HTML to S3")
            self.log_info(f"{log_prefix} DEBUG: html_content length: {len(html_content) if html_content else 0}")
            self.log_info(f"{log_prefix} DEBUG: json_path: {json_path}")
            self.log_info(f"{log_prefix} DEBUG: s3_async_storage available: {self.s3_async_storage is not None}")
            
            if json_path and html_content:
                self.log_info(f"{log_prefix} DEBUG: Conditions met - calling _upload_html_to_s3")
                upload_success = await self._upload_html_to_s3(case_details, html_content, json_path)
                self.log_info(f"{log_prefix} DEBUG: Upload result: {upload_success}")
                
                if upload_success:
                    # Construct CDN URL after successful upload
                    s3_html_link = await self.construct_s3_html_link(json_path, case_details)
                    if s3_html_link:
                        case_details['s3_html'] = s3_html_link
                        self.log_info(f"{log_prefix} ✅ Raw HTML uploaded to S3: {s3_html_link}")
                else:
                    self.log_warning(f"{log_prefix} ⚠️ HTML upload to S3 failed, but continuing with processing")
            else:
                self.log_warning(f"{log_prefix} DEBUG: Upload conditions NOT met - json_path: {bool(json_path)}, html_content: {bool(html_content)}")
            
            # Step 5: Use DataUpdaterService for detailed parsing if available
            if self.html_data_updater and json_path:
                await self._integrate_html_data_updater(case_details, json_path)
                self.log_info(f"{log_prefix} ✅ DataUpdaterService integration completed")
            
            # Step 6: Process transfer information for compatibility
            if case_details.get('case_in_other_court'):
                self._process_transfer_compatibility(case_details)
                self.log_info(f"{log_prefix} ✅ Transfer compatibility processing completed")
            
            # Step 7: Ensure field consistency and deduplication
            case_details = self._ensure_field_consistency(case_details)
            
            self.log_info(f"{log_prefix} ✅ HTML processing completed successfully")
            return case_details
            
        except Exception as e:
            self.log_error(f"{log_prefix} ❌ Error in HTML processing: {e}")
            case_details['_html_processing_error'] = str(e)
            return case_details
    
    async def extract_and_deduplicate_attorneys(self, html_content: str,
                                                 case_details: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        Extract attorneys from HTML content and deduplicate them.
        
        Args:
            html_content: HTML content to parse
            case_details: Case details for context
            
        Returns:
            List of unique attorney dictionaries
        """
        try:
            # If DataUpdaterService is available, use its parser
            if self.html_data_updater and hasattr(self.html_data_updater, 'html_parser'):
                parser = self.html_data_updater.html_parser
            else:
                # Create a new parser instance
                parser = CaseParserService(self.logger, html_content)
            
            # Parse HTML to extract case information
            parsed_data = parser.parse_html(html_content)
            attorneys = []
            
            # Extract attorneys from plaintiffs
            plaintiffs = parsed_data.get('plaintiffs', [])
            for plaintiff in plaintiffs:
                if isinstance(plaintiff, dict) and 'attorney' in plaintiff:
                    plaintiff_attorneys = plaintiff['attorney']
                    if isinstance(plaintiff_attorneys, list):
                        attorneys.extend(plaintiff_attorneys)
                    elif isinstance(plaintiff_attorneys, dict):
                        attorneys.append(plaintiff_attorneys)
            
            # Deduplicate attorneys
            if self.html_data_updater:
                return self.html_data_updater._deduplicate_attorneys(attorneys)
            else:
                # Simple deduplication by email
                seen_emails = set()
                unique_attorneys = []
                for attorney in attorneys:
                    if isinstance(attorney, dict):
                        email = attorney.get('email', '').lower()
                        if email and email not in seen_emails:
                            seen_emails.add(email)
                            unique_attorneys.append(attorney)
                        elif not email:
                            # Include attorneys without email (can't deduplicate)
                            unique_attorneys.append(attorney)
                return unique_attorneys
                
        except Exception as e:
            self.log_error(f"Error extracting attorneys: {e}")
            return []
    
    async def construct_s3_html_link(self, json_path: str, case_details: Dict[str, Any]) -> Optional[str]:
        """
        Construct S3 HTML link from JSON path and case details.
        
        Args:
            json_path: Path to JSON file
            case_details: Case details for constructing filename
            
        Returns:
            S3 HTML URL or None
        """
        try:
            # Extract date from JSON path
            match = re.search(r'(\d{8})', json_path)
            if not match:
                self.log_warning(f"Could not extract date from JSON path: {json_path}")
                return None
            
            date_str = match.group(1)
            
            # Construct filename
            new_filename = case_details.get('new_filename', '')
            if not new_filename:
                self.log_warning("No new_filename in case details")
                return None
            
            # Change extension to .html
            html_filename = os.path.splitext(new_filename)[0] + '.html'
            
            # Construct S3 key
            s3_key = f"{date_str}/html/{html_filename}"
            
            # Construct CDN URL
            cdn_url = f"https://lexgeniusdev-rovo.s3.us-west-2.amazonaws.com/{s3_key}"
            
            # Verify if file exists (if S3 storage is available)
            if self.s3_async_storage:
                exists = await self._verify_s3_html_exists(cdn_url)
                if not exists:
                    self.log_warning(f"S3 HTML file does not exist: {cdn_url}")
                    return None
            
            return cdn_url
            
        except Exception as e:
            self.log_error(f"Error constructing S3 HTML link: {e}")
            return None
    
    async def _upload_html_to_s3(self, case_details: Dict[str, Any],
                                 html_content: str, json_path: str) -> bool:
        """
        Upload HTML content to S3.
        
        Args:
            case_details: Case details for constructing S3 key
            html_content: HTML content to upload
            json_path: JSON path for extracting date
            
        Returns:
            True if upload successful, False otherwise
        """
        current_court_id = self.court_id or case_details.get('court_id', 'UNKNOWN_COURT')
        log_prefix = f"[{current_court_id}][{case_details.get('docket_num', 'UNKNOWN')}] HTMLProcessing:"
        
        self.log_info(f"{log_prefix} DEBUG: _upload_html_to_s3 called")
        self.log_info(f"{log_prefix} DEBUG: html_content length: {len(html_content) if html_content else 0}")
        self.log_info(f"{log_prefix} DEBUG: json_path: {json_path}")
        self.log_info(f"{log_prefix} DEBUG: case_details new_filename: {case_details.get('new_filename', 'NOT_SET')}")
        
        if not self.s3_async_storage:
            self.log_error(f"{log_prefix} DEBUG: S3 storage not available, skipping HTML upload")
            return False
        
        try:
            # Extract ISO date from JSON path (e.g., /data/20250812/dockets/...)
            import re
            date_match = re.search(r'/(\d{8})/', json_path)
            if date_match:
                iso_date = date_match.group(1)  # YYYYMMDD format
            else:
                # Fallback to config ISO date
                iso_date = self.config.get('iso_date', '')
                if not iso_date:
                    from datetime import datetime
                    iso_date = datetime.now().strftime('%Y%m%d')
                    self.log_warning(f"No ISO date found in path or config, using today: {iso_date}")
            
            # Get base filename from case details or json path
            base_filename = case_details.get('new_filename', '')
            if not base_filename:
                base_filename = os.path.splitext(os.path.basename(json_path))[0]
            
            # Ensure .html extension
            if not base_filename.endswith('.html'):
                html_filename = f"{base_filename}.html"
            else:
                html_filename = base_filename
            
            # Construct S3 key: {iso_date}/html/{base_filename}.html
            s3_key = f"{iso_date}/html/{html_filename}"
            
            # Debug: Log S3 upload details before attempting upload
            self.log_info(f"{log_prefix} DEBUG: About to upload to S3 with key: {s3_key}")
            self.log_info(f"{log_prefix} DEBUG: HTML content first 100 chars: {html_content[:100] if html_content else 'EMPTY'}")
            self.log_info(f"{log_prefix} DEBUG: S3 storage type: {type(self.s3_async_storage)}")
            
            # Upload to S3 with error handling
            try:
                self.log_info(f"{log_prefix} DEBUG: Calling s3_async_storage.upload_content...")
                success = await self.s3_async_storage.upload_content(
                    content=html_content, 
                    object_key=s3_key, 
                    content_type='text/html',
                    overwrite=True  # Always overwrite to ensure latest version
                )
                self.log_info(f"{log_prefix} DEBUG: S3 upload_content returned: {success}")
                
                if success:
                    self.log_info(f"✅ Successfully uploaded HTML to S3: {s3_key}")
                    # Store the S3 key for reference
                    case_details['s3_html_key'] = s3_key
                    # Also construct the CDN URL
                    case_details['s3_html'] = f"https://cdn.lexgenius.ai/{s3_key}"
                else:
                    self.log_error(f"❌ Failed to upload HTML to S3: {s3_key}")
                
                return success
                
            except Exception as upload_error:
                self.log_error(f"❌ S3 upload error for {s3_key}: {upload_error}")
                # Continue processing even if upload fails
                return False
                
        except Exception as e:
            self.log_error(f"Error preparing HTML for S3 upload: {e}")
            return False
    
    async def _verify_s3_html_exists(self, s3_url: str) -> bool:
        """
        Verify if S3 HTML file exists.
        
        Args:
            s3_url: S3 URL to verify
            
        Returns:
            True if file exists, False otherwise
        """
        if not self.s3_async_storage:
            return True  # Assume exists if we can't verify
        
        try:
            # Extract S3 key from URL
            if 's3.us-west-2.amazonaws.com/' in s3_url:
                s3_key = s3_url.split('s3.us-west-2.amazonaws.com/')[-1]
            else:
                return False
            
            # Check if file exists
            return await self.s3_async_storage.file_exists(s3_key)
            
        except Exception as e:
            self.log_error(f"Error verifying S3 file existence: {e}")
            return False
    
    async def _integrate_html_data_updater(self, case_details: Dict[str, Any], json_path: str):
        """
        Integrate with DataUpdaterService for comprehensive HTML parsing.
        
        Args:
            case_details: Case details to update
            json_path: JSON file path
        """
        if not self.html_data_updater:
            return
        
        try:
            # Call DataUpdaterService for detailed parsing
            updated = await self.html_data_updater.update_with_pacer_html(json_path)
            if updated:
                self.log_debug("DataUpdaterService successfully processed HTML")
            else:
                self.log_warning("DataUpdaterService did not update case details")
                
        except Exception as e:
            self.log_error(f"Error integrating with DataUpdaterService: {e}")
    
    def _process_transfer_compatibility(self, case_details: Dict[str, Any]):
        """
        Process transfer information for compatibility.
        
        Args:
            case_details: Case details to process
        """
        try:
            # Handle transfer case compatibility
            if case_details.get('case_in_other_court'):
                transfer_info = case_details.get('case_in_other_court', {})
                
                # Ensure transfer fields are populated
                if isinstance(transfer_info, dict):
                    if 'court' in transfer_info:
                        case_details['transferee_court_id'] = transfer_info['court']
                    if 'docket_number' in transfer_info:
                        case_details['transferee_docket_num'] = transfer_info['docket_number']
                
                # Mark as transferred
                case_details['is_transferred'] = True
                
        except Exception as e:
            self.log_error(f"Error processing transfer compatibility: {e}")
    
    def _ensure_field_consistency(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Ensure field consistency and handle deduplication.
        
        Args:
            case_details: Case details to process
            
        Returns:
            Processed case details
        """
        try:
            # Merge plaintiff/plaintiffs fields
            if 'plaintiffs' in case_details and 'plaintiff' not in case_details:
                plaintiffs = case_details['plaintiffs']
                if isinstance(plaintiffs, list) and plaintiffs:
                    # Extract names from plaintiff objects
                    plaintiff_names = []
                    for p in plaintiffs:
                        if isinstance(p, dict):
                            name = p.get('name', '')
                        else:
                            name = str(p)
                        if name:
                            plaintiff_names.append(name)
                    
                    # Deduplicate
                    seen = set()
                    case_details['plaintiff'] = [
                        x for x in plaintiff_names if not (x in seen or seen.add(x))
                    ]
            
            # Merge defendant/defendants fields
            if 'defendants' in case_details and 'defendant' not in case_details:
                defendants = case_details['defendants']
                if isinstance(defendants, list):
                    # Deduplicate defendants
                    seen = set()
                    unique_defendants = []
                    for d in defendants:
                        if isinstance(d, dict):
                            name = d.get('name', '')
                        else:
                            name = str(d)
                        name_lower = name.lower()
                        if name and name_lower not in seen:
                            unique_defendants.append(name)
                            seen.add(name_lower)
                    case_details['defendant'] = unique_defendants
            
            # Clean up plural fields to avoid confusion
            fields_to_remove = ['plaintiffs', 'defendants', 'plaintiffs_gpt']
            for field in fields_to_remove:
                if field in case_details:
                    del case_details[field]
            
            return case_details
            
        except Exception as e:
            self.log_error(f"Error ensuring field consistency: {e}")
            return case_details
    
    def _deep_merge_case_data(self, case_details: Dict[str, Any], parsed_data: Dict[str, Any]) -> None:
        """
        Deep merge parsed HTML data into existing case details.
        HTML data OVERWRITES existing fields but NEVER deletes fields not present in HTML.
        
        Args:
            case_details: Existing case details dictionary (modified in-place)
            parsed_data: Parsed data from HTML to merge
        """
        # Merge case_info fields - HTML OVERWRITES existing values
        if 'case_info' in parsed_data:
            case_info = parsed_data['case_info']
            for key, value in case_info.items():
                if value is not None:  # Only overwrite if HTML has a value
                    case_details[key] = value  # OVERWRITE existing value
        
        # OVERWRITE plaintiffs if HTML has them
        if 'plaintiffs' in parsed_data and parsed_data['plaintiffs']:
            new_plaintiffs = []
            for p in parsed_data['plaintiffs']:
                if isinstance(p, dict) and 'name' in p:
                    new_plaintiffs.append(p['name'])
                elif isinstance(p, str):
                    new_plaintiffs.append(p)
            
            # REPLACE with HTML data (deduplicated)
            if new_plaintiffs:
                seen = set()
                case_details['plaintiff'] = [x for x in new_plaintiffs if not (x in seen or seen.add(x))]
        # If HTML doesn't have plaintiffs, keep existing ones
        
        # OVERWRITE defendants if HTML has them
        if 'defendants' in parsed_data and parsed_data['defendants']:
            new_defendants = []
            for d in parsed_data['defendants']:
                if isinstance(d, str):
                    new_defendants.append(d)
            
            # REPLACE with HTML data (deduplicated)
            if new_defendants:
                seen = set()
                case_details['defendant'] = [x for x in new_defendants if not (x.lower() in seen or seen.add(x.lower()))]
        # If HTML doesn't have defendants, keep existing ones
        
        # Merge any other top-level fields from parsed_data
        for key, value in parsed_data.items():
            if key not in ['case_info', 'plaintiffs', 'defendants'] and value is not None:
                case_details[key] = value  # OVERWRITE with HTML value
    
    def set_s3_manager(self, s3_manager: Any):
        """
        Set the S3 manager for this service.
        
        Args:
            s3_manager: S3 manager instance
        """
        self.s3_async_storage = s3_manager
        self.log_debug("S3 manager set for HTML processing orchestrator")
    
    def set_html_data_updater(self, html_data_updater: DataUpdaterService):
        """
        Set the HTML data updater service.
        
        Args:
            html_data_updater: DataUpdaterService instance
        """
        self.html_data_updater = html_data_updater
        self.log_debug("HTML data updater set for processing orchestrator")
    
    def _deduplicate_attorneys_list(self, attorneys: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Deduplicate a list of attorney dictionaries.
        
        Args:
            attorneys: List of attorney dictionaries
            
        Returns:
            Deduplicated list of attorneys
        """
        if not attorneys:
            return []
        
        unique_attorneys = []
        seen_attorneys = set()
        
        for attorney in attorneys:
            if not isinstance(attorney, dict):
                continue
            
            attorney_name = attorney.get('attorney_name', '').strip()
            law_firm = attorney.get('law_firm', '').strip()
            
            # Skip if no name
            if not attorney_name:
                continue
            
            # Create unique key
            attorney_key = (attorney_name.lower(), law_firm.lower())
            
            if attorney_key not in seen_attorneys:
                seen_attorneys.add(attorney_key)
                unique_attorneys.append(attorney)
        
        return unique_attorneys
    
    def validate_html_processing_results(self, updated_data: Dict[str, Any],
                                         original_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate HTML processing results and generate report.
        
        Args:
            updated_data: Data after HTML processing
            original_data: Original data before processing
            
        Returns:
            Validation report with changes and issues
        """
        validation_report = {
            'valid': True,
            'changes': [],
            'issues': [],
            'stats': {
                'attorneys_added': 0,
                'plaintiffs_added': 0,
                'defendants_added': 0,
                's3_html_added': False
            }
        }
        
        try:
            # Check attorney changes
            original_attorneys = original_data.get('attorney', [])
            updated_attorneys = updated_data.get('attorney', [])
            if len(updated_attorneys) > len(original_attorneys):
                attorneys_added = len(updated_attorneys) - len(original_attorneys)
                validation_report['stats']['attorneys_added'] = attorneys_added
                validation_report['changes'].append(f"Added {attorneys_added} attorneys")
            
            # Check plaintiff changes
            if 'plaintiff' in updated_data and 'plaintiff' not in original_data:
                validation_report['changes'].append("Added plaintiff information")
                validation_report['stats']['plaintiffs_added'] = len(updated_data.get('plaintiff', []))
            
            # Check defendant changes
            if 'defendant' in updated_data and 'defendant' not in original_data:
                validation_report['changes'].append("Added defendant information")
                validation_report['stats']['defendants_added'] = len(updated_data.get('defendant', []))
            
            # Check S3 HTML link
            if 's3_html' in updated_data and 's3_html' not in original_data:
                validation_report['changes'].append("Added S3 HTML link")
                validation_report['stats']['s3_html_added'] = True
            
            # Check for errors
            if '_html_processing_error' in updated_data:
                validation_report['valid'] = False
                validation_report['issues'].append(f"Processing error: {updated_data['_html_processing_error']}")
            
            return validation_report
            
        except Exception as e:
            self.log_error(f"Error validating HTML processing results: {e}")
            validation_report['valid'] = False
            validation_report['issues'].append(f"Validation error: {str(e)}")
            return validation_report