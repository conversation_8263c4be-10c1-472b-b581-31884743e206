"""
Enhanced Factory for creating HTML service instances with improved S3 injection.

This factory provides centralized creation of HTML services with proper dependency injection
and enhanced S3 service integration.
"""

from typing import Dict, Any, TYPE_CHECKING

from src.services.html.case_parser_service import CaseParserService
from src.services.html.data_updater_service import DataUpdaterService
from src.services.html.html_processing_orchestrator import HTMLProcessingOrchestrator
from src.services.html.enhanced_html_processing_orchestrator import EnhancedHTMLProcessingOrchestrator
from src.pacer.utils.s3_injection_helper import S3InjectionHelper

if TYPE_CHECKING:
    from src.repositories.pacer_repository import PacerRepository
    from src.infrastructure.storage.s3_async import S3AsyncStorage
    from src.infrastructure.protocols.logger import LoggerProtocol


class EnhancedHtmlServiceFactory:
    """Enhanced factory for creating HTML service instances with improved S3 injection."""

    @staticmethod
    def create_case_parser(logger: 'LoggerProtocol', html_content: str = None) -> CaseParserService:
        """
        Create a CaseParserService instance.
        
        Args:
            logger: Logger instance
            html_content: Optional HTML content to initialize the parser with
            
        Returns:
            CaseParserService instance
        """
        return CaseParserService(logger, html_content)

    @staticmethod
    def create_data_updater(
            logger: 'LoggerProtocol',
            config: Dict,
            s3_manager: 'S3AsyncStorage',
            pacer_db: 'PacerRepository'
        ) -> DataUpdaterService:
        """
        Create a DataUpdaterService instance with dependencies.
        
        Args:
            logger: Logger instance
            config: Configuration dictionary
            s3_manager: S3 async storage instance
            pacer_db: PACER repository instance
            
        Returns:
            DataUpdaterService instance
        """
        # Create html_parser instance for DataUpdaterService
        html_parser = CaseParserService(logger)
        return DataUpdaterService(logger, config, s3_manager, pacer_db, html_parser)

    @staticmethod
    def create_html_processing_orchestrator(
            logger: 'LoggerProtocol',
            config: Dict,
            court_id: str = None,
            html_data_updater: DataUpdaterService = None,
            s3_async_storage: 'S3AsyncStorage' = None
        ) -> HTMLProcessingOrchestrator:
        """
        Create an HTMLProcessingOrchestrator instance.
        
        Args:
            logger: Logger instance
            config: Configuration dictionary
            court_id: Optional court identifier
            html_data_updater: Optional DataUpdaterService instance
            s3_async_storage: Optional S3 async storage instance
            
        Returns:
            HTMLProcessingOrchestrator instance
        """
        return HTMLProcessingOrchestrator(
            logger=logger,
            config=config,
            court_id=court_id,
            html_data_updater=html_data_updater,
            s3_async_storage=s3_async_storage
        )
    
    @staticmethod
    def create_enhanced_html_processing_orchestrator(
            logger: 'LoggerProtocol',
            config: Dict,
            court_id: str = None,
            html_data_updater: DataUpdaterService = None,
            s3_async_storage: 'S3AsyncStorage' = None,
            s3_manager: Any = None
        ) -> EnhancedHTMLProcessingOrchestrator:
        """
        Create an EnhancedHTMLProcessingOrchestrator instance with improved S3 support.
        
        Args:
            logger: Logger instance
            config: Configuration dictionary
            court_id: Optional court identifier
            html_data_updater: Optional DataUpdaterService instance
            s3_async_storage: Optional S3 async storage instance
            s3_manager: Optional S3 manager component
            
        Returns:
            EnhancedHTMLProcessingOrchestrator instance
        """
        return EnhancedHTMLProcessingOrchestrator(
            logger=logger,
            config=config,
            court_id=court_id,
            html_data_updater=html_data_updater,
            s3_async_storage=s3_async_storage,
            s3_manager=s3_manager
        )
    
    @staticmethod
    def create_html_processing_orchestrator_with_s3_injection(
            logger: 'LoggerProtocol',
            config: Dict,
            s3_async_storage: 'S3AsyncStorage',
            court_id: str = None,
            html_data_updater: DataUpdaterService = None,
            enhanced: bool = True
        ):
        """
        Create an HTML processing orchestrator with guaranteed S3 injection.
        
        This method uses the S3InjectionHelper to ensure proper S3 service injection
        and validation.
        
        Args:
            logger: Logger instance
            config: Configuration dictionary
            s3_async_storage: S3 async storage instance (required)
            court_id: Optional court identifier
            html_data_updater: Optional DataUpdaterService instance
            enhanced: Whether to create enhanced orchestrator (default: True)
            
        Returns:
            HTMLProcessingOrchestrator or EnhancedHTMLProcessingOrchestrator instance
        """
        if enhanced:
            orchestrator = EnhancedHTMLProcessingOrchestrator(
                logger=logger,
                config=config,
                court_id=court_id,
                html_data_updater=html_data_updater,
                s3_async_storage=s3_async_storage
            )
        else:
            orchestrator = S3InjectionHelper.create_html_processing_orchestrator_with_s3(
                s3_async_storage=s3_async_storage,
                logger=logger,
                config=config,
                court_id=court_id
            )
        
        # Validate S3 injection
        S3InjectionHelper.verify_s3_injection(orchestrator, logger)
        
        return orchestrator
    
    @staticmethod
    def create_html_processing_bundle_with_dependencies(
            logger: 'LoggerProtocol',
            config: Dict,
            s3_async_storage: 'S3AsyncStorage',
            pacer_db: 'PacerRepository' = None,
            court_id: str = None
        ) -> Dict[str, Any]:
        """
        Create a complete HTML processing bundle with all dependencies.
        
        Args:
            logger: Logger instance
            config: Configuration dictionary
            s3_async_storage: S3 async storage instance
            pacer_db: Optional PACER repository instance
            court_id: Optional court identifier
            
        Returns:
            Dictionary containing all HTML processing components
        """
        # Create case parser
        case_parser = EnhancedHtmlServiceFactory.create_case_parser(logger)
        
        # Create data updater if repository is available
        data_updater = None
        if pacer_db:
            data_updater = EnhancedHtmlServiceFactory.create_data_updater(
                logger=logger,
                config=config,
                s3_manager=s3_async_storage,
                pacer_db=pacer_db
            )
        
        # Create enhanced orchestrator
        orchestrator = EnhancedHtmlServiceFactory.create_enhanced_html_processing_orchestrator(
            logger=logger,
            config=config,
            court_id=court_id,
            html_data_updater=data_updater,
            s3_async_storage=s3_async_storage
        )
        
        # Create S3 service bundle
        s3_bundle = S3InjectionHelper.create_s3_service_bundle(s3_async_storage, logger)
        
        return {
            'case_parser': case_parser,
            'data_updater': data_updater,
            'html_processing_orchestrator': orchestrator,
            's3_bundle': s3_bundle,
            'logger': logger,
            'config': config,
            'court_id': court_id,
            's3_injection_verified': S3InjectionHelper.verify_s3_injection(orchestrator, logger)
        }