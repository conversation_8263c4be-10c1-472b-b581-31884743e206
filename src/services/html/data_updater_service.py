import asyncio
import json
import os
from time import sleep
from typing import TYPE_CHECKING

import requests

# Removed dependency_injector imports - using container-based injection
from src.infrastructure.protocols.logger import LoggerProtocol
from src.services.html.case_parser_service import CaseParserService

if TYPE_CHECKING:
    from src.infrastructure.storage.s3_async import S3AsyncStorage
    from src.repositories.pacer_repository import PacerRepository


class DataUpdaterService:
    """Service for updating JSON data with information extracted from PACER HTML."""

    def __init__(
        self,
        logger: LoggerProtocol,
        config: dict,
        s3_manager: "S3AsyncStorage",
        pacer_db: "PacerRepository",
        html_parser: CaseParserService,
    ):
        self.logger = logger
        self.config = config
        # Initialize S3 manager with fallback handling
        if s3_manager is None:
            self.logger.warning(
                "S3 manager not properly injected - S3 operations will be disabled"
            )
        self.s3_manager = s3_manager
        self.pacer_db = pacer_db
        self.html_parser = html_parser

    def update_with_pacer_html(self, json_path: str, data: dict) -> None:
        """Update JSON data with PACER HTML information."""
        try:
            # Ensure 'new_filename' which is used for constructing S3 paths is present
            if "new_filename" not in data:
                if "original_filename" in data:
                    data["new_filename"] = data["original_filename"]
                else:
                    # Fallback: derive from json_path if other fields are missing
                    base_name = os.path.basename(json_path)
                    data["new_filename"] = base_name.replace(".json", "")
                    self.logger.info(
                        f"Derived 'new_filename': {data['new_filename']} from json_path for HTML processing."
                    )

            pdf_path = json_path.replace(
                ".json", ".pdf"
            )  # For logging/checking presence
            if not os.path.exists(pdf_path):
                self.logger.warning(
                    f"No local PDF found at {pdf_path} while preparing for HTML update."
                )
                if not data.get("s3_link"):  # s3_link for the PDF document itself
                    self.logger.warning(
                        f"No local PDF and no s3_link (for PDF) found for {json_path}. HTML processing might be less effective if it relies on PDF context not available."
                    )
                else:
                    self.logger.info(
                        f"No local PDF, but s3_link for PDF exists: {data.get('s3_link')}"
                    )

            # --- Determine the CDN URL for the HTML content ---
            final_cdn_url = None
            s3_html_in_data = data.get(
                "s3_html"
            )  # This might be a full URL, an S3 key, or absent

            if s3_html_in_data and isinstance(s3_html_in_data, str):
                if s3_html_in_data.startswith("http"):
                    # CRITICAL: Validate and fix old incorrect URL format
                    if "/data/html/" in s3_html_in_data:
                        self.logger.warning(
                            f"Detected old incorrect S3 URL format: {s3_html_in_data}"
                        )
                        # Extract filename and reconstruct with correct format
                        filename = s3_html_in_data.split("/")[-1]
                        iso_date = self.config.get("iso_date", "")
                        if iso_date:
                            final_cdn_url = f"https://cdn.lexgenius.ai/{iso_date}/html/{filename}"
                            self.logger.info(
                                f"Corrected S3 URL format: {final_cdn_url}"
                            )
                        else:
                            # If no iso_date, try to extract from filename pattern
                            self.logger.warning(
                                f"No iso_date in config, will search for HTML file by pattern"
                            )
                            final_cdn_url = None
                    else:
                        final_cdn_url = s3_html_in_data
                        self.logger.info(
                            f"Using pre-existing full HTML URL from data: {final_cdn_url}"
                        )
                elif s3_html_in_data.startswith(
                    "/"
                ):  # Assumed to be an S3 key like /YYYYMMDD/html/file.html
                    final_cdn_url = f"https://cdn.lexgenius.ai{s3_html_in_data}"
                    self.logger.info(
                        f"Constructed HTML URL from s3_html key in data: {final_cdn_url}"
                    )
                # else: s3_html_in_data is present but not a usable URL or key, proceed to construct/verify

            if not final_cdn_url:
                # Don't construct URL from json path - search for the actual file
                self.logger.info(
                    "No s3_html in data, will search for HTML file by pattern"
                )

                # HTML files are always in the current date directory
                iso_date = self.config.get("iso_date")
                s3_html_dir_path = (
                    None  # Initialize to None to avoid undefined variable errors
                )

                if iso_date:
                    s3_html_dir_path = (
                        f"{iso_date}/html/"  # Use current date, not JSON file date
                    )

                    # Build search pattern from court_id and docket_num
                    court_id = data.get("court_id", "").strip()
                    docket_num = data.get("docket_num", "").strip()

                    self.logger.debug(
                        f"DataUpdaterService S3 search - court_id: '{court_id}', docket_num: '{docket_num}'"
                    )

                    if court_id and docket_num:
                        # Extract YY: two digits after the colon
                        import re

                        year_match = re.search(r":(\d{2})", docket_num)
                        # Extract NNNNN: 5 consecutive digits after the case type (cv, sf, etc.)
                        num_match = re.search(r"-([a-zA-Z]{2})-(\d{5})", docket_num)

                        if year_match and num_match:
                            year_str = year_match.group(1)
                            num_str = num_match.group(2)

                            # Build search pattern: {court_id}_{YY}_{NNNNN}
                            search_pattern = f"{court_id}_{year_str}_{num_str}"
                            self.logger.info(
                                f"S3 HTML search pattern: {search_pattern}"
                            )

                            try:
                                # list_existing_files returns S3 keys relative to bucket root
                                self.logger.info(
                                    f"Listing S3 files with prefix: {s3_html_dir_path}"
                                )
                                matching_s3_keys = self.s3_manager.list_existing_files(
                                    prefix=s3_html_dir_path
                                )
                                self.logger.info(
                                    f"Found {len(matching_s3_keys) if matching_s3_keys else 0} files in {s3_html_dir_path}"
                                )
                                found_s3_keys = []

                                if matching_s3_keys:
                                    # Find files that BEGIN with our search pattern (case-insensitive)
                                    search_pattern_lower = search_pattern.lower()
                                    for key in matching_s3_keys:
                                        s3_filename = os.path.basename(key)
                                        if s3_filename.lower().startswith(
                                            search_pattern_lower
                                        ):
                                            found_s3_keys.append(key)
                                            self.logger.debug(
                                                f"Found S3 HTML file matching pattern: {s3_filename}"
                                            )

                                if found_s3_keys:
                                    # Use the first match
                                    if len(found_s3_keys) > 1:
                                        self.logger.warning(
                                            f"Multiple S3 HTML files found for pattern '{search_pattern}': {found_s3_keys}. "
                                            f"Using first match."
                                        )
                                    found_s3_key = found_s3_keys[0]
                                    final_cdn_url = (
                                        f"https://cdn.lexgenius.ai/{found_s3_key}"
                                    )
                                    self.logger.info(
                                        f"Found HTML URL via pattern search: {final_cdn_url}"
                                    )
                                else:
                                    self.logger.warning(
                                        f"No S3 HTML file found starting with pattern: {search_pattern}. "
                                        f"Searched {len(matching_s3_keys)} files in {s3_html_dir_path}"
                                    )
                                    # Don't use constructed URL - it has wrong capitalization
                                    final_cdn_url = None
                            except Exception as s3_list_err:
                                self.logger.error(
                                    f"Error listing S3 files for prefix '{s3_html_dir_path}': {s3_list_err}"
                                )
                                final_cdn_url = None
                        else:
                            self.logger.warning(
                                f"Could not extract YY/NNNNN from docket_num: {docket_num}"
                            )
                            final_cdn_url = None
                    else:
                        self.logger.warning(
                            "Missing court_id or docket_num for pattern-based search"
                        )
                        final_cdn_url = None
                else:
                    self.logger.error(
                        "iso_date not found in config, cannot search for HTML files in S3"
                    )
                    final_cdn_url = None

            if not final_cdn_url:
                self.logger.error(
                    f"CRITICAL: Failed to determine a valid CDN URL for HTML processing for {os.path.basename(json_path)}. Skipping HTML update."
                )
                data["s3_html_error"] = "CDN URL determination failed"
                data["s3_html"] = None
                # Save data with error and return, as further steps rely on HTML
                self._save_json(json_path, data)
                return

            data["s3_html"] = final_cdn_url  # Store the finalized CDN URL

            # --- Fetch and Parse HTML ---
            try:
                case_info = self._extract_case_info_from_html(
                    final_cdn_url
                )  # Uses the determined final_cdn_url
                self.logger.debug(
                    f"Extracted case info from {final_cdn_url}: {json.dumps(case_info, indent=2)}"
                )
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 403 or e.response.status_code == 404:
                    self.logger.warning(
                        f"CDN access forbidden or file not found for {final_cdn_url} (Status: {e.response.status_code}). Will fall back to HTML file search."
                    )
                    data["s3_html_error"] = f"CDN Error {e.response.status_code}"
                    # Attempt fallback search for HTML file
                    fallback_url = self._search_for_html_file(data)
                    if fallback_url:
                        self.logger.info(f"Found fallback HTML file: {fallback_url}")
                        final_cdn_url = fallback_url
                        # Try to extract case info from the fallback URL
                        try:
                            case_info = self._extract_case_info_from_html(fallback_url)
                            self.logger.debug(f"Extracted case info from fallback URL: {json.dumps(case_info, indent=2)}")
                        except Exception as fallback_err:
                            self.logger.error(f"Failed to extract case info from fallback URL {fallback_url}: {fallback_err}")
                            data["s3_html_error"] = f"Fallback extraction failed: {fallback_err}"
                            self._save_json(json_path, data)
                            return
                    else:
                        self.logger.error("No fallback HTML file found")
                        data["s3_html_error"] = f"CDN Error {e.response.status_code} - No fallback found"
                        self._save_json(json_path, data)
                        return
                else:
                    self.logger.error(
                        f"HTTPError fetching HTML from {final_cdn_url}: {e}",
                        exc_info=True,
                    )
                    raise  # Re-raise other HTTP errors
            except (
                Exception
            ) as extract_err:  # Catch other errors from _extract_case_info_from_html
                self.logger.error(
                    f"Error extracting case info from HTML ({final_cdn_url}): {extract_err}",
                    exc_info=True,
                )
                data["s3_html_error"] = f"Extraction Error: {extract_err}"
                self._save_json(json_path, data)
                return

            # --- Update data with extracted info ---
            if case_info.get(
                "attorney"
            ):  # 'attorney' key is set by _extract_case_info_from_html
                # Apply deduplication to attorneys from case info extraction with party representation
                deduplicated_attorneys = self._deduplicate_attorneys(
                    case_info["attorney"]
                )
                
                # Additional filtering for removal cases
                if case_info.get("is_removal"):
                    filtered_attorneys = []
                    for attorney in deduplicated_attorneys:
                        attorney_name = attorney.get("attorney_name", "").strip().upper()
                        law_firm = attorney.get("law_firm", "").strip().upper()
                        
                        # In removal cases, filter out PRO SE more aggressively
                        if (attorney_name not in ["PRO SE", "PRO-SE", ""] and 
                            law_firm not in ["PRO SE", "PRO-SE", ""] and
                            attorney_name != law_firm):
                            filtered_attorneys.append(attorney)
                        else:
                            self.logger.debug(f"Filtered PRO SE attorney in removal case: {attorney_name}")
                    
                    deduplicated_attorneys = filtered_attorneys
                
                data["attorney"] = deduplicated_attorneys
                self.logger.info(
                    f"Extracted {len(deduplicated_attorneys)} unique attorneys (after deduplication and party association)"
                )
            data["lead_case"] = case_info.get("lead_case")
            data["case_in_other_court"] = case_info.get("case_in_other_court")
            if case_info.get("is_removal"):
                data["is_removal"] = case_info.get("is_removal")
            if case_info.get("removal_date"):
                data["removal_date"] = case_info.get("removal_date")
            if case_info.get("initial_filing_date"):
                data["date_filed"] = case_info.get(
                    "initial_filing_date"
                )  # Ensure 'date_filed' is updated

            if data.get("case_in_other_court"):
                self._process_transfer_info(data)

            # _update_json_with_pacer_html re-parses. Could be optimized to pass parsed_content.
            # For now, let it re-fetch and re-parse if it has additional logic.
            self._update_json_with_pacer_html(
                json_path, data
            )  # data['s3_html'] is now the final_cdn_url

            self._save_json(json_path, data)
            self.logger.info(
                f"Successfully updated and saved JSON with PACER HTML data for {os.path.basename(json_path)}"
            )

        except Exception as e:
            self.logger.error(
                f"Error in update_with_pacer_html for {os.path.basename(json_path)}: {str(e)}",
                exc_info=True,
            )
            # Attempt to save error state to data if possible
            if isinstance(data, dict):
                data["html_update_error"] = str(e)
                self._save_json(json_path, data)

    @staticmethod
    def _convert_url_to_cdn_format(file_url: str) -> str:
        """Convert a file URL to CDN format."""
        parts = file_url.split("/")

        # Handle both nested (data/2025/07/15/court_id/file.json) and flat (data/20250715/court_id/file.json) structures
        if len(parts) >= 6 and parts[-6] == "data" and len(parts[-5]) == 4 and parts[-5].isdigit():
            # Nested structure: data/2025/07/15/court_id/file.json
            year, month, day = parts[-5], parts[-4], parts[-3]
            date_part = f"{year}{month.zfill(2)}{day.zfill(2)}"
        else:
            # Flat structure: data/20250715/court_id/file.json
            date_part = parts[-3]

        filename = parts[-1].replace(".json", ".html")
        return f"https://cdn.lexgenius.ai/{date_part}/html/{filename}"

    def _extract_case_info_from_html(
        self, url: str, max_retries: int = 3, retry_delay: int = 1
    ) -> dict:
        """Extract case information from HTML with retry mechanism."""
        for attempt in range(max_retries):
            try:
                self.logger.debug(f"Attempting to fetch HTML from URL: {url}")
                # Add proper headers to avoid 403 Forbidden from CDN
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                }
                response = requests.get(url, headers=headers, timeout=10)
                self.logger.debug(f"Response status: {response.status_code}")
                response.raise_for_status()
                html_content = response.text
                self.logger.debug(f"Successfully fetched HTML content, length: {len(html_content)}")

                # Use logger directly - no validation needed with proper DI

                parser = CaseParserService(self.logger, html_content)
                parsed_content = parser.parse()
                case_info = parsed_content.get("case_info", {})

                # Get attorneys directly from parser result and add party representation
                attorneys_with_party = []
                plaintiffs = parsed_content.get("plaintiffs", [])
                defendants = parsed_content.get("defendants", [])
                
                # Extract attorneys from plaintiffs and mark as representing plaintiffs
                for plaintiff in plaintiffs:
                    if isinstance(plaintiff, dict):
                        plaintiff_name = plaintiff.get("name", "")
                        for attorney in plaintiff.get("attorneys", []):
                            if isinstance(attorney, dict):
                                attorney_with_party = attorney.copy()
                                attorney_with_party["represents"] = "plaintiff"
                                attorney_with_party["represents_party"] = plaintiff_name
                                attorneys_with_party.append(attorney_with_party)
                
                # Extract attorneys from defendants (if any) and mark as representing defendants
                # Note: In most cases, defendants don't have attorneys listed in the standard structure
                # but this handles cases where they might be in removal cases or other scenarios
                if isinstance(defendants, list):
                    for defendant in defendants:
                        if isinstance(defendant, dict) and defendant.get("attorneys"):
                            defendant_name = defendant.get("name", "")
                            for attorney in defendant.get("attorneys", []):
                                if isinstance(attorney, dict):
                                    attorney_with_party = attorney.copy()
                                    attorney_with_party["represents"] = "defendant"
                                    attorney_with_party["represents_party"] = defendant_name
                                    attorneys_with_party.append(attorney_with_party)
                
                # Also include any attorneys from the direct attorney list (fallback)
                direct_attorneys = parsed_content.get("attorney", [])
                for attorney in direct_attorneys:
                    if isinstance(attorney, dict):
                        attorney_with_party = attorney.copy()
                        # If no represents field, try to infer or mark as unknown
                        if "represents" not in attorney_with_party:
                            attorney_with_party["represents"] = "unknown"
                            attorney_with_party["represents_party"] = ""
                        attorneys_with_party.append(attorney_with_party)
                
                case_info["attorney"] = attorneys_with_party

                # Check if this is a removal case
                is_removal = parser.is_removal()
                if is_removal:
                    case_info["is_removal"] = True
                    case_info["removal_date"] = is_removal.get("removal_date")
                else:
                    case_info["is_removal"] = False

                # Get initial filing date
                filing_info = parser.get_initial_filing_date()
                if filing_info:
                    case_info["initial_filing_date"] = filing_info.get(
                        "initial_filing_date"
                    )

                return case_info

            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    self.logger.error(f"Final attempt failed for URL {url}: {str(e)}")
                    raise
                # Use safe warning to catch logger corruption
                self.logger.warning(
                    f"Attempt {attempt + 1} failed: {str(e)}. Retrying..."
                )
                sleep(retry_delay * (attempt + 1))
        return {"attorney": [], "lead_case": None, "case_in_other_court": None}

    @staticmethod
    def check_and_convert_s3_link(data: dict) -> None:
        """Convert s3_link format to s3_html format if needed."""
        if "S3Link" in data:
            data["s3_link"] = data["S3Link"]

        if "s3_link" in data and data["s3_link"]:
            s3_link = data["s3_link"]
            if not s3_link.startswith("http"):
                # Add https:// prefix if missing
                s3_link = f"https://{s3_link}"

            # Extract the path portion after the domain
            path_parts = s3_link.split("/", 3)
            if len(path_parts) >= 4:
                # Store just the path part in s3_html
                data["s3_html"] = path_parts[3]
            else:
                data["s3_html"] = None
        else:
            data["s3_html"] = None

    def _process_transfer_info(self, data: dict) -> None:
        """
        Process transfer information from case_in_other_court field.
        """
        try:
            # Split case information into court name and docket number
            case_info = data.get("case_in_other_court", "")
            if "," in case_info:
                transferor_docket_num = case_info.split(",")[1].strip()
                transferor_court_name = case_info.split(",")[0].strip()

                data["transferor_court_docket_num"] = transferor_docket_num
                data["transferor_court"] = transferor_court_name
                self.logger.info(
                    f"Processed transfer info: {transferor_court_name}, {transferor_docket_num}"
                )
        except Exception as e:
            self.logger.error(f"Error processing transfer info: {str(e)}")

    def _update_json_with_pacer_html(self, json_path: str, data: dict) -> None:
        """Update JSON data with information from PACER HTML."""
        # This method handles both relative and absolute URLs for s3_html
        try:
            url = data.get("s3_html")
            if not url or not isinstance(url, str):
                self.logger.warning(
                    f"Invalid or missing s3_html URL in data for {os.path.basename(json_path)}: '{url}'. Skipping detailed parse."
                )
                return

            # Convert relative path to full CDN URL if needed
            if not url.startswith("http"):
                # Remove leading slash if present
                url = url.lstrip("/")
                url = f"https://cdn.lexgenius.ai/{url}"
                self.logger.debug(f"Converted relative s3_html path to full URL: {url}")

            self.logger.debug(f"Fetching HTML for detailed parsing from: {url}")

            # Try to fetch the HTML content
            response = None
            try:
                response = requests.get(url, timeout=10)
                response.raise_for_status()
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 403:
                    # Try alternative naming patterns for S3 objects
                    alternative_urls = self._generate_alternative_urls(url, data)
                    for alt_url in alternative_urls:
                        try:
                            self.logger.debug(f"Trying alternative URL: {alt_url}")
                            response = requests.get(alt_url, timeout=10)
                            response.raise_for_status()
                            self.logger.info(
                                f"Successfully loaded HTML from alternative URL: {alt_url}"
                            )
                            break
                        except requests.exceptions.RequestException:
                            continue

                    if not response or response.status_code != 200:
                        self.logger.warning(
                            f"Failed to load HTML from any URL variant for {os.path.basename(json_path)}"
                        )
                        return
                else:
                    raise

            # Use the instance's parser. Set new HTML content for it.
            self.html_parser.set_html(response.text)
            parsed_content = self.html_parser.parse()  # Call parse() on the instance

            # Extract ONLY the names from plaintiffs, nothing else
            if parsed_content.get("plaintiffs"):
                names = []
                for p_entry in parsed_content["plaintiffs"]:  # Renamed p to p_entry
                    if isinstance(p_entry, dict):
                        name = p_entry.get("name")
                        if name and isinstance(name, str):
                            names.append(name.strip())

                # Remove any duplicates while preserving order
                seen = set()
                # Store in singular field only - remove plural duplication
                data["plaintiff"] = [x for x in names if not (x in seen or seen.add(x))]

            # Extract attorneys directly from parser with party representation
            if parsed_content.get("attorney") or parsed_content.get("plaintiffs"):
                # Extract attorneys with party representation
                attorneys_with_party = []
                plaintiffs = parsed_content.get("plaintiffs", [])
                defendants = parsed_content.get("defendants", [])
                
                # Extract attorneys from plaintiffs
                for plaintiff in plaintiffs:
                    if isinstance(plaintiff, dict):
                        plaintiff_name = plaintiff.get("name", "")
                        for attorney in plaintiff.get("attorneys", []):
                            if isinstance(attorney, dict):
                                attorney_with_party = attorney.copy()
                                attorney_with_party["represents"] = "plaintiff"
                                attorney_with_party["represents_party"] = plaintiff_name
                                attorneys_with_party.append(attorney_with_party)
                
                # Extract attorneys from defendants (if structured that way)
                for defendant in defendants:
                    if isinstance(defendant, dict) and defendant.get("attorneys"):
                        defendant_name = defendant.get("name", "")
                        for attorney in defendant.get("attorneys", []):
                            if isinstance(attorney, dict):
                                attorney_with_party = attorney.copy()
                                attorney_with_party["represents"] = "defendant"
                                attorney_with_party["represents_party"] = defendant_name
                                attorneys_with_party.append(attorney_with_party)
                
                # Include direct attorney list as fallback
                direct_attorneys = parsed_content.get("attorney", [])
                for attorney in direct_attorneys:
                    if isinstance(attorney, dict):
                        attorney_with_party = attorney.copy()
                        if "represents" not in attorney_with_party:
                            attorney_with_party["represents"] = "unknown"
                            attorney_with_party["represents_party"] = ""
                        attorneys_with_party.append(attorney_with_party)
                
                # Apply final deduplication to ensure uniqueness while preserving party info
                deduplicated_attorneys = self._deduplicate_attorneys(attorneys_with_party)
                
                # Special handling for removal cases - filter PRO SE more aggressively
                if data.get("is_removal"):
                    filtered_attorneys = []
                    for attorney in deduplicated_attorneys:
                        attorney_name = attorney.get("attorney_name", "").strip().upper()
                        law_firm = attorney.get("law_firm", "").strip().upper()
                        
                        # In removal cases, be more aggressive about filtering PRO SE
                        if (attorney_name not in ["PRO SE", "PRO-SE", ""] and 
                            law_firm not in ["PRO SE", "PRO-SE", ""] and
                            attorney_name != law_firm):
                            filtered_attorneys.append(attorney)
                        else:
                            self.logger.debug(f"Filtered PRO SE attorney in removal case: {attorney_name}")
                    
                    deduplicated_attorneys = filtered_attorneys
                
                data["attorney"] = deduplicated_attorneys
                self.logger.debug(
                    f"Set {len(data['attorney'])} attorneys from parsed content (after final deduplication and party association)"
                )

            # Ensure 'defendant' is a list of strings with proper deduplication
            raw_defendants = parsed_content.get("defendants")
            if isinstance(raw_defendants, list):
                # Filter out any non-string or empty string items, ensuring clean list
                defendants = [
                    str(d).strip()
                    for d in raw_defendants
                    if isinstance(d, str | int | float) and str(d).strip()
                ]

                # Deduplicate defendants while preserving order
                seen_defendants = set()
                data["defendant"] = [
                    x
                    for x in defendants
                    if not (
                        x.lower() in seen_defendants or seen_defendants.add(x.lower())
                    )
                ]
            elif isinstance(raw_defendants, str) and raw_defendants.strip():
                data["defendant"] = [
                    raw_defendants.strip()
                ]  # Wrap single defendant in a list
            else:
                data["defendant"] = []  # Default to empty list if no valid defendants

            # Clean up: Remove plural fields to avoid confusion
            if "plaintiffs" in data:
                del data["plaintiffs"]
            if "defendants" in data:
                del data["defendants"]
            if "plaintiffs_gpt" in data:
                del data["plaintiffs_gpt"]

            # Update case_info fields from the detailed parse if not already set by _extract_case_info_from_html
            # This helps consolidate where fields like 'date_filed' are updated from HTML.
            html_case_info = parsed_content.get("case_info", {})
            if html_case_info:
                data.setdefault("assigned_to", html_case_info.get("assigned_to"))
                data.setdefault("referred_to", html_case_info.get("referred_to"))
                # 'date_filed' should be updated here if HTML is the primary source for it
                # Note: _extract_case_info_from_html already sets 'date_filed' if 'initial_filing_date' is found
                # This ensures it's based on the first docket entry date if available.
                # If general 'date_filed' from header is preferred, can get it here:
                # if html_case_info.get('date_filed'): data['date_filed'] = html_case_info.get('date_filed')
                data.setdefault("jury_demand", html_case_info.get("jury_demand"))
                data.setdefault("nos", html_case_info.get("nos"))  # Nature of Suit
                data.setdefault("jurisdiction", html_case_info.get("jurisdiction"))
                data.setdefault("demand", html_case_info.get("demand"))
                data.setdefault("cause", html_case_info.get("cause"))
                data.setdefault(
                    "versus", html_case_info.get("versus")
                )  # Case Title/Versus

        except (
            requests.exceptions.RequestException
        ) as req_ex:  # More specific exception
            self.logger.error(
                f"Request error in _update_json_with_pacer_html for {os.path.basename(json_path)} (URL: {url}): {req_ex}",
                exc_info=True,
            )
            data["s3_html_error"] = f"Request Error: {req_ex}"
        except Exception as e:
            self.logger.error(
                f"Generic error in _update_json_with_pacer_html for {os.path.basename(json_path)}: {str(e)}",
                exc_info=True,
            )
            data["s3_html_error"] = f"Parsing/Update Error: {e}"

    def _deduplicate_attorneys(self, attorneys: list[dict]) -> list[dict]:
        """
        Deduplicate attorneys based on (attorney_name, normalized_law_firm, represents) combination.
        This ensures that the same attorney representing different parties is kept as separate entries.

        Args:
            attorneys: List of attorney dictionaries

        Returns:
            List of unique attorney dictionaries
        """
        if not attorneys:
            return []

        unique_attorneys = []
        seen_attorneys = set()

        for attorney in attorneys:
            if not isinstance(attorney, dict):
                continue

            attorney_name = attorney.get("attorney_name", "").strip()
            law_firm = attorney.get("law_firm", "").strip()
            represents = attorney.get("represents", "unknown")
            represents_party = attorney.get("represents_party", "")

            # Skip if attorney name is empty
            if not attorney_name:
                continue
                
            # Filter out PRO SE attorneys in removal cases if this is a removal case
            # PRO SE should be handled at the case level, not attorney level
            if attorney_name.upper() in ["PRO SE", "PRO-SE"] or law_firm.upper() in ["PRO SE", "PRO-SE"]:
                self.logger.debug(f"Filtered out PRO SE attorney: {attorney_name}")
                continue

            # Create unique key based on attorney name, normalized law firm, and party representation
            normalized_law_firm = self.html_parser.normalize_law_firm_name(law_firm) if law_firm else ""
            attorney_key = (attorney_name.lower(), normalized_law_firm, represents, represents_party.lower())

            # Only add if we haven't seen this attorney/firm/party combination
            if attorney_key not in seen_attorneys:
                seen_attorneys.add(attorney_key)
                unique_attorneys.append(attorney)
            else:
                self.logger.debug(
                    f"Final deduplication removed duplicate: {attorney_name} at {law_firm} representing {represents} {represents_party}"
                )

        if len(attorneys) != len(unique_attorneys):
            self.logger.info(
                f"Final attorney deduplication: {len(attorneys)} -> {len(unique_attorneys)} unique attorneys"
            )

        return unique_attorneys

    def _save_json(self, json_path: str, data: dict) -> None:
        """
        Save the JSON data to a file.
        """
        try:
            # Create a copy of the data to avoid modifying the original
            filtered_data = data.copy()

            # Remove non-serializable objects
            if "_pdf_processor" in filtered_data:
                filtered_data.pop("_pdf_processor")

            # Filter out null values
            # Note: PRO SE filtering is now handled at the attorney level, not here
            # This preserves PRO SE information at the case level when appropriate
            NULL_CONDITIONS = ["", "NA", None, "None", []]
            filtered_data = {
                key: value
                for key, value in filtered_data.items()
                if value not in NULL_CONDITIONS
            }

            # Create parent directory if it doesn't exist
            os.makedirs(os.path.dirname(json_path), exist_ok=True)

            # Write the JSON file
            with open(json_path, "w") as f:
                json.dump(filtered_data, f, indent=2)  # type: ignore[SupportedTypes]

            self.logger.debug(f"Successfully saved JSON to {json_path}")

        except Exception as e:
            self.logger.error(f"Error saving JSON to {json_path}: {str(e)}")
            self.logger.error("Stack trace:", exc_info=True)

    async def download_file_as_string(self, s3_url: str) -> str | None:
        """
        Download a file from S3 and return its contents as a string.

        Args:
            s3_url: Full URL (e.g. https://cdn.lexgenius.ai/20250311/html/file.html)

        Returns:
            Optional[str]: The file contents as a string, or None if download fails
        """
        try:
            # Validate input
            if not isinstance(s3_url, str):
                self.logger.error(f"Invalid S3 URL type: {type(s3_url)}")
                return None

            if not s3_url:
                self.logger.error("Empty S3 URL provided")
                return None

            # Extract the path portion after cdn.lexgenius.ai/
            if "cdn.lexgenius.ai/" in s3_url:
                s3_key = s3_url.split("cdn.lexgenius.ai/")[1]
            else:
                s3_key = s3_url

            # Debug log the key being passed
            self.logger.debug(f"Extracted S3 key: {s3_key}")

            # Ensure we're not passing the logger by mistake
            if s3_key is self.logger:
                self.logger.error("Logger being passed as S3 key")
                return None

            # Use async method to avoid event loop conflicts
            content = await self.s3_manager.download_content(s3_key)
            if content is None:
                self.logger.error(f"Failed to download content from {s3_url}")
                return None

            # Convert bytes to string if needed
            if isinstance(content, bytes):
                return content.decode("utf-8")
            return content

        except Exception as e:
            self.logger.error(f"Error processing S3 URL {s3_url}: {str(e)}")
            return None

    async def parse_html_async(self, html_link: str) -> dict | None:
        """
        Parse HTML content from a URL asynchronously.

        This method is expected by DocketHTMLProcessor for async HTML parsing.

        Args:
            html_link: URL to HTML content to parse

        Returns:
            Parsed content dictionary or None if parsing fails
        """
        try:
            if not html_link or not isinstance(html_link, str):
                self.logger.error(f"Invalid HTML link provided: {html_link}")
                return None

            self.logger.debug(f"Parsing HTML content from: {html_link}")

            # Use existing extraction method to get case info
            case_info = await asyncio.to_thread(
                self._extract_case_info_from_html, html_link
            )

            if not case_info:
                self.logger.warning(f"No case info extracted from HTML: {html_link}")
                return None

            # Also get detailed parsing using the HTML parser
            try:
                import requests

                response = requests.get(html_link, timeout=10)
                response.raise_for_status()

                # Use the instance's parser to get detailed content
                self.html_parser.set_html(response.text)
                parsed_content = self.html_parser.parse()

                # Merge case_info with parsed_content
                if parsed_content:
                    result = {
                        "case_info": case_info,
                        "plaintiffs": parsed_content.get("plaintiffs", []),
                        "defendants": parsed_content.get("defendants", []),
                        **parsed_content,  # Include any other parsed fields
                    }
                else:
                    result = {"case_info": case_info}

                self.logger.debug(
                    f"Successfully parsed HTML content with {len(result)} fields"
                )
                return result

            except Exception as parse_error:
                self.logger.warning(
                    f"Detailed parsing failed, returning basic case info: {parse_error}"
                )
                return {"case_info": case_info}

        except Exception as e:
            self.logger.error(
                f"Error in parse_html_async for {html_link}: {e}", exc_info=True
            )
            return None

    def parse_html(self, html_link: str) -> dict | None:
        """
        Parse HTML content from a URL synchronously.

        This method is expected by DocketHTMLProcessor as a fallback for sync HTML parsing.

        Args:
            html_link: URL to HTML content to parse

        Returns:
            Parsed content dictionary or None if parsing fails
        """
        try:
            if not html_link or not isinstance(html_link, str):
                self.logger.error(f"Invalid HTML link provided: {html_link}")
                return None

            self.logger.debug(f"Parsing HTML content synchronously from: {html_link}")

            # Use existing extraction method to get case info
            case_info = self._extract_case_info_from_html(html_link)

            if not case_info:
                self.logger.warning(f"No case info extracted from HTML: {html_link}")
                return None

            # Also get detailed parsing using the HTML parser
            try:
                import requests

                response = requests.get(html_link, timeout=10)
                response.raise_for_status()

                # Use the instance's parser to get detailed content
                self.html_parser.set_html(response.text)
                parsed_content = self.html_parser.parse()

                # Merge case_info with parsed_content
                if parsed_content:
                    result = {
                        "case_info": case_info,
                        "plaintiffs": parsed_content.get("plaintiffs", []),
                        "defendants": parsed_content.get("defendants", []),
                        **parsed_content,  # Include any other parsed fields
                    }
                else:
                    result = {"case_info": case_info}

                self.logger.debug(
                    f"Successfully parsed HTML content with {len(result)} fields"
                )
                return result

            except Exception as parse_error:
                self.logger.warning(
                    f"Detailed parsing failed, returning basic case info: {parse_error}"
                )
                return {"case_info": case_info}

        except Exception as e:
            self.logger.error(
                f"Error in parse_html for {html_link}: {e}", exc_info=True
            )
            return None

    def _generate_alternative_urls(self, original_url: str, data: dict) -> list[str]:
        """
        Generate alternative URLs for S3 HTML objects based on naming patterns.

        Args:
            original_url: The original URL that failed
            data: The docket data dictionary

        Returns:
            List of alternative URLs to try
        """
        alternative_urls = []

        # Extract base URL and path components
        if "cdn.lexgenius.ai" in original_url:
            base_url = original_url.split("/html/")[0]
            if "/html/" in original_url:
                # date_part = base_url.split("/")[-1]  # Extract YYYYMMDD - unused

                # Get court_id and docket_num from data
                court_id = data.get("court_id", "")
                docket_num = data.get("docket_num", "")

                if court_id and docket_num:
                    # Extract YY and NNNNN from docket_num (e.g., "1:25-cv-05323")
                    import re

                    docket_match = re.search(r":(\d{2})-\w+-?(\d+)", docket_num)
                    if docket_match:
                        yy = docket_match.group(1)
                        case_num = docket_match.group(2)
                        # Ensure case_num is 5 digits, padding with zeros if needed
                        case_num_padded = case_num.zfill(5)

                        # Generate pattern: {court_id}_{YY}_{NNNNN}
                        base_pattern = f"{court_id}_{yy}_{case_num_padded}"

                        # Try to fetch list of HTML files from S3 for this date
                        # For now, we'll try common variations
                        versus_text = data.get("versus", "")
                        if versus_text:
                            # Clean versus text for filename
                            versus_clean = re.sub(r"[^\w\s]", "_", versus_text)
                            versus_clean = re.sub(r"\s+", "_", versus_clean)

                            # Try different naming patterns
                            alternative_urls.extend(
                                [
                                    f"{base_url}/html/{base_pattern}_{versus_clean}.html",
                                    f"{base_url}/html/{base_pattern}_{versus_clean.upper()}.html",
                                    f"{base_url}/html/{base_pattern}_{versus_clean.lower()}.html",
                                    f"{base_url}/html/{court_id}_{docket_num.replace(':', '_').replace('/', '_')}_{versus_clean}.html",
                                ]
                            )

                        # Also try without versus text
                        alternative_urls.append(f"{base_url}/html/{base_pattern}.html")

        return alternative_urls

    def _search_for_html_file(self, data: dict) -> str | None:
        """Search for HTML file by pattern when pre-existing URL is not accessible."""
        iso_date = self.config.get("iso_date")
        if not iso_date:
            return None

        s3_html_dir_path = f"{iso_date}/html/"
        court_id = data.get("court_id", "").strip()
        docket_num = data.get("docket_num", "").strip()

        if not court_id or not docket_num:
            return None

        # Extract YY: two digits after the colon
        import re
        year_match = re.search(r":(\d{2})", docket_num)
        # Extract NNNNN: 5 consecutive digits after the case type (cv, sf, etc.)
        num_match = re.search(r"-([a-zA-Z]{2})-(\d{5})", docket_num)

        if not year_match or not num_match:
            return None

        year_str = year_match.group(1)
        num_str = num_match.group(2)

        # Build search pattern: {court_id}_{YY}_{NNNNN}
        search_pattern = f"{court_id}_{year_str}_{num_str}"
        self.logger.info(f"Fallback S3 HTML search pattern: {search_pattern}")

        try:
            # Use the injected S3AsyncStorage manager
            if not self.s3_manager:
                self.logger.error("S3 manager not available for fallback search")
                return None

            # Search for matching files
            matching_files = self.s3_manager.list_existing_files(s3_html_dir_path)

            for file_key in matching_files:
                if search_pattern in file_key and file_key.endswith(".html"):
                    # Found a match - construct CDN URL
                    cdn_url = f"https://cdn.lexgenius.ai/{file_key}"
                    self.logger.info(f"Found matching HTML file: {cdn_url}")
                    return cdn_url

        except Exception as e:
            self.logger.error(f"Error searching for HTML file: {e}")

        return None
