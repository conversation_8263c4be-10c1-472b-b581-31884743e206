"""
Enhanced HTML Processing Orchestrator with Improved S3 Integration

This service extends the base HTMLProcessingOrchestrator with better S3 service injection
and error handling, specifically designed to work with the PACER dependency injection system.
"""

import logging
import os
import re
from typing import Any, Dict, List, Optional

from src.services.html.html_processing_orchestrator import HTMLProcessingOrchestrator
from src.pacer.utils.s3_injection_helper import S3InjectionHelper


class EnhancedHTMLProcessingOrchestrator(HTMLProcessingOrchestrator):
    """
    Enhanced HTML processing orchestrator with improved S3 service injection.
    
    This class extends the base HTMLProcessingOrchestrator to provide:
    1. Better S3 service injection validation
    2. Fallback mechanisms when S3 is not available
    3. Enhanced logging and debugging
    4. Integration with PACER dependency injection system
    """
    
    def __init__(self,
                 logger: Any = None,
                 config: Dict[str, Any] = None,
                 court_id: Optional[str] = None,
                 html_data_updater: Optional[Any] = None,
                 s3_async_storage: Optional[Any] = None,
                 s3_manager: Optional[Any] = None):
        """
        Initialize the Enhanced HTML Processing Orchestrator.
        
        Args:
            logger: Logger instance
            config: Configuration dictionary
            court_id: Optional court identifier
            html_data_updater: Optional DataUpdaterService instance
            s3_async_storage: Optional S3 async storage service
            s3_manager: Optional S3 manager component (alternative to s3_async_storage)
        """
        super().__init__(logger, config, court_id, html_data_updater, s3_async_storage)
        
        # Enhanced S3 service handling - support both direct S3 storage and S3 manager
        if s3_manager and not self.s3_async_storage:
            # Extract S3 storage from S3 manager if available
            if hasattr(s3_manager, 's3_async_storage'):
                self.s3_async_storage = s3_manager.s3_async_storage
                self.log_info("S3 service extracted from S3Manager component")
        
        # Store S3 manager for component-based operations
        self.s3_manager = s3_manager
        
        # Validate S3 injection on initialization
        self._validate_s3_injection()
    
    def _validate_s3_injection(self):
        """Validate that S3 services are properly injected."""
        s3_available = self.s3_async_storage is not None
        s3_manager_available = self.s3_manager is not None
        
        validation_result = S3InjectionHelper.verify_s3_injection(self, self.logger)
        
        self.log_info("S3 injection validation completed", extra={
            's3_async_storage_available': s3_available,
            's3_manager_available': s3_manager_available,
            'validation_passed': validation_result,
            'court_id': self.court_id
        })
        
        if not s3_available and not s3_manager_available:
            self.log_warning("No S3 services available - HTML uploads will be skipped")
    
    async def process_html_content(self, case_details: Dict[str, Any],
                                   html_content: str, json_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Enhanced HTML content processing with improved S3 integration.
        
        This method extends the base implementation with:
        - Enhanced S3 service validation
        - Fallback mechanisms
        - Better error handling
        - Support for both S3 async storage and S3 manager components
        """
        current_court_id = self.court_id or case_details.get('court_id', 'UNKNOWN_COURT')
        log_prefix = f"[{current_court_id}][{case_details.get('docket_num', 'UNKNOWN')}] EnhancedHTMLProcessing:"
        
        try:
            self.log_info(f"{log_prefix} 🔄 Starting enhanced HTML processing")
            
            # Call parent implementation for base processing
            result = await super().process_html_content(case_details, html_content, json_path)
            
            # Enhanced S3 upload with fallback mechanisms
            if json_path and html_content:
                upload_success = await self._enhanced_s3_upload(case_details, html_content, json_path, log_prefix)
                
                if upload_success:
                    self.log_info(f"{log_prefix} ✅ Enhanced S3 upload completed successfully")
                else:
                    self.log_warning(f"{log_prefix} ⚠️ Enhanced S3 upload failed, but processing continues")
            
            self.log_info(f"{log_prefix} ✅ Enhanced HTML processing completed successfully")
            return result
            
        except Exception as e:
            self.log_error(f"{log_prefix} ❌ Error in enhanced HTML processing: {e}")
            case_details['_enhanced_html_processing_error'] = str(e)
            return case_details
    
    async def _enhanced_s3_upload(self, case_details: Dict[str, Any], 
                                  html_content: str, json_path: str, log_prefix: str) -> bool:
        """
        Enhanced S3 upload with multiple fallback strategies.
        
        Args:
            case_details: Case details dictionary
            html_content: HTML content to upload
            json_path: JSON file path for date extraction
            log_prefix: Logging prefix for consistency
            
        Returns:
            True if upload successful via any method, False otherwise
        """
        upload_methods = []
        
        # Method 1: Direct S3AsyncStorage upload
        if self.s3_async_storage:
            upload_methods.append(('s3_async_storage', self._upload_via_s3_async_storage))
        
        # Method 2: S3Manager component upload
        if self.s3_manager:
            upload_methods.append(('s3_manager', self._upload_via_s3_manager))
        
        # Method 3: Base implementation fallback
        upload_methods.append(('base_implementation', self._upload_html_to_s3))
        
        for method_name, upload_method in upload_methods:
            try:
                self.log_info(f"{log_prefix} Attempting upload via {method_name}")
                success = await upload_method(case_details, html_content, json_path)
                
                if success:
                    self.log_info(f"{log_prefix} ✅ Upload successful via {method_name}")
                    return True
                else:
                    self.log_warning(f"{log_prefix} ⚠️ Upload failed via {method_name}, trying next method")
            except Exception as e:
                self.log_error(f"{log_prefix} ❌ Exception in {method_name} upload: {e}")
                continue
        
        self.log_error(f"{log_prefix} ❌ All upload methods failed")
        return False
    
    async def _upload_via_s3_async_storage(self, case_details: Dict[str, Any],
                                           html_content: str, json_path: str) -> bool:
        """Upload using direct S3AsyncStorage instance."""
        try:
            # Extract date and construct S3 key
            date_match = re.search(r'/(\d{8})/', json_path)
            if not date_match:
                return False
            
            iso_date = date_match.group(1)
            base_filename = case_details.get('new_filename', case_details.get('base_filename', ''))
            
            if not base_filename:
                return False
            
            # Ensure .html extension
            if not base_filename.endswith('.html'):
                html_filename = f"{os.path.splitext(base_filename)[0]}.html"
            else:
                html_filename = base_filename
            
            s3_key = f"{iso_date}/html/{html_filename}"
            
            # Upload using S3AsyncStorage
            success = await self.s3_async_storage.upload_content(
                content=html_content,
                object_key=s3_key,
                content_type='text/html',
                overwrite=True
            )
            
            if success:
                case_details['s3_html_key'] = s3_key
                case_details['s3_html'] = f"https://cdn.lexgenius.ai/{s3_key}"
            
            return success
        except Exception as e:
            self.log_error(f"S3AsyncStorage upload error: {e}")
            return False
    
    async def _upload_via_s3_manager(self, case_details: Dict[str, Any],
                                     html_content: str, json_path: str) -> bool:
        """Upload using S3Manager component."""
        try:
            result = await self.s3_manager.execute({
                "action": "upload_html",
                "case_details": case_details,
                "html_content": html_content,
                "json_path": json_path
            })
            
            if isinstance(result, dict):
                success = result.get('success', False)
                if success:
                    case_details['s3_html_key'] = result.get('s3_key')
                    case_details['s3_html'] = result.get('s3_html')
                return success
            
            return bool(result)
        except Exception as e:
            self.log_error(f"S3Manager upload error: {e}")
            return False
    
    def set_s3_manager(self, s3_manager: Any):
        """
        Enhanced S3 manager setter with validation.
        
        Args:
            s3_manager: S3 manager instance
        """
        self.s3_manager = s3_manager
        
        # Extract S3 storage if available
        if hasattr(s3_manager, 's3_async_storage') and not self.s3_async_storage:
            self.s3_async_storage = s3_manager.s3_async_storage
            self.log_info("S3AsyncStorage extracted from new S3Manager")
        
        # Re-validate S3 injection
        self._validate_s3_injection()
        
        self.log_debug("Enhanced S3 manager set for HTML processing orchestrator")
    
    def get_s3_status(self) -> Dict[str, Any]:
        """
        Get comprehensive S3 service status for debugging.
        
        Returns:
            Dictionary with S3 service availability and configuration
        """
        return {
            'has_s3_async_storage': self.s3_async_storage is not None,
            'has_s3_manager': self.s3_manager is not None,
            's3_async_storage_type': type(self.s3_async_storage).__name__ if self.s3_async_storage else None,
            's3_manager_type': type(self.s3_manager).__name__ if self.s3_manager else None,
            'bucket_name': getattr(self.s3_async_storage, 'bucket_name', None) if self.s3_async_storage else None,
            'court_id': self.court_id,
            'validation_passed': S3InjectionHelper.verify_s3_injection(self)
        }