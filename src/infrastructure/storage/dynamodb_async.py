"""
Async DynamoDB Storage Implementation
"""

import logging
from typing import Any

import aioboto3
from botocore.exceptions import ClientError
from tenacity import (
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import ConfigurationError, StorageError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.infrastructure.storage.retry_decorators import with_retry


class AsyncDynamoDBStorage(ComponentImplementation):
    """Async DynamoDB storage implementation with ComponentImplementation patterns"""

    def __init__(self, config: Any, logger: LoggerProtocol):
        # Initialize ComponentImplementation base class with injected logger
        super().__init__(logger, config)

        # AsyncDynamoDBStorage specific initialization
        self.session = aioboto3.Session()
        self.dynamodb = None

        # Configure retry settings from config or use defaults
        # Increased retries and delays for high-throughput scenarios
        self.max_retries = getattr(config, "dynamodb_max_retries", 15)
        self.base_delay = getattr(config, "dynamodb_base_delay", 2.0)
        self.max_delay = getattr(config, "dynamodb_max_delay", 120.0)

        # Validate configuration
        self._validate_config()

    def _validate_config(self) -> None:
        """Validate storage configuration"""
        if not self.config:
            raise ConfigurationError(
                "Configuration is required for AsyncDynamoDBStorage"
            )

    async def _execute_action(self, data: any) -> any:
        """Required implementation for ComponentImplementation - not used in AsyncDynamoDBStorage."""
        raise NotImplementedError(
            "AsyncDynamoDBStorage does not use the _execute_action pattern. Use specific methods like get_item, put_item, etc."
        )

    async def __aenter__(self):
        """Async context manager entry"""
        import os

        # Get region from environment or config with correct default
        aws_region = (
            os.getenv("AWS_REGION")
            or os.getenv("LEXGENIUS_AWS_REGION")
            or os.getenv("REGION_NAME")
            or getattr(self.config, "aws_region", "us-west-2")
        )

        # Get credentials from config if available
        aws_access_key_id = getattr(self.config, "aws_access_key_id", None)
        aws_secret_access_key = getattr(self.config, "aws_secret_access_key", None)

        # Build resource parameters
        # Only use local DynamoDB if explicitly enabled
        use_local_dynamodb = os.getenv("USE_LOCAL_DYNAMODB", "false").lower() == "true"
        if use_local_dynamodb:
            dynamodb_endpoint = (
                os.getenv("LOCAL_DYNAMODB_ENDPOINT_URL")
                or getattr(self.config, "dynamodb_endpoint", None)
                or "http://localhost:8000"
            )
            self.log_warning(
                "Using local DynamoDB endpoint", {"endpoint": dynamodb_endpoint}
            )
        else:
            dynamodb_endpoint = os.getenv(
                "DYNAMODB_ENDPOINT_URL"
            )  # Use AWS DynamoDB or default

        resource_params = {"region_name": aws_region, "endpoint_url": dynamodb_endpoint}

        # For local DynamoDB, always use dummy credentials
        # For AWS DynamoDB, only add credentials if they're provided in config
        if use_local_dynamodb:
            # Local DynamoDB requires dummy credentials regardless of config
            resource_params["aws_access_key_id"] = "dummy"
            resource_params["aws_secret_access_key"] = "dummy"
        elif aws_access_key_id and aws_secret_access_key:
            # AWS DynamoDB uses real credentials from config
            resource_params["aws_access_key_id"] = aws_access_key_id
            resource_params["aws_secret_access_key"] = aws_secret_access_key

        self.dynamodb = await self.session.resource(
            "dynamodb", **resource_params
        ).__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.dynamodb:
            await self.dynamodb.__aexit__(exc_type, exc_val, exc_tb)
            self.dynamodb = None
        # The session is created once and should persist for the lifetime of the
        # storage object. It does not need to be closed or reset.
        # Removing the line that sets self.session = None makes this
        # context manager re-entrant.

    async def get_table(self, table_name: str):
        """Get DynamoDB table reference"""
        if not self.dynamodb:
            # Initialize dynamodb if not already done
            import os

            # Get region from environment or config with correct default
            aws_region = (
                os.getenv("AWS_REGION")
                or os.getenv("LEXGENIUS_AWS_REGION")
                or os.getenv("REGION_NAME")
                or getattr(self.config, "aws_region", "us-west-2")
            )

            # Get credentials from config if available
            aws_access_key_id = getattr(self.config, "aws_access_key_id", None)
            aws_secret_access_key = getattr(self.config, "aws_secret_access_key", None)

            # Build resource parameters
            # Only use local DynamoDB if explicitly enabled
            use_local_dynamodb = (
                os.getenv("USE_LOCAL_DYNAMODB", "false").lower() == "true"
            )
            if use_local_dynamodb:
                dynamodb_endpoint = (
                    os.getenv("LOCAL_DYNAMODB_ENDPOINT_URL")
                    or getattr(self.config, "dynamodb_endpoint", None)
                    or "http://localhost:8000"
                )
                self.log_warning(
                    "Using local DynamoDB endpoint", {"endpoint": dynamodb_endpoint}
                )
            else:
                dynamodb_endpoint = os.getenv(
                    "DYNAMODB_ENDPOINT_URL"
                )  # Use AWS DynamoDB or default

            resource_params = {
                "region_name": aws_region,
                "endpoint_url": dynamodb_endpoint,
            }

            # Only add credentials if they're provided in config
            if aws_access_key_id and aws_secret_access_key:
                resource_params["aws_access_key_id"] = aws_access_key_id
                resource_params["aws_secret_access_key"] = aws_secret_access_key

            resource = self.session.resource("dynamodb", **resource_params)
            self.dynamodb = await resource.__aenter__()
        table = self.dynamodb.Table(table_name)
        # In aioboto3, Table() returns a coroutine that needs to be awaited
        if hasattr(table, "__await__"):
            return await table
        return table

    @retry(
        stop=stop_after_attempt(15),
        wait=wait_exponential(multiplier=2, min=2, max=120),
        retry=retry_if_exception_type((ClientError,)),
        before_sleep=before_sleep_log(logging.getLogger(__name__), logging.WARNING),
        reraise=True,
    )
    async def get_item(
        self, table_name: str, key: dict[str, Any]
    ) -> dict[str, Any] | None:
        """Get single item by key with tenacity retry"""
        try:
            self.log_debug(
                "Getting item from DynamoDB", {"table": table_name, "key": key}
            )
            table = await self.get_table(table_name)
            response = await table.get_item(Key=key, ConsistentRead=True)
            item = response.get("Item")
            self.log_debug(
                "Successfully retrieved item",
                {"table": table_name, "found": item is not None},
            )
            return item
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            self.log_error(
                "Failed to get item from DynamoDB",
                {
                    "table": table_name,
                    "key": key,
                    "error_code": error_code,
                    "error_message": str(e),
                },
            )
            # Re-raise the original ClientError so the retry decorator can handle it
            raise
        except Exception as e:
            self.log_exception(
                "Unexpected error getting item from DynamoDB",
                {"table": table_name, "key": key},
            )
            raise StorageError(
                f"Unexpected error getting item from {table_name}: {str(e)}",
                {"table": table_name, "key": key, "original_error": e},
            )

    def _validate_item_for_dynamodb(
        self, item: dict[str, Any], table_name: str
    ) -> dict[str, Any]:
        """
        Validate and sanitize item for DynamoDB upload.

        Args:
            item: Item to validate
            table_name: Table name for context

        Returns:
            Sanitized item ready for DynamoDB

        Raises:
            StorageError: If item is invalid
        """
        if not isinstance(item, dict):
            raise StorageError(
                f"Item must be a dictionary for table {table_name}",
                {"table": table_name, "item_type": type(item).__name__},
            )

        if not item:
            raise StorageError(
                f"Item cannot be empty for table {table_name}", {"table": table_name}
            )

        # Check item size (DynamoDB limit is 400KB)
        item_str = str(item)
        item_size = len(item_str.encode("utf-8"))
        if item_size > 390_000:  # Leave some buffer for DynamoDB overhead
            self.log_warning(
                "Item approaching DynamoDB size limit",
                {
                    "table": table_name,
                    "item_size_bytes": item_size,
                    "size_limit": 400_000,
                    "item_keys": list(item.keys()),
                },
            )

        # Sanitize values for DynamoDB compatibility
        sanitized_item = {}
        for key, value in item.items():
            if not isinstance(key, str):
                self.log_warning(
                    f"Converting non-string key to string: {key}",
                    {
                        "table": table_name,
                        "original_key": key,
                        "original_type": type(key).__name__,
                    },
                )
                key = str(key)

            sanitized_value = self._sanitize_dynamodb_value(
                value, f"{table_name}.{key}"
            )
            sanitized_item[key] = sanitized_value

        return sanitized_item

    def _sanitize_dynamodb_value(self, value: Any, context: str = "") -> Any:
        """
        Sanitize a value for DynamoDB compatibility.

        Args:
            value: Value to sanitize
            context: Context for logging

        Returns:
            Sanitized value
        """
        if value is None:
            return None
        elif isinstance(value, bool):
            return value
        elif isinstance(value, (int, float)):
            # DynamoDB doesn't support NaN or Infinity
            if isinstance(value, float) and (
                value != value or value == float("inf") or value == float("-inf")
            ):
                self.log_warning(
                    f"Converting invalid float to string: {value}", {"context": context}
                )
                return str(value)
            return value
        elif isinstance(value, str):
            # DynamoDB doesn't support empty strings
            if value == "":
                return None
            return value
        elif isinstance(value, (list, tuple)):
            return [
                self._sanitize_dynamodb_value(item, f"{context}[{i}]")
                for i, item in enumerate(value)
            ]
        elif isinstance(value, dict):
            return {
                k: self._sanitize_dynamodb_value(v, f"{context}.{k}")
                for k, v in value.items()
            }
        else:
            # Convert other types to string
            self.log_debug(
                f"Converting {type(value).__name__} to string", {"context": context}
            )
            return str(value)

    @retry(
        stop=stop_after_attempt(15),
        wait=wait_exponential(multiplier=2, min=2, max=120),
        retry=retry_if_exception_type((ClientError,)),
        before_sleep=before_sleep_log(logging.getLogger(__name__), logging.WARNING),
        reraise=True,
    )
    async def put_item(self, table_name: str, item: dict[str, Any]) -> None:
        """
        Put single item with tenacity retry for DynamoDB errors.

        Args:
            table_name: Name of the DynamoDB table
            item: Item to put
        """
        import asyncio
        import random

        try:
            # Validate and sanitize item before upload
            sanitized_item = self._validate_item_for_dynamodb(item, table_name)

            self.log_debug("Putting item to DynamoDB", {"table": table_name})
            table = await self.get_table(table_name)

            # Add jitter before the operation to reduce thundering herd
            jitter = random.uniform(0, 0.5)
            await asyncio.sleep(jitter)

            await table.put_item(Item=sanitized_item)
            self.log_debug("Successfully put item to DynamoDB", {"table": table_name})
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            error_message = e.response.get("Error", {}).get("Message", str(e))
            http_status = e.response.get("ResponseMetadata", {}).get(
                "HTTPStatusCode", "Unknown"
            )

            # Enhanced error categorization
            if error_code == "ValidationException":
                # Include detailed item information for debugging
                item_details = {}
                if isinstance(item, dict):
                    item_details = {
                        "item_keys": list(item.keys()),
                        "item_size_bytes": len(str(item).encode("utf-8")),
                        "problematic_item_data": item,  # Full item for debugging
                        "field_types": {k: type(v).__name__ for k, v in item.items()},
                        "empty_fields": [k for k, v in item.items() if v in (None, "", [], {})],
                        "large_fields": [
                            k for k, v in item.items() 
                            if isinstance(v, str) and len(v) > 1000
                        ],
                    }
                    # Check for problematic field patterns
                    problematic_patterns = []
                    for key, value in item.items():
                        if isinstance(value, float) and (value != value or value == float('inf') or value == float('-inf')):
                            problematic_patterns.append(f"NaN/Infinity in field '{key}': {value}")
                        elif isinstance(value, str) and len(value.encode('utf-8')) > 400000:
                            problematic_patterns.append(f"Oversized string in field '{key}': {len(value.encode('utf-8'))} bytes")
                        elif isinstance(value, (list, dict)) and len(str(value)) > 100000:
                            problematic_patterns.append(f"Large collection in field '{key}': {len(str(value))} chars")
                    if problematic_patterns:
                        item_details["detected_issues"] = problematic_patterns
                else:
                    item_details = {
                        "item_type": type(item).__name__,
                        "item_repr": repr(item)[:1000] + "..." if len(repr(item)) > 1000 else repr(item)
                    }

                self.log_error(
                    f"DynamoDB ValidationException - Invalid item format: {error_message}",
                    {
                        "table": table_name,
                        "error_code": error_code,
                        "error_message": error_message,
                        "http_status": http_status,
                        "diagnosis": "Item validation failed - check field types and constraints",
                        **item_details,
                    },
                )
            elif error_code == "ItemSizeTooLargeException":
                item_size = len(str(item)) if item else 0
                self.log_error(
                    "DynamoDB ItemSizeTooLargeException - Item exceeds 400KB limit",
                    {
                        "table": table_name,
                        "error_code": error_code,
                        "error_message": error_message,
                        "http_status": http_status,
                        "estimated_item_size": item_size,
                        "diagnosis": "Item too large - consider reducing field sizes or splitting data",
                    },
                )
            elif error_code == "ProvisionedThroughputExceededException":
                self.log_warning(
                    "DynamoDB ProvisionedThroughputExceededException - Rate limiting",
                    {
                        "table": table_name,
                        "error_code": error_code,
                        "error_message": error_message,
                        "http_status": http_status,
                        "diagnosis": "Write capacity exceeded - retry will handle this automatically",
                    },
                )
            elif error_code == "ResourceNotFoundException":
                self.log_error(
                    "DynamoDB ResourceNotFoundException - Table not found",
                    {
                        "table": table_name,
                        "error_code": error_code,
                        "error_message": error_message,
                        "http_status": http_status,
                        "diagnosis": "Table does not exist or region mismatch",
                    },
                )
            else:
                self.log_error(
                    "DynamoDB ClientError - Unhandled error type",
                    {
                        "table": table_name,
                        "error_code": error_code,
                        "error_message": error_message,
                        "http_status": http_status,
                        "diagnosis": "Unknown DynamoDB error - check AWS documentation",
                    },
                )

            # Re-raise the original ClientError so the retry decorator can handle it
            raise
        except Exception as e:
            self.log_exception(
                "Unexpected error putting item to DynamoDB", {"table": table_name}
            )
            raise StorageError(
                f"Unexpected error putting item to {table_name}: {str(e)}",
                {"table": table_name, "original_error": e},
            )

    @retry(
        stop=stop_after_attempt(15),
        wait=wait_exponential(multiplier=2, min=2, max=120),
        retry=retry_if_exception_type((ClientError,)),
        before_sleep=before_sleep_log(logging.getLogger(__name__), logging.WARNING),
        reraise=True,
    )
    async def update_item(
        self,
        table_name: str,
        key: dict[str, Any],
        update_data: str | dict[str, Any] | None = None,
        expression_values: dict[str, Any] | None = None,
        expression_names: dict[str, str] | None = None,
    ) -> None:
        """
        Update single item with tenacity retry - supports both dict and expression interfaces.

        Args:
            table_name: Name of the DynamoDB table
            key: Primary key of the item to update
            update_data: Update data (dict or expression string)
            expression_values: Expression attribute values
            expression_names: Expression attribute names
        """
        table = await self.get_table(table_name)

        try:
            self.log_debug(
                "Updating item in DynamoDB", {"table": table_name, "key": key}
            )

            # Support both interfaces for backward compatibility
            if isinstance(update_data, dict):
                # Legacy dict interface
                if not update_data:
                    return

                # Build update expression from dict
                update_parts = []
                expr_vals = {}
                expr_names = {}

                for idx, (attr, value) in enumerate(update_data.items()):
                    # Skip key attributes
                    if attr in key:
                        continue

                    attr_name = f"#attr{idx}"
                    attr_value = f":val{idx}"

                    update_parts.append(f"{attr_name} = {attr_value}")
                    expr_names[attr_name] = attr
                    expr_vals[attr_value] = value

                if not update_parts:
                    return

                update_expression = "SET " + ", ".join(update_parts)
                params = {
                    "Key": key,
                    "UpdateExpression": update_expression,
                    "ExpressionAttributeValues": expr_vals,
                    "ExpressionAttributeNames": expr_names,
                }
            else:
                # New expression interface
                update_expression = update_data
                params = {
                    "Key": key,
                    "UpdateExpression": update_expression,
                    "ExpressionAttributeValues": expression_values or {},
                }
                if expression_names:
                    params["ExpressionAttributeNames"] = expression_names

            await table.update_item(**params)
            self.log_debug(
                "Successfully updated item in DynamoDB", {"table": table_name}
            )
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            self.log_error(
                "Failed to update item in DynamoDB",
                {
                    "table": table_name,
                    "key": key,
                    "error_code": error_code,
                    "error_message": str(e),
                },
            )
            # Re-raise the original ClientError so the retry decorator can handle it
            raise
        except Exception as e:
            self.log_exception(
                "Unexpected error updating item in DynamoDB",
                {"table": table_name, "key": key},
            )
            raise StorageError(
                f"Unexpected error updating item in {table_name}: {str(e)}",
                {"table": table_name, "key": key, "original_error": e},
            )

    @retry(
        stop=stop_after_attempt(15),
        wait=wait_exponential(multiplier=2, min=2, max=120),
        retry=retry_if_exception_type((ClientError,)),
        before_sleep=before_sleep_log(logging.getLogger(__name__), logging.WARNING),
        reraise=True,
    )
    async def query(
        self,
        table_name: str,
        key_condition: Any,
        index_name: str | None = None,
        filter_expression: Any | None = None,
        projection_expression: str | None = None,
    ) -> list[dict[str, Any]]:
        """Query table with pagination support and tenacity retry"""
        try:
            self.log_debug(
                "Querying DynamoDB table",
                {
                    "table": table_name,
                    "index": index_name,
                    "has_filter": filter_expression is not None,
                },
            )
            table = await self.get_table(table_name)

            params = {"KeyConditionExpression": key_condition}
            if index_name:
                params["IndexName"] = index_name
            if filter_expression:
                params["FilterExpression"] = filter_expression
            if projection_expression:
                params["ProjectionExpression"] = projection_expression

            # Handle pagination
            items = []
            response = await table.query(**params)
            items.extend(response.get("Items", []))

            # Handle pagination
            while "LastEvaluatedKey" in response:
                params["ExclusiveStartKey"] = response["LastEvaluatedKey"]
                response = await table.query(**params)
                page_items = response.get("Items", [])
                items.extend(page_items)

            self.log_debug(
                "Successfully queried DynamoDB table",
                {"table": table_name, "index": index_name, "item_count": len(items)},
            )
            return items
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            error_message = e.response.get("Error", {}).get("Message", str(e))

            # Enhanced error logging for better debugging
            self.log_error(
                "Failed to query DynamoDB table",
                {
                    "table": table_name,
                    "index": index_name,
                    "error_code": error_code,
                    "error_message": error_message,
                    "key_condition": (
                        str(key_condition) if "key_condition" in locals() else None
                    ),
                    "filter_expression": (
                        str(filter_expression) if filter_expression else None
                    ),
                    "projection_expression": (
                        projection_expression if projection_expression else None
                    ),
                },
            )
            # Re-raise the original ClientError so the retry decorator can handle it
            raise
        except Exception as e:
            self.log_exception(
                "Unexpected error querying DynamoDB table",
                {"table": table_name, "index": index_name},
            )
            raise StorageError(
                f"Unexpected error querying {table_name}: {str(e)}",
                {"table": table_name, "index": index_name, "original_error": e},
            )

    @retry(
        stop=stop_after_attempt(15),
        wait=wait_exponential(multiplier=2, min=2, max=120),
        retry=retry_if_exception_type((ClientError,)),
        before_sleep=before_sleep_log(logging.getLogger(__name__), logging.WARNING),
        reraise=True,
    )
    async def scan(
        self,
        table_name: str,
        filter_expression: Any | None = None,
        projection_expression: str | None = None,
        expression_attribute_names: dict[str, str] | None = None,
        expression_attribute_values: dict[str, Any] | None = None,
        **kwargs,
    ) -> list[dict[str, Any]]:
        """Scan table with tenacity retry"""
        try:
            self.log_debug(
                "Scanning DynamoDB table",
                {
                    "table": table_name,
                    "has_filter": filter_expression is not None,
                    "has_projection": projection_expression is not None,
                },
            )
            table = await self.get_table(table_name)

            params = {}
            if filter_expression:
                params["FilterExpression"] = filter_expression
            if projection_expression:
                params["ProjectionExpression"] = projection_expression
            if expression_attribute_names:
                params["ExpressionAttributeNames"] = expression_attribute_names
            if expression_attribute_values:
                params["ExpressionAttributeValues"] = expression_attribute_values

            # Add any additional keyword arguments
            params.update(kwargs)

            items = []
            response = await table.scan(**params)
            items.extend(response.get("Items", []))

            # Handle pagination
            while "LastEvaluatedKey" in response:
                params["ExclusiveStartKey"] = response["LastEvaluatedKey"]
                response = await table.scan(**params)
                items.extend(response.get("Items", []))

            self.log_debug(
                "Successfully scanned DynamoDB table",
                {"table": table_name, "item_count": len(items)},
            )
            return items
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            self.log_error(
                "Failed to scan DynamoDB table",
                {
                    "table": table_name,
                    "error_code": error_code,
                    "error_message": str(e),
                },
            )
            # Re-raise the original ClientError so the retry decorator can handle it
            raise
        except Exception as e:
            self.log_exception(
                "Unexpected error scanning DynamoDB table", {"table": table_name}
            )
            raise StorageError(
                f"Unexpected error scanning {table_name}: {str(e)}",
                {"table": table_name, "original_error": e},
            )

    @retry(
        stop=stop_after_attempt(15),
        wait=wait_exponential(multiplier=2, min=2, max=120),
        retry=retry_if_exception_type((ClientError,)),
        before_sleep=before_sleep_log(logging.getLogger(__name__), logging.WARNING),
        reraise=True,
    )
    async def batch_write_item(
        self, table_name: str, items: list[dict[str, Any]], max_retries: int = 15
    ) -> None:
        """
        Write multiple items in batches with tenacity retry.

        Args:
            table_name: Name of the DynamoDB table
            items: List of items to write (max 25 per batch)
        """
        import asyncio
        import random

        if not items:
            return

        self.log_info(f"Starting batch write of {len(items)} items to {table_name}")

        # Validate and sanitize all items before batching
        sanitized_items = []
        for item in items:
            try:
                sanitized_item = self._validate_item_for_dynamodb(item, table_name)
                sanitized_items.append(sanitized_item)
            except Exception as e:
                # Enhanced error logging for batch validation failures
                item_details = {}
                if isinstance(item, dict):
                    item_details = {
                        "item_keys": list(item.keys()),
                        "item_size_bytes": len(str(item).encode("utf-8")),
                        "problematic_item_data": item,  # Full item for debugging
                        "field_types": {k: type(v).__name__ for k, v in item.items()},
                        "empty_fields": [k for k, v in item.items() if v in (None, "", [], {})],
                        "large_fields": [
                            k for k, v in item.items() 
                            if isinstance(v, str) and len(v) > 1000
                        ],
                    }
                else:
                    item_details = {
                        "item_type": type(item).__name__,
                        "item_repr": repr(item)[:1000] + "..." if len(repr(item)) > 1000 else repr(item)
                    }

                self.log_error(
                    f"Failed to validate item for batch write: {e}",
                    {
                        "table": table_name,
                        "validation_error": str(e),
                        "batch_position": len(sanitized_items),
                        **item_details,
                    },
                )
                # Skip invalid items but continue with the rest
                continue

        if not sanitized_items:
            self.log_warning("No valid items to write after sanitization")
            return

        # Process items in batches of 25 (DynamoDB limit)
        batch_size = 25
        total_batches = (len(sanitized_items) + batch_size - 1) // batch_size

        for batch_num, i in enumerate(range(0, len(sanitized_items), batch_size)):
            batch = sanitized_items[i : i + batch_size]

            # Add jitter to reduce thundering herd effect
            jitter = random.uniform(0, 0.5)
            await asyncio.sleep(jitter)

            self.log_debug(
                f"Writing batch {batch_num + 1}/{total_batches} with {len(batch)} items"
            )

            # Prepare request items
            request_items = {
                table_name: [{"PutRequest": {"Item": item}} for item in batch]
            }

            try:
                response = await self.dynamodb.batch_write_item(
                    RequestItems=request_items
                )

                # Handle unprocessed items
                unprocessed = response.get("UnprocessedItems", {})
                retry_count = 0

                while unprocessed and retry_count < max_retries:
                    unprocessed_count = len(unprocessed.get(table_name, []))
                    self.log_warning(
                        f"Retrying {unprocessed_count} unprocessed items (attempt {retry_count + 1}/{max_retries})"
                    )

                    # Use exponential backoff for unprocessed items
                    backoff_time = min((2**retry_count) * random.uniform(0.5, 1.5), 30)
                    await asyncio.sleep(backoff_time)

                    response = await self.dynamodb.batch_write_item(
                        RequestItems=unprocessed
                    )
                    unprocessed = response.get("UnprocessedItems", {})
                    retry_count += 1

                if unprocessed:
                    failed_count = len(unprocessed.get(table_name, []))
                    self.log_error(
                        f"Failed to process {failed_count} items after {max_retries} retries"
                    )
                    raise StorageError(
                        f"Batch write incomplete: {failed_count} items failed",
                        {"table": table_name, "failed_items": failed_count},
                    )

                self.log_debug(
                    f"Successfully wrote batch {batch_num + 1}/{total_batches}"
                )

            except ClientError as e:
                error_code = e.response.get("Error", {}).get("Code", "Unknown")
                error_message = e.response.get("Error", {}).get("Message", str(e))
                
                if error_code == "ValidationException":
                    # Log detailed information about the problematic batch
                    batch_details = {
                        "table": table_name,
                        "batch_number": batch_num + 1,
                        "total_batches": total_batches,
                        "batch_size": len(batch),
                        "error_code": error_code,
                        "error_message": error_message,
                        "batch_items": batch,  # Full batch for debugging
                        "batch_item_keys": [list(item.keys()) if isinstance(item, dict) else str(type(item)) for item in batch],
                        "batch_item_sizes": [len(str(item).encode("utf-8")) for item in batch],
                    }
                    
                    self.log_error(
                        f"DynamoDB ValidationException in batch write: {error_message}",
                        batch_details
                    )
                elif error_code == "ProvisionedThroughputExceededException":
                    self.log_warning(
                        f"Batch write throttled for batch {batch_num + 1}, retry will handle this"
                    )
                raise  # Let the retry decorator handle it

        self.log_info(
            f"Successfully completed batch write of {len(sanitized_items)} items to {table_name}"
        )

    @with_retry()
    async def put_item_with_condition(
        self,
        table_name: str,
        item: dict[str, Any],
        condition_expression: str,
        expression_values: dict[str, Any] | None = None,
    ) -> None:
        """
        Put item with condition expression and proper retry handling.

        ConditionalCheckFailedException is NOT retried as it indicates business logic
        failure (e.g., record already exists), not a transient AWS error.

        Args:
            table_name: Name of the DynamoDB table
            item: Item to put
            condition_expression: Condition expression for the put operation
            expression_values: Expression attribute values for condition
        """
        table = await self.get_table(table_name)

        params = {"Item": item, "ConditionExpression": condition_expression}
        if expression_values:
            params["ExpressionAttributeValues"] = expression_values

        try:
            await table.put_item(**params)
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "")
            error_message = e.response.get("Error", {}).get("Message", str(e))
            
            if error_code == "ConditionalCheckFailedException":
                self.logger.debug(
                    f"Conditional check failed for table {table_name}: {condition_expression}. "
                    f"This is expected when condition is not met (e.g., record already exists)."
                )
            elif error_code == "ValidationException":
                # Include detailed item information for debugging
                item_details = {}
                if isinstance(item, dict):
                    item_details = {
                        "item_keys": list(item.keys()),
                        "item_size_bytes": len(str(item).encode("utf-8")),
                        "problematic_item_data": item,  # Full item for debugging
                        "field_types": {k: type(v).__name__ for k, v in item.items()},
                        "condition_expression": condition_expression,
                        "expression_values": expression_values,
                    }
                else:
                    item_details = {
                        "item_type": type(item).__name__,
                        "item_repr": repr(item)[:1000] + "..." if len(repr(item)) > 1000 else repr(item),
                        "condition_expression": condition_expression,
                        "expression_values": expression_values,
                    }

                self.log_error(
                    f"DynamoDB ValidationException in conditional put: {error_message}",
                    {
                        "table": table_name,
                        "error_code": error_code,
                        "error_message": error_message,
                        **item_details,
                    },
                )
            raise

    @retry(
        stop=stop_after_attempt(15),
        wait=wait_exponential(multiplier=2, min=2, max=120),
        retry=retry_if_exception_type((ClientError,)),
        before_sleep=before_sleep_log(logging.getLogger(__name__), logging.WARNING),
        reraise=True,
    )
    async def delete_item(self, table_name: str, key: dict[str, Any]) -> None:
        """
        Delete single item by key with tenacity retry.

        Args:
            table_name: Name of the DynamoDB table
            key: Key to identify the item to delete
        """
        table = await self.get_table(table_name)
        await table.delete_item(Key=key)
