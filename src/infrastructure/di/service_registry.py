"""
Service registry for dependency injection.

This module provides a registry for managing service descriptors
and their relationships.
"""

from typing import Dict, Any, Optional, List, Set
import logging

from .base_container import ServiceDescriptor, BaseContainer


class ServiceRegistry(BaseContainer):
    """
    Enhanced service registry with dependency resolution and lifecycle management.
    
    Extends BaseContainer to provide full DI functionality.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the service registry.
        
        Args:
            logger: Optional logger instance
        """
        super().__init__()
        self.logger = logger or logging.getLogger(__name__)
        self._resolved_dependencies: Dict[str, Set[str]] = {}
    
    def register(self, name: str, descriptor: ServiceDescriptor) -> None:
        """
        Register a service with the registry.
        
        Args:
            name: Service name
            descriptor: Service descriptor
        """
        super().register(name, descriptor)
        self.logger.debug(f"Registered service: {name} (tags: {descriptor.tags})")
    
    def _create_instance(self, name: str, descriptor: ServiceDescriptor) -> Any:
        """
        Create a new instance of a service with dependency injection.
        
        Args:
            name: Service name
            descriptor: Service descriptor
            
        Returns:
            Service instance
        """
        if not descriptor.service_class:
            raise ValueError(f"No service_class defined for {name}")
        
        # Import the service class
        module_path, class_name = descriptor.service_class.rsplit(".", 1)
        try:
            module = __import__(module_path, fromlist=[class_name])
            service_class = getattr(module, class_name)
        except (ImportError, AttributeError) as e:
            self.logger.error(f"Failed to import {descriptor.service_class}: {e}")
            raise
        
        # Resolve dependencies
        kwargs = {}
        for param_name, service_name in descriptor.dependencies.items():
            if service_name == "logger":
                kwargs[param_name] = self.logger
            elif service_name == "config":
                # Config should be registered as a service
                kwargs[param_name] = self.get("config") if self.has_service("config") else {}
            else:
                kwargs[param_name] = self.get(service_name)
        
        # Create instance
        try:
            instance = service_class(**kwargs)
            self.logger.debug(f"Created instance of {name}")
            return instance
        except Exception as e:
            self.logger.error(f"Failed to create instance of {name}: {e}")
            raise
    
    def resolve_dependencies(self, name: str) -> Set[str]:
        """
        Resolve all dependencies for a service (including transitive).
        
        Args:
            name: Service name
            
        Returns:
            Set of all dependency names
        """
        if name in self._resolved_dependencies:
            return self._resolved_dependencies[name]
        
        actual_name = self._aliases.get(name, name)
        if actual_name not in self._services:
            return set()
        
        descriptor = self._services[actual_name]
        dependencies = set()
        
        for dep_name in descriptor.dependencies.values():
            if dep_name in ["logger", "config"]:
                continue  # Skip built-in dependencies
            
            dependencies.add(dep_name)
            # Recursively resolve transitive dependencies
            dependencies.update(self.resolve_dependencies(dep_name))
        
        self._resolved_dependencies[name] = dependencies
        return dependencies
    
    def get_initialization_order(self) -> List[str]:
        """
        Get the order in which services should be initialized.
        
        Returns:
            List of service names in initialization order
        """
        # Build dependency graph
        visited = set()
        order = []
        
        def visit(name: str):
            if name in visited:
                return
            visited.add(name)
            
            actual_name = self._aliases.get(name, name)
            if actual_name not in self._services:
                return
            
            descriptor = self._services[actual_name]
            for dep_name in descriptor.dependencies.values():
                if dep_name not in ["logger", "config"]:
                    visit(dep_name)
            
            order.append(name)
        
        for service_name in self._services:
            visit(service_name)
        
        return order
    
    def validate(self) -> List[str]:
        """
        Validate the registry for missing dependencies or cycles.
        
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        for name, descriptor in self._services.items():
            for param_name, dep_name in descriptor.dependencies.items():
                if dep_name in ["logger", "config"]:
                    continue
                
                if not self.has_service(dep_name):
                    errors.append(f"Service '{name}' depends on missing service '{dep_name}'")
        
        # Check for circular dependencies
        try:
            self.get_initialization_order()
        except RecursionError:
            errors.append("Circular dependency detected in service registry")
        
        return errors
    
    def get_service_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a registered service.
        
        Args:
            name: Service name or alias
            
        Returns:
            Service information dict or None if not found
        """
        actual_name = self._aliases.get(name, name)
        if actual_name not in self._services:
            return None
        
        descriptor = self._services[actual_name]
        return {
            "name": actual_name,
            "class": descriptor.service_class,
            "singleton": descriptor.singleton,
            "dependencies": descriptor.dependencies,
            "tags": descriptor.tags,
            "metadata": descriptor.metadata,
            "has_instance": actual_name in self._instances or descriptor.instance is not None
        }
    
    def shutdown(self) -> None:
        """
        Shutdown the registry and cleanup resources.
        
        Calls cleanup methods on services that have them.
        """
        for name, instance in self._instances.items():
            if hasattr(instance, "cleanup"):
                try:
                    self.logger.debug(f"Cleaning up service: {name}")
                    instance.cleanup()
                except Exception as e:
                    self.logger.error(f"Error cleaning up {name}: {e}")
            elif hasattr(instance, "close"):
                try:
                    self.logger.debug(f"Closing service: {name}")
                    instance.close()
                except Exception as e:
                    self.logger.error(f"Error closing {name}: {e}")
        
        self.clear()
        self.logger.info("Service registry shutdown complete")