"""
Base container and service descriptor for dependency injection.

This module provides the foundational classes for the DI system.
"""

from dataclasses import dataclass, field
from typing import Any, Dict, Optional, List, Union


@dataclass
class ServiceDescriptor:
    """
    Descriptor for a service to be registered in the DI container.
    
    Attributes:
        service_class: Fully qualified class name as string (for lazy loading)
        instance: Pre-created instance (for mocks or singletons)
        singleton: Whether to create only one instance
        dependencies: Dict mapping parameter names to service names
        tags: List of tags for categorizing the service
        metadata: Additional metadata about the service
    """
    service_class: Optional[str] = None
    instance: Optional[Any] = None
    singleton: bool = True
    dependencies: Dict[str, str] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate the descriptor."""
        if not self.service_class and not self.instance:
            raise ValueError("Either service_class or instance must be provided")
        if self.service_class and self.instance:
            raise ValueError("Cannot provide both service_class and instance")


class BaseContainer:
    """
    Base container for dependency injection.
    
    Provides basic container functionality that can be extended
    by specific container implementations.
    """
    
    def __init__(self):
        """Initialize the base container."""
        self._services: Dict[str, ServiceDescriptor] = {}
        self._instances: Dict[str, Any] = {}
        self._aliases: Dict[str, str] = {}
    
    def register(self, name: str, descriptor: ServiceDescriptor) -> None:
        """
        Register a service with the container.
        
        Args:
            name: Service name
            descriptor: Service descriptor
        """
        self._services[name] = descriptor
        
        # If an instance is provided, store it
        if descriptor.instance is not None:
            self._instances[name] = descriptor.instance
    
    def add_alias(self, alias: str, service_name: str) -> None:
        """
        Add an alias for a service.
        
        Args:
            alias: Alias name
            service_name: Actual service name
        """
        if service_name not in self._services:
            raise ValueError(f"Service '{service_name}' not found")
        self._aliases[alias] = service_name
    
    def get(self, name: str) -> Any:
        """
        Get a service instance.
        
        Args:
            name: Service name or alias
            
        Returns:
            Service instance
            
        Raises:
            KeyError: If service not found
        """
        # Resolve alias if needed
        actual_name = self._aliases.get(name, name)
        
        if actual_name not in self._services:
            raise KeyError(f"Service '{name}' not found")
        
        descriptor = self._services[actual_name]
        
        # Return pre-created instance if available
        if descriptor.instance is not None:
            return descriptor.instance
        
        # For singleton, check if already created
        if descriptor.singleton and actual_name in self._instances:
            return self._instances[actual_name]
        
        # Create new instance (subclasses should override this)
        instance = self._create_instance(actual_name, descriptor)
        
        # Store if singleton
        if descriptor.singleton:
            self._instances[actual_name] = instance
        
        return instance
    
    def _create_instance(self, name: str, descriptor: ServiceDescriptor) -> Any:
        """
        Create a new instance of a service.
        
        This should be overridden by subclasses to provide actual
        instance creation logic.
        
        Args:
            name: Service name
            descriptor: Service descriptor
            
        Returns:
            Service instance
        """
        raise NotImplementedError("Subclasses must implement _create_instance")
    
    def has_service(self, name: str) -> bool:
        """
        Check if a service is registered.
        
        Args:
            name: Service name or alias
            
        Returns:
            True if service exists
        """
        actual_name = self._aliases.get(name, name)
        return actual_name in self._services
    
    def get_services_by_tag(self, tag: str) -> List[str]:
        """
        Get all services with a specific tag.
        
        Args:
            tag: Tag to search for
            
        Returns:
            List of service names
        """
        result = []
        for name, descriptor in self._services.items():
            if tag in descriptor.tags:
                result.append(name)
        return result
    
    def clear(self) -> None:
        """Clear all registered services and instances."""
        self._services.clear()
        self._instances.clear()
        self._aliases.clear()