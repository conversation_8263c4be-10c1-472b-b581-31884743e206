"""
Dual logger setup for both console and file output.
This ensures all components log to both console and file.
"""

import logging
import os
import sys
from pathlib import Path
from typing import Optional

try:
    from rich.logging import <PERSON>Hand<PERSON>
    from rich.console import Console
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False


def create_dual_logger(
    name: str = "application",  # Changed from "lexgenius" to prevent accidental creation
    log_dir: Optional[str] = None,
    log_file: Optional[str] = None,
    level: int = logging.DEBUG
) -> logging.Logger:
    """
    Create a logger that outputs to both console and file.
    
    CRITICAL WARNING: This function should NOT be used with name="lexgenius" as it will create lexgenius.log files!
    Use only for specific component logging with explicit log_file names.
    
    Args:
        name: Logger name (DO NOT use "lexgenius")
        log_dir: Directory for log files (default: ./logs)
        log_file: Log file name (MUST be explicitly specified to avoid lexgenius.log creation)
        level: Logging level
        
    Returns:
        Logger configured with both file and console handlers
    """
    logger = logging.getLogger(name)
    
    # Avoid adding duplicate handlers
    if logger.handlers:
        return logger
    
    logger.setLevel(level)
    logger.propagate = False  # Prevent duplicate logging
    
    # Create formatter for file handler
    file_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    
    # Setup log directory and file
    if log_dir is None:
        log_dir = os.path.join(os.getcwd(), "logs")
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    if log_file is None:
        # CRITICAL: Never allow default log file names that could create lexgenius.log
        if name.lower() == "lexgenius":
            raise ValueError(
                "CRITICAL: Attempted to create lexgenius.log file! "
                "This is explicitly forbidden. Use console-only logger instead or specify explicit log_file."
            )
        log_file = f"{name}.log"
    
    log_path = os.path.join(log_dir, log_file)
    
    # Create file handler
    file_handler = logging.FileHandler(log_path)
    file_handler.setLevel(level)
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    # Create console handler
    if RICH_AVAILABLE:
        # Use RichHandler for beautiful console output
        console = Console(stderr=True, force_terminal=True)
        console_handler = RichHandler(
            console=console,
            rich_tracebacks=True,
            tracebacks_show_locals=True,
            show_time=True,
            show_level=True,
            show_path=True,
            markup=True,
            log_time_format="[%Y-%m-%d %H:%M:%S]"
        )
    else:
        # Fallback to standard StreamHandler
        console_handler = logging.StreamHandler(sys.stderr)
        console_formatter = logging.Formatter(
            "%(asctime)s - %(levelname)-8s - %(message)s",
            datefmt="%H:%M:%S"
        )
        console_handler.setFormatter(console_formatter)
    
    console_handler.setLevel(level)
    logger.addHandler(console_handler)
    
    return logger