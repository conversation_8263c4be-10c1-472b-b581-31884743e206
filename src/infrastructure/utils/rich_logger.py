"""
Rich logging utilities for enhanced console output.

This module provides utilities for creating rich, formatted console logs
with colors, styles, and better visual organization.
"""

from typing import Dict, Any, Optional
import logging
from datetime import datetime

try:
    from rich.console import Console
    from rich.logging import <PERSON><PERSON>and<PERSON>
    from rich.text import Text
    from rich.table import Table
    from rich.panel import Panel
    from rich.syntax import Syntax
    from rich import print as rprint
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False


class RichCourtLogger:
    """Enhanced court logger with rich formatting capabilities."""
    
    # Color scheme for different log levels and court operations
    COLORS = {
        'DEBUG': 'dim cyan',
        'INFO': 'green',
        'WARNING': 'yellow',
        'ERROR': 'red bold',
        'CRITICAL': 'red bold reverse',
        'SUCCESS': 'green bold',
        'COURT': 'blue bold',
        'DOCKET': 'magenta',
        'DOWNLOAD': 'cyan',
        'PROCESSING': 'yellow',
    }
    
    # Icons for different operations
    ICONS = {
        'START': '🚀',
        'SUCCESS': '✅',
        'ERROR': '❌',
        'WARNING': '⚠️',
        'INFO': 'ℹ️',
        'DEBUG': '🔍',
        'COURT': '⚖️',
        'DOCKET': '📋',
        'DOWNLOAD': '⬇️',
        'PROCESSING': '⚙️',
        'COMPLETE': '🎉',
    }
    
    @classmethod
    def format_court_message(cls, 
                            level: str,
                            court_id: str,
                            message: str,
                            context: Optional[Dict[str, Any]] = None) -> str:
        """
        Format a court log message with rich markup.
        
        Args:
            level: Log level (DEBUG, INFO, WARNING, ERROR)
            court_id: Court identifier
            message: Log message
            context: Optional context dictionary
            
        Returns:
            Formatted message with rich markup
        """
        if not RICH_AVAILABLE:
            return f"[{court_id}] {message}"
        
        # Get color for level
        color = cls.COLORS.get(level.upper(), 'white')
        
        # Get icon
        icon = cls.ICONS.get(level.upper(), '')
        
        # Format court ID with color
        court_str = f"[{cls.COLORS['COURT']}][{court_id.upper()}][/{cls.COLORS['COURT']}]"
        
        # Format message based on content
        if 'Phase' in message:
            message = f"[{cls.COLORS['PROCESSING']}]{message}[/{cls.COLORS['PROCESSING']}]"
        elif 'Download' in message:
            message = f"[{cls.COLORS['DOWNLOAD']}]{message}[/{cls.COLORS['DOWNLOAD']}]"
        elif 'Docket' in message or 'docket' in message:
            message = f"[{cls.COLORS['DOCKET']}]{message}[/{cls.COLORS['DOCKET']}]"
        elif 'SUCCESS' in message or 'completed' in message.lower():
            message = f"[{cls.COLORS['SUCCESS']}]{message}[/{cls.COLORS['SUCCESS']}]"
        else:
            message = f"[{color}]{message}[/{color}]"
        
        # Build final message
        formatted = f"{icon} {court_str} {message}"
        
        # Add context if provided
        if context:
            context_str = " | ".join([f"[dim]{k}[/dim]=[bold]{v}[/bold]" for k, v in context.items()])
            formatted += f" | {context_str}"
        
        return formatted
    
    @classmethod
    def create_progress_panel(cls, 
                            court_id: str,
                            phase: str,
                            steps_completed: int,
                            total_steps: int,
                            current_task: str) -> Panel:
        """
        Create a rich panel showing processing progress.
        
        Args:
            court_id: Court identifier
            phase: Current phase name
            steps_completed: Number of completed steps
            total_steps: Total number of steps
            current_task: Current task description
            
        Returns:
            Rich Panel object
        """
        if not RICH_AVAILABLE:
            return None
        
        # Create progress bar
        progress = steps_completed / total_steps if total_steps > 0 else 0
        bar_length = 20
        filled = int(bar_length * progress)
        bar = '█' * filled + '░' * (bar_length - filled)
        
        # Create content
        content = Text()
        content.append(f"Court: ", style="bold")
        content.append(f"{court_id.upper()}\n", style="blue bold")
        content.append(f"Phase: ", style="bold")
        content.append(f"{phase}\n", style="yellow")
        content.append(f"Progress: ", style="bold")
        content.append(f"[{bar}] {steps_completed}/{total_steps}\n", style="green")
        content.append(f"Current: ", style="bold")
        content.append(f"{current_task}", style="cyan")
        
        return Panel(
            content,
            title=f"🔄 Processing Status",
            border_style="blue",
            padding=(1, 2)
        )
    
    @classmethod
    def create_summary_table(cls, 
                            court_id: str,
                            results: Dict[str, Any]) -> Table:
        """
        Create a rich table summarizing processing results.
        
        Args:
            court_id: Court identifier
            results: Processing results dictionary
            
        Returns:
            Rich Table object
        """
        if not RICH_AVAILABLE:
            return None
        
        table = Table(
            title=f"📊 {court_id.upper()} Processing Summary",
            show_header=True,
            header_style="bold magenta"
        )
        
        table.add_column("Metric", style="cyan", width=20)
        table.add_column("Value", style="green")
        table.add_column("Status", justify="center", width=10)
        
        for key, value in results.items():
            status_icon = cls.ICONS['SUCCESS'] if value else cls.ICONS['ERROR']
            table.add_row(
                key.replace('_', ' ').title(),
                str(value),
                status_icon
            )
        
        return table


def setup_rich_court_logger(court_id: str, iso_date: str, config: Dict[str, Any]) -> logging.Logger:
    """
    Setup a court logger with rich formatting.
    
    Args:
        court_id: Court identifier
        iso_date: ISO date string
        config: Configuration dictionary
        
    Returns:
        Configured logger with rich formatting
    """
    if not RICH_AVAILABLE:
        # Fallback to standard logging
        from src.infrastructure.patterns.component_base import ComponentImplementation
        base = ComponentImplementation(logging.getLogger(), config)
        return base.create_court_logger(court_id, iso_date)
    
    logger_name = f"pacer.court.{court_id.lower()}"
    logger = logging.getLogger(logger_name)
    
    if logger.handlers:
        return logger
    
    logger.setLevel(logging.DEBUG)
    logger.propagate = False
    
    # Create file handler
    data_dir = config.get("DATA_DIR", "./data")
    log_dir = f"{data_dir}/{iso_date}/logs/pacer"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = f"{log_dir}/{court_id.lower()}.log"
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    file_handler.setFormatter(file_formatter)
    
    # Create rich console handler
    console = Console(
        stderr=True,
        force_terminal=True,
        color_system="auto",
        width=120
    )
    
    rich_handler = RichHandler(
        console=console,
        rich_tracebacks=True,
        tracebacks_show_locals=True,
        show_time=True,
        show_level=True,
        show_path=True,
        markup=True,
        log_time_format="[%H:%M:%S]",
        keywords=[
            "Phase", "SUCCESS", "ERROR", "WARNING",
            "Download", "Docket", "Court", "Processing",
            court_id.upper()
        ]
    )
    rich_handler.setLevel(logging.DEBUG)
    
    # Add custom filter to enhance messages
    class RichMessageFilter(logging.Filter):
        def filter(self, record):
            # Enhance the message with rich formatting
            if hasattr(record, 'court_id'):
                record.msg = RichCourtLogger.format_court_message(
                    record.levelname,
                    record.court_id,
                    record.msg,
                    getattr(record, 'context', None)
                )
            return True
    
    rich_handler.addFilter(RichMessageFilter())
    
    # Add handlers
    logger.addHandler(file_handler)
    logger.addHandler(rich_handler)
    
    return logger