"""
Date formatter utility for standardized date formatting across the application.

This module provides centralized date formatting with configurable format options,
specifically for handling ISO date formats in file paths and data directories.
"""

from __future__ import annotations
from datetime import datetime, date
from typing import Union, Optional, Dict, Any
from enum import Enum


class DateFormat(Enum):
    """Supported date format options."""
    YYYYMMDD = "%Y%m%d"  # Default format: 20240101
    YYYY_MM_DD = "%Y-%m-%d"  # Alternative format: 2024-01-01
    YYYY_MM_DD_SLASH = "%Y/%m/%d"  # Slash format: 2024/01/01


class DateFormatter:
    """
    Centralized date formatter for consistent date formatting.
    
    This class ensures all date formatting in the application follows
    a consistent pattern and can be easily switched between formats.
    """
    
    # Default format - YYYYMMDD as per current requirements
    _default_format = DateFormat.YYYYMMDD
    _format_override: Optional[DateFormat] = None
    
    @classmethod
    def configure(cls, config: Optional[Dict[str, Any]] = None) -> None:
        """
        Configure the date formatter with custom settings.
        
        Args:
            config: Configuration dictionary with optional 'date_format' key
        """
        if config and 'date_format' in config:
            format_str = config['date_format']
            try:
                cls._format_override = DateFormat[format_str] if isinstance(format_str, str) else DateFormat(format_str)
            except (KeyError, ValueError):
                # Invalid format specified, stick with default
                pass
    
    @classmethod
    def set_format(cls, date_format: DateFormat) -> None:
        """
        Set the date format to use globally.
        
        Args:
            date_format: The DateFormat enum value to use
        """
        cls._format_override = date_format
    
    @classmethod
    def get_current_format(cls) -> DateFormat:
        """
        Get the currently active date format.
        
        Returns:
            The active DateFormat enum value
        """
        return cls._format_override or cls._default_format
    
    @classmethod
    def format_date(cls, 
                   date_input: Union[datetime, date, str],
                   format_override: Optional[DateFormat] = None) -> str:
        """
        Format a date according to the configured format.
        
        Args:
            date_input: Date to format (datetime, date, or string in various formats)
            format_override: Optional format to use instead of the configured default
            
        Returns:
            Formatted date string
            
        Raises:
            ValueError: If date_input cannot be parsed
        """
        # Determine which format to use
        format_to_use = format_override or cls._format_override or cls._default_format
        
        # Convert input to datetime object if needed
        if isinstance(date_input, str):
            # Try to parse common formats
            for parse_format in ["%Y%m%d", "%Y-%m-%d", "%Y/%m/%d", "%m/%d/%Y", "%m/%d/%y"]:
                try:
                    date_obj = datetime.strptime(date_input, parse_format)
                    break
                except ValueError:
                    continue
            else:
                raise ValueError(f"Unable to parse date string: {date_input}")
        elif isinstance(date_input, date) and not isinstance(date_input, datetime):
            date_obj = datetime.combine(date_input, datetime.min.time())
        else:
            date_obj = date_input
        
        return date_obj.strftime(format_to_use.value)
    
    @classmethod
    def format_iso_date(cls, 
                       date_input: Union[datetime, date, str],
                       use_legacy: bool = True) -> str:
        """
        Format a date for use in ISO date paths.
        
        This is the primary method for formatting dates in file paths.
        By default, it uses YYYYMMDD format for backward compatibility.
        
        Args:
            date_input: Date to format
            use_legacy: If True, always use YYYYMMDD format regardless of configuration
            
        Returns:
            Formatted date string suitable for use in file paths
        """
        if use_legacy:
            # Always use YYYYMMDD for legacy compatibility
            return cls.format_date(date_input, DateFormat.YYYYMMDD)
        else:
            # Use configured format
            return cls.format_date(date_input)
    
    @classmethod
    def parse_iso_date(cls, iso_date_str: str) -> datetime:
        """
        Parse an ISO date string back to a datetime object.
        
        Args:
            iso_date_str: ISO date string to parse
            
        Returns:
            Parsed datetime object
            
        Raises:
            ValueError: If the string cannot be parsed
        """
        # Try parsing with all known formats
        for date_format in DateFormat:
            try:
                return datetime.strptime(iso_date_str, date_format.value)
            except ValueError:
                continue
        
        raise ValueError(f"Unable to parse ISO date string: {iso_date_str}")
    
    @classmethod
    def convert_format(cls,
                      date_str: str,
                      from_format: DateFormat,
                      to_format: DateFormat) -> str:
        """
        Convert a date string from one format to another.
        
        Args:
            date_str: Date string to convert
            from_format: Current format of the date string
            to_format: Desired format for the output
            
        Returns:
            Date string in the new format
        """
        date_obj = datetime.strptime(date_str, from_format.value)
        return date_obj.strftime(to_format.value)
    
    @classmethod
    def validate_iso_date(cls, iso_date_str: str) -> bool:
        """
        Validate if a string is a valid ISO date in any supported format.
        
        Args:
            iso_date_str: String to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            cls.parse_iso_date(iso_date_str)
            return True
        except ValueError:
            return False


# Convenience functions for backward compatibility
def format_iso_date(date_input: Union[datetime, date, str]) -> str:
    """
    Format a date as YYYYMMDD for use in file paths.
    
    This function maintains backward compatibility with existing code.
    
    Args:
        date_input: Date to format
        
    Returns:
        Date formatted as YYYYMMDD string
    """
    return DateFormatter.format_iso_date(date_input, use_legacy=True)


def get_iso_date_today() -> str:
    """
    Get today's date formatted as YYYYMMDD.
    
    Returns:
        Today's date as YYYYMMDD string
    """
    return DateFormatter.format_iso_date(datetime.now(), use_legacy=True)


def get_data_path(iso_date: Union[str, datetime, date], 
                  subpath: Optional[str] = None,
                  base_path: str = "data") -> str:
    """
    Get a properly formatted data path for the given ISO date.
    
    This function ensures consistent path formatting across the application.
    
    Args:
        iso_date: Date for the path (will be formatted as YYYYMMDD)
        subpath: Optional subpath to append (e.g., "logs/pacer", "dockets")
        base_path: Base data directory (default: "data")
        
    Returns:
        Formatted path string (e.g., "data/20240115" or "data/20240115/logs/pacer")
        
    Examples:
        >>> get_data_path("2024-01-15")
        'data/20240115'
        >>> get_data_path("2024-01-15", "logs/pacer")
        'data/20240115/logs/pacer'
        >>> get_data_path("20240115", "dockets", base_path="/custom/path")
        '/custom/path/20240115/dockets'
    """
    formatted_date = DateFormatter.format_iso_date(iso_date, use_legacy=True)
    
    if subpath:
        return f"{base_path}/{formatted_date}/{subpath}"
    else:
        return f"{base_path}/{formatted_date}"