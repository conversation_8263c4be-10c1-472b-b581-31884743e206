"""OpenAI/GPT-4 client implementation."""

import asyncio
from typing import Any

import aiohttp
from aiohttp import ClientTimeout

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import (
    ExternalServiceConnectionError,
    ExternalServiceError,
)
from src.infrastructure.protocols.logger import LoggerProtocol
from src.protocols.external import ILLMService


class OpenAIClient(ComponentImplementation, ILLMService):
    """OpenAI API client for GPT models."""

    def __init__(
        self,
        api_key: str,
        base_url: str = "https://api.openai.com/v1",
        timeout: int = 300,
        max_retries: int = 3,
        config: dict[str, Any] = None,
        logger: LoggerProtocol | None = None,
    ):
        """
        Initialize OpenAI client.

        Args:
            api_key: OpenAI API key
            base_url: API base URL
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries
            config: Configuration dictionary (optional)
            logger: Logger instance for dependency injection
        """
        # Initialize ComponentImplementation
        super().__init__(logger, config or {})

        self.api_key = api_key
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
        self.max_retries = max_retries
        # Logger is already set by ComponentImplementation as self.logger
        self._session = None

    async def __aenter__(self):
        """Async context manager entry."""
        self._session = aiohttp.ClientSession(
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            },
            timeout=ClientTimeout(total=self.timeout),
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._session:
            await self._session.close()

    async def close_session(self):
        """Close the aiohttp session. Called by cleanup functions."""
        if self._session and not self._session.closed:
            await self._session.close()
            self.log_debug("Closed OpenAI client session")
            self._session = None

    async def _execute_action(self, action_name: str, *args, **kwargs) -> Any:
        """
        Execute API actions with consistent error handling and logging.
        """
        if action_name == "generate_text":
            return await self.generate_text(*args, **kwargs)
        elif action_name == "process_batch":
            return await self.process_batch(*args, **kwargs)
        elif action_name == "summarize":
            return await self.summarize(*args, **kwargs)
        else:
            raise ValueError(f"Unknown action: {action_name}")

    async def generate_text(self, prompt: str, **kwargs) -> str:
        """
        Generate text from a prompt using GPT.

        Args:
            prompt: Input prompt
            **kwargs: Additional parameters (model, temperature, max_tokens, etc.)

        Returns:
            Generated text
        """
        if not self._session:
            raise ExternalServiceConnectionError(
                "Client not initialized. Use async context manager.", {}
            )

        model = kwargs.get("model", "gpt-4.1-nano")
        temperature = kwargs.get("temperature", 0.1)
        max_tokens = kwargs.get("max_tokens", 500)
        system_prompt = kwargs.get("system_prompt", "You are a helpful assistant.")

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt},
        ]

        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
        }

        # Add any additional parameters
        for key in ["top_p", "frequency_penalty", "presence_penalty", "stop", "n"]:
            if key in kwargs:
                payload[key] = kwargs[key]

        # Retry logic
        for attempt in range(self.max_retries):
            try:
                async with self._session.post(
                    f"{self.base_url}/chat/completions", json=payload
                ) as response:
                    response.raise_for_status()
                    data = await response.json()

                    if "choices" in data and data["choices"]:
                        return data["choices"][0]["message"]["content"]
                    else:
                        self.log_error(f"Unexpected response format: {data}")
                        raise ExternalServiceError(
                            "Invalid response format from OpenAI API", {"data": data}
                        )

            except aiohttp.ClientError as e:
                self.log_warning(
                    f"OpenAI API error (attempt {attempt + 1}/{self.max_retries}): {e}"
                )
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(2**attempt)  # Exponential backoff
                else:
                    raise ExternalServiceConnectionError(
                        f"OpenAI API error after {self.max_retries} attempts: {e}",
                        {"max_retries": self.max_retries},
                    )

    async def process_batch(self, prompts: list[str], **kwargs) -> list[str]:
        """
        Process multiple prompts efficiently.

        Args:
            prompts: List of input prompts
            **kwargs: Additional parameters

        Returns:
            List of generated texts
        """
        # OpenAI doesn't have native batch API for chat completions,
        # so we process concurrently with rate limiting
        semaphore = asyncio.Semaphore(5)  # Limit concurrent requests

        async def process_with_semaphore(prompt):
            async with semaphore:
                return await self.generate_text(prompt, **kwargs)

        tasks = [process_with_semaphore(prompt) for prompt in prompts]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Convert exceptions to None
        return [result if isinstance(result, str) else None for result in results]

    async def summarize(self, text: str, max_length: int = 200, **kwargs) -> str:
        """
        Generate a summary of the given text.

        Args:
            text: Text to summarize
            max_length: Maximum length of summary
            **kwargs: Additional parameters

        Returns:
            Summary text
        """
        prompt = f"""Please provide a concise summary of the following text in no more than {max_length} words:

{text}

Summary:"""

        return await self.generate_text(
            prompt,
            system_prompt="You are an expert at creating clear, concise summaries.",
            temperature=0.5,
            **kwargs,
        )

    async def determine_court_id(self, court_description: str) -> dict[str, any]:
        """
        Determine court ID from court description.

        Args:
            court_description: Description of the court

        Returns:
            Dictionary with court_id and confidence
        """
        system_prompt = """You are a legal expert who identifies US federal court IDs.
        Given a court description, return the official court ID code.

        Examples:
        - "Missouri Eastern" -> "moed"
        - "Northern District of California" -> "cand"
        - "Southern District of New York" -> "nysd"

        Return only the court ID code, nothing else."""

        prompt = f"What is the court ID for: {court_description}"

        try:
            response = await self.generate_text(
                prompt, system_prompt=system_prompt, temperature=0.0, max_tokens=10
            )

            if response and isinstance(response, str):
                # Extract just the court ID (should be 3-4 letter code)
                court_id = response.strip().lower()
                if len(court_id) >= 3 and len(court_id) <= 5 and court_id.isalpha():
                    return {"court_id": court_id, "confidence": 0.8}
                else:
                    self.log_warning(f"Invalid court ID format from GPT: '{court_id}'")
                    return {"court_id": None, "confidence": 0}
            else:
                self.log_error(f"Invalid response type from GPT: {type(response)}")
                return {"court_id": None, "confidence": 0}

        except Exception as e:
            self.log_error(f"Error in determine_court_id: {e}")
            return {"court_id": None, "confidence": 0}
