"""
Command Pattern Implementation

Encapsulates requests as objects, allowing parameterization and queuing.
Part of the infrastructure patterns library.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import logging
import asyncio
from collections import deque


class CommandStatus(Enum):
    """Command execution status."""
    PENDING = "pending"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Command(ABC):
    """
    Abstract command interface.
    
    Encapsulates an action and its parameters.
    """
    
    @abstractmethod
    def execute(self) -> Any:
        """Execute the command."""
        pass
    
    @abstractmethod
    def undo(self) -> None:
        """Undo the command (if supported)."""
        pass
    
    def can_undo(self) -> bool:
        """Check if command supports undo."""
        return False


class AsyncCommand(ABC):
    """
    Asynchronous command interface.
    """
    
    @abstractmethod
    async def execute(self) -> Any:
        """Execute the command asynchronously."""
        pass
    
    async def undo(self) -> None:
        """Undo the command asynchronously."""
        raise NotImplementedError("Async undo not implemented")


@dataclass
class CommandResult:
    """Result of command execution."""
    command_id: str
    status: CommandStatus
    result: Any = None
    error: Optional[Exception] = None
    executed_at: datetime = field(default_factory=datetime.now)
    execution_time_ms: float = 0.0


class SimpleCommand(Command):
    """
    Simple command implementation with callable.
    """
    
    def __init__(self, receiver: Any, action: Callable, *args, **kwargs):
        self._receiver = receiver
        self._action = action
        self._args = args
        self._kwargs = kwargs
        self._result = None
    
    def execute(self) -> Any:
        """Execute the command action."""
        self._result = self._action(self._receiver, *self._args, **self._kwargs)
        return self._result
    
    def undo(self) -> None:
        """Undo not supported for simple commands."""
        raise NotImplementedError("Simple commands don't support undo")


class UndoableCommand(Command):
    """
    Command with undo support.
    """
    
    def __init__(self, do_action: Callable, undo_action: Callable):
        self._do_action = do_action
        self._undo_action = undo_action
        self._state = None
    
    def execute(self) -> Any:
        """Execute and save state for undo."""
        result = self._do_action()
        self._state = result
        return result
    
    def undo(self) -> None:
        """Undo the command."""
        if self._state is not None:
            self._undo_action(self._state)
            self._state = None
    
    def can_undo(self) -> bool:
        """This command supports undo."""
        return True


class MacroCommand(Command):
    """
    Composite command that executes multiple commands.
    """
    
    def __init__(self):
        self._commands: List[Command] = []
    
    def add_command(self, command: Command) -> None:
        """Add a command to the macro."""
        self._commands.append(command)
    
    def execute(self) -> List[Any]:
        """Execute all commands in sequence."""
        results = []
        for command in self._commands:
            result = command.execute()
            results.append(result)
        return results
    
    def undo(self) -> None:
        """Undo all commands in reverse order."""
        for command in reversed(self._commands):
            if command.can_undo():
                command.undo()
    
    def can_undo(self) -> bool:
        """Check if all commands support undo."""
        return all(cmd.can_undo() for cmd in self._commands)


class CommandInvoker:
    """
    Invoker that manages command execution.
    
    Maintains command history and supports undo/redo.
    """
    
    def __init__(self, history_size: int = 100):
        self._history: deque = deque(maxlen=history_size)
        self._undo_stack: List[Command] = []
        self._redo_stack: List[Command] = []
        self._logger = logging.getLogger(self.__class__.__name__)
    
    def execute(self, command: Command) -> Any:
        """Execute a command and maintain history."""
        try:
            result = command.execute()
            self._history.append(command)
            
            if command.can_undo():
                self._undo_stack.append(command)
                self._redo_stack.clear()  # Clear redo stack on new command
            
            self._logger.info(f"Executed command: {command.__class__.__name__}")
            return result
            
        except Exception as e:
            self._logger.error(f"Command execution failed: {e}")
            raise
    
    def undo(self) -> bool:
        """Undo the last undoable command."""
        if not self._undo_stack:
            self._logger.warning("No commands to undo")
            return False
        
        command = self._undo_stack.pop()
        try:
            command.undo()
            self._redo_stack.append(command)
            self._logger.info(f"Undone command: {command.__class__.__name__}")
            return True
        except Exception as e:
            self._logger.error(f"Undo failed: {e}")
            return False
    
    def redo(self) -> bool:
        """Redo the last undone command."""
        if not self._redo_stack:
            self._logger.warning("No commands to redo")
            return False
        
        command = self._redo_stack.pop()
        try:
            command.execute()
            self._undo_stack.append(command)
            self._logger.info(f"Redone command: {command.__class__.__name__}")
            return True
        except Exception as e:
            self._logger.error(f"Redo failed: {e}")
            return False
    
    def get_history(self) -> List[Command]:
        """Get command history."""
        return list(self._history)
    
    def clear_history(self) -> None:
        """Clear all history and stacks."""
        self._history.clear()
        self._undo_stack.clear()
        self._redo_stack.clear()


class CommandQueue:
    """
    Queue for command execution.
    
    Supports priority and delayed execution.
    """
    
    def __init__(self):
        self._queue: List[tuple] = []  # (priority, command)
        self._processing = False
        self._logger = logging.getLogger(self.__class__.__name__)
    
    def enqueue(self, command: Command, priority: int = 0) -> None:
        """Add command to queue with priority."""
        self._queue.append((priority, command))
        self._queue.sort(key=lambda x: x[0], reverse=True)
    
    def dequeue(self) -> Optional[Command]:
        """Remove and return highest priority command."""
        if self._queue:
            _, command = self._queue.pop(0)
            return command
        return None
    
    def process_all(self) -> List[Any]:
        """Process all commands in queue."""
        results = []
        self._processing = True
        
        while self._queue and self._processing:
            command = self.dequeue()
            if command:
                try:
                    result = command.execute()
                    results.append(result)
                except Exception as e:
                    self._logger.error(f"Command failed: {e}")
                    results.append(None)
        
        self._processing = False
        return results
    
    def stop_processing(self) -> None:
        """Stop processing commands."""
        self._processing = False
    
    def clear(self) -> None:
        """Clear the queue."""
        self._queue.clear()
    
    def size(self) -> int:
        """Get queue size."""
        return len(self._queue)


class AsyncCommandQueue:
    """
    Asynchronous command queue.
    """
    
    def __init__(self):
        self._queue: asyncio.Queue = asyncio.Queue()
        self._processing = False
        self._logger = logging.getLogger(self.__class__.__name__)
    
    async def enqueue(self, command: AsyncCommand) -> None:
        """Add command to queue."""
        await self._queue.put(command)
    
    async def dequeue(self) -> Optional[AsyncCommand]:
        """Get next command from queue."""
        try:
            return await asyncio.wait_for(self._queue.get(), timeout=1.0)
        except asyncio.TimeoutError:
            return None
    
    async def process_all(self) -> List[Any]:
        """Process all commands asynchronously."""
        results = []
        self._processing = True
        
        while self._processing:
            command = await self.dequeue()
            if command:
                try:
                    result = await command.execute()
                    results.append(result)
                except Exception as e:
                    self._logger.error(f"Async command failed: {e}")
                    results.append(None)
            elif self._queue.empty():
                break
        
        self._processing = False
        return results
    
    def stop_processing(self) -> None:
        """Stop processing commands."""
        self._processing = False