"""
Strategy Pattern Implementation

Defines a family of algorithms and makes them interchangeable.
Part of the infrastructure patterns library.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Type, Callable
import logging


class Strategy(ABC):
    """
    Abstract strategy interface.
    
    Defines the interface for all concrete strategies.
    """
    
    @abstractmethod
    def execute(self, *args, **kwargs) -> Any:
        """Execute the strategy algorithm."""
        pass


class Context:
    """
    Context class that uses strategies.
    
    Maintains a reference to a strategy object and delegates execution.
    """
    
    def __init__(self, strategy: Optional[Strategy] = None):
        self._strategy = strategy
        self._logger = logging.getLogger(self.__class__.__name__)
    
    def set_strategy(self, strategy: Strategy) -> None:
        """Set or change the strategy."""
        self._strategy = strategy
        self._logger.debug(f"Strategy set to: {strategy.__class__.__name__}")
    
    def execute_strategy(self, *args, **kwargs) -> Any:
        """Execute the current strategy."""
        if not self._strategy:
            raise ValueError("No strategy set")
        
        return self._strategy.execute(*args, **kwargs)


class StrategyRegistry:
    """
    Registry for managing multiple strategies.
    
    Allows dynamic strategy selection based on conditions.
    """
    
    def __init__(self):
        self._strategies: Dict[str, Strategy] = {}
        self._default_strategy: Optional[str] = None
        self._logger = logging.getLogger(self.__class__.__name__)
    
    def register(self, name: str, strategy: Strategy, set_as_default: bool = False) -> None:
        """Register a strategy with a name."""
        self._strategies[name] = strategy
        
        if set_as_default or not self._default_strategy:
            self._default_strategy = name
        
        self._logger.info(f"Registered strategy: {name}")
    
    def get_strategy(self, name: str) -> Strategy:
        """Get a strategy by name."""
        if name not in self._strategies:
            raise ValueError(f"Strategy '{name}' not found")
        
        return self._strategies[name]
    
    def get_default_strategy(self) -> Strategy:
        """Get the default strategy."""
        if not self._default_strategy:
            raise ValueError("No default strategy set")
        
        return self._strategies[self._default_strategy]
    
    def execute(self, strategy_name: Optional[str] = None, *args, **kwargs) -> Any:
        """Execute a strategy by name or use default."""
        if strategy_name:
            strategy = self.get_strategy(strategy_name)
        else:
            strategy = self.get_default_strategy()
        
        return strategy.execute(*args, **kwargs)
    
    def list_strategies(self) -> list:
        """List all registered strategy names."""
        return list(self._strategies.keys())


class ConditionalStrategy(Strategy):
    """
    Strategy that selects sub-strategies based on conditions.
    
    Implements conditional logic for strategy selection.
    """
    
    def __init__(self):
        self._conditions: list = []
        self._default_strategy: Optional[Strategy] = None
    
    def add_condition(self, condition: Callable, strategy: Strategy) -> None:
        """Add a condition-strategy pair."""
        self._conditions.append((condition, strategy))
    
    def set_default(self, strategy: Strategy) -> None:
        """Set the default strategy when no conditions match."""
        self._default_strategy = strategy
    
    def execute(self, *args, **kwargs) -> Any:
        """Execute the appropriate strategy based on conditions."""
        # Check conditions in order
        for condition, strategy in self._conditions:
            if condition(*args, **kwargs):
                return strategy.execute(*args, **kwargs)
        
        # Use default if no conditions match
        if self._default_strategy:
            return self._default_strategy.execute(*args, **kwargs)
        
        raise ValueError("No matching strategy found and no default set")


class CachingStrategy(Strategy):
    """
    Decorator strategy that adds caching to another strategy.
    
    Caches results based on input parameters.
    """
    
    def __init__(self, strategy: Strategy, cache_size: int = 128):
        self._strategy = strategy
        self._cache: Dict[tuple, Any] = {}
        self._cache_size = cache_size
        self._logger = logging.getLogger(self.__class__.__name__)
    
    def execute(self, *args, **kwargs) -> Any:
        """Execute with caching."""
        # Create cache key from arguments
        cache_key = (args, tuple(sorted(kwargs.items())))
        
        # Check cache
        if cache_key in self._cache:
            self._logger.debug("Cache hit")
            return self._cache[cache_key]
        
        # Execute strategy
        result = self._strategy.execute(*args, **kwargs)
        
        # Update cache (with simple size limit)
        if len(self._cache) >= self._cache_size:
            # Remove oldest entry (simple FIFO)
            self._cache.pop(next(iter(self._cache)))
        
        self._cache[cache_key] = result
        self._logger.debug("Cache miss - result cached")
        
        return result
    
    def clear_cache(self) -> None:
        """Clear the cache."""
        self._cache.clear()


class CompositeStrategy(Strategy):
    """
    Composite strategy that combines multiple strategies.
    
    Can execute strategies in sequence or aggregate their results.
    """
    
    def __init__(self, aggregation_func: Optional[Callable] = None):
        self._strategies: list = []
        self._aggregation_func = aggregation_func or self._default_aggregation
    
    def add_strategy(self, strategy: Strategy) -> None:
        """Add a strategy to the composite."""
        self._strategies.append(strategy)
    
    def execute(self, *args, **kwargs) -> Any:
        """Execute all strategies and aggregate results."""
        results = []
        
        for strategy in self._strategies:
            result = strategy.execute(*args, **kwargs)
            results.append(result)
        
        return self._aggregation_func(results)
    
    @staticmethod
    def _default_aggregation(results: list) -> Any:
        """Default aggregation returns all results."""
        return results


# Concrete strategy implementations for common use cases

class ValidationStrategy(Strategy):
    """Strategy for validation logic."""
    
    def __init__(self, validation_func: Callable):
        self._validation_func = validation_func
    
    def execute(self, data: Any) -> bool:
        """Execute validation."""
        return self._validation_func(data)


class TransformationStrategy(Strategy):
    """Strategy for data transformation."""
    
    def __init__(self, transform_func: Callable):
        self._transform_func = transform_func
    
    def execute(self, data: Any) -> Any:
        """Execute transformation."""
        return self._transform_func(data)


class ProcessingStrategy(Strategy):
    """Strategy for data processing."""
    
    def __init__(self, process_func: Callable):
        self._process_func = process_func
    
    def execute(self, *args, **kwargs) -> Any:
        """Execute processing."""
        return self._process_func(*args, **kwargs)