"""
Resource Pool Pattern Implementation

Manages a pool of reusable resources to improve performance.
Part of the infrastructure patterns library.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Generic, TypeVar, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque
import asyncio
import threading
import logging
from contextlib import contextmanager
from enum import Enum


T = TypeVar('T')


class ResourceState(Enum):
    """State of a pooled resource."""
    AVAILABLE = "available"
    IN_USE = "in_use"
    INVALID = "invalid"
    INITIALIZING = "initializing"


@dataclass
class PooledResource(Generic[T]):
    """Wrapper for pooled resources with metadata."""
    resource: T
    state: ResourceState = ResourceState.AVAILABLE
    created_at: datetime = field(default_factory=datetime.now)
    last_used: datetime = field(default_factory=datetime.now)
    use_count: int = 0
    
    def is_expired(self, max_age: timedelta) -> bool:
        """Check if resource has expired."""
        return datetime.now() - self.created_at > max_age
    
    def is_idle(self, max_idle: timedelta) -> bool:
        """Check if resource has been idle too long."""
        return datetime.now() - self.last_used > max_idle


class ResourcePool(ABC, Generic[T]):
    """
    Abstract base class for resource pools.
    
    Manages creation, validation, and lifecycle of pooled resources.
    """
    
    def __init__(
        self,
        min_size: int = 1,
        max_size: int = 10,
        max_age: Optional[timedelta] = None,
        max_idle: Optional[timedelta] = None
    ):
        self.min_size = min_size
        self.max_size = max_size
        self.max_age = max_age or timedelta(hours=1)
        self.max_idle = max_idle or timedelta(minutes=30)
        
        self._pool: deque = deque()
        self._in_use: Dict[int, PooledResource[T]] = {}
        self._lock = threading.RLock()
        self._logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize minimum resources
        self._initialize_pool()
    
    @abstractmethod
    def create_resource(self) -> T:
        """Create a new resource instance."""
        pass
    
    @abstractmethod
    def validate_resource(self, resource: T) -> bool:
        """Validate that a resource is still usable."""
        pass
    
    @abstractmethod
    def destroy_resource(self, resource: T) -> None:
        """Clean up and destroy a resource."""
        pass
    
    def _initialize_pool(self) -> None:
        """Initialize the pool with minimum resources."""
        for _ in range(self.min_size):
            try:
                resource = self.create_resource()
                pooled = PooledResource(resource)
                self._pool.append(pooled)
            except Exception as e:
                self._logger.error(f"Failed to create resource: {e}")
    
    def acquire(self, timeout: Optional[float] = None) -> T:
        """
        Acquire a resource from the pool.
        
        Args:
            timeout: Maximum time to wait for a resource
            
        Returns:
            A resource instance
            
        Raises:
            TimeoutError: If no resource available within timeout
        """
        start_time = datetime.now()
        
        while True:
            with self._lock:
                # Try to get an available resource
                pooled = self._get_available_resource()
                
                if pooled:
                    pooled.state = ResourceState.IN_USE
                    pooled.last_used = datetime.now()
                    pooled.use_count += 1
                    self._in_use[id(pooled.resource)] = pooled
                    self._logger.debug(f"Resource acquired: {id(pooled.resource)}")
                    return pooled.resource
                
                # Try to create a new resource if under max size
                if len(self._pool) + len(self._in_use) < self.max_size:
                    try:
                        resource = self.create_resource()
                        pooled = PooledResource(resource, state=ResourceState.IN_USE)
                        pooled.use_count = 1
                        self._in_use[id(resource)] = pooled
                        self._logger.debug(f"New resource created: {id(resource)}")
                        return resource
                    except Exception as e:
                        self._logger.error(f"Failed to create resource: {e}")
            
            # Check timeout
            if timeout:
                elapsed = (datetime.now() - start_time).total_seconds()
                if elapsed >= timeout:
                    raise TimeoutError(f"Could not acquire resource within {timeout} seconds")
            
            # Brief wait before retry
            threading.Event().wait(0.1)
    
    def release(self, resource: T) -> None:
        """
        Release a resource back to the pool.
        
        Args:
            resource: The resource to release
        """
        with self._lock:
            resource_id = id(resource)
            
            if resource_id not in self._in_use:
                self._logger.warning(f"Attempting to release unknown resource: {resource_id}")
                return
            
            pooled = self._in_use.pop(resource_id)
            
            # Validate resource before returning to pool
            if self.validate_resource(resource):
                pooled.state = ResourceState.AVAILABLE
                self._pool.append(pooled)
                self._logger.debug(f"Resource released: {resource_id}")
            else:
                # Destroy invalid resource
                self._logger.warning(f"Invalid resource destroyed: {resource_id}")
                self.destroy_resource(resource)
                
                # Create replacement if below minimum
                if len(self._pool) < self.min_size:
                    try:
                        new_resource = self.create_resource()
                        new_pooled = PooledResource(new_resource)
                        self._pool.append(new_pooled)
                    except Exception as e:
                        self._logger.error(f"Failed to create replacement resource: {e}")
    
    def _get_available_resource(self) -> Optional[PooledResource[T]]:
        """Get an available resource from the pool."""
        while self._pool:
            pooled = self._pool.popleft()
            
            # Check if resource is still valid
            if pooled.is_expired(self.max_age) or pooled.is_idle(self.max_idle):
                self._logger.debug(f"Resource expired/idle: {id(pooled.resource)}")
                self.destroy_resource(pooled.resource)
                continue
            
            if self.validate_resource(pooled.resource):
                return pooled
            else:
                self._logger.debug(f"Resource validation failed: {id(pooled.resource)}")
                self.destroy_resource(pooled.resource)
        
        return None
    
    @contextmanager
    def get_resource(self, timeout: Optional[float] = None):
        """
        Context manager for resource acquisition and release.
        
        Usage:
            with pool.get_resource() as resource:
                # Use resource
                pass
        """
        resource = self.acquire(timeout)
        try:
            yield resource
        finally:
            self.release(resource)
    
    def clear(self) -> None:
        """Clear all resources from the pool."""
        with self._lock:
            # Destroy pooled resources
            while self._pool:
                pooled = self._pool.popleft()
                self.destroy_resource(pooled.resource)
            
            # Destroy in-use resources (careful!)
            for pooled in self._in_use.values():
                self._logger.warning(f"Destroying in-use resource: {id(pooled.resource)}")
                self.destroy_resource(pooled.resource)
            
            self._in_use.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get pool statistics."""
        with self._lock:
            return {
                "available": len(self._pool),
                "in_use": len(self._in_use),
                "total": len(self._pool) + len(self._in_use),
                "min_size": self.min_size,
                "max_size": self.max_size
            }


class AsyncResourcePool(Generic[T]):
    """
    Asynchronous resource pool implementation.
    """
    
    def __init__(
        self,
        create_func: Callable[[], T],
        validate_func: Callable[[T], bool],
        destroy_func: Callable[[T], None],
        min_size: int = 1,
        max_size: int = 10
    ):
        self.create_func = create_func
        self.validate_func = validate_func
        self.destroy_func = destroy_func
        self.min_size = min_size
        self.max_size = max_size
        
        self._pool: asyncio.Queue = asyncio.Queue()
        self._size = 0
        self._lock = asyncio.Lock()
        self._logger = logging.getLogger(self.__class__.__name__)
    
    async def initialize(self) -> None:
        """Initialize the pool with minimum resources."""
        for _ in range(self.min_size):
            resource = await self.create_func()
            await self._pool.put(resource)
            self._size += 1
    
    async def acquire(self) -> T:
        """Acquire a resource from the pool."""
        # Try to get from pool
        try:
            resource = self._pool.get_nowait()
            
            # Validate resource
            if await self.validate_func(resource):
                return resource
            else:
                await self.destroy_func(resource)
                self._size -= 1
        except asyncio.QueueEmpty:
            pass
        
        # Create new resource if under max size
        async with self._lock:
            if self._size < self.max_size:
                resource = await self.create_func()
                self._size += 1
                return resource
        
        # Wait for available resource
        resource = await self._pool.get()
        
        # Validate and return or recurse
        if await self.validate_func(resource):
            return resource
        else:
            await self.destroy_func(resource)
            self._size -= 1
            return await self.acquire()
    
    async def release(self, resource: T) -> None:
        """Release a resource back to the pool."""
        if await self.validate_func(resource):
            await self._pool.put(resource)
        else:
            await self.destroy_func(resource)
            self._size -= 1
            
            # Create replacement if below minimum
            if self._size < self.min_size:
                new_resource = await self.create_func()
                await self._pool.put(new_resource)
                self._size += 1