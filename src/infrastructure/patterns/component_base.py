# /src/infrastructure/patterns/component_base.py

"""
Base implementation template for standardized component patterns.

This module provides the ComponentImplementation base class that follows
the template defined in the backup rules for consistent error handling,
logging, and configuration management.
"""

import logging
import os
import sys
from abc import ABC, abstractmethod
from typing import Any

from src.infrastructure.protocols.exceptions import Configuration<PERSON>rror, ServiceError
from src.infrastructure.protocols.logger import LoggerProtocol

try:
    from rich.logging import RichHandler
    from rich.console import Console
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False


class ComponentImplementation(ABC):
    """
    Base implementation class for any component following the ComponentImplementation template.

    This provides standardized error handling, logging, and configuration management
    patterns across all service components.
    """

    def __init__(self, logger: LoggerProtocol | logging.Logger, config: dict[str, Any] | None = None):
        """Initialize the component with logger and config."""
        if logger is None:
            # Create a basic logger as fallback to prevent NoneType errors
            import logging
            self.logger = logging.getLogger(self.__class__.__name__)
            self.logger.warning(f"{self.__class__.__name__} initialized with None logger, using fallback logger")
        else:
            self.logger = logger
        
        # Ensure config is always a dict, even if a string is passed
        if isinstance(config, str):
            try:
                import json
                self.config = json.loads(config)
            except (json.JSONDecodeError, TypeError, ValueError):
                if logger:
                    logger.warning(f"{self.__class__.__name__} received string config but couldn't parse as JSON, using empty dict")
                self.config = {}
        elif config is None:
            self.config = {}
        else:
            self.config = config
            
        self._validate_config()

    def _validate_config(self) -> None:
        """Validate component configuration. Override in subclasses."""
        # Base implementation does nothing - override in subclasses for specific validation
        return

    @abstractmethod
    async def _execute_action(self, data: Any) -> Any:
        """The actual implementation. Must be implemented by subclasses."""
        pass

    async def execute(self, data: Any) -> Any:
        """
        Execute the component's action. Primary interface method.
        
        Args:
            data: Any data input.
            
        Returns:
            Any: Result of execution
            
        Raises:
            ServiceError: For component-specific errors
        """
        return await self.perform_action(data)

    async def perform_action(self, data: Any) -> Any:
        """
        Perform the core action with standardized error handling.

        Args:
           data: Any data input.

        Returns:
           Any: Result of action

        Raises:
           ServiceError: For component-specific errors
        """
        try:
            self.logger.debug(
                "performing action",
                extra={"component": self.__class__.__name__, "data": data},
            )
            result = await self._execute_action(data)
            self.logger.info(
                "action complete",
                extra={"component": self.__class__.__name__, "result": result},
            )
            return result
        except Exception as e:
            self.logger.exception(
                "action failed",
                extra={
                    "component": self.__class__.__name__,
                    "error": str(e),
                    "data": data,
                },
            )
            raise ServiceError(
                f"{self.__class__.__name__} failed: {str(e)}",
                {"original_error": e, "data": data},
            )

    def log_debug(self, message: str, extra: dict[str, Any] | None = None) -> None:
        """Log debug message with component context."""
        context = {"component": self.__class__.__name__}
        if extra:
            context.update(extra)
        self.logger.debug(message, extra=context)

    def log_info(self, message: str, extra: dict[str, Any] | None = None) -> None:
        """Log info message with component context."""
        context = {"component": self.__class__.__name__}
        if extra:
            context.update(extra)
        self.logger.info(message, extra=context)

    def log_warning(self, message: str, extra: dict[str, Any] | None = None) -> None:
        """Log warning message with component context."""
        context = {"component": self.__class__.__name__}
        if extra:
            context.update(extra)
        self.logger.warning(message, extra=context)

    def log_error(
        self,
        message: str,
        extra: dict[str, Any] | None = None,
        exc_info: bool = False,
    ) -> None:
        """Log error message with component context."""
        context = {"component": self.__class__.__name__}
        if extra:
            context.update(extra)
        self.logger.error(message, extra=context, exc_info=exc_info)

    def log_critical(
        self,
        message: str,
        extra: dict[str, Any] | None = None,
        exc_info: bool = False,
    ) -> None:
        """Log critical message with component context."""
        context = {"component": self.__class__.__name__}
        if extra:
            context.update(extra)
        if hasattr(self.logger, 'critical'):
            self.logger.critical(message, extra=context, exc_info=exc_info)
        else:
            # Fallback to error if critical not available
            self.logger.error(f"CRITICAL: {message}", extra=context, exc_info=exc_info)

    def log_exception(
        self,
        message: str,
        extra: dict[str, Any] | None = None,
    ) -> None:
        """Log exception message with component context and stack trace."""
        context = {"component": self.__class__.__name__}
        if extra:
            context.update(extra)
        self.logger.exception(message, extra=context)

    def create_court_logger(self, court_id: str, iso_date: str) -> logging.Logger:
        """
        Create a court-specific logger with comprehensive log interception.
        
        Uses the enhanced CourtLogger to ensure ALL log messages are captured
        in the court-specific log file at data/{iso_date}/logs/pacer/{court_id}.log
        
        Args:
            court_id: The court identifier (e.g., 'cand', 'nysd')
            iso_date: The ISO date string for directory organization
            
        Returns:
            logging.Logger: Court-specific logger with enhanced capabilities
        """
        try:
            from src.pacer.utils.court_logger import create_court_logger
            court_logger_instance = create_court_logger(court_id, iso_date, self.config)
            logger = court_logger_instance.get_logger()
            
            # CRITICAL: Replace this component's logger with the court logger
            # This ensures ALL logging from this component goes to the court log
            self.logger = logger
            
            self.log_debug(f"Created enhanced court logger for {court_id}", {
                "court_id": court_id,
                "logger_name": logger.name,
                "handlers": len(logger.handlers)
            })
            
            return logger
            
        except ImportError:
            # Fallback to original implementation if CourtLogger is not available
            return self._create_court_logger_fallback(court_id, iso_date)
    
    def _create_court_logger_fallback(self, court_id: str, iso_date: str) -> logging.Logger:
        """Fallback court logger implementation."""
        logger_name = f"pacer.court.{court_id.lower()}"
        court_logger = logging.getLogger(logger_name)
        
        # Avoid adding duplicate handlers
        if court_logger.handlers:
            # CRITICAL: Replace this component's logger with the court logger
            self.logger = court_logger
            return court_logger
            
        court_logger.setLevel(logging.DEBUG)
        court_logger.propagate = False  # Prevent propagation to root logger
        
        # Create the pacer logs directory using component's config
        data_dir = self.config.get("DATA_DIR", os.path.join(os.getcwd(), "data"))
        log_dir = os.path.join(data_dir, iso_date, "logs", "pacer")
        os.makedirs(log_dir, exist_ok=True)
        
        # Create formatter for both handlers
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        
        # Create file handler for court-specific logging
        log_file = os.path.join(log_dir, f"{court_id.lower()}.log")
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # Create console handler with rich formatting if available
        if RICH_AVAILABLE:
            # Use RichHandler for beautiful console output
            console = Console(stderr=True, force_terminal=True)
            console_handler = RichHandler(
                console=console,
                rich_tracebacks=True,
                tracebacks_show_locals=True,
                show_time=True,
                show_level=True,
                show_path=True,
                markup=True,
                log_time_format="[%Y-%m-%d %H:%M:%S]"
            )
            console_handler.setLevel(logging.DEBUG)
            # RichHandler uses its own formatting, no need to set formatter
        else:
            # Fallback to standard StreamHandler
            console_handler = logging.StreamHandler(sys.stderr)
            console_handler.setLevel(logging.DEBUG)
            console_handler.setFormatter(formatter)
        
        # Add both handlers to the logger
        court_logger.addHandler(file_handler)
        court_logger.addHandler(console_handler)
        
        # CRITICAL: Replace this component's logger with the court logger
        # This ensures ALL future logging from this component goes to court log
        self.logger = court_logger
        
        self.log_debug(f"Created fallback court logger for {court_id}", {
            "court_id": court_id,
            "log_file": log_file,
            "logger_name": logger_name
        })
        
        return court_logger
    
    def create_court_logging_context(self, court_id: str, iso_date: str):
        """
        Create a comprehensive court logging context that intercepts ALL logs.
        
        This context manager ensures that all logging within its scope is captured
        in the court-specific log file, preventing any logs from escaping.
        
        Args:
            court_id: Court identifier (e.g., 'cand', 'nysd')
            iso_date: ISO date string for directory organization
            
        Usage:
            with self.create_court_logging_context('cand', '20250110') as court_logger:
                # All logs within this block go to court-specific file
                self.process_court_data()
        
        Returns:
            Context manager that yields the court logger
        """
        try:
            from src.pacer.utils.court_logger import setup_court_logging_context
            context = setup_court_logging_context(court_id, iso_date, self.config)
            
            # Create a custom context manager that also replaces this component's logger
            from contextlib import contextmanager
            
            @contextmanager
            def enhanced_context():
                original_logger = self.logger
                try:
                    with context as court_logger:
                        # Replace this component's logger during the context
                        self.logger = court_logger
                        yield court_logger
                finally:
                    # Restore original logger when context exits
                    self.logger = original_logger
            
            return enhanced_context()
            
        except ImportError:
            # Fallback to regular court logger without full interception
            court_logger = self.create_court_logger(court_id, iso_date)
            from contextlib import nullcontext
            return nullcontext(court_logger)
    
    def log_to_court(self, court_logger, level: str, message: str, court_id: str = None, extra: dict = None) -> None:
        """
        Helper method to log to both general logger and court-specific logger.
        
        Args:
            court_logger: Court-specific logger instance
            level: Log level ('info', 'debug', 'warning', 'error', 'exception')
            message: Log message
            court_id: Court identifier (optional, for formatting)
            extra: Extra context dictionary
        """
        # Format message for court logger with court ID prefix
        court_message = f"[{court_id.upper()}] {message}" if court_id else message
        
        # Log to court-specific logger first (primary)
        if court_logger and hasattr(court_logger, level):
            getattr(court_logger, level)(court_message, extra=extra)
        
        # Also log to general logger (secondary, for debugging/monitoring)
        general_method = getattr(self, f"log_{level}", None)
        if general_method:
            general_method(f"[{court_id}] {message}" if court_id else message, extra)


class AsyncServiceBase(ComponentImplementation):
    """
    Base class for async services with additional service-specific patterns.

    Extends ComponentImplementation with service lifecycle management
    and dependency injection patterns.
    """

    def __init__(self, logger: LoggerProtocol | logging.Logger, config: dict[str, Any] | None = None):
        super().__init__(logger, config)
        self._initialized = False
        self._dependencies: dict[str, Any] = {}

    async def initialize(self) -> None:
        """Initialize the service. Override in subclasses."""
        if self._initialized:
            return

        try:
            await self._initialize_service()
            self._initialized = True
            self.log_info("service initialized successfully")
        except Exception as e:
            self.log_error("service initialization failed", {"error": str(e)})
            raise ServiceError(
                f"Failed to initialize {self.__class__.__name__}: {str(e)}"
            )

    async def cleanup(self) -> None:
        """Cleanup service resources. Override in subclasses."""
        try:
            await self._cleanup_service()
            self._initialized = False
            self.log_info("service cleanup completed")
        except Exception as e:
            self.log_warning("service cleanup failed", {"error": str(e)})

    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
        return False

    def set_dependency(self, name: str, dependency: Any) -> None:
        """Set a service dependency."""
        self._dependencies[name] = dependency
        self.log_debug(
            f"dependency '{name}' injected",
            {"dependency_type": type(dependency).__name__},
        )

    def get_dependency(self, name: str) -> Any:
        """Get a service dependency."""
        if name not in self._dependencies:
            raise ConfigurationError(
                f"Required dependency '{name}' not found in {self.__class__.__name__}"
            )
        return self._dependencies[name]

    async def _initialize_service(self) -> None:
        """Override in subclasses for service-specific initialization."""
        pass

    async def _cleanup_service(self) -> None:
        """Override in subclasses for service-specific cleanup."""
        pass
