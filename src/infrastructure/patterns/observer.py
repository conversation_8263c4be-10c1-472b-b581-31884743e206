"""
Observer Pattern Implementation

Provides event-driven communication between objects.
Part of the infrastructure patterns library.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Callable, Optional
from enum import Enum
import asyncio
import logging
from dataclasses import dataclass
from datetime import datetime


class EventType(Enum):
    """Standard event types."""
    CREATED = "created"
    UPDATED = "updated"
    DELETED = "deleted"
    ERROR = "error"
    INFO = "info"
    WARNING = "warning"
    CUSTOM = "custom"


@dataclass
class Event:
    """Event data structure."""
    type: EventType
    source: str
    data: Any
    timestamp: datetime = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.metadata is None:
            self.metadata = {}


class Observer(ABC):
    """
    Abstract observer interface.
    
    Defines the update method that all observers must implement.
    """
    
    @abstractmethod
    def update(self, event: Event) -> None:
        """Receive and process an event."""
        pass


class AsyncObserver(ABC):
    """
    Asynchronous observer interface.
    
    For observers that need to perform async operations.
    """
    
    @abstractmethod
    async def update(self, event: Event) -> None:
        """Receive and process an event asynchronously."""
        pass


class Subject(ABC):
    """
    Abstract subject (observable) interface.
    
    Manages observers and notifies them of events.
    """
    
    def __init__(self):
        self._observers: List[Observer] = []
        self._logger = logging.getLogger(self.__class__.__name__)
    
    def attach(self, observer: Observer) -> None:
        """Attach an observer."""
        if observer not in self._observers:
            self._observers.append(observer)
            self._logger.debug(f"Attached observer: {observer.__class__.__name__}")
    
    def detach(self, observer: Observer) -> None:
        """Detach an observer."""
        if observer in self._observers:
            self._observers.remove(observer)
            self._logger.debug(f"Detached observer: {observer.__class__.__name__}")
    
    def notify(self, event: Event) -> None:
        """Notify all observers of an event."""
        for observer in self._observers:
            try:
                observer.update(event)
            except Exception as e:
                self._logger.error(f"Observer error: {e}")


class AsyncSubject(ABC):
    """
    Asynchronous subject for async observers.
    """
    
    def __init__(self):
        self._observers: List[AsyncObserver] = []
        self._logger = logging.getLogger(self.__class__.__name__)
    
    def attach(self, observer: AsyncObserver) -> None:
        """Attach an async observer."""
        if observer not in self._observers:
            self._observers.append(observer)
    
    def detach(self, observer: AsyncObserver) -> None:
        """Detach an async observer."""
        if observer in self._observers:
            self._observers.remove(observer)
    
    async def notify(self, event: Event) -> None:
        """Notify all observers asynchronously."""
        tasks = [observer.update(event) for observer in self._observers]
        await asyncio.gather(*tasks, return_exceptions=True)


class EventBus:
    """
    Central event bus for decoupled communication.
    
    Implements a publish-subscribe pattern with topic-based routing.
    """
    
    def __init__(self):
        self._subscribers: Dict[str, List[Callable]] = {}
        self._async_subscribers: Dict[str, List[Callable]] = {}
        self._logger = logging.getLogger(self.__class__.__name__)
    
    def subscribe(self, topic: str, callback: Callable) -> None:
        """Subscribe to a topic with a callback."""
        if topic not in self._subscribers:
            self._subscribers[topic] = []
        
        self._subscribers[topic].append(callback)
        self._logger.debug(f"Subscribed to topic '{topic}'")
    
    def subscribe_async(self, topic: str, callback: Callable) -> None:
        """Subscribe to a topic with an async callback."""
        if topic not in self._async_subscribers:
            self._async_subscribers[topic] = []
        
        self._async_subscribers[topic].append(callback)
    
    def unsubscribe(self, topic: str, callback: Callable) -> None:
        """Unsubscribe from a topic."""
        if topic in self._subscribers and callback in self._subscribers[topic]:
            self._subscribers[topic].remove(callback)
            
            if not self._subscribers[topic]:
                del self._subscribers[topic]
    
    def publish(self, topic: str, event: Event) -> None:
        """Publish an event to a topic."""
        if topic in self._subscribers:
            for callback in self._subscribers[topic]:
                try:
                    callback(event)
                except Exception as e:
                    self._logger.error(f"Error in subscriber callback: {e}")
    
    async def publish_async(self, topic: str, event: Event) -> None:
        """Publish an event asynchronously."""
        tasks = []
        
        # Handle sync subscribers
        if topic in self._subscribers:
            for callback in self._subscribers[topic]:
                tasks.append(asyncio.create_task(
                    asyncio.to_thread(callback, event)
                ))
        
        # Handle async subscribers
        if topic in self._async_subscribers:
            for callback in self._async_subscribers[topic]:
                tasks.append(asyncio.create_task(callback(event)))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    def clear(self, topic: Optional[str] = None) -> None:
        """Clear subscribers for a topic or all topics."""
        if topic:
            self._subscribers.pop(topic, None)
            self._async_subscribers.pop(topic, None)
        else:
            self._subscribers.clear()
            self._async_subscribers.clear()


class ConcreteObserver(Observer):
    """
    Concrete observer implementation for testing and simple use cases.
    """
    
    def __init__(self, name: str, handler: Optional[Callable] = None):
        self.name = name
        self._handler = handler
        self._events_received: List[Event] = []
    
    def update(self, event: Event) -> None:
        """Process received event."""
        self._events_received.append(event)
        
        if self._handler:
            self._handler(event)
    
    def get_events(self) -> List[Event]:
        """Get all received events."""
        return self._events_received.copy()
    
    def clear_events(self) -> None:
        """Clear received events."""
        self._events_received.clear()


# Global event bus instance
_global_event_bus = EventBus()


def get_event_bus() -> EventBus:
    """Get the global event bus instance."""
    return _global_event_bus


def publish_event(topic: str, event: Event) -> None:
    """Publish an event to the global event bus."""
    _global_event_bus.publish(topic, event)


async def publish_event_async(topic: str, event: Event) -> None:
    """Publish an event asynchronously to the global event bus."""
    await _global_event_bus.publish_async(topic, event)