"""
Builder Pattern Implementation

Provides a flexible solution for constructing complex objects step by step.
Part of the infrastructure patterns library.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, TypeVar, Generic
import copy

T = TypeVar('T')


class Builder(ABC, Generic[T]):
    """
    Abstract Builder interface.
    
    Defines the steps for constructing complex objects.
    """
    
    @abstractmethod
    def reset(self) -> None:
        """Reset the builder to initial state."""
        pass
    
    @abstractmethod
    def build(self) -> T:
        """Build and return the final product."""
        pass


class ConfigurableBuilder(Builder[T]):
    """
    Builder with configuration support.
    
    Allows building objects with configuration dictionaries.
    """
    
    def __init__(self):
        self._config: Dict[str, Any] = {}
        self._product: Optional[T] = None
        self.reset()
    
    def reset(self) -> None:
        """Reset builder to initial state."""
        self._config = {}
        self._product = None
    
    def with_config(self, config: Dict[str, Any]) -> 'ConfigurableBuilder':
        """Set configuration from dictionary."""
        self._config.update(config)
        return self
    
    def set(self, key: str, value: Any) -> 'ConfigurableBuilder':
        """Set a configuration value."""
        self._config[key] = value
        return self
    
    def get_config(self) -> Dict[str, Any]:
        """Get current configuration."""
        return copy.deepcopy(self._config)
    
    @abstractmethod
    def build(self) -> T:
        """Build the product with current configuration."""
        pass


class ServiceBuilder(ConfigurableBuilder[Any]):
    """
    Builder for constructing service instances.
    
    Handles dependency injection and configuration.
    """
    
    def __init__(self, service_class: type):
        super().__init__()
        self._service_class = service_class
        self._dependencies: Dict[str, Any] = {}
        self._initialization_params: Dict[str, Any] = {}
    
    def with_dependency(self, name: str, dependency: Any) -> 'ServiceBuilder':
        """Add a dependency."""
        self._dependencies[name] = dependency
        return self
    
    def with_dependencies(self, dependencies: Dict[str, Any]) -> 'ServiceBuilder':
        """Add multiple dependencies."""
        self._dependencies.update(dependencies)
        return self
    
    def with_param(self, name: str, value: Any) -> 'ServiceBuilder':
        """Add an initialization parameter."""
        self._initialization_params[name] = value
        return self
    
    def with_params(self, params: Dict[str, Any]) -> 'ServiceBuilder':
        """Add multiple initialization parameters."""
        self._initialization_params.update(params)
        return self
    
    def build(self) -> Any:
        """Build the service instance."""
        # Combine dependencies and params
        all_params = {
            **self._dependencies,
            **self._initialization_params,
            **self._config
        }
        
        # Create instance
        instance = self._service_class(**all_params)
        
        # Reset for next build
        self.reset()
        
        return instance
    
    def reset(self) -> None:
        """Reset builder state."""
        super().reset()
        self._dependencies = {}
        self._initialization_params = {}


class FluentBuilder:
    """
    Fluent interface builder for creating objects with method chaining.
    
    Provides a readable way to construct complex objects.
    """
    
    def __init__(self):
        self._properties: Dict[str, Any] = {}
    
    def __getattr__(self, name: str):
        """Dynamic method generation for property setting."""
        if name.startswith('with_'):
            property_name = name[5:]  # Remove 'with_' prefix
            
            def setter(value: Any) -> 'FluentBuilder':
                self._properties[property_name] = value
                return self
            
            return setter
        
        raise AttributeError(f"'{self.__class__.__name__}' has no attribute '{name}'")
    
    def build(self, target_class: type) -> Any:
        """Build the object with collected properties."""
        return target_class(**self._properties)
    
    def get_properties(self) -> Dict[str, Any]:
        """Get collected properties."""
        return copy.deepcopy(self._properties)
    
    def reset(self) -> 'FluentBuilder':
        """Reset the builder."""
        self._properties = {}
        return self


class Director:
    """
    Director class that defines the order of building steps.
    
    Works with builders to construct products according to specific patterns.
    """
    
    def __init__(self, builder: Builder):
        self._builder = builder
    
    def set_builder(self, builder: Builder) -> None:
        """Change the builder instance."""
        self._builder = builder
    
    @abstractmethod
    def construct(self) -> Any:
        """Construct the product using the builder."""
        pass


class StandardDirector(Director):
    """
    Standard director implementation with predefined construction patterns.
    """
    
    def __init__(self, builder: Builder):
        super().__init__(builder)
        self._construction_steps: list = []
    
    def add_step(self, step_func: callable) -> 'StandardDirector':
        """Add a construction step."""
        self._construction_steps.append(step_func)
        return self
    
    def construct(self) -> Any:
        """Execute all construction steps and build the product."""
        for step in self._construction_steps:
            step(self._builder)
        
        return self._builder.build()
    
    def reset_steps(self) -> None:
        """Clear all construction steps."""
        self._construction_steps = []