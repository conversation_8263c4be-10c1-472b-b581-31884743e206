"""
Registry Pattern Implementation

Provides a centralized registry for component registration and lookup.
Part of the infrastructure patterns library.
"""

from typing import Dict, Type, Any, Optional, TypeVar, Generic
from abc import ABC, abstractmethod
import threading

T = TypeVar('T')


class ComponentRegistry(Generic[T], ABC):
    """
    Abstract base class for component registries.
    
    Implements the Registry pattern for managing collections of components.
    Thread-safe implementation for concurrent access.
    """
    
    def __init__(self):
        self._registry: Dict[str, T] = {}
        self._lock = threading.RLock()
    
    def register(self, name: str, component: T) -> None:
        """Register a component with the given name."""
        with self._lock:
            if name in self._registry:
                raise ValueError(f"Component '{name}' is already registered")
            self._registry[name] = component
    
    def unregister(self, name: str) -> None:
        """Unregister a component by name."""
        with self._lock:
            if name not in self._registry:
                raise KeyError(f"Component '{name}' is not registered")
            del self._registry[name]
    
    def get(self, name: str) -> Optional[T]:
        """Get a component by name."""
        with self._lock:
            return self._registry.get(name)
    
    def get_all(self) -> Dict[str, T]:
        """Get all registered components."""
        with self._lock:
            return dict(self._registry)
    
    def exists(self, name: str) -> bool:
        """Check if a component is registered."""
        with self._lock:
            return name in self._registry
    
    def clear(self) -> None:
        """Clear all registered components."""
        with self._lock:
            self._registry.clear()
    
    @abstractmethod
    def create_default(self) -> T:
        """Create a default component instance."""
        pass


class ServiceRegistry(ComponentRegistry[Any]):
    """Registry for service components."""
    
    def create_default(self) -> Any:
        """Services don't have a default implementation."""
        raise NotImplementedError("Services must be explicitly registered")


class SingletonRegistry:
    """
    Registry for singleton instances.
    
    Ensures only one instance of each registered class exists.
    """
    
    _instances: Dict[Type, Any] = {}
    _lock = threading.RLock()
    
    @classmethod
    def get_instance(cls, class_type: Type[T], *args, **kwargs) -> T:
        """Get or create a singleton instance of the given class."""
        with cls._lock:
            if class_type not in cls._instances:
                cls._instances[class_type] = class_type(*args, **kwargs)
            return cls._instances[class_type]
    
    @classmethod
    def clear(cls):
        """Clear all singleton instances."""
        with cls._lock:
            cls._instances.clear()


# Global service registry
_service_registry = ServiceRegistry()


def register_service(name: str, service: Any) -> None:
    """Register a service in the global registry."""
    _service_registry.register(name, service)


def get_service(name: str) -> Optional[Any]:
    """Get a service from the global registry."""
    return _service_registry.get(name)


def service_exists(name: str) -> bool:
    """Check if a service is registered."""
    return _service_registry.exists(name)