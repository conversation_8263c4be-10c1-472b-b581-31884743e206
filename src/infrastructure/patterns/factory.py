"""
Factory Pattern Implementation

Provides abstract factory and factory method patterns for object creation.
Part of the infrastructure patterns library.
"""

from abc import ABC, abstractmethod
from typing import TypeVar, Type, Dict, Any, Optional, Callable
import logging

T = TypeVar('T')


class AbstractFactory(ABC):
    """
    Abstract Factory pattern base class.
    
    Provides an interface for creating families of related objects.
    """
    
    @abstractmethod
    def create(self, *args, **kwargs) -> Any:
        """Create an instance of the product."""
        pass


class Factory(AbstractFactory):
    """
    Concrete factory implementation with registration capabilities.
    
    Allows registration of creation functions for different types.
    """
    
    def __init__(self):
        self._creators: Dict[str, Callable] = {}
        self._logger = logging.getLogger(self.__class__.__name__)
    
    def register(self, key: str, creator: Callable) -> None:
        """Register a creator function for a given key."""
        self._creators[key] = creator
        self._logger.debug(f"Registered creator for key: {key}")
    
    def create(self, key: str, *args, **kwargs) -> Any:
        """Create an instance using the registered creator."""
        creator = self._creators.get(key)
        if not creator:
            raise ValueError(f"No creator registered for key: {key}")
        
        return creator(*args, **kwargs)
    
    def exists(self, key: str) -> bool:
        """Check if a creator is registered for the given key."""
        return key in self._creators
    
    def get_registered_keys(self) -> list:
        """Get all registered keys."""
        return list(self._creators.keys())


class ServiceFactory:
    """
    Factory specifically for creating service instances.
    
    Manages service creation with dependency injection support.
    """
    
    def __init__(self):
        self._service_classes: Dict[str, Type] = {}
        self._service_instances: Dict[str, Any] = {}
        self._logger = logging.getLogger(self.__class__.__name__)
    
    def register_service(self, name: str, service_class: Type) -> None:
        """Register a service class."""
        self._service_classes[name] = service_class
        self._logger.info(f"Registered service: {name}")
    
    def create_service(self, name: str, **dependencies) -> Any:
        """
        Create a service instance with dependencies.
        
        Args:
            name: Service name
            **dependencies: Dependencies to inject
            
        Returns:
            Service instance
        """
        if name not in self._service_classes:
            raise ValueError(f"Service '{name}' is not registered")
        
        service_class = self._service_classes[name]
        
        # Create instance with dependencies
        try:
            instance = service_class(**dependencies)
            self._logger.info(f"Created service instance: {name}")
            return instance
        except Exception as e:
            self._logger.error(f"Failed to create service '{name}': {e}")
            raise
    
    def get_or_create_service(self, name: str, **dependencies) -> Any:
        """
        Get existing service instance or create a new one.
        
        Implements singleton pattern for services.
        """
        if name not in self._service_instances:
            self._service_instances[name] = self.create_service(name, **dependencies)
        
        return self._service_instances[name]
    
    def clear_instances(self) -> None:
        """Clear all cached service instances."""
        self._service_instances.clear()
        self._logger.info("Cleared all service instances")


class ComponentFactory:
    """
    Factory for creating component instances.
    
    Supports different component types with customizable creation strategies.
    """
    
    def __init__(self):
        self._component_types: Dict[str, Type] = {}
        self._creation_strategies: Dict[str, Callable] = {}
        self._logger = logging.getLogger(self.__class__.__name__)
    
    def register_component(
        self, 
        component_type: str, 
        component_class: Type,
        creation_strategy: Optional[Callable] = None
    ) -> None:
        """
        Register a component type with optional creation strategy.
        
        Args:
            component_type: Type identifier for the component
            component_class: Class to instantiate
            creation_strategy: Optional custom creation function
        """
        self._component_types[component_type] = component_class
        
        if creation_strategy:
            self._creation_strategies[component_type] = creation_strategy
        
        self._logger.debug(f"Registered component type: {component_type}")
    
    def create_component(self, component_type: str, *args, **kwargs) -> Any:
        """
        Create a component instance.
        
        Uses custom creation strategy if available, otherwise standard instantiation.
        """
        if component_type not in self._component_types:
            raise ValueError(f"Component type '{component_type}' is not registered")
        
        # Use custom strategy if available
        if component_type in self._creation_strategies:
            strategy = self._creation_strategies[component_type]
            return strategy(*args, **kwargs)
        
        # Default instantiation
        component_class = self._component_types[component_type]
        return component_class(*args, **kwargs)
    
    def get_component_types(self) -> list:
        """Get all registered component types."""
        return list(self._component_types.keys())


# Global factories
_service_factory = ServiceFactory()
_component_factory = ComponentFactory()


def get_service_factory() -> ServiceFactory:
    """Get the global service factory instance."""
    return _service_factory


def get_component_factory() -> ComponentFactory:
    """Get the global component factory instance."""
    return _component_factory