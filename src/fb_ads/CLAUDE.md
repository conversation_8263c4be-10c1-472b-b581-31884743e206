# Facebook Ads Domain Module

## Overview
This is the Facebook Ads domain module containing all business logic for Facebook ad processing, classification, and API integration. This is a MODERATE complexity domain with ~30-40 files.

## Architecture Status
🚧 **NEW DOMAIN**: Being created from components in `src/services/fb_ads/`

## Directory Structure
```
fb_ads/
├── models/              # Domain models
│   ├── ad.py
│   ├── advertiser.py
│   └── job_models.py
├── components/          # Component architecture
│   ├── api/            # API client components
│   │   ├── base/       # Base API classes
│   │   ├── camoufox/   # Camoufox integration
│   │   └── graphql/    # GraphQL parsing
│   ├── processing/     # Ad processing logic
│   │   ├── classifier.py
│   │   ├── categorizer.py
│   │   └── ner_analyzer.py
│   ├── workflow/       # Workflow management
│   │   ├── concurrent_workflow.py
│   │   └── workflow_service.py
│   └── storage/        # Storage and caching
│       ├── disk_cache.py
│       ├── image_handler.py
│       └── local_queue.py
├── core/                # Core services
│   ├── ad_processor.py
│   ├── api_client.py
│   ├── session_manager.py
│   └── orchestrator.py
├── factories/           # Domain factories
│   ├── api_client_factory.py
│   └── session_manager_factory.py
├── interfaces/          # Domain interfaces and protocols
│   └── fb_ads_protocol.py
└── exceptions.py        # Domain-specific exceptions
```

## Key Principles
1. **NO cross-domain dependencies** - FB Ads cannot import from pacer, transformer, or reports
2. **Use infrastructure patterns** - Inherit from `src.infrastructure.patterns.component_base.AsyncServiceBase`
3. **API isolation** - All Facebook API calls through dedicated components
4. **Domain-driven design** - Business logic lives here, not in service layer

## Migration Notes
- Moving from `src/services/fb_ads/` (40+ files)
- Service layer will only contain orchestration
- Complex API integration requires careful component organization

## Base Classes
```python
# ALWAYS use this base class, NEVER create domain-specific base classes
from src.infrastructure.patterns.component_base import AsyncServiceBase
```

## Core Services
1. **AdProcessor** - Main ad processing logic
2. **APIClient** - Facebook API integration
3. **SessionManager** - Session and auth management
4. **Classifier** - Ad classification logic
5. **WorkflowService** - Workflow orchestration

## Testing
All components should have corresponding tests in `tests/unit/fb_ads/`

## Dependencies
- Infrastructure patterns
- External: Facebook API, Database, Image processing
- NO dependencies on other domains

## Contact
Architecture questions: See docs/ARCHITECTURAL_CONSISTENCY_PLAN.md