"""Processing-related Pydantic models for transformer service."""
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator

from src.transformer.config.constants import (
    JobStatus, ProcessingStatus, ValidationStatus
)


class ProcessingMetadata(BaseModel):
    """Metadata for tracking processing state and history."""
    
    start_time: datetime = Field(default_factory=datetime.utcnow)
    end_time: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    processor_id: Optional[str] = None
    processor_version: Optional[str] = None
    
    # Processing context
    iso_date: Optional[str] = None
    court_id: Optional[str] = None
    batch_id: Optional[str] = None
    worker_id: Optional[int] = None
    
    # Resource usage
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    
    def calculate_duration(self) -> float:
        """Calculate processing duration in seconds."""
        if self.end_time and self.start_time:
            self.duration_seconds = (self.end_time - self.start_time).total_seconds()
        return self.duration_seconds or 0.0


class ValidationReport(BaseModel):
    """Report from validation operations."""
    
    status: ValidationStatus
    is_valid: bool = Field(default=True)
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    validated_fields: List[str] = Field(default_factory=list)
    missing_fields: List[str] = Field(default_factory=list)
    
    # Validation details
    field_reports: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    validation_rules_applied: List[str] = Field(default_factory=list)
    
    @field_validator('status', mode='before')
    @classmethod
    def ensure_enum(cls, v):
        """Ensure status is a ValidationStatus enum."""
        if isinstance(v, str):
            return ValidationStatus(v)
        return v
    
    def add_error(self, field: str, message: str):
        """Add an error for a specific field."""
        self.errors.append(f"{field}: {message}")
        self.is_valid = False
        self.status = ValidationStatus.ERROR
        
        if field not in self.field_reports:
            self.field_reports[field] = {'errors': [], 'warnings': []}
        self.field_reports[field]['errors'].append(message)
    
    def add_warning(self, field: str, message: str):
        """Add a warning for a specific field."""
        self.warnings.append(f"{field}: {message}")
        if self.status == ValidationStatus.VALID:
            self.status = ValidationStatus.WARNING
            
        if field not in self.field_reports:
            self.field_reports[field] = {'errors': [], 'warnings': []}
        self.field_reports[field]['warnings'].append(message)


class ProcessingResult(BaseModel):
    """Result from a processing operation."""
    
    status: ProcessingStatus
    success: bool = Field(default=True)
    data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    
    # Processing details
    operation_name: str
    input_count: int = 0
    output_count: int = 0
    skipped_count: int = 0
    error_count: int = 0
    
    # Metadata
    metadata: ProcessingMetadata = Field(default_factory=ProcessingMetadata)
    
    @field_validator('status', mode='before')
    @classmethod
    def ensure_enum(cls, v):
        """Ensure status is a ProcessingStatus enum."""
        if isinstance(v, str):
            return ProcessingStatus(v)
        return v
    
    def mark_success(self, message: Optional[str] = None):
        """Mark the result as successful."""
        self.status = ProcessingStatus.COMPLETED
        self.success = True
        if message:
            self.data = self.data or {}
            self.data['message'] = message
        self.metadata.end_time = datetime.utcnow()
        self.metadata.calculate_duration()
    
    def mark_error(self, error: Union[str, Exception]):
        """Mark the result as failed."""
        self.status = ProcessingStatus.ERROR
        self.success = False
        self.error_message = str(error)
        self.errors.append(str(error))
        self.error_count += 1
        self.metadata.end_time = datetime.utcnow()
        self.metadata.calculate_duration()


class TransformationResult(BaseModel):
    """Result from a complete transformation job."""
    
    job_id: str
    status: JobStatus
    
    # File information
    json_path: str
    pdf_path: Optional[str] = None
    md_path: Optional[str] = None
    
    # Processing results
    processing_results: Dict[str, ProcessingResult] = Field(default_factory=dict)
    validation_report: Optional[ValidationReport] = None
    
    # Job metadata
    force_reprocess: bool = False
    start_time: datetime = Field(default_factory=datetime.utcnow)
    end_time: Optional[datetime] = None
    processing_time: Optional[float] = None
    
    # Data
    original_data: Optional[Dict[str, Any]] = None
    transformed_data: Optional[Dict[str, Any]] = None
    
    # Errors and warnings
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    
    @field_validator('status', mode='before')
    @classmethod
    def ensure_enum(cls, v):
        """Ensure status is a JobStatus enum."""
        if isinstance(v, str):
            return JobStatus(v)
        return v
    
    def add_processing_result(self, operation: str, result: ProcessingResult):
        """Add a processing result for an operation."""
        self.processing_results[operation] = result
        
        # Update overall status based on results
        if not result.success:
            self.status = JobStatus.ERROR
            self.errors.extend(result.errors)
        
        self.warnings.extend(result.warnings)
    
    def complete(self):
        """Mark the transformation as complete."""
        self.end_time = datetime.utcnow()
        self.processing_time = (self.end_time - self.start_time).total_seconds()
        
        # Determine final status
        if any(not r.success for r in self.processing_results.values()):
            self.status = JobStatus.ERROR
        elif self.status == JobStatus.IN_PROGRESS:
            self.status = JobStatus.SUCCESS


class ValidationResult(BaseModel):
    """Result from a validation operation."""
    
    is_valid: bool
    validation_type: str
    field_name: Optional[str] = None
    value: Any = None
    
    # Validation details
    error_message: Optional[str] = None
    suggestion: Optional[str] = None
    applied_rules: List[str] = Field(default_factory=list)
    
    def to_error_string(self) -> str:
        """Convert to error string for logging."""
        if self.field_name:
            return f"{self.field_name}: {self.error_message or 'Invalid'}"
        return self.error_message or 'Validation failed'


class ActionRequest(BaseModel):
    """Request model for action handlers."""
    
    action: str = Field(..., description="Action name to execute")
    data: Dict[str, Any] = Field(default_factory=dict, description="Action data")
    
    # Optional metadata
    request_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    source: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for action handler."""
        return {
            'action': self.action,
            'data': self.data
        }


class ActionResponse(BaseModel):
    """Response model from action handlers."""
    
    success: bool
    action: str
    result: Any = None
    error: Optional[str] = None
    
    # Response metadata
    response_id: Optional[str] = None
    request_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    duration_ms: Optional[float] = None
    
    @classmethod
    def success_response(cls, action: str, result: Any) -> 'ActionResponse':
        """Create a successful response."""
        return cls(success=True, action=action, result=result)
    
    @classmethod
    def error_response(cls, action: str, error: Union[str, Exception]) -> 'ActionResponse':
        """Create an error response."""
        return cls(success=False, action=action, error=str(error))