"""
PACER scraping configuration.
"""

from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field, field_validator

from .base import WorkflowConfig


class ScraperConfig(WorkflowConfig):
    """PACER scraping configuration"""
    # Core scraper settings
    scraper: bool = Field(True, description="Enable PACER scraping")
    post_process: bool = Field(True, description="Enable post-processing")
    upload: bool = Field(True, description="Enable upload to AWS")
    headless: bool = Field(False, description="Run browser in headless mode")
    run_parallel: bool = Field(True, description="Enable parallel processing")

    model_config = {
        **WorkflowConfig.model_config,
        "extra": "allow"  # Allow extra fields during migration
    }

    # Court processing
    process_single_court: Optional[List[str]] = Field(None, description="Specific court IDs to process")
    skip_courts: Optional[List[str]] = Field(None, description="Court IDs to skip")
    court_batch_size: int = Field(5, ge=1, le=20, description="Number of courts to process in parallel")

    # File processing
    process_single_file: Optional[List[str]] = Field(None, description="Specific files to process")
    file_batch_size: int = Field(10, ge=1, le=50, description="Number of files to process in parallel")

    # Performance settings
    page_load_timeout: int = Field(30, ge=5, le=120, description="Page load timeout in seconds")
    element_wait_timeout: int = Field(10, ge=1, le=30, description="Element wait timeout in seconds")
    download_timeout: int = Field(300, ge=30, le=600, description="File download timeout in seconds")

    # Retry settings
    max_retries: int = Field(3, ge=0, le=10, description="Maximum retry attempts")
    retry_delay_seconds: int = Field(5, ge=1, le=60, description="Delay between retries")

    # Processing options
    skip_existing: bool = Field(True, description="Skip already processed files")
    force_reprocess: bool = Field(False, description="Force reprocessing of existing files")

    # Relevance configuration
    relevance_check_enabled: bool = Field(True, description="Enable case relevance checking")
    relevance_threshold: float = Field(0.7, ge=0.0, le=1.0, description="Relevance score threshold")

    # Delay configuration for rate limiting
    enable_delays: bool = Field(True, description="Enable delays between operations")
    inter_court_delay: float = Field(0.5, ge=0.0, le=60.0, description="Delay between courts in seconds")
    inter_case_delay: float = Field(0.5, ge=0.0, le=30.0, description="Delay between cases in seconds")
    inter_operation_delay: float = Field(0.05, ge=0.0, le=10.0, description="Delay between operations in seconds")
    inter_download_delay: float = Field(1.0, ge=0.0, le=30.0, description="Delay between downloads in seconds")
    randomize_delays: bool = Field(True, description="Add random variation to delays")
    randomization_factor: float = Field(0.05, ge=0.0, le=0.5, description="Delay randomization factor")

    # Browser pool configuration
    browser_pool_size: Optional[int] = Field(None, description="Size of browser pool (defaults to num_workers)")
    reuse_browser_for_failed_dockets: bool = Field(True,
                                                   description="Reuse browser contexts for failed docket processing")
    reuse_browser_for_parallel_courts: bool = Field(True,
                                                    description="Reuse browser contexts for parallel court processing")
    max_courts_per_batch: int = Field(10, ge=1, le=100,
                                      description="Maximum courts to process in one batch for failed dockets")

    @field_validator('process_single_court', 'skip_courts')
    @classmethod
    def validate_court_lists(cls, v):
        """Ensure court IDs are valid"""
        if v:
            return [court.strip().lower() for court in v if court.strip()]
        return v

    @field_validator('force_reprocess')
    @classmethod
    def validate_reprocess_flag(cls, v, info):
        """Ensure force_reprocess and skip_existing are consistent"""
        if v and info.data.get('skip_existing', True):
            raise ValueError("Cannot have both force_reprocess=True and skip_existing=True")
        return v


class RelevanceCriteria(BaseModel):
    """Criteria for determining case relevance"""
    # MDL-related criteria
    mdl_numbers: List[str] = Field(default_factory=list, description="MDL numbers to match")
    mdl_keywords: List[str] = Field(default_factory=list, description="MDL-related keywords")

    # Defendant criteria
    relevant_defendants: List[str] = Field(default_factory=list, description="List of relevant defendants")
    defendant_keywords: List[str] = Field(default_factory=list, description="Defendant-related keywords")

    # Case type criteria
    case_types: List[str] = Field(default_factory=list, description="Relevant case types")
    case_type_keywords: List[str] = Field(default_factory=list, description="Case type keywords")

    # Exclusion criteria
    exclude_keywords: List[str] = Field(default_factory=list, description="Keywords to exclude")
    exclude_case_types: List[str] = Field(default_factory=list, description="Case types to exclude")

    # Scoring weights
    mdl_weight: float = Field(0.4, ge=0.0, le=1.0, description="Weight for MDL matches")
    defendant_weight: float = Field(0.3, ge=0.0, le=1.0, description="Weight for defendant matches")
    case_type_weight: float = Field(0.3, ge=0.0, le=1.0, description="Weight for case type matches")

    @field_validator('mdl_weight', 'defendant_weight', 'case_type_weight')
    @classmethod
    def validate_weights(cls, v, info):
        """Ensure weights sum to 1.0"""
        weights = [
            info.data.get('mdl_weight', 0.4),
            info.data.get('defendant_weight', 0.3),
            info.data.get('case_type_weight', 0.3)
        ]
        if v in weights:
            weights[weights.index(v)] = v
        if abs(sum(weights) - 1.0) > 0.001:
            raise ValueError(f"Weights must sum to 1.0, got {sum(weights)}")
        return v


class DownloadCriteria(BaseModel):
    """Criteria for determining which files to download"""
    # File type filters
    download_file_types: List[str] = Field(
        default_factory=lambda: ['.pdf', '.doc', '.docx'],
        description="File types to download"
    )

    # Size limits
    max_file_size_mb: int = Field(50, ge=1, le=500, description="Maximum file size in MB")
    min_file_size_kb: int = Field(1, ge=0, le=1000, description="Minimum file size in KB")

    # Document type filters
    include_document_types: List[str] = Field(
        default_factory=list,
        description="Document types to include (empty = all)"
    )
    exclude_document_types: List[str] = Field(
        default_factory=lambda: ['Notice of Electronic Filing', 'Certificate of Service'],
        description="Document types to exclude"
    )

    # Ignore patterns
    ignore_patterns: List[str] = Field(
        default_factory=list,
        description="Regex patterns for files to ignore"
    )

    # Date filters
    download_after_date: Optional[datetime] = Field(None, description="Only download files after this date")
    download_before_date: Optional[datetime] = Field(None, description="Only download files before this date")
