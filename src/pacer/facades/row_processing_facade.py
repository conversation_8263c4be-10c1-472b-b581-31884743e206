"""
Row processing facade for PACER operations.
Handles row-by-row processing of case data with proper JSON structure.
"""

import re
from datetime import datetime
from typing import Any, Dict, List, Optional
from playwright.async_api import Page, Locator
import asyncio

from src.infrastructure.patterns.component_base import AsyncServiceBase


class RowProcessingFacade(AsyncServiceBase):
    """
    Facade for processing table rows and extracting structured case data.
    """

    async def execute(self, data: Any) -> Any:
        """Main execution method for the facade."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        
        if action == 'extract_cases_from_report':
            return await self._extract_cases_from_report(
                data['page'], 
                data['court_id']
            )
        elif action == 'process_extracted_case':
            return await self._process_extracted_case(**data)
        elif action == 'parse_case_data_from_cells':
            return self._parse_case_data_from_cells(
                data['raw_cells'], 
                data['court_id'], 
                data['row_index']
            )
        elif action == 'process_report_rows':
            return await self._process_report_rows(**data)
        else:
            raise ValueError(f"Unknown action for RowProcessingFacade: {action}")

    def _parse_case_data_from_cells(self, raw_cells: List[Dict[str, Any]], court_id: str, row_index: int) -> Optional[Dict[str, Any]]:
        """
        Parse structured case data from raw table cells into proper JSON format.
        
        Args:
            raw_cells: List of cell data with text and links
            court_id: Court identifier
            row_index: Row index for debugging
            
        Returns:
            Properly structured case dictionary or None if parsing fails
        """
        try:
            if len(raw_cells) < 2:
                return None
            
            current_time = datetime.now().isoformat()
            
            # First cell contains case number and title (combined)
            first_cell = raw_cells[0]
            case_text = first_cell["text"]
            
            # Extract docket number (first part, exactly 13 characters if it matches pattern)
            docket_link = None
            docket_num = ""
            versus = case_text
            
            if first_cell["links"]:
                docket_link = first_cell["links"][0]["href"]
                link_text = first_cell["links"][0]["text"].strip()
                # Extract exactly 13 character docket number (N:YY-cv-NNNNN format)
                if len(link_text) >= 13 and ':' in link_text and '-cv-' in link_text:
                    docket_num = link_text[:13]  # Take exactly 13 characters
                    # Extract versus part (everything after docket number)
                    if len(case_text) > len(link_text):
                        versus = case_text[len(link_text):].strip()
                        # Remove any leading spaces or separators
                        versus = versus.lstrip(' -').strip()
            
            # Search for filing date in multiple cells with flexible patterns
            filing_date = ""
            import re
            
            # Try multiple patterns for date extraction
            date_patterns = [
                r"Case filed:\s*(\d{1,2}/\d{1,2}/\d{2,4})",
                r"Filed:\s*(\d{1,2}/\d{1,2}/\d{2,4})",
                r"Date Filed:\s*(\d{1,2}/\d{1,2}/\d{2,4})",
                r"Filing Date:\s*(\d{1,2}/\d{1,2}/\d{2,4})",
                # Standalone date pattern (MM/DD/YYYY or MM/DD/YY)
                r"^(\d{1,2}/\d{1,2}/\d{2,4})$"
            ]
            
            # Search through first few cells for filing date
            for cell_idx in range(min(4, len(raw_cells))):
                if filing_date:  # Already found
                    break
                    
                cell_text = raw_cells[cell_idx]["text"]
                
                # Try each pattern
                for pattern in date_patterns:
                    match = re.search(pattern, cell_text.strip(), re.IGNORECASE)
                    if match:
                        filing_date = match.group(1).strip()
                        # Convert MM/DD/YY to MM/DD/YYYY if needed
                        if filing_date and len(filing_date.split('/')[-1]) == 2:
                            parts = filing_date.split('/')
                            # Assume 20YY for years 00-30, 19YY for years 31-99
                            year = int(parts[2])
                            if year <= 30:
                                parts[2] = f"20{parts[2]}"
                            else:
                                parts[2] = f"19{parts[2]}"
                            filing_date = '/'.join(parts)
                        break
            
            # Fourth cell usually contains detailed case information
            cause = ""
            nos = ""
            defendants = []
            
            if len(raw_cells) > 3:
                details_text = raw_cells[3]["text"]
                
                # Parse cause information
                if "Cause:" in details_text:
                    cause_parts = details_text.split("Cause:")[1].split("\n")[0].strip()
                    cause = cause_parts
                
                # Parse NOS (Nature of Suit)
                if "NOS:" in details_text:
                    nos_parts = details_text.split("NOS:")[1].split("\n")[0].strip()
                    nos = nos_parts
                
                # Try to extract defendants from versus text with improved parsing
                defendant_text = self._extract_defendant_from_versus(versus)
                if defendant_text:
                    defendants = self._parse_defendants_properly(defendant_text)
            
            # Create properly structured case data (matching required format)
            case_data = {
                "court_id": court_id,
                "docket_num": docket_num,
                "versus": versus,
                "filing_date": filing_date,
                "docket_link": docket_link or "",
                "extracted_at": current_time,
                "source": "PACER Case Report",
                "defendants": defendants,
                "defendant": defendants,  # Both formats for compatibility
                "cause": cause,
                "nos": nos
            }
            
            return case_data
            
        except Exception as e:
            self.log_debug(f"Error parsing case data from cells at row {row_index}: {e}")
            return None
    
    def _extract_defendant_from_versus(self, versus: str) -> str:
        """Extract defendant text from versus field"""
        if not versus:
            return ""
        
        # Look for various patterns of "v." or "vs."
        vs_patterns = [' v. ', ' V. ', ' vs. ', ' Vs. ', ' v ', ' V ']
        
        for pattern in vs_patterns:
            if pattern in versus:
                parts = versus.split(pattern, 1)  # Split only on first occurrence
                if len(parts) > 1:
                    return parts[1].strip()
        
        return ""
    
    def _parse_defendants_properly(self, defendant_text: str) -> List[Dict[str, str]]:
        """Parse defendant text into proper defendant objects with improved logic"""
        if not defendant_text:
            return []
        
        defendants = []
        
        # Handle "et al" cases - remove et al suffix
        if " et al" in defendant_text.lower():
            defendant_text = re.sub(r'\s+et\s+al\.?.*$', '', defendant_text, flags=re.IGNORECASE).strip()
        
        # Check if this looks like a single entity (contains Inc., LLC, Corp., etc.)
        entity_indicators = ['Inc.', 'LLC', 'Corp.', 'Ltd.', 'Co.', 'Company', 'Corporation']
        has_entity_indicator = any(indicator in defendant_text for indicator in entity_indicators)
        
        if has_entity_indicator:
            # Treat as single defendant to avoid splitting "Avlon Industries, Inc."
            defendants.append({"name": defendant_text.strip()})
        else:
            # Split by comma for multiple defendants, but be careful with company names
            parts = [part.strip() for part in defendant_text.split(',')]
            
            # Rejoin parts that might be part of a company name
            processed_parts = []
            i = 0
            while i < len(parts):
                current_part = parts[i]
                
                # Check if next part looks like a company suffix
                if (i + 1 < len(parts) and 
                    parts[i + 1].strip().lower() in ['inc.', 'inc', 'llc', 'corp.', 'corp', 'ltd.', 'ltd', 'co.', 'co']):
                    # Combine with next part
                    combined = f"{current_part}, {parts[i + 1]}".strip()
                    processed_parts.append(combined)
                    i += 2  # Skip next part since we combined it
                else:
                    if current_part:  # Only add non-empty parts
                        processed_parts.append(current_part)
                    i += 1
            
            # Convert to defendant objects
            for name in processed_parts:
                if name.strip():
                    defendants.append({"name": name.strip()})
        
        return defendants

    async def _extract_cases_from_report(self, page: Page, court_id: str) -> List[Dict[str, Any]]:
        """
        Extract case data from the current page report using proper JSON structure.
        
        Args:
            page: Current Playwright page
            court_id: Court identifier
            
        Returns:
            List of structured case dictionaries
        """
        cases = []
        log_prefix = f"[{court_id.upper()}]"
        
        try:
            # Find the main table containing case data
            table_locator = page.locator("table")
            table_count = await table_locator.count()
            
            if table_count == 0:
                self.log_warning(f"{log_prefix} No tables found on the page")
                return cases
            
            self.log_debug(f"{log_prefix} Found {table_count} tables on the page")
            
            # Find the largest table (usually contains the data)
            main_table = None
            max_rows = 0
            
            for i in range(table_count):
                table = table_locator.nth(i)
                rows = table.locator("tr")
                row_count = await rows.count()
                
                if row_count > max_rows:
                    max_rows = row_count
                    main_table = table
            
            if main_table is None or max_rows <= 1:
                self.log_warning(f"{log_prefix} No data rows found in tables")
                return cases
            
            self.log_info(f"{log_prefix} Processing {max_rows - 1} case rows from report table")
            
            # Extract data from each row (skip header row)
            rows = main_table.locator("tr")
            
            for row_index in range(1, max_rows):  # Skip header row
                try:
                    row = rows.nth(row_index)
                    cells = row.locator("td, th")
                    cell_count = await cells.count()
                    
                    if cell_count < 2:  # Need at least case number and title
                        continue
                    
                    # Extract raw cell data for proper parsing
                    raw_cells = []
                    for cell_index in range(cell_count):
                        cell = cells.nth(cell_index)
                        cell_text = await cell.text_content()
                        
                        # Check for links within the cell
                        links = cell.locator("a")
                        link_count = await links.count()
                        
                        cell_links = []
                        for link_index in range(link_count):
                            link = links.nth(link_index)
                            link_text = await link.text_content()
                            link_href = await link.get_attribute("href")
                            
                            cell_links.append({
                                "text": link_text.strip() if link_text else "",
                                "href": link_href or ""
                            })
                        
                        raw_cells.append({
                            "text": cell_text.strip() if cell_text else "",
                            "links": cell_links
                        })
                    
                    # Parse structured case data from raw cells (use proper JSON format)
                    case_data = self._parse_case_data_from_cells(raw_cells, court_id, row_index)
                    
                    # Only add cases that have essential information
                    if case_data and case_data.get("docket_num") and case_data.get("docket_link"):
                        cases.append(case_data)
                        self.log_debug(f"{log_prefix} Extracted case: {case_data['docket_num']} - {case_data.get('versus', 'No title')}")
                    else:
                        self.log_debug(f"{log_prefix} Skipping row {row_index} - missing essential data")
                    
                except Exception as row_error:
                    self.log_debug(f"{log_prefix} Error extracting row {row_index}: {row_error}")
                    continue
            
            self.log_info(f"{log_prefix} Successfully extracted {len(cases)} cases from report")
            return cases
            
        except Exception as e:
            self.log_error(f"{log_prefix} Failed to extract cases from report: {e}", exc_info=True)
            return cases

    async def _process_extracted_case(self, **kwargs) -> Dict[str, Any]:
        """
        Process a case using pre-extracted case data instead of parsing table rows.
        
        Args:
            case_data: Pre-extracted case information
            court_id: Court identifier
            iso_date: ISO formatted date
            case_index: Index of the case
            processor_config: Processing configuration
            
        Returns:
            Processed case data dictionary
        """
        case_data = kwargs.get('case_data', {})
        court_id = kwargs.get('court_id', '')
        iso_date = kwargs.get('iso_date', '')
        case_index = kwargs.get('case_index', 0)
        processor_config = kwargs.get('processor_config', {})
        
        log_prefix = f"[{court_id.upper()}] Case {case_index + 1}"
        
        try:
            # Use the properly structured case data directly
            processed_case = {
                "case_index": case_index,
                "court_id": court_id,
                "iso_date": iso_date,
                **case_data  # Merge in the structured case data
            }
            
            self.log_info(f"{log_prefix} Processing case: {case_data.get('docket_num', 'Unknown')} - {case_data.get('versus', 'No title')}")
            
            return processed_case
            
        except Exception as e:
            self.log_error(f"{log_prefix} Error processing extracted case: {e}")
            return {
                "case_index": case_index,
                "court_id": court_id,
                "iso_date": iso_date,
                "error": str(e),
                **case_data
            }

    async def _process_report_rows(self, **kwargs) -> Dict[str, int]:
        """
        Process all rows from a PACER report by extracting cases and providing counts.
        
        This method provides compatibility with the workflow orchestrator's expectations
        while using the facade's existing extraction capabilities.
        
        Args:
            original_page: Current Playwright page
            court_id: Court identifier
            iso_date: ISO formatted date
            start_date_obj: Start date object
            end_date_obj: End date object
            context: Browser context
            processor_config: Processing configuration
            relevance_engine: Relevance service
            court_logger: Court logger
            
        Returns:
            Dictionary with processing counts
        """
        original_page = kwargs.get("original_page")
        court_id = kwargs.get("court_id", "")
        iso_date = kwargs.get("iso_date", "")
        start_date_obj = kwargs.get("start_date_obj")
        end_date_obj = kwargs.get("end_date_obj")
        context = kwargs.get("context")
        processor_config = kwargs.get("processor_config", {})
        relevance_engine = kwargs.get("relevance_engine")
        court_logger = kwargs.get("court_logger")
        
        log_prefix = f"[{court_id.upper()}]"
        logger_to_use = court_logger if court_logger else self.logger
        
        # Use court logger if available, otherwise fall back to component logger
        if court_logger:
            court_logger.info(f"{log_prefix} Processing report rows using RowProcessingFacade with court-specific logging")
        else:
            self.log_info(f"{log_prefix} Processing report rows using RowProcessingFacade")
        
        # Initialize counts similar to ReportProcessor
        row_counts = {
            "total_rows": 0,
            "jobs_created": 0,
            "success_downloaded": 0,
            "success_html_only": 0,
            "success_no_download": 0,
            "skipped_relevance": 0,
            "skipped_exists": 0,
            "failed": 0,
            "skipped_date_range": 0,
        }
        
        try:
            # Extract cases from the report using existing method
            cases = await self._extract_cases_from_report(original_page, court_id)
            row_counts["total_rows"] = len(cases)
            
            if court_logger:
                court_logger.info(f"{log_prefix} Extracted {len(cases)} cases from report")
            else:
                self.log_info(f"{log_prefix} Extracted {len(cases)} cases from report")
            
            # For now, we'll mark all extracted cases as successfully processed
            # In a full implementation, this would process each case through the job system
            for case_data in cases:
                if case_data and case_data.get("docket_num"):
                    row_counts["jobs_created"] += 1
                    row_counts["success_html_only"] += 1  # Mark as HTML-only success for now
                    
                    if court_logger:
                        court_logger.debug(f"{log_prefix} Processed case: {case_data['docket_num']}")
                    else:
                        self.log_debug(f"{log_prefix} Processed case: {case_data['docket_num']}")
                else:
                    row_counts["failed"] += 1
            
            if court_logger:
                court_logger.info(f"{log_prefix} Report row processing complete. Counts: {row_counts}")
            else:
                self.log_info(f"{log_prefix} Report row processing complete. Counts: {row_counts}")
            return row_counts
            
        except Exception as e:
            if court_logger:
                court_logger.error(f"{log_prefix} Error processing report rows: {e}", exc_info=True)
            else:
                self.log_error(f"{log_prefix} Error processing report rows: {e}", exc_info=True)
            row_counts["failed"] = row_counts["total_rows"] if row_counts["total_rows"] > 0 else 1
            return row_counts