"""
Docket Processing Orchestrator for PACER Core Services.

This service is the main workflow coordinator that orchestrates the complete
docket processing lifecycle using all other core services.
"""

import asyncio
from typing import Any, Dict, Optional, List
from playwright.async_api import Page, BrowserContext

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.services.case_processing_service import CaseProcessingService
from src.pacer.services.relevance_service import RelevanceService
from src.pacer.services.classification_service import ClassificationService
from src.pacer.services.verification_service import VerificationService
from src.pacer.services.download_service import DownloadService
from src.pacer.services.file_operations_service import FileOperationsService
from src.pacer.services.configuration_service import ConfigurationService
from src.pacer.services.browser_service import BrowserService


class DocketOrchestrator(AsyncServiceBase):
    """
    Main Docket Processing Orchestrator for PACER workflow coordination.
    
    This service coordinates the complete docket processing lifecycle:
    - Phase 1: HTML Content Processing (Case Processing Service)
    - Phase 2: Relevance & Classification (Relevance + Classification Services)  
    - Phase 3: Case Verification (Verification Service)
    - Phase 4: Download Workflow (Download Orchestration Service)
    - Phase 5: File Operations & S3 Upload (File Operations Service)
    
    Additional capabilities:
    - Court-level processing with automatic PACER docket discovery
    - Browser-based navigation and authentication via Browser Service
    - Date-range queries to discover available dockets in courts
    - Robust parsing of PACER search results
    - Comprehensive error handling and resource cleanup
    
    Follows the exact workflow from docket_processing.md.
    """

    def __init__(
        self,
        case_processing_service: Optional[CaseProcessingService] = None,
        relevance_service: Optional[RelevanceService] = None,
        classification_service: Optional[ClassificationService] = None,
        verification_service: Optional[VerificationService] = None,
        download_service: Optional[DownloadService] = None,
        file_operations_service: Optional[FileOperationsService] = None,
        configuration_service: Optional[ConfigurationService] = None,
        browser_service: Optional[BrowserService] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
    ):
        super().__init__(logger, config)
        self._case_processing = case_processing_service
        self._relevance = relevance_service
        self._classification = classification_service
        self._verification = verification_service
        self._download_service = download_service
        self._file_operations = file_operations_service
        self._configuration = configuration_service
        self._browser_service = browser_service
        
        # Initialize browser resource management semaphore for isolated context processing
        max_concurrent_contexts = config.get('max_concurrent_browser_contexts', 3) if config else 3
        self._browser_semaphore = asyncio.Semaphore(max_concurrent_contexts)
        self._isolated_contexts: Dict[str, BrowserContext] = {}
        
        self.log_info(f"DocketOrchestrator initialized with browser semaphore limit: {max_concurrent_contexts}")

    async def _initialize_service(self) -> None:
        """Initialize the orchestrator and setup dependencies."""
        if self._initialized:
            self.log_info("Docket Orchestrator already initialized, skipping")
            return
            
        self.log_info("Starting Docket Orchestrator initialization")
        await self._setup_dependencies()
        await self._validate_configuration()
        self.log_info("Docket Orchestrator initialized successfully")

    async def _setup_dependencies(self) -> None:
        """Setup orchestrator dependencies."""
        services = {
            "case_processing": self._case_processing,
            "relevance": self._relevance,
            "classification": self._classification,
            "verification": self._verification,
            "download_service": self._download_service,
            "file_operations": self._file_operations,
            "configuration": self._configuration,
            "browser_service": self._browser_service,
        }
        
        # Step 1: Inject dependencies between services before initialization
        await self._inject_service_dependencies(services)
        
        # Step 2: Initialize services in dependency order
        initialization_order = [
            "configuration",  # Core config service first
            "file_operations",  # Basic file operations
            "browser_service",  # Browser service
            "case_processing",  # Case processing
            "relevance",  # Relevance service (needs configuration)
            "classification",  # Classification service
            "verification",  # Verification service
            "download_service",  # Download service (may need others)
        ]
        
        # Initialize services in order
        for service_name in initialization_order:
            service = services.get(service_name)
            if service:
                try:
                    # Validate service has required attributes
                    service_type = type(service).__name__
                    self.log_debug(f"Checking service: {service_name} (type: {service_type})")
                    
                    # Check if service has _initialized attribute and is not initialized
                    if hasattr(service, '_initialized'):
                        if not service._initialized:
                            self.log_info(f"Initializing service: {service_name} (type: {service_type})")
                            if hasattr(service, 'initialize'):
                                await service.initialize()
                                # Verify it was actually initialized
                                if hasattr(service, '_initialized') and service._initialized:
                                    self.log_info(f"Service {service_name} initialized successfully")
                                else:
                                    self.log_warning(f"Service {service_name} initialize() completed but _initialized flag not set")
                            else:
                                self.log_error(f"Service {service_name} has _initialized but no initialize method")
                                raise AttributeError(f"Service {service_name} missing initialize method")
                        else:
                            self.log_debug(f"Service {service_name} already initialized, skipping")
                    else:
                        # Service doesn't have _initialized flag, try to initialize anyway
                        self.log_warning(f"Service {service_name} doesn't have _initialized flag, attempting to initialize")
                        if hasattr(service, 'initialize'):
                            await service.initialize()
                            self.log_info(f"Service {service_name} initialized successfully (no _initialized flag)")
                        else:
                            self.log_warning(f"Service {service_name} doesn't have initialize method - may not be an AsyncServiceBase")
                except Exception as e:
                    self.log_error(f"Failed to initialize service {service_name}: {e}", exc_info=True)
                    raise
            else:
                self.log_debug(f"Service {service_name} is None, skipping initialization")
        
        # Step 3: Initialize any remaining services not in the order list
        for name, service in services.items():
            if name not in initialization_order and service:
                if hasattr(service, '_initialized') and not service._initialized:
                    self.log_info(f"Initializing remaining service: {name}")
                    await service.initialize()

    async def _inject_service_dependencies(self, services: Dict[str, Any]) -> None:
        """Inject dependencies between services before initialization."""
        self.log_info("Injecting service dependencies")
        
        # Configuration service - usually standalone
        config_service = services.get("configuration")
        
        # Inject configuration service into services that need it
        services_needing_config = [
            "relevance", "classification", "verification", "download_service", "file_operations"
        ]
        
        for service_name in services_needing_config:
            service = services.get(service_name)
            if service and config_service:
                if hasattr(service, 'set_dependency'):
                    service.set_dependency('configuration_service', config_service)
                    self.log_debug(f"Injected configuration_service into {service_name}")
        
        # Inject file operations service into services that need it
        file_ops_service = services.get("file_operations")
        if file_ops_service:
            services_needing_file_ops = ["download_service"]
            for service_name in services_needing_file_ops:
                service = services.get(service_name)
                if service and hasattr(service, 'set_dependency'):
                    service.set_dependency('file_operations_service', file_ops_service)
                    self.log_debug(f"Injected file_operations_service into {service_name}")
        
        # Inject browser service into services that need it
        browser_service = services.get("browser_service")
        if browser_service:
            services_needing_browser = ["download_service", "case_processing"]
            for service_name in services_needing_browser:
                service = services.get(service_name)
                if service and hasattr(service, 'set_dependency'):
                    service.set_dependency('browser_service', browser_service)
                    self.log_debug(f"Injected browser_service into {service_name}")
        
        self.log_info("Service dependencies injection completed")

    async def _validate_configuration(self) -> None:
        """Validate orchestrator configuration."""
        # Core required services
        required_services = [
            ("case_processing", self._case_processing),
            ("relevance", self._relevance),
            ("verification", self._verification),
            ("download_service", self._download_service),
            ("file_operations", self._file_operations),
            ("browser_service", self._browser_service),
        ]
        
        # Optional services
        optional_services = [
            ("classification", self._classification),
        ]
        
        missing = [name for name, service in required_services if not service]
        if missing:
            raise ValueError(f"Missing required services for DocketOrchestrator: {missing}")
            
        # Log optional services status
        for name, service in optional_services:
            if service:
                self.log_info(f"Optional service '{name}' is available")
            else:
                self.log_info(f"Optional service '{name}' is not available, skipping related functionality")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate orchestration methods."""
        action = data.get("action")
        
        if action == "process_docket":
            return await self.process_docket_comprehensive(
                page=data.get("page"),
                initial_details=data.get("initial_details", {})
            )
        elif action == "process_docket_case":
            return await self.process_docket_case(
                page=data.get("page"),
                initial_details=data.get("initial_details", {})
            )
        elif action == "process_court":
            return await self.process_court(data)
        elif action == "process_court_isolated":
            return await self._process_court_with_isolated_context(
                court_id=data.get("court_id"),
                processing_mode=data.get("processing_mode", "date_range"),
                workflow_config=data.get("workflow_config", {}),
                processor_config=data.get("processor_config", {})
            )
        else:
            raise ValueError(f"Unknown action for DocketOrchestrator: {action}")

    async def process_docket_comprehensive(
        self, page: Page, initial_details: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Execute the complete docket processing workflow.
        
        This is the main orchestration method that follows the exact phases
        from docket_processing.md Section 4.
        """
        court_id = initial_details.get("court_id", "N/A")
        docket_num = initial_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Starting comprehensive docket processing")
        
        try:
            # PHASE 1: HTML Content Processing
            case_details = await self._execute_phase_1_html_processing(page, initial_details)
            if not case_details:
                self.log_error(f"[{court_id}][{docket_num}] Phase 1 (HTML Processing) failed")
                return None
            
            # PHASE 2: Relevance & Classification
            case_details = await self._execute_phase_2_relevance_classification(case_details)
            
            # Check if case should be skipped after relevance filters
            if case_details.get("_skip_processing", False):
                self.log_info(f"[{court_id}][{docket_num}] Case skipped after relevance filters")
                # Still save metadata
                return await self._save_metadata_only(case_details)
            
            # PHASE 3: Case Verification  
            should_process = await self._execute_phase_3_verification(case_details)
            if not should_process:
                self.log_info(f"[{court_id}][{docket_num}] Case skipped after verification")
                # Save metadata only
                return await self._save_metadata_only(case_details)
            
            # PHASE 4: Download Workflow
            case_details = await self._execute_phase_4_download_workflow(page, case_details)
            
            # PHASE 5: File Operations & S3 Upload
            result = await self._execute_phase_5_save_and_upload(case_details)
            
            self.log_info(f"[{court_id}][{docket_num}] Comprehensive docket processing completed successfully")
            return result
            
        except Exception as e:
            self.log_error(f"[{court_id}][{docket_num}] Comprehensive docket processing failed: {e}")
            return None

    async def _execute_phase_1_html_processing(
        self, page: Page, initial_details: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Execute Phase 1: HTML Content Processing."""
        court_id = initial_details.get("court_id", "N/A")
        docket_num = initial_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 1: HTML Content Processing")
        
        # Add null check before accessing page.content()
        if page is None:
            self.log_error(f"[{court_id}][{docket_num}] Page object is None - cannot retrieve HTML content")
            return None
        
        try:
            # Get HTML content with proper error handling
            html_content = await page.content()
            
            if not html_content:
                self.log_error(f"[{court_id}][{docket_num}] Retrieved empty HTML content from page")
                return None
        except AttributeError as e:
            self.log_error(f"[{court_id}][{docket_num}] AttributeError accessing page.content() - page may be None: {e}")
            return None
        except Exception as e:
            self.log_error(f"[{court_id}][{docket_num}] Unexpected error retrieving HTML content: {e}")
            return None
        
        # Process case HTML
        case_details = await self._case_processing.perform_action({
            "action": "process_case",
            "page": page,
            "initial_details": initial_details,
            "html_content": html_content
        })
        
        if not case_details:
            self.log_error(f"[{court_id}][{docket_num}] HTML processing returned no case details")
            return None
        
        # Add processing metadata
        case_details.update({
            "_processing_phase": "html_completed",
            "_html_content": html_content,  # Store for later phases
            "base_filename": case_details.get("base_filename") or f"{court_id}_{docket_num}"
        })
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 1 completed successfully")
        return case_details
    async def _execute_phase_2_relevance_classification(
        self, case_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute Phase 2: Relevance & Classification."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 2: Relevance & Classification")
        
        # Step 1: Determine case relevance
        relevance_result = await self._relevance.perform_action({
            "action": "determine_case_relevance",
            "case_details": case_details
        })
        
        case_details["is_relevant"] = relevance_result.get('is_relevant', False)
        case_details["relevance_reason"] = relevance_result.get('relevance_reason', 'unknown')
        
        # Step 2: Classify case (regardless of relevance for metadata)
        html_content = case_details.get("_html_content", "")
        classification_result = await self._classification.perform_action({
            "action": "classify_case",
            "case_details": case_details,
            "html_content": html_content
        })
        
        case_details.update(classification_result)
        
        # Step 3: Apply relevance-based processing decisions
        if not case_details["is_relevant"]:
            case_details["_skip_processing"] = True
            case_details["_skip_reason"] = "Not relevant"
        
        case_details["_processing_phase"] = "relevance_classification_completed"
        
        self.log_info(f"[{court_id}][{docket_num}] "
                     f"Phase 2 completed: relevant={case_details['is_relevant']}")
        return case_details

    async def _execute_phase_3_verification(
        self, case_details: Dict[str, Any]
    ) -> bool:
        """Execute Phase 3: Case Verification."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 3: Case Verification")
        
        # Check if explicitly requested
        is_explicitly_requested = case_details.get("_is_explicitly_requested", False)
        
        # Verify case should be processed
        verification_result = await self._verification.perform_action({
            "action": "verify_case",
            "case_details": case_details,
            "is_explicitly_requested": is_explicitly_requested
        })
        
        # Handle both bool and dict return types from verification service
        if isinstance(verification_result, bool):
            should_process = verification_result
        else:
            should_process = verification_result.get('should_process', True)
        case_details["_verification_result"] = verification_result
        case_details["_processing_phase"] = "verification_completed"
        
        self.log_info(f"[{court_id}][{docket_num}] "
                     f"Phase 3 completed: should_process={should_process}")
        return should_process

    async def _execute_phase_4_download_workflow(
        self, page: Page, case_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute Phase 4: Download Workflow."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 4: Download Workflow")
        
        # Execute download orchestration
        download_result = await self._download_service.perform_action({
            "action": "process_download_workflow",
            "case_details": case_details,
            "page": page,
            "is_explicitly_requested": case_details.get("_is_explicitly_requested", False)
        })
        
        case_details.update(download_result)
        case_details["_processing_phase"] = "download_completed"
        
        download_status = case_details.get("processing_status", "unknown")
        self.log_info(f"[{court_id}][{docket_num}] "
                     f"Phase 4 completed: status={download_status}")
        return case_details

    async def _execute_phase_5_save_and_upload(
        self, case_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute Phase 5: File Operations & S3 Upload."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 5: File Operations & S3 Upload")
        
        # Get ISO date from config or case details
        iso_date = case_details.get("iso_date") or self.config.get("iso_date")
        if not iso_date:
            raise ValueError("ISO date not available for file operations")
        
        # Clean up temporary fields before saving
        case_details = await self._cleanup_temporary_fields(case_details)
        
        # Save case data
        local_path = await self._file_operations.save_case_data(case_details, iso_date)
        
        # Try to upload to S3 if configured
        s3_url = ""
        try:
            s3_key = f"{iso_date}/{court_id}/{case_details.get('base_filename', 'unknown')}.json"
            s3_url = await self._file_operations.upload_to_s3(local_path, s3_key)
        except Exception as e:
            self.log_warning(f"S3 upload failed: {str(e)}")
        
        # Merge result with case details
        case_details.update({
            "_processing_phase": "completed",
            "_local_path": local_path,
            "_s3_url": s3_url,
            "_processing_status": "success"
        })
        
        self.log_info(f"[{court_id}][{docket_num}] Phase 5 completed successfully")
        return case_details

    async def _save_metadata_only(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Save case metadata without full processing."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Saving metadata only (skipped processing)")
        
        # Clean up temporary fields
        case_details = await self._cleanup_temporary_fields(case_details)
        case_details["_processing_status"] = "metadata_only"
        
        # Get ISO date
        iso_date = case_details.get("iso_date") or self.config.get("iso_date")
        if not iso_date:
            raise ValueError("ISO date not available for file operations")
        
        # Save metadata
        local_path = await self._file_operations.save_case_data(case_details, iso_date)
        
        case_details.update({
            "_local_path": local_path,
            "_s3_url": ""
        })
        
        return case_details

    async def _cleanup_temporary_fields(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Remove temporary processing fields before final save."""
        cleaned_data = case_details.copy()
        
        # Remove HTML content (too large for storage)
        cleaned_data.pop("_html_content", None)
        
        # Remove other temporary fields but keep important processing metadata
        fields_to_remove = [
            field for field in cleaned_data.keys() 
            if field.startswith('_') and field not in [
                '_processing_phase', '_processing_status', '_local_path', '_s3_url',
                '_verification_result', '_is_explicitly_requested'
            ]
        ]
        
        for field in fields_to_remove:
            cleaned_data.pop(field, None)
        
        return cleaned_data

    # Interface implementation methods
    async def process_docket(self, docket_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process docket using new core services workflow."""
        page = docket_data.get("page")
        initial_details = docket_data.get("initial_details", {})
        
        if not page:
            raise ValueError("Page object required for docket processing")
        
        return await self.process_docket_comprehensive(page, initial_details)

    async def process_docket_case(
        self, page: Page, initial_details: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Alias for comprehensive processing - maintains compatibility."""
        return await self.process_docket_comprehensive(page, initial_details)

    async def orchestrate_job(self, job_data: Dict[str, Any]) -> str:
        """Orchestrate a processing job."""
        court_id = job_data.get("court_id", "N/A")
        self.log_info(f"Orchestrating job for court: {court_id}")
        
        # This would integrate with the job processing system
        # For now, return a job ID
        import uuid
        job_id = str(uuid.uuid4())
        
        self.log_info(f"Job orchestrated with ID: {job_id}")
        return job_id

    async def execute_bulk_processing(self, jobs: list[Dict[str, Any]]) -> list[Dict[str, Any]]:
        """Execute bulk processing of multiple jobs."""
        self.log_info(f"Executing bulk processing for {len(jobs)} jobs")
        
        results = []
        for job in jobs:
            try:
                result = await self.process_docket(job)
                if result:
                    results.append(result)
            except Exception as e:
                self.log_error(f"Bulk processing job failed: {e}")
                # Continue with other jobs
        
        self.log_info(f"Bulk processing completed: {len(results)}/{len(jobs)} successful")
        return results

    async def handle_legacy_workflows(self, workflow_type: str, data: Dict[str, Any]) -> Any:
        """Handle legacy workflow patterns."""
        self.log_info(f"Handling legacy workflow: {workflow_type}")
        
        if workflow_type == "single_docket":
            # Route to comprehensive processing
            return await self.process_docket(data)
        elif workflow_type == "bulk_docket":
            # Route to bulk processing  
            return await self.execute_bulk_processing(data.get("jobs", []))
        else:
            raise ValueError(f"Unsupported legacy workflow type: {workflow_type}")

    async def get_processing_status(self, job_id: str) -> Dict[str, Any]:
        """Get status of a processing job."""
        # Placeholder for job status tracking
        return {
            "job_id": job_id,
            "status": "completed",
            "message": "Job processing status tracking not yet implemented"
        }

    async def process_courts(self,
                           court_ids: List[str],
                           context: Any = None,
                           iso_date: Optional[str] = None,
                           start_date: Any = None,
                           end_date: Any = None,
                           **kwargs) -> Dict[str, Any]:
        """
        Process multiple courts for report scraping.
        
        Args:
            court_ids: List of court IDs to process
            context: Browser context (optional)
            iso_date: ISO date string
            start_date: Start date for processing
            end_date: End date for processing
            **kwargs: Additional arguments
            
        Returns:
            Aggregated processing results for all courts
        """
        self.log_info(f"Processing {len(court_ids)} courts: {court_ids}")
        
        results = {}
        total_processed = 0
        total_failed = 0
        
        for court_id in court_ids:
            try:
                # Prepare request data for this court
                request_data = {
                    "court_id": court_id,
                    "browser_context": context,
                    "iso_date": iso_date,
                    "start_date": start_date,
                    "end_date": end_date,
                    "config": self.config,
                    **kwargs
                }
                
                # Process individual court
                result = await self.process_court(request_data)
                results[court_id] = result
                
                # Aggregate statistics
                if result.get("status") == "success":
                    total_processed += result.get("processed_dockets", 0)
                    total_failed += result.get("failed_dockets", 0)
                    
                self.log_info(f"Court {court_id} processed: {result.get('processed_dockets', 0)} dockets")
                
            except Exception as e:
                self.log_error(f"Error processing court {court_id}: {e}")
                results[court_id] = {
                    "status": "error",
                    "error": str(e),
                    "court_id": court_id
                }
        
        self.log_info(f"Multi-court processing complete: {total_processed} total dockets processed, {total_failed} failed across {len(court_ids)} courts")
        
        return {
            "status": "success",
            "courts_processed": len(court_ids),
            "total_dockets_processed": total_processed,
            "total_dockets_failed": total_failed,
            "court_results": results
        }

    async def process_court(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a specific court by loading existing dockets or generating new civil cases report.
        
        This method implements the EXACT court processing logic from the working branch:
        1. Check for existing docket_report_log files
        2. If exists: Load cases from the log and process them
        3. If missing: Generate civil cases report first, then process
        4. Process each docket through the comprehensive docket processing pipeline
        5. Return aggregated results with proper status tracking
        """
        court_id = request_data.get("court_id", "unknown")
        config = request_data.get("config", {})
        browser_context = request_data.get("browser_context")
        iso_date = request_data.get("iso_date")
        start_date = request_data.get("start_date") 
        end_date = request_data.get("end_date")
        docket_list_input = request_data.get("docket_list_input")
        
        self.log_info(f"Processing court: {court_id} for date range {start_date} to {end_date}")
        
        if not all([court_id, start_date, end_date]):
            return {
                "status": "failed",
                "court_id": court_id,
                "error": "Missing required parameters: court_id, start_date, end_date",
                "processed_dockets": 0
            }
        
        try:
            # Step 1: Setup directories if file operations available
            if self._file_operations and hasattr(self._file_operations, 'setup_directories') and iso_date:
                try:
                    await self._file_operations.setup_directories(iso_date)
                    self.log_info(f"Setup directories for court {court_id} processing")
                except Exception as e:
                    self.log_warning(f"Could not setup directories: {e}")
            
            # Step 2: Determine if we have existing docket reports or need to generate them
            cases_to_process = []
            
            # Check for special docket list input first (highest priority)
            if docket_list_input:
                self.log_info(f"Using provided docket list for court {court_id}: {len(docket_list_input)} dockets")
                # Convert docket list input to cases format
                for docket_item in docket_list_input:
                    if docket_item.get("court_id") == court_id:  # Only process matching court
                        case = {
                            "docket_num": docket_item.get("docket_num"),
                            "court_id": court_id,
                            "case_title": docket_item.get("case_title", ""),
                            "filed_date": docket_item.get("filed_date", ""),
                            **docket_item  # Include all fields from docket item
                        }
                        cases_to_process.append(case)
            else:
                # Check for existing docket report log using parent orchestrator's method
                # We need to access the parent orchestrator that has the load_docket_report_log method
                existing_cases = None
                if hasattr(self, '_parent_orchestrator') and hasattr(self._parent_orchestrator, 'load_docket_report_log'):
                    existing_cases = await self._parent_orchestrator.load_docket_report_log(iso_date, court_id)
                elif iso_date:
                    # Try direct method call if we have the functionality
                    existing_cases = await self._load_docket_report_log_direct(iso_date, court_id)
                
                if existing_cases:
                    # Use existing docket report log
                    self.log_info(f"Found existing docket report log for court {court_id}: {len(existing_cases)} cases")
                    cases_to_process = existing_cases
                else:
                    # Civil cases report should already be generated by orchestrator service
                    # This is a fallback that should rarely be needed
                    self.log_warning(f"No existing docket report found for court {court_id} - this should have been generated by orchestrator service")
                    
                    # Try one more time to load the report in case it was just created
                    existing_cases_retry = await self._load_docket_report_log_retry(court_id, iso_date)
                    
                    if existing_cases_retry:
                        cases_to_process = existing_cases_retry
                        self.log_info(f"Found docket report on retry for court {court_id}: {len(cases_to_process)} cases")
                    else:
                        self.log_error(f"Still no docket report found for court {court_id} after orchestrator should have generated it")
                        return {
                            "status": "failed",
                            "court_id": court_id,
                            "error": f"Docket report missing - orchestrator service should have generated it",
                            "processed_dockets": 0,
                            "source": "missing_report"
                        }
            
            # Step 3: Process each case through the comprehensive docket processing pipeline
            if not cases_to_process:
                self.log_info(f"No cases to process for court {court_id}")
                return {
                    "status": "success_no_cases",
                    "court_id": court_id,
                    "processed_dockets": 0,
                    "cases_found": 0,
                    "message": f"No cases found for processing in court {court_id}",
                    "source": "no_cases"
                }
            
            # Get or create browser context for processing
            context_id = f"court_{court_id}_processing"
            page_id = f"court_{court_id}_docket_page"
            page = None
            
            if not browser_context and self._browser_service:
                try:
                    browser_context = await self._browser_service.create_context({
                        "context_id": context_id,
                        "ignore_https_errors": True
                    })
                    self.log_info(f"Created browser context for court {court_id} processing")
                except Exception as e:
                    self.log_error(f"Failed to create browser context: {e}")
                    browser_context = None
            
            if browser_context and self._browser_service:
                try:
                    await self._browser_service.create_page({
                        "context_id": browser_context,
                        "page_id": page_id
                    })
                    page = await self._browser_service.get_page({"page_id": page_id})
                except Exception as e:
                    self.log_warning(f"Could not create page for docket processing: {e}")
            
            # Process each case
            processed_count = 0
            failed_count = 0
            processed_dockets = []
            
            for case in cases_to_process:
                try:
                    # Prepare initial details for comprehensive docket processing
                    initial_details = {
                        "court_id": court_id,
                        "docket_num": case.get("docket_num"),
                        "case_title": case.get("case_title", ""),
                        "filed_date": case.get("filed_date", ""),
                        "iso_date": iso_date,
                        "_is_explicitly_requested": True,
                        **case  # Include all case fields
                    }
                    
                    # Process the docket using comprehensive processing
                    if page:
                        # Navigate to docket if we have a page
                        try:
                            docket_url = f"https://ecf.{court_id}.uscourts.gov/cgi-bin/DktRpt.pl?{case.get('docket_num', '')}"
                            await self._browser_service.navigate({
                                "page_id": page_id,
                                "url": docket_url,
                                "wait_until": "networkidle"
                            })
                        except Exception as e:
                            self.log_warning(f"Could not navigate to docket {case.get('docket_num')}: {e}")
                    
                    # Process through the comprehensive workflow
                    result = await self.process_docket_comprehensive(page, initial_details)
                    
                    if result:
                        processed_dockets.append(result)
                        processed_count += 1
                        self.log_info(f"Successfully processed docket {case.get('docket_num')}")
                    else:
                        failed_count += 1
                        self.log_warning(f"Failed to process docket {case.get('docket_num')}")
                        
                except Exception as e:
                    failed_count += 1
                    self.log_error(f"Error processing docket {case.get('docket_num', 'unknown')}: {e}")
            
            # Step 4: Clean up browser resources
            if self._browser_service:
                try:
                    if page:
                        await self._browser_service.close_page({"page_id": page_id})
                    if browser_context and not request_data.get("browser_context"):  # Only close if we created it
                        await self._browser_service.close_context({"context_id": browser_context})
                except Exception as e:
                    self.log_warning(f"Error cleaning up browser resources: {e}")
            
            # Step 5: Return aggregated results
            self.log_info(f"Court {court_id} processing completed: {processed_count} processed, {failed_count} failed from {len(cases_to_process)} total cases")
            
            return {
                "status": "success" if processed_count > 0 else "success_no_cases",
                "court_id": court_id,
                "processed_dockets": processed_count,
                "failed_dockets": failed_count,
                "cases_loaded": len(cases_to_process),
                "cases_found": len(cases_to_process),
                "docket_results": processed_dockets,
                "message": f"Court processing completed for {court_id}: {processed_count}/{len(cases_to_process)} successful",
                "source": "docket_report_log" if not docket_list_input else "docket_list_input"
            }
            
        except Exception as e:
            self.log_error(f"Error processing court {court_id}: {str(e)}", exc_info=True)
            
            # Clean up resources on error
            try:
                if self._browser_service and 'page_id' in locals():
                    await self._browser_service.close_page({"page_id": page_id})
                if self._browser_service and 'context_id' in locals() and not request_data.get("browser_context"):
                    await self._browser_service.close_context({"context_id": context_id})
            except:
                pass  # Ignore cleanup errors
            
            return {
                "status": "failed",
                "court_id": court_id,
                "error": str(e),
                "processed_dockets": 0,
                "source": "processing_error"
            }
    
    async def _discover_court_dockets(
        self, page: Page, court_id: str, start_date: str, end_date: str
    ) -> List[Dict[str, Any]]:
        """
        Discover available dockets for a court within the specified date range.
        
        This method performs the actual PACER query to find dockets.
        """
        self.log_info(f"Discovering dockets for court {court_id} from {start_date} to {end_date}")
        
        discovered_dockets = []
        
        try:
            # STEP 1: First, authenticate with PACER BEFORE navigating to civil reports
            self.log_info(f"Authenticating with PACER for court {court_id}")
            
            # Always perform PACER authentication first to establish session
            if self._browser_service and hasattr(self._browser_service, 'authenticate_pacer'):
                try:
                    # Get the context_id from the page
                    context_id = None
                    for cid, ctx in self._browser_service._contexts.items():
                        if page.context == ctx:
                            context_id = cid
                            break
                    
                    if context_id:
                        self.log_info(f"Performing PACER authentication for context {context_id}")
                        auth_page = await self._browser_service.authenticate_pacer({'context_id': context_id})
                        if auth_page:
                            self.log_info(f"Successfully authenticated with PACER for court {court_id}")
                            page = auth_page  # Use the authenticated page
                        else:
                            self.log_error(f"PACER authentication failed for court {court_id}")
                            return discovered_dockets
                    else:
                        self.log_error(f"Could not find context ID for authentication for court {court_id}")
                        return discovered_dockets
                except Exception as e:
                    self.log_error(f"PACER authentication error for court {court_id}: {e}")
                    return discovered_dockets
            else:
                self.log_error(f"Browser service authentication not available for court {court_id}")
                return discovered_dockets
            
            # STEP 2: Navigate directly to the civil cases filed report page
            # Use the correct URL pattern for civil cases filed reports
            # Georgia courts have a different URL pattern
            if court_id.startswith('ga'):  # Georgia courts: gand, gamd, gasd
                civil_reports_url = f"https://ecf.{court_id}.uscourts.gov/cgi-bin/{court_id.upper()}c_CaseFiled-Rpt.pl"
            else:
                civil_reports_url = f"https://ecf.{court_id}.uscourts.gov/cgi-bin/CaseFiled-Rpt.pl"
            
            self.log_info(f"Navigating directly to civil cases filed reports page: {civil_reports_url}")
            try:
                response = await page.goto(civil_reports_url, wait_until="networkidle", timeout=30000)
                if not response or not response.ok:
                    self.log_warning(f"Navigation to civil reports failed for court {court_id}, status: {response.status if response else 'No response'}")
                    # Try alternative URLs as fallback
                    alternative_urls = [
                        f"https://ecf.{court_id}.uscourts.gov/cgi-bin/rss_outside.pl",
                        f"https://ecf.{court_id}.uscourts.gov/cgi-bin/NewCasesRpt.pl",
                        f"https://ecf.{court_id}.uscourts.gov/cgi-bin/CivCasesSummary.pl"
                    ]
                    
                    for alt_url in alternative_urls:
                        try:
                            self.log_info(f"Trying alternative civil reports URL: {alt_url}")
                            response = await page.goto(alt_url, wait_until="networkidle", timeout=30000)
                            if response and response.ok:
                                self.log_info(f"Successfully reached alternative reports page at: {alt_url}")
                                break
                        except:
                            continue
                    else:
                        self.log_error(f"All civil reports URLs failed for court {court_id}")
                        return discovered_dockets
                        
            except Exception as e:
                self.log_error(f"Error navigating to civil reports for court {court_id}: {e}")
                return discovered_dockets
            
            # Check if we're on the correct civil cases reports page
            try:
                page_title = await page.title()
                
                # Add null check and error handling for page.content()
                if page is None:
                    self.log_error(f"Page object is None during content check for court {court_id}")
                    return discovered_dockets
                
                try:
                    page_content = await page.content()
                    if not page_content:
                        self.log_warning(f"Retrieved empty page content for court {court_id}")
                        page_content = ""  # Set to empty string to continue processing
                except AttributeError as e:
                    self.log_error(f"AttributeError accessing page.content() for court {court_id} - page may be None: {e}")
                    return discovered_dockets
                except Exception as e:
                    self.log_error(f"Unexpected error retrieving page content for court {court_id}: {e}")
                    return discovered_dockets
                    
            except Exception as title_e:
                self.log_error(f"Error getting page title for court {court_id}: {title_e}")
                return discovered_dockets
            
            # If still on login page, authentication failed
            if "login" in page_title.lower():
                self.log_error(f"Still on login page after authentication attempt for court {court_id}")
                return discovered_dockets
            
            if "civil" not in page_title.lower() and "civil" not in page_content.lower():
                # Try alternative civil reports URL
                alternative_url = f"https://ecf.{court_id}.uscourts.gov/cgi-bin/iquery.pl"
                self.log_info(f"Trying alternative civil reports URL: {alternative_url}")
                await page.goto(alternative_url, wait_until="networkidle")
                page_title = await page.title()
                
                # Check again if still on login page
                if "login" in page_title.lower():
                    self.log_error(f"Alternative URL also requires login for court {court_id}")
                    return discovered_dockets
            
            self.log_info(f"On civil reports page for court {court_id}: {page_title}")
            
            # Enhanced debugging - capture current URL and page content info  
            current_url = page.url
            self.log_info(f"Current URL for court {court_id}: {current_url}")
            
            # Debug: Get all form elements on the page
            try:
                all_forms = await page.query_selector_all('form')
                self.log_info(f"Found {len(all_forms)} forms on page for court {court_id}")
                
                all_inputs = await page.query_selector_all('input')
                all_selects = await page.query_selector_all('select')
                all_buttons = await page.query_selector_all('button')
                
                self.log_info(f"Found {len(all_inputs)} inputs, {len(all_selects)} selects, {len(all_buttons)} buttons for court {court_id}")
                
                # Get form field details
                form_details = []
                for inp in all_inputs[:10]:  # First 10 inputs only
                    name = await inp.get_attribute('name') or 'no-name'
                    input_type = await inp.get_attribute('type') or 'text'
                    value = await inp.get_attribute('value') or ''
                    form_details.append(f"{name}({input_type})='{value}'")
                
                self.log_info(f"Sample form inputs for court {court_id}: {', '.join(form_details)}")
                
            except Exception as e:
                self.log_warning(f"Error debugging page structure for court {court_id}: {e}")
            
            # Look for civil cases report form elements
            # Try to find date range input fields for civil cases filed reports
            date_fields_found = False
            
            # Try different common selectors for date fields
            start_date_selectors = [
                'input[name="date_from"]',
                'input[name="start_date"]',
                'input[name="from_date"]',
                'input[id*="date_from"]',
                'input[id*="start"]'
            ]
            
            end_date_selectors = [
                'input[name="date_to"]',
                'input[name="end_date"]', 
                'input[name="to_date"]',
                'input[id*="date_to"]',
                'input[id*="end"]'
            ]
            
            # Try to fill date fields
            for start_selector in start_date_selectors:
                try:
                    start_element = await page.wait_for_selector(start_selector, timeout=2000)
                    if start_element:
                        await start_element.fill(start_date)
                        date_fields_found = True
                        self.log_info(f"Found and filled start date field: {start_selector}")
                        break
                except:
                    continue
            
            for end_selector in end_date_selectors:
                try:
                    end_element = await page.wait_for_selector(end_selector, timeout=2000)
                    if end_element:
                        await end_element.fill(end_date)
                        self.log_info(f"Found and filled end date field: {end_selector}")
                        break
                except:
                    continue
            
            if not date_fields_found:
                self.log_warning(f"Could not find date fields on civil cases reports page for court {court_id}")
                # Try to dump available form fields for debugging
                try:
                    form_inputs = await page.query_selector_all('input')
                    input_names = []
                    for inp in form_inputs:
                        name = await inp.get_attribute('name')
                        if name:
                            input_names.append(name)
                    self.log_info(f"Available form inputs for court {court_id}: {input_names}")
                except:
                    pass
                # Try to submit anyway or look for alternative query methods
            
            # Look for and click submit button (civil reports typically have "Run Report" or similar)
            submit_selectors = [
                'input[type="submit"]',
                'button[type="submit"]',
                'input[value*="Submit"]',
                'input[value*="Search"]',
                'input[value*="Run"]',        # Common in civil reports
                'input[value*="Report"]',     # "Run Report" buttons  
                'input[value*="Generate"]',   # "Generate Report" buttons
                'button:has-text("Submit")',
                'button:has-text("Search")',
                'button:has-text("Run")',
                'button:has-text("Report")'
            ]
            
            submit_clicked = False
            for submit_selector in submit_selectors:
                try:
                    submit_element = await page.wait_for_selector(submit_selector, timeout=2000)
                    if submit_element:
                        await submit_element.click()
                        submit_clicked = True
                        self.log_info(f"Clicked submit button: {submit_selector}")
                        break
                except:
                    continue
            
            if not submit_clicked:
                self.log_warning(f"Could not find submit button for court {court_id}")
                return discovered_dockets
            
            # Wait for results to load
            await page.wait_for_load_state("networkidle")
            
            # Parse docket results from the page
            discovered_dockets = await self._parse_docket_results(page, court_id)
            
        except Exception as e:
            self.log_error(f"Error during docket discovery for court {court_id}: {e}")
            
        return discovered_dockets
    
    async def _parse_docket_results(self, page: Page, court_id: str) -> List[Dict[str, Any]]:
        """
        Parse docket results from the PACER search results page.
        """
        dockets = []
        
        try:
            # Look for common table patterns in PACER results
            # Most PACER results are in tables with specific patterns
            
            # Try to find result tables
            table_selectors = [
                'table[summary*="case"]',
                'table[summary*="docket"]', 
                'table.results',
                'table[border="1"]',
                'table:has(tr:has(td:has(a[href*="DktRpt"])))'
            ]
            
            results_table = None
            for selector in table_selectors:
                try:
                    table = await page.wait_for_selector(selector, timeout=3000)
                    if table:
                        results_table = table
                        self.log_info(f"Found results table with selector: {selector}")
                        break
                except:
                    continue
            
            if not results_table:
                self.log_warning(f"Could not find results table for court {court_id}")
                # Try to extract from page text as fallback
                return await self._parse_docket_results_from_text(page, court_id)
            
            # Extract docket rows from the table
            rows = await page.query_selector_all(f'{table_selectors[0]} tr')
            
            for row in rows:
                try:
                    # Look for links that contain case/docket information
                    links = await row.query_selector_all('a[href*="DktRpt"]')
                    
                    if links:
                        for link in links:
                            # Extract docket number and case title
                            link_text = await link.inner_text()
                            href = await link.get_attribute('href')
                            
                            # Parse docket number from link text or href
                            docket_num = self._extract_docket_number(link_text, href)
                            
                            if docket_num:
                                # Get additional case details from the row
                                row_text = await row.inner_text()
                                case_title = self._extract_case_title(row_text, link_text)
                                filed_date = self._extract_filed_date(row_text)
                                
                                docket_metadata = {
                                    "docket_num": docket_num,
                                    "case_title": case_title,
                                    "filed_date": filed_date,
                                    "docket_url": href,
                                    "court_id": court_id
                                }
                                
                                dockets.append(docket_metadata)
                                self.log_debug(f"Parsed docket: {docket_num}")
                                
                except Exception as e:
                    self.log_debug(f"Error parsing row for court {court_id}: {e}")
                    continue
            
        except Exception as e:
            self.log_error(f"Error parsing docket results for court {court_id}: {e}")
        
        self.log_info(f"Parsed {len(dockets)} dockets from results for court {court_id}")
        return dockets
    
    async def _parse_docket_results_from_text(self, page: Page, court_id: str) -> List[Dict[str, Any]]:
        """
        Fallback method to parse docket information from page text when table parsing fails.
        """
        dockets = []
        
        try:
            page_text = await page.inner_text('body')
            
            # Look for docket number patterns in the text
            import re
            from src.utils.docket_utils import extract_docket_digits
            
            # Enhanced docket number patterns with optional judge initials
            docket_patterns = [
                r'\d+:\d{2}-cv-\d+(?:-\w+)?',  # Civil cases with optional judge initials
                r'\d+:\d{2}-cr-\d+(?:-\w+)?',  # Criminal cases with optional judge initials 
                r'\d+:\d{2}-bk-\d+(?:-\w+)?',  # Bankruptcy cases with optional judge initials
                r'\d+:\d{2}-md-\d+(?:-\w+)?',  # MDL cases with optional judge initials
                r'\d+:\d{2}-[a-zA-Z]{2}-\d+(?:-\w+)?',  # Generic pattern for any case type
            ]
            
            for pattern in docket_patterns:
                matches = re.finditer(pattern, page_text, re.IGNORECASE)
                
                for match in matches:
                    raw_docket = match.group()
                    # Use robust extraction to get clean 13-character format
                    docket_num = extract_docket_digits(raw_docket)
                    
                    self.logger.debug(f"Text parsing extracted: '{raw_docket}' -> '{docket_num}'")
                    
                    # Try to extract surrounding context for case title
                    start = max(0, match.start() - 100)
                    end = min(len(page_text), match.end() + 100)
                    context = page_text[start:end]
                    
                    case_title = self._extract_case_title_from_context(context, docket_num)
                    
                    docket_metadata = {
                        "docket_num": docket_num,
                        "case_title": case_title,
                        "filed_date": "",
                        "docket_url": "",
                        "court_id": court_id
                    }
                    
                    dockets.append(docket_metadata)
            
        except Exception as e:
            self.log_error(f"Error in text-based docket parsing for court {court_id}: {e}")
        
        return dockets
    
    def _extract_docket_number(self, link_text: str, href: str) -> str:
        """Extract docket number from link text or URL using robust extraction."""
        import re
        from src.utils.docket_utils import extract_docket_digits
        
        # Try link text first with enhanced patterns
        docket_patterns = [
            r'\d+:\d{2}-cv-\d+(?:-\w+)?',  # Civil cases with optional judge initials
            r'\d+:\d{2}-cr-\d+(?:-\w+)?',  # Criminal cases with optional judge initials
            r'\d+:\d{2}-bk-\d+(?:-\w+)?',  # Bankruptcy cases with optional judge initials
            r'\d+:\d{2}-md-\d+(?:-\w+)?',  # MDL cases with optional judge initials
            r'\d+:\d{2}-[a-zA-Z]{2}-\d+(?:-\w+)?'  # Generic pattern for any case type
        ]
        
        # Search link text first
        for pattern in docket_patterns:
            match = re.search(pattern, link_text, re.IGNORECASE)
            if match:
                raw_docket = match.group()
                # Use robust extraction to get clean 13-character format
                extracted = extract_docket_digits(raw_docket)
                self.logger.debug(f"Extracted docket from link_text: '{raw_docket}' -> '{extracted}'")
                return extracted
        
        # Try href as fallback
        for pattern in docket_patterns:
            match = re.search(pattern, href, re.IGNORECASE)
            if match:
                raw_docket = match.group()
                # Use robust extraction to get clean 13-character format
                extracted = extract_docket_digits(raw_docket)
                self.logger.debug(f"Extracted docket from href: '{raw_docket}' -> '{extracted}'")
                return extracted
        
        self.logger.warning(f"No docket number pattern found in link_text: '{link_text}' or href: '{href}'")
        return ""
    
    def _extract_case_title(self, row_text: str, link_text: str) -> str:
        """Extract case title from row text."""
        # Remove the docket number and URL components to get case title
        import re
        
        # Clean up the text
        cleaned_text = row_text.replace(link_text, "").strip()
        
        # Remove common PACER artifacts
        artifacts_to_remove = [
            r'\d+:\d{2}-\w{2}-\d+',  # Docket numbers
            r'https?://[^\s]+',       # URLs
            r'\d{1,2}/\d{1,2}/\d{4}', # Dates
            r'Judge:?\s*\w+',         # Judge names
            r'Filed:?\s*\d+/\d+/\d+', # Filed dates
        ]
        
        for pattern in artifacts_to_remove:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.IGNORECASE)
        
        # Take the first substantial text as case title
        parts = [part.strip() for part in cleaned_text.split() if len(part.strip()) > 2]
        case_title = ' '.join(parts[:10])  # Limit to first 10 words
        
        return case_title.strip()
    
    def _extract_filed_date(self, row_text: str) -> str:
        """Extract filed date from row text."""
        import re
        
        # Look for date patterns
        date_patterns = [
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'\d{4}-\d{2}-\d{2}',
            r'\w{3}\s+\d{1,2},?\s+\d{4}'  # Mar 15, 2024
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, row_text)
            if match:
                return match.group()
        
        return ""
    
    def _extract_case_title_from_context(self, context: str, docket_num: str) -> str:
        """Extract case title from surrounding context."""
        # Simple extraction - take text after docket number
        parts = context.split(docket_num)
        if len(parts) > 1:
            after_docket = parts[1].strip()
            # Take first line or up to 100 characters
            case_title = after_docket.split('\n')[0][:100].strip()
            return case_title
        
        return ""

    async def _load_docket_report_log_direct(self, iso_date: str, court_id: str) -> Optional[List[Dict[str, Any]]]:
        """
        Direct method to load docket report log when parent orchestrator is not available.
        """
        try:
            from pathlib import Path
            import json
            
            # Get data path from config or use default
            data_path = self.config.get("data_path", "./data") if self.config else "./data"
            
            # Construct the docket report file path
            docket_report_path = (
                Path(data_path)
                / iso_date
                / "logs"
                / "docket_report" 
                / f"{court_id}.json"
            )
            
            self.log_info(f"Checking for docket report log: {docket_report_path}")
            
            # Check if file exists
            if not docket_report_path.exists():
                self.log_info(f"No docket report log found for court {court_id}")
                return None
                
            # Load and parse JSON content
            with open(docket_report_path, 'r', encoding='utf-8') as f:
                log_data = json.load(f)
            
            # Extract cases from the log data
            cases = log_data.get('cases', [])
            metadata = log_data.get('metadata', {})
            
            self.log_info(f"Loaded {len(cases)} cases from docket report log for court {court_id}")
            if metadata:
                self.log_info(f"Report metadata: generated_at={metadata.get('generated_at', 'unknown')}")
            
            return cases
            
        except Exception as e:
            self.log_error(f"Error loading docket report log for court {court_id}: {str(e)}", exc_info=True)
            return None

    async def _load_docket_report_log_retry(self, court_id: str, iso_date: str) -> Optional[List[Dict[str, Any]]]:
        """
        Retry method to load docket report log with proper error handling.
        This method implements retry logic for loading docket report logs.
        
        Args:
            court_id: Court identifier
            iso_date: ISO date for the report
            
        Returns:
            List of cases if successful, None if not found
        """
        try:
            from pathlib import Path
            import json
            import asyncio
            
            # Get data path from config or use default
            data_path = self.config.get("data_path", "./data") if self.config else "./data"
            
            # Construct the docket report file path
            docket_report_path = (
                Path(data_path)
                / iso_date
                / "logs"
                / "docket_report" 
                / f"{court_id}.json"
            )
            
            self.log_info(f"[RETRY] Checking for docket report log: {docket_report_path}")
            
            # Try multiple times with small delays
            max_attempts = 3
            for attempt in range(max_attempts):
                # Check if file exists
                if docket_report_path.exists():
                    try:
                        # Load and parse JSON content
                        with open(docket_report_path, 'r', encoding='utf-8') as f:
                            log_data = json.load(f)
                        
                        # Extract cases from the log data
                        cases = log_data.get('cases', [])
                        metadata = log_data.get('metadata', {})
                        
                        self.log_info(f"[RETRY] Loaded {len(cases)} cases from docket report log for court {court_id}")
                        if metadata:
                            self.log_info(f"[RETRY] Report metadata: generated_at={metadata.get('generated_at', 'unknown')}")
                        
                        return cases
                        
                    except (json.JSONDecodeError, KeyError, FileNotFoundError) as e:
                        self.log_warning(f"[RETRY] Error reading docket report log attempt {attempt + 1}: {str(e)}")
                        
                        # Wait a bit before retrying
                        if attempt < max_attempts - 1:
                            await asyncio.sleep(1.0)
                        continue
                else:
                    self.log_info(f"[RETRY] Docket report log not found (attempt {attempt + 1}): {docket_report_path}")
                    
                    # Wait a bit before retrying
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(2.0)
            
            self.log_info(f"[RETRY] No docket report log found for court {court_id} after {max_attempts} attempts")
            return None
            
        except Exception as e:
            self.log_error(f"[RETRY] Error loading docket report log for court {court_id}: {str(e)}", exc_info=True)
            return None

    async def _generate_civil_cases_report_for_court(
        self, 
        court_id: str, 
        iso_date: str, 
        start_date: str, 
        end_date: str,
        config: Dict[str, Any],
        browser_context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate civil cases report for a specific court.
        
        This method creates a new docket report by discovering dockets and saving them
        to the proper docket_report_log format.
        """
        self.log_info(f"Generating civil cases report for court {court_id}")
        
        try:
            # Get or create browser context for report generation
            context_id = f"civil_report_{court_id}"
            page_id = f"civil_report_page_{court_id}"
            page = None
            
            if not browser_context and self._browser_service:
                try:
                    browser_context = await self._browser_service.create_context({
                        "context_id": context_id,
                        "ignore_https_errors": True
                    })
                    self.log_info(f"Created browser context for civil cases report: {court_id}")
                except Exception as e:
                    self.log_error(f"Failed to create browser context: {e}")
                    return {
                        "status": "failed",
                        "error": f"Browser setup failed: {e}",
                        "cases": []
                    }
            
            if browser_context and self._browser_service:
                try:
                    await self._browser_service.create_page({
                        "context_id": browser_context,
                        "page_id": page_id
                    })
                    page = await self._browser_service.get_page({"page_id": page_id})
                    
                    if not page:
                        raise ValueError(f"Failed to create page for court {court_id}")
                        
                    # Navigate to court PACER page
                    court_url = f"https://ecf.{court_id}.uscourts.gov/"
                    navigation_success = await self._browser_service.navigate({
                        "page_id": page_id,
                        "url": court_url,
                        "wait_until": "networkidle"
                    })
                    
                    if not navigation_success:
                        raise ValueError(f"Failed to navigate to court {court_id} PACER page")
                    
                    # Discover dockets using existing discovery method
                    discovered_dockets = await self._discover_court_dockets(
                        page, court_id, start_date, end_date
                    )
                    
                    # Convert discovered dockets to cases format
                    cases = []
                    for docket_metadata in discovered_dockets:
                        case = {
                            "docket_num": docket_metadata.get("docket_num"),
                            "court_id": court_id,
                            "case_title": docket_metadata.get("case_title", ""),
                            "filed_date": docket_metadata.get("filed_date", ""),
                            "docket_url": docket_metadata.get("docket_url", "")
                        }
                        cases.append(case)
                    
                    # Save the report to docket_report format
                    if cases:
                        await self._save_docket_report_log_direct(court_id, iso_date, cases)
                    
                    self.log_info(f"Generated civil cases report for court {court_id}: {len(cases)} cases")
                    
                    return {
                        "status": "success",
                        "cases": cases,
                        "cases_found": len(cases),
                        "message": f"Civil cases report generated successfully for court {court_id}"
                    }
                    
                except Exception as e:
                    self.log_error(f"Error during civil cases report generation: {e}")
                    return {
                        "status": "failed", 
                        "error": f"Report generation failed: {e}",
                        "cases": []
                    }
            else:
                return {
                    "status": "failed",
                    "error": "Browser service not available",
                    "cases": []
                }
                
        except Exception as e:
            self.log_error(f"Failed to generate civil cases report for court {court_id}: {str(e)}", exc_info=True)
            return {
                "status": "failed",
                "error": str(e),
                "cases": []
            }
        finally:
            # Clean up browser resources
            if self._browser_service:
                try:
                    if page:
                        await self._browser_service.close_page({"page_id": page_id})
                    if browser_context and context_id:
                        await self._browser_service.close_context({"context_id": browser_context})
                except Exception as e:
                    self.log_warning(f"Error cleaning up browser resources: {e}")

    async def _save_docket_report_log_direct(self, court_id: str, iso_date: str, cases: List[Dict[str, Any]]) -> None:
        """
        Save cases to docket report log format directly.
        
        Saves to: data/{iso_date}/logs/docket_report/{court_id}.json
        """
        try:
            from pathlib import Path
            import json
            from datetime import datetime
            
            # Get data path from config or use default
            data_path = self.config.get("data_path", "./data") if self.config else "./data"
            
            # Construct the docket report file path
            docket_report_path = (
                Path(data_path)
                / iso_date
                / "logs"
                / "docket_report" 
                / f"{court_id}.json"
            )
            
            # Ensure directory exists
            docket_report_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create the report structure
            report_data = {
                "cases": cases,
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "total_cases": len(cases),
                    "court_id": court_id,
                    "iso_date": iso_date,
                    "source": "docket_orchestrator"
                }
            }
            
            # Save to file
            with open(docket_report_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.log_info(f"Saved docket report log for court {court_id}: {len(cases)} cases to {docket_report_path}")
            
        except Exception as e:
            self.log_error(f"Error saving docket report log for court {court_id}: {str(e)}", exc_info=True)
            raise

    async def _process_court_with_isolated_context(
        self,
        court_id: str,
        processing_mode: str,
        workflow_config: Dict[str, Any],
        processor_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a court with completely isolated browser context for resource limiting.
        
        Implements the ISOLATED CONTEXT PROCESSING ARCHITECTURE:
        1. Acquire browser semaphore slot for resource limiting
        2. Create court-specific BrowserService instance  
        3. CREATE isolated browser context: browser.new_context()
        4. Set download path for court-specific file isolation
        5. Track browser context for resource management
        6. DETERMINE processing mode (docket_report_log exists vs standard processing)
        7. CLEANUP: Close isolated browser context + Release semaphore slot
        
        Args:
            court_id: Court identifier to process
            processing_mode: Mode of processing (date_range, specific_dockets, etc.)
            workflow_config: Workflow configuration parameters
            processor_config: Additional processor configuration
            
        Returns:
            Processing results with status and metadata
        """
        log_prefix = f"[{court_id}] IsolatedContext({processing_mode}):"
        self.log_info(f"{log_prefix} Starting isolated context processing")
        
        # Initialize result structure
        result = {
            'court_id': court_id,
            'processing_mode': processing_mode,
            'status': 'failed',
            'error': None,
            'processed_dockets': 0,
            'context_info': {
                'isolated_context_used': True,
                'semaphore_acquired': False,
                'context_created': False,
                'download_path_set': False
            }
        }
        
        # STEP 1: Acquire browser semaphore slot for resource limiting
        self.log_info(f"{log_prefix} Acquiring browser semaphore slot...")
        
        async with self._browser_semaphore:
            result['context_info']['semaphore_acquired'] = True
            self.log_info(f"{log_prefix} Browser semaphore acquired")
            
            isolated_context = None
            context_id = f"isolated_court_{court_id}_{processing_mode}"
            
            try:
                # STEP 2: Create court-specific BrowserService instance if not available
                if not self._browser_service:
                    self.log_error(f"{log_prefix} BrowserService not available for isolated context creation")
                    result['error'] = "BrowserService not available"
                    return result
                
                # STEP 3: CREATE isolated browser context
                self.log_info(f"{log_prefix} Creating isolated browser context: {context_id}")
                
                # Create isolated browser context with court-specific settings
                context_options = {
                    "context_id": context_id,
                    "ignore_https_errors": True,
                    "viewport": {"width": 1920, "height": 1080},
                    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
                }
                
                isolated_context = await self._browser_service.create_context(context_options)
                if isolated_context:
                    result['context_info']['context_created'] = True
                    self._isolated_contexts[context_id] = isolated_context
                    self.log_info(f"{log_prefix} Isolated browser context created successfully")
                else:
                    raise ValueError("Failed to create isolated browser context")
                
                # STEP 4: Set download path for court-specific file isolation
                iso_date = workflow_config.get('iso_date')
                if iso_date and self._file_operations:
                    try:
                        # Setup court-specific download directory
                        download_path = f"data/{iso_date}/downloads/{court_id}"
                        
                        # Ensure directory exists
                        import os
                        os.makedirs(download_path, exist_ok=True)
                        
                        # Configure browser context download path
                        if hasattr(isolated_context, 'set_default_download_directory'):
                            await isolated_context.set_default_download_directory(download_path)
                        
                        result['context_info']['download_path_set'] = True
                        result['context_info']['download_path'] = download_path
                        
                        self.log_info(f"{log_prefix} Download path configured: {download_path}")
                        
                        # Update processor config with isolated download path
                        if processor_config:
                            processor_config['isolated_download_path'] = download_path
                        
                    except Exception as e:
                        self.log_warning(f"{log_prefix} Could not set isolated download path: {e}")
                
                # STEP 5: Track browser context for resource management
                result['context_info']['context_id'] = context_id
                result['context_info']['context_tracked'] = True
                
                # STEP 6: DETERMINE processing mode (docket_report_log exists vs standard processing)
                processing_strategy = await self._determine_processing_strategy(
                    court_id, workflow_config, iso_date
                )
                
                self.log_info(f"{log_prefix} Determined processing strategy: {processing_strategy}")
                result['processing_strategy'] = processing_strategy
                
                # Execute the appropriate processing workflow with isolated context
                if processing_strategy == 'resume_from_log':
                    processing_result = await self._process_court_from_docket_log(
                        court_id=court_id,
                        isolated_context=isolated_context,
                        workflow_config=workflow_config,
                        processor_config=processor_config
                    )
                elif processing_strategy == 'standard_processing':
                    processing_result = await self._process_court_standard_workflow(
                        court_id=court_id,
                        isolated_context=isolated_context,
                        workflow_config=workflow_config,
                        processor_config=processor_config
                    )
                else:
                    raise ValueError(f"Unknown processing strategy: {processing_strategy}")
                
                # Update result with processing outcome
                result.update(processing_result)
                result['status'] = 'success' if processing_result.get('status') == 'success' else 'failed'
                
                self.log_info(f"{log_prefix} Isolated context processing completed: {result.get('processed_dockets', 0)} dockets processed")
                
            except Exception as e:
                self.log_error(f"{log_prefix} Error during isolated context processing: {e}", exc_info=True)
                result['error'] = str(e)
                result['status'] = 'failed'
                
            finally:
                # STEP 7: CLEANUP - Close isolated browser context + Release semaphore slot
                await self._cleanup_isolated_context(context_id, isolated_context, log_prefix)
                result['context_info']['cleanup_completed'] = True
        
        self.log_info(f"{log_prefix} Isolated context processing finished with status: {result['status']}")
        return result

    async def _determine_processing_strategy(
        self, 
        court_id: str, 
        workflow_config: Dict[str, Any], 
        iso_date: Optional[str]
    ) -> str:
        """
        Determine whether to resume from existing docket_report_log or start new processing.
        
        Args:
            court_id: Court identifier
            workflow_config: Workflow configuration
            iso_date: ISO date string
            
        Returns:
            Processing strategy: 'resume_from_log' or 'standard_processing'
        """
        if not iso_date:
            self.log_warning(f"No ISO date available for court {court_id}, defaulting to standard processing")
            return 'standard_processing'
        
        # Check for existing docket_report_log
        existing_cases = await self._load_docket_report_log_direct(iso_date, court_id)
        
        if existing_cases and len(existing_cases) > 0:
            self.log_info(f"Found existing docket report log for {court_id} with {len(existing_cases)} cases")
            return 'resume_from_log'
        else:
            self.log_info(f"No existing docket report log found for {court_id}, using standard processing")
            return 'standard_processing'

    async def _process_court_from_docket_log(
        self,
        court_id: str,
        isolated_context: BrowserContext,
        workflow_config: Dict[str, Any],
        processor_config: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Process court using existing docket_report_log data with isolated context.
        
        Args:
            court_id: Court identifier
            isolated_context: Isolated browser context
            workflow_config: Workflow configuration
            processor_config: Processor configuration
            
        Returns:
            Processing results
        """
        log_prefix = f"[{court_id}] DocketLogProcessing:"
        self.log_info(f"{log_prefix} Processing court from existing docket log")
        
        try:
            iso_date = workflow_config.get('iso_date')
            existing_cases = await self._load_docket_report_log_direct(iso_date, court_id)
            
            if not existing_cases:
                return {
                    'status': 'failed',
                    'error': 'No cases found in docket log',
                    'processed_dockets': 0
                }
            
            # Create page from isolated context
            page = await isolated_context.new_page()
            
            # Process each case using the isolated context
            processed_count = 0
            failed_count = 0
            processed_results = []
            
            for case in existing_cases:
                try:
                    # Prepare initial details for comprehensive processing
                    initial_details = {
                        "court_id": court_id,
                        "docket_num": case.get("docket_num"),
                        "case_title": case.get("case_title", ""),
                        "filed_date": case.get("filed_date", ""),
                        "iso_date": iso_date,
                        "_is_explicitly_requested": True,
                        "_processing_source": "docket_log",
                        **case
                    }
                    
                    # Process through comprehensive docket processing
                    case_result = await self.process_docket_comprehensive(page, initial_details)
                    
                    if case_result:
                        processed_results.append(case_result)
                        processed_count += 1
                        self.log_info(f"{log_prefix} Processed docket {case.get('docket_num')}")
                    else:
                        failed_count += 1
                        self.log_warning(f"{log_prefix} Failed to process docket {case.get('docket_num')}")
                        
                except Exception as e:
                    failed_count += 1
                    self.log_error(f"{log_prefix} Error processing case {case.get('docket_num', 'unknown')}: {e}")
            
            # CRITICAL: Close the single page after all processing
            if page and not page.is_closed():
                await page.close()
                self.log_info(f"{log_prefix} Single processing page closed successfully")
            
            return {
                'status': 'success',
                'processed_dockets': processed_count,
                'failed_dockets': failed_count,
                'total_cases': len(existing_cases),
                'processing_results': processed_results,
                'message': f"Processed {processed_count}/{len(existing_cases)} cases from docket log"
            }
            
        except Exception as e:
            self.log_error(f"{log_prefix} Error in docket log processing: {e}", exc_info=True)
            return {
                'status': 'failed',
                'error': str(e),
                'processed_dockets': 0
            }

    async def _process_court_standard_workflow(
        self,
        court_id: str,
        isolated_context: BrowserContext,
        workflow_config: Dict[str, Any],
        processor_config: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Process court using standard workflow (generate report then process) with isolated context.
        
        Args:
            court_id: Court identifier
            isolated_context: Isolated browser context
            workflow_config: Workflow configuration
            processor_config: Processor configuration
            
        Returns:
            Processing results
        """
        log_prefix = f"[{court_id}] StandardProcessing:"
        self.log_info(f"{log_prefix} Processing court using standard workflow")
        
        try:
            iso_date = workflow_config.get('iso_date')
            start_date = workflow_config.get('start_date_obj')
            end_date = workflow_config.get('end_date_obj')
            
            if not all([iso_date, start_date, end_date]):
                return {
                    'status': 'failed',
                    'error': 'Missing required date parameters',
                    'processed_dockets': 0
                }
            
            # Create page from isolated context
            page = await isolated_context.new_page()
            
            # Step 1: Generate civil cases report to discover dockets
            start_date_str = start_date.strftime('%m/%d/%y')
            end_date_str = end_date.strftime('%m/%d/%y')
            
            discovered_dockets = await self._discover_court_dockets(
                page, court_id, start_date_str, end_date_str
            )
            
            if not discovered_dockets:
                await page.close()
                return {
                    'status': 'success_no_cases',
                    'message': f"No dockets discovered for court {court_id}",
                    'processed_dockets': 0
                }
            
            # Step 2: Save discovered dockets to docket_report_log format
            cases_to_save = []
            for docket_metadata in discovered_dockets:
                case = {
                    "docket_num": docket_metadata.get("docket_num"),
                    "court_id": court_id,
                    "case_title": docket_metadata.get("case_title", ""),
                    "filed_date": docket_metadata.get("filed_date", ""),
                    "docket_url": docket_metadata.get("docket_url", "")
                }
                cases_to_save.append(case)
            
            await self._save_docket_report_log_direct(court_id, iso_date, cases_to_save)
            
            # Step 3: Process each discovered docket using isolated context
            processed_count = 0
            failed_count = 0
            processed_results = []
            
            for case in cases_to_save:
                try:
                    # Prepare initial details for comprehensive processing
                    initial_details = {
                        "court_id": court_id,
                        "docket_num": case.get("docket_num"),
                        "case_title": case.get("case_title", ""),
                        "filed_date": case.get("filed_date", ""),
                        "iso_date": iso_date,
                        "_is_explicitly_requested": False,  # Discovered, not explicitly requested
                        "_processing_source": "standard_discovery",
                        **case
                    }
                    
                    # Process through comprehensive docket processing
                    case_result = await self.process_docket_comprehensive(page, initial_details)
                    
                    if case_result:
                        processed_results.append(case_result)
                        processed_count += 1
                        self.log_info(f"{log_prefix} Processed discovered docket {case.get('docket_num')}")
                    else:
                        failed_count += 1
                        self.log_warning(f"{log_prefix} Failed to process discovered docket {case.get('docket_num')}")
                        
                except Exception as e:
                    failed_count += 1
                    self.log_error(f"{log_prefix} Error processing discovered case {case.get('docket_num', 'unknown')}: {e}")
            
            # CRITICAL: Close the single page after all processing
            if page and not page.is_closed():
                await page.close()
                self.log_info(f"{log_prefix} Single processing page closed successfully")
            
            return {
                'status': 'success',
                'processed_dockets': processed_count,
                'failed_dockets': failed_count,
                'total_discovered': len(discovered_dockets),
                'processing_results': processed_results,
                'message': f"Discovered and processed {processed_count}/{len(discovered_dockets)} dockets"
            }
            
        except Exception as e:
            self.log_error(f"{log_prefix} Error in standard workflow processing: {e}", exc_info=True)
            return {
                'status': 'failed',
                'error': str(e),
                'processed_dockets': 0
            }

    async def _cleanup_isolated_context(
        self, 
        context_id: str, 
        isolated_context: Optional[BrowserContext], 
        log_prefix: str
    ) -> None:
        """
        Clean up isolated browser context and remove from tracking.
        
        Args:
            context_id: Context identifier
            isolated_context: Browser context to cleanup
            log_prefix: Logging prefix for consistency
        """
        try:
            self.log_info(f"{log_prefix} Starting cleanup of isolated context: {context_id}")
            
            # Remove from tracking first
            if context_id in self._isolated_contexts:
                del self._isolated_contexts[context_id]
                self.log_info(f"{log_prefix} Removed context from tracking: {context_id}")
            
            # Close isolated browser context
            if isolated_context and self._browser_service:
                try:
                    await self._browser_service.close_context({"context_id": context_id})
                    self.log_info(f"{log_prefix} Isolated browser context closed successfully")
                except Exception as e:
                    self.log_warning(f"{log_prefix} Error closing isolated context: {e}")
                    
                    # Fallback: try to close context directly
                    try:
                        await isolated_context.close()
                        self.log_info(f"{log_prefix} Context closed via direct method")
                    except Exception as direct_e:
                        self.log_error(f"{log_prefix} Failed to close context directly: {direct_e}")
            
            self.log_info(f"{log_prefix} Isolated context cleanup completed")
            
        except Exception as e:
            self.log_error(f"{log_prefix} Unexpected error during context cleanup: {e}", exc_info=True)