# src/pacer/facades/authentication_facade.py
from typing import Any, Dict, Optional
from enum import Enum

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.components.authentication.ecf_login_handler import ECFLoginHandler
from src.pacer.components.authentication.login_handler import LoginHandler


class AuthenticationState(Enum):
    """Authentication workflow states based on PACER_WORKFLOW_FLOWCHART.md"""
    INITIAL = "initial"
    LOGIN_REQUIRED = "login_required"
    CLIENT_CODE_REQUIRED = "client_code_required"
    NEXTGEN_SELECTION = "nextgen_selection"
    ECF_NAVIGATION = "ecf_navigation"
    COURT_HOME = "court_home"
    AUTHENTICATED = "authenticated"
    ERROR = "error"


class AuthenticationFacade(AsyncServiceBase):
    """
    Simplified authentication facade following PACER_WORKFLOW_FLOWCHART.md
    
    Workflow:
    1. Detect initial page state (username/password vs client code)
    2. Route to appropriate strategy (NextGen vs Client Code)
    3. Handle MDD court popup if needed
    4. Complete authentication to court home page
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        login_handler: Optional[LoginHandler] = None,
        ecf_login_handler: Optional[ECFLoginHandler] = None,
    ):
        super().__init__(logger, config)
        self.login_handler = login_handler or LoginHandler(logger=logger, config=config)
        self.ecf_login_handler = ecf_login_handler or ECFLoginHandler(logger=logger, config=config)

    async def execute(self, data: Any) -> Any:
        """Main execution method for the facade."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """Routes actions to the appropriate authentication components."""
        action = data.get("action")
        
        if action == "login":
            return await self.perform_login(**data)
        else:
            raise ValueError(f"Unknown action for AuthenticationFacade: {action}")

    async def detect_initial_state(self, navigator, court_id: str) -> AuthenticationState:
        """
        Detects initial page state to determine authentication strategy.
        
        Based on PACER_WORKFLOW_FLOWCHART.md - Check Page State:
        - Username/Password Fields → LOGIN_REQUIRED
        - Client Code Field → CLIENT_CODE_REQUIRED
        """
        log_prefix = f"[{court_id}] AuthFacade:"
        self.log_info(f"{log_prefix} Detecting initial authentication state...")
        
        try:
            # Navigate to PACER login URL
            login_url = "https://pacer.login.uscourts.gov/csologin/login.jsf"
            self.log_info(f"{log_prefix} Navigating to PACER login: {login_url}")
            await navigator.goto(login_url)
            
            # Wait for page to load
            await navigator.page.wait_for_load_state('domcontentloaded')
            
            # Check page content to determine state
            page_content = await navigator.page.content()
            
            # Check for client code field (already logged in scenario)
            if ("clientCode" in page_content or 
                "Client Code" in page_content or
                "Logged in as" in page_content):
                self.log_info(f"{log_prefix} Initial state: CLIENT_CODE_REQUIRED (already logged in)")
                return AuthenticationState.CLIENT_CODE_REQUIRED
            
            # Check for username/password fields (need to login scenario)
            elif ("username" in page_content.lower() and 
                  "password" in page_content.lower()):
                self.log_info(f"{log_prefix} Initial state: LOGIN_REQUIRED (need to enter credentials)")
                return AuthenticationState.LOGIN_REQUIRED
            
            # Default to login required if unclear
            self.log_warning(f"{log_prefix} Could not determine state clearly, defaulting to LOGIN_REQUIRED")
            return AuthenticationState.LOGIN_REQUIRED
            
        except Exception as e:
            self.log_error(f"{log_prefix} Error detecting initial state: {e}")
            return AuthenticationState.ERROR
    
    async def perform_login(self, **kwargs) -> bool:
        """
        Simplified authentication workflow following PACER_WORKFLOW_FLOWCHART.md
        
        Steps:
        1. Detect initial page state (username/password vs client code)
        2. Route to appropriate authentication strategy
        3. Handle ECF navigation and MDD court popup if needed
        4. Verify authentication to court home page
        """
        navigator = kwargs.get("navigator")
        court_id = kwargs.get("court_id")
        
        if not navigator or not court_id:
            self.log_error("AuthFacade: Navigator and court_id are required")
            return False
        
        log_prefix = f"[{court_id}] AuthFacade:"
        self.log_info(f"{log_prefix} Starting simplified authentication workflow...")
        
        try:
            # Step 1: Detect initial authentication state
            initial_state = await self.detect_initial_state(navigator, court_id)
            
            if initial_state == AuthenticationState.ERROR:
                return False
            
            # Step 2: Route to appropriate authentication strategy
            if initial_state == AuthenticationState.LOGIN_REQUIRED:
                self.log_info(f"{log_prefix} Using LOGIN_REQUIRED strategy...")
                # Perform main PACER login (credentials → possible NextGen/Client Code path)
                login_result = await self.login_handler.perform_main_pacer_login(navigator, court_id)
                if not login_result:
                    self.log_error(f"{log_prefix} Login strategy failed")
                    return False
            
            elif initial_state == AuthenticationState.CLIENT_CODE_REQUIRED:
                self.log_info(f"{log_prefix} Using CLIENT_CODE_REQUIRED strategy...")
                # Handle client code entry directly
                client_code_result = await self.ecf_login_handler._handle_client_code_page(navigator, court_id)
                if not client_code_result:
                    self.log_error(f"{log_prefix} Client code strategy failed")
                    return False
            
            # Step 3: Complete ECF navigation and authentication
            self.log_info(f"{log_prefix} Completing ECF navigation...")
            ecf_result = await self.ecf_login_handler.perform_simplified_ecf_navigation(navigator, court_id)
            
            if ecf_result:
                self.log_info(f"{log_prefix} Simplified authentication workflow completed successfully")
                return True
            else:
                self.log_error(f"{log_prefix} ECF navigation failed")
                return False
                
        except Exception as e:
            self.log_error(f"{log_prefix} Authentication workflow error: {e}", exc_info=True)
            return False