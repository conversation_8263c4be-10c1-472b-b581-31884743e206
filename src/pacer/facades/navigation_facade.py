# src/pacer/facades/navigation_facade.py
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.components.navigation.page_navigator import PageNavigator
from src.pacer.components.navigation.element_locator import ElementLocator
from src.utils.pacer_utils import get_document_range_defaults, validate_and_format_docket_number


class NavigationFacade(AsyncServiceBase):
    """
    Facade for all navigation-related operations in PACER.
    Provides a unified interface for page navigation and element interaction.
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        page_navigator: Optional[PageNavigator] = None,
        element_locator: Optional[ElementLocator] = None,
    ):
        super().__init__(logger, config)
        self.element_locator = element_locator or ElementLocator(logger=logger, config=config)
        self.page_navigator = page_navigator or PageNavigator(
            element_locator=self.element_locator,
            logger=logger, 
            config=config
        )

    async def execute(self, data: Any) -> Any:
        """Main execution method for the facade."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """Routes actions to the appropriate navigation components."""
        action = data.get("action")
        
        if action == "go_to_query_page":
            return await self.go_to_query_page(**data)
        elif action == "navigate_to_query_page":
            return await self.navigate_to_query_page(**data)
        elif action == "query_docket_and_get_page":
            return await self.query_docket_and_get_page(**data)
        elif action == "navigate_to_civil_cases_report":
            return await self.navigate_to_civil_cases_report(**data)
        elif action == "navigate_to_reports":
            return await self.navigate_to_reports(**data)
        elif action == "click_element":
            return await self.page_navigator.perform_action({
                "action": "click_element",
                "navigator": data.get("navigator"),
                "element_name": data.get("element_name")
            })
        elif action == "fill_element":
            return await self.page_navigator.perform_action({
                "action": "fill_element",
                "navigator": data.get("navigator"),
                "element_name": data.get("element_name"),
                "value": data.get("value")
            })
        elif action == "auto_populate_document_range":
            return await self.auto_populate_document_range(**data)
        elif action == "fill_docket_query_form":
            return await self.fill_docket_query_form(**data)
        elif action == "execute_complete_case_query_workflow":
            return await self.execute_complete_case_query_workflow(**data)
        else:
            raise ValueError(f"Unknown action for NavigationFacade: {action}")

    async def navigate_to_query_page(self, **kwargs) -> bool:
        """
        Navigates to the query page by finding and clicking the Query link.
        Uses multiple flexible approaches to handle different PACER page structures.
        
        Args:
            navigator: PacerNavigator instance
            court_id: Court identifier
            
        Returns:
            bool: True if navigation successful
        """
        navigator = kwargs.get("navigator")
        court_id = kwargs.get("court_id")
        
        log_prefix = f"[{court_id}] NavFacade:"
        
        # Log before action (as specified in legacy spec)
        self.log_info(f"{log_prefix} Looking for Query navigation link")
        
        # Check if already on query page
        current_url = navigator.page.url
        if "iquery.pl" in current_url:
            self.log_info(f"{log_prefix} Already on query page: {current_url}")
            return True
        
        try:
            # First, log all available links for debugging
            await self._log_available_links(navigator, court_id)
            
            # Try multiple patterns in order of preference with enhanced fallback strategies
            navigation_patterns = [
                # PRIMARY STRATEGY: Direct navigation if we know the URL pattern works
                ('DIRECT_NAVIGATION', "Direct navigation to iquery.pl (fastest and most reliable)"),
                
                # Most specific: YUI menu bar Query link with iquery.pl href
                ('a.yuimenubaritemlabel[href*="iquery.pl"]', "YUI menu Query link with iquery.pl href"),
                
                # Direct iquery.pl link (most reliable)
                ('a[href*="iquery.pl"]', "Direct iquery.pl link"),
                
                # CSS selector for links containing iquery
                ('a[href*="/cgi-bin/iquery"]', "CGI-bin iquery link pattern"),
                
                # YUI menu bar Query link (common PACER pattern)
                ('a.yuimenubaritemlabel:has-text("Query")', "YUI menu Query link with Query text"),
                
                # Case-insensitive Query text with flexible whitespace
                ('a:text-matches("\\s*[Qq]uery\\s*", "i")', "Flexible Query text with whitespace"),
                
                # Any link containing "Query" text (case-insensitive)
                ('a:text-matches("Query", "i")', "Case-insensitive Query text match"),
                
                # Contains Query text with underlined Q (common PACER format)
                ('a:has-text("Query")', "Contains Query text"),
                
                # Broader pattern for Query with any formatting
                ('a:text-matches(".*[Qq]uery.*", "")', "Regex pattern for Query"),
                
                # Navigation menu patterns
                ('nav a:has-text("Query")', "Navigation menu Query link"),
                ('ul.menu a:has-text("Query")', "Menu list Query link"),
                
                # Generic query-related patterns
                ('a[title*="Query" i]', "Link with Query in title attribute"),
                ('a[alt*="Query" i]', "Link with Query in alt attribute"),
                
                # Exact text match (original pattern, kept for compatibility)
                ('a:text-is("Query")', "Exact text 'Query'"),
            ]
            
            # Try each pattern
            for pattern, description in navigation_patterns:
                try:
                    # Special handling for direct navigation strategy
                    if pattern == 'DIRECT_NAVIGATION':
                        self.log_info(f"{log_prefix} Trying strategy: {description}")
                        success = await self._try_direct_navigation(navigator, court_id)
                        if success:
                            return True
                        else:
                            self.log_debug(f"{log_prefix} Direct navigation failed, continuing with other patterns")
                            continue
                    
                    self.log_info(f"{log_prefix} Trying pattern: {description} ({pattern})")
                    locator = navigator.page.locator(pattern)
                    count = await locator.count()
                    
                    if count > 0:
                        self.log_info(f"{log_prefix} Found {count} link(s) with pattern: {description}")
                        
                        # Try each matching element until one works
                        for i in range(count):
                            element = locator.nth(i)
                            try:
                                # Check if element is visible and enabled
                                is_visible = await element.is_visible()
                                is_enabled = await element.is_enabled()
                                
                                if is_visible and is_enabled:
                                    # Get element details for logging
                                    href = await element.get_attribute('href')
                                    text_content = await element.text_content()
                                    inner_html = await element.inner_html()
                                    
                                    self.log_info(f"{log_prefix} Attempting click on element #{i+1}: href='{href}', text='{text_content}', html='{inner_html[:100]}...'")
                                    
                                    # Click the element
                                    await element.click()
                                    self.log_info(f"{log_prefix} Successfully clicked Query link")
                                    
                                    # Wait for navigation with longer timeout
                                    try:
                                        await navigator.page.wait_for_load_state("domcontentloaded", timeout=15000)
                                        await navigator.page.wait_for_timeout(1000)
                                    except Exception as wait_error:
                                        self.log_debug(f"{log_prefix} Wait timeout, but continuing: {wait_error}")
                                    
                                    # Verify we're on the query page
                                    new_url = navigator.page.url
                                    if "iquery.pl" in new_url:
                                        self.log_info(f"{log_prefix} Successfully navigated to query page: {new_url}")
                                        return True
                                    else:
                                        self.log_warning(f"{log_prefix} Click succeeded but URL is unexpected: {new_url}")
                                        # Continue trying other elements/patterns
                                else:
                                    self.log_debug(f"{log_prefix} Element #{i+1} not clickable (visible: {is_visible}, enabled: {is_enabled})")
                                    
                            except Exception as element_error:
                                self.log_debug(f"{log_prefix} Failed to click element #{i+1}: {element_error}")
                                continue
                    else:
                        self.log_debug(f"{log_prefix} No elements found for pattern: {description}")
                        
                except Exception as pattern_error:
                    self.log_debug(f"{log_prefix} Pattern '{description}' failed: {pattern_error}")
                    continue
            
            # Advanced strategies before direct navigation
            self.log_warning(f"{log_prefix} All basic click attempts failed, trying advanced strategies")
            
            # Try menu expansion and hidden element strategies
            advanced_success = await self._try_advanced_navigation_strategies(navigator, court_id)
            if advanced_success:
                return True
            
            # Final fallback: Direct navigation
            self.log_warning(f"{log_prefix} All navigation strategies failed, trying direct navigation")
            return await self._try_direct_navigation(navigator, court_id)
            
        except Exception as e:
            self.log_error(f"{log_prefix} Navigation to Query page failed: {e}", exc_info=True)
            # Try direct navigation as last resort
            return await self._try_direct_navigation(navigator, court_id)

    async def _try_advanced_navigation_strategies(self, navigator, court_id):
        """
        Try advanced navigation strategies for finding Query links:
        1. Hover over menu items to reveal dropdowns
        2. Check for hidden elements that become visible
        3. Try JavaScript-based navigation
        4. Look for iframe content
        """
        log_prefix = f"[{court_id}] NavFacade:"
        
        try:
            self.log_info(f"{log_prefix} Trying advanced navigation strategies")
            
            # Strategy 1: Try hovering over menu items to reveal dropdowns
            menu_selectors = [
                'a.yuimenubaritemlabel',           # YUI menu items
                'li.yuimenubaritem',               # YUI menu list items
                'nav a',                           # Navigation anchors
                'ul.menu li',                      # Menu list items
                '.navbar a',                       # Bootstrap navbar
                '.menu-item',                      # Generic menu items
            ]
            
            for menu_selector in menu_selectors:
                try:
                    menu_items = navigator.page.locator(menu_selector)
                    count = await menu_items.count()
                    
                    if count > 0:
                        self.log_debug(f"{log_prefix} Found {count} menu items with selector: {menu_selector}")
                        
                        # Hover over each menu item and look for Query links
                        for i in range(min(5, count)):  # Limit to first 5 to avoid excessive hovering
                            menu_item = menu_items.nth(i)
                            try:
                                # Hover over the menu item
                                await menu_item.hover(timeout=2000)
                                await navigator.page.wait_for_timeout(500)  # Wait for dropdown
                                
                                # Now look for Query links that might have appeared
                                query_patterns = [
                                    'a[href*="iquery.pl"]',
                                    'a:has-text("Query")',
                                    'a:text-matches("Query", "i")',
                                ]
                                
                                for pattern in query_patterns:
                                    try:
                                        query_links = navigator.page.locator(pattern)
                                        query_count = await query_links.count()
                                        
                                        if query_count > 0:
                                            # Try clicking the first visible Query link
                                            for j in range(query_count):
                                                query_link = query_links.nth(j)
                                                if await query_link.is_visible():
                                                    href = await query_link.get_attribute('href')
                                                    text = await query_link.text_content()
                                                    
                                                    self.log_info(f"{log_prefix} Found Query link after hover: href='{href}', text='{text}'")
                                                    
                                                    await query_link.click()
                                                    await navigator.page.wait_for_timeout(1000)
                                                    
                                                    # Check if navigation succeeded
                                                    new_url = navigator.page.url
                                                    if "iquery.pl" in new_url:
                                                        self.log_info(f"{log_prefix} Advanced strategy succeeded: {new_url}")
                                                        return True
                                    except Exception:
                                        continue
                                        
                            except Exception as hover_error:
                                self.log_debug(f"{log_prefix} Hover strategy failed for item {i}: {hover_error}")
                                continue
                                
                except Exception as menu_error:
                    self.log_debug(f"{log_prefix} Menu selector {menu_selector} failed: {menu_error}")
                    continue
            
            # Strategy 2: Try JavaScript-based approach
            self.log_debug(f"{log_prefix} Trying JavaScript-based query link detection")
            
            try:
                # Use JavaScript to find links that might not be visible to standard selectors
                js_script = """
                () => {
                    const links = Array.from(document.querySelectorAll('a'));
                    const queryLinks = links.filter(link => {
                        const href = link.href || '';
                        const text = link.textContent || '';
                        const title = link.title || '';
                        
                        return href.includes('iquery.pl') || 
                               text.toLowerCase().includes('query') ||
                               title.toLowerCase().includes('query');
                    });
                    
                    return queryLinks.map(link => ({
                        href: link.href,
                        text: link.textContent.trim(),
                        visible: link.offsetParent !== null,
                        rect: link.getBoundingClientRect()
                    }));
                }
                """
                
                js_result = await navigator.page.evaluate(js_script)
                
                if js_result and len(js_result) > 0:
                    self.log_info(f"{log_prefix} JavaScript found {len(js_result)} potential Query links")
                    
                    for idx, link_info in enumerate(js_result):
                        href = link_info.get('href', '')
                        text = link_info.get('text', '')
                        visible = link_info.get('visible', False)
                        
                        self.log_debug(f"{log_prefix} JS Link {idx}: href='{href}', text='{text}', visible={visible}")
                        
                        if href and ('iquery.pl' in href or 'query.pl' in href):
                            # Try to click this link using JavaScript
                            try:
                                click_script = f"""
                                () => {{
                                    const link = document.querySelector('a[href="{href}"]');
                                    if (link) {{
                                        link.click();
                                        return true;
                                    }}
                                    return false;
                                }}
                                """
                                
                                click_success = await navigator.page.evaluate(click_script)
                                
                                if click_success:
                                    self.log_info(f"{log_prefix} JavaScript click succeeded for: {href}")
                                    await navigator.page.wait_for_timeout(2000)
                                    
                                    # Verify navigation
                                    new_url = navigator.page.url
                                    if "iquery.pl" in new_url:
                                        self.log_info(f"{log_prefix} JavaScript strategy succeeded: {new_url}")
                                        return True
                                        
                            except Exception as js_click_error:
                                self.log_debug(f"{log_prefix} JavaScript click failed: {js_click_error}")
                                continue
                
            except Exception as js_error:
                self.log_debug(f"{log_prefix} JavaScript strategy failed: {js_error}")
            
            # Strategy 3: Check for iframes that might contain Query links
            try:
                frames = navigator.page.frames
                if len(frames) > 1:  # More than just the main frame
                    self.log_debug(f"{log_prefix} Checking {len(frames)} frames for Query links")
                    
                    for frame in frames[1:]:  # Skip main frame
                        try:
                            frame_query_links = frame.locator('a[href*="iquery.pl"], a:has-text("Query")')
                            frame_count = await frame_query_links.count()
                            
                            if frame_count > 0:
                                self.log_info(f"{log_prefix} Found {frame_count} Query links in frame")
                                
                                for i in range(frame_count):
                                    frame_link = frame_query_links.nth(i)
                                    if await frame_link.is_visible():
                                        await frame_link.click()
                                        await navigator.page.wait_for_timeout(2000)
                                        
                                        new_url = navigator.page.url
                                        if "iquery.pl" in new_url:
                                            self.log_info(f"{log_prefix} Frame strategy succeeded: {new_url}")
                                            return True
                                            
                        except Exception as frame_error:
                            self.log_debug(f"{log_prefix} Frame check failed: {frame_error}")
                            continue
                            
            except Exception as iframe_error:
                self.log_debug(f"{log_prefix} Iframe strategy failed: {iframe_error}")
            
            self.log_debug(f"{log_prefix} All advanced strategies failed")
            return False
            
        except Exception as e:
            self.log_error(f"{log_prefix} Advanced navigation strategies failed: {e}")
            return False

    async def _log_available_links(self, navigator, court_id):
        """Log available links on the page for debugging purposes with enhanced pattern matching."""
        log_prefix = f"[{court_id}] NavFacade:"
        
        try:
            # Log all links on the page for debugging
            all_links = navigator.page.locator('a')
            link_count = await all_links.count()
            
            self.log_info(f"{log_prefix} Found {link_count} total links on page: {navigator.page.url}")
            
            # Track potential Query links
            query_candidates = []
            iquery_candidates = []
            
            # Log first 20 links with their href and text (increased from 10)
            for i in range(min(20, link_count)):
                link = all_links.nth(i)
                try:
                    href = await link.get_attribute('href')
                    text = await link.text_content()
                    class_attr = await link.get_attribute('class')
                    title_attr = await link.get_attribute('title')
                    inner_html = await link.inner_html()
                    
                    # Clean up text for logging
                    clean_text = (text or '').strip().replace('\n', ' ').replace('\t', ' ')
                    # Remove extra whitespace
                    import re
                    clean_text = re.sub(r'\s+', ' ', clean_text)[:100]
                    
                    self.log_debug(f"{log_prefix} Link #{i+1}: href='{href}', text='{clean_text}', class='{class_attr}', title='{title_attr}'")
                    
                    # Specifically log if this looks like a Query link
                    if href and 'iquery.pl' in href.lower():
                        iquery_candidates.append((i+1, href, clean_text))
                        self.log_info(f"{log_prefix} *** FOUND iquery.pl link #{i+1}: href='{href}', text='{clean_text}'")
                    
                    if text and 'query' in text.lower():
                        query_candidates.append((i+1, href, clean_text))
                        self.log_info(f"{log_prefix} *** FOUND Query text link #{i+1}: href='{href}', text='{clean_text}'")
                    
                    # Check for Query in other attributes
                    if title_attr and 'query' in title_attr.lower():
                        self.log_info(f"{log_prefix} *** FOUND Query in title #{i+1}: title='{title_attr}', href='{href}'")
                    
                    # Check inner HTML for Query patterns
                    if inner_html and 'query' in inner_html.lower():
                        self.log_debug(f"{log_prefix} Query found in HTML #{i+1}: {inner_html[:100]}...")
                        
                except Exception as link_error:
                    self.log_debug(f"{log_prefix} Error examining link #{i+1}: {link_error}")
            
            # Summary of findings
            if iquery_candidates:
                self.log_info(f"{log_prefix} Summary: Found {len(iquery_candidates)} iquery.pl links: {[f'#{num}' for num, _, _ in iquery_candidates]}")
            else:
                self.log_warning(f"{log_prefix} Summary: NO iquery.pl links found on page")
            
            if query_candidates:
                self.log_info(f"{log_prefix} Summary: Found {len(query_candidates)} Query text links: {[f'#{num}' for num, _, _ in query_candidates]}")
            else:
                self.log_warning(f"{log_prefix} Summary: NO Query text links found on page")
            
            # Log current page title and URL for context
            try:
                page_title = await navigator.page.title()
                self.log_info(f"{log_prefix} Current page title: '{page_title}', URL: {navigator.page.url}")
            except Exception:
                pass
                    
        except Exception as e:
            self.log_debug(f"{log_prefix} Error logging available links: {e}")

    async def _try_direct_navigation(self, navigator, court_id):
        """
        Attempt direct navigation to the iquery.pl page using multiple URL patterns.
        This is often the most reliable method as it bypasses inconsistent link text.
        """
        log_prefix = f"[{court_id}] NavFacade:"
        
        try:
            # Extract base URL from current URL
            current_url = navigator.page.url
            self.log_debug(f"{log_prefix} Current URL for base extraction: {current_url}")
            
            # Parse the base URL (e.g., "https://ecf.ilnd.uscourts.gov")
            from urllib.parse import urlparse
            parsed_url = urlparse(current_url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            # Try multiple URL patterns for different PACER systems
            query_url_patterns = [
                f"{base_url}/cgi-bin/iquery.pl",           # Standard pattern
                f"{base_url}/cgi-bin/iQuery.pl",           # Capitalized Query
                f"{base_url}/cgi-bin/iqry.pl",             # Abbreviated
                f"{base_url}/cgi-bin/query.pl",            # Alternative naming
                f"{base_url}/scripts/iquery.pl",           # Alternative scripts path
            ]
            
            for query_url in query_url_patterns:
                try:
                    self.log_info(f"{log_prefix} Attempting direct navigation to: {query_url}")
                    
                    # Navigate directly with error handling
                    response = await navigator.page.goto(query_url, timeout=15000, wait_until="domcontentloaded")
                    
                    # Check if the response is valid (not 404, 500, etc.)
                    if response and response.status >= 400:
                        self.log_warning(f"{log_prefix} HTTP error {response.status} for URL: {query_url}")
                        continue
                    
                    # Give page time to load
                    await navigator.page.wait_for_timeout(1000)
                    
                    # Verify we're on the query page
                    new_url = navigator.page.url
                    if "iquery.pl" in new_url.lower() or "query.pl" in new_url.lower():
                        self.log_info(f"{log_prefix} Successfully navigated to query page via direct navigation: {new_url}")
                        
                        # Additional verification: check if we have query form elements
                        try:
                            case_input = navigator.page.locator('input[name=\"case_num\"]')
                            if await case_input.count() > 0:
                                self.log_info(f"{log_prefix} Confirmed query form is present on page")
                                return True
                            else:
                                self.log_debug(f"{log_prefix} Query form not found, but URL looks correct")
                        except Exception:
                            pass  # Form check is just additional verification
                        
                        return True
                    else:
                        self.log_warning(f"{log_prefix} Navigation succeeded but URL is unexpected: {new_url}")
                        continue
                        
                except Exception as url_error:
                    self.log_debug(f"{log_prefix} Failed to navigate to {query_url}: {url_error}")
                    continue
            
            # If all URL patterns failed
            self.log_error(f"{log_prefix} All direct navigation URL patterns failed")
            return False
                
        except Exception as e:
            self.log_error(f"{log_prefix} Direct navigation failed with exception: {e}")
            try:
                await navigator.save_screenshot(f"query_navigation_failed_{court_id}")
            except Exception:
                pass  # Don't let screenshot failure prevent returning the error
            return False
    
    # Keep go_to_query_page as alias for backward compatibility
    async def go_to_query_page(self, **kwargs) -> bool:
        """Backward compatibility alias for navigate_to_query_page"""
        return await self.navigate_to_query_page(**kwargs)

    async def navigate_to_civil_cases_report(self, **kwargs) -> bool:
        """
        Navigates to the civil cases report page.
        Used when starting fresh (no existing docket_report_log).
        
        Args:
            navigator: PacerNavigator instance
            court_id: Court identifier
            
        Returns:
            bool: True if navigation successful
        """
        navigator = kwargs.get("navigator")
        court_id = kwargs.get("court_id")
        
        log_prefix = f"[{court_id}] NavFacade:"
        self.log_info(f"{log_prefix} Navigating to civil cases report page.")
        
        try:
            # Navigate to Reports menu first
            await self.page_navigator.perform_action({
                "action": "click_element",
                "navigator": navigator,
                "element_name": "reports_link"
            })
            
            # Then navigate to Civil Cases
            await self.page_navigator.perform_action({
                "action": "click_element", 
                "navigator": navigator,
                "element_name": "civil_cases_link"
            })
            
            self.log_info(f"{log_prefix} Successfully navigated to civil cases report page.")
            return True
            
        except Exception as e:
            self.log_error(f"{log_prefix} Navigation failed: {e}", exc_info=True)
            return False

    async def navigate_to_reports(self, **kwargs) -> bool:
        """
        Navigates to the reports section.
        
        Args:
            navigator: PacerNavigator instance
            court_id: Court identifier
            
        Returns:
            bool: True if navigation successful
        """
        navigator = kwargs.get("navigator")
        court_id = kwargs.get("court_id")
        
        log_prefix = f"[{court_id}] NavFacade:"
        self.log_info(f"{log_prefix} Navigating to reports section.")
        
        try:
            await self.page_navigator.perform_action({
                "action": "click_element",
                "navigator": navigator,
                "element_name": "reports_link"
            })
            
            self.log_info(f"{log_prefix} Successfully navigated to reports section.")
            return True
            
        except Exception as e:
            self.log_error(f"{log_prefix} Reports navigation failed: {e}", exc_info=True)
            return False

    async def auto_populate_document_range(self, **kwargs) -> bool:
        """
        Auto-populates document range fields with default values (1 to 1).
        
        Args:
            navigator: PacerNavigator instance
            court_id: Court identifier
            
        Returns:
            bool: True if auto-population successful
        """
        navigator = kwargs.get("navigator")
        court_id = kwargs.get("court_id")
        
        log_prefix = f"[{court_id}] NavFacade:"
        self.log_info(f"{log_prefix} Auto-populating document range fields with defaults.")
        
        try:
            # Get default values for document range fields
            defaults = get_document_range_defaults()
            
            # Fill documents_numbered_from_ field
            try:
                await self.page_navigator.perform_action({
                    "action": "fill_element",
                    "navigator": navigator,
                    "element_name": "documents_numbered_from",
                    "value": defaults['documents_numbered_from_']
                })
                self.log_info(f"{log_prefix} Filled documents_numbered_from_ with '{defaults['documents_numbered_from_']}'")
            except Exception as e:
                self.log_warning(f"{log_prefix} Could not fill documents_numbered_from_ field: {e}")
            
            # Fill documents_numbered_to_ field
            try:
                await self.page_navigator.perform_action({
                    "action": "fill_element",
                    "navigator": navigator,
                    "element_name": "documents_numbered_to",
                    "value": defaults['documents_numbered_to_']
                })
                self.log_info(f"{log_prefix} Filled documents_numbered_to_ with '{defaults['documents_numbered_to_']}'")
            except Exception as e:
                self.log_warning(f"{log_prefix} Could not fill documents_numbered_to_ field: {e}")
            
            self.log_info(f"{log_prefix} Document range auto-population completed successfully.")
            return True
            
        except Exception as e:
            self.log_error(f"{log_prefix} Document range auto-population failed: {e}", exc_info=True)
            return False

    async def fill_docket_query_form(self, **kwargs) -> bool:
        """
        Fills the docket query form with case number and auto-populates document ranges.
        
        Args:
            navigator: PacerNavigator instance
            court_id: Court identifier
            case_number: Raw case/docket number
            
        Returns:
            bool: True if form filling successful
        """
        navigator = kwargs.get("navigator")
        court_id = kwargs.get("court_id")
        case_number = kwargs.get("case_number")
        
        log_prefix = f"[{court_id}] NavFacade:"
        self.log_info(f"{log_prefix} Filling docket query form for case: {case_number}")
        
        try:
            # Validate and format the docket number
            formatted_docket = validate_and_format_docket_number(case_number)
            if formatted_docket:
                self.log_info(f"{log_prefix} Formatted docket number: {case_number} -> {formatted_docket}")
                case_to_use = formatted_docket
            else:
                self.log_warning(f"{log_prefix} Could not format docket number, using original: {case_number}")
                case_to_use = case_number
            
            # Fill case number field if it exists
            try:
                await self.page_navigator.perform_action({
                    "action": "fill_element",
                    "navigator": navigator,
                    "element_name": "case_num_input",
                    "value": case_to_use
                })
                self.log_info(f"{log_prefix} Filled case number field with: {case_to_use}")
            except Exception as e:
                self.log_warning(f"{log_prefix} Could not fill case number field: {e}")
            
            # Auto-populate document range fields
            await self.auto_populate_document_range(navigator=navigator, court_id=court_id)
            
            self.log_info(f"{log_prefix} Docket query form filling completed successfully.")
            return True
            
        except Exception as e:
            self.log_error(f"{log_prefix} Docket query form filling failed: {e}", exc_info=True)
            return False

    async def query_docket_and_get_page(self, **kwargs) -> bool:
        """
        Performs the exact sequence of actions for finding a case as per legacy spec:
        1. Fill input[name='case_num'] with case number
        2. Trigger a blur event on the input
        3. Click "Find This Case" button
        4. Click "Run Query" button  
        5. Click "Docket Report" link
        
        Args:
            navigator: PacerNavigator instance
            court_id: Court identifier
            case_number: Case/docket number to query
            
        Returns:
            bool: True if workflow successful
        """
        navigator = kwargs.get("navigator")
        court_id = kwargs.get("court_id")
        case_number = kwargs.get("case_number")
        
        log_prefix = f"[{court_id}] NavFacade:"
        self.log_info(f"{log_prefix} Starting query_docket_and_get_page for case: {case_number}")
        
        try:
            # Step 1: Fill input[name='case_num'] with case number
            self.log_info(f"{log_prefix} Step 1: Filling case_num input field")
            
            # Validate and format the docket number
            formatted_docket = validate_and_format_docket_number(case_number)
            if formatted_docket:
                self.log_info(f"{log_prefix} Formatted docket number: {case_number} -> {formatted_docket}")
                case_to_use = formatted_docket
            else:
                self.log_warning(f"{log_prefix} Could not format docket number, using original: {case_number}")
                case_to_use = case_number
            
            # Fill the case_num input field
            case_input = navigator.page.locator('input[name="case_num"]')
            await case_input.fill(case_to_use)
            self.log_info(f"{log_prefix} Filled case_num input with: {case_to_use}")
            
            # Step 2: Trigger a blur event on the input (as per legacy spec)
            self.log_info(f"{log_prefix} Step 2: Triggering blur event on case_num input")
            await case_input.dispatch_event('blur')
            self.log_info(f"{log_prefix} Blur event triggered on case_num input")
            
            # Brief wait for JavaScript to process the blur event
            await navigator.page.wait_for_timeout(500)
            
            # Step 3: Click "Find This Case" button
            self.log_info(f"{log_prefix} Step 3: Clicking 'Find This Case' button")
            
            find_case_button = navigator.page.locator('input[value="Find This Case"]')
            if await find_case_button.count() > 0:
                await find_case_button.first.click()
                self.log_info(f"{log_prefix} Successfully clicked 'Find This Case' button")
            else:
                # Try alternative selectors
                self.log_warning(f"{log_prefix} Primary selector failed, trying alternatives")
                alternative_patterns = [
                    'input#case_number_find_button_0[value="Find This Case"]',
                    'input[type="button"][value*="Find"]',
                    'button[value="Find This Case"]'
                ]
                
                clicked = False
                for pattern in alternative_patterns:
                    alt_button = navigator.page.locator(pattern)
                    if await alt_button.count() > 0:
                        await alt_button.first.click()
                        self.log_info(f"{log_prefix} Clicked 'Find This Case' with pattern: {pattern}")
                        clicked = True
                        break
                
                if not clicked:
                    self.log_error(f"{log_prefix} Could not find 'Find This Case' button")
                    return False
            
            # Wait for Run Query button to appear
            await navigator.page.wait_for_timeout(500)
            
            # Step 4: Click "Run Query" button
            self.log_info(f"{log_prefix} Step 4: Clicking 'Run Query' button")
            
            run_query_button = navigator.page.locator('input[value="Run Query"]')
            if await run_query_button.count() > 0:
                await run_query_button.first.click()
                self.log_info(f"{log_prefix} Successfully clicked 'Run Query' button")
            else:
                # Try alternative selectors
                self.log_warning(f"{log_prefix} Primary selector failed, trying alternatives")
                alternative_patterns = [
                    'button[value="Run Query"]',
                    'input[type="submit"][value="Run Query"]',
                    'input[type="button"][value="Run Query"]'
                ]
                
                clicked = False
                for pattern in alternative_patterns:
                    alt_button = navigator.page.locator(pattern)
                    if await alt_button.count() > 0:
                        await alt_button.first.click()
                        self.log_info(f"{log_prefix} Clicked 'Run Query' with pattern: {pattern}")
                        clicked = True
                        break
                
                if not clicked:
                    self.log_error(f"{log_prefix} Could not find 'Run Query' button")
                    return False
            
            # Wait for page to load after Run Query
            await navigator.page.wait_for_load_state('domcontentloaded')
            await navigator.page.wait_for_timeout(1000)
            
            # Step 5: Click "Docket Report" link
            self.log_info(f"{log_prefix} Step 5: Clicking 'Docket Report' link")
            
            docket_link = navigator.page.locator('a[href*="DktRpt.pl"]')
            if await docket_link.count() > 0:
                await docket_link.first.click()
                self.log_info(f"{log_prefix} Successfully clicked 'Docket Report' link")
            else:
                # Try alternative selectors
                self.log_warning(f"{log_prefix} Primary selector failed, trying alternatives")
                alternative_patterns = [
                    'a:has-text("Docket Report")',
                    'a:text-matches("Docket Report", "i")',
                    'a[href*="/cgi-bin/DktRpt"]'
                ]
                
                clicked = False
                for pattern in alternative_patterns:
                    alt_link = navigator.page.locator(pattern)
                    if await alt_link.count() > 0:
                        await alt_link.first.click()
                        self.log_info(f"{log_prefix} Clicked 'Docket Report' with pattern: {pattern}")
                        clicked = True
                        break
                
                if not clicked:
                    self.log_error(f"{log_prefix} Could not find 'Docket Report' link")
                    return False
            
            # Wait for navigation to complete
            await navigator.page.wait_for_load_state('domcontentloaded')
            self.log_info(f"{log_prefix} Successfully completed query_docket_and_get_page workflow")
            
            return True
            
        except Exception as e:
            self.log_error(f"{log_prefix} query_docket_and_get_page failed: {e}", exc_info=True)
            return False
    
    async def check_docket_report_log_exists(self, court_id: str, iso_date: str) -> bool:
        """
        Check if a docket report log file exists for the given court and date.
        
        Args:
            court_id: Court identifier (e.g., 'ilnd', 'cand')
            iso_date: Date in YYYYMMDD format
            
        Returns:
            bool: True if docket report log exists and has cases, False otherwise
        """
        import os
        import json
        
        # Construct the path using lowercase court_id
        log_path = f"data/{iso_date}/logs/docket_report/{court_id.lower()}.json"
        
        self.log_info(f"[{court_id}] Checking for docket report log at: {log_path}")
        
        if not os.path.exists(log_path):
            self.log_info(f"[{court_id}] No docket report log found")
            return False
        
        try:
            with open(log_path, 'r') as f:
                data = json.load(f)
                cases = data.get('cases', [])
                
                if cases:
                    self.log_info(f"[{court_id}] Found docket report log with {len(cases)} cases - will use resume mode")
                    return True
                else:
                    self.log_info(f"[{court_id}] Docket report log exists but has 0 cases - will generate new report")
                    return False
        except Exception as e:
            self.log_error(f"[{court_id}] Error reading docket report log: {e}")
            return False
    
    async def determine_navigation_strategy(self, court_id: str, iso_date: str, **kwargs) -> Dict[str, Any]:
        """
        Determine the navigation strategy based on docket report log existence.
        
        Args:
            court_id: Court identifier
            iso_date: Date in YYYYMMDD format
            
        Returns:
            Dict with strategy information including:
            - resume_mode: True if should resume from existing log
            - has_cases: True if log exists with cases
            - navigation_action: 'go_to_query_page' or 'navigate_to_civil_cases_report'
        """
        has_docket_log = await self.check_docket_report_log_exists(court_id, iso_date)
        
        strategy = {
            'resume_mode': has_docket_log,
            'has_cases': has_docket_log,
            'navigation_action': 'go_to_query_page' if has_docket_log else 'navigate_to_civil_cases_report'
        }
        
        self.log_info(f"[{court_id}] Navigation strategy determined: resume_mode={strategy['resume_mode']}, action={strategy['navigation_action']}")
        
        return strategy
    
    async def execute_complete_case_query_workflow(self, **kwargs) -> bool:
        """
        Legacy method preserved for backward compatibility.
        Now delegates to query_docket_and_get_page and adds document range population and Run Report.
        
        This method extends query_docket_and_get_page with:
        - Auto-populate document range fields (1 and 1) on Query Docket Sheet page
        - Click "Run Report" button to navigate to the docket sheet
        """
        # Extract required parameters from kwargs
        court_id = kwargs.get('court_id')
        case_number = kwargs.get('case_number')
        navigator = kwargs.get('navigator')
        
        if not court_id or not case_number or not navigator:
            self.log_error("Missing required parameters: court_id, case_number, and navigator")
            return False
        
        log_prefix = f"[{court_id}] NavFacade:"
        self.log_info(f"{log_prefix} Executing complete case query workflow for case: {case_number}")
        
        try:
            # Step 1: Enter case number in the input field
            self.log_info(f"{log_prefix} Step 1: Entering case number...")
            
            # Validate and format the docket number
            formatted_docket = validate_and_format_docket_number(case_number)
            if formatted_docket:
                self.log_info(f"{log_prefix} Formatted docket number: {case_number} -> {formatted_docket}")
                case_to_use = formatted_docket
            else:
                self.log_warning(f"{log_prefix} Could not format docket number, using original: {case_number}")
                case_to_use = case_number
            
            await self.page_navigator.perform_action({
                "action": "fill_element",
                "navigator": navigator,
                "element_name": "case_num_input",
                "value": case_to_use
            })
            
            # Brief wait for JavaScript to enable buttons
            await navigator.page.wait_for_timeout(500)
            
            # Step 2: Click "Find This Case" button
            self.log_info(f"{log_prefix} Step 2: Clicking 'Find This Case' button...")
            
            try:
                await self.page_navigator.perform_action({
                    "action": "click_element",
                    "navigator": navigator,
                    "element_name": "find_this_case_button"
                })
                self.log_info(f"{log_prefix} Successfully clicked 'Find This Case' button")
            except Exception as e:
                # Fallback to multiple patterns if standardized selector fails
                self.log_warning(f"{log_prefix} Standard selector failed, trying fallback patterns: {e}")
                
                find_case_patterns = [
                    'input[value="Find This Case"]',
                    'input#case_number_find_button_0[value="Find This Case"]',
                    'input[type="button"][value*="Find"]',
                    'input[name*="find"][value*="Case"]'
                ]
                
                find_case_clicked = False
                for pattern in find_case_patterns:
                    try:
                        find_case_locator = navigator.page.locator(pattern)
                        if await find_case_locator.count() > 0:
                            self.log_info(f"{log_prefix} Found 'Find This Case' button with pattern: {pattern}")
                            await find_case_locator.first.click()
                            find_case_clicked = True
                            break
                    except Exception as e2:
                        self.log_debug(f"{log_prefix} Pattern {pattern} failed: {e2}")
                        continue
                
                if not find_case_clicked:
                    self.log_error(f"{log_prefix} Could not find 'Find This Case' button with any pattern")
                    return False
            
            # Brief wait for Run Query button to appear
            await navigator.page.wait_for_timeout(500)
            
            # Step 3: Click "Run Query" button
            self.log_info(f"{log_prefix} Step 3: Clicking 'Run Query' button...")
            
            await self.page_navigator.perform_action({
                "action": "click_element",
                "navigator": navigator,
                "element_name": "run_query_button"
            })
            
            # Wait for page to load after clicking Run Query
            await navigator.page.wait_for_load_state('domcontentloaded')
            await navigator.page.wait_for_timeout(1000)  # Give page time to fully render
            
            # Step 4: Click on "Docket Report ..." link (e.g., <a href="/cgi-bin/DktRpt.pl?484018">Docket Report ...</a>)
            self.log_info(f"{log_prefix} Step 4: Looking for and clicking 'Docket Report' link...")
            
            try:
                # Try primary selector first
                await self.page_navigator.perform_action({
                    "action": "click_element",
                    "navigator": navigator,
                    "element_name": "docket_report_link"
                })
                self.log_info(f"{log_prefix} Successfully clicked 'Docket Report' link")
            except Exception as e:
                # Fallback to alternative selectors
                self.log_warning(f"{log_prefix} Primary selector failed, trying alternatives: {e}")
                
                docket_link_patterns = [
                    'a[href*="DktRpt.pl"]',  # Link containing DktRpt.pl
                    'a:has-text("Docket Report")',  # Link with Docket Report text
                    'a:text-matches("Docket Report", "i")',  # Case-insensitive match
                    'a[href*="/cgi-bin/DktRpt"]',  # Broader match
                ]
                
                docket_link_clicked = False
                for pattern in docket_link_patterns:
                    try:
                        docket_locator = navigator.page.locator(pattern)
                        if await docket_locator.count() > 0:
                            self.log_info(f"{log_prefix} Found 'Docket Report' link with pattern: {pattern}")
                            await docket_locator.first.click()
                            docket_link_clicked = True
                            break
                    except Exception as e2:
                        self.log_debug(f"{log_prefix} Pattern {pattern} failed: {e2}")
                        continue
                
                if not docket_link_clicked:
                    self.log_error(f"{log_prefix} Could not find 'Docket Report' link with any pattern")
                    return False
            
            # Wait for page to load to the Query Docket Sheet
            await navigator.page.wait_for_load_state('domcontentloaded')
            self.log_info(f"{log_prefix} Successfully navigated to Query Docket Sheet page")
            
            # Step 5: Auto-populate document range fields (now on correct page)
            self.log_info(f"{log_prefix} Step 5: Auto-populating document range fields...")
            
            success = await self.auto_populate_document_range(
                navigator=navigator, 
                court_id=court_id
            )
            
            if not success:
                self.log_warning(f"{log_prefix} Document range auto-population had issues, continuing...")
            
            # Add delay to ensure JavaScript activation after document range population
            import asyncio
            await asyncio.sleep(1.0)  # Wait 1 second for JavaScript to fully activate
            self.log_debug(f"{log_prefix} Waited for JavaScript activation after document range population")
            
            # Step 6: Click "Run Report" button to navigate to the docket sheet
            self.log_info(f"{log_prefix} Step 6: Clicking 'Run Report' button to get docket sheet...")
            
            try:
                # Try primary selector first
                await self.page_navigator.perform_action({
                    "action": "click_element",
                    "navigator": navigator,
                    "element_name": "run_report_button"
                })
                self.log_info(f"{log_prefix} Successfully clicked 'Run Report' button")
            except Exception as e:
                # Try alternative selector without type attribute
                self.log_warning(f"{log_prefix} Primary selector failed, trying alternative: {e}")
                try:
                    await self.page_navigator.perform_action({
                        "action": "click_element",
                        "navigator": navigator,
                        "element_name": "run_report_button_alt"
                    })
                    self.log_info(f"{log_prefix} Successfully clicked 'Run Report' button with alternative selector")
                except Exception as e2:
                    # Fallback to direct locator patterns
                    self.log_warning(f"{log_prefix} Alternative selector failed, trying direct patterns: {e2}")
                    
                    run_report_patterns = [
                        'input[value="Run Report"]',
                        'button[value="Run Report"]',
                        'input[type="submit"][value="Run Report"]',
                        'input[type="button"][value="Run Report"]',
                        'input[name="button1"][value="Run Report"]'
                    ]
                    
                    run_report_clicked = False
                    for pattern in run_report_patterns:
                        try:
                            run_report_locator = navigator.page.locator(pattern)
                            if await run_report_locator.count() > 0:
                                self.log_info(f"{log_prefix} Found 'Run Report' button with pattern: {pattern}")
                                await run_report_locator.first.click()
                                run_report_clicked = True
                                break
                        except Exception as e3:
                            self.log_debug(f"{log_prefix} Pattern {pattern} failed: {e3}")
                            continue
                    
                    if not run_report_clicked:
                        self.log_error(f"{log_prefix} Could not click 'Run Report' button with any pattern")
                        return False
            
            # Wait for docket sheet to load
            await navigator.page.wait_for_load_state('domcontentloaded')
            self.log_info(f"{log_prefix} Successfully navigated to docket sheet")
            
            self.log_info(f"{log_prefix} Complete case query workflow executed successfully.")
            return True
                
        except Exception as e:
            self.log_error(f"{log_prefix} Complete case query workflow failed: {e}", exc_info=True)
            return False