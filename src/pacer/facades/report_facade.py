# src/pacer/facades/report_facade.py
import re
from typing import Any, Dict, List, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.components.report.report_generator import ReportGenerator


class ReportFacade(AsyncServiceBase):
    """
    Facade for all report-related operations in PACER.
    Provides a unified interface for report generation and data extraction.
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        report_generator: Optional[ReportGenerator] = None,
        court_logger: Optional[Any] = None,
    ):
        super().__init__(logger, config)
        # Pass court_logger to report_generator if provided
        self.court_logger = court_logger
        
        # CRITICAL: If court_logger is provided, replace this facade's logger too
        if court_logger:
            self.logger = court_logger
            self.log_debug("ReportFacade: Using court-specific logger")
            
        self.report_generator = report_generator or ReportGenerator(
            logger=logger, 
            config=config, 
            court_logger=court_logger  # ReportGenerator correctly accepts court_logger parameter
        )
    
    def set_court_logger(self, court_id: str, iso_date: str) -> None:
        """
        Set up court-specific logging for both facade and report generator.
        
        Args:
            court_id: Court identifier (e.g., 'cand', 'nysd')
            iso_date: ISO date string for directory organization
        """
        try:
            # Create court logger using component_base functionality
            court_logger = self.create_court_logger(court_id, iso_date)
            self.court_logger = court_logger
            
            # Update report generator's logger too
            if hasattr(self.report_generator, 'set_court_logger'):
                self.report_generator.set_court_logger(court_id, iso_date)
            else:
                self.report_generator.logger = court_logger
                
            self.log_info(f"[{court_id.upper()}] ReportFacade: Court logger configured - all logs will go to data/{iso_date}/logs/pacer/{court_id.lower()}.log")
        except Exception as e:
            self.log_error(f"Failed to create court logger for {court_id}: {e}", exc_info=True)

    async def execute(self, data: Any) -> Any:
        """Main execution method for the facade."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """Routes actions to the appropriate report components."""
        action = data.get("action")
        
        if action == "generate_civil_cases_report":
            return await self.generate_civil_cases_report(**data)
        elif action == "extract_report_data":
            return await self.extract_report_data(**data)
        elif action == "configure_case_filed_report":
            return await self.configure_case_filed_report(**data)
        elif action == "extract_and_save_report_list":
            return await self._extract_and_save_report_list(**data)
        else:
            raise ValueError(f"Unknown action for ReportFacade: {action}")

    async def generate_civil_cases_report(self, **kwargs) -> bool:
        """
        Generates a civil cases report for the specified date range.
        
        Args:
            navigator: PacerNavigator instance
            court_id: Court identifier
            from_date_str: Start date string (MM/DD/YY format)
            to_date_str: End date string (MM/DD/YY format)
            ignore_download_service: Service for handling downloads
            iso_date: ISO date string (YYYYMMDD format) for directory paths
            
        Returns:
            bool: True if cases were found, False otherwise
        """
        navigator = kwargs.get("navigator")
        court_id = kwargs.get("court_id")
        from_date_str = kwargs.get("from_date_str")
        to_date_str = kwargs.get("to_date_str")
        ignore_download_service = kwargs.get("ignore_download_service")
        iso_date = kwargs.get("iso_date")  # Get iso_date from kwargs
        court_logger = kwargs.get("court_logger")  # Check for passed court logger
        
        # CRITICAL: Set up court-specific logging if not already configured
        if court_logger and not self.court_logger:
            self.court_logger = court_logger
            self.logger = court_logger
            self.log_debug(f"[{court_id.upper()}] ReportFacade: Using provided court logger")
        elif not self.court_logger and court_id and iso_date:
            self.set_court_logger(court_id, iso_date)
        
        log_prefix = f"[{court_id}] ReportFacade:"
        self.log_info(f"{log_prefix} Generating civil cases report for {from_date_str} to {to_date_str}.")
        
        try:
            # Use the report generator component
            has_cases = await self.report_generator.execute({
                "action": "generate_civil_cases_report",
                "navigator": navigator,
                "court_id": court_id,
                "from_date_str": from_date_str,
                "to_date_str": to_date_str,
                "ignore_download_service": ignore_download_service,
                "iso_date": iso_date,  # Pass iso_date to report generator
                "court_logger": self.court_logger  # Pass court logger for proper logging
            })
            
            if has_cases:
                self.log_info(f"{log_prefix} Report generated successfully with cases found.")
            else:
                self.log_info(f"{log_prefix} Report generated but no cases found for date range.")
                
            return has_cases
            
        except Exception as e:
            self.log_error(f"{log_prefix} Report generation failed: {e}", exc_info=True)
            return False

    async def extract_report_data(self, **kwargs) -> list:
        """
        Extracts data from a generated report page.
        
        Args:
            navigator: PacerNavigator instance
            court_id: Court identifier
            
        Returns:
            list: Extracted report data
        """
        navigator = kwargs.get("navigator")
        court_id = kwargs.get("court_id")
        iso_date = kwargs.get("iso_date")
        court_logger = kwargs.get("court_logger")
        
        # CRITICAL: Set up court-specific logging if not already configured
        if court_logger and not self.court_logger:
            self.court_logger = court_logger
            self.logger = court_logger
            self.log_debug(f"[{court_id.upper()}] ReportFacade: Using provided court logger for data extraction")
        elif not self.court_logger and court_id and iso_date:
            self.set_court_logger(court_id, iso_date)
        
        log_prefix = f"[{court_id}] ReportFacade:"
        self.log_info(f"{log_prefix} Extracting report data.")
        
        try:
            # Use the report generator component for data extraction
            report_data = await self.report_generator.extract_report_data(
                navigator=navigator,
                court_id=court_id,
                iso_date=iso_date,
                court_logger=self.court_logger
            )
            
            self.log_info(f"{log_prefix} Extracted {len(report_data)} report records.")
            return report_data
            
        except Exception as e:
            self.log_error(f"{log_prefix} Report data extraction failed: {e}", exc_info=True)
            return []

    async def configure_case_filed_report(self, **kwargs) -> bool:
        """
        Legacy API method: Configure the case filed report form.
        Implements EXACT Step 2.5 behavior from legacy_pacer_spec.md.
        
        Args:
            navigator: PacerNavigator instance
            from_date_str: Start date string (MM/DD/YY format)
            to_date_str: End date string (MM/DD/YY format)
            court_id: Court identifier
            iso_date: ISO date string for logging
            
        Returns:
            bool: True if configuration successful
        """
        navigator = kwargs.get("navigator")
        from_date_str = kwargs.get("from_date_str")
        to_date_str = kwargs.get("to_date_str")
        court_id = kwargs.get("court_id")
        iso_date = kwargs.get("iso_date")
        court_logger = kwargs.get("court_logger")
        
        # Set up court-specific logging if provided
        if court_logger and not self.court_logger:
            self.court_logger = court_logger
            self.logger = court_logger
        elif not self.court_logger and court_id and iso_date:
            self.set_court_logger(court_id, iso_date)
        
        log_prefix = f"[{court_id}] ReportFacade:"
        
        try:
            self.log_info(f"{log_prefix} Configuring case filed report for {from_date_str} to {to_date_str}")
            
            # Step 1: Click "Reports" link
            self.log_info(f"{log_prefix} Navigating to Reports menu...")
            reports_link = navigator.page.locator("a:text('Reports')")
            await reports_link.click()
            await navigator.page.wait_for_load_state("networkidle")
            self.log_info(f"{log_prefix} Successfully clicked Reports link")
            
            # Step 2: Click "Civil Cases" link
            self.log_info(f"{log_prefix} Navigating to Civil Cases...")
            civil_cases_link = navigator.page.locator("a:text('Civil Cases')")
            await civil_cases_link.click()
            await navigator.page.wait_for_load_state("networkidle")
            self.log_info(f"{log_prefix} Successfully clicked Civil Cases link")
            
            # Step 3: Fill date fields
            self.log_info(f"{log_prefix} Filling date fields...")
            await navigator.page.fill("input[name='filed_from']", from_date_str)
            await navigator.page.fill("input[name='filed_to']", to_date_str)
            self.log_info(f"{log_prefix} Date fields filled: {from_date_str} to {to_date_str}")
            
            # Step 4: Get case types and NOS codes from config if available
            case_types = kwargs.get("case_types", [])
            nos_codes = kwargs.get("nos_codes", [])
            
            # Configure dropdowns if specified
            if nos_codes:
                self.log_info(f"{log_prefix} Selecting nature of suit codes...")
                for nos_code in nos_codes:
                    await navigator.page.select_option("select[name='nature_of_suit']", value=nos_code)
                    
            if case_types:
                self.log_info(f"{log_prefix} Selecting case types...")
                for case_type in case_types:
                    await navigator.page.select_option("select[name='case_type']", value=case_type)
            
            # Step 5: Click "Run Report" button
            self.log_info(f"{log_prefix} Clicking Run Report button...")
            run_report_button = navigator.page.locator("input[value='Run Report']")
            await run_report_button.click()
            await navigator.page.wait_for_load_state("networkidle")
            self.log_info(f"{log_prefix} Report configuration completed successfully")
            
            return True
            
        except Exception as e:
            self.log_error(f"{log_prefix} Failed to configure case filed report: {e}", exc_info=True)
            return False

    async def _extract_and_save_report_list(self, **kwargs) -> List[Dict]:
        """
        Legacy API method: Extract report data and save to JSON file.
        Implements EXACT Step 2.6 behavior from legacy_pacer_spec.md.
        
        Args:
            navigator: PacerNavigator instance
            court_id: Court identifier
            iso_date: ISO date string (YYYYMMDD format)
            
        Returns:
            list: Extracted report data in legacy format
        """
        import json
        import os
        from datetime import datetime
        
        navigator = kwargs.get("navigator")
        court_id = kwargs.get("court_id")
        iso_date = kwargs.get("iso_date")
        court_logger = kwargs.get("court_logger")
        
        # Set up court-specific logging if provided
        if court_logger and not self.court_logger:
            self.court_logger = court_logger
            self.logger = court_logger
        elif not self.court_logger and court_id and iso_date:
            self.set_court_logger(court_id, iso_date)
        
        log_prefix = f"[{court_id}] ReportFacade:"
        
        try:
            self.log_info(f"{log_prefix} Extracting and saving report list...")
            
            # Extract data from HTML table
            report_data = []
            table_rows = navigator.page.locator("table tr")
            row_count = await table_rows.count()
            
            self.log_info(f"{log_prefix} Found {row_count} rows in report table")
            
            # Skip header row, process data rows
            for i in range(1, row_count):
                row = table_rows.nth(i)
                cells = row.locator("td")
                cell_count = await cells.count()
                
                if cell_count >= 5:  # Ensure we have enough cells
                    # Extract data matching legacy structure from Section 3.1
                    case_data = {
                        "court_id": court_id,
                        "docket_num": await cells.nth(0).text_content(),
                        "versus": await cells.nth(1).text_content(),
                        "filing_date": await cells.nth(2).text_content(),
                        "docket_link": await cells.nth(0).locator("a").get_attribute("href") if await cells.nth(0).locator("a").count() > 0 else None,
                        "extracted_at": datetime.now().isoformat(),
                        "source": "PACER Case Report"
                    }
                    
                    # Parse additional fields if available
                    if cell_count >= 6:
                        case_data["cause"] = await cells.nth(3).text_content()
                        case_data["nos"] = await cells.nth(4).text_content()
                        
                    # Extract case flags if present
                    if cell_count >= 7:
                        flags_text = await cells.nth(5).text_content()
                        if flags_text:
                            case_data["case_flags"] = flags_text
                            case_data["flags"] = [flags_text]
                            
                            # Extract MDL number if present
                            mdl_match = re.search(r'MDL\s+(\d+)', flags_text)
                            if mdl_match:
                                case_data["mdl_num"] = mdl_match.group(1)
                    
                    # Parse defendants from versus field
                    versus_text = case_data.get("versus", "")
                    if " v. " in versus_text:
                        defendant_part = versus_text.split(" v. ", 1)[1]
                        case_data["defendants"] = [{"name": defendant_part.strip()}]
                    
                    report_data.append(case_data)
            
            self.log_info(f"{log_prefix} Extracted {len(report_data)} cases from report")
            
            # Save to EXACT legacy path: data/{iso_date}/logs/docket_report/{court_id}.json
            json_dir = os.path.join("data", iso_date, "logs", "docket_report")
            os.makedirs(json_dir, exist_ok=True)
            
            json_path = os.path.join(json_dir, f"{court_id}.json")
            
            with open(json_path, "w") as f:
                json.dump(report_data, f, indent=2)
            
            self.log_info(f"{log_prefix} Saved report data to {json_path}")
            
            return report_data
            
        except Exception as e:
            self.log_error(f"{log_prefix} Failed to extract and save report list: {e}", exc_info=True)
            return []