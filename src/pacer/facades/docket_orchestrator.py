"""
PACER Workflow Orchestrator for Phase-Separated Processing.

This orchestrator coordinates the complete PACER workflow through distinct phases:
Phase 1: Court Processing → Phase 2: Docket Processing → Phase 3: Row Processing → Phase 4: Download Workflow
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, List
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.components.processing.court_processor import CourtProcessor
from src.pacer.components.processing.docket_processor import DocketProcessor
from src.pacer.components.processing.row_processor import RowProcessor
from src.pacer.components.download.download_manager import DownloadManager
from src.pacer.services.file_operations_service import FileOperationsService
from src.pacer.facades.navigation_facade import NavigationFacade


class DocketOrchestrator(AsyncServiceBase):
    """
    Orchestrates the complete PACER workflow through distinct phases.
    
    This orchestrator implements clean phase separation:
    1. Court Processing - Authentication, configuration, docket discovery
    2. Docket Processing - HTML parsing, case extraction  
    3. Row Processing - Business rules, classification, download decisions
    4. Download Workflow - Document downloads for qualified cases
    
    Each phase is cleanly separated and testable independently.
    """

    def __init__(
        self,
        court_processor: Optional[CourtProcessor] = None,
        docket_processor: Optional[DocketProcessor] = None,
        row_processor: Optional[RowProcessor] = None,
        download_manager: Optional[DownloadManager] = None,
        file_operations_service: Optional[FileOperationsService] = None,
        navigation_facade: Optional[NavigationFacade] = None,
        report_facade: Optional[Any] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        pacer_repository: Optional[Any] = None,
        async_dynamodb_storage: Optional[Any] = None,
    ):
        super().__init__(logger, config)
        self.court_processor = court_processor
        self.docket_processor = docket_processor
        self.row_processor = row_processor
        self.download_manager = download_manager
        self.file_operations_service = file_operations_service
        self.navigation_facade = navigation_facade
        self.report_facade = report_facade
        self.pacer_repository = pacer_repository
        self.async_dynamodb_storage = async_dynamodb_storage
        self._ignore_download_config = None

    async def _initialize_service(self) -> None:
        """Initialize the workflow orchestrator and validate dependencies."""
        if self._initialized:
            return
        
        # Validate required processors
        required_processors = []
        if not self.court_processor:
            required_processors.append("CourtProcessor")
        if not self.docket_processor:
            required_processors.append("DocketProcessor")
        if not self.row_processor:
            required_processors.append("RowProcessor")
        if not self.download_manager:
            required_processors.append("DownloadManager")
        if not self.file_operations_service:
            required_processors.append("FileOperationsService")
        
        if required_processors:
            raise ValueError(f"Missing required processors for PacerWorkflowOrchestrator: {required_processors}")
        
        # Load ignore download configuration
        await self._load_ignore_download_config()
        
        self.log_info("PacerWorkflowOrchestrator initialized successfully")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate workflow methods."""
        action = data.get("action")
        
        if action == "execute_workflow":
            return await self.execute_workflow(
                court_ids=data.get("court_ids", []),
                workflow_config=data.get("workflow_config", {}),
                processor_config=data.get("processor_config", {})
            )
        elif action == "process_court":
            return await self.process_single_court(
                court_id=data.get("court_id"),
                workflow_config=data.get("workflow_config", {}),
                processor_config=data.get("processor_config", {})
            )
        elif action == "process_docket":
            return await self.process_single_docket(
                page=data.get("page"),
                initial_details=data.get("initial_details", {})
            )
        else:
            raise ValueError(f"Unknown action for PacerWorkflowOrchestrator: {action}")

    async def execute_court_processing_workflow(
        self,
        court_ids: List[str],
        iso_date: Optional[str] = None,
        start_date: Optional[Any] = None,
        end_date: Optional[Any] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Service layer compatible method for executing court processing workflow.
        
        Args:
            court_ids: List of court IDs to process
            iso_date: ISO formatted date
            start_date: Start date for processing
            end_date: End date for processing
            **kwargs: Additional workflow parameters
            
        Returns:
            Workflow execution results
        """
        # Prepare workflow configuration from kwargs
        workflow_config = {
            'iso_date': iso_date,
            'start_date': start_date,
            'end_date': end_date,
            **kwargs
        }
        
        # Call the main execute_workflow method
        return await self.execute_workflow(
            court_ids=court_ids,
            workflow_config=workflow_config,
            processor_config=kwargs.get('processor_config', {})
        )
    
    async def execute_workflow(
        self, 
        court_ids: List[str], 
        workflow_config: Optional[Dict[str, Any]] = None,
        processor_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute the complete PACER workflow with clear phase separation.
        
        Args:
            court_ids: List of court IDs to process
            workflow_config: Workflow configuration parameters
            processor_config: Additional processor configuration
            
        Returns:
            Aggregated results from all processing phases
        """
        workflow_config = workflow_config or {}
        processor_config = processor_config or {}
        
        self.log_info(f"Executing PACER workflow for {len(court_ids)} courts: {court_ids}")
        
        # Initialize results structure
        results = {
            'workflow_status': 'started',
            'total_courts': len(court_ids),
            'court_results': {},
            'phase_summaries': {
                'court_processing': {'courts': 0, 'dockets_discovered': 0},
                'docket_processing': {'dockets_processed': 0, 'cases_extracted': 0},
                'row_processing': {'rows_processed': 0, 'download_required': 0},
                'download_workflow': {'downloads_completed': 0, 'files_saved': 0}
            },
            'error_summary': {'courts_failed': 0, 'errors': []}
        }
        
        try:
            # Setup workflow environment
            await self._setup_workflow_environment(workflow_config)
            
            # Process each court through all phases
            for court_id in court_ids:
                try:
                    self.log_info(f"Starting workflow processing for court: {court_id}")
                    
                    court_result = await self.process_single_court(
                        court_id=court_id,
                        workflow_config=workflow_config,
                        processor_config=processor_config
                    )
                    
                    results['court_results'][court_id] = court_result
                    
                    # Update phase summaries
                    self._update_phase_summaries(results, court_result)
                    
                    self.log_info(f"Completed workflow processing for court: {court_id}")
                    
                except Exception as e:
                    error_msg = f"Workflow failed for court {court_id}: {e}"
                    self.log_error(error_msg)
                    
                    results['court_results'][court_id] = {
                        'status': 'failed',
                        'error': str(e),
                        'court_id': court_id
                    }
                    results['error_summary']['courts_failed'] += 1
                    results['error_summary']['errors'].append(error_msg)
            
            # Finalize results
            results['workflow_status'] = 'completed'
            successful_courts = len([r for r in results['court_results'].values() if r.get('status') == 'success'])
            
            self.log_info(f"PACER workflow completed: {successful_courts}/{len(court_ids)} courts successful")
            
            return results
            
        except Exception as e:
            self.log_error(f"Critical workflow error: {e}")
            results['workflow_status'] = 'failed'
            results['workflow_error'] = str(e)
            return results

    async def process_courts(
        self, 
        court_ids: List[str],
        context=None,  # Browser context from caller
        iso_date: Optional[str] = None,
        start_date: Optional[Any] = None,
        end_date: Optional[Any] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process multiple courts through the complete PACER workflow.
        
        This is the entry point for the legacy workflow implementation.
        
        Args:
            court_ids: List of court IDs to process
            context: Browser context (optional)
            iso_date: ISO date string for workflow processing
            start_date: Start date for processing
            end_date: End date for processing
            **kwargs: Additional parameters
            
        Returns:
            Aggregated results from all processing phases with legacy_final_data structure
        """
        self.log_info(f"Processing {len(court_ids)} courts")
        
        # Initialize configuration and browser if needed
        if not self.config:
            await self._load_configuration()
        
        # Initialize browser context if not provided
        if not context:
            # Create browser context using browser service
            if hasattr(self, 'browser_service') and self.browser_service:
                context_result = await self.browser_service.create_context({})
                context = context_result.get('context')
        
        # Process courts through the legacy workflow
        results = {
            'workflow_status': 'started',
            'total_courts': len(court_ids),
            'court_results': {},
            'legacy_final_data': [],
            'error_summary': {'courts_failed': 0, 'errors': []}
        }
        
        for court_id in court_ids:
            try:
                # Run the court report generation workflow for each court
                court_result = await self._run_court_report_generation_workflow(
                    court_id=court_id,
                    workflow_config={
                        'iso_date': iso_date,
                        'start_date': start_date,
                        'end_date': end_date,
                        'browser_context': context,
                        **kwargs
                    },
                    processor_config=kwargs.get('processor_config', {})
                )
                
                results['court_results'][court_id] = court_result
                
                # Add legacy data to results
                if court_result.get('legacy_final_data'):
                    results['legacy_final_data'].extend(court_result['legacy_final_data'])
                
                if court_result.get('status') != 'success':
                    results['error_summary']['courts_failed'] += 1
                    results['error_summary']['errors'].append(
                        f"Court {court_id}: {court_result.get('error', 'Unknown error')}"
                    )
                    
            except Exception as e:
                self.log_error(f"Failed to process court {court_id}: {e}")
                results['error_summary']['courts_failed'] += 1
                results['error_summary']['errors'].append(f"Court {court_id}: {str(e)}")
        
        results['workflow_status'] = 'completed'
        return results

    async def process_single_court(
        self, 
        court_id: str, 
        workflow_config: Dict[str, Any],
        processor_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process a single court through all workflow phases.
        
        This method now directly calls _run_court_report_generation_workflow.
        
        Args:
            court_id: Court identifier to process
            workflow_config: Workflow configuration
            processor_config: Processor configuration
            
        Returns:
            Complete processing results for the court with legacy_final_data structure
        """
        self.log_info(f"Processing court {court_id} through all workflow phases")
        
        # Direct call to the legacy workflow - no feature flags
        return await self._run_court_report_generation_workflow(
            court_id=court_id,
            workflow_config=workflow_config,
            processor_config=processor_config
        )
        
        result = {
            'court_id': court_id,
            'status': 'failed',
            'phases_completed': [],
            'phase_results': {}
        }
        
        try:
            # PHASE 1: Court Processing
            self.log_info(f"[{court_id}] Phase 1: Court Processing")
            
            court_result = await self.court_processor.perform_action({
                "action": "process_court",
                "court_id": court_id,
                "start_date": workflow_config.get('start_date'),
                "end_date": workflow_config.get('end_date'),
                "config": workflow_config,
                "browser_context": workflow_config.get('browser_context'),
                "iso_date": workflow_config.get('iso_date'),
                "share_context_with_next_phase": True  # Enable context sharing
            })
            
            result['phase_results']['court_processing'] = court_result
            
            if court_result.get('status') != 'success':
                result['error'] = f"Court processing failed: {court_result.get('error')}"
                return result
            
            result['phases_completed'].append('court_processing')
            discovered_dockets = court_result.get('dockets', [])
            
            if not discovered_dockets:
                result['status'] = 'success_no_dockets'
                result['message'] = f"No dockets discovered for court {court_id}"
                return result
            
            # PHASE 2: Docket Processing  
            self.log_info(f"[{court_id}] Phase 2: Docket Processing ({len(discovered_dockets)} dockets)")
            
            # ARCHITECTURAL FIX: Extract browser context from court_result, not workflow_config
            navigator = court_result.get('navigator')
            context = court_result.get('browser_context')  # Fixed: get from court_result
            context_id = court_result.get('context_id')
            
            if not context:
                # Fallback to workflow_config for backwards compatibility
                context = workflow_config.get('browser_context')
                self.log_warning(f"[{court_id}] Browser context not found in court_result, using fallback")
            
            docket_results = await self.process_dockets_for_court(
                court_id, 
                discovered_dockets, 
                workflow_config,
                navigator=navigator,
                context=context,
                court_result=court_result
            )
            
            result['phase_results']['docket_processing'] = {
                'total_dockets': len(discovered_dockets),
                'processed_dockets': len(docket_results),
                'docket_results': docket_results
            }
            result['phases_completed'].append('docket_processing')
            
            if not docket_results:
                result['status'] = 'success_no_cases'
                result['message'] = f"No cases extracted from dockets for court {court_id}"
                return result
            
            # PHASE 3: Row Processing
            self.log_info(f"[{court_id}] Phase 3: Row Processing ({len(docket_results)} cases)")
            
            row_results = await self.process_rows_for_dockets(docket_results)
            
            result['phase_results']['row_processing'] = {
                'total_rows': len(docket_results),
                'processed_rows': len(row_results),
                'download_required': len([r for r in row_results if r.get('requires_download')]),
                'row_results': row_results
            }
            result['phases_completed'].append('row_processing')
            
            # PHASE 4: Download Workflow
            download_candidates = [r for r in row_results if r.get('requires_download')]
            
            if download_candidates:
                self.log_info(f"[{court_id}] Phase 4: Download Workflow ({len(download_candidates)} downloads)")
                
                download_results = await self.execute_downloads_for_rows(download_candidates, workflow_config)
                
                result['phase_results']['download_workflow'] = {
                    'total_candidates': len(download_candidates),
                    'completed_downloads': len(download_results),
                    'download_results': download_results
                }
                result['phases_completed'].append('download_workflow')
            else:
                self.log_info(f"[{court_id}] Phase 4: No downloads required")
                result['phase_results']['download_workflow'] = {
                    'total_candidates': 0,
                    'completed_downloads': 0,
                    'download_results': []
                }
            
            # Success
            result['status'] = 'success'
            result['message'] = f"All phases completed successfully for court {court_id}"
            
            self.log_info(f"[{court_id}] All workflow phases completed successfully")
            return result
            
        except Exception as e:
            self.log_error(f"Error processing court {court_id}: {e}")
            result['error'] = str(e)
            return result
            
        finally:
            # ARCHITECTURAL FIX: Orchestrator handles context cleanup after all phases
            await self._cleanup_shared_browser_context(court_result, court_id)

    async def process_dockets_for_court(
        self, 
        court_id: str, 
        discovered_dockets: List[Dict[str, Any]], 
        workflow_config: Dict[str, Any],
        navigator: Optional[Any] = None,
        context: Optional[Any] = None,
        court_result: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Process all dockets for a court through SEQUENTIAL docket processing.
        
        CRITICAL: This method implements TRUE SEQUENTIAL PROCESSING where each docket
        is processed completely before moving to the next, with "Return and Continue"
        navigation logic between dockets.
        
        Updated to ensure the data dictionaries conform to the legacy_final_data structure.
        
        Args:
            court_id: Court identifier
            discovered_dockets: List of dockets discovered in court processing
            workflow_config: Workflow configuration
            navigator: Optional PacerNavigator instance (will be created if not provided)  
            context: REQUIRED browser context for creating pages
            court_result: Optional court processing result containing preserved page and context
            
        Returns:
            List of processed docket results conforming to legacy_final_data structure
        """
        self.log_info(f"[{court_id}] SEQUENTIAL PROCESSING: Starting {len(discovered_dockets)} dockets")
        
        # Initialize sequential workflow manager if not already done
        if not hasattr(self, '_sequential_workflow_manager'):
            await self._initialize_sequential_workflow_manager(court_id, workflow_config)
        
        # Use sequential workflow manager for processing
        sequential_result = await self._sequential_workflow_manager.execute({
            "action": "process_dockets_sequentially",
            "court_id": court_id,
            "dockets": discovered_dockets,
            "navigator": navigator,
            "context": context,
            "workflow_config": workflow_config
        })
        
        # Extract results in expected format
        if sequential_result.get('success'):
            return sequential_result.get('results', [])
        else:
            self.log_error(f"[{court_id}] Sequential processing failed: {sequential_result.get('error', 'Unknown error')}")
            return []
    async def _initialize_sequential_workflow_manager(self, court_id: str, workflow_config: Dict[str, Any]):
        """Initialize the sequential workflow manager for this court processing."""
        try:
            from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager
            from src.pacer.components.processing.state_validator import StateValidator
            from src.pacer.components.navigation.return_and_continue_manager import ReturnAndContinueManager
            from src.pacer.utils.court_logger import create_court_logger
            
            # Create court logger if not already available
            iso_date = workflow_config.get('iso_date', '')
            court_logger = create_court_logger(court_id, iso_date, self.config or {})
            
            # Initialize state validator
            state_validator = StateValidator(
                court_logger=court_logger.get_logger(),
                logger=self.logger,
                config=self.config
            )
            await state_validator.initialize()
            
            # Initialize return and continue manager
            return_manager = ReturnAndContinueManager(
                navigation_facade=self.navigation_facade,
                state_validator=state_validator,
                court_logger=court_logger.get_logger(),
                logger=self.logger,
                config=self.config
            )
            await return_manager.initialize()
            
            # Initialize sequential workflow manager with ALL required dependencies
            self._sequential_workflow_manager = SequentialWorkflowManager(
                navigation_facade=self.navigation_facade,
                docket_processor=self.docket_processor,
                state_validator=state_validator,
                return_manager=return_manager,
                court_logger=court_logger.get_logger(),
                logger=self.logger,
                config=self.config,
                # CRITICAL FIX: Ensure database dependencies are ALWAYS injected
                pacer_repository=self.pacer_repository,
                async_dynamodb_storage=self.async_dynamodb_storage
            )
            await self._sequential_workflow_manager.initialize()
            
            # Store components for later use
            self._state_validator = state_validator
            self._return_manager = return_manager
            self._court_logger = court_logger
            
            self.log_info(f"[{court_id}] Sequential workflow manager initialized successfully")
            
        except Exception as e:
            self.log_error(f"[{court_id}] Failed to initialize sequential workflow manager: {e}", exc_info=True)
            raise

    async def process_rows_for_dockets(self, docket_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process rows (cases) extracted from dockets through row processing phase.
        
        Args:
            docket_results: List of docket processing results
            
        Returns:
            List of row processing results with business rules applied
        """
        self.log_info(f"Processing rows for {len(docket_results)} dockets")
        
        # Extract all cases from docket results and apply ignore download logic
        all_cases = []
        for docket_result in docket_results:
            # Apply ignore download logic before row processing
            processed_case = self._apply_ignore_download_logic(docket_result)
            all_cases.append(processed_case)
        
        # Process all cases through row processor
        row_results = await self.row_processor.perform_action({
            "action": "process_rows",
            "cases": all_cases
        })
        
        self.log_info(f"Row processing completed: {len(row_results)} cases processed")
        return row_results

    async def execute_downloads_for_rows(
        self, 
        row_results: List[Dict[str, Any]], 
        workflow_config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Execute downloads for cases that require them through download workflow phase.
        
        Args:
            row_results: List of row processing results requiring downloads
            workflow_config: Workflow configuration
            
        Returns:
            List of download workflow results
        """
        self.log_info(f"Executing downloads for {len(row_results)} qualified cases")
        
        download_results = []
        
        for row in row_results:
            try:
                if row.get('requires_download'):
                    # Execute download workflow for this case
                    download_result = await self.download_manager.perform_action({
                        "action": "execute_download_workflow",
                        "case_details": row,
                        "page": None,  # Page will be created as needed
                        "is_explicitly_requested": row.get("_is_explicitly_requested", False)
                    })
                    
                    if download_result:
                        # Save case data with download results
                        final_result = await self._save_case_data_with_download(row, download_result, workflow_config)
                        download_results.append(final_result)
                    
            except Exception as e:
                self.log_error(f"Error executing download for case {row.get('docket_num', 'unknown')}: {e}")
                continue
        
        self.log_info(f"Download workflow completed: {len(download_results)} downloads processed")
        return download_results

    async def process_single_docket(
        self, 
        page: Any, 
        initial_details: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Process a single docket through all phases (for compatibility/direct use).
        
        Args:
            page: Browser page with docket content
            initial_details: Initial docket/case details
            
        Returns:
            Complete processing result for the single docket
        """
        court_id = initial_details.get("court_id", "N/A")
        docket_num = initial_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Processing single docket through all phases")
        
        try:
            # Phase 1: Docket Processing
            docket_result = await self.docket_processor.process_docket(
                page=page,
                initial_details=initial_details
            )
            
            if not docket_result:
                return None
            
            # Phase 2: Row Processing  
            row_result = await self.row_processor.process_single_row(docket_result)
            
            # Phase 3: Download Workflow (if required)
            if row_result.get('requires_download'):
                download_result = await self.download_manager.perform_action({
                    "action": "execute_download_workflow",
                    "case_details": row_result,
                    "page": page,
                    "is_explicitly_requested": row_result.get("_is_explicitly_requested", False)
                })
                
                if download_result:
                    row_result.update(download_result)
            
            # Phase 4: Save Data
            iso_date = initial_details.get("iso_date") or self.config.get("iso_date")
            if iso_date and self.file_operations_service:
                try:
                    local_path = await self.file_operations_service.save_case_data(row_result, iso_date)
                    row_result["_local_path"] = local_path
                    
                    # Try S3 upload
                    try:
                        s3_key = f"{iso_date}/{court_id}/{row_result.get('base_filename', 'unknown')}.json"
                        s3_url = await self.file_operations_service.upload_to_s3(local_path, s3_key)
                        row_result["_s3_url"] = s3_url
                    except Exception as s3_e:
                        self.log_warning(f"S3 upload failed: {s3_e}")
                        row_result["_s3_url"] = ""
                        
                except Exception as save_e:
                    self.log_error(f"Failed to save case data: {save_e}")
            
            row_result["_processing_phase"] = "completed"
            row_result["_processing_status"] = "success"
            
            self.log_info(f"[{court_id}][{docket_num}] Single docket processing completed successfully")
            return row_result
            
        except Exception as e:
            self.log_error(f"[{court_id}][{docket_num}] Single docket processing failed: {e}")
            return None

    async def _setup_workflow_environment(self, workflow_config: Dict[str, Any]) -> None:
        """Setup workflow environment and directories."""
        iso_date = workflow_config.get('iso_date')
        if iso_date and self.file_operations_service:
            try:
                if hasattr(self.file_operations_service, 'setup_directories'):
                    await self.file_operations_service.setup_directories(iso_date)
                    self.log_info(f"Setup workflow directories for date: {iso_date}")
            except Exception as e:
                self.log_warning(f"Could not setup workflow directories: {e}")

    async def _save_case_data_with_download(
        self, 
        case_details: Dict[str, Any], 
        download_result: Dict[str, Any], 
        workflow_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Save case data with download results."""
        # Merge download results
        final_case = case_details.copy()
        final_case.update(download_result)
        
        # Save to file system
        iso_date = workflow_config.get("iso_date")
        if iso_date and self.file_operations_service:
            try:
                # Clean temporary fields
                cleaned_case = self._cleanup_temporary_fields(final_case)
                
                local_path = await self.file_operations_service.save_case_data(cleaned_case, iso_date)
                final_case["_local_path"] = local_path
                
                # Try S3 upload
                try:
                    court_id = final_case.get("court_id", "unknown")
                    base_filename = final_case.get("base_filename", "unknown")
                    s3_key = f"{iso_date}/{court_id}/{base_filename}.json"
                    s3_url = await self.file_operations_service.upload_to_s3(local_path, s3_key)
                    final_case["_s3_url"] = s3_url
                except Exception as s3_e:
                    self.log_warning(f"S3 upload failed: {s3_e}")
                    final_case["_s3_url"] = ""
                    
            except Exception as save_e:
                self.log_error(f"Failed to save case data with download: {save_e}")
        
        return final_case

    def _update_phase_summaries(self, results: Dict[str, Any], court_result: Dict[str, Any]) -> None:
        """Update phase summaries with court result."""
        if court_result.get('status') == 'success':
            results['phase_summaries']['court_processing']['courts'] += 1
            
            # Update docket processing summary
            docket_phase = court_result.get('phase_results', {}).get('docket_processing', {})
            results['phase_summaries']['docket_processing']['dockets_processed'] += docket_phase.get('processed_dockets', 0)
            results['phase_summaries']['docket_processing']['cases_extracted'] += docket_phase.get('processed_dockets', 0)
            
            # Update row processing summary
            row_phase = court_result.get('phase_results', {}).get('row_processing', {})
            results['phase_summaries']['row_processing']['rows_processed'] += row_phase.get('processed_rows', 0)
            results['phase_summaries']['row_processing']['download_required'] += row_phase.get('download_required', 0)
            
            # Update download workflow summary
            download_phase = court_result.get('phase_results', {}).get('download_workflow', {})
            results['phase_summaries']['download_workflow']['downloads_completed'] += download_phase.get('completed_downloads', 0)

    def _cleanup_temporary_fields(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Remove temporary processing fields before final save."""
        cleaned_data = case_details.copy()
        
        # Remove HTML content (too large for storage)
        cleaned_data.pop("_html_content", None)
        
        # Remove other temporary fields but keep important processing metadata
        fields_to_remove = [
            field for field in cleaned_data.keys() 
            if field.startswith('_') and field not in [
                '_processing_phase', '_processing_status', '_local_path', '_s3_url',
                '_verification_result', '_is_explicitly_requested'
            ]
        ]
        
        for field in fields_to_remove:
            cleaned_data.pop(field, None)
        
        return cleaned_data

    async def _run_court_report_generation_workflow(
        self,
        court_id: str,
        workflow_config: Dict[str, Any],
        processor_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute the legacy court report generation workflow (Steps 2.3 - 2.9).
        
        This method implements the high-level logic for the scraping workflow:
        - Step 2.3: Authentication (3-phase PACER authentication)
        - Step 2.4: Navigate to Report Menu
        - Step 2.5: Fill Report Parameters  
        - Step 2.6: Generate Report
        - Step 2.7: Parse Report Results
        - Step 2.8: Process Each Row
        - Step 2.9: Aggregate Data
        
        Args:
            court_id: Court identifier to process
            workflow_config: Workflow configuration with dates and settings
            processor_config: Processor-specific configuration
            
        Returns:
            Dict containing legacy_final_data structure with all processed cases
        """
        self.log_info(f"[{court_id}] Starting legacy court report generation workflow")
        
        result = {
            'court_id': court_id,
            'status': 'failed',
            'phases_completed': [],
            'phase_results': {},
            'legacy_final_data': []
        }
        
        # Initialize variables that will be used in finally block
        browser_context = None
        auth_result = {}
        
        try:
            # Step 2.3: Authentication - Use existing authentication facade
            self.log_info(f"[{court_id}] Step 2.3: Authenticating to PACER")
            auth_result = await self.court_processor.perform_action({
                "action": "authenticate_court",
                "court_id": court_id,
                "config": workflow_config
            })
            
            if not auth_result.get('success'):
                result['error'] = f"Authentication failed: {auth_result.get('error')}"
                return result
            
            result['phases_completed'].append('authentication')
            browser_context = auth_result.get('browser_context')
            navigator = auth_result.get('navigator')
            
            # CRITICAL DECISION POINT: Check if docket_report_log exists
            iso_date = workflow_config.get('iso_date') or datetime.now().strftime('%Y%m%d')
            docket_log_path = os.path.join(
                self.config.get('DATA_DIR', 'data'),
                iso_date,
                'logs',
                'docket_report',
                f'{court_id}.json'
            )
            
            # Check if docket report log exists and has cases
            docket_log_exists = False
            existing_dockets = []
            if os.path.exists(docket_log_path):
                try:
                    with open(docket_log_path, 'r') as f:
                        log_data = json.load(f)
                        existing_dockets = log_data.get('cases', [])
                        if existing_dockets:
                            docket_log_exists = True
                            self.log_info(f"[{court_id}] PATH A: Found existing docket_report_log with {len(existing_dockets)} cases")
                        else:
                            self.log_info(f"[{court_id}] Docket log exists but has 0 cases, will generate new report")
                except Exception as e:
                    self.log_warning(f"[{court_id}] Error reading docket log: {e}")
            
            if not docket_log_exists:
                # PATH B: No existing log or empty log - Generate new report
                self.log_info(f"[{court_id}] PATH B: No docket_report_log found - will generate new report")
                self.log_info(f"[{court_id}] Steps 2.4-2.6: Navigating to report and generating")
                
                # First, ensure we're on the correct page by clicking Document Filing System if needed
                current_url = navigator.page.url if navigator else ""
                if "ShowIndex.pl" in current_url:
                    self.log_info(f"[{court_id}] On ShowIndex.pl - MUST click 'Document Filing System' link")
                    try:
                        # Wait for page to be ready
                        await navigator.page.wait_for_load_state('networkidle', timeout=10000)
                        await asyncio.sleep(2)  # CRITICAL: Wait for dynamic content
                        
                        # The link text is ALWAYS "Document Filing System"
                        doc_filing_link = navigator.page.locator('a:has-text("Document Filing System")').first
                        if await doc_filing_link.count() > 0:
                            await doc_filing_link.click()
                            await navigator.page.wait_for_load_state('networkidle', timeout=15000)
                            await asyncio.sleep(1)
                            self.log_info(f"[{court_id}] Successfully clicked Document Filing System link")
                        else:
                            self.log_error(f"[{court_id}] CRITICAL: Document Filing System link MUST exist but wasn't found")
                            import sys
                            sys.exit(1)
                    except Exception as e:
                        self.log_error(f"[{court_id}] CRITICAL ERROR clicking Document Filing System link: {e}")
                        import sys
                        sys.exit(1)
                
                # Now use the report facade to generate the report
                if self.report_facade:
                    # Get dates from workflow config and format them
                    start_date = workflow_config.get('start_date')
                    end_date = workflow_config.get('end_date')
                    
                    # Format dates as strings if they're not already
                    from_date_str = start_date if isinstance(start_date, str) else start_date.strftime('%m/%d/%Y') if start_date else None
                    to_date_str = end_date if isinstance(end_date, str) else end_date.strftime('%m/%d/%Y') if end_date else None
                    
                    # Use default dates if not provided
                    if not from_date_str or not to_date_str:
                        from datetime import datetime, timedelta
                        today = datetime.now()
                        thirty_days_ago = today - timedelta(days=30)
                        from_date_str = from_date_str or thirty_days_ago.strftime('%m/%d/%Y')
                        to_date_str = to_date_str or today.strftime('%m/%d/%Y')
                    
                    # Get ISO date for directory structure
                    iso_date = workflow_config.get('iso_date') or datetime.now().strftime('%Y%m%d')
                    
                    report_result = await self.report_facade.execute({
                        "action": "generate_civil_cases_report",
                        "court_id": court_id,
                        "from_date_str": from_date_str,
                        "to_date_str": to_date_str,
                        "navigator": navigator,
                        "context": browser_context,
                        "iso_date": iso_date,
                        "ignore_download_service": None  # Can be added if needed
                    })
                    
                    # report_result is a boolean indicating if cases were found
                    if report_result:
                        self.log_info(f"[{court_id}] Report generated successfully with cases found")
                        result['success'] = True
                        result['has_cases'] = True
                    else:
                        self.log_info(f"[{court_id}] Report generated but no cases found")
                        result['success'] = True
                        result['has_cases'] = False
                        
                    result['phases_completed'].append('report_generation')
                    result['phase_results']['report_generation'] = report_result
                    
                    # PATH B continues: Navigate to Query page after report generation
                    if report_result and self.navigation_facade:
                        self.log_info(f"[{court_id}] PATH B: Report generated, now navigating to Query page to process dockets")
                        query_success = await self.navigation_facade.navigate_to_query_page(navigator=navigator, court_id=court_id)
                        if query_success:
                            self.log_info(f"[{court_id}] Successfully navigated to Query page after report generation")
                            result['phases_completed'].append('query_navigation')
                        else:
                            self.log_warning(f"[{court_id}] Failed to navigate to Query page after report generation")
            else:
                # PATH A: Use existing docket_report_log - Navigate to Query page
                self.log_info(f"[{court_id}] PATH A: Using existing docket_report_log - navigating to Query page")
                
                # Navigate to Query page instead of generating report
                # Use navigation facade from DI container
                if self.navigation_facade:
                    query_success = await self.navigation_facade.navigate_to_query_page(navigator=navigator, court_id=court_id)
                else:
                    self.log_error(f"[{court_id}] Navigation facade not available from DI container")
                    result['success'] = False
                    result['error'] = "Navigation facade not available"
                    return result
                if query_success:
                    self.log_info(f"[{court_id}] Successfully navigated to Query page")
                    result['success'] = True
                    result['has_cases'] = True
                    result['phases_completed'].append('query_navigation')
                    result['phase_results']['query_navigation'] = True
                else:
                    self.log_error(f"[{court_id}] Failed to navigate to Query page")
                    result['success'] = False
                    result['error'] = "Failed to navigate to Query page"
                    return result
            
            
            # CONVERGENCE POINT: Both PATH A and PATH B load dockets from the log file
            # Step 2.7: Parse Report Results - Load dockets from the log file
            self.log_info(f"[{court_id}] Step 2.7: Loading dockets from report log (for both PATH A and PATH B)")
            
            # Read the docket report log to get the discovered dockets
            iso_date = workflow_config.get('iso_date') or datetime.now().strftime('%Y%m%d')
            log_file_path = os.path.join(
                self.config.get('DATA_DIR', 'data'),
                iso_date,
                'logs',
                'docket_report',
                f'{court_id}.json'
            )
            
            discovered_dockets = []
            if os.path.exists(log_file_path):
                try:
                    with open(log_file_path, 'r') as f:
                        docket_data = json.load(f)
                        # Handle both list format and object format with 'cases' key
                        if isinstance(docket_data, list):
                            discovered_dockets = docket_data
                        elif isinstance(docket_data, dict) and 'cases' in docket_data:
                            discovered_dockets = docket_data['cases']
                        else:
                            discovered_dockets = []
                        self.log_info(f"[{court_id}] Loaded {len(discovered_dockets)} dockets from report log")
                except Exception as e:
                    self.log_error(f"[{court_id}] Failed to read docket report log: {e}")
                    discovered_dockets = []
            else:
                self.log_warning(f"[{court_id}] No docket report log found at {log_file_path} - this should not happen!")
                result['error'] = f"Docket report log not found after generation/checking"
                return result
            
            self.log_info(f"[{court_id}] CONVERGED: Both paths continue with {len(discovered_dockets)} discovered dockets")
            
            # Step 2.8: Process Each Row (Docket)
            self.log_info(f"[{court_id}] Step 2.8: Processing {len(discovered_dockets)} dockets")
            
            # Process dockets sequentially as per legacy workflow
            docket_results = await self.process_dockets_for_court(
                court_id=court_id,
                discovered_dockets=discovered_dockets,
                workflow_config=workflow_config,
                navigator=navigator,
                context=browser_context,
                court_result=auth_result
            )
            
            result['phase_results']['docket_processing'] = {
                'total_dockets': len(discovered_dockets),
                'processed_dockets': len(docket_results),
                'docket_results': docket_results
            }
            result['phases_completed'].append('docket_processing')
            
            # Step 2.9: Aggregate Data into legacy_final_data format
            self.log_info(f"[{court_id}] Step 2.9: Aggregating data to legacy format")
            
            for docket_result in docket_results:
                # Transform to legacy_final_data structure
                legacy_data = self._transform_to_legacy_format(docket_result)
                result['legacy_final_data'].append(legacy_data)
            
            result['phases_completed'].append('data_aggregation')
            result['status'] = 'success'
            result['message'] = f"Legacy workflow completed for court {court_id}"
                
        except Exception as e:
            self.log_error(f"[{court_id}] Legacy workflow failed: {e}", exc_info=True)
            result['error'] = str(e)
            
        finally:
            # Cleanup browser context if created
            if browser_context and auth_result.get('context_created'):
                await self._cleanup_shared_browser_context(auth_result, court_id)
        
        return result
    
    async def _process_court_with_docket_log(
        self,
        court_ids: List[str],
        docket_log_path: str,
        iso_date: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process courts using a pre-downloaded docket log file.
        
        This method implements the legacy workflow for processing from a 
        pre-downloaded docket list, skipping authentication and report generation.
        
        Args:
            court_ids: List of court IDs to process
            docket_log_path: Path to JSON file containing pre-downloaded docket list
            iso_date: ISO date string for file organization
            **kwargs: Additional parameters
            
        Returns:
            Processing results with legacy_final_data structure
        """
        self.log_info(f"Processing from docket log: {docket_log_path}")
        
        results = {
            'workflow_mode': 'docket_log',
            'docket_log_path': docket_log_path,
            'court_results': {},
            'legacy_final_data': [],
            'status': 'started'
        }
        
        try:
            # Load the docket log file
            if not os.path.exists(docket_log_path):
                results['error'] = f"Docket log file not found: {docket_log_path}"
                results['status'] = 'failed'
                return results
            
            with open(docket_log_path, 'r') as f:
                docket_log_data = json.load(f)
            
            self.log_info(f"Loaded {len(docket_log_data)} dockets from log file")
            
            # Filter dockets by court_ids if specified
            if court_ids:
                filtered_dockets = [
                    d for d in docket_log_data 
                    if d.get('court_id') in court_ids
                ]
            else:
                filtered_dockets = docket_log_data
            
            self.log_info(f"Processing {len(filtered_dockets)} dockets after filtering")
            
            # Process each docket through enrichment and download workflow
            for docket_data in filtered_dockets:
                court_id = docket_data.get('court_id', 'unknown')
                
                try:
                    # Skip authentication/navigation - go straight to docket processing
                    # Enrich the docket with full details
                    enriched_docket = await self.docket_processor.perform_action({
                        "action": "enrich_docket",
                        "docket_data": docket_data,
                        "iso_date": iso_date
                    })
                    
                    # Apply row processing rules
                    row_result = await self.row_processor.perform_action({
                        "action": "process_rows",
                        "cases": [enriched_docket]
                    })
                    
                    # Execute download workflow if needed
                    if row_result and row_result[0].get('requires_download'):
                        download_result = await self.download_manager.perform_action({
                            "action": "execute_download_workflow",
                            "case_details": row_result[0],
                            "is_explicitly_requested": True
                        })
                        
                        if download_result:
                            row_result[0].update(download_result)
                    
                    # Transform to legacy format and add to results
                    legacy_data = self._transform_to_legacy_format(row_result[0])
                    results['legacy_final_data'].append(legacy_data)
                    
                    # Track per-court results
                    if court_id not in results['court_results']:
                        results['court_results'][court_id] = {
                            'processed_count': 0,
                            'success_count': 0
                        }
                    
                    results['court_results'][court_id]['processed_count'] += 1
                    if legacy_data.get('workflow_result', {}).get('success'):
                        results['court_results'][court_id]['success_count'] += 1
                    
                except Exception as e:
                    self.log_error(f"Error processing docket from log: {e}")
                    continue
            
            results['status'] = 'completed'
            results['message'] = f"Processed {len(results['legacy_final_data'])} dockets from log"
            
        except Exception as e:
            self.log_error(f"Failed to process docket log: {e}", exc_info=True)
            results['error'] = str(e)
            results['status'] = 'failed'
        
        return results
    
    def _transform_to_legacy_format(self, case_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform modern case data structure to legacy_final_data format.
        
        Args:
            case_data: Modern format case data
            
        Returns:
            Dict in legacy_final_data structure
        """
        return {
            'court_id': case_data.get('court_id', ''),
            'docket_number': case_data.get('docket_num', ''),
            'case_title': case_data.get('case_title', ''),
            'case_name': case_data.get('case_name', ''),
            'date_filed': case_data.get('filing_date', ''),
            'date_terminated': case_data.get('date_terminated', ''),
            'judge_name': case_data.get('judge_name', ''),
            'case_status': case_data.get('case_status', ''),
            'nature_of_suit': case_data.get('nature_of_suit', ''),
            'cause_of_action': case_data.get('cause', ''),
            'jury_demand': case_data.get('jury_demand', ''),
            'mdl_status': case_data.get('is_mdl', False),
            'lead_case_id': case_data.get('lead_case_id', ''),
            'plaintiffs': case_data.get('plaintiffs', []),
            'defendants': case_data.get('defendants', []),
            'plaintiff_attorneys': case_data.get('plaintiff_attorneys', []),
            'defendant_attorneys': case_data.get('defendant_attorneys', []),
            'download_status': case_data.get('download_status', 'not_attempted'),
            'documents_downloaded': case_data.get('documents_downloaded', []),
            'base_filename': case_data.get('base_filename', ''),
            's3_path': case_data.get('_s3_url', ''),
            'local_path': case_data.get('_local_path', ''),
            'is_relevant': case_data.get('is_relevant', False),
            'relevance_score': case_data.get('relevance_score', 0.0),
            'workflow_result': {
                'success': case_data.get('_processing_status') == 'success',
                'phases_completed': case_data.get('_phases_completed', []),
                'errors': case_data.get('_errors', [])
            },
            'metadata': {
                'processing_timestamp': case_data.get('_processing_timestamp', ''),
                'workflow_version': '2.0_legacy_compatible',
                'source': case_data.get('_source', 'pacer_scraping')
            }
        }
    
    async def _load_configuration(self) -> None:
        """
        Load configuration if not already loaded.
        """
        if not self.config:
            try:
                # Load default configuration
                config_path = os.path.join(
                    os.path.dirname(__file__),
                    '../../config/pacer/pacer_config.json'
                )
                if os.path.exists(config_path):
                    with open(config_path, 'r') as f:
                        self.config = json.load(f)
                else:
                    self.config = {}
                    
                self.log_info("Configuration loaded successfully")
            except Exception as e:
                self.log_error(f"Failed to load configuration: {e}")
                self.config = {}

    async def _cleanup_shared_browser_context(self, court_result: Dict[str, Any], court_id: str) -> None:
        """
        Cleanup shared browser context after all phases complete.
        
        This method handles the context lifecycle management responsibility that was
        transferred from the CourtProcessor to the orchestrator.
        """
        try:
            context_id = court_result.get('context_id') if court_result else None
            context_created = court_result.get('context_created', False) if court_result else False
            
            # Only clean up if we created the context (not if it was passed in)
            if context_id and context_created and hasattr(self, 'court_processor') and self.court_processor:
                # Get browser service from court processor
                browser_service = getattr(self.court_processor, 'browser_service', None)
                
                if browser_service:
                    await browser_service.close_context({"context_id": context_id})
                    self.log_info(f"[{court_id}] Browser context {context_id} cleaned up by orchestrator after all phases")
                else:
                    self.log_warning(f"[{court_id}] Browser service not available for context cleanup")
            else:
                self.log_debug(f"[{court_id}] No shared context to cleanup (created={context_created})")
                
        except Exception as e:
            self.log_warning(f"[{court_id}] Error cleaning up shared browser context: {e}")

    async def _load_ignore_download_config(self) -> None:
        """Load the ignore download configuration from JSON file."""
        try:
            config_path = os.path.join(
                os.path.dirname(__file__), 
                '../../config/pacer/ignore_download/ignore_download.json'
            )
            
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    self._ignore_download_config = json.load(f)
                self.log_info(f"Loaded {len(self._ignore_download_config)} ignore download rules")
            else:
                self._ignore_download_config = []
                self.log_warning(f"Ignore download config not found at: {config_path}")
                
        except Exception as e:
            self.log_error(f"Error loading ignore download config: {e}")
            self._ignore_download_config = []

    def _should_ignore_download(self, case_details: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Check if a case should have downloads ignored based on configuration.
        
        Args:
            case_details: Case details to check against ignore rules
            
        Returns:
            Dict with mdl_num and flags if case should be ignored, None otherwise
        """
        if not self._ignore_download_config:
            return None
        
        court_id = case_details.get('court_id', '').lower()
        case_title = case_details.get('case_title', '').lower()
        defendants = case_details.get('defendants', [])
        
        # Convert defendants to lowercase for comparison
        if isinstance(defendants, list):
            defendants_lower = [d.lower() if isinstance(d, str) else str(d).lower() for d in defendants]
        else:
            defendants_lower = [str(defendants).lower()] if defendants else []
        
        for rule in self._ignore_download_config:
            rule_court_id = rule.get('court_id', '').lower()
            rule_defendants = rule.get('defendants', [])
            
            # Check if court_id matches
            if rule_court_id and court_id != rule_court_id:
                continue
                
            # If rule has defendants, check if any match
            if rule_defendants:
                rule_defendants_lower = [d.lower() for d in rule_defendants]
                
                # Check if any rule defendant appears in case title or defendant list
                defendant_match = False
                for rule_def in rule_defendants_lower:
                    if rule_def in case_title:
                        defendant_match = True
                        break
                    for case_def in defendants_lower:
                        if rule_def in case_def:
                            defendant_match = True
                            break
                    if defendant_match:
                        break
                
                if not defendant_match:
                    continue
            
            # If we get here, the rule matches
            self.log_info(f"Case matches ignore download rule: court={rule_court_id}, mdl_num={rule.get('mdl_num')}")
            return {
                'mdl_num': rule.get('mdl_num'),
                'flags': rule.get('flags', [])
            }
        
        return None

    def _apply_ignore_download_logic(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply ignore download logic to a case, setting html_only and inheriting mdl_num & flags.
        
        Args:
            case_details: Case details to process
            
        Returns:
            Modified case details with ignore logic applied
        """
        ignore_info = self._should_ignore_download(case_details)
        
        if ignore_info:
            # Set to html_only and skip download
            case_details['html_only'] = True
            case_details['requires_download'] = False
            
            # Inherit mdl_num and flags from the rule
            if ignore_info.get('mdl_num'):
                case_details['mdl_num'] = ignore_info['mdl_num']
            if ignore_info.get('flags'):
                case_details['flags'] = ignore_info['flags']
            
            case_details['_ignore_download_applied'] = True
            case_details['_ignore_download_reason'] = 'Matched ignore download configuration'
            
            self.log_info(f"Applied ignore download logic to case {case_details.get('docket_num', 'N/A')}: html_only=True, mdl_num={ignore_info.get('mdl_num')}")
        
        return case_details

    async def get_workflow_statistics(self) -> Dict[str, Any]:
        """Get statistics about workflow operations."""
        return {
            "total_workflows_executed": 0,
            "phases_breakdown": {
                "court_processing": {"total": 0, "successful": 0},
                "docket_processing": {"total": 0, "successful": 0},
                "row_processing": {"total": 0, "successful": 0},
                "download_workflow": {"total": 0, "successful": 0}
            },
            "performance_metrics": {
                "average_court_processing_time": 0,
                "average_docket_processing_time": 0,
                "average_row_processing_time": 0,
                "average_download_time": 0
            }
        }