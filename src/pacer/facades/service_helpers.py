# /src/pacer/facades/service_helpers.py

"""
Service Helper Functions for DocketOrchestrator initialization.

This module provides helper functions to properly create and initialize
all required services for the DocketOrchestrator with proper error handling
and dependency management.
"""

from __future__ import annotations
import logging
from typing import Any, Dict, Optional, Tuple, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

# Import all required service classes
from src.pacer.services.case_processing_service import CaseProcessingService
from src.pacer.services.relevance_service import RelevanceService
from src.pacer.services.classification_service import ClassificationService
from src.pacer.services.verification_service import VerificationService
from src.pacer.services.download_service import DownloadService
from src.pacer.services.file_operations_service import FileOperationsService
from src.pacer.services.configuration_service import ConfigurationService
from src.pacer.services.browser_service import BrowserService

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol


class ServiceInitializationResult:
    """
    Container for service initialization results.
    
    Provides structured access to initialized services and tracks
    initialization status for proper error handling.
    """
    
    def __init__(self):
        self.case_processing: Optional[CaseProcessingService] = None
        self.relevance: Optional[RelevanceService] = None
        self.classification: Optional[ClassificationService] = None
        self.verification: Optional[VerificationService] = None
        self.download: Optional[DownloadService] = None
        self.file_operations: Optional[FileOperationsService] = None
        self.configuration: Optional[ConfigurationService] = None
        self.browser: Optional[BrowserService] = None
        
        # Track initialization status
        self.initialized_services: Dict[str, bool] = {}
        self.initialization_errors: Dict[str, str] = {}
        self.all_initialized = False
    
    def get_services_dict(self) -> Dict[str, Optional[AsyncServiceBase]]:
        """Get all services as a dictionary for DocketOrchestrator."""
        return {
            'case_processing_service': self.case_processing,
            'relevance_service': self.relevance,
            'classification_service': self.classification,
            'verification_service': self.verification,
            'download_service': self.download,
            'file_operations_service': self.file_operations,
            'configuration_service': self.configuration,
            'browser_service': self.browser,
        }
    
    def get_initialization_summary(self) -> Dict[str, Any]:
        """Get a summary of the initialization process."""
        return {
            'all_initialized': self.all_initialized,
            'services_initialized': list(self.initialized_services.keys()),
            'initialization_errors': self.initialization_errors,
            'total_services': len(self.get_services_dict()),
            'successful_initializations': len([s for s in self.initialized_services.values() if s])
        }


async def create_and_initialize_orchestrator_services(
    logger: Optional[LoggerProtocol] = None,
    config: Optional[Dict[str, Any]] = None,
    skip_browser_init: bool = False
) -> ServiceInitializationResult:
    """
    Create and initialize all required services for DocketOrchestrator.
    
    This function creates all required services, properly initializes each one
    by calling their initialize() method, and returns them ready for use.
    
    Args:
        logger: Logger instance to use for all services
        config: Configuration dictionary to pass to all services
        skip_browser_init: If True, skip browser service initialization (useful for testing)
        
    Returns:
        ServiceInitializationResult: Container with all initialized services
        
    Raises:
        PacerServiceError: If critical services fail to initialize
    """
    result = ServiceInitializationResult()
    
    # Create fallback logger if none provided
    if logger is None:
        logger = logging.getLogger("ServiceHelper")
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
    
    # Use empty config if none provided
    if config is None:
        config = {}
    
    logger.info("Starting service initialization for DocketOrchestrator")
    
    # Define service creation order - dependencies first
    service_definitions = [
        ('configuration', ConfigurationService, True),  # Core config service
        ('file_operations', FileOperationsService, True),  # Core file operations
        ('case_processing', CaseProcessingService, True),  # Core processing
        ('relevance', RelevanceService, True),  # Core business logic
        ('classification', ClassificationService, False),  # Optional classification
        ('verification', VerificationService, True),  # Core verification
        ('download', DownloadService, True),  # Core download orchestration
        ('browser', BrowserService, True),  # Core browser management
    ]
    
    # Create and initialize each service
    for service_name, service_class, is_required in service_definitions:
        try:
            logger.info(f"Creating {service_name} service ({service_class.__name__})")
            
            # Skip browser service if requested
            if service_name == 'browser' and skip_browser_init:
                logger.info(f"Skipping {service_name} service initialization as requested")
                result.initialized_services[service_name] = True
                continue
            
            # Create service instance
            service_instance = service_class(logger=logger, config=config)
            
            # Initialize the service
            logger.info(f"Initializing {service_name} service...")
            await service_instance.initialize()
            
            # Store in result container
            setattr(result, service_name, service_instance)
            result.initialized_services[service_name] = True
            
            logger.info(f"Successfully initialized {service_name} service")
            
        except Exception as e:
            error_msg = f"Failed to initialize {service_name} service: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            result.initialization_errors[service_name] = str(e)
            result.initialized_services[service_name] = False
            
            # If this is a required service, we should raise an error
            if is_required:
                logger.error(f"Required service {service_name} failed to initialize")
                # Continue trying other services but mark as failed
                continue
            else:
                logger.warning(f"Optional service {service_name} failed to initialize, continuing")
                continue
    
    # Check if all required services were initialized
    required_services = ['configuration', 'file_operations', 'case_processing', 
                        'relevance', 'verification', 'download', 'browser']
    
    missing_required = []
    for service_name in required_services:
        if service_name == 'browser' and skip_browser_init:
            continue  # Skip browser check if we skipped initialization
        if not result.initialized_services.get(service_name, False):
            missing_required.append(service_name)
    
    if missing_required:
        error_msg = f"Failed to initialize required services: {missing_required}"
        logger.error(error_msg)
        raise PacerServiceError(error_msg)
    
    result.all_initialized = True
    logger.info("All services initialized successfully for DocketOrchestrator")
    
    # Log initialization summary
    summary = result.get_initialization_summary()
    logger.info(f"Service initialization summary: {summary}")
    
    return result


async def create_orchestrator_with_services(
    logger: Optional[LoggerProtocol] = None,
    config: Optional[Dict[str, Any]] = None,
    skip_browser_init: bool = False,
    pacer_repository: Optional[Any] = None,
    async_dynamodb_storage: Optional[Any] = None
) -> Tuple["DocketOrchestrator", ServiceInitializationResult]:
    """
    Create a fully initialized DocketOrchestrator with all required services.
    
    This is a convenience function that handles the complete setup process:
    1. Create and initialize all required services
    2. Create DocketOrchestrator with the initialized services
    3. Initialize the orchestrator itself
    4. Return both the orchestrator and service initialization result
    
    Args:
        logger: Logger instance to use
        config: Configuration dictionary
        skip_browser_init: If True, skip browser service initialization
        pacer_repository: PacerRepository instance for database access
        async_dynamodb_storage: AsyncDynamoDBStorage instance for direct DynamoDB access
        
    Returns:
        Tuple of (DocketOrchestrator, ServiceInitializationResult)
        
    Raises:
        PacerServiceError: If initialization fails
    """
    from src.pacer.facades.docket_orchestrator import DocketOrchestrator
    
    # Initialize all services
    service_result = await create_and_initialize_orchestrator_services(
        logger=logger,
        config=config,
        skip_browser_init=skip_browser_init
    )
    
    # Create DocketOrchestrator with initialized services including storage dependencies
    orchestrator = DocketOrchestrator(
        case_processing_service=service_result.case_processing,
        relevance_service=service_result.relevance,
        classification_service=service_result.classification,
        verification_service=service_result.verification,
        download_service=service_result.download,
        file_operations_service=service_result.file_operations,
        configuration_service=service_result.configuration,
        browser_service=service_result.browser,
        # CRITICAL FIX: Add missing storage dependencies
        pacer_repository=pacer_repository,
        async_dynamodb_storage=async_dynamodb_storage,
        logger=logger,
        config=config
    )
    
    # Initialize the orchestrator itself
    await orchestrator.initialize()
    
    return orchestrator, service_result


async def cleanup_orchestrator_services(service_result: ServiceInitializationResult) -> None:
    """
    Clean up all services in a ServiceInitializationResult.
    
    This function properly cleans up all initialized services by calling
    their cleanup() methods in reverse order of initialization.
    
    Args:
        service_result: The ServiceInitializationResult containing services to cleanup
    """
    if not service_result:
        return
    
    services_to_cleanup = []
    
    # Get services in reverse order for cleanup
    service_names = ['browser', 'download', 'verification', 'classification', 
                    'relevance', 'case_processing', 'file_operations', 'configuration']
    
    for service_name in service_names:
        service = getattr(service_result, service_name, None)
        if service and service_result.initialized_services.get(service_name, False):
            services_to_cleanup.append((service_name, service))
    
    # Cleanup each service
    for service_name, service in services_to_cleanup:
        try:
            if hasattr(service, 'cleanup'):
                await service.cleanup()
            service_result.initialized_services[service_name] = False
        except Exception as e:
            # Log cleanup errors but don't raise - we want to try cleaning up all services
            logging.getLogger("ServiceHelper").warning(
                f"Error cleaning up {service_name} service: {str(e)}"
            )


# Convenience function for quick service creation during development/testing
async def create_minimal_orchestrator_services(
    logger: Optional[LoggerProtocol] = None,
    config: Optional[Dict[str, Any]] = None
) -> ServiceInitializationResult:
    """
    Create a minimal set of services for basic orchestrator functionality.
    
    This creates only the most essential services needed for basic operation,
    useful for testing or limited functionality scenarios.
    
    Args:
        logger: Logger instance to use
        config: Configuration dictionary
        
    Returns:
        ServiceInitializationResult with minimal services initialized
    """
    # For minimal setup, we'll skip browser service and classification
    return await create_and_initialize_orchestrator_services(
        logger=logger,
        config=config,
        skip_browser_init=True
    )