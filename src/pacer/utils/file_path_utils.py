"""
File Path and Naming Utilities for PACER Download Orchestration

This module provides comprehensive utilities for generating temporary and final file paths
for PACER document downloads, including:

1. Temporary path generation: data/{iso_date}/dockets/temp/{court_id}_..._{uuid}/
2. Final path generation: data/{iso_date}/dockets/{court_id}_{year}_{case_num}_{versus}.pdf
3. Year extraction from case numbers (YY-##### format)
4. Versus field extraction and sanitization
5. UUID generation for unique temporary directories

Key Features:
- ISO date formatting for consistent directory structure
- Court ID and case number validation
- Versus field parsing and sanitization for filenames
- UUID-based temporary directory naming
- Path safety validation and filesystem compatibility
- Integration with existing docket utilities
"""

import logging
import os
import re
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List
from urllib.parse import quote

from src.utils.docket_utils import parse_docket_number_numeric_only, extract_docket_components
from src.infrastructure.patterns.component_base import ComponentImplementation


class FilePathBuilder(ComponentImplementation):
    """
    Comprehensive file path builder for PACER document processing.
    
    Handles the complete lifecycle of path generation from temporary working
    directories to final organized storage paths.
    """

    def __init__(self, base_data_dir: str = "data", logger: Optional[logging.Logger] = None):
        """
        Initialize the FilePathBuilder.
        
        Args:
            base_data_dir: Base directory for all data storage (default: "data")
            logger: Optional logger instance (court-specific logger from DI)
        """
        # Create fallback logger if none provided
        if logger is None:
            import logging
            logger = logging.getLogger(__name__)
        
        # Initialize base class with logger
        super().__init__(logger)
        self.base_data_dir = Path(base_data_dir)
    
    async def _execute_action(self, data):
        """Implementation of abstract method from ComponentImplementation."""
        # This is a utility class, so this method is not typically used
        # In the future, this could be used for batch path generation operations
        return {"action": "file_path_utility", "data": data}
        
    def generate_temporary_path(self, 
                              court_id: str, 
                              case_details: Dict[str, Any],
                              iso_date: Optional[str] = None) -> str:
        """
        Generate temporary processing path for a case.
        
        Format: data/{iso_date}/dockets/temp/{court_id}_{year}_{case_num}_{versus_short}_{uuid}/
        
        Args:
            court_id: Court identifier (e.g., "ilnd", "nysd")
            case_details: Dictionary containing case information
            iso_date: ISO formatted date (YYYY-MM-DD), defaults to today
            
        Returns:
            Absolute path to temporary directory
            
        Example:
            >>> builder.generate_temporary_path("ilnd", {"docket_num": "1:25-cv-09395", "title": "Smith v. Acme Corp"})
            "/path/to/data/2025-08-11/dockets/temp/ilnd_25_09395_smith_v_acme_corp_a1b2c3d4/"
        """
        if not iso_date:
            iso_date = datetime.now().strftime("%Y-%m-%d")
            
        # Extract year and case number from docket
        year, case_num = self._extract_year_and_case_number(case_details.get('docket_num', ''))
        
        # Extract and sanitize versus field
        versus_sanitized = self._extract_versus_for_path(case_details)
        
        # Generate unique identifier
        unique_id = str(uuid.uuid4()).split('-')[0]  # First 8 characters of UUID
        
        # Build temporary directory name
        temp_dir_name = f"{court_id}_{year}_{case_num}_{versus_sanitized}_{unique_id}"
        
        # Construct full path
        temp_path = self.base_data_dir / iso_date / "dockets" / "temp" / temp_dir_name
        
        self.logger.debug(f"Generated temporary path: {temp_path}")
        return str(temp_path.resolve())

    def generate_final_path(self, 
                           court_id: str, 
                           case_details: Dict[str, Any],
                           iso_date: Optional[str] = None,
                           file_extension: str = "pdf") -> str:
        """
        Generate final storage path for a case document.
        
        Format: data/{iso_date}/dockets/{court_id}_{year}_{case_num}_{versus}.pdf
        
        Args:
            court_id: Court identifier
            case_details: Dictionary containing case information
            iso_date: ISO formatted date, defaults to today
            file_extension: File extension (default: "pdf")
            
        Returns:
            Absolute path to final file location
            
        Example:
            >>> builder.generate_final_path("ilnd", {"docket_num": "1:25-cv-09395", "title": "Smith v. Acme Corp"})
            "/path/to/data/2025-08-11/dockets/ilnd_25_09395_smith_v_acme_corp.pdf"
        """
        if not iso_date:
            iso_date = datetime.now().strftime("%Y-%m-%d")
            
        # Extract year and case number from docket
        year, case_num = self._extract_year_and_case_number(case_details.get('docket_num', ''))
        
        # Extract and sanitize versus field
        versus_sanitized = self._extract_versus_for_path(case_details)
        
        # Build filename
        filename = f"{court_id}_{year}_{case_num}_{versus_sanitized}.{file_extension}"
        
        # Construct full path
        final_path = self.base_data_dir / iso_date / "dockets" / filename
        
        self.logger.debug(f"Generated final path: {final_path}")
        return str(final_path.resolve())

    def _extract_year_and_case_number(self, docket_num: str) -> Tuple[str, str]:
        """
        Extract year and case number from docket number.
        
        Expected format: N:YY-XX-NNNNN (e.g., "1:25-cv-09395")
        Returns: (year, case_number) as ("25", "09395")
        
        Args:
            docket_num: Raw docket number
            
        Returns:
            Tuple of (year, case_number) both as strings
        """
        try:
            # Use existing docket utilities to get numeric parts
            numeric_parts = parse_docket_number_numeric_only(docket_num)  # Returns "YY_NNNNN"
            
            if numeric_parts and numeric_parts != "unknown_docket" and '_' in numeric_parts:
                year, case_num = numeric_parts.split('_', 1)
                self.logger.debug(f"Extracted year={year}, case_num={case_num} from {docket_num}")
                return year, case_num
            else:
                self.logger.warning(f"Could not parse docket number: {docket_num}")
                return "unknown", "00000"
                
        except Exception as e:
            self.logger.error(f"Error extracting year/case from docket {docket_num}: {e}")
            return "unknown", "00000"

    def _extract_versus_for_path(self, case_details: Dict[str, Any]) -> str:
        """
        Extract and sanitize versus field for use in file paths.
        
        This method processes the case title to extract a clean, filesystem-safe
        representation of the case parties for use in file naming.
        
        Args:
            case_details: Dictionary containing case information
            
        Returns:
            Sanitized versus string suitable for file paths
            
        Example:
            >>> _extract_versus_for_path({"title": "Smith v. Acme Corp., Inc. et al"})
            "smith_v_acme_corp"
        """
        title = case_details.get('title', '')
        
        if not title:
            # Try alternative field names
            title = case_details.get('case_title', '') or case_details.get('name', '')
            
        if not title:
            self.logger.warning("No case title found for versus extraction")
            return "unknown_case"
        
        try:
            # Parse versus field to get parties
            versus_data = self._parse_versus_field(title)
            
            if versus_data['plaintiff'] and versus_data['defendants']:
                plaintiff = versus_data['plaintiff']
                # Use first defendant for filename
                defendant = versus_data['defendants'][0] if versus_data['defendants'] else 'unknown'
                
                # Create versus string
                versus_raw = f"{plaintiff}_v_{defendant}"
                
                # Sanitize for filesystem
                versus_clean = self._sanitize_for_filename(versus_raw)
                
                # Limit length to prevent filesystem issues
                if len(versus_clean) > 50:
                    versus_clean = versus_clean[:47] + "..."
                
                self.logger.debug(f"Extracted versus: '{title}' -> '{versus_clean}'")
                return versus_clean
                
            else:
                # Fallback: sanitize entire title
                sanitized_title = self._sanitize_for_filename(title)
                if len(sanitized_title) > 30:
                    sanitized_title = sanitized_title[:27] + "..."
                return sanitized_title
                
        except Exception as e:
            self.logger.error(f"Error extracting versus from title '{title}': {e}")
            return "parsing_error"

    def _parse_versus_field(self, case_title: str) -> Dict[str, Any]:
        """
        Parse case title to extract plaintiff and defendants.
        
        This is a simplified version of versus parsing focused on filename generation.
        
        Args:
            case_title: Full case title string
            
        Returns:
            Dictionary with plaintiff, defendants, and parsing metadata
        """
        if not case_title or not case_title.strip():
            return {
                'plaintiff': '',
                'defendants': [],
                'has_et_al': False,
                'parsing_notes': ['No valid case title provided']
            }
        
        # Common versus patterns
        versus_patterns = [
            r'\s+v\.\s+',    # " v. "
            r'\s+V\.\s+',    # " V. " 
            r'\s+vs\.\s+',   # " vs. "
            r'\s+Vs\.\s+',   # " Vs. "
            r'\s+v\s+',      # " v "
            r'\s+V\s+'       # " V "
        ]
        
        case_title = case_title.strip()
        
        # Find versus pattern
        split_result = None
        for pattern in versus_patterns:
            if re.search(pattern, case_title):
                split_result = re.split(pattern, case_title, 1)
                break
        
        if not split_result or len(split_result) < 2:
            return {
                'plaintiff': '',
                'defendants': [],
                'has_et_al': False,
                'parsing_notes': ['No valid versus pattern found']
            }
        
        plaintiff = split_result[0].strip()
        defendant_text = split_result[1].strip()
        
        # Check for et al
        has_et_al = False
        et_al_pattern = r'\s+(et\s+al\.?|ET\s+AL\.?)\s*$'
        if re.search(et_al_pattern, defendant_text, re.IGNORECASE):
            has_et_al = True
            defendant_text = re.sub(et_al_pattern, '', defendant_text, flags=re.IGNORECASE).strip()
        
        # Extract defendants
        defendants = self._extract_defendants_from_string(defendant_text)
        
        return {
            'plaintiff': plaintiff,
            'defendants': defendants,
            'has_et_al': has_et_al,
            'parsing_notes': [f'Extracted {len(defendants)} defendant(s)']
        }

    def _extract_defendants_from_string(self, defendant_text: str) -> List[str]:
        """
        Extract individual defendants from a string containing multiple parties.
        
        Args:
            defendant_text: Text containing defendant names
            
        Returns:
            List of individual defendant names
        """
        if not defendant_text or not defendant_text.strip():
            return []
        
        defendant_text = defendant_text.strip()
        
        # Company suffixes that should not be split on commas
        company_suffixes = [
            'Inc.', 'Inc', 'LLC', 'Corp.', 'Corp', 'Ltd.', 'Ltd',
            'Co.', 'Co', 'Company', 'Corporation', 'Limited',
            'L.L.C.', 'L.P.', 'LP', 'LLP', 'PLLC'
        ]
        
        # Split on commas, but be smart about company suffixes
        defendants = []
        parts = defendant_text.split(',')
        
        i = 0
        while i < len(parts):
            current_part = parts[i].strip()
            
            # Check if next part is a company suffix
            if i + 1 < len(parts):
                next_part = parts[i + 1].strip()
                if any(next_part == suffix for suffix in company_suffixes):
                    # Combine with next part
                    defendants.append(f"{current_part}, {next_part}")
                    i += 2  # Skip the next part
                    continue
            
            if current_part:
                defendants.append(current_part)
            i += 1
        
        return [d.strip() for d in defendants if d.strip()]

    def _sanitize_for_filename(self, text: str) -> str:
        """
        Sanitize text for use in filenames.
        
        Removes or replaces characters that are problematic in filenames
        while preserving readability.
        
        Args:
            text: Raw text to sanitize
            
        Returns:
            Filesystem-safe string
        """
        if not text:
            return "sanitized"
        
        # Convert to lowercase
        text = text.lower()
        
        # Replace problematic characters with underscores, but be careful with dots
        # Keep letters, numbers, hyphens, underscores
        text = re.sub(r'[^\w\-_ ]', '_', text)
        
        # Replace spaces with underscores
        text = re.sub(r'\s+', '_', text)
        
        # Remove multiple consecutive underscores
        text = re.sub(r'_+', '_', text)
        
        # Remove leading/trailing underscores and dots
        text = text.strip('_.')
        
        # Handle empty result after processing
        if not text:
            return "sanitized"
            
        return text

    def ensure_directory_exists(self, path: str) -> bool:
        """
        Ensure that a directory exists, creating it if necessary.
        
        Args:
            path: Directory path to create
            
        Returns:
            True if directory exists or was created successfully
        """
        try:
            Path(path).mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"Ensured directory exists: {path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to create directory {path}: {e}")
            return False

    def generate_s3_key(self, 
                       court_id: str, 
                       case_details: Dict[str, Any],
                       iso_date: Optional[str] = None,
                       file_type: str = "html") -> str:
        """
        Generate S3 key for cloud storage.
        
        Format: dockets/{iso_date}/{court_id}/{court_id}_{year}_{case_num}_{versus}.{file_type}
        
        Args:
            court_id: Court identifier
            case_details: Dictionary containing case information
            iso_date: ISO formatted date, defaults to today
            file_type: File type extension (default: "html")
            
        Returns:
            S3 key string
        """
        if not iso_date:
            iso_date = datetime.now().strftime("%Y-%m-%d")
            
        # Extract year and case number from docket
        year, case_num = self._extract_year_and_case_number(case_details.get('docket_num', ''))
        
        # Extract and sanitize versus field
        versus_sanitized = self._extract_versus_for_path(case_details)
        
        # Build S3 key
        filename = f"{court_id}_{year}_{case_num}_{versus_sanitized}.{file_type}"
        s3_key = f"dockets/{iso_date}/{court_id}/{filename}"
        
        self.logger.debug(f"Generated S3 key: {s3_key}")
        return s3_key

    def get_path_components(self, case_details: Dict[str, Any]) -> Dict[str, str]:
        """
        Extract all path components for a case.
        
        This method returns a dictionary containing all the individual components
        that can be used to build various types of paths.
        
        Args:
            case_details: Dictionary containing case information
            
        Returns:
            Dictionary containing path components
        """
        docket_num = case_details.get('docket_num', '')
        year, case_num = self._extract_year_and_case_number(docket_num)
        versus_sanitized = self._extract_versus_for_path(case_details)
        iso_date = datetime.now().strftime("%Y-%m-%d")
        
        return {
            'iso_date': iso_date,
            'year': year,
            'case_num': case_num,
            'versus_sanitized': versus_sanitized,
            'docket_num': docket_num,
            'unique_id': str(uuid.uuid4()).split('-')[0]
        }


# Convenience functions for backwards compatibility and direct usage
def generate_temporary_path(court_id: str, 
                          case_details: Dict[str, Any],
                          iso_date: Optional[str] = None,
                          base_dir: str = "data") -> str:
    """
    Generate temporary path for case processing.
    
    Convenience function that creates a FilePathBuilder and generates a temporary path.
    """
    builder = FilePathBuilder(base_dir)
    return builder.generate_temporary_path(court_id, case_details, iso_date)


def generate_final_path(court_id: str, 
                       case_details: Dict[str, Any],
                       iso_date: Optional[str] = None,
                       file_extension: str = "pdf",
                       base_dir: str = "data") -> str:
    """
    Generate final storage path for case document.
    
    Convenience function that creates a FilePathBuilder and generates a final path.
    """
    builder = FilePathBuilder(base_dir)
    return builder.generate_final_path(court_id, case_details, iso_date, file_extension)


def extract_year_from_case_number(docket_num: str) -> str:
    """
    Extract year from case number in YY-##### format.
    
    Args:
        docket_num: Docket number (e.g., "1:25-cv-09395")
        
    Returns:
        Two-digit year string (e.g., "25")
    """
    try:
        # Use regex to extract year part
        pattern = r'\d+:(\d{2})-[a-zA-Z]{2}-\d+'
        match = re.search(pattern, docket_num)
        
        if match:
            return match.group(1)
        else:
            logger.warning(f"Could not extract year from docket: {docket_num}")
            return "unknown"
            
    except Exception as e:
        logger.error(f"Error extracting year from docket {docket_num}: {e}")
        return "unknown"


def sanitize_versus_field(case_title: str, max_length: int = 50) -> str:
    """
    Extract and sanitize versus field from case title for filename use.
    
    Args:
        case_title: Full case title
        max_length: Maximum length of output string
        
    Returns:
        Sanitized versus string suitable for filenames
    """
    builder = FilePathBuilder()
    versus = builder._extract_versus_for_path({'title': case_title})
    
    if len(versus) > max_length:
        versus = versus[:max_length-3] + "..."
        
    return versus


def create_directory_structure(base_path: str, court_id: str, iso_date: Optional[str] = None) -> str:
    """
    Create the standard directory structure for a court and date.
    
    Args:
        base_path: Base directory path
        court_id: Court identifier
        iso_date: ISO date string, defaults to today
        
    Returns:
        Path to created directory structure
    """
    if not iso_date:
        iso_date = datetime.now().strftime("%Y-%m-%d")
        
    # Create main structure
    main_dir = Path(base_path) / iso_date / "dockets"
    temp_dir = main_dir / "temp"
    
    # Create directories
    temp_dir.mkdir(parents=True, exist_ok=True)
    
    logger.debug(f"Created directory structure: {main_dir}")
    return str(main_dir.resolve())


def validate_path_safety(file_path: str) -> bool:
    """
    Validate that a file path is safe for filesystem operations.
    
    Args:
        file_path: Path to validate
        
    Returns:
        True if path is safe, False otherwise
    """
    try:
        path = Path(file_path)
        
        # Check for path traversal attempts
        if '..' in path.parts:
            return False
        
        # Check if path is absolute - this is actually OK for our use case
        # but we need to be careful about checking individual parts
        path_str = str(path)
        if '..' in path_str:
            return False
            
        # Check for reserved names (Windows)
        reserved_names = {
            'CON', 'PRN', 'AUX', 'NUL',
            'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
            'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        }
        
        for part in path.parts:
            # Check if any part (without extension) is a reserved name
            part_name = part.split('.')[0].upper()
            if part_name in reserved_names:
                return False
                
        return True
        
    except Exception:
        return False