"""
S3 Service Injection Helper

This utility provides functions to properly inject S3 services into HTML processing components
and other services that need S3 functionality.
"""

from typing import Any, Dict, Optional
from src.services.html.html_service_factory import HtmlServiceFactory


class S3InjectionHelper:
    """Helper class for injecting S3 services into components that need them."""
    
    @staticmethod
    def create_html_processing_orchestrator_with_s3(
        s3_async_storage: Any,
        logger: Any,
        config: Optional[Dict] = None,
        court_id: Optional[str] = None
    ):
        """
        Create an HTMLProcessingOrchestrator with proper S3 service injection.
        
        Args:
            s3_async_storage: S3AsyncStorage instance from storage container
            logger: Logger instance
            config: Optional configuration dictionary
            court_id: Optional court identifier
            
        Returns:
            HTMLProcessingOrchestrator instance with S3 support
        """
        if config is None:
            config = {}
            
        # Create HTML processing orchestrator with S3 storage
        html_orchestrator = HtmlServiceFactory.create_html_processing_orchestrator(
            logger=logger,
            config=config,
            court_id=court_id,
            html_data_updater=None,  # Can be set later if needed
            s3_async_storage=s3_async_storage
        )
        
        # Log that S3 has been properly injected
        if logger:
            logger.info(f"HTMLProcessingOrchestrator created with S3 injection: {s3_async_storage is not None}")
        
        return html_orchestrator
    
    @staticmethod
    def verify_s3_injection(component: Any, logger: Any = None) -> bool:
        """
        Verify that a component has proper S3 service injection.
        
        Args:
            component: Component to verify
            logger: Optional logger instance
            
        Returns:
            True if S3 is properly injected, False otherwise
        """
        has_s3 = hasattr(component, 's3_async_storage') and component.s3_async_storage is not None
        
        if logger:
            status = "✅ PASSED" if has_s3 else "❌ FAILED"
            logger.info(f"S3 injection verification {status}: {type(component).__name__}")
            
        return has_s3
    
    @staticmethod
    def create_s3_service_bundle(s3_async_storage: Any, logger: Any) -> Dict[str, Any]:
        """
        Create S3 service bundle for dependency injection.
        
        Args:
            s3_async_storage: S3AsyncStorage instance
            logger: Logger instance
            
        Returns:
            Dictionary containing S3 services and utilities
        """
        return {
            's3_async_storage': s3_async_storage,
            's3_service': s3_async_storage,  # Alias for compatibility
            'logger': logger,
            'enabled': s3_async_storage is not None,
            'bucket_name': getattr(s3_async_storage, 'bucket_name', None) if s3_async_storage else None
        }
    
    @staticmethod
    def inject_s3_into_existing_component(component: Any, s3_async_storage: Any, logger: Any = None):
        """
        Inject S3 service into an existing component that supports it.
        
        Args:
            component: Component to inject S3 into
            s3_async_storage: S3AsyncStorage instance
            logger: Optional logger instance
        """
        if hasattr(component, 'set_s3_manager'):
            component.set_s3_manager(s3_async_storage)
            if logger:
                logger.info(f"S3 service injected into {type(component).__name__} via set_s3_manager")
        elif hasattr(component, 's3_async_storage'):
            component.s3_async_storage = s3_async_storage
            if logger:
                logger.info(f"S3 service injected into {type(component).__name__} via direct assignment")
        else:
            if logger:
                logger.warning(f"Component {type(component).__name__} does not support S3 injection")