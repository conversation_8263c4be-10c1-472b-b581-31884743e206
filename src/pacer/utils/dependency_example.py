"""
Example usage of the dependency validation system in PACER workflows.
"""

import sys
from typing import Any, Dict
from src.infrastructure.protocols.logger import LoggerProtocol
from src.pacer.utils.dependency_validator import DependencyValidator
from src.pacer.utils.dependency_integration import (
    DependencyIntegration, 
    validate_pacer_pipeline_dependencies,
    emergency_dependency_check
)
from src.pacer.utils.validated_component_factory import ValidatedComponentFactory


def initialize_pacer_with_validation(
    core_services: Dict[str, Any],
    logger: LoggerProtocol
) -> Dict[str, Any]:
    """
    Example of how to initialize PACER with full dependency validation.
    
    This function demonstrates the recommended pattern for starting any PACER operation:
    1. Emergency dependency check
    2. Core service validation
    3. Component creation with validation
    4. Pipeline validation
    5. Ready for processing
    
    Args:
        core_services: Dict of core services (logger, s3_service, etc.)
        logger: Logger instance
        
    Returns:
        Validated PACER pipeline components
    """
    logger.info("Starting PACER initialization with dependency validation")
    
    try:
        # Step 1: Emergency check for critical modules
        emergency_dependency_check(logger)
        
        # Step 2: Validate core services
        required_core = [
            'logger',
            's3_service', 
            'pacer_db',
            'async_dynamodb_storage',
            'configuration_service',
            'browser_context'
        ]
        
        DependencyValidator.validate_required_dependencies(
            core_services,
            required_core,
            logger,
            "PACER_Initialization"
        )
        
        # Step 3: Create validated component factory
        factory = ValidatedComponentFactory(logger)
        
        # Step 4: Create validated pipeline
        pipeline = factory.create_validated_pipeline(core_services)
        
        # Step 5: Final pipeline validation
        validate_pacer_pipeline_dependencies(pipeline, logger)
        
        logger.info("✓ PACER initialization complete - all dependencies validated")
        return pipeline
        
    except SystemExit:
        logger.critical("PACER initialization failed due to dependency validation errors")
        raise
    except Exception as e:
        logger.critical(f"Unexpected error during PACER initialization: {e}")
        sys.exit(1)


def validate_existing_pacer_workflow(
    workflow_components: Dict[str, Any],
    logger: LoggerProtocol
) -> bool:
    """
    Example of validating an existing PACER workflow.
    
    Use this when you have existing components and want to ensure they're properly configured.
    
    Args:
        workflow_components: Dict of component name -> component instance
        logger: Logger instance
        
    Returns:
        True if all components are valid, False otherwise
    """
    logger.info("Validating existing PACER workflow components")
    
    # Create validation report
    components = list(workflow_components.values())
    report = DependencyValidator.create_dependency_report(components, logger)
    
    # Check results
    valid_components = []
    invalid_components = []
    
    for component_name, component_report in report.items():
        if component_report['status'] == 'valid':
            valid_components.append(component_name)
        else:
            invalid_components.append(component_name)
            logger.error(f"Component {component_name} validation failed: {component_report.get('errors', [])}")
    
    # Log summary
    total = len(valid_components) + len(invalid_components)
    success_rate = len(valid_components) / total if total > 0 else 0
    
    logger.info(f"Workflow validation summary:")
    logger.info(f"  Valid components: {len(valid_components)}")
    logger.info(f"  Invalid components: {len(invalid_components)}")
    logger.info(f"  Success rate: {success_rate:.1%}")
    
    if invalid_components:
        logger.warning(f"Invalid components found: {', '.join(invalid_components)}")
        return False
    
    logger.info("✓ All workflow components are valid")
    return True


def demonstrate_dependency_validation():
    """
    Demonstration of the dependency validation system.
    
    This shows the key patterns and how validation prevents common issues.
    """
    print("=== PACER Dependency Validation Demonstration ===")
    
    # Mock logger for demonstration
    class MockLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def critical(self, msg): print(f"CRITICAL: {msg}")
        def debug(self, msg): print(f"DEBUG: {msg}")
    
    logger = MockLogger()
    
    print("\n1. Testing missing dependency detection:")
    try:
        # This should fail - missing required dependencies
        DependencyValidator.validate_required_dependencies(
            {'logger': logger},  # Missing s3_service, pacer_db, etc.
            ['logger', 's3_service', 'pacer_db'],
            logger,
            "TestComponent"
        )
    except SystemExit:
        print("✓ Correctly detected missing dependencies and prevented startup")
    
    print("\n2. Testing valid dependency validation:")
    # This should pass
    DependencyValidator.validate_required_dependencies(
        {
            'logger': logger,
            's3_service': "MockS3Service",
            'pacer_db': "MockDatabase"
        },
        ['logger', 's3_service', 'pacer_db'],
        logger,
        "TestComponent"
    )
    print("✓ Valid dependencies passed validation")
    
    print("\n3. Testing initialization order validation:")
    try:
        # Test correct order
        DependencyValidator.validate_initialization_order(
            ['logger', 's3_service', 'docket_processor'],
            {
                'logger': [],
                's3_service': ['logger'],
                'docket_processor': ['logger', 's3_service']
            },
            logger
        )
        print("✓ Correct initialization order validated")
        
        # Test incorrect order (should fail)
        DependencyValidator.validate_initialization_order(
            ['docket_processor', 's3_service', 'logger'],  # Wrong order
            {
                'logger': [],
                's3_service': ['logger'],
                'docket_processor': ['logger', 's3_service']
            },
            logger
        )
    except SystemExit:
        print("✓ Correctly detected invalid initialization order")
    
    print("\n4. Testing factory validation:")
    factory = ValidatedComponentFactory(logger)
    
    # This demonstrates how the factory ensures components are properly validated
    print("Factory created - ready for validated component creation")
    
    print("\n=== Demonstration Complete ===")
    print("\nKey benefits of this validation system:")
    print("✓ Prevents runtime failures due to missing dependencies")
    print("✓ Ensures components are properly configured before use")
    print("✓ Provides clear error messages for debugging")
    print("✓ Validates initialization order to prevent dependency cycles")
    print("✓ Creates comprehensive reports for troubleshooting")


if __name__ == "__main__":
    demonstrate_dependency_validation()