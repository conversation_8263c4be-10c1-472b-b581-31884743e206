import sys
from typing import Any, Dict, List, Optional, Type
from src.infrastructure.protocols.logger import LoggerProtocol


class DependencyValidator:
    """Validates that all required dependencies are injected before processing."""
    
    @staticmethod
    def validate_required_dependencies(
        dependencies: Dict[str, Any],
        required: List[str],
        logger: LoggerProtocol,
        component_name: str
    ) -> None:
        """
        Validate that all required dependencies are present.
        HARD FAILS if any are missing.
        
        Args:
            dependencies: Dict of available dependencies
            required: List of required dependency names
            logger: Logger for error reporting
            component_name: Name of component being validated
        """
        missing = []
        for dep_name in required:
            if dep_name not in dependencies or dependencies[dep_name] is None:
                missing.append(dep_name)
        
        if missing:
            error_msg = f"CRITICAL: {component_name} is missing required dependencies: {', '.join(missing)}"
            logger.critical(error_msg)
            logger.critical("Cannot proceed without these dependencies. Exiting...")
            sys.exit(1)  # HARD FAIL
    
    @staticmethod
    def validate_component_dependencies(component: Any, logger: LoggerProtocol) -> None:
        """
        Validate a component has all required dependencies based on its type.
        """
        component_name = component.__class__.__name__
        
        # Define required dependencies per component type
        requirements = {
            'SequentialWorkflowManager': [
                'pacer_db',
                'async_dynamodb_storage', 
                's3_service',
                'logger'
            ],
            'DocketProcessor': [
                's3_service',
                'logger',
                'html_parser'
            ],
            'ReportFacade': [
                'logger',
                'browser_context',
                'navigation_facade'
            ],
            'CourtProcessor': [
                'logger',
                'browser_context',
                'configuration_service'
            ],
            'AuthenticationService': [
                'logger',
                'browser_context',
                'configuration_service'
            ],
            'DocumentWorkflowService': [
                'logger',
                's3_service',
                'async_dynamodb_storage'
            ],
            'ArtifactChecker': [
                'logger',
                's3_service',
                'pacer_db'
            ],
            'WorkflowOrchestrator': [
                'logger',
                'configuration_service',
                'pacer_db'
            ],
            'CaseVerifier': [
                'logger',
                'pacer_db',
                'configuration_service'
            ],
            'RelevanceService': [
                'logger',
                'configuration_service'
            ],
            'NavigationFacade': [
                'logger',
                'browser_context'
            ],
            'S3Service': [
                'logger'
            ],
            'PacerDatabase': [
                'logger'
            ]
        }
        
        if component_name in requirements:
            required = requirements[component_name]
            deps = {}
            
            for dep in required:
                deps[dep] = getattr(component, dep, None)
            
            DependencyValidator.validate_required_dependencies(
                deps, required, logger, component_name
            )
    
    @staticmethod
    def validate_factory_dependencies(
        factory_name: str,
        dependencies: Dict[str, Any],
        logger: LoggerProtocol
    ) -> None:
        """
        Validate factory has all required dependencies for component creation.
        """
        factory_requirements = {
            'ServiceFactory': [
                'configuration_service',
                'logger'
            ],
            'PacerServiceFactory': [
                'configuration_service',
                'logger',
                'browser_context'
            ],
            'WorkflowFactory': [
                'logger',
                'configuration_service',
                's3_service',
                'pacer_db'
            ]
        }
        
        if factory_name in factory_requirements:
            required = factory_requirements[factory_name]
            DependencyValidator.validate_required_dependencies(
                dependencies, required, logger, factory_name
            )
    
    @staticmethod
    def validate_container_state(
        container: Any,
        expected_services: List[str],
        logger: LoggerProtocol
    ) -> None:
        """
        Validate that a DI container has all expected services registered.
        """
        container_name = container.__class__.__name__
        missing_services = []
        
        for service_name in expected_services:
            try:
                service = getattr(container, service_name, None)
                if service is None:
                    missing_services.append(service_name)
            except Exception as e:
                logger.warning(f"Error checking service {service_name}: {e}")
                missing_services.append(service_name)
        
        if missing_services:
            error_msg = f"CRITICAL: {container_name} is missing services: {', '.join(missing_services)}"
            logger.critical(error_msg)
            logger.critical("Container is not properly initialized. Exiting...")
            sys.exit(1)
    
    @staticmethod
    def validate_protocol_compliance(
        instance: Any,
        protocol: Type,
        logger: LoggerProtocol
    ) -> None:
        """
        Validate that an instance complies with a protocol interface.
        """
        instance_name = instance.__class__.__name__
        protocol_name = protocol.__name__
        
        # Check if instance has all required methods/attributes
        required_attrs = getattr(protocol, '__annotations__', {})
        missing_attrs = []
        
        for attr_name in required_attrs:
            if not hasattr(instance, attr_name):
                missing_attrs.append(attr_name)
        
        if missing_attrs:
            error_msg = f"CRITICAL: {instance_name} does not implement {protocol_name}. Missing: {', '.join(missing_attrs)}"
            logger.critical(error_msg)
            logger.critical("Protocol compliance failure. Cannot proceed.")
            sys.exit(1)
    
    @staticmethod
    def validate_initialization_order(
        component_order: List[str],
        dependencies_map: Dict[str, List[str]],
        logger: LoggerProtocol
    ) -> None:
        """
        Validate that components are initialized in correct dependency order.
        """
        initialized = set()
        
        for component in component_order:
            deps = dependencies_map.get(component, [])
            missing_deps = [dep for dep in deps if dep not in initialized]
            
            if missing_deps:
                error_msg = f"CRITICAL: {component} cannot be initialized. Missing dependencies: {', '.join(missing_deps)}"
                logger.critical(error_msg)
                logger.critical("Invalid initialization order. Exiting...")
                sys.exit(1)
            
            initialized.add(component)
            logger.debug(f"Component {component} validated for initialization")
    
    @staticmethod
    def create_dependency_report(
        components: List[Any],
        logger: LoggerProtocol
    ) -> Dict[str, Dict[str, Any]]:
        """
        Create a comprehensive report of all component dependencies.
        """
        report = {}
        
        for component in components:
            component_name = component.__class__.__name__
            component_report = {
                'status': 'unknown',
                'dependencies': {},
                'missing': [],
                'errors': []
            }
            
            try:
                # Get all attributes that might be dependencies
                for attr_name in dir(component):
                    if not attr_name.startswith('_'):
                        attr_value = getattr(component, attr_name, None)
                        if attr_value is not None and not callable(attr_value):
                            component_report['dependencies'][attr_name] = type(attr_value).__name__
                
                # Validate if known component
                DependencyValidator.validate_component_dependencies(component, logger)
                component_report['status'] = 'valid'
                
            except SystemExit:
                # Re-raise system exit
                raise
            except Exception as e:
                component_report['status'] = 'error'
                component_report['errors'].append(str(e))
                logger.error(f"Error validating {component_name}: {e}")
            
            report[component_name] = component_report
        
        return report