"""
Integration utilities for dependency validation in PACER components.
"""

from typing import Any, Dict, List, Optional
from src.infrastructure.protocols.logger import LoggerProtocol
from src.pacer.utils.dependency_validator import DependencyValidator


class DependencyIntegration:
    """Helper class for integrating dependency validation into component lifecycles."""
    
    @staticmethod
    def validate_and_initialize_component(
        component_class: type,
        dependencies: Dict[str, Any],
        logger: LoggerProtocol,
        **kwargs
    ) -> Any:
        """
        Create and validate a component with all required dependencies.
        
        Args:
            component_class: The class to instantiate
            dependencies: Available dependencies
            logger: Logger instance
            **kwargs: Additional constructor arguments
            
        Returns:
            Validated component instance
        """
        component_name = component_class.__name__
        logger.info(f"Initializing {component_name} with dependency validation")
        
        # Pre-validation: Check if we have what we need
        validator = DependencyValidator()
        
        # Create instance with dependencies
        try:
            # Merge dependencies with kwargs
            init_args = {**dependencies, **kwargs}
            component = component_class(**init_args)
            
            # Post-validation: Ensure component is properly configured
            validator.validate_component_dependencies(component, logger)
            
            logger.info(f"✓ {component_name} successfully initialized and validated")
            return component
            
        except Exception as e:
            logger.critical(f"Failed to initialize {component_name}: {e}")
            raise
    
    @staticmethod
    def validate_service_container(
        container: Any,
        logger: LoggerProtocol
    ) -> None:
        """
        Comprehensive validation of a service container.
        """
        logger.info("Validating service container configuration")
        
        # Expected core services
        core_services = [
            'configuration_service',
            'logger',
            's3_service',
            'pacer_db',
            'async_dynamodb_storage'
        ]
        
        # Expected PACER services  
        pacer_services = [
            'authentication_service',
            'navigation_facade',
            'report_facade',
            'court_processor',
            'docket_processor'
        ]
        
        # Expected workflow services
        workflow_services = [
            'workflow_orchestrator',
            'artifact_checker',
            'document_workflow_service',
            'relevance_service'
        ]
        
        all_services = core_services + pacer_services + workflow_services
        
        try:
            DependencyValidator.validate_container_state(
                container, all_services, logger
            )
            logger.info("✓ Service container validation passed")
            
        except SystemExit:
            logger.critical("Service container validation failed - cannot proceed")
            raise
    
    @staticmethod
    def validate_initialization_sequence(
        sequence: List[str],
        logger: LoggerProtocol
    ) -> None:
        """
        Validate the order of component initialization.
        """
        # Define dependency relationships
        dependencies_map = {
            'configuration_service': [],
            'logger': [],
            's3_service': ['logger'],
            'pacer_db': ['logger'],
            'async_dynamodb_storage': ['logger'],
            'authentication_service': ['logger', 'configuration_service'],
            'navigation_facade': ['logger'],
            'report_facade': ['logger', 'navigation_facade'],
            'court_processor': ['logger', 'authentication_service'],
            'docket_processor': ['logger', 's3_service'],
            'workflow_orchestrator': ['logger', 'configuration_service', 'pacer_db'],
            'artifact_checker': ['logger', 's3_service', 'pacer_db'],
            'document_workflow_service': ['logger', 's3_service', 'async_dynamodb_storage'],
            'relevance_service': ['logger', 'configuration_service']
        }
        
        DependencyValidator.validate_initialization_order(
            sequence, dependencies_map, logger
        )
        
        logger.info("✓ Initialization sequence validation passed")
    
    @staticmethod
    def create_startup_validator(logger: LoggerProtocol):
        """
        Create a startup validator that can be used during application boot.
        """
        class StartupValidator:
            def __init__(self):
                self.validated_components = set()
                self.validation_errors = []
            
            def validate_component(self, component: Any) -> bool:
                """Validate a single component and track results."""
                try:
                    DependencyValidator.validate_component_dependencies(component, logger)
                    component_name = component.__class__.__name__
                    self.validated_components.add(component_name)
                    return True
                except SystemExit:
                    raise
                except Exception as e:
                    self.validation_errors.append(str(e))
                    return False
            
            def get_validation_summary(self) -> Dict[str, Any]:
                """Get summary of validation results."""
                return {
                    'validated_components': list(self.validated_components),
                    'validation_errors': self.validation_errors,
                    'total_validated': len(self.validated_components),
                    'total_errors': len(self.validation_errors),
                    'success_rate': len(self.validated_components) / (len(self.validated_components) + len(self.validation_errors)) if (len(self.validated_components) + len(self.validation_errors)) > 0 else 0
                }
            
            def ensure_all_valid(self) -> None:
                """Ensure all validations passed, exit if not."""
                if self.validation_errors:
                    logger.critical(f"Startup validation failed with {len(self.validation_errors)} errors")
                    for error in self.validation_errors:
                        logger.critical(f"  - {error}")
                    logger.critical("Cannot proceed with invalid dependencies. Exiting...")
                    import sys
                    sys.exit(1)
                
                logger.info(f"✓ All {len(self.validated_components)} components validated successfully")
        
        return StartupValidator()


def validate_pacer_pipeline_dependencies(
    pipeline_components: Dict[str, Any],
    logger: LoggerProtocol
) -> None:
    """
    Validate all dependencies for a complete PACER processing pipeline.
    
    Args:
        pipeline_components: Dict of component name -> component instance
        logger: Logger for reporting
    """
    logger.info("Starting comprehensive PACER pipeline dependency validation")
    
    # Create startup validator
    validator = DependencyIntegration.create_startup_validator(logger)
    
    # Validate each component
    for name, component in pipeline_components.items():
        logger.debug(f"Validating {name}...")
        if not validator.validate_component(component):
            logger.error(f"Component {name} failed validation")
    
    # Ensure all validations passed
    validator.ensure_all_valid()
    
    # Get and log summary
    summary = validator.get_validation_summary()
    logger.info(f"Pipeline validation complete: {summary['total_validated']} components validated, {summary['total_errors']} errors")
    
    if summary['total_errors'] == 0:
        logger.info("✓ PACER pipeline dependency validation PASSED - ready for processing")
    else:
        logger.critical("✗ PACER pipeline dependency validation FAILED - cannot proceed")


def emergency_dependency_check(logger: LoggerProtocol) -> None:
    """
    Emergency check for critical dependencies that must be available.
    Called at the very start of any PACER operation.
    """
    logger.info("Running emergency dependency check...")
    
    critical_modules = [
        'src.infrastructure.protocols.logger',
        'src.services.configuration_service',
        'src.services.s3_service',
        'src.services.pacer_database'
    ]
    
    missing_modules = []
    for module in critical_modules:
        try:
            __import__(module)
        except ImportError as e:
            missing_modules.append(f"{module}: {e}")
    
    if missing_modules:
        logger.critical("CRITICAL: Missing essential modules:")
        for missing in missing_modules:
            logger.critical(f"  - {missing}")
        logger.critical("Cannot proceed without these modules. Exiting...")
        import sys
        sys.exit(1)
    
    logger.info("✓ Emergency dependency check passed")