"""
Enhanced CourtLogger for PACER operations with comprehensive log interception.

This module provides a CourtLogger that ensures ALL log messages are captured
in the court-specific log file at data/{iso_date}/logs/pacer/{court_id}.log,
preventing any logs from escaping to lexgenius-*.log files.
"""

import logging
import os
import sys
from contextlib import contextmanager
from typing import Dict, Any, Optional, List
from datetime import datetime
import threading
import glob
import shutil

try:
    from rich.console import Console
    from rich.logging import RichHandler
    from rich.text import Text
    from rich.panel import Panel
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False


class LogInterceptor(logging.Handler):
    """Custom handler that intercepts all log messages and redirects to court-specific logger."""
    
    def __init__(self, court_logger: logging.Logger, court_id: str):
        super().__init__()
        self.court_logger = court_logger
        self.court_id = court_id.lower()
        self.setLevel(logging.DEBUG)  # Capture all levels
        self._processing = False  # Prevent infinite recursion
        
    def emit(self, record):
        """Redirect log records to the court-specific logger."""
        # Prevent infinite recursion
        if self._processing:
            return
            
        try:
            self._processing = True
            
            # Skip if this is already from the court logger to prevent loops
            if (record.name == self.court_logger.name or 
                record.name.startswith(f"pacer.court.{self.court_id}") or
                record.name.startswith("LogInterceptor")):
                return
                
            # Skip intercepting logs that are from other court loggers to prevent cross-contamination
            if record.name.startswith("pacer.court.") and not record.name.startswith(f"pacer.court.{self.court_id}"):
                return
                
            # Also intercept any logs that might go to lexgenius-* files
            original_msg = record.getMessage()
            
            # Add court prefix if not already present
            if not original_msg.startswith(f"[{self.court_id.upper()}]"):
                prefixed_msg = f"[{self.court_id.upper()}] {original_msg}"
            else:
                prefixed_msg = original_msg
            
            # Create new record that will go to court logger
            court_record = logging.LogRecord(
                name=self.court_logger.name,
                level=record.levelno,
                pathname=record.pathname,
                lineno=record.lineno,
                msg=prefixed_msg,
                args=(),
                exc_info=record.exc_info,
                func=record.funcName,
                stack_info=record.stack_info
            )
            
            # Set the module and function name for better context
            court_record.module = record.module if hasattr(record, 'module') else record.name.split('.')[-1]
            court_record.funcName = record.funcName
            
            # Add extra context from original record
            if hasattr(record, 'component'):
                court_record.component = record.component
            
            # Handle the record using the court logger's handlers directly
            # This bypasses normal propagation and ensures it goes only to court log
            for handler in self.court_logger.handlers:
                try:
                    if handler.filter(court_record):
                        handler.handle(court_record)
                except Exception:
                    pass  # Silently ignore handler errors to prevent cascading failures
                    
        except Exception as e:
            # Fallback to prevent logging failures from breaking the application
            # Use stderr to avoid infinite loops
            import sys
            sys.stderr.write(f"LogInterceptor error: {e}\n")
            sys.stderr.flush()
        finally:
            self._processing = False


class CourtLoggerFilter(logging.Filter):
    """Filter that ensures only court-specific logs pass through."""
    
    def __init__(self, court_id: str, logger_names: List[str]):
        super().__init__()
        self.court_id = court_id.lower()
        self.logger_names = logger_names
        
    def filter(self, record):
        """Allow only logs from specified court-related loggers."""
        return any(record.name.startswith(name) for name in self.logger_names)


class CourtLogger:
    """
    Enhanced court logger with comprehensive log interception.
    
    Ensures ALL log messages during court processing are captured in the
    court-specific log file, preventing any logs from escaping to other files.
    """
    
    _instances: Dict[str, 'CourtLogger'] = {}
    _lock = threading.Lock()
    
    def __init__(self, court_id: str, iso_date: str, config: Dict[str, Any]):
        self.court_id = court_id.lower()
        self.iso_date = iso_date
        self.config = config
        self.logger_name = f"pacer.court.{self.court_id}"
        self.original_handlers = {}
        self.interceptor_handlers = {}
        self.court_logger = None
        self._setup_court_logger()
        
    @classmethod
    def get_instance(cls, court_id: str, iso_date: str, config: Dict[str, Any]) -> 'CourtLogger':
        """Get or create a CourtLogger instance (singleton per court)."""
        key = f"{court_id.lower()}_{iso_date}"
        
        with cls._lock:
            if key not in cls._instances:
                cls._instances[key] = cls(court_id, iso_date, config)
            return cls._instances[key]
    
    def _setup_court_logger(self):
        """Setup the court-specific logger with proper handlers."""
        self.court_logger = logging.getLogger(self.logger_name)
        
        # Avoid duplicate handlers
        if self.court_logger.handlers:
            return
            
        self.court_logger.setLevel(logging.DEBUG)
        self.court_logger.propagate = False  # Critical: prevent propagation
        
        # Create directory structure
        data_dir = self.config.get("DATA_DIR", os.path.join(os.getcwd(), "data"))
        log_dir = os.path.join(data_dir, self.iso_date, "logs", "pacer")
        os.makedirs(log_dir, exist_ok=True)
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s:%(lineno)d - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )
        
        # File handler - PRIMARY handler for court logs
        log_file = os.path.join(log_dir, f"{self.court_id}.log")
        file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        
        # Console handler with Rich formatting if available
        if RICH_AVAILABLE:
            console = Console(stderr=True, force_terminal=True, width=120)
            console_handler = RichHandler(
                console=console,
                rich_tracebacks=True,
                tracebacks_show_locals=False,  # Reduce noise
                show_time=True,
                show_level=True,
                show_path=False,  # Reduce noise
                markup=True,
                log_time_format="[%H:%M:%S]",
                keywords=[
                    "Phase", "SUCCESS", "ERROR", "WARNING", "CRITICAL",
                    "Download", "Docket", "Court", "Processing", "Authentication",
                    self.court_id.upper()
                ]
            )
            console_handler.setLevel(logging.INFO)  # Only show INFO+ on console
        else:
            console_handler = logging.StreamHandler(sys.stderr)
            console_handler.setLevel(logging.INFO)
            simple_formatter = logging.Formatter(
                "%(asctime)s [%(levelname)s] %(message)s",
                datefmt="%H:%M:%S"
            )
            console_handler.setFormatter(simple_formatter)
        
        # Add handlers
        self.court_logger.addHandler(file_handler)
        self.court_logger.addHandler(console_handler)
        
        # Log the setup
        self.court_logger.info(f"Court logger initialized for {self.court_id.upper()}")
        self.court_logger.debug(f"Logging to file: {log_file}")
    
    @contextmanager
    def intercept_all_logs(self):
        """
        Context manager that intercepts ALL logging and redirects to court logger.
        
        This ensures NO logs escape to lexgenius-*.log or any other files during court processing.
        
        Usage:
            with court_logger.intercept_all_logs():
                # All logging within this block goes to court-specific log
                some_operation()
        """
        import logging
        
        # Get ALL existing loggers at the time of interception
        # This is more comprehensive than hardcoded lists
        existing_loggers = []
        for name in logging.Logger.manager.loggerDict.keys():
            if isinstance(name, str):
                existing_loggers.append(name)
        
        # Critical loggers that MUST be intercepted
        critical_loggers = [
            "",  # Root logger - CRITICAL for catching all logs
            "root",  # Alternative root logger name
            "src",
            "src.pacer", 
            "src.services",
            "src.infrastructure",
            "src.infrastructure.patterns",
            "src.containers",
            "urllib3",
            "selenium",
            "asyncio",
            "werkzeug",
            "requests",
            "httpx",
        ]
        
        # Add all lexgenius-* loggers (these are problematic)
        lexgenius_loggers = [name for name in existing_loggers if name.startswith("lexgenius")]
        
        # Add any ComponentImplementation or AsyncServiceBase loggers 
        component_loggers = [name for name in existing_loggers 
                           if any(pattern in name for pattern in 
                                ["Component", "Service", "Orchestrator", "Manager", "Handler", "Processor"])]
        
        # Combine all target loggers
        target_loggers = list(set(critical_loggers + existing_loggers + lexgenius_loggers + component_loggers))
        
        interceptors = []
        original_levels = {}
        original_handlers = {}
        
        try:
            # Install interceptors on ALL loggers
            for logger_name in target_loggers:
                try:
                    logger = logging.getLogger(logger_name)
                    
                    # Store original state
                    original_levels[logger_name] = logger.level
                    original_handlers[logger_name] = logger.handlers.copy()
                    
                    # Set to DEBUG to capture everything
                    logger.setLevel(logging.DEBUG)
                    
                    # For root logger and lexgenius loggers, be more aggressive
                    if logger_name == "" or logger_name.startswith("lexgenius") or logger_name == "root":
                        # Clear existing handlers to prevent any log escape
                        for handler in logger.handlers[:]:
                            logger.removeHandler(handler)
                        
                        # Disable propagation completely
                        logger.propagate = False
                    
                    # Create and add interceptor
                    interceptor = LogInterceptor(self.court_logger, self.court_id)
                    interceptor.setLevel(logging.DEBUG)
                    logger.addHandler(interceptor)
                    interceptors.append((logger, interceptor, logger_name))
                    
                except Exception as e:
                    # Log setup error but continue
                    sys.stderr.write(f"Failed to setup interceptor for {logger_name}: {e}\n")
            
            # Also intercept any NEW loggers that get created during execution
            # by monitoring the logging manager
            initial_logger_count = len(logging.Logger.manager.loggerDict)
            
            # Yield control back to caller
            yield self.court_logger
            
            # Check for new loggers created during execution
            final_logger_count = len(logging.Logger.manager.loggerDict)
            if final_logger_count > initial_logger_count:
                new_loggers = []
                for name in logging.Logger.manager.loggerDict.keys():
                    if isinstance(name, str) and name not in target_loggers:
                        new_loggers.append(name)
                
                # Intercept new loggers too
                for logger_name in new_loggers:
                    try:
                        logger = logging.getLogger(logger_name)
                        interceptor = LogInterceptor(self.court_logger, self.court_id)
                        interceptor.setLevel(logging.DEBUG)
                        logger.addHandler(interceptor)
                        interceptors.append((logger, interceptor, logger_name))
                    except Exception:
                        pass  # Ignore errors for late interception
            
        finally:
            # Clean up interceptors and restore original state
            for logger, interceptor, logger_name in interceptors:
                try:
                    logger.removeHandler(interceptor)
                    interceptor.close()
                    
                    # Restore original handlers if we removed them
                    if logger_name in original_handlers:
                        # Clear current handlers
                        for handler in logger.handlers[:]:
                            logger.removeHandler(handler)
                        
                        # Restore original handlers
                        for handler in original_handlers[logger_name]:
                            logger.addHandler(handler)
                        
                        # Restore propagation for root/lexgenius loggers
                        if logger_name == "" or logger_name.startswith("lexgenius") or logger_name == "root":
                            logger.propagate = True
                            
                except Exception:
                    pass  # Ignore cleanup errors
            
            # Restore original levels
            for logger_name, level in original_levels.items():
                try:
                    logging.getLogger(logger_name).setLevel(level)
                except Exception:
                    pass
    
    def get_logger(self) -> logging.Logger:
        """Get the court-specific logger instance."""
        return self.court_logger
    
    def log_phase_start(self, phase: str, context: Optional[Dict[str, Any]] = None):
        """Log the start of a processing phase."""
        message = f"🚀 PHASE START: {phase}"
        if context:
            context_str = " | ".join([f"{k}={v}" for k, v in context.items()])
            message += f" | {context_str}"
        self.court_logger.info(message)
    
    def log_phase_complete(self, phase: str, duration: Optional[float] = None, context: Optional[Dict[str, Any]] = None):
        """Log the completion of a processing phase."""
        message = f"✅ PHASE COMPLETE: {phase}"
        if duration:
            message += f" | Duration: {duration:.2f}s"
        if context:
            context_str = " | ".join([f"{k}={v}" for k, v in context.items()])
            message += f" | {context_str}"
        self.court_logger.info(message)
    
    def log_error_with_context(self, error: str, context: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """Log an error with full context."""
        message = f"❌ ERROR: {error}"
        if context:
            context_str = " | ".join([f"{k}={v}" for k, v in context.items()])
            message += f" | Context: {context_str}"
        self.court_logger.error(message, exc_info=exc_info)
    
    def log_warning_with_context(self, warning: str, context: Optional[Dict[str, Any]] = None):
        """Log a warning with context."""
        message = f"⚠️ WARNING: {warning}"
        if context:
            context_str = " | ".join([f"{k}={v}" for k, v in context.items()])
            message += f" | Context: {context_str}"
        self.court_logger.warning(message)
    
    def log_success(self, success: str, context: Optional[Dict[str, Any]] = None):
        """Log a success message."""
        message = f"🎉 SUCCESS: {success}"
        if context:
            context_str = " | ".join([f"{k}={v}" for k, v in context.items()])
            message += f" | Context: {context_str}"
        self.court_logger.info(message)
    
    def log_progress(self, current: int, total: int, description: str = "Processing"):
        """Log progress information."""
        percentage = (current / total * 100) if total > 0 else 0
        message = f"📊 PROGRESS: {description} - {current}/{total} ({percentage:.1f}%)"
        self.court_logger.info(message)
    
    def log_docket_info(self, docket_id: str, action: str, context: Optional[Dict[str, Any]] = None):
        """Log docket-specific information."""
        message = f"📋 DOCKET: {docket_id} - {action}"
        if context:
            context_str = " | ".join([f"{k}={v}" for k, v in context.items()])
            message += f" | {context_str}"
        self.court_logger.info(message)
    
    def log_download_info(self, item: str, action: str, context: Optional[Dict[str, Any]] = None):
        """Log download-specific information."""
        message = f"⬇️ DOWNLOAD: {item} - {action}"
        if context:
            context_str = " | ".join([f"{k}={v}" for k, v in context.items()])
            message += f" | {context_str}"
        self.court_logger.info(message)
    
    def log_authentication_info(self, action: str, context: Optional[Dict[str, Any]] = None):
        """Log authentication-specific information."""
        message = f"🔐 AUTH: {action}"
        if context:
            # Don't log sensitive information
            safe_context = {k: v for k, v in context.items() if 'password' not in k.lower() and 'token' not in k.lower()}
            if safe_context:
                context_str = " | ".join([f"{k}={v}" for k, v in safe_context.items()])
                message += f" | {context_str}"
        self.court_logger.info(message)
    
    def create_summary_panel(self, title: str, data: Dict[str, Any]) -> Optional[Panel]:
        """Create a rich panel for summary information."""
        if not RICH_AVAILABLE:
            # Fallback to simple logging
            summary_lines = [f"{title}:"]
            for key, value in data.items():
                summary_lines.append(f"  {key}: {value}")
            self.court_logger.info("\n".join(summary_lines))
            return None
        
        # Create rich panel
        content = Text()
        for key, value in data.items():
            content.append(f"{key}: ", style="bold cyan")
            content.append(f"{value}\n", style="white")
        
        return Panel(
            content,
            title=f"📊 {title}",
            title_align="left",
            border_style="blue",
            padding=(1, 2)
        )
    
    def flush(self):
        """Flush all handlers to ensure logs are written."""
        for handler in self.court_logger.handlers:
            if hasattr(handler, 'flush'):
                handler.flush()
    
    def close(self):
        """Close all handlers and clean up resources."""
        for handler in self.court_logger.handlers[:]:
            try:
                handler.close()
                self.court_logger.removeHandler(handler)
            except Exception:
                pass
        
        # Remove from instances
        key = f"{self.court_id}_{self.iso_date}"
        with self._lock:
            if key in self._instances:
                del self._instances[key]
    
    @classmethod
    def cleanup_all(cls):
        """Clean up all court logger instances."""
        with cls._lock:
            for instance in cls._instances.values():
                instance.close()
            cls._instances.clear()
    
    @classmethod
    def disable_all_file_handlers(cls, except_court_id: Optional[str] = None):
        """
        Disable ALL file handlers in the logging system except court-specific ones.
        
        This is a nuclear option to ensure NO logs escape to other files.
        
        Args:
            except_court_id: Court ID to exempt from disabling (current processing court)
        """
        import logging
        
        disabled_handlers = []
        
        # Check all loggers in the system
        for logger_name in list(logging.Logger.manager.loggerDict.keys()) + [""]:
            try:
                logger = logging.getLogger(logger_name)
                
                for handler in logger.handlers[:]:
                    # Check if it's a file handler
                    if isinstance(handler, logging.FileHandler):
                        file_path = getattr(handler, 'baseFilename', '')
                        
                        # Keep court-specific handlers for the current court
                        if (except_court_id and 
                            f"/pacer/{except_court_id.lower()}.log" in file_path):
                            continue
                        
                        # Disable all other file handlers
                        handler.setLevel(logging.CRITICAL + 1)  # Effectively disable
                        disabled_handlers.append((logger, handler))
                        
            except Exception:
                continue  # Skip problematic loggers
        
        return disabled_handlers
    
    @classmethod  
    def restore_file_handlers(cls, disabled_handlers):
        """Restore previously disabled file handlers."""
        for logger, handler in disabled_handlers:
            try:
                handler.setLevel(logging.DEBUG)  # Re-enable
            except Exception:
                continue
    
    @classmethod
    def nuclear_prevent_lexgenius_log(cls, base_path: str = None):
        """
        NUCLEAR OPTION: Comprehensively prevent lexgenius.log creation.
        
        This function:
        1. Removes all lexgenius-* handlers from ALL loggers
        2. Deletes existing lexgenius.log files
        3. Creates interceptor to redirect lexgenius logs
        4. Monitors and prevents future lexgenius.log creation
        
        Args:
            base_path: Base path to search for lexgenius.log files (defaults to current directory)
        """
        if base_path is None:
            base_path = os.getcwd()
        
        # Step 1: Remove all lexgenius handlers from ALL loggers
        removed_handlers = cls._remove_all_lexgenius_handlers()
        
        # Step 2: Delete all existing lexgenius.log files
        deleted_files = cls._delete_all_lexgenius_logs(base_path)
        
        # Step 3: Setup lexgenius logger interception
        cls._setup_lexgenius_interception()
        
        # Step 4: Monitor for new lexgenius loggers and prevent them
        cls._monitor_and_prevent_lexgenius_loggers()
        
        return {
            'removed_handlers': removed_handlers,
            'deleted_files': deleted_files,
            'status': 'nuclear_prevention_active'
        }
    
    @classmethod
    def _remove_all_lexgenius_handlers(cls):
        """
        Remove ALL handlers from ALL loggers that might create lexgenius.log files.
        """
        removed_handlers = []
        
        # Get ALL loggers in the system
        all_logger_names = list(logging.Logger.manager.loggerDict.keys()) + [""]
        
        for logger_name in all_logger_names:
            try:
                logger = logging.getLogger(logger_name)
                
                # Check all handlers in this logger
                for handler in logger.handlers[:]:
                    should_remove = False
                    
                    # Check if it's a FileHandler pointing to lexgenius
                    if isinstance(handler, logging.FileHandler):
                        file_path = getattr(handler, 'baseFilename', '')
                        if 'lexgenius' in os.path.basename(file_path).lower():
                            should_remove = True
                    
                    # Check logger names that might create lexgenius logs
                    if (logger_name.startswith('lexgenius') or 
                        'lexgenius' in logger_name.lower()):
                        should_remove = True
                    
                    # Remove the handler
                    if should_remove:
                        logger.removeHandler(handler)
                        try:
                            handler.close()
                        except Exception:
                            pass
                        removed_handlers.append((logger_name, str(handler)))
                        
                # Also disable propagation for lexgenius loggers
                if (logger_name.startswith('lexgenius') or 
                    'lexgenius' in logger_name.lower()):
                    logger.propagate = False
                    logger.setLevel(logging.CRITICAL + 1)  # Effectively disable
                    
            except Exception as e:
                # Log error but continue
                sys.stderr.write(f"Error removing handlers from {logger_name}: {e}\n")
                continue
        
        return removed_handlers
    
    @classmethod
    def _delete_all_lexgenius_logs(cls, base_path: str):
        """
        Find and delete ALL lexgenius.log files recursively.
        """
        deleted_files = []
        
        # Search patterns for lexgenius log files
        search_patterns = [
            '**/lexgenius*.log',
            'lexgenius*.log',
            '**/logs/lexgenius*.log',
            'logs/lexgenius*.log',
            'data/**/lexgenius*.log'
        ]
        
        for pattern in search_patterns:
            try:
                # Use glob to find all matching files
                full_pattern = os.path.join(base_path, pattern)
                matching_files = glob.glob(full_pattern, recursive=True)
                
                for file_path in matching_files:
                    try:
                        if os.path.exists(file_path):
                            # Try to delete the file
                            os.remove(file_path)
                            deleted_files.append(file_path)
                            sys.stderr.write(f"NUCLEAR: Deleted lexgenius log file: {file_path}\n")
                    except Exception as e:
                        sys.stderr.write(f"NUCLEAR: Failed to delete {file_path}: {e}\n")
                        
            except Exception as e:
                sys.stderr.write(f"NUCLEAR: Error searching for pattern {pattern}: {e}\n")
                continue
        
        return deleted_files
    
    @classmethod
    def _setup_lexgenius_interception(cls):
        """
        Setup interception for ANY logger that might create lexgenius.log files.
        """
        # Create a null handler that discards all logs
        null_handler = logging.NullHandler()
        
        # Target all potential lexgenius loggers
        lexgenius_logger_patterns = [
            'lexgenius',
            'lexgenius.fallback',
            'lexgenius.core',
            'lexgenius.main',
            'lexgenius.services',
            'lexgenius.pacer',
        ]
        
        for pattern in lexgenius_logger_patterns:
            try:
                logger = logging.getLogger(pattern)
                
                # Clear all existing handlers
                for handler in logger.handlers[:]:
                    logger.removeHandler(handler)
                    try:
                        handler.close()
                    except Exception:
                        pass
                
                # Add null handler to discard logs
                logger.addHandler(null_handler)
                logger.propagate = False
                logger.setLevel(logging.CRITICAL + 1)  # Effectively disable
                
                sys.stderr.write(f"NUCLEAR: Intercepted and disabled logger: {pattern}\n")
                
            except Exception as e:
                sys.stderr.write(f"NUCLEAR: Error setting up interception for {pattern}: {e}\n")
    
    @classmethod
    def _monitor_and_prevent_lexgenius_loggers(cls):
        """
        Set up monitoring to prevent NEW lexgenius loggers from being created.
        
        This patches the logging.getLogger function to intercept lexgenius logger creation.
        """
        # Store the original getLogger function
        if not hasattr(logging, '_original_getLogger'):
            logging._original_getLogger = logging.getLogger
        
        def patched_getLogger(name=None):
            """Patched getLogger that prevents lexgenius loggers."""
            logger = logging._original_getLogger(name)
            
            # If this is a lexgenius logger, neuter it immediately
            if name and ('lexgenius' in name.lower()):
                # Clear any handlers
                for handler in logger.handlers[:]:
                    logger.removeHandler(handler)
                    try:
                        handler.close()
                    except Exception:
                        pass
                
                # Add null handler and disable
                logger.addHandler(logging.NullHandler())
                logger.propagate = False
                logger.setLevel(logging.CRITICAL + 1)
                
                sys.stderr.write(f"NUCLEAR: Prevented new lexgenius logger: {name}\n")
            
            return logger
        
        # Install the patch
        logging.getLogger = patched_getLogger
        
        sys.stderr.write("NUCLEAR: Installed lexgenius logger prevention patch\n")
    
    @classmethod
    def continuously_delete_lexgenius_logs(cls, base_path: str = None, interval: int = 5):
        """
        Start a background thread that continuously monitors and deletes lexgenius.log files.
        
        Args:
            base_path: Base path to monitor (defaults to current directory)
            interval: Check interval in seconds (default: 5)
        """
        if base_path is None:
            base_path = os.getcwd()
        
        def monitor_and_delete():
            """Background function to continuously delete lexgenius logs."""
            import time
            
            while True:
                try:
                    # Delete any lexgenius logs that may have been created
                    deleted = cls._delete_all_lexgenius_logs(base_path)
                    
                    if deleted:
                        sys.stderr.write(f"NUCLEAR: Continuously deleted {len(deleted)} lexgenius log files\n")
                    
                    # Sleep for the interval
                    time.sleep(interval)
                    
                except Exception as e:
                    sys.stderr.write(f"NUCLEAR: Error in continuous monitoring: {e}\n")
                    time.sleep(interval)
        
        # Start the monitoring thread
        monitor_thread = threading.Thread(target=monitor_and_delete, daemon=True)
        monitor_thread.start()
        
        sys.stderr.write(f"NUCLEAR: Started continuous lexgenius.log deletion thread (interval: {interval}s)\n")
        return monitor_thread


def create_court_logger(court_id: str, iso_date: str, config: Dict[str, Any]) -> CourtLogger:
    """
    Factory function to create or get a CourtLogger instance.
    
    Args:
        court_id: Court identifier (e.g., 'cand', 'nysd')
        iso_date: ISO date string for directory organization
        config: Configuration dictionary
        
    Returns:
        CourtLogger instance
    """
    return CourtLogger.get_instance(court_id, iso_date, config)


@contextmanager
def setup_court_logging_context(court_id: str, iso_date: str, config: Dict[str, Any]):
    """
    Setup a COMPLETE court logging context that captures ALL logs and prevents escapes.
    
    This is the MOST ROBUST method to ensure logs only go to court-specific files.
    
    Usage:
        with setup_court_logging_context('cand', '20250110', config) as court_logger:
            # ALL logs within this block go ONLY to court-specific file
            # NO logs will escape to lexgenius-*.log or any other files
            process_court_data()
    """
    court_logger = create_court_logger(court_id, iso_date, config)
    
    # First, disable all other file handlers
    disabled_handlers = CourtLogger.disable_all_file_handlers(except_court_id=court_id)
    
    try:
        # Then use the interceptor context
        with court_logger.intercept_all_logs() as logger:
            yield logger
    finally:
        # Restore other file handlers
        CourtLogger.restore_file_handlers(disabled_handlers)


# Nuclear option convenience functions
@contextmanager
def nuclear_lexgenius_prevention(base_path: str = None, continuous_monitoring: bool = True):
    """
    NUCLEAR CONTEXT MANAGER: Completely prevent lexgenius.log creation.
    
    This is the most aggressive approach to ensure NO lexgenius.log files are created.
    
    Usage:
        with nuclear_lexgenius_prevention():
            # NO lexgenius.log files can be created within this block
            # All attempts are intercepted and either discarded or redirected
            process_data()
    
    Args:
        base_path: Base path to monitor for lexgenius logs (defaults to current directory)
        continuous_monitoring: Whether to start continuous background deletion (default: True)
    """
    if base_path is None:
        base_path = os.getcwd()
    
    # Activate nuclear prevention
    result = CourtLogger.nuclear_prevent_lexgenius_log(base_path)
    
    # Start continuous monitoring if requested
    monitor_thread = None
    if continuous_monitoring:
        monitor_thread = CourtLogger.continuously_delete_lexgenius_logs(base_path)
    
    try:
        sys.stderr.write("🚨 NUCLEAR LEXGENIUS PREVENTION ACTIVE 🚨\\n")
        sys.stderr.write(f"Removed {len(result['removed_handlers'])} handlers\\n")
        sys.stderr.write(f"Deleted {len(result['deleted_files'])} log files\\n")
        yield result
    finally:
        # Final cleanup - delete any logs that may have been created during execution
        final_deleted = CourtLogger._delete_all_lexgenius_logs(base_path)
        if final_deleted:
            sys.stderr.write(f"🚨 NUCLEAR: Final cleanup deleted {len(final_deleted)} lexgenius log files\\n")
        
        sys.stderr.write("🚨 NUCLEAR LEXGENIUS PREVENTION DEACTIVATED 🚨\\n")


def activate_nuclear_lexgenius_prevention(base_path: str = None) -> Dict[str, Any]:
    """
    Activate nuclear prevention of lexgenius.log creation (non-context version).
    
    This function can be called at the start of any script/process to prevent
    lexgenius.log creation for the entire duration of the process.
    
    Args:
        base_path: Base path to monitor for lexgenius logs
        
    Returns:
        Dict with information about what was prevented/removed
    """
    return CourtLogger.nuclear_prevent_lexgenius_log(base_path)


def emergency_delete_all_lexgenius_logs(base_path: str = None) -> List[str]:
    """
    Emergency function to immediately delete ALL lexgenius.log files.
    
    Args:
        base_path: Base path to search (defaults to current directory)
        
    Returns:
        List of deleted file paths
    """
    if base_path is None:
        base_path = os.getcwd()
    
    return CourtLogger._delete_all_lexgenius_logs(base_path)


# Convenience functions for backward compatibility
def get_court_logger(court_id: str, iso_date: str, config: Dict[str, Any]) -> logging.Logger:
    """Get a court-specific logger instance (backward compatibility)."""
    court_logger = create_court_logger(court_id, iso_date, config)
    return court_logger.get_logger()