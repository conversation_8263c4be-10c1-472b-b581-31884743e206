"""
Factory for creating PACER components with automatic dependency validation.
"""

from typing import Any, Dict, Optional, Type
from src.infrastructure.protocols.logger import LoggerProtocol
from src.pacer.utils.dependency_validator import DependencyValidator
from src.pacer.utils.dependency_integration import DependencyIntegration


class ValidatedComponentFactory:
    """
    Factory that creates PACER components with mandatory dependency validation.
    All components created through this factory are guaranteed to have valid dependencies.
    """
    
    def __init__(self, logger: LoggerProtocol):
        self.logger = logger
        self.validator = DependencyValidator()
        self.integration = DependencyIntegration()
        
    def create_sequential_workflow_manager(
        self,
        pacer_db: Any,
        async_dynamodb_storage: Any,
        s3_service: Any,
        logger: LoggerProtocol,
        **kwargs
    ) -> Any:
        """Create a validated SequentialWorkflowManager."""
        dependencies = {
            'pacer_db': pacer_db,
            'async_dynamodb_storage': async_dynamodb_storage,
            's3_service': s3_service,
            'logger': logger
        }
        
        # Import here to avoid circular imports
        from src.services.workflow.sequential_workflow_manager import SequentialWorkflowManager
        
        return self.integration.validate_and_initialize_component(
            SequentialWorkflowManager,
            dependencies,
            self.logger,
            **kwargs
        )
    
    def create_docket_processor(
        self,
        s3_service: Any,
        logger: LoggerProtocol,
        html_parser: Any,
        **kwargs
    ) -> Any:
        """Create a validated DocketProcessor."""
        dependencies = {
            's3_service': s3_service,
            'logger': logger,
            'html_parser': html_parser
        }
        
        from src.services.processing.docket_processor import DocketProcessor
        
        return self.integration.validate_and_initialize_component(
            DocketProcessor,
            dependencies,
            self.logger,
            **kwargs
        )
    
    def create_report_facade(
        self,
        logger: LoggerProtocol,
        browser_context: Any,
        navigation_facade: Any,
        **kwargs
    ) -> Any:
        """Create a validated ReportFacade."""
        dependencies = {
            'logger': logger,
            'browser_context': browser_context,
            'navigation_facade': navigation_facade
        }
        
        from src.pacer.facades.report_facade import ReportFacade
        
        return self.integration.validate_and_initialize_component(
            ReportFacade,
            dependencies,
            self.logger,
            **kwargs
        )
    
    def create_court_processor(
        self,
        logger: LoggerProtocol,
        browser_context: Any,
        configuration_service: Any,
        **kwargs
    ) -> Any:
        """Create a validated CourtProcessor."""
        dependencies = {
            'logger': logger,
            'browser_context': browser_context,
            'configuration_service': configuration_service
        }
        
        from src.pacer.services.court_processor import CourtProcessor
        
        return self.integration.validate_and_initialize_component(
            CourtProcessor,
            dependencies,
            self.logger,
            **kwargs
        )
    
    def create_authentication_service(
        self,
        logger: LoggerProtocol,
        browser_context: Any,
        configuration_service: Any,
        **kwargs
    ) -> Any:
        """Create a validated AuthenticationService."""
        dependencies = {
            'logger': logger,
            'browser_context': browser_context,
            'configuration_service': configuration_service
        }
        
        from src.pacer.services.authentication_service import AuthenticationService
        
        return self.integration.validate_and_initialize_component(
            AuthenticationService,
            dependencies,
            self.logger,
            **kwargs
        )
    
    def create_workflow_orchestrator(
        self,
        logger: LoggerProtocol,
        configuration_service: Any,
        pacer_db: Any,
        **kwargs
    ) -> Any:
        """Create a validated WorkflowOrchestrator."""
        dependencies = {
            'logger': logger,
            'configuration_service': configuration_service,
            'pacer_db': pacer_db
        }
        
        from src.services.workflow.workflow_orchestrator import WorkflowOrchestrator
        
        return self.integration.validate_and_initialize_component(
            WorkflowOrchestrator,
            dependencies,
            self.logger,
            **kwargs
        )
    
    def create_artifact_checker(
        self,
        logger: LoggerProtocol,
        s3_service: Any,
        pacer_db: Any,
        **kwargs
    ) -> Any:
        """Create a validated ArtifactChecker."""
        dependencies = {
            'logger': logger,
            's3_service': s3_service,
            'pacer_db': pacer_db
        }
        
        from src.services.processing.artifact_checker import ArtifactChecker
        
        return self.integration.validate_and_initialize_component(
            ArtifactChecker,
            dependencies,
            self.logger,
            **kwargs
        )
    
    def create_navigation_facade(
        self,
        logger: LoggerProtocol,
        browser_context: Any,
        **kwargs
    ) -> Any:
        """Create a validated NavigationFacade."""
        dependencies = {
            'logger': logger,
            'browser_context': browser_context
        }
        
        from src.pacer.facades.navigation_facade import NavigationFacade
        
        return self.integration.validate_and_initialize_component(
            NavigationFacade,
            dependencies,
            self.logger,
            **kwargs
        )
    
    def create_document_workflow_service(
        self,
        logger: LoggerProtocol,
        s3_service: Any,
        async_dynamodb_storage: Any,
        **kwargs
    ) -> Any:
        """Create a validated DocumentWorkflowService."""
        dependencies = {
            'logger': logger,
            's3_service': s3_service,
            'async_dynamodb_storage': async_dynamodb_storage
        }
        
        from src.services.workflow.document_workflow_service import DocumentWorkflowService
        
        return self.integration.validate_and_initialize_component(
            DocumentWorkflowService,
            dependencies,
            self.logger,
            **kwargs
        )
    
    def create_validated_pipeline(
        self,
        core_dependencies: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create a complete validated PACER processing pipeline.
        
        Args:
            core_dependencies: Dict containing core services like logger, s3_service, etc.
            
        Returns:
            Dict of component name -> validated component instance
        """
        self.logger.info("Creating validated PACER processing pipeline")
        
        # Validate core dependencies first
        required_core = [
            'logger',
            's3_service',
            'pacer_db',
            'async_dynamodb_storage',
            'configuration_service',
            'browser_context'
        ]
        
        self.validator.validate_required_dependencies(
            core_dependencies,
            required_core,
            self.logger,
            "PipelineFactory"
        )
        
        pipeline = {}
        
        try:
            # Create components in dependency order
            pipeline['navigation_facade'] = self.create_navigation_facade(
                core_dependencies['logger'],
                core_dependencies['browser_context']
            )
            
            pipeline['report_facade'] = self.create_report_facade(
                core_dependencies['logger'],
                core_dependencies['browser_context'],
                pipeline['navigation_facade']
            )
            
            pipeline['authentication_service'] = self.create_authentication_service(
                core_dependencies['logger'],
                core_dependencies['browser_context'],
                core_dependencies['configuration_service']
            )
            
            pipeline['court_processor'] = self.create_court_processor(
                core_dependencies['logger'],
                core_dependencies['browser_context'],
                core_dependencies['configuration_service']
            )
            
            pipeline['docket_processor'] = self.create_docket_processor(
                core_dependencies['s3_service'],
                core_dependencies['logger'],
                None  # html_parser can be None for now
            )
            
            pipeline['workflow_orchestrator'] = self.create_workflow_orchestrator(
                core_dependencies['logger'],
                core_dependencies['configuration_service'],
                core_dependencies['pacer_db']
            )
            
            pipeline['artifact_checker'] = self.create_artifact_checker(
                core_dependencies['logger'],
                core_dependencies['s3_service'],
                core_dependencies['pacer_db']
            )
            
            pipeline['document_workflow_service'] = self.create_document_workflow_service(
                core_dependencies['logger'],
                core_dependencies['s3_service'],
                core_dependencies['async_dynamodb_storage']
            )
            
            pipeline['sequential_workflow_manager'] = self.create_sequential_workflow_manager(
                core_dependencies['pacer_db'],
                core_dependencies['async_dynamodb_storage'],
                core_dependencies['s3_service'],
                core_dependencies['logger']
            )
            
            self.logger.info(f"✓ Successfully created validated pipeline with {len(pipeline)} components")
            
            # Final validation of entire pipeline
            from src.pacer.utils.dependency_integration import validate_pacer_pipeline_dependencies
            validate_pacer_pipeline_dependencies(pipeline, self.logger)
            
            return pipeline
            
        except Exception as e:
            self.logger.critical(f"Failed to create validated pipeline: {e}")
            raise
    
    def validate_existing_component(self, component: Any) -> bool:
        """
        Validate an existing component instance.
        
        Returns:
            True if valid, False otherwise
        """
        try:
            self.validator.validate_component_dependencies(component, self.logger)
            return True
        except SystemExit:
            raise
        except Exception as e:
            self.logger.error(f"Component validation failed: {e}")
            return False