"""
Container S3 Injection Fix

This utility provides functions to fix S3 service injection in existing PACER containers
without requiring major refactoring of the existing dependency injection system.
"""

from typing import Any, Dict, Optional
from dependency_injector import providers

from src.services.html.html_service_factory_enhanced import EnhancedHtmlServiceFactory
from src.pacer.utils.s3_injection_helper import S3InjectionHelper


class ContainerS3InjectionFix:
    """Utility class to fix S3 injection in existing containers."""
    
    @staticmethod
    def enhance_html_processing_facade(
        container,
        logger,
        config,
        html_parser,
        law_firm_corrector, 
        s3_manager,
        field_consistency_manager,
        transfer_info_processor
    ):
        """
        Enhanced HTML processing facade provider with proper S3 injection.
        
        This replaces the existing html_processing_facade provider in the PACER container
        with an enhanced version that ensures S3 services are properly injected.
        """
        # Extract S3AsyncStorage from S3Manager if available
        s3_async_storage = None
        if s3_manager and hasattr(s3_manager, 's3_async_storage'):
            s3_async_storage = s3_manager.s3_async_storage
        elif hasattr(s3_manager, 'provided') and hasattr(s3_manager.provided, 's3_async_storage'):
            s3_async_storage = s3_manager.provided.s3_async_storage
        
        # Create enhanced HTML processing orchestrator
        enhanced_orchestrator = EnhancedHtmlServiceFactory.create_enhanced_html_processing_orchestrator(
            logger=logger,
            config=config or {},
            court_id=None,  # Will be set at runtime
            html_data_updater=None,  # Can be injected later
            s3_async_storage=s3_async_storage,
            s3_manager=s3_manager
        )
        
        # Validate S3 injection
        S3InjectionHelper.verify_s3_injection(enhanced_orchestrator, logger)
        
        # Log enhancement
        if logger:
            logger.info("Enhanced HTML processing facade created with S3 injection fix")
        
        return enhanced_orchestrator
    
    @staticmethod
    def create_enhanced_s3_manager_provider(storage_container):
        """
        Create enhanced S3Manager provider with proper S3AsyncStorage injection.
        
        Args:
            storage_container: Storage container reference
            
        Returns:
            Enhanced S3Manager provider
        """
        @providers.Factory
        def enhanced_s3_manager_factory(logger, config, s3_async_storage):
            """Factory for enhanced S3Manager with validated S3 injection."""
            from src.pacer.components.download.s3_manager import S3Manager
            
            # Create S3Manager with S3AsyncStorage
            s3_manager = S3Manager(
                logger=logger,
                config=config,
                s3_async_storage=s3_async_storage
            )
            
            # Validate injection
            if s3_async_storage:
                S3InjectionHelper.verify_s3_injection(s3_manager, logger)
                if logger:
                    logger.info("Enhanced S3Manager created with validated S3AsyncStorage injection")
            else:
                if logger:
                    logger.warning("Enhanced S3Manager created but S3AsyncStorage is None")
            
            return s3_manager
        
        return enhanced_s3_manager_factory
    
    @staticmethod
    def patch_pacer_container_s3_injection(container):
        """
        Patch existing PACER container to fix S3 injection issues.
        
        This method updates the existing container providers to ensure
        S3 services are properly injected into HTML processing components.
        
        Args:
            container: PACER container instance to patch
        """
        # Store original providers for reference
        original_s3_manager = getattr(container, 's3_manager', None)
        original_html_processing_facade = getattr(container, 'html_processing_facade', None)
        
        # Enhanced S3Manager provider
        if hasattr(container, 'storage_container') and hasattr(container.storage_container, 's3_async_storage'):
            container.enhanced_s3_manager = providers.Singleton(
                ContainerS3InjectionFix.create_enhanced_s3_manager_provider(container.storage_container),
                logger=container.logger,
                config=container.config,
                s3_async_storage=container.storage_container.s3_async_storage
            )
        
        # Enhanced HTML processing facade provider
        if hasattr(container, 'html_processing_facade'):
            container.enhanced_html_processing_facade = providers.Factory(
                ContainerS3InjectionFix.enhance_html_processing_facade,
                container=container,
                logger=container.logger,
                config=container.config,
                html_parser=getattr(container, 'html_parser', None),
                law_firm_corrector=getattr(container, 'law_firm_corrector', None),
                s3_manager=getattr(container, 'enhanced_s3_manager', getattr(container, 's3_manager', None)),
                field_consistency_manager=getattr(container, 'field_consistency_manager', None),
                transfer_info_processor=getattr(container, 'transfer_info_processor', None)
            )
        
        # Add S3 injection validation provider
        container.s3_injection_validator = providers.Factory(
            S3InjectionHelper.verify_s3_injection,
            component=providers.Dependency(),
            logger=container.logger
        )
        
        # Add S3 service bundle provider
        if hasattr(container, 'storage_container') and hasattr(container.storage_container, 's3_async_storage'):
            container.s3_service_bundle = providers.Factory(
                S3InjectionHelper.create_s3_service_bundle,
                s3_async_storage=container.storage_container.s3_async_storage,
                logger=container.logger
            )
        
        return container
    
    @staticmethod
    def validate_container_s3_injection(container, logger=None):
        """
        Validate S3 injection across all relevant providers in a container.
        
        Args:
            container: Container to validate
            logger: Optional logger for validation results
            
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            'storage_container_s3_async_storage': False,
            's3_manager_s3_injection': False,
            'html_processing_facade_s3_injection': False,
            'enhanced_providers_available': False,
            'overall_validation': False
        }
        
        try:
            # Check storage container S3AsyncStorage
            if hasattr(container, 'storage_container'):
                storage_container = container.storage_container
                if hasattr(storage_container, 's3_async_storage'):
                    try:
                        s3_storage = storage_container.s3_async_storage()
                        validation_results['storage_container_s3_async_storage'] = s3_storage is not None
                    except:
                        validation_results['storage_container_s3_async_storage'] = False
            
            # Check S3Manager injection
            if hasattr(container, 's3_manager') or hasattr(container, 'enhanced_s3_manager'):
                s3_manager_provider = getattr(container, 'enhanced_s3_manager', getattr(container, 's3_manager', None))
                if s3_manager_provider:
                    try:
                        s3_manager = s3_manager_provider()
                        validation_results['s3_manager_s3_injection'] = hasattr(s3_manager, 's3_async_storage') and s3_manager.s3_async_storage is not None
                    except:
                        validation_results['s3_manager_s3_injection'] = False
            
            # Check HTML processing facade injection
            if hasattr(container, 'html_processing_facade') or hasattr(container, 'enhanced_html_processing_facade'):
                html_facade_provider = getattr(container, 'enhanced_html_processing_facade', getattr(container, 'html_processing_facade', None))
                if html_facade_provider:
                    try:
                        html_facade = html_facade_provider()
                        validation_results['html_processing_facade_s3_injection'] = S3InjectionHelper.verify_s3_injection(html_facade)
                    except:
                        validation_results['html_processing_facade_s3_injection'] = False
            
            # Check if enhanced providers are available
            validation_results['enhanced_providers_available'] = (
                hasattr(container, 'enhanced_s3_manager') or 
                hasattr(container, 'enhanced_html_processing_facade') or
                hasattr(container, 's3_service_bundle')
            )
            
            # Overall validation
            validation_results['overall_validation'] = (
                validation_results['storage_container_s3_async_storage'] and
                (validation_results['s3_manager_s3_injection'] or validation_results['html_processing_facade_s3_injection'])
            )
            
        except Exception as e:
            if logger:
                logger.error(f"Error validating container S3 injection: {e}")
        
        # Log results
        if logger:
            logger.info("Container S3 injection validation results:", extra=validation_results)
        
        return validation_results
    
    @staticmethod
    def apply_runtime_s3_injection_fix(html_processing_component, s3_async_storage, logger=None):
        """
        Apply S3 injection fix to HTML processing component at runtime.
        
        This can be used to fix S3 injection issues for components that have
        already been instantiated but are missing S3 services.
        
        Args:
            html_processing_component: HTML processing component to fix
            s3_async_storage: S3AsyncStorage instance to inject
            logger: Optional logger
            
        Returns:
            True if fix was applied successfully, False otherwise
        """
        try:
            # Inject S3AsyncStorage directly
            if hasattr(html_processing_component, 's3_async_storage'):
                html_processing_component.s3_async_storage = s3_async_storage
            
            # Inject via S3Manager if available
            if hasattr(html_processing_component, 's3_manager') and html_processing_component.s3_manager:
                S3InjectionHelper.inject_s3_into_existing_component(
                    html_processing_component.s3_manager, 
                    s3_async_storage, 
                    logger
                )
            
            # Inject via set_s3_manager method if available
            if hasattr(html_processing_component, 'set_s3_manager'):
                html_processing_component.set_s3_manager(s3_async_storage)
            
            # Validate injection
            validation_result = S3InjectionHelper.verify_s3_injection(html_processing_component, logger)
            
            if logger:
                status = "SUCCESS" if validation_result else "FAILED"
                logger.info(f"Runtime S3 injection fix {status} for {type(html_processing_component).__name__}")
            
            return validation_result
            
        except Exception as e:
            if logger:
                logger.error(f"Runtime S3 injection fix failed: {e}")
            return False