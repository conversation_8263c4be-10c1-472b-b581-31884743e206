"""
Service Registry for PACER Core Services.

This module provides a centralized registry for managing core service instances,
following the Registry pattern from DESIGN_PATTERNS.md and supporting the 
consolidation of 23+ facade services into 9 core services.
Moved from src/services/pacer/_core_services/registry/service_registry.py
"""

from __future__ import annotations
import asyncio
from typing import Any, Dict, List, Optional, Type, TypeVar, TYPE_CHECKING
from enum import Enum

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import ConfigurationError, ServiceError
from src.pacer.interfaces.pacer_protocol import (
    PacerCoreServiceProtocol,
    ServiceHealthStatus
)

if TYPE_CHECKING:
    from dependency_injector import containers
    from src.infrastructure.protocols.logger import LoggerProtocol

T = TypeVar('T', bound=AsyncServiceBase)


class ServiceStatus(Enum):
    """Service status enumeration."""
    NOT_INITIALIZED = "not_initialized"
    INITIALIZING = "initializing"
    READY = "ready"
    ERROR = "error"
    SHUTTING_DOWN = "shutting_down"
    SHUTDOWN = "shutdown"


class ServiceInfo:
    """Information about a registered service."""
    
    def __init__(self, 
                 service_name: str,
                 service_class: Type[AsyncServiceBase],
                 dependencies: List[str] = None,
                 singleton: bool = True):
        self.service_name = service_name
        self.service_class = service_class
        self.dependencies = dependencies or []
        self.singleton = singleton
        self.instance: Optional[AsyncServiceBase] = None
        self.status = ServiceStatus.NOT_INITIALIZED
        self.initialization_time: Optional[float] = None
        self.error_message: Optional[str] = None


class PacerCoreServiceRegistry:
    """
    Registry for managing PACER core service instances.
    
    Provides:
    - Service registration and discovery
    - Dependency resolution
    - Service lifecycle management
    - Health monitoring
    - Graceful shutdown
    """

    def __init__(self, 
                 container: Optional[containers.DeclarativeContainer] = None,
                 logger: Optional[LoggerProtocol] = None):
        self._container = container
        self._logger = logger
        self._services: Dict[str, ServiceInfo] = {}
        self._instances: Dict[str, AsyncServiceBase] = {}
        self._initialization_lock = asyncio.Lock()
        self._shutdown_requested = False
        
        # Core service definitions (9 core services)
        self._core_services = {
            "configuration_service": {
                "class": "ConfigurationService",
                "dependencies": [],
                "singleton": True
            },
            "browser_service": {
                "class": "BrowserService",
                "dependencies": ["configuration_service"],
                "singleton": True
            },
            "case_processing_service": {
                "class": "CaseProcessingService", 
                "dependencies": ["configuration_service"],
                "singleton": True
            },
            "relevance_service": {
                "class": "RelevanceService",
                "dependencies": ["configuration_service"],
                "singleton": True
            },
            "classification_service": {
                "class": "ClassificationService",
                "dependencies": ["configuration_service"],
                "singleton": True
            },
            "verification_service": {
                "class": "VerificationService",
                "dependencies": ["configuration_service"],
                "singleton": True
            },
            "download_orchestration_service": {
                "class": "DownloadOrchestrationService",
                "dependencies": ["configuration_service", "verification_service", "browser_service"],
                "singleton": True
            },
            "file_operations_service": {
                "class": "FileOperationsService",
                "dependencies": ["configuration_service"],
                "singleton": True
            },
            "metrics_reporting_service": {
                "class": "MetricsReportingService",
                "dependencies": ["configuration_service"],
                "singleton": True
            },
            "s3_management_service": {
                "class": "S3ManagementService",
                "dependencies": ["configuration_service"],
                "singleton": True
            },
            "docket_processing_orchestrator": {
                "class": "DocketProcessingOrchestrator",
                "dependencies": [
                    "configuration_service",
                    "browser_service",
                    "case_processing_service",
                    "relevance_service",
                    "verification_service",
                    "download_orchestration_service",
                    "file_operations_service",
                    "metrics_reporting_service",
                    "s3_management_service"
                ],
                "singleton": True
            }
        }

    async def register_service(self, 
                             service_name: str,
                             service_class: Type[AsyncServiceBase],
                             dependencies: List[str] = None,
                             singleton: bool = True) -> None:
        """
        Register a service with the registry.
        
        Args:
            service_name: Unique service identifier
            service_class: Service class to instantiate
            dependencies: List of dependency service names
            singleton: Whether to maintain single instance
        """
        if service_name in self._services:
            raise ConfigurationError(f"Service '{service_name}' is already registered")
        
        service_info = ServiceInfo(
            service_name=service_name,
            service_class=service_class,
            dependencies=dependencies or [],
            singleton=singleton
        )
        
        self._services[service_name] = service_info
        
        if self._logger:
            self._logger.info(f"Registered service: {service_name}", extra={
                "service_class": service_class.__name__,
                "dependencies": dependencies or [],
                "singleton": singleton
            })

    async def get_service(self, service_name: str) -> AsyncServiceBase:
        """
        Get a service instance by name, creating it if necessary.
        
        Args:
            service_name: Name of the service to retrieve
            
        Returns:
            Service instance
            
        Raises:
            ConfigurationError: If service is not registered
            ServiceError: If service initialization fails
        """
        if self._shutdown_requested:
            raise ServiceError("Registry is shutting down - cannot provide services")
            
        if service_name not in self._services:
            raise ConfigurationError(f"Service '{service_name}' is not registered")
        
        service_info = self._services[service_name]
        
        # Return existing instance for singletons
        if service_info.singleton and service_info.instance:
            if service_info.status == ServiceStatus.READY:
                return service_info.instance
            elif service_info.status == ServiceStatus.ERROR:
                raise ServiceError(f"Service '{service_name}' is in error state: {service_info.error_message}")
        
        # Create new instance with dependency resolution
        async with self._initialization_lock:
            return await self._create_service_instance(service_name)

    async def _create_service_instance(self, service_name: str) -> AsyncServiceBase:
        """Create and initialize a service instance with dependency resolution."""
        service_info = self._services[service_name]
        
        if service_info.status == ServiceStatus.INITIALIZING:
            # Wait for ongoing initialization
            while service_info.status == ServiceStatus.INITIALIZING:
                await asyncio.sleep(0.1)
            
            if service_info.status == ServiceStatus.READY and service_info.instance:
                return service_info.instance
            elif service_info.status == ServiceStatus.ERROR:
                raise ServiceError(f"Service '{service_name}' initialization failed: {service_info.error_message}")
        
        try:
            service_info.status = ServiceStatus.INITIALIZING
            
            # Resolve dependencies first
            dependency_instances = {}
            for dep_name in service_info.dependencies:
                if dep_name not in self._services:
                    raise ConfigurationError(f"Dependency '{dep_name}' for service '{service_name}' is not registered")
                dependency_instances[dep_name] = await self.get_service(dep_name)
            
            # Create service instance
            if self._container:
                # Use DI container if available
                service_instance = getattr(self._container, service_name)()
            else:
                # Manual instantiation
                service_instance = await self._manual_service_creation(service_name, dependency_instances)
            
            # Initialize the service
            await service_instance.initialize()
            
            # Store instance for singletons
            if service_info.singleton:
                service_info.instance = service_instance
                self._instances[service_name] = service_instance
            
            service_info.status = ServiceStatus.READY
            service_info.initialization_time = asyncio.get_event_loop().time()
            
            if self._logger:
                self._logger.info(f"Service '{service_name}' initialized successfully", extra={
                    "dependencies": service_info.dependencies,
                    "singleton": service_info.singleton
                })
            
            return service_instance
            
        except Exception as e:
            service_info.status = ServiceStatus.ERROR
            service_info.error_message = str(e)
            
            if self._logger:
                self._logger.error(f"Failed to initialize service '{service_name}': {str(e)}", exc_info=True)
            
            raise ServiceError(f"Service '{service_name}' initialization failed: {str(e)}")

    async def _manual_service_creation(self, service_name: str, dependencies: Dict[str, AsyncServiceBase]) -> AsyncServiceBase:
        """Manually create service instance when DI container is not available."""
        # This would need to be implemented based on specific service requirements
        # For now, raise an error to indicate DI container is required
        raise ConfigurationError(f"Manual service creation not implemented for '{service_name}' - DI container required")

    async def get_all_services(self) -> Dict[str, AsyncServiceBase]:
        """Get all initialized service instances."""
        services = {}
        for service_name in self._services.keys():
            try:
                services[service_name] = await self.get_service(service_name)
            except Exception as e:
                if self._logger:
                    self._logger.warning(f"Could not get service '{service_name}': {str(e)}")
        return services

    async def initialize_all_services(self) -> Dict[str, bool]:
        """Initialize all registered services."""
        results = {}
        
        # Sort services by dependency order
        sorted_services = self._sort_services_by_dependencies()
        
        for service_name in sorted_services:
            try:
                await self.get_service(service_name)
                results[service_name] = True
                if self._logger:
                    self._logger.info(f"Successfully initialized service: {service_name}")
            except Exception as e:
                results[service_name] = False
                if self._logger:
                    self._logger.error(f"Failed to initialize service '{service_name}': {str(e)}")
        
        return results

    def _sort_services_by_dependencies(self) -> List[str]:
        """Sort services by dependency order using topological sort."""
        visited = set()
        temp_visited = set()
        result = []
        
        def visit(service_name: str):
            if service_name in temp_visited:
                raise ConfigurationError(f"Circular dependency detected involving service '{service_name}'")
            
            if service_name not in visited:
                temp_visited.add(service_name)
                
                service_info = self._services.get(service_name)
                if service_info:
                    for dep in service_info.dependencies:
                        visit(dep)
                
                temp_visited.remove(service_name)
                visited.add(service_name)
                result.append(service_name)
        
        for service_name in self._services.keys():
            if service_name not in visited:
                visit(service_name)
        
        return result

    async def health_check(self) -> ServiceHealthStatus:
        """Perform health check on all services."""
        health_status = {
            "registry_status": "healthy",
            "services": {},
            "summary": {
                "total_services": len(self._services),
                "healthy_services": 0,
                "unhealthy_services": 0,
                "not_initialized": 0
            }
        }
        
        for service_name, service_info in self._services.items():
            try:
                if service_info.instance and service_info.status == ServiceStatus.READY:
                    service_health = await service_info.instance.health_check()
                    health_status["services"][service_name] = service_health
                    
                    if service_health.get("status") == "healthy":
                        health_status["summary"]["healthy_services"] += 1
                    else:
                        health_status["summary"]["unhealthy_services"] += 1
                else:
                    health_status["services"][service_name] = {
                        "status": service_info.status.value,
                        "error": service_info.error_message
                    }
                    health_status["summary"]["not_initialized"] += 1
                    
            except Exception as e:
                health_status["services"][service_name] = {
                    "status": "error",
                    "error": str(e)
                }
                health_status["summary"]["unhealthy_services"] += 1
        
        # Determine overall registry health
        if health_status["summary"]["unhealthy_services"] > 0:
            health_status["registry_status"] = "degraded"
        
        if health_status["summary"]["healthy_services"] == 0:
            health_status["registry_status"] = "unhealthy"
        
        return health_status

    async def shutdown(self, timeout: float = 30.0) -> None:
        """Gracefully shutdown all services."""
        self._shutdown_requested = True
        
        if self._logger:
            self._logger.info("Starting graceful shutdown of all services")
        
        # Shutdown services in reverse dependency order
        sorted_services = self._sort_services_by_dependencies()
        sorted_services.reverse()
        
        shutdown_tasks = []
        for service_name in sorted_services:
            service_info = self._services[service_name]
            if service_info.instance and service_info.status == ServiceStatus.READY:
                service_info.status = ServiceStatus.SHUTTING_DOWN
                shutdown_tasks.append(self._shutdown_service(service_name, service_info.instance))
        
        # Wait for all shutdowns to complete with timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*shutdown_tasks, return_exceptions=True),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            if self._logger:
                self._logger.warning("Service shutdown timeout exceeded")
        
        # Mark all services as shutdown
        for service_info in self._services.values():
            service_info.status = ServiceStatus.SHUTDOWN
            service_info.instance = None
        
        self._instances.clear()
        
        if self._logger:
            self._logger.info("Service registry shutdown completed")

    async def _shutdown_service(self, service_name: str, service_instance: AsyncServiceBase) -> None:
        """Shutdown a single service."""
        try:
            await service_instance.cleanup()
            if self._logger:
                self._logger.info(f"Service '{service_name}' shutdown successfully")
        except Exception as e:
            if self._logger:
                self._logger.error(f"Error shutting down service '{service_name}': {str(e)}")

    def get_service_info(self, service_name: str) -> Optional[ServiceInfo]:
        """Get information about a registered service."""
        return self._services.get(service_name)

    def list_services(self) -> List[str]:
        """List all registered service names."""
        return list(self._services.keys())

    def get_service_status(self, service_name: str) -> Optional[ServiceStatus]:
        """Get the status of a specific service."""
        service_info = self._services.get(service_name)
        return service_info.status if service_info else None

    async def register_core_services(self) -> None:
        """Register all core PACER services."""
        # This would need to import the actual service classes
        # For now, just log the registration intent
        if self._logger:
            self._logger.info("Core service registration would be implemented here", extra={
                "core_services": list(self._core_services.keys())
            })