# /src/pacer/core/download_orchestration/download_orchestration_service.py

"""
Download Orchestration Service for PACER processing.

Consolidates DownloadOrchestrationFacadeService into a unified service that handles 
all download workflow orchestration following PHASE 4: Download Workflow from docket_processing.md.
Updated to match EXACT legacy behavior from legacy_pacer_spec.md Section 2.5.
"""

from __future__ import annotations
from typing import Any, Dict, List, Optional, TYPE_CHECKING
import os
import json
import uuid
import re
import shutil
from pathlib import Path
from datetime import datetime

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.pacer.interfaces.pacer_protocol import DownloadOrchestrationServiceInterface

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol
    from playwright.async_api import Page


class DownloadOrchestrationService(AsyncServiceBase):
    """
    Consolidated download orchestration service for PACER processing.
    
    This service implements PHASE 4 of the docket processing lifecycle:
    Download Workflow - process_download_workflow method that:
    1. Validates case for download
    2. Checks ignore_download_config.json
    3. Handles document checkboxes (uncheck all except first)
    4. Executes document download to temp directory
    5. Moves files to final location with legacy naming
    """

    def __init__(self,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        
        # Dependencies will be injected
        self._download_validator = None
        self._file_downloader = None
        self._download_manager = None
        self._s3_manager = None
        self._authentication_service = None
        self._navigation_service = None
        
        # Legacy workflow configuration
        self._ignore_download_config = None
        self._config_path = Path('src/config/pacer/ignore_download/ignore_download.json')

    async def _initialize_service(self) -> None:
        """Initialize the download orchestration service."""
        # Get injected dependencies
        self._download_validator = self.get_dependency('download_validator')
        self._file_downloader = self.get_dependency('file_downloader')
        self._download_manager = self.get_dependency('download_manager')
        self._s3_manager = self.get_dependency('s3_manager')
        self._authentication_service = self.get_dependency('authentication_service')
        self._navigation_service = self.get_dependency('navigation_service')
        
        # Load ignore download configuration
        await self._load_ignore_download_config()
        
        self.log_info("Download orchestration service initialized successfully")

    def _get_logger_context(self, court_id: str = "", docket_num: str = "") -> Dict[str, Any]:
        """Get logger context with consistent [court_id][docket_num] prefix format."""
        context = {"service": "DownloadOrchestrationService"}
        if court_id or docket_num:
            context["case_prefix"] = f"[{court_id}][{docket_num}]"
        return context

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        court_id = data.get('court_id', '')
        docket_num = data.get('docket_num', '')
        
        context = self._get_logger_context(court_id, docket_num)
        self.log_debug(f"Executing download orchestration action: {action}", context)
        
        if action == 'process_download_workflow':
            return await self.process_download_workflow(
                data['case_details'],
                data.get('is_explicitly_requested', False),
                data.get('page'),
                court_id=court_id,
                docket_num=docket_num
            )
        elif action == 'execute_download_workflow':
            return await self.execute_download_workflow(data['case_details'])
        elif action == 'authenticate_session':
            return await self.authenticate_session(data['credentials'])
        elif action == 'navigate_to_case':
            return await self.navigate_to_case(data['case_details'])
        elif action == 'execute_query':
            return await self.execute_query(data['query_params'])
        elif action == 'upload_html_to_s3':
            return await self.upload_html_to_s3(
                data['base_filename'], 
                data['html_content'], 
                data['iso_date']
            )
        else:
            raise PacerServiceError(f"Unknown action for DownloadOrchestrationService: {action}")

    async def process_download_workflow(self, 
                                      case_details: Dict[str, Any],
                                      is_explicitly_requested: bool = False,
                                      page: Optional[Page] = None,
                                      court_id: str = "",
                                      docket_num: str = "",
                                      court_logger: Optional[Any] = None) -> Dict[str, Any]:
        """
        PHASE 4: Download Workflow with EXACT Step 2.5 legacy behavior
        
        Implements the download workflow from legacy_pacer_spec.md:
        1. Check should_skip_download() against ignore_download_config.json
        2. If not skipping:
           - Attach download event listener
           - Call handle_document_checkboxes_async() (uncheck all except first)
           - Click "Download Documents" or "View Document"
           - Save to temp: data/{iso_date}/dockets/temp/{court_id}_..._{uuid}/
           - Move to final: data/{iso_date}/dockets/{court_id}_{year}_{case_num}_{versus}.pdf
        
        Args:
            case_details: Case details dictionary
            is_explicitly_requested: Whether this is an explicit request
            page: Browser page object
            court_id: Court identifier for logging
            docket_num: Docket number for logging
            court_logger: Court-specific logger for consistent logging
            
        Returns:
            Updated case details with download results
        """
        # Use court_logger if provided, otherwise fall back to self.logger
        logger = court_logger or self.logger
        
        context = self._get_logger_context(court_id, docket_num)
        logger.info("Starting PHASE 4: Download Workflow", extra=context)
        
        # Make a copy to avoid modifying the original
        updated_case_details = case_details.copy()
        
        # LEGACY Step 2.7.6: Check ignore_download_config.json first
        if await self._should_skip_download(case_details, court_id):
            logger.info(f"Skipping download for {court_id} based on ignore_download_config.json", extra=context)
            updated_case_details.update({
                'is_downloaded': False,
                'download_skipped': True,
                'skip_reason': 'ignore_download_config',
                'html_only': True
            })
            return updated_case_details
        
        # P4_2: Validate Case for Download
        logger.debug("P4_2: Validate Case for Download", extra=context)
        should_download, skip_reason = await self._validate_case_for_download(case_details, is_explicitly_requested)
        
        # P4_3: Should Attempt Download?
        if not should_download:
            # P4_SKIP: Skip Download with Reason
            logger.debug(f"P4_SKIP: Skip Download - {skip_reason}", extra=context)
            updated_case_details.update({
                'is_downloaded': False,
                'download_skipped': True,
                'skip_reason': skip_reason,
                'html_only': True
            })
            return updated_case_details
        
        # P4_4: Prepare Download Context with Legacy paths
        logger.debug("P4_4: Prepare Download Context", extra=context)
        download_context = await self._prepare_download_context_legacy(case_details, page, court_id)
        
        # P4_5: Execute Document Download with Legacy workflow
        logger.debug("P4_5: Execute Document Download", extra=context)
        download_result = await self._execute_document_download_legacy(case_details, download_context, page)
        
        # P4_6: Handle Success/Failure
        if download_result.get('success', False):
            # P4_SUCCESS: Handle Download Success
            logger.debug("P4_SUCCESS: Handle Download Success", extra=context)
            updated_case_details.update({
                'is_downloaded': True,
                'download_success': True,
                'download_files': download_result.get('files', []),
                'download_metadata': download_result.get('metadata', {}),
                'html_only': False,
                'base_filename': download_context['base_filename'],
                'original_filename': download_result['metadata'].get('original_filename'),
                'new_filename': os.path.basename(download_result['metadata'].get('final_path', ''))
            })
        else:
            # P4_FAIL: Handle Download Failure
            self.log_debug("P4_FAIL: Handle Download Failure", context)
            updated_case_details.update({
                'is_downloaded': False,
                'download_failed': True,
                'download_error': download_result.get('error', 'Unknown download error'),
                'html_only': True
            })
        
        self.log_info("PHASE 4: Download Workflow completed", context)
        return updated_case_details

    async def _load_ignore_download_config(self) -> None:
        """Load the ignore_download_config.json file."""
        try:
            if self._config_path.exists():
                with open(self._config_path, 'r') as f:
                    self._ignore_download_config = json.load(f)
                    self.log_info(f"Loaded ignore_download_config from {self._config_path}")
            else:
                self.log_warning(f"ignore_download_config.json not found at {self._config_path}")
                self._ignore_download_config = {}
        except Exception as e:
            self.log_error(f"Failed to load ignore_download_config: {str(e)}")
            self._ignore_download_config = {}
    
    async def _should_skip_download(self, case_details: Dict[str, Any], court_id: str) -> bool:
        """Check if download should be skipped based on ignore_download_config.json."""
        if not self._ignore_download_config:
            return False
        
        # Check court-level ignore rules
        court_rules = self._ignore_download_config.get(court_id, [])
        if not isinstance(court_rules, list):
            court_rules = [court_rules]
            
        for rule in court_rules:
            # Check attorney-based rules
            if 'attorney_name' in rule:
                if case_details.get('attorney_name') == rule['attorney_name']:
                    self.log_info(f"Skipping download: attorney match {rule['attorney_name']}")
                    return True
            
            # Check MDL-based rules
            if 'mdl_num' in rule:
                if case_details.get('mdl_num') == rule['mdl_num']:
                    self.log_info(f"Skipping download: MDL match {rule['mdl_num']}")
                    return True
            
            # Check defendant-based rules
            if 'defendants' in rule:
                case_defendants = case_details.get('defendants', [])
                for defendant in rule['defendants']:
                    if defendant in case_defendants:
                        self.log_info(f"Skipping download: defendant match {defendant}")
                        return True
            
            # Check flags
            if 'flags' in rule:
                case_flags = case_details.get('flags', [])
                for flag in rule.get('flags', []):
                    if flag in case_flags:
                        self.log_info(f"Skipping download: flag match {flag}")
                        return True
        
        return False
    
    def _generate_temp_path(self, iso_date: str, court_id: str) -> str:
        """Generate temporary download path following legacy spec."""
        # Legacy format: data/{iso_date}/dockets/temp/{court_id}_..._{uuid}/
        unique_id = str(uuid.uuid4())[:8]
        return f"data/{iso_date}/dockets/temp/{court_id}_download_{unique_id}"
    
    def _generate_final_path(self, iso_date: str, court_id: str, case_details: Dict[str, Any]) -> str:
        """Generate final file path following legacy spec."""
        # Use the base filename and add .pdf extension
        base_filename = self._generate_base_filename(court_id, case_details)
        
        # Legacy format: data/{iso_date}/dockets/{base_filename}.pdf
        return f"data/{iso_date}/dockets/{base_filename}.pdf"
    
    def _extract_versus_field(self, case_details: Dict[str, Any]) -> str:
        """Extract and clean the versus field from case details."""
        case_title = case_details.get('case_title', '')
        
        # Try to extract plaintiff v. defendant pattern
        versus_match = re.search(r'(.+?)\s+v\.?\s+(.+)', case_title, re.IGNORECASE)
        if versus_match:
            plaintiff = versus_match.group(1).strip()
            defendant = versus_match.group(2).strip()
            # Clean and truncate for filename
            versus = f"{plaintiff}_v_{defendant}"
        else:
            versus = case_title
        
        # Clean for filename safety
        versus = re.sub(r'[^\w\s\-]', '', versus)
        versus = re.sub(r'\s+', '_', versus)
        
        # Truncate if too long
        if len(versus) > 50:
            versus = versus[:50]
        
        return versus or "Unknown_Party"
    
    async def _prepare_download_context_legacy(self, 
                                               case_details: Dict[str, Any], 
                                               page: Optional[Page],
                                               court_id: str) -> Dict[str, Any]:
        """Prepare download context with legacy path structure."""
        iso_date = datetime.now().strftime('%Y%m%d')
        
        # Generate paths according to legacy spec
        temp_path = self._generate_temp_path(iso_date, court_id)
        final_path = self._generate_final_path(iso_date, court_id, case_details)
        
        # Create temp directory if it doesn't exist
        os.makedirs(temp_path, exist_ok=True)
        
        download_context = {
            'case_details': case_details,
            'page': page,
            'temp_download_path': temp_path,
            'final_path': final_path,
            'court_id': court_id,
            'docket_num': case_details.get('docket_num'),
            'base_filename': self._generate_base_filename(court_id, case_details),
            'iso_date': iso_date
        }
        
        self.log_info(f"Prepared download context - Temp: {temp_path}, Final: {final_path}")
        
        return download_context
    
    def _generate_base_filename(self, court_id: str, case_details: Dict[str, Any]) -> str:
        """Generate base filename without extension.
        Format: {court_id}_{YY}_{NNNNN}_{versus}
        Example: cand_25_09395_Smith_v_Jones
        """
        case_num = case_details.get('docket_num', '')
        
        # Extract year and case number WITHOUT the case type code
        year = '00'
        number = '00000'
        
        # Pattern 1: 1:25-cv-09395 -> extract 25 and 09395
        match = re.match(r'\d+:(\d{2})-[a-z]{2}-(\d+)', case_num)
        if match:
            year = match.group(1)
            number = match.group(2)
        else:
            # Pattern 2: 25-cv-09395 -> extract 25 and 09395
            match = re.match(r'(\d{2})-[a-z]{2}-(\d+)', case_num)
            if match:
                year = match.group(1)
                number = match.group(2)
            else:
                # Pattern 3: 1:25-09395 (no case type) -> extract 25 and 09395
                match = re.match(r'\d+:(\d{2})-(\d+)', case_num)
                if match:
                    year = match.group(1)
                    number = match.group(2)
                else:
                    # Pattern 4: 25-09395 (no case type) -> extract 25 and 09395
                    match = re.match(r'(\d{2})-(\d+)', case_num)
                    if match:
                        year = match.group(1)
                        number = match.group(2)
        
        # Extract and clean versus field
        versus = self._extract_versus_field(case_details)
        
        # Format: {court_id}_{YY}_{NNNNN}_{versus}
        return f"{court_id}_{year}_{number}_{versus}"
    
    async def _handle_document_checkboxes_async(self, page: Page) -> None:
        """Uncheck all document checkboxes except the first one."""
        try:
            # Find all checkboxes in the document selection area
            checkboxes = await page.query_selector_all('input[type="checkbox"][name*="doc"]')
            
            if checkboxes:
                # Uncheck all checkboxes except the first
                for i, checkbox in enumerate(checkboxes):
                    if i > 0:  # Skip the first checkbox
                        is_checked = await checkbox.is_checked()
                        if is_checked:
                            await checkbox.uncheck()
                            self.log_debug(f"Unchecked checkbox {i+1}")
                
                # Ensure first checkbox is checked
                if checkboxes:
                    first_checkbox = checkboxes[0]
                    if not await first_checkbox.is_checked():
                        await first_checkbox.check()
                        self.log_debug("Checked first checkbox")
            
            self.log_info(f"Handled {len(checkboxes)} document checkboxes - kept only first checked")
        
        except Exception as e:
            self.log_error(f"Failed to handle document checkboxes: {str(e)}")
    
    async def _execute_document_download_legacy(self, 
                                               case_details: Dict[str, Any], 
                                               download_context: Dict[str, Any],
                                               page: Optional[Page]) -> Dict[str, Any]:
        """Execute document download with legacy workflow."""
        try:
            if not page:
                return {
                    'success': False,
                    'error': 'No page object available for download',
                    'files': [],
                    'metadata': {}
                }
            
            # Handle document checkboxes (uncheck all except first)
            await self._handle_document_checkboxes_async(page)
            
            # Setup download handler
            temp_path = download_context['temp_download_path']
            final_path = download_context['final_path']
            
            # Attach download event listener
            download_promise = page.expect_download()
            
            # Click download button
            download_button = await page.query_selector('input[value="Download Documents"]')
            if not download_button:
                download_button = await page.query_selector('input[value="View Document"]')
            
            if download_button:
                await download_button.click()
                self.log_info("Clicked download button")
                
                # Wait for download to complete
                download = await download_promise
                
                # Save to temporary location first
                temp_file = os.path.join(temp_path, os.path.basename(download.suggested_filename))
                await download.save_as(temp_file)
                self.log_info(f"Saved to temp: {temp_file}")
                
                # Move to final location
                final_dir = os.path.dirname(final_path)
                os.makedirs(final_dir, exist_ok=True)
                
                shutil.move(temp_file, final_path)
                self.log_info(f"Moved to final: {final_path}")
                
                # Clean up temp directory if empty
                try:
                    os.rmdir(temp_path)
                except:
                    pass  # Directory not empty or doesn't exist
                
                return {
                    'success': True,
                    'files': [final_path],
                    'metadata': {
                        'original_filename': download.suggested_filename,
                        'final_path': final_path,
                        'base_filename': download_context['base_filename'],
                        'download_time': datetime.now().isoformat()
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'Download button not found',
                    'files': [],
                    'metadata': {}
                }
                
        except Exception as e:
            self.log_error(f"Document download failed: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'files': [],
                'metadata': {}
            }

    async def _validate_case_for_download(self, 
                                        case_details: Dict[str, Any], 
                                        is_explicitly_requested: bool) -> tuple[bool, Optional[str]]:
        """P4_2: Validate Case for Download."""
        try:
            if self._download_validator:
                result = await self._download_validator.execute({
                    "action": "validate_case_for_download",
                    "case_details": case_details,
                    "is_explicitly_requested": is_explicitly_requested
                })
                return result.get('should_download', False), result.get('skip_reason')
            
            # Fallback validation logic
            if case_details.get('_no_proceedings_detected'):
                return False, "no_proceedings_detected"
            
            if case_details.get('_transaction_receipt_detected'):
                return False, "transaction_receipt_detected"
                
            return True, None
            
        except Exception as e:
            self.log_error(f"Case validation failed: {str(e)}", exc_info=True)
            return False, f"validation_error: {str(e)}"

    async def _prepare_download_context(self, 
                                      case_details: Dict[str, Any], 
                                      page: Optional[Page]) -> Dict[str, Any]:
        """P4_4: Prepare Download Context (fallback for non-legacy calls)."""
        download_context = {
            'case_details': case_details,
            'page': page,
            'download_path': case_details.get('download_path'),
            'court_id': case_details.get('court_id'),
            'docket_num': case_details.get('docket_num'),
            'base_filename': case_details.get('base_filename')
        }
        
        # Add any additional context needed for download
        if self._download_manager:
            try:
                additional_context = await self._download_manager.execute({
                    "action": "prepare_context",
                    "case_details": case_details
                })
                download_context.update(additional_context)
            except Exception as e:
                self.log_warning(f"Failed to prepare additional download context: {str(e)}")
        
        return download_context

    async def _execute_document_download(self, 
                                       case_details: Dict[str, Any], 
                                       download_context: Dict[str, Any]) -> Dict[str, Any]:
        """P4_5: Execute Document Download (fallback for non-legacy calls)."""
        try:
            if self._file_downloader:
                download_result = await self._file_downloader.execute({
                    "action": "download_documents",
                    "case_details": case_details,
                    "download_context": download_context
                })
                return download_result
            
            # Fallback if no downloader available
            return {
                'success': False,
                'error': 'File downloader not available',
                'files': [],
                'metadata': {}
            }
            
        except Exception as e:
            self.log_error(f"Document download failed: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'files': [],
                'metadata': {}
            }

    async def upload_html_to_s3(self, base_filename: str, html_content: str, iso_date: str) -> bool:
        """Upload HTML content to S3."""
        try:
            if self._s3_manager:
                result = await self._s3_manager.execute({
                    "action": "upload_html",
                    "base_filename": base_filename,
                    "html_content": html_content,
                    "iso_date": iso_date
                })
                return result.get('success', False)
            return False
        except Exception as e:
            self.log_error(f"HTML upload to S3 failed: {str(e)}", exc_info=True)
            return False

    # Interface implementation methods
    async def execute_download_workflow(self, case_details: Dict[str, Any], 
                                       is_explicitly_requested: bool = False,
                                       page: Optional[Page] = None,
                                       court_logger: Optional[Any] = None) -> Dict[str, Any]:
        """Execute complete download workflow for a case."""
        return await self.process_download_workflow(
            case_details=case_details,
            is_explicitly_requested=is_explicitly_requested,
            page=page,
            court_logger=court_logger
        )

    async def authenticate_session(self, credentials: Dict[str, Any]) -> bool:
        """Authenticate PACER session."""
        try:
            if self._authentication_service:
                result = await self._authentication_service.execute({
                    "action": "authenticate_court",
                    "credentials": credentials
                })
                return result.get('authenticated', False)
            return False
        except Exception as e:
            self.log_error(f"Authentication failed: {str(e)}", exc_info=True)
            return False

    async def navigate_to_case(self, case_details: Dict[str, Any]) -> bool:
        """Navigate browser to case page."""
        try:
            if self._navigation_service:
                result = await self._navigation_service.execute({
                    "action": "navigate_to_case",
                    "case_details": case_details
                })
                return result.get('success', False)
            return False
        except Exception as e:
            self.log_error(f"Navigation failed: {str(e)}", exc_info=True)
            return False

    async def execute_query(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute PACER query and return results."""
        try:
            if self._navigation_service:
                result = await self._navigation_service.execute({
                    "action": "execute_query",
                    "query_params": query_params
                })
                return result.get('results', [])
            return []
        except Exception as e:
            self.log_error(f"Query execution failed: {str(e)}", exc_info=True)
            return []

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the service and components."""
        return {
            "service": "DownloadOrchestrationService",
            "status": "healthy" if self._initialized else "unhealthy",
            "components": {
                "download_validator": self._download_validator is not None,
                "file_downloader": self._file_downloader is not None,
                "download_manager": self._download_manager is not None,
                "s3_manager": self._s3_manager is not None,
                "authentication_service": self._authentication_service is not None,
                "navigation_service": self._navigation_service is not None,
                "ignore_download_config": self._ignore_download_config is not None
            }
        }