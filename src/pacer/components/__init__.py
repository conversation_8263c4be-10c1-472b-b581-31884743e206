# /src/pacer/components/__init__.py

"""
PACER components package.

This package contains all PACER-specific components organized by functionality:
- analytics: Data analysis and summarization components
- authentication: Login and session management components
- browser: Browser automation and navigation components
- case_processing: Case data processing and transformation components
- classification: Case classification and categorization components
- configuration: Configuration loading and management components
- download: File download and validation components
- export: Data export and output components
- file_operations: File system operations and path management
- navigation: Page navigation and URL handling components
- processing: Workflow orchestration and processing components
- query: Search query building and result parsing components
- report: Report generation and formatting components
- transfer: Case transfer detection and processing components
- verification: Data validation and verification components
"""