from __future__ import annotations
import asyncio
from typing import Any, Dict, TYPE_CHECKING

from playwright.async_api import (
    TimeoutError as PlaywrightTimeoutError,
    Error as PlaywrightError
)

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer.components.browser.navigator import PacerNavigator
    from src.infrastructure.protocols.logger import LoggerProtocol

class ECFLoginHandler(ComponentImplementation):
    """Handles court-specific ECF login sequences."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'perform_ecf_login':
            return await self.perform_ecf_login_sequence(
                data['navigator'],
                data['court_id'],
            )
        else:
            raise PacerServiceError(f"Unknown action for ECFLoginHandler: {action}")

    async def perform_simplified_ecf_navigation(self, navigator: PacerNavigator, court_id: str, iso_date: str = None) -> bool:
        """
        Simplified ECF navigation following PACER_WORKFLOW_FLOWCHART.md
        
        Handles the ECF navigation portion after main authentication is complete:
        1. Check current URL and determine next steps
        2. Handle NextGen path (court-cmecf-lookup) if present
        3. Navigate to ECF court page if needed
        4. Handle MDD court popup if court_id == 'mdd'
        5. Click "Document Filing System" if on ShowIndex.pl
        6. Verify arrival at court home page
        7. Check for docket report log to determine navigation strategy
        """
        log_prefix = f"[{court_id}] ECFNav:"
        self.log_info(f"{log_prefix} Starting simplified ECF navigation...")
        
        try:
            current_url = navigator.page.url
            self.log_info(f"{log_prefix} Current URL: {current_url}")
            
            # Handle NextGen path (court-cmecf-lookup)
            if "court-cmecf-lookup" in current_url:
                self.log_info(f"{log_prefix} On NextGen court-cmecf-lookup page - selecting court link")
                nextgen_result = await self._handle_nextgen_selection(navigator, court_id)
                if not nextgen_result:
                    return False
            
            # Check if we need to navigate to ECF court site
            current_url = navigator.page.url
            if f"ecf.{court_id.lower()}.uscourts.gov" not in current_url:
                self.log_info(f"{log_prefix} Not on ECF site, navigating to ECF...")
                ecf_url = f"https://ecf.{court_id.lower()}.uscourts.gov/"
                await navigator.goto(ecf_url)
                await navigator.page.wait_for_load_state('domcontentloaded')
                await asyncio.sleep(1)
            
            # Handle MDD court popup if needed
            if court_id.lower() == 'mdd':
                await self._handle_mdd_popup(navigator, court_id)
            
            # Check if we're on ShowIndex.pl and need to click Document Filing System
            current_url = navigator.page.url
            if "ShowIndex.pl" in current_url or "showindex" in current_url.lower():
                self.log_info(f"{log_prefix} On ShowIndex.pl - clicking Document Filing System")
                doc_filing_result = await self._click_document_filing_system(navigator, court_id)
                if not doc_filing_result:
                    return False
            
            # Verify we're at the court home page and check for docket report log
            return await self._verify_court_home_page(navigator, court_id, iso_date)
            
        except Exception as e:
            self.log_error(f"{log_prefix} Error in simplified ECF navigation: {e}")
            return False
    
    async def _handle_nextgen_selection(self, navigator: PacerNavigator, court_id: str) -> bool:
        """
        Handles NextGen court selection from court-cmecf-lookup page.
        Selects the NextGen link for the specific court.
        """
        log_prefix = f"[{court_id}] NextGen:"
        self.log_info(f"{log_prefix} Handling NextGen court selection...")
        
        try:
            # Look for NextGen link for the specific court
            court_link_selectors = [
                f"a:has-text('{court_id.upper()}')",
                f"a[href*='{court_id.lower()}']",
                f"a[href*='ecf.{court_id.lower()}.uscourts.gov']"
            ]
            
            # Retry NextGen link selection with exponential backoff
            max_retries = 3
            base_delay = 1.0
            
            for retry_attempt in range(max_retries):
                for selector in court_link_selectors:
                    try:
                        # Increase timeout with each retry
                        timeout = 2000 + (retry_attempt * 1000)
                        link = navigator.page.locator(selector).first
                        
                        if await link.is_visible(timeout=timeout):
                            self.log_info(f"{log_prefix} Found NextGen link with selector: {selector} (attempt {retry_attempt + 1})")
                            
                            # Click with retry logic
                            await asyncio.wait_for(link.click(), timeout=5.0)
                            
                            # Wait for navigation with timeout
                            await asyncio.wait_for(
                                navigator.page.wait_for_load_state('domcontentloaded'),
                                timeout=15.0
                            )
                            
                            await asyncio.sleep(1)
                            self.log_info(f"{log_prefix} NextGen selection completed")
                            return True
                    except Exception as e:
                        self.log_debug(f"{log_prefix} Selector {selector} failed on attempt {retry_attempt + 1}: {e}")
                        continue
                
                # If we reach here, all selectors failed for this retry attempt
                if retry_attempt < max_retries - 1:
                    delay = base_delay * (2 ** retry_attempt)
                    self.log_info(f"{log_prefix} All selectors failed on attempt {retry_attempt + 1}, retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
            
            self.log_error(f"{log_prefix} Could not find NextGen link for court {court_id}")
            return False
            
        except Exception as e:
            self.log_error(f"{log_prefix} Error handling NextGen selection: {e}")
            return False
    
    async def _handle_mdd_popup(self, navigator: PacerNavigator, court_id: str) -> bool:
        """
        Handles MDD (Maryland District) court popup.
        Clicks closePopupAlert button if present.
        """
        log_prefix = f"[{court_id}] MDD:"
        self.log_info(f"{log_prefix} Handling MDD court popup...")
        
        try:
            # Wait a moment for popup to appear
            await asyncio.sleep(1)
            
            # Try to find and click the close popup button with retry
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    popup_button = navigator.page.locator("#closePopupAlert")
                    timeout = 2000 + (attempt * 1000)
                    
                    if await popup_button.count() > 0 and await popup_button.is_visible(timeout=timeout):
                        self.log_info(f"{log_prefix} Found MDD popup, closing it... (attempt {attempt + 1})")
                        
                        # Click with timeout
                        await asyncio.wait_for(popup_button.click(), timeout=5.0)
                        await asyncio.sleep(0.5)
                        
                        self.log_info(f"{log_prefix} MDD popup closed")
                        break
                    else:
                        if attempt == 0:
                            self.log_info(f"{log_prefix} No MDD popup found on first attempt (normal)")
                        else:
                            self.log_debug(f"{log_prefix} No MDD popup found on attempt {attempt + 1}")
                        break
                        
                except Exception as e:
                    self.log_debug(f"{log_prefix} MDD popup handling attempt {attempt + 1} failed: {e}")
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(1.0)
                    else:
                        self.log_warning(f"{log_prefix} All MDD popup handling attempts failed")
            
            return True
            
        except Exception as e:
            self.log_warning(f"{log_prefix} Error handling MDD popup (continuing anyway): {e}")
            return True  # Don't fail the flow for popup issues
    
    async def _click_document_filing_system(self, navigator: PacerNavigator, court_id: str) -> bool:
        """
        Clicks the "Document Filing System" link on ShowIndex.pl page.
        """
        log_prefix = f"[{court_id}] DFS:"
        self.log_info(f"{log_prefix} Clicking Document Filing System link...")
        
        try:
            doc_filing_selectors = [
                "a:has-text('Document Filing System')",
                "a:has-text('Filing System')",
                "a[href*='login.pl']"
            ]
            
            for selector in doc_filing_selectors:
                try:
                    link = navigator.page.locator(selector).first
                    if await link.is_visible(timeout=2000):
                        self.log_info(f"{log_prefix} Found Document Filing System link: {selector}")
                        await link.click()
                        await navigator.page.wait_for_load_state('domcontentloaded')
                        await asyncio.sleep(1)
                        self.log_info(f"{log_prefix} Document Filing System link clicked")
                        return True
                except Exception:
                    continue
            
            self.log_error(f"{log_prefix} Could not find Document Filing System link")
            return False
            
        except Exception as e:
            self.log_error(f"{log_prefix} Error clicking Document Filing System: {e}")
            return False
    
    async def _verify_court_home_page(self, navigator: PacerNavigator, court_id: str, iso_date: str = None) -> bool:
        """
        Verifies we've successfully reached the court home page and determines navigation strategy.
        """
        log_prefix = f"[{court_id}] Verify:"
        self.log_info(f"{log_prefix} Verifying court home page...")
        
        try:
            current_url = navigator.page.url
            current_url_lower = current_url.lower()
            
            # Check if we're on a valid authenticated court page
            if (f"ecf.{court_id.lower()}.uscourts.gov" in current_url and
                "login.pl" in current_url_lower and
                "logout" not in current_url_lower):
                self.log_info(f"{log_prefix} SUCCESS - At court home page: {current_url}")
                
                # NOW check for docket report log to determine navigation strategy
                if iso_date:
                    self.log_info(f"{log_prefix} Checking for existing docket report log...")
                    from src.pacer.facades.navigation_facade import NavigationFacade
                    nav_facade = NavigationFacade(logger=self.logger)
                    
                    strategy = await nav_facade.determine_navigation_strategy(court_id, iso_date)
                    
                    if strategy['resume_mode']:
                        self.log_info(f"{log_prefix} RESUMPTION MODE: Found existing docket report log - will navigate to Query page")
                    else:
                        self.log_info(f"{log_prefix} NEW REPORT MODE: No docket report log found - will navigate to Reports > Civil Cases")
                
                return True
            elif "logout" in current_url_lower:
                self.log_error(f"{log_prefix} FAILED - Logged out state detected")
                return False
            elif f"ecf.{court_id.lower()}.uscourts.gov" in current_url:
                self.log_info(f"{log_prefix} SUCCESS - On ECF site (assuming authenticated): {current_url}")
                return True
            else:
                self.log_error(f"{log_prefix} FAILED - Not on expected court site: {current_url}")
                return False
                
        except Exception as e:
            self.log_error(f"{log_prefix} Error verifying court home page: {e}")
            return False
    
    async def perform_ecf_login_sequence(self, navigator: PacerNavigator, court_id: str, iso_date: str = None) -> bool:
        """
        Legacy method - now delegates to simplified ECF navigation.
        Kept for backward compatibility.
        """
        self.log_info(f"[{court_id}] ECF: Using simplified navigation (legacy method called)")
        return await self.perform_simplified_ecf_navigation(navigator, court_id, iso_date)
    
    async def _perform_main_pacer_login(self, navigator: PacerNavigator, username: str, password: str) -> bool:
        """
        Performs main PACER login at https://pacer.login.uscourts.gov/csologin/login.jsf
        Uses exact selectors from legacy spec:
        - Username: #loginForm:loginName
        - Password: #loginForm:password
        - Submit: #loginForm:fbtnLogin
        """
        log_prefix = "[PACER Login]"
        login_url = "https://pacer.login.uscourts.gov/csologin/login.jsf"
        
        self.log_info(f"{log_prefix} Navigating to main PACER login page: {login_url}")
        
        try:
            # Navigate to PACER login page
            await navigator.goto(login_url)
            await navigator.page.wait_for_load_state('domcontentloaded')
            await asyncio.sleep(1)
            
            # Fill username field
            username_selector = "#loginForm\\:loginName"
            self.log_info(f"{log_prefix} Filling username field")
            username_field = navigator.page.locator(username_selector)
            await username_field.fill(username)
            
            # Fill password field
            password_selector = "#loginForm\\:password"
            self.log_info(f"{log_prefix} Filling password field")
            password_field = navigator.page.locator(password_selector)
            await password_field.fill(password)
            
            # Click login button
            login_button_selector = "#loginForm\\:fbtnLogin"
            self.log_info(f"{log_prefix} Clicking login button")
            login_button = navigator.page.locator(login_button_selector)
            await login_button.click()
            
            # Wait for navigation
            await navigator.page.wait_for_load_state('domcontentloaded')
            await asyncio.sleep(2)
            
            # Log success
            self.log_info(f"{log_prefix} Main PACER login successful.")
            return True
            
        except Exception as e:
            self.log_error(f"{log_prefix} Failed to perform main PACER login: {e}")
            return False
    
    async def login_to_court_ecf(self, navigator: PacerNavigator, court_id: str) -> bool:
        """
        Logs into court ECF system after navigation.
        Clicks "Document Filing System" link and handles client code prompt.
        For 'flnd' court, fills client code with "007".
        """
        log_prefix = f"[{court_id}] ECF Login:"
        
        try:
            # Click Document Filing System link
            self.log_info(f"{log_prefix} Looking for Document Filing System link")
            
            doc_filing_selectors = [
                "a:has-text('Document Filing System')",
                "a:has-text('Filing System')",
                "a[href*='login.pl']"
            ]
            
            link_clicked = False
            for selector in doc_filing_selectors:
                try:
                    link = navigator.page.locator(selector).first
                    if await link.is_visible(timeout=2000):
                        self.log_info(f"{log_prefix} Found Document Filing System link, clicking...")
                        await link.click()
                        await navigator.page.wait_for_load_state('domcontentloaded')
                        await asyncio.sleep(1)
                        link_clicked = True
                        break
                except Exception:
                    continue
            
            if not link_clicked:
                self.log_warning(f"{log_prefix} Document Filing System link not found, may already be on login page")
            
            # Handle client code prompt for flnd court
            if court_id.lower() == 'flnd':
                self.log_info(f"{log_prefix} Handling client code prompt for flnd court")
                
                # Look for client code input field
                client_code_selectors = [
                    "#logoutForm\\:clientCode",
                    "input[name='logoutForm:clientCode']",
                    "input[id*='clientCode']"
                ]
                
                for selector in client_code_selectors:
                    try:
                        client_input = navigator.page.locator(selector)
                        if await client_input.count() > 0:
                            self.log_info(f"{log_prefix} Found client code field, filling with '007'")
                            await client_input.fill("007")
                            
                            # Submit the form
                            submit_selectors = [
                                "#logoutForm\\:btnChangeClientCode",
                                "button[id*='ChangeClientCode']",
                                "button:has-text('Submit')"
                            ]
                            
                            for submit_sel in submit_selectors:
                                try:
                                    submit_btn = navigator.page.locator(submit_sel)
                                    if await submit_btn.count() > 0:
                                        await submit_btn.click()
                                        await navigator.page.wait_for_load_state('domcontentloaded')
                                        break
                                except Exception:
                                    continue
                            
                            break
                    except Exception:
                        continue
            
            # Log success
            self.log_info(f"{log_prefix} Court ECF login successful.")
            return True
            
        except Exception as e:
            self.log_error(f"{log_prefix} Failed to login to court ECF: {e}")
            return False

    async def navigate_to_court_ecf(self, navigator: PacerNavigator, court_id: str) -> bool:
        """
        Navigates to court ECF via court-cmecf-lookup page as per legacy spec.
        Navigate to https://pacer.uscourts.gov/file-case/court-cmecf-lookup,
        find and click the anchor href containing ecf.{court_id}.uscourts.gov
        """
        log_prefix = f"[{court_id}]"
        lookup_url = "https://pacer.uscourts.gov/file-case/court-cmecf-lookup"
        
        self.log_info(f"{log_prefix} Navigating to court-cmecf-lookup: {lookup_url}")
        
        try:
            # Navigate to the court lookup page
            await navigator.goto(lookup_url)
            await navigator.page.wait_for_load_state('domcontentloaded')
            await asyncio.sleep(1)
            
            # Find and click the anchor with href containing ecf.{court_id}.uscourts.gov
            target_href = f"ecf.{court_id.lower()}.uscourts.gov"
            self.log_info(f"{log_prefix} Looking for link containing: {target_href}")
            
            # Try multiple selectors to find the court link
            link_selectors = [
                f"a[href*='{target_href}']",
                f"a[href*='ecf.{court_id.lower()}']",
                f"a:has-text('{court_id.upper()}')"
            ]
            
            link_found = False
            for selector in link_selectors:
                try:
                    link = navigator.page.locator(selector).first
                    if await link.is_visible(timeout=2000):
                        self.log_info(f"{log_prefix} Found court link with selector: {selector}")
                        await link.click()
                        await navigator.page.wait_for_load_state('domcontentloaded')
                        await asyncio.sleep(1)
                        
                        # Log success with the URL we navigated to
                        current_url = navigator.page.url
                        self.log_info(f"{log_prefix} Court ECF navigation successful. URL: {current_url}")
                        link_found = True
                        break
                except Exception as e:
                    self.log_debug(f"{log_prefix} Selector {selector} failed: {e}")
                    continue
            
            if not link_found:
                self.log_error(f"{log_prefix} Could not find court link for {court_id}")
                return False
            
            return True
                
        except Exception as e:
            self.log_error(f"{log_prefix} Failed to navigate to court ECF site: {e}")
            return False
    
    async def _navigate_to_court_ecf(self, navigator: PacerNavigator, court_id: str) -> bool:
        """
        Legacy method - delegates to navigate_to_court_ecf for backward compatibility.
        """
        return await self.navigate_to_court_ecf(navigator, court_id)

    async def _handle_court_specific_ui(self, navigator: PacerNavigator, court_id: str) -> None:
        """Legacy method - now delegates to specific handlers."""
        # Handle MDD court popup specifically
        if court_id.lower() == 'mdd':
            await self._handle_mdd_popup(navigator, court_id)
        
        # Handle other generic popups
        log_prefix = f"[{court_id}]"
        other_popup_selectors = [
            "div[role='dialog'] button:has-text('OK')",
            "button:has-text('Close')",
        ]
        for selector in other_popup_selectors:
            try:
                popup_element = navigator.page.locator(selector).first
                if await popup_element.is_visible(timeout=1000):
                    self.log_info(f"{log_prefix} Handling generic popup: {selector}")
                    await popup_element.click()
                    await asyncio.sleep(0.5)
            except Exception:
                continue

    async def _handle_client_code_page(self, navigator: PacerNavigator, court_id: str) -> bool:
        """
        Handles the client code page that appears at https://pacer.login.uscourts.gov/csologin/login.jsf
        when user is already logged in and being redirected from ECF.
        
        The page has:
        1. Client code input field: input[name='logoutForm:clientCode']
        2. Submit button: button[id='logoutForm:btnChangeClientCode']
        """
        log_prefix = f"[{court_id}]"
        
        try:
            # Check if we're on the PACER login URL with client code page
            current_url = navigator.page.url
            
            # The client code page shows at the PACER login URL
            if 'pacer.login.uscourts.gov/csologin/login.jsf' in current_url:
                self.log_info(f"{log_prefix} At PACER login URL, checking for client code page...")
                
                # Small delay to ensure page is loaded
                await asyncio.sleep(1)
                page_content = await navigator.page.content()
                
                # Check for client code page indicators
                if ("Client Code" in page_content or 
                    "clientCode" in page_content or
                    "Logged in as" in page_content):
                    self.log_info(f"{log_prefix} Client code page detected (user already authenticated)")
                    
                    # Try to find the client code input field using the exact selector from the HTML
                    client_input_selector = "input#logoutForm\\:clientCode"
                    try:
                        client_input = navigator.page.locator(client_input_selector)
                        if await client_input.count() > 0:
                            self.log_info(f"{log_prefix} Found client code input field")
                            
                            # Fill the client code field with retry
                            fill_success = False
                            for fill_attempt in range(3):
                                try:
                                    await asyncio.wait_for(client_input.fill("007"), timeout=5.0)
                                    await asyncio.sleep(0.5)
                                    self.log_info(f"{log_prefix} Filled client code with '007' (attempt {fill_attempt + 1})")
                                    fill_success = True
                                    break
                                except Exception as fill_e:
                                    self.log_debug(f"{log_prefix} Fill attempt {fill_attempt + 1} failed: {fill_e}")
                                    if fill_attempt < 2:
                                        await asyncio.sleep(1.0)
                            
                            if not fill_success:
                                self.log_warning(f"{log_prefix} Could not fill client code after 3 attempts")
                            
                            # Select the appropriate court from the dropdown using robust method
                            court_dropdown_selector = "select#logoutForm\\:courtId_input"
                            self.log_info(f"{log_prefix} Using robust dropdown selection for court selection...")
                            
                            # Build the option value and display text for the court
                            court_value = self._get_court_dropdown_value(court_id)
                            court_display_text = self._get_court_display_text(court_id)
                            
                            # Use the robust dropdown selection method
                            court_selection_success = await navigator.select_dropdown_robust(
                                selector=court_dropdown_selector,
                                target_value=court_value,
                                target_label=court_display_text,
                                timeout_override_ms=8000
                            )
                            
                            if court_selection_success:
                                self.log_info(f"{log_prefix} Successfully selected court using robust method: {court_value}")
                            else:
                                self.log_warning(f"{log_prefix} Court selection failed, trying alternative approaches...")
                                
                                # Try with alternative selectors
                                alt_dropdown_selectors = [
                                    "select[name='logoutForm:courtId']",
                                    "select[id*='courtId']",
                                    "div#logoutForm\\:courtId"  # PrimeFaces visual dropdown
                                ]
                                
                                court_selected = False
                                for alt_selector in alt_dropdown_selectors:
                                    if court_selected:
                                        break
                                        
                                    self.log_info(f"{log_prefix} Trying alternative court selector: {alt_selector}")
                                    
                                    if "div#" in alt_selector:
                                        # Handle PrimeFaces visual dropdown
                                        try:
                                            dropdown_div = navigator.page.locator(alt_selector)
                                            if await dropdown_div.count() > 0:
                                                await dropdown_div.click()
                                                await asyncio.sleep(0.5)
                                                
                                                # Try to find and click the option by text
                                                option_selector = f"li[data-label*='{court_display_text}']"
                                                option = navigator.page.locator(option_selector)
                                                if await option.count() > 0:
                                                    await option.click()
                                                    self.log_info(f"{log_prefix} Selected court via PrimeFaces dropdown: {court_display_text}")
                                                    court_selected = True
                                                else:
                                                    self.log_debug(f"{log_prefix} Court option not found in PrimeFaces dropdown")
                                        except Exception as e:
                                            self.log_debug(f"{log_prefix} PrimeFaces dropdown failed: {e}")
                                    else:
                                        # Handle regular select dropdown
                                        alt_success = await navigator.select_dropdown_robust(
                                            selector=alt_selector,
                                            target_value=court_value,
                                            target_label=court_display_text,
                                            timeout_override_ms=5000
                                        )
                                        
                                        if alt_success:
                                            self.log_info(f"{log_prefix} Selected court with alternative selector: {alt_selector}")
                                            court_selected = True
                                
                                if not court_selected:
                                    self.log_warning(f"{log_prefix} All court selection methods failed - continuing anyway")
                            
                            await asyncio.sleep(0.5)
                            
                            # Click the submit button using the exact ID from the HTML with retry
                            submit_button_selector = "button#logoutForm\\:btnChangeClientCode"
                            submit_button = navigator.page.locator(submit_button_selector)
                            
                            if await submit_button.count() > 0:
                                submit_success = False
                                for submit_attempt in range(3):
                                    try:
                                        self.log_info(f"{log_prefix} Found submit button, clicking... (attempt {submit_attempt + 1})")
                                        
                                        await asyncio.wait_for(submit_button.click(), timeout=5.0)
                                        
                                        # Wait for navigation after submit with timeout
                                        await asyncio.wait_for(
                                            navigator.page.wait_for_load_state('domcontentloaded'),
                                            timeout=15000
                                        )
                                        await asyncio.sleep(1)
                                        
                                        self.log_info(f"{log_prefix} Client code submitted successfully")
                                        self.log_info(f"{log_prefix} Navigated to: {navigator.page.url}")
                                        submit_success = True
                                        break
                                        
                                    except Exception as submit_e:
                                        self.log_debug(f"{log_prefix} Submit attempt {submit_attempt + 1} failed: {submit_e}")
                                        if submit_attempt < 2:
                                            await asyncio.sleep(2.0)
                                
                                if submit_success:
                                    return True
                            else:
                                self.log_warning(f"{log_prefix} Submit button not found, trying alternative methods")
                                
                                # Try pressing Enter as fallback
                                await client_input.press("Enter")
                                await navigator.page.wait_for_load_state('domcontentloaded', timeout=10000)
                                self.log_info(f"{log_prefix} Client code submitted via Enter key")
                                return True
                        else:
                            self.log_warning(f"{log_prefix} Client code input field not found with primary selector")
                        
                            # Try alternative selectors as fallback
                            alt_selectors = [
                                "input[name='logoutForm:clientCode']",
                                "input[id*='clientCode']",
                                "input[name*='clientCode']"
                            ]
                            
                            for alt_selector in alt_selectors:
                                alt_input = navigator.page.locator(alt_selector).first
                                if await alt_input.count() > 0:
                                    self.log_info(f"{log_prefix} Found client code input with: {alt_selector}")
                                    await alt_input.fill("007")
                                    await asyncio.sleep(0.5)
                                    
                                    # Try to find any submit button
                                    submit_attempts = [
                                        "button:has-text('Submit')",
                                        "button[type='submit']",
                                        "input[type='submit']"
                                    ]
                                    
                                    for submit_sel in submit_attempts:
                                        submit_btn = navigator.page.locator(submit_sel).first
                                        if await submit_btn.count() > 0:
                                            await submit_btn.click()
                                            await navigator.page.wait_for_load_state('domcontentloaded')
                                            self.log_info(f"{log_prefix} Client code submitted with button: {submit_sel}")
                                            return True
                                    
                                    # Last resort: press Enter
                                    await alt_input.press("Enter")
                                    await navigator.page.wait_for_load_state('domcontentloaded')
                                    self.log_info(f"{log_prefix} Client code submitted via Enter key (fallback)")
                                    return True
                        
                            self.log_warning(f"{log_prefix} Could not find client code input with any selector")
                            
                    except Exception as e:
                        self.log_error(f"{log_prefix} Error handling client code input: {e}")
                        return False
                else:
                    # No client code page at PACER login URL - this might be normal login page
                    self.log_info(f"{log_prefix} At PACER login URL but no client code page - may need regular login")
                    return True
            else:
                # Not at PACER login URL - check if we're on ECF or elsewhere
                if f"ecf.{court_id.lower()}.uscourts.gov" in current_url:
                    self.log_info(f"{log_prefix} Already at ECF URL - no client code page needed")
                    return True
                else:
                    self.log_info(f"{log_prefix} Not at PACER login or ECF URL - proceeding normally")
                    return True
                
        except Exception as e:
            self.log_warning(f"{log_prefix} Error checking for client code page: {e}")
            # Don't fail the flow - continue anyway
            return True
    
    def _get_court_dropdown_value(self, court_id: str) -> str:
        """
        Convert court_id to the dropdown value format.
        E.g., "ilnd" -> "N_ILNDC" for Illinois Northern District Court
        """
        # Map common court IDs to their dropdown values based on PACER patterns
        court_map = {
            "ilnd": "N_ILNDC",  # Illinois Northern District Court
            "ilcd": "N_ILCDC",  # Illinois Central District Court
            "ilsd": "N_ILSDC",  # Illinois Southern District Court
            "cand": "N_CANDC",  # California Northern District Court
            "cacd": "N_CACDC",  # California Central District Court
            "caed": "N_CAEDC",  # California Eastern District Court
            "casd": "N_CASDC",  # California Southern District Court
            "nysd": "N_NYSDC",  # New York Southern District Court
            "nyed": "N_NYEDC",  # New York Eastern District Court
            "nynd": "N_NYNDC",  # New York Northern District Court
            "nywd": "N_NYWDC",  # New York Western District Court
            "txsd": "N_TXSDC",  # Texas Southern District Court
            "txed": "N_TXEDC",  # Texas Eastern District Court
            "txnd": "N_TXNDC",  # Texas Northern District Court
            "txwd": "N_TXWDC",  # Texas Western District Court
            "mdd": "N_MDDC",   # Maryland District Court
            # Add more mappings as needed
        }
        
        court_id_lower = court_id.lower()
        if court_id_lower in court_map:
            return court_map[court_id_lower]
        
        # Try to build it dynamically for district courts
        # Most district courts follow pattern: N_{STATE}{REGION}DC
        return f"N_{court_id.upper()}C"
    
    def _get_court_display_text(self, court_id: str) -> str:
        """
        Get the display text for a court to match in the dropdown.
        E.g., "ilnd" -> "Illinois Northern"
        """
        # Map court IDs to their display text based on PACER dropdown options
        court_display = {
            "ilnd": "Illinois Northern",
            "ilcd": "Illinois Central",
            "ilsd": "Illinois Southern",
            "cand": "California Northern",
            "cacd": "California Central",
            "caed": "California Eastern",
            "casd": "California Southern",
            "nysd": "New York Southern",
            "nyed": "New York Eastern",
            "nynd": "New York Northern",
            "nywd": "New York Western",
            "txsd": "Texas Southern",
            "txed": "Texas Eastern",
            "txnd": "Texas Northern",
            "txwd": "Texas Western",
            "mdd": "Maryland",
            # Add more mappings as needed
        }
        
        court_id_lower = court_id.lower()
        if court_id_lower in court_display:
            return court_display[court_id_lower]
        
        # Default to uppercase court ID
        return court_id.upper()