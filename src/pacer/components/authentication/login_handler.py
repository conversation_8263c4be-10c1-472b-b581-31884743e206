from __future__ import annotations
import asyncio
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer.components.browser.navigator import PacerNavigator
    from src.infrastructure.protocols.logger import LoggerProtocol

class LoginHandler(ComponentImplementation):
    """
    Handles the core PACER login operations following documented authentication flow.
    Uses exact selectors and URLs from PACER_SCRAPING_PIPELINE_ANALYSIS.md
    """

    # Documented authentication URLs and selectors
    LOGIN_URL = 'https://pacer.login.uscourts.gov/csologin/login.jsf'
    USERNAME_SELECTOR = "#loginForm\\:loginName"
    PASSWORD_SELECTOR = "#loginForm\\:password"
    LOGIN_BUTTON_SELECTOR = "#loginForm\\:fbtnLogin"
    LOGIN_STATUS_SELECTOR = "span.psc-login-panel-header.psc-pacer-blue-fg"  # "Logged in as" indicator
    LOGIN_SUCCESS_INDICATOR_SELECTOR = "#logoutForm\\:courtId"  # Present after successful auth

    _main_login_lock = asyncio.Lock()

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        """Execute login-related actions."""
        action = data.get('action')
        if action == 'perform_login':
            return await self.perform_login(data['navigator'])
        else:
            raise PacerServiceError(f"Unknown action for LoginHandler: {action}")

    async def perform_main_pacer_login(self, navigator: PacerNavigator, court_id: str = None) -> bool:
        """
        Performs main PACER login following exact specification requirements:
        1. ACQUIRE class-level login lock for synchronization
        2. Navigate to: https://pacer.login.uscourts.gov/csologin/login.jsf
        3. Check if already logged in via DOM inspection
        4. Fill username field: #loginForm\:loginName
        5. Fill password field: #loginForm\:password
        6. Click login button: #loginForm\:fbtnLogin
        7. Verify login success + RELEASE login lock
        
        Uses exact selectors from PACER_SCRAPING_PIPELINE_ANALYSIS.md
        """
        log_prefix = "[MainPACERLogin]"
        async with self._main_login_lock:
            self.log_info(f"{log_prefix} ACQUIRED class-level login lock for synchronization.")

            # Try multiple config key formats for credentials
            username = self.config.get('username_prod') or self.config.get('PACER_USERNAME_PROD') or self.config.get('PACER_USERNAME')
            password = self.config.get('password_prod') or self.config.get('PACER_PASSWORD_PROD') or self.config.get('PACER_PASSWORD')

            if not username or not password:
                available_keys = list(self.config.keys()) if self.config else []
                self.log_error(f"{log_prefix} PACER username or password not found in config. Available keys: {available_keys}")
                self.log_error(f"{log_prefix} Config object type: {type(self.config)}, Config is None: {self.config is None}")
                return False

            try:
                # Step 2: Navigate to documented login URL
                self.log_info(f"{log_prefix} Step 2: Navigating to: {self.LOGIN_URL}")
                await navigator.goto(self.LOGIN_URL)
                await asyncio.sleep(1)

                # Step 3: Check if already logged in via DOM inspection
                try:
                    login_status = navigator.page.locator(self.LOGIN_STATUS_SELECTOR)
                    if await login_status.count() > 0:
                        status_text = await login_status.text_content()
                        if status_text and "logged in as" in status_text.lower():
                            self.log_info(f"{log_prefix} Step 3: Already logged in via DOM inspection: {status_text}")
                            return True
                except Exception:
                    pass  # Not logged in, continue with login process

                self.log_info(f"{log_prefix} Step 4-6: Performing login with exact selectors...")
                
                # Step 4: Fill username field: #loginForm\:loginName
                await navigator.fill(self.USERNAME_SELECTOR, "")
                await asyncio.sleep(0.5)
                await navigator.fill(self.USERNAME_SELECTOR, username)
                await asyncio.sleep(0.5)
                
                # Step 5: Fill password field: #loginForm\:password
                await navigator.fill(self.PASSWORD_SELECTOR, "")
                await asyncio.sleep(0.5)
                await navigator.fill(self.PASSWORD_SELECTOR, password)
                await asyncio.sleep(0.5)
                
                # Step 6: Click login button: #loginForm\:fbtnLogin
                await navigator.click(self.LOGIN_BUTTON_SELECTOR)
                await navigator.page.wait_for_load_state('networkidle', timeout=15000)

                # Step 7: Verify login success
                try:
                    success_indicator = navigator.page.locator(self.LOGIN_SUCCESS_INDICATOR_SELECTOR)
                    await success_indicator.wait_for(state="visible", timeout=10000)
                    
                    # Double-check with status selector
                    login_status = navigator.page.locator(self.LOGIN_STATUS_SELECTOR)
                    if await login_status.count() > 0:
                        status_text = await login_status.text_content()
                        self.log_info(f"{log_prefix} Step 7: Login success verified. Status: {status_text}")
                        
                        # CRITICAL FIX: Check if we need client code or if we're already at ShowIndex.pl
                        if status_text and "logged in as" in status_text.lower():
                            # Check if client code field exists on the page
                            page_content = await navigator.page.content()
                            has_client_code = "clientCode" in page_content or "client code" in page_content.lower()
                            current_url = navigator.page.url
                            
                            if has_client_code and "ShowIndex.pl" not in current_url:
                                # We're on client code page, need to handle it
                                self.log_info(f"{log_prefix} Step 8: Detected 'Logged in as' with client code field - handling client code page")
                                client_code_success = await self._handle_client_code(navigator, court_id)
                                if not client_code_success:
                                    self.log_error(f"{log_prefix} Client code handling failed")
                                    return False
                                self.log_info(f"{log_prefix} Client code handling completed successfully")
                            elif "ShowIndex.pl" in current_url:
                                # We're already at ShowIndex.pl - skip directly to Document Filing System
                                self.log_info(f"{log_prefix} Step 8: Already at ShowIndex.pl - no client code needed")
                                return await self._handle_document_filing_system_click(navigator, log_prefix)
                            else:
                                # Logged in but no client code field - might be at a different page
                                self.log_info(f"{log_prefix} Step 8: Logged in but no client code field found, checking current page")
                            
                            # Check if we ended up on court-cmecf-lookup page (NextGen path)
                            current_url_after_client_code = navigator.page.url
                            if 'court-cmecf-lookup' in current_url_after_client_code:
                                self.log_info(f"{log_prefix} Step 8b: Detected NextGen path - handling court selection")
                                nextgen_success = await self._handle_nextgen_court_selection(navigator, court_id)
                                if not nextgen_success:
                                    self.log_error(f"{log_prefix} NextGen court selection failed")
                                    return False
                                self.log_info(f"{log_prefix} NextGen court selection completed successfully")
                    else:
                        self.log_info(f"{log_prefix} Step 7: Login success verified - success indicator found.")
                    
                    self.log_info(f"{log_prefix} RELEASING login lock after successful authentication.")
                    return True
                    
                except Exception as e:
                    self.log_error(f"{log_prefix} Step 7: Login verification failed: {e}")
                    # Save debug information
                    await navigator.save_screenshot("main_pacer_login_verification_fail")
                    current_url = navigator.page.url
                    
                    # CRITICAL FIX: Handle execution context destroyed error
                    page_title = "Unknown"
                    try:
                        if navigator.page and not navigator.page.is_closed():
                            page_title = await navigator.page.title()
                        else:
                            page_title = "Context destroyed"
                    except Exception as title_error:
                        if "execution context was destroyed" in str(title_error).lower():
                            page_title = "Context destroyed"
                        else:
                            page_title = f"Title error: {title_error}"
                    
                    self.log_error(f"{log_prefix} Current URL: {current_url}, Page title: {page_title}")
                    return False
                    
            except Exception as e:
                self.log_error(f"{log_prefix} Login process failed: {e}")
                await navigator.save_screenshot("main_pacer_login_process_fail")
                return False
            finally:
                self.log_info(f"{log_prefix} RELEASING class-level login lock.")

    async def _handle_client_code(self, navigator: PacerNavigator, court_id: str = None) -> bool:
        """
        Handles client code entry when "Logged in as" text is detected.
        This indicates the user is authenticated but needs to enter client code
        to continue to the appropriate CM/ECF courts.
        
        Steps:
        1. Enter client code (007)
        2. Select appropriate CM/ECF court from dropdown
        3. Click submit
        """
        log_prefix = "[ClientCode]"
        
        try:
            current_url = navigator.page.url
            self.log_info(f"{log_prefix} Starting client code handling at URL: {current_url}")
            
            # Small delay to ensure page is fully loaded
            await asyncio.sleep(1)
            
            # Step 1: Fill client code field
            if not await self._fill_client_code_field(navigator, log_prefix):
                return False
            
            # Step 2: Select court from dropdown
            if not await self._set_court_dropdown(navigator, court_id, log_prefix):
                return False
                
            await asyncio.sleep(0.5)
            
            # Step 3: Submit the form
            return await self._submit_client_code_form(navigator, court_id, log_prefix)
                
        except Exception as e:
            self.log_error(f"{log_prefix} Unexpected error in client code handling: {e}")
            return False

    async def _fill_client_code_field(self, navigator: PacerNavigator, log_prefix: str) -> bool:
        """Fill the client code field with value '007'."""
        client_code_selector = "input#logoutForm\\:clientCode"
        
        try:
            client_input = navigator.page.locator(client_code_selector)
            if await client_input.count() > 0:
                self.log_info(f"{log_prefix} Found client code input field")
                await client_input.fill("007")
                await asyncio.sleep(0.5)
                self.log_info(f"{log_prefix} Entered client code: 007")
                return True
            
            # Try alternative selectors
            alt_selectors = [
                "input[name='logoutForm:clientCode']",
                "input[id*='clientCode']",
                "input[name*='clientCode']"
            ]
            
            for alt_selector in alt_selectors:
                alt_input = navigator.page.locator(alt_selector).first
                if await alt_input.count() > 0:
                    self.log_info(f"{log_prefix} Found client code input with: {alt_selector}")
                    await alt_input.fill("007")
                    await asyncio.sleep(0.5)
                    self.log_info(f"{log_prefix} Entered client code: 007")
                    return True
            
            self.log_error(f"{log_prefix} Could not find client code input field")
            return False
                        
        except Exception as e:
            self.log_error(f"{log_prefix} Error filling client code: {e}")
            return False

    async def _set_court_dropdown(self, navigator: PacerNavigator, court_id: str, log_prefix: str) -> bool:
        """Set the court dropdown value using JavaScript."""
        # Convert court_id to the correct format: 'ilnd' -> 'N_ILNDC'
        if court_id:
            formatted_court_value = f"N_{court_id.upper()}C"
        else:
            # Default fallback if no court_id provided
            formatted_court_value = "CTL"
        
        try:
            if court_id:
                self.log_info(f"{log_prefix} Converting court_id '{court_id}' to dropdown value '{formatted_court_value}'")
            self.log_info(f"{log_prefix} Setting court dropdown value via JavaScript (hidden element)")
            
            # The select is hidden, set value directly via JavaScript
            await navigator.page.evaluate(f'''
                document.getElementById('logoutForm:courtId_input').value = '{formatted_court_value}';
                // Trigger change event for JSF
                var event = new Event('change', {{ bubbles: true }});
                document.getElementById('logoutForm:courtId_input').dispatchEvent(event);
            ''')
            await asyncio.sleep(0.5)
            
            self.log_info(f"{log_prefix} Successfully set court dropdown VALUE='{formatted_court_value}' via JavaScript")
            return True
                    
        except Exception as e:
            self.log_error(f"{log_prefix} Error setting court dropdown value: {e}")
            return False

    async def _submit_client_code_form(self, navigator: PacerNavigator, court_id: str, log_prefix: str) -> bool:
        """Submit the client code form using various methods."""
        # Try primary submit button first
        submit_button_selector = "button#logoutForm\\:btnChangeClientCode"
        
        try:
            submit_button = navigator.page.locator(submit_button_selector)
            if await submit_button.count() > 0:
                self.log_info(f"{log_prefix} Found submit button, clicking...")
                await submit_button.click()
                
                # Wait for navigation after submit
                await navigator.page.wait_for_load_state('domcontentloaded', timeout=10000)
                await asyncio.sleep(1)
                
                self.log_info(f"{log_prefix} Client code form submitted successfully")
                self.log_info(f"{log_prefix} Navigated to: {navigator.page.url}")
                
                return await self._handle_post_submit_navigation(navigator, court_id, log_prefix)
            else:
                return await self._try_alternative_submit_methods(navigator, court_id, log_prefix)
                        
        except Exception as e:
            self.log_error(f"{log_prefix} Error submitting client code form: {e}")
            return False

    async def _try_alternative_submit_methods(self, navigator: PacerNavigator, court_id: str, log_prefix: str) -> bool:
        """Try alternative methods to submit the form."""
        self.log_warning(f"{log_prefix} Submit button not found, trying alternative methods")
        
        # Try alternative submit methods
        submit_attempts = [
            "button:has-text('Submit')",
            "button[type='submit']", 
            "input[type='submit']"
        ]
        
        for submit_sel in submit_attempts:
            submit_btn = navigator.page.locator(submit_sel).first
            if await submit_btn.count() > 0:
                await submit_btn.click()
                await navigator.page.wait_for_load_state('domcontentloaded', timeout=10000)
                await asyncio.sleep(1)
                self.log_info(f"{log_prefix} Client code submitted with button: {submit_sel}")
                
                return await self._handle_post_submit_navigation(navigator, court_id, log_prefix)
        
        # Last resort: press Enter on the client code field
        return await self._try_enter_key_submit(navigator, court_id, log_prefix)

    async def _try_enter_key_submit(self, navigator: PacerNavigator, court_id: str, log_prefix: str) -> bool:
        """Try submitting by pressing Enter on the client code field."""
        client_code_selector = "input#logoutForm\\:clientCode"
        client_input = navigator.page.locator(client_code_selector)
        
        if await client_input.count() > 0:
            await client_input.press("Enter")
            await navigator.page.wait_for_load_state('domcontentloaded', timeout=10000)
            await asyncio.sleep(1)
            self.log_info(f"{log_prefix} Client code submitted via Enter key")
            
            return await self._handle_post_submit_navigation(navigator, court_id, log_prefix)
        else:
            self.log_error(f"{log_prefix} No submit method worked")
            return False

    async def _handle_post_submit_navigation(self, navigator: PacerNavigator, court_id: str, log_prefix: str) -> bool:
        """Handle navigation after form submission based on the resulting URL."""
        current_url = navigator.page.url
        
        # If we selected a specific court (N_ILNDC), we should be at the ECF page
        if 'ecf.' in current_url and 'uscourts.gov' in current_url:
            self.log_info(f"{log_prefix} Successfully navigated to ECF page: {current_url}")
            
            # If we're on ShowIndex.pl, need to click "Document Filing System"
            if 'ShowIndex.pl' in current_url:
                return await self._handle_document_filing_system_click(navigator, log_prefix)
            
            return True
        
        # If we selected CTL, we'd be at court-cmecf-lookup page
        elif 'court-cmecf-lookup' in current_url:
            return await self._handle_nextgen_navigation(navigator, court_id, log_prefix)
        
        # Successful if we're at any expected PACER/ECF page
        else:
            self.log_info(f"{log_prefix} Navigation successful to: {current_url}")
            return True

    async def _handle_document_filing_system_click(self, navigator: PacerNavigator, log_prefix: str) -> bool:
        """Handle clicking the Document Filing System link on ShowIndex.pl pages."""
        self.log_info(f"{log_prefix} On ShowIndex.pl page, MUST click 'Document Filing System' link")
        
        try:
            # CRITICAL: Wait for page to fully load before looking for link
            await navigator.page.wait_for_load_state('networkidle', timeout=15000)
            await asyncio.sleep(3)  # MUST wait for dynamic content to render
            
            # The link text is ALWAYS "Document Filing System" 
            doc_filing_link = navigator.page.locator("a:has-text('Document Filing System')").first
            if await doc_filing_link.count() > 0:
                self.log_info(f"{log_prefix} Found 'Document Filing System' link, clicking...")
                await doc_filing_link.click()
                await navigator.page.wait_for_load_state('networkidle', timeout=15000)
                await asyncio.sleep(2)
                self.log_info(f"{log_prefix} Successfully clicked 'Document Filing System' link")
                self.log_info(f"{log_prefix} Navigated to: {navigator.page.url}")
                return True
            else:
                self.log_error(f"{log_prefix} CRITICAL: 'Document Filing System' link MUST exist on ShowIndex.pl but wasn't found")
                import sys
                sys.exit(1)  # Exit immediately - link MUST exist
        except Exception as e:
            self.log_error(f"{log_prefix} CRITICAL ERROR clicking 'Document Filing System' link: {e}")
            import sys
            sys.exit(1)  # Exit immediately on error

    async def _handle_nextgen_navigation(self, navigator: PacerNavigator, court_id: str, log_prefix: str) -> bool:
        """Handle NextGen court selection navigation."""
        self.log_info(f"{log_prefix} On court-cmecf-lookup page, need to handle NextGen path")
        nextgen_success = await self._handle_nextgen_court_selection(navigator, court_id)
        if not nextgen_success:
            self.log_error(f"{log_prefix} Failed to handle NextGen court selection")
            return False
        self.log_info(f"{log_prefix} Successfully handled NextGen court selection")
        return True

    async def _handle_nextgen_court_selection(self, navigator: PacerNavigator, court_id: str = None) -> bool:
        """
        Handles NextGen path through the court-cmecf-lookup page.
        
        This method:
        1. Detects when on the court-cmecf-lookup page
        2. Finds and clicks the NextGen link for the specified court
        3. Handles navigation to ShowIndex.pl
        4. Includes MDD court popup handling if court_id == 'mdd'
        
        Args:
            navigator: The PacerNavigator instance
            court_id: The court ID (e.g., 'ilnd', 'mdd')
        
        Returns:
            bool: True if successful, False otherwise
        """
        log_prefix = "[NextGenPath]"
        
        try:
            current_url = navigator.page.url
            self.log_info(f"{log_prefix} Starting NextGen court selection at: {current_url}")
            
            # Verify we're on the court-cmecf-lookup page
            if 'court-cmecf-lookup' not in current_url:
                self.log_error(f"{log_prefix} Not on expected court-cmecf-lookup page: {current_url}")
                return False
            
            # Give the page time to load fully
            await asyncio.sleep(2)
            
            # Step 1: Find the NextGen link for the specified court
            court_link_found = False
            
            if court_id:
                self.log_info(f"{log_prefix} Looking for NextGen link for court: {court_id}")
                
                # Build possible selectors for the court link
                court_selectors = [
                    f"a[href*='ecf.{court_id.lower()}.uscourts.gov']",  # Direct ECF link
                    f"a:has-text('{court_id.upper()}')",  # Link containing court ID
                    f"a:has-text('{self._get_court_display_name(court_id)}')",  # Link containing court name
                    f"tr:has-text('{court_id.upper()}') a",  # Link in table row containing court ID
                    f"td:has-text('{court_id.upper()}') + td a",  # Link in adjacent cell
                ]
                
                # Try each selector to find the court link
                for selector in court_selectors:
                    try:
                        links = navigator.page.locator(selector)
                        link_count = await links.count()
                        
                        if link_count > 0:
                            self.log_info(f"{log_prefix} Found {link_count} potential court links with: {selector}")
                            
                            # Check each link to find the NextGen one
                            for i in range(link_count):
                                link = links.nth(i)
                                if await link.count() > 0:
                                    link_text = await link.text_content() or ""
                                    link_href = await link.get_attribute('href') or ""
                                    
                                    # Look for NextGen indicators
                                    if ('nextgen' in link_text.lower() or 
                                        'next gen' in link_text.lower() or
                                        'nextgen' in link_href.lower() or
                                        f'ecf.{court_id.lower()}.uscourts.gov' in link_href):
                                        
                                        self.log_info(f"{log_prefix} Found NextGen link: '{link_text}' -> {link_href}")
                                        
                                        # Click the NextGen link
                                        await link.click()
                                        
                                        # Wait for navigation
                                        await navigator.page.wait_for_load_state('domcontentloaded', timeout=15000)
                                        await asyncio.sleep(2)
                                        
                                        court_link_found = True
                                        break
                            
                            if court_link_found:
                                break
                                
                    except Exception as e:
                        self.log_debug(f"{log_prefix} Selector {selector} failed: {e}")
                        continue
            
            # If no specific court_id provided or not found, try generic NextGen selectors
            if not court_link_found:
                self.log_info(f"{log_prefix} Trying generic NextGen link selectors")
                
                generic_selectors = [
                    "a:has-text('NextGen')",
                    "a:has-text('Next Gen')",
                    "a[href*='nextgen']",
                    "table a[href*='ecf.']",  # Any ECF link in table
                    "tbody tr td a:first-child",  # First link in each table row
                ]
                
                for selector in generic_selectors:
                    try:
                        links = navigator.page.locator(selector)
                        link_count = await links.count()
                        
                        if link_count > 0:
                            self.log_info(f"{log_prefix} Found {link_count} generic links with: {selector}")
                            
                            # Click the first available link
                            first_link = links.first
                            link_text = await first_link.text_content() or ""
                            link_href = await first_link.get_attribute('href') or ""
                            
                            self.log_info(f"{log_prefix} Clicking generic NextGen link: '{link_text}' -> {link_href}")
                            
                            await first_link.click()
                            await navigator.page.wait_for_load_state('domcontentloaded', timeout=15000)
                            await asyncio.sleep(2)
                            
                            court_link_found = True
                            break
                            
                    except Exception as e:
                        self.log_debug(f"{log_prefix} Generic selector {selector} failed: {e}")
                        continue
            
            if not court_link_found:
                self.log_error(f"{log_prefix} Could not find any NextGen court links")
                await navigator.save_screenshot("nextgen_no_links_found")
                return False
            
            # Step 2: Handle navigation result
            new_url = navigator.page.url
            self.log_info(f"{log_prefix} Navigated to: {new_url}")
            
            # Step 3: Handle ShowIndex.pl page if we land there
            if 'ShowIndex.pl' in new_url or 'showindex' in new_url.lower():
                self.log_info(f"{log_prefix} On ShowIndex.pl page, need to click 'Document Filing System'")
                
                doc_filing_selectors = [
                    "a:has-text('Document Filing System')",
                    "a:has-text('Filing System')",
                    "a[href*='login.pl']",
                    "a[href*='Login.pl']"
                ]
                
                doc_filing_clicked = False
                for selector in doc_filing_selectors:
                    try:
                        doc_link = navigator.page.locator(selector).first
                        if await doc_link.count() > 0 and await doc_link.is_visible(timeout=2000):
                            self.log_info(f"{log_prefix} Found Document Filing System link with: {selector}")
                            await doc_link.click()
                            
                            # Wait for navigation to login page
                            await navigator.page.wait_for_load_state('domcontentloaded', timeout=10000)
                            await asyncio.sleep(1)
                            
                            self.log_info(f"{log_prefix} Successfully clicked Document Filing System link")
                            self.log_info(f"{log_prefix} Navigated to: {navigator.page.url}")
                            doc_filing_clicked = True
                            break
                    except Exception as e:
                        self.log_debug(f"{log_prefix} Document Filing selector {selector} failed: {e}")
                        continue
                
                if not doc_filing_clicked:
                    self.log_warning(f"{log_prefix} Could not find or click Document Filing System link")
                    # Continue anyway as we might still be on the right page
            
            # Step 4: Handle MDD court popup if court_id is 'mdd'
            if court_id and court_id.lower() == 'mdd':
                self.log_info(f"{log_prefix} Handling MDD court popup")
                await self._handle_mdd_popup(navigator)
            
            # Step 5: Verify successful navigation
            final_url = navigator.page.url
            if ('ecf.' in final_url and 'uscourts.gov' in final_url) or 'login.pl' in final_url:
                self.log_info(f"{log_prefix} Successfully completed NextGen navigation to: {final_url}")
                return True
            else:
                self.log_warning(f"{log_prefix} Unexpected final URL: {final_url}")
                # Don't fail entirely - might still be valid
                return True
            
        except Exception as e:
            self.log_error(f"{log_prefix} Error handling NextGen court selection: {e}")
            await navigator.save_screenshot("nextgen_court_selection_error")
            return False

    async def _handle_mdd_popup(self, navigator: PacerNavigator) -> None:
        """
        Handles the MDD (Maryland District) court popup that appears after clicking the NextGen link.
        
        Args:
            navigator: The PacerNavigator instance
        """
        log_prefix = "[MDDPopup]"
        
        try:
            self.log_info(f"{log_prefix} Looking for MDD court popup")
            
            # Wait a moment for popup to appear
            await asyncio.sleep(1)
            
            # Common popup selectors
            popup_selectors = [
                "#closePopupAlert",
                "div[role='dialog'] button:has-text('OK')",
                "div[role='dialog'] button:has-text('Close')",
                ".modal button:has-text('OK')",
                ".popup button:has-text('OK')",
                "button.btn-primary:has-text('OK')",
                "button[onclick*='close']",
                ".ui-dialog button:has-text('OK')",
            ]
            
            popup_handled = False
            for selector in popup_selectors:
                try:
                    popup_element = navigator.page.locator(selector).first
                    if await popup_element.is_visible(timeout=2000):
                        self.log_info(f"{log_prefix} Found MDD popup with selector: {selector}")
                        await popup_element.click()
                        await asyncio.sleep(0.5)
                        self.log_info(f"{log_prefix} Successfully clicked MDD popup button")
                        popup_handled = True
                        break
                except Exception as e:
                    self.log_debug(f"{log_prefix} Popup selector {selector} failed: {e}")
                    continue
            
            if not popup_handled:
                # Try to press Escape key as fallback
                try:
                    await navigator.page.keyboard.press("Escape")
                    await asyncio.sleep(0.5)
                    self.log_info(f"{log_prefix} Tried Escape key for popup handling")
                except Exception as e:
                    self.log_debug(f"{log_prefix} Escape key failed: {e}")
            
            self.log_info(f"{log_prefix} MDD popup handling completed")
            
        except Exception as e:
            self.log_warning(f"{log_prefix} Error handling MDD popup: {e}")
            # Don't fail the flow for popup handling issues

    def _get_court_display_name(self, court_id: str) -> str:
        """
        Get the display name for a court to help identify it in the NextGen table.
        
        Args:
            court_id: The court ID (e.g., 'ilnd', 'mdd')
        
        Returns:
            str: The display name for the court
        """
        court_names = {
            "ilnd": "Illinois Northern",
            "ilcd": "Illinois Central", 
            "ilsd": "Illinois Southern",
            "mdd": "Maryland",
            "cand": "California Northern",
            "cacd": "California Central",
            "caed": "California Eastern",
            "casd": "California Southern",
            "nysd": "New York Southern",
            "nyed": "New York Eastern",
            "nynd": "New York Northern",
            "nywd": "New York Western",
            "txsd": "Texas Southern",
            "txed": "Texas Eastern", 
            "txnd": "Texas Northern",
            "txwd": "Texas Western",
            # Add more court mappings as needed
        }
        
        court_id_lower = court_id.lower()
        return court_names.get(court_id_lower, court_id.upper())

    async def _handle_court_lookup_page(self, navigator: PacerNavigator) -> bool:
        """
        Handles the court-cmecf-lookup page that appears after client code submission.
        This page shows a table with court links that need to be clicked to access specific courts.
        
        The page URL is: https://pacer.uscourts.gov/file-case/court-cmecf-lookup
        Court links are typically in a table format with court names.
        """
        log_prefix = "[CourtLookup]"
        
        try:
            current_url = navigator.page.url
            self.log_info(f"{log_prefix} Handling court lookup page at: {current_url}")
            
            # Give the page time to load fully
            await asyncio.sleep(2)
            
            # Look for court links in different possible formats
            court_link_selectors = [
                "table a[href*='ecf']",  # Links to ECF courts in a table
                "a[href*='ecf.'][href*='uscourts.gov']",  # Any ECF court links
                "table td a",  # Generic table links
                "a:has-text('Northern District')",  # Specific district text
                "a:has-text('District')",  # Any district text
                "tbody tr td a"  # Table body links
            ]
            
            court_link_found = False
            
            for selector in court_link_selectors:
                try:
                    links = navigator.page.locator(selector)
                    link_count = await links.count()
                    
                    if link_count > 0:
                        self.log_info(f"{log_prefix} Found {link_count} court links with selector: {selector}")
                        
                        # For now, click the first available court link
                        # In the future, we could make this configurable to select specific courts
                        first_link = links.first
                        
                        # Get the link text and href for logging
                        link_text = await first_link.text_content() if await first_link.count() > 0 else "Unknown"
                        link_href = await first_link.get_attribute('href') if await first_link.count() > 0 else "Unknown"
                        
                        self.log_info(f"{log_prefix} Clicking court link: '{link_text}' -> {link_href}")
                        
                        # Click the link
                        await first_link.click()
                        
                        # Wait for navigation
                        await navigator.page.wait_for_load_state('domcontentloaded', timeout=10000)
                        await asyncio.sleep(2)
                        
                        # Verify we navigated to an ECF court site
                        new_url = navigator.page.url
                        if 'ecf.' in new_url and 'uscourts.gov' in new_url:
                            self.log_info(f"{log_prefix} Successfully navigated to court ECF site: {new_url}")
                            court_link_found = True
                            break
                        else:
                            self.log_warning(f"{log_prefix} Link did not lead to ECF site: {new_url}")
                            # Continue trying other selectors
                            
                except Exception as e:
                    self.log_debug(f"{log_prefix} Selector {selector} failed: {e}")
                    continue
            
            if not court_link_found:
                self.log_error(f"{log_prefix} Could not find or click any court links")
                # Save screenshot for debugging
                await navigator.save_screenshot("court_lookup_page_no_links")
                return False
            
            return True
            
        except Exception as e:
            self.log_error(f"{log_prefix} Error handling court lookup page: {e}")
            await navigator.save_screenshot("court_lookup_page_error")
            return False

    async def perform_login(self, navigator: PacerNavigator) -> bool:
        """
        Compatibility wrapper - delegates to perform_main_pacer_login.
        """
        return await self.perform_main_pacer_login(navigator)
