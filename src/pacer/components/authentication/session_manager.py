# /src/services/pacer/_authentication_components/session_manager.py
from __future__ import annotations
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer.components.navigator import PacerNavigator
    from src.infrastructure.protocols.logger import LoggerProtocol

class SessionManager(ComponentImplementation):
    """Manages PACER session state."""

    LOGGED_IN_INDICATOR_SELECTOR = "span.psc-login-panel-header.psc-pacer-blue-fg"
    LOGGED_IN_TEXT = 'Logged in as'

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        """Execute session-related actions."""
        action = data.get('action')
        if action == 'is_logged_in':
            return await self.is_logged_in(data['navigator'])
        else:
            raise PacerServiceError(f"Unknown action for SessionManager: {action}")

    async def is_logged_in(self, navigator: PacerNavigator) -> bool:
        """Checks if the user session is already active on the main PACER site."""
        log_prefix = "[SessionCheck]"
        try:
            # Use a shorter timeout for session check since login may have just completed
            logged_in_indicator_text = await navigator.get_text(self.LOGGED_IN_INDICATOR_SELECTOR, timeout_override_ms=2000)
            if logged_in_indicator_text and self.LOGGED_IN_TEXT in logged_in_indicator_text:
                user = logged_in_indicator_text.split(self.LOGGED_IN_TEXT)[-1].strip()
                self.log_info(f"{log_prefix} Already logged in as '{user}'")
                return True
        except Exception:
            self.log_debug(f"{log_prefix} Not logged in or error checking status.")
        return False
