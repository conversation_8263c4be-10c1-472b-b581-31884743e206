# /src/services/pacer/_report_components/report_generator.py
import asyncio
import json
import os
import re
from datetime import datetime
from typing import Any, Dict, List

from playwright.async_api import Error as PlaywrightError

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.pacer.components.browser.navigator import PacerNavigator


class ReportGenerator(ComponentImplementation):
    """
    Stateless component for generating reports from PACER. It handles
    navigation, configuration, and execution of reports.
    
    Uses court-specific logging to ensure ALL logs go to 
    data/{iso_date}/logs/pacer/{court_id}.log
    """
    
    def __init__(self, logger=None, config=None, court_logger=None):
        """
        Initialize ReportGenerator with optional court_logger.
        
        Args:
            logger: Standard logger
            config: Configuration dictionary
            court_logger: Optional court-specific logger to override standard logger
        """
        super().__init__(logger, config)
        
        # Use court_logger if provided, otherwise fall back to standard logger
        if court_logger:
            self.logger = court_logger
            self.log_debug("ReportGenerator initialized with court-specific logger")
    
    def set_court_logger(self, court_id: str, iso_date: str) -> None:
        """
        Set up court-specific logging for this component.
        
        Args:
            court_id: Court identifier (e.g., 'cand', 'nysd') 
            iso_date: ISO date string for directory organization
        """
        try:
            # Create court logger using component_base functionality
            court_logger = self.create_court_logger(court_id, iso_date)
            self.log_info(f"[{court_id.upper()}] ReportGenerator: Court logger configured - all logs will go to data/{iso_date}/logs/pacer/{court_id.lower()}.log")
        except Exception as e:
            self.log_error(f"Failed to create court logger for {court_id}: {e}", exc_info=True)

    async def execute(self, data: Any) -> Any:
        """Main execution method for the report generator."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a report generation action.
        """
        action = data.get("action")
        if action == "generate_civil_cases_report":
            return await self.generate_civil_cases_report(**data)
        else:
            raise ValueError(f"Unknown action for ReportGenerator: {action}")

    async def _debug_page_structure(self, navigator: PacerNavigator, log_prefix: str) -> None:
        """Debug helper to analyze current page structure."""
        try:
            current_url = navigator.page.url
            self.log_info(f"{log_prefix} 🔍 DEBUG URL: {current_url}")
            
            # Safely get page title with error handling
            try:
                page_title = await navigator.page.title()
                self.log_info(f"{log_prefix} 🔍 DEBUG Title: {page_title}")
            except PlaywrightError as title_error:
                if "Execution context was destroyed" in str(title_error):
                    self.log_info(f"{log_prefix} 🔍 DEBUG Title: [Context Destroyed]")
                else:
                    self.log_warning(f"{log_prefix} 🔍 DEBUG Title: [Error: {title_error}]")
            
            # Log all links on the page for debugging
            all_links = navigator.page.locator("a")
            link_count = await all_links.count()
            self.log_info(f"{log_prefix} 🔍 Total links on page: {link_count}")
            
            if link_count > 0 and link_count <= 25:  # Only log if reasonable number
                for i in range(min(link_count, 20)):  # Log first 20 links
                    link = all_links.nth(i)
                    href = await link.get_attribute("href")
                    text = await link.text_content()
                    self.log_info(f"{log_prefix} 🔍 Link {i}: text='{text}' href='{href}'")
            
        except Exception as e:
            self.log_debug(f"{log_prefix} Debug page structure failed: {e}")

    async def generate_civil_cases_report(self, **kwargs) -> bool:
        """
        Navigates to, configures, and runs the Civil Cases Filed report.

        Args:
            **kwargs: A dictionary containing:
                - navigator (PacerNavigator)
                - court_id (str)
                - from_date_str (str)
                - to_date_str (str)
                - ignore_download_service (PacerIgnoreDownloadService)
                - iso_date (str): ISO date string (YYYYMMDD format) for directory paths
                - court_logger (optional): Court-specific logger instance

        Returns:
            bool: True if cases were found, False otherwise.
        """
        navigator: PacerNavigator = kwargs.get("navigator")
        court_id: str = kwargs.get("court_id")
        from_date_str: str = kwargs.get("from_date_str")
        to_date_str: str = kwargs.get("to_date_str")
        ignore_download_service = kwargs.get("ignore_download_service")
        iso_date: str = kwargs.get("iso_date")
        court_logger = kwargs.get("court_logger")

        # CRITICAL: Set up court-specific logging to ensure ALL logs go to court log file
        if court_logger:
            # Use provided court logger
            self.logger = court_logger
            self.log_debug(f"[{court_id.upper()}] ReportGenerator: Using provided court logger")
        elif court_id and iso_date:
            # Create court logger if not provided
            self.set_court_logger(court_id, iso_date)
        
        log_prefix = f"[{court_id}] ReportGen:"
        self.log_info(f"{log_prefix} Starting civil cases report generation.")

        try:
            # Debug: Check current page state before navigation
            current_url = navigator.page.url
            
            # Safely get page title with fallback to URL if context is destroyed
            try:
                page_title = await navigator.page.title()
                self.log_info(f"{log_prefix} Current page title: {page_title}")
            except PlaywrightError as title_error:
                if "Execution context was destroyed" in str(title_error):
                    self.log_info(f"{log_prefix} Page title unavailable (execution context destroyed)")
                    page_title = "Context Destroyed"
                else:
                    self.log_warning(f"{log_prefix} Failed to get page title: {title_error}")
                    page_title = "Unknown"
            
            self.log_info(f"{log_prefix} Current page URL: {current_url}")
            
            # Check if we're in the right ECF system
            if 'ecf.' not in current_url or 'uscourts.gov' not in current_url:
                self.log_error(f"{log_prefix} Not in ECF system! URL: {current_url}")
                raise Exception("Not in court ECF system")
            
            # CRITICAL FIX: Check if we're already on the Civil Cases Filed report page
            # BUT we need to make sure it's the form page, not just any page with CaseFiled-Rpt.pl
            civil_report_patterns = [
                'CaseFiled-Rpt.pl',
                'c_CaseFiled-Rpt.pl',  # Court-specific pattern like CANDc_CaseFiled-Rpt.pl
                'Civil_Cases',
                'civil-cases',
                'civil_filed'
            ]
            
            for pattern in civil_report_patterns:
                if pattern in current_url:
                    self.log_info(f"{log_prefix} Found Civil Cases report page pattern: {pattern}")
                    
                    # Wait a bit for page to fully load
                    await navigator.page.wait_for_load_state('networkidle', timeout=5000)
                    await asyncio.sleep(1)
                    
                    # Check if we have the form fields needed to configure the report
                    try:
                        # Log page content for debugging
                        page_content = await navigator.page.content()
                        self.log_debug(f"{log_prefix} Page has {len(page_content)} characters")
                        
                        # Check for the filed_from input field
                        filed_from_locator = navigator.page.locator('input[name="filed_from"]')
                        filed_from_count = await filed_from_locator.count()
                        
                        if filed_from_count > 0:
                            self.log_info(f"{log_prefix} ✅ Already on Civil Cases Filed report form page - skipping Reports navigation")
                            self.log_info(f"{log_prefix} Found {filed_from_count} filed_from input fields")
                            # Skip to Step 2: Configure report parameters
                            await self._configure_and_run_report(navigator, court_id, from_date_str, to_date_str, log_prefix, ignore_download_service, iso_date)
                            return True
                        else:
                            self.log_info(f"{log_prefix} On CaseFiled-Rpt.pl but no form fields found")
                            self.log_info(f"{log_prefix} This might be a result page, not the form page")
                            self.log_info(f"{log_prefix} Will attempt to navigate to Reports menu")
                    except Exception as e:
                        self.log_debug(f"{log_prefix} Error checking for form fields: {e}")
                    break  # Exit the pattern loop since we found a match
            
            # Additional check: If we're already on a query or reports page, try to find direct civil cases link
            if 'query' in current_url.lower() or 'report' in current_url.lower() or 'rpt' in current_url.lower():
                self.log_info(f"{log_prefix} On query/reports page, checking for direct civil cases links...")
                
                # Try direct civil cases navigation
                civil_direct_patterns = [
                    "a:has-text('Civil Cases')",
                    "a:has-text('Civil')", 
                    "a[href*='Civil']",
                    "a[href*='CaseFiled']"
                ]
                
                for pattern in civil_direct_patterns:
                    try:
                        civil_locator = navigator.page.locator(pattern)
                        if await civil_locator.count() > 0:
                            self.log_info(f"{log_prefix} Found direct civil cases link: {pattern}")
                            await navigator.click(pattern)
                            self.log_info(f"{log_prefix} Clicked civil cases link, proceeding to configure report")
                            await self._configure_and_run_report(navigator, court_id, from_date_str, to_date_str, log_prefix, ignore_download_service, iso_date)
                            return True
                    except Exception as e:
                        self.log_debug(f"{log_prefix} Civil cases link {pattern} failed: {e}")
                        continue

            # First check if we need to navigate to Reports menu (from court-reporting-system.html)
            # The Reports link might be in the menu bar
            reports_menu_patterns = [
                'a.yuimenubaritemlabel:has-text("Reports")',  # Menu bar item
                'a[href*="DisplayMenu.pl?Reports"]',           # Direct menu link
                'a:has-text("Reports")'                        # Generic Reports link
            ]
            
            reports_count = 0
            reports_locator = None
            
            for pattern in reports_menu_patterns:
                try:
                    test_locator = navigator.page.locator(pattern)
                    count = await test_locator.count()
                    if count > 0:
                        reports_locator = test_locator
                        reports_count = count
                        self.log_info(f"{log_prefix} Found {reports_count} 'Reports' links with pattern: {pattern}")
                        break
                except Exception as e:
                    self.log_debug(f"{log_prefix} Pattern {pattern} failed: {e}")
                    continue
            
            if reports_count == 0:
                # Enhanced debugging and navigation strategy
                self.log_info(f"{log_prefix} 🔍 DEBUGGING: No standard Reports links found, analyzing page structure...")
                
                # First, log page structure for debugging
                await self._debug_page_structure(navigator, log_prefix)
                
                # Try comprehensive variations of Reports links
                report_variations = [
                    # Text-based selectors
                    "a:has-text('Report')",
                    "a:has-text('report')",
                    "a:has-text('REPORT')",
                    "a:has-text('Reports')",
                    "a:text('Reports')",
                    "a:text-is('Reports')",
                    
                    # Href-based selectors
                    "a[href*='reports']", 
                    "a[href*='Reports']",
                    "a[href*='rptmenu']",
                    "a[href*='qryReports']",
                    "a[href*='report']",
                    "a[href*='Report']",
                    
                    # Menu/navigation patterns
                    "li a:has-text('Reports')",
                    "td a:has-text('Reports')",
                    "div a:has-text('Reports')",
                    "nav a:has-text('Reports')",
                    ".menu a:has-text('Reports')",
                    ".nav a:has-text('Reports')",
                    
                    # Court-specific patterns
                    "input[value*='report']",
                    "button:has-text('Reports')",
                    "area[alt*='report']",
                ]
                
                reports_found = False
                for variation in report_variations:
                    try:
                        var_locator = navigator.page.locator(variation)
                        var_count = await var_locator.count()
                        if var_count > 0:
                            self.log_info(f"{log_prefix} ✅ Found {var_count} links with selector: {variation}")
                            # Try to use this variation
                            await var_locator.first.click()
                            self.log_info(f"{log_prefix} ✅ Successfully clicked {variation}")
                            reports_found = True
                            break
                    except Exception as e:
                        self.log_debug(f"{log_prefix} ❌ Failed to click {variation}: {e}")
                        continue
                
                if not reports_found:
                    # Try frame-based navigation
                    try:
                        frames = navigator.page.frames
                        self.log_info(f"{log_prefix} 🔍 Checking {len(frames)} frames for Reports links...")
                        
                        for frame in frames:
                            try:
                                frame_reports = frame.locator("a:has-text('Reports')")
                                frame_count = await frame_reports.count()
                                if frame_count > 0:
                                    self.log_info(f"{log_prefix} ✅ Found Reports link in frame: {frame.name or 'unnamed'}")
                                    await frame_reports.first.click()
                                    reports_found = True
                                    break
                            except Exception as frame_e:
                                self.log_debug(f"{log_prefix} Frame check failed: {frame_e}")
                                continue
                    except Exception as e:
                        self.log_debug(f"{log_prefix} Frame navigation failed: {e}")
                
                if not reports_found:
                    # Final fallback: try to navigate to reports URL directly
                    try:
                        current_url = navigator.page.url
                        if 'ecf.' in current_url and 'uscourts.gov' in current_url:
                            # Extract base URL and try common reports paths
                            base_url = current_url.split('/cgi-bin')[0] if '/cgi-bin' in current_url else current_url.rstrip('/')
                            reports_urls = [
                                f"{base_url}/cgi-bin/rptmenu.pl",
                                f"{base_url}/cgi-bin/qryReports.pl",
                                f"{base_url}/reports",
                                f"{base_url}/Reports"
                            ]
                            
                            for reports_url in reports_urls:
                                try:
                                    self.log_info(f"{log_prefix} 🔄 Trying direct navigation to: {reports_url}")
                                    await navigator.goto(reports_url)
                                    # Check if we successfully reached reports page
                                    await navigator.page.wait_for_load_state('domcontentloaded', timeout=5000)
                                    page_content = await navigator.page.content()
                                    if 'civil' in page_content.lower() or 'report' in page_content.lower():
                                        self.log_info(f"{log_prefix} ✅ Successfully navigated to reports via direct URL")
                                        reports_found = True
                                        break
                                except Exception as url_e:
                                    self.log_debug(f"{log_prefix} Direct URL failed {reports_url}: {url_e}")
                                    continue
                    except Exception as e:
                        self.log_debug(f"{log_prefix} Direct URL navigation failed: {e}")
                
                if not reports_found:
                    raise Exception("Could not find any Reports link variation after comprehensive search")
            else:
                # Use the Reports link we found
                await reports_locator.first.click()
                self.log_info(f"{log_prefix} Clicked Reports link")
                
                # Wait for the page or menu to load
                await navigator.page.wait_for_load_state('networkidle', timeout=5000)
                await asyncio.sleep(1)  # Brief pause for menu rendering
            
            # Step 1: Click on "Civil Cases" (category link)
            civil_category_variations = [
                "a:has-text('Civil Cases')",
                "a:has-text('Civil')",
                "a[href*='civil']",
                "a[href*='Civil']"
            ]
            
            civil_category_found = False
            for variation in civil_category_variations:
                try:
                    var_locator = navigator.page.locator(variation)
                    var_count = await var_locator.count()
                    if var_count > 0:
                        self.log_info(f"{log_prefix} Found {var_count} Civil Cases category links with selector: {variation}")
                        await navigator.click(variation)
                        self.log_info(f"{log_prefix} Successfully clicked Civil Cases category: {variation}")
                        civil_category_found = True
                        break
                except Exception as e:
                    self.log_debug(f"{log_prefix} Failed to click {variation}: {e}")
                    continue
            
            if not civil_category_found:
                # Log available links on the reports page
                self.log_info(f"{log_prefix} Could not find Civil Cases category link. Available links:")
                all_links = navigator.page.locator("a")
                link_count = await all_links.count()
                self.log_info(f"{log_prefix} Total links on reports page: {link_count}")
                
                for i in range(min(link_count, 20)):  # Log first 20 links
                    link = all_links.nth(i)
                    href = await link.get_attribute("href")
                    text = await link.text_content()
                    self.log_info(f"{log_prefix} Reports Link {i}: text='{text}' href='{href}'")
                
                raise Exception("Could not find Civil Cases category link")
            
            # We're already on the civil cases report page after clicking Civil Cases
            self.log_info(f"{log_prefix} Already on civil cases report page after clicking Civil Cases.")

            # Configure
            await navigator.fill('input[name="filed_from"]', from_date_str)
            await navigator.fill('input[name="filed_to"]', to_date_str)
            self.log_info(f"{log_prefix} Configured date range: {from_date_str} to {to_date_str}.")
            
            # Configure case type and NOS filters for better targeting
            await self._configure_form_filters(navigator, log_prefix, court_id)

            # Find and click the submit button
            submit_variations = [
                'input[type="submit"][value="Run Report"]',
                'input[type="submit"]',
                'button[type="submit"]',
                'input[value*="Run"]',
                'input[value*="Submit"]',
                'button:has-text("Run")',
                'button:has-text("Submit")'
            ]
            
            submit_found = False
            for variation in submit_variations:
                try:
                    var_locator = navigator.page.locator(variation)
                    var_count = await var_locator.count()
                    if var_count > 0:
                        self.log_info(f"{log_prefix} Found {var_count} submit buttons with selector: {variation}")
                        await navigator.click(variation)
                        self.log_info(f"{log_prefix} Successfully clicked submit button: {variation}")
                        submit_found = True
                        break
                except Exception as e:
                    self.log_debug(f"{log_prefix} Failed to click {variation}: {e}")
                    continue
            
            if not submit_found:
                # Log all input and button elements
                self.log_info(f"{log_prefix} Could not find submit button. Available form elements:")
                all_inputs = navigator.page.locator("input, button")
                input_count = await all_inputs.count()
                for i in range(min(input_count, 20)):
                    elem = all_inputs.nth(i)
                    tag = await elem.evaluate("el => el.tagName")
                    input_type = await elem.get_attribute("type")
                    value = await elem.get_attribute("value")
                    text = await elem.text_content()
                    self.log_info(f"{log_prefix} Form Element {i}: {tag} type='{input_type}' value='{value}' text='{text}'")
                
                raise Exception("Could not find submit button to run report")
            
            self.log_info(f"{log_prefix} 'Run Report' clicked.")

            # Verify
            no_cases_locator = navigator.page.locator("text=No cases found matching the criteria")
            if await no_cases_locator.is_visible():
                self.log_info(f"{log_prefix} No cases found.")
                
                # Save empty docket report log (no filtering needed for empty list)
                await self._save_docket_report_log(
                    court_id=court_id,
                    from_date_str=from_date_str,
                    to_date_str=to_date_str,
                    cases=[],
                    log_prefix=log_prefix,
                    iso_date=iso_date
                )
                return False

            # Extract cases from the report and save the docket log
            cases_data = await self._extract_cases_from_report(navigator, court_id, log_prefix)
            
            # CRITICAL FIX: Apply relevance filtering before saving docket report log
            filtered_cases = await self._filter_cases_for_relevance(cases_data, court_id, log_prefix)
            
            # Save docket report log with filtered cases only
            await self._save_docket_report_log(
                court_id=court_id,
                from_date_str=from_date_str,
                to_date_str=to_date_str,
                cases=filtered_cases,
                log_prefix=log_prefix,
                iso_date=iso_date
            )
            
            self.log_info(f"{log_prefix} Report generated successfully, cases found.")
            return True

        except PlaywrightError as e:
            self.log_error(f"{log_prefix} Report generation failed: {e}", exc_info=True)
            raise

    async def _configure_and_run_report(self, navigator, court_id: str, from_date_str: str, to_date_str: str, log_prefix: str, ignore_download_service, iso_date: str) -> None:
        """
        Configure and run the civil cases report when already on the report page.
        
        Args:
            navigator: PacerNavigator instance
            court_id: Court identifier
            from_date_str: Start date string (MM/DD/YY format)
            to_date_str: End date string (MM/DD/YY format)  
            log_prefix: Logging prefix
            ignore_download_service: Service for handling downloads
            iso_date: ISO date string for directory organization
        """
        try:
            self.log_info(f"{log_prefix} Configuring report parameters...")
            
            # Configure date range
            await navigator.fill('input[name="filed_from"]', from_date_str)
            await navigator.fill('input[name="filed_to"]', to_date_str)
            self.log_info(f"{log_prefix} Configured date range: {from_date_str} to {to_date_str}.")
            
            # Configure case type and NOS filters for better targeting
            await self._configure_form_filters(navigator, log_prefix, court_id)

            # Find and click the submit button
            submit_variations = [
                'input[type="submit"][value="Run Report"]',
                'input[type="submit"]',
                'button[type="submit"]',
                'input[value*="Run"]',
                'input[value*="Submit"]',
                'button:has-text("Run")',
                'button:has-text("Submit")'
            ]
            
            submit_found = False
            for variation in submit_variations:
                try:
                    var_locator = navigator.page.locator(variation)
                    var_count = await var_locator.count()
                    if var_count > 0:
                        self.log_info(f"{log_prefix} Found {var_count} submit buttons with selector: {variation}")
                        await navigator.click(variation)
                        self.log_info(f"{log_prefix} Successfully clicked submit button: {variation}")
                        submit_found = True
                        break
                except Exception as e:
                    self.log_debug(f"{log_prefix} Failed to click {variation}: {e}")
                    continue
            
            if not submit_found:
                # Log all input and button elements for debugging
                self.log_info(f"{log_prefix} Could not find submit button. Available form elements:")
                all_inputs = navigator.page.locator("input, button")
                input_count = await all_inputs.count()
                for i in range(min(input_count, 20)):
                    elem = all_inputs.nth(i)
                    tag = await elem.evaluate("el => el.tagName")
                    input_type = await elem.get_attribute("type")
                    value = await elem.get_attribute("value")
                    text = await elem.text_content()
                    self.log_info(f"{log_prefix} Form Element {i}: {tag} type='{input_type}' value='{value}' text='{text}'")
                
                raise Exception("Could not find submit button to run report")
            
            self.log_info(f"{log_prefix} Report execution initiated successfully.")
            
            # Wait for report to generate and check for cases
            await asyncio.sleep(2)
            
            # Check if report generated successfully
            no_cases_locator = navigator.page.locator("text=No cases found matching the criteria")
            if await no_cases_locator.is_visible():
                self.log_info(f"{log_prefix} No cases found in configured report.")
                # Save empty docket report log (no filtering needed for empty list)
                await self._save_docket_report_log(
                    court_id=court_id,
                    from_date_str=from_date_str,
                    to_date_str=to_date_str,
                    cases=[],
                    log_prefix=log_prefix,
                    iso_date=iso_date
                )
                return
            
            # Extract cases and save log
            cases_data = await self._extract_cases_from_report(navigator, court_id, log_prefix)
            
            # CRITICAL FIX: Apply relevance filtering before saving docket report log
            filtered_cases = await self._filter_cases_for_relevance(cases_data, court_id, log_prefix)
            
            await self._save_docket_report_log(
                court_id=court_id,
                from_date_str=from_date_str,
                to_date_str=to_date_str,
                cases=filtered_cases,
                log_prefix=log_prefix,
                iso_date=iso_date
            )

        except Exception as e:
            self.log_error(f"{log_prefix} Report configuration/execution failed: {e}", exc_info=True)
            raise
    
    async def _configure_form_filters(self, navigator, log_prefix: str, court_id: str = None) -> None:
        """
        Configure case type and nature of suit filters based on relevance configuration.
        
        Args:
            navigator: PacerNavigator instance
            log_prefix: Logging prefix
            court_id: Court identifier for court-specific configurations
        """
        try:
            self.log_info(f"{log_prefix} Configuring form filters for better case targeting...")
            
            # Configure case type - select civil cases
            case_type_selector = 'select[name="case_type"]'
            try:
                case_type_locator = navigator.page.locator(case_type_selector)
                if await case_type_locator.count() > 0:
                    # Select civil case type (value="cv")
                    await navigator.select_option(case_type_selector, ["cv"])
                    self.log_info(f"{log_prefix} ✅ Selected case type: civil (cv)")
                else:
                    self.log_warning(f"{log_prefix} Case type selector not found: {case_type_selector}")
            except Exception as e:
                self.log_warning(f"{log_prefix} Failed to configure case type: {e}")
            
            # Configure Nature of Suit codes based on relevance configuration
            nos_selector = 'select[name="nature_of_suit"]'
            try:
                nos_locator = navigator.page.locator(nos_selector)
                if await nos_locator.count() > 0:
                    # Get relevant NOS codes from configuration
                    relevant_nos_codes = await self._get_relevant_nos_codes()
                    
                    if relevant_nos_codes:
                        await navigator.select_option(nos_selector, relevant_nos_codes)
                        self.log_info(f"{log_prefix} ✅ Selected {len(relevant_nos_codes)} relevant NOS codes: {', '.join(relevant_nos_codes[:10])}{'...' if len(relevant_nos_codes) > 10 else ''}")
                    else:
                        self.log_info(f"{log_prefix} No specific NOS codes configured - searching all civil cases")
                else:
                    self.log_warning(f"{log_prefix} Nature of suit selector not found: {nos_selector}")
            except Exception as e:
                self.log_warning(f"{log_prefix} Failed to configure nature of suit: {e}")
            
            # CRITICAL FIX: Configure closed cases checkbox for specific courts
            if court_id and court_id.lower() == 'gand':
                self.log_info(f"{log_prefix} Configuring closed cases checkbox for GAND court...")
                closed_cases_selectors = [
                    'input[name="terminated"]',
                    'input[name="closed_cases"]',
                    'input[type="checkbox"][name="closed"]',
                    'input#terminated',
                    'input#closed_cases',
                    'input[type="checkbox"][value="terminated"]'
                ]
                
                checkbox_found = False
                for selector in closed_cases_selectors:
                    try:
                        checkbox_locator = navigator.page.locator(selector)
                        if await checkbox_locator.count() > 0:
                            # Check the checkbox
                            await checkbox_locator.check()
                            self.log_info(f"{log_prefix} ✅ Checked closed cases checkbox for GAND: {selector}")
                            checkbox_found = True
                            break
                    except Exception as e:
                        self.log_debug(f"{log_prefix} Closed cases checkbox selector {selector} failed: {e}")
                        continue
                
                if not checkbox_found:
                    self.log_warning(f"{log_prefix} Could not find closed cases checkbox for GAND court")
            
            self.log_info(f"{log_prefix} Form filter configuration completed")
            
        except Exception as e:
            self.log_error(f"{log_prefix} Form filter configuration failed: {e}", exc_info=True)
            # Don't raise - form filtering is optional, continue with report generation
    
    async def _get_relevant_nos_codes(self) -> List[str]:
        """
        Get relevant Nature of Suit codes from configuration.
        
        Returns:
            List of NOS codes that are explicitly or potentially relevant
        """
        try:
            # Import configuration service
            from src.pacer.services.configuration_service import ConfigurationService
            
            # Initialize configuration service
            config_service = ConfigurationService(
                logger=self.logger,
                config=self.config
            )
            await config_service._initialize_service()
            
            # Get relevance configuration
            relevance_config = await config_service.get_relevance_config()
            
            # Combine explicitly relevant and potentially relevant NOS codes
            explicit_nos = relevance_config.get('explicitly_relevant_nos', [])
            potential_nos = relevance_config.get('potentially_relevant_nos', [])
            
            # Combine and deduplicate
            relevant_codes = list(set(explicit_nos + potential_nos))
            
            self.log_debug(f"Found {len(relevant_codes)} relevant NOS codes: {relevant_codes}")
            return relevant_codes
            
        except Exception as e:
            self.log_warning(f"Failed to load relevant NOS codes from configuration: {e}")
            # Fallback to hardcoded relevant codes for mass tort/product liability cases
            return ['365', '367', '368', '245', '360', '240', '470']

    async def _extract_cases_from_report(self, navigator: PacerNavigator, court_id: str, log_prefix: str) -> List[Dict[str, Any]]:
        """
        Extract cases from the generated civil cases report in proper JSON format.
        
        Args:
            navigator: PacerNavigator instance
            court_id: Court identifier
            log_prefix: Logging prefix
            
        Returns:
            List of properly structured case dictionaries
        """
        cases = []
        
        try:
            # Wait for the report table to load
            await navigator.page.wait_for_load_state('networkidle', timeout=10000)
            await asyncio.sleep(1)
            
            # Find the main results table
            table_locator = navigator.page.locator("table")
            table_count = await table_locator.count()
            
            if table_count == 0:
                self.log_warning(f"{log_prefix} No tables found on report page")
                return cases
            
            # Get the main data table (usually the largest one)
            main_table = None
            max_rows = 0
            
            for i in range(table_count):
                table = table_locator.nth(i)
                rows = table.locator("tr")
                row_count = await rows.count()
                
                if row_count > max_rows:
                    max_rows = row_count
                    main_table = table
            
            if main_table is None or max_rows <= 1:
                self.log_warning(f"{log_prefix} No data rows found in tables")
                return cases
            
            self.log_info(f"{log_prefix} Processing {max_rows - 1} case rows from report table")
            
            # Extract data from each row (skip header row)
            rows = main_table.locator("tr")
            current_time = datetime.now().isoformat()
            
            for row_index in range(1, max_rows):  # Skip header row
                try:
                    row = rows.nth(row_index)
                    cells = row.locator("td, th")
                    cell_count = await cells.count()
                    
                    if cell_count < 2:  # Need at least case number and title
                        continue
                    
                    # Extract raw cell data for parsing
                    raw_cells = []
                    for cell_index in range(cell_count):
                        cell = cells.nth(cell_index)
                        cell_text = await cell.text_content()
                        
                        # Check for links within the cell
                        links = cell.locator("a")
                        link_count = await links.count()
                        
                        cell_links = []
                        for link_index in range(link_count):
                            link = links.nth(link_index)
                            link_text = await link.text_content()
                            link_href = await link.get_attribute("href")
                            
                            cell_links.append({
                                "text": link_text.strip() if link_text else "",
                                "href": link_href or ""
                            })
                        
                        raw_cells.append({
                            "text": cell_text.strip() if cell_text else "",
                            "links": cell_links
                        })
                    
                    # Parse structured case data from raw cells
                    case_data = self._parse_case_data_from_cells(raw_cells, court_id, current_time, log_prefix)
                    
                    if case_data:
                        cases.append(case_data)
                    
                except Exception as row_error:
                    self.log_debug(f"{log_prefix} Error extracting row {row_index}: {row_error}")
                    continue
            
            self.log_info(f"{log_prefix} Successfully extracted {len(cases)} cases from report")
            return cases
            
        except Exception as e:
            self.log_error(f"{log_prefix} Failed to extract cases from report: {e}", exc_info=True)
            return cases
    
    def _parse_case_data_from_cells(self, raw_cells: List[Dict[str, Any]], court_id: str, current_time: str, log_prefix: str) -> Dict[str, Any]:
        """
        Parse structured case data from raw table cells into proper JSON format.
        
        Args:
            raw_cells: List of cell data with text and links
            court_id: Court identifier
            current_time: Current timestamp
            log_prefix: Logging prefix
            
        Returns:
            Properly structured case dictionary or None if parsing fails
        """
        try:
            if len(raw_cells) < 2:
                return None
            
            # First cell contains case number and title (combined)
            first_cell = raw_cells[0]
            case_text = first_cell["text"]
            
            # Extract docket number (first part, exactly 13 characters if it matches pattern)
            docket_link = None
            docket_num = ""
            versus = case_text
            
            if first_cell["links"]:
                docket_link = first_cell["links"][0]["href"]
                link_text = first_cell["links"][0]["text"].strip()
                # Extract exactly 13 character docket number (N:YY-cv-NNNNN format)
                if len(link_text) >= 13 and ':' in link_text and '-cv-' in link_text:
                    docket_num = link_text[:13]  # Take exactly 13 characters
                    # Extract versus part (everything after docket number)
                    if len(case_text) > len(link_text):
                        versus = case_text[len(link_text):].strip()
                        # Remove any leading spaces or separators
                        versus = versus.lstrip(' -').strip()
            
            # Search for filing date in multiple cells with flexible patterns
            filing_date = ""
            
            # Try multiple patterns for date extraction
            date_patterns = [
                r"Case filed:\s*(\d{1,2}/\d{1,2}/\d{2,4})",
                r"Filed:\s*(\d{1,2}/\d{1,2}/\d{2,4})",
                r"Date Filed:\s*(\d{1,2}/\d{1,2}/\d{2,4})",
                r"Filing Date:\s*(\d{1,2}/\d{1,2}/\d{2,4})",
                # Standalone date pattern (MM/DD/YYYY or MM/DD/YY)
                r"^(\d{1,2}/\d{1,2}/\d{2,4})$"
            ]
            
            # Search through first few cells for filing date
            for cell_idx in range(min(4, len(raw_cells))):
                if filing_date:  # Already found
                    break
                    
                cell_text = raw_cells[cell_idx]["text"]
                
                # Try each pattern
                for pattern in date_patterns:
                    match = re.search(pattern, cell_text.strip(), re.IGNORECASE)
                    if match:
                        filing_date = match.group(1).strip()
                        # Convert MM/DD/YY to MM/DD/YYYY if needed
                        if filing_date and len(filing_date.split('/')[-1]) == 2:
                            parts = filing_date.split('/')
                            # Assume 20YY for years 00-30, 19YY for years 31-99
                            year = int(parts[2])
                            if year <= 30:
                                parts[2] = f"20{parts[2]}"
                            else:
                                parts[2] = f"19{parts[2]}"
                            filing_date = '/'.join(parts)
                        break
            
            # Fourth cell usually contains detailed case information
            cause = ""
            nos = ""
            defendants = []
            
            if len(raw_cells) > 3:
                details_text = raw_cells[3]["text"]
                
                # Parse cause information
                if "Cause:" in details_text:
                    cause_parts = details_text.split("Cause:")[1].split("\n")[0].strip()
                    cause = cause_parts
                
                # Parse NOS (Nature of Suit)
                if "NOS:" in details_text:
                    nos_parts = details_text.split("NOS:")[1].split("\n")[0].strip()
                    nos = nos_parts
                
                # Extract defendants using improved parsing logic
                defendants = self._extract_defendants_from_versus(versus, log_prefix)
                if not defendants:
                    self.log_warning(f"{log_prefix} ⚠️ DEFENDANT EXTRACTION: No defendants found in versus: '{versus}'")
            
            # Create properly structured case data
            case_data = {
                "court_id": court_id,
                "docket_num": docket_num,
                "versus": versus,
                "filing_date": filing_date,
                "docket_link": docket_link or "",
                "extracted_at": current_time,
                "source": "PACER Case Report",
                "defendants": defendants,
                "defendant": defendants,  # Both formats for compatibility
                "cause": cause,
                "nos": nos
            }
            
            return case_data
            
        except Exception as e:
            self.log_debug(f"{log_prefix} Error parsing case data from cells: {e}")
            return None
    
    async def _save_docket_report_log(
        self, 
        court_id: str, 
        from_date_str: str, 
        to_date_str: str, 
        cases: List[Dict[str, Any]], 
        log_prefix: str,
        iso_date: str = None
    ) -> None:
        """
        Save the docket report as a JSON file with properly structured format.
        CRITICAL: DO NOT OVERWRITE EXISTING FILES!
        
        Args:
            court_id: Court identifier
            from_date_str: Start date string (MM/DD/YY format)
            to_date_str: End date string (MM/DD/YY format)
            cases: List of structured case data
            log_prefix: Logging prefix
            iso_date: ISO date string (YYYYMMDD format) from config
        """
        try:
            # Use iso_date from config if provided, ensure it's in correct format
            if not iso_date:
                raise ValueError("iso_date must be provided from configuration")
            
            # Format the iso_date to ensure it's YYYYMMDD
            from src.infrastructure.utils.date_formatter import format_iso_date
            iso_date = format_iso_date(iso_date)
            
            # Get current datetime for timestamp only
            current_date = datetime.now()
            iso_date_formatted = current_date.strftime("%Y%m%d")
            
            # Create directory structure
            log_dir = f"data/{iso_date}/logs/docket_report"
            os.makedirs(log_dir, exist_ok=True)
            
            # Create log file path
            log_file_path = f"{log_dir}/{court_id.lower()}.json"
            
            # CRITICAL CHECK: DO NOT OVERWRITE EXISTING FILES
            if os.path.exists(log_file_path):
                self.log_warning(f"{log_prefix} DOCKET REPORT LOG ALREADY EXISTS at {log_file_path} - NOT OVERWRITING!")
                # Load existing file to check if it has cases
                with open(log_file_path, 'r') as f:
                    existing_data = json.load(f)
                    existing_cases = existing_data.get('cases', [])
                    if existing_cases:
                        self.log_info(f"{log_prefix} Existing file has {len(existing_cases)} cases - KEEPING EXISTING FILE")
                        return  # DO NOT SAVE/OVERWRITE
                    else:
                        self.log_warning(f"{log_prefix} Existing file has 0 cases, will update with new data ({len(cases)} cases)")
            
            # Prepare properly structured log data with metadata section
            log_data = {
                "metadata": {
                    "court_id": court_id,
                    "generated_at": current_date.isoformat(),
                    "date_range": f"{from_date_str} to {to_date_str}",
                    "total_cases": len(cases),
                    "source": "PACER Case Filed Report",
                    "scraping_date_used": iso_date,
                    "iso_date_provided": iso_date
                },
                "cases": cases
            }
            
            # Write JSON file
            with open(log_file_path, 'w') as f:
                json.dump(log_data, f, indent=2, ensure_ascii=False)
            
            self.log_info(f"{log_prefix} Docket report log saved: {log_file_path} ({len(cases)} cases)")
            
        except Exception as e:
            self.log_error(f"{log_prefix} Failed to save docket report log: {e}", exc_info=True)
            raise
    
    async def _filter_cases_for_relevance(self, cases: List[Dict[str, Any]], court_id: str, log_prefix: str) -> List[Dict[str, Any]]:
        """
        CRITICAL FIX: Filter cases using both relevance engine and local file existence checks.
        This performs two-stage filtering:
        1. Check if docket already exists locally (JSON + PDF/ZIP/MD)
        2. Apply relevance filtering using relevance_config.json
        
        Args:
            cases: List of case dictionaries
            court_id: Court identifier
            log_prefix: Logging prefix
            
        Returns:
            List of filtered case dictionaries (only relevant cases that don't exist locally)
        """
        try:
            # Import required services
            from src.pacer.services.relevance_service import RelevanceService
            from src.pacer.services.configuration_service import ConfigurationService
            from src.pacer.components.download.artifact_checker import DocketArtifactChecker
            
            # Initialize services
            config_service = ConfigurationService(logger=self.logger, config=self.config)
            await config_service._initialize_service()
            
            relevance_service = RelevanceService(
                configuration_service=config_service,
                logger=self.logger,
                config=self.config
            )
            await relevance_service._initialize_service()
            
            # Initialize artifact checker for local file existence checks
            # Pass court_logger (which is self.logger for this component)
            artifact_checker = DocketArtifactChecker(
                court_logger=self.logger,  # Changed from 'logger' to 'court_logger'
                config=self.config,
                pacer_repository=None  # Repository not needed for file checks
            )
            
            filtered_cases = []
            total_cases = len(cases)
            local_exists_count = 0
            relevance_excluded_count = 0
            iso_date = self.config.get('iso_date')
            
            self.log_info(f"{log_prefix} 🔍 Starting two-stage filtering for {total_cases} cases...")
            
            for case in cases:
                try:
                    docket_num = case.get('docket_num', '')
                    versus = case.get('versus', '')
                    
                    # STAGE 1: Check if docket already exists locally
                    if iso_date:
                        should_download, reason = await artifact_checker.execute({
                            "action": "should_download_docket",
                            "court_id": court_id,
                            "docket_num": docket_num,
                            "versus": versus,
                            "iso_date": iso_date
                        })
                        
                        if not should_download:
                            local_exists_count += 1
                            self.log_debug(f"{log_prefix} 📁 EXISTS LOCALLY: {versus} - {reason}")
                            continue  # Skip this case, it already exists
                    
                    # STAGE 2: Apply relevance filtering
                    relevance_result = await relevance_service.determine_case_relevance(
                        case_details=case,
                        court_id=court_id,
                        docket_num=docket_num
                    )
                    
                    if relevance_result.get('is_relevant', False):
                        # Keep relevant cases that don't exist locally
                        filtered_cases.append(case)
                        self.log_debug(f"{log_prefix} ✅ RELEVANT & NEW: {versus} - {relevance_result.get('relevance_reason', 'unknown')}")
                    else:
                        # Log filtered out cases for transparency
                        relevance_excluded_count += 1
                        reason = relevance_result.get('relevance_reason', 'unknown')
                        cause = case.get('cause', 'N/A')
                        nos = case.get('nos', 'N/A')
                        self.log_info(f"{log_prefix} ❌ NOT RELEVANT: {versus} (Cause: {cause}, NOS: {nos}) - Reason: {reason}")
                        
                except Exception as case_error:
                    self.log_warning(f"{log_prefix} Error filtering case {case.get('versus', 'Unknown')}: {case_error}")
                    # When in doubt, exclude the case to prevent data pollution
                    continue
            
            filtered_count = len(filtered_cases)
            total_excluded = total_cases - filtered_count
            
            self.log_info(f"{log_prefix} 🔍 TWO-STAGE FILTERING COMPLETE:")
            self.log_info(f"{log_prefix}   📁 Already exists locally: {local_exists_count}")
            self.log_info(f"{log_prefix}   ❌ Not relevant: {relevance_excluded_count}")
            self.log_info(f"{log_prefix}   ✅ To process (relevant & new): {filtered_count}")
            self.log_info(f"{log_prefix}   📊 Total cases examined: {total_cases}")
            
            return filtered_cases
            
        except Exception as e:
            self.log_error(f"{log_prefix} CRITICAL: Case filtering failed: {e}", exc_info=True)
            # Return empty list to prevent data pollution if filtering fails
            self.log_warning(f"{log_prefix} Returning empty case list to prevent saving unfiltered data")
            return []
    
    async def extract_report_data(self, **kwargs) -> list:
        """
        Public method to extract data from a report page.
        This method can be called externally to extract case data.
        """
        navigator = kwargs.get("navigator")
        court_id = kwargs.get("court_id", "UNKNOWN")
        iso_date = kwargs.get("iso_date")
        court_logger = kwargs.get("court_logger")
        
        # CRITICAL: Set up court-specific logging
        if court_logger:
            self.logger = court_logger
            self.log_debug(f"[{court_id.upper()}] ReportGenerator: Using provided court logger for data extraction")
        elif court_id and iso_date:
            self.set_court_logger(court_id, iso_date)
        
        log_prefix = f"[{court_id}] ReportGen:"
        
        if not navigator:
            self.log_error(f"{log_prefix} Navigator not provided for data extraction")
            return []
        
        return await self._extract_cases_from_report(navigator, court_id, log_prefix)
    
    def _extract_defendants_from_versus(self, versus: str, log_prefix: str) -> List[Dict[str, str]]:
        """Extract defendants from versus field with comprehensive logging and parsing"""
        if not versus:
            self.log_debug(f"{log_prefix} No versus field provided")
            return []
        
        defendants = []
        
        # Look for various patterns of "v." or "vs."
        vs_patterns = [' v. ', ' V. ', ' vs. ', ' Vs. ', ' v ', ' V ']
        defendant_text = None
        matched_pattern = None
        
        for pattern in vs_patterns:
            if pattern in versus:
                parts = versus.split(pattern, 1)  # Split only on first occurrence
                if len(parts) > 1:
                    defendant_text = parts[1].strip()
                    matched_pattern = pattern
                    self.log_debug(f"{log_prefix} 🎯 DEFENDANT EXTRACTION: Found pattern '{pattern}' in versus")
                    break
        
        if not defendant_text:
            self.log_warning(f"{log_prefix} ⚠️ DEFENDANT EXTRACTION: No valid versus pattern found in: '{versus}'")
            return []
        
        self.log_debug(f"{log_prefix} 🎯 DEFENDANT EXTRACTION: Raw defendant text: '{defendant_text}'")
        
        # Handle "et al" cases - remove et al suffix
        if " et al" in defendant_text.lower():
            original_text = defendant_text
            defendant_text = re.sub(r'\s+et\s+al\.?.*$', '', defendant_text, flags=re.IGNORECASE).strip()
            self.log_info(f"{log_prefix} 🎯 DEFENDANT EXTRACTION: Removed 'et al': '{original_text}' -> '{defendant_text}'")
        
        # Check if this looks like a single entity (contains Inc., LLC, Corp., etc.)
        entity_indicators = ['Inc.', 'LLC', 'Corp.', 'Ltd.', 'Co.', 'Company', 'Corporation']
        has_entity_indicator = any(indicator in defendant_text for indicator in entity_indicators)
        
        if has_entity_indicator:
            # Treat as single defendant to avoid splitting "Avlon Industries, Inc."
            defendants.append({"name": defendant_text})
            self.log_info(f"{log_prefix} 🎯 DEFENDANT EXTRACTION: Single entity defendant: '{defendant_text}'")
        else:
            # Split by comma for multiple defendants, but be careful with company names
            parts = [part.strip() for part in defendant_text.split(',')]
            
            # Rejoin parts that might be part of a company name
            processed_parts = []
            i = 0
            while i < len(parts):
                current_part = parts[i]
                
                # Check if next part looks like a company suffix
                if (i + 1 < len(parts) and 
                    parts[i + 1].strip().lower() in ['inc.', 'inc', 'llc', 'corp.', 'corp', 'ltd.', 'ltd', 'co.', 'co']):
                    # Combine with next part
                    combined = f"{current_part}, {parts[i + 1]}".strip()
                    processed_parts.append(combined)
                    self.log_debug(f"{log_prefix} 🎯 DEFENDANT EXTRACTION: Combined company parts: '{current_part}' + '{parts[i + 1]}' = '{combined}'")
                    i += 2  # Skip next part since we combined it
                else:
                    if current_part:  # Only add non-empty parts
                        processed_parts.append(current_part)
                    i += 1
            
            # Convert to defendant objects
            for name in processed_parts:
                if name.strip():
                    defendants.append({"name": name.strip()})
                    self.log_debug(f"{log_prefix} 🎯 DEFENDANT EXTRACTION: Added defendant: '{name.strip()}'")
        
        self.log_info(f"{log_prefix} 🎯 DEFENDANT EXTRACTION: Final result - {len(defendants)} defendants extracted")
        for i, defendant in enumerate(defendants):
            self.log_info(f"{log_prefix} 🎯 DEFENDANT EXTRACTION: Defendant {i+1}: '{defendant['name']}'")
        
        return defendants