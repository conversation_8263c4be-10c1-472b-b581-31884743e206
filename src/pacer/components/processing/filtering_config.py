"""
Filtering Configuration Manager

This module provides configuration management for docket artifact filtering,
including loading settings from config files and applying environment-specific
overrides.
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path


class FilteringConfig:
    """
    Configuration manager for docket artifact filtering.
    
    Handles loading configuration from YAML files, applying environment-specific
    overrides, and providing convenient access to filtering settings.
    """
    
    def __init__(self, config_path: Optional[str] = None, environment: Optional[str] = None):
        """
        Initialize the filtering configuration.
        
        Args:
            config_path: Path to the filtering configuration file
            environment: Environment name for specific overrides (development, testing, production)
        """
        self.config_path = config_path or self._get_default_config_path()
        self.environment = environment or self._detect_environment()
        self.config = self._load_config()
    
    def _get_default_config_path(self) -> str:
        """Get the default path to the filtering configuration file."""
        # Look for config file in project root
        project_root = Path(__file__).parent.parent.parent.parent.parent
        config_file = project_root / "config" / "docket_filtering.yml"
        
        if config_file.exists():
            return str(config_file)
        
        # Fallback to a basic configuration
        return None
    
    def _detect_environment(self) -> str:
        """Detect the current environment from environment variables."""
        env = os.getenv('ENVIRONMENT', os.getenv('ENV', 'development'))
        return env.lower()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file and apply environment overrides."""
        # Default configuration
        default_config = {
            'docket_filtering': {
                'enabled': True,
                'batch_processing': {
                    'batch_size': 50,
                    'max_concurrent_batches': 3,
                    'batch_timeout': 30
                },
                'artifact_checking': {
                    'check_json_files': True,
                    'check_artifact_files': True,
                    'check_html_only_flag': True,
                    'check_transferred_cases': True,
                    'enable_dynamodb_checking': True
                },
                'filtering_rules': {
                    'skip_html_only': True,
                    'skip_transferred_with_existing': True,
                    'skip_cases_with_artifacts': True,
                    'skip_json_without_artifacts': False
                },
                'error_handling': {
                    'continue_on_filter_error': True,
                    'max_filter_retries': 2,
                    'retry_delay': 1.0,
                    'detailed_error_logging': True
                },
                'performance': {
                    'enable_result_caching': True,
                    'cache_ttl': 300,
                    'parallel_fs_operations': True,
                    'max_fs_operations': 10
                },
                'logging': {
                    'log_filter_statistics': True,
                    'log_individual_decisions': False,
                    'log_performance_metrics': True,
                    'include_filter_reasons': True
                },
                'integration': {
                    'auto_initialize_with_repository': True,
                    'enable_in_workflow_orchestrator': True,
                    'enable_in_docket_orchestrator': True,
                    'enable_in_parallel_processing': True
                }
            }
        }
        
        # Load from file if available
        if self.config_path and os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    file_config = yaml.safe_load(f)
                    
                # Merge file config with defaults
                if file_config:
                    default_config = self._deep_merge(default_config, file_config)
                    
            except Exception as e:
                # Use defaults if config file can't be loaded
                pass
        
        # Apply environment-specific overrides
        if 'environments' in default_config and self.environment in default_config['environments']:
            env_overrides = default_config['environments'][self.environment]
            default_config = self._deep_merge(default_config, env_overrides)
        
        return default_config['docket_filtering']
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries."""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
                
        return result
    
    def is_enabled(self) -> bool:
        """Check if docket filtering is enabled."""
        return self.config.get('enabled', True)
    
    def get_batch_size(self) -> int:
        """Get the batch size for parallel filtering."""
        return self.config.get('batch_processing', {}).get('batch_size', 50)
    
    def get_max_concurrent_batches(self) -> int:
        """Get the maximum number of concurrent batches."""
        return self.config.get('batch_processing', {}).get('max_concurrent_batches', 3)
    
    def get_batch_timeout(self) -> int:
        """Get the timeout for batch processing in seconds."""
        return self.config.get('batch_processing', {}).get('batch_timeout', 30)
    
    def should_check_json_files(self) -> bool:
        """Check if JSON file checking is enabled."""
        return self.config.get('artifact_checking', {}).get('check_json_files', True)
    
    def should_check_artifact_files(self) -> bool:
        """Check if artifact file checking is enabled."""
        return self.config.get('artifact_checking', {}).get('check_artifact_files', True)
    
    def should_check_html_only_flag(self) -> bool:
        """Check if html_only flag checking is enabled."""
        return self.config.get('artifact_checking', {}).get('check_html_only_flag', True)
    
    def should_check_transferred_cases(self) -> bool:
        """Check if transferred case checking is enabled."""
        return self.config.get('artifact_checking', {}).get('check_transferred_cases', True)
    
    def is_dynamodb_checking_enabled(self) -> bool:
        """Check if DynamoDB existence checking is enabled."""
        return self.config.get('artifact_checking', {}).get('enable_dynamodb_checking', True)
    
    def should_skip_html_only(self) -> bool:
        """Check if html_only cases should be skipped."""
        return self.config.get('filtering_rules', {}).get('skip_html_only', True)
    
    def should_skip_transferred_with_existing(self) -> bool:
        """Check if transferred cases with existing transferor should be skipped."""
        return self.config.get('filtering_rules', {}).get('skip_transferred_with_existing', True)
    
    def should_skip_cases_with_artifacts(self) -> bool:
        """Check if cases with existing artifacts should be skipped."""
        return self.config.get('filtering_rules', {}).get('skip_cases_with_artifacts', True)
    
    def should_skip_json_without_artifacts(self) -> bool:
        """Check if cases with JSON but no artifacts should be skipped."""
        return self.config.get('filtering_rules', {}).get('skip_json_without_artifacts', False)
    
    def should_continue_on_filter_error(self) -> bool:
        """Check if processing should continue when filtering fails."""
        return self.config.get('error_handling', {}).get('continue_on_filter_error', True)
    
    def get_max_filter_retries(self) -> int:
        """Get the maximum number of filter retries."""
        return self.config.get('error_handling', {}).get('max_filter_retries', 2)
    
    def get_retry_delay(self) -> float:
        """Get the retry delay in seconds."""
        return self.config.get('error_handling', {}).get('retry_delay', 1.0)
    
    def should_log_detailed_errors(self) -> bool:
        """Check if detailed error logging is enabled."""
        return self.config.get('error_handling', {}).get('detailed_error_logging', True)
    
    def is_result_caching_enabled(self) -> bool:
        """Check if result caching is enabled."""
        return self.config.get('performance', {}).get('enable_result_caching', True)
    
    def get_cache_ttl(self) -> int:
        """Get the cache TTL in seconds."""
        return self.config.get('performance', {}).get('cache_ttl', 300)
    
    def is_parallel_fs_operations_enabled(self) -> bool:
        """Check if parallel file system operations are enabled."""
        return self.config.get('performance', {}).get('parallel_fs_operations', True)
    
    def get_max_fs_operations(self) -> int:
        """Get the maximum number of concurrent file system operations."""
        return self.config.get('performance', {}).get('max_fs_operations', 10)
    
    def should_log_filter_statistics(self) -> bool:
        """Check if filter statistics logging is enabled."""
        return self.config.get('logging', {}).get('log_filter_statistics', True)
    
    def should_log_individual_decisions(self) -> bool:
        """Check if individual decision logging is enabled."""
        return self.config.get('logging', {}).get('log_individual_decisions', False)
    
    def should_log_performance_metrics(self) -> bool:
        """Check if performance metrics logging is enabled."""
        return self.config.get('logging', {}).get('log_performance_metrics', True)
    
    def should_include_filter_reasons(self) -> bool:
        """Check if filter reasons should be included in processed items."""
        return self.config.get('logging', {}).get('include_filter_reasons', True)
    
    def should_auto_initialize_with_repository(self) -> bool:
        """Check if filter service should be auto-initialized when repository is available."""
        return self.config.get('integration', {}).get('auto_initialize_with_repository', True)
    
    def is_enabled_in_workflow_orchestrator(self) -> bool:
        """Check if filtering is enabled in workflow orchestrator."""
        return self.config.get('integration', {}).get('enable_in_workflow_orchestrator', True)
    
    def is_enabled_in_docket_orchestrator(self) -> bool:
        """Check if filtering is enabled in docket processing orchestrator."""
        return self.config.get('integration', {}).get('enable_in_docket_orchestrator', True)
    
    def is_enabled_in_parallel_processing(self) -> bool:
        """Check if filtering is enabled in parallel processing."""
        return self.config.get('integration', {}).get('enable_in_parallel_processing', True)
    
    def get_config_dict(self) -> Dict[str, Any]:
        """Get the complete configuration dictionary."""
        return self.config.copy()
    
    def get_config_summary(self) -> str:
        """Get a human-readable summary of the current configuration."""
        summary_lines = [
            f"Docket Filtering Configuration (Environment: {self.environment})",
            f"  Enabled: {self.is_enabled()}",
            f"  Batch size: {self.get_batch_size()}",
            f"  Max concurrent batches: {self.get_max_concurrent_batches()}",
            f"  DynamoDB checking: {self.is_dynamodb_checking_enabled()}",
            f"  Skip html_only: {self.should_skip_html_only()}",
            f"  Skip with artifacts: {self.should_skip_cases_with_artifacts()}",
            f"  Skip transferred with existing: {self.should_skip_transferred_with_existing()}",
            f"  Continue on error: {self.should_continue_on_filter_error()}",
            f"  Result caching: {self.is_result_caching_enabled()}",
            f"  Log statistics: {self.should_log_filter_statistics()}",
            f"  Workflow orchestrator: {self.is_enabled_in_workflow_orchestrator()}",
            f"  Docket orchestrator: {self.is_enabled_in_docket_orchestrator()}"
        ]
        
        return "\n".join(summary_lines)


# Global configuration instance
_global_config = None


def get_filtering_config(config_path: Optional[str] = None, environment: Optional[str] = None) -> FilteringConfig:
    """
    Get the global filtering configuration instance.
    
    Args:
        config_path: Path to configuration file (only used on first call)
        environment: Environment name (only used on first call)
        
    Returns:
        FilteringConfig instance
    """
    global _global_config
    
    if _global_config is None:
        _global_config = FilteringConfig(config_path, environment)
    
    return _global_config


def reset_filtering_config():
    """Reset the global configuration instance (useful for testing)."""
    global _global_config
    _global_config = None