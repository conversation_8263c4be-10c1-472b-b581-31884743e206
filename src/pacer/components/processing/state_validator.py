"""
State Validator for Sequential PACER Workflow.

This validator ensures proper state transitions and validation checkpoints
between workflow steps in the sequential docket processing system.

MISSION: Validate state between workflow steps
KEY REQUIREMENTS:
- Ensure each step completes successfully before proceeding
- Validate page state and navigation results
- Provide checkpoints for error recovery
- Track state history for debugging
"""

import asyncio
import json
from typing import Any, Dict, Optional, List, Union
from enum import Enum
from datetime import datetime
from src.infrastructure.patterns.component_base import AsyncServiceBase


class ValidationResult(Enum):
    """Results of state validation."""
    VALID = "valid"
    INVALID = "invalid"
    WARNING = "warning"
    ERROR = "error"
    TIMEOUT = "timeout"


class PageState(Enum):
    """Expected page states for validation."""
    UNKNOWN = "unknown"
    LOGIN_PAGE = "login_page"
    COURT_HOME = "court_home"
    QUERY_PAGE = "query_page"
    QUERY_RESULTS = "query_results"
    DOCKET_SELECTION = "docket_selection"
    DOCKET_OPTIONS = "docket_options"
    DOCKET_SHEET = "docket_sheet"
    ERROR_PAGE = "error_page"


class StateValidator(AsyncServiceBase):
    """
    Validates states and transitions in the sequential workflow.
    
    Provides comprehensive validation of page states, navigation results,
    and workflow step completion to ensure robust sequential processing.
    """

    def __init__(
        self,
        court_logger: Optional[Any] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
    ):
        super().__init__(logger, config)
        self.court_logger = court_logger
        
        # State validation history
        self._validation_history = []
        self._checkpoint_states = {}
        
        # Validation rules and patterns
        self._page_patterns = {
            PageState.LOGIN_PAGE: [
                "login", "authentication", "sign", "password", "username"
            ],
            PageState.COURT_HOME: [
                "ShowIndex.pl", "court home", "main menu", "welcome"
            ],
            PageState.QUERY_PAGE: [
                "iquery.pl", "query", "case search", "docket search"
            ],
            PageState.QUERY_RESULTS: [
                "query results", "search results", "case list", "docket list"
            ],
            PageState.DOCKET_SELECTION: [
                "select docket", "docket report", "case selection"
            ],
            PageState.DOCKET_OPTIONS: [
                "docket report options", "report configuration", "date range"
            ],
            PageState.DOCKET_SHEET: [
                "DktRpt.pl", "docket sheet", "case docket", "docket report"
            ],
            PageState.ERROR_PAGE: [
                "error", "not found", "access denied", "invalid", "unavailable"
            ]
        }

    async def _initialize_service(self) -> None:
        """Initialize the state validator."""
        if self._initialized:
            return
        
        self.log_info("StateValidator initialized successfully")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate validation methods."""
        action = data.get("action")
        
        if action == "validate_page_state":
            return await self.validate_page_state(
                navigator=data.get("navigator"),
                expected_state=data.get("expected_state"),
                timeout_ms=data.get("timeout_ms", 10000)
            )
        elif action == "validate_navigation_result":
            return await self.validate_navigation_result(
                navigator=data.get("navigator"),
                expected_patterns=data.get("expected_patterns", []),
                timeout_ms=data.get("timeout_ms", 10000)
            )
        elif action == "validate_workflow_step_completion":
            return await self.validate_workflow_step_completion(
                step_name=data.get("step_name"),
                navigator=data.get("navigator"),
                expected_results=data.get("expected_results", {})
            )
        elif action == "create_checkpoint":
            return await self.create_checkpoint(
                checkpoint_name=data.get("checkpoint_name"),
                navigator=data.get("navigator"),
                context=data.get("context", {})
            )
        elif action == "validate_checkpoint":
            return await self.validate_checkpoint(
                checkpoint_name=data.get("checkpoint_name"),
                navigator=data.get("navigator")
            )
        else:
            raise ValueError(f"Unknown action for StateValidator: {action}")

    async def validate_page_state(
        self,
        navigator: Any,
        expected_state: Union[PageState, str],
        timeout_ms: int = 10000
    ) -> Dict[str, Any]:
        """
        Validate that the current page matches the expected state.
        
        Args:
            navigator: PacerNavigator instance
            expected_state: Expected page state (PageState enum or string)
            timeout_ms: Timeout for validation in milliseconds
            
        Returns:
            Validation result with details
        """
        if isinstance(expected_state, str):
            try:
                expected_state = PageState(expected_state.lower())
            except ValueError:
                expected_state = PageState.UNKNOWN
        
        validation_start = datetime.now()
        
        try:
            # Wait for page to stabilize
            await navigator.page.wait_for_load_state('networkidle', timeout=timeout_ms)
            
            # Get current page information with error handling
            current_url = navigator.page.url
            
            # CRITICAL FIX: Handle execution context destroyed error
            page_title = "Unknown"
            try:
                if navigator.page and not navigator.page.is_closed():
                    page_title = await navigator.page.title()
                else:
                    page_title = "Context destroyed"
            except Exception as title_error:
                if "execution context was destroyed" in str(title_error).lower():
                    self.log_debug("Page title unavailable - execution context destroyed during validation")
                    page_title = "Context destroyed"
                else:
                    self.log_debug(f"Failed to get page title during validation: {title_error}")
                    page_title = "Title unavailable"
            
            page_content = await navigator.page.content()
            
            # Determine actual page state
            actual_state = await self._determine_page_state(current_url, page_title, page_content)
            
            # Check if states match
            is_valid = actual_state == expected_state
            validation_result = ValidationResult.VALID if is_valid else ValidationResult.INVALID
            
            # Create validation record
            validation_record = {
                'validation_type': 'page_state',
                'expected_state': expected_state.value,
                'actual_state': actual_state.value,
                'result': validation_result.value,
                'is_valid': is_valid,
                'current_url': current_url,
                'page_title': page_title,
                'validation_time': datetime.now().isoformat(),
                'validation_duration_ms': (datetime.now() - validation_start).total_seconds() * 1000
            }
            
            # Add to validation history
            self._validation_history.append(validation_record)
            
            # Log validation result
            if self.court_logger:
                if is_valid:
                    self.court_logger.info(f"STATE VALIDATION PASSED: Expected {expected_state.value}, Got {actual_state.value}")
                else:
                    self.court_logger.warning(f"STATE VALIDATION FAILED: Expected {expected_state.value}, Got {actual_state.value}")
                    self.court_logger.debug(f"Current URL: {current_url}")
                    self.court_logger.debug(f"Page Title: {page_title}")
            
            return validation_record
            
        except Exception as e:
            error_record = {
                'validation_type': 'page_state',
                'expected_state': expected_state.value,
                'actual_state': 'error',
                'result': ValidationResult.ERROR.value,
                'is_valid': False,
                'error': str(e),
                'validation_time': datetime.now().isoformat(),
                'validation_duration_ms': (datetime.now() - validation_start).total_seconds() * 1000
            }
            
            self._validation_history.append(error_record)
            
            if self.court_logger:
                self.court_logger.error(f"STATE VALIDATION ERROR: {e}", exc_info=True)
            
            return error_record

    async def validate_navigation_result(
        self,
        navigator: Any,
        expected_patterns: List[str],
        timeout_ms: int = 10000
    ) -> Dict[str, Any]:
        """
        Validate that navigation resulted in expected page patterns.
        
        Args:
            navigator: PacerNavigator instance
            expected_patterns: List of expected URL/content patterns
            timeout_ms: Timeout for validation in milliseconds
            
        Returns:
            Validation result with pattern matches
        """
        validation_start = datetime.now()
        
        try:
            # Wait for navigation to complete
            await navigator.page.wait_for_load_state('networkidle', timeout=timeout_ms)
            
            # Get current page information with error handling
            current_url = navigator.page.url
            page_content = await navigator.page.content()
            
            # CRITICAL FIX: Handle execution context destroyed error
            page_title = "Unknown"
            try:
                if navigator.page and not navigator.page.is_closed():
                    page_title = await navigator.page.title()
                else:
                    page_title = "Context destroyed"
            except Exception as title_error:
                if "execution context was destroyed" in str(title_error).lower():
                    self.log_debug("Page title unavailable - execution context destroyed during pattern validation")
                    page_title = "Context destroyed"
                else:
                    self.log_debug(f"Failed to get page title during pattern validation: {title_error}")
                    page_title = "Title unavailable"
            
            # Check pattern matches
            matched_patterns = []
            for pattern in expected_patterns:
                pattern_lower = pattern.lower()
                if (pattern_lower in current_url.lower() or
                    pattern_lower in page_content.lower() or
                    pattern_lower in page_title.lower()):
                    matched_patterns.append(pattern)
            
            # Determine validation result
            has_matches = len(matched_patterns) > 0
            all_matches = len(matched_patterns) == len(expected_patterns)
            
            if all_matches:
                validation_result = ValidationResult.VALID
            elif has_matches:
                validation_result = ValidationResult.WARNING  # Partial match
            else:
                validation_result = ValidationResult.INVALID
            
            # Create validation record
            validation_record = {
                'validation_type': 'navigation_result',
                'expected_patterns': expected_patterns,
                'matched_patterns': matched_patterns,
                'result': validation_result.value,
                'is_valid': has_matches,
                'all_patterns_matched': all_matches,
                'current_url': current_url,
                'page_title': page_title,
                'validation_time': datetime.now().isoformat(),
                'validation_duration_ms': (datetime.now() - validation_start).total_seconds() * 1000
            }
            
            # Add to validation history
            self._validation_history.append(validation_record)
            
            # Log validation result
            if self.court_logger:
                if all_matches:
                    self.court_logger.info(f"NAVIGATION VALIDATION PASSED: All {len(expected_patterns)} patterns matched")
                elif has_matches:
                    self.court_logger.warning(f"NAVIGATION VALIDATION PARTIAL: {len(matched_patterns)}/{len(expected_patterns)} patterns matched")
                    self.court_logger.debug(f"Matched: {matched_patterns}")
                    self.court_logger.debug(f"Missing: {set(expected_patterns) - set(matched_patterns)}")
                else:
                    self.court_logger.error(f"NAVIGATION VALIDATION FAILED: No patterns matched")
                    self.court_logger.debug(f"Expected: {expected_patterns}")
                    self.court_logger.debug(f"Current URL: {current_url}")
            
            return validation_record
            
        except Exception as e:
            error_record = {
                'validation_type': 'navigation_result',
                'expected_patterns': expected_patterns,
                'matched_patterns': [],
                'result': ValidationResult.ERROR.value,
                'is_valid': False,
                'error': str(e),
                'validation_time': datetime.now().isoformat(),
                'validation_duration_ms': (datetime.now() - validation_start).total_seconds() * 1000
            }
            
            self._validation_history.append(error_record)
            
            if self.court_logger:
                self.court_logger.error(f"NAVIGATION VALIDATION ERROR: {e}", exc_info=True)
            
            return error_record

    async def validate_workflow_step_completion(
        self,
        step_name: str,
        navigator: Any,
        expected_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate that a workflow step completed successfully.
        
        Args:
            step_name: Name of the workflow step
            navigator: PacerNavigator instance
            expected_results: Expected results/conditions after step completion
            
        Returns:
            Validation result for step completion
        """
        validation_start = datetime.now()
        
        try:
            # Get current state with error handling
            current_url = navigator.page.url
            
            # CRITICAL FIX: Handle execution context destroyed error
            page_title = "Unknown"
            try:
                if navigator.page and not navigator.page.is_closed():
                    page_title = await navigator.page.title()
                else:
                    page_title = "Context destroyed"
            except Exception as title_error:
                if "execution context was destroyed" in str(title_error).lower():
                    self.log_debug("Page title unavailable - execution context destroyed during step validation")
                    page_title = "Context destroyed"
                else:
                    self.log_debug(f"Failed to get page title during step validation: {title_error}")
                    page_title = "Title unavailable"
            
            # Validate expected conditions
            validation_checks = []
            
            # Check URL patterns if specified
            if 'url_patterns' in expected_results:
                for pattern in expected_results['url_patterns']:
                    matches = pattern.lower() in current_url.lower()
                    validation_checks.append({
                        'check_type': 'url_pattern',
                        'pattern': pattern,
                        'matches': matches
                    })
            
            # Check page title patterns if specified
            if 'title_patterns' in expected_results:
                for pattern in expected_results['title_patterns']:
                    matches = pattern.lower() in page_title.lower()
                    validation_checks.append({
                        'check_type': 'title_pattern',
                        'pattern': pattern,
                        'matches': matches
                    })
            
            # Check element presence if specified
            if 'required_elements' in expected_results:
                for selector in expected_results['required_elements']:
                    try:
                        element_count = await navigator.page.locator(selector).count()
                        matches = element_count > 0
                    except:
                        matches = False
                    
                    validation_checks.append({
                        'check_type': 'element_presence',
                        'selector': selector,
                        'matches': matches
                    })
            
            # Check element absence if specified
            if 'absent_elements' in expected_results:
                for selector in expected_results['absent_elements']:
                    try:
                        element_count = await navigator.page.locator(selector).count()
                        matches = element_count == 0
                    except:
                        matches = True
                    
                    validation_checks.append({
                        'check_type': 'element_absence',
                        'selector': selector,
                        'matches': matches
                    })
            
            # Determine overall validation result
            passed_checks = [check for check in validation_checks if check['matches']]
            failed_checks = [check for check in validation_checks if not check['matches']]
            
            total_checks = len(validation_checks)
            passed_count = len(passed_checks)
            
            if total_checks == 0:
                validation_result = ValidationResult.WARNING  # No checks defined
            elif passed_count == total_checks:
                validation_result = ValidationResult.VALID
            elif passed_count > 0:
                validation_result = ValidationResult.WARNING  # Partial success
            else:
                validation_result = ValidationResult.INVALID
            
            # Create validation record
            validation_record = {
                'validation_type': 'workflow_step_completion',
                'step_name': step_name,
                'result': validation_result.value,
                'is_valid': validation_result == ValidationResult.VALID,
                'total_checks': total_checks,
                'passed_checks': passed_count,
                'failed_checks': len(failed_checks),
                'validation_details': validation_checks,
                'current_url': current_url,
                'page_title': page_title,
                'validation_time': datetime.now().isoformat(),
                'validation_duration_ms': (datetime.now() - validation_start).total_seconds() * 1000
            }
            
            # Add to validation history
            self._validation_history.append(validation_record)
            
            # Log validation result
            if self.court_logger:
                if validation_result == ValidationResult.VALID:
                    self.court_logger.info(f"STEP VALIDATION PASSED: {step_name} - {passed_count}/{total_checks} checks passed")
                elif validation_result == ValidationResult.WARNING:
                    self.court_logger.warning(f"STEP VALIDATION PARTIAL: {step_name} - {passed_count}/{total_checks} checks passed")
                    for check in failed_checks:
                        self.court_logger.debug(f"Failed check: {check['check_type']} - {check.get('pattern', check.get('selector', 'N/A'))}")
                else:
                    self.court_logger.error(f"STEP VALIDATION FAILED: {step_name} - {passed_count}/{total_checks} checks passed")
                    for check in failed_checks:
                        self.court_logger.debug(f"Failed check: {check['check_type']} - {check.get('pattern', check.get('selector', 'N/A'))}")
            
            return validation_record
            
        except Exception as e:
            error_record = {
                'validation_type': 'workflow_step_completion',
                'step_name': step_name,
                'result': ValidationResult.ERROR.value,
                'is_valid': False,
                'error': str(e),
                'validation_time': datetime.now().isoformat(),
                'validation_duration_ms': (datetime.now() - validation_start).total_seconds() * 1000
            }
            
            self._validation_history.append(error_record)
            
            if self.court_logger:
                self.court_logger.error(f"STEP VALIDATION ERROR for {step_name}: {e}", exc_info=True)
            
            return error_record

    async def create_checkpoint(
        self,
        checkpoint_name: str,
        navigator: Any,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Create a state checkpoint for recovery purposes.
        
        Args:
            checkpoint_name: Unique name for the checkpoint
            navigator: PacerNavigator instance
            context: Additional context to store with checkpoint
            
        Returns:
            Checkpoint creation result
        """
        try:
            # Capture current state with error handling
            current_url = navigator.page.url
            
            # CRITICAL FIX: Handle execution context destroyed error
            page_title = "Unknown"
            try:
                if navigator.page and not navigator.page.is_closed():
                    page_title = await navigator.page.title()
                else:
                    page_title = "Context destroyed"
            except Exception as title_error:
                if "execution context was destroyed" in str(title_error).lower():
                    self.log_debug("Page title unavailable - execution context destroyed during checkpoint creation")
                    page_title = "Context destroyed"
                else:
                    self.log_debug(f"Failed to get page title during checkpoint creation: {title_error}")
                    page_title = "Title unavailable"
            
            # Create checkpoint record
            checkpoint = {
                'checkpoint_name': checkpoint_name,
                'creation_time': datetime.now().isoformat(),
                'current_url': current_url,
                'page_title': page_title,
                'page_state': await self._determine_page_state(current_url, page_title, ""),
                'context': context or {}
            }
            
            # Store checkpoint
            self._checkpoint_states[checkpoint_name] = checkpoint
            
            if self.court_logger:
                self.court_logger.info(f"CHECKPOINT CREATED: {checkpoint_name} at {current_url}")
            
            return {
                'status': 'created',
                'checkpoint_name': checkpoint_name,
                'creation_time': checkpoint['creation_time']
            }
            
        except Exception as e:
            if self.court_logger:
                self.court_logger.error(f"CHECKPOINT CREATION ERROR for {checkpoint_name}: {e}", exc_info=True)
            
            return {
                'status': 'error',
                'checkpoint_name': checkpoint_name,
                'error': str(e)
            }

    async def validate_checkpoint(
        self,
        checkpoint_name: str,
        navigator: Any
    ) -> Dict[str, Any]:
        """
        Validate current state against a previously created checkpoint.
        
        Args:
            checkpoint_name: Name of checkpoint to validate against
            navigator: PacerNavigator instance
            
        Returns:
            Validation result comparing current state to checkpoint
        """
        if checkpoint_name not in self._checkpoint_states:
            return {
                'validation_type': 'checkpoint_comparison',
                'checkpoint_name': checkpoint_name,
                'result': ValidationResult.ERROR.value,
                'is_valid': False,
                'error': f'Checkpoint {checkpoint_name} not found'
            }
        
        try:
            checkpoint = self._checkpoint_states[checkpoint_name]
            
            # Get current state with error handling
            current_url = navigator.page.url
            
            # CRITICAL FIX: Handle execution context destroyed error
            page_title = "Unknown"
            try:
                if navigator.page and not navigator.page.is_closed():
                    page_title = await navigator.page.title()
                else:
                    page_title = "Context destroyed"
            except Exception as title_error:
                if "execution context was destroyed" in str(title_error).lower():
                    self.log_debug("Page title unavailable - execution context destroyed during checkpoint validation")
                    page_title = "Context destroyed"
                else:
                    self.log_debug(f"Failed to get page title during checkpoint validation: {title_error}")
                    page_title = "Title unavailable"
            
            current_state = await self._determine_page_state(current_url, page_title, "")
            
            # Compare with checkpoint
            url_matches = current_url == checkpoint['current_url']
            title_matches = page_title == checkpoint['page_title']
            state_matches = current_state == PageState(checkpoint['page_state'])
            
            # Determine validation result
            if url_matches and title_matches and state_matches:
                validation_result = ValidationResult.VALID
            elif state_matches:
                validation_result = ValidationResult.WARNING  # Same state, different details
            else:
                validation_result = ValidationResult.INVALID
            
            validation_record = {
                'validation_type': 'checkpoint_comparison',
                'checkpoint_name': checkpoint_name,
                'result': validation_result.value,
                'is_valid': validation_result == ValidationResult.VALID,
                'comparisons': {
                    'url_matches': url_matches,
                    'title_matches': title_matches,
                    'state_matches': state_matches
                },
                'current_state': {
                    'url': current_url,
                    'title': page_title,
                    'state': current_state.value
                },
                'checkpoint_state': {
                    'url': checkpoint['current_url'],
                    'title': checkpoint['page_title'],
                    'state': checkpoint['page_state']
                },
                'validation_time': datetime.now().isoformat()
            }
            
            self._validation_history.append(validation_record)
            
            if self.court_logger:
                if validation_result == ValidationResult.VALID:
                    self.court_logger.info(f"CHECKPOINT VALIDATION PASSED: {checkpoint_name}")
                else:
                    self.court_logger.warning(f"CHECKPOINT VALIDATION FAILED: {checkpoint_name}")
                    if not url_matches:
                        self.court_logger.debug(f"URL changed: {checkpoint['current_url']} -> {current_url}")
                    if not title_matches:
                        self.court_logger.debug(f"Title changed: {checkpoint['page_title']} -> {page_title}")
                    if not state_matches:
                        self.court_logger.debug(f"State changed: {checkpoint['page_state']} -> {current_state.value}")
            
            return validation_record
            
        except Exception as e:
            error_record = {
                'validation_type': 'checkpoint_comparison',
                'checkpoint_name': checkpoint_name,
                'result': ValidationResult.ERROR.value,
                'is_valid': False,
                'error': str(e),
                'validation_time': datetime.now().isoformat()
            }
            
            self._validation_history.append(error_record)
            
            if self.court_logger:
                self.court_logger.error(f"CHECKPOINT VALIDATION ERROR for {checkpoint_name}: {e}", exc_info=True)
            
            return error_record

    async def _determine_page_state(
        self,
        url: str,
        title: str,
        content: str
    ) -> PageState:
        """
        Determine the current page state based on URL, title, and content.
        
        Args:
            url: Current page URL
            title: Current page title
            content: Current page content
            
        Returns:
            Detected page state
        """
        url_lower = url.lower()
        title_lower = title.lower()
        content_lower = content.lower()
        
        # Check each page state pattern
        for state, patterns in self._page_patterns.items():
            for pattern in patterns:
                pattern_lower = pattern.lower()
                if (pattern_lower in url_lower or
                    pattern_lower in title_lower or
                    pattern_lower in content_lower):
                    return state
        
        return PageState.UNKNOWN

    def get_validation_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get validation history records.
        
        Args:
            limit: Maximum number of records to return (None for all)
            
        Returns:
            List of validation records
        """
        if limit is None:
            return self._validation_history.copy()
        else:
            return self._validation_history[-limit:].copy()

    def get_checkpoint_states(self) -> Dict[str, Dict[str, Any]]:
        """Get all stored checkpoint states."""
        return self._checkpoint_states.copy()

    def clear_validation_history(self) -> int:
        """
        Clear validation history.
        
        Returns:
            Number of records cleared
        """
        count = len(self._validation_history)
        self._validation_history.clear()
        return count

    def clear_checkpoints(self) -> int:
        """
        Clear all checkpoint states.
        
        Returns:
            Number of checkpoints cleared
        """
        count = len(self._checkpoint_states)
        self._checkpoint_states.clear()
        return count

    def get_validation_statistics(self) -> Dict[str, Any]:
        """Get comprehensive validation statistics."""
        if not self._validation_history:
            return {
                'total_validations': 0,
                'success_rate': 0,
                'validation_types': {},
                'recent_validations': []
            }
        
        # Count by result type
        result_counts = {}
        type_counts = {}
        
        for record in self._validation_history:
            result = record.get('result', 'unknown')
            validation_type = record.get('validation_type', 'unknown')
            
            result_counts[result] = result_counts.get(result, 0) + 1
            type_counts[validation_type] = type_counts.get(validation_type, 0) + 1
        
        # Calculate success rate
        valid_count = result_counts.get('valid', 0)
        total_count = len(self._validation_history)
        success_rate = (valid_count / total_count * 100) if total_count > 0 else 0
        
        return {
            'total_validations': total_count,
            'success_rate': success_rate,
            'result_breakdown': result_counts,
            'validation_types': type_counts,
            'checkpoints_created': len(self._checkpoint_states),
            'recent_validations': self._validation_history[-10:] if len(self._validation_history) > 10 else self._validation_history
        }