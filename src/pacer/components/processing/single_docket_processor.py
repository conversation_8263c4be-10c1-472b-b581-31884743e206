# /src/services/pacer/_processing_components/single_docket_processor.py
import logging
from datetime import date as DateType
from src.infrastructure.utils.date_formatter import format_iso_date
from typing import Any, Dict, Optional

from playwright.async_api import Page

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.pacer.jobs.docket_job import DocketProcessingJob
from src.pacer.services.relevance_service import RelevanceService

# Import the new unified processor
from src.pacer.components.processing.unified_docket_processor import UnifiedDocketProcessor


class SingleDocketProcessor(ComponentImplementation):
    """
    Component for processing a single, specific docket page. This is used
    for workflows that start directly from a case page, not a report.
    """

    def __init__(
        self,
        logger: Any = None,
        config: Dict[str, Any] = None,
        pacer_repository: Any = None,
        file_manager: Any = None,
        case_processing_service: Any = None,
        download_orchestration_service: Any = None,
        file_operations_service: Any = None,
        case_classification_service: Any = None,
        # New unified processor dependencies
        unified_processor: UnifiedDocketProcessor = None,
    ):
        super().__init__(logger, config)
        self.pacer_repository = pacer_repository
        self.file_manager = file_manager
        self.case_processing_service = case_processing_service
        self.download_orchestration_service = download_orchestration_service
        self.file_operations_service = file_operations_service
        self.case_classification_service = case_classification_service
        
        # Use unified processor if available, otherwise fall back to legacy implementation
        self.unified_processor = unified_processor
        
        # Migration flag to control behavior
        self._use_unified_workflow = unified_processor is not None

    async def execute(self, data: Any) -> Any:
        """Main execution method for the component."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a single docket processing action.

        Args:
            data (Dict): A dictionary containing action parameters.
                For process_docket:
                - case_page (Page)
                - initial_details (Dict)
                - court_id (str)
                - court_logger (logging.Logger)
                - processor_config (Optional[Dict])
                
                For process_single_case_row:
                - row_element (Playwright Element)
                - court_id (str)
                - iso_date (str)
                - case_index (int)
                - processor_config (Optional[Dict])
                - context (BrowserContext)
                - court_logger (logging.Logger)

        Returns:
            Optional[Dict[str, Any]]: The processed case details or None on failure.
        """
        action = data.get("action")
        if action == "process_docket":
            return await self.process_single_docket(**data)
        elif action == "process_single_case_row":
            return await self.process_single_case_row(**data)
        else:
            raise ValueError(f"Unknown action for SingleDocketProcessor: {action}")

    async def process_single_docket(self, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Processes a single docket page to extract and download case details.
        
        This method now delegates to the UnifiedDocketProcessor when available
        to ensure consistent workflow execution.
        """
        case_page = kwargs.get("case_page")
        initial_details = kwargs.get("initial_details")
        court_id = kwargs.get("court_id")
        court_logger = kwargs.get("court_logger")
        processor_config = kwargs.get("processor_config")

        log_prefix = f"[{court_id}] SingleDocketProc:"
        court_logger.info(f"{log_prefix} Starting single docket processing for URL: {case_page.url}")
        
        # Use unified workflow if available
        if self._use_unified_workflow and self.unified_processor:
            court_logger.info(f"{log_prefix} Delegating to UnifiedDocketProcessor")
            self.log_info(f"{log_prefix} Delegating to UnifiedDocketProcessor")
            
            # Extract docket number from initial details or page
            docket_number = initial_details.get("docket_num") if initial_details else None
            if not docket_number:
                # Try to extract from URL or page content
                docket_number = await self._extract_docket_from_page(case_page, court_logger)
            
            if not docket_number:
                court_logger.error(f"{log_prefix} Could not determine docket number for unified processing")
                # Fall back to legacy implementation
            else:
                # Get browser context from the page
                context = case_page.context
                iso_date = processor_config.get("iso_date") if processor_config else None
                
                if not iso_date:
                    iso_date = self.config.get("iso_date")
                
                if not iso_date:
                    court_logger.error(f"{log_prefix} iso_date not available for unified processing")
                    # Fall back to legacy implementation
                else:
                    try:
                        # Use unified processor
                        return await self.unified_processor.process_docket(
                            docket_number=docket_number,
                            court_id=court_id,
                            context=context,
                            iso_date=iso_date,
                            initial_details=initial_details,
                            processor_config=processor_config,
                            court_logger=court_logger
                        )
                    except Exception as e:
                        court_logger.error(f"{log_prefix} Unified processor failed, falling back to legacy: {e}")
                        # Fall back to legacy implementation below
        
        # Legacy implementation (existing code)

        job = await self._create_job_for_single_docket(
            case_page, initial_details, court_id, court_logger, processor_config
        )

        try:
            # Phase 1: Parse - Add null check before accessing page.content()
            if job.page is None:
                court_logger.error(f"{log_prefix} Job page object is None - cannot retrieve HTML content")
                return None
            
            try:
                job.html_content = await job.page.content()
                
                if not job.html_content:
                    court_logger.error(f"{log_prefix} Retrieved empty HTML content from job page")
                    return None
            except AttributeError as e:
                court_logger.error(f"{log_prefix} AttributeError accessing job.page.content() - page may be None: {e}")
                return None
            except Exception as e:
                court_logger.error(f"{log_prefix} Unexpected error retrieving HTML content: {e}")
                return None
            
            current_details = {"court_id": court_id, "filing_date": job.initial_filing_date, **initial_details}
            job.case_details = self.case_processing_service.update_case_details(job.html_content, current_details)

            # Phase 1.5: Classify
            job.case_details = self.case_classification_service.classify_case_from_html(
                job.case_details, job.html_content
            )
            job.base_filename = self.case_processing_service.create_base_filename(job.case_details)
            job.case_details["base_filename"] = job.base_filename
            court_logger.info(f"{log_prefix} Generated base_filename: {job.base_filename}")

            # Phase 2: Save HTML
            # Use iso_date from config - it should already be provided
            iso_date = job.config.get("iso_date")
            if not iso_date:
                self.log_error("iso_date not provided in job config")
                raise ValueError("iso_date must be provided in job configuration")
            
            # Ensure it's in the correct format (YYYYMMDD)
            iso_date = format_iso_date(iso_date)
            
            await self.download_orchestration_service.upload_html_to_s3(
                job.base_filename, job.html_content, iso_date
            )
            await self.file_operations_service.save_case_data_to_json(
                job.case_details, court_id, iso_date
            )

            # Phase 3: Relevance & Download
            if job.case_details.get("_html_indicates_removal") or job.case_details.get("is_removal"):
                court_logger.info(f"{log_prefix} Removal case detected, saving to review list and skipping download.")
                if self.file_manager:
                    await self.file_manager.save_review_case_details(job.case_details)
                return job.case_details

            is_explicitly_requested = job.config.get("_processing_explicit_dockets", False)
            if not is_explicitly_requested and job.relevance_engine._determine_case_review_status(job.case_details):
                court_logger.info(f"{log_prefix} Case irrelevant, saving to review list.")
                if self.file_manager:
                    await self.file_manager.save_review_case_details(job.case_details)
                return job.case_details

            job.case_details = await self.download_orchestration_service.execute_download_workflow(
                case_details=job.case_details,
                is_explicitly_requested=is_explicitly_requested,
                page=job.page,
            )

            court_logger.info(f"{log_prefix} Single docket processing completed.")
            return job.case_details

        except Exception as e:
            court_logger.error(f"{log_prefix} Single docket processing failed: {e}", exc_info=True)
            return None

    async def process_single_case_row(self, **kwargs) -> Dict[str, Any]:
        """
        Processes a single case row from a PACER report table to extract case details
        and navigate to the individual case page for processing.
        
        This method now delegates to the UnifiedDocketProcessor when available
        to ensure consistent workflow execution.
        
        Args:
            row_element: The table row element containing case information
            court_id: Court identifier
            iso_date: ISO formatted date
            case_index: Index of the case in the report
            processor_config: Optional processing configuration
            context: Browser context for navigation
            court_logger: Court-specific logger
            
        Returns:
            Dict with processing status and results
        """
        row_element = kwargs.get("row_element")
        court_id = kwargs.get("court_id")
        iso_date = kwargs.get("iso_date")
        case_index = kwargs.get("case_index", 0)
        processor_config = kwargs.get("processor_config", {})
        context = kwargs.get("context")
        court_logger = kwargs.get("court_logger", self.logger)
        
        log_prefix = f"[{court_id}] SingleDocketProc-Row[{case_index}]:"
        court_logger.info(f"{log_prefix} Processing case row.")
        
        # Use unified workflow if available
        if self._use_unified_workflow and self.unified_processor:
            court_logger.info(f"{log_prefix} Delegating to UnifiedDocketProcessor")
            self.log_info(f"{log_prefix} Delegating to UnifiedDocketProcessor")
            try:
                return await self.unified_processor.process_case_row(
                    row_element=row_element,
                    court_id=court_id,
                    context=context,
                    iso_date=iso_date,
                    case_index=case_index,
                    processor_config=processor_config,
                    court_logger=court_logger
                )
            except Exception as e:
                court_logger.error(f"{log_prefix} Unified processor failed, falling back to legacy: {e}")
                # Fall back to legacy implementation below
        
        # Legacy implementation
        try:
            # Extract case details from the table row
            case_details = await self._extract_case_details_from_row(
                row_element, court_id, court_logger
            )
            
            if not case_details:
                court_logger.warning(f"{log_prefix} Failed to extract case details from row.")
                return {
                    "status": "failed",
                    "error": "Failed to extract case details from row",
                    "dockets_processed": 0
                }
            
            court_logger.info(f"{log_prefix} Extracted case: {case_details.get('docket_num', 'Unknown')} - {case_details.get('versus', 'Unknown')}")
            
            # Find the docket link in the row
            docket_link = await self._find_docket_link_in_row(row_element, court_logger)
            
            if not docket_link:
                court_logger.warning(f"{log_prefix} No docket link found in row.")
                return {
                    "status": "failed", 
                    "error": "No docket link found in row",
                    "dockets_processed": 0
                }
            
            # Navigate to the case page
            case_page = await context.new_page()
            try:
                await case_page.goto(docket_link, wait_until="networkidle", timeout=30000)
                court_logger.info(f"{log_prefix} Navigated to case page: {docket_link}")
                
                # Process the individual docket page
                docket_result = await self.process_single_docket(
                    case_page=case_page,
                    initial_details=case_details,
                    court_id=court_id,
                    court_logger=court_logger,
                    processor_config=processor_config
                )
                
                if docket_result:
                    court_logger.info(f"{log_prefix} Case processing completed successfully.")
                    return {
                        "status": "success",
                        "case_details": docket_result,
                        "dockets_processed": 1
                    }
                else:
                    court_logger.warning(f"{log_prefix} Case processing failed.")
                    return {
                        "status": "failed",
                        "error": "Case processing failed",
                        "dockets_processed": 0
                    }
                    
            finally:
                await case_page.close()
                
        except Exception as e:
            court_logger.error(f"{log_prefix} Row processing failed: {e}", exc_info=True)
            return {
                "status": "failed",
                "error": str(e),
                "dockets_processed": 0
            }

    async def _extract_case_details_from_row(self, row_element, court_id: str, logger) -> Optional[Dict[str, Any]]:
        """
        Extracts case details from a table row in a PACER civil cases report.
        
        Typical PACER report row structure:
        - Column 0: Case number (with link)
        - Column 1: Versus/Title
        - Column 2: Filing date
        - Column 3: Judge (optional)
        - Column 4: Status/Nature (optional)
        """
        try:
            # Get all cells in the row
            cells = row_element.locator("td")
            cell_count = await cells.count()
            
            if cell_count < 3:
                logger.warning(f"Row has insufficient cells ({cell_count}), skipping.")
                return None
            
            case_details = {"court_id": court_id}
            
            # Extract case number from first cell
            case_num_cell = cells.nth(0)
            case_num_text = await case_num_cell.text_content()
            if case_num_text and case_num_text.strip():
                case_details["docket_num"] = case_num_text.strip()
            
            # Extract versus text from second cell
            if cell_count > 1:
                versus_cell = cells.nth(1)
                versus_text = await versus_cell.text_content()
                if versus_text and versus_text.strip():
                    case_details["versus"] = versus_text.strip()
            
            # Extract filing date from third cell
            if cell_count > 2:
                date_cell = cells.nth(2)
                date_text = await date_cell.text_content()
                if date_text and date_text.strip():
                    case_details["filing_date"] = date_text.strip()
            
            # Extract judge from fourth cell if available
            if cell_count > 3:
                judge_cell = cells.nth(3)
                judge_text = await judge_cell.text_content()
                if judge_text and judge_text.strip():
                    case_details["judge"] = judge_text.strip()
            
            # Extract nature/status from fifth cell if available
            if cell_count > 4:
                nature_cell = cells.nth(4)
                nature_text = await nature_cell.text_content()
                if nature_text and nature_text.strip():
                    case_details["nature_of_suit"] = nature_text.strip()
            
            logger.debug(f"Extracted case details: {case_details}")
            return case_details
            
        except Exception as e:
            logger.error(f"Failed to extract case details from row: {e}", exc_info=True)
            return None

    async def _find_docket_link_in_row(self, row_element, logger) -> Optional[str]:
        """
        Finds the docket link URL in a table row.
        Usually the case number in the first cell is a clickable link.
        """
        try:
            # Look for links in the first cell (case number)
            first_cell = row_element.locator("td").nth(0)
            link = first_cell.locator("a").first
            
            if await link.count() > 0:
                href = await link.get_attribute("href")
                if href:
                    logger.debug(f"Found docket link: {href}")
                    return href
            
            # If no link in first cell, check other cells
            all_links = row_element.locator("a")
            link_count = await all_links.count()
            
            if link_count > 0:
                for i in range(link_count):
                    link = all_links.nth(i)
                    href = await link.get_attribute("href")
                    link_text = await link.text_content()
                    
                    # Look for links that seem to be case/docket related
                    if href and (
                        "doc1" in href.lower() or 
                        "docket" in href.lower() or
                        (link_text and any(char.isdigit() for char in link_text))
                    ):
                        logger.debug(f"Found potential docket link: {href}")
                        return href
            
            logger.warning("No docket link found in row")
            return None
            
        except Exception as e:
            logger.error(f"Error finding docket link in row: {e}", exc_info=True)
            return None

    async def _create_job_for_single_docket(
        self, case_page, initial_details, court_id, court_logger, processor_config
    ):

        relevance_config = self.config.get("relevance_config", {})
        relevance_engine = RelevanceService(
            config=self.config,
            relevance_config_dict=relevance_config,
            relevant_defendants_list=[d.lower() for d in relevance_config.get("relevant_defendants", [])],
            usa_defendant_regex_str="|".join(relevance_config.get("usa_defendant_patterns", [])),
            court_id=court_id,
            logger=court_logger,
        )

        job_config = self.config.copy()
        
        # iso_date should be provided in the config - don't generate it
        if "iso_date" not in job_config:
            if processor_config and "iso_date" in processor_config:
                job_config["iso_date"] = processor_config["iso_date"]
            else:
                self.log_error("iso_date not provided in configuration")
                raise ValueError("iso_date must be provided in configuration - use the date from config, not today's date")
        
        # Ensure iso_date is in correct format (YYYYMMDD)
        job_config["iso_date"] = format_iso_date(job_config["iso_date"])
        
        if processor_config:
            job_config.update(processor_config)

        # ARCHITECTURAL FIX: Process docket directly instead of creating separate job
        # This ensures proper sequential processing within court context
        try:
            from src.pacer.jobs.job_processor import JobProcessor
            
            # Create job processor for this docket processing
            job_processor = JobProcessor(
                docket_orchestrator=self.docket_orchestrator,
                logger=court_logger,
                config=job_config
            )
            
            # Create temporary job data for processing
            job_data = {
                "court_id": court_id,
                "docket_num": initial_details.get("docket_num"),
                "docket_link_href": case_page.url,
                "versus": initial_details.get("versus", ""),
                "filing_date": initial_details.get("filing_date", ""),
                "page": case_page,
                "initial_details": initial_details,
                "row_num": 1,
                "total_rows": 1,
                "_processing_source": "single_docket_processor",
                "config": job_config,
                "processor_config": processor_config or {}
            }
            
            # Process the docket directly using the orchestrator
            if self.docket_orchestrator:
                result = await self.docket_orchestrator.execute({
                    "action": "process_docket_page",
                    "page": case_page,
                    "initial_details": initial_details
                })
                return result
            else:
                court_logger.error("DocketOrchestrator not available for single docket processing")
                return None
                
        except Exception as e:
            court_logger.error(f"Error in single docket processing: {e}", exc_info=True)
            return None
    
    async def _extract_docket_from_page(self, page: Page, logger: logging.Logger) -> Optional[str]:
        """Extract docket number from page URL or content."""
        try:
            # Try to extract from URL parameters
            url = page.url
            if "case_num=" in url:
                import re
                match = re.search(r"case_num=([^&]+)", url)
                if match:
                    docket_num = match.group(1).replace("%3A", ":")
                    logger.debug(f"Extracted docket number from URL: {docket_num}")
                    return docket_num
            
            # Try to extract from page content
            try:
                # Look for docket number in common locations
                selectors = [
                    "td:contains('Case Number')",
                    "b:contains('Case')",
                    ".case-number"
                ]
                
                for selector in selectors:
                    try:
                        element = page.locator(selector)
                        if await element.count() > 0:
                            text = await element.first.text_content()
                            if text and ":" in text:
                                # Extract pattern like "1:23-cv-12345"
                                import re
                                match = re.search(r"(\d+:\d+-[a-zA-Z]+-\d+)", text)
                                if match:
                                    docket_num = match.group(1)
                                    logger.debug(f"Extracted docket number from content: {docket_num}")
                                    return docket_num
                    except:
                        continue
                        
            except Exception as e:
                logger.debug(f"Could not extract docket from page content: {e}")
            
            logger.warning("Could not extract docket number from page")
            return None
            
        except Exception as e:
            logger.error(f"Failed to extract docket from page: {e}")
            return None
