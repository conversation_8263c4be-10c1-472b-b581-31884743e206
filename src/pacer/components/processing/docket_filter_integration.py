"""
Docket Filter Integration - Helper module for integrating artifact filtering

This module provides utility functions and integration helpers for adding
docket artifact filtering to existing workflow components.
"""

from typing import Dict, Any, List, Optional, TYPE_CHECKING
from datetime import datetime

if TYPE_CHECKING:
    from src.pacer.components.processing.docket_filter_service import DocketFilterService
    from src.infrastructure.protocols.logger import LoggerProtocol


class DocketFilterIntegrator:
    """
    Helper class for integrating docket filtering into existing workflows.
    
    This class provides standardized methods for applying artifact filtering
    across different components while maintaining consistent logging and
    error handling.
    """
    
    def __init__(
        self,
        logger: 'LoggerProtocol',
        filter_service: Optional['DocketFilterService'] = None
    ):
        """
        Initialize the filter integrator.
        
        Args:
            logger: Logger instance for logging
            filter_service: Optional docket filter service
        """
        self.logger = logger
        self.filter_service = filter_service
    
    async def apply_filtering_to_docket_items(
        self,
        docket_items: List[Dict[str, Any]],
        context_name: str,
        iso_date: Optional[str] = None,
        court_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Apply artifact filtering to a list of docket items with standardized logging.
        
        Args:
            docket_items: List of docket items to filter
            context_name: Name of the calling context for logging
            iso_date: ISO date for artifact checking
            court_id: Optional court ID for enhanced logging
            
        Returns:
            Dictionary containing filtering results
        """
        if not docket_items:
            return {
                'original_items': docket_items,
                'filtered_items': [],
                'items_to_process': [],
                'filtering_applied': False,
                'filter_summary': 'No items to filter'
            }
        
        context_prefix = f"[{court_id}] {context_name}" if court_id else context_name
        
        if not self.filter_service:
            self.logger.info(
                f"{context_prefix}: No docket filter service available - "
                f"processing all {len(docket_items)} items"
            )
            return {
                'original_items': docket_items,
                'filtered_items': [],
                'items_to_process': docket_items,
                'filtering_applied': False,
                'filter_summary': f'No filtering service - processing all {len(docket_items)} items'
            }
        
        try:
            self.logger.info(
                f"{context_prefix}: Applying artifact filtering to {len(docket_items)} docket items"
            )
            
            start_time = datetime.now()
            
            # Apply filtering
            filter_results = await self.filter_service.filter_docket_report_log(
                docket_items, iso_date
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            filtered_items = filter_results.get('filtered_items', [])
            items_to_process = filter_results.get('items_to_process', [])
            
            # Log filtering summary
            total_items = filter_results.get('total_items', len(docket_items))
            items_filtered = len(filtered_items)
            items_to_process_count = len(items_to_process)
            
            self.logger.info(
                f"{context_prefix}: Artifact filtering complete in {processing_time:.2f}s - "
                f"{total_items} total -> {items_to_process_count} to process, {items_filtered} filtered out"
            )
            
            # Log detailed statistics if available
            stats = filter_results.get('skip_statistics', {})
            if any(stats.values()):
                self.logger.info(
                    f"{context_prefix}: Filter breakdown - html_only={stats.get('html_only', 0)}, "
                    f"transferred={stats.get('transferred_with_existing', 0)}, "
                    f"has_artifacts={stats.get('has_artifacts', 0)}, "
                    f"errors={stats.get('errors', 0)}"
                )
            
            # Generate summary
            filter_summary = (
                f"Filtered {items_filtered}/{total_items} items in {processing_time:.2f}s. "
                f"Reasons: html_only={stats.get('html_only', 0)}, "
                f"transferred={stats.get('transferred_with_existing', 0)}, "
                f"has_artifacts={stats.get('has_artifacts', 0)}"
            )
            
            return {
                'original_items': docket_items,
                'filtered_items': filtered_items,
                'items_to_process': items_to_process,
                'filtering_applied': True,
                'filter_summary': filter_summary,
                'filter_statistics': stats,
                'processing_time': processing_time
            }
            
        except Exception as filter_error:
            self.logger.error(
                f"{context_prefix}: Error applying artifact filtering: {filter_error}",
                exc_info=True
            )
            
            # Fall back to unfiltered items if filtering fails
            self.logger.warning(f"{context_prefix}: Falling back to unfiltered docket items")
            
            return {
                'original_items': docket_items,
                'filtered_items': [],
                'items_to_process': docket_items,
                'filtering_applied': False,
                'filter_summary': f'Filtering failed: {str(filter_error)} - processing all items',
                'error': str(filter_error)
            }
    
    def log_filtering_metrics(
        self,
        filter_results: Dict[str, Any],
        context_name: str,
        court_id: Optional[str] = None
    ) -> None:
        """
        Log standardized filtering metrics.
        
        Args:
            filter_results: Results from apply_filtering_to_docket_items
            context_name: Name of the calling context
            court_id: Optional court ID for enhanced logging
        """
        context_prefix = f"[{court_id}] {context_name}" if court_id else context_name
        
        if not filter_results.get('filtering_applied', False):
            self.logger.info(f"{context_prefix}: No filtering applied - {filter_results.get('filter_summary', '')}")
            return
        
        original_count = len(filter_results.get('original_items', []))
        filtered_count = len(filter_results.get('filtered_items', []))
        to_process_count = len(filter_results.get('items_to_process', []))
        processing_time = filter_results.get('processing_time', 0.0)
        
        # Calculate efficiency metrics
        if original_count > 0:
            filter_efficiency = (filtered_count / original_count) * 100
            self.logger.info(
                f"{context_prefix}: Filtering efficiency: {filter_efficiency:.1f}% "
                f"({filtered_count}/{original_count} items filtered in {processing_time:.2f}s)"
            )
        
        # Log detailed statistics if available
        stats = filter_results.get('filter_statistics', {})
        if stats and any(stats.values()):
            total_filtered = sum(stats.values())
            breakdown = []
            for reason, count in stats.items():
                if count > 0:
                    percentage = (count / total_filtered * 100) if total_filtered > 0 else 0
                    breakdown.append(f"{reason}={count}({percentage:.1f}%)")
            
            self.logger.info(f"{context_prefix}: Filter reason breakdown: {', '.join(breakdown)}")


def create_filter_integrator(
    logger: 'LoggerProtocol',
    config: Dict[str, Any],
    pacer_repository = None
) -> DocketFilterIntegrator:
    """
    Factory function to create a DocketFilterIntegrator with appropriate services.
    
    Args:
        logger: Logger instance
        config: Configuration dictionary
        pacer_repository: Optional PACER repository for DynamoDB checking
        
    Returns:
        Configured DocketFilterIntegrator instance
    """
    filter_service = None
    
    # Create filter service if repository is available
    if pacer_repository:
        try:
            from src.pacer.components.processing.docket_filter_service import DocketFilterService
            
            filter_service = DocketFilterService(
                logger=logger,
                config=config,
                pacer_repository=pacer_repository,
                batch_size=config.get('filter_batch_size', 50)
            )
            
            logger.info("Created DocketFilterService with repository integration")
            
        except Exception as e:
            logger.warning(f"Could not create DocketFilterService: {e}")
    
    return DocketFilterIntegrator(logger, filter_service)


def apply_docket_filtering_if_available(
    docket_items: List[Dict[str, Any]],
    filter_integrator: Optional[DocketFilterIntegrator],
    context_name: str,
    iso_date: Optional[str] = None,
    court_id: Optional[str] = None,
    logger = None
) -> List[Dict[str, Any]]:
    """
    Convenience function to apply docket filtering if integrator is available.
    
    This is a synchronous wrapper that can be used in contexts where async is not available.
    Note: This function will not actually apply filtering since it's synchronous.
    Use apply_filtering_to_docket_items directly for async filtering.
    
    Args:
        docket_items: List of docket items
        filter_integrator: Optional filter integrator
        context_name: Context name for logging
        iso_date: ISO date for filtering
        court_id: Court ID for logging
        logger: Optional logger for fallback logging
        
    Returns:
        Original docket items (filtering must be done asynchronously)
    """
    if logger:
        context_prefix = f"[{court_id}] {context_name}" if court_id else context_name
        
        if filter_integrator and filter_integrator.filter_service:
            logger.warning(
                f"{context_prefix}: Docket filtering available but requires async execution - "
                f"processing all {len(docket_items)} items without filtering"
            )
        else:
            logger.info(
                f"{context_prefix}: No docket filtering available - "
                f"processing all {len(docket_items)} items"
            )
    
    return docket_items