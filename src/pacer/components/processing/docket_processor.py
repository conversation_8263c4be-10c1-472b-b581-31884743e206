"""
Docket Processing Component for PACER Workflow.

This component handles docket-level operations including HTML parsing,
case extraction, and structured data processing.
"""

from typing import Any, Dict, Optional, List
from playwright.async_api import Page

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.services.case_processing_service import CaseProcessingService
from src.pacer.services.relevance_service import RelevanceService


class DocketProcessor(AsyncServiceBase):
    """
    Handles docket-level operations: parsing, case extraction, HTML processing.
    
    Responsibilities:
    - Parse docket HTML content and extract structured data
    - Extract individual case details from parsed dockets
    - Process docket reports and metadata
    - Apply initial data transformation and cleaning
    """

    def __init__(
        self,
        case_processing_service: Optional[CaseProcessingService] = None,
        relevance_service: Optional[RelevanceService] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
    ):
        super().__init__(logger, config)
        self.case_processing_service = case_processing_service
        self.relevance_service = relevance_service

    async def _initialize_service(self) -> None:
        """Initialize the docket processor and validate dependencies."""
        if self._initialized:
            return
        
        # Validate required dependencies
        if not self.case_processing_service:
            raise ValueError("CaseProcessingService is required for DocketProcessor")
        
        logger.info("DocketProcessor initialized successfully")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate docket processing methods."""
        action = data.get("action")
        
        if action == "process_docket":
            return await self.process_docket(
                page=data.get("page"),
                initial_details=data.get("initial_details", {}),
                html_content=data.get("html_content"),
                court_logger=data.get("court_logger")
            )
        elif action == "parse_docket_html":
            return await self.parse_docket_html(
                html_content=data.get("html_content"),
                initial_details=data.get("initial_details", {}),
                court_logger=data.get("court_logger")
            )
        elif action == "extract_case_details":
            return await self.extract_case_details(
                parsed_data=data.get("parsed_data", {}),
                initial_details=data.get("initial_details", {}),
                court_logger=data.get("court_logger")
            )
        else:
            raise ValueError(f"Unknown action for DocketProcessor: {action}")

    async def process_docket(
        self, 
        page: Page, 
        initial_details: Dict[str, Any],
        html_content: Optional[str] = None,
        court_logger: Optional[Any] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Process a single docket: parse HTML, extract cases, and structure data.
        
        Args:
            page: Browser page containing the docket
            initial_details: Initial case/docket information
            html_content: Optional pre-extracted HTML content
            
        Returns:
            Processed docket data with extracted case details
        """
        court_id = initial_details.get("court_id", "N/A")
        docket_num = initial_details.get("docket_num", "N/A")
        
        # Use court_logger if provided, otherwise fallback to service logger
        logger = court_logger if court_logger else self
        
        logger.info(f"[{court_id}][{docket_num}] Processing docket HTML content")
        
        try:
            # Step 1: Get HTML content if not provided
            if not html_content:
                if page is None:
                    logger.error(f"[{court_id}][{docket_num}] Page object is None - cannot retrieve HTML content")
                    return None
                
                try:
                    html_content = await page.content()
                    if not html_content:
                        logger.error(f"[{court_id}][{docket_num}] Retrieved empty HTML content from page")
                        return None
                except Exception as e:
                    logger.error(f"[{court_id}][{docket_num}] Error retrieving HTML content: {e}")
                    return None
            
            # Step 2: Parse HTML and extract structured data
            parsed_data = await self.parse_docket_html(html_content, initial_details, court_logger)
            if not parsed_data:
                logger.error(f"[{court_id}][{docket_num}] HTML parsing returned no data")
                return None
            
            # Step 3: Extract individual case details
            case_details = await self.extract_case_details(parsed_data, initial_details, court_logger)
            if not case_details:
                logger.error(f"[{court_id}][{docket_num}] Case extraction returned no details")
                return None
            
            # Step 4: Add processing metadata
            # Generate proper base_filename using case processing service
            if not case_details.get("base_filename"):
                # Create base filename with proper docket parsing that excludes cv/cr/bk letters
                try:
                    from src.utils.docket_utils import parse_docket_number_numeric_only
                    clean_docket = parse_docket_number_numeric_only(docket_num)
                    
                    # Add versus for better filename
                    versus = case_details.get('versus', case_details.get('case_title', ''))
                    if versus:
                        # Clean versus for filename
                        import re
                        clean_versus = re.sub(r'[^\w\d\s]', '_', versus)
                        clean_versus = '_'.join(clean_versus.split()[:6])  # First 6 words max
                        base_filename = f"{court_id}_{clean_docket}_{clean_versus}"
                    else:
                        base_filename = f"{court_id}_{clean_docket}"
                except Exception as e:
                    logger.error(f"Error creating base_filename: {e}")
                    # Fallback to simple court_id_docket format
                    clean_docket = docket_num.replace(':', '_').replace('-', '_')
                    base_filename = f"{court_id}_{clean_docket}"
                
                case_details["base_filename"] = base_filename
            
            case_details.update({
                "_processing_phase": "docket_html_completed",
                "_html_content": html_content,
                "_docket_parsed": True,
                "_parsed_timestamp": self._get_timestamp()
            })
            
            logger.info(f"[{court_id}][{docket_num}] Docket processing completed successfully")
            return case_details
            
        except Exception as e:
            logger.error(f"[{court_id}][{docket_num}] Docket processing failed: {e}")
            return None

    async def parse_docket_html(
        self, 
        html_content: str, 
        initial_details: Dict[str, Any],
        court_logger: Optional[Any] = None
    ) -> Dict[str, Any]:
        """
        Parse docket HTML and extract structured data.
        
        Args:
            html_content: Raw HTML content from the docket page
            initial_details: Initial case/docket information
            
        Returns:
            Parsed and structured docket data
        """
        court_id = initial_details.get("court_id", "N/A")
        docket_num = initial_details.get("docket_num", "N/A")
        
        # Use court_logger if provided, otherwise fallback to service logger
        logger = court_logger if court_logger else self
        
        logger.info(f"[{court_id}][{docket_num}] Parsing docket HTML content (length: {len(html_content)} chars)")
        
        try:
            # Use case processing service to parse HTML
            # Using update_case_details action which accepts html_content instead of page
            case_details = await self.case_processing_service.perform_action({
                "action": "update_case_details",
                "initial_details": initial_details,
                "html_content": html_content
            })
            
            if not case_details:
                logger.error(f"[{court_id}][{docket_num}] Case processing service returned no data")
                return {}
            
            # REQUIREMENT: Log HTML parsing results (summary of data extracted or error)
            extracted_fields = len(case_details)
            attorneys_count = len(case_details.get('attorney', [])) if isinstance(case_details.get('attorney'), list) else (1 if case_details.get('attorney') else 0)
            has_proceedings = not case_details.get('_no_proceedings_detected', False)
            
            logger.info(f"[{court_id}][{docket_num}] HTML PARSING RESULT: extracted {extracted_fields} fields, {attorneys_count} attorneys, proceedings_found: {has_proceedings}")
            
            # Add docket-specific parsing metadata
            case_details.update({
                "_html_parsed": True,
                "_html_length": len(html_content),
                "_parsing_method": "case_processing_service",
                "_parsed_at": self._get_timestamp()
            })
            
            logger.info(f"[{court_id}][{docket_num}] HTML parsing completed successfully")
            return case_details
            
        except Exception as e:
            # REQUIREMENT: Log HTML parsing errors
            logger.error(f"[{court_id}][{docket_num}] HTML PARSING ERROR: {str(e)}")
            return {}

    async def extract_case_details(
        self, 
        parsed_data: Dict[str, Any], 
        initial_details: Dict[str, Any],
        court_logger: Optional[Any] = None
    ) -> Dict[str, Any]:
        """
        Extract individual case details from parsed docket data.
        
        Args:
            parsed_data: Parsed docket data from HTML processing
            initial_details: Initial case/docket information
            
        Returns:
            Enhanced case details with extracted information
        """
        court_id = initial_details.get("court_id", "N/A")
        docket_num = initial_details.get("docket_num", "N/A")
        
        # Use court_logger if provided, otherwise fallback to service logger
        logger = court_logger if court_logger else self
        
        logger.info(f"[{court_id}][{docket_num}] Extracting case details from parsed data")
        
        try:
            # Start with initial details and parsed data
            case_details = {**initial_details, **parsed_data}
            
            # Extract key case information
            case_details.update({
                # Core identifiers
                "court_id": court_id,
                "docket_num": docket_num,
                
                # Case metadata
                "case_title": self._extract_case_title(parsed_data, initial_details),
                "filed_date": self._extract_filed_date(parsed_data, initial_details),
                "case_type": self._extract_case_type(parsed_data),
                "case_status": self._extract_case_status(parsed_data),
                "nature_of_suit": self._extract_nature_of_suit(parsed_data),
                
                # Party information
                "plaintiff": self._extract_plaintiff(parsed_data),
                "defendant": self._extract_defendant(parsed_data),
                "attorney": self._extract_attorneys(parsed_data),  # SINGULAR 'attorney' not 'attorneys'
                
                # Docket entries
                "docket_entries": self._extract_docket_entries(parsed_data),
                "document_count": self._count_documents(parsed_data),
                
                # Processing metadata
                "_case_extracted": True,
                "_extraction_timestamp": self._get_timestamp(),
                "_extraction_method": "docket_processor"
            })
            
            # Validate extracted data
            if not self._validate_case_details(case_details):
                logger.warning(f"[{court_id}][{docket_num}] Case details validation failed")
            
            logger.info(f"[{court_id}][{docket_num}] Case details extraction completed")
            return case_details
            
        except Exception as e:
            logger.error(f"[{court_id}][{docket_num}] Case details extraction failed: {e}")
            return parsed_data  # Return original data if extraction fails

    def _extract_case_title(self, parsed_data: Dict[str, Any], initial_details: Dict[str, Any]) -> str:
        """Extract case title from parsed data or initial details."""
        return (
            parsed_data.get("case_title") or 
            initial_details.get("case_title") or 
            parsed_data.get("title") or
            ""
        )

    def _extract_filed_date(self, parsed_data: Dict[str, Any], initial_details: Dict[str, Any]) -> str:
        """Extract filed date from parsed data or initial details."""
        return (
            parsed_data.get("filed_date") or 
            initial_details.get("filed_date") or 
            parsed_data.get("date_filed") or
            ""
        )

    def _extract_case_type(self, parsed_data: Dict[str, Any]) -> str:
        """Extract case type from parsed data."""
        case_type = parsed_data.get("case_type", "")
        if not case_type:
            # Try to infer from docket number
            docket_num = parsed_data.get("docket_num", "")
            if "-cv-" in docket_num.lower():
                case_type = "Civil"
            elif "-cr-" in docket_num.lower():
                case_type = "Criminal"
            elif "-bk-" in docket_num.lower():
                case_type = "Bankruptcy"
        
        return case_type

    def _extract_case_status(self, parsed_data: Dict[str, Any]) -> str:
        """Extract case status from parsed data."""
        return parsed_data.get("case_status", parsed_data.get("status", ""))

    def _extract_nature_of_suit(self, parsed_data: Dict[str, Any]) -> str:
        """Extract nature of suit from parsed data."""
        return parsed_data.get("nature_of_suit", parsed_data.get("nos", ""))

    def _extract_plaintiff(self, parsed_data: Dict[str, Any]) -> str:
        """Extract plaintiff information from parsed data."""
        return parsed_data.get("plaintiff", parsed_data.get("petitioner", ""))

    def _extract_defendant(self, parsed_data: Dict[str, Any]) -> str:
        """Extract defendant information from parsed data."""
        return parsed_data.get("defendant", parsed_data.get("respondent", ""))

    def _extract_attorneys(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract attorney information from parsed data."""
        # CRITICAL FIX: HTML parser returns "attorney" not "attorneys"
        attorneys = parsed_data.get("attorney", [])
        if isinstance(attorneys, str):
            # Convert string to list format
            attorneys = [{"name": attorneys, "role": "unknown"}]
        elif not isinstance(attorneys, list):
            attorneys = []
        
        return attorneys

    def _extract_docket_entries(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract docket entries from parsed data."""
        entries = parsed_data.get("docket_entries", [])
        if not isinstance(entries, list):
            entries = []
        
        # Ensure each entry has required fields
        standardized_entries = []
        for entry in entries:
            if isinstance(entry, dict):
                standardized_entry = {
                    "entry_number": entry.get("entry_number", ""),
                    "date": entry.get("date", ""),
                    "description": entry.get("description", ""),
                    "document_links": entry.get("document_links", []),
                    "filer": entry.get("filer", "")
                }
                standardized_entries.append(standardized_entry)
        
        return standardized_entries

    def _count_documents(self, parsed_data: Dict[str, Any]) -> int:
        """Count total documents in the docket."""
        entries = self._extract_docket_entries(parsed_data)
        doc_count = 0
        
        for entry in entries:
            doc_links = entry.get("document_links", [])
            doc_count += len(doc_links) if isinstance(doc_links, list) else 0
        
        return doc_count

    def _validate_case_details(self, case_details: Dict[str, Any]) -> bool:
        """Validate extracted case details for completeness."""
        required_fields = ["court_id", "docket_num"]
        
        for field in required_fields:
            if not case_details.get(field):
                logger.warning(f"Missing required field: {field}")
                return False
        
        # Additional validation checks
        if case_details.get("document_count", 0) < 0:
            logger.warning("Invalid document count")
            return False
        
        return True

    def _get_timestamp(self) -> str:
        """Get current timestamp for processing metadata."""
        from datetime import datetime
        return datetime.now().isoformat()

    async def process_multiple_dockets(
        self, 
        docket_list: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Process multiple dockets in batch.
        
        Args:
            docket_list: List of docket information to process
            
        Returns:
            List of processed docket results
        """
        logger.info(f"Processing {len(docket_list)} dockets in batch")
        
        processed_dockets = []
        
        for i, docket_info in enumerate(docket_list):
            try:
                logger.info(f"Processing docket {i+1}/{len(docket_list)}")
                
                result = await self.process_docket(
                    page=docket_info.get("page"),
                    initial_details=docket_info.get("initial_details", {}),
                    html_content=docket_info.get("html_content")
                )
                
                if result:
                    processed_dockets.append(result)
                    
            except Exception as e:
                logger.error(f"Error processing docket {i+1}: {e}")
                continue
        
        logger.info(f"Batch processing completed: {len(processed_dockets)}/{len(docket_list)} successful")
        return processed_dockets