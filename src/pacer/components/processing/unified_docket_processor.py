"""
Unified Docket Processor - Single implementation for all docket processing workflows.

This processor implements the complete workflow sequence: N→Q→R→T→U→Y→Z→AA→BB→CC→DD→HH→KK→LL/MM
It replaces the divergent logic in single_docket_processor.py and ensures EVERY docket
goes through the query page first, regardless of source path.

Workflow Steps:
N - Check if docket number exists
Q - Navigate to query page  
R - Enter docket number and submit
T - Navigate to query results
U - Extract docket information 
Y - Validate case data
Z - Apply relevance checks
AA - Process ignore_download rules
BB - Extract HTML content
CC - Parse case details
DD - Generate metadata
HH - Upload to S3
KK - Save JSON data
LL - Process documents (if relevant)
MM - Complete workflow
"""

import logging
from typing import Any, Dict, Optional, List
from datetime import datetime
from playwright.async_api import Page, BrowserContext, Error as PlaywrightError

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.infrastructure.utils.date_formatter import format_iso_date


class UnifiedDocketProcessor(AsyncServiceBase):
    """
    Unified processor for all docket processing workflows.
    
    This processor ensures consistent behavior regardless of whether the docket
    comes from a report or is processed individually. ALL dockets go through
    the query page workflow for consistency.
    """

    def __init__(
        self,
        logger: Optional[logging.Logger] = None,
        config: Optional[Dict[str, Any]] = None,
        # Core services for workflow steps
        navigation_manager: Any = None,
        case_parser_service: Any = None,
        relevance_service: Any = None,
        ignore_download_service: Any = None,
        transfer_service: Any = None,
        s3_service: Any = None,
        file_operations_service: Any = None,
        case_classification_service: Any = None,
        download_orchestration_service: Any = None,
        # Supporting components
        element_locator: Any = None,
        page_navigator: Any = None,
        ecf_login_handler: Any = None,
    ):
        super().__init__(logger, config)
        
        # Core services
        self.navigation_manager = navigation_manager
        self.case_parser_service = case_parser_service
        self.relevance_service = relevance_service
        self.ignore_download_service = ignore_download_service
        self.transfer_service = transfer_service
        self.s3_service = s3_service
        self.file_operations_service = file_operations_service
        self.case_classification_service = case_classification_service
        self.download_orchestration_service = download_orchestration_service
        
        # Supporting components
        self.element_locator = element_locator
        self.page_navigator = page_navigator
        self.ecf_login_handler = ecf_login_handler

    async def _execute_action(self, data: Any) -> Any:
        """Execute unified docket processing actions."""
        action = data.get("action")
        
        if action == "process_docket":
            return await self.process_docket(**data)
        elif action == "process_case_row":
            return await self.process_case_row(**data)
        else:
            raise PacerServiceError(f"Unknown action for UnifiedDocketProcessor: {action}")

    async def process_docket(
        self,
        docket_number: str,
        court_id: str,
        context: BrowserContext,
        iso_date: str,
        initial_details: Optional[Dict[str, Any]] = None,
        processor_config: Optional[Dict[str, Any]] = None,
        court_logger: Optional[logging.Logger] = None,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """
        Process a single docket using the unified workflow.
        
        This method implements the complete workflow sequence and ALWAYS
        navigates to the query page first, regardless of source.
        
        Args:
            docket_number: The docket number to process
            court_id: Court identifier
            context: Browser context for navigation
            iso_date: Processing date in ISO format
            initial_details: Pre-extracted case details (optional)
            processor_config: Processing configuration
            court_logger: Court-specific logger
            
        Returns:
            Dict containing processed case details or None on failure
        """
        logger = court_logger or self.logger
        log_prefix = f"[{court_id}] UnifiedDocketProc:"
        
        logger.info(f"{log_prefix} Starting unified workflow for docket: {docket_number}")
        
        page = None
        try:
            # Step N: Check if docket number exists
            if not docket_number or not docket_number.strip():
                logger.error(f"{log_prefix} Invalid docket number provided")
                return None
            
            docket_number = docket_number.strip()
            logger.info(f"{log_prefix} Step N - Validated docket number: {docket_number}")
            
            # Create new page for processing
            page = await context.new_page()
            
            # Step Q: Navigate to query page
            logger.info(f"{log_prefix} Step Q - Navigating to query page")
            query_url = await self._get_query_page_url(court_id)
            if not query_url:
                logger.error(f"{log_prefix} Could not determine query page URL for court: {court_id}")
                return None
                
            await page.goto(query_url, wait_until="networkidle", timeout=30000)
            logger.info(f"{log_prefix} Successfully navigated to query page: {query_url}")
            
            # Step R: Enter docket number and submit
            logger.info(f"{log_prefix} Step R - Entering docket number and submitting")
            case_page = await self._submit_docket_query(page, docket_number, court_id, logger)
            if not case_page:
                logger.error(f"{log_prefix} Failed to submit docket query or navigate to case page")
                return None
            
            # Step T: Navigate to query results (already done in step R)
            logger.info(f"{log_prefix} Step T - Successfully reached case page: {case_page.url}")
            
            # Step U: Extract docket information
            logger.info(f"{log_prefix} Step U - Extracting docket information")
            
            # Step BB: Extract HTML content (moved up to fix "Page object is None" error)
            logger.info(f"{log_prefix} Step BB - Extracting HTML content")
            html_content = await self._extract_html_content(case_page, logger)
            if not html_content:
                logger.error(f"{log_prefix} Failed to extract HTML content from case page")
                return None
            
            # Step CC: Parse case details
            logger.info(f"{log_prefix} Step CC - Parsing case details")
            logger.info(f"{log_prefix} Step CC - HTML content length: {len(html_content)} characters")
            
            case_details = await self._parse_case_details(
                html_content, initial_details or {}, court_id, docket_number, logger
            )
            if not case_details:
                logger.error(f"{log_prefix} Failed to parse case details")
                return None
            
            # COMPREHENSIVE DEFENDANT EXTRACTION LOGGING
            logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION ANALYSIS:")
            versus = case_details.get('versus', case_details.get('case_title', ''))
            logger.info(f"{log_prefix} Step CC - Raw versus field: '{versus}'")
            
            # Log defendant extraction from versus field
            if versus:
                # Check for list-style format (comma-separated after v.)
                vs_patterns = [' v. ', ' V. ', ' vs. ', ' Vs. ', ' versus ']
                found_pattern = None
                split_versus = None
                
                for pattern in vs_patterns:
                    if pattern in versus:
                        found_pattern = pattern
                        split_versus = versus.split(pattern, 1)
                        break
                
                if split_versus and len(split_versus) >= 2:
                    plaintiff_part = split_versus[0].strip()
                    defendant_part = split_versus[1].strip()
                    
                    logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - Pattern found: '{found_pattern}'")
                    logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - Plaintiff part: '{plaintiff_part}'")
                    logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - Defendant part (raw): '{defendant_part}'")
                    
                    # Check if defendants are in list format (comma-separated)
                    if ',' in defendant_part:
                        defendant_list = [d.strip() for d in defendant_part.split(',') if d.strip()]
                        logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - List format detected: {len(defendant_list)} defendants")
                        for i, defendant in enumerate(defendant_list, 1):
                            logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - Defendant {i}: '{defendant}'")
                        logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - Source: LIST (comma-separated after versus)")
                    else:
                        logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - Single defendant: '{defendant_part}'")
                        logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - Source: SINGLE (after versus pattern)")
                else:
                    logger.warning(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - No versus pattern found in '{versus}'")
                    logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - Source: NONE (no pattern match)")
            else:
                logger.warning(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - No versus/case_title field found")
                logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - Source: NONE (no versus field)")
            
            # Log parsed defendant info from case_details
            parsed_defendant = case_details.get('defendant', '')
            if parsed_defendant:
                logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - Parsed defendant field: '{parsed_defendant}'")
            else:
                logger.info(f"{log_prefix} Step CC - DEFENDANT EXTRACTION - No parsed defendant field found")
            
            # Step Y: Validate case data
            logger.info(f"{log_prefix} Step Y - Validating case data")
            logger.info(f"{log_prefix} Step Y - Validating fields: court_id={case_details.get('court_id')}, docket_num={case_details.get('docket_num')}")
            logger.info(f"{log_prefix} Step Y - Validating versus/title: versus='{case_details.get('versus', '')}', title='{case_details.get('title', '')}'")
            
            if not self._validate_case_data(case_details, logger):
                logger.warning(f"{log_prefix} Case data validation failed")
                return None
            
            logger.info(f"{log_prefix} Step Y - Case data validation PASSED")
            
            # Step Z: Apply relevance checks
            logger.info(f"{log_prefix} Step Z - Applying relevance checks")
            logger.info(f"{log_prefix} Step Z - Input to RelevanceService: docket_num={case_details.get('docket_num')}, versus='{case_details.get('versus')}'")
            logger.info(f"{log_prefix} Step Z - Input to RelevanceService: case_type={case_details.get('case_type')}, nature_of_suit={case_details.get('nature_of_suit')}")
            
            relevance_result = await self._check_case_relevance(case_details, processor_config, logger)
            
            logger.info(f"{log_prefix} Step Z - RelevanceService RESULT: {relevance_result}")
            if relevance_result:
                logger.info(f"{log_prefix} Step Z - Case determined RELEVANT by RelevanceService")
            else:
                logger.warning(f"{log_prefix} Step Z - Case determined NOT RELEVANT by RelevanceService")
            
            # Step AA: Process ignore_download rules
            logger.info(f"{log_prefix} Step AA - Processing ignore_download rules")
            logger.info(f"{log_prefix} Step AA - Input to IgnoreDownloadService: status={case_details.get('status')}, disposition={case_details.get('disposition')}")
            logger.info(f"{log_prefix} Step AA - Input to IgnoreDownloadService: judge={case_details.get('judge')}, case_type={case_details.get('case_type')}")
            
            ignore_download = await self._check_ignore_download(case_details, logger)
            case_details["ignore_download"] = ignore_download
            
            logger.info(f"{log_prefix} Step AA - IgnoreDownloadService RESULT: {ignore_download}")
            if ignore_download:
                logger.warning(f"{log_prefix} Step AA - Case marked to IGNORE DOWNLOAD by IgnoreDownloadService")
            else:
                logger.info(f"{log_prefix} Step AA - Case approved for download processing by IgnoreDownloadService")
            
            # COMPREHENSIVE SECOND RELEVANCE CHECK - After docket sheet loads
            logger.info(f"{log_prefix} ============ SECOND RELEVANCE CHECK - COMPREHENSIVE ANALYSIS ============")
            logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - Case details loaded: {case_details.get('docket_num', 'Unknown')}")
            logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - Case title: {case_details.get('versus', 'Unknown')}")
            logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - Filing date: {case_details.get('filing_date', 'Unknown')}")
            logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - Case type: {case_details.get('case_type', 'Unknown')}")
            logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - Nature of suit: {case_details.get('nature_of_suit', 'Unknown')}")
            logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - Case status: {case_details.get('status', 'Unknown')}")
            logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - Judge: {case_details.get('judge', 'Unknown')}")
            
            # Log results of relevance_service
            logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - RelevanceService result: {relevance_result}")
            if relevance_result:
                logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - Case determined RELEVANT by RelevanceService")
            else:
                logger.warning(f"{log_prefix} SECOND RELEVANCE CHECK - Case determined NOT RELEVANT by RelevanceService")
            
            # Log results of ignore_download_service
            logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - IgnoreDownloadService result: {ignore_download}")
            if ignore_download:
                logger.warning(f"{log_prefix} SECOND RELEVANCE CHECK - Case marked for DOWNLOAD IGNORE by IgnoreDownloadService")
            else:
                logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - Case NOT marked for ignore by IgnoreDownloadService")
            
            # Check transfer_service if available
            transfer_result = None
            try:
                if hasattr(self, 'transfer_service') and self.transfer_service:
                    transfer_result = await self._check_transfer_service(case_details, logger)
                    logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - TransferService result: {transfer_result}")
                    if transfer_result:
                        logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - Case determined as TRANSFER by TransferService")
                    else:
                        logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - Case determined as NOT TRANSFER by TransferService")
                else:
                    logger.info(f"{log_prefix} SECOND RELEVANCE CHECK - TransferService not available or not configured")
            except Exception as e:
                logger.error(f"{log_prefix} SECOND RELEVANCE CHECK - TransferService check failed: {e}")
            
            # COMPREHENSIVE FINAL DECISION LOGGING
            logger.info(f"{log_prefix} ============ FINAL DECISION SUMMARY ============")
            logger.info(f"{log_prefix} FINAL DECISION -   Relevance Service: {'RELEVANT' if relevance_result else 'NOT RELEVANT'}")
            logger.info(f"{log_prefix} FINAL DECISION -   Ignore Download Service: {'IGNORE' if ignore_download else 'PROCESS'}")
            logger.info(f"{log_prefix} FINAL DECISION -   Transfer Service: {('TRANSFER' if transfer_result else 'NOT TRANSFER') if transfer_result is not None else 'N/A'}")
            logger.info(f"{log_prefix} FINAL DECISION -   Processing explicit dockets: {processor_config.get('_processing_explicit_dockets', False) if processor_config else False}")
            logger.info(f"{log_prefix} FINAL DECISION -   HTML-only mode: {processor_config.get('html_only', False) if processor_config else False}")
            
            # COMPREHENSIVE DECISION FACTORS ANALYSIS
            is_removal = case_details.get("_html_indicates_removal", False) or case_details.get("is_removal", False)
            will_skip_documents = not relevance_result and not processor_config.get("_processing_explicit_dockets", False)
            is_transferred = case_details.get("is_transferred", False)
            has_transfer_result = transfer_result is not None and transfer_result
            
            logger.info(f"{log_prefix} ============ DECISION FACTORS ANALYSIS ============")
            logger.info(f"{log_prefix} DECISION FACTORS -   Is removal case: {is_removal}")
            logger.info(f"{log_prefix} DECISION FACTORS -   Is transferred case: {is_transferred}")
            logger.info(f"{log_prefix} DECISION FACTORS -   Transfer service positive: {has_transfer_result}")
            logger.info(f"{log_prefix} DECISION FACTORS -   Will skip document processing: {will_skip_documents}")
            logger.info(f"{log_prefix} DECISION FACTORS -   Override for explicit processing: {processor_config.get('_processing_explicit_dockets', False) if processor_config else False}")
            logger.info(f"{log_prefix} ============ END COMPREHENSIVE RELEVANCE CHECK ============")
            
            # Step DD: Generate metadata
            logger.info(f"{log_prefix} Step DD - Generating metadata")
            case_details = await self._generate_metadata(case_details, iso_date, logger)
            
            # Step HH: Upload to S3
            logger.info(f"{log_prefix} Step HH - Uploading HTML to S3")
            await self._upload_to_s3(case_details, html_content, iso_date, logger)
            
            # Step KK: Save JSON data
            logger.info(f"{log_prefix} Step KK - Saving JSON data")
            await self._save_json_data(case_details, court_id, iso_date, logger)
            
            # NEXT STEPS EVALUATION WITH COMPREHENSIVE LOGGING
            logger.info(f"{log_prefix} ============ POST-RELEVANCE EVALUATION ============")
            logger.info(f"{log_prefix} POST-RELEVANCE - Evaluating workflow continuation decisions")
            
            if case_details.get("_html_indicates_removal") or case_details.get("is_removal"):
                logger.info(f"{log_prefix} POST-RELEVANCE DECISION - REMOVAL CASE DETECTED")
                logger.info(f"{log_prefix} POST-RELEVANCE DECISION - _html_indicates_removal: {case_details.get('_html_indicates_removal', False)}")
                logger.info(f"{log_prefix} POST-RELEVANCE DECISION - is_removal: {case_details.get('is_removal', False)}")
                logger.info(f"{log_prefix} POST-RELEVANCE DECISION - EARLY EXIT - Completing workflow WITHOUT document processing")
                logger.info(f"{log_prefix} ============ WORKFLOW COMPLETED - REMOVAL CASE ============")
                return case_details
            
            if not relevance_result and not processor_config.get("_processing_explicit_dockets", False):
                logger.info(f"{log_prefix} POST-RELEVANCE DECISION - IRRELEVANT CASE DETECTED")
                logger.info(f"{log_prefix} POST-RELEVANCE DECISION - relevance_result: {relevance_result}")
                logger.info(f"{log_prefix} POST-RELEVANCE DECISION - _processing_explicit_dockets: {processor_config.get('_processing_explicit_dockets', False) if processor_config else False}")
                logger.info(f"{log_prefix} POST-RELEVANCE DECISION - EARLY EXIT - Completing workflow WITHOUT document processing")
                logger.info(f"{log_prefix} ============ WORKFLOW COMPLETED - IRRELEVANT CASE ============")
                return case_details
            
            # DOCUMENT PROCESSING APPROVAL WITH DETAILED REASONING
            logger.info(f"{log_prefix} POST-RELEVANCE DECISION - CASE APPROVED FOR DOCUMENT PROCESSING")
            logger.info(f"{log_prefix} POST-RELEVANCE DECISION - Approval reasons:")
            if relevance_result:
                logger.info(f"{log_prefix} POST-RELEVANCE DECISION -   ✓ Case determined RELEVANT by RelevanceService")
            if processor_config.get("_processing_explicit_dockets", False):
                logger.info(f"{log_prefix} POST-RELEVANCE DECISION -   ✓ EXPLICIT dockets processing override enabled")
            logger.info(f"{log_prefix} POST-RELEVANCE DECISION - Proceeding to Step LL: Document Processing")
            logger.info(f"{log_prefix} ============ CONTINUING TO DOCUMENT PROCESSING ============")
            
            # Step LL: Process documents (if relevant)
            logger.info(f"{log_prefix} ============ Step LL - DOCUMENT PROCESSING ============")
            logger.info(f"{log_prefix} Step LL - Starting document download and processing workflow")
            logger.info(f"{log_prefix} Step LL - Case approved for document processing based on relevance/explicit flags")
            
            case_details = await self._process_documents(case_details, case_page, processor_config, logger)
            
            logger.info(f"{log_prefix} Step LL - Document processing workflow completed")
            
            # Step MM: Complete workflow
            logger.info(f"{log_prefix} ============ Step MM - WORKFLOW COMPLETION ============")
            logger.info(f"{log_prefix} Step MM - Full unified workflow completed successfully")
            logger.info(f"{log_prefix} Step MM - All processing steps executed: N→Q→R→T→U→Y→Z→AA→BB→CC→DD→HH→KK→LL→MM")
            logger.info(f"{log_prefix} ============ UNIFIED WORKFLOW SUCCESS ============")
            return case_details
            
        except Exception as e:
            logger.error(f"{log_prefix} Unified docket processing failed: {e}", exc_info=True)
            return None
        finally:
            if page and not page.is_closed():
                await page.close()

    async def process_case_row(
        self,
        row_element: Any,
        court_id: str,
        context: BrowserContext,
        iso_date: str,
        case_index: int = 0,
        processor_config: Optional[Dict[str, Any]] = None,
        court_logger: Optional[logging.Logger] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a case row from a PACER report using the unified workflow.
        
        This method extracts case details from the row and then uses the
        unified process_docket method for consistent processing.
        """
        logger = court_logger or self.logger
        log_prefix = f"[{court_id}] UnifiedDocketProc-Row[{case_index}]:"
        
        logger.info(f"{log_prefix} Processing case row")
        
        try:
            # Extract case details from the table row
            case_details = await self._extract_case_details_from_row(row_element, court_id, logger)
            if not case_details:
                logger.warning(f"{log_prefix} Failed to extract case details from row")
                return {
                    "status": "failed",
                    "error": "Failed to extract case details from row",
                    "dockets_processed": 0
                }
            
            docket_number = case_details.get("docket_num")
            if not docket_number:
                logger.warning(f"{log_prefix} No docket number found in row")
                return {
                    "status": "failed",
                    "error": "No docket number found in row",
                    "dockets_processed": 0
                }
            
            logger.info(f"{log_prefix} Extracted docket: {docket_number} - {case_details.get('versus', 'Unknown')}")
            
            # Use unified workflow to process this docket
            docket_result = await self.process_docket(
                docket_number=docket_number,
                court_id=court_id,
                context=context,
                iso_date=iso_date,
                initial_details=case_details,
                processor_config=processor_config,
                court_logger=logger
            )
            
            if docket_result:
                logger.info(f"{log_prefix} Case processing completed successfully")
                return {
                    "status": "success",
                    "case_details": docket_result,
                    "dockets_processed": 1
                }
            else:
                logger.warning(f"{log_prefix} Case processing failed")
                return {
                    "status": "failed",
                    "error": "Case processing failed",
                    "dockets_processed": 0
                }
                
        except Exception as e:
            logger.error(f"{log_prefix} Row processing failed: {e}", exc_info=True)
            return {
                "status": "failed",
                "error": str(e),
                "dockets_processed": 0
            }

    async def _get_query_page_url(self, court_id: str) -> Optional[str]:
        """Get the query page URL for a specific court."""
        # This would typically come from configuration
        base_urls = {
            "ilnd": "https://ecf.ilnd.uscourts.gov/cgi-bin/CaseSummary.pl",
            "nysd": "https://ecf.nysd.uscourts.gov/cgi-bin/CaseSummary.pl",
            # Add more court URLs as needed
        }
        
        return base_urls.get(court_id.lower())

    async def _submit_docket_query(
        self, 
        page: Page, 
        docket_number: str, 
        court_id: str, 
        logger: logging.Logger
    ) -> Optional[Page]:
        """Submit docket query and navigate to case page."""
        try:
            # Fill docket number field
            docket_field_selector = "input[name='case_num']"
            await page.wait_for_selector(docket_field_selector, timeout=10000)
            await page.fill(docket_field_selector, docket_number)
            logger.info(f"Filled docket number field with: {docket_number}")
            
            # Submit the form
            submit_selector = "input[type='submit'], button[type='submit']"
            await page.click(submit_selector)
            logger.info("Submitted docket query form")
            
            # Wait for navigation to case page
            await page.wait_for_load_state("networkidle", timeout=30000)
            
            # Verify we're on a case page
            current_url = page.url
            if "doc1" in current_url.lower() or "case" in current_url.lower():
                logger.info(f"Successfully navigated to case page: {current_url}")
                return page
            else:
                logger.warning(f"Unexpected page after query submission: {current_url}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to submit docket query: {e}", exc_info=True)
            return None

    async def _extract_html_content(self, page: Page, logger: logging.Logger) -> Optional[str]:
        """Extract HTML content from the current page."""
        try:
            if page is None or page.is_closed():
                logger.error("Cannot extract HTML from None or closed page")
                return None
            
            html_content = await page.content()
            if not html_content or len(html_content.strip()) == 0:
                logger.error("Retrieved empty HTML content")
                return None
            
            logger.info(f"Successfully extracted HTML content ({len(html_content)} characters)")
            return html_content
            
        except Exception as e:
            logger.error(f"Failed to extract HTML content: {e}", exc_info=True)
            return None

    async def _parse_case_details(
        self,
        html_content: str,
        initial_details: Dict[str, Any],
        court_id: str,
        docket_number: str,
        logger: logging.Logger
    ) -> Optional[Dict[str, Any]]:
        """Parse case details from HTML content."""
        try:
            if not self.case_parser_service:
                logger.error("CaseParserService not available")
                return None
            
            # Set HTML content in parser
            self.case_parser_service.set_html(html_content)
            
            # Start with initial details
            case_details = {
                "court_id": court_id,
                "docket_num": docket_number,
                **initial_details
            }
            
            # Parse additional details from HTML
            parsed_details = self.case_parser_service.parse_case_details()
            if parsed_details:
                case_details.update(parsed_details)
            
            # Classify the case
            if self.case_classification_service:
                case_details = self.case_classification_service.classify_case_from_html(
                    case_details, html_content
                )
            
            logger.info(f"Successfully parsed case details for docket: {docket_number}")
            return case_details
            
        except Exception as e:
            logger.error(f"Failed to parse case details: {e}", exc_info=True)
            return None

    def _validate_case_data(self, case_details: Dict[str, Any], logger: logging.Logger) -> bool:
        """Validate that case data contains required fields and check case title/versus with defendant extraction."""
        required_fields = ["court_id", "docket_num"]
        
        for field in required_fields:
            if not case_details.get(field):
                logger.error(f"Missing required field: {field}")
                return False
        
        # Enhanced validation with defendants list check first
        versus = case_details.get("versus", "")
        title = case_details.get("title", "")
        defendants = case_details.get("defendants", [])
        
        court_id = case_details.get("court_id", "N/A")
        log_prefix = f"[{court_id}] DefendantValidation:"
        
        # Step 1: Check defendants list first
        if defendants and isinstance(defendants, list) and len(defendants) > 0:
            logger.info(f"{log_prefix} Found defendants list with {len(defendants)} defendants - validation passed")
            for i, defendant in enumerate(defendants[:3]):  # Log first 3
                defendant_name = defendant.get('name', str(defendant)) if isinstance(defendant, dict) else str(defendant)
                logger.debug(f"{log_prefix} Defendant {i+1}: '{defendant_name}'")
            return True
        
        # Step 2: Fall back to parsing versus field
        if versus:
            logger.info(f"{log_prefix} No defendants list, checking versus field: '{versus}'")
            defendants_from_versus = self._extract_defendants_from_versus(versus, logger, log_prefix)
            if defendants_from_versus:
                # Update case_details with extracted defendants
                case_details["defendants"] = defendants_from_versus
                logger.info(f"{log_prefix} Successfully extracted {len(defendants_from_versus)} defendants from versus field")
                return True
            else:
                logger.warning(f"{log_prefix} Could not extract defendants from versus field: '{versus}'")
        
        # Step 3: Check title field as final fallback
        if title:
            logger.info(f"{log_prefix} Checking title field: '{title}'")
            defendants_from_title = self._extract_defendants_from_versus(title, logger, log_prefix)
            if defendants_from_title:
                case_details["defendants"] = defendants_from_title
                logger.info(f"{log_prefix} Successfully extracted {len(defendants_from_title)} defendants from title field")
                return True
        
        # Final validation check
        if not versus and not title:
            logger.warning(f"{log_prefix} Missing both 'versus' and 'title' fields and no defendants list")
            return False
        
        logger.info(f"{log_prefix} Case data validation passed with warnings")
        return True

    async def _check_case_relevance(
        self,
        case_details: Dict[str, Any],
        processor_config: Optional[Dict[str, Any]],
        logger: logging.Logger
    ) -> bool:
        """Check if the case is relevant using the RelevanceService."""
        try:
            if not self.relevance_service:
                logger.warning("RelevanceService not available, assuming relevant")
                return True
            
            # Check for explicit request override
            if processor_config and processor_config.get("_processing_explicit_dockets", False):
                logger.info("Explicit docket processing - bypassing relevance check")
                return True
            
            # Log case details being evaluated for relevance
            logger.info(f"Checking relevance for case: {case_details.get('docket_num', 'Unknown')}")
            logger.info(f"Case title: {case_details.get('versus', 'Unknown')}")
            logger.info(f"Case type: {case_details.get('case_type', 'Unknown')}")
            logger.info(f"Nature of suit: {case_details.get('nature_of_suit', 'Unknown')}")
            
            # Use RelevanceService to determine relevance with court_logger
            is_relevant = await self.relevance_service._execute_action({
                "action": "determine_case_relevance",
                "case_details": case_details,
                "court_logger": logger
            })
            
            logger.info(f"RelevanceService determination: {is_relevant} for docket {case_details.get('docket_num', 'Unknown')}")
            if is_relevant:
                logger.info(f"Case {case_details.get('docket_num', 'Unknown')} marked as RELEVANT")
            else:
                logger.warning(f"Case {case_details.get('docket_num', 'Unknown')} marked as NOT RELEVANT")
            
            return is_relevant
            
        except Exception as e:
            logger.error(f"Failed to check case relevance: {e}", exc_info=True)
            # Default to relevant on error to avoid losing data
            logger.warning("Defaulting to RELEVANT due to error")
            return True

    async def _check_ignore_download(
        self, 
        case_details: Dict[str, Any], 
        logger: logging.Logger
    ) -> bool:
        """Check if the case should be ignored for download."""
        try:
            if not self.ignore_download_service:
                logger.info("IgnoreDownloadService not available, not ignoring download")
                return False
            
            # Log case details being evaluated for ignore download
            logger.info(f"Checking ignore download rules for case: {case_details.get('docket_num', 'Unknown')}")
            logger.info(f"Case status: {case_details.get('status', 'Unknown')}")
            logger.info(f"Case disposition: {case_details.get('disposition', 'Unknown')}")
            logger.info(f"Judge: {case_details.get('judge', 'Unknown')}")
            
            should_ignore = await self.ignore_download_service._execute_action({
                "action": "should_ignore_download",
                "case_details": case_details,
                "court_logger": logger
            })
            
            logger.info(f"IgnoreDownloadService determination: {should_ignore} for docket {case_details.get('docket_num', 'Unknown')}")
            if should_ignore:
                logger.warning(f"Case {case_details.get('docket_num', 'Unknown')} marked to IGNORE DOWNLOAD")
            else:
                logger.info(f"Case {case_details.get('docket_num', 'Unknown')} approved for download processing")
            
            return should_ignore
            
        except Exception as e:
            logger.error(f"Failed to check ignore download: {e}", exc_info=True)
            # Default to not ignoring on error
            logger.warning("Defaulting to NOT IGNORE due to error")
            return False

    async def _generate_metadata(
        self,
        case_details: Dict[str, Any],
        iso_date: str,
        logger: logging.Logger
    ) -> Dict[str, Any]:
        """Generate additional metadata for the case."""
        try:
            # Ensure iso_date is properly formatted
            iso_date = format_iso_date(iso_date)
            
            # Generate base filename if not present
            if "base_filename" not in case_details:
                base_filename = f"{case_details['court_id']}_{case_details['docket_num'].replace(':', '-')}"
                case_details["base_filename"] = base_filename
            
            # Add processing metadata
            case_details.update({
                "processing_date": iso_date,
                "processed_at": datetime.now().isoformat(),
                "processor_version": "unified_v1.0"
            })
            
            logger.info(f"Generated metadata for case: {case_details.get('base_filename')}")
            return case_details
            
        except Exception as e:
            logger.error(f"Failed to generate metadata: {e}", exc_info=True)
            return case_details

    async def _upload_to_s3(
        self,
        case_details: Dict[str, Any],
        html_content: str,
        iso_date: str,
        logger: logging.Logger
    ) -> None:
        """Upload HTML content to S3."""
        try:
            if not self.s3_service:
                logger.warning("S3Service not available, skipping upload")
                return
            
            base_filename = case_details.get("base_filename")
            if not base_filename:
                logger.error("No base_filename available for S3 upload")
                return
            
            await self.s3_service._execute_action({
                "action": "upload_html",
                "base_filename": base_filename,
                "html_content": html_content,
                "iso_date": format_iso_date(iso_date),
                "court_logger": logger
            })
            
            logger.info(f"Successfully uploaded HTML to S3 for: {base_filename}")
            
        except Exception as e:
            logger.error(f"Failed to upload to S3: {e}", exc_info=True)

    async def _save_json_data(
        self,
        case_details: Dict[str, Any],
        court_id: str,
        iso_date: str,
        logger: logging.Logger
    ) -> None:
        """Save case details to JSON file."""
        try:
            if not self.file_operations_service:
                logger.warning("FileOperationsService not available, skipping JSON save")
                return
            
            await self.file_operations_service._execute_action({
                "action": "save_case_data_to_json",
                "case_details": case_details,
                "court_id": court_id,
                "iso_date": format_iso_date(iso_date),
                "court_logger": logger
            })
            
            logger.info(f"Successfully saved JSON data for: {case_details.get('base_filename')}")
            
        except Exception as e:
            logger.error(f"Failed to save JSON data: {e}", exc_info=True)

    def _extract_defendants_from_versus(self, versus: str, logger: logging.Logger, log_prefix: str) -> List[Dict[str, str]]:
        """Extract defendants from versus field with comprehensive logging and parsing."""
        if not versus:
            logger.debug(f"{log_prefix} No versus field provided")
            return []
        
        defendants = []
        
        # Look for various patterns of "v." or "vs."
        vs_patterns = [' v. ', ' V. ', ' vs. ', ' Vs. ', ' v ', ' V ']
        defendant_text = None
        matched_pattern = None
        
        logger.debug(f"{log_prefix} Parsing versus field: '{versus}'")
        
        for pattern in vs_patterns:
            if pattern in versus:
                parts = versus.split(pattern, 1)  # Split only on first occurrence
                if len(parts) > 1:
                    defendant_text = parts[1].strip()
                    matched_pattern = pattern
                    logger.debug(f"{log_prefix} Found pattern '{pattern}' in versus, defendant part: '{defendant_text}'")
                    break
        
        if not defendant_text:
            logger.warning(f"{log_prefix} No valid versus pattern found in: '{versus}'")
            return []
        
        # Handle "et al" cases - remove et al suffix
        if " et al" in defendant_text.lower():
            original_text = defendant_text
            defendant_text = self._clean_et_al(defendant_text)
            logger.info(f"{log_prefix} Removed 'et al': '{original_text}' -> '{defendant_text}'")
        
        # Parse defendant names
        defendant_names = self._parse_defendant_names(defendant_text, logger, log_prefix)
        
        # Convert to defendant objects
        for name in defendant_names:
            if name.strip():
                defendants.append({"name": name.strip()})
                logger.debug(f"{log_prefix} Added defendant: '{name.strip()}'")
        
        logger.info(f"{log_prefix} Final result - {len(defendants)} defendants extracted")
        return defendants
    
    def _clean_et_al(self, text: str) -> str:
        """Remove 'et al' and variations from defendant text."""
        import re
        # Remove various forms of "et al"
        patterns = [
            r'\s*,?\s*et\s+al\.?.*$',  # ", et al." or " et al" at end
            r'\s*,?\s*and\s+others.*$',  # ", and others" at end
            r'\s*,?\s*etc\.?.*$'  # ", etc." at end
        ]
        
        cleaned = text
        for pattern in patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE).strip()
        
        return cleaned
    
    def _parse_defendant_names(self, defendant_text: str, logger: logging.Logger, log_prefix: str) -> List[str]:
        """Parse defendant names from text with proper name handling."""
        if not defendant_text:
            return []
        
        # Check if this looks like a single entity (contains Inc., LLC, Corp., etc.)
        entity_indicators = ['Inc.', 'LLC', 'Corp.', 'Ltd.', 'Co.', 'Company', 'Corporation', 'L.L.C.', 'L.P.']
        has_entity_indicator = any(indicator in defendant_text for indicator in entity_indicators)
        
        if has_entity_indicator:
            # Treat as single defendant to avoid splitting "Avlon Industries, Inc."
            logger.debug(f"{log_prefix} Single entity defendant detected: '{defendant_text}'")
            return [defendant_text]
        
        # Split by comma for multiple defendants, but be careful with company names
        parts = [part.strip() for part in defendant_text.split(',')]
        
        if len(parts) == 1:
            # Single defendant
            return [defendant_text.strip()]
        
        # Multiple parts - rejoin parts that might be part of a company name
        processed_names = []
        i = 0
        while i < len(parts):
            current_part = parts[i]
            
            # Check if next part looks like a company suffix (but only if it's short)
            if (i + 1 < len(parts) and 
                len(parts[i + 1].strip()) <= 10 and  # Only combine short suffixes
                parts[i + 1].strip().lower() in ['inc.', 'inc', 'llc', 'corp.', 'corp', 'ltd.', 'ltd', 'co.', 'co', 'l.l.c.', 'l.p.']):
                # Combine with next part
                combined = f"{current_part}, {parts[i + 1]}".strip()
                processed_names.append(combined)
                logger.debug(f"{log_prefix} Combined company parts: '{current_part}' + '{parts[i + 1]}' = '{combined}'")
                i += 2  # Skip next part since we combined it
            else:
                if current_part:
                    processed_names.append(current_part)
                i += 1
        
        return processed_names

    async def _process_documents(
        self,
        case_details: Dict[str, Any],
        page: Page,
        processor_config: Optional[Dict[str, Any]],
        logger: logging.Logger
    ) -> Dict[str, Any]:
        """Process documents for the case."""
        try:
            if not self.download_orchestration_service:
                logger.warning("DownloadOrchestrationService not available, skipping document processing")
                return case_details
            
            is_explicitly_requested = processor_config.get("_processing_explicit_dockets", False) if processor_config else False
            
            case_details = await self.download_orchestration_service.execute_download_workflow(
                case_details=case_details,
                is_explicitly_requested=is_explicitly_requested,
                page=page,
                court_logger=logger
            )
            
            logger.info(f"Successfully processed documents for: {case_details.get('base_filename')}")
            return case_details
            
        except Exception as e:
            logger.error(f"Failed to process documents: {e}", exc_info=True)
            return case_details

    async def _extract_case_details_from_row(
        self,
        row_element: Any,
        court_id: str,
        logger: logging.Logger
    ) -> Optional[Dict[str, Any]]:
        """Extract case details from a table row in a PACER report."""
        try:
            # Get all cells in the row
            cells = row_element.locator("td")
            cell_count = await cells.count()
            
            if cell_count < 3:
                logger.warning(f"Row has insufficient cells ({cell_count}), skipping")
                return None
            
            case_details = {"court_id": court_id}
            
            # Extract case number from first cell
            case_num_cell = cells.nth(0)
            case_num_text = await case_num_cell.text_content()
            if case_num_text and case_num_text.strip():
                case_details["docket_num"] = case_num_text.strip()
            
            # Extract versus text from second cell
            if cell_count > 1:
                versus_cell = cells.nth(1)
                versus_text = await versus_cell.text_content()
                if versus_text and versus_text.strip():
                    case_details["versus"] = versus_text.strip()
            
            # Extract filing date from third cell
            if cell_count > 2:
                date_cell = cells.nth(2)
                date_text = await date_cell.text_content()
                if date_text and date_text.strip():
                    case_details["filing_date"] = date_text.strip()
            
            # Extract judge from fourth cell if available
            if cell_count > 3:
                judge_cell = cells.nth(3)
                judge_text = await judge_cell.text_content()
                if judge_text and judge_text.strip():
                    case_details["judge"] = judge_text.strip()
            
            # Extract nature/status from fifth cell if available
            if cell_count > 4:
                nature_cell = cells.nth(4)
                nature_text = await nature_cell.text_content()
                if nature_text and nature_text.strip():
                    case_details["nature_of_suit"] = nature_text.strip()
            
            logger.debug(f"Extracted case details: {case_details}")
            return case_details
            
        except Exception as e:
            logger.error(f"Failed to extract case details from row: {e}", exc_info=True)
            return None

    async def _check_transfer_service(
        self, 
        case_details: Dict[str, Any], 
        logger: logging.Logger
    ) -> bool:
        """Check if the case should be handled by the transfer service."""
        try:
            if not self.transfer_service:
                logger.info("TransferService not available")
                return False
            
            should_transfer = await self.transfer_service._execute_action({
                "action": "should_transfer_case",
                "case_details": case_details,
                "court_logger": logger
            })
            
            logger.info(f"Transfer service determination: {should_transfer}")
            return should_transfer
            
        except Exception as e:
            logger.error(f"Failed to check transfer service: {e}", exc_info=True)
            # Default to not transferring on error
            return False