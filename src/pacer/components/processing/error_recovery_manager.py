"""
Error Recovery Manager for Sequential PACER Workflow.

This manager provides comprehensive error handling and recovery mechanisms
for the sequential docket processing system.

MISSION: Handle errors and provide recovery mechanisms
KEY REQUIREMENTS:
- Graceful error handling with detailed logging
- Recovery strategies for common failure scenarios
- State preservation during errors
- Rollback capabilities for failed operations
"""

import asyncio
import json
import traceback
from typing import Any, Dict, Optional, List, Callable
from enum import Enum
from datetime import datetime, timedelta
from src.infrastructure.patterns.component_base import AsyncServiceBase


class ErrorType(Enum):
    """Types of errors that can occur in the workflow."""
    NAVIGATION_ERROR = "navigation_error"
    PAGE_TIMEOUT = "page_timeout"
    ELEMENT_NOT_FOUND = "element_not_found"
    CONTENT_PARSING_ERROR = "content_parsing_error"
    AUTHENTICATION_ERROR = "authentication_error"
    NETWORK_ERROR = "network_error"
    BROWSER_ERROR = "browser_error"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"


class RecoveryStrategy(Enum):
    """Recovery strategies for different error types."""
    RETRY = "retry"
    SKIP = "skip"
    ROLLBACK = "rollback"
    RESTART = "restart"
    MANUAL_INTERVENTION = "manual_intervention"
    ABORT = "abort"


class ErrorSeverity(Enum):
    """Severity levels for errors."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorRecoveryManager(AsyncServiceBase):
    """
    Manages error handling and recovery strategies for the sequential workflow.
    
    Provides comprehensive error analysis, recovery options, and state
    management to ensure robust workflow execution.
    """

    def __init__(
        self,
        state_validator: Optional[Any] = None,
        navigation_facade: Optional[Any] = None,
        court_logger: Optional[Any] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
    ):
        super().__init__(logger, config)
        self.state_validator = state_validator
        self.navigation_facade = navigation_facade
        self.court_logger = court_logger
        
        # Error tracking and recovery state
        self._error_history = []
        self._recovery_strategies = self._initialize_recovery_strategies()
        self._error_patterns = self._initialize_error_patterns()
        self._recovery_statistics = {
            'total_errors': 0,
            'recovered_errors': 0,
            'failed_recoveries': 0,
            'recovery_success_rate': 0.0
        }
        
        # Circuit breaker for repeated failures
        self._circuit_breaker = {
            'failure_count': 0,
            'failure_threshold': 5,
            'reset_timeout': timedelta(minutes=5),
            'last_failure_time': None,
            'state': 'closed'  # closed, open, half_open
        }

    async def _initialize_service(self) -> None:
        """Initialize the error recovery manager."""
        if self._initialized:
            return
        
        self.log_info("ErrorRecoveryManager initialized successfully")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate recovery methods."""
        action = data.get("action")
        
        if action == "handle_error":
            return await self.handle_error(
                error=data.get("error"),
                error_context=data.get("error_context", {}),
                navigator=data.get("navigator"),
                recovery_attempts=data.get("recovery_attempts", 0)
            )
        elif action == "analyze_error":
            return await self.analyze_error(
                error=data.get("error"),
                error_context=data.get("error_context", {})
            )
        elif action == "execute_recovery":
            return await self.execute_recovery(
                recovery_plan=data.get("recovery_plan"),
                navigator=data.get("navigator"),
                error_context=data.get("error_context", {})
            )
        elif action == "check_circuit_breaker":
            return self.check_circuit_breaker()
        elif action == "reset_circuit_breaker":
            return self.reset_circuit_breaker()
        else:
            raise ValueError(f"Unknown action for ErrorRecoveryManager: {action}")

    async def handle_error(
        self,
        error: Exception,
        error_context: Dict[str, Any],
        navigator: Optional[Any] = None,
        recovery_attempts: int = 0
    ) -> Dict[str, Any]:
        """
        Handle an error with comprehensive analysis and recovery.
        
        Args:
            error: The exception that occurred
            error_context: Context information about where/when error occurred
            navigator: PacerNavigator instance (if available)
            recovery_attempts: Number of recovery attempts already made
            
        Returns:
            Recovery result with success status and details
        """
        start_time = datetime.now()
        
        # Update circuit breaker
        await self._update_circuit_breaker_on_failure()
        
        # Check if circuit breaker is open
        circuit_state = self.check_circuit_breaker()
        if circuit_state['state'] == 'open':
            return {
                'success': False,
                'recovery_strategy': RecoveryStrategy.ABORT.value,
                'reason': 'Circuit breaker is open - too many recent failures',
                'circuit_breaker_state': circuit_state
            }
        
        try:
            # Analyze the error
            error_analysis = await self.analyze_error(error, error_context)
            
            # Determine recovery strategy
            recovery_plan = await self._determine_recovery_strategy(
                error_analysis, 
                recovery_attempts,
                navigator
            )
            
            # Log error and recovery plan
            if self.court_logger:
                court_id = error_context.get('court_id', 'unknown')
                docket_num = error_context.get('docket_num', 'unknown')
                
                self.court_logger.error(
                    f"[{court_id}][{docket_num}] ERROR RECOVERY: {error_analysis['error_type']} - {str(error)}"
                )
                self.court_logger.info(
                    f"[{court_id}][{docket_num}] RECOVERY PLAN: {recovery_plan['strategy']} "
                    f"(attempt {recovery_attempts + 1})"
                )
            
            # Execute recovery if possible
            if recovery_plan['strategy'] != RecoveryStrategy.ABORT:
                recovery_result = await self.execute_recovery(
                    recovery_plan, 
                    navigator, 
                    error_context
                )
                
                # Update statistics
                self._recovery_statistics['total_errors'] += 1
                if recovery_result['success']:
                    self._recovery_statistics['recovered_errors'] += 1
                else:
                    self._recovery_statistics['failed_recoveries'] += 1
                
                # Update success rate
                total = self._recovery_statistics['total_errors']
                recovered = self._recovery_statistics['recovered_errors']
                self._recovery_statistics['recovery_success_rate'] = (recovered / total) * 100 if total > 0 else 0
                
                # Create error record
                error_record = {
                    'timestamp': start_time.isoformat(),
                    'error_type': error_analysis['error_type'],
                    'error_message': str(error),
                    'error_context': error_context,
                    'recovery_strategy': recovery_plan['strategy'],
                    'recovery_attempts': recovery_attempts + 1,
                    'recovery_success': recovery_result['success'],
                    'recovery_duration_seconds': (datetime.now() - start_time).total_seconds(),
                    'error_analysis': error_analysis,
                    'recovery_plan': recovery_plan,
                    'recovery_result': recovery_result
                }
                
                self._error_history.append(error_record)
                
                return {
                    'success': recovery_result['success'],
                    'recovery_strategy': recovery_plan['strategy'],
                    'recovery_attempts': recovery_attempts + 1,
                    'error_record': error_record,
                    'recovery_details': recovery_result
                }
            else:
                # Cannot recover - abort
                error_record = {
                    'timestamp': start_time.isoformat(),
                    'error_type': error_analysis['error_type'],
                    'error_message': str(error),
                    'error_context': error_context,
                    'recovery_strategy': RecoveryStrategy.ABORT.value,
                    'recovery_attempts': recovery_attempts,
                    'recovery_success': False,
                    'abort_reason': recovery_plan.get('reason', 'Unknown'),
                    'error_analysis': error_analysis
                }
                
                self._error_history.append(error_record)
                
                return {
                    'success': False,
                    'recovery_strategy': RecoveryStrategy.ABORT.value,
                    'reason': recovery_plan.get('reason', 'Recovery not possible'),
                    'error_record': error_record
                }
                
        except Exception as recovery_error:
            # Recovery itself failed
            if self.court_logger:
                self.court_logger.error(f"ERROR RECOVERY FAILED: {recovery_error}", exc_info=True)
            
            return {
                'success': False,
                'recovery_strategy': RecoveryStrategy.ABORT.value,
                'reason': f'Recovery process failed: {recovery_error}',
                'original_error': str(error),
                'recovery_error': str(recovery_error)
            }

    async def analyze_error(
        self,
        error: Exception,
        error_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Analyze an error to determine its type, severity, and potential causes.
        
        Args:
            error: The exception that occurred
            error_context: Context information about the error
            
        Returns:
            Comprehensive error analysis
        """
        error_str = str(error).lower()
        error_type_name = type(error).__name__.lower()
        
        # Determine error type
        error_type = self._classify_error(error, error_str, error_type_name, error_context)
        
        # Determine severity
        severity = self._determine_error_severity(error_type, error_context)
        
        # Extract relevant information
        stack_trace = traceback.format_exc()
        
        # Check for known error patterns
        patterns_matched = []
        for pattern_name, pattern_info in self._error_patterns.items():
            if any(keyword in error_str for keyword in pattern_info['keywords']):
                patterns_matched.append({
                    'pattern': pattern_name,
                    'description': pattern_info['description'],
                    'common_causes': pattern_info['common_causes']
                })
        
        return {
            'error_type': error_type.value,
            'severity': severity.value,
            'error_message': str(error),
            'error_class': type(error).__name__,
            'stack_trace': stack_trace,
            'patterns_matched': patterns_matched,
            'context': error_context,
            'analysis_timestamp': datetime.now().isoformat(),
            'recoverable': self._is_error_recoverable(error_type, severity),
            'recommended_actions': self._get_recommended_actions(error_type, patterns_matched)
        }

    async def execute_recovery(
        self,
        recovery_plan: Dict[str, Any],
        navigator: Optional[Any],
        error_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute a recovery plan based on the error analysis.
        
        Args:
            recovery_plan: The recovery plan to execute
            navigator: PacerNavigator instance
            error_context: Context information about the error
            
        Returns:
            Recovery execution result
        """
        strategy = RecoveryStrategy(recovery_plan['strategy'])
        start_time = datetime.now()
        
        try:
            if strategy == RecoveryStrategy.RETRY:
                return await self._execute_retry_recovery(recovery_plan, navigator, error_context)
            
            elif strategy == RecoveryStrategy.SKIP:
                return await self._execute_skip_recovery(recovery_plan, error_context)
            
            elif strategy == RecoveryStrategy.ROLLBACK:
                return await self._execute_rollback_recovery(recovery_plan, navigator, error_context)
            
            elif strategy == RecoveryStrategy.RESTART:
                return await self._execute_restart_recovery(recovery_plan, navigator, error_context)
            
            elif strategy == RecoveryStrategy.MANUAL_INTERVENTION:
                return await self._execute_manual_intervention_recovery(recovery_plan, error_context)
            
            else:
                return {
                    'success': False,
                    'strategy': strategy.value,
                    'reason': f'Recovery strategy {strategy.value} not implemented',
                    'duration_seconds': (datetime.now() - start_time).total_seconds()
                }
                
        except Exception as e:
            return {
                'success': False,
                'strategy': strategy.value,
                'error': str(e),
                'reason': f'Recovery execution failed: {e}',
                'duration_seconds': (datetime.now() - start_time).total_seconds()
            }

    def _initialize_recovery_strategies(self) -> Dict[ErrorType, Dict[str, Any]]:
        """Initialize recovery strategies for different error types."""
        return {
            ErrorType.NAVIGATION_ERROR: {
                'primary': RecoveryStrategy.RETRY,
                'secondary': RecoveryStrategy.ROLLBACK,
                'max_retries': 3,
                'retry_delay': 2
            },
            ErrorType.PAGE_TIMEOUT: {
                'primary': RecoveryStrategy.RETRY,
                'secondary': RecoveryStrategy.RESTART,
                'max_retries': 2,
                'retry_delay': 5
            },
            ErrorType.ELEMENT_NOT_FOUND: {
                'primary': RecoveryStrategy.RETRY,
                'secondary': RecoveryStrategy.SKIP,
                'max_retries': 2,
                'retry_delay': 1
            },
            ErrorType.CONTENT_PARSING_ERROR: {
                'primary': RecoveryStrategy.RETRY,
                'secondary': RecoveryStrategy.SKIP,
                'max_retries': 1,
                'retry_delay': 1
            },
            ErrorType.AUTHENTICATION_ERROR: {
                'primary': RecoveryStrategy.RESTART,
                'secondary': RecoveryStrategy.MANUAL_INTERVENTION,
                'max_retries': 1,
                'retry_delay': 5
            },
            ErrorType.NETWORK_ERROR: {
                'primary': RecoveryStrategy.RETRY,
                'secondary': RecoveryStrategy.RESTART,
                'max_retries': 3,
                'retry_delay': 5
            },
            ErrorType.BROWSER_ERROR: {
                'primary': RecoveryStrategy.RESTART,
                'secondary': RecoveryStrategy.MANUAL_INTERVENTION,
                'max_retries': 1,
                'retry_delay': 3
            },
            ErrorType.VALIDATION_ERROR: {
                'primary': RecoveryStrategy.RETRY,
                'secondary': RecoveryStrategy.SKIP,
                'max_retries': 1,
                'retry_delay': 1
            },
            ErrorType.UNKNOWN_ERROR: {
                'primary': RecoveryStrategy.SKIP,
                'secondary': RecoveryStrategy.MANUAL_INTERVENTION,
                'max_retries': 0,
                'retry_delay': 0
            }
        }

    def _initialize_error_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize known error patterns and their characteristics."""
        return {
            'timeout_error': {
                'keywords': ['timeout', 'timed out', 'wait_for'],
                'description': 'Page or element loading timeout',
                'common_causes': ['Slow network', 'Server issues', 'Heavy page load']
            },
            'element_not_found': {
                'keywords': ['element not found', 'no such element', 'locator'],
                'description': 'Required page element could not be found',
                'common_causes': ['Page structure changed', 'Slow loading', 'Wrong selector']
            },
            'navigation_failed': {
                'keywords': ['navigation', 'navigate', 'goto', 'url'],
                'description': 'Page navigation failed',
                'common_causes': ['Invalid URL', 'Network issues', 'Server redirect']
            },
            'authentication_error': {
                'keywords': ['login', 'authentication', 'unauthorized', '401', '403'],
                'description': 'Authentication or authorization failure',
                'common_causes': ['Session expired', 'Invalid credentials', 'Access denied']
            },
            'parsing_error': {
                'keywords': ['parse', 'parsing', 'malformed', 'invalid format'],
                'description': 'Content parsing or data extraction failure',
                'common_causes': ['Unexpected page format', 'Missing data', 'HTML structure change']
            }
        }

    def _classify_error(
        self,
        error: Exception,
        error_str: str,
        error_type_name: str,
        error_context: Dict[str, Any]
    ) -> ErrorType:
        """Classify error based on its characteristics."""
        
        # Check for specific error patterns
        if 'timeout' in error_str or 'timed out' in error_str:
            return ErrorType.PAGE_TIMEOUT
        
        if 'element not found' in error_str or 'no such element' in error_str:
            return ErrorType.ELEMENT_NOT_FOUND
        
        if 'navigation' in error_str or 'navigate' in error_str or 'goto' in error_str:
            return ErrorType.NAVIGATION_ERROR
        
        if any(auth_keyword in error_str for auth_keyword in ['login', 'auth', 'unauthorized', '401', '403']):
            return ErrorType.AUTHENTICATION_ERROR
        
        if 'parse' in error_str or 'parsing' in error_str or 'malformed' in error_str:
            return ErrorType.CONTENT_PARSING_ERROR
        
        if any(net_keyword in error_str for net_keyword in ['network', 'connection', 'dns', 'socket']):
            return ErrorType.NETWORK_ERROR
        
        if any(browser_keyword in error_str for browser_keyword in ['browser', 'chrome', 'playwright']):
            return ErrorType.BROWSER_ERROR
        
        if 'validation' in error_str or 'validate' in error_str:
            return ErrorType.VALIDATION_ERROR
        
        return ErrorType.UNKNOWN_ERROR

    def _determine_error_severity(self, error_type: ErrorType, error_context: Dict[str, Any]) -> ErrorSeverity:
        """Determine error severity based on type and context."""
        high_severity_types = {
            ErrorType.AUTHENTICATION_ERROR,
            ErrorType.BROWSER_ERROR
        }
        
        medium_severity_types = {
            ErrorType.NAVIGATION_ERROR,
            ErrorType.PAGE_TIMEOUT,
            ErrorType.NETWORK_ERROR
        }
        
        if error_type in high_severity_types:
            return ErrorSeverity.HIGH
        elif error_type in medium_severity_types:
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW

    def _is_error_recoverable(self, error_type: ErrorType, severity: ErrorSeverity) -> bool:
        """Determine if an error is recoverable."""
        unrecoverable_types = {ErrorType.UNKNOWN_ERROR}
        critical_severity = {ErrorSeverity.CRITICAL}
        
        return error_type not in unrecoverable_types and severity not in critical_severity

    def _get_recommended_actions(self, error_type: ErrorType, patterns_matched: List[Dict]) -> List[str]:
        """Get recommended actions for error type."""
        actions = {
            ErrorType.NAVIGATION_ERROR: [
                "Retry navigation with longer timeout",
                "Check URL validity",
                "Verify network connectivity"
            ],
            ErrorType.PAGE_TIMEOUT: [
                "Increase page timeout",
                "Check page loading performance",
                "Verify server responsiveness"
            ],
            ErrorType.ELEMENT_NOT_FOUND: [
                "Verify element selector",
                "Wait for page to load completely",
                "Check if page structure changed"
            ],
            ErrorType.AUTHENTICATION_ERROR: [
                "Refresh authentication",
                "Check credentials",
                "Verify session validity"
            ]
        }
        
        return actions.get(error_type, ["Contact support for assistance"])

    async def _determine_recovery_strategy(
        self,
        error_analysis: Dict[str, Any],
        recovery_attempts: int,
        navigator: Optional[Any]
    ) -> Dict[str, Any]:
        """Determine the best recovery strategy for the error."""
        error_type = ErrorType(error_analysis['error_type'])
        strategy_config = self._recovery_strategies.get(error_type, self._recovery_strategies[ErrorType.UNKNOWN_ERROR])
        
        # Check if we've exceeded max retries
        if recovery_attempts >= strategy_config['max_retries']:
            # Use secondary strategy or abort
            strategy = strategy_config.get('secondary', RecoveryStrategy.ABORT)
        else:
            # Use primary strategy
            strategy = strategy_config['primary']
        
        return {
            'strategy': strategy.value,
            'max_retries': strategy_config['max_retries'],
            'retry_delay': strategy_config['retry_delay'],
            'current_attempts': recovery_attempts,
            'reason': f'Using {strategy.value} strategy for {error_type.value}'
        }

    async def _execute_retry_recovery(
        self,
        recovery_plan: Dict[str, Any],
        navigator: Optional[Any],
        error_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute retry recovery strategy."""
        retry_delay = recovery_plan.get('retry_delay', 2)
        
        if self.court_logger:
            self.court_logger.info(f"RECOVERY: Waiting {retry_delay} seconds before retry")
        
        await asyncio.sleep(retry_delay)
        
        return {
            'success': True,  # Retry success is determined by whether the retried operation succeeds
            'strategy': RecoveryStrategy.RETRY.value,
            'action_taken': f'Waited {retry_delay} seconds for retry',
            'next_step': 'Retry the failed operation'
        }

    async def _execute_skip_recovery(
        self,
        recovery_plan: Dict[str, Any],
        error_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute skip recovery strategy."""
        if self.court_logger:
            self.court_logger.info("RECOVERY: Skipping failed operation and continuing")
        
        return {
            'success': True,
            'strategy': RecoveryStrategy.SKIP.value,
            'action_taken': 'Skipped failed operation',
            'next_step': 'Continue with next operation'
        }

    async def _execute_rollback_recovery(
        self,
        recovery_plan: Dict[str, Any],
        navigator: Optional[Any],
        error_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute rollback recovery strategy."""
        if self.court_logger:
            self.court_logger.info("RECOVERY: Executing rollback recovery strategy")
        
        try:
            # Get rollback information from recovery plan
            rollback_url = recovery_plan.get('rollback_url')
            rollback_state = recovery_plan.get('rollback_state', {})
            
            # If navigator is available, attempt to navigate back to stable state
            if navigator and rollback_url:
                if self.court_logger:
                    self.court_logger.info(f"RECOVERY: Navigating back to stable URL: {rollback_url}")
                
                # Navigate to the rollback URL with timeout
                await asyncio.wait_for(
                    navigator.navigate_to_url(rollback_url),
                    timeout=15.0
                )
                
                # Wait for page to load
                await asyncio.wait_for(
                    asyncio.sleep(2),
                    timeout=5.0
                )
                
                # Verify we're at the expected state
                current_url = await asyncio.wait_for(
                    navigator.get_current_url(),
                    timeout=5.0
                )
                if rollback_url in current_url:
                    if self.court_logger:
                        self.court_logger.info("RECOVERY: Successfully rolled back to stable state")
                    
                    return {
                        'success': True,
                        'strategy': RecoveryStrategy.ROLLBACK.value,
                        'action_taken': f'Rolled back to {rollback_url}',
                        'rollback_url': rollback_url,
                        'current_state': rollback_state,
                        'next_step': 'Resume processing from stable checkpoint'
                    }
                else:
                    if self.court_logger:
                        self.court_logger.warning(f"RECOVERY: Rollback navigation may have failed. Expected: {rollback_url}, Got: {current_url}")
            
            # If no navigator or URL, attempt logical state rollback
            if rollback_state:
                if self.court_logger:
                    self.court_logger.info("RECOVERY: Restoring application state from checkpoint")
                
                # In a real implementation, this would restore application state
                # from the saved rollback_state dictionary
                restored_properties = []
                for key, value in rollback_state.items():
                    # This would restore actual state properties
                    restored_properties.append(f"{key}={value}")
                
                return {
                    'success': True,
                    'strategy': RecoveryStrategy.ROLLBACK.value,
                    'action_taken': f'Restored state: {", ".join(restored_properties)}',
                    'restored_state': rollback_state,
                    'next_step': 'Resume from restored checkpoint'
                }
            
            # Fallback: minimal rollback
            if self.court_logger:
                self.court_logger.info("RECOVERY: Performing minimal rollback (clearing error state)")
            
            return {
                'success': True,
                'strategy': RecoveryStrategy.ROLLBACK.value,
                'action_taken': 'Cleared error state, ready to retry',
                'next_step': 'Retry original operation'
            }
            
        except Exception as rollback_error:
            if self.court_logger:
                self.court_logger.error(f"RECOVERY: Rollback failed: {rollback_error}")
            
            return {
                'success': False,
                'strategy': RecoveryStrategy.ROLLBACK.value,
                'action_taken': 'Rollback attempt failed',
                'error': str(rollback_error),
                'next_step': 'Consider alternative recovery strategy'
            }

    async def _execute_restart_recovery(
        self,
        recovery_plan: Dict[str, Any],
        navigator: Optional[Any],
        error_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute restart recovery strategy."""
        # This would implement restart logic (browser, session, etc.)
        if self.court_logger:
            self.court_logger.info("RECOVERY: Initiating restart procedure")
        
        return {
            'success': True,
            'strategy': RecoveryStrategy.RESTART.value,
            'action_taken': 'Initiated restart procedure',
            'next_step': 'Resume from beginning of failed operation'
        }

    async def _execute_manual_intervention_recovery(
        self,
        recovery_plan: Dict[str, Any],
        error_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute manual intervention recovery strategy."""
        if self.court_logger:
            self.court_logger.error("RECOVERY: Manual intervention required")
        
        return {
            'success': False,
            'strategy': RecoveryStrategy.MANUAL_INTERVENTION.value,
            'action_taken': 'Flagged for manual intervention',
            'next_step': 'Human review and intervention required'
        }

    async def _update_circuit_breaker_on_failure(self):
        """Update circuit breaker state on failure."""
        self._circuit_breaker['failure_count'] += 1
        self._circuit_breaker['last_failure_time'] = datetime.now()
        
        if self._circuit_breaker['failure_count'] >= self._circuit_breaker['failure_threshold']:
            self._circuit_breaker['state'] = 'open'
            if self.court_logger:
                self.court_logger.warning(
                    f"CIRCUIT BREAKER: Opened due to {self._circuit_breaker['failure_count']} failures"
                )

    def check_circuit_breaker(self) -> Dict[str, Any]:
        """Check current circuit breaker state."""
        current_time = datetime.now()
        
        if self._circuit_breaker['state'] == 'open':
            if (self._circuit_breaker['last_failure_time'] and
                current_time - self._circuit_breaker['last_failure_time'] > self._circuit_breaker['reset_timeout']):
                self._circuit_breaker['state'] = 'half_open'
                if self.court_logger:
                    self.court_logger.info("CIRCUIT BREAKER: Moved to half-open state")
        
        return {
            'state': self._circuit_breaker['state'],
            'failure_count': self._circuit_breaker['failure_count'],
            'failure_threshold': self._circuit_breaker['failure_threshold'],
            'last_failure_time': self._circuit_breaker['last_failure_time'].isoformat() if self._circuit_breaker['last_failure_time'] else None,
            'reset_timeout_minutes': self._circuit_breaker['reset_timeout'].total_seconds() / 60
        }

    def reset_circuit_breaker(self) -> Dict[str, Any]:
        """Reset circuit breaker to closed state."""
        self._circuit_breaker.update({
            'failure_count': 0,
            'last_failure_time': None,
            'state': 'closed'
        })
        
        if self.court_logger:
            self.court_logger.info("CIRCUIT BREAKER: Reset to closed state")
        
        return {'status': 'reset', 'state': 'closed'}

    def get_error_statistics(self) -> Dict[str, Any]:
        """Get comprehensive error and recovery statistics."""
        if not self._error_history:
            return {
                'total_errors': 0,
                'recovery_success_rate': 0,
                'error_breakdown': {},
                'recent_errors': []
            }
        
        # Count by error type
        error_counts = {}
        recovery_counts = {}
        
        for error_record in self._error_history:
            error_type = error_record['error_type']
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
            
            if error_record.get('recovery_success'):
                recovery_counts[error_type] = recovery_counts.get(error_type, 0) + 1
        
        return {
            'total_errors': len(self._error_history),
            'recovery_statistics': self._recovery_statistics,
            'error_breakdown': error_counts,
            'recovery_breakdown': recovery_counts,
            'circuit_breaker_state': self.check_circuit_breaker(),
            'recent_errors': self._error_history[-10:] if len(self._error_history) > 10 else self._error_history
        }

    def clear_error_history(self) -> int:
        """Clear error history."""
        count = len(self._error_history)
        self._error_history.clear()
        return count