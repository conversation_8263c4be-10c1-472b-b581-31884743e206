"""
WorkflowOrchestrator with proper dependency injection.
This version uses DI container instead of manual instantiation.
"""
from typing import Any, Dict, Optional, List
from src.infrastructure.patterns.component_base import ComponentImplementation
from src.pacer.components.download.artifact_checker import DocketArtifactChecker


class WorkflowOrchestrator(ComponentImplementation):
    """
    Component for orchestrating the high-level processing workflow for a court.
    Uses dependency injection for all services.
    """

    def __init__(
        self,
        # Core dependency - the DocketOrchestrator from DI container
        docket_orchestrator: Any = None,
        # Optional facades for backward compatibility
        authentication_facade: Any = None,
        navigation_facade: Any = None,
        report_facade: Any = None,
        row_facade: Any = None,
        file_service: Any = None,
        ignore_download_service: Any = None,
        relevance_service: Any = None,
        artifact_checker: Any = None,
        # CRITICAL: Storage dependencies for passing to SequentialWorkflowManager
        pacer_repository: Any = None,
        async_dynamodb_storage: Any = None,
        # Logger and config
        logger: Any = None,
        config: Dict[str, Any] = None,
    ):
        super().__init__(logger, config)
        # Store the injected DocketOrchestrator
        self.docket_orchestrator = docket_orchestrator
        
        # Store optional facades
        self.auth_facade = authentication_facade
        self.nav_facade = navigation_facade
        self.report_facade = report_facade
        self.row_facade = row_facade
        self.file_service = file_service
        self.ignore_download_service = ignore_download_service
        self.relevance_service = relevance_service
        self.artifact_checker = artifact_checker
        
        # CRITICAL: Store storage dependencies for passing to SequentialWorkflowManager
        self.pacer_repository = pacer_repository
        self.async_dynamodb_storage = async_dynamodb_storage

    async def execute(self, data: Any) -> Any:
        """Main execution method for the workflow orchestrator."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """Execute the requested action."""
        action = data.get("action")
        
        if action == "process_single_court":
            return await self.process_single_court_task(**data)
        elif action == "process_multiple_dockets":
            return await self.process_multiple_dockets_for_court_task(**data)
        else:
            self.log_error(f"Unknown action: {action}")
            return {"status": "error", "message": f"Unknown action: {action}"}

    async def process_single_court_task(self, **kwargs) -> Dict[str, Any]:
        """
        Process a single docket for a court using injected DocketOrchestrator.
        The DocketOrchestrator already has all dependencies injected from the container.
        """
        court_id = kwargs.get("court_id")
        docket_num = kwargs.get("docket_num")
        page = kwargs.get("page")
        
        if not all([court_id, docket_num, page]):
            return {"status": "error", "message": "Missing required parameters"}
        
        # Create court-specific logger
        from src.utils.date import DateUtils, FORMAT_ISO
        iso_date = DateUtils.current_date(FORMAT_ISO)
        court_logger = self.create_court_logger(court_id, iso_date)
        
        log_prefix = f"[{court_id}][{docket_num}]"
        court_logger.info(f"{log_prefix} Starting single court task processing")
        
        # Check if DocketOrchestrator was injected
        if not self.docket_orchestrator:
            court_logger.error(f"{log_prefix} No DocketOrchestrator injected!")
            return {"status": "error", "message": "DocketOrchestrator not configured"}
        
        # The DocketOrchestrator already has all its dependencies from the container
        # We just need to override its logger with the court-specific one
        original_logger = self.docket_orchestrator.logger
        self.docket_orchestrator.logger = court_logger
        
        try:
            # Process the docket
            initial_details = {
                "court_id": court_id,
                "docket_num": docket_num,
                "case_name": kwargs.get("case_name", "")
            }
            
            result = await self.docket_orchestrator.process_docket({
                "page": page,
                "initial_details": initial_details
            })
            
            court_logger.info(f"{log_prefix} Completed processing")
            return result
            
        except Exception as e:
            court_logger.error(f"{log_prefix} Error: {e}")
            return {"status": "error", "message": str(e)}
            
        finally:
            # Restore original logger
            self.docket_orchestrator.logger = original_logger

    async def process_multiple_dockets_for_court_task(self, **kwargs) -> Dict[str, Any]:
        """Process multiple dockets for a court."""
        court_id = kwargs.get("court_id")
        dockets = kwargs.get("dockets", [])
        page = kwargs.get("page")
        
        if not all([court_id, dockets, page]):
            return {"status": "error", "message": "Missing required parameters"}
        
        results = []
        for docket in dockets:
            result = await self.process_single_court_task(
                court_id=court_id,
                docket_num=docket.get("docket_num"),
                case_name=docket.get("case_name", ""),
                page=page
            )
            results.append(result)
        
        return {"status": "success", "results": results}

    async def _check_log_exists(self, path: str) -> bool:
        """Check if a log file exists."""
        import os
        return os.path.exists(path)

    async def _load_docket_log(self, path: str) -> Dict[str, Any]:
        """Load a docket log file."""
        import json
        try:
            with open(path, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.log_error(f"Failed to load docket log: {e}")
            return {}

    # Service layer compatible interface methods
    async def execute_court_processing_workflow(self, 
                                              court_ids: List[str],
                                              iso_date: Optional[str] = None,
                                              start_date: Any = None,
                                              end_date: Any = None,
                                              **kwargs) -> Dict[str, Any]:
        """Execute court processing workflow (called by service layer)"""
        if not self.docket_orchestrator:
            self.log_error("No DocketOrchestrator available for court processing")
            return {"status": "error", "message": "DocketOrchestrator not configured"}
        
        # CRITICAL FIX: Extract browser context from kwargs and pass explicitly
        browser_context = kwargs.get('browser_context') or kwargs.get('context')
        
        # Remove context-related keys from kwargs to avoid parameter duplication
        clean_kwargs = {k: v for k, v in kwargs.items() if k not in ['browser_context', 'context']}
        
        # Use DocketOrchestrator to process courts with explicit context parameter
        return await self.docket_orchestrator.process_courts(
            court_ids=court_ids,
            context=browser_context,  # ✅ Explicitly pass browser context
            iso_date=iso_date,
            start_date=start_date,
            end_date=end_date,
            **clean_kwargs
        )
    
    async def execute_single_docket_workflow(self,
                                           court_id: str,
                                           docket_num: str,
                                           **kwargs) -> Dict[str, Any]:
        """Execute single docket workflow (called by service layer)"""
        if not self.docket_orchestrator:
            self.log_error("No DocketOrchestrator available for single docket processing")
            return {"status": "error", "message": "DocketOrchestrator not configured"}
        
        # Process single docket
        return await self.docket_orchestrator.process_docket(
            docket_num=docket_num,
            court_ids=[court_id],
            **kwargs
        )
    
    async def execute_standard_workflow(self, court_ids: List[str], **kwargs) -> Dict[str, Any]:
        """Execute standard workflow (called by service layer)"""
        # Browser context will be handled by execute_court_processing_workflow
        return await self.execute_court_processing_workflow(court_ids, **kwargs)
    
    async def execute_html_only_workflow(self, court_ids: List[str], **kwargs) -> Dict[str, Any]:
        """Execute HTML-only workflow (called by service layer)"""
        # Set html_only flag and execute (browser context will be handled by execute_court_processing_workflow)
        kwargs['html_only'] = True
        return await self.execute_court_processing_workflow(court_ids, **kwargs)
    
    async def execute_download_only_workflow(self, court_ids: List[str], **kwargs) -> Dict[str, Any]:
        """Execute download-only workflow (called by service layer)"""
        # Set download_only flag and execute (browser context will be handled by execute_court_processing_workflow)
        kwargs['download_only'] = True
        return await self.execute_court_processing_workflow(court_ids, **kwargs)