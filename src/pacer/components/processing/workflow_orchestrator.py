# /src/services/pacer/_processing_components/workflow_orchestrator.py
import logging
import json
import os
import threading
from typing import Any, Dict, Optional
from datetime import datetime, timedelta

from playwright.async_api import BrowserContext

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.pacer.components.browser.navigator import PacerNavigator
from src.pacer.components.authentication.ecf_login_handler import ECFLoginHandler
from src.pacer.components.navigation.page_navigator import PageNavigator
from src.pacer.components.download.artifact_checker import DocketArtifactChecker


class WorkflowOrchestrator(ComponentImplementation):
    """
    Component for orchestrating the high-level processing workflow for a court.
    It manages login, navigation, and report processing tasks.
    """

    def __init__(
        self,
        logger: Any = None,
        config: Dict[str, Any] = None,
        # Core dependency from DI container
        docket_orchestrator_factory: Any = None,  # Factory to create DocketOrchestrator
        # Storage dependencies for database access
        pacer_repository: Any = None,
        async_dynamodb_storage: Any = None,
        # Optional facades for backward compatibility
        authentication_facade: Any = None,
        navigation_facade: Any = None,
        report_facade: Any = None,
        row_facade: Any = None,
        file_service: Any = None,
        ignore_download_service: Any = None,
        relevance_service: Any = None,
        artifact_checker: Any = None,
    ):
        super().__init__(logger, config)
        self.docket_orchestrator_factory = docket_orchestrator_factory
        # Store storage dependencies for passing to DocketOrchestrator
        self.pacer_repository = pacer_repository
        self.async_dynamodb_storage = async_dynamodb_storage
        self.auth_facade = authentication_facade
        self.nav_facade = navigation_facade
        self.report_facade = report_facade
        self.row_facade = row_facade
        self.file_service = file_service
        self.ignore_download_service = ignore_download_service
        self.relevance_service = relevance_service
        self.artifact_checker = artifact_checker
    
    def _ensure_config_dict(self) -> Dict[str, Any]:
        """Convert config to dict if it's a string or None."""
        if isinstance(self.config, str):
            try:
                # Try to parse as JSON first
                return json.loads(self.config)
            except (json.JSONDecodeError, TypeError):
                # If not JSON, use empty dict
                if self.logger:
                    self.logger.warning(f"Config is string but not valid JSON, using empty dict: {self.config[:50] if self.config else ''}...")
                return {}
        elif self.config is None:
            return {}
        else:
            return self.config

    async def execute(self, data: Any) -> Any:
        """Main execution method for the workflow orchestrator."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a workflow orchestration action.
        """
        action = data.get("action")
        if action == "process_court_task":
            return await self.process_single_court_task(**data)
        elif action == "process_multi_docket_task":
            return await self.process_multiple_dockets_for_court_task(**data)
        else:
            raise ValueError(f"Unknown action for WorkflowOrchestrator: {action}")

    def _is_page_valid(self, page) -> bool:
        """
        Check if a page is still valid and can be used.

        Args:
            page: Playwright page object

        Returns:
            bool: True if page is valid, False otherwise
        """
        try:
            if not page:
                return False
            return not page.is_closed()
        except Exception:
            # If we can't even check if the page is closed, it's not valid
            return False

    async def _safe_page_wait(self, page, timeout_ms: int, log_prefix: str, operation_name: str = "operation", court_logger=None) -> bool:
        """
        Safely wait for a timeout on a page with proper error handling.

        Args:
            page: Playwright page object
            timeout_ms: Timeout in milliseconds
            log_prefix: Logging prefix for error messages
            operation_name: Name of the operation for logging
            court_logger: Court-specific logger instance

        Returns:
            bool: True if wait succeeded, False if page is invalid or wait failed
        """
        logger = court_logger or self.logger
        try:
            if not self._is_page_valid(page):
                logger.error(f"{log_prefix} Page is closed or invalid, cannot perform {operation_name}")
                return False

            await page.wait_for_timeout(timeout_ms)
            return True

        except Exception as e:
            logger.error(f"{log_prefix} Page timeout failed during {operation_name} (page may be closed): {e}")
            return False
    
    def _ensure_directories_exist(self, iso_date: str) -> None:
        """Ensure required data directories exist for the given date."""
        directories = [
            f"data/{iso_date}",
            f"data/{iso_date}/logs",
            f"data/{iso_date}/courts",
            f"data/{iso_date}/downloads"
        ]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            self.log_debug(f"Ensured directory exists: {directory}")
    
    def _get_docket_log_path(self, court_id: str, iso_date: str) -> str:
        """Get the path to the docket report log file for a specific court and date."""
        # Ensure the directory exists
        log_dir = f"data/{iso_date}/logs/docket_report"
        os.makedirs(log_dir, exist_ok=True)
        # Return path with correct naming convention - should be .json not .log
        return f"{log_dir}/{court_id.lower()}.json"
    
    async def _check_log_exists(self, path: str) -> bool:
        """Check if a docket report log file exists."""
        return os.path.exists(path)
    
    async def _load_docket_log(self, path: str) -> Dict[str, Any]:
        """Load existing docket report log from file."""
        try:
            with open(path, 'r') as f:
                data = json.load(f)
                self.log_info(f"Loaded docket log with {len(data.get('cases', []))} cases")
                return data
        except Exception as e:
            self.log_error(f"Failed to load docket log: {e}")
            return None

    async def process_single_court_task(self, **kwargs) -> Dict[str, Any]:
        """
        Processes a single court, handling login, navigation, and report processing.
        """
        court_id = kwargs.get("court_id")
        context = kwargs.get("context")
        iso_date = kwargs.get("iso_date")
        start_date_obj = kwargs.get("start_date_obj")
        end_date_obj = kwargs.get("end_date_obj")
        processor_config = kwargs.get("processor_config")
        relevance_engine = kwargs.get("relevance_engine")
        court_logger = kwargs.get("court_logger")
        
        # Create court logger if not provided
        if not court_logger:
            court_logger = self.create_court_logger(court_id, iso_date)
            court_logger.info(f"[{court_id.upper()}] Court-specific logger created at data/{iso_date}/logs/pacer/{court_id.lower()}.log")
        
        # Ensure directory structure exists
        import os
        reports_dir = f"data/{iso_date}/reports"
        logs_dir = f"data/{iso_date}/logs/pacer"
        dockets_dir = f"data/{iso_date}/dockets"
        os.makedirs(reports_dir, exist_ok=True)
        os.makedirs(logs_dir, exist_ok=True)
        os.makedirs(dockets_dir, exist_ok=True)
        
        log_prefix = f"[{court_id}] CourtTask:"
        court_logger.info(f"[{court_id.upper()}] Created required directories: reports, logs/pacer, dockets")
        
        # Validate required date objects
        if not start_date_obj or not end_date_obj:
            error_msg = f"Missing required date objects: start_date_obj={start_date_obj}, end_date_obj={end_date_obj}"
            court_logger.error(f"[{court_id.upper()}] Configuration error: Missing date objects - {error_msg}")
            raise ValueError(f"Configuration error: {error_msg}")
        
        # Use court logger for all operations in this workflow
        court_logger.info(f"[{court_id.upper()}] 🚀 Starting court processing workflow")

        result = {'court_id': court_id, 'status': 'failed', 'error': None}
        
        # CRITICAL FIX: Validate browser context before using
        if context is None:
            court_logger.error(f"[{court_id.upper()}] No browser context provided - cannot proceed")
            return {'court_id': court_id, 'status': 'failed', 'error': 'No browser context available', 'navigator': None}
        
        # Validate context is not closed
        try:
            # Check if context is valid by attempting to access its properties
            context_pages = len(context.pages)  # This will throw if context is closed
            court_logger.info(f"[{court_id.upper()}] Using valid browser context with {context_pages} existing pages")
        except Exception as context_error:
            court_logger.error(f"[{court_id.upper()}] Browser context is invalid or closed: {context_error}")
            return {'court_id': court_id, 'status': 'failed', 'error': f'Invalid browser context: {context_error}', 'navigator': None}
            
        # Create new page from valid context
        try:
            page = await context.new_page()
            court_logger.info(f"[{court_id.upper()}] Successfully created new page from browser context")
        except Exception as page_error:
            court_logger.error(f"[{court_id.upper()}] Failed to create new page: {page_error}")
            return {'court_id': court_id, 'status': 'failed', 'error': f'Failed to create page: {page_error}', 'navigator': None}
        
        # CRITICAL FIX: Validate page before creating navigator
        if page.is_closed():
            court_logger.error(f"[{court_id.upper()}] Newly created page is already closed")
            return {'court_id': court_id, 'status': 'failed', 'error': 'Created page is closed', 'navigator': None}
        
        # Create PacerNavigator with validated page
        try:
            config_dict = self._ensure_config_dict()
            navigator = PacerNavigator(
                page=page, 
                config=config_dict, 
                screenshot_dir=config_dict.get('screenshot_dir', './data/screenshots')
            )
            court_logger.info(f"[{court_id.upper()}] PacerNavigator created successfully")
        except Exception as nav_error:
            court_logger.error(f"[{court_id.upper()}] Failed to create PacerNavigator: {nav_error}")
            try:
                await page.close()
            except:
                pass
            return {'court_id': court_id, 'status': 'failed', 'error': f'Failed to create navigator: {nav_error}', 'navigator': None}

        try:
            # CRITICAL FIX: Validate navigator before authentication
            if not navigator or not navigator.is_ready:
                raise Exception(f"Navigator is not ready for authentication: {navigator}")
            
            # ROLLBACK FIX: Always perform authentication, never skip
            if not self.auth_facade:
                court_logger.warning(f"[{court_id.upper()}] AuthenticationFacade not available - creating manually")
                
                # ROLLBACK FIX: Create authentication facade manually if not available
                from src.pacer.facades.authentication_facade import AuthenticationFacade
                from src.pacer.components.authentication.login_handler import LoginHandler
                from src.pacer.components.authentication.ecf_login_handler import ECFLoginHandler
                
                # Create authentication components with proper config
                config_dict = self._ensure_config_dict()
                login_handler = LoginHandler(logger=court_logger, config=config_dict)
                ecf_login_handler = ECFLoginHandler(logger=court_logger, config=config_dict)
                
                # Create authentication facade
                self.auth_facade = AuthenticationFacade(
                    logger=court_logger,
                    config=config_dict,
                    login_handler=login_handler,
                    ecf_login_handler=ecf_login_handler
                )
                court_logger.info(f"[{court_id.upper()}] Created AuthenticationFacade manually")
            
            # CRITICAL FIX 1: Ensure directories exist
            self._ensure_directories_exist(iso_date)
            
            # CRITICAL FIX 2: Check for existing docket_report_log BEFORE authentication
            log_file_path = self._get_docket_log_path(court_id, iso_date)
            court_logger.info(f"[{court_id.upper()}] Checking for existing docket log at: {log_file_path}")
            court_logger.info(f"[{court_id.upper()}] Checking for existing docket log at: {log_file_path}")
            
            docket_log_data = None
            has_cases = False
            resume_mode = False
            
            if await self._check_log_exists(log_file_path):
                # PATH A: Resume from existing log
                court_logger.info(f"[{court_id.upper()}] Found existing docket_report_log - RESUMING from previous session")
                court_logger.info(f"[{court_id.upper()}] Found existing docket_report_log - RESUMING from previous session")
                docket_log_data = await self._load_docket_log(log_file_path)
                
                if docket_log_data and docket_log_data.get('cases'):
                    has_cases = True
                    resume_mode = True
                    court_logger.info(f"[{court_id.upper()}] Loaded {len(docket_log_data['cases'])} cases from existing log")
                    court_logger.info(f"[{court_id.upper()}] Loaded {len(docket_log_data['cases'])} cases from existing log")
                else:
                    court_logger.warning(f"[{court_id.upper()}] Log file exists but is empty or invalid, will generate new report")
                    court_logger.warning(f"[{court_id.upper()}] Log file exists but is empty or invalid, will generate new report")
                    docket_log_data = None
            
            # ROLLBACK FIX: Perform authentication with navigation context
            try:
                court_logger.info(f"[{court_id.upper()}] Starting ECF authentication (resume_mode: {resume_mode})")
                login_result = await self.auth_facade.execute({
                    "action": "login", 
                    "navigator": navigator, 
                    "court_id": court_id,
                    "resume_mode": resume_mode  # Pass context to authentication
                })
                
                # Validate login result
                if isinstance(login_result, dict):
                    login_success = login_result.get('success', False) or login_result.get('status') == 'success'
                else:
                    login_success = bool(login_result)
                
                if login_success:
                    court_logger.info(f"[{court_id.upper()}] ECF authentication completed successfully")
                    court_logger.info(f"[{court_id.upper()}] ECF authentication completed successfully")
                else:
                    court_logger.error(f"[{court_id.upper()}] ECF authentication failed: {login_result}")
                    court_logger.error(f"[{court_id.upper()}] ECF authentication failed: {login_result}")
                    
            except Exception as auth_error:
                court_logger.error(f"[{court_id.upper()}] Authentication error: {auth_error}")
                court_logger.error(f"[{court_id.upper()}] Authentication error: {auth_error}")
                login_success = False
                    
            if not login_success:
                raise Exception("ECF login sequence failed - cannot proceed without authentication")
            
            # Verify browser is still functional after authentication
            if not navigator.is_ready:
                raise Exception("Browser/navigator is no longer ready after authentication")
            
            court_logger.info(f"[{court_id.upper()}] Authentication completed - browser ready for court processing")
            court_logger.info(f"[{court_id.upper()}] Authentication completed - browser ready for court processing")

            # CRITICAL FIX: Navigate based on session type
            if resume_mode and docket_log_data:
                # PATH A: Resume from existing log - navigate to Query page
                court_logger.info(f"[{court_id.upper()}] Using existing docket_report_log - SKIPPING report generation")
                has_cases = True  # We have cases from the existing log
                
                # Navigate directly to Query page when resuming
                court_logger.info(f"[{court_id.upper()}] Navigating to Query page for case processing...")
                if self.nav_facade:
                    await self.nav_facade.execute({
                        "action": "go_to_query_page", "navigator": navigator, "court_id": court_id
                    })
                    court_logger.info(f"[{court_id.upper()}] Navigation to query page successful - ready for DocketOrchestrator processing")
                else:
                    court_logger.warning(f"[{court_id.upper()}] NavigationFacade not available - continuing without navigation")
            else:
                # PATH B: Start new - Generate civil cases report
                court_logger.info(f"[{court_id.upper()}] No existing docket_report_log - STARTING NEW session")
                court_logger.info(f"[{court_id.upper()}] No existing docket_report_log - STARTING NEW session")
                
                # Navigate to Civil Cases Report page (NOT Query page) for new sessions
                if not self.nav_facade:
                    court_logger.warning(f"[{court_id.upper()}] NavigationFacade not available - skipping navigation")
                    court_logger.warning(f"[{court_id.upper()}] NavigationFacade not available - skipping navigation")
                else:
                    await self.nav_facade.execute({
                        "action": "navigate_to_civil_cases_report", "navigator": navigator, "court_id": court_id
                    })
                court_logger.info(f"[{court_id.upper()}] Navigation to civil cases report page successful.")
                court_logger.info(f"[{court_id.upper()}] Navigation to civil cases report page successful.")

                # Report Generation
                if not self.report_facade:
                    court_logger.warning(f"[{court_id.upper()}] ReportFacade not available - marking as completed without report")
                    result['status'] = 'completed'
                    result['error'] = 'No report facade available'
                    result['navigator'] = navigator
                    return result
                
                has_cases = await self.report_facade.execute({
                    "action": "generate_civil_cases_report",
                    "navigator": navigator,
                    "court_id": court_id,
                    "from_date_str": start_date_obj.strftime('%m/%d/%y'),
                    "to_date_str": end_date_obj.strftime('%m/%d/%y'),
                    "ignore_download_service": self.ignore_download_service,
                    "iso_date": iso_date,  # Pass iso_date to report generation
                })
                
                if has_cases:
                    court_logger.info(f"[{court_id.upper()}] Report generated successfully, cases found.")
            
            # Check if we should continue processing
            if not has_cases and not docket_log_data:
                result['status'] = 'success_no_cases'
                return result

            # CRITICAL FIX: Single path processing based on session type
            if resume_mode and docket_log_data and docket_log_data.get('cases'):
                # PATH A: Resume from existing docket log
                row_counts = await self._process_resume_from_docket_log(
                    docket_log_data=docket_log_data,
                    navigator=navigator,
                    context=context,
                    court_id=court_id,
                    iso_date=iso_date,
                    start_date_obj=start_date_obj,
                    end_date_obj=end_date_obj,
                    processor_config=processor_config,
                    log_prefix=log_prefix
                )
            else:
                # PATH B: Process new session from report page
                row_counts = await self._process_new_session_from_report(
                    page=page,
                    navigator=navigator,
                    context=context,
                    court_id=court_id,
                    iso_date=iso_date,
                    log_prefix=log_prefix
                )

            result.update({
                'status': 'success', 
                'navigator': navigator,  # CRITICAL FIX: Include navigator in return for docket processing phase
                **row_counts
            })
            return result

        except Exception as e:
            result['error'] = str(e)
            result['navigator'] = navigator if 'navigator' in locals() else None  # Include navigator even on error
            court_logger.error(f"[{court_id.upper()}] Workflow failed: {e}", exc_info=True)
            return result
        finally:
            # CRITICAL FIX: Ensure page remains available for subsequent processing
            # The page and context will be managed by the calling orchestrator
            if navigator and hasattr(navigator, 'page') and navigator.page:
                try:
                    # Ensure page is in a clean state for next operations
                    current_url = navigator.page.url
                    court_logger.debug(f"[{court_id.upper()}] Workflow completed, page URL: {current_url}")
                except Exception as page_e:
                    court_logger.warning(f"[{court_id.upper()}] Could not access page after workflow completion: {page_e}")
            pass

    async def process_multiple_dockets_for_court_task(self, **kwargs) -> Dict[str, Any]:
        """
        Process multiple specific dockets for a court without running a report.
        This method handles the scenario where we already have a list of dockets to process.
        """
        court_id = kwargs.get("court_id")
        context = kwargs.get("context")
        iso_date = kwargs.get("iso_date")
        dockets_to_process = kwargs.get("dockets", [])
        processor_config = kwargs.get("processor_config", {})
        court_logger = kwargs.get("court_logger")
        
        log_prefix = f"[{court_id}] MultiDocketTask:"
        
        # Create court logger if not provided
        if not court_logger:
            court_logger = self.create_court_logger(court_id, iso_date)
            court_logger.info(f"[{court_id.upper()}] Created court-specific logger")
        
        court_logger.info(f"[{court_id.upper()}] Starting multi-docket processing for {len(dockets_to_process)} dockets")
        
        result = {'court_id': court_id, 'status': 'failed', 'error': None, 'dockets_processed': 0}
        
        # CRITICAL: Validate browser context is provided
        if context is None:
            court_logger.error(f"[{court_id.upper()}] No browser context provided - cannot proceed with unified processing")
            return {'court_id': court_id, 'status': 'failed', 'error': 'No browser context available'}
        
        # Validate context is not closed
        try:
            context_pages = len(context.pages)
            court_logger.info(f"[{court_id.upper()}] Using valid browser context with {context_pages} existing pages")
        except Exception as context_error:
            court_logger.error(f"[{court_id.upper()}] Browser context is invalid or closed: {context_error}")
            return {'court_id': court_id, 'status': 'failed', 'error': f'Invalid browser context: {context_error}'}
        
        try:
            # Create navigator for this court's docket processing
            page = await context.new_page()
            from src.pacer.components.browser.navigator import PacerNavigator
            navigator = PacerNavigator(
                page=page,
                config=self._ensure_config_dict(),
                screenshot_dir=self.config.get('screenshot_dir', './data/screenshots') if self.config else './data/screenshots'
            )
            
            court_logger.info(f"[{court_id.upper()}] Created navigator for multi-docket processing")
            
            # Authenticate if needed
            if self.auth_facade:
                court_logger.info(f"[{court_id.upper()}] Starting ECF authentication")
                login_result = await self.auth_facade.execute({
                    "action": "login",
                    "navigator": navigator,
                    "court_id": court_id
                })
                
                login_success = login_result.get('success', False) if isinstance(login_result, dict) else bool(login_result)
                if not login_success:
                    court_logger.error(f"[{court_id.upper()}] ECF authentication failed")
                    return {'court_id': court_id, 'status': 'failed', 'error': 'Authentication failed'}
            
            # Use DocketOrchestrator to process the specific dockets with the browser context
            if self.docket_orchestrator_factory:
                from src.pacer.facades.docket_orchestrator import DocketOrchestrator
                config_dict = self._ensure_config_dict()
                docket_orchestrator = DocketOrchestrator(
                    logger=court_logger, 
                    config=config_dict,
                    # CRITICAL FIX: Pass storage dependencies
                    pacer_repository=self.pacer_repository,
                    async_dynamodb_storage=self.async_dynamodb_storage
                )
                
                # Process dockets through unified workflow with browser context
                workflow_config = {
                    "iso_date": iso_date,
                    "browser_context": context,  # Pass the browser context
                    **processor_config
                }
                
                processing_result = await docket_orchestrator.process_dockets_for_court(
                    court_id=court_id,
                    discovered_dockets=dockets_to_process,
                    workflow_config=workflow_config,
                    navigator=navigator,
                    context=context  # CRITICAL: Pass browser context for unified processing
                )
                
                # Convert result to expected format
                if isinstance(processing_result, list):
                    successful_dockets = [d for d in processing_result if d and d.get('status') == 'success']
                    failed_dockets = len(processing_result) - len(successful_dockets)
                else:
                    successful_dockets = []
                    failed_dockets = len(dockets_to_process)
                
                result = {
                    'court_id': court_id,
                    'status': 'success' if successful_dockets else 'failed',
                    'dockets_processed': len(successful_dockets),
                    'dockets_failed': failed_dockets,
                    'total_dockets': len(dockets_to_process)
                }
                
                court_logger.info(f"[{court_id.upper()}] Multi-docket processing completed: {len(successful_dockets)}/{len(dockets_to_process)} successful")
            else:
                court_logger.error(f"[{court_id.upper()}] DocketOrchestrator not available")
                result['error'] = 'DocketOrchestrator not available'
            
            return result
            
        except Exception as e:
            court_logger.error(f"[{court_id.upper()}] Multi-docket processing failed: {e}", exc_info=True)
            result['error'] = str(e)
            return result
        finally:
            # Clean up navigator page if created
            if 'page' in locals() and page and not page.is_closed():
                try:
                    await page.close()
                except:
                    pass

    async def _pre_query_validation(
        self, 
        case: Dict[str, Any], 
        court_id: str, 
        docket_num: str, 
        iso_date: str, 
        log_prefix: str
    ) -> bool:
        """
        Pre-query validation checks to determine if a case should be processed.
        
        Implements the validation workflow:
        1. Call relevance check first
        2. Route non-relevant cases appropriately (skip processing)
        3. Call artifact/dedup check for relevant cases
        4. Skip query if artifact exists (within 7 days)
        5. Only proceed to query for new, relevant cases
        
        Args:
            case: Case data dictionary
            court_id: Court identifier
            docket_num: Docket number
            iso_date: Processing date
            log_prefix: Logging prefix
            
        Returns:
            bool: True if case should proceed to query, False if should skip
        """
        # Create court logger for this validation
        court_logger = self.create_court_logger(court_id, iso_date)
        
        court_logger.debug(f"[{court_id.upper()}] Starting pre-query validation for {docket_num}")
        
        # STEP 1: Relevance check first
        if self.relevance_service:
            try:
                court_logger.debug(f"[{court_id.upper()}] Checking case relevance for {docket_num}")
                
                relevance_result = await self.relevance_service.execute({
                    "action": "determine_case_relevance",
                    "case_details": case,
                    "court_id": court_id,
                    "docket_num": docket_num
                })
                
                is_relevant = relevance_result.get('is_relevant', False)
                relevance_reason = relevance_result.get('relevance_reason', 'unknown')
                
                if not is_relevant:
                    court_logger.info(
                        f"{log_prefix} SKIP - Case {docket_num} not relevant: {relevance_reason}"
                    )
                    return False
                
                court_logger.info(
                    f"{log_prefix} Case {docket_num} is relevant: {relevance_reason}"
                )
                
            except Exception as e:
                court_logger.error(f"[{court_id.upper()}] Error checking relevance for {docket_num}: {e}")
                # On error, assume relevant and continue (fail-open)
                court_logger.warning(f"[{court_id.upper()}] Proceeding with {docket_num} due to relevance check error")
        else:
            court_logger.warning(f"[{court_id.upper()}] No relevance service - assuming {docket_num} is relevant")
        
        # STEP 2: Artifact/deduplication check for relevant cases
        if self.artifact_checker is not None:
            try:
                court_logger.debug(f"[{court_id.upper()}] Checking artifacts for {docket_num}")
                
                # Get versus/title from case data
                versus = case.get('versus', case.get('title', case.get('case_title', '')))
                
                should_download, reason = await self.artifact_checker.execute({
                    "action": "should_download_docket",
                    "court_id": court_id,
                    "docket_num": docket_num,
                    "versus": versus,
                    "iso_date": iso_date
                })
                
                if not should_download:
                    # Check if this is due to recent processing (within 7 days)
                    if self._is_recent_artifact(reason, court_id, docket_num, versus, iso_date):
                        court_logger.info(
                            f"{log_prefix} SKIP - Case {docket_num} processed recently: {reason}"
                        )
                        return False
                    else:
                        # Artifact exists but not recent, log but continue
                        court_logger.info(
                            f"{log_prefix} Artifact exists but not recent for {docket_num}: {reason}"
                        )
                
                court_logger.debug(f"[{court_id.upper()}] Case {docket_num} needs processing: {reason}")
                
            except Exception as e:
                court_logger.error(f"[{court_id.upper()}] Error checking artifacts for {docket_num}: {e}")
                # On error, assume we should process (fail-open)
                court_logger.warning(f"[{court_id.upper()}] Proceeding with {docket_num} due to artifact check error")
        else:
            court_logger.warning(f"[{court_id.upper()}] No artifact checker - assuming {docket_num} needs processing")
        
        court_logger.info(f"[{court_id.upper()}] Pre-query validation passed for {docket_num} - proceeding to query")
        return True
    
    def _is_on_docket_sheet_page(self, navigator: Any) -> bool:
        """
        Check if the current page is a docket sheet page.
        
        Args:
            navigator: PacerNavigator instance
            
        Returns:
            bool: True if on docket sheet page, False otherwise
        """
        try:
            current_url = navigator.page.url
            
            # Check for DktRpt.pl in URL (main indicator)
            if "DktRpt.pl" in current_url:
                self.log_debug(f"Detected docket sheet page by URL: {current_url}")
                return True
            
            # Check for other docket sheet URL patterns
            docket_patterns = [
                "/cgi-bin/DktRpt",
                "DocketReport",
                "docket_report"
            ]
            
            for pattern in docket_patterns:
                if pattern in current_url:
                    self.log_debug(f"Detected docket sheet page by pattern '{pattern}' in URL: {current_url}")
                    return True
            
            return False
            
        except Exception as e:
            self.log_error(f"Error checking if on docket sheet page: {e}")
            return False
    
    def _is_recent_artifact(
        self, 
        reason: str, 
        court_id: str, 
        docket_num: str, 
        versus: str, 
        iso_date: str
    ) -> bool:
        """
        Check if an artifact was created recently (within 7 days).
        
        Args:
            reason: Reason from artifact checker
            court_id: Court identifier
            docket_num: Docket number
            versus: Case title
            iso_date: Current processing date
            
        Returns:
            bool: True if artifact is recent (within 7 days)
        """
        try:
            # Generate the expected artifact paths
            from src.transformer.components.file.file_handler_utils import FileHandlerUtils
            
            file_utils = FileHandlerUtils(self.logger, self.config)
            base_filename = file_utils.create_filename({
                'court_id': court_id.lower(),
                'docket_num': docket_num,
                'versus': versus
            })
            
            # Check various artifact types
            base_dir = f"data/{iso_date}/dockets"
            artifact_extensions = ['.json', '.pdf', '.zip', '.md', '.html']
            
            current_date = datetime.strptime(iso_date, '%Y%m%d')
            seven_days_ago = current_date - timedelta(days=7)
            
            for ext in artifact_extensions:
                artifact_path = os.path.join(base_dir, f"{base_filename}{ext}")
                
                if os.path.exists(artifact_path):
                    # Get file modification time
                    mod_time = datetime.fromtimestamp(os.path.getmtime(artifact_path))
                    
                    if mod_time >= seven_days_ago:
                        self.log_debug(
                            f"Found recent artifact {artifact_path} modified on {mod_time}"
                        )
                        return True
            
            # No recent artifacts found
            return False
            
        except Exception as e:
            self.log_error(f"Error checking artifact recency: {e}")
            # On error, assume not recent (fail-open for processing)
            return False
    
    async def _process_resume_from_docket_log(
        self,
        docket_log_data: Dict[str, Any],
        navigator: Any,
        context: Any,
        court_id: str,
        iso_date: str,
        start_date_obj: Any,
        end_date_obj: Any,
        processor_config: Dict[str, Any],
        log_prefix: str
    ) -> Dict[str, Any]:
        """
        Process cases from loaded docket_report_log (resume path).
        
        Args:
            docket_log_data: Loaded docket log data
            navigator: PacerNavigator instance
            context: Browser context
            court_id: Court identifier
            iso_date: Processing date
            start_date_obj: Start date object
            end_date_obj: End date object
            processor_config: Processor configuration
            log_prefix: Logging prefix
            
        Returns:
            Row counts dictionary with processing statistics
        """
        court_logger.info(f"[{court_id.upper()}] Processing {len(docket_log_data['cases'])} cases from loaded docket log")
        
        # ARCHITECTURAL FIX: Process loaded dockets SEQUENTIALLY within court context
        # Instead of using DocketOrchestrator with separate processing, handle sequentially
        court_logger.info(f"[{court_id.upper()}] Processing {len(docket_log_data['cases'])} loaded dockets SEQUENTIALLY")
        
        processing_result = []
        successful_count = 0
        failed_count = 0
        
        try:
            for i, case_data in enumerate(docket_log_data['cases']):
                try:
                    docket_num = case_data.get('docket_num', f'case_{i+1}')
                    case_log_prefix = f"{log_prefix}[{docket_num}]({i+1}/{len(docket_log_data['cases'])})"
                    
                    court_logger.info(f"{case_log_prefix} Starting SEQUENTIAL resume processing")
                    
                    # Navigate to query page if needed  
                    if self.nav_facade:
                        await self.nav_facade.execute({
                            "action": "go_to_query_page",
                            "navigator": navigator,
                            "court_id": court_id
                        })
                    
                    # Execute complete case query workflow to get to docket sheet
                    if self.nav_facade:
                        query_success = await self.nav_facade.execute({
                            "action": "execute_complete_case_query_workflow",
                            "navigator": navigator,
                            "court_id": court_id,
                            "case_number": docket_num
                        })
                        
                        if not query_success:
                            court_logger.error(f"{case_log_prefix} Resume query workflow failed, skipping")
                            failed_count += 1
                            continue
                    
                    # Wait for page to stabilize
                    await navigator.page.wait_for_load_state('networkidle', timeout=10000)
                    
                    # Get HTML content from docket sheet
                    html_content = await navigator.page.content()
                    if not html_content:
                        court_logger.error(f"{case_log_prefix} No HTML content retrieved")
                        failed_count += 1
                        continue
                    
                    # Prepare initial details with loaded case data
                    initial_details = {
                        "court_id": court_id,
                        "iso_date": iso_date,
                        "_processing_source": "workflow_orchestrator_resume",
                        **case_data  # Include all loaded case data
                    }
                    
                    # Use DocketOrchestrator for single docket processing
                    from src.pacer.facades.docket_orchestrator import DocketOrchestrator
                    config_dict = self._ensure_config_dict()
                    docket_orchestrator = DocketOrchestrator(
                        logger=court_logger, 
                        config=config_dict,
                        # CRITICAL FIX: Pass storage dependencies
                        pacer_repository=self.pacer_repository,
                        async_dynamodb_storage=self.async_dynamodb_storage
                    )
                    
                    # Process single docket with current page state
                    docket_result = await docket_orchestrator.execute({
                        "action": "process_docket",
                        "page": navigator.page,
                        "initial_details": initial_details
                    })
                    
                    if docket_result:
                        processing_result.append({
                            "status": "success",
                            "docket_num": docket_num,
                            "result": docket_result
                        })
                        successful_count += 1
                        court_logger.info(f"{case_log_prefix} SEQUENTIAL resume processing completed successfully")
                    else:
                        processing_result.append({
                            "status": "failed",
                            "docket_num": docket_num,
                            "error": "DocketOrchestrator returned None"
                        })
                        failed_count += 1
                        court_logger.warning(f"{case_log_prefix} SEQUENTIAL resume processing failed")
                
                except Exception as docket_error:
                    docket_num = case_data.get('docket_num', f'case_{i+1}')
                    case_log_prefix = f"{log_prefix}[{docket_num}]({i+1}/{len(docket_log_data['cases'])})"
                    court_logger.error(f"{case_log_prefix} SEQUENTIAL resume processing error: {docket_error}", exc_info=True)
                    processing_result.append({
                        "status": "failed",
                        "docket_num": docket_num,
                        "error": str(docket_error)
                    })
                    failed_count += 1
            
            court_logger.info(f"[{court_id.upper()}] SEQUENTIAL resume processing completed: {successful_count} successful, {failed_count} failed")
            successful_dockets = [r for r in processing_result if r.get('status') == 'success']
            
            # Results are already in the correct format from sequential processing
            failed_dockets = failed_count
            
            row_counts = {
                'total_rows': len(docket_log_data['cases']),
                'jobs_created': len(processing_result) if isinstance(processing_result, list) else 0,
                'success_downloaded': len(successful_dockets),
                'failed': failed_dockets,
                'status': 'success' if successful_dockets else 'partial' if processing_result else 'failed'
            }
            
            court_logger.info(f"[{court_id.upper()}] Resume processing completed: {len(successful_dockets)} successful, {failed_dockets} failed")
            return row_counts
            
        except Exception as e:
            court_logger.error(f"[{court_id.upper()}] Error in resume processing: {e}", exc_info=True)
            return {
                'total_rows': len(docket_log_data['cases']),
                'jobs_created': 0,
                'success_downloaded': 0,
                'failed': len(docket_log_data['cases']),
                'status': 'failed',
                'error': str(e)
            }
    
    async def create_browser_context_with_download_path(self, 
                                                       browser_service,
                                                       court_id: str,
                                                       iso_date: str,
                                                       operation_type: str = "report"):
        """
        Create a browser context with appropriate download path for court operations.
        
        Args:
            browser_service: Browser service instance
            court_id: Court identifier (e.g., 'cand', 'nysd')
            iso_date: ISO date string for directory organization  
            operation_type: Type of operation ('report' or 'docket_log')
            
        Returns:
            BrowserContext: Browser context with configured download path
        """
        # Create court logger early since we have court_id
        court_logger = self.create_court_logger(court_id, iso_date)
        
        # Log context creation
        court_logger.info(f"[{court_id.upper()}] Creating browser context for {operation_type} operation")
        
        try:
            # Create download directory structure
            import os
            base_dir = os.path.join("data", iso_date)
            
            if operation_type == "docket_log":
                download_dir = os.path.join(base_dir, "dockets", court_id.lower())
            else:  # report or other operations
                download_dir = os.path.join(base_dir, "reports", court_id.lower())
            
            os.makedirs(download_dir, exist_ok=True)
            court_logger.info(f"[{court_id.upper()}] Created download directory: {download_dir}")
            
            # Get browser context with download path
            context = await browser_service.get_context(download_path=download_dir)
            court_logger.info(f"[{court_id.upper()}] Browser context created successfully with download path")
            
            return context
            
        except Exception as e:
            court_logger.error(f"[{court_id.upper()}] Error creating browser context: {e}", exc_info=True)
            raise
    
    async def _process_new_session_from_report(
        self,
        page: Any,
        navigator: Any,
        context: Any,
        court_id: str,
        iso_date: str,
        log_prefix: str
    ) -> Dict[str, Any]:
        """
        Process new session by extracting cases from report page.
        
        Args:
            page: Playwright page object
            navigator: PacerNavigator instance
            context: Browser context
            court_id: Court identifier
            iso_date: Processing date
            log_prefix: Logging prefix
            
        Returns:
            Row counts dictionary with processing statistics
        """
        court_logger.info(f"[{court_id.upper()}] Processing report rows from page")
        
        if not self.row_facade:
            court_logger.warning(f"[{court_id.upper()}] RowFacade not available - cannot extract cases")
            return {
                "total_rows": 0,
                "jobs_created": 0,
                "success_downloaded": 0,
                "failed": 0,
                "status": "failed",
                "error": "RowFacade not available"
            }
        
        try:
            # First extract the cases from the civil cases report
            extracted_cases = await self.row_facade.execute({
                "action": "extract_cases_from_report",
                "page": page,
                "court_id": court_id
            })
            
            if not extracted_cases:
                court_logger.info(f"[{court_id.upper()}] No cases extracted from civil cases report")
                return {
                    "total_rows": 0,
                    "jobs_created": 0,
                    "success_downloaded": 0,
                    "failed": 0,
                    "status": "success_no_cases"
                }
            
            court_logger.info(f"[{court_id.upper()}] Extracted {len(extracted_cases)} cases from civil cases report")
            
            # ARCHITECTURAL FIX: Process dockets SEQUENTIALLY within court context
            # Instead of delegating to DocketOrchestrator with separate jobs,
            # process each docket sequentially in the SAME browser context
            court_logger.info(f"[{court_id.upper()}] Processing {len(extracted_cases)} dockets SEQUENTIALLY in court context")
            
            processing_result = []
            successful_count = 0
            failed_count = 0
            
            for i, case_data in enumerate(extracted_cases):
                try:
                    docket_num = case_data.get('docket_num', f'case_{i+1}')
                    case_log_prefix = f"{log_prefix}[{docket_num}]({i+1}/{len(extracted_cases)})"
                    
                    court_logger.info(f"{case_log_prefix} Starting SEQUENTIAL docket processing")
                    
                    # Navigate to query page if needed
                    if self.nav_facade:
                        await self.nav_facade.execute({
                            "action": "go_to_query_page",
                            "navigator": navigator,
                            "court_id": court_id
                        })
                    
                    # Execute complete case query workflow to get to docket sheet
                    if self.nav_facade:
                        query_success = await self.nav_facade.execute({
                            "action": "execute_complete_case_query_workflow",
                            "navigator": navigator,
                            "court_id": court_id,
                            "case_number": docket_num
                        })
                        
                        if not query_success:
                            court_logger.error(f"{case_log_prefix} Query workflow failed, skipping")
                            failed_count += 1
                            continue
                    
                    # Wait for page to stabilize
                    await navigator.page.wait_for_load_state('networkidle', timeout=10000)
                    
                    # Get HTML content from docket sheet
                    html_content = await navigator.page.content()
                    if not html_content:
                        court_logger.error(f"{case_log_prefix} No HTML content retrieved")
                        failed_count += 1
                        continue
                    
                    # Prepare initial details with case data
                    initial_details = {
                        "court_id": court_id,
                        "iso_date": iso_date,
                        "_processing_source": "workflow_orchestrator_sequential",
                        **case_data  # Include all extracted case data
                    }
                    
                    # Use DocketOrchestrator for single docket processing
                    from src.pacer.facades.docket_orchestrator import DocketOrchestrator
                    config_dict = self._ensure_config_dict()
                    docket_orchestrator = DocketOrchestrator(
                        logger=court_logger, 
                        config=config_dict,
                        # CRITICAL FIX: Pass storage dependencies
                        pacer_repository=self.pacer_repository,
                        async_dynamodb_storage=self.async_dynamodb_storage
                    )
                    
                    # Process single docket with current page state
                    docket_result = await docket_orchestrator.execute({
                        "action": "process_docket",
                        "page": navigator.page,
                        "initial_details": initial_details
                    })
                    
                    if docket_result:
                        processing_result.append({
                            "status": "success",
                            "docket_num": docket_num,
                            "result": docket_result
                        })
                        successful_count += 1
                        court_logger.info(f"{case_log_prefix} SEQUENTIAL processing completed successfully")
                    else:
                        processing_result.append({
                            "status": "failed",
                            "docket_num": docket_num,
                            "error": "DocketOrchestrator returned None"
                        })
                        failed_count += 1
                        court_logger.warning(f"{case_log_prefix} SEQUENTIAL processing failed")
                
                except Exception as docket_error:
                    docket_num = case_data.get('docket_num', f'case_{i+1}')
                    case_log_prefix = f"{log_prefix}[{docket_num}]({i+1}/{len(extracted_cases)})"
                    court_logger.error(f"{case_log_prefix} SEQUENTIAL processing error: {docket_error}", exc_info=True)
                    processing_result.append({
                        "status": "failed",
                        "docket_num": docket_num,
                        "error": str(docket_error)
                    })
                    failed_count += 1
            
            court_logger.info(f"[{court_id.upper()}] SEQUENTIAL processing completed: {successful_count} successful, {failed_count} failed")
            successful_dockets = [r for r in processing_result if r.get('status') == 'success']
            
            # Results are already in the correct format from sequential processing
            failed_dockets = failed_count
            
            row_counts = {
                "total_rows": len(extracted_cases),
                "jobs_created": len(processing_result) if isinstance(processing_result, list) else 0,
                "success_downloaded": len(successful_dockets),
                "success_html_only": 0,
                "success_no_download": 0,
                "skipped_relevance": 0,
                "skipped_exists": 0,
                "failed": failed_dockets,
                "skipped_date_range": 0,
                "status": 'success' if successful_dockets else 'partial' if processing_result else 'failed'
            }
            
            court_logger.info(f"[{court_id.upper()}] New session processing completed: {len(successful_dockets)} successful, {failed_dockets} failed")
            return row_counts
            
        except Exception as e:
            court_logger.error(f"[{court_id.upper()}] Error in new session processing: {e}", exc_info=True)
            return {
                "total_rows": 0,
                "jobs_created": 0,
                "success_downloaded": 0,
                "failed": 0,
                "status": "failed",
                "error": str(e)
            }

    async def _process_docket_sheet_after_step_6(
        self,
        navigator: Any,
        case_details: Dict[str, Any],
        court_id: str,
        docket_num: str,
        iso_date: str,
        log_prefix: str
    ) -> Optional[Dict[str, Any]]:
        """
        Process docket sheet after Step 6 navigation workflow completion.
        
        This method implements the critical missing piece that processes the 
        docket sheet HTML after the navigation workflow has successfully 
        navigated to the docket page and clicked "Run Report".
        
        Args:
            navigator: PacerNavigator instance
            case_details: Case details dictionary
            court_id: Court identifier
            docket_num: Docket number
            iso_date: Processing date
            log_prefix: Logging prefix
            
        Returns:
            Processed case details or None if failed
        """
        # Create court logger for this processing
        court_logger = self.create_court_logger(court_id, iso_date)
        
        try:
            # Get the current page from navigator
            page = navigator.page
            
            if not page:
                court_logger.error(f"[{court_id.upper()}] No page available for docket processing")
                return None
            
            # Wait for the page to be fully loaded and stable
            try:
                await page.wait_for_load_state('networkidle', timeout=10000)
                court_logger.info(f"[{court_id.upper()}] Page load state reached networkidle")
            except Exception as wait_e:
                court_logger.warning(f"[{court_id.upper()}] Wait for networkidle timed out, continuing: {wait_e}")
            
            # Additional wait to ensure page is completely stable
            if not await self._safe_page_wait(page, 1000, log_prefix, "docket sheet stabilization", court_logger):
                return None
            
            # Get HTML content from the docket sheet page
            try:
                html_content = await page.content()
                if not html_content:
                    court_logger.error(f"[{court_id.upper()}] Empty HTML content retrieved from docket sheet")
                    return None
                    
            except Exception as html_e:
                court_logger.error(f"[{court_id.upper()}] Error retrieving HTML content: {html_e}")
                return None
            
            # Prepare initial details for processing
            initial_details = {
                "court_id": court_id,
                "docket_num": docket_num,
                "versus": case_details.get("versus", case_details.get("title", "")),
                "filing_date": case_details.get("filing_date", case_details.get("filed_date", "")),
                "iso_date": iso_date,
                "_processing_source": "step_6_navigation",
                **case_details
            }
            
            # Use SingleDocketProcessor if available to process the docket sheet
            if hasattr(self, '_single_docket_processor') and self._single_docket_processor:
                try:
                    result = await self._single_docket_processor.execute({
                        "action": "process_docket",
                        "case_page": page,
                        "initial_details": initial_details,
                        "court_id": court_id,
                        "court_logger": court_logger,
                        "processor_config": self.config
                    })
                    
                    if result:
                        court_logger.info(f"[{court_id.upper()}] SingleDocketProcessor completed for {docket_num}")
                        return result
                    else:
                        court_logger.warning(f"[{court_id.upper()}] SingleDocketProcessor returned None for {docket_num}")
                        
                except Exception as processor_e:
                    court_logger.error(f"[{court_id.upper()}] SingleDocketProcessor error for {docket_num}: {processor_e}")
            
            # Fallback: Use DocketOrchestrator if available
            try:
                from src.pacer.facades.docket_orchestrator import DocketOrchestrator
                from src.pacer.services.case_processing_service import CaseProcessingService
                from src.pacer.services.relevance_service import RelevanceService
                from src.pacer.services.classification_service import ClassificationService
                from src.pacer.services.verification_service import VerificationService
                from src.pacer.services.download_service import DownloadService
                from src.pacer.core.file_operations.file_operations_service import FileOperationsService  # Use core implementation
                from src.pacer.services.configuration_service import ConfigurationService
                from src.pacer.services.browser_service import BrowserService
                
                # Import component classes for dependency injection
                from src.pacer.components.verification.case_verifier import CaseVerifier
                from src.pacer.components.download.file_downloader import FileDownloader
                from src.pacer.components.download.download_manager import DownloadManager
                from src.pacer.components.file_operations.file_manager import FileManager
                from src.pacer.components.classification.case_classifier import CaseClassifier
                from src.pacer.components.browser.playwright_manager import PlaywrightManager
                from src.pacer.components.navigation.page_navigator import PageNavigator
                from src.pacer.components.authentication.ecf_login_handler import ECFLoginHandler
                from src.repositories.pacer_repository import PacerRepository
                from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
                
                # Ensure config is a dict before passing to services
                config_dict = self._ensure_config_dict()
                
                # Create all required services using the court logger
                case_processing_service = CaseProcessingService(logger=court_logger, config=config_dict)
                relevance_service = RelevanceService(logger=court_logger, config=config_dict) if self.relevance_service is None else self.relevance_service
                classification_service = ClassificationService(logger=court_logger, config=config_dict)
                verification_service = VerificationService(logger=court_logger, config=config_dict)
                download_service = DownloadService(logger=court_logger, config=config_dict)
                file_operations_service = self.file_service if self.file_service else FileOperationsService(logger=court_logger, config=config_dict)
                configuration_service = ConfigurationService(logger=court_logger, config=config_dict)
                browser_service = BrowserService(logger=court_logger, config=config_dict)
                
                # Create component dependencies
                # First create file_manager since it's needed for case_verifier
                file_manager = FileManager(logger=court_logger, config=config_dict)
                
                # Create repository for case_verifier
                # Create config object with table_name for DynamoDB storage
                dynamodb_config = dict(config_dict) if config_dict else {}
                dynamodb_config['table_name'] = 'Pacer'
                
                dynamodb_storage = AsyncDynamoDBStorage(config=dynamodb_config, logger=court_logger)
                pacer_repository = PacerRepository(storage=dynamodb_storage, logger=court_logger)
                
                # Now create case_verifier with required repository and file_manager
                case_verifier = CaseVerifier(
                    repository=pacer_repository,
                    file_manager=file_manager,
                    logger=court_logger,
                    config=config_dict
                )
                
                # Create S3 storage for download manager
                from src.infrastructure.storage.s3_async import S3AsyncStorage
                import os
                
                # Ensure S3 configuration is complete
                s3_config = dict(config_dict) if config_dict else {}
                
                # Add bucket name if not present
                if 'bucket_name' not in s3_config or not s3_config['bucket_name']:
                    s3_config['bucket_name'] = os.getenv('S3_BUCKET_NAME', 'lexgenius-data')
                
                # Add AWS credentials if not present (S3AsyncStorage will also check env vars)
                if 'aws_access_key' not in s3_config:
                    s3_config['aws_access_key'] = os.getenv('AWS_ACCESS_KEY_ID') or os.getenv('AWS_ACCESS_KEY')
                if 'aws_secret_key' not in s3_config:
                    s3_config['aws_secret_key'] = os.getenv('AWS_SECRET_ACCESS_KEY') or os.getenv('AWS_SECRET_KEY')
                if 'aws_region' not in s3_config:
                    s3_config['aws_region'] = os.getenv('AWS_REGION', 'us-west-2')
                
                s3_storage = S3AsyncStorage(logger=court_logger, config=s3_config)
                
                file_downloader = FileDownloader(logger=court_logger, config=config_dict)
                download_manager = DownloadManager(s3_storage=s3_storage, logger=court_logger, config=config_dict)
                case_classifier = CaseClassifier(logger=court_logger, config=config_dict)
                playwright_manager = PlaywrightManager(logger=court_logger, config=config_dict)
                
                # Create ElementLocator first, as PageNavigator requires it
                from src.pacer.components.navigation.element_locator import ElementLocator
                element_locator = ElementLocator(logger=court_logger, config=config_dict)
                
                # Now create PageNavigator with required element_locator
                page_navigator = PageNavigator(element_locator=element_locator, logger=court_logger, config=config_dict)
                ecf_login_handler = ECFLoginHandler(logger=court_logger, config=config_dict)
                
                court_logger.info(f"[{court_id.upper()}] Created service dependencies for {docket_num}")
                
                # Set dependencies for verification service
                verification_service.set_dependency('case_verifier', case_verifier)
                verification_service.set_dependency('pacer_repository', pacer_repository)  # Use actual repository
                verification_service.set_dependency('file_manager', file_manager)
                
                # Set dependencies for download service
                download_service.set_dependency('download_validator', case_verifier)  # Reuse verifier for validation
                download_service.set_dependency('file_downloader', file_downloader)
                download_service.set_dependency('download_manager', download_manager)
                download_service.set_dependency('s3_manager', None)  # Will be handled gracefully
                download_service.set_dependency('authentication_service', ecf_login_handler)
                download_service.set_dependency('navigation_service', page_navigator)
                
                # Set dependencies for classification service
                classification_service.set_dependency('case_classifier', case_classifier)
                classification_service.set_dependency('relevance_engine', None)  # Will be handled gracefully
                
                # Set dependencies for browser service
                browser_service.set_dependency('playwright_manager', playwright_manager)
                browser_service.set_dependency('authentication_handler', ecf_login_handler)
                browser_service.set_dependency('page_navigator', page_navigator)
                
                # Set dependencies for file operations service (if created new)
                if not self.file_service:
                    file_operations_service.set_dependency('config_service', configuration_service)
                    file_operations_service.set_dependency('s3_service', None)  # Will be handled gracefully
                
                # Inject configuration_service dependency into relevance_service
                if self.relevance_service is None:  # Only inject if we created a new one
                    relevance_service.set_dependency('configuration_service', configuration_service)
                
                # Initialize all services before passing to DocketOrchestrator
                await case_processing_service.initialize()
                if self.relevance_service is None:  # Only initialize if we created a new one
                    await relevance_service.initialize()
                await classification_service.initialize()
                await verification_service.initialize()
                await download_service.initialize()
                if not self.file_service:  # Only initialize if we created a new FileOperationsService
                    await file_operations_service.initialize()
                await configuration_service.initialize()
                await browser_service.initialize()
                
                # Create DocketOrchestrator with all required services including storage dependencies
                docket_orchestrator = DocketOrchestrator(
                    case_processing_service=case_processing_service,
                    relevance_service=relevance_service,
                    classification_service=classification_service,
                    verification_service=verification_service,
                    download_service=download_service,
                    file_operations_service=file_operations_service,
                    configuration_service=configuration_service,
                    browser_service=browser_service,
                    # CRITICAL FIX: Add missing storage dependencies
                    pacer_repository=self.pacer_repository,
                    async_dynamodb_storage=self.async_dynamodb_storage,
                    logger=court_logger,
                    config=self.config
                )
                
                result = await docket_orchestrator.process_docket({
                    "page": page,
                    "initial_details": initial_details
                })
                
                if result:
                    court_logger.info(f"[{court_id.upper()}] DocketOrchestrator completed for {docket_num}")
                    return result
                else:
                    court_logger.warning(f"[{court_id.upper()}] DocketOrchestrator returned None for {docket_num}")
                    
            except Exception as orchestrator_e:
                court_logger.error(f"[{court_id.upper()}] DocketOrchestrator error for {docket_num}: {orchestrator_e}")
            
            # Final fallback: Basic processing using available facades
            try:
                # Create a basic case details structure with the HTML content
                processed_case = initial_details.copy()
                processed_case.update({
                    "_html_content": html_content,
                    "_processing_status": "step_6_basic_processing",
                    "_processed_timestamp": iso_date,
                    "base_filename": f"{court_id.lower()}_{docket_num}_{iso_date}"
                })
                
                # Try to save HTML using file_service if available
                if self.file_service:
                    try:
                        await self.file_service.save_case_data_to_json(processed_case, court_id, iso_date)
                        court_logger.info(f"[{court_id.upper()}] Basic HTML processing completed for {docket_num}")
                    except Exception as file_e:
                        court_logger.warning(f"[{court_id.upper()}] Could not save case data for {docket_num}: {file_e}")
                
                return processed_case
                
            except Exception as basic_e:
                court_logger.error(f"[{court_id.upper()}] Basic processing failed for {docket_num}: {basic_e}")
            
            # If all processing methods fail, return None
            court_logger.error(f"[{court_id.upper()}] All processing methods failed for {docket_num}")
            return None
            
        except Exception as e:
            court_logger.error(f"[{court_id.upper()}] Critical error in _process_docket_sheet_after_step_6: {e}", exc_info=True)
            return None
