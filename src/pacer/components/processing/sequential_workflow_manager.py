"""
Sequential Workflow Manager for PACER Docket Processing.

This manager implements the core sequential logic for processing dockets one at a time
with proper state management, navigation control, and "Return and Continue" logic.

MISSION: Process ONE docket completely before starting next
KEY REQUIREMENTS:
- Navigate back to Query page after each docket (skip or complete)
- Implement all conditional logic for transfers and ignore list
- Maintain proper state between workflow steps
- Integrate with existing components (html_parser, transfer detection, etc.)
"""

import asyncio
import json
import os
import sys
from typing import Any, Dict, Optional, List, Union
from enum import Enum
from datetime import datetime
from src.infrastructure.patterns.component_base import AsyncServiceBase


class DocketProcessingState(Enum):
    """States for docket processing workflow."""
    PENDING = "pending"
    NAVIGATING_TO_QUERY = "navigating_to_query"
    EXECUTING_QUERY = "executing_query"
    ON_DOCKET_SHEET = "on_docket_sheet"
    PROCESSING_DOCKET = "processing_docket"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class WorkflowStep(Enum):
    """Sequential workflow steps."""
    STEP_1_QUERY_PAGE = "step_1_query_page"
    STEP_2_ENTER_CASE = "step_2_enter_case"
    STEP_3_CLICK_QUERY = "step_3_click_query"
    STEP_4_SELECT_DOCKET = "step_4_select_docket"
    STEP_5_CONFIGURE_OPTIONS = "step_5_configure_options"
    STEP_6_RUN_REPORT = "step_6_run_report"
    STEP_7_PROCESS_DOCKET = "step_7_process_docket"


class SequentialWorkflowManager(AsyncServiceBase):
    """
    Manages sequential processing of dockets with robust state tracking.
    
    Implements the "Return and Continue" logic where each docket is processed
    completely before moving to the next, with navigation back to Query page
    between dockets.
    """

    def __init__(
        self,
        navigation_facade: Optional[Any] = None,
        docket_processor: Optional[Any] = None,
        state_validator: Optional[Any] = None,
        return_manager: Optional[Any] = None,
        court_logger: Optional[Any] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        pacer_repository: Optional[Any] = None,
        async_dynamodb_storage: Optional[Any] = None,
    ):
        super().__init__(logger, config)
        self.navigation_facade = navigation_facade
        self.docket_processor = docket_processor
        self.state_validator = state_validator
        self.return_manager = return_manager
        self.court_logger = court_logger
        self.pacer_repository = pacer_repository
        self.async_dynamodb_storage = async_dynamodb_storage
        
        # State management
        self._current_state = DocketProcessingState.PENDING
        self._current_step = None
        self._processing_stats = {
            'total_dockets': 0,
            'completed_dockets': 0,
            'failed_dockets': 0,
            'skipped_dockets': 0,
            'processing_errors': []
        }
        
        # Sequential processing queue
        self._docket_queue = []
        self._current_docket_index = 0
        self._processing_context = {}

    async def _initialize_service(self) -> None:
        """Initialize the sequential workflow manager with enhanced dependency validation."""
        if self._initialized:
            return
        
        # Enhanced dependency validation with detailed error reporting
        missing_deps = []
        dependency_status = {
            "NavigationFacade": self.navigation_facade is not None,
            "DocketProcessor": self.docket_processor is not None,
            "PacerRepository": self.pacer_repository is not None,
            "AsyncDynamoDBStorage": self.async_dynamodb_storage is not None
        }
        
        # Check core processing dependencies
        if not self.navigation_facade:
            missing_deps.append("NavigationFacade - required for page navigation")
        if not self.docket_processor:
            missing_deps.append("DocketProcessor - required for docket processing")
        
        # Check database dependencies (at least one required)
        if not self.pacer_repository and not self.async_dynamodb_storage:
            missing_deps.append("PacerRepository OR AsyncDynamoDBStorage - at least one database dependency required")
        
        # Log dependency status for debugging
        self.log_info("SequentialWorkflowManager dependency validation:")
        for dep_name, available in dependency_status.items():
            status_symbol = "✅" if available else "❌"
            self.log_info(f"  {status_symbol} {dep_name}: {'Available' if available else 'Missing'}")
        
        # If any critical dependencies are missing, provide detailed error and exit
        if missing_deps:
            error_msg = f"CRITICAL DEPENDENCY INJECTION FAILURE - Missing required dependencies:\n"
            for i, dep in enumerate(missing_deps, 1):
                error_msg += f"  {i}. {dep}\n"
            error_msg += "\nThis indicates a problem with dependency injection configuration.\n"
            error_msg += "Check MainServiceFactory and container configuration.\n"
            error_msg += "Dependencies should be injected by the DI container, not created internally."
            
            self.log_error(error_msg)
            self.log_error("SequentialWorkflowManager cannot proceed - terminating with exit(1)")
            sys.exit(1)  # HARD FAIL - these dependencies are required
        
        # Log successful initialization
        database_info = []
        if self.pacer_repository:
            database_info.append("PacerRepository")
        if self.async_dynamodb_storage:
            database_info.append("AsyncDynamoDBStorage")
        
        self.log_info(f"✅ SequentialWorkflowManager initialized successfully with database: {', '.join(database_info)}")
        self._initialized = True

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate workflow methods."""
        action = data.get("action")
        
        if action == "process_dockets_sequentially":
            return await self.process_dockets_sequentially(
                court_id=data.get("court_id"),
                dockets=data.get("dockets", []),
                navigator=data.get("navigator"),
                context=data.get("context"),
                workflow_config=data.get("workflow_config", {})
            )
        elif action == "process_single_docket_sequential":
            return await self.process_single_docket_sequential(
                court_id=data.get("court_id"),
                docket_info=data.get("docket_info"),
                navigator=data.get("navigator"),
                workflow_config=data.get("workflow_config", {})
            )
        elif action == "get_processing_state":
            return self.get_current_processing_state()
        elif action == "reset_workflow_state":
            return await self.reset_workflow_state()
        else:
            raise ValueError(f"Unknown action for SequentialWorkflowManager: {action}")

    async def process_dockets_sequentially(
        self,
        court_id: str,
        dockets: List[Dict[str, Any]],
        navigator: Any,
        context: Any,
        workflow_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process multiple dockets sequentially with "Return and Continue" logic.
        
        Args:
            court_id: Court identifier
            dockets: List of dockets to process
            navigator: PacerNavigator instance
            context: Browser context
            workflow_config: Configuration for workflow processing
            
        Returns:
            Sequential processing results with comprehensive statistics
        """
        if not self.court_logger:
            from src.pacer.utils.court_logger import create_court_logger
            self.court_logger = create_court_logger(court_id, workflow_config.get('iso_date', ''))

        log_prefix = f"[{court_id}] SEQUENTIAL:"
        
        self.court_logger.info(f"{log_prefix} Starting sequential processing of {len(dockets)} dockets")
        
        # Initialize processing context
        self._docket_queue = dockets.copy()
        self._current_docket_index = 0
        self._processing_context = {
            'court_id': court_id,
            'navigator': navigator,
            'context': context,
            'workflow_config': workflow_config,
            'start_time': datetime.now(),
            'total_dockets': len(dockets)
        }
        
        # Reset processing stats
        self._processing_stats = {
            'total_dockets': len(dockets),
            'completed_dockets': 0,
            'failed_dockets': 0,
            'skipped_dockets': 0,
            'processing_errors': []
        }
        
        results = []
        
        try:
            # ========================================
            # DOCKET REPORT LOG FILTERING BEFORE UNIFIED WORKFLOW
            # ========================================
            self.court_logger.info(f"{log_prefix} FILTERING PHASE: Starting comprehensive docket_report_log filtering")
            self.court_logger.info(f"{log_prefix} FILTERING PHASE: Initial docket count: {len(dockets)}")
            
            # Import the docket filter service for comprehensive filtering
            try:
                from src.pacer.components.processing.docket_filter_service import DocketFilterService
                
                # Initialize the docket filter service
                docket_filter_service = DocketFilterService(
                    logger=self.court_logger,
                    config=workflow_config
                )
                # DocketFilterService doesn't have initialize() method - it's ready after __init__
                
                # Filter the ENTIRE docket_report_log with comprehensive logging
                filter_results = await docket_filter_service.filter_docket_report_log(
                    docket_items=dockets,
                    iso_date=workflow_config.get('iso_date'),
                    batch_size=workflow_config.get('filter_batch_size', 50)
                )
                
                if filter_results and filter_results.get('success'):
                    # Extract filtered dockets and statistics
                    filtered_dockets = filter_results.get('filtered_dockets', [])
                    filtering_stats = filter_results.get('statistics', {})
                    
                    # Log comprehensive filtering results
                    self.court_logger.info(f"{log_prefix} FILTERING COMPLETED:")
                    self.court_logger.info(f"{log_prefix} - Original dockets: {filtering_stats.get('total_dockets', len(dockets))}")
                    self.court_logger.info(f"{log_prefix} - Filtered dockets: {filtering_stats.get('filtered_count', len(filtered_dockets))}")
                    self.court_logger.info(f"{log_prefix} - Excluded by artifact check: {filtering_stats.get('excluded_artifact_check', 0)}")
                    self.court_logger.info(f"{log_prefix} - Excluded by database check: {filtering_stats.get('excluded_database_check', 0)}")
                    self.court_logger.info(f"{log_prefix} - Excluded by business rules: {filtering_stats.get('excluded_business_rules', 0)}")
                    self.court_logger.info(f"{log_prefix} - Excluded by social security filter: {filtering_stats.get('excluded_social_security', 0)}")
                    
                    # Log each filtering decision for transparency
                    excluded_dockets = filter_results.get('excluded_dockets', [])
                    if excluded_dockets:
                        self.court_logger.info(f"{log_prefix} EXCLUDED DOCKETS ({len(excluded_dockets)}):")
                        for excluded in excluded_dockets[:10]:  # Log first 10 to avoid overwhelming logs
                            docket_num = excluded.get('docket_num', 'unknown')
                            reason = excluded.get('exclusion_reason', 'no reason provided')
                            case_title = excluded.get('versus', excluded.get('case_title', ''))[:50] + '...' if excluded.get('versus', excluded.get('case_title', '')) else 'No title'
                            self.court_logger.info(f"{log_prefix}   - {docket_num}: {reason} [{case_title}]")
                        
                        if len(excluded_dockets) > 10:
                            self.court_logger.info(f"{log_prefix}   - ... and {len(excluded_dockets) - 10} more excluded dockets")
                    
                    # Log included dockets for processing
                    if filtered_dockets:
                        self.court_logger.info(f"{log_prefix} INCLUDED DOCKETS FOR PROCESSING ({len(filtered_dockets)}):")
                        for included in filtered_dockets[:10]:  # Log first 10 to avoid overwhelming logs
                            docket_num = included.get('docket_num', 'unknown')
                            case_title = included.get('versus', included.get('case_title', ''))[:50] + '...' if included.get('versus', included.get('case_title', '')) else 'No title'
                            self.court_logger.info(f"{log_prefix}   - {docket_num}: [{case_title}]")
                        
                        if len(filtered_dockets) > 10:
                            self.court_logger.info(f"{log_prefix}   - ... and {len(filtered_dockets) - 10} more dockets to process")
                    
                    # Update the docket queue with filtered results
                    self._docket_queue = filtered_dockets
                    dockets = filtered_dockets  # Use filtered dockets for subsequent processing
                    
                    # Update processing context and stats
                    self._processing_context['total_dockets'] = len(filtered_dockets)
                    self._processing_context['original_docket_count'] = len(dockets) if 'original_dockets' not in self._processing_context else self._processing_context['original_docket_count']
                    self._processing_context['filtering_applied'] = True
                    self._processing_context['filtering_stats'] = filtering_stats
                    
                    # Update processing stats
                    original_count = self._processing_stats['total_dockets']  # Save original count
                    self._processing_stats['total_dockets'] = len(filtered_dockets)
                    self._processing_stats['original_docket_count'] = original_count
                    self._processing_stats['filtering_excluded'] = filtering_stats.get('excluded_count', 0)
                    
                    if len(filtered_dockets) == 0:
                        self.court_logger.info(f"{log_prefix} FILTERING RESULT: All dockets filtered out - no processing needed")
                        return {
                            'court_id': court_id,
                            'status': 'completed',
                            'message': 'All dockets filtered out by comprehensive filtering',
                            'sequential_processing_stats': self._processing_stats,
                            'filtering_applied': True,
                            'filtering_stats': filtering_stats,
                            'processing_time_seconds': 0,
                            'results': [],
                            'workflow_type': 'sequential_docket_processing_filtered_out'
                        }
                    
                    self.court_logger.info(f"{log_prefix} FILTERING PHASE COMPLETED: Proceeding with {len(filtered_dockets)} filtered dockets")
                    
                else:
                    # Filtering failed or returned no results
                    filter_error = filter_results.get('error', 'Unknown filtering error') if filter_results else 'No filter results returned'
                    self.court_logger.warning(f"{log_prefix} FILTERING FAILED: {filter_error} - proceeding with original docket list")
                    # Continue with original dockets
                    
            except Exception as filter_error:
                self.court_logger.error(f"{log_prefix} FILTERING ERROR: Failed to apply docket filtering: {filter_error}", exc_info=True)
                self.court_logger.info(f"{log_prefix} FILTERING ERROR: Proceeding with original docket list due to filtering failure")
                # Continue with original dockets if filtering fails
            
            # SEQUENTIAL PROCESSING LOOP: Process ONE docket completely before next
            for index, docket_info in enumerate(dockets):
                docket_num = docket_info.get('docket_num', f'docket_{index}')
                self._current_docket_index = index
                
                self.court_logger.info(f"{log_prefix} [{index+1}/{len(dockets)}] Processing docket {docket_num}")
                
                try:
                    # CRITICAL: Set processing state and validate prerequisites
                    await self._set_processing_state(DocketProcessingState.PENDING)
                    
                    # Process single docket with full workflow
                    docket_result = await self.process_single_docket_sequential(
                        court_id=court_id,
                        docket_info=docket_info,
                        navigator=navigator,
                        workflow_config=workflow_config
                    )
                    
                    if docket_result:
                        if docket_result.get('status') == 'completed':
                            self._processing_stats['completed_dockets'] += 1
                            results.append(docket_result)
                            self.court_logger.info(f"{log_prefix} [{index+1}/{len(dockets)}] Docket {docket_num} completed successfully")
                        elif docket_result.get('status') == 'skipped':
                            self._processing_stats['skipped_dockets'] += 1
                            results.append(docket_result)
                            self.court_logger.info(f"{log_prefix} [{index+1}/{len(dockets)}] Docket {docket_num} skipped: {docket_result.get('skip_reason', 'Unknown')}")
                        else:
                            self._processing_stats['failed_dockets'] += 1
                            self.court_logger.warning(f"{log_prefix} [{index+1}/{len(dockets)}] Docket {docket_num} processing failed")
                    else:
                        self._processing_stats['failed_dockets'] += 1
                        self.court_logger.error(f"{log_prefix} [{index+1}/{len(dockets)}] Docket {docket_num} returned no result")
                    
                    # CRITICAL: "Return and Continue" logic - Navigate back to Query page
                    # This ensures we're in a clean state for the next docket
                    if index < len(dockets) - 1:  # Not the last docket
                        self.court_logger.info(f"{log_prefix} Returning to Query page for next docket")
                        
                        if self.return_manager:
                            # Use dedicated return manager
                            return_result = await self.return_manager.execute({
                                "action": "return_to_query_page",
                                "court_id": court_id,
                                "navigator": navigator
                            })
                            
                            if not return_result.get('success'):
                                self.court_logger.warning(f"{log_prefix} Return to query page failed: {return_result.get('error', 'Unknown error')}")
                        else:
                            # Fallback to internal method
                            await self._return_to_query_page_for_next_docket(court_id, navigator)
                    
                except Exception as docket_error:
                    error_msg = f"Sequential processing failed for docket {docket_num}: {docket_error}"
                    self.court_logger.error(f"{log_prefix} [{index+1}/{len(dockets)}] {error_msg}", exc_info=True)
                    
                    self._processing_stats['failed_dockets'] += 1
                    self._processing_stats['processing_errors'].append({
                        'docket_num': docket_num,
                        'error': str(docket_error),
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    # Continue with next docket even if current one fails
                    results.append({
                        'docket_num': docket_num,
                        'status': 'failed',
                        'error': str(docket_error),
                        'processing_step': self._current_step.value if self._current_step else 'unknown'
                    })
                    
                    # Still try to return to query page for next docket
                    if index < len(dockets) - 1:
                        try:
                            await self._return_to_query_page_for_next_docket(court_id, navigator)
                        except Exception as nav_error:
                            self.court_logger.error(f"{log_prefix} Failed to return to query page after error: {nav_error}")
            
            # Final statistics and results
            end_time = datetime.now()
            total_time = (end_time - self._processing_context['start_time']).total_seconds()
            
            # Include filtering statistics if filtering was applied
            filtering_applied = self._processing_context.get('filtering_applied', False)
            filtering_stats = self._processing_context.get('filtering_stats', {})
            
            final_result = {
                'court_id': court_id,
                'status': 'completed',
                'sequential_processing_stats': self._processing_stats,
                'processing_time_seconds': total_time,
                'results': results,
                'workflow_type': 'sequential_docket_processing'
            }
            
            # Add filtering information to results
            if filtering_applied:
                final_result['filtering_applied'] = True
                final_result['filtering_stats'] = filtering_stats
                final_result['original_docket_count'] = self._processing_stats.get('original_docket_count', 0)
                final_result['workflow_type'] = 'sequential_docket_processing_with_filtering'
            
            success_rate = (self._processing_stats['completed_dockets'] / self._processing_stats['total_dockets']) * 100 if self._processing_stats['total_dockets'] > 0 else 0
            
            self.court_logger.info(f"{log_prefix} SEQUENTIAL PROCESSING COMPLETED")
            if filtering_applied:
                original_count = self._processing_stats.get('original_docket_count', 0)
                filtered_count = self._processing_stats['total_dockets']
                excluded_count = self._processing_stats.get('filtering_excluded', 0)
                self.court_logger.info(f"{log_prefix} FILTERING SUMMARY: Original: {original_count}, Filtered: {filtered_count}, Excluded: {excluded_count}")
            self.court_logger.info(f"{log_prefix} PROCESSING SUMMARY: Total: {self._processing_stats['total_dockets']}, Completed: {self._processing_stats['completed_dockets']}, Failed: {self._processing_stats['failed_dockets']}, Skipped: {self._processing_stats['skipped_dockets']}")
            self.court_logger.info(f"{log_prefix} Success Rate: {success_rate:.1f}%, Total Time: {total_time:.1f}s")
            
            return final_result
            
        except Exception as e:
            self.court_logger.error(f"{log_prefix} CRITICAL ERROR in sequential processing: {e}", exc_info=True)
            
            # Include filtering statistics in error result if filtering was applied
            filtering_applied = self._processing_context.get('filtering_applied', False)
            filtering_stats = self._processing_context.get('filtering_stats', {})
            
            error_result = {
                'court_id': court_id,
                'status': 'failed',
                'error': str(e),
                'sequential_processing_stats': self._processing_stats,
                'results': results,
                'workflow_type': 'sequential_docket_processing'
            }
            
            if filtering_applied:
                error_result['filtering_applied'] = True
                error_result['filtering_stats'] = filtering_stats
                error_result['original_docket_count'] = self._processing_stats.get('original_docket_count', 0)
                error_result['workflow_type'] = 'sequential_docket_processing_with_filtering_failed'
            
            return error_result

    async def process_single_docket_sequential(
        self,
        court_id: str,
        docket_info: Dict[str, Any],
        navigator: Any,
        workflow_config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Process a single docket through the complete sequential workflow.
        
        Implements MANDATORY PRE-CHECKS + 7 workflow steps:
        PRE-CHECK 1: LOCAL ARTIFACT CHECK - Check if artifacts already exist locally
        PRE-CHECK 2: DYNAMODB DUPLICATE CHECK - Check if docket already exists in database
        1. Navigate to Query page (only if not already there)
        2. Enter case number
        3. Click Query button
        4. Select docket from results
        5. Configure docket report options
        6. Run report
        7. Process docket sheet
        
        The pre-checks run BEFORE starting the unified workflow and will skip
        processing if the docket is already available locally or in the database.
        All checks are logged through court_logger for visibility.
        
        Args:
            court_id: Court identifier
            docket_info: Docket information to process
            navigator: PacerNavigator instance
            workflow_config: Configuration for workflow processing
            
        Returns:
            Complete processing result or None if failed
        """
        docket_num = docket_info.get('docket_num', 'unknown')
        log_prefix = f"[{court_id}][{docket_num}] SEQUENTIAL:"
        
        if not self.court_logger:
            from src.pacer.utils.court_logger import create_court_logger
            self.court_logger = create_court_logger(court_id, workflow_config.get('iso_date', ''))
        
        self.court_logger.info(f"{log_prefix} Starting sequential workflow for docket {docket_num}")
        
        # Initialize result structure
        result = {
            'docket_num': docket_num,
            'court_id': court_id,
            'status': 'failed',
            'workflow_steps_completed': [],
            'processing_time_seconds': 0,
            'start_time': datetime.now().isoformat()
        }
        
        start_time = datetime.now()
        
        try:
            # ========================================
            # PRE-CHECK 1: LOCAL ARTIFACT CHECK
            # ========================================
            self.court_logger.info(f"{log_prefix} PRE-CHECK 1: Running LOCAL ARTIFACT CHECK")
            
            try:
                from src.pacer.components.download.artifact_checker import DocketArtifactChecker
                
                # Debug log to see what fields are in docket_info
                self.court_logger.debug(f"{log_prefix} Docket info fields: {list(docket_info.keys())}")
                self.court_logger.debug(f"{log_prefix} Versus field value: '{docket_info.get('versus', 'MISSING')}'")
                
                # Get versus/title from docket_info (docket_report_log uses 'versus' field)
                versus = docket_info.get('versus', docket_info.get('case_title', ''))
                
                # Create artifact checker with court_logger for consistent logging
                artifact_checker = DocketArtifactChecker(
                    court_logger=self.court_logger,
                    config=workflow_config
                )
                
                # Check if docket should be downloaded (returns False if artifacts exist)
                should_download, artifact_reason = await artifact_checker.should_download_docket(
                    court_id=court_id,
                    docket_num=docket_num,
                    versus=versus,
                    iso_date=workflow_config.get('iso_date')
                )
                
                result['workflow_steps_completed'].append('pre_check_local_artifacts')
                result['local_artifact_check'] = {
                    'should_download': should_download,
                    'reason': artifact_reason,
                    'status': 'completed'
                }
                
                if not should_download:
                    # Artifacts already exist locally - skip processing
                    self.court_logger.info(f"{log_prefix} PRE-CHECK 1 RESULT: Skipping docket - {artifact_reason}")
                    
                    end_time = datetime.now()
                    processing_time = (end_time - start_time).total_seconds()
                    
                    result.update({
                        'status': 'skipped',
                        'skip_reason': f'Local artifacts exist: {artifact_reason}',
                        'processing_time_seconds': processing_time,
                        'end_time': end_time.isoformat(),
                        'skipped_at_stage': 'pre_check_local_artifacts'
                    })
                    
                    return result
                else:
                    self.court_logger.info(f"{log_prefix} PRE-CHECK 1 RESULT: Proceeding with download - {artifact_reason}")
                    
            except Exception as artifact_error:
                self.court_logger.warning(f"{log_prefix} PRE-CHECK 1 ERROR: Local artifact check failed: {artifact_error}")
                result['local_artifact_check'] = {
                    'status': 'failed',
                    'error': str(artifact_error)
                }
                # Continue processing despite artifact check failure
            
            # ========================================
            # PRE-CHECK 2: DYNAMODB DUPLICATE CHECK
            # ========================================
            self.court_logger.info(f"{log_prefix} PRE-CHECK 2: Running DYNAMODB DUPLICATE CHECK")
            
            try:
                # Use injected dependencies instead of direct imports
                if self.pacer_repository:
                    # If pacer_repository is a DI provider, call it to get the instance
                    if hasattr(self.pacer_repository, '__call__'):
                        pacer_repo = self.pacer_repository()
                    else:
                        pacer_repo = self.pacer_repository
                    
                    # Now use the actual repository instance
                    docket_exists = await pacer_repo.check_docket_exists(court_id, docket_num)
                    
                    result['workflow_steps_completed'].append('pre_check_dynamodb_duplicate')
                    result['dynamodb_duplicate_check'] = {
                        'docket_exists': docket_exists,
                        'status': 'completed'
                    }
                    
                    if docket_exists:
                        # Docket already exists in database - skip processing
                        self.court_logger.info(f"{log_prefix} PRE-CHECK 2 RESULT: Skipping docket - already exists in DynamoDB")
                        
                        end_time = datetime.now()
                        processing_time = (end_time - start_time).total_seconds()
                        
                        result.update({
                            'status': 'skipped',
                            'skip_reason': 'Docket already exists in DynamoDB database',
                            'processing_time_seconds': processing_time,
                            'end_time': end_time.isoformat(),
                            'skipped_at_stage': 'pre_check_dynamodb_duplicate'
                        })
                        
                        return result
                    else:
                        self.court_logger.info(f"{log_prefix} PRE-CHECK 2 RESULT: Proceeding - docket not found in DynamoDB")
                
                elif self.async_dynamodb_storage:
                    # If async_dynamodb_storage is a DI provider, call it to get the instance
                    if hasattr(self.async_dynamodb_storage, '__call__'):
                        storage_instance = self.async_dynamodb_storage()
                    else:
                        storage_instance = self.async_dynamodb_storage
                    
                    # Create repository from injected storage
                    from src.repositories.pacer_repository import PacerRepository
                    pacer_repo = PacerRepository(storage=storage_instance, logger=self.court_logger)
                    
                    docket_exists = await pacer_repo.check_docket_exists(court_id, docket_num)
                    
                    result['workflow_steps_completed'].append('pre_check_dynamodb_duplicate')
                    result['dynamodb_duplicate_check'] = {
                        'docket_exists': docket_exists,
                        'status': 'completed'
                    }
                    
                    if docket_exists:
                        # Docket already exists in database - skip processing
                        self.court_logger.info(f"{log_prefix} PRE-CHECK 2 RESULT: Skipping docket - already exists in DynamoDB")
                        
                        end_time = datetime.now()
                        processing_time = (end_time - start_time).total_seconds()
                        
                        result.update({
                            'status': 'skipped',
                            'skip_reason': 'Docket already exists in DynamoDB database',
                            'processing_time_seconds': processing_time,
                            'end_time': end_time.isoformat(),
                            'skipped_at_stage': 'pre_check_dynamodb_duplicate'
                        })
                        
                        return result
                    else:
                        self.court_logger.info(f"{log_prefix} PRE-CHECK 2 RESULT: Proceeding - docket not found in DynamoDB")
                        
                else:
                    # CRITICAL: No DynamoDB dependencies available - HARD FAIL
                    self.court_logger.critical(f"{log_prefix} CRITICAL: No PacerRepository or AsyncDynamoDBStorage injected. Cannot proceed without database dependencies!")
                    sys.exit(1)  # HARD FAIL - these dependencies are required
                    
            except Exception as db_error:
                self.court_logger.warning(f"{log_prefix} PRE-CHECK 2 ERROR: DynamoDB duplicate check failed: {db_error}")
                result['dynamodb_duplicate_check'] = {
                    'status': 'failed',
                    'error': str(db_error)
                }
                # Continue processing despite database check failure
            
            # ========================================
            # PRE-CHECKS COMPLETED - PROCEED WITH UNIFIED WORKFLOW
            # ========================================
            self.court_logger.info(f"{log_prefix} PRE-CHECKS COMPLETED: Both LOCAL ARTIFACT and DYNAMODB checks passed - starting unified workflow")
            result['pre_checks_status'] = 'all_passed'
            
            # SMART NAVIGATION: Check current page before attempting navigation
            current_url = navigator.page.url
            is_on_docket_sheet = await self._validate_docket_sheet_page(navigator)
            is_on_query_page = "iquery.pl" in current_url
            
            # Only navigate to Query page if we're not already there AND not on target docket sheet
            if is_on_docket_sheet:
                self.court_logger.info(f"{log_prefix} Already on docket sheet: {current_url} - skipping navigation to Query page")
                result['workflow_steps_completed'].extend([
                    'step_1_query_page', 'step_2_enter_case', 'step_3_click_query', 
                    'step_4_select_docket', 'step_5_configure_options', 'step_6_run_report'
                ])
                
                # Jump directly to Step 7: Process docket sheet
                await self._set_current_step(WorkflowStep.STEP_7_PROCESS_DOCKET)
                await self._set_processing_state(DocketProcessingState.ON_DOCKET_SHEET)
                self.court_logger.info(f"{log_prefix} Proceeding directly to Step 7: Process docket sheet")
                
            elif is_on_query_page:
                self.court_logger.info(f"{log_prefix} Already on query page: {current_url} - proceeding to case workflow")
                result['workflow_steps_completed'].append('step_1_query_page')
                
                # Skip Step 1, proceed to Steps 2-6
                await self._set_current_step(WorkflowStep.STEP_2_ENTER_CASE)
                await self._set_processing_state(DocketProcessingState.EXECUTING_QUERY)
                
            else:
                # STEP 1: Navigate to Query page (only when needed)
                await self._set_current_step(WorkflowStep.STEP_1_QUERY_PAGE)
                await self._set_processing_state(DocketProcessingState.NAVIGATING_TO_QUERY)
                
                self.court_logger.info(f"{log_prefix} Step 1: Navigating to Query page from {current_url}")
                
                navigation_success = await self.navigation_facade.execute({
                    "action": "go_to_query_page",
                    "navigator": navigator,
                    "court_id": court_id
                })
                
                if not navigation_success:
                    self.court_logger.error(f"{log_prefix} Step 1 FAILED: Could not navigate to Query page")
                    result['error'] = 'Failed to navigate to Query page'
                    result['failed_step'] = 'step_1_query_page'
                    return result
                
                result['workflow_steps_completed'].append('step_1_query_page')
                self.court_logger.info(f"{log_prefix} Step 1 COMPLETED: Successfully navigated to Query page")
                
                # Set state for next steps
                await self._set_current_step(WorkflowStep.STEP_2_ENTER_CASE)
                await self._set_processing_state(DocketProcessingState.EXECUTING_QUERY)
            
            # If we're not already on the docket sheet, execute the query workflow
            if not is_on_docket_sheet:
                self.court_logger.info(f"{log_prefix} Steps 2-6: Executing complete case query workflow")
                
                query_success = await self.navigation_facade.execute({
                    "action": "execute_complete_case_query_workflow",
                    "navigator": navigator,
                    "court_id": court_id,
                    "case_number": docket_num
                })
                
                if not query_success:
                    self.court_logger.error(f"{log_prefix} Steps 2-6 FAILED: Query workflow failed for {docket_num}")
                    result['error'] = f'Query workflow failed for case {docket_num}'
                    result['failed_step'] = 'steps_2_6_query_workflow'
                    return result
                
                # Add remaining workflow steps if not already added
                remaining_steps = [
                    'step_2_enter_case', 'step_3_click_query', 'step_4_select_docket',
                    'step_5_configure_options', 'step_6_run_report'
                ]
                for step in remaining_steps:
                    if step not in result['workflow_steps_completed']:
                        result['workflow_steps_completed'].append(step)
                
                self.court_logger.info(f"{log_prefix} Steps 2-6 COMPLETED: Successfully executed query workflow")
                
                # Set state for docket processing
                await self._set_current_step(WorkflowStep.STEP_7_PROCESS_DOCKET)
                await self._set_processing_state(DocketProcessingState.ON_DOCKET_SHEET)
            
            # STEP 7: Process docket sheet (whether we just navigated here or were already here)
            self.court_logger.info(f"{log_prefix} Step 7: Processing docket sheet")
            
            # Ensure step 7 is marked as completed
            if 'step_7_process_docket' not in result['workflow_steps_completed']:
                result['workflow_steps_completed'].append('step_7_process_docket')
            
            # CRITICAL FIX: Wait for page to fully load before extracting content
            # This prevents "Unable to retrieve content because the page is navigating" error
            try:
                await navigator.page.wait_for_load_state('networkidle', timeout=10000)
                # Additional wait for DOM content to be ready
                await navigator.page.wait_for_load_state('domcontentloaded', timeout=5000)
                # Small buffer to ensure page is stable
                await asyncio.sleep(0.5)
            except Exception as wait_error:
                self.court_logger.warning(f"{log_prefix} Page load wait failed: {wait_error}, continuing anyway")
            
            # Validate we're on a docket sheet page
            if not await self._validate_docket_sheet_page(navigator):
                self.court_logger.error(f"{log_prefix} Step 7 FAILED: Not on a valid docket sheet page")
                result['error'] = 'Navigation did not result in docket sheet page'
                result['failed_step'] = 'step_7_process_docket'
                return result
            
            await self._set_processing_state(DocketProcessingState.PROCESSING_DOCKET)
            
            # CRITICAL: Capture HTML immediately after validation to avoid navigation issues
            # Some PACER courts have JavaScript that triggers additional navigation after initial load
            self.court_logger.info(f"{log_prefix} Capturing HTML content immediately after validation")
            
            # Extract HTML content with retry logic for navigation issues
            html_content = None
            max_retries = 3
            for retry in range(max_retries):
                try:
                    html_content = await navigator.page.content()
                    if html_content:
                        break
                except Exception as content_error:
                    if "navigating" in str(content_error).lower():
                        self.court_logger.warning(f"{log_prefix} Page still navigating, waiting and retrying ({retry + 1}/{max_retries})")
                        # Wait for page to stabilize
                        try:
                            await navigator.page.wait_for_load_state('networkidle', timeout=5000)
                        except:
                            pass
                        await asyncio.sleep(1.0)
                    else:
                        raise
            
            if not html_content:
                self.court_logger.error(f"{log_prefix} Step 7 FAILED: No HTML content extracted after {max_retries} retries")
                result['error'] = 'No HTML content extracted from docket sheet'
                result['failed_step'] = 'step_7_process_docket'
                return result
            
            # CRITICAL: Parse HTML to extract docket entries, attorneys, and metadata
            self.court_logger.info(f"{log_prefix} Parsing HTML content to extract docket data")
            parsed_data = {}
            try:
                from src.services.html.case_parser_service import CaseParserService
                parser = CaseParserService(self.court_logger, html_content)
                # Parse the HTML to extract all critical data
                parsed_data = parser.parse_html(html_content)
                
                # Log what was extracted
                docket_entries_count = len(parsed_data.get('docket_entries', []))
                attorneys_count = len(parsed_data.get('attorney', []))
                plaintiffs_count = len(parsed_data.get('plaintiff', []))
                defendants_count = len(parsed_data.get('defendant', []))
                
                self.court_logger.info(f"{log_prefix} HTML parsed successfully - Docket entries: {docket_entries_count}, "
                                     f"Attorneys: {attorneys_count}, Plaintiffs: {plaintiffs_count}, Defendants: {defendants_count}")
                
                # Extract critical fields from parsed data
                if parsed_data:
                    # Log some key parsed fields for verification
                    if parsed_data.get('demand'):
                        self.court_logger.info(f"{log_prefix} Parsed demand: {parsed_data.get('demand')}")
                    if parsed_data.get('jury_demand'):
                        self.court_logger.info(f"{log_prefix} Parsed jury_demand: {parsed_data.get('jury_demand')}")
                    if parsed_data.get('jurisdiction'):
                        self.court_logger.info(f"{log_prefix} Parsed jurisdiction: {parsed_data.get('jurisdiction')}")
                        
            except Exception as parse_error:
                self.court_logger.error(f"{log_prefix} Failed to parse HTML: {parse_error}", exc_info=True)
                # Continue processing even if parsing fails - we still have basic data
            
            # Prepare initial details for docket processing
            # Map docket_report_log fields to expected field names
            initial_details = {
                "court_id": court_id,
                "docket_num": docket_num,
                # docket_report_log uses 'versus' field, not 'case_title'
                "case_title": docket_info.get("versus", docket_info.get("case_title", "")),
                # docket_report_log uses 'filing_date' field, not 'filed_date'
                "filed_date": docket_info.get("filing_date", docket_info.get("filed_date", "")),
                "iso_date": workflow_config.get("iso_date"),
                "_processing_source": "sequential_workflow_manager",
                **docket_info,  # Include all docket metadata
                **parsed_data   # MERGE PARSED HTML DATA INTO INITIAL DETAILS
            }
            
            # Process docket through DocketProcessor with court_logger
            docket_result = await self.docket_processor.perform_action({
                "action": "process_docket",
                "page": navigator.page,
                "initial_details": initial_details,
                "html_content": html_content,
                "court_logger": self.court_logger
            })
            
            # REQUIREMENT 2: S3 HTML Upload and CDN Link Generation
            await self._upload_html_to_s3_and_merge_link(
                html_content, docket_result, workflow_config, initial_details
            )
            
            # CRITICAL: S3 Upload Checkpoint Integration - Call at specific workflow checkpoints
            await self._execute_s3_upload_checkpoints(
                docket_result, html_content, initial_details, workflow_config
            )
            
            if not docket_result:
                self.court_logger.error(f"{log_prefix} Step 7 FAILED: Docket processor returned no result")
                result['error'] = 'Docket processor returned no result'
                result['failed_step'] = 'step_7_process_docket'
                return result
            
            result['docket_processing_result'] = docket_result
            
            # CRITICAL FIX: Check conditions for early JSON save and skip further processing
            html_only = workflow_config.get('html_only', False) or docket_result.get('html_only', False)
            is_transferred = docket_result.get('is_transferred', False)
            downloaded_docket_exists = await self._check_downloaded_docket_exists(docket_result, workflow_config)
            
            should_save_json_early = html_only or (is_transferred and downloaded_docket_exists)
            
            if should_save_json_early:
                # Save JSON BEFORE proceeding to next docket when conditions are met
                try:
                    iso_date = workflow_config.get("iso_date")
                    if iso_date:
                        # Import the enhanced file operations service to save the JSON
                        from src.pacer.services.file_operations_service import FileOperationsService
                        
                        # CRITICAL FIX: Get s3_service - try DI first, then direct creation
                        s3_service = None
                        try:
                            # Try to get from DI container first
                            from src.containers.pacer_core import PacerCoreContainer
                            container = PacerCoreContainer()
                            container.wire(modules=[__name__])
                            s3_service = container.s3_management_service()
                            await s3_service.initialize()  # Initialize the S3 service
                            self.court_logger.info(f"{log_prefix} Successfully obtained S3 service from DI container")
                        except Exception as e1:
                            # Fallback to direct creation
                            try:
                                from src.pacer.services.s3_service import S3Service
                                s3_service = S3Service(logger=self.court_logger, config=self.config)
                                await s3_service.initialize()
                                self.court_logger.info(f"{log_prefix} Created S3 service directly")
                            except Exception as e2:
                                self.court_logger.warning(f"{log_prefix} Could not create S3 service: {str(e2)}")
                        
                        # Create file ops service with S3 service from DI
                        file_ops_service = FileOperationsService(
                            logger=self.court_logger, 
                            config=self.config,
                            s3_service=s3_service  # Pass the S3 service from DI
                        )
                        await file_ops_service.initialize()
                        
                        # Extract HTML content for comprehensive merging
                        html_content = docket_result.get('html_content')
                        
                        # Extract S3 metadata from upload checkpoints for early save
                        s3_upload_data = self._extract_s3_data_from_checkpoints(docket_result)
                        
                        # CRITICAL: Include s3_html in the case data BEFORE saving
                        if s3_upload_data.get('s3_html'):
                            docket_result['s3_html'] = s3_upload_data['s3_html']
                            self.court_logger.info(f"{log_prefix} Added s3_html to case data: {s3_upload_data['s3_html']}")
                        
                        # REQUIREMENT 3: Ensure title and allegations are defaulted if missing
                        if 'title' not in docket_result or not docket_result.get('title'):
                            docket_result['title'] = ''
                        if 'allegations' not in docket_result or not docket_result.get('allegations'):
                            docket_result['allegations'] = ''
                        
                        s3_metadata = {
                            'early_save_reason': 'html_only=True' if html_only else 'is_transferred=True AND downloaded_docket_exists=True',
                            'save_trigger': 'html_only' if html_only else 'transfer_with_existing',
                            'upload_timestamp': s3_upload_data.get('upload_timestamp'),
                            's3_key': s3_upload_data.get('s3_key'),
                            's3_html': s3_upload_data.get('s3_html'),
                            's3_url': s3_upload_data.get('s3_url'),
                            'processing_step': 'step_7_early_save'
                        }
                        
                        # CRITICAL: Clean JSON data BEFORE save to match GOOD EXAMPLE schema
                        self.court_logger.info(f"{log_prefix} Step 7: Applying comprehensive JSON cleanup before early save")
                        
                        # Apply data cleaning to remove unwanted internal fields
                        cleaned_docket_result = self._apply_json_cleanup_for_schema_compliance(docket_result)
                        
                        # Save with enhanced data merging, filename validation, and JSON cleanup
                        json_path = await file_ops_service.save_case_data(
                            case_data=cleaned_docket_result,
                            iso_date=iso_date,
                            html_content=html_content,
                            s3_metadata=s3_metadata,
                            court_logger=self.court_logger  # Pass court_logger to file operations service
                        )
                        
                        # Add the saved path to the result
                        docket_result['_json_saved_path'] = json_path
                        docket_result['_json_saved_timestamp'] = datetime.now().isoformat()
                        docket_result['_enhanced_save_version'] = '2.0'
                        docket_result['_early_save_reason'] = (
                            'html_only=True' if html_only else 
                            'is_transferred=True AND downloaded_docket_exists=True'
                        )
                        result['docket_processing_result'] = docket_result
                        
                        skip_reason = (
                            "html_only mode enabled" if html_only else
                            "transferred case with existing download"
                        )
                        
                        self.court_logger.info(f"{log_prefix} Step 7: JSON saved to {json_path} EARLY due to: {skip_reason}")
                        self.court_logger.info(f"{log_prefix} Step 7: Skipping further processing - {skip_reason}")
                        
                        # Mark as completed and return early
                        await self._set_processing_state(DocketProcessingState.COMPLETED)
                        
                        end_time = datetime.now()
                        processing_time = (end_time - start_time).total_seconds()
                        
                        result.update({
                            'status': 'completed',
                            'processing_time_seconds': processing_time,
                            'end_time': end_time.isoformat(),
                            'total_steps_completed': len(result['workflow_steps_completed']),
                            'sequential_workflow_success': True,
                            'early_completion_reason': skip_reason
                        })
                        
                        return result
                        
                    else:
                        self.court_logger.warning(f"{log_prefix} Step 7: No iso_date provided, cannot save JSON early")
                        
                except Exception as save_error:
                    self.court_logger.error(f"{log_prefix} Step 7: Failed to save JSON file early: {save_error}")
                    # Don't fail the entire process for a save error, continue processing
                    docket_result['_json_save_error'] = str(save_error)
                    result['docket_processing_result'] = docket_result
            
            # If not early save, continue with full processing and save JSON at the end
            if not should_save_json_early:
                # Continue with document processing or other workflow steps here if needed
                # For now, save JSON after full processing completion
                try:
                    iso_date = workflow_config.get("iso_date")
                    if iso_date:
                        # Import the enhanced file operations service to save the JSON
                        from src.pacer.services.file_operations_service import FileOperationsService
                        
                        # CRITICAL FIX: Get s3_service - try DI first, then direct creation
                        s3_service = None
                        try:
                            # Try to get from DI container first
                            from src.containers.pacer_core import PacerCoreContainer
                            container = PacerCoreContainer()
                            container.wire(modules=[__name__])
                            s3_service = container.s3_management_service()
                            await s3_service.initialize()  # Initialize the S3 service
                            self.court_logger.info(f"{log_prefix} Successfully obtained S3 service from DI container")
                        except Exception as e1:
                            # Fallback to direct creation
                            try:
                                from src.pacer.services.s3_service import S3Service
                                s3_service = S3Service(logger=self.court_logger, config=self.config)
                                await s3_service.initialize()
                                self.court_logger.info(f"{log_prefix} Created S3 service directly")
                            except Exception as e2:
                                self.court_logger.warning(f"{log_prefix} Could not create S3 service: {str(e2)}")
                        
                        # Create file ops service with S3 service from DI
                        file_ops_service = FileOperationsService(
                            logger=self.court_logger, 
                            config=self.config,
                            s3_service=s3_service  # Pass the S3 service from DI
                        )
                        await file_ops_service.initialize()
                        
                        # Extract HTML content for comprehensive merging
                        html_content = docket_result.get('html_content')
                        
                        # Extract S3 metadata from upload checkpoints for complete processing save
                        s3_upload_data = self._extract_s3_data_from_checkpoints(docket_result)
                        
                        # CRITICAL: Include s3_html in the case data BEFORE saving
                        if s3_upload_data.get('s3_html'):
                            docket_result['s3_html'] = s3_upload_data['s3_html']
                            self.court_logger.info(f"{log_prefix} Added s3_html to case data: {s3_upload_data['s3_html']}")
                        
                        # REQUIREMENT 3: Ensure title and allegations are defaulted if missing
                        if 'title' not in docket_result or not docket_result.get('title'):
                            docket_result['title'] = ''
                        if 'allegations' not in docket_result or not docket_result.get('allegations'):
                            docket_result['allegations'] = ''
                        
                        s3_metadata = {
                            'save_trigger': 'complete_processing',
                            'upload_timestamp': s3_upload_data.get('upload_timestamp'),
                            's3_key': s3_upload_data.get('s3_key'),
                            's3_html': s3_upload_data.get('s3_html'),
                            's3_url': s3_upload_data.get('s3_url'),
                            'processing_step': 'step_7_complete_save',
                            'download_completed': docket_result.get('_download_completed', False),
                            'documents_processed': docket_result.get('_documents_processed', 0)
                        }
                        
                        # Save with enhanced data merging, filename validation, and JSON cleanup
                        self.court_logger.info(f"{log_prefix} Step 7: Applying comprehensive JSON cleanup before save")

                        # Apply data cleaning to remove unwanted internal fields
                        cleaned_docket_result = self._apply_json_cleanup_for_schema_compliance(docket_result)
                        json_path = await file_ops_service.save_case_data(
                            case_data=cleaned_docket_result,
                            iso_date=iso_date,
                            html_content=html_content,
                            s3_metadata=s3_metadata,
                            court_logger=self.court_logger  # Pass court_logger to file operations service
                        )
                        
                        # Add the saved path to the result
                        docket_result['_json_saved_path'] = json_path
                        docket_result['_json_saved_timestamp'] = datetime.now().isoformat()
                        docket_result['_enhanced_save_version'] = '2.0'
                        result['docket_processing_result'] = docket_result  # Update result with JSON save info
                        
                        self.court_logger.info(f"{log_prefix} Step 7: Enhanced JSON saved to {json_path} AFTER complete docket processing", {
                            "has_html_merged": html_content is not None,
                            "enhanced_save": True
                        })
                    else:
                        self.court_logger.warning(f"{log_prefix} Step 7: No iso_date provided, skipping JSON save")
                        
                except Exception as save_error:
                    self.court_logger.error(f"{log_prefix} Step 7: Failed to save JSON file: {save_error}")
                    # Don't fail the entire process for a save error, just log it
                    docket_result['_json_save_error'] = str(save_error)
                    result['docket_processing_result'] = docket_result  # Update result with save error info
            
            # SUCCESS: Mark as completed
            await self._set_processing_state(DocketProcessingState.COMPLETED)
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            result.update({
                'status': 'completed',
                'processing_time_seconds': processing_time,
                'end_time': end_time.isoformat(),
                'total_steps_completed': len(result['workflow_steps_completed']),
                'sequential_workflow_success': True
            })
            
            self.court_logger.info(f"{log_prefix} Step 7 COMPLETED: Docket processing successful")
            self.court_logger.info(f"{log_prefix} SEQUENTIAL WORKFLOW COMPLETED in {processing_time:.2f}s")
            
            return result
            
        except Exception as e:
            await self._set_processing_state(DocketProcessingState.FAILED)
            
            error_msg = f"Sequential workflow failed: {e}"
            self.court_logger.error(f"{log_prefix} {error_msg}", exc_info=True)
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            result.update({
                'error': str(e),
                'processing_time_seconds': processing_time,
                'end_time': end_time.isoformat(),
                'failed_step': self._current_step.value if self._current_step else 'unknown',
                'sequential_workflow_success': False
            })
            
            return result

    async def _return_to_query_page_for_next_docket(self, court_id: str, navigator: Any) -> bool:
        """
        Navigate back to Query page for processing the next docket.
        
        This implements the "Return and Continue" logic that ensures each docket
        processing starts from a clean Query page state.
        
        Args:
            court_id: Court identifier
            navigator: PacerNavigator instance
            
        Returns:
            True if navigation successful, False otherwise
        """
        log_prefix = f"[{court_id}] RETURN_AND_CONTINUE:"
        
        try:
            self.court_logger.info(f"{log_prefix} Returning to Query page for next docket")
            
            # Use navigation facade to return to query page
            navigation_success = await self.navigation_facade.execute({
                "action": "go_to_query_page",
                "navigator": navigator,
                "court_id": court_id
            })
            
            if navigation_success:
                # Validate we're actually on the query page
                await navigator.page.wait_for_load_state('networkidle', timeout=5000)
                current_url = navigator.page.url
                
                if 'iquery.pl' in current_url:
                    self.court_logger.info(f"{log_prefix} Successfully returned to Query page: {current_url}")
                    return True
                else:
                    self.court_logger.warning(f"{log_prefix} Navigation completed but not on query page: {current_url}")
                    return False
            else:
                self.court_logger.error(f"{log_prefix} Navigation to Query page failed")
                return False
                
        except Exception as e:
            self.court_logger.error(f"{log_prefix} Error returning to Query page: {e}", exc_info=True)
            return False

    async def _validate_docket_sheet_page(self, navigator: Any) -> bool:
        """
        Validate that we're on a valid docket sheet page.
        
        Args:
            navigator: PacerNavigator instance
            
        Returns:
            True if on docket sheet page, False otherwise
        """
        try:
            current_url = navigator.page.url
            
            # Check for DktRpt.pl in URL (main indicator)
            if "DktRpt.pl" in current_url:
                return True
            
            # Check for other docket sheet URL patterns
            docket_patterns = [
                "/cgi-bin/DktRpt",
                "DocketReport",
                "docket_report"
            ]
            
            for pattern in docket_patterns:
                if pattern in current_url:
                    return True
            
            # Check page content for docket sheet indicators with error handling
            try:
                # CRITICAL FIX: Add page state validation before accessing page.title()
                if navigator.page and not navigator.page.is_closed():
                    page_title = await navigator.page.title()
                    if "docket" in page_title.lower():
                        return True
                else:
                    self.log_debug("Page context is destroyed or closed - skipping title validation for docket sheet")
            except Exception as title_error:
                # Handle execution context destroyed and other page access errors
                if "execution context was destroyed" in str(title_error).lower():
                    self.log_debug("Page title unavailable - execution context destroyed during docket sheet validation")
                else:
                    self.log_debug(f"Failed to get page title for docket sheet validation: {title_error}")
                # Continue without title validation since we have URL validation above
            
            return False
            
        except Exception as e:
            self.log_error(f"Error validating docket sheet page: {e}")
            return False

    async def _set_processing_state(self, state: DocketProcessingState) -> None:
        """Set the current processing state."""
        self._current_state = state
        if self.court_logger:
            self.court_logger.debug(f"Processing state changed to: {state.value}")

    async def _set_current_step(self, step: WorkflowStep) -> None:
        """Set the current workflow step."""
        self._current_step = step
        if self.court_logger:
            self.court_logger.debug(f"Current workflow step: {step.value}")

    def get_current_processing_state(self) -> Dict[str, Any]:
        """Get current processing state and statistics."""
        return {
            'current_state': self._current_state.value,
            'current_step': self._current_step.value if self._current_step else None,
            'current_docket_index': self._current_docket_index,
            'total_dockets': len(self._docket_queue),
            'processing_stats': self._processing_stats,
            'processing_context': {
                'court_id': self._processing_context.get('court_id'),
                'start_time': self._processing_context.get('start_time').isoformat() if self._processing_context.get('start_time') else None,
                'total_dockets': self._processing_context.get('total_dockets', 0)
            }
        }

    async def reset_workflow_state(self) -> Dict[str, Any]:
        """Reset workflow state for new processing session."""
        self._current_state = DocketProcessingState.PENDING
        self._current_step = None
        self._docket_queue = []
        self._current_docket_index = 0
        self._processing_context = {}
        self._processing_stats = {
            'total_dockets': 0,
            'completed_dockets': 0,
            'failed_dockets': 0,
            'skipped_dockets': 0,
            'processing_errors': []
        }
        
        if self.court_logger:
            self.court_logger.info("Sequential workflow state has been reset")
        
        return {"status": "reset_complete", "message": "Workflow state has been reset"}

    async def _check_downloaded_docket_exists(self, docket_result: Dict[str, Any], workflow_config: Dict[str, Any]) -> bool:
        """
        Check if a docket has already been downloaded (artifacts exist).
        
        This checks for the existence of downloaded artifacts like PDFs, ZIPs, or MD files
        that would indicate the docket has already been processed and downloaded.
        
        Args:
            docket_result: The docket processing result containing case details
            workflow_config: Workflow configuration containing iso_date and other settings
            
        Returns:
            True if downloaded docket artifacts exist, False otherwise
        """
        try:
            import os
            from pathlib import Path
            
            # Get necessary information
            court_id = docket_result.get('court_id', '')
            docket_num = docket_result.get('docket_num', '')
            # Generate proper base_filename for artifact checking: {court_id}_{YY}_{NNNNN} ONLY
            if docket_num and ':' in docket_num:
                try:
                    from src.utils.docket_utils import parse_docket_number_numeric_only
                    clean_docket = parse_docket_number_numeric_only(docket_num)
                    default_base_filename = f"{court_id}_{clean_docket}"
                except Exception as e:
                    self.court_logger.warning(f"Could not parse docket number numerically: {e}, using fallback")
                    default_base_filename = f"{court_id}_{docket_num}".replace(':', '_').replace('-', '_')
            else:
                default_base_filename = f"{court_id}_{docket_num}".replace(':', '_').replace('/', '_')
            base_filename = docket_result.get('base_filename', default_base_filename)
            iso_date = workflow_config.get('iso_date')
            
            if not all([court_id, docket_num, iso_date]):
                self.court_logger.warning(f"Missing required info for artifact check: court_id={court_id}, docket_num={docket_num}, iso_date={iso_date}")
                return False
            
            # Define paths to check for artifacts
            base_dir = Path('./data') / iso_date
            
            # Check for different artifact types that indicate a completed download
            artifact_paths = [
                base_dir / 'dockets' / f"{base_filename}.pdf",
                base_dir / 'dockets' / f"{base_filename}.zip", 
                base_dir / 'dockets' / f"{base_filename}.md",
                base_dir / 'html' / f"{base_filename}.html",
                # Check court-specific directories as well
                base_dir / 'courts' / court_id / f"{base_filename}.pdf",
                base_dir / 'courts' / court_id / f"{base_filename}.zip",
                base_dir / 'courts' / court_id / f"{base_filename}.md"
            ]
            
            # Check if any artifacts exist
            artifacts_found = []
            for artifact_path in artifact_paths:
                if artifact_path.exists():
                    artifacts_found.append(str(artifact_path.name))
            
            if artifacts_found:
                self.court_logger.info(f"Downloaded docket artifacts found for {base_filename}: {', '.join(artifacts_found)}")
                return True
            else:
                self.court_logger.debug(f"No downloaded docket artifacts found for {base_filename}")
                return False
                
        except Exception as e:
            self.court_logger.error(f"Error checking for downloaded docket existence: {e}")
            return False

    async def _execute_s3_upload_checkpoints(
        self,
        docket_result: Dict[str, Any],
        html_content: str,
        initial_details: Dict[str, Any],
        workflow_config: Dict[str, Any]
    ) -> None:
        """
        Execute S3 HTML upload at specific workflow checkpoints using idempotent upload method.
        
        This method checks all 4 checkpoints and triggers uploads as needed:
        1. Step 9: Relevance Check - after relevance_service determines case is relevant
        2. Step 10: Transfer Check - after transfer check identifies is_transferred cases  
        3. Step 11: Ignore Download Service - after ignore_download_service check returns html_only: true
        4. Step 12: Download Workflow - before/during execute_download_workflow
        """
        if not docket_result or not html_content:
            return
        
        court_id = initial_details.get("court_id")
        docket_num = initial_details.get("docket_num")
        iso_date = workflow_config.get("iso_date")
        
        log_prefix = f"[{court_id}][{docket_num}] S3_CHECKPOINTS:"
        
        if not all([court_id, docket_num, iso_date]):
            self.court_logger.warning(f"{log_prefix} Missing required data for S3 upload: court_id={court_id}, docket_num={docket_num}, iso_date={iso_date}")
            return
        
        # Execute checkpoints in sequence
        try:
            # Step 9: Relevance Check Checkpoint
            await self._checkpoint_9_relevance_check(
                docket_result, html_content, initial_details, workflow_config
            )
            
            # Step 10: Transfer Check Checkpoint
            await self._checkpoint_10_transfer_check(
                docket_result, html_content, initial_details, workflow_config
            )
            
            # Step 11: Ignore Download Service Checkpoint
            await self._checkpoint_11_ignore_download_service(
                docket_result, html_content, initial_details, workflow_config
            )
            
            # Step 12: Download Workflow Checkpoint
            await self._checkpoint_12_download_workflow(
                docket_result, html_content, initial_details, workflow_config
            )
            
        except Exception as e:
            self.court_logger.error(f"{log_prefix} Error executing S3 upload checkpoints: {e}", exc_info=True)
            # Update docket result with checkpoint execution error
            if "s3_upload_checkpoints" not in docket_result:
                docket_result["s3_upload_checkpoints"] = {}
            docket_result["s3_upload_checkpoints"]["execution_error"] = {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _checkpoint_9_relevance_check(
        self,
        docket_result: Dict[str, Any],
        html_content: str,
        initial_details: Dict[str, Any],
        workflow_config: Dict[str, Any]
    ) -> None:
        """
        Step 9: Relevance Check - Upload HTML to S3 after relevance_service determines case is relevant.
        
        Trigger condition: case is marked as relevant by relevance service
        """
        court_id = initial_details.get("court_id")
        docket_num = initial_details.get("docket_num")
        log_prefix = f"[{court_id}][{docket_num}] CHECKPOINT_9:"
        
        # Check trigger condition: case is relevant
        is_relevant = docket_result.get("is_relevant", False)
        relevance_score = docket_result.get("relevance_score", 0.0)
        relevance_reason = docket_result.get("relevance_reason", "")
        
        if is_relevant:
            self.court_logger.info(f"{log_prefix} TRIGGERED - Relevance check passed (score: {relevance_score}, reason: {relevance_reason})")
            
            upload_result = await self._upload_html_to_s3_idempotent(
                html_content, docket_result, initial_details, workflow_config,
                checkpoint_name="Step_9_Relevance_Check",
                trigger_condition=f"is_relevant=True (score: {relevance_score})"
            )
            
            # Update docket result with checkpoint info
            if "s3_upload_checkpoints" not in docket_result:
                docket_result["s3_upload_checkpoints"] = {}
            docket_result["s3_upload_checkpoints"]["step_9_relevance"] = {
                "triggered": True,
                "condition": f"is_relevant=True (score: {relevance_score})",
                "upload_result": upload_result,
                "timestamp": datetime.now().isoformat()
            }
        else:
            self.court_logger.debug(f"{log_prefix} Not triggered - Case not relevant (score: {relevance_score})")
            # Record checkpoint as not triggered
            if "s3_upload_checkpoints" not in docket_result:
                docket_result["s3_upload_checkpoints"] = {}
            docket_result["s3_upload_checkpoints"]["step_9_relevance"] = {
                "triggered": False,
                "condition": f"is_relevant=False (score: {relevance_score})",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _checkpoint_10_transfer_check(
        self,
        docket_result: Dict[str, Any],
        html_content: str,
        initial_details: Dict[str, Any],
        workflow_config: Dict[str, Any]
    ) -> None:
        """
        Step 10: Transfer Check - Upload HTML to S3 after transfer check identifies is_transferred cases.
        
        Trigger condition: case.is_transferred == true
        """
        court_id = initial_details.get("court_id")
        docket_num = initial_details.get("docket_num")
        log_prefix = f"[{court_id}][{docket_num}] CHECKPOINT_10:"
        
        # Check trigger condition: case is transferred
        is_transferred = docket_result.get("is_transferred", False)
        transfer_court = docket_result.get("transfer_court", "")
        transfer_reason = docket_result.get("transfer_reason", "")
        
        if is_transferred:
            self.court_logger.info(f"{log_prefix} TRIGGERED - Transfer case detected (court: {transfer_court}, reason: {transfer_reason})")
            
            upload_result = await self._upload_html_to_s3_idempotent(
                html_content, docket_result, initial_details, workflow_config,
                checkpoint_name="Step_10_Transfer_Check",
                trigger_condition=f"is_transferred=True (court: {transfer_court})"
            )
            
            # Update docket result with checkpoint info
            if "s3_upload_checkpoints" not in docket_result:
                docket_result["s3_upload_checkpoints"] = {}
            docket_result["s3_upload_checkpoints"]["step_10_transfer"] = {
                "triggered": True,
                "condition": f"is_transferred=True (court: {transfer_court})",
                "upload_result": upload_result,
                "timestamp": datetime.now().isoformat()
            }
        else:
            self.court_logger.debug(f"{log_prefix} Not triggered - Case not transferred")
            # Record checkpoint as not triggered
            if "s3_upload_checkpoints" not in docket_result:
                docket_result["s3_upload_checkpoints"] = {}
            docket_result["s3_upload_checkpoints"]["step_10_transfer"] = {
                "triggered": False,
                "condition": "is_transferred=False",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _checkpoint_11_ignore_download_service(
        self,
        docket_result: Dict[str, Any],
        html_content: str,
        initial_details: Dict[str, Any],
        workflow_config: Dict[str, Any]
    ) -> None:
        """
        Step 11: Ignore Download Service - Upload HTML to S3 after ignore_download_service check.
        
        Trigger condition: service returns html_only: true
        """
        court_id = initial_details.get("court_id")
        docket_num = initial_details.get("docket_num")
        log_prefix = f"[{court_id}][{docket_num}] CHECKPOINT_11:"
        
        # Check trigger conditions: html_only or ignore_download flags
        html_only = docket_result.get("html_only", False) or workflow_config.get("html_only", False)
        ignore_download = docket_result.get("ignore_download", False)
        ignore_reason = docket_result.get("ignore_download_reason", "")
        
        if html_only or ignore_download:
            trigger_reason = []
            if html_only:
                trigger_reason.append("html_only=True")
            if ignore_download:
                trigger_reason.append(f"ignore_download=True (reason: {ignore_reason})")
            
            trigger_description = ", ".join(trigger_reason)
            self.court_logger.info(f"{log_prefix} TRIGGERED - Ignore download service ({trigger_description})")
            
            upload_result = await self._upload_html_to_s3_idempotent(
                html_content, docket_result, initial_details, workflow_config,
                checkpoint_name="Step_11_Ignore_Download_Service",
                trigger_condition=trigger_description
            )
            
            # Update docket result with checkpoint info
            if "s3_upload_checkpoints" not in docket_result:
                docket_result["s3_upload_checkpoints"] = {}
            docket_result["s3_upload_checkpoints"]["step_11_ignore_download"] = {
                "triggered": True,
                "condition": trigger_description,
                "upload_result": upload_result,
                "timestamp": datetime.now().isoformat()
            }
        else:
            self.court_logger.debug(f"{log_prefix} Not triggered - Download not ignored (html_only={html_only}, ignore_download={ignore_download})")
            # Record checkpoint as not triggered
            if "s3_upload_checkpoints" not in docket_result:
                docket_result["s3_upload_checkpoints"] = {}
            docket_result["s3_upload_checkpoints"]["step_11_ignore_download"] = {
                "triggered": False,
                "condition": f"html_only={html_only}, ignore_download={ignore_download}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _checkpoint_12_download_workflow(
        self,
        docket_result: Dict[str, Any],
        html_content: str,
        initial_details: Dict[str, Any],
        workflow_config: Dict[str, Any]
    ) -> None:
        """
        Step 12: Download Workflow - Upload HTML to S3 before/during execute_download_workflow.
        
        Trigger condition: download workflow is initiated
        """
        court_id = initial_details.get("court_id")
        docket_num = initial_details.get("docket_num")
        log_prefix = f"[{court_id}][{docket_num}] CHECKPOINT_12:"
        
        # Check trigger conditions: download workflow indicators
        requires_download = docket_result.get("requires_download", False)
        download_initiated = docket_result.get("download_initiated", False)
        download_count = docket_result.get("download_count", 0)
        processing_status = docket_result.get("processing_status", "")
        
        # Consider workflow initiated if any download indicators are present
        workflow_initiated = (
            requires_download or
            download_initiated or
            download_count > 0 or
            processing_status in ["completed", "processing", "downloading"]
        )
        
        if workflow_initiated:
            trigger_conditions = []
            if requires_download:
                trigger_conditions.append("requires_download=True")
            if download_initiated:
                trigger_conditions.append("download_initiated=True")
            if download_count > 0:
                trigger_conditions.append(f"download_count={download_count}")
            if processing_status in ["completed", "processing", "downloading"]:
                trigger_conditions.append(f"processing_status={processing_status}")
            
            trigger_description = ", ".join(trigger_conditions)
            self.court_logger.info(f"{log_prefix} TRIGGERED - Download workflow initiated ({trigger_description})")
            
            upload_result = await self._upload_html_to_s3_idempotent(
                html_content, docket_result, initial_details, workflow_config,
                checkpoint_name="Step_12_Download_Workflow",
                trigger_condition=trigger_description
            )
            
            # Update docket result with checkpoint info
            if "s3_upload_checkpoints" not in docket_result:
                docket_result["s3_upload_checkpoints"] = {}
            docket_result["s3_upload_checkpoints"]["step_12_download_workflow"] = {
                "triggered": True,
                "condition": trigger_description,
                "upload_result": upload_result,
                "timestamp": datetime.now().isoformat()
            }
        else:
            self.court_logger.debug(f"{log_prefix} Not triggered - Download workflow not initiated (requires_download={requires_download}, download_count={download_count}, status={processing_status})")
            # Record checkpoint as not triggered
            if "s3_upload_checkpoints" not in docket_result:
                docket_result["s3_upload_checkpoints"] = {}
            docket_result["s3_upload_checkpoints"]["step_12_download_workflow"] = {
                "triggered": False,
                "condition": f"requires_download={requires_download}, download_count={download_count}, status={processing_status}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _upload_html_to_s3_idempotent(
        self,
        html_content: str,
        docket_result: Dict[str, Any],
        initial_details: Dict[str, Any],
        workflow_config: Dict[str, Any],
        checkpoint_name: str,
        trigger_condition: str
    ) -> Dict[str, Any]:
        """
        Upload HTML to S3 using the idempotent upload method from CaseProcessingService.
        
        Returns:
            Dict containing upload result with metadata
        """
        court_id = initial_details.get("court_id")
        docket_num = initial_details.get("docket_num")
        iso_date = workflow_config.get("iso_date")
        
        log_prefix = f"[{court_id}][{docket_num}] S3_UPLOAD:"
        
        try:
            # Get or create case processing service for idempotent upload
            case_processing_service = None
            
            # Try to get from docket processor first
            if hasattr(self.docket_processor, 'case_processing_service'):
                case_processing_service = self.docket_processor.case_processing_service
            
            # Create new instance if not available
            if not case_processing_service:
                from src.pacer.services.case_processing_service import CaseProcessingService
                # Get S3 service from container for proper S3 functionality
                try:
                    from src.containers import storage
                    from src.pacer.services.s3_service import S3Service
                    storage_container = storage.StorageContainer()
                    storage_container.config.from_dict(self.config or {})
                    s3_service = S3Service(logger=self.court_logger, config=self.config, s3_async_storage=storage_container.s3_async_storage())
                    await s3_service.initialize()
                except Exception as e:
                    self.court_logger.warning(f"Could not initialize S3 service: {e}")
                    s3_service = None
                
                case_processing_service = CaseProcessingService(
                    logger=self.court_logger, 
                    config=self.config,
                    s3_service=s3_service
                )
                await case_processing_service.initialize()
            
            # Prepare case details for S3 upload
            case_details = {
                "court_id": court_id,
                "docket_num": docket_num,
                "base_filename": docket_result.get("base_filename") or self._generate_base_filename_fallback(court_id, docket_num),
                **initial_details,
                **docket_result
            }
            
            self.court_logger.info(f"{log_prefix} Executing idempotent S3 upload for {checkpoint_name} (trigger: {trigger_condition})")
            
            # Call idempotent upload method
            upload_result = await case_processing_service.upload_html_to_s3_idempotent(
                html_content=html_content,
                case_details=case_details,
                iso_date=iso_date,
                court_logger=self.court_logger
            )
            
            if upload_result.get("success"):
                if upload_result.get("already_uploaded"):
                    self.court_logger.info(f"{log_prefix} {checkpoint_name} - HTML already uploaded (idempotent)")
                else:
                    self.court_logger.info(f"{log_prefix} {checkpoint_name} - HTML successfully uploaded to S3")
            else:
                error_msg = upload_result.get("error", "Unknown error")
                self.court_logger.error(f"{log_prefix} {checkpoint_name} - HTML upload failed: {error_msg}")
            
            # Add checkpoint context to upload result
            upload_result["checkpoint"] = {
                "name": checkpoint_name,
                "trigger_condition": trigger_condition,
                "timestamp": datetime.now().isoformat()
            }
            
            return upload_result
            
        except Exception as e:
            error_msg = f"Idempotent S3 upload failed for {checkpoint_name}: {str(e)}"
            self.court_logger.error(f"{log_prefix} {error_msg}", exc_info=True)
            
            return {
                "success": False,
                "error": error_msg,
                "checkpoint": {
                    "name": checkpoint_name,
                    "trigger_condition": trigger_condition,
                    "timestamp": datetime.now().isoformat()
                },
                "exception_type": type(e).__name__
            }

    async def get_workflow_statistics(self) -> Dict[str, Any]:
        """Get comprehensive workflow statistics."""
        return {
            "sequential_processing": {
                "current_state": self._current_state.value,
                "current_step": self._current_step.value if self._current_step else None,
                "queue_length": len(self._docket_queue),
                "current_index": self._current_docket_index
            },
            "processing_stats": self._processing_stats,
            "workflow_performance": {
                "average_processing_time": 0,  # Could be calculated from processing history
                "success_rate": (self._processing_stats['completed_dockets'] / self._processing_stats['total_dockets']) * 100 if self._processing_stats['total_dockets'] > 0 else 0,
                "error_rate": (self._processing_stats['failed_dockets'] / self._processing_stats['total_dockets']) * 100 if self._processing_stats['total_dockets'] > 0 else 0
            }
        }

    def _extract_s3_data_from_checkpoints(self, docket_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract S3 upload data from checkpoint results for data merging.
        
        Args:
            docket_result: The docket processing result containing s3_upload_checkpoints
            
        Returns:
            Dict containing extracted S3 data for metadata merging
        """
        s3_data = {
            's3_key': None,
            's3_html': None, 
            's3_url': None,
            'upload_timestamp': None
        }
        
        # Get S3 upload checkpoints
        s3_checkpoints = docket_result.get('s3_upload_checkpoints', {})
        
        # Look for successful uploads in checkpoints (priority order)
        checkpoint_priority = [
            'step_9_relevance',      # Most recent/important
            'step_6_transfers',      # Transfer processing
            'step_5_ignore_list',    # Ignore list processing
            'step_4_downloads'       # Download processing
        ]
        
        for checkpoint_name in checkpoint_priority:
            checkpoint_data = s3_checkpoints.get(checkpoint_name, {})
            if not checkpoint_data.get('triggered', False):
                continue
                
            upload_result = checkpoint_data.get('upload_result', {})
            if not upload_result.get('success', False):
                continue
            
            # Extract S3 data from successful upload
            s3_data['s3_key'] = upload_result.get('s3_key')
            s3_data['s3_html'] = upload_result.get('s3_html')
            s3_data['s3_url'] = upload_result.get('s3_url')
            s3_data['upload_timestamp'] = checkpoint_data.get('timestamp')
            
            # If we found s3_html, we have what we need
            if s3_data['s3_html']:
                # Log successful extraction for debugging
                if hasattr(self, 'court_logger') and self.court_logger:
                    self.court_logger.debug(f"Extracted s3_html from checkpoint {checkpoint_name}: {s3_data['s3_html']}")
                break
        
        return s3_data

    def _apply_json_cleanup_for_schema_compliance(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply JSON cleanup to ensure final output matches GOOD EXAMPLE schema.
        
        Per pacer_prompt.md Requirement 3:
        - Remove _html_content field (MUST BE DELETED)
        - Remove s3_upload_checkpoints, s3_upload, processing_metadata, _processing_phase
        - Add default empty string values for title and allegations if missing
        - Keep some top-level _ prefixed keys that ARE part of the correct schema
        
        Args:
            data: Raw case data dictionary
            
        Returns:
            Cleaned data dictionary ready for JSON save
        """
        cleaned_data = data.copy()
        
        # REQUIREMENT 3: Remove specific unwanted fields (CRITICAL CLEANUP)
        fields_to_remove = [
            # HTML content must be deleted
            '_html_content',
            # Internal processing metadata must be deleted
            's3_upload_checkpoints', 's3_upload', 'processing_metadata', '_processing_phase',
            'workflow_metadata', 'checkpoint_metadata', 'upload_metadata',
            # HTML parsing metadata (internal processing)
            '_html_parsed', '_html_length', '_parsing_method', '_parsed_at',
            # Case extraction metadata (internal processing) 
            '_case_extracted', '_extraction_timestamp', '_extraction_method',
            # Docket parsing metadata (internal processing)
            '_docket_parsed', '_parsed_timestamp',
            # Data validation and merging metadata (internal processing)
            '_data_validation_status', '_record_created_timestamp', 
            '_record_merge_complete', '_merger_version',
            # Internal processing timestamps
            's3_upload_timestamp', '_processed_timestamp'
        ]
        
        removed_fields = []
        for field in fields_to_remove:
            if field in cleaned_data:
                cleaned_data.pop(field, None)
                removed_fields.append(field)
        
        if removed_fields and self.court_logger:
            self.court_logger.debug(f"Pre-save cleanup: Removed internal fields: {removed_fields}")
        
        # REQUIREMENT 3: Add default fields if missing
        if 'title' not in cleaned_data or cleaned_data.get('title') is None:
            cleaned_data['title'] = ''
            
        if 'allegations' not in cleaned_data or cleaned_data.get('allegations') is None:
            cleaned_data['allegations'] = ''
        
        return cleaned_data

    def _generate_base_filename_fallback(self, court_id: str, docket_num: str) -> str:
        """
        Generate base_filename: {court_id}_{YY}_{NNNNN} format ONLY.
        
        For S3 uploads and artifact checking - NO versus, NO extra fields.
        """
        if docket_num and ':' in docket_num:
            try:
                from src.utils.docket_utils import parse_docket_number_numeric_only
                clean_docket = parse_docket_number_numeric_only(docket_num)
                return f"{court_id}_{clean_docket}"
            except Exception as e:
                if self.court_logger:
                    self.court_logger.warning(f"Could not parse docket number numerically: {e}, using fallback")
                return f"{court_id}_{docket_num}".replace(':', '_').replace('-', '_')
        else:
            return f"{court_id}_{docket_num}".replace(':', '_').replace('/', '_')
    
    async def _upload_html_to_s3_and_merge_link(
        self, 
        html_content: str, 
        docket_result: Dict[str, Any], 
        workflow_config: Dict[str, Any],
        initial_details: Dict[str, Any]
    ) -> None:
        """
        REQUIREMENT 2: Upload raw HTML to S3 and merge CDN link into case data.
        
        This method implements the core S3 upload workflow:
        1. Upload HTML content to S3 with key format: {iso_date}/html/{base_filename}.html  
        2. Generate CDN link: "https://cdn.lexgenius.ai/" + s3_key
        3. Merge s3_html field into main JSON data
        
        Args:
            html_content: Raw HTML content to upload
            docket_result: Case data dictionary to merge s3_html into
            workflow_config: Configuration containing iso_date
            initial_details: Initial case details
        """
        court_id = initial_details.get('court_id', 'unknown')
        docket_num = initial_details.get('docket_num', 'unknown')
        iso_date = workflow_config.get('iso_date')
        
        log_prefix = f"[{court_id}][{docket_num}] S3_HTML_UPLOAD:"
        
        if not html_content or not iso_date:
            self.court_logger.warning(f"{log_prefix} Cannot upload HTML - missing content or iso_date")
            return
        
        try:
            # Get or create case processing service for S3 upload
            case_processing_service = None
            
            # Try to get from docket processor first
            if hasattr(self.docket_processor, 'case_processing_service'):
                case_processing_service = self.docket_processor.case_processing_service
            
            # Create new instance if not available
            if not case_processing_service:
                from src.pacer.services.case_processing_service import CaseProcessingService
                # Get S3 service from container for proper S3 functionality
                try:
                    from src.containers import storage
                    from src.pacer.services.s3_service import S3Service
                    storage_container = storage.StorageContainer()
                    storage_container.config.from_dict(self.config or {})
                    s3_service = S3Service(logger=self.court_logger, config=self.config, s3_async_storage=storage_container.s3_async_storage())
                    await s3_service.initialize()
                except Exception as e:
                    self.court_logger.warning(f"Could not initialize S3 service: {e}")
                    s3_service = None
                
                case_processing_service = CaseProcessingService(
                    logger=self.court_logger, 
                    config=self.config,
                    s3_service=s3_service
                )
                await case_processing_service.initialize()
            
            # REQUIREMENT 1: Log HTML parsing results summary
            docket_entries_count = len(docket_result.get('docket_entries', []))
            attorneys_count = len(docket_result.get('attorneys', docket_result.get('attorney', [])))
            self.court_logger.info(f"{log_prefix} HTML parsing results - Docket entries: {docket_entries_count}, Attorneys: {attorneys_count}, HTML size: {len(html_content)} chars")
            
            # Call idempotent upload method
            upload_result = await case_processing_service.upload_html_to_s3_idempotent(
                html_content=html_content,
                case_details={**initial_details, **docket_result},
                iso_date=iso_date,
                court_logger=self.court_logger
            )
            
            if upload_result.get('success'):
                # REQUIREMENT 2: Merge s3_html CDN link into case data
                s3_html = upload_result.get('s3_html')
                if s3_html:
                    docket_result['s3_html'] = s3_html
                    self.court_logger.info(f"{log_prefix} Successfully merged s3_html CDN link: {s3_html}")
                else:
                    self.court_logger.warning(f"{log_prefix} Upload successful but no s3_html CDN link returned")
            else:
                error_msg = upload_result.get('error', 'Unknown upload error')
                self.court_logger.error(f"{log_prefix} HTML upload failed: {error_msg}")
                # Don't fail processing, just log the error
                
        except Exception as e:
            self.court_logger.error(f"{log_prefix} Exception during HTML upload: {e}", exc_info=True)
            # Don't fail processing for upload errors