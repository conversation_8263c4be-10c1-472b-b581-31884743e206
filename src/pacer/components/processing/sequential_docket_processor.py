"""
Sequential Docket Processor - Implementation of 6-Step Sequential Workflow Architecture

This module implements the complete sequential docket processing workflow with robust
state management, error handling, and integration with existing PACER infrastructure.

Workflow Steps:
1. Parse HTML Step - Extract and validate HTML content
2. Relevance Check Step - Apply relevance logic with S3 upload
3. Transfer Handling Step - Multi-conditional logic with transferor checks  
4. Ignore List Check Step - Apply ignore_download rules
5. Document Download Step - Download case documents
6. Return and Continue Step - Navigate back to Query page

Architecture: Sequential state machine with fail-safe navigation
Integration: Existing services via dependency injection
Logging: Court-specific logging throughout all steps
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional, List

from playwright.async_api import BrowserContext, Page

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError


class WorkflowState(Enum):
    """Enumeration of workflow step states"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class WorkflowContext:
    """Shared context across all workflow steps"""
    court_id: str
    docket_number: str
    html_content: str
    case_details: Dict[str, Any]
    browser_context: BrowserContext
    court_logger: logging.Logger
    config: Dict[str, Any]
    step_results: Dict[str, Any] = field(default_factory=dict)
    processing_start_time: datetime = field(default_factory=datetime.now)


class StepResult:
    """Result object for workflow step execution"""
    
    def __init__(self, 
                 success: bool, 
                 data: Dict[str, Any] = None, 
                 error: str = None,
                 next_step: str = None,
                 skip_to_return: bool = False):
        self.success = success
        self.data = data or {}
        self.error = error
        self.next_step = next_step
        self.skip_to_return = skip_to_return  # Force jump to return step


class WorkflowStep(ABC):
    """Base class for all sequential workflow steps"""
    
    def __init__(self, name: str, required_services: List[str] = None):
        self.name = name
        self.state = WorkflowState.PENDING
        self.required_services = required_services or []
        self.execution_time = None
        
    @abstractmethod
    async def execute(self, context: WorkflowContext) -> StepResult:
        """Execute the step logic - must be implemented by subclasses"""
        pass
    
    async def validate_preconditions(self, context: WorkflowContext) -> bool:
        """Validate that step can execute - override in subclasses if needed"""
        return True
    
    async def validate_postconditions(self, result: StepResult, context: WorkflowContext) -> bool:
        """Validate step completed successfully - override in subclasses if needed"""
        return result.success
    
    async def handle_error(self, error: Exception, context: WorkflowContext) -> StepResult:
        """Handle step execution errors - override for custom error handling"""
        error_msg = f"Step {self.name} failed: {str(error)}"
        context.court_logger.error(error_msg, exc_info=True)
        return StepResult(success=False, error=error_msg, skip_to_return=True)


class ParseHtmlStep(WorkflowStep):
    """Step 1: Parse HTML content and extract case information"""
    
    def __init__(self, html_parser_service: Any):
        super().__init__("parse_html", required_services=["html_parser_service"])
        self.html_parser_service = html_parser_service
    
    async def execute(self, context: WorkflowContext) -> StepResult:
        """Extract attorneys and case details from HTML content"""
        context.court_logger.info(f"🟢 START: Parse HTML Step for docket {context.docket_number}")
        context.court_logger.log_phase_start("Parse HTML Step")
        
        try:
            # Log input validation
            context.court_logger.debug(f"HTML content validation: length={len(context.html_content) if context.html_content else 0}")
            
            # Validate HTML content exists
            if not context.html_content or not context.html_content.strip():
                context.court_logger.warning("❌ RESULT: Parse HTML Step - No HTML content provided")
                context.court_logger.info("🔴 END: Parse HTML Step - FAILED (no HTML content)")
                return StepResult(
                    success=False, 
                    error="No HTML content provided",
                    skip_to_return=True
                )
            
            context.court_logger.debug(f"Extracting attorneys from HTML content (length: {len(context.html_content)} chars)")
            
            # Extract attorneys using existing HtmlParser
            attorneys = await self.html_parser_service._execute_action({
                "action": "extract_attorneys",
                "html_content": context.html_content
            })
            
            context.court_logger.info(f"📊 RESULT: Parse HTML Step - Found {len(attorneys)} attorneys")
            
            # Generate parsing metadata
            parse_metadata = {
                "attorneys_count": len(attorneys),
                "html_length": len(context.html_content),
                "parsed_at": datetime.now().isoformat()
            }
            
            result_data = {
                "parsed_attorneys": attorneys,
                "html_metadata": parse_metadata
            }
            
            context.court_logger.info(f"✅ RESULT: Parse HTML Step - Successfully parsed {len(attorneys)} attorneys from {len(context.html_content)} chars")
            context.court_logger.log_phase_complete(
                "Parse HTML Step", 
                context={"attorneys_found": len(attorneys), "html_size": len(context.html_content)}
            )
            context.court_logger.info("🟢 END: Parse HTML Step - SUCCESS")
            
            return StepResult(success=True, data=result_data)
            
        except Exception as e:
            context.court_logger.error(f"💥 RESULT: Parse HTML Step - Exception occurred: {str(e)}")
            context.court_logger.info("🔴 END: Parse HTML Step - FAILED (exception)")
            return await self.handle_error(e, context)


class RelevanceCheckStep(WorkflowStep):
    """Step 2: Check case relevance with defendant iteration and S3 upload"""
    
    def __init__(self, relevance_service: Any, s3_service: Any = None):
        super().__init__("relevance_check", required_services=["relevance_service"])
        self.relevance_service = relevance_service
        self.s3_service = s3_service
    
    async def execute(self, context: WorkflowContext) -> StepResult:
        """Apply relevance logic and upload analysis to S3"""
        context.court_logger.info(f"🟢 START: Relevance Check Step for docket {context.docket_number}")
        context.court_logger.log_phase_start("Relevance Check Step")
        
        try:
            # Extract defendants from case details
            context.court_logger.debug("Extracting defendants from case details")
            defendants = self._extract_defendants(context)
            context.court_logger.debug(f"Found {len(defendants)} defendants: {defendants[:3] if len(defendants) > 3 else defendants}")
            
            # Apply relevance scoring
            context.court_logger.debug("Applying relevance scoring logic")
            relevance_result = await self.relevance_service._execute_action({
                "action": "determine_case_relevance",
                "case_details": context.case_details
            })
            
            is_relevant = relevance_result.get("is_relevant", False)
            relevance_score = relevance_result.get("score", 0.0)
            relevance_reason = relevance_result.get("reason", "Unknown")
            
            context.court_logger.info(f"📊 RESULT: Relevance Check - is_relevant={is_relevant}, score={relevance_score:.2f}, reason='{relevance_reason}'")
            
            # Upload relevance analysis to S3 if available
            if self.s3_service:
                context.court_logger.debug("Uploading relevance analysis to S3")
                await self._upload_relevance_analysis(context, relevance_result)
            else:
                context.court_logger.debug("S3 service not available - skipping relevance upload")
            
            result_data = {
                "is_relevant": is_relevant,
                "relevance_score": relevance_score,
                "defendant_count": len(defendants),
                "relevance_details": relevance_result,
                "relevance_reason": relevance_reason
            }
            
            context.court_logger.log_phase_complete(
                "Relevance Check Step",
                context={
                    "relevant": is_relevant, 
                    "score": relevance_score,
                    "defendants": len(defendants),
                    "reason": relevance_reason
                }
            )
            
            # Decision logic with detailed logging
            if not is_relevant:
                context.court_logger.warning(f"❌ DECISION: Case not relevant (score: {relevance_score:.2f}, reason: {relevance_reason}) - skipping to return step")
                context.court_logger.info("🔴 END: Relevance Check Step - SKIP TO RETURN (not relevant)")
                return StepResult(
                    success=True, 
                    data=result_data, 
                    skip_to_return=True
                )
            
            context.court_logger.info(f"✅ DECISION: Case is relevant (score: {relevance_score:.2f}, reason: {relevance_reason}) - continuing to next step")
            context.court_logger.info("🟢 END: Relevance Check Step - SUCCESS (relevant)")
            return StepResult(success=True, data=result_data)
            
        except Exception as e:
            context.court_logger.error(f"💥 RESULT: Relevance Check Step - Exception occurred: {str(e)}")
            context.court_logger.info("🔴 END: Relevance Check Step - FAILED (exception)")
            return await self.handle_error(e, context)
    
    def _extract_defendants(self, context: WorkflowContext) -> List[str]:
        """Extract defendant names from case details with proper logging and parsing logic"""
        court_logger = context.court_logger
        defendants = []
        
        # First check for explicit defendants list
        if "defendants" in context.case_details:
            defendants_data = context.case_details["defendants"]
            court_logger.debug(f"Found 'defendants' field with data type: {type(defendants_data)}")
            
            if isinstance(defendants_data, list):
                # Handle list of defendant objects or strings
                for defendant in defendants_data:
                    if isinstance(defendant, dict):
                        defendant_name = defendant.get("name", "").strip()
                        if defendant_name:
                            defendants.append(defendant_name)
                            court_logger.debug(f"Extracted defendant from object: '{defendant_name}'")
                    elif isinstance(defendant, str) and defendant.strip():
                        defendants.append(defendant.strip())
                        court_logger.debug(f"Extracted defendant from string: '{defendant.strip()}'")
                        
                court_logger.info(f"Extracted {len(defendants)} defendants from 'defendants' list")
                return defendants
            
            elif isinstance(defendants_data, str) and defendants_data.strip():
                # Handle string representation of defendants
                court_logger.debug("Processing defendants from string format")
                parsed_defendants = self._parse_defendant_string(defendants_data, court_logger)
                defendants.extend(parsed_defendants)
                court_logger.info(f"Parsed {len(defendants)} defendants from defendants string")
                return defendants
        
        # Fallback to versus field parsing if no defendants list
        court_logger.debug("No 'defendants' field found, falling back to 'versus' field parsing")
        versus = context.case_details.get("versus", "")
        
        if versus and isinstance(versus, str):
            court_logger.debug(f"Processing versus field: '{versus}'")
            
            # Look for various patterns of "v." or "vs."
            vs_patterns = [' v. ', ' V. ', ' vs. ', ' Vs. ', ' v ', ' V ']
            defendant_part = None
            
            for pattern in vs_patterns:
                if pattern in versus:
                    parts = versus.split(pattern, 1)  # Split only on first occurrence
                    if len(parts) > 1:
                        defendant_part = parts[1].strip()
                        court_logger.debug(f"Found defendant part after '{pattern}': '{defendant_part}'")
                        break
            
            if defendant_part:
                parsed_defendants = self._parse_defendant_string(defendant_part, court_logger)
                defendants.extend(parsed_defendants)
                court_logger.info(f"Extracted {len(defendants)} defendants from versus field")
            else:
                court_logger.warning(f"No valid versus pattern found in: '{versus}'")
        else:
            court_logger.warning("No valid 'versus' field found for defendant extraction")
        
        # Log final results
        unique_defendants = list(dict.fromkeys(defendants))  # Remove duplicates while preserving order
        court_logger.info(f"Final defendant extraction result: {len(unique_defendants)} unique defendants")
        for i, defendant in enumerate(unique_defendants):
            court_logger.debug(f"Defendant {i+1}: '{defendant}'")
        
        return unique_defendants
    
    def _parse_defendant_string(self, defendant_text: str, court_logger) -> List[str]:
        """Parse a defendant string into individual defendant names"""
        if not defendant_text or not defendant_text.strip():
            return []
        
        defendants = []
        
        # Handle "et al" cases - remove et al suffix and treat as single defendant
        if " et al" in defendant_text.lower():
            # Remove et al and any trailing punctuation
            clean_text = re.sub(r'\s+et\s+al\.?.*$', '', defendant_text, flags=re.IGNORECASE).strip()
            court_logger.debug(f"Removed 'et al' suffix: '{defendant_text}' -> '{clean_text}'")
            defendant_text = clean_text
        
        # Check if this looks like a single entity (contains Inc., LLC, Corp., etc.)
        entity_indicators = ['Inc.', 'LLC', 'Corp.', 'Ltd.', 'Co.', 'Company', 'Corporation']
        has_entity_indicator = any(indicator in defendant_text for indicator in entity_indicators)
        
        if has_entity_indicator:
            # Treat as single defendant to avoid splitting "Avlon Industries, Inc."
            court_logger.debug(f"Found entity indicator - treating as single defendant: '{defendant_text}'")
            defendants.append(defendant_text.strip())
        else:
            # Split by comma for multiple defendants, but be careful with company names
            parts = [part.strip() for part in defendant_text.split(',')]
            
            # Rejoin parts that might be part of a company name
            processed_parts = []
            i = 0
            while i < len(parts):
                current_part = parts[i]
                
                # Check if next part looks like a company suffix
                if (i + 1 < len(parts) and 
                    parts[i + 1].strip().lower() in ['inc.', 'inc', 'llc', 'corp.', 'corp', 'ltd.', 'ltd', 'co.', 'co']):
                    # Combine with next part
                    combined = f"{current_part}, {parts[i + 1]}".strip()
                    processed_parts.append(combined)
                    court_logger.debug(f"Combined company name parts: '{current_part}' + '{parts[i + 1]}' = '{combined}'")
                    i += 2  # Skip next part since we combined it
                else:
                    if current_part:  # Only add non-empty parts
                        processed_parts.append(current_part)
                    i += 1
            
            defendants.extend(processed_parts)
            court_logger.debug(f"Split defendant string into {len(defendants)} parts: {defendants}")
        
        # Clean up each defendant name
        cleaned_defendants = []
        for defendant in defendants:
            cleaned = defendant.strip()
            if cleaned and cleaned not in cleaned_defendants:  # Avoid duplicates
                cleaned_defendants.append(cleaned)
        
        return cleaned_defendants
    
    async def _upload_relevance_analysis(self, context: WorkflowContext, analysis: Dict[str, Any]):
        """Upload relevance analysis results to S3"""
        try:
            base_filename = context.case_details.get("base_filename", f"{context.court_id}_{context.docket_number}")
            
            await self.s3_service._execute_action({
                "action": "upload_relevance_analysis",
                "base_filename": base_filename,
                "analysis_data": analysis,
                "iso_date": context.config.get("iso_date")
            })
            
            context.court_logger.debug("Relevance analysis uploaded to S3")
            
        except Exception as e:
            # Log error but don't fail the step
            context.court_logger.warning(f"Failed to upload relevance analysis to S3: {e}")


class TransferHandlingStep(WorkflowStep):
    """Step 3: Multi-conditional logic with transferor checks"""
    
    def __init__(self, transfer_service: Any = None):
        super().__init__("transfer_handling", required_services=[])
        self.transfer_service = transfer_service
    
    async def execute(self, context: WorkflowContext) -> StepResult:
        """Apply transfer handling logic with complex business rules"""
        context.court_logger.info(f"🟢 START: Transfer Handling Step for docket {context.docket_number}")
        context.court_logger.log_phase_start("Transfer Handling Step")
        
        try:
            # Check for transfer indicators in case details
            context.court_logger.debug("Detecting transfer indicators in case details")
            transfer_indicators = self._detect_transfer_indicators(context)
            
            has_transfer = transfer_indicators["has_transfer"]
            transfer_type = transfer_indicators.get("transfer_type", "none")
            confidence = transfer_indicators.get("confidence", 0.0)
            
            context.court_logger.info(f"📊 RESULT: Transfer Detection - has_transfer={has_transfer}, type='{transfer_type}', confidence={confidence:.2f}")
            
            if not has_transfer:
                context.court_logger.info("✅ DECISION: No transfer detected - continuing to next step")
                context.court_logger.log_phase_complete(
                    "Transfer Handling Step",
                    context={"transfer_detected": False}
                )
                context.court_logger.info("🟢 END: Transfer Handling Step - SUCCESS (no transfer)")
                return StepResult(success=True, data={"transfer_status": "none", "is_transferred": False})
            
            # Apply transfer rules
            context.court_logger.debug(f"Applying transfer rules for detected transfer (type: {transfer_type}, confidence: {confidence:.2f})")
            transfer_decision = await self._apply_transfer_rules(context, transfer_indicators)
            
            # Determine if this is a transferred case
            is_transferred = transfer_decision["status"] == "detected" or transfer_indicators["has_transfer"]
            decision = transfer_decision["decision"]
            
            context.court_logger.info(f"📊 RESULT: Transfer Rules Applied - status={transfer_decision['status']}, decision='{decision}', is_transferred={is_transferred}")
            
            result_data = {
                "transfer_status": transfer_decision["status"],
                "transferor_info": transfer_decision.get("transferor"),
                "handling_decision": transfer_decision["decision"],
                "is_transferred": is_transferred,  # Add this for HTML upload condition
                "transfer_indicators": transfer_indicators
            }
            
            # CRITICAL FIX: Upload HTML to S3 when is_transferred is true
            if is_transferred:
                context.court_logger.info(f"📤 DECISION: Uploading HTML to S3 due to transfer detection (is_transferred={is_transferred})")
                await self._upload_html_on_transfer(context)
            else:
                context.court_logger.debug("No HTML upload needed - not a transferred case")
            
            context.court_logger.log_phase_complete(
                "Transfer Handling Step",
                context={
                    "transfer_status": transfer_decision["status"],
                    "decision": transfer_decision["decision"],
                    "is_transferred": is_transferred,
                    "transfer_type": transfer_type,
                    "confidence": confidence
                }
            )
            
            # Decision logic with detailed logging
            if transfer_decision["decision"] == "skip_processing":
                context.court_logger.warning(f"❌ DECISION: Transfer case requires special handling - skipping to return step")
                context.court_logger.info("🔴 END: Transfer Handling Step - SKIP TO RETURN (special handling)")
                return StepResult(
                    success=True,
                    data=result_data,
                    skip_to_return=True
                )
            
            context.court_logger.info(f"✅ DECISION: Transfer case can continue processing - proceeding to next step")
            context.court_logger.info("🟢 END: Transfer Handling Step - SUCCESS (continuing)")
            return StepResult(success=True, data=result_data)
            
        except Exception as e:
            context.court_logger.error(f"💥 RESULT: Transfer Handling Step - Exception occurred: {str(e)}")
            context.court_logger.info("🔴 END: Transfer Handling Step - FAILED (exception)")
            return await self.handle_error(e, context)
    
    async def _upload_html_on_transfer(self, context: WorkflowContext) -> None:
        """Upload HTML to S3 when is_transferred is true."""
        try:
            iso_date = context.config.get("iso_date")
            if not iso_date or not context.html_content:
                context.court_logger.debug("Skipping HTML upload on transfer - missing iso_date or html_content")
                return
            
            base_filename = context.case_details.get("base_filename") or f"{context.court_id}_{context.docket_number}"
            
            # Import download orchestration service for HTML upload
            from src.pacer.core.download_orchestration.download_orchestration_service import DownloadOrchestrationService
            
            download_service = DownloadOrchestrationService(logger=context.court_logger)
            await download_service.initialize()
            
            # Upload HTML to S3
            upload_success = await download_service.upload_html_to_s3(
                base_filename=base_filename,
                html_content=context.html_content,
                iso_date=iso_date
            )
            
            if upload_success:
                context.court_logger.info(f"HTML uploaded to S3 due to is_transferred: {iso_date}/html/{base_filename}.html")
                context.case_details["html_s3_upload_transfer"] = {
                    "success": True,
                    "s3_path": f"{iso_date}/html/{base_filename}.html",
                    "reason": "is_transferred_true",
                    "timestamp": datetime.now().isoformat()
                }
            else:
                context.court_logger.error("Failed to upload HTML to S3 on transfer")
                
        except Exception as e:
            context.court_logger.error(f"Exception during HTML upload on transfer: {e}", exc_info=True)
    
    def _detect_transfer_indicators(self, context: WorkflowContext) -> Dict[str, Any]:
        """Detect transfer-related indicators in case details"""
        indicators = {
            "has_transfer": False,
            "transfer_type": None,
            "confidence": 0.0
        }
        
        # Check various fields for transfer keywords
        transfer_keywords = ["transfer", "transferor", "removed", "removal"]
        
        fields_to_check = ["case_title", "versus", "nature_of_suit", "cause_of_action"]
        
        for field in fields_to_check:
            value = context.case_details.get(field, "").lower()
            for keyword in transfer_keywords:
                if keyword in value:
                    indicators["has_transfer"] = True
                    indicators["transfer_type"] = keyword
                    indicators["confidence"] += 0.25
        
        return indicators
    
    async def _apply_transfer_rules(self, context: WorkflowContext, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Apply complex transfer handling rules"""
        decision = {
            "status": "detected",
            "decision": "continue",
            "transferor": None
        }
        
        # Simple rule: if transfer confidence is high, mark for special handling
        if indicators["confidence"] > 0.5:
            decision["decision"] = "skip_processing"
            context.court_logger.info(
                f"High transfer confidence ({indicators['confidence']}) - marking for special handling"
            )
        
        return decision


class IgnoreListCheckStep(WorkflowStep):
    """Step 4: Check ignore_download rules and configuration"""
    
    def __init__(self, ignore_download_service: Any):
        super().__init__("ignore_list_check", required_services=["ignore_download_service"])
        self.ignore_download_service = ignore_download_service
    
    async def execute(self, context: WorkflowContext) -> StepResult:
        """Apply ignore_download rules to determine if case should be ignored"""
        context.court_logger.info(f"🟢 START: Ignore List Check Step for docket {context.docket_number}")
        context.court_logger.log_phase_start("Ignore List Check Step")
        
        try:
            # Check if case should be ignored for download
            context.court_logger.debug("Checking case against ignore_download rules")
            should_ignore = await self.ignore_download_service._execute_action({
                "action": "should_ignore_download",
                "case_details": context.case_details
            })
            
            # Check for explicit processing override
            is_explicit = context.config.get("_processing_explicit_dockets", False)
            
            context.court_logger.info(f"📊 RESULT: Ignore Check - should_ignore={should_ignore}, explicit_override={is_explicit}")
            
            final_decision = should_ignore and not is_explicit
            ignore_reason = "ignore_list_match" if should_ignore else None
            
            if should_ignore and is_explicit:
                context.court_logger.info("⚡ OVERRIDE: Case would be ignored but explicit processing requested")
            
            result_data = {
                "should_ignore": should_ignore,
                "explicit_override": is_explicit,
                "final_ignore_decision": final_decision,
                "ignore_reason": ignore_reason,
                "ignore_download": should_ignore  # Add this for HTML upload condition
            }
            
            # CRITICAL FIX: Upload HTML to S3 when ignore_download is triggered
            if should_ignore:
                context.court_logger.info(f"📤 DECISION: Uploading HTML to S3 due to ignore_download trigger")
                await self._upload_html_on_ignore(context)
            else:
                context.court_logger.debug("No HTML upload needed - case not ignored")
            
            context.court_logger.log_phase_complete(
                "Ignore List Check Step",
                context={
                    "ignore_decision": final_decision,
                    "explicit_override": is_explicit,
                    "should_ignore": should_ignore,
                    "reason": ignore_reason
                }
            )
            
            # Decision logic with detailed logging
            if final_decision:
                context.court_logger.warning(f"❌ DECISION: Case matches ignore list (reason: {ignore_reason}) - skipping to return step")
                context.court_logger.info("🔴 END: Ignore List Check Step - SKIP TO RETURN (ignored)")
                return StepResult(
                    success=True,
                    data=result_data,
                    skip_to_return=True
                )
            
            if should_ignore and is_explicit:
                context.court_logger.info(f"✅ DECISION: Case ignored but explicit override applied - continuing to next step")
            else:
                context.court_logger.info(f"✅ DECISION: Case not in ignore list - continuing to next step")
            
            context.court_logger.info("🟢 END: Ignore List Check Step - SUCCESS (continuing)")
            return StepResult(success=True, data=result_data)
            
        except Exception as e:
            # On error, default to continuing (fail-safe)
            context.court_logger.warning(f"💥 RESULT: Ignore list check failed, defaulting to continue: {e}")
            context.court_logger.info("🔴 END: Ignore List Check Step - FAILED (fail-safe continue)")
            return StepResult(success=True, data={"should_ignore": False, "error": str(e), "fail_safe": True})
    
    async def _upload_html_on_ignore(self, context: WorkflowContext) -> None:
        """Upload HTML to S3 when ignore_download is triggered."""
        try:
            iso_date = context.config.get("iso_date")
            if not iso_date or not context.html_content:
                context.court_logger.debug("Skipping HTML upload on ignore - missing iso_date or html_content")
                return
            
            base_filename = context.case_details.get("base_filename") or f"{context.court_id}_{context.docket_number}"
            
            # Import download orchestration service for HTML upload
            from src.pacer.core.download_orchestration.download_orchestration_service import DownloadOrchestrationService
            
            download_service = DownloadOrchestrationService(logger=context.court_logger)
            await download_service.initialize()
            
            # Upload HTML to S3
            upload_success = await download_service.upload_html_to_s3(
                base_filename=base_filename,
                html_content=context.html_content,
                iso_date=iso_date
            )
            
            if upload_success:
                context.court_logger.info(f"HTML uploaded to S3 due to ignore_download: {iso_date}/html/{base_filename}.html")
                context.case_details["html_s3_upload_ignore"] = {
                    "success": True,
                    "s3_path": f"{iso_date}/html/{base_filename}.html",
                    "reason": "ignore_download_triggered",
                    "timestamp": datetime.now().isoformat()
                }
            else:
                context.court_logger.error("Failed to upload HTML to S3 on ignore_download")
                
        except Exception as e:
            context.court_logger.error(f"Exception during HTML upload on ignore: {e}", exc_info=True)


class DocumentDownloadStep(WorkflowStep):
    """Step 5: Download case documents"""
    
    def __init__(self, download_orchestration_service: Any):
        super().__init__("document_download", required_services=["download_orchestration_service"])
        self.download_orchestration_service = download_orchestration_service
    
    async def execute(self, context: WorkflowContext) -> StepResult:
        """Execute document download workflow"""
        context.court_logger.info(f"🟢 START: Document Download Step for docket {context.docket_number}")
        context.court_logger.log_phase_start("Document Download Step")
        
        try:
            # Get the current page from browser context
            context.court_logger.debug("Retrieving current page from browser context")
            pages = context.browser_context.pages
            current_page = pages[0] if pages else None
            
            if not current_page:
                context.court_logger.warning("❌ RESULT: Document Download Step - No page available for download")
                context.court_logger.info("🔴 END: Document Download Step - FAILED (no page)")
                return StepResult(
                    success=False,
                    error="No page available for download",
                    data={"downloads_completed": 0}
                )
            
            context.court_logger.debug(f"Current page available: {current_page.url}")
            
            # Execute download workflow
            is_explicit = context.config.get("_processing_explicit_dockets", False)
            
            context.court_logger.info(f"Executing download workflow (explicit_request={is_explicit})")
            
            updated_case_details = await self.download_orchestration_service.execute_download_workflow(
                case_details=context.case_details,
                is_explicitly_requested=is_explicit,
                page=current_page
            )
            
            # Calculate download summary
            download_count = updated_case_details.get("download_count", 0)
            processing_status = updated_case_details.get("processing_status", "unknown")
            continue_download = download_count > 0 or processing_status == "completed"
            
            context.court_logger.info(f"📊 RESULT: Document Download - completed={download_count}, status='{processing_status}', continue_download={continue_download}")
            
            result_data = {
                "downloads_completed": download_count,
                "updated_case_details": updated_case_details,
                "processing_status": processing_status,
                "continue_download": continue_download  # Add this for HTML upload condition
            }
            
            # Update context with updated case details
            context.case_details.update(updated_case_details)
            
            # CRITICAL FIX: Upload HTML to S3 when continuing to download docket
            if continue_download:
                context.court_logger.info(f"📤 DECISION: Uploading HTML to S3 due to continue download (downloads={download_count}, status='{processing_status}')")
                await self._upload_html_on_continue_download(context)
            else:
                context.court_logger.debug("No HTML upload needed - not continuing download")
            
            # NOTE: JSON saving is now handled by the SequentialWorkflowManager AFTER complete docket processing
            # This ensures JSON is saved after any HTML S3 uploads have completed
            
            context.court_logger.log_phase_complete(
                "Document Download Step",
                context={
                    "downloads": download_count,
                    "status": processing_status,
                    "continue_download": continue_download
                }
            )
            
            context.court_logger.info(f"✅ DECISION: Document download workflow completed - processed {download_count} documents")
            context.court_logger.info("🟢 END: Document Download Step - SUCCESS")
            
            return StepResult(success=True, data=result_data)
            
        except Exception as e:
            # Log error but continue to return step to maintain workflow
            context.court_logger.error(f"💥 RESULT: Document Download Step - Exception occurred: {str(e)}", exc_info=True)
            context.court_logger.info("🔴 END: Document Download Step - FAILED (exception)")
            return StepResult(
                success=False,
                error=str(e),
                data={"downloads_completed": 0}
            )
    
    async def _upload_html_on_continue_download(self, context: WorkflowContext) -> None:
        """Upload HTML to S3 when continuing to download docket."""
        try:
            iso_date = context.config.get("iso_date")
            if not iso_date or not context.html_content:
                context.court_logger.debug("Skipping HTML upload on continue download - missing iso_date or html_content")
                return
            
            base_filename = context.case_details.get("base_filename") or f"{context.court_id}_{context.docket_number}"
            
            # Upload HTML to S3
            upload_success = await self.download_orchestration_service.upload_html_to_s3(
                base_filename=base_filename,
                html_content=context.html_content,
                iso_date=iso_date
            )
            
            if upload_success:
                context.court_logger.info(f"HTML uploaded to S3 due to continue download: {iso_date}/html/{base_filename}.html")
                context.case_details["html_s3_upload_continue"] = {
                    "success": True,
                    "s3_path": f"{iso_date}/html/{base_filename}.html",
                    "reason": "continue_download_docket",
                    "timestamp": datetime.now().isoformat()
                }
            else:
                context.court_logger.error("Failed to upload HTML to S3 on continue download")
                
        except Exception as e:
            context.court_logger.error(f"Exception during HTML upload on continue download: {e}", exc_info=True)


class ReturnAndContinueStep(WorkflowStep):
    """Step 6: Navigate back to Query page for next docket processing"""
    
    def __init__(self, navigation_manager: Any = None):
        super().__init__("return_and_continue", required_services=[])
        self.navigation_manager = navigation_manager
    
    async def execute(self, context: WorkflowContext) -> StepResult:
        """Navigate back to query page and verify readiness"""
        context.court_logger.info(f"🟢 START: Return and Continue Step for docket {context.docket_number}")
        context.court_logger.log_phase_start("Return and Continue Step")
        
        try:
            # Navigate back to query page
            context.court_logger.debug(f"Determining query page URL for court: {context.court_id}")
            query_url = self._get_query_page_url(context.court_id)
            
            if not query_url:
                context.court_logger.warning(f"❌ RESULT: Return and Continue Step - No query URL configured for court: {context.court_id}")
                context.court_logger.info("🔴 END: Return and Continue Step - FAILED (no query URL)")
                return StepResult(
                    success=False,
                    error=f"No query URL configured for court: {context.court_id}"
                )
            
            context.court_logger.debug(f"Query URL found: {query_url}")
            
            # Get or create page for navigation
            context.court_logger.debug("Getting or creating page for navigation")
            pages = context.browser_context.pages
            if pages:
                page = pages[0]
                context.court_logger.debug(f"Using existing page: {page.url}")
            else:
                page = await context.browser_context.new_page()
                context.court_logger.debug("Created new page for navigation")
            
            # Navigate to query page
            context.court_logger.info(f"🔄 Navigating back to query page: {query_url}")
            await page.goto(query_url, wait_until="networkidle", timeout=30000)
            
            # Validate query page is ready
            context.court_logger.debug("Validating query page readiness")
            is_ready = await self._validate_query_page_ready(page, context.court_logger)
            
            navigation_status = "success" if is_ready else "warning"
            
            context.court_logger.info(f"📊 RESULT: Return and Continue - navigation_status='{navigation_status}', query_page_ready={is_ready}")
            
            result_data = {
                "navigation_url": query_url,
                "query_page_ready": is_ready,
                "navigation_status": navigation_status
            }
            
            context.court_logger.log_phase_complete(
                "Return and Continue Step",
                context={
                    "ready": is_ready, 
                    "url": query_url,
                    "status": navigation_status
                }
            )
            
            if is_ready:
                context.court_logger.info(f"✅ DECISION: Navigation successful - query page ready for next docket")
                context.court_logger.info("🟢 END: Return and Continue Step - SUCCESS")
            else:
                context.court_logger.warning(f"⚠️ DECISION: Navigation completed but query page not fully ready")
                context.court_logger.info("🟡 END: Return and Continue Step - WARNING (partial success)")
            
            return StepResult(success=is_ready, data=result_data)
            
        except Exception as e:
            context.court_logger.error(f"💥 RESULT: Return and Continue Step - Exception occurred: {str(e)}")
            context.court_logger.info("🔴 END: Return and Continue Step - FAILED (exception)")
            return await self.handle_error(e, context)
    
    def _get_query_page_url(self, court_id: str) -> Optional[str]:
        """Get the query page URL for a specific court"""
        query_urls = {
            "cand": "https://ecf.cand.uscourts.gov/cgi-bin/iquery.pl",
            "nysd": "https://ecf.nysd.uscourts.gov/cgi-bin/iquery.pl", 
            "ilnd": "https://ecf.ilnd.uscourts.gov/cgi-bin/iquery.pl",
            "txsd": "https://ecf.txsd.uscourts.gov/cgi-bin/iquery.pl",
            # Add more courts as needed
        }
        
        return query_urls.get(court_id.lower())
    
    async def _validate_query_page_ready(self, page: Page, logger: logging.Logger) -> bool:
        """Validate that query page is ready for input"""
        try:
            # Check for query form elements
            query_input = page.locator("input[name='case_num']")
            submit_button = page.locator("input[type='submit'], button[type='submit']")
            
            # Wait for elements to be visible
            await query_input.wait_for(state="visible", timeout=5000)
            await submit_button.wait_for(state="visible", timeout=5000)
            
            input_visible = await query_input.is_visible()
            button_visible = await submit_button.is_visible()
            
            if input_visible and button_visible:
                logger.debug("Query page form elements are ready")
                return True
            else:
                logger.warning("Query page form elements not ready")
                return False
                
        except Exception as e:
            logger.warning(f"Error validating query page: {e}")
            return False


class ErrorRecoveryManager:
    """Manages error recovery and retry logic for workflow steps"""
    
    def __init__(self):
        self.retry_counts = {}
        self.max_retries = 3
        
    async def handle_step_error(self, 
                               step_name: str, 
                               error: Exception,
                               context: WorkflowContext) -> StepResult:
        """Handle step execution errors with retry logic"""
        
        context.court_logger.info(f"🔧 ERROR RECOVERY START: Handling error for step {step_name}")
        
        error_type = self._classify_error(error)
        context.court_logger.error(
            f"📊 ERROR CLASSIFICATION: Step {step_name} error classified as: {error_type}",
            context={"error": str(error), "step": step_name, "error_type": error_type}
        )
        
        if error_type == "recoverable":
            context.court_logger.info(f"🔄 ERROR RECOVERY: Attempting retry with backoff for {step_name}")
            return await self._retry_with_backoff(step_name, context)
        elif error_type == "step_level":
            context.court_logger.info(f"⚠️ ERROR RECOVERY: Step-level error for {step_name} - skipping to return step")
            return StepResult(success=False, skip_to_return=True)
        else:  # critical
            context.court_logger.error(f"💥 ERROR RECOVERY: Critical error for {step_name} - halting workflow")
            return StepResult(success=False, error="Critical failure - halting workflow")
    
    def _classify_error(self, error: Exception) -> str:
        """Classify error type for recovery strategy"""
        error_str = str(error).lower()
        
        # Recoverable errors
        if any(keyword in error_str for keyword in ["timeout", "network", "connection"]):
            return "recoverable"
        
        # Critical errors
        if any(keyword in error_str for keyword in ["authentication", "permission", "browser"]):
            return "critical"
        
        # Default to step-level
        return "step_level"
    
    async def _retry_with_backoff(self, step_name: str, context: WorkflowContext):
        """Implement exponential backoff retry logic"""
        retry_count = self.retry_counts.get(step_name, 0)
        
        context.court_logger.debug(f"Current retry count for {step_name}: {retry_count}/{self.max_retries}")
        
        if retry_count < self.max_retries:
            self.retry_counts[step_name] = retry_count + 1
            wait_time = 2 ** retry_count  # Exponential backoff
            
            context.court_logger.warning(
                f"⏳ ERROR RECOVERY: Retrying step {step_name} in {wait_time}s (attempt {retry_count + 1}/{self.max_retries})"
            )
            
            await asyncio.sleep(wait_time)
            context.court_logger.info(f"🔄 ERROR RECOVERY: Retry backoff completed for {step_name} - continuing to return step")
            return StepResult(success=False, skip_to_return=True)  # Continue to return step
        else:
            context.court_logger.error(f"💥 ERROR RECOVERY: Step {step_name} exceeded max retries ({self.max_retries}) - giving up")
            return StepResult(success=False, skip_to_return=True)


class SequentialDocketProcessor(AsyncServiceBase):
    """
    Main orchestrator for sequential docket processing workflow.
    
    Implements the 6-step sequential workflow with robust state management,
    error handling, and integration with existing PACER services.
    """
    
    def __init__(self,
                 logger: Optional[logging.Logger] = None,
                 config: Optional[Dict[str, Any]] = None,
                 # Service dependencies
                 html_parser_service: Any = None,
                 relevance_service: Any = None,
                 ignore_download_service: Any = None,
                 download_orchestration_service: Any = None,
                 s3_service: Any = None,
                 transfer_service: Any = None,
                 navigation_manager: Any = None):
        
        super().__init__(logger, config)
        
        # Initialize workflow steps with service dependencies
        self.steps = {
            "parse_html": ParseHtmlStep(html_parser_service),
            "relevance_check": RelevanceCheckStep(relevance_service, s3_service),
            "transfer_handling": TransferHandlingStep(transfer_service),
            "ignore_list_check": IgnoreListCheckStep(ignore_download_service),
            "document_download": DocumentDownloadStep(download_orchestration_service),
            "return_and_continue": ReturnAndContinueStep(navigation_manager)
        }
        
        # Define workflow execution order
        self.workflow_order = [
            "parse_html",
            "relevance_check", 
            "transfer_handling",
            "ignore_list_check",
            "document_download",
            "return_and_continue"
        ]
        
        # Initialize error recovery manager
        self.error_manager = ErrorRecoveryManager()
    
    async def _execute_action(self, data: Any) -> Any:
        """Execute sequential docket processing actions"""
        action = data.get("action")
        
        if action == "process_docket":
            return await self.process_docket(**data)
        else:
            raise PacerServiceError(f"Unknown action for SequentialDocketProcessor: {action}")
    
    async def process_docket(self,
                           court_id: str,
                           docket_number: str, 
                           html_content: str,
                           browser_context: BrowserContext,
                           court_logger: logging.Logger,
                           initial_case_details: Optional[Dict[str, Any]] = None,
                           **kwargs) -> Dict[str, Any]:
        """
        Execute the complete sequential docket processing workflow.
        
        Args:
            court_id: Court identifier
            docket_number: Docket number to process
            html_content: HTML content from docket page
            browser_context: Browser context for navigation
            court_logger: Court-specific logger
            initial_case_details: Pre-extracted case details (optional)
            
        Returns:
            Dict containing final processing results and case details
        """
        
        # Initialize workflow context
        context = WorkflowContext(
            court_id=court_id,
            docket_number=docket_number,
            html_content=html_content,
            case_details=initial_case_details or {"court_id": court_id, "docket_num": docket_number},
            browser_context=browser_context,
            court_logger=court_logger,
            config=self.config or {},
            step_results={}
        )
        
        court_logger.log_phase_start(
            "Sequential Docket Processing",
            context={
                "docket": docket_number,
                "court": court_id,
                "workflow_steps": len(self.workflow_order)
            }
        )
        
        # Execute workflow steps sequentially
        current_step_name = self.workflow_order[0]
        workflow_start_time = datetime.now()
        
        try:
            while current_step_name:
                step = self.steps[current_step_name]
                step_start_time = datetime.now()
                
                try:
                    court_logger.info(f"🔄 WORKFLOW: Executing step {self.workflow_order.index(current_step_name) + 1}/{len(self.workflow_order)}: {current_step_name}")
                    
                    # Validate preconditions
                    court_logger.debug(f"Validating preconditions for step: {current_step_name}")
                    if not await step.validate_preconditions(context):
                        court_logger.warning(f"❌ WORKFLOW: Preconditions failed for step: {current_step_name} - jumping to return step")
                        current_step_name = "return_and_continue"
                        continue
                    
                    court_logger.debug(f"Preconditions validated for step: {current_step_name}")
                    
                    # Execute step
                    step.state = WorkflowState.RUNNING
                    court_logger.debug(f"Step state changed to RUNNING: {current_step_name}")
                    
                    result = await step.execute(context)
                    
                    # Calculate execution time
                    step.execution_time = (datetime.now() - step_start_time).total_seconds()
                    court_logger.debug(f"Step execution time: {current_step_name} = {step.execution_time:.2f}s")
                    
                    # Store step result
                    context.step_results[current_step_name] = result
                    
                    # Update case details with step results
                    if result.data:
                        context.case_details.update(result.data)
                        court_logger.debug(f"Updated case details with {len(result.data)} result fields from step: {current_step_name}")
                    
                    # Determine next step based on result
                    if result.success:
                        step.state = WorkflowState.SUCCESS
                        court_logger.info(f"✅ WORKFLOW: Step completed successfully: {current_step_name} ({step.execution_time:.2f}s)")
                        
                        # Check if step requested skip to return
                        if result.skip_to_return:
                            court_logger.info(f"🔄 WORKFLOW: Step {current_step_name} requested skip to return step")
                            current_step_name = "return_and_continue"
                        else:
                            next_step = self._get_next_step(current_step_name, result)
                            if next_step:
                                court_logger.info(f"🔄 WORKFLOW: Proceeding to next step: {next_step}")
                            else:
                                court_logger.info(f"🏁 WORKFLOW: Reached end of workflow after step: {current_step_name}")
                            current_step_name = next_step
                    else:
                        step.state = WorkflowState.FAILED
                        court_logger.error(f"❌ WORKFLOW: Step failed: {current_step_name}, error: {result.error}")
                        
                        # Handle step failure - usually go to return step
                        if result.skip_to_return or current_step_name != "return_and_continue":
                            court_logger.info(f"🔄 WORKFLOW: Step failure - jumping to return step from: {current_step_name}")
                            current_step_name = "return_and_continue"
                        else:
                            # If return step failed, exit workflow
                            court_logger.error(f"💥 WORKFLOW: Return step failed - halting workflow")
                            break
                    
                    # Validate postconditions
                    court_logger.debug(f"Validating postconditions for step: {current_step_name}")
                    if not await step.validate_postconditions(result, context):
                        court_logger.warning(f"⚠️ WORKFLOW: Postconditions failed for step: {current_step_name}")
                    else:
                        court_logger.debug(f"Postconditions validated for step: {current_step_name}")
                        
                except Exception as e:
                    step.state = WorkflowState.FAILED
                    court_logger.error(f"💥 WORKFLOW: Step execution exception: {current_step_name}", exc_info=True)
                    
                    # Use error recovery manager
                    court_logger.info(f"🔧 WORKFLOW: Engaging error recovery for step: {current_step_name}")
                    recovery_result = await self.error_manager.handle_step_error(
                        current_step_name, e, context
                    )
                    
                    context.step_results[current_step_name] = recovery_result
                    
                    if recovery_result.skip_to_return:
                        court_logger.info(f"🔄 WORKFLOW: Error recovery - jumping to return step from: {current_step_name}")
                        current_step_name = "return_and_continue"
                    else:
                        # Critical failure - halt workflow
                        court_logger.error(f"💥 WORKFLOW: Critical failure - halting workflow at step: {current_step_name}")
                        break
            
            # Calculate total workflow time
            total_workflow_time = (datetime.now() - workflow_start_time).total_seconds()
            
            # Build final result with comprehensive logging
            court_logger.info(f"🏁 WORKFLOW: Building final result (total time: {total_workflow_time:.2f}s)")
            final_result = self._build_final_result(context, total_workflow_time)
            
            # Log comprehensive workflow summary
            steps_completed = len([s for s in self.steps.values() if s.state == WorkflowState.SUCCESS])
            steps_failed = len([s for s in self.steps.values() if s.state == WorkflowState.FAILED])
            
            court_logger.info(f"📊 WORKFLOW SUMMARY: status={final_result['workflow_status']}, "
                            f"completed={steps_completed}/{len(self.steps)}, failed={steps_failed}, "
                            f"duration={total_workflow_time:.2f}s")
            
            # Log step-by-step execution summary
            court_logger.debug("📋 STEP EXECUTION SUMMARY:")
            for step_name in self.workflow_order:
                step = self.steps[step_name]
                step_result = context.step_results.get(step_name)
                if step_result:
                    court_logger.debug(f"  • {step_name}: {step.state.value} ({step.execution_time:.2f}s) - "
                                     f"success={step_result.success}, skip_to_return={step_result.skip_to_return}")
                else:
                    court_logger.debug(f"  • {step_name}: {step.state.value} - no result stored")
            
            court_logger.log_phase_complete(
                "Sequential Docket Processing",
                duration=total_workflow_time,
                context={
                    "docket": docket_number,
                    "final_status": final_result["workflow_status"],
                    "steps_completed": steps_completed,
                    "steps_failed": steps_failed,
                    "total_steps": len(self.steps)
                }
            )
            
            court_logger.info(f"✅ WORKFLOW COMPLETE: Sequential docket processing finished for {docket_number}")
            return final_result
            
        except Exception as e:
            total_workflow_time = (datetime.now() - workflow_start_time).total_seconds()
            court_logger.error(f"💥 WORKFLOW CRITICAL FAILURE: Exception in main workflow loop after {total_workflow_time:.2f}s", exc_info=True)
            court_logger.error(f"💥 WORKFLOW CRITICAL FAILURE: {str(e)}")
            
            # Log current workflow state at time of failure
            steps_completed = len([s for s in self.steps.values() if s.state == WorkflowState.SUCCESS])
            steps_failed = len([s for s in self.steps.values() if s.state == WorkflowState.FAILED])
            
            court_logger.error(f"📊 FAILURE STATE: completed={steps_completed}/{len(self.steps)}, "
                             f"failed={steps_failed}, duration={total_workflow_time:.2f}s")
            
            return {
                "workflow_status": "critical_failure",
                "error": str(e),
                "case_details": context.case_details,
                "step_results": context.step_results,
                "failure_time": total_workflow_time,
                "steps_completed": steps_completed,
                "steps_failed": steps_failed
            }
    
    def _get_next_step(self, current_step: str, result: StepResult) -> Optional[str]:
        """Determine the next step in the workflow"""
        if result.next_step:
            return result.next_step
        
        # Default sequential progression
        try:
            current_index = self.workflow_order.index(current_step)
            if current_index < len(self.workflow_order) - 1:
                return self.workflow_order[current_index + 1]
            else:
                return None  # End of workflow
        except ValueError:
            return None
    
    def _build_final_result(self, context: WorkflowContext, total_time: float) -> Dict[str, Any]:
        """Build final result dictionary with comprehensive information"""
        
        # Determine overall workflow status
        step_states = [step.state for step in self.steps.values()]
        
        if all(state == WorkflowState.SUCCESS for state in step_states):
            workflow_status = "success"
        elif WorkflowState.FAILED in step_states:
            workflow_status = "partial_success"
        else:
            workflow_status = "unknown"
        
        # Calculate step execution summary
        step_summary = {}
        for name, step in self.steps.items():
            step_summary[name] = {
                "state": step.state.value,
                "execution_time": step.execution_time,
                "result": context.step_results.get(name, {})
            }
        
        return {
            "workflow_status": workflow_status,
            "total_execution_time": total_time,
            "case_details": context.case_details,
            "step_summary": step_summary,
            "docket_number": context.docket_number,
            "court_id": context.court_id,
            "processed_at": datetime.now().isoformat()
        }