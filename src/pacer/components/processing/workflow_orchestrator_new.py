"""
PACER Workflow Orchestrator for Phase-Separated Processing.

This orchestrator coordinates the complete PACER workflow through distinct phases:
Phase 1: Court Processing → Phase 2: Docket Processing → Phase 3: Row Processing → Phase 4: Download Workflow
"""

import asyncio
from typing import Any, Dict, Optional, List
from datetime import datetime
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.components.processing.court_processor import CourtProcessor
from src.pacer.components.processing.docket_processor import DocketProcessor
from src.pacer.components.processing.row_processor import RowProcessor
from src.pacer.components.download.download_manager import DownloadManager
from src.pacer.facades.file_operations_service import FileOperationsService


class PacerWorkflowOrchestrator(AsyncServiceBase):
    """
    Orchestrates the complete PACER workflow through distinct phases.
    
    This orchestrator implements clean phase separation:
    1. Court Processing - Authentication, configuration, docket discovery
    2. Docket Processing - HTML parsing, case extraction  
    3. Row Processing - Business rules, classification, download decisions
    4. Download Workflow - Document downloads for qualified cases
    
    Each phase is cleanly separated and testable independently.
    """

    def __init__(
        self,
        court_processor: Optional[CourtProcessor] = None,
        docket_processor: Optional[DocketProcessor] = None,
        row_processor: Optional[RowProcessor] = None,
        download_orchestrator: Optional[DownloadManager] = None,
        file_operations_service: Optional[FileOperationsService] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
    ):
        super().__init__(logger, config)
        # Store the raw dependencies (might be providers or instances)
        self._court_processor_dep = court_processor
        self._docket_processor_dep = docket_processor
        self._row_processor_dep = row_processor
        self._download_orchestrator_dep = download_orchestrator
        self._file_operations_service_dep = file_operations_service
        
        # These will be set to actual instances during initialization
        self.court_processor = None
        self.docket_processor = None
        self.row_processor = None
        self.download_orchestrator = None
        self.file_operations_service = None

    async def _initialize_service(self) -> None:
        """Initialize the workflow orchestrator and validate dependencies."""
        if self._initialized:
            return
        
        # Resolve dependencies (handle DI providers vs instances)
        def resolve_dependency(dep):
            """Resolve a dependency that might be a DI provider or an instance."""
            if dep is None:
                return None
            # If it's a DI provider (has __call__ method), call it to get the instance
            if hasattr(dep, '__call__') and hasattr(dep, '__class__') and 'Provider' in dep.__class__.__name__:
                return dep()
            return dep
        
        # Resolve all dependencies to actual instances
        self.court_processor = resolve_dependency(self._court_processor_dep)
        self.docket_processor = resolve_dependency(self._docket_processor_dep)
        self.row_processor = resolve_dependency(self._row_processor_dep)
        self.download_orchestrator = resolve_dependency(self._download_orchestrator_dep)
        self.file_operations_service = resolve_dependency(self._file_operations_service_dep)
        
        # Validate required processors
        required_processors = []
        if not self.court_processor:
            required_processors.append("CourtProcessor")
        if not self.docket_processor:
            required_processors.append("DocketProcessor")
        if not self.row_processor:
            required_processors.append("RowProcessor")
        if not self.download_orchestrator:
            required_processors.append("DownloadManager")
        if not self.file_operations_service:
            required_processors.append("FileOperationsService")
        
        if required_processors:
            raise ValueError(f"Missing required processors for PacerWorkflowOrchestrator: {required_processors}")
        
        self.log_info("PacerWorkflowOrchestrator initialized successfully")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate workflow methods."""
        action = data.get("action")
        
        if action == "execute_workflow":
            return await self.execute_workflow(
                court_ids=data.get("court_ids", []),
                workflow_config=data.get("workflow_config", {}),
                processor_config=data.get("processor_config", {})
            )
        elif action == "process_court":
            return await self.process_single_court(
                court_id=data.get("court_id"),
                workflow_config=data.get("workflow_config", {}),
                processor_config=data.get("processor_config", {})
            )
        elif action == "process_docket":
            return await self.process_single_docket(
                page=data.get("page"),
                initial_details=data.get("initial_details", {})
            )
        else:
            raise ValueError(f"Unknown action for PacerWorkflowOrchestrator: {action}")

    async def execute_workflow(
        self, 
        court_ids: List[str], 
        workflow_config: Optional[Dict[str, Any]] = None,
        processor_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute the complete PACER workflow with clear phase separation.
        
        Args:
            court_ids: List of court IDs to process
            workflow_config: Workflow configuration parameters
            processor_config: Additional processor configuration
            
        Returns:
            Aggregated results from all processing phases
        """
        workflow_config = workflow_config or {}
        processor_config = processor_config or {}
        
        self.log_info(f"Executing PACER workflow for {len(court_ids)} courts: {court_ids}")
        
        # Initialize results structure
        results = {
            'workflow_status': 'started',
            'total_courts': len(court_ids),
            'court_results': {},
            'phase_summaries': {
                'court_processing': {'courts': 0, 'dockets_discovered': 0},
                'docket_processing': {'dockets_processed': 0, 'cases_extracted': 0},
                'row_processing': {'rows_processed': 0, 'download_required': 0},
                'download_workflow': {'downloads_completed': 0, 'files_saved': 0}
            },
            'error_summary': {'courts_failed': 0, 'errors': []}
        }
        
        try:
            # Setup workflow environment
            await self._setup_workflow_environment(workflow_config)
            
            # Process each court through all phases
            for court_id in court_ids:
                try:
                    self.log_info(f"Starting workflow processing for court: {court_id}")
                    
                    court_result = await self.process_single_court(
                        court_id=court_id,
                        workflow_config=workflow_config,
                        processor_config=processor_config
                    )
                    
                    results['court_results'][court_id] = court_result
                    
                    # Update phase summaries
                    self._update_phase_summaries(results, court_result)
                    
                    self.log_info(f"Completed workflow processing for court: {court_id}")
                    
                except Exception as e:
                    error_msg = f"Workflow failed for court {court_id}: {e}"
                    self.log_error(error_msg)
                    
                    results['court_results'][court_id] = {
                        'status': 'failed',
                        'error': str(e),
                        'court_id': court_id
                    }
                    results['error_summary']['courts_failed'] += 1
                    results['error_summary']['errors'].append(error_msg)
            
            # Finalize results
            results['workflow_status'] = 'completed'
            successful_courts = len([r for r in results['court_results'].values() if r.get('status') == 'success'])
            
            self.log_info(f"PACER workflow completed: {successful_courts}/{len(court_ids)} courts successful")
            
            return results
            
        except Exception as e:
            self.log_error(f"Critical workflow error: {e}")
            results['workflow_status'] = 'failed'
            results['workflow_error'] = str(e)
            return results

    async def process_single_court(
        self, 
        court_id: str, 
        workflow_config: Dict[str, Any],
        processor_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process a single court through all workflow phases.
        
        Args:
            court_id: Court identifier to process
            workflow_config: Workflow configuration
            processor_config: Processor configuration
            
        Returns:
            Complete processing results for the court
        """
        self.log_info(f"Processing court {court_id} through all workflow phases")
        
        result = {
            'court_id': court_id,
            'status': 'failed',
            'phases_completed': [],
            'phase_results': {}
        }
        
        try:
            # PHASE 1: Court Processing
            self.log_info(f"[{court_id}] Phase 1: Court Processing")
            
            court_result = await self.court_processor.perform_action({
                "action": "process_court",
                "court_id": court_id,
                "start_date": workflow_config.get('start_date'),
                "end_date": workflow_config.get('end_date'),
                "config": workflow_config,
                "browser_context": workflow_config.get('browser_context')
            })
            
            result['phase_results']['court_processing'] = court_result
            
            if court_result.get('status') != 'success':
                result['error'] = f"Court processing failed: {court_result.get('error')}"
                return result
            
            result['phases_completed'].append('court_processing')
            discovered_dockets = court_result.get('dockets', [])
            
            if not discovered_dockets:
                result['status'] = 'success_no_dockets'
                result['message'] = f"No dockets discovered for court {court_id}"
                return result
            
            # PHASE 2: Docket Processing  
            self.log_info(f"[{court_id}] Phase 2: Docket Processing ({len(discovered_dockets)} dockets)")
            
            docket_results = await self.process_dockets_for_court(
                court_id, discovered_dockets, workflow_config
            )
            
            result['phase_results']['docket_processing'] = {
                'total_dockets': len(discovered_dockets),
                'processed_dockets': len(docket_results),
                'docket_results': docket_results
            }
            result['phases_completed'].append('docket_processing')
            
            if not docket_results:
                result['status'] = 'success_no_cases'
                result['message'] = f"No cases extracted from dockets for court {court_id}"
                return result
            
            # PHASE 3: Row Processing
            self.log_info(f"[{court_id}] Phase 3: Row Processing ({len(docket_results)} cases)")
            
            row_results = await self.process_rows_for_dockets(docket_results)
            
            result['phase_results']['row_processing'] = {
                'total_rows': len(docket_results),
                'processed_rows': len(row_results),
                'download_required': len([r for r in row_results if r.get('requires_download')]),
                'row_results': row_results
            }
            result['phases_completed'].append('row_processing')
            
            # PHASE 4: Download Workflow
            download_candidates = [r for r in row_results if r.get('requires_download')]
            
            if download_candidates:
                self.log_info(f"[{court_id}] Phase 4: Download Workflow ({len(download_candidates)} downloads)")
                
                download_results = await self.execute_downloads_for_rows(download_candidates, workflow_config)
                
                result['phase_results']['download_workflow'] = {
                    'total_candidates': len(download_candidates),
                    'completed_downloads': len(download_results),
                    'download_results': download_results
                }
                result['phases_completed'].append('download_workflow')
            else:
                self.log_info(f"[{court_id}] Phase 4: No downloads required")
                result['phase_results']['download_workflow'] = {
                    'total_candidates': 0,
                    'completed_downloads': 0,
                    'download_results': []
                }
            
            # Success
            result['status'] = 'success'
            result['message'] = f"All phases completed successfully for court {court_id}"
            
            self.log_info(f"[{court_id}] All workflow phases completed successfully")
            return result
            
        except Exception as e:
            self.log_error(f"Error processing court {court_id}: {e}")
            result['error'] = str(e)
            return result

    async def process_dockets_for_court(
        self, 
        court_id: str, 
        discovered_dockets: List[Dict[str, Any]], 
        workflow_config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Process all dockets for a court through docket processing phase.
        
        Args:
            court_id: Court identifier
            discovered_dockets: List of dockets discovered in court processing
            workflow_config: Workflow configuration
            
        Returns:
            List of processed docket results with extracted case details
        """
        self.log_info(f"Processing {len(discovered_dockets)} dockets for court {court_id}")
        
        docket_results = []
        
        for i, docket_info in enumerate(discovered_dockets):
            try:
                self.log_debug(f"Processing docket {i+1}/{len(discovered_dockets)}: {docket_info.get('docket_num')}")
                
                # Prepare initial details for docket processing
                # Map docket_report_log fields to expected field names
                initial_details = {
                    "court_id": court_id,
                    "docket_num": docket_info.get("docket_num"),
                    # docket_report_log uses 'versus' field, not 'case_title'
                    "case_title": docket_info.get("versus", docket_info.get("case_title", "")),
                    # docket_report_log uses 'filing_date' field, not 'filed_date'
                    "filed_date": docket_info.get("filing_date", docket_info.get("filed_date", "")),
                    "iso_date": workflow_config.get("iso_date"),
                    **docket_info  # Include all docket metadata
                }
                
                # Process through docket processor (this will handle HTML parsing and case extraction)
                docket_result = await self.docket_processor.perform_action({
                    "action": "process_docket", 
                    "page": None,  # Page will be created as needed
                    "initial_details": initial_details,
                    "html_content": None  # Will be retrieved as needed
                })
                
                if docket_result:
                    # CRITICAL FIX: Save JSON file with comprehensive data merging
                    try:
                        iso_date = workflow_config.get("iso_date")
                        if iso_date and self.file_operations_service:
                            # Extract HTML content if available for merging
                            html_content = docket_result.get('html_content')
                            
                            # Prepare S3 metadata if available
                            s3_metadata = {
                                'upload_timestamp': docket_result.get('_html_upload_timestamp'),
                                's3_key': docket_result.get('_s3_html_key'),
                                'upload_status': docket_result.get('_html_upload_status')
                            } if any(docket_result.get(k) for k in ['_html_upload_timestamp', '_s3_html_key', '_html_upload_status']) else None
                            
                            # Save with comprehensive data merging
                            json_path = await self.file_operations_service.save_case_data(
                                case_data=docket_result,
                                iso_date=iso_date,
                                html_content=html_content,
                                s3_metadata=s3_metadata
                            )
                            
                            docket_result['_json_saved_path'] = json_path
                            docket_result['_json_saved_timestamp'] = datetime.now().isoformat()
                            docket_result['_enhanced_save_version'] = '2.0'
                            
                            self.log_info(f"Enhanced JSON saved to {json_path} for docket {docket_info.get('docket_num')}", {
                                "has_html_merged": html_content is not None,
                                "has_s3_metadata": s3_metadata is not None
                            })
                        else:
                            self.log_warning(f"No iso_date or file_operations_service available, skipping JSON save for docket {docket_info.get('docket_num')}")
                    except Exception as save_error:
                        self.log_error(f"Failed to save JSON file for docket {docket_info.get('docket_num')}: {save_error}")
                        # Don't fail the entire process for a save error
                        docket_result['_json_save_error'] = str(save_error)
                    
                    docket_results.append(docket_result)
                
            except Exception as e:
                self.log_error(f"Error processing docket {docket_info.get('docket_num', 'unknown')}: {e}")
                continue
        
        self.log_info(f"Docket processing completed for court {court_id}: {len(docket_results)}/{len(discovered_dockets)} successful")
        return docket_results

    async def process_rows_for_dockets(self, docket_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process rows (cases) extracted from dockets through row processing phase.
        
        Args:
            docket_results: List of docket processing results
            
        Returns:
            List of row processing results with business rules applied
        """
        self.log_info(f"Processing rows for {len(docket_results)} dockets")
        
        # Extract all cases from docket results
        all_cases = []
        for docket_result in docket_results:
            # Each docket result represents one case
            all_cases.append(docket_result)
        
        # Process all cases through row processor
        row_results = await self.row_processor.perform_action({
            "action": "process_rows",
            "cases": all_cases
        })
        
        self.log_info(f"Row processing completed: {len(row_results)} cases processed")
        return row_results

    async def execute_downloads_for_rows(
        self, 
        row_results: List[Dict[str, Any]], 
        workflow_config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Execute downloads for cases that require them through download workflow phase.
        
        Args:
            row_results: List of row processing results requiring downloads
            workflow_config: Workflow configuration
            
        Returns:
            List of download workflow results
        """
        self.log_info(f"Executing downloads for {len(row_results)} qualified cases")
        
        download_results = []
        
        for row in row_results:
            try:
                if row.get('requires_download'):
                    # Execute download workflow for this case
                    download_result = await self.download_orchestrator.perform_action({
                        "action": "execute_download_workflow",
                        "case_details": row,
                        "page": None,  # Page will be created as needed
                        "is_explicitly_requested": row.get("_is_explicitly_requested", False)
                    })
                    
                    if download_result:
                        # Save case data with download results
                        final_result = await self._save_case_data_with_download(row, download_result, workflow_config)
                        download_results.append(final_result)
                    
            except Exception as e:
                self.log_error(f"Error executing download for case {row.get('docket_num', 'unknown')}: {e}")
                continue
        
        self.log_info(f"Download workflow completed: {len(download_results)} downloads processed")
        return download_results

    async def process_single_docket(
        self, 
        page: Any, 
        initial_details: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Process a single docket through all phases (for compatibility/direct use).
        
        Args:
            page: Browser page with docket content
            initial_details: Initial docket/case details
            
        Returns:
            Complete processing result for the single docket
        """
        court_id = initial_details.get("court_id", "N/A")
        docket_num = initial_details.get("docket_num", "N/A")
        
        self.log_info(f"[{court_id}][{docket_num}] Processing single docket through all phases")
        
        try:
            # Phase 1: Docket Processing
            docket_result = await self.docket_processor.process_docket(
                page=page,
                initial_details=initial_details
            )
            
            if not docket_result:
                return None
            
            # Phase 2: Row Processing  
            row_result = await self.row_processor.process_single_row(docket_result)
            
            # Phase 3: Download Workflow (if required)
            if row_result.get('requires_download'):
                download_result = await self.download_orchestrator.perform_action({
                    "action": "execute_download_workflow",
                    "case_details": row_result,
                    "page": page,
                    "is_explicitly_requested": row_result.get("_is_explicitly_requested", False)
                })
                
                if download_result:
                    row_result.update(download_result)
            
            # Phase 4: Save Data
            iso_date = initial_details.get("iso_date") or self.config.get("iso_date")
            if iso_date and self.file_operations_service:
                try:
                    local_path = await self.file_operations_service.save_case_data(row_result, iso_date)
                    row_result["_local_path"] = local_path
                    
                    # Try S3 upload
                    try:
                        s3_key = f"{iso_date}/{court_id}/{row_result.get('base_filename', 'unknown')}.json"
                        s3_url = await self.file_operations_service.upload_to_s3(local_path, s3_key)
                        row_result["_s3_url"] = s3_url
                    except Exception as s3_e:
                        self.log_warning(f"S3 upload failed: {s3_e}")
                        row_result["_s3_url"] = ""
                        
                except Exception as save_e:
                    self.log_error(f"Failed to save case data: {save_e}")
            
            row_result["_processing_phase"] = "completed"
            row_result["_processing_status"] = "success"
            
            self.log_info(f"[{court_id}][{docket_num}] Single docket processing completed successfully")
            return row_result
            
        except Exception as e:
            self.log_error(f"[{court_id}][{docket_num}] Single docket processing failed: {e}")
            return None

    async def _setup_workflow_environment(self, workflow_config: Dict[str, Any]) -> None:
        """Setup workflow environment and directories."""
        iso_date = workflow_config.get('iso_date')
        if iso_date and self.file_operations_service:
            try:
                if hasattr(self.file_operations_service, 'setup_directories'):
                    await self.file_operations_service.setup_directories(iso_date)
                    self.log_info(f"Setup workflow directories for date: {iso_date}")
            except Exception as e:
                self.log_warning(f"Could not setup workflow directories: {e}")

    async def _save_case_data_with_download(
        self, 
        case_details: Dict[str, Any], 
        download_result: Dict[str, Any], 
        workflow_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Save case data with download results."""
        # Merge download results
        final_case = case_details.copy()
        final_case.update(download_result)
        
        # Save to file system
        iso_date = workflow_config.get("iso_date")
        if iso_date and self.file_operations_service:
            try:
                # Clean temporary fields
                cleaned_case = self._cleanup_temporary_fields(final_case)
                
                local_path = await self.file_operations_service.save_case_data(cleaned_case, iso_date)
                final_case["_local_path"] = local_path
                
                # Try S3 upload
                try:
                    court_id = final_case.get("court_id", "unknown")
                    base_filename = final_case.get("base_filename", "unknown")
                    s3_key = f"{iso_date}/{court_id}/{base_filename}.json"
                    s3_url = await self.file_operations_service.upload_to_s3(local_path, s3_key)
                    final_case["_s3_url"] = s3_url
                except Exception as s3_e:
                    self.log_warning(f"S3 upload failed: {s3_e}")
                    final_case["_s3_url"] = ""
                    
            except Exception as save_e:
                self.log_error(f"Failed to save case data with download: {save_e}")
        
        return final_case

    def _update_phase_summaries(self, results: Dict[str, Any], court_result: Dict[str, Any]) -> None:
        """Update phase summaries with court result."""
        if court_result.get('status') == 'success':
            results['phase_summaries']['court_processing']['courts'] += 1
            
            # Update docket processing summary
            docket_phase = court_result.get('phase_results', {}).get('docket_processing', {})
            results['phase_summaries']['docket_processing']['dockets_processed'] += docket_phase.get('processed_dockets', 0)
            results['phase_summaries']['docket_processing']['cases_extracted'] += docket_phase.get('processed_dockets', 0)
            
            # Update row processing summary
            row_phase = court_result.get('phase_results', {}).get('row_processing', {})
            results['phase_summaries']['row_processing']['rows_processed'] += row_phase.get('processed_rows', 0)
            results['phase_summaries']['row_processing']['download_required'] += row_phase.get('download_required', 0)
            
            # Update download workflow summary
            download_phase = court_result.get('phase_results', {}).get('download_workflow', {})
            results['phase_summaries']['download_workflow']['downloads_completed'] += download_phase.get('completed_downloads', 0)

    def _cleanup_temporary_fields(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Remove temporary processing fields before final save."""
        cleaned_data = case_details.copy()
        
        # Remove HTML content (too large for storage)
        cleaned_data.pop("_html_content", None)
        
        # Remove other temporary fields but keep important processing metadata
        fields_to_remove = [
            field for field in cleaned_data.keys() 
            if field.startswith('_') and field not in [
                '_processing_phase', '_processing_status', '_local_path', '_s3_url',
                '_verification_result', '_is_explicitly_requested'
            ]
        ]
        
        for field in fields_to_remove:
            cleaned_data.pop(field, None)
        
        return cleaned_data

    async def get_workflow_statistics(self) -> Dict[str, Any]:
        """Get statistics about workflow operations."""
        return {
            "total_workflows_executed": 0,
            "phases_breakdown": {
                "court_processing": {"total": 0, "successful": 0},
                "docket_processing": {"total": 0, "successful": 0},
                "row_processing": {"total": 0, "successful": 0},
                "download_workflow": {"total": 0, "successful": 0}
            },
            "performance_metrics": {
                "average_court_processing_time": 0,
                "average_docket_processing_time": 0,
                "average_row_processing_time": 0,
                "average_download_time": 0
            }
        }