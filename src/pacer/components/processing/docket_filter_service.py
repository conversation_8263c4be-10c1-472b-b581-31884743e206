"""
Docket Filter Service - Batch filtering integration for docket processing pipeline

This service integrates the DocketArtifactChecker with the docket processing pipeline
to efficiently filter docket_report_log items before processing. It provides:

1. Batch filtering of docket items using artifact_checker
2. DynamoDB existence checking for transferred dockets
3. Parallel processing for efficiency
4. Comprehensive logging and error handling
5. Integration with existing docket processing architecture

Usage:
    - Integrates at the beginning of docket processing pipeline
    - Filters docket_report_log items before individual processing
    - Provides statistics on filtered vs processed items
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.pacer.components.download.artifact_checker import DocketArtifactChecker
from src.repositories.pacer_repository import PacerRepository


class DocketFilterService(ComponentImplementation):
    """
    Service for batch filtering of docket report log items using artifact checker.
    
    This service provides efficient filtering of docket items before processing,
    integrating DynamoDB existence checking and parallel batch processing.
    """
    
    def __init__(
        self,
        logger,
        config: Dict[str, Any],
        pacer_repository: Optional[PacerRepository] = None,
        artifact_checker: Optional[DocketArtifactChecker] = None,
        batch_size: int = 50
    ):
        """
        Initialize the docket filter service.
        
        Args:
            logger: Logger instance for logging
            config: Configuration dictionary
            pacer_repository: Repository for DynamoDB checking
            artifact_checker: Artifact checker component (created if not provided)
            batch_size: Size of batches for parallel processing
        """
        super().__init__(logger, config)
        self.pacer_repository = pacer_repository
        self.batch_size = batch_size
        
        # Initialize artifact checker if not provided
        if artifact_checker:
            self.artifact_checker = artifact_checker
        else:
            self.artifact_checker = DocketArtifactChecker(
                logger, config, pacer_repository
            )
    
    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        
        if action == 'filter_docket_report_log':
            return await self.filter_docket_report_log(
                data['docket_items'],
                data.get('iso_date'),
                data.get('batch_size', self.batch_size)
            )
        elif action == 'filter_and_process_batches':
            return await self.filter_and_process_batches(
                data['docket_items'],
                data.get('iso_date'),
                data.get('processing_callback')
            )
        elif action == 'health_check':
            return await self.health_check()
        else:
            raise ValueError(f"Unknown action for DocketFilterService: {action}")
    
    async def filter_docket_report_log(
        self,
        docket_items: List[Dict[str, Any]],
        iso_date: Optional[str] = None,
        batch_size: Optional[int] = None,
        court_logger = None
    ) -> Dict[str, Any]:
        """
        Filter a list of docket items from report log using artifact checker.
        
        Args:
            docket_items: List of docket items from report log
            iso_date: ISO date for artifact checking
            batch_size: Size of batches for parallel processing
            court_logger: Court-specific logger for consistent logging
            
        Returns:
            Dictionary containing filtering results and statistics
        """
        start_time = datetime.now()
        batch_size = batch_size or self.batch_size
        iso_date = iso_date or self.config.get('iso_date')
        
        # Use court_logger if provided for consistent logging
        logger = court_logger or self.logger
        log_prefix = "DOCKET_FILTER_SERVICE:"
        
        logger.info(f"{log_prefix} ============ STARTING DOCKET_REPORT_LOG FILTERING ============")
        logger.info(f"{log_prefix} Input parameters: {len(docket_items)} docket items, batch_size={batch_size}, iso_date={iso_date}")
        
        # Log sample of input docket items for analysis
        if docket_items:
            logger.info(f"{log_prefix} SAMPLE INPUT ANALYSIS - First 3 docket items:")
            for i, item in enumerate(docket_items[:3]):
                court_id = item.get('court_id', 'Unknown')
                docket_num = item.get('docket_num', 'Unknown')
                versus = item.get('versus', item.get('title', 'Unknown'))
                logger.info(f"{log_prefix} Sample {i+1}: court_id={court_id}, docket_num={docket_num}, versus='{versus}'")
        
        logger.info(f"{log_prefix} Starting batch filtering of {len(docket_items)} docket items")
        
        # Initialize result tracking
        filtering_results = {
            'total_items': len(docket_items),
            'filtered_items': [],
            'items_to_process': [],
            'skip_statistics': {
                'html_only': 0,
                'transferred_with_existing': 0,
                'has_artifacts': 0,
                'no_json_but_skip': 0,
                'errors': 0
            },
            'processing_time': 0.0,
            'batches_processed': 0
        }
        
        if not docket_items:
            self.log_info("No docket items to filter")
            return filtering_results
        
        try:
            # Process items in batches for efficiency
            batches = [
                docket_items[i:i + batch_size] 
                for i in range(0, len(docket_items), batch_size)
            ]
            
            self.log_info(f"Processing {len(batches)} batches of size {batch_size}")
            
            # Process batches in parallel (limited concurrency)
            semaphore = asyncio.Semaphore(3)  # Limit concurrent batch processing
            batch_tasks = [
                self._process_filter_batch(batch, semaphore, iso_date, batch_idx, logger)
                for batch_idx, batch in enumerate(batches)
            ]
            
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # Aggregate results from all batches
            for batch_idx, batch_result in enumerate(batch_results):
                if isinstance(batch_result, Exception):
                    self.log_error(f"Batch {batch_idx} failed: {batch_result}")
                    filtering_results['skip_statistics']['errors'] += len(batches[batch_idx])
                    continue
                
                # Merge batch results
                filtering_results['filtered_items'].extend(batch_result['filtered_items'])
                filtering_results['items_to_process'].extend(batch_result['items_to_process'])
                
                # Aggregate statistics
                for stat_key, count in batch_result['statistics'].items():
                    filtering_results['skip_statistics'][stat_key] += count
                
                filtering_results['batches_processed'] += 1
            
            # Calculate final statistics
            processing_time = (datetime.now() - start_time).total_seconds()
            filtering_results['processing_time'] = processing_time
            
            # COMPREHENSIVE FILTERING RESULTS LOGGING
            total_to_process = len(filtering_results['items_to_process'])
            total_filtered = len(filtering_results['filtered_items'])
            
            logger.info(f"{log_prefix} ============ FILTERING RESULTS SUMMARY ============")
            logger.info(
                f"{log_prefix} RESULTS: {total_to_process} items TO PROCESS, "
                f"{total_filtered} items FILTERED OUT of {len(docket_items)} total "
                f"in {processing_time:.2f}s"
            )
            
            # Log detailed statistics with enhanced breakdown
            stats = filtering_results['skip_statistics']
            logger.info(f"{log_prefix} DETAILED FILTER STATISTICS:")
            logger.info(f"{log_prefix} - HTML-only flags: {stats['html_only']} items")
            logger.info(f"{log_prefix} - Transferred with existing: {stats['transferred_with_existing']} items")
            logger.info(f"{log_prefix} - Has existing artifacts: {stats['has_artifacts']} items")
            logger.info(f"{log_prefix} - Other skip reasons: {stats['no_json_but_skip']} items")
            logger.info(f"{log_prefix} - Processing errors: {stats['errors']} items")
            
            # Calculate and log efficiency metrics
            if len(docket_items) > 0:
                filter_rate = (total_filtered / len(docket_items)) * 100
                process_rate = (total_to_process / len(docket_items)) * 100
                logger.info(f"{log_prefix} EFFICIENCY METRICS:")
                logger.info(f"{log_prefix} - Filter rate: {filter_rate:.1f}% (items skipped)")
                logger.info(f"{log_prefix} - Process rate: {process_rate:.1f}% (items to process)")
                logger.info(f"{log_prefix} - Processing speed: {len(docket_items)/processing_time:.1f} items/second")
            
            logger.info(f"{log_prefix} ============ DOCKET_REPORT_LOG FILTERING COMPLETE ============")
            
            return filtering_results
            
        except Exception as e:
            self.log_error(f"Error during batch filtering: {str(e)}", exc_info=True)
            filtering_results['skip_statistics']['errors'] = len(docket_items)
            filtering_results['processing_time'] = (datetime.now() - start_time).total_seconds()
            return filtering_results
    
    async def _process_filter_batch(
        self,
        batch_items: List[Dict[str, Any]],
        semaphore: asyncio.Semaphore,
        iso_date: str,
        batch_idx: int,
        court_logger = None
    ) -> Dict[str, Any]:
        """
        Process a single batch of docket items through artifact checker.
        
        Args:
            batch_items: Batch of docket items to filter
            semaphore: Concurrency control semaphore
            iso_date: ISO date for artifact checking
            batch_idx: Index of this batch for logging
            court_logger: Court-specific logger for consistent logging
            
        Returns:
            Dictionary with filtering results for this batch
        """
        async with semaphore:
            # Use court_logger if provided for consistent logging
            logger = court_logger or self.logger
            log_prefix = f"FILTER_BATCH_{batch_idx}:"
            
            logger.info(f"{log_prefix} Starting filter batch {batch_idx} with {len(batch_items)} items")
            
            # Log detailed input for this batch
            logger.info(f"{log_prefix} BATCH INPUT ANALYSIS:")
            for i, item in enumerate(batch_items):
                court_id = item.get('court_id', 'Unknown')
                docket_num = item.get('docket_num', 'Unknown')
                versus = item.get('versus', item.get('title', 'Unknown'))
                logger.info(f"{log_prefix} Item {i+1}: {court_id}/{docket_num} - '{versus}'")
            
            batch_result = {
                'filtered_items': [],
                'items_to_process': [],
                'statistics': {
                    'html_only': 0,
                    'transferred_with_existing': 0,
                    'has_artifacts': 0,
                    'no_json_but_skip': 0,
                    'errors': 0
                }
            }
            
            try:
                # Use artifact checker's batch processing
                check_results = await self.artifact_checker.check_batch(
                    batch_items, iso_date
                )
                
                # Process results with comprehensive logging
                logger.info(f"{log_prefix} ARTIFACT CHECKER RESULTS:")
                logger.info(f"{log_prefix} - Items to download: {len(check_results['to_download'])}")
                logger.info(f"{log_prefix} - Items to skip: {len(check_results['to_skip'])}")
                
                # Process TO_DOWNLOAD results
                for item_info in check_results['to_download']:
                    # Items that need processing
                    original_item = self._find_original_item(batch_items, item_info)
                    if original_item:
                        court_id = original_item.get('court_id', 'Unknown')
                        docket_num = original_item.get('docket_num', 'Unknown')
                        reason = item_info['reason']
                        
                        logger.info(f"{log_prefix} TO_PROCESS: {court_id}/{docket_num} - Reason: {reason}")
                        
                        batch_result['items_to_process'].append({
                            **original_item,
                            '_filter_reason': reason,
                            '_filter_decision': 'process'
                        })
                
                # Process TO_SKIP results
                for item_info in check_results['to_skip']:
                    # Items to be filtered out
                    original_item = self._find_original_item(batch_items, item_info)
                    if original_item:
                        court_id = original_item.get('court_id', 'Unknown')
                        docket_num = original_item.get('docket_num', 'Unknown')
                        reason = item_info['reason']
                        
                        logger.info(f"{log_prefix} TO_SKIP: {court_id}/{docket_num} - Reason: {reason}")
                        
                        batch_result['filtered_items'].append({
                            **original_item,
                            '_filter_reason': reason,
                            '_filter_decision': 'skip'
                        })
                        
                        # Update statistics based on reason with detailed logging
                        reason_lower = reason.lower()
                        if 'html_only' in reason_lower:
                            batch_result['statistics']['html_only'] += 1
                            logger.info(f"{log_prefix} SKIP_CATEGORY: html_only - {court_id}/{docket_num}")
                        elif 'transferred' in reason_lower:
                            batch_result['statistics']['transferred_with_existing'] += 1
                            logger.info(f"{log_prefix} SKIP_CATEGORY: transferred_with_existing - {court_id}/{docket_num}")
                        elif 'artifacts' in reason_lower or 'exist' in reason_lower:
                            batch_result['statistics']['has_artifacts'] += 1
                            logger.info(f"{log_prefix} SKIP_CATEGORY: has_artifacts - {court_id}/{docket_num}")
                        else:
                            batch_result['statistics']['no_json_but_skip'] += 1
                            logger.info(f"{log_prefix} SKIP_CATEGORY: other - {court_id}/{docket_num}")
                
                # Log batch completion with detailed results
                logger.info(f"{log_prefix} ============ BATCH {batch_idx} COMPLETE ============")
                logger.info(
                    f"{log_prefix} BATCH RESULTS: {len(batch_result['items_to_process'])} to process, "
                    f"{len(batch_result['filtered_items'])} filtered out of {len(batch_items)} total"
                )
                logger.info(f"{log_prefix} BATCH STATISTICS: {batch_result['statistics']}")
                
                return batch_result
                
            except Exception as e:
                logger = court_logger or self.logger
                log_prefix = f"FILTER_BATCH_{batch_idx}:"
                
                logger.error(f"{log_prefix} ERROR processing filter batch {batch_idx}: {str(e)}", exc_info=True)
                logger.error(f"{log_prefix} ERROR - Batch had {len(batch_items)} items, all marked as errors")
                
                # Mark all items in batch as errors
                batch_result['statistics']['errors'] = len(batch_items)
                
                # Log the failed items for debugging
                for i, item in enumerate(batch_items):
                    court_id = item.get('court_id', 'Unknown')
                    docket_num = item.get('docket_num', 'Unknown')
                    logger.error(f"{log_prefix} ERROR_ITEM {i+1}: {court_id}/{docket_num} - Processing failed")
                
                return batch_result
    
    def _find_original_item(
        self,
        batch_items: List[Dict[str, Any]],
        item_info: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Find the original docket item corresponding to the filtered item info.
        
        Args:
            batch_items: Original batch items
            item_info: Filtered item info with court_id, docket_num, versus
            
        Returns:
            Original item if found, None otherwise
        """
        target_court = item_info.get('court_id', '').lower()
        target_docket = item_info.get('docket_num', '')
        target_versus = item_info.get('versus', '')
        
        for item in batch_items:
            item_court = item.get('court_id', '').lower()
            item_docket = item.get('docket_num', '')
            item_versus = item.get('versus', item.get('title', ''))
            
            if (item_court == target_court and 
                item_docket == target_docket and 
                item_versus == target_versus):
                return item
        
        # If exact match fails, try partial matching
        for item in batch_items:
            item_court = item.get('court_id', '').lower()
            item_docket = item.get('docket_num', '')
            
            if item_court == target_court and item_docket == target_docket:
                return item
        
        self.log_warning(
            f"Could not find original item for court={target_court}, "
            f"docket={target_docket}"
        )
        return None
    
    async def filter_and_process_batches(
        self,
        docket_items: List[Dict[str, Any]],
        iso_date: Optional[str] = None,
        processing_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """
        Filter docket items and optionally process them in batches.
        
        This method combines filtering with optional batch processing,
        useful for streaming processing scenarios.
        
        Args:
            docket_items: List of docket items to filter and process
            iso_date: ISO date for artifact checking
            processing_callback: Optional callback for processing filtered items
            
        Returns:
            Dictionary with filtering and processing results
        """
        # First filter the items
        filter_results = await self.filter_docket_report_log(
            docket_items, iso_date
        )
        
        items_to_process = filter_results['items_to_process']
        
        processing_results = {
            'filtering': filter_results,
            'processing': {
                'total_items': len(items_to_process),
                'processed_items': 0,
                'failed_items': 0,
                'processing_time': 0.0
            }
        }
        
        # Process filtered items if callback provided
        if processing_callback and items_to_process:
            self.log_info(f"Starting processing of {len(items_to_process)} filtered items")
            
            process_start = datetime.now()
            processed_count = 0
            failed_count = 0
            
            # Process in batches for memory efficiency
            for i in range(0, len(items_to_process), self.batch_size):
                batch = items_to_process[i:i + self.batch_size]
                
                try:
                    batch_results = await processing_callback(batch)
                    
                    if isinstance(batch_results, dict):
                        processed_count += batch_results.get('processed', 0)
                        failed_count += batch_results.get('failed', 0)
                    else:
                        # Assume success if no detailed results
                        processed_count += len(batch)
                        
                except Exception as e:
                    self.log_error(f"Processing batch failed: {str(e)}", exc_info=True)
                    failed_count += len(batch)
            
            processing_time = (datetime.now() - process_start).total_seconds()
            
            processing_results['processing'].update({
                'processed_items': processed_count,
                'failed_items': failed_count,
                'processing_time': processing_time
            })
            
            self.log_info(
                f"Processing complete: {processed_count} processed, "
                f"{failed_count} failed in {processing_time:.2f}s"
            )
        
        return processing_results
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the filter service.
        
        Returns:
            Dictionary with health status information
        """
        health_status = {
            'service': 'DocketFilterService',
            'status': 'healthy',
            'components': {}
        }
        
        # Check artifact checker
        try:
            if self.artifact_checker:
                health_status['components']['artifact_checker'] = 'available'
            else:
                health_status['components']['artifact_checker'] = 'not_available'
                health_status['status'] = 'degraded'
        except Exception as e:
            health_status['components']['artifact_checker'] = f'error: {str(e)}'
            health_status['status'] = 'degraded'
        
        # Check repository
        try:
            if self.pacer_repository:
                health_status['components']['pacer_repository'] = 'available'
            else:
                health_status['components']['pacer_repository'] = 'not_available'
                health_status['status'] = 'degraded'
        except Exception as e:
            health_status['components']['pacer_repository'] = f'error: {str(e)}'
            health_status['status'] = 'degraded'
        
        # Check configuration
        health_status['components']['configuration'] = {
            'batch_size': self.batch_size,
            'has_iso_date': bool(self.config.get('iso_date'))
        }
        
        return health_status
    
    def get_filter_statistics_summary(self, filter_results: Dict[str, Any]) -> str:
        """
        Generate a human-readable summary of filtering statistics.
        
        Args:
            filter_results: Results from filter_docket_report_log
            
        Returns:
            Formatted string with filtering summary
        """
        stats = filter_results['skip_statistics']
        
        summary_lines = [
            f"Filtering Summary:",
            f"  Total items: {filter_results['total_items']}",
            f"  Items to process: {len(filter_results['items_to_process'])}",
            f"  Items filtered out: {len(filter_results['filtered_items'])}",
            f"  Processing time: {filter_results['processing_time']:.2f}s",
            f"",
            f"Skip reasons:",
            f"  HTML only: {stats['html_only']}",
            f"  Transferred with existing: {stats['transferred_with_existing']}",
            f"  Has artifacts: {stats['has_artifacts']}",
            f"  Other: {stats['no_json_but_skip']}",
            f"  Errors: {stats['errors']}"
        ]
        
        return "\n".join(summary_lines)