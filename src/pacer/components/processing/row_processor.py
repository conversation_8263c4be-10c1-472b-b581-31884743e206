"""
Row Processing Component for PACER Workflow.

This component handles row-level operations including business rules,
classification, and download decisions for individual cases.
"""

from typing import Any, Dict, Optional, List
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.services.relevance_service import RelevanceService
from src.pacer.services.classification_service import ClassificationService
from src.pacer.services.verification_service import VerificationService


class RowProcessor(AsyncServiceBase):
    """
    Handles row-level operations: business rules, classification, download decisions.
    
    Responsibilities:
    - Process individual case rows with business rules
    - Apply case classification logic
    - Determine download requirements for each case
    - Execute verification rules
    - Apply relevance filtering
    """

    def __init__(
        self,
        relevance_service: Optional[RelevanceService] = None,
        classification_service: Optional[ClassificationService] = None,
        verification_service: Optional[VerificationService] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
    ):
        super().__init__(logger, config)
        self.relevance_service = relevance_service
        self.classification_service = classification_service
        self.verification_service = verification_service

    async def _initialize_service(self) -> None:
        """Initialize the row processor and validate dependencies."""
        if self._initialized:
            return
        
        # Validate required dependencies
        required_services = []
        if not self.relevance_service:
            required_services.append("RelevanceService")
        if not self.verification_service:
            required_services.append("VerificationService")
        
        if required_services:
            raise ValueError(f"Missing required services for RowProcessor: {required_services}")
        
        # Classification service is optional
        if not self.classification_service:
            self.log_info("ClassificationService not available, skipping classification functionality")
        
        self.log_info("RowProcessor initialized successfully")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate row processing methods."""
        action = data.get("action")
        
        if action == "process_rows":
            return await self.process_rows(data.get("cases", []))
        elif action == "process_single_row":
            return await self.process_single_row(data.get("case_details", {}))
        elif action == "apply_business_rules":
            return await self.apply_business_rules(data.get("case_details", {}))
        elif action == "determine_download_requirement":
            return await self.determine_download_requirement(data.get("case_details", {}))
        elif action == "classify_case":
            return await self.classify_case(data.get("case_details", {}))
        else:
            raise ValueError(f"Unknown action for RowProcessor: {action}")

    async def process_rows(self, cases: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process individual case rows with business rules.
        
        Args:
            cases: List of case details to process
            
        Returns:
            List of processed cases with applied business rules and decisions
        """
        self.log_info(f"Processing {len(cases)} case rows")
        
        processed_rows = []
        
        for i, case in enumerate(cases):
            try:
                self.log_debug(f"Processing row {i+1}/{len(cases)}: {case.get('docket_num', 'unknown')}")
                
                processed_case = await self.process_single_row(case)
                processed_rows.append(processed_case)
                
            except Exception as e:
                self.log_error(f"Error processing row {i+1}: {e}")
                # Add error metadata but include the case
                case_with_error = case.copy()
                case_with_error.update({
                    "_row_processing_error": str(e),
                    "_processing_status": "error",
                    "requires_download": False,
                    "_skip_processing": True
                })
                processed_rows.append(case_with_error)
        
        self.log_info(f"Row processing completed: {len(processed_rows)} rows processed")
        return processed_rows

    async def process_single_row(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single case row through the complete pipeline.
        
        Args:
            case_details: Case details to process
            
        Returns:
            Processed case with all business rules applied
        """
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        if self.court_logger:
            self.court_logger.debug(f"[{court_id.upper()}][{docket_num}] Processing single row")
        
        try:
            # Start with a copy of the original case details
            processed_case = case_details.copy()
            
            # Phase 0: Defendant extraction (new phase)
            processed_case = await self._extract_and_validate_defendants(processed_case)
            
            # Phase 1: Apply relevance filtering
            processed_case = await self._apply_relevance_filtering(processed_case)
            
            # Phase 2: Apply case classification (if available)
            if self.classification_service:
                processed_case = await self._apply_case_classification(processed_case)
            
            # Phase 3: Apply business rules
            processed_case = await self.apply_business_rules(processed_case)
            
            # Phase 4: Determine download requirement
            download_required = await self.determine_download_requirement(processed_case)
            processed_case['requires_download'] = download_required
            
            # Phase 5: Apply verification rules
            processed_case = await self._apply_verification_rules(processed_case)
            
            # Add row processing metadata
            processed_case.update({
                "_row_processing_completed": True,
                "_row_processing_timestamp": self._get_timestamp(),
                "_processing_phase": "row_completed"
            })
            
            if self.court_logger:
                self.court_logger.debug(f"[{court_id.upper()}][{docket_num}] Single row processing completed")
            return processed_case
            
        except Exception as e:
            if self.court_logger:
                self.court_logger.error(f"[{court_id.upper()}][{docket_num}] Single row processing failed: {e}")
            
            # Return case with error metadata
            error_case = case_details.copy()
            error_case.update({
                "_row_processing_error": str(e),
                "_processing_status": "error",
                "requires_download": False,
                "_skip_processing": True
            })
            return error_case

    async def apply_business_rules(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply business rules to case data.
        
        Args:
            case_details: Case details to apply rules to
            
        Returns:
            Case details with business rules applied
        """
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        if self.court_logger:
            self.court_logger.debug(f"[{court_id.upper()}][{docket_num}] Applying business rules")
        
        try:
            enhanced_case = case_details.copy()
            
            # Business Rule 1: Set processing priority
            enhanced_case["processing_priority"] = self._determine_processing_priority(case_details)
            
            # Business Rule 2: Apply date-based rules
            enhanced_case = self._apply_date_based_rules(enhanced_case)
            
            # Business Rule 3: Apply case type specific rules
            enhanced_case = self._apply_case_type_rules(enhanced_case)
            
            # Business Rule 4: Apply court-specific rules
            enhanced_case = self._apply_court_specific_rules(enhanced_case)
            
            # Business Rule 5: Apply document count rules
            enhanced_case = self._apply_document_count_rules(enhanced_case)
            
            # Add business rules metadata
            enhanced_case.update({
                "_business_rules_applied": True,
                "_business_rules_timestamp": self._get_timestamp()
            })
            
            if self.court_logger:
                self.court_logger.debug(f"[{court_id.upper()}][{docket_num}] Business rules applied successfully")
            return enhanced_case
            
        except Exception as e:
            if self.court_logger:
                self.court_logger.error(f"[{court_id.upper()}][{docket_num}] Business rules application failed: {e}")
            return case_details

    async def determine_download_requirement(self, case_details: Dict[str, Any]) -> bool:
        """
        Determine if case requires document download based on business rules.
        
        Args:
            case_details: Case details to evaluate
            
        Returns:
            True if download is required, False otherwise
        """
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        if self.court_logger:
            self.court_logger.debug(f"[{court_id.upper()}][{docket_num}] Determining download requirement")
        
        try:
            # Check if processing should be skipped
            if case_details.get("_skip_processing", False):
                if self.court_logger:
                    self.court_logger.debug(f"[{court_id.upper()}][{docket_num}] Processing skipped, no download required")
                return False
            
            # Check if case is relevant
            if not case_details.get("is_relevant", True):
                if self.court_logger:
                    self.court_logger.debug(f"[{court_id.upper()}][{docket_num}] Case not relevant, no download required")
                return False
            
            # Check if explicitly requested
            if case_details.get("_is_explicitly_requested", False):
                if self.court_logger:
                    self.court_logger.debug(f"[{court_id.upper()}][{docket_num}] Case explicitly requested, download required")
                return True
            
            # Check business rule criteria
            download_criteria = [
                self._check_priority_download_criteria(case_details),
                self._check_case_type_download_criteria(case_details),
                self._check_date_download_criteria(case_details),
                self._check_document_download_criteria(case_details)
            ]
            
            # Download required if any criteria is met
            download_required = any(download_criteria)
            
            if self.court_logger:
                self.court_logger.debug(f"[{court_id.upper()}][{docket_num}] Download requirement: {download_required}")
            return download_required
            
        except Exception as e:
            if self.court_logger:
                self.court_logger.error(f"[{court_id.upper()}][{docket_num}] Download requirement determination failed: {e}")
            return False

    async def classify_case(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply case classification logic.
        
        Args:
            case_details: Case details to classify
            
        Returns:
            Case details with classification applied
        """
        if not self.classification_service:
            self.log_debug("Classification service not available, skipping classification")
            return case_details
        
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        if self.court_logger:
            self.court_logger.debug(f"[{court_id.upper()}][{docket_num}] Applying case classification")
        
        try:
            html_content = case_details.get("_html_content", "")
            
            classification_result = await self.classification_service.perform_action({
                "action": "classify_case",
                "case_details": case_details,
                "html_content": html_content
            })
            
            # Merge classification results
            classified_case = case_details.copy()
            classified_case.update(classification_result)
            
            # Add classification metadata
            classified_case.update({
                "_case_classified": True,
                "_classification_timestamp": self._get_timestamp()
            })
            
            if self.court_logger:
                self.court_logger.debug(f"[{court_id.upper()}][{docket_num}] Case classification completed")
            return classified_case
            
        except Exception as e:
            if self.court_logger:
                self.court_logger.error(f"[{court_id.upper()}][{docket_num}] Case classification failed: {e}")
            return case_details

    # Internal helper methods
    async def _apply_relevance_filtering(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Apply relevance filtering using RelevanceService."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        try:
            relevance_result = await self.relevance_service.perform_action({
                "action": "determine_case_relevance",
                "case_details": case_details
            })
            
            enhanced_case = case_details.copy()
            enhanced_case["is_relevant"] = relevance_result.get('is_relevant', False)
            enhanced_case["relevance_reason"] = relevance_result.get('relevance_reason', 'unknown')
            
            # Mark for skipping if not relevant
            if not enhanced_case["is_relevant"]:
                enhanced_case["_skip_processing"] = True
                enhanced_case["_skip_reason"] = "Not relevant"
            
            return enhanced_case
            
        except Exception as e:
            if self.court_logger:
                self.court_logger.error(f"[{court_id.upper()}][{docket_num}] Relevance filtering failed: {e}")
            return case_details

    async def _apply_case_classification(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Apply case classification using ClassificationService."""
        return await self.classify_case(case_details)

    async def _apply_verification_rules(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Apply verification rules using VerificationService."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        
        try:
            is_explicitly_requested = case_details.get("_is_explicitly_requested", False)
            
            verification_result = await self.verification_service.perform_action({
                "action": "verify_case",
                "case_details": case_details,
                "is_explicitly_requested": is_explicitly_requested
            })
            
            enhanced_case = case_details.copy()
            
            # Handle both bool and dict return types
            if isinstance(verification_result, bool):
                should_process = verification_result
                enhanced_case["_verification_result"] = {"should_process": should_process}
            else:
                should_process = verification_result.get('should_process', True)
                enhanced_case["_verification_result"] = verification_result
            
            # Update processing flag based on verification
            if not should_process:
                enhanced_case["_skip_processing"] = True
                enhanced_case["_skip_reason"] = "Failed verification"
                enhanced_case["requires_download"] = False
            
            return enhanced_case
            
        except Exception as e:
            if self.court_logger:
                self.court_logger.error(f"[{court_id.upper()}][{docket_num}] Verification rules failed: {e}")
            return case_details

    def _determine_processing_priority(self, case_details: Dict[str, Any]) -> str:
        """Determine processing priority based on case characteristics."""
        # High priority conditions
        if case_details.get("_is_explicitly_requested", False):
            return "high"
        
        case_type = case_details.get("case_type", "").lower()
        if "mdl" in case_type or "class action" in str(case_details.get("case_title", "")).lower():
            return "high"
        
        # Medium priority conditions
        filed_date = case_details.get("filed_date", "")
        if self._is_recent_case(filed_date):
            return "medium"
        
        # Default to normal priority
        return "normal"

    def _apply_date_based_rules(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Apply rules based on filing dates and other temporal factors."""
        enhanced_case = case_details.copy()
        
        filed_date = case_details.get("filed_date", "")
        if filed_date:
            enhanced_case["is_recent_case"] = self._is_recent_case(filed_date)
            enhanced_case["case_age_category"] = self._categorize_case_age(filed_date)
        
        return enhanced_case

    def _apply_case_type_rules(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Apply rules specific to different case types."""
        enhanced_case = case_details.copy()
        
        case_type = case_details.get("case_type", "").lower()
        docket_num = case_details.get("docket_num", "").lower()
        
        # Civil case rules
        if "civil" in case_type or "-cv-" in docket_num:
            enhanced_case["case_category"] = "civil"
            enhanced_case["requires_detailed_analysis"] = True
        
        # Criminal case rules
        elif "criminal" in case_type or "-cr-" in docket_num:
            enhanced_case["case_category"] = "criminal"
            enhanced_case["requires_detailed_analysis"] = False
        
        # Bankruptcy case rules
        elif "bankruptcy" in case_type or "-bk-" in docket_num:
            enhanced_case["case_category"] = "bankruptcy"
            enhanced_case["requires_detailed_analysis"] = True
        
        return enhanced_case

    def _apply_court_specific_rules(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Apply rules specific to different courts."""
        enhanced_case = case_details.copy()
        
        court_id = case_details.get("court_id", "").lower()
        
        # High-volume courts
        high_volume_courts = ["nysd", "cacd", "txnd", "flsd"]
        if court_id in high_volume_courts:
            enhanced_case["court_volume"] = "high"
            enhanced_case["processing_complexity"] = "standard"
        
        # Complex jurisdiction courts
        complex_courts = ["dcd", "cadc"]
        if court_id in complex_courts:
            enhanced_case["court_complexity"] = "high"
            enhanced_case["requires_detailed_analysis"] = True
        
        return enhanced_case

    def _apply_document_count_rules(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Apply rules based on document count and docket activity."""
        enhanced_case = case_details.copy()
        
        doc_count = case_details.get("document_count", 0)
        
        if doc_count > 100:
            enhanced_case["document_volume"] = "high"
            enhanced_case["processing_complexity"] = "complex"
        elif doc_count > 20:
            enhanced_case["document_volume"] = "medium"
        else:
            enhanced_case["document_volume"] = "low"
        
        return enhanced_case

    def _check_priority_download_criteria(self, case_details: Dict[str, Any]) -> bool:
        """Check if case meets priority download criteria."""
        priority = case_details.get("processing_priority", "normal")
        return priority in ["high", "urgent"]

    def _check_case_type_download_criteria(self, case_details: Dict[str, Any]) -> bool:
        """Check if case type requires download."""
        case_category = case_details.get("case_category", "")
        requires_detailed_analysis = case_details.get("requires_detailed_analysis", False)
        
        return case_category in ["civil", "bankruptcy"] and requires_detailed_analysis

    def _check_date_download_criteria(self, case_details: Dict[str, Any]) -> bool:
        """Check if case meets date-based download criteria."""
        return case_details.get("is_recent_case", False)

    def _check_document_download_criteria(self, case_details: Dict[str, Any]) -> bool:
        """Check if document characteristics require download."""
        doc_volume = case_details.get("document_volume", "low")
        complexity = case_details.get("processing_complexity", "standard")
        
        return doc_volume in ["medium", "high"] or complexity == "complex"

    def _is_recent_case(self, filed_date: str) -> bool:
        """Check if case is recent (within last 2 years)."""
        if not filed_date:
            return False
        
        try:
            from datetime import datetime, timedelta
            
            # Parse common date formats
            for date_format in ['%m/%d/%Y', '%m/%d/%y']:
                try:
                    case_date = datetime.strptime(filed_date, date_format)
                    cutoff_date = datetime.now() - timedelta(days=730)  # 2 years
                    return case_date > cutoff_date
                except ValueError:
                    continue
        except Exception:
            pass
        
        return False

    def _categorize_case_age(self, filed_date: str) -> str:
        """Categorize case by age."""
        if self._is_recent_case(filed_date):
            return "recent"
        
        # Could add more sophisticated age categorization
        return "older"

    def _get_timestamp(self) -> str:
        """Get current timestamp for processing metadata."""
        from datetime import datetime
        return datetime.now().isoformat()

    async def _extract_and_validate_defendants(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and validate defendants from case details with comprehensive logging."""
        court_id = case_details.get("court_id", "N/A")
        docket_num = case_details.get("docket_num", "N/A")
        log_prefix = f"[{court_id.upper()}][{docket_num}] DefendantExtraction:"
        
        enhanced_case = case_details.copy()
        
        try:
            # Step 1: Check if defendants list already exists and is valid
            defendants = case_details.get("defendants", [])
            if defendants and isinstance(defendants, list) and len(defendants) > 0:
                if self.court_logger:
                    self.court_logger.info(f"{log_prefix} Step 1 - Found existing defendants list with {len(defendants)} defendants")
                    for i, defendant in enumerate(defendants[:3]):  # Log first 3
                        defendant_name = defendant.get('name', str(defendant)) if isinstance(defendant, dict) else str(defendant)
                        self.court_logger.debug(f"{log_prefix} Defendant {i+1}: '{defendant_name}'")
                
                # Validate and clean existing defendants
                enhanced_case["defendants"] = self._validate_defendant_list(defendants, log_prefix)
                enhanced_case["_defendant_extraction_method"] = "existing_list"
                return enhanced_case
            
            # Step 2: Fall back to parsing versus field
            versus = case_details.get("versus", "")
            if versus and isinstance(versus, str) and versus.strip():
                if self.court_logger:
                    self.court_logger.info(f"{log_prefix} Step 2 - No defendants list, parsing versus field: '{versus}'")
                
                defendants_from_versus = self._extract_defendants_from_versus(versus, log_prefix)
                if defendants_from_versus:
                    enhanced_case["defendants"] = defendants_from_versus
                    enhanced_case["_defendant_extraction_method"] = "versus_parsing"
                    if self.court_logger:
                        self.court_logger.info(f"{log_prefix} Successfully extracted {len(defendants_from_versus)} defendants from versus field")
                    return enhanced_case
            
            # Step 3: Check title field as final fallback
            title = case_details.get("title", "")
            if title and isinstance(title, str) and title.strip():
                if self.court_logger:
                    self.court_logger.info(f"{log_prefix} Step 3 - Parsing title field: '{title}'")
                
                defendants_from_title = self._extract_defendants_from_versus(title, log_prefix)
                if defendants_from_title:
                    enhanced_case["defendants"] = defendants_from_title
                    enhanced_case["_defendant_extraction_method"] = "title_parsing"
                    if self.court_logger:
                        self.court_logger.info(f"{log_prefix} Successfully extracted {len(defendants_from_title)} defendants from title field")
                    return enhanced_case
            
            # Step 4: Handle missing defendant information
            if self.court_logger:
                self.court_logger.warning(f"{log_prefix} Step 4 - No defendants found in any field (defendants, versus, title)")
            
            enhanced_case["defendants"] = []
            enhanced_case["_defendant_extraction_method"] = "none_found"
            enhanced_case["_defendant_extraction_warning"] = "No defendants found in case data"
            
            return enhanced_case
            
        except Exception as e:
            if self.court_logger:
                self.court_logger.error(f"{log_prefix} Defendant extraction failed: {e}")
            
            enhanced_case["defendants"] = []
            enhanced_case["_defendant_extraction_method"] = "error"
            enhanced_case["_defendant_extraction_error"] = str(e)
            return enhanced_case
    
    def _validate_defendant_list(self, defendants: List, log_prefix: str) -> List[Dict[str, str]]:
        """Validate and clean an existing defendants list."""
        validated_defendants = []
        
        for i, defendant in enumerate(defendants):
            try:
                if isinstance(defendant, dict):
                    name = defendant.get('name', '').strip()
                    if name:
                        validated_defendants.append({"name": name})
                        if self.court_logger:
                            self.court_logger.debug(f"{log_prefix} Validated defendant {i+1}: '{name}'")
                elif isinstance(defendant, str) and defendant.strip():
                    validated_defendants.append({"name": defendant.strip()})
                    if self.court_logger:
                        self.court_logger.debug(f"{log_prefix} Validated defendant {i+1}: '{defendant.strip()}'")
                else:
                    if self.court_logger:
                        self.court_logger.warning(f"{log_prefix} Skipped invalid defendant {i+1}: {defendant}")
            except Exception as e:
                if self.court_logger:
                    self.court_logger.error(f"{log_prefix} Error validating defendant {i+1}: {e}")
        
        return validated_defendants
    
    def _extract_defendants_from_versus(self, versus: str, log_prefix: str) -> List[Dict[str, str]]:
        """Extract defendants from versus field with comprehensive logging and parsing."""
        if not versus:
            if self.court_logger:
                self.court_logger.debug(f"{log_prefix} No versus field provided")
            return []
        
        defendants = []
        
        # Look for various patterns of "v." or "vs."
        vs_patterns = [' v. ', ' V. ', ' vs. ', ' Vs. ', ' v ', ' V ']
        defendant_text = None
        matched_pattern = None
        
        if self.court_logger:
            self.court_logger.debug(f"{log_prefix} Parsing versus field: '{versus}'")
        
        for pattern in vs_patterns:
            if pattern in versus:
                parts = versus.split(pattern, 1)  # Split only on first occurrence
                if len(parts) > 1:
                    defendant_text = parts[1].strip()
                    matched_pattern = pattern
                    if self.court_logger:
                        self.court_logger.debug(f"{log_prefix} Found pattern '{pattern}', defendant part: '{defendant_text}'")
                    break
        
        if not defendant_text:
            if self.court_logger:
                self.court_logger.warning(f"{log_prefix} No valid versus pattern found in: '{versus}'")
            return []
        
        # Handle "et al" cases - remove et al suffix
        if " et al" in defendant_text.lower():
            original_text = defendant_text
            defendant_text = self._clean_et_al(defendant_text)
            if self.court_logger:
                self.court_logger.info(f"{log_prefix} Removed 'et al': '{original_text}' -> '{defendant_text}'")
        
        # Parse defendant names
        defendant_names = self._parse_defendant_names(defendant_text, log_prefix)
        
        # Convert to defendant objects
        for name in defendant_names:
            if name.strip():
                defendants.append({"name": name.strip()})
                if self.court_logger:
                    self.court_logger.debug(f"{log_prefix} Added defendant: '{name.strip()}'")
        
        if self.court_logger:
            self.court_logger.info(f"{log_prefix} Final result - {len(defendants)} defendants extracted")
        
        return defendants
    
    def _clean_et_al(self, text: str) -> str:
        """Remove 'et al' and variations from defendant text."""
        import re
        # Remove various forms of "et al"
        patterns = [
            r'\s*,?\s*et\s+al\.?.*$',  # ", et al." or " et al" at end
            r'\s*,?\s*and\s+others.*$',  # ", and others" at end
            r'\s*,?\s*etc\.?.*$'  # ", etc." at end
        ]
        
        cleaned = text
        for pattern in patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE).strip()
        
        return cleaned
    
    def _parse_defendant_names(self, defendant_text: str, log_prefix: str) -> List[str]:
        """Parse defendant names from text with proper name handling."""
        if not defendant_text:
            return []
        
        # Check if this looks like a single entity (contains Inc., LLC, Corp., etc.)
        entity_indicators = ['Inc.', 'LLC', 'Corp.', 'Ltd.', 'Co.', 'Company', 'Corporation', 'L.L.C.', 'L.P.']
        has_entity_indicator = any(indicator in defendant_text for indicator in entity_indicators)
        
        if has_entity_indicator:
            # Treat as single defendant to avoid splitting "Avlon Industries, Inc."
            if self.court_logger:
                self.court_logger.debug(f"{log_prefix} Single entity defendant detected: '{defendant_text}'")
            return [defendant_text]
        
        # Split by comma for multiple defendants, but be careful with company names
        parts = [part.strip() for part in defendant_text.split(',')]
        
        if len(parts) == 1:
            # Single defendant
            return [defendant_text.strip()]
        
        # Multiple parts - rejoin parts that might be part of a company name
        processed_names = []
        i = 0
        while i < len(parts):
            current_part = parts[i]
            
            # Check if next part looks like a company suffix (but only if it's short)
            if (i + 1 < len(parts) and 
                len(parts[i + 1].strip()) <= 10 and  # Only combine short suffixes
                parts[i + 1].strip().lower() in ['inc.', 'inc', 'llc', 'corp.', 'corp', 'ltd.', 'ltd', 'co.', 'co', 'l.l.c.', 'l.p.']):
                # Combine with next part
                combined = f"{current_part}, {parts[i + 1]}".strip()
                processed_names.append(combined)
                if self.court_logger:
                    self.court_logger.debug(f"{log_prefix} Combined company parts: '{current_part}' + '{parts[i + 1]}' = '{combined}'")
                i += 2  # Skip next part since we combined it
            else:
                if current_part:
                    processed_names.append(current_part)
                i += 1
        
        return processed_names

    async def get_processing_statistics(self) -> Dict[str, Any]:
        """Get statistics about row processing operations."""
        # This could be enhanced to track actual processing statistics
        return {
            "total_rows_processed": 0,
            "download_decisions": {
                "required": 0,
                "not_required": 0
            },
            "relevance_filtering": {
                "relevant": 0,
                "not_relevant": 0
            },
            "verification_results": {
                "passed": 0,
                "failed": 0
            }
        }