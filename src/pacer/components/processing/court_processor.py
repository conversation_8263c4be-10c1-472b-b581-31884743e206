"""
Court Processing Component for PACER Workflow.

This component handles court-level operations including authentication, 
configuration, setup, and docket discovery.
"""

import asyncio
import json
import os
from typing import Any, Dict, Optional, List
from playwright.async_api import Page, BrowserContext

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.services.browser_service import BrowserService
from src.pacer.services.configuration_service import ConfigurationService


class AuthenticationResult(dict):
    """
    A dict that supports boolean evaluation for backward compatibility.
    
    This class acts as a dictionary but also evaluates to True/False based on
    the 'success' key, maintaining backward compatibility with code that expects
    boolean results from authentication methods.
    """
    
    def __init__(self, success: bool, navigator: Any = None, court_id: str = None, error: str = None):
        super().__init__()
        self['success'] = success
        self['navigator'] = navigator
        if court_id:
            self['court_id'] = court_id
        if error:
            self['error'] = error
    
    def __bool__(self) -> bool:
        """Return success status for backward compatibility with boolean checks."""
        return self.get('success', False)
    
    def __eq__(self, other) -> bool:
        """Support comparison with boolean True/False for backward compatibility."""
        if isinstance(other, bool):
            return bool(self) == other
        return super().__eq__(other)


class CourtProcessor(AsyncServiceBase):
    """
    Handles court-level operations: authentication, configuration, setup, and discovery.
    
    Responsibilities:
    - Court authentication with PACER
    - Court-specific configuration and setup
    - Browser context initialization for court processing
    - Court-specific error handling and cleanup
    - Docket discovery within date ranges
    """

    def __init__(
        self,
        browser_service: Optional[BrowserService] = None,
        configuration_service: Optional[ConfigurationService] = None,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
    ):
        super().__init__(logger, config)
        self.browser_service = browser_service
        self.configuration_service = configuration_service

    async def _initialize_service(self) -> None:
        """Initialize the court processor and validate dependencies."""
        if self._initialized:
            return
        
        # Validate required dependencies
        if not self.browser_service:
            raise ValueError("BrowserService is required for CourtProcessor")
        
        self.log_info("CourtProcessor initialized successfully")

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate court processing methods."""
        action = data.get("action")
        
        if action == "process_court":
            return await self.process_court(
                court_id=data.get("court_id"),
                start_date=data.get("start_date"),
                end_date=data.get("end_date"),
                config=data.get("config", {}),
                browser_context=data.get("browser_context"),
                iso_date=data.get("iso_date"),
                share_context_with_next_phase=data.get("share_context_with_next_phase", True)
            )
        elif action == "authenticate_court":
            return await self.authenticate_court(
                court_id=data.get("court_id"),
                context_id=data.get("context_id")
            )
        elif action == "discover_dockets":
            return await self.discover_court_dockets(
                page=data.get("page"),
                court_id=data.get("court_id"),
                start_date=data.get("start_date"),
                end_date=data.get("end_date")
            )
        else:
            # Log the full data context to help debug where this is coming from
            import traceback
            self.log_error(f"Unknown action for CourtProcessor: {action}")
            self.log_error(f"Full data received: {data}")
            self.log_error(f"Stack trace: {traceback.format_stack()}")
            
            # If it's authenticate without _court, handle it
            if action == "authenticate":
                self.log_warning(f"Received 'authenticate' action, redirecting to 'authenticate_court'")
                return await self.authenticate_court(
                    court_id=data.get("court_id"),
                    context_id=data.get("context_id")
                )
            
            raise ValueError(f"Unknown action for CourtProcessor: {action}")

    async def process_court(
        self,
        court_id: str,
        start_date: str,
        end_date: str,
        config: Dict[str, Any],
        browser_context: Optional[str] = None,
        iso_date: Optional[str] = None,
        share_context_with_next_phase: bool = True
    ) -> Dict[str, Any]:
        """
        Process a single court: authenticate, configure, and discover dockets.
        
        Args:
            court_id: Court identifier (e.g., 'flsd', 'nysd')
            start_date: Start date for docket discovery
            end_date: End date for docket discovery
            config: Court processing configuration
            browser_context: Optional existing browser context
            iso_date: Optional ISO date for log file checking (e.g., '2024-08-09')
            share_context_with_next_phase: Whether to keep context alive for next phase (default: True)
            
        Returns:
            Dict containing court processing results including discovered dockets, navigator, and browser context
        """
        # Create court-specific logger if iso_date is provided
        court_logger = None
        if iso_date:
            court_logger = self.create_court_logger(court_id, iso_date)
            court_logger.info(f"[{court_id.upper()}] Starting court processing from {start_date} to {end_date}")
        
        self.log_info(f"Processing court: {court_id} from {start_date} to {end_date}")
        if court_logger:
            court_logger.info(f"[{court_id.upper()}] Processing court from {start_date} to {end_date}")
        
        result = {
            "court_id": court_id,
            "status": "failed",
            "authenticated": False,
            "dockets_discovered": 0,
            "dockets": [],
            "navigator": None,
            "context_id": None,
            "browser_context": None,
            "page": None,
            "page_id": None,
            "error": None
        }
        
        # Track whether we created the context internally
        created_context_internally = False
        
        try:
            # Step 1: Setup browser context for this court
            context_id = browser_context or f"court_{court_id}_processing"
            page_id = f"court_{court_id}_discovery_page"
            page = None
            
            if not browser_context:
                created_context_internally = True  # We're creating the context
                # CRITICAL FIX: Ensure browser service is initialized before use
                try:
                    if not self.browser_service._initialized:
                        self.log_info(f"Initializing browser service for court {court_id}")
                        if court_logger:
                            court_logger.info(f"[{court_id.upper()}] Initializing browser service")
                        await self.browser_service._initialize_service()
                    
                    # Create new context for this court
                    context_result = await self.browser_service.create_context({
                        "context_id": context_id,
                        "ignore_https_errors": True
                    })
                    result["context_created"] = bool(context_result)
                    result["context_id"] = context_id
                    
                    # Get the actual browser context object for sharing
                    if context_result:
                        actual_context = await self.browser_service.get_context({"context_id": context_id})
                        result["browser_context"] = actual_context
                    
                    self.log_info(f"Created browser context for court {court_id}")
                    if court_logger:
                        court_logger.info(f"[{court_id.upper()}] Created browser context successfully")
                except Exception as e:
                    result["error"] = f"Failed to create browser context: {e}"
                    self.log_error(f"Browser service initialization failed for court {court_id}: {e}")
                    if court_logger:
                        court_logger.error(f"[{court_id.upper()}] Browser service initialization failed: {e}")
                    return result
            
            # Step 2: Create page for court processing
            try:
                await self.browser_service.create_page({
                    "context_id": context_id,
                    "page_id": page_id
                })
                page = await self.browser_service.get_page({"page_id": page_id})
                
                if not page:
                    raise ValueError("Failed to create page for court processing")
                
                # Store page info in result for potential sharing with Phase 2
                result["page"] = page
                result["page_id"] = page_id
                    
            except Exception as e:
                result["error"] = f"Failed to create page: {e}"
                return result
            
            # Step 3: Authenticate with PACER for this court and get navigator
            # Ensure browser service is fully initialized before authentication
            if not self.browser_service._initialized:
                await self.browser_service._initialize_service()
            
            auth_result = await self.authenticate_court(court_id, context_id)
            
            if isinstance(auth_result, dict) and auth_result.get("success"):
                result["authenticated"] = True
                result["navigator"] = auth_result.get("navigator")
                self.log_info(f"Authentication successful for court {court_id}, navigator ready")
                # Court logger already logs this above at line 225
                if court_logger:
                    court_logger.info(f"[{court_id.upper()}] PACER authentication successful, navigator ready for operations")
            else:
                result["authenticated"] = False
                result["error"] = f"Authentication failed for court {court_id}"
                if court_logger:
                    court_logger.error(f"[{court_id.upper()}] PACER authentication failed: {result['error']}")
                return result
            
            # Step 4: Get court-specific configuration
            court_config = await self.get_court_configuration(court_id)
            result["court_config"] = court_config
            
            # Step 4.5: Check for existing docket_report_log files before generating new reports
            discovered_dockets = []
            if iso_date:
                log_file_path = self._get_docket_log_path(court_id, iso_date)
                self.log_info(f"Checking for existing docket log at: {log_file_path}")
                if court_logger:
                    court_logger.info(f"[{court_id.upper()}] Checking for existing docket log")
                
                if await self._check_log_exists(log_file_path):
                    # PATH A: Resume from existing log
                    self.log_info(f"Found existing docket_report_log - RESUMING from previous session for court {court_id}")
                    if court_logger:
                        court_logger.info(f"[{court_id.upper()}] Found existing docket report - RESUMING from previous session")
                    docket_log_data = await self._load_docket_log(log_file_path)
                    
                    if docket_log_data and docket_log_data.get('cases'):
                        # Convert existing log cases to discovered_dockets format
                        for case in docket_log_data.get('cases', []):
                            discovered_dockets.append({
                                "docket_num": case.get('docket_num', ''),
                                "versus": case.get('versus', ''),  # docket_report_log uses 'versus' not 'case_title'
                                "filing_date": case.get('filing_date', ''),  # docket_report_log uses 'filing_date' not 'filed_date'
                                "docket_link": case.get('docket_link', ''),  # docket_report_log uses 'docket_link' not 'docket_url'
                                "court_id": court_id
                            })
                        self.log_info(f"PATH A: Loaded {len(discovered_dockets)} cases from existing log for court {court_id}")
                        if court_logger:
                            court_logger.info(f"[{court_id.upper()}] PATH A: Loaded {len(discovered_dockets)} cases from existing log")
                    else:
                        self.log_warning(f"Log file exists but is empty or invalid for court {court_id}, will generate new report")
                        if court_logger:
                            court_logger.warning(f"[{court_id.upper()}] Log file exists but is empty or invalid, will generate new report")
                        # Fall through to PATH B
                else:
                    self.log_info(f"No existing docket_report_log found for court {court_id}")
                    if court_logger:
                        court_logger.info(f"[{court_id.upper()}] No existing docket report found")
            
            # Step 5: Discover available dockets in the date range (PATH B: only if no existing log)
            if not discovered_dockets:
                # PATH B: Start new - Generate civil cases report
                self.log_info(f"PATH B: STARTING NEW session - generating docket report for court {court_id}")
                if court_logger:
                    court_logger.info(f"[{court_id.upper()}] PATH B: STARTING NEW session - generating docket report")
                navigator = result.get("navigator")
                if navigator:
                    discovered_dockets = await self.discover_court_dockets(
                        navigator.page, court_id, start_date, end_date
                    )
                else:
                    self.log_error(f"No navigator available for docket discovery in court {court_id}")
                    if court_logger:
                        court_logger.error(f"[{court_id.upper()}] No navigator available for docket discovery")
                    result["error"] = "Navigator not available for docket discovery"
                    return result
            else:
                # PATH A was successful - we loaded cases from docket report log
                self.log_info(f"PATH A: RESUMING from existing log - loaded {len(discovered_dockets)} cases, SKIPPING report generation")
                self.log_info(f"PATH A: NOT navigating to Civil Cases Report page - using existing data")
                if court_logger:
                    court_logger.info(f"[{court_id.upper()}] PATH A: Using {len(discovered_dockets)} cases from existing log - NO NEW REPORT NEEDED")
                    court_logger.info(f"[{court_id.upper()}] PATH A: Skipping Civil Cases Report navigation entirely")
                
                # Navigate to Query page instead since we already have the dockets
                navigator = result.get("navigator")
                if navigator:
                    self.log_info(f"PATH A: Navigating to Query page to process existing dockets")
                    from src.pacer.facades.navigation_facade import NavigationFacade
                    nav_facade = NavigationFacade(logger=self.logger)
                    query_success = await nav_facade.navigate_to_query_page(navigator, court_id)
                    if not query_success:
                        self.log_warning(f"PATH A: Could not navigate to Query page, but continuing with existing dockets")
                    else:
                        self.log_info(f"PATH A: Successfully navigated to Query page")
            
            result["dockets"] = discovered_dockets
            result["dockets_discovered"] = len(discovered_dockets)
            result["status"] = "success"
            
            # Confirm navigator is available in result for Phase 2
            if result.get("navigator"):
                self.log_info(f"Court {court_id} processing complete: {len(discovered_dockets)} dockets discovered, navigator ready for Phase 2")
                # Court logger already logs this above at lines 287-292
                if court_logger:
                    court_logger.info(f"[{court_id.upper()}] Court processing completed successfully")
                    court_logger.info(f"[{court_id.upper()}] - Discovered {len(discovered_dockets)} dockets")
                    court_logger.info(f"[{court_id.upper()}] - Navigator ready for Phase 2 operations")
                    if iso_date:
                        court_logger.info(f"[{court_id.upper()}] - Log file saved to: data/{iso_date}/logs/pacer/{court_id.lower()}.log")
            else:
                self.log_warning(f"Court {court_id} processing complete but navigator not available for Phase 2")
                # Court logger already logs this above at line 295
                if court_logger:
                    court_logger.warning(f"[{court_id.upper()}] Court processing complete but navigator not available for Phase 2")
            
            return result
            
        except Exception as e:
            self.log_error(f"Error processing court {court_id}: {e}")
            result["error"] = str(e)
            if court_logger:
                court_logger.error(f"[{court_id.upper()}] Court processing failed with error: {e}")
            return result
            
        finally:
            # Cleanup browser resources with conditional cleanup for Phase 2 sharing
            try:
                # CRITICAL FIX: Only close page if we're NOT sharing context with next phase
                if page and page_id:
                    if not share_context_with_next_phase:
                        self.log_info(f"Closing page for court {court_id} (not shared)")
                        await self.browser_service.close_page({"page_id": page_id})
                    else:
                        self.log_info(f"Keeping page alive for court {court_id} (shared with Phase 2)")
                        if court_logger:
                            court_logger.info(f"[{court_id.upper()}] Page preserved for Phase 2 processing")
                
                # ARCHITECTURAL FIX: Only close context if we created it AND we're not sharing it with next phase
                if created_context_internally and context_id:
                    if not share_context_with_next_phase:
                        self.log_info(f"Closing browser context for court {court_id} (not shared)")
                        await self.browser_service.close_context({"context_id": context_id})
                    else:
                        self.log_info(f"Keeping browser context alive for court {court_id} (shared with Phase 2)")
                        if court_logger:
                            court_logger.info(f"[{court_id.upper()}] Browser context preserved for Phase 2 processing")
            except Exception as e:
                self.log_warning(f"Error cleaning up browser resources: {e}")

    async def authenticate_court(self, court_id: str, context_id: str = None) -> Dict[str, Any]:
        """
        Handle court-specific authentication with PACER.
        
        Args:
            court_id: Court identifier
            context_id: Browser context identifier (optional)
            
        Returns:
            Dict with 'success' boolean and 'navigator' if successful.
            For backward compatibility, this can also be accessed as a boolean.
        """
        self.log_info(f"Authenticating with PACER for court {court_id}")
        
        # Create court-specific logger for authentication (use current date if no specific date provided)
        from datetime import datetime
        iso_date = datetime.now().strftime("%Y%m%d")
        court_logger = self.create_court_logger(court_id, iso_date)
        court_logger.info(f"[{court_id.upper()}] Starting PACER authentication process")
        
        try:
            # Ensure browser service is initialized
            if not self.browser_service._initialized:
                await self.browser_service._initialize_service()
            
            # Use provided context_id or create a new one
            if not context_id:
                context_id = f"court_{court_id}_processing"
                # Create browser context if it doesn't exist
                try:
                    await self.browser_service.create_context({"context_id": context_id})
                    self.log_info(f"Created new browser context for court {court_id}")
                except Exception as e:
                    self.log_debug(f"Context might already exist: {e}")
            
            # Get or create the page for this court
            page_id = f"court_{court_id}_discovery_page"
            page = await self.browser_service.get_page({"page_id": page_id})
            
            # If page doesn't exist, create it
            if not page:
                self.log_info(f"Creating page for court {court_id}")
                await self.browser_service.create_page({
                    "context_id": context_id,
                    "page_id": page_id
                })
                page = await self.browser_service.get_page({"page_id": page_id})
            
            if not page:
                self.log_error(f"Failed to create or get page for court {court_id}")
                result = AuthenticationResult(
                    success=False,
                    navigator=None,
                    court_id=court_id,
                    error="Page not found"
                )
                return result
            
            # Create navigator for the existing page
            from src.pacer.components.browser.navigator import PacerNavigator
            from src.pacer.components.authentication.ecf_login_handler import ECFLoginHandler
            
            navigator = PacerNavigator(
                page=page,
                config=self.config,
                screenshot_dir=self.config.get('screenshot_dir', '/tmp/pacer_screenshots'),
                timeout_ms=self.config.get('auth_timeout', 30000),
                logger=self.logger
            )
            
            # Perform login using the LoginHandler for main PACER login
            from src.pacer.components.authentication.login_handler import LoginHandler
            login_handler = LoginHandler(self.logger, self.config)
            self.log_info(f"Performing PACER login on existing page for court {court_id}")
            court_logger.info(f"[{court_id.upper()}] Performing PACER login using login handler")
            login_success = await login_handler.perform_main_pacer_login(navigator, court_id)
            
            if login_success:
                self.log_info(f"Successfully authenticated with PACER for court {court_id}")
                court_logger.info(f"[{court_id.upper()}] PACER authentication completed successfully")
                court_logger.info(f"[{court_id.upper()}] Navigator created and ready for court operations")
                
                # Create the result dict with backward compatibility
                result = AuthenticationResult(
                    success=True,
                    navigator=navigator,
                    court_id=court_id
                )
                return result
            else:
                self.log_error(f"PACER authentication failed for court {court_id}")
                court_logger.error(f"[{court_id.upper()}] PACER authentication failed - login unsuccessful")
                result = AuthenticationResult(
                    success=False,
                    navigator=None,
                    court_id=court_id,
                    error="Login failed"
                )
                return result
                
        except Exception as e:
            self.log_error(f"PACER authentication error for court {court_id}: {e}")
            court_logger.error(f"[{court_id.upper()}] PACER authentication error: {e}")
            result = AuthenticationResult(
                success=False,
                navigator=None,
                court_id=court_id,
                error=str(e)
            )
            return result

    async def get_court_configuration(self, court_id: str) -> Dict[str, Any]:
        """
        Get court-specific configuration and settings.
        
        Args:
            court_id: Court identifier
            
        Returns:
            Court configuration dictionary
        """
        court_config = {
            "court_id": court_id,
            "base_url": f"https://ecf.{court_id}.uscourts.gov/",
            "civil_reports_url": self._get_civil_reports_url(court_id),
            "docket_report_url": f"https://ecf.{court_id}.uscourts.gov/cgi-bin/DktRpt.pl"
        }
        
        # Add court-specific settings if available
        if self.configuration_service:
            try:
                custom_config = await self.configuration_service.get_court_config(court_id)
                court_config.update(custom_config)
            except Exception as e:
                self.log_warning(f"Could not load custom config for court {court_id}: {e}")
        
        return court_config

    async def discover_court_dockets(
        self, 
        page: Page, 
        court_id: str, 
        start_date: str, 
        end_date: str
    ) -> List[Dict[str, Any]]:
        """
        Discover available dockets for a court within the specified date range.
        
        Args:
            page: Authenticated browser page
            court_id: Court identifier  
            start_date: Start date for discovery (MM/DD/YY format)
            end_date: End date for discovery (MM/DD/YY format)
            
        Returns:
            List of discovered docket metadata
        """
        self.log_info(f"Discovering dockets for court {court_id} from {start_date} to {end_date}")
        
        discovered_dockets = []
        
        try:
            # Navigate to civil cases filed report page
            civil_reports_url = self._get_civil_reports_url(court_id)
            
            self.log_info(f"Navigating to civil cases reports: {civil_reports_url}")
            response = await page.goto(civil_reports_url, wait_until="networkidle", timeout=30000)
            
            # Wait for any redirects to complete
            await page.wait_for_load_state("domcontentloaded", timeout=5000)
            await asyncio.sleep(3)
            
            # Get the ACTUAL current URL after any redirects
            actual_url = page.url
            self.log_info(f"ACTUAL URL after navigation and redirects: {actual_url}")
            
            # CRITICAL CHECK: Are we at the PACER login page instead of ECF?
            if "pacer.login.uscourts.gov/csologin/login.jsf" in actual_url:
                self.log_info("*** REDIRECTED TO PACER LOGIN - THIS IS THE CLIENT CODE PAGE ***")
                is_client_code_page = True
            else:
                # Also check page content for client code indicators
                page_content = await page.content()
                
                if "Logged in as" in page_content:
                    self.log_info("*** DETECTED 'Logged in as' - THIS IS THE CLIENT CODE PAGE ***")
                    is_client_code_page = True
                elif ("Client Code" in page_content or 
                      "clientCode" in page_content or 
                      "logoutForm:clientCode" in page_content):
                    self.log_info("*** DETECTED Client Code form elements - THIS IS THE CLIENT CODE PAGE ***")
                    is_client_code_page = True
                else:
                    is_client_code_page = False
            
            if is_client_code_page:
                self.log_info("*** HANDLING CLIENT CODE PAGE - WILL FILL FORM AND SUBMIT ***")
                
                # STEP 1: Fill the client code field
                self.log_info("STEP 1: Filling client code...")
                client_code_filled = False
                
                # Try the most specific selector first
                try:
                    # Use JavaScript to directly set the value - most reliable method
                    await page.evaluate("""
                        var input = document.getElementById('logoutForm:clientCode');
                        if (input) {
                            input.value = '007';
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            input.dispatchEvent(new Event('change', { bubbles: true }));
                        }
                    """)
                    self.log_info("✅ STEP 1 COMPLETE: Client code filled using JavaScript")
                    client_code_filled = True
                except Exception as e:
                    self.log_warning(f"JavaScript fill failed: {e}")
                    
                    # Fallback to Playwright selectors
                    client_input_selectors = [
                        "input#logoutForm\\:clientCode",
                        "input[name='logoutForm:clientCode']",
                        "input[id='logoutForm:clientCode']"
                    ]
                    
                    if not client_code_filled:
                        for selector in client_input_selectors:
                            try:
                                await page.fill(selector, "007")
                                self.log_info(f"✅ STEP 1 COMPLETE: Client code filled using {selector}")
                                client_code_filled = True
                                break
                            except:
                                continue
                    
                if not client_code_filled:
                    self.log_error("❌ STEP 1 FAILED: Could not fill client code")
                    return discovered_dockets
                
                await asyncio.sleep(1)
                
                # STEP 2: Select court from dropdown
                self.log_info("STEP 2: Selecting court from dropdown...")
                court_dropdown_filled = False
                
                # Map court IDs to dropdown values
                court_value_map = {
                    "ilnd": "N_ILNDC",
                    "ilcd": "N_ILCDC", 
                    "ilsd": "N_ILSDC",
                    "cand": "N_CANDC",
                    "cacd": "N_CACDC",
                    "nysd": "N_NYSDC",
                }
                court_value = court_value_map.get(court_id.lower(), f"N_{court_id.upper()}C")
                
                # Try JavaScript first - most reliable
                try:
                    await page.evaluate(f"""
                        var select = document.getElementById('logoutForm:courtId_input');
                        if (select) {{
                            select.value = '{court_value}';
                            select.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        }}
                    """)
                    self.log_info(f"✅ STEP 2 COMPLETE: Selected court {court_value} using JavaScript")
                    court_dropdown_filled = True
                except Exception as e:
                    self.log_warning(f"JavaScript select failed: {e}")
                    
                if not court_dropdown_filled:
                    # Fallback to Playwright select
                    try:
                        await page.select_option("select#logoutForm\\:courtId_input", value=court_value)
                        self.log_info(f"✅ STEP 2 COMPLETE: Selected court using Playwright")
                        court_dropdown_filled = True
                    except:
                        self.log_warning("❌ STEP 2 WARNING: Could not select court, will try to proceed")
                    
                await asyncio.sleep(1)
                
                # STEP 3: Click submit button - CRITICAL!
                self.log_info("STEP 3: Clicking submit button...")
                submit_clicked = False
                
                # Try JavaScript click first - most reliable
                try:
                    await page.evaluate("""
                        var button = document.getElementById('logoutForm:btnChangeClientCode');
                        if (button) {
                            button.click();
                        }
                    """)
                    self.log_info("✅ STEP 3 COMPLETE: Clicked submit button using JavaScript")
                    submit_clicked = True
                except Exception as e:
                    self.log_warning(f"JavaScript click failed: {e}")
                
                if not submit_clicked:
                    # Try Playwright click
                    submit_selectors = [
                        "button#logoutForm\\:btnChangeClientCode",
                        "button[id='logoutForm:btnChangeClientCode']",
                        "button:has-text('Submit')"
                    ]
                    
                    for selector in submit_selectors:
                        try:
                            await page.click(selector)
                            self.log_info(f"✅ STEP 3 COMPLETE: Clicked submit using {selector}")
                            submit_clicked = True
                            break
                        except:
                            continue
                    
                if not submit_clicked:
                    # Last resort - press Enter
                    try:
                        await page.keyboard.press("Enter")
                        self.log_info("✅ STEP 3 COMPLETE: Submitted using Enter key")
                        submit_clicked = True
                    except:
                        self.log_error("❌ STEP 3 FAILED: Could not submit form")
                        return discovered_dockets
                
                # Wait for navigation after submit
                self.log_info("Waiting for page to load after submit...")
                await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)
                
                new_url = page.url
                self.log_info(f"*** CLIENT CODE FORM SUBMITTED - Now at: {new_url} ***")
                
                # Verify we're at ECF
                if f"ecf.{court_id.lower()}.uscourts.gov" in new_url:
                    self.log_info("✅ SUCCESS: Now at ECF system, continuing with report generation...")
                else:
                    self.log_warning(f"⚠️ Not at expected ECF URL, but continuing anyway...")
            
            # Continue with original response check
            if not response or not response.ok:
                self.log_warning(f"Navigation to civil reports failed for court {court_id}")
                # Try alternative URLs
                alternative_urls = self._get_alternative_report_urls(court_id)
                
                for alt_url in alternative_urls:
                    try:
                        response = await page.goto(alt_url, wait_until="networkidle", timeout=30000)
                        if response and response.ok:
                            self.log_info(f"Successfully reached alternative reports page: {alt_url}")
                            break
                    except:
                        continue
                else:
                    self.log_error(f"All civil reports URLs failed for court {court_id}")
                    return discovered_dockets
            
            # Fill date range fields and submit form
            await self._fill_date_range_form(page, start_date, end_date, court_id)
            
            # Parse results from the response page
            discovered_dockets = await self._parse_docket_results(page, court_id)
            
        except Exception as e:
            self.log_error(f"Error during docket discovery for court {court_id}: {e}")
            
        self.log_info(f"Discovered {len(discovered_dockets)} dockets for court {court_id}")
        return discovered_dockets

    def _get_docket_log_path(self, court_id: str, iso_date: str) -> str:
        """Get the path to the docket report log file for a specific court and date."""
        # Ensure the directory exists
        log_dir = f"data/{iso_date}/logs/docket_report"
        os.makedirs(log_dir, exist_ok=True)
        # Return path with correct naming convention - should be .json not .log
        return f"{log_dir}/{court_id.lower()}.json"
    
    async def _check_log_exists(self, path: str) -> bool:
        """Check if a docket report log file exists."""
        return os.path.exists(path)
    
    async def _load_docket_log(self, path: str) -> Dict[str, Any]:
        """Load existing docket report log from file."""
        try:
            with open(path, 'r') as f:
                data = json.load(f)
                self.log_info(f"Loaded docket log with {len(data.get('cases', []))} cases")
                return data
        except Exception as e:
            self.log_error(f"Failed to load docket log: {e}")
            return None

    def _get_civil_reports_url(self, court_id: str) -> str:
        """Get the civil cases filed report URL for a court."""
        # Georgia courts have different URL pattern
        if court_id.startswith('ga'):
            return f"https://ecf.{court_id}.uscourts.gov/cgi-bin/{court_id.upper()}c_CaseFiled-Rpt.pl"
        else:
            return f"https://ecf.{court_id}.uscourts.gov/cgi-bin/CaseFiled-Rpt.pl"

    def _get_alternative_report_urls(self, court_id: str) -> List[str]:
        """Get alternative civil report URLs for fallback."""
        return [
            f"https://ecf.{court_id}.uscourts.gov/cgi-bin/rss_outside.pl",
            f"https://ecf.{court_id}.uscourts.gov/cgi-bin/NewCasesRpt.pl",
            f"https://ecf.{court_id}.uscourts.gov/cgi-bin/CivCasesSummary.pl",
            f"https://ecf.{court_id}.uscourts.gov/cgi-bin/iquery.pl"
        ]

    async def _fill_date_range_form(
        self, 
        page: Page, 
        start_date: str, 
        end_date: str, 
        court_id: str
    ) -> None:
        """Fill date range form fields and submit."""
        # Try different selectors for date fields
        start_date_selectors = [
            'input[name="date_from"]',
            'input[name="start_date"]',
            'input[name="from_date"]',
            'input[id*="date_from"]',
            'input[id*="start"]'
        ]
        
        end_date_selectors = [
            'input[name="date_to"]',
            'input[name="end_date"]', 
            'input[name="to_date"]',
            'input[id*="date_to"]',
            'input[id*="end"]'
        ]
        
        # Fill start date
        for selector in start_date_selectors:
            try:
                element = await page.wait_for_selector(selector, timeout=2000)
                if element:
                    await element.fill(start_date)
                    self.log_info(f"Filled start date field: {selector}")
                    break
            except:
                continue
        
        # Fill end date
        for selector in end_date_selectors:
            try:
                element = await page.wait_for_selector(selector, timeout=2000)
                if element:
                    await element.fill(end_date)
                    self.log_info(f"Filled end date field: {selector}")
                    break
            except:
                continue
        
        # Submit form
        submit_selectors = [
            'input[type="submit"]',
            'button[type="submit"]',
            'input[value*="Submit"]',
            'input[value*="Search"]',
            'input[value*="Run"]',
            'input[value*="Report"]',
            'input[value*="Generate"]'
        ]
        
        for selector in submit_selectors:
            try:
                element = await page.wait_for_selector(selector, timeout=2000)
                if element:
                    await element.click()
                    self.log_info(f"Clicked submit button: {selector}")
                    await page.wait_for_load_state("networkidle")
                    return
            except:
                continue
        
        self.log_warning(f"Could not find submit button for court {court_id}")

    async def _parse_docket_results(self, page: Page, court_id: str) -> List[Dict[str, Any]]:
        """Parse docket results from the PACER search results page."""
        dockets = []
        
        try:
            # Look for result tables
            table_selectors = [
                'table[summary*="case"]',
                'table[summary*="docket"]', 
                'table.results',
                'table[border="1"]',
                'table:has(tr:has(td:has(a[href*="DktRpt"])))'
            ]
            
            results_table = None
            for selector in table_selectors:
                try:
                    table = await page.wait_for_selector(selector, timeout=3000)
                    if table:
                        results_table = table
                        break
                except:
                    continue
            
            if not results_table:
                # Fallback to text parsing
                return await self._parse_docket_results_from_text(page, court_id)
            
            # Extract docket rows from table
            rows = await page.query_selector_all('table tr')
            
            for row in rows:
                try:
                    links = await row.query_selector_all('a[href*="DktRpt"]')
                    
                    if links:
                        for link in links:
                            link_text = await link.inner_text()
                            href = await link.get_attribute('href')
                            
                            docket_num = self._extract_docket_number(link_text, href)
                            
                            if docket_num:
                                row_text = await row.inner_text()
                                case_title = self._extract_case_title(row_text, link_text)
                                filed_date = self._extract_filed_date(row_text)
                                
                                docket_metadata = {
                                    "docket_num": docket_num,
                                    "case_title": case_title,
                                    "filed_date": filed_date,
                                    "docket_url": href,
                                    "court_id": court_id
                                }
                                
                                dockets.append(docket_metadata)
                                
                except Exception as e:
                    self.log_debug(f"Error parsing row for court {court_id}: {e}")
                    continue
            
        except Exception as e:
            self.log_error(f"Error parsing docket results for court {court_id}: {e}")
        
        return dockets

    async def _parse_docket_results_from_text(self, page: Page, court_id: str) -> List[Dict[str, Any]]:
        """Fallback method to parse docket information from page text."""
        dockets = []
        
        try:
            page_text = await page.inner_text('body')
            
            import re
            from src.utils.docket_utils import extract_docket_digits
            
            docket_patterns = [
                r'\d+:\d{2}-cv-\d+(?:-\w+)?',
                r'\d+:\d{2}-cr-\d+(?:-\w+)?',
                r'\d+:\d{2}-bk-\d+(?:-\w+)?',
                r'\d+:\d{2}-md-\d+(?:-\w+)?',
                r'\d+:\d{2}-[a-zA-Z]{2}-\d+(?:-\w+)?'
            ]
            
            for pattern in docket_patterns:
                matches = re.finditer(pattern, page_text, re.IGNORECASE)
                
                for match in matches:
                    raw_docket = match.group()
                    docket_num = extract_docket_digits(raw_docket)
                    
                    # Extract context for case title
                    start = max(0, match.start() - 100)
                    end = min(len(page_text), match.end() + 100)
                    context = page_text[start:end]
                    
                    case_title = self._extract_case_title_from_context(context, docket_num)
                    
                    docket_metadata = {
                        "docket_num": docket_num,
                        "case_title": case_title,
                        "filed_date": "",
                        "docket_url": "",
                        "court_id": court_id
                    }
                    
                    dockets.append(docket_metadata)
            
        except Exception as e:
            self.log_error(f"Error in text-based docket parsing for court {court_id}: {e}")
        
        return dockets

    def _extract_docket_number(self, link_text: str, href: str) -> str:
        """Extract docket number from link text or URL."""
        import re
        from src.utils.docket_utils import extract_docket_digits
        
        docket_patterns = [
            r'\d+:\d{2}-cv-\d+(?:-\w+)?',
            r'\d+:\d{2}-cr-\d+(?:-\w+)?',
            r'\d+:\d{2}-bk-\d+(?:-\w+)?',
            r'\d+:\d{2}-md-\d+(?:-\w+)?',
            r'\d+:\d{2}-[a-zA-Z]{2}-\d+(?:-\w+)?'
        ]
        
        # Search link text first
        for pattern in docket_patterns:
            match = re.search(pattern, link_text, re.IGNORECASE)
            if match:
                return extract_docket_digits(match.group())
        
        # Try href as fallback
        for pattern in docket_patterns:
            match = re.search(pattern, href, re.IGNORECASE)
            if match:
                return extract_docket_digits(match.group())
        
        return ""

    def _extract_case_title(self, row_text: str, link_text: str) -> str:
        """Extract case title from row text."""
        import re
        
        cleaned_text = row_text.replace(link_text, "").strip()
        
        # Remove common PACER artifacts
        artifacts_to_remove = [
            r'\d+:\d{2}-\w{2}-\d+',
            r'https?://[^\s]+',
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'Judge:?\s*\w+',
            r'Filed:?\s*\d+/\d+/\d+'
        ]
        
        for pattern in artifacts_to_remove:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.IGNORECASE)
        
        parts = [part.strip() for part in cleaned_text.split() if len(part.strip()) > 2]
        case_title = ' '.join(parts[:10])
        
        return case_title.strip()

    def _extract_filed_date(self, row_text: str) -> str:
        """Extract filed date from row text."""
        import re
        
        date_patterns = [
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'\d{4}-\d{2}-\d{2}',
            r'\w{3}\s+\d{1,2},?\s+\d{4}'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, row_text)
            if match:
                return match.group()
        
        return ""

    def _extract_case_title_from_context(self, context: str, docket_num: str) -> str:
        """Extract case title from surrounding context."""
        parts = context.split(docket_num)
        if len(parts) > 1:
            after_docket = parts[1].strip()
            case_title = after_docket.split('\n')[0][:100].strip()
            return case_title
        
        return ""