# /src/services/pacer/_verification_components/case_verifier.py
from __future__ import annotations
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.utils.docket_utils import normalize_docket_number

if TYPE_CHECKING:
    from src.repositories.pacer_repository import PacerRepository
    from src.pacer.components.file_operations.file_manager import FileManager
    from src.infrastructure.protocols.logger import LoggerProtocol

class CaseVerifier(ComponentImplementation):
    """Verifies if a case should be processed."""

    def __init__(self,
                 repository: PacerRepository,
                 file_manager: FileManager,
                 logger: LoggerProtocol = None,
                 court_logger: LoggerProtocol = None,
                 config: Dict[str, Any] = None):
        super().__init__(logger, config)
        self._repo = repository
        self._file_manager = file_manager
        self._court_logger = court_logger or logger

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        case_details = data.get('case_details', {})
        
        if self._court_logger:
            court_id = case_details.get('court_id', 'unknown')
            self._court_logger.info(f"Executing CaseVerifier action: {action} for court: {court_id}")
        
        if action == 'verify_case':
            return await self.verify_case(case_details)
        elif action == 'check_database':
            return await self.check_database_gsi(case_details)
        elif action == 'comprehensive_verification':
            return await self.comprehensive_verification(case_details)
        elif action == 'check_local_artifacts':
            return await self.check_local_artifacts_pdf_zip(case_details)
        elif action == 'check_all_files':
            return await self.check_all_local_files(case_details)
        else:
            error_msg = f"Unknown action for CaseVerifier: {action}"
            if self._court_logger:
                self._court_logger.error(error_msg)
            raise PacerServiceError(error_msg)

    async def verify_case(self, case_details: Dict[str, Any]) -> bool:
        """
        Verifies if a case should be processed by checking for its existence.
        Returns True if the case should be processed, False otherwise.
        """
        court_id = case_details.get('court_id')
        docket_num = case_details.get('docket_num')

        if not court_id or not docket_num:
            if self._court_logger:
                self._court_logger.warning("Missing court_id or docket_num for verification.")
            else:
                self.log_warning("Missing court_id or docket_num for verification.")
            return False

        # 1. Check database
        if self._court_logger:
            self._court_logger.info(f"Starting database verification for case {court_id}:{docket_num}")
        
        db_docket_num = normalize_docket_number(docket_num)
        db_exists = await self._repo.check_docket_exists(court_id, db_docket_num)
        if db_exists:
            self.log_verification_result(case_details, 'DATABASE_CHECK', False, 'Case already exists in database')
            return False

        # 2. Check local files (simplified for this example)
        # In a real implementation, this would be more robust.
        # For now, we assume if it's not in the DB, it needs processing.

        self.log_verification_result(case_details, 'CASE_VERIFICATION', True, 'Case does not exist, needs processing')
        return True

    async def check_database_gsi(self, case_details: Dict[str, Any]) -> bool:
        """Check if case exists in database using GSI."""
        court_id = case_details.get('court_id')
        docket_num = case_details.get('docket_num')

        if not court_id or not docket_num:
            if self._court_logger:
                self._court_logger.warning('Missing court_id or docket_num for GSI check.')
            else:
                self.log_warning('Missing court_id or docket_num for GSI check.')
            return False

        # Use existing repository check
        if self._court_logger:
            self._court_logger.info(f"Checking database GSI for case {court_id}:{docket_num}")
        
        from src.utils.docket_utils import normalize_docket_number
        db_docket_num = normalize_docket_number(docket_num)
        result = await self._repo.check_docket_exists(court_id, db_docket_num)
        
        self.log_verification_result(case_details, 'GSI_CHECK', result, f"Database GSI check {'found' if result else 'not found'}")
        
        return result

    async def check_local_artifacts_pdf_zip(self, case_details: Dict[str, Any]) -> bool:
        """Check for local PDF/ZIP artifacts only (for explicitly requested cases)."""
        court_id = case_details.get('court_id')
        docket_num = case_details.get('docket_num')
        
        if self._court_logger:
            self._court_logger.info(f"Checking local PDF/ZIP artifacts for case {court_id}:{docket_num}")
        
        # This is a simplified implementation - in practice would check specific file types
        result = await self._file_manager.check_local_files(case_details, file_types=['pdf', 'zip'])
        
        self.log_verification_result(case_details, 'PDF_ZIP_CHECK', result, f"Local PDF/ZIP artifacts {'found' if result else 'not found'}")
        
        return result
    
    async def check_all_local_files(self, case_details: Dict[str, Any]) -> bool:
        """Check for any local files (for report-scraped cases)."""
        court_id = case_details.get('court_id')
        docket_num = case_details.get('docket_num')
        
        if self._court_logger:
            self._court_logger.info(f"Checking all local files for case {court_id}:{docket_num}")
        
        # This checks for any existing files
        result = await self._file_manager.check_local_files(case_details, file_types=None)
        
        self.log_verification_result(case_details, 'ALL_FILES_CHECK', result, f"All local files {'found' if result else 'not found'}")
        
        return result

    def log_verification_result(self, case_details: Dict[str, Any], verification_type: str, result: bool, details: str = None):
        """Log verification results with court_logger for tracking."""
        court_id = case_details.get('court_id', 'unknown')
        docket_num = case_details.get('docket_num', 'unknown')
        
        result_text = 'PASS' if result else 'FAIL'
        message = f"Verification [{verification_type}] for {court_id}:{docket_num}: {result_text}"
        
        if details:
            message += f" - {details}"
        
        if self._court_logger:
            if result:
                self._court_logger.info(message)
            else:
                self._court_logger.warning(message)
        else:
            if result:
                self.log_info(message)
            else:
                self.log_warning(message)

    async def comprehensive_verification(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive verification and return detailed results."""
        court_id = case_details.get('court_id', 'unknown')
        docket_num = case_details.get('docket_num', 'unknown')
        
        if self._court_logger:
            self._court_logger.info(f"Starting comprehensive verification for case {court_id}:{docket_num}")
        
        results = {
            'case_details': case_details,
            'database_exists': False,
            'local_files_exist': False,
            'pdf_zip_exist': False,
            'should_process': False,
            'verification_summary': []
        }
        
        try:
            # Database check
            results['database_exists'] = await self.check_database_gsi(case_details)
            results['verification_summary'].append(f"Database check: {'EXISTS' if results['database_exists'] else 'NOT_FOUND'}")
            
            # Local files check
            results['local_files_exist'] = await self.check_all_local_files(case_details)
            results['verification_summary'].append(f"Local files check: {'FOUND' if results['local_files_exist'] else 'NOT_FOUND'}")
            
            # PDF/ZIP check
            results['pdf_zip_exist'] = await self.check_local_artifacts_pdf_zip(case_details)
            results['verification_summary'].append(f"PDF/ZIP check: {'FOUND' if results['pdf_zip_exist'] else 'NOT_FOUND'}")
            
            # Overall decision
            results['should_process'] = not results['database_exists']
            results['verification_summary'].append(f"Processing decision: {'PROCESS' if results['should_process'] else 'SKIP'}")
            
            if self._court_logger:
                self._court_logger.info(f"Comprehensive verification completed for {court_id}:{docket_num}: {results['verification_summary']}")
            
        except Exception as e:
            error_msg = f"Comprehensive verification failed for {court_id}:{docket_num}: {str(e)}"
            results['verification_summary'].append(f"ERROR: {str(e)}")
            
            if self._court_logger:
                self._court_logger.error(error_msg)
            else:
                self.log_error(error_msg)
        
        return results
