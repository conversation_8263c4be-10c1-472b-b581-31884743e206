"""
Filename Validator for PACER Processing

This component validates JSON filename generation and ensures consistent
filename handling across all workflow paths using create_base_filename method.
"""

from __future__ import annotations
from typing import Any, Dict, Optional, List
import re
from pathlib import Path

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError


class FilenameValidator(AsyncServiceBase):
    """
    Validates and ensures consistent filename generation for JSON saves.
    
    Integrates with create_base_filename method to provide comprehensive
    filename validation and consistency checks across all workflow paths.
    """

    def __init__(self, logger: Optional[Any] = None, config: Optional[Dict] = None):
        super().__init__(logger, config)
        self._validation_statistics = {
            "total_validations": 0,
            "filename_corrections": 0,
            "validation_failures": 0,
            "format_violations": 0
        }
        
        # Filename validation rules
        self.filename_rules = {
            'max_length': 200,
            'allowed_chars': r'[a-zA-Z0-9_\-.]',
            'forbidden_chars': r'[<>:"/\\|?*\s]',
            'required_extension': '.json',
            'court_id_required': True,
            'docket_num_required': True
        }

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate validation methods."""
        action = data.get('action')
        
        if action == 'validate_filename':
            return await self.validate_filename(
                case_data=data['case_data'],
                filename=data.get('filename'),
                use_create_base_filename=data.get('use_create_base_filename', True)
            )
        elif action == 'generate_validated_filename':
            return await self.generate_validated_filename(
                case_data=data['case_data'],
                override_filename=data.get('override_filename')
            )
        elif action == 'validate_batch_filenames':
            return await self.validate_batch_filenames(data['case_data_list'])
        elif action == 'get_filename_statistics':
            return self.get_filename_statistics()
        else:
            raise PacerServiceError(f"Unknown action for FilenameValidator: {action}")

    async def validate_filename(
        self, 
        case_data: Dict[str, Any], 
        filename: Optional[str] = None,
        use_create_base_filename: bool = True
    ) -> Dict[str, Any]:
        """
        Validate filename for JSON save operation.
        
        Args:
            case_data: Case data dictionary
            filename: Optional explicit filename to validate
            use_create_base_filename: Whether to use create_base_filename method for validation
            
        Returns:
            Validation result with corrected filename if needed
        """
        log_prefix = f"[{case_data.get('court_id', 'N/A')}][{case_data.get('docket_num', 'N/A')}]"
        
        try:
            self._validation_statistics['total_validations'] += 1
            
            # Get filename to validate
            if filename:
                target_filename = filename
                filename_source = 'explicit'
            elif use_create_base_filename and 'base_filename' in case_data:
                target_filename = case_data['base_filename']
                filename_source = 'base_filename'
            else:
                # Generate using create_base_filename logic
                target_filename = await self._generate_base_filename(case_data)
                filename_source = 'generated'
            
            # Add .json extension if not present
            if not target_filename.endswith('.json'):
                target_filename += '.json'
            
            # Perform validation checks
            validation_issues = []
            corrected_filename = target_filename
            
            # Check length
            if len(target_filename) > self.filename_rules['max_length']:
                validation_issues.append(f"Filename too long: {len(target_filename)} > {self.filename_rules['max_length']}")
                corrected_filename = self._truncate_filename(target_filename)
            
            # Check forbidden characters
            forbidden_matches = re.findall(self.filename_rules['forbidden_chars'], target_filename)
            if forbidden_matches:
                validation_issues.append(f"Forbidden characters found: {forbidden_matches}")
                corrected_filename = self._clean_forbidden_chars(corrected_filename)
            
            # Check court_id presence
            if self.filename_rules['court_id_required']:
                court_id = case_data.get('court_id', '')
                if court_id and court_id.lower() not in corrected_filename.lower():
                    validation_issues.append(f"Court ID not found in filename: {court_id}")
            
            # Check docket_num presence
            if self.filename_rules['docket_num_required']:
                docket_num = case_data.get('docket_num', '')
                if docket_num:
                    clean_docket = self._clean_docket_for_comparison(docket_num)
                    if not any(part in corrected_filename for part in clean_docket.split('_')):
                        validation_issues.append(f"Docket number not found in filename: {docket_num}")
            
            # Final validation
            if not re.match(r'^' + self.filename_rules['allowed_chars'] + r'+\.json$', corrected_filename):
                validation_issues.append(f"Invalid characters in corrected filename: {corrected_filename}")
                corrected_filename = self._ensure_valid_chars(corrected_filename)
                self._validation_statistics['filename_corrections'] += 1
            
            # Create result
            is_valid = len(validation_issues) == 0 or target_filename == corrected_filename
            
            result = {
                'is_valid': is_valid,
                'original_filename': target_filename,
                'corrected_filename': corrected_filename,
                'filename_source': filename_source,
                'validation_issues': validation_issues,
                'needs_correction': target_filename != corrected_filename,
                'validation_timestamp': self._get_timestamp()
            }
            
            if validation_issues:
                self._validation_statistics['format_violations'] += len(validation_issues)
                if not is_valid:
                    self._validation_statistics['validation_failures'] += 1
            
            self.log_info(f"{log_prefix} Filename validation completed", {
                "original_filename": target_filename,
                "corrected_filename": corrected_filename,
                "is_valid": is_valid,
                "issues_count": len(validation_issues)
            })
            
            return result
            
        except Exception as e:
            self._validation_statistics['validation_failures'] += 1
            self.log_error(f"{log_prefix} Filename validation failed: {e}")
            raise PacerServiceError(f"Filename validation failed: {e}")

    async def generate_validated_filename(
        self,
        case_data: Dict[str, Any],
        override_filename: Optional[str] = None
    ) -> str:
        """
        Generate a validated filename using create_base_filename logic.
        
        Args:
            case_data: Case data dictionary
            override_filename: Optional filename to validate instead of generating
            
        Returns:
            Validated filename ready for JSON save
        """
        log_prefix = f"[{case_data.get('court_id', 'N/A')}][{case_data.get('docket_num', 'N/A')}]"
        
        try:
            if override_filename:
                # Validate provided filename
                validation_result = await self.validate_filename(
                    case_data, override_filename, use_create_base_filename=False
                )
                final_filename = validation_result['corrected_filename']
            else:
                # Generate using create_base_filename logic and validate
                base_filename = await self._generate_base_filename(case_data)
                validation_result = await self.validate_filename(
                    case_data, base_filename + '.json', use_create_base_filename=False
                )
                final_filename = validation_result['corrected_filename']
            
            self.log_info(f"{log_prefix} Generated validated filename: {final_filename}")
            return final_filename
            
        except Exception as e:
            self.log_error(f"{log_prefix} Failed to generate validated filename: {e}")
            # Return fallback filename
            court_id = case_data.get('court_id', 'unknown')
            docket_num = case_data.get('docket_num', 'unknown')
            fallback = f"{court_id}_{docket_num}.json".replace(':', '_').replace('/', '_')
            return self._ensure_valid_chars(fallback)

    async def validate_batch_filenames(self, case_data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate filenames for a batch of cases.
        
        Args:
            case_data_list: List of case data dictionaries
            
        Returns:
            Batch validation results
        """
        try:
            batch_results = []
            summary = {
                'total_cases': len(case_data_list),
                'valid_filenames': 0,
                'invalid_filenames': 0,
                'corrections_made': 0,
                'validation_errors': 0
            }
            
            for i, case_data in enumerate(case_data_list):
                try:
                    validation_result = await self.validate_filename(case_data)
                    batch_results.append({
                        'case_index': i,
                        'court_id': case_data.get('court_id'),
                        'docket_num': case_data.get('docket_num'),
                        'validation_result': validation_result
                    })
                    
                    if validation_result['is_valid']:
                        summary['valid_filenames'] += 1
                    else:
                        summary['invalid_filenames'] += 1
                    
                    if validation_result['needs_correction']:
                        summary['corrections_made'] += 1
                        
                except Exception as e:
                    summary['validation_errors'] += 1
                    batch_results.append({
                        'case_index': i,
                        'court_id': case_data.get('court_id'),
                        'docket_num': case_data.get('docket_num'),
                        'validation_error': str(e)
                    })
            
            self.log_info(f"Batch filename validation completed", summary)
            
            return {
                'summary': summary,
                'results': batch_results,
                'batch_timestamp': self._get_timestamp()
            }
            
        except Exception as e:
            self.log_error(f"Batch filename validation failed: {e}")
            raise PacerServiceError(f"Batch validation failed: {e}")

    async def _generate_base_filename(self, case_data: Dict[str, Any]) -> str:
        """
        Generate base filename using CORRECTED create_base_filename logic.
        
        This method matches the logic from CaseTransformer._create_base_filename
        to ensure consistency across all filename generation.
        """
        try:
            court_id = case_data.get("court_id", "unknown")
            docket_num = case_data.get("docket_num", "unknown")
            versus = case_data.get("versus", case_data.get("case_title", "unknown_versus"))

            # CRITICAL FIX: Extract only numeric parts from docket number
            # "1:25-cv-09395" -> "25_09395" (excludes "cv")
            clean_docket = self._parse_docket_numeric_only(docket_num)

            # Clean versus - remove ALL punctuation, keep only alphanumeric and spaces
            clean_versus = re.sub(r'[^\w\s]', '', versus)  # L'Oreal -> LOreal, remove all punctuation
            # Replace multiple spaces with single underscore and strip
            clean_versus = re.sub(r'\s+', '_', clean_versus).strip('_')
            # Limit length
            clean_versus = clean_versus[:60]

            # Create base filename with numeric-only docket format
            base_filename = f"{court_id.lower()}_{clean_docket}_{clean_versus}"
            # Clean any remaining problematic characters
            base_filename = re.sub(r'[^\w_-]+', '', base_filename)
            
            return base_filename
            
        except Exception as e:
            self.log_error(f"Error generating base filename: {e}")
            court_id = case_data.get('court_id', 'unknown')
            docket_num = case_data.get('docket_num', 'unknown')
            return f"{court_id.lower()}_{docket_num}".replace(':', '_').replace('/', '_')

    def _parse_docket_numeric_only(self, docket_num: str) -> str:
        """
        Extract only numeric parts from docket number, excluding case type letters.
        
        This matches CaseTransformer._parse_docket_numeric_only logic:
        - "1:25-cv-09395" -> "25_09395" (excludes "cv")
        - "2:24-cr-12345" -> "24_12345" (excludes "cr")
        - "4:24-md-03087" -> "24_03087" (excludes "md")
        """
        if not docket_num or ':' not in docket_num:
            self.log_warning(f"Invalid docket format for numeric parsing: '{docket_num}'")
            return "unknown_docket"
        
        try:
            # Use regex to extract numeric parts: N:YY-XX-NNNNN -> YY_NNNNN
            pattern = r'\d+:(\d{2})-[a-zA-Z]{2}-(\d{1,5})'
            match = re.search(pattern, docket_num.strip())
            
            if not match:
                self.log_warning(f"No valid numeric docket pattern found in: '{docket_num}'")
                # Fallback: try to extract any numbers after colon
                fallback_pattern = r':(\d+).*?(\d+)'
                fallback_match = re.search(fallback_pattern, docket_num)
                if fallback_match:
                    year_part = fallback_match.group(1)[:2].zfill(2)  # Take first 2 digits, zero-pad
                    case_part = fallback_match.group(2).zfill(5)[:5]   # Take digits, zero-pad to 5, max 5
                    result = f"{year_part}_{case_part}"
                    self.log_info(f"Used fallback parsing: '{docket_num}' -> '{result}'")
                    return result
                return "unknown_docket"
                
            year = match.group(1)          # YY (2 digits)
            case_number = match.group(2)   # NNNNN (1-5 digits)
            
            # Zero-pad case number to exactly 5 digits
            case_number_padded = case_number.zfill(5)
            
            # Create YY_NNNNN format (numeric only)
            result = f"{year}_{case_number_padded}"
            
            self.log_debug(f"Parsed numeric docket: '{docket_num}' -> '{result}'")
            return result
            
        except Exception as e:
            self.log_error(f"Error parsing numeric docket from '{docket_num}': {e}")
            return "unknown_docket"

    def _truncate_filename(self, filename: str) -> str:
        """Truncate filename to maximum allowed length while preserving extension."""
        if not filename.endswith('.json'):
            filename += '.json'
        
        max_base_length = self.filename_rules['max_length'] - 5  # Reserve space for .json
        if len(filename) > self.filename_rules['max_length']:
            base = filename[:-5]  # Remove .json
            truncated_base = base[:max_base_length]
            return truncated_base + '.json'
        return filename

    def _clean_forbidden_chars(self, filename: str) -> str:
        """Clean forbidden characters from filename."""
        # Replace forbidden chars with underscores
        cleaned = re.sub(self.filename_rules['forbidden_chars'], '_', filename)
        # Remove multiple consecutive underscores
        cleaned = re.sub(r'_+', '_', cleaned)
        return cleaned

    def _ensure_valid_chars(self, filename: str) -> str:
        """Ensure filename contains only valid characters."""
        # Keep only allowed characters
        cleaned = re.sub(r'[^a-zA-Z0-9_\-.]', '_', filename)
        # Ensure it has .json extension
        if not cleaned.endswith('.json'):
            cleaned = cleaned.rstrip('.') + '.json'
        return cleaned

    def _clean_docket_for_comparison(self, docket_num: str) -> str:
        """Clean docket number for filename comparison."""
        return docket_num.replace(':', '_').replace('-', '_').replace('/', '_')

    def _get_timestamp(self) -> str:
        """Get current timestamp for validation records."""
        from datetime import datetime
        return datetime.now().isoformat()

    def get_filename_statistics(self) -> Dict[str, Any]:
        """Get filename validation statistics."""
        return {
            **self._validation_statistics,
            'service_status': 'operational' if self._initialized else 'not_initialized',
            'validation_rules': self.filename_rules
        }