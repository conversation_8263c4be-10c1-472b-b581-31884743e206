"""
Enhanced Download Manager for PACER Core Services Integration.

This component provides enhanced download management capabilities that integrate
properly with the new component-based architecture while maintaining compatibility
with existing facade patterns.
"""

from pathlib import Path
from typing import Any, Dict, Optional, TYPE_CHECKING
import asyncio

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol
    from src.infrastructure.storage.s3_async_storage import S3AsyncStorage


class EnhancedDownloadManager(ComponentImplementation):
    """Enhanced manager for download processes with full S3 integration.
    
    Uses court-specific logging to ensure ALL logs go to 
    data/{iso_date}/logs/pacer/{court_id}.log when court_logger is provided.
    """

    def __init__(
        self,
        s3_storage: Optional['S3AsyncStorage'] = None,
        logger: Optional['LoggerProtocol'] = None,
        config: Optional[Dict[str, Any]] = None,
        court_logger: Optional['LoggerProtocol'] = None
    ):
        super().__init__(logger, config)
        self._s3_storage = s3_storage
        
        # Configuration defaults
        self._cdn_base_url = (config or {}).get("cdn_base_url", "https://cdn.example.com")
        self._retry_attempts = (config or {}).get("download_retry_attempts", 3)
        self._retry_delay = (config or {}).get("download_retry_delay", 1.0)
        
        # Use court_logger if provided, otherwise fall back to standard logger
        if court_logger:
            self.logger = court_logger
            self.log_debug("EnhancedDownloadManager initialized with court-specific logger")
    
    def set_court_logger(self, court_id: str, iso_date: str) -> None:
        """
        Set up court-specific logging for this component.
        
        Args:
            court_id: Court identifier (e.g., 'cand', 'nysd') 
            iso_date: ISO date string for directory organization
        """
        try:
            # Create court logger using component_base functionality
            court_logger = self.create_court_logger(court_id, iso_date)
            self.log_info(f"[{court_id.upper()}] EnhancedDownloadManager: Court logger configured - all logs will go to data/{iso_date}/logs/pacer/{court_id.lower()}.log")
        except Exception as e:
            self.log_error(f"[DOWNLOAD] Failed to create court logger for {court_id}: {e}", exc_info=True)

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate download methods."""
        action = data.get('action')
        
        if action == 'upload_to_s3':
            return await self.upload_to_s3(
                file_path=data['file_path'],
                s3_key=data['s3_key']
            )
        elif action == 'process_html_only':
            return await self.process_html_only(
                html_content=data['html_content'],
                s3_key=data['s3_key']
            )
        elif action == 'upload_document':
            return await self.upload_document(
                document_path=data['document_path'],
                case_details=data['case_details']
            )
        elif action == 'prepare_context':
            return await self.prepare_download_context(
                case_details=data['case_details']
            )
        elif action == 'cleanup_temp_files':
            return await self.cleanup_temporary_files(
                file_paths=data.get('file_paths', [])
            )
        else:
            raise PacerServiceError(f"Unknown action for EnhancedDownloadManager: {action}")

    async def upload_to_s3(self, file_path: str, s3_key: str) -> Optional[str]:
        """
        Upload a file to S3 with retry logic and return the CDN link.
        
        Args:
            file_path: Local path to the file to upload
            s3_key: S3 key for the upload
            
        Returns:
            CDN link if successful, None if failed
        """
        self.log_info(f"[DOWNLOAD] Uploading {file_path} to S3 at {s3_key}")
        
        if not self._s3_storage:
            self.log_error("[DOWNLOAD] S3 storage not configured for upload")
            return None
        
        # Verify file exists
        file_path_obj = Path(file_path)
        if not file_path_obj.exists():
            self.log_error(f"[DOWNLOAD] File not found: {file_path}")
            return None
        
        # Retry upload with exponential backoff and jitter
        for attempt in range(self._retry_attempts):
            try:
                # Add timeout wrapper around upload
                upload_timeout = 60.0 + (attempt * 30.0)  # Increase timeout each attempt
                
                success, message = await asyncio.wait_for(
                    self._s3_storage.upload_file(file_path, s3_key),
                    timeout=upload_timeout
                )
                
                if success:
                    cdn_link = f"{self._cdn_base_url}/{s3_key}"
                    self.log_info(f"[DOWNLOAD] Upload successful. CDN link: {cdn_link} (attempt {attempt + 1})")
                    return cdn_link
                else:
                    self.log_warning(f"[DOWNLOAD] S3 upload attempt {attempt + 1} failed: {message}")
                    
                    if attempt < self._retry_attempts - 1:
                        # Exponential backoff with jitter
                        wait_time = self._retry_delay * (2 ** attempt) + (0.1 * attempt)
                        self.log_info(f"[DOWNLOAD] Retrying upload in {wait_time:.1f} seconds...")
                        await asyncio.sleep(wait_time)
                    
            except asyncio.TimeoutError:
                self.log_error(f"[DOWNLOAD] Upload timeout on attempt {attempt + 1} after {upload_timeout} seconds")
                
                if attempt < self._retry_attempts - 1:
                    wait_time = self._retry_delay * (2 ** attempt) + (0.2 * attempt)
                    self.log_info(f"[DOWNLOAD] Retrying upload after timeout in {wait_time:.1f} seconds...")
                    await asyncio.sleep(wait_time)
                    
            except Exception as e:
                self.log_error(f"[DOWNLOAD] Exception during S3 upload attempt {attempt + 1}: {e}")
                
                if attempt < self._retry_attempts - 1:
                    wait_time = self._retry_delay * (2 ** attempt) + (0.1 * attempt)
                    await asyncio.sleep(wait_time)
        
        self.log_error(f"[DOWNLOAD] All upload attempts failed for {file_path}")
        return None

    async def process_html_only(self, html_content: str, s3_key: str) -> Optional[str]:
        """
        Process HTML-only cases by uploading HTML content to S3.
        
        Args:
            html_content: HTML content as string
            s3_key: S3 key for the HTML file
            
        Returns:
            CDN link if successful, None if failed
        """
        self.log_info(f"[DOWNLOAD] Processing HTML-only case. Uploading to {s3_key}")
        
        if not self._s3_storage:
            self.log_error("[DOWNLOAD] S3 storage not configured for HTML upload")
            return None
        
        if not html_content or html_content.strip() == "":
            self.log_error("[DOWNLOAD] Empty HTML content provided")
            return None
        
        # Retry upload with exponential backoff and timeout handling
        for attempt in range(self._retry_attempts):
            try:
                # Add timeout wrapper around HTML upload
                upload_timeout = 30.0 + (attempt * 15.0)  # Increase timeout each attempt
                
                success = await asyncio.wait_for(
                    self._s3_storage.upload_html_string_async(html_content, s3_key),
                    timeout=upload_timeout
                )
                
                if success:
                    cdn_link = f"{self._cdn_base_url}/{s3_key}"
                    self.log_info(f"[DOWNLOAD] HTML upload successful. CDN link: {cdn_link} (attempt {attempt + 1})")
                    return cdn_link
                else:
                    self.log_warning(f"[DOWNLOAD] HTML upload attempt {attempt + 1} failed")
                    
                    if attempt < self._retry_attempts - 1:
                        # Exponential backoff with jitter
                        wait_time = self._retry_delay * (2 ** attempt) + (0.1 * attempt)
                        self.log_info(f"[DOWNLOAD] Retrying HTML upload in {wait_time:.1f} seconds...")
                        await asyncio.sleep(wait_time)
                    
            except asyncio.TimeoutError:
                self.log_error(f"[DOWNLOAD] HTML upload timeout on attempt {attempt + 1} after {upload_timeout} seconds")
                
                if attempt < self._retry_attempts - 1:
                    wait_time = self._retry_delay * (2 ** attempt) + (0.2 * attempt)
                    self.log_info(f"[DOWNLOAD] Retrying HTML upload after timeout in {wait_time:.1f} seconds...")
                    await asyncio.sleep(wait_time)
                    
            except Exception as e:
                self.log_error(f"[DOWNLOAD] Exception during HTML upload attempt {attempt + 1}: {e}")
                
                if attempt < self._retry_attempts - 1:
                    wait_time = self._retry_delay * (2 ** attempt) + (0.1 * attempt)
                    await asyncio.sleep(wait_time)
        
        self.log_error("[DOWNLOAD] All HTML upload attempts failed")
        return None

    async def upload_document(
        self, 
        document_path: str, 
        case_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Upload a document with automatic S3 key generation.
        
        Args:
            document_path: Path to the document file
            case_details: Case details for S3 key generation
            
        Returns:
            Upload result with status and links
        """
        try:
            # Generate S3 key from case details
            iso_date = case_details.get('iso_date', 'unknown_date')
            base_filename = case_details.get('base_filename', 'unknown_file')
            court_id = case_details.get('court_id', 'unknown_court')
            
            # Determine file extension
            file_ext = Path(document_path).suffix or '.pdf'
            s3_key = f"{iso_date}/dockets/{court_id}/{base_filename}{file_ext}"
            
            # Upload to S3 with comprehensive error handling
            try:
                cdn_link = await asyncio.wait_for(
                    self.upload_to_s3(document_path, s3_key),
                    timeout=120.0  # 2 minute timeout for document upload
                )
                
                return {
                    'success': cdn_link is not None,
                    'cdn_link': cdn_link,
                    's3_key': s3_key,
                    'local_path': document_path,
                    'file_size': Path(document_path).stat().st_size if Path(document_path).exists() else 0
                }
                
            except asyncio.TimeoutError:
                self.log_error(f"[DOWNLOAD] Document upload timeout for {document_path} after 2 minutes")
                return {
                    'success': False,
                    'error': 'Upload timeout after 2 minutes',
                    'cdn_link': None,
                    's3_key': s3_key,
                    'local_path': document_path
                }
                
            except Exception as upload_e:
                self.log_error(f"[DOWNLOAD] Document upload exception for {document_path}: {upload_e}")
                return {
                    'success': False,
                    'error': str(upload_e),
                    'cdn_link': None,
                    's3_key': s3_key,
                    'local_path': document_path
                }
            
        except Exception as e:
            self.log_error(f"[DOWNLOAD] Error uploading document {document_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'cdn_link': None,
                's3_key': None,
                'local_path': document_path
            }

    async def prepare_download_context(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare download context for case processing.
        
        Args:
            case_details: Case details dictionary
            
        Returns:
            Enhanced context with download-specific metadata
        """
        try:
            court_id = case_details.get('court_id', 'unknown')
            docket_num = case_details.get('docket_num', 'unknown')
            iso_date = case_details.get('iso_date', 'unknown')
            
            # Generate base filename if not present
            if not case_details.get('base_filename'):
                base_filename = f"{court_id.lower()}_{docket_num}_{iso_date}"
                base_filename = base_filename.replace(':', '').replace('/', '_')
            else:
                base_filename = case_details['base_filename']
            
            # Prepare download paths
            temp_download_dir = Path(self.config.get('temp_download_dir', './temp/downloads'))
            temp_download_dir.mkdir(parents=True, exist_ok=True)
            
            download_context = {
                'base_filename': base_filename,
                'temp_download_dir': str(temp_download_dir),
                'expected_pdf_path': str(temp_download_dir / f"{base_filename}.pdf"),
                'expected_zip_path': str(temp_download_dir / f"{base_filename}.zip"),
                's3_key_prefix': f"{iso_date}/dockets/{court_id}",
                'html_s3_key': f"{iso_date}/html/{court_id}/{base_filename}.html",
                'metadata': {
                    'court_id': court_id,
                    'docket_num': docket_num,
                    'iso_date': iso_date,
                    'prepared_at': self._get_current_timestamp()
                }
            }
            
            self.log_debug(f"[DOWNLOAD] Prepared download context for {court_id}/{docket_num}")
            return download_context
            
        except Exception as e:
            self.log_error(f"[DOWNLOAD] Error preparing download context: {e}")
            return {}

    async def cleanup_temporary_files(self, file_paths: list[str]) -> Dict[str, Any]:
        """
        Clean up temporary files after processing.
        
        Args:
            file_paths: List of file paths to clean up
            
        Returns:
            Cleanup results
        """
        cleaned_count = 0
        failed_count = 0
        errors = []
        
        # Add timeout wrapper around cleanup operations
        cleanup_tasks = []
        
        async def cleanup_single_file(file_path: str) -> tuple[bool, str]:
            try:
                # Add timeout for each file cleanup
                await asyncio.wait_for(
                    asyncio.to_thread(self._cleanup_file_sync, file_path),
                    timeout=10.0  # 10 second timeout per file
                )
                return True, f"Cleaned up temporary file: {file_path}"
            except asyncio.TimeoutError:
                return False, f"Cleanup timeout for {file_path}"
            except Exception as e:
                return False, f"Failed to clean up {file_path}: {e}"
        
        # Process all cleanup operations concurrently with individual timeouts
        cleanup_results = await asyncio.gather(
            *[cleanup_single_file(fp) for fp in file_paths],
            return_exceptions=True
        )
        
        for i, result in enumerate(cleanup_results):
            file_path = file_paths[i]
            
            if isinstance(result, Exception):
                failed_count += 1
                error_msg = f"Cleanup exception for {file_path}: {result}"
                errors.append(error_msg)
                self.log_warning(error_msg)
            else:
                success, message = result
                if success:
                    cleaned_count += 1
                    self.log_debug(message)
                else:
                    failed_count += 1
                    errors.append(message)
                    self.log_warning(message)
        
        result = {
            'cleaned_count': cleaned_count,
            'failed_count': failed_count,
            'total_requested': len(file_paths),
            'errors': errors
        }
        
        if cleaned_count > 0:
            self.log_info(f"[DOWNLOAD] Cleanup completed: {cleaned_count} files cleaned, {failed_count} failed")
        
        return result
    
    def _cleanup_file_sync(self, file_path: str) -> None:
        """Synchronous file cleanup for use with asyncio.to_thread."""
        path_obj = Path(file_path)
        if path_obj.exists():
            path_obj.unlink()
        # If file doesn't exist, consider it already cleaned

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime
        return datetime.now().isoformat()

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on download manager components.
        
        Returns:
            Health status information
        """
        health_status = {
            'service': 'EnhancedDownloadManager',
            'status': 'healthy',
            'components': {
                's3_storage': self._s3_storage is not None,
                'cdn_configured': bool(self._cdn_base_url),
                'temp_dir_accessible': True
            },
            'configuration': {
                'cdn_base_url': self._cdn_base_url,
                'retry_attempts': self._retry_attempts,
                'retry_delay': self._retry_delay
            }
        }
        
        # Test temp directory access
        try:
            temp_dir = Path(self.config.get('temp_download_dir', './temp/downloads'))
            temp_dir.mkdir(parents=True, exist_ok=True)
            test_file = temp_dir / '.health_check'
            test_file.write_text('test')
            test_file.unlink()
        except Exception as e:
            health_status['components']['temp_dir_accessible'] = False
            health_status['status'] = 'degraded'
            health_status['temp_dir_error'] = str(e)
        
        # Test S3 connection if available
        if self._s3_storage:
            try:
                # This would be a simple S3 connectivity test
                # Implementation depends on S3AsyncStorage interface
                health_status['components']['s3_connection'] = 'unknown'  # Would test actual connection
            except Exception as e:
                health_status['components']['s3_connection'] = f'error: {e}'
                health_status['status'] = 'degraded'
        
        return health_status