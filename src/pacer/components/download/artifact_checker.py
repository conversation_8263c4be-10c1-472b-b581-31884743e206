"""
Artifact checker module for determining if dockets need to be downloaded.

This module implements the logic to check if docket artifacts already exist
in the file system and determines whether a docket should be downloaded or skipped.
All logging is done through the court_logger passed from parent components.
"""
import os
import json
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timedelta

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.transformer.components.file.file_handler_utils import FileHandlerUtils
from src.repositories.pacer_repository import PacerRepository


class DocketArtifactChecker(ComponentImplementation):
    """
    Checks for existing docket artifacts to determine if downloads are needed.
    
    Implements the following rules:
    1. If html_only: true, skip processing
    2. If is_transferred: true and transferor exists, skip
    3. If JSON + (PDF or ZIP or MD) exist, skip download
    4. All above conditions mean docket should not be downloaded
    
    All logging goes through court_logger to data/{iso_date}/logs/pacer/{court_id}.log
    """
    
    def __init__(
        self, 
        court_logger,
        config: Dict[str, Any],
        pacer_repository: Optional[PacerRepository] = None
    ):
        """
        Initialize the artifact checker.
        
        Args:
            court_logger: Court logger instance from parent component
            config: Configuration dictionary containing iso_date
            pacer_repository: Optional repository for checking transferred dockets
        """
        super().__init__(court_logger, config)
        self.court_logger = court_logger
        self.pacer_repository = pacer_repository
        # Use the same court logger for FileHandlerUtils to maintain consistent logging
        self.file_utils = FileHandlerUtils(court_logger, config)
    
    def _get_past_7_days_dates(self, base_date: Optional[str] = None) -> List[str]:
        """
        Generate list of ISO date strings for the past 7 days (including today).
        
        Args:
            base_date: Optional base date string in YYYYMMDD format. If None, uses today.
            
        Returns:
            List of 7 date strings in YYYYMMDD format, from today going back 7 days
            Example: ['20250811', '20250810', '20250809', '20250808', '20250807', '20250806', '20250805']
        """
        if base_date:
            try:
                # Parse provided base_date (expected in YYYYMMDD format)
                base_datetime = datetime.strptime(base_date, '%Y%m%d')
            except ValueError:
                self.court_logger.warning(f"Invalid date format '{base_date}', using today")
                base_datetime = datetime.now()
        else:
            base_datetime = datetime.now()
        
        # Generate past 7 days including today
        dates = []
        for i in range(7):
            date_obj = base_datetime - timedelta(days=i)
            date_str = date_obj.strftime('%Y%m%d')
            dates.append(date_str)
            
        self.court_logger.debug(f"Generated 7-day date range: {dates}")
        return dates

    async def execute(self, data: Any) -> Any:
        """
        Public execute method that delegates to _execute_action.
        This provides the expected interface for callers like WorkflowOrchestrator.
        """
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        
        if action == 'should_download_docket':
            return await self.should_download_docket(
                data['court_id'], 
                data['docket_num'],
                data.get('versus', ''),
                data.get('iso_date')
            )
        elif action == 'check_batch':
            return await self.check_batch(
                data['docket_items'],
                data.get('iso_date')
            )
        elif action == 'check_docket_exists':
            return await self.check_docket_exists(
                data['court_id'],
                data['docket_num']
            )
        else:
            raise ValueError(f"Unknown action for DocketArtifactChecker: {action}")
        
    def _get_base_filename(
        self, 
        court_id: str, 
        docket_num: str, 
        versus: str
    ) -> str:
        """
        Generate normalized base filename using file utils.
        
        Args:
            court_id: Court identifier (e.g., 'cand')
            docket_num: Docket number (e.g., '3:25-cv-43190')
            versus: Case title (e.g., 'Jones v. Johnson & Johnson')
            
        Returns:
            Normalized filename without extension
        """
        data = {
            'court_id': court_id.lower(),
            'docket_num': docket_num,
            'versus': versus
        }
        
        try:
            # Use the file handler utils to create normalized filename
            filename = self.file_utils.create_filename(data)
            return filename
        except Exception as e:
            self.court_logger.error(f"Error creating filename: {e}", extra={"court_id": court_id, "docket_num": docket_num})
            # Fallback to basic normalization
            return self._fallback_filename(court_id, docket_num, versus)
    
    def _fallback_filename(
        self, 
        court_id: str, 
        docket_num: str, 
        versus: str
    ) -> str:
        """
        CRITICAL FIX: Fallback filename generation that correctly excludes case type letters.
        Uses the centralized docket parsing utility to ensure consistency.
        
        Args:
            court_id: Court identifier
            docket_num: Docket number (e.g., "1:25-cv-09502")
            versus: Case title
            
        Returns:
            Basic normalized filename following {court_id}_{YY}_{NNNNN}_{versus} pattern
            Example: "ilnd_25_09502_Cary_v_LOreal_USA_Inc_et_al" (NO "cv"!)
        """
        import re
        
        # CRITICAL FIX: Use centralized utility to extract ONLY numeric parts
        try:
            from src.utils.docket_utils import parse_docket_number_numeric_only
            clean_docket = parse_docket_number_numeric_only(docket_num)
            
            # Verify the format is YY_NNNNN
            if clean_docket and '_' in clean_docket:
                parts = clean_docket.split('_')
                if len(parts) == 2 and len(parts[0]) == 2 and len(parts[1]) == 5:
                    # Valid format: YY_NNNNN
                    self.court_logger.debug(f"Successfully parsed docket '{docket_num}' to '{clean_docket}'")
                else:
                    self.court_logger.warning(f"Unexpected format from parse_docket_number_numeric_only: '{clean_docket}' for docket '{docket_num}'")
            else:
                self.court_logger.warning(f"Invalid clean_docket format: '{clean_docket}' for docket '{docket_num}'")
                
        except ImportError:
            # Fallback if utility not available - manually extract numeric parts
            self.court_logger.warning(f"Could not import docket_utils, using manual parsing for {docket_num}")
            clean_docket = self._manual_parse_numeric_only(docket_num)
        except Exception as e:
            self.court_logger.error(f"Error using parse_docket_number_numeric_only for '{docket_num}': {e}")
            clean_docket = self._manual_parse_numeric_only(docket_num)
        
        # If versus is empty in fallback, this should not happen in normal operation
        # as we skip empty versus items in check_batch. But if it does happen:
        if not versus or versus.strip() == '':
            self.court_logger.error(
                f"Fallback filename generation called with empty versus: {court_id}/{docket_num}"
            )
            versus = f"NoVersus_{docket_num.replace(':', '_').replace('-', '_')}"
        
        # Normalize versus string using same logic as FileHandlerUtils
        versus_clean = versus.replace('.', '').replace(',', '').replace(' ', '_').replace('/', '_')
        versus_clean = re.sub(r'[^\w\s_-]', '', versus_clean)
        versus_clean = versus_clean.strip('_')
        
        # Limit length like FileHandlerUtils
        max_len = 100
        if len(versus_clean) > max_len:
            name_base = versus_clean[:max_len]
            last_underscore = name_base.rfind('_')
            if last_underscore > max_len / 2:
                versus_clean = name_base[:last_underscore]
            else:
                versus_clean = name_base
        
        # CRITICAL FIX: Use clean_docket (YY_NNNNN) instead of year_str and num_str separately
        return f"{court_id.lower()}_{clean_docket}_{versus_clean}"
    
    def _manual_parse_numeric_only(self, docket_num: str) -> str:
        """
        Manual parsing fallback that extracts only numeric parts from docket number.
        
        Args:
            docket_num: Docket number (e.g., "1:25-cv-09502")
            
        Returns:
            Numeric-only parts in YY_NNNNN format (e.g., "25_09502")
        """
        import re
        
        # Extract numeric parts: N:YY-XX-NNNNN -> YY_NNNNN (skip letters XX)
        pattern = r'\d+:(\d{2})-[a-zA-Z]{2}-(\d{1,5})'
        match = re.search(pattern, docket_num)
        if match:
            year = match.group(1)          # YY (2 digits)
            case_number = match.group(2)   # NNNNN (1-5 digits)
            case_number_padded = case_number.zfill(5)  # Zero-pad to 5 digits
            result = f"{year}_{case_number_padded}"
            self.court_logger.debug(f"Manual parsing: '{docket_num}' -> '{result}'")
            return result
        else:
            self.court_logger.error(f"Could not manually parse docket number: {docket_num}")
            return "unknown_docket"
    
    def _get_artifact_paths(self, iso_date: str, base_filename: str) -> Dict[str, str]:
        """
        Generate full paths for all potential artifact files.
        
        Args:
            iso_date: ISO format date for directory structure
            base_filename: Base filename without extension
            
        Returns:
            Dictionary of artifact type to full path
        """
        base_dir = f"data/{iso_date}/dockets"
        
        return {
            'json': os.path.join(base_dir, f"{base_filename}.json"),
            'pdf': os.path.join(base_dir, f"{base_filename}.pdf"),
            'zip': os.path.join(base_dir, f"{base_filename}.zip"),
            'md': os.path.join(base_dir, f"{base_filename}.md"),
            'html': os.path.join(base_dir, f"{base_filename}.html")
        }
    
    def _find_existing_artifacts_across_dates(
        self, 
        base_filename: str, 
        base_date: Optional[str] = None
    ) -> Tuple[bool, Optional[str], Dict[str, str]]:
        """
        Search for existing artifacts across the past 7 days.
        PATTERN MATCHING: Files that START WITH {court_id}_{YY}_{NNNNN}
        
        Args:
            base_filename: Base filename pattern to search for (without extension)
            base_date: Optional base date to search from (YYYYMMDD format)
            
        Returns:
            Tuple of (found_any, found_date, artifact_paths) where:
            - found_any: True if any artifacts were found in any date
            - found_date: The date where artifacts were found (YYYYMMDD format)
            - artifact_paths: Dict of artifact types found with their paths
        """
        import glob
        search_dates = self._get_past_7_days_dates(base_date)
        
        # Extract the core pattern from base_filename: {court_id}_{YY}_{NNNNN}
        # CRITICAL: We must match ONLY the starting pattern, not the full filename
        # Example: ilnd_25_09354 should match ilnd_25_09354_Starlet_Postell_v_Beauty_Bell_Enterprises_LLC_et_al.json
        
        # Parse the docket number to get the correct pattern
        if '_' in base_filename:
            parts = base_filename.split('_')
            if len(parts) >= 3:
                # Use only court_id_YY_NNNNN for matching
                pattern_prefix = f"{parts[0]}_{parts[1]}_{parts[2]}"
                self.court_logger.debug(f"Using pattern prefix: {pattern_prefix} from base_filename: {base_filename}")
            else:
                pattern_prefix = base_filename
        else:
            pattern_prefix = base_filename
        
        self.court_logger.debug(f"Searching for files starting with pattern: {pattern_prefix}")
        
        for search_date in search_dates:
            base_dir = f"data/{search_date}/dockets"
            
            # Use glob to find files that START WITH the pattern
            json_pattern = os.path.join(base_dir, f"{pattern_prefix}*.json")
            json_files = glob.glob(json_pattern)
            
            # Log what we're searching for
            if os.path.exists(base_dir):
                self.court_logger.debug(f"Searching in {base_dir} for files matching: {pattern_prefix}*.json")
            
            if json_files:
                # Found at least one JSON file matching the pattern
                json_file = json_files[0]  # Use the first match
                base_name = os.path.splitext(os.path.basename(json_file))[0]
                
                self.court_logger.info(f"✓ Found existing artifact matching {pattern_prefix}: {os.path.basename(json_file)}")
                
                # Now check for other artifacts with the same base name
                found_artifacts = {'json': json_file}
                
                pdf_file = os.path.join(base_dir, f"{base_name}.pdf")
                if os.path.exists(pdf_file):
                    found_artifacts['pdf'] = pdf_file
                    
                zip_file = os.path.join(base_dir, f"{base_name}.zip")
                if os.path.exists(zip_file):
                    found_artifacts['zip'] = zip_file
                    
                md_file = os.path.join(base_dir, f"{base_name}.md")
                if os.path.exists(md_file):
                    found_artifacts['md'] = md_file
                
                # If we found JSON + any other artifacts, return success
                if len(found_artifacts) > 1:
                    self.court_logger.info(
                        f"Found existing artifacts matching pattern {pattern_prefix} in {search_date}: {list(found_artifacts.keys())}"
                    )
                    return True, search_date, found_artifacts
                else:
                    self.court_logger.debug(f"Found JSON matching {pattern_prefix} in {search_date} but no other artifacts")
        
        self.court_logger.debug(f"No existing artifacts found matching pattern {pattern_prefix} across past 7 days: {search_dates}")
        return False, None, {}
    
    def _check_json_flags(self, json_path: str) -> Tuple[bool, bool, Optional[Dict]]:
        """
        Check JSON file for html_only and is_transferred flags.
        
        Args:
            json_path: Path to JSON file
            
        Returns:
            Tuple of (html_only, is_transferred, transferor_info)
        """
        if not os.path.exists(json_path):
            return False, False, None
            
        try:
            with open(json_path, 'r') as f:
                data = json.load(f)
                
            html_only = data.get('html_only', False)
            is_transferred = data.get('is_transferred', False)
            
            transferor_info = None
            if is_transferred:
                transferor_info = {
                    'court_id': data.get('transferor_court_id'),
                    'docket_num': data.get('transferor_docket_num')
                }
                
            return html_only, is_transferred, transferor_info
            
        except Exception as e:
            self.court_logger.error(f"Error reading JSON file {json_path}: {e}", extra={"json_path": json_path})
            return False, False, None
    
    async def check_docket_exists(
        self, 
        court_id: str, 
        docket_num: str
    ) -> bool:
        """
        Check if a docket exists in the repository.
        
        Args:
            court_id: Court identifier
            docket_num: Docket number
            
        Returns:
            True if docket exists, False otherwise
        """
        if not self.pacer_repository:
            self.court_logger.warning("No repository available for checking transferred dockets", extra={"court_id": court_id, "docket_num": docket_num})
            return False
            
        try:
            exists = await self.pacer_repository.check_docket_exists(court_id, docket_num)
            return exists
        except Exception as e:
            self.court_logger.error(f"Error checking docket existence: {e}", extra={"court_id": court_id, "docket_num": docket_num})
            return False
    
    async def should_download_docket(
        self,
        court_id: str,
        docket_num: str,
        versus: str,
        iso_date: Optional[str] = None
    ) -> Tuple[bool, str]:
        """
        Determine if a docket should be downloaded based on existing artifacts.
        NOW CHECKS ARTIFACTS ACROSS THE PAST 7 DAYS.
        
        Args:
            court_id: Court identifier
            docket_num: Docket number
            versus: Case title
            iso_date: Optional ISO date, uses config if not provided
            
        Returns:
            Tuple of (should_download, reason)
        """
        # Get ISO date from parameter or config
        iso_date = iso_date or self.config.get('iso_date')
        if not iso_date:
            return True, "No ISO date available for checking"
        
        # Generate base filename
        base_filename = self._get_base_filename(court_id, docket_num, versus)
        
        # CRITICAL FIX: Check for existing artifacts across past 7 days instead of just today
        artifacts_found, found_date, found_paths = self._find_existing_artifacts_across_dates(
            base_filename, iso_date
        )
        
        # If no artifacts found across 7 days, needs download
        if not artifacts_found:
            self.court_logger.info(f"No artifacts found for {base_filename} across past 7 days, needs download", 
                         extra={"court_id": court_id, "docket_num": docket_num, "base_filename": base_filename})
            return True, "No artifacts found across past 7 days"
        
        # We found artifacts - now check JSON flags from the found file
        json_path = found_paths.get('json')
        if not json_path:
            # This shouldn't happen since _find_existing_artifacts_across_dates requires JSON
            return True, "Found artifacts but no JSON path"
        
        # Check JSON flags from the found artifact date
        html_only, is_transferred, transferor_info = self._check_json_flags(json_path)
        
        # Rule 1: If html_only is true, skip download
        if html_only:
            self.court_logger.info(f"Skipping {base_filename}: html_only flag is true (found in {found_date})", 
                         extra={"court_id": court_id, "docket_num": docket_num, "base_filename": base_filename, "found_date": found_date})
            return False, f"html_only flag is true (found in {found_date})"
        
        # Rule 2: If transferred and transferor exists, skip
        if is_transferred and transferor_info:
            if transferor_info['court_id'] and transferor_info['docket_num']:
                transferor_exists = await self.check_docket_exists(
                    transferor_info['court_id'],
                    transferor_info['docket_num']
                )
                if transferor_exists:
                    self.court_logger.info(
                        f"Skipping {base_filename}: transferred case with existing transferor "
                        f"{transferor_info['court_id']}/{transferor_info['docket_num']} (found in {found_date})",
                        extra={
                            "court_id": court_id, 
                            "docket_num": docket_num,
                            "base_filename": base_filename,
                            "transferor_court_id": transferor_info['court_id'],
                            "transferor_docket_num": transferor_info['docket_num'],
                            "found_date": found_date
                        }
                    )
                    return False, f"Transferred case with existing transferor (found in {found_date})"
        
        # Rule 3: We already know artifacts exist (PDF, ZIP, or MD) from the search
        artifact_types = [key.upper() for key in found_paths.keys() if key != 'json']
        
        self.court_logger.info(
            f"Skipping {base_filename}: artifacts found in {found_date} ({', '.join(artifact_types)})",
            extra={
                "court_id": court_id,
                "docket_num": docket_num,
                "base_filename": base_filename,
                "artifacts_found": artifact_types,
                "found_date": found_date,
                "found_paths": list(found_paths.keys())
            }
        )
        return False, f"Artifacts found in {found_date}: {', '.join(artifact_types)}"
    
    async def check_batch(
        self,
        docket_items: list,
        iso_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Check a batch of dockets from a report log.
        
        Args:
            docket_items: List of docket dictionaries from report
            iso_date: Optional ISO date
            
        Returns:
            Dictionary with download/skip statistics and lists
        """
        iso_date = iso_date or self.config.get('iso_date')
        
        results = {
            'total': len(docket_items),
            'to_download': [],
            'to_skip': [],
            'skip_reasons': {
                'html_only': [],
                'transferred': [],
                'has_artifacts': [],
                'no_json': []
            }
        }
        
        for item in docket_items:
            court_id = item.get('court_id', '')
            docket_num = item.get('docket_num', '')
            # docket_report_log only has 'versus' field
            versus = item.get('versus', '')
            
            # Only require court_id and docket_num; versus is optional but helpful
            if not court_id or not docket_num:
                self.court_logger.warning(
                    f"Skipping item with missing required fields (court_id or docket_num): "
                    f"court_id='{court_id}', docket_num='{docket_num}', versus='{versus}'",
                    extra={"item": item}
                )
                continue
            
            # If versus is truly empty, skip this item - no fallback needed
            if not versus or versus.strip() == '':
                self.court_logger.warning(
                    f"Skipping item with empty versus field: {court_id}/{docket_num}",
                    extra={"court_id": court_id, "docket_num": docket_num, "item": item}
                )
                continue
            
            should_download, reason = await self.should_download_docket(
                court_id, docket_num, versus, iso_date
            )
            
            item_info = {
                'court_id': court_id,
                'docket_num': docket_num,
                'versus': versus,
                'reason': reason
            }
            
            if should_download:
                results['to_download'].append(item_info)
            else:
                results['to_skip'].append(item_info)
                
                # Categorize skip reasons
                if 'html_only' in reason:
                    results['skip_reasons']['html_only'].append(item_info)
                elif 'Transferred' in reason:
                    results['skip_reasons']['transferred'].append(item_info)
                elif 'Artifacts' in reason:
                    results['skip_reasons']['has_artifacts'].append(item_info)
                elif 'JSON file not found' in reason:
                    results['skip_reasons']['no_json'].append(item_info)
        
        # Log summary
        self.court_logger.info(f"Artifact check summary: {len(results['to_download'])} to download, "
                     f"{len(results['to_skip'])} to skip out of {results['total']} total",
                     extra={
                         "total": results['total'],
                         "to_download": len(results['to_download']),
                         "to_skip": len(results['to_skip']),
                         "iso_date": iso_date
                     })
        
        return results