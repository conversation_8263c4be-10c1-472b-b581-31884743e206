# /src/services/pacer/_download_components/s3_manager.py
import asyncio
import os
import re
from typing import Any, Dict, List, Optional

from src.infrastructure.patterns.component_base import ComponentImplementation


class S3Manager(ComponentImplementation):
    """
    Component for managing interactions with S3, including uploading HTML,
    constructing links, and verifying file existence.
    
    Features comprehensive court_logger integration:
    - Logs all S3 upload attempts with court_id, docket_num, and S3 keys
    - Tracks upload success/failure with detailed error context
    - Records S3 search operations and file discovery
    - Provides visibility into upload paths, CDN URLs, and file sizes
    - Supports both initialization-time and runtime court_logger injection
    
    All S3 operations are logged with structured data including:
    - court_id: Court identifier for filtering
    - docket_num: Case docket number for context
    - s3_key: Full S3 object key being operated on
    - upload_path: Complete S3 path for operations
    - cdn_url: Generated CDN URL for access
    - content_size_bytes: Size of content being uploaded
    - operation: Type of S3 operation (html_upload, find_html_link, etc.)
    - error_type: Categorized error types for analysis
    
    Logs are directed to: data/{iso_date}/logs/pacer/{court_id}.log
    """

    def __init__(
        self, 
        logger: Any = None, 
        config: Optional[Dict] = None, 
        s3_async_storage: Optional[Any] = None,
        court_logger: Optional[Any] = None
    ):
        """
        Initialize S3Manager with optional court_logger.
        
        Args:
            logger: Standard logger instance
            config: Configuration dictionary
            s3_async_storage: S3 async storage client
            court_logger: Optional court-specific logger to override standard logger
        """
        super().__init__(logger, config)
        self.s3_async_storage = s3_async_storage
        
        # Use court_logger if provided, otherwise fall back to standard logger
        if court_logger:
            self.logger = court_logger
            self.log_debug("S3Manager initialized with court_logger", {
                "logger_name": court_logger.name if hasattr(court_logger, 'name') else 'unknown'
            })
    
    def set_court_logger(self, court_logger: Any) -> None:
        """
        Set or update the court logger for this S3Manager instance.
        
        This allows parent components to update the logger after initialization
        to ensure all S3 operations are logged to the appropriate court log file.
        
        Args:
            court_logger: Court-specific logger instance
        """
        if court_logger:
            self.logger = court_logger
            self.log_debug("S3Manager court_logger updated", {
                "logger_name": court_logger.name if hasattr(court_logger, 'name') else 'unknown'
            })
    
    def create_court_logger_context(self, court_id: str, iso_date: str):
        """
        Create a court logging context for S3 operations.
        
        This ensures all S3 operations within the context are logged to the
        court-specific log file at data/{iso_date}/logs/pacer/{court_id}.log
        
        Args:
            court_id: Court identifier (e.g., 'cand', 'nysd')
            iso_date: ISO date string for directory organization
            
        Returns:
            Context manager that yields the court logger
        """
        return self.create_court_logging_context(court_id, iso_date)

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes an S3 management action.

        Args:
            data (Dict): A dictionary containing action and parameters.
                - action: 'upload_html', 'find_html_link'
                - ... other params based on action

        Returns:
            Depends on the action.
            - upload_html: bool (success)
            - find_html_link: Optional[str] (the verified S3 URL)
        """
        action = data.get("action")
        court_logger = data.get("court_logger", self.logger)
        
        if action == "upload_html":
            return await self.upload_html_to_s3(
                case_details=data.get("case_details", {}),
                html_content=data.get("html_content", ""),
                json_path=data.get("json_path", ""),
            )
        elif action == "find_html_link":
            return await self.find_s3_html_link(
                case_details=data.get("case_details", {}),
                json_path=data.get("json_path", ""),
            )
        else:
            raise ValueError(f"Unknown S3Manager action: {action}")

    async def upload_html_to_s3(
        self, case_details: Dict[str, Any], html_content: str, json_path: str
    ) -> bool:
        """
        Uploads HTML content to an S3 bucket with the correct key format.

        Args:
            case_details: Dictionary containing case details like 'base_filename'.
            html_content: The HTML content string to upload.
            json_path: The path to the JSON file, used to extract the date.

        Returns:
            True if upload was successful, False otherwise.
        """
        # Extract identifiers for logging context
        court_id = case_details.get("court_id", "unknown")
        docket_num = case_details.get("docket_num", "unknown")
        base_filename = case_details.get("base_filename", "unknown")
        
        self.log_info(f"[S3_UPLOAD_START] Starting HTML upload for case", {
            "court_id": court_id,
            "docket_num": docket_num,
            "base_filename": base_filename,
            "json_path": json_path,
            "content_size_bytes": len(html_content) if html_content else 0
        })
        
        if not self.s3_async_storage:
            self.log_error(f"[S3_UPLOAD_ERROR] S3 async storage unavailable for {court_id} case {docket_num}", {
                "court_id": court_id,
                "docket_num": docket_num,
                "error_type": "storage_unavailable"
            })
            return False

        try:
            # Extract date from json_path (e.g., 'data/YYYYMMDD/dockets/file.json')
            date_match = re.search(r"data/(\d{8})/dockets/", json_path)
            if not date_match:
                self.log_error(f"[S3_UPLOAD_ERROR] Cannot extract date from json_path", {
                    "court_id": court_id,
                    "docket_num": docket_num,
                    "json_path": json_path,
                    "error_type": "date_extraction_failed"
                })
                return False
            iso_date = date_match.group(1)

            if not base_filename or base_filename == "unknown":
                self.log_error(f"[S3_UPLOAD_ERROR] Missing base_filename for {court_id} case {docket_num}", {
                    "court_id": court_id,
                    "docket_num": docket_num,
                    "case_details": case_details,
                    "error_type": "missing_base_filename"
                })
                return False

            # Construct the S3 key with correct format: {iso_date}/html/{base_filename}.html
            s3_key = f"{iso_date}/html/{base_filename}.html"
            
            self.log_info(f"[S3_UPLOAD_ATTEMPT] Uploading HTML content", {
                "court_id": court_id,
                "docket_num": docket_num,
                "s3_key": s3_key,
                "iso_date": iso_date,
                "base_filename": base_filename,
                "content_size_bytes": len(html_content),
                "upload_path": f"s3://bucket/{s3_key}"
            })

            # Upload using the appropriate async method
            if hasattr(self.s3_async_storage, "upload_html_string_async"):
                success = await self.s3_async_storage.upload_html_string_async(
                    html_content, s3_key
                )
            else:
                # Fallback to run synchronous method in an executor
                loop = asyncio.get_running_loop()
                success = await loop.run_in_executor(
                    None, self.s3_async_storage.upload_html_string, html_content, s3_key
                )

            if success:
                # Generate CDN link after successful upload
                s3_html = f"https://cdn.lexgenius.ai/{s3_key}"
                
                self.log_info(f"[S3_UPLOAD_SUCCESS] HTML successfully uploaded", {
                    "court_id": court_id,
                    "docket_num": docket_num,
                    "s3_key": s3_key,
                    "upload_path": f"s3://bucket/{s3_key}",
                    "cdn_url": s3_html,
                    "content_size_bytes": len(html_content),
                    "operation": "html_upload"
                })
                
                return {
                    'success': True,
                    's3_key': s3_key, 
                    's3_html': s3_html,
                    'upload_path': f"s3://bucket/{s3_key}"
                }
            else:
                self.log_error(f"[S3_UPLOAD_FAILURE] Upload operation returned false", {
                    "court_id": court_id,
                    "docket_num": docket_num,
                    "s3_key": s3_key,
                    "upload_path": f"s3://bucket/{s3_key}",
                    "error_type": "upload_returned_false",
                    "operation": "html_upload"
                })
                return {'success': False, 'error': 'Upload operation returned false'}
        except Exception as e:
            self.log_error(f"[S3_UPLOAD_EXCEPTION] Exception during HTML upload for {court_id} case {docket_num}", {
                "court_id": court_id,
                "docket_num": docket_num,
                "s3_key": s3_key if 's3_key' in locals() else 'not_constructed',
                "error": str(e),
                "error_type": type(e).__name__,
                "json_path": json_path,
                "operation": "html_upload"
            }, exc_info=True)
            return {'success': False, 'error': str(e)}

    async def find_s3_html_link(
        self, case_details: Dict[str, Any], json_path: str
    ) -> Optional[str]:
        """
        Finds and verifies the S3 HTML link for a case. It first tries to construct
        a URL and then verifies it by searching S3.

        This combines logic from PacerHTMLProcessingService and DataUpdaterService.

        Args:
            case_details: Case details for building search patterns.
            json_path: Path to the JSON file for context.

        Returns:
            A verified S3 CDN URL, or None if not found.
        """
        # Extract identifiers for logging context
        court_id = case_details.get("court_id", "unknown").strip()
        docket_num = case_details.get("docket_num", "unknown").strip()
        
        self.log_info(f"[S3_SEARCH_START] Starting S3 HTML link search", {
            "court_id": court_id,
            "docket_num": docket_num,
            "json_path": json_path,
            "operation": "find_html_link"
        })
        
        if not self.s3_async_storage:
            self.log_error(f"[S3_SEARCH_ERROR] S3 async storage unavailable for {court_id} case {docket_num}", {
                "court_id": court_id,
                "docket_num": docket_num,
                "error_type": "storage_unavailable",
                "operation": "find_html_link"
            })
            return None

        try:
            if not court_id or not docket_num or court_id == "unknown" or docket_num == "unknown":
                self.log_warning(f"[S3_SEARCH_WARNING] Missing identifiers for S3 HTML search", {
                    "court_id": court_id,
                    "docket_num": docket_num,
                    "json_path": json_path,
                    "error_type": "missing_identifiers"
                })
                return None

            # Extract YY: two digits after the colon
            year_match = re.search(r":(\d{2})", docket_num)
            # Extract NNNNN: 5 consecutive digits after the case type
            num_match = re.search(r"-[a-zA-Z]{2}-(\d{5})", docket_num)

            if not year_match or not num_match:
                self.log_warning(f"[S3_SEARCH_WARNING] Cannot extract YY/NNNNN pattern from docket_num", {
                    "court_id": court_id,
                    "docket_num": docket_num,
                    "year_match": bool(year_match),
                    "num_match": bool(num_match),
                    "error_type": "pattern_extraction_failed"
                })
                return None

            search_pattern = f"{court_id}_{year_match.group(1)}_{num_match.group(2)}"
            
            # Determine the directory to search in S3
            config_date = self.config.get("iso_date")
            if not config_date:
                self.log_error(f"[S3_SEARCH_ERROR] Config iso_date not set for {court_id} case", {
                    "court_id": court_id,
                    "docket_num": docket_num,
                    "search_pattern": search_pattern,
                    "error_type": "missing_iso_date"
                })
                return None
            s3_html_dir_path = f"{config_date}/html/"

            self.log_info(f"[S3_SEARCH_ATTEMPT] Searching S3 for HTML files", {
                "court_id": court_id,
                "docket_num": docket_num,
                "search_pattern": search_pattern,
                "s3_directory": s3_html_dir_path,
                "iso_date": config_date,
                "operation": "directory_listing"
            })

            # List files in the directory
            if hasattr(self.s3_async_storage, "list_existing_files_async"):
                s3_keys = await self.s3_async_storage.list_existing_files_async(prefix=s3_html_dir_path)
            else:
                loop = asyncio.get_running_loop()
                s3_keys = await loop.run_in_executor(
                    None, self.s3_async_storage.list_existing_files, s3_html_dir_path
                )

            self.log_debug(f"[S3_SEARCH_LISTING] Found {len(s3_keys)} files in S3 directory", {
                "court_id": court_id,
                "docket_num": docket_num,
                "s3_directory": s3_html_dir_path,
                "files_found": len(s3_keys),
                "search_pattern": search_pattern
            })

            # Find a file matching the pattern (case-insensitive)
            search_pattern_lower = search_pattern.lower()
            for s3_key in s3_keys:
                s3_filename = os.path.basename(s3_key)
                if s3_filename.lower().startswith(search_pattern_lower):
                    cdn_url = f"https://cdn.lexgenius.ai/{s3_key}"
                    
                    self.log_info(f"[S3_SEARCH_SUCCESS] Found matching S3 HTML file", {
                        "court_id": court_id,
                        "docket_num": docket_num,
                        "s3_key": s3_key,
                        "s3_filename": s3_filename,
                        "search_pattern": search_pattern,
                        "cdn_url": cdn_url,
                        "operation": "html_link_found"
                    })
                    return cdn_url

            self.log_warning(f"[S3_SEARCH_NOT_FOUND] No matching S3 HTML file found", {
                "court_id": court_id,
                "docket_num": docket_num,
                "search_pattern": search_pattern,
                "s3_directory": s3_html_dir_path,
                "files_searched": len(s3_keys),
                "error_type": "no_matching_file"
            })
            return None

        except Exception as e:
            self.log_error(f"[S3_SEARCH_EXCEPTION] Exception during S3 HTML search for {court_id} case {docket_num}", {
                "court_id": court_id,
                "docket_num": docket_num,
                "search_pattern": search_pattern if 'search_pattern' in locals() else 'not_constructed',
                "s3_directory": s3_html_dir_path if 's3_html_dir_path' in locals() else 'not_constructed',
                "error": str(e),
                "error_type": type(e).__name__,
                "json_path": json_path,
                "operation": "find_html_link"
            }, exc_info=True)
            return None
