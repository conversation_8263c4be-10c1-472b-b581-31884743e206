# /src/services/pacer/_download_components/download_validator.py
from __future__ import annotations
from typing import Any, Dict, Tuple, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.pacer.components.download.artifact_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol
    from src.repositories.pacer_repository import PacerRepository

class DownloadValidator(ComponentImplementation):
    """Validates if a download should be skipped based on case details and config."""

    def __init__(self, 
                 logger: LoggerProtocol = None, 
                 config: Dict[str, Any] = None,
                 pacer_repository: Optional['PacerRepository'] = None):
        super().__init__(logger, config)
        
        # Initialize artifact checker with repository for transfer checks
        self.artifact_checker = DocketArtifactChecker(
            logger=logger,
            config=config or {},
            pacer_repository=pacer_repository
        )

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'should_skip_download':
            return self.should_skip_download(data['case_details'])
        elif action == 'validate_case_for_download':
            return await self.validate_case_for_download(
                data['case_details'],
                data.get('is_explicitly_requested', False)
            )
        else:
            raise PacerServiceError(f"Unknown action for DownloadValidator: {action}")

    def should_skip_download(self, case_details: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Determines if a case should skip PDF download.
        """
        # This is a simplified version of the logic in PacerDownloadOrchestrationService.
        # A full implementation would also check ignore_download service and other configs.

        self.log_info(f"Validating download for docket: {case_details.get('docket_num')}")

        if self.config.get("html_only", False):
            return True, "HTML_ONLY mode is enabled in the configuration."

        mdl_flags_config = self.config.get("mdl_flags")
        if mdl_flags_config:
            case_flags = case_details.get("flags", [])
            for config_flag in mdl_flags_config:
                if str(config_flag).strip() in [str(cf).strip() for cf in case_flags]:
                    return True, f"MDL flag match: {config_flag}"

        return False, "Full download required."

    async def validate_case_for_download(self, 
                                       case_details: Dict[str, Any], 
                                       is_explicitly_requested: bool = False) -> Dict[str, Any]:
        """
        Enhanced validation that includes artifact checking.
        
        Args:
            case_details: Case details dictionary containing court_id, docket_num, versus
            is_explicitly_requested: Whether this download was explicitly requested
            
        Returns:
            Dictionary with validation results: {'should_download': bool, 'skip_reason': str}
        """
        # First, perform the basic validation checks
        should_skip_basic, basic_reason = self.should_skip_download(case_details)
        
        if should_skip_basic:
            self.log_info(f"Basic validation failed: {basic_reason}")
            return {
                'should_download': False,
                'skip_reason': basic_reason
            }
        
        # If explicitly requested, skip artifact checking (user override)
        if is_explicitly_requested:
            self.log_info("Download explicitly requested, bypassing artifact check")
            return {
                'should_download': True,
                'skip_reason': None
            }
        
        # Enhanced validation: Check artifacts using DocketArtifactChecker
        court_id = case_details.get('court_id', '')
        docket_num = case_details.get('docket_num', '')
        versus = case_details.get('versus', case_details.get('title', ''))
        
        if not all([court_id, docket_num]):
            self.log_warning(f"Missing required data for artifact check: court_id={court_id}, docket_num={docket_num}")
            return {
                'should_download': True,
                'skip_reason': None
            }
        
        try:
            # Call the artifact checker to determine if download is needed
            should_download, artifact_reason = await self.artifact_checker.should_download_docket(
                court_id=court_id,
                docket_num=docket_num,
                versus=versus
            )
            
            if not should_download:
                self.log_info(f"Artifact check blocked download: {artifact_reason}")
                return {
                    'should_download': False,
                    'skip_reason': f"Artifact check: {artifact_reason}"
                }
            
            self.log_info(f"Artifact check passed: {artifact_reason}")
            return {
                'should_download': True,
                'skip_reason': None
            }
            
        except Exception as e:
            self.log_error(f"Artifact checking failed: {str(e)}", exc_info=True)
            # On error, allow download to proceed (fail-safe behavior)
            return {
                'should_download': True,
                'skip_reason': None
            }
