# /src/services/pacer/_download_components/file_downloader.py
from __future__ import annotations
import async<PERSON>
from pathlib import Path
from typing import Any, Dict, TYPE_CHECKING, Optional

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.pacer.components.navigator import PacerNavigator
    from src.infrastructure.protocols.logger import LoggerProtocol
    from playwright.async_api import Download as PlaywrightDownload

class FileDownloader(ComponentImplementation):
    """Handles the actual file download process."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'download_file':
            return await self.download_file(data['navigator'], data['case_details'])
        elif action == 'download_documents':
            return await self.download_documents(
                case_details=data['case_details'],
                download_context=data['download_context']
            )
        else:
            raise PacerServiceError(f"Unknown action for FileDownloader: {action}")

    async def download_file(self, navigator: Any, case_details: Dict[str, Any]) -> Optional[str]:
        """
        Enhanced file download process with better error handling and multiple download strategies.
        Handles PDF, ZIP, and other document formats from PACER docket sheets.
        """
        log_prefix = f"[{case_details.get('court_id', 'N/A')}][{case_details.get('docket_num', 'N/A')}]"
        self.log_info(f"{log_prefix} Starting enhanced file download.")

        if not hasattr(navigator, 'page') or not navigator.page:
            self.log_error(f"{log_prefix} Invalid navigator or page object")
            return None

        page = navigator.page
        download_path = Path(self.config.get("temp_download_dir", "/tmp/downloads"))
        download_path.mkdir(parents=True, exist_ok=True)

        try:
            # Strategy 1: Look for document links in order of preference
            download_selectors = [
                "a[href*='doc1']",           # Primary document link
                "a[href*='.pdf']",           # Direct PDF links
                "a[href*='docketreport']",   # Docket report downloads
                "a[href*='download']",       # Generic download links
                "a:has-text('PDF')",         # Links containing PDF text
                "input[type='submit'][value*='Download']",  # Download buttons
                "button:has-text('Download')"  # Download buttons
            ]
            
            download_element = None
            used_selector = None
            
            for selector in download_selectors:
                try:
                    elements = await page.locator(selector).all()
                    if elements:
                        download_element = elements[0]  # Take the first match
                        used_selector = selector
                        self.log_info(f"{log_prefix} Found download element with selector: {selector}")
                        break
                except Exception as e:
                    self.log_debug(f"{log_prefix} Selector {selector} failed: {e}")
                    continue
            
            if not download_element:
                # Strategy 2: Look for any clickable element that might trigger download
                self.log_warning(f"{log_prefix} No direct download links found, trying fallback approach")
                
                fallback_selectors = [
                    "a[onclick*='download']",
                    "a[href*='cgi-bin']",
                    "form input[type='submit']"
                ]
                
                for selector in fallback_selectors:
                    try:
                        elements = await page.locator(selector).all()
                        if elements:
                            download_element = elements[0]
                            used_selector = f"fallback:{selector}"
                            self.log_info(f"{log_prefix} Found fallback download element: {selector}")
                            break
                    except Exception as e:
                        self.log_debug(f"{log_prefix} Fallback selector {selector} failed: {e}")
                        continue
            
            if not download_element:
                self.log_error(f"{log_prefix} No download elements found on page")
                return None
            
            # Strategy 3: Attempt download with proper error handling and retry
            download_success = False
            download_attempts = 3
            base_delay = 2.0
            
            for download_attempt in range(download_attempts):
                try:
                    # Set up download expectations with timeout
                    timeout = 30000 + (download_attempt * 10000)  # Increase timeout with attempts
                    async with page.expect_download(timeout=timeout) as download_info:
                        await download_element.click()
                        self.log_info(f"{log_prefix} Clicked download element ({used_selector}) - attempt {download_attempt + 1}")

                    download = await download_info.value
                    download_success = True
                    break
                    
                except Exception as download_e:
                    self.log_warning(f"{log_prefix} Download attempt {download_attempt + 1} failed: {download_e}")
                    
                    if download_attempt < download_attempts - 1:
                        # Exponential backoff
                        delay = base_delay * (2 ** download_attempt)
                        self.log_info(f"{log_prefix} Retrying download in {delay} seconds...")
                        await asyncio.sleep(delay)
                        
                        # Re-find element for retry
                        try:
                            for selector in download_selectors:
                                elements = await page.locator(selector).all()
                                if elements:
                                    download_element = elements[0]
                                    used_selector = selector
                                    break
                        except Exception:
                            continue
                    else:
                        # All attempts failed, try alternative methods
                        self.log_error(f"{log_prefix} All {download_attempts} download attempts failed")
                        download_success = False
                        break
            
            if download_success:
                
                # Generate appropriate filename
                base_filename = case_details.get('base_filename', f"download_{self._generate_timestamp()}")
                suggested_name = download.suggested_filename or f"{base_filename}.pdf"
                
                # Clean filename for filesystem
                clean_filename = self._sanitize_filename(suggested_name)
                download_file_path = download_path / clean_filename
                
                # Save the downloaded file with retry logic
                save_success = False
                save_attempts = 3
                
                for save_attempt in range(save_attempts):
                    try:
                        await download.save_as(download_file_path)
                        
                        # Verify download was successful
                        if download_file_path.exists() and download_file_path.stat().st_size > 0:
                            file_size = download_file_path.stat().st_size
                            self.log_info(f"{log_prefix} File downloaded successfully: {download_file_path} ({file_size} bytes) - attempt {save_attempt + 1}")
                            return str(download_file_path)
                        else:
                            self.log_warning(f"{log_prefix} Downloaded file is empty or missing after save attempt {save_attempt + 1}")
                            
                            if save_attempt < save_attempts - 1:
                                await asyncio.sleep(1.0)
                                continue
                            else:
                                self.log_error(f"{log_prefix} Downloaded file is empty or missing after all attempts")
                                return None
                                
                    except Exception as save_e:
                        self.log_warning(f"{log_prefix} Save attempt {save_attempt + 1} failed: {save_e}")
                        
                        if save_attempt < save_attempts - 1:
                            await asyncio.sleep(1.0)
                        else:
                            self.log_error(f"{log_prefix} All save attempts failed")
                            return None
            
            else:
                self.log_error(f"{log_prefix} Download execution failed after all attempts")
                
                # Strategy 4: Try alternative download methods with retry
                alt_attempts = 2
                for alt_attempt in range(alt_attempts):
                    try:
                        self.log_info(f"{log_prefix} Attempting alternative download method (attempt {alt_attempt + 1})...")
                        
                        # Sometimes PACER requires form submission instead of direct link click
                        form_element = await page.query_selector("form")
                        if form_element:
                            # Add timeout to form submission
                            await asyncio.wait_for(
                                form_element.evaluate("form => form.submit()"),
                                timeout=10.0
                            )
                            
                            # Wait for response with timeout and retry
                            await asyncio.wait_for(
                                page.wait_for_load_state('networkidle'),
                                timeout=20.0
                            )
                            
                            # Check if this resulted in a download or redirect
                            current_url = page.url
                            if 'download' in current_url.lower() or current_url.endswith('.pdf'):
                                self.log_info(f"{log_prefix} Form submission may have initiated download")
                                break
                            
                    except Exception as alt_e:
                        self.log_debug(f"{log_prefix} Alternative download attempt {alt_attempt + 1} failed: {alt_e}")
                        
                        if alt_attempt < alt_attempts - 1:
                            await asyncio.sleep(2.0)
                
                return None

        except Exception as e:
            self.log_error(f"{log_prefix} Critical error during file download: {e}", exc_info=True)
            
            # Take screenshot for debugging
            try:
                screenshot_path = download_path / f"download_error_{self._generate_timestamp()}.png"
                await page.screenshot(path=str(screenshot_path))
                self.log_info(f"{log_prefix} Error screenshot saved: {screenshot_path}")
            except Exception as screenshot_e:
                self.log_debug(f"{log_prefix} Could not save error screenshot: {screenshot_e}")
            
            return None

    async def download_documents(self, case_details: Dict[str, Any], download_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced method for downloading multiple document types with context.
        
        Args:
            case_details: Case details dictionary
            download_context: Download context from orchestrator
            
        Returns:
            Download results with metadata
        """
        log_prefix = f"[{case_details.get('court_id', 'N/A')}][{case_details.get('docket_num', 'N/A')}]"
        
        try:
            navigator = download_context.get('navigator') or download_context.get('page')
            if not navigator:
                return {
                    'success': False,
                    'error': 'No navigator or page provided in download context',
                    'files': [],
                    'metadata': {}
                }
            
            # Attempt primary document download
            downloaded_file = await self.download_file(navigator, case_details)
            
            result = {
                'success': downloaded_file is not None,
                'files': [downloaded_file] if downloaded_file else [],
                'metadata': {
                    'download_method': 'enhanced_file_downloader',
                    'timestamp': self._generate_timestamp(),
                    'case_details': case_details,
                    'download_context_keys': list(download_context.keys())
                }
            }
            
            if downloaded_file:
                # Add file metadata
                file_path = Path(downloaded_file)
                result['metadata'].update({
                    'file_size': file_path.stat().st_size if file_path.exists() else 0,
                    'file_extension': file_path.suffix,
                    'original_filename': file_path.name
                })
                
                self.log_info(f"{log_prefix} Document download completed successfully")
            else:
                result['error'] = 'Download failed - no file returned'
                self.log_warning(f"{log_prefix} Document download failed")
            
            return result
            
        except Exception as e:
            self.log_error(f"{log_prefix} Error in download_documents: {e}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'files': [],
                'metadata': {'error_type': type(e).__name__}
            }

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility."""
        import re
        # Remove or replace problematic characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Limit length
        if len(sanitized) > 255:
            name, ext = Path(sanitized).stem, Path(sanitized).suffix
            sanitized = name[:255-len(ext)] + ext
        return sanitized

    def _generate_timestamp(self) -> str:
        """Generate timestamp string for filenames."""
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d_%H%M%S")
