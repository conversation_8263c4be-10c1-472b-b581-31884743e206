"""
MDL Manager Component

Handles Multi-District Litigation (MDL) specific logic including:
- MDL number to court ID mapping
- MDL-specific transfer rules
- Pending CTO (Conditional Transfer Order) logic
"""

import logging
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.repositories.district_courts_repository import DistrictCourtsRepository


class MDLManager(ComponentImplementation):
    """
    Component for managing MDL (Multi-District Litigation) cases.
    Handles MDL lookups, transfer rules, and pending CTO logic.
    """

    # Null condition constants
    NULL_CONDITIONS = [
        None, "", "NULL", "null", "N/A", "n/a", "NA", "na", "None", "none"
    ]

    # Special MDL numbers (test/placeholder cases)
    SPECIAL_MDL_NUMBERS = ["25", "9000"]

    def __init__(
        self,
        logger: Any = None,
        config: Optional[Dict] = None,
        district_court_repository: Optional[DistrictCourtsRepository] = None,
    ):
        super().__init__(logger, config)
        self.district_court_repository = district_court_repository
        self._mdl_lookup_cache = None

    async def _execute_action(self, data: Any) -> Any:
        """Execute MDL management actions."""
        action = data.get("action")
        
        if action == "create_mdl_lookup":
            return await self.create_mdl_lookup()
        elif action == "get_mdl_court":
            mdl_num = data.get("mdl_num")
            return await self.get_mdl_court_id(mdl_num)
        elif action == "is_special_mdl":
            mdl_num = data.get("mdl_num")
            return self.is_special_mdl(mdl_num)
        elif action == "check_pending_cto":
            case_details = data.get("case_details")
            return await self.check_pending_cto(case_details)
        else:
            raise ValueError(f"Unknown action for MDLManager: {action}")

    async def create_mdl_lookup(self) -> Dict[str, str]:
        """
        Create MDL number to court ID lookup dictionary.
        
        Returns:
            Dictionary mapping MDL numbers to court IDs
        """
        if self._mdl_lookup_cache is not None:
            return self._mdl_lookup_cache

        self.log_info("Creating MDL lookup dictionary...")

        if not self.district_court_repository:
            self.log_error(
                "❌ CRITICAL: No district court repository available for MDL lookup"
            )
            self._mdl_lookup_cache = {}
            return self._mdl_lookup_cache

        try:
            # Get all district court records
            self.log_info("🔍 Scanning DistrictCourts table for MDL mappings...")
            court_items = await self.district_court_repository.scan_all()
            self.log_info(f"📊 DistrictCourts scan returned {len(court_items)} items")

            mdl_lookup = {}
            total_mdl_entries = 0
            sample_mdls = []

            for court in court_items:
                mdl_num = court.get("mdl_num")
                court_id = court.get("court_id")

                if mdl_num and court_id and mdl_num not in self.NULL_CONDITIONS:
                    # Clean potential float artifact
                    mdl_num_str = str(mdl_num).replace(".0", "")
                    mdl_lookup[mdl_num_str] = court_id
                    total_mdl_entries += 1

                    # Collect sample MDLs for debugging
                    if len(sample_mdls) < 5:
                        sample_mdls.append(f"{mdl_num_str}→{court_id}")

            self.log_info(
                f"📊 Found {total_mdl_entries} total MDL entries. Sample: {sample_mdls}"
            )

            self._mdl_lookup_cache = mdl_lookup
            self.log_info(f"MDL lookup created with {len(mdl_lookup)} entries")

            return mdl_lookup

        except Exception as e:
            self.log_error(f"Error creating MDL lookup: {e}")
            self._mdl_lookup_cache = {}
            return self._mdl_lookup_cache

    async def get_mdl_court_id(self, mdl_num: Any) -> Optional[str]:
        """
        Get the court ID for an MDL number.
        
        Args:
            mdl_num: MDL number (can be string or number)
            
        Returns:
            Court ID if found, None otherwise
        """
        if mdl_num in self.NULL_CONDITIONS:
            return None

        # Ensure lookup is loaded
        mdl_lookup = await self.create_mdl_lookup()
        
        # Clean MDL number
        mdl_num_str = str(mdl_num).replace(".0", "")
        
        court_id = mdl_lookup.get(mdl_num_str)
        if court_id:
            self.log_info(f"Found MDL {mdl_num_str} → Court: {court_id}")
        else:
            self.log_debug(f"MDL {mdl_num_str} not found in lookup")
            
        return court_id

    def is_special_mdl(self, mdl_num: Any) -> bool:
        """
        Check if MDL number is a special test/placeholder case.
        
        Args:
            mdl_num: MDL number to check
            
        Returns:
            True if special MDL number
        """
        if mdl_num in self.NULL_CONDITIONS:
            return False
            
        mdl_num_str = str(mdl_num).replace(".0", "")
        return mdl_num_str in self.SPECIAL_MDL_NUMBERS

    async def check_pending_cto(self, case_details: Dict[str, Any]) -> bool:
        """
        Check if a case has pending CTO (Conditional Transfer Order).
        
        A case has pending CTO if:
        1. It has an MDL number
        2. It's not in the MDL court
        3. It's not already transferred
        4. It's not a special MDL case
        
        Args:
            case_details: Case details to check
            
        Returns:
            True if case has pending CTO
        """
        mdl_num = case_details.get("mdl_num")
        
        # No MDL = no pending CTO
        if mdl_num in self.NULL_CONDITIONS:
            return False
            
        # Special MDL cases never have pending CTO
        if self.is_special_mdl(mdl_num):
            self.log_info(f"Special MDL {mdl_num} - no pending CTO")
            return False
            
        # Get MDL court
        mdl_court_id = await self.get_mdl_court_id(mdl_num)
        if not mdl_court_id:
            return False
            
        current_court_id = case_details.get("court_id")
        
        # Check conditions for pending CTO
        is_in_mdl_court = current_court_id == mdl_court_id
        is_transferred = case_details.get("is_transferred", False)
        is_removal = case_details.get("is_removal", False)
        
        # For removals: pending CTO if has MDL and not in MDL court
        if is_removal:
            pending_cto = not is_in_mdl_court
            self.log_info(
                f"Removal case - MDL {mdl_num}, "
                f"in_mdl_court={is_in_mdl_court}, "
                f"pending_cto={pending_cto}"
            )
            return pending_cto
            
        # For transfers: no pending CTO (already transferred)
        if is_transferred:
            self.log_info(f"Already transferred - no pending CTO")
            return False
            
        # Default: pending if not in MDL court
        pending_cto = not is_in_mdl_court
        self.log_info(
            f"MDL {mdl_num}, current_court={current_court_id}, "
            f"mdl_court={mdl_court_id}, pending_cto={pending_cto}"
        )
        
        return pending_cto