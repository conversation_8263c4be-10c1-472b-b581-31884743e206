"""
Data Inheritor Component

Handles inheritance of data from transferor cases including:
- S3 link inheritance and CDN conversion
- MDL number inheritance
- Attorney and law firm data inheritance
- Initial filing date propagation
"""

import re
from typing import Any, Dict, Optional, List

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.repositories.pacer_repository import PacerRepository


class DataInheritor(ComponentImplementation):
    """
    Component for inheriting data from transferor cases.
    Handles S3 links, MDL numbers, attorneys, and law firms.
    """

    # Null condition constants
    NULL_CONDITIONS = [
        None, "", "NULL", "null", "N/A", "n/a", "NA", "na", "None", "none"
    ]

    def __init__(
        self,
        logger: Any = None,
        config: Optional[Dict] = None,
        pacer_repository: Optional[PacerRepository] = None,
    ):
        super().__init__(logger, config)
        self.pacer_repository = pacer_repository

    async def _execute_action(self, data: Any) -> Any:
        """Execute data inheritance actions."""
        action = data.get("action")
        
        if action == "inherit_from_transferor":
            case_details = data.get("case_details")
            return await self.inherit_from_transferor(case_details)
        elif action == "inherit_s3_link":
            case_details = data.get("case_details")
            return await self.inherit_s3_link(case_details)
        elif action == "convert_to_cdn":
            s3_link = data.get("s3_link")
            return self.convert_to_cdn_format(s3_link)
        elif action == "is_valid_s3_link":
            s3_link = data.get("s3_link")
            return self.is_valid_s3_link(s3_link)
        else:
            raise ValueError(f"Unknown action for DataInheritor: {action}")

    async def inherit_from_transferor(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Inherit all relevant data from transferor case.
        
        Args:
            case_details: Current case details
            
        Returns:
            Updated case details with inherited data
        """
        log_prefix = f"[{case_details.get('court_id', 'N/A')}][{case_details.get('docket_num', 'UNKNOWN')}]"
        
        # Check if we should inherit
        if not case_details.get("transferred_in"):
            self.log_debug(f"{log_prefix} Not a transferred-in case")
            return case_details
            
        # Check if we already have a valid S3 link
        current_s3_link = case_details.get("s3_link")
        if self.is_valid_s3_link(current_s3_link):
            self.log_debug(f"{log_prefix} Already has valid S3 link")
            return case_details
            
        transferor_court_id = case_details.get("transferor_court_id")
        transferor_docket_num = case_details.get("transferor_docket_num")
        
        if not transferor_court_id or not transferor_docket_num:
            self.log_debug(f"{log_prefix} No transferor information available")
            return case_details
            
        try:
            # Query transferor case
            self.log_info(
                f"{log_prefix} Querying transferor case {transferor_court_id}:{transferor_docket_num}"
            )
            
            if not self.pacer_repository:
                self.log_warning(f"{log_prefix} No repository available for inheritance")
                return case_details
                
            results = await self.pacer_repository.query_by_court_and_docket(
                transferor_court_id, transferor_docket_num
            )
            
            if not results:
                self.log_warning(f"{log_prefix} Transferor case not found in database")
                return case_details
                
            transferor_data = results[0]
            inherited_items = []
            
            # Inherit S3 link
            s3_link = transferor_data.get("s3_link")
            if s3_link and self.is_valid_s3_link(s3_link):
                cdn_link = self.convert_to_cdn_format(s3_link)
                case_details["s3_link"] = cdn_link
                inherited_items.append("S3 link")
                self.log_info(f"{log_prefix} ✅ Inherited S3 link: {cdn_link}")
                
                # Update filenames
                if "base_filename" in case_details:
                    case_details["original_filename"] = case_details["base_filename"]
                    case_details["new_filename"] = case_details["base_filename"]
                    
            # Inherit MDL number if needed
            transferor_mdl_num = transferor_data.get("mdl_num")
            if transferor_mdl_num and transferor_mdl_num not in self.NULL_CONDITIONS:
                current_mdl_num = case_details.get("mdl_num")
                if not current_mdl_num or current_mdl_num in self.NULL_CONDITIONS:
                    case_details["mdl_num"] = transferor_mdl_num
                    inherited_items.append("MDL number")
                    self.log_info(f"{log_prefix} ✅ Inherited MDL number: {transferor_mdl_num}")
                    
            # Inherit attorneys_gpt if needed
            transferor_attorneys = transferor_data.get("attorneys_gpt")
            if self._should_inherit_field(transferor_attorneys, case_details.get("attorneys_gpt")):
                case_details["attorneys_gpt"] = transferor_attorneys
                inherited_items.append("attorneys_gpt")
                self.log_info(f"{log_prefix} ✅ Inherited attorneys_gpt")
                
            # Inherit law_firm if needed
            transferor_law_firm = transferor_data.get("law_firm")
            if self._should_inherit_field(transferor_law_firm, case_details.get("law_firm")):
                case_details["law_firm"] = transferor_law_firm
                inherited_items.append("law_firm")
                self.log_info(f"{log_prefix} ✅ Inherited law_firm: {transferor_law_firm}")
                
            # Inherit law_firms array if needed
            transferor_law_firms = transferor_data.get("law_firms")
            if self._should_inherit_field(transferor_law_firms, case_details.get("law_firms")):
                case_details["law_firms"] = transferor_law_firms
                inherited_items.append("law_firms")
                self.log_info(f"{log_prefix} ✅ Inherited law_firms")
                
            # Set initial filing date from transferor
            filing_date = transferor_data.get("filing_date")
            if filing_date:
                case_details["initial_filing_date"] = filing_date
                inherited_items.append("initial_filing_date")
                self.log_info(f"{log_prefix} ✅ Set initial_filing_date: {filing_date}")
                
            # Add processing note
            if inherited_items:
                notes = case_details.get("_processing_notes", "")
                inheritance_note = f"{', '.join(inherited_items)} inherited from transferor."
                case_details["_processing_notes"] = f"{notes} {inheritance_note}".strip()
                
        except Exception as e:
            self.log_error(f"{log_prefix} Error inheriting from transferor: {e}")
            
        return case_details

    async def inherit_s3_link(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Inherit only S3 link from transferor case.
        
        Args:
            case_details: Current case details
            
        Returns:
            Updated case details with S3 link if found
        """
        # Just inherit S3 link specifically
        updated_details = await self.inherit_from_transferor(case_details)
        return updated_details

    def is_valid_s3_link(self, s3_link: Any) -> bool:
        """
        Check if S3 link is valid.
        
        Args:
            s3_link: Link to validate
            
        Returns:
            True if valid S3/CDN link
        """
        if not s3_link or not isinstance(s3_link, str):
            return False
            
        # Must be a PDF link
        if not s3_link.lower().endswith(".pdf"):
            return False
            
        # Must be from our domain or S3
        valid_domains = ["cdn.lexgenius.ai", "lexgenius.s3", "amazonaws.com"]
        return any(domain in s3_link for domain in valid_domains)

    def convert_to_cdn_format(self, s3_link: str) -> str:
        """
        Convert S3 link to CDN format.
        
        Args:
            s3_link: S3 link to convert
            
        Returns:
            CDN formatted link
        """
        # Already CDN format
        if "cdn.lexgenius.ai" in s3_link:
            return s3_link
            
        # Convert from S3 format
        s3_pattern = r"https://lexgenius\.s3\.us-west-2\.amazonaws\.com/(.+)"
        match = re.match(s3_pattern, s3_link)
        if match:
            path = match.group(1)
            return f"https://cdn.lexgenius.ai/{path}"
            
        # Try to extract path from other S3 formats
        if "amazonaws.com" in s3_link:
            parts = s3_link.split("amazonaws.com/")
            if len(parts) > 1:
                path = parts[1]
                return f"https://cdn.lexgenius.ai/{path}"
                
        return s3_link

    def _should_inherit_field(self, transferor_value: Any, current_value: Any) -> bool:
        """
        Check if a field should be inherited from transferor.
        
        Args:
            transferor_value: Value from transferor case
            current_value: Current case value
            
        Returns:
            True if should inherit
        """
        # Don't inherit if transferor doesn't have value
        if transferor_value in self.NULL_CONDITIONS:
            return False
            
        # Inherit if current doesn't have value
        if current_value in self.NULL_CONDITIONS:
            return True
            
        # Check for empty lists
        if isinstance(current_value, list) and len(current_value) == 0:
            return True
            
        # Don't override existing values
        return False