# /src/services/pacer/_transfer_components/court_identifier.py
import re
from typing import Any, Dict, Optional

from src.infrastructure.external.openai_client import OpenAIClient as GPT4
from src.infrastructure.patterns.component_base import ComponentImplementation
from src.repositories.district_courts_repository import DistrictCourtsRepository


class CourtIdentifier(ComponentImplementation):
    """
    Component for identifying a court's ID from its name.
    It uses a lookup table and fuzzy matching to identify courts.
    """

    def __init__(
        self,
        logger: Any = None,
        config: Optional[Dict] = None,
        court_lookup: Optional[Dict[str, str]] = None,
        gpt_interface: Optional[GPT4] = None,
        district_court_repository: Optional[DistrictCourtsRepository] = None,
    ):
        super().__init__(logger, config)
        self.court_lookup = court_lookup or {}
        self.gpt_interface = gpt_interface
        self.district_court_repository = district_court_repository
        self._court_lookup_initialized = False
        self._district_court_lookup_cache = None

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes a court identification action.
        """
        action = data.get("action")
        if action == "get_court_id":
            court_name = data.get("court_name")
            if not court_name:
                raise ValueError("court_name must be provided.")
            return await self.determine_court_id(court_name)
        elif action == "is_federal_court":
            court_id = data.get("court_id")
            return self.is_federal_court(court_id)
        elif action == "extract_state_from_county":
            court_name = data.get("court_name")
            return self.extract_state_from_county(court_name)
        else:
            raise ValueError(f"Unknown action for CourtIdentifier: {action}")

    async def determine_court_id(self, court_name: str) -> str:
        """
        Determine court ID from court name using fuzzy matching and AI.
        
        Args:
            court_name: Court name to look up
            
        Returns:
            Court ID or "UNK" if not found
        """
        log_prefix = f"DetermineCourtID:"
        
        self.log_info(f"{log_prefix} Input court name: '{court_name}'")
        
        # Ensure court lookup is loaded
        await self._ensure_court_lookup_loaded()
        
        # Try exact match first
        if self.court_lookup and court_name in self.court_lookup:
            court_id = self.court_lookup[court_name]
            self.log_info(f"{log_prefix} ✅ EXACT MATCH: '{court_id}'")
            return court_id
            
        # Try fuzzy matching
        fuzzy_match = self._fuzzy_match_court(court_name)
        if fuzzy_match:
            self.log_info(f"{log_prefix} ✅ FUZZY MATCH: '{fuzzy_match}'")
            return fuzzy_match
            
        # Use AI service for court identification
        if self.gpt_interface:
            self.log_info(f"{log_prefix} Using AI service for court identification")
            try:
                response = await self.gpt_interface.determine_court_id(court_name)
                gpt_court_id = response.get("court_id")
                if gpt_court_id and isinstance(gpt_court_id, str) and len(gpt_court_id) > 1:
                    self.log_info(f"{log_prefix} ✅ AI determined: '{gpt_court_id}'")
                    return gpt_court_id
            except Exception as e:
                self.log_error(f"{log_prefix} AI service error: {e}")
                
        # Fallback logic
        return self._fallback_court_identification(court_name)

    async def _ensure_court_lookup_loaded(self):
        """Ensure court lookup dictionary is populated."""
        if self._court_lookup_initialized:
            return
            
        try:
            if self.district_court_repository:
                self.log_info("Loading district courts for lookup...")
                district_courts = await self.district_court_repository.scan_all()
                
                for court in district_courts:
                    court_id = court.get("CourtId") or court.get("court_id")
                    court_name = court.get("CourtName") or court.get("court_name")
                    
                    if court_id and court_name:
                        self.court_lookup[court_name] = court_id
                        
                        # Add variations
                        if "District" in court_name:
                            simplified = court_name.replace("District of ", "").replace(" District", "")
                            if simplified != court_name:
                                self.court_lookup[simplified] = court_id
                                
                self.log_info(f"Loaded {len(self.court_lookup)} court mappings")
        except Exception as e:
            self.log_error(f"Error loading court lookup: {e}")
            
        self._court_lookup_initialized = True

    def _fuzzy_match_court(self, court_name: str) -> Optional[str]:
        """Fuzzy match court name against lookup."""
        if not self.court_lookup:
            return None
            
        input_words = set(self._extract_court_words(court_name))
        
        for lookup_name, court_id in self.court_lookup.items():
            lookup_words = set(self._extract_court_words(lookup_name))
            
            if input_words and input_words.issubset(lookup_words):
                self.log_info(f"Fuzzy matched '{court_name}' → '{court_id}'")
                return court_id
                
        return None

    def _extract_court_words(self, court_name: str) -> list:
        """Extract meaningful words from court name."""
        stop_words = {"usdc", "u.s.", "us", "district", "court", "of", "the", "for"}
        
        words = court_name.lower().replace(".", " ").replace(",", " ").split()
        return [w.strip() for w in words if w.strip() not in stop_words and len(w.strip()) > 1]

    def _fallback_court_identification(self, court_name: str) -> str:
        """Fallback court identification logic."""
        court_lower = court_name.lower()
        state_indicators = ["county", "circuit", "superior", "state court", "municipal", "supreme"]
        
        if any(indicator in court_lower for indicator in state_indicators):
            state_id = self.extract_state_from_county(court_name)
            if state_id != "UNK_STATE":
                return state_id
                
        return "UNK"

    def is_federal_court(self, court_id: str) -> bool:
        """Check if court ID represents a federal court."""
        if not court_id or court_id.startswith("UNK"):
            return False
            
        # State courts are 2-character codes
        if len(court_id) == 2:
            return False
            
        # Check court lookup
        if self.court_lookup and court_id in self.court_lookup.values():
            return True
            
        # Check federal patterns
        federal_patterns = [
            r"^[a-z]{2,4}d[cmnsew]?$",  # District courts
            r"^jpml$",  # JPML
        ]
        
        court_id_lower = court_id.lower()
        return any(re.fullmatch(pattern, court_id_lower) for pattern in federal_patterns)

    def extract_state_from_county(self, court_name: str) -> str:
        """Extract state abbreviation from county court name."""
        county_state_map = {
            "madison": "IL",
            "cook": "IL", 
            "harris": "TX",
            "los angeles": "CA",
            "miami-dade": "FL",
            "orange": "FL",
            "king": "WA",
            "wayne": "MI",
        }
        
        court_lower = court_name.lower()
        for county, state in county_state_map.items():
            if county in court_lower:
                self.log_info(f"Found state '{state}' for county '{county}'")
                return state
                
        return "UNK_STATE"
