# /src/services/pacer/_transfer_components/transfer_processor.py
"""

PACER Transfer Processor Component
Handles case transfer detection, S3 link inheritance, and transferor case updates.

This processor provides:
- Transfer detection from HTML content
- S3 link inheritance from transferor cases
- Transferor case updates with transferee information
- Integration with case processing workflow
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict, Optional

from src.infrastructure.external.openai_client import OpenAIClient as GPT4
from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.infrastructure.storage.s3_async import S3AsyncStorage
from src.pacer.components.transfer.court_identifier import CourtIdentifier
from src.pacer.services.file_operations_service import FileOperationsService
from src.pacer.services.relevance_service import RelevanceService
from src.repositories.district_courts_repository import DistrictCourtsRepository
from src.repositories.pacer_repository import PacerRepository


class TransferProcessor(ComponentImplementation):
    """
    Component for processing case transfers. It identifies the transferor court,
    checks for MDL status, and handles S3 link inheritance.
    """

    def __init__(
        self,
        logger: Any = None,
        config: Optional[Dict] = None,
        court_identifier: Optional[CourtIdentifier] = None,
        file_manager: Optional[Any] = None,
        relevance_service: Optional[RelevanceService] = None,
        court_id: str = None,
        pacer_repository: PacerRepository | None = None,
        s3_async_storage: S3AsyncStorage | None = None,
        gpt_interface: GPT4 | None = None,
        court_lookup: dict[str, str] | None = None,
        district_court_repository: DistrictCourtsRepository | None = None,
    ):
        super().__init__(logger, config)
        self.court_identifier = court_identifier
        self.file_manager = file_manager
        self.relevance_service = relevance_service
        
        # Extended attributes from transfer_service.py
        self.court_id = court_id
        self.pacer_repository = pacer_repository
        # Initialize S3 storage with fallback handling
        if s3_async_storage is None:
            self.log_warning(
                "S3 storage not properly injected - S3 operations will be disabled"
            )
        self.s3_storage = s3_async_storage
        self.gpt_interface = gpt_interface
        self._court_lookup = court_lookup or {}
        self.district_court_repository = district_court_repository

        # MDL and court lookup caches
        self._mdl_lookup_cache = None
        self._district_court_lookup_cache = None

        # Null condition constants (matching transfer_handler.py)
        self.NULL_CONDITIONS = [
            None,
            "",
            "NULL",
            "null",
            "N/A",
            "n/a",
            "NA",
            "na",
            "None",
            "none",
        ]

        # Initialize court lookup asynchronously
        self._court_lookup_initialized = False

        self.log_info(f"Transfer processor initialized for court: {court_id}")

    async def _execute_action(self, data: Any) -> Any:
        """Execute TransferProcessor actions."""
        if isinstance(data, dict):
            action = data.get("action")
            if action == "process_transfer":
                case_details = data.get("case_details")
                if not case_details:
                    raise ValueError("case_details must be provided.")
                return await self.process_transfer_case(case_details)
            elif action == "process_transfer_full":
                return await self.process_transfer(data["case_details"])
            elif action == "is_potential_transfer":
                return self._is_potential_transfer(data["case_details"])
            elif action == "gather_transfer_info":
                return await self._gather_transfer_info(data["case_details"])
            elif action == "clean_docket_number":
                return self._clean_docket_number(data["docket_num_raw"])
            elif action == "extract_court_words":
                return self._extract_court_words(data["court_name"])
            elif action == "fuzzy_match_court":
                return self._fuzzy_match_court(data["court_name"])
            elif action == "extract_state_from_county":
                return self._extract_state_from_county(data["court_name"])
            elif action == "determine_court_id":
                return await self._determine_court_id(data["court_name"])
            elif action == "set_transfer_status":
                await self._set_transfer_status(data["case_details"])
                return None
            elif action == "is_federal_court":
                return self._is_federal_court(data["court_id"])
            elif action == "inherit_s3_link":
                await self._inherit_s3_link(data["case_details"])
                return None
            elif action == "is_valid_s3_link":
                return self._is_valid_s3_link(data["s3_link"])
            elif action == "convert_to_cdn_format":
                return self._convert_to_cdn_format(data["s3_link"])
            elif action == "update_transferor_case":
                await self._update_transferor_case(data["case_details"])
                return None
            elif action == "detect_removal_keywords":
                return self.detect_removal_keywords(data["html_content"])
            elif action == "detect_transfer_keywords":
                return self.detect_transfer_keywords(data["html_content"])
            else:
                raise ValueError(f"Unknown action for TransferProcessor: {action}")
        raise PacerServiceError("Invalid action data provided to TransferProcessor")

    async def process_transfer_case(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Legacy method for backward compatibility.
        Processes a case that has been transferred from another court.
        """
        log_prefix = f"[{case_details.get('court_id', 'N/A')}]"
        self.log_info(f"{log_prefix} Starting transfer case processing.")

        # Identify transferor court
        transferor_court_name = case_details.get("transferor_court")
        if transferor_court_name:
            transferor_court_id = await self.court_identifier.execute({
                "action": "get_court_id", "court_name": transferor_court_name
            })
            case_details["transferor_court_id"] = transferor_court_id
            self.log_info(f"{log_prefix} Identified transferor court: {transferor_court_id}")

        # Check for MDL and inherit S3 link
        is_mdl = await self.relevance_service.is_mdl_case(case_details)
        if is_mdl:
            self.log_info(f"{log_prefix} MDL case detected, attempting to inherit S3 link.")
            case_details["is_transferred"] = True
            case_details["is_mdl_case"] = True

            target_case = await self.file_manager.get_target_case_details(case_details)
            if target_case and target_case.get("s3_link"):
                case_details["s3_link"] = target_case["s3_link"]
                self.log_info(f"{log_prefix} Inherited S3 link: {target_case['s3_link']}")
        else:
            case_details["is_transferred"] = False

        return case_details

    async def process_transfer(self, case_details: dict[str, Any]) -> dict[str, Any]:
        """
        Main entry point for transfer processing.

        Args:
            case_details: Case details dictionary

        Returns:
            Updated case details with transfer information
        """
        log_prefix = f"[{self.court_id}][{case_details.get('docket_num', 'UNKNOWN')}] TransferProcessor:"

        try:
            self.log_info(f"{log_prefix} 🔄 Starting transfer processing")

            # Step 1: Check if this is a transfer case
            if not self._is_potential_transfer(case_details):
                self.log_debug(f"{log_prefix} No transfer indicators found")
                return case_details

            # Step 2: Process transfer information
            transfer_info = await self._gather_transfer_info(case_details)
            if transfer_info:
                case_details.update(transfer_info)
                self.log_info(
                    f"{log_prefix} ✅ Transfer information gathered: {transfer_info}"
                )

            # Step 3: Set transfer status flags
            await self._set_transfer_status(case_details)

            # Step 4: Handle S3 link inheritance if transferred_in
            if case_details.get("transferred_in"):
                await self._inherit_s3_link(case_details)

            # Step 5: Update transferor case if we have the information
            if case_details.get("transferor_court_id") and case_details.get(
                "transferor_docket_num"
            ):
                await self._update_transferor_case(case_details)

            self.log_info(f"{log_prefix} ✅ Transfer processing completed")
            return case_details

        except Exception as e:
            self.log_error(f"{log_prefix} ❌ Error in transfer processing: {e}")
            case_details["_transfer_processing_error"] = str(e)
            return case_details

    def _is_potential_transfer(self, case_details: dict[str, Any]) -> bool:
        """
        Check if case has transfer indicators.

        Args:
            case_details: Case details to check

        Returns:
            True if transfer indicators found
        """
        # Check for transferred_from field (set by HTML processing)
        if case_details.get("transferred_from"):
            return True

        # Check for case_in_other_court field
        if case_details.get("case_in_other_court"):
            return True

        # Check for transferor fields already set
        if case_details.get("transferor_court_id") or case_details.get(
            "transferor_docket_num"
        ):
            return True

        # Check for removal flag
        if case_details.get("is_removal"):
            return True

        # Check for MDL-related transfers
        if case_details.get("mdl_num") and case_details.get("flags"):
            # Check for specific MDL transfer patterns in flags
            flags_str = str(case_details.get("flags", []))
            if "transfer" in flags_str.lower() or "mdl" in flags_str.lower():
                return True

        return False

    async def _gather_transfer_info(
        self, case_details: dict[str, Any]
    ) -> dict[str, str] | None:
        """
        Extract transfer information from case details.

        Args:
            case_details: Case details containing transfer information

        Returns:
            Dictionary with transferor information or None
        """
        log_prefix = f"[{self.court_id}][{case_details.get('docket_num', 'UNKNOWN')}] GatherTransfer:"

        # Priority 1: Use transferred_from field
        transfer_source = case_details.get(
            "transferred_from", case_details.get("case_in_other_court")
        )

        if not transfer_source:
            return None

        self.log_info(f"{log_prefix} Parsing transfer source: '{transfer_source}'")

        try:
            # Check for JPML/MDL transfer
            if any(
                kw in transfer_source.lower()
                for kw in ["jpml", "mdl no", "multidistrict litigation"]
            ):
                court_name, court_id = "JPML/MDL", "jpml"
                mdl_match = re.search(
                    r"(MDL No\.|MDL)\s*(\d+)", transfer_source, re.IGNORECASE
                )
                docket_num = f"MDL_{mdl_match.group(2)}" if mdl_match else "MDL_Unknown"
                self.log_info(f"{log_prefix} Identified as JPML/MDL transfer")
            else:
                # Standard format: "Court Name, Docket Number"
                parts = transfer_source.rsplit(",", 1)
                if len(parts) == 2:
                    court_name = parts[0].strip()
                    docket_num = self._clean_docket_number(parts[1].strip())
                    court_id = await self._determine_court_id(court_name)
                    self.log_info(
                        f"{log_prefix} Parsed: Court='{court_name}' ({court_id}), Docket='{docket_num}'"
                    )
                else:
                    self.log_warning(
                        f"{log_prefix} Cannot parse transfer source format"
                    )
                    return None

            result = {
                "transferor_court_name": court_name,
                "transferor_court_id": court_id,
                "transferor_docket_num": docket_num,
            }

            return result

        except Exception as e:
            self.log_error(f"{log_prefix} Error parsing transfer source: {e}")
            return None

    def _clean_docket_number(self, docket_num_raw: str) -> str:
        """Clean and normalize docket number."""
        cleaned = re.sub(r"[\s\u200B\uFEFF]+", "", docket_num_raw).strip()

        # For federal dockets, always use first 13 characters (N:YY-XX-NNNNN)
        federal_pattern = re.match(
            r"^(\d:\d{2}-[a-z]{2}-\d{5})", cleaned, re.IGNORECASE
        )
        if federal_pattern:
            return federal_pattern.group(1)

        # Extract core docket pattern (e.g., 1:23-cv-12345)
        core_match = re.match(r"^(\d:\d{2}-\w+-\d+)", cleaned)
        if core_match:
            return core_match.group(1)

        # Handle judge initials suffix
        parts = cleaned.split("-")
        if len(parts) > 3:
            suffix_parts = parts[3:]
            if all(re.fullmatch(r"[A-Z]{1,3}", p) for p in suffix_parts):
                return "-".join(parts[:3])

        return cleaned

    def _extract_court_words(self, court_name: str) -> list[str]:
        """Extract meaningful words from court name for fuzzy matching."""
        # Remove common legal terms
        stop_words = {"usdc", "u.s.", "us", "district", "court", "of", "the", "for"}

        # Split and clean
        words = court_name.lower().replace(".", " ").replace(",", " ").split()
        meaningful_words = [
            word.strip()
            for word in words
            if word.strip() not in stop_words and len(word.strip()) > 1
        ]

        return meaningful_words

    async def _ensure_court_lookup_loaded(self):
        """Ensure court lookup dictionary is populated with district courts data."""
        if self._court_lookup_initialized:
            return

        try:
            if self.district_court_repository:
                self.log_info("Loading district courts for court lookup...")
                district_courts = await self.district_court_repository.scan_all()

                # Build court lookup dictionary
                court_lookup = {}
                for court in district_courts:
                    court_id = court.get("CourtId") or court.get("court_id")
                    court_name = court.get("CourtName") or court.get("court_name")

                    if court_id and court_name:
                        court_lookup[court_name] = court_id

                        # Add common variations
                        if "District" in court_name:
                            # "Northern District of California" -> "California Northern"
                            simplified = court_name.replace("District of ", "").replace(
                                " District", ""
                            )
                            if simplified != court_name:
                                court_lookup[simplified] = court_id

                            # Add "Direction + State" format
                            parts = court_name.split()
                            if (
                                len(parts) >= 4
                                and "District" in parts
                                and "of" in parts
                            ):
                                direction = parts[0]  # Northern, Southern, etc.
                                state = parts[-1]  # California, etc.
                                alt_name = f"{state} {direction}"
                                court_lookup[alt_name] = court_id

                # Update the instance court lookup
                self._court_lookup.update(court_lookup)
                self._court_lookup_initialized = True

                self.log_info(
                    f"✅ Loaded {len(court_lookup)} court mappings into lookup dictionary"
                )

                # Test specific case
                if "Missouri Eastern" in self._court_lookup:
                    self.log_info(
                        f"✅ Found 'Missouri Eastern' → '{self._court_lookup['Missouri Eastern']}'"
                    )
                else:
                    # Check for variations
                    for name, court_id in self._court_lookup.items():
                        if "missouri" in name.lower() and "eastern" in name.lower():
                            self.log_info(
                                f"✅ Found Missouri Eastern variant: '{name}' → '{court_id}'"
                            )
                            # Add the exact variant we need
                            self._court_lookup["Missouri Eastern"] = court_id
                            break
                    else:
                        self.log_warning(
                            "❌ Could not find 'Missouri Eastern' in court lookup"
                        )

            else:
                self.log_warning(
                    "District court repository not available - court lookup will be empty"
                )

        except Exception as e:
            self.log_error(f"Error loading court lookup: {e}")

        self._court_lookup_initialized = True

    def _fuzzy_match_court(self, court_name: str) -> str | None:
        """Fuzzy match court name against district court lookup."""
        if not self._court_lookup:
            return None

        input_words = set(self._extract_court_words(court_name))

        for lookup_name, court_id in self._court_lookup.items():
            lookup_words = set(self._extract_court_words(lookup_name))

            # Check if all input words are found in lookup words
            if input_words and input_words.issubset(lookup_words):
                self.log_info(
                    f"Fuzzy matched '{court_name}' to '{lookup_name}' → '{court_id}'"
                )
                return court_id

        return None

    def _extract_state_from_county(self, court_name: str) -> str:
        """Extract state abbreviation from county court name."""
        log_prefix = f"[{self.court_id}] ExtractState:"

        # Common county-to-state mappings
        county_state_map = {
            "madison": "IL",
            "cook": "IL",
            "harris": "TX",
            "los angeles": "CA",
            "miami-dade": "FL",
            "orange": "FL",  # Could be CA or FL, defaulting to FL
            "king": "WA",
            "wayne": "MI",
        }

        court_lower = court_name.lower()
        self.log_info(
            f"{log_prefix} Input: '{court_name}' → lowercase: '{court_lower}'"
        )
        self.log_info(
            f"{log_prefix} County mappings to check: {list(county_state_map.keys())}"
        )

        for county, state in county_state_map.items():
            if county in court_lower:
                self.log_info(
                    f"{log_prefix} ✅ MATCH FOUND: '{county}' in '{court_lower}' → state '{state}'"
                )
                return state
            else:
                self.log_debug(
                    f"{log_prefix} No match: '{county}' not in '{court_lower}'"
                )

        # Default fallback - try to extract from common patterns
        if "county" in court_lower:
            self.log_info(
                f"{log_prefix} Contains 'county' but no specific mapping found"
            )
            # Could add more sophisticated state detection here
            pass

        self.log_warning(f"{log_prefix} ❌ No state mapping found for '{court_name}'")
        return "UNK_STATE"

    async def _determine_court_id(self, court_name: str) -> str:
        """
        Determine court ID from court name using fuzzy matching.

        Args:
            court_name: Court name to look up

        Returns:
            Court ID or "UNK" if not found
        """
        log_prefix = f"[{self.court_id}] DetermineCourtID:"

        self.log_info(f"{log_prefix} === STARTING COURT ID DETERMINATION ===")
        self.log_info(f"{log_prefix} Input court name: '{court_name}'")

        # Ensure court lookup is loaded
        await self._ensure_court_lookup_loaded()

        # Try exact match first
        if self._court_lookup and court_name in self._court_lookup:
            court_id = self._court_lookup[court_name]
            self.log_info(
                f"{log_prefix} ✅ EXACT MATCH found for '{court_name}': '{court_id}'"
            )
            return court_id
        else:
            self.log_info(
                f"{log_prefix} ❌ No exact match found in court lookup (has {len(self._court_lookup) if self._court_lookup else 0} entries)"
            )

        # Try fuzzy matching against federal district courts
        self.log_info(f"{log_prefix} === ATTEMPTING FUZZY MATCHING ===")
        fuzzy_match = self._fuzzy_match_court(court_name)
        if fuzzy_match:
            self.log_info(f"{log_prefix} ✅ FUZZY MATCH found: '{fuzzy_match}'")
            return fuzzy_match
        else:
            self.log_info(f"{log_prefix} ❌ No fuzzy match found")

        # Use AI service (GPT/DeepSeek) as PRIMARY method for court identification
        # This leverages the configured prompts that understand federal vs state court patterns
        if self.gpt_interface:
            self.log_info(f"{log_prefix} === USING AI SERVICE FOR COURT IDENTIFICATION ===")
            self.log_info(f"{log_prefix} Querying AI service for court name: '{court_name}'")
            try:
                response = await self.gpt_interface.determine_court_id(court_name)
                gpt_court_id = response.get("court_id")
                self.log_info(f"{log_prefix} AI service response: {response}")
                if (
                    gpt_court_id
                    and isinstance(gpt_court_id, str)
                    and len(gpt_court_id) > 1
                ):
                    self.log_info(
                        f"{log_prefix} ✅ AI service determined court ID: '{gpt_court_id}'"
                    )
                    return gpt_court_id
                else:
                    self.log_warning(
                        f"{log_prefix} ❌ AI service did not return valid court ID: '{gpt_court_id}'"
                    )
                    # Fall back to local logic if AI service fails
                    return self._fallback_court_identification(court_name, log_prefix)
            except Exception as e:
                self.log_error(f"{log_prefix} ❌ Error querying AI service: {e}")
                # Fall back to local logic if AI service fails
                return self._fallback_court_identification(court_name, log_prefix)
        else:
            self.log_info(f"{log_prefix} ❌ No AI service available")
            # Fall back to local logic if AI service not available
            return self._fallback_court_identification(court_name, log_prefix)

    def _fallback_court_identification(self, court_name: str, log_prefix: str) -> str:
        """
        Fallback court identification using local logic when AI service is unavailable.
        """
        self.log_info(f"{log_prefix} === USING FALLBACK LOCAL LOGIC ===")
        
        # Check if this looks like a state court
        court_lower = court_name.lower()
        state_court_indicators = [
            "county",
            "circuit", 
            "superior",
            "state court",
            "municipal",
            "supreme"  # Added to catch state supreme courts
        ]

        matching_indicators = [
            indicator
            for indicator in state_court_indicators
            if indicator in court_lower
        ]
        
        if matching_indicators:
            self.log_info(
                f"{log_prefix} ✅ IDENTIFIED AS STATE COURT (contains: {matching_indicators})"
            )
            # This is likely a state court
            state_id = self._extract_state_from_county(court_name)
            self.log_info(f"{log_prefix} State extraction result: '{state_id}'")
            if state_id != "UNK_STATE":
                self.log_info(
                    f"{log_prefix} ✅ FALLBACK RESULT: '{court_name}' → '{state_id}' (STATE COURT)"
                )
                return state_id

        self.log_warning(
            f"{log_prefix} ❌ FALLBACK RESULT: No court ID found for '{court_name}', returning 'UNK'"
        )
        return "UNK"

    async def _create_mdl_lookup_async(self) -> dict[str, str]:
        """
        Create MDL number to court ID lookup dictionary.

        Returns:
            Dictionary mapping MDL numbers to court IDs
        """
        if self._mdl_lookup_cache is not None:
            return self._mdl_lookup_cache

        self.log_info("Creating MDL lookup dictionary...")

        if not self.district_court_repository:
            self.log_error(
                "❌ CRITICAL: No district court repository available for MDL lookup - MDL cases will not work!"
            )
            self._mdl_lookup_cache = {}
            return self._mdl_lookup_cache
        else:
            self.log_info(
                f"✅ District court repository available: {type(self.district_court_repository).__name__}"
            )

        try:
            # Get all district court records
            self.log_info("🔍 Scanning DistrictCourts table for MDL mappings...")
            court_items = await self.district_court_repository.scan_all()
            self.log_info(f"📊 DistrictCourts scan returned {len(court_items)} items")

            mdl_lookup = {}
            mdl_2873_found = False
            total_mdl_entries = 0
            sample_mdls = []

            for court in court_items:
                mdl_num = court.get("mdl_num")
                court_id = court.get("court_id")

                if mdl_num and court_id and mdl_num not in self.NULL_CONDITIONS:
                    mdl_num_str = str(mdl_num).replace(
                        ".0", ""
                    )  # Clean potential float artifact
                    mdl_lookup[mdl_num_str] = court_id
                    total_mdl_entries += 1

                    # Collect sample MDLs for debugging
                    if len(sample_mdls) < 5:
                        sample_mdls.append(f"{mdl_num_str}→{court_id}")

                    # Debug logging for MDL 2873 specifically
                    if mdl_num_str == "2873":
                        mdl_2873_found = True
                        self.log_info(f"🎯 Found MDL 2873 → Court: {court_id}")

            self.log_info(
                f"📊 Found {total_mdl_entries} total MDL entries. Sample: {sample_mdls}"
            )

            self._mdl_lookup_cache = mdl_lookup
            self.log_info(f"MDL lookup created with {len(mdl_lookup)} entries")

            # Debug logging for MDL 2873
            if mdl_2873_found:
                self.log_info(
                    f"✅ MDL 2873 lookup result: {mdl_lookup.get('2873', 'NOT_FOUND')}"
                )
            else:
                self.log_warning(
                    f"❌ MDL 2873 NOT FOUND in DistrictCourts scan of {len(court_items)} items"
                )

            return mdl_lookup

        except Exception as e:
            self.log_error(f"Error creating MDL lookup: {e}")
            self._mdl_lookup_cache = {}
            return self._mdl_lookup_cache

    async def _create_district_court_lookup_async(self) -> dict[str, str]:
        """
        Create district court name to court ID lookup dictionary.

        Returns:
            Dictionary mapping court names to court IDs
        """
        if self._district_court_lookup_cache is not None:
            return self._district_court_lookup_cache

        self.log_info("Creating district court lookup dictionary...")

        if not self.district_court_repository:
            self.log_warning("No district court repository available for court lookup")
            self._district_court_lookup_cache = {}
            return self._district_court_lookup_cache

        try:
            # Get all district court records
            court_items = await self.district_court_repository.scan_all()

            lookup_dict = {}
            for court in court_items:
                full_name = court.get("court_name")
                short_name = (
                    full_name.replace(" District Court", "") if full_name else None
                )
                court_id = court.get("court_id")

                if full_name and court_id:
                    lookup_dict[full_name.strip()] = court_id
                if short_name and court_id and short_name != full_name:
                    lookup_dict[short_name.strip()] = court_id

            self._district_court_lookup_cache = lookup_dict
            self.log_info(
                f"District court lookup created with {len(lookup_dict)} entries"
            )
            return lookup_dict

        except Exception as e:
            self.log_error(f"Error creating district court lookup: {e}")
            self._district_court_lookup_cache = {}
            return self._district_court_lookup_cache

    async def _determine_court_type(
        self, court_name: str, docket_num: str, federal_lookup: dict[str, str]
    ) -> tuple:
        """
        Determine if court is STATE or FEDERAL and return court ID.
        Uses AI service for accurate court identification.

        Args:
            court_name: Name of the court
            docket_num: Docket number
            federal_lookup: Federal court lookup dictionary

        Returns:
            Tuple of (court_type, court_id) where court_type is "STATE" or "FEDERAL"
        """
        log_prefix = f"[{self.court_id}] DetermineCourtType:"

        self.log_info(
            f"{log_prefix} Input: court_name='{court_name}', docket_num='{docket_num}'"
        )

        # First, try to get court ID using AI service
        determined_court_id = await self._determine_court_id(court_name)
        
        # Check if it's a federal court using exact lookup
        if court_name in federal_lookup:
            federal_court_id = federal_lookup[court_name]
            self.log_info(
                f"{log_prefix} ✅ Exact federal match: '{court_name}' → '{federal_court_id}'"
            )
            return "FEDERAL", federal_court_id

        # Try fuzzy matching for federal courts
        fuzzy_match = self._fuzzy_match_court(court_name)
        if fuzzy_match:
            self.log_info(
                f"{log_prefix} ✅ Fuzzy federal match: '{court_name}' → '{fuzzy_match}'"
            )
            return "FEDERAL", fuzzy_match

        # Check if determined_court_id looks like a federal court
        if determined_court_id and self._is_federal_court(determined_court_id):
            self.log_info(
                f"{log_prefix} ✅ AI service identified as FEDERAL court: '{court_name}' → '{determined_court_id}'"
            )
            return "FEDERAL", determined_court_id

        # If not federal, treat as state court
        if determined_court_id and determined_court_id not in ["UNK", "UNK_GPT", "UNK_ERR"]:
            self.log_info(
                f"{log_prefix} ✅ AI service identified as STATE court: '{court_name}' → '{determined_court_id}'"
            )
            return "STATE", determined_court_id

        # Fallback logic for when AI service fails
        self.log_info(f"{log_prefix} === USING FALLBACK LOGIC ===")
        
        # Check for state court indicators as fallback
        court_lower = court_name.lower()
        state_indicators = ["county", "circuit", "superior", "state court", "municipal", "supreme"]

        has_state_indicators = any(
            indicator in court_lower for indicator in state_indicators
        )
        
        if has_state_indicators:
            # This is likely a state court
            state_id = self._extract_state_from_county(court_name)
            self.log_info(
                f"{log_prefix} ✅ Fallback identified as STATE court: '{court_name}' → '{state_id}'"
            )
            return "STATE", state_id

        # Default to STATE if no federal match found
        self.log_info(
            f"{log_prefix} ❌ Defaulting to STATE court: '{court_name}' (no federal match)"
        )
        state_id = self._extract_state_from_county(court_name)
        if state_id != "UNK_STATE":
            return "STATE", state_id
        else:
            return "STATE", "UNK"

    def _calculate_transfer_flags(
        self,
        court_type: str,
        determined_court_id: str,
        current_court_id: str,
        mdl_num: str,
        mdl_court_id: str,
    ) -> dict:
        """
        Calculate all transfer flags based on court type and MDL status.

        Returns dict with: is_removal, is_transferred, pending_cto, transferor_court_id
        """
        # Check if has valid MDL
        has_mdl = mdl_num and mdl_num not in self.NULL_CONDITIONS

        # Calculate flags
        if court_type == "STATE":
            # STATE-TO-FEDERAL = REMOVAL
            is_removal = True
            is_transferred = False
            transferor_court_id = (
                determined_court_id  # Use the determined state court ID
            )

            # pending_cto: True if has MDL and not in MDL court and not transferred
            pending_cto = bool(
                has_mdl
                and mdl_court_id
                and current_court_id != mdl_court_id
                and not is_transferred
            )

        elif court_type == "FEDERAL":
            # FEDERAL-TO-FEDERAL
            is_removal = False
            transferor_court_id = (
                determined_court_id  # Use the determined federal court ID
            )

            # is_transferred: True if different courts AND has MDL
            is_transferred = bool(determined_court_id != current_court_id and has_mdl)

            # pending_cto: False (federal-to-federal cases don't have pending_cto)
            pending_cto = False

        else:
            # Default/unknown
            is_removal = False
            is_transferred = False
            pending_cto = False
            transferor_court_id = "UNK"

        return {
            "is_removal": is_removal,
            "is_transferred": is_transferred,
            "pending_cto": pending_cto,
            "transferor_court_id": transferor_court_id,
        }

    async def _set_transfer_status(self, case_details: dict[str, Any]) -> None:
        """
        Set transfer status flags based on case information using the same rules as Transfer Handler.

        This sets:
        - is_removal: True if case was removed from state court
        - is_transferred: True if case was transferred (but not removed)
        - pending_cto: True if case has MDL but not yet transferred to MDL court
        - transferred_in: True if this court received the transfer

        Args:
            case_details: Case details to update
        """
        log_prefix = f"[{self.court_id}][{case_details.get('docket_num', 'UNKNOWN')}] SetTransferStatus:"

        current_court_id = case_details.get("court_id", self.court_id)
        is_removal_initial = case_details.get("is_removal", False)
        mdl_num_raw = case_details.get("mdl_num")
        docket_num = case_details.get("docket_num", "UNKNOWN")

        self.log_info(f"{log_prefix} === STARTING TRANSFER STATUS DETERMINATION ===")
        self.log_info(f"{log_prefix} current_court_id: '{current_court_id}'")
        self.log_info(f"{log_prefix} is_removal_initial: {is_removal_initial}")
        self.log_info(f"{log_prefix} mdl_num_raw: '{mdl_num_raw}'")

        # Initialize flags with explicit boolean coercion
        case_details["is_removal"] = bool(is_removal_initial)
        case_details["is_transferred"] = False
        case_details["pending_cto"] = False  # Explicitly set to False, never null
        case_details["transferred_in"] = False

        # Get MDL lookup for transfer calculations
        mdl_lookup = await self._create_mdl_lookup_async()
        mdl_num_str = None
        mdl_court_id = None

        if mdl_num_raw not in self.NULL_CONDITIONS:
            mdl_num_str = str(mdl_num_raw).replace(
                ".0", ""
            )  # Clean potential float artifact
            mdl_court_id = mdl_lookup.get(mdl_num_str)

        self.log_info(
            f"{log_prefix} MDL info: mdl_num_str='{mdl_num_str}', mdl_court_id='{mdl_court_id}'"
        )

        # Debug logging for MDL 2873 specifically
        if mdl_num_str == "2873":
            self.log_info(
                f"{log_prefix} 🎯 DEBUG MDL 2873: current_court_id='{current_court_id}', mdl_court_id='{mdl_court_id}', different_courts={current_court_id != mdl_court_id if mdl_court_id else 'mdl_court_id_is_None'}"
            )

        # Rule 1: Special MDL cases (test/placeholder cases)
        if mdl_num_str in ["25", "9000"]:
            case_details.update({"is_transferred": False, "pending_cto": False})
            self.log_info(
                f"{log_prefix} ✅ Special MDL rule applied for MDL {mdl_num_str} - pending_cto set to False"
            )
            return

        # Process 'case_in_other_court' field first (main transfer logic)
        case_in_other_court_str = case_details.get("case_in_other_court")
        if (
            isinstance(case_in_other_court_str, str)
            and case_in_other_court_str not in self.NULL_CONDITIONS
        ):
            try:
                parts = case_in_other_court_str.split(",", 1)  # Split only once
                if len(parts) == 2:
                    potential_transferor_name = parts[0].strip()
                    potential_transferor_docket = parts[1].strip()

                    # Set transferor docket number
                    if (
                        case_details.get("transferor_docket_num")
                        != potential_transferor_docket
                    ):
                        case_details["transferor_docket_num"] = (
                            potential_transferor_docket
                        )

                    self.log_info(
                        f"{log_prefix} Processing transferor: '{potential_transferor_name}', '{potential_transferor_docket}'"
                    )

                    # Get federal court lookup
                    federal_lookup = await self._create_district_court_lookup_async()

                    # Determine court type (STATE or FEDERAL)
                    court_type, determined_court_id = await self._determine_court_type(
                        potential_transferor_name,
                        potential_transferor_docket,
                        federal_lookup,
                    )

                    # Calculate all transfer flags
                    transfer_flags = self._calculate_transfer_flags(
                        court_type,
                        determined_court_id,
                        current_court_id,
                        mdl_num_str,
                        mdl_court_id,
                    )

                    # Update case details with transfer information - ensure boolean types
                    case_details["transferor_court_id"] = transfer_flags[
                        "transferor_court_id"
                    ]
                    case_details["is_removal"] = bool(transfer_flags["is_removal"])
                    case_details["is_transferred"] = bool(
                        transfer_flags["is_transferred"]
                    )
                    case_details["pending_cto"] = bool(
                        transfer_flags["pending_cto"]
                    )  # Explicit bool coercion

                    # Set transferred_in flag
                    if transfer_flags["is_removal"] or transfer_flags["is_transferred"]:
                        case_details["transferred_in"] = True

                    self.log_info(
                        f"{log_prefix} Transfer determination: "
                        f"court_type={court_type}, transferor_court_id={transfer_flags['transferor_court_id']}, "
                        f"is_removal={transfer_flags['is_removal']}, is_transferred={transfer_flags['is_transferred']}, "
                        f"pending_cto={transfer_flags['pending_cto']}"
                    )

                else:
                    self.log_warning(
                        f"{log_prefix} Could not parse 'case_in_other_court' into two parts: {case_in_other_court_str}"
                    )
            except Exception as e:
                self.log_error(
                    f"{log_prefix} Error processing 'case_in_other_court' field: {e}"
                )

        # Apply remaining rules after case_in_other_court processing
        transferor_court_id = case_details.get("transferor_court_id")

        # Rule 2: Has MDL, NO transferor info, NOT in MDL court -> Pending CTO
        if (
            mdl_num_str
            and not transferor_court_id
            and mdl_court_id
            and current_court_id != mdl_court_id
        ):
            case_details.update({"is_transferred": False, "pending_cto": True})
            self.log_info(
                f"{log_prefix} ✅ Pending CTO rule applied (has MDL {mdl_num_str}, not in MDL court {mdl_court_id}) - pending_cto set to True"
            )
            return

        # Rule 3: Has MDL, NO transferor info, IS in MDL court -> Not transferred/pending
        if (
            mdl_num_str
            and not transferor_court_id
            and mdl_court_id
            and current_court_id == mdl_court_id
        ):
            case_details.update({"is_transferred": False, "pending_cto": False})
            self.log_info(
                f"{log_prefix} ✅ In MDL court rule applied (already in MDL court {mdl_court_id}) - pending_cto set to False"
            )
            return

        # Rule 4: Has MDL, IS in MDL court, HAS different valid transferor -> Transferred
        is_valid_transferor = (
            isinstance(transferor_court_id, str) and 3 <= len(transferor_court_id) <= 4
        )
        if (
            mdl_num_str
            and mdl_court_id
            and current_court_id == mdl_court_id
            and is_valid_transferor
            and transferor_court_id != current_court_id
        ):
            case_details.update({"is_transferred": True, "pending_cto": False})
            self.log_info(
                f"{log_prefix} ✅ Transferred rule applied (in MDL court {mdl_court_id} with valid transferor {transferor_court_id}) - pending_cto set to False"
            )
            return

        # Handle cases with existing transferor_court_id but no case_in_other_court
        if transferor_court_id and not transferor_court_id.startswith("UNK"):
            self.log_info(
                f"{log_prefix} === PROCESSING EXISTING TRANSFEROR COURT ID ==="
            )
            is_federal = self._is_federal_court(transferor_court_id)
            self.log_info(
                f"{log_prefix} _is_federal_court('{transferor_court_id}') = {is_federal}"
            )

            if is_federal:
                self.log_info(f"{log_prefix} FEDERAL court detected")
                if transferor_court_id != current_court_id:
                    self.log_info(
                        f"{log_prefix} ✅ FEDERAL-TO-FEDERAL TRANSFER: '{transferor_court_id}' → '{current_court_id}'"
                    )
                    # Federal-to-federal transfer (only for MDL cases)
                    has_mdl = mdl_num_str and mdl_num_str not in self.NULL_CONDITIONS
                    case_details["is_transferred"] = has_mdl
                    case_details["transferred_in"] = True
                    case_details["pending_cto"] = (
                        False  # Federal-to-federal never pending_cto
                    )
                    if not is_removal_initial:
                        case_details["is_removal"] = False
                    self.log_info(
                        f"{log_prefix} Set: is_transferred={case_details['is_transferred']}, transferred_in=True, pending_cto=False"
                    )
                else:
                    self.log_info(
                        f"{log_prefix} ❌ Same court ({transferor_court_id} == {current_court_id}), not a transfer"
                    )
            else:
                self.log_info(f"{log_prefix} STATE court detected")
                self.log_info(
                    f"{log_prefix} ✅ STATE-TO-FEDERAL REMOVAL: '{transferor_court_id}' → '{current_court_id}'"
                )
                # State-to-federal is a removal, NOT a transfer
                case_details["is_removal"] = True
                case_details["is_transferred"] = False
                case_details["transferred_in"] = True
                # pending_cto: True if has MDL and not in MDL court
                has_mdl = mdl_num_str and mdl_num_str not in self.NULL_CONDITIONS
                pending_cto_value = (
                    has_mdl and mdl_court_id and current_court_id != mdl_court_id
                )
                case_details["pending_cto"] = pending_cto_value

                # Debug logging for pending_cto calculation
                if mdl_num_str == "2873":
                    self.log_info(
                        f"{log_prefix} 🎯 DEBUG MDL 2873 pending_cto calculation:"
                    )
                    self.log_info(f"{log_prefix}   has_mdl={has_mdl}")
                    self.log_info(
                        f"{log_prefix}   mdl_court_id='{mdl_court_id}' (truthy: {bool(mdl_court_id)})"
                    )
                    self.log_info(
                        f"{log_prefix}   current_court_id != mdl_court_id = '{current_court_id}' != '{mdl_court_id}' = {current_court_id != mdl_court_id if mdl_court_id else 'N/A'}"
                    )
                    self.log_info(
                        f"{log_prefix}   pending_cto_value = {pending_cto_value}"
                    )

                self.log_info(
                    f"{log_prefix} Set: is_removal=True, is_transferred=False, pending_cto={pending_cto_value}"
                )

        elif is_removal_initial:
            self.log_info(f"{log_prefix} === TRUSTING INITIAL REMOVAL DETECTION ===")
            # Trust initial removal detection - removals are NOT transfers
            case_details["is_removal"] = True
            case_details["is_transferred"] = False
            case_details["transferred_in"] = True
            # For removals, check pending_cto based on MDL status
            has_mdl = mdl_num_str and mdl_num_str not in self.NULL_CONDITIONS
            pending_cto_value = (
                has_mdl and mdl_court_id and current_court_id != mdl_court_id
            )
            case_details["pending_cto"] = pending_cto_value

            # Debug logging for pending_cto calculation
            if mdl_num_str == "2873":
                self.log_info(
                    f"{log_prefix} 🎯 DEBUG MDL 2873 removal pending_cto calculation:"
                )
                self.log_info(f"{log_prefix}   has_mdl={has_mdl}")
                self.log_info(
                    f"{log_prefix}   mdl_court_id='{mdl_court_id}' (truthy: {bool(mdl_court_id)})"
                )
                self.log_info(
                    f"{log_prefix}   current_court_id != mdl_court_id = '{current_court_id}' != '{mdl_court_id}' = {current_court_id != mdl_court_id if mdl_court_id else 'N/A'}"
                )
                self.log_info(f"{log_prefix}   pending_cto_value = {pending_cto_value}")

            self.log_info(
                f"{log_prefix} ✅ Removal confirmed, pending_cto={pending_cto_value}"
            )
        else:
            self.log_info(
                f"{log_prefix} ❌ No transfer information available, flags remain at default values"
            )

        # Final validation: ensure pending_cto is never null
        if case_details.get("pending_cto") is None:
            case_details["pending_cto"] = False
            self.log_warning(f"{log_prefix} ⚠️ pending_cto was null, reset to False")

        self.log_info(f"{log_prefix} === FINAL TRANSFER STATUS ===")
        self.log_info(
            f"{log_prefix} is_removal={case_details['is_removal']}, "
            f"is_transferred={case_details['is_transferred']}, "
            f"pending_cto={case_details['pending_cto']} (type: {type(case_details['pending_cto']).__name__}), "
            f"transferred_in={case_details['transferred_in']}"
        )

    def _is_federal_court(self, court_id: str) -> bool:
        """Check if court ID represents a federal court."""
        log_prefix = f"[{self.court_id}] IsFederalCourt:"

        self.log_info(f"{log_prefix} Checking court_id: '{court_id}'")

        if not court_id or court_id.startswith("UNK"):
            self.log_info(f"{log_prefix} ❌ Empty or UNK court_id → False")
            return False

        # State courts are 2-character codes (e.g., "IL", "NY")
        if len(court_id) == 2:
            self.log_info(
                f"{log_prefix} ❌ Length 2 ('{court_id}') → STATE COURT → False"
            )
            return False

        # Check court lookup
        if self._court_lookup and court_id in self._court_lookup.values():
            self.log_info(f"{log_prefix} ✅ Found in court lookup → FEDERAL → True")
            return True

        # Check federal court patterns - more restrictive
        # District courts: e.g., cand, nysd, flmb, laed, dcd, ilsd
        federal_patterns = [
            r"^[a-z]{2,4}d[cmnsew]?$",  # e.g., nysd, cacd, flmd, ilsd
            r"^jpml$",  # JPML
        ]

        court_id_lower = court_id.lower()
        self.log_info(f"{log_prefix} Testing patterns against '{court_id_lower}'")

        for i, pattern in enumerate(federal_patterns):
            match = re.fullmatch(pattern, court_id_lower)
            self.log_info(
                f"{log_prefix} Pattern {i + 1} '{pattern}': {'✅ MATCH' if match else '❌ no match'}"
            )
            if match:
                self.log_info(
                    f"{log_prefix} ✅ Matches federal pattern → FEDERAL → True"
                )
                return True

        self.log_info(f"{log_prefix} ❌ No federal patterns matched → False")
        return False

    async def _inherit_s3_link(self, case_details: dict[str, Any]) -> None:
        """
        Inherit S3 link and MDL number from transferor case if current case doesn't have them.

        Args:
            case_details: Case details to update
        """
        log_prefix = (
            f"[{self.court_id}][{case_details.get('docket_num', 'UNKNOWN')}] InheritS3:"
        )

        # Check if we already have a valid S3 link
        current_s3_link = case_details.get("s3_link")
        if self._is_valid_s3_link(current_s3_link):
            self.log_debug(f"{log_prefix} Already has valid S3 link")
            return

        transferor_court_id = case_details.get("transferor_court_id")
        transferor_docket_num = case_details.get("transferor_docket_num")

        if not transferor_court_id or not transferor_docket_num:
            self.log_debug(f"{log_prefix} No transferor information available")
            return

        try:
            # Query transferor case
            self.log_info(
                f"{log_prefix} Querying transferor case {transferor_court_id}:{transferor_docket_num}"
            )

            if self.pacer_repository:
                results = await self.pacer_repository.query_by_court_and_docket(
                    transferor_court_id, transferor_docket_num
                )

                if results and len(results) > 0:
                    transferor_data = results[0]
                    # Repository returns snake_case data due to @convert_case_on_read decorator
                    s3_link = transferor_data.get("s3_link")

                    if s3_link and self._is_valid_s3_link(s3_link):
                        # Convert to CDN format if needed
                        cdn_link = self._convert_to_cdn_format(s3_link)
                        case_details["s3_link"] = cdn_link
                        self.log_info(
                            f"{log_prefix} ✅ Inherited S3 link from transferor: {cdn_link}"
                        )

                        # Update filenames
                        if "base_filename" in case_details:
                            case_details["original_filename"] = case_details[
                                "base_filename"
                            ]
                            case_details["new_filename"] = case_details["base_filename"]

                        # Inherit MDL number if needed
                        transferor_mdl_num = transferor_data.get("mdl_num")
                        if (
                            transferor_mdl_num
                            and transferor_mdl_num not in self.NULL_CONDITIONS
                        ):
                            current_mdl_num = case_details.get("mdl_num")
                            if (
                                not current_mdl_num
                                or current_mdl_num in self.NULL_CONDITIONS
                            ):
                                case_details["mdl_num"] = transferor_mdl_num
                                self.log_info(
                                    f"{log_prefix} ✅ Inherited MDL number from transferor: {transferor_mdl_num}"
                                )

                        # Inherit attorneys_gpt if needed (for law firm processing)
                        transferor_attorneys_gpt = transferor_data.get("attorneys_gpt")
                        if (
                            transferor_attorneys_gpt
                            and transferor_attorneys_gpt not in self.NULL_CONDITIONS
                        ):
                            current_attorneys_gpt = case_details.get("attorneys_gpt")
                            if (
                                not current_attorneys_gpt
                                or current_attorneys_gpt in self.NULL_CONDITIONS
                                or (
                                    isinstance(current_attorneys_gpt, list)
                                    and len(current_attorneys_gpt) == 0
                                )
                            ):
                                case_details["attorneys_gpt"] = transferor_attorneys_gpt
                                self.log_info(
                                    f"{log_prefix} ✅ Inherited attorneys_gpt from transferor (law firm processing)"
                                )

                        # Inherit law_firm if needed (primary law firm string)
                        transferor_law_firm = transferor_data.get("law_firm")
                        if (
                            transferor_law_firm
                            and transferor_law_firm not in self.NULL_CONDITIONS
                        ):
                            current_law_firm = case_details.get("law_firm")
                            if (
                                not current_law_firm
                                or current_law_firm in self.NULL_CONDITIONS
                            ):
                                case_details["law_firm"] = transferor_law_firm
                                self.log_info(
                                    f"{log_prefix} ✅ Inherited law_firm from transferor: {transferor_law_firm}"
                                )

                        # Inherit law_firms if needed (array of law firms)
                        transferor_law_firms = transferor_data.get("law_firms")
                        if (
                            transferor_law_firms
                            and transferor_law_firms not in self.NULL_CONDITIONS
                        ):
                            current_law_firms = case_details.get("law_firms")
                            if (
                                not current_law_firms
                                or current_law_firms in self.NULL_CONDITIONS
                                or (
                                    isinstance(current_law_firms, list)
                                    and len(current_law_firms) == 0
                                )
                            ):
                                case_details["law_firms"] = transferor_law_firms
                                self.log_info(
                                    f"{log_prefix} ✅ Inherited law_firms from transferor: {transferor_law_firms}"
                                )

                        # Add processing note
                        notes = case_details.get("_processing_notes", "")
                        inherited_items = ["S3 link"]
                        if case_details.get("mdl_num") == transferor_mdl_num:
                            inherited_items.append("MDL number")
                        if (
                            case_details.get("attorneys_gpt")
                            == transferor_attorneys_gpt
                        ):
                            inherited_items.append("attorneys_gpt")
                        if (
                            transferor_law_firm
                            and case_details.get("law_firm") == transferor_law_firm
                        ):
                            inherited_items.append("law_firm")
                        if (
                            transferor_law_firms
                            and case_details.get("law_firms") == transferor_law_firms
                        ):
                            inherited_items.append("law_firms")
                        inheritance_note = (
                            f"{', '.join(inherited_items)} inherited from transferor."
                        )
                        case_details["_processing_notes"] = (
                            f"{notes} {inheritance_note}".strip()
                        )
                    else:
                        self.log_warning(
                            f"{log_prefix} Transferor has no valid S3 link"
                        )
                else:
                    self.log_warning(
                        f"{log_prefix} Transferor case not found in database"
                    )

        except Exception as e:
            self.log_error(f"{log_prefix} Error inheriting S3 link: {e}")

    def _is_valid_s3_link(self, s3_link: Any) -> bool:
        """Check if S3 link is valid."""
        if not s3_link or not isinstance(s3_link, str):
            return False

        # Must be a PDF link
        if not s3_link.lower().endswith(".pdf"):
            return False

        # Must be from our domain or S3
        valid_domains = ["cdn.lexgenius.ai", "lexgenius.s3", "amazonaws.com"]
        return any(domain in s3_link for domain in valid_domains)

    def _convert_to_cdn_format(self, s3_link: str) -> str:
        """Convert S3 link to CDN format."""
        # Already CDN format
        if "cdn.lexgenius.ai" in s3_link:
            return s3_link

        # Convert from S3 format
        s3_pattern = r"https://lexgenius\.s3\.us-west-2\.amazonaws\.com/(.+)"
        match = re.match(s3_pattern, s3_link)
        if match:
            path = match.group(1)
            return f"https://cdn.lexgenius.ai/{path}"

        # Try to extract path from other S3 formats
        if "amazonaws.com" in s3_link:
            parts = s3_link.split("amazonaws.com/")
            if len(parts) > 1:
                path = parts[1]
                return f"https://cdn.lexgenius.ai/{path}"

        return s3_link

    async def _update_transferor_case(self, case_details: dict[str, Any]) -> None:
        """
        Update transferor case with transferee information.

        Args:
            case_details: Current case details with transferor info
        """
        log_prefix = f"[{self.court_id}][{case_details.get('docket_num', 'UNKNOWN')}] UpdateTransferor:"

        if not self.pacer_repository:
            self.log_warning(f"{log_prefix} No repository available")
            return

        transferor_court_id = case_details.get("transferor_court_id")
        transferor_docket_num = case_details.get("transferor_docket_num")
        current_court_id = case_details.get("court_id", self.court_id)
        current_docket_num = case_details.get("docket_num")

        self.log_info(f"{log_prefix} Transfer update check:")
        self.log_info(f"{log_prefix}   transferor_court_id: {transferor_court_id}")
        self.log_info(f"{log_prefix}   transferor_docket_num: {transferor_docket_num}")
        self.log_info(f"{log_prefix}   current_court_id: {current_court_id}")
        self.log_info(f"{log_prefix}   current_docket_num: {current_docket_num}")

        if not all(
            [
                transferor_court_id,
                transferor_docket_num,
                current_court_id,
                current_docket_num,
            ]
        ):
            self.log_warning(
                f"{log_prefix} Missing required information for update - skipping"
            )
            return

        try:
            # Check if pacer_repository is available
            if not self.pacer_repository:
                self.log_error(
                    f"{log_prefix} ❌ CRITICAL: pacer_repository is None - cannot update transferor case"
                )
                return

            # First fetch transferor case to get primary key
            results = await self.pacer_repository.query_by_court_and_docket(
                transferor_court_id, transferor_docket_num
            )

            if not results or len(results) == 0:
                self.log_warning(f"{log_prefix} Transferor case not found in database")
                return

            transferor_data = results[0]
            # Repository returns snake_case data due to @convert_case_on_read decorator
            filing_date = transferor_data.get("filing_date")
            docket_num = transferor_data.get("docket_num")

            if not filing_date or not docket_num:
                self.log_error(
                    f"{log_prefix} Missing primary key fields for transferor case {transferor_court_id}:{transferor_docket_num}"
                )
                self.log_error(
                    f"{log_prefix} Available fields: {list(transferor_data.keys())}"
                )
                return

            # Set initial_filing_date from transferor's filing_date
            case_details["initial_filing_date"] = filing_date
            self.log_info(
                f"{log_prefix} Set initial_filing_date from transferor: {filing_date}"
            )

            # Prepare update
            update_payload = {
                "transferee_docket_num": current_docket_num,
                "transferee_court_id": current_court_id,
                "last_transfer_update_timestamp": datetime.now().isoformat(),
            }

            # Repository expects PascalCase keys for update operations
            key = {"FilingDate": filing_date, "DocketNum": docket_num}

            self.log_info(
                f"{log_prefix} Updating transferor {transferor_court_id}:{transferor_docket_num} "
                f"with transferee {current_court_id}:{current_docket_num}"
            )

            # Update transferor case
            self.log_info(f"{log_prefix} Attempting update with key: {key}")
            self.log_info(f"{log_prefix} Update payload: {update_payload}")

            success = await self.pacer_repository.update_item(
                key, update_payload, consistent_read_verify=False
            )

            if success:
                self.log_info(f"{log_prefix} ✅ Successfully updated transferor case")
            else:
                self.log_error(
                    f"{log_prefix} ❌ Failed to update transferor case - repository update_item returned False"
                )
                self.log_error(f"{log_prefix} This could be due to:")
                self.log_error(f"{log_prefix}   1. DynamoDB permissions/access issues")
                self.log_error(f"{log_prefix}   2. Primary key mismatch")
                self.log_error(
                    f"{log_prefix}   3. Table doesn't exist or is in different region"
                )
                self.log_error(f"{log_prefix}   4. Network connectivity issues")
                self.log_error(f"{log_prefix} Key used: {key}")
                self.log_error(f"{log_prefix} Payload used: {update_payload}")
                self.log_error(
                    f"{log_prefix} Repository type: {type(self.pacer_repository)}"
                )

                # In development, this might be expected behavior
                is_dev_env = self.config.get("environment", "").lower() in [
                    "dev",
                    "development",
                    "test",
                ]
                if is_dev_env:
                    self.log_warning(
                        f"{log_prefix} Development environment detected - DynamoDB updates may be disabled"
                    )

        except Exception as e:
            self.log_error(f"{log_prefix} Error updating transferor case: {e}")

    def detect_removal_keywords(self, html_content: str) -> bool:
        """
        Detect removal keywords in HTML content.

        Args:
            html_content: HTML content to search

        Returns:
            True if removal keywords found
        """
        removal_keywords = [
            "REMOVAL",
            "REMOVED FROM",
            "NOTICE OF REMOVAL",
            "PETITION FOR REMOVAL",
            "REMOVED FROM STATE COURT",
        ]

        content_upper = html_content.upper()
        for keyword in removal_keywords:
            if keyword in content_upper:
                self.log_info(f"Found removal keyword: '{keyword}'")
                return True

        return False

    def detect_transfer_keywords(self, html_content: str) -> bool:
        """
        Detect transfer keywords in HTML content.

        Args:
            html_content: HTML content to search

        Returns:
            True if transfer keywords found
        """
        transfer_keywords = [
            "TRANSFER",
            "TRANSFERRED FROM",
            "TRANSFERRED TO",
            "MULTIDISTRICT LITIGATION",
            "MDL TRANSFER",
            "CASE TRANSFERRED",
        ]

        content_upper = html_content.upper()
        for keyword in transfer_keywords:
            if keyword in content_upper:
                self.log_info(f"Found transfer keyword: '{keyword}'")
                return True

        return False