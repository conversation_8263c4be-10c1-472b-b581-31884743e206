# /src/services/pacer/_case_processing_components/html_parser.py
from typing import Any, Dict, List

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.services.html.case_parser_service import CaseParserService


class HtmlParser(ComponentImplementation):
    """
    Component for parsing HTML content to extract case-related information,
    specifically attorneys. It acts as a wrapper around the shared
    CaseParserService.
    """

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes the HTML parsing action.

        Args:
            data (Dict): A dictionary containing:
                - html_content (str): The HTML content to parse.

        Returns:
            List[Dict[str, str]]: A list of unique attorney dictionaries.
        """
        action = data.get("action")
        if action == "extract_attorneys":
            html_content = data.get("html_content")
            if not html_content:
                self.log_warning("No HTML content provided for attorney extraction.")
                return []
            return await self._extract_and_deduplicate_attorneys(html_content)
        else:
            raise ValueError(f"Unknown action: {action}")

    async def _extract_and_deduplicate_attorneys(
        self, html_content: str
    ) -> List[Dict[str, str]]:
        """
        Extracts attorneys from HTML content and deduplicates them.

        This method uses the centralized logic in the shared CaseParserService.

        Args:
            html_content: HTML content to parse.

        Returns:
            List of unique attorney dictionaries.
        """
        try:
            # CaseParserService is synchronous, so we don't need to await it,
            # but we keep this method async to conform to the Component pattern.
            parser = CaseParserService(self.logger, html_content)
            parsed_content = parser.parse(enable_preprocessing=True, enable_fallback=True)

            # Get attorneys directly from parser result
            attorneys = parsed_content.get("attorney", [])
            
            # DEBUGGING: Log more details about parsing
            plaintiffs = parsed_content.get("plaintiffs", [])
            defendants = parsed_content.get("defendants", [])
            case_info = parsed_content.get("case_info", {})
            
            self.log_info(f"HTML parsing results: plaintiffs={len(plaintiffs)}, defendants={len(defendants)}, attorneys={len(attorneys)}")
            self.log_debug(f"Case info keys: {list(case_info.keys())}")
            
            # ENHANCED DEBUGGING: Log plaintiff details to understand why attorneys aren't extracted
            for i, plaintiff in enumerate(plaintiffs):
                if isinstance(plaintiff, dict):
                    plaintiff_attorneys = plaintiff.get("attorneys", [])
                    self.log_info(f"Plaintiff {i}: name='{plaintiff.get('name', 'N/A')}', attorneys_count={len(plaintiff_attorneys)}")
                    for j, att in enumerate(plaintiff_attorneys):
                        if isinstance(att, dict):
                            self.log_info(f"  Attorney {j}: name='{att.get('attorney_name', 'N/A')}', law_firm='{att.get('law_firm', 'N/A')}'")
                else:
                    self.log_info(f"Plaintiff {i}: {plaintiff}")
            
            if len(attorneys) == 0 and len(plaintiffs) > 0:
                self.log_warning("No attorneys found despite having plaintiffs - check attorney extraction logic")
            elif len(attorneys) == 0 and len(plaintiffs) == 0:
                self.log_warning("No plaintiffs OR attorneys found - check HTML structure parsing")
            
            return attorneys
        except Exception as e:
            self.log_error(f"Error extracting attorneys from HTML: {e}")
            return []
