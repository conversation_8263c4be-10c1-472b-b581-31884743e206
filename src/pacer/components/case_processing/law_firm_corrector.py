# /src/services/pacer/_case_processing_components/law_firm_corrector.py
import re
from typing import Any, Dict, List, Optional

from src.infrastructure.patterns.component_base import ComponentImplementation


class LawFirmCorrector(ComponentImplementation):
    """
    Component responsible for correcting law firm fields that mistakenly
    contain street addresses.
    """

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes the law firm correction action.

        Args:
            data (Dict): A dictionary containing:
                - attorneys (List[Dict]): The list of attorney records to process.

        Returns:
            List[Dict[str, str]]: The list of attorneys with corrected law firm names.
        """
        action = data.get("action")
        if action == "correct_law_firms":
            attorneys = data.get("attorneys")
            if not attorneys:
                self.log_warning("No attorneys provided for law firm correction.")
                return []
            return self._correct_street_address_law_firms(attorneys)
        else:
            raise ValueError(f"Unknown action: {action}")

    def _correct_street_address_law_firms(
        self, attorneys: List[Dict[str, str]]
    ) -> List[Dict[str, str]]:
        """
        Corrects law firm fields that contain street addresses by finding the proper
        law firm name from other attorneys with the same email domain.

        Args:
            attorneys: List of attorney dictionaries.

        Returns:
            List of attorneys with corrected law firm names.
        """
        # Build mapping of email domains to proper law firm names
        domain_to_law_firm = {}

        # First pass: collect all non-address law firms by domain
        for attorney in attorneys:
            if not isinstance(attorney, dict):
                continue

            email = attorney.get('email', '')
            law_firm = attorney.get('law_firm', '')

            if email and law_firm:
                domain = self._extract_email_domain(email)
                if domain and not self._is_street_address(law_firm):
                    if domain not in domain_to_law_firm:
                        domain_to_law_firm[domain] = law_firm
                        self.log_debug(f"Found law firm '{law_firm}' for domain '{domain}'")

        # Second pass: correct street addresses using domain mapping
        corrected_count = 0
        for attorney in attorneys:
            if not isinstance(attorney, dict):
                continue

            email = attorney.get('email', '')
            law_firm = attorney.get('law_firm', '')

            if email and law_firm and self._is_street_address(law_firm):
                domain = self._extract_email_domain(email)
                if domain and domain in domain_to_law_firm:
                    correct_firm = domain_to_law_firm[domain]
                    self.log_info(
                        f"Correcting law firm for {attorney.get('attorney_name', 'Unknown')}: "
                        f"'{law_firm}' -> '{correct_firm}' (based on @{domain})"
                    )
                    attorney['law_firm'] = correct_firm
                    corrected_count += 1
                else:
                    self.log_warning(
                        f"Detected street address '{law_firm}' for "
                        f"{attorney.get('attorney_name', 'Unknown')} "
                        f"but no other attorney found with domain @{domain or 'unknown'}"
                    )

        if corrected_count > 0:
            self.log_info(f"Corrected {corrected_count} street addresses in law firm fields.")

        return attorneys

    @staticmethod
    def _extract_email_domain(email: str) -> Optional[str]:
        """Extracts domain from an email address."""
        if not email or '@' not in email:
            return None
        try:
            domain = email.strip().lower().split('@')[1]
            return re.sub(r'[^\w.-]', '', domain) or None
        except (IndexError, AttributeError):
            return None

    @staticmethod
    def _is_street_address(text: str) -> bool:
        """Detects if a string is likely a street address."""
        if not text:
            return False
        text_lower = text.lower().strip()

        # Common street address indicators
        street_indicators = [
            'street', 'st.', 'st ', 'avenue', 'ave.', 'ave ', 'road', 'rd.', 'rd ',
            'drive', 'dr.', 'dr ', 'boulevard', 'blvd.', 'blvd ', 'lane', 'ln.',
            'ln ', 'way', 'plaza', 'place', 'pl.', 'pl ', 'court', 'ct.', 'ct ',
            'suite', 'ste.', 'ste ', 'floor', 'fl.', 'fl '
        ]

        # Check for indicators
        if any(indicator in text_lower for indicator in street_indicators):
            # Require a number for most indicators to reduce false positives
            if re.search(r'\d', text):
                return True

        # Check for patterns like "123 Main St"
        if re.match(r'^\d+\s+[A-Za-z]', text):
            return True

        # Check for P.O. Box
        if re.search(r'p\.?o\.?\s*box', text_lower):
            return True

        return False
