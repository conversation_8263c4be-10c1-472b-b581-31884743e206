# /src/services/pacer/_case_processing_components/case_transformer.py
from __future__ import annotations
import re
from datetime import datetime
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class CaseTransformer(ComponentImplementation):
    """Transforms case data into the final format."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'transform_case':
            return self.transform_case(data['case_details'])
        else:
            raise PacerServiceError(f"Unknown action for CaseTransformer: {action}")

    def transform_case(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Performs all transformation steps on the case details.
        """
        transformed_details = self._create_base_filename(case_details)
        transformed_details = self._normalize_field_names(transformed_details)
        transformed_details = self._format_dates(transformed_details)
        transformed_details = self._ensure_required_fields(transformed_details)
        transformed_details = self._add_processing_metadata(transformed_details)
        transformed_details = self._clean_dict_values(transformed_details)
        return transformed_details

    def _create_base_filename(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Creates a base filename for the case with correct docket number parsing."""
        try:
            court_id = case_details.get("court_id", "unknown")
            docket_num = case_details.get("docket_num", "unknown")
            versus = case_details.get("versus", "unknown_versus")

            # CRITICAL FIX: Extract only numeric parts from docket number
            # Current: "1:25-cv-09395" -> "ilnd_25_cv_09395_..." (WRONG - includes "cv")  
            # Required: "1:25-cv-09395" -> "ilnd_25_09395_..." (CORRECT - excludes "cv")
            clean_docket = self._parse_docket_numeric_only(docket_num)

            # Clean versus - remove ALL punctuation, keep only alphanumeric and spaces
            clean_versus = re.sub(r'[^\w\s]', '', versus)  # L'Oreal -> LOreal, remove all punctuation
            # Replace multiple spaces with single underscore and strip
            clean_versus = re.sub(r'\s+', '_', clean_versus).strip('_')
            # Limit length
            clean_versus = clean_versus[:60]

            # Create base filename with numeric-only docket format
            base_filename = f"{court_id.lower()}_{clean_docket}_{clean_versus}"
            # Clean any remaining problematic characters
            base_filename = re.sub(r'[^\w_-]+', '', base_filename)
            
            self.log_debug(f"Generated base filename: '{docket_num}' -> '{base_filename}'")
            case_details['base_filename'] = base_filename
            return case_details
        except Exception as e:
            self.log_error(f"Error creating base filename: {e}")
            case_details['base_filename'] = f"{case_details.get('court_id', 'unknown')}_error"
            return case_details

    def _parse_docket_numeric_only(self, docket_num: str) -> str:
        """
        Extract only numeric parts from docket number, excluding case type letters.
        
        This implements the CRITICAL fix for filename generation:
        - "1:25-cv-09395" -> "25_09395" (excludes "cv")
        - "2:24-cr-12345" -> "24_12345" (excludes "cr")
        - "4:24-md-03087" -> "24_03087" (excludes "md")
        
        Args:
            docket_num: Raw docket number string
            
        Returns:
            Numeric-only parts in YY_NNNNN format
        """
        # Use the centralized utility function to ensure consistency
        try:
            from src.utils.docket_utils import parse_docket_number_numeric_only
            return parse_docket_number_numeric_only(docket_num)
        except ImportError:
            self.log_warning("Could not import docket_utils, using fallback parsing")
            return self._fallback_parse_numeric_only(docket_num)
    
    def _fallback_parse_numeric_only(self, docket_num: str) -> str:
        """Fallback parsing when docket_utils is not available."""
        if not docket_num or ':' not in docket_num:
            self.log_warning(f"Invalid docket format for numeric parsing: '{docket_num}'")
            return "unknown_docket"
        
        try:
            # Use regex to extract numeric parts: N:YY-XX-NNNNN -> YY_NNNNN
            pattern = r'\d+:(\d{2})-[a-zA-Z]{2}-(\d{1,5})'
            match = re.search(pattern, docket_num.strip())
            
            if not match:
                self.log_warning(f"No valid numeric docket pattern found in: '{docket_num}'")
                # Fallback: try to extract any numbers after colon
                fallback_pattern = r':(\d+).*?(\d+)'
                fallback_match = re.search(fallback_pattern, docket_num)
                if fallback_match:
                    year_part = fallback_match.group(1)[:2].zfill(2)  # Take first 2 digits, zero-pad
                    case_part = fallback_match.group(2).zfill(5)[:5]   # Take digits, zero-pad to 5, max 5
                    result = f"{year_part}_{case_part}"
                    self.log_info(f"Used fallback parsing: '{docket_num}' -> '{result}'")
                    return result
                return "unknown_docket"
                
            year = match.group(1)          # YY (2 digits)
            case_number = match.group(2)   # NNNNN (1-5 digits)
            
            # Zero-pad case number to exactly 5 digits
            case_number_padded = case_number.zfill(5)
            
            # Create YY_NNNNN format (numeric only)
            result = f"{year}_{case_number_padded}"
            
            self.log_debug(f"Parsed numeric docket: '{docket_num}' -> '{result}'")
            return result
            
        except Exception as e:
            self.log_error(f"Error parsing numeric docket from '{docket_num}': {e}")
            return "unknown_docket"

    def _normalize_field_names(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize field names to match expected format."""
        # Ensure proper field names and structure
        if 'filing_date' in case_details and isinstance(case_details['filing_date'], str):
            # Convert MM/DD/YYYY to YYYYMMDD if needed
            filing_date = case_details['filing_date']
            if '/' in filing_date:
                try:
                    from datetime import datetime
                    parsed_date = datetime.strptime(filing_date, '%m/%d/%Y')
                    case_details['filing_date'] = parsed_date.strftime('%Y%m%d')
                except ValueError:
                    pass  # Keep original if parsing fails

        # Ensure date_filed matches filing_date
        if 'filing_date' in case_details:
            case_details['date_filed'] = case_details['filing_date']

        # Normalize defendant/defendants fields
        if 'defendants' in case_details and isinstance(case_details['defendants'], list):
            # Convert from list of dicts to list of strings
            defendant_names = []
            for defendant in case_details['defendants']:
                if isinstance(defendant, dict) and 'name' in defendant:
                    defendant_names.append(defendant['name'])
                elif isinstance(defendant, str):
                    defendant_names.append(defendant)
            case_details['defendant'] = defendant_names

        return case_details

    def _format_dates(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Format dates to proper ISO format."""
        # Add extracted_at timestamp if not present
        if 'extracted_at' not in case_details:
            case_details['extracted_at'] = datetime.now().isoformat()

        # Add iso_date if not present
        if 'iso_date' not in case_details and 'filing_date' in case_details:
            case_details['iso_date'] = case_details['filing_date']

        return case_details

    def _ensure_required_fields(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Ensure all required fields are present with proper defaults."""
        # Set default source if not present
        if 'source' not in case_details:
            case_details['source'] = 'docket_report_log'

        # Ensure flags is a list
        if 'flags' not in case_details:
            case_details['flags'] = []
        elif not isinstance(case_details['flags'], list):
            case_details['flags'] = [case_details['flags']] if case_details['flags'] else []

        # Ensure boolean fields are properly typed
        boolean_fields = ['is_removal', 'html_only', 'is_transferred', 'pending_cto']
        for field in boolean_fields:
            if field in case_details:
                if isinstance(case_details[field], str):
                    case_details[field] = case_details[field].lower() in ('true', '1', 'yes')
                elif case_details[field] is None:
                    case_details[field] = False

        # Ensure list fields are properly typed
        list_fields = ['plaintiff', 'defendant', 'attorney', 'attorneys_gpt', 'plaintiffs_gpt', 'law_firms']
        for field in list_fields:
            if field in case_details and not isinstance(case_details[field], list):
                if case_details[field] is None:
                    case_details[field] = []
                else:
                    case_details[field] = [case_details[field]]

        # Set filename fields based on base_filename
        base_filename = case_details.get('base_filename', '')
        if base_filename:
            case_details['original_filename'] = base_filename
            case_details['new_filename'] = base_filename

        return case_details

    def _add_processing_metadata(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Adds processing metadata to the case details."""
        case_details["_processed_timestamp"] = datetime.now().isoformat()
        case_details["_processed_by"] = "CaseProcessingFacadeService"
        return case_details

    def _clean_dict_values(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively cleans dictionary values."""
        cleaned = {}
        for key, value in data.items():
            if isinstance(value, dict):
                cleaned[key] = self._clean_dict_values(value)
            elif isinstance(value, str):
                cleaned[key] = value.strip()
            else:
                cleaned[key] = value
        return cleaned
