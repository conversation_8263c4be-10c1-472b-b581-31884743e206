# /src/services/pacer/_case_processing_components/field_consistency_manager.py
from typing import Any, Dict

from src.infrastructure.patterns.component_base import ComponentImplementation


class FieldConsistencyManager(ComponentImplementation):
    """
    Component responsible for ensuring field consistency in case data,
    such as handling plural/singular field names and deduplicating parties.
    """

    async def _execute_action(self, data: Any) -> Any:
        """
        Executes the field consistency action.

        Args:
            data (Dict): A dictionary containing:
                - case_details (Dict): The case data to process.

        Returns:
            Dict[str, Any]: The processed case data with consistent fields.
        """
        action = data.get("action")
        if action == "ensure_consistency":
            case_details = data.get("case_details")
            if not case_details:
                self.log_warning("No case_details provided for consistency check.")
                return {}
            return self.ensure_field_consistency(case_details)
        else:
            raise ValueError(f"Unknown action: {action}")

    def ensure_field_consistency(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Ensures field consistency and handles plural/singular conversions.

        Args:
            case_details: Case details to clean up.

        Returns:
            Cleaned case details dictionary.
        """
        # Handle plaintiff/plaintiffs consistency
        if 'plaintiffs' in case_details:
            plaintiffs = case_details['plaintiffs']
            if isinstance(plaintiffs, list):
                plaintiff_names = []
                for p in plaintiffs:
                    name = p.get('name', str(p)) if isinstance(p, dict) else str(p)
                    if name and name.strip():
                        plaintiff_names.append(name.strip())

                # Deduplicate while preserving order
                seen = set()
                case_details['plaintiff'] = [
                    x for x in plaintiff_names if not (x in seen or seen.add(x))
                ]

        # Handle defendant/defendants consistency with deduplication
        if 'defendants' in case_details:
            defendants = case_details['defendants']
            if isinstance(defendants, list):
                clean_defendants = []
                seen_defendants = set()
                for d in defendants:
                    name = d.get('name', str(d)) if isinstance(d, dict) else str(d)
                    name = name.strip()
                    if name and name.lower() not in seen_defendants:
                        clean_defendants.append(name)
                        seen_defendants.add(name.lower())
                case_details['defendant'] = clean_defendants

        # Clean up plural fields to avoid confusion
        fields_to_remove = ['plaintiffs', 'defendants', 'plaintiffs_gpt']
        for field in fields_to_remove:
            if field in case_details:
                del case_details[field]

        self.log_debug("Ensured field consistency for case details.")
        return case_details
