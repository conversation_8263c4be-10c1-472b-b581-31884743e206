# /src/services/pacer/_case_processing_components/case_enricher.py
from __future__ import annotations
import re
from typing import Any, Dict, TYPE_CHECKING

from src.infrastructure.patterns.component_base import ComponentImplementation
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol

class CaseEnricher(ComponentImplementation):
    """Enriches case data with additional information like MDL flags and removal status."""

    def __init__(self, logger: LoggerProtocol = None, config: Dict[str, Any] = None):
        super().__init__(logger, config)

    async def _execute_action(self, data: Any) -> Any:
        action = data.get('action')
        if action == 'enrich_case':
            return self.enrich_case(data['case_details'], data.get('html_content'))
        else:
            raise PacerServiceError(f"Unknown action for CaseEnricher: {action}")

    def enrich_case(self, case_details: Dict[str, Any], html_content: str = None) -> Dict[str, Any]:
        """
        Performs all enrichment steps on the case details.
        """
        enriched_details = self._process_mdl_flags(case_details)
        if html_content:
            enriched_details = self._determine_removal_status(enriched_details, html_content)
        return enriched_details

    def _process_mdl_flags(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Processes MDL flags and extracts MDL numbers."""
        flags = case_details.get("flags", [])
        if not flags:
            return case_details

        for flag in flags:
            mdl_match = re.search(r"MDL[\s-]?(\d{4,})", str(flag), re.IGNORECASE)
            if mdl_match:
                extracted_mdl_num = mdl_match.group(1)
                case_details["mdl_num"] = extracted_mdl_num
                self.log_info(f"Set mdl_num='{extracted_mdl_num}' from flag: '{flag}'")
                break
        return case_details

    def _determine_removal_status(self, case_details: Dict[str, Any], html_content: str) -> Dict[str, Any]:
        """Determines the final removal status based on HTML content."""
        # Simplified logic from PacerCaseProcessingService
        removal_pattern = r"(?:notice\s+of\s+removal|petition\s+for\s+removal)"
        if re.search(removal_pattern, html_content, re.IGNORECASE):
            case_details['is_removal'] = True
            self.log_info("Detected notice of removal in HTML content.")
        else:
            case_details['is_removal'] = False

        return case_details
