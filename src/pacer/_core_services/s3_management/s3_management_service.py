"""
S3 Management Service

Handles AWS S3 operations including upload, download, and bucket management.
Provides secure and efficient file storage in the cloud.
This is a bridge service that uses the underlying S3Service for actual operations.
"""

from typing import Any, Dict, Optional
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.services.s3_service import S3Service


class S3ManagementService(AsyncServiceBase):
    """
    S3 Management Service for PACER operations.
    
    This service acts as a bridge to the core S3Service, providing
    PACER-specific S3 operations and HTML upload functionality.
    """
    
    def __init__(self, logger=None, config=None, config_service=None):
        super().__init__(logger, config)
        self.config_service = config_service
        
        # Initialize the underlying S3Service
        self._s3_service = S3Service(
            logger=logger,
            config=config,
            config_service=config_service
        )
        
    async def _initialize_service(self) -> None:
        """Initialize the S3 management service."""
        # Initialize the underlying S3Service
        await self._s3_service.initialize()
        self.log_info("S3ManagementService initialized successfully")
    
    async def _execute_action(self, data: Any) -> Any:
        """
        Execute S3 management actions by delegating to the underlying S3Service.
        
        This method supports all S3Service actions and provides additional
        PACER-specific functionality.
        """
        # Delegate all actions to the underlying S3Service
        return await self._s3_service._execute_action(data)
    
    # Expose key S3Service methods directly for compatibility
    
    async def upload_file(self, file_path: str, object_key: str, 
                         content_type: Optional[str] = None, 
                         force_upload: bool = False) -> str:
        """Upload a file to S3 and return the S3 URL."""
        return await self._s3_service.upload_file(file_path, object_key, content_type, force_upload)
    
    async def download_file(self, s3_key: str, local_file_path: str) -> bool:
        """Download a file from S3 to local path."""
        return await self._s3_service.download_file(s3_key, local_file_path)
    
    async def file_exists(self, s3_key: str) -> bool:
        """Check if a file exists in S3."""
        return await self._s3_service.file_exists(s3_key)
    
    async def delete_file(self, s3_key: str) -> bool:
        """Delete a file from S3."""
        return await self._s3_service.delete_file(s3_key)
    
    async def health_check(self) -> Dict[str, Any]:
        """Return service health status."""
        s3_health = await self._s3_service.health_check()
        return {
            'service': 'S3ManagementService',
            'status': 'healthy' if self._initialized else 'not_initialized',
            'underlying_s3_service': s3_health
        }
    
    @property
    def enabled(self) -> bool:
        """Check if S3 service is enabled."""
        return self._s3_service.enabled
    
    @property
    def bucket_name(self) -> Optional[str]:
        """Get the configured bucket name."""
        return self._s3_service.bucket_name