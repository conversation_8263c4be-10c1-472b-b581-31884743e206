"""
Download Path Integration for PACER Download Orchestration Service

This module provides integration between the file path utilities and the 
download orchestration service, demonstrating how to:

1. Generate proper temporary and final paths during download workflow
2. Integrate path generation with existing download orchestration
3. Handle path management throughout the download lifecycle
4. Provide clean integration points for S3 uploads and file management

Integration Points:
- DownloadOrchestrationService.process_download_workflow
- File downloader components
- S3 upload manager
- Artifact checker and validation
"""

import logging
from typing import Dict, Any, Optional, Tuple
from pathlib import Path

from src.pacer.utils.file_path_utils import FilePathBuilder, validate_path_safety
from src.utils.file_utils import ensure_dir_exists
from src.infrastructure.patterns.component_base import ComponentImplementation


class DownloadPathManager(ComponentImplementation):
    """
    Manages file paths throughout the download orchestration workflow.
    
    This class serves as the integration point between file path utilities
    and the download orchestration service, providing a clean interface
    for path management during the entire download lifecycle.
    """

    def __init__(self, 
                 logger: logging.Logger,
                 base_data_dir: str = "data"):
        """
        Initialize the DownloadPathManager.
        
        Args:
            logger: Logger instance (court-specific logger from DI)
            base_data_dir: Base directory for all data storage
        """
        # Initialize base class with logger
        super().__init__(logger)
        self.base_data_dir = base_data_dir
        self.path_builder = FilePathBuilder(base_data_dir, logger)

    def prepare_download_paths(self, 
                              case_details: Dict[str, Any], 
                              court_id: str,
                              iso_date: Optional[str] = None) -> Dict[str, str]:
        """
        Prepare all necessary paths for a download workflow.
        
        This method generates and validates all paths needed during the download
        process, including temporary processing directories and final storage locations.
        
        Args:
            case_details: Case information dictionary
            court_id: Court identifier
            iso_date: ISO date string, defaults to current date
            
        Returns:
            Dictionary containing all prepared paths
            
        Example:
            >>> manager.prepare_download_paths(case_details, "ilnd")
            {
                'temp_dir': '/data/2025-08-11/dockets/temp/ilnd_25_09395_smith_v_acme_a1b2/',
                'final_pdf_path': '/data/2025-08-11/dockets/ilnd_25_09395_smith_v_acme.pdf',
                'final_html_path': '/data/2025-08-11/dockets/ilnd_25_09395_smith_v_acme.html',
                's3_html_key': 'dockets/2025-08-11/ilnd/ilnd_25_09395_smith_v_acme.html',
                's3_pdf_key': 'dockets/2025-08-11/ilnd/ilnd_25_09395_smith_v_acme.pdf',
                'working_dir': '/data/2025-08-11/dockets/',
                'components': {...}
            }
        """
        try:
            self.logger.info(f"Preparing download paths for case {case_details.get('docket_num', 'unknown')}")
            
            # Generate temporary directory path
            temp_dir = self.path_builder.generate_temporary_path(court_id, case_details, iso_date)
            
            # Generate final paths for different file types
            final_pdf_path = self.path_builder.generate_final_path(court_id, case_details, iso_date, "pdf")
            final_html_path = self.path_builder.generate_final_path(court_id, case_details, iso_date, "html")
            
            # Generate S3 keys
            s3_html_key = self.path_builder.generate_s3_key(court_id, case_details, iso_date, "html")
            s3_pdf_key = self.path_builder.generate_s3_key(court_id, case_details, iso_date, "pdf")
            
            # Get working directory (parent of final paths)
            working_dir = str(Path(final_pdf_path).parent)
            
            # Get path components for additional use
            components = self.path_builder.get_path_components(case_details)
            
            # Validate all paths for safety
            paths_to_validate = [temp_dir, final_pdf_path, final_html_path, working_dir]
            for path in paths_to_validate:
                if not validate_path_safety(path):
                    raise ValueError(f"Generated unsafe path: {path}")
            
            # Prepare return dictionary
            paths = {
                'temp_dir': temp_dir,
                'final_pdf_path': final_pdf_path,
                'final_html_path': final_html_path,
                's3_html_key': s3_html_key,
                's3_pdf_key': s3_pdf_key,
                'working_dir': working_dir,
                'components': components,
                'iso_date': iso_date or components['iso_date']
            }
            
            self.logger.debug(f"Prepared download paths: {paths}")
            return paths
            
        except Exception as e:
            self.logger.error(f"Failed to prepare download paths: {e}")
            raise

    def setup_working_directories(self, paths: Dict[str, str]) -> bool:
        """
        Create all necessary working directories for the download process.
        
        Args:
            paths: Dictionary of paths from prepare_download_paths()
            
        Returns:
            True if all directories were created successfully
        """
        try:
            directories_to_create = [
                paths['temp_dir'],
                paths['working_dir']
            ]
            
            for directory in directories_to_create:
                if not self.path_builder.ensure_directory_exists(directory):
                    self.logger.error(f"Failed to create directory: {directory}")
                    return False
                    
            self.logger.info("Successfully created all working directories")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup working directories: {e}")
            return False

    def move_to_final_location(self, 
                              source_path: str, 
                              destination_type: str,
                              paths: Dict[str, str]) -> Optional[str]:
        """
        Move a file from temporary location to final storage location.
        
        Args:
            source_path: Path to source file
            destination_type: Type of destination ('pdf' or 'html')
            paths: Dictionary of paths from prepare_download_paths()
            
        Returns:
            Final path if successful, None if failed
        """
        try:
            if destination_type == 'pdf':
                final_path = paths['final_pdf_path']
            elif destination_type == 'html':
                final_path = paths['final_html_path']
            else:
                raise ValueError(f"Unknown destination type: {destination_type}")
            
            source = Path(source_path)
            destination = Path(final_path)
            
            if not source.exists():
                self.logger.error(f"Source file does not exist: {source_path}")
                return None
            
            # Ensure destination directory exists
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            # Move the file
            source.rename(destination)
            
            self.logger.info(f"Successfully moved {destination_type} file to: {final_path}")
            return final_path
            
        except Exception as e:
            self.logger.error(f"Failed to move file to final location: {e}")
            return None

    def cleanup_temporary_files(self, paths: Dict[str, str]) -> bool:
        """
        Clean up temporary files and directories after download completion.
        
        Args:
            paths: Dictionary of paths from prepare_download_paths()
            
        Returns:
            True if cleanup was successful
        """
        try:
            temp_dir = Path(paths['temp_dir'])
            
            if temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir)
                self.logger.debug(f"Cleaned up temporary directory: {temp_dir}")
                
            return True
            
        except Exception as e:
            self.logger.warning(f"Failed to cleanup temporary files: {e}")
            return False

    def get_s3_upload_info(self, paths: Dict[str, str], file_type: str) -> Dict[str, str]:
        """
        Get S3 upload information for a specific file type.
        
        Args:
            paths: Dictionary of paths from prepare_download_paths()
            file_type: Type of file ('pdf' or 'html')
            
        Returns:
            Dictionary with S3 upload information
        """
        if file_type == 'pdf':
            return {
                'local_path': paths['final_pdf_path'],
                's3_key': paths['s3_pdf_key'],
                'content_type': 'application/pdf'
            }
        elif file_type == 'html':
            return {
                'local_path': paths['final_html_path'],
                's3_key': paths['s3_html_key'],
                'content_type': 'text/html'
            }
        else:
            raise ValueError(f"Unsupported file type: {file_type}")


class DownloadOrchestrationIntegration:
    """
    Integration layer for the download orchestration service.
    
    This class provides methods that can be directly integrated into
    the DownloadOrchestrationService to add path management capabilities.
    """

    @staticmethod
    def enhance_case_details_with_paths(case_details: Dict[str, Any], 
                                      court_id: str,
                                      path_manager: DownloadPathManager) -> Dict[str, Any]:
        """
        Enhance case details dictionary with path information.
        
        This method adds path-related information to the case details
        for use throughout the download workflow.
        
        Args:
            case_details: Original case details
            court_id: Court identifier
            path_manager: DownloadPathManager instance
            
        Returns:
            Enhanced case details with path information
        """
        enhanced_details = case_details.copy()
        
        # Prepare paths
        paths = path_manager.prepare_download_paths(case_details, court_id)
        
        # Add path information to case details
        enhanced_details.update({
            '_paths': paths,
            'temp_working_dir': paths['temp_dir'],
            'final_pdf_path': paths['final_pdf_path'],
            'final_html_path': paths['final_html_path'],
            'base_filename': Path(paths['final_pdf_path']).stem  # For backwards compatibility
        })
        
        return enhanced_details

    @staticmethod
    def prepare_download_context_with_paths(case_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare download context with path information.
        
        This method can be used in the _prepare_download_context method
        of DownloadOrchestrationService to add path management.
        
        Args:
            case_details: Case details with path information
            
        Returns:
            Download context dictionary
        """
        paths = case_details.get('_paths', {})
        
        return {
            'temp_working_dir': paths.get('temp_dir'),
            'final_pdf_path': paths.get('final_pdf_path'),
            'final_html_path': paths.get('final_html_path'),
            's3_keys': {
                'html': paths.get('s3_html_key'),
                'pdf': paths.get('s3_pdf_key')
            },
            'working_dir': paths.get('working_dir'),
            'path_components': paths.get('components', {})
        }

    @staticmethod
    async def process_download_workflow_with_paths(orchestrator_service,
                                                 case_details: Dict[str, Any],
                                                 court_id: str,
                                                 is_explicitly_requested: bool = False,
                                                 page=None,
                                                 docket_num: str = "",
                                                 court_logger=None) -> Dict[str, Any]:
        """
        Enhanced download workflow processing with integrated path management.
        
        This is an example of how to integrate the path management into
        the existing download orchestration workflow.
        
        Args:
            orchestrator_service: DownloadOrchestrationService instance
            case_details: Case details dictionary
            court_id: Court identifier
            is_explicitly_requested: Whether download is explicitly requested
            page: Browser page object
            docket_num: Docket number for logging
            court_logger: Court-specific logger
            
        Returns:
            Updated case details with download results and path information
        """
        # Initialize path manager
        path_manager = DownloadPathManager()
        
        # Enhance case details with path information
        enhanced_case_details = DownloadOrchestrationIntegration.enhance_case_details_with_paths(
            case_details, court_id, path_manager
        )
        
        # Setup working directories
        paths = enhanced_case_details['_paths']
        if not path_manager.setup_working_directories(paths):
            return {
                **enhanced_case_details,
                'is_downloaded': False,
                'download_failed': True,
                'download_error': 'Failed to setup working directories'
            }
        
        # Call the original download workflow
        result = await orchestrator_service.process_download_workflow(
            enhanced_case_details,
            is_explicitly_requested,
            page,
            court_id,
            docket_num,
            court_logger
        )
        
        # Add path management to result
        result['_paths'] = paths
        result['path_components'] = paths.get('components', {})
        
        # Cleanup temporary files if download completed
        if result.get('is_downloaded') or result.get('download_failed'):
            path_manager.cleanup_temporary_files(paths)
        
        return result


# Example usage functions
def example_integration_usage():
    """
    Example of how to use the download path integration.
    """
    
    # Initialize path manager
    path_manager = DownloadPathManager(base_data_dir="./data")
    
    # Sample case details
    case_details = {
        'docket_num': '1:25-cv-09395',
        'title': 'Williams v. Avlon Industries, Inc. et al',
        'court_id': 'ilnd'
    }
    
    # Prepare paths
    paths = path_manager.prepare_download_paths(case_details, "ilnd", "2025-08-11")
    
    # Setup directories
    path_manager.setup_working_directories(paths)
    
    # Example: Move downloaded file to final location
    # (This would be called after actual download)
    # final_path = path_manager.move_to_final_location(
    #     "/tmp/downloaded_file.pdf", 
    #     "pdf", 
    #     paths
    # )
    
    # Get S3 upload information
    s3_info = path_manager.get_s3_upload_info(paths, "html")
    
    # Example output (use proper logging in production)
    # Temporary directory: {paths['temp_dir']}
    # Final PDF path: {paths['final_pdf_path']} 
    # S3 HTML key: {s3_info['s3_key']}
    
    # Cleanup when done
    path_manager.cleanup_temporary_files(paths)


if __name__ == "__main__":
    example_integration_usage()