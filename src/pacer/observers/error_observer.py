"""
Error observer implementations for PACER services.

Provides centralized error handling and notification.
"""

from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import traceback
import asyncio

from src.infrastructure.protocols.logger import LoggerProtocol
from src.infrastructure.protocols.exceptions import ServiceError
from .event_bus import Event, EventType, Observer, AsyncObserver


class ErrorSeverity(Enum):
    """Error severity levels."""
    DEBUG = 0
    INFO = 1
    WARNING = 2
    ERROR = 3
    CRITICAL = 4
    FATAL = 5


@dataclass
class ErrorEvent(Event):
    """Event for error notifications."""
    error_id: str = ""
    severity: ErrorSeverity = ErrorSeverity.ERROR
    error_type: str = ""
    error_message: str = ""
    error_details: Optional[str] = None
    stack_trace: Optional[str] = None
    service_name: str = ""
    operation: str = ""
    recoverable: bool = True
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if not self.event_type:
            self.event_type = EventType.ERROR_OCCURRED
        
        # Capture stack trace if exception is available
        if not self.stack_trace and self.data.get('exception'):
            self.stack_trace = traceback.format_exc()


@dataclass
class ErrorStatistics:
    """Statistics for error tracking."""
    total_errors: int = 0
    errors_by_severity: Dict[ErrorSeverity, int] = field(default_factory=dict)
    errors_by_type: Dict[str, int] = field(default_factory=dict)
    errors_by_service: Dict[str, int] = field(default_factory=dict)
    recovered_errors: int = 0
    failed_retries: int = 0
    last_error_time: Optional[datetime] = None
    
    def update(self, error_event: ErrorEvent) -> None:
        """Update statistics with new error."""
        self.total_errors += 1
        self.last_error_time = error_event.timestamp
        
        # Update by severity
        if error_event.severity not in self.errors_by_severity:
            self.errors_by_severity[error_event.severity] = 0
        self.errors_by_severity[error_event.severity] += 1
        
        # Update by type
        if error_event.error_type not in self.errors_by_type:
            self.errors_by_type[error_event.error_type] = 0
        self.errors_by_type[error_event.error_type] += 1
        
        # Update by service
        if error_event.service_name not in self.errors_by_service:
            self.errors_by_service[error_event.service_name] = 0
        self.errors_by_service[error_event.service_name] += 1


class ErrorObserver(Observer):
    """
    Observer for handling error events.
    """
    
    def __init__(self, logger: LoggerProtocol,
                 error_callback: Optional[Callable[[ErrorEvent], None]] = None):
        self.logger = logger
        self.error_callback = error_callback
        self._error_history: List[ErrorEvent] = []
        self._max_history = 100
        self._statistics = ErrorStatistics()
    
    def handle(self, event: Event) -> None:
        """Handle error events."""
        if isinstance(event, ErrorEvent):
            self._handle_error(event)
    
    def _handle_error(self, error_event: ErrorEvent) -> None:
        """Process an error event."""
        # Update statistics
        self._statistics.update(error_event)
        
        # Add to history
        self._error_history.append(error_event)
        if len(self._error_history) > self._max_history:
            self._error_history = self._error_history[-self._max_history:]
        
        # Log based on severity
        self._log_error(error_event)
        
        # Call callback if provided
        if self.error_callback:
            try:
                self.error_callback(error_event)
            except Exception as e:
                self.logger.error(f"Error callback failed: {str(e)}")
        
        # Check if we should escalate
        if self._should_escalate(error_event):
            self._escalate_error(error_event)
    
    def _log_error(self, error_event: ErrorEvent) -> None:
        """Log error based on severity."""
        message = (
            f"[{error_event.service_name}] {error_event.operation}: "
            f"{error_event.error_message}"
        )
        
        if error_event.severity == ErrorSeverity.DEBUG:
            self.logger.debug(message)
        elif error_event.severity == ErrorSeverity.INFO:
            self.logger.info(message)
        elif error_event.severity == ErrorSeverity.WARNING:
            self.logger.warning(message)
        elif error_event.severity in [ErrorSeverity.ERROR, ErrorSeverity.CRITICAL]:
            self.logger.error(message)
            if error_event.stack_trace:
                self.logger.error(f"Stack trace:\n{error_event.stack_trace}")
        elif error_event.severity == ErrorSeverity.FATAL:
            self.logger.critical(message)
            if error_event.stack_trace:
                self.logger.critical(f"Stack trace:\n{error_event.stack_trace}")
    
    def _should_escalate(self, error_event: ErrorEvent) -> bool:
        """Check if error should be escalated."""
        # Escalate critical and fatal errors
        if error_event.severity in [ErrorSeverity.CRITICAL, ErrorSeverity.FATAL]:
            return True
        
        # Escalate if max retries exceeded
        if error_event.retry_count >= error_event.max_retries:
            return True
        
        # Check error rate
        recent_errors = [
            e for e in self._error_history[-10:]
            if (datetime.now() - e.timestamp).total_seconds() < 60
        ]
        if len(recent_errors) >= 5:
            return True
        
        return False
    
    def _escalate_error(self, error_event: ErrorEvent) -> None:
        """Escalate critical error."""
        self.logger.critical(
            f"ESCALATION: Critical error in {error_event.service_name} - "
            f"{error_event.error_message}"
        )
        # In production, this could trigger alerts, emails, etc.
    
    def get_statistics(self) -> ErrorStatistics:
        """Get error statistics."""
        return self._statistics
    
    def get_error_history(self, limit: Optional[int] = None) -> List[ErrorEvent]:
        """Get error history."""
        if limit:
            return self._error_history[-limit:]
        return self._error_history.copy()


class ErrorCollector(AsyncObserver):
    """
    Async observer that collects and aggregates errors.
    """
    
    def __init__(self, logger: LoggerProtocol):
        self.logger = logger
        self._errors: Dict[str, List[ErrorEvent]] = {}
        self._lock = asyncio.Lock()
        self._alert_thresholds = {
            ErrorSeverity.WARNING: 10,
            ErrorSeverity.ERROR: 5,
            ErrorSeverity.CRITICAL: 1
        }
    
    async def handle(self, event: Event) -> None:
        """Handle error events asynchronously."""
        if isinstance(event, ErrorEvent):
            await self._collect_error(event)
    
    async def _collect_error(self, error_event: ErrorEvent) -> None:
        """Collect and categorize error."""
        async with self._lock:
            key = f"{error_event.service_name}:{error_event.error_type}"
            
            if key not in self._errors:
                self._errors[key] = []
            
            self._errors[key].append(error_event)
            
            # Check thresholds
            await self._check_thresholds(key, error_event.severity)
    
    async def _check_thresholds(self, key: str, severity: ErrorSeverity) -> None:
        """Check if error thresholds are exceeded."""
        errors = self._errors[key]
        threshold = self._alert_thresholds.get(severity)
        
        if threshold and len(errors) >= threshold:
            # Recent errors within last 5 minutes
            recent_errors = [
                e for e in errors
                if (datetime.now() - e.timestamp).total_seconds() < 300
            ]
            
            if len(recent_errors) >= threshold:
                await self._trigger_alert(key, severity, recent_errors)
    
    async def _trigger_alert(self, key: str, severity: ErrorSeverity, 
                           errors: List[ErrorEvent]) -> None:
        """Trigger alert for threshold breach."""
        self.logger.warning(
            f"Alert: {len(errors)} {severity.name} errors for {key} in last 5 minutes"
        )
        
        # In production, this could send notifications
        # For now, just log
        for error in errors[:3]:  # Show first 3 errors
            self.logger.warning(f"  - {error.error_message}")
    
    async def get_error_summary(self) -> Dict[str, Any]:
        """Get error summary."""
        async with self._lock:
            summary = {
                'total_error_types': len(self._errors),
                'total_errors': sum(len(errors) for errors in self._errors.values()),
                'errors_by_service': {},
                'top_errors': []
            }
            
            # Aggregate by service
            for key, errors in self._errors.items():
                service_name = key.split(':')[0]
                if service_name not in summary['errors_by_service']:
                    summary['errors_by_service'][service_name] = 0
                summary['errors_by_service'][service_name] += len(errors)
            
            # Get top error types
            sorted_errors = sorted(
                self._errors.items(),
                key=lambda x: len(x[1]),
                reverse=True
            )
            
            for key, errors in sorted_errors[:5]:
                summary['top_errors'].append({
                    'type': key,
                    'count': len(errors),
                    'last_error': errors[-1].error_message if errors else None
                })
            
            return summary
    
    async def clear_old_errors(self, age_seconds: int = 3600) -> None:
        """Clear errors older than specified age."""
        async with self._lock:
            cutoff_time = datetime.now()
            
            for key in list(self._errors.keys()):
                self._errors[key] = [
                    e for e in self._errors[key]
                    if (cutoff_time - e.timestamp).total_seconds() < age_seconds
                ]
                
                # Remove key if no errors remain
                if not self._errors[key]:
                    del self._errors[key]


class ErrorNotifier(Observer):
    """
    Observer that sends error notifications.
    """
    
    def __init__(self, logger: LoggerProtocol,
                 notification_handlers: Optional[List[Callable]] = None):
        self.logger = logger
        self.notification_handlers = notification_handlers or []
        self._notification_cooldown = {}  # Prevent spam
        self._cooldown_seconds = 60
    
    def handle(self, event: Event) -> None:
        """Handle error events for notification."""
        if isinstance(event, ErrorEvent):
            if self._should_notify(event):
                self._send_notifications(event)
    
    def _should_notify(self, error_event: ErrorEvent) -> bool:
        """Check if notification should be sent."""
        # Only notify for severe errors
        if error_event.severity.value < ErrorSeverity.ERROR.value:
            return False
        
        # Check cooldown
        key = f"{error_event.service_name}:{error_event.error_type}"
        if key in self._notification_cooldown:
            last_notified = self._notification_cooldown[key]
            if (datetime.now() - last_notified).total_seconds() < self._cooldown_seconds:
                return False
        
        return True
    
    def _send_notifications(self, error_event: ErrorEvent) -> None:
        """Send error notifications."""
        # Update cooldown
        key = f"{error_event.service_name}:{error_event.error_type}"
        self._notification_cooldown[key] = datetime.now()
        
        # Send to all handlers
        for handler in self.notification_handlers:
            try:
                handler(error_event)
            except Exception as e:
                self.logger.error(f"Notification handler failed: {str(e)}")
    
    def add_notification_handler(self, handler: Callable) -> None:
        """Add a notification handler."""
        self.notification_handlers.append(handler)
    
    def remove_notification_handler(self, handler: Callable) -> None:
        """Remove a notification handler."""
        if handler in self.notification_handlers:
            self.notification_handlers.remove(handler)