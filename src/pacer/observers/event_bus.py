"""
Event bus implementation for PACER services.

Provides a central event dispatcher for decoupled communication
between components using the Observer pattern.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, Set
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import asyncio
import uuid
from collections import defaultdict
import weakref

from src.infrastructure.protocols.logger import LoggerProtocol


class EventType(Enum):
    """Standard event types in PACER system."""
    # Processing events
    PROCESSING_STARTED = "processing_started"
    PROCESSING_COMPLETED = "processing_completed"
    PROCESSING_FAILED = "processing_failed"
    
    # Download events
    DOWNLOAD_STARTED = "download_started"
    DOWNLOAD_PROGRESS = "download_progress"
    DOWNLOAD_COMPLETED = "download_completed"
    DOWNLOAD_FAILED = "download_failed"
    
    # Authentication events
    AUTH_SUCCESS = "auth_success"
    AUTH_FAILED = "auth_failed"
    SESSION_EXPIRED = "session_expired"
    
    # Case events
    CASE_CREATED = "case_created"
    CASE_UPDATED = "case_updated"
    CASE_VERIFIED = "case_verified"
    CASE_REJECTED = "case_rejected"
    
    # Error events
    ERROR_OCCURRED = "error_occurred"
    ERROR_RECOVERED = "error_recovered"
    
    # Performance events
    METRIC_RECORDED = "metric_recorded"
    THRESHOLD_EXCEEDED = "threshold_exceeded"
    
    # System events
    SYSTEM_STARTED = "system_started"
    SYSTEM_SHUTDOWN = "system_shutdown"
    SERVICE_REGISTERED = "service_registered"
    SERVICE_UNREGISTERED = "service_unregistered"


@dataclass
class Event:
    """Base event class."""
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: EventType = EventType.PROCESSING_STARTED
    source: str = ""
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary."""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'source': self.source,
            'data': self.data,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata
        }


class Observer(ABC):
    """Base observer interface."""
    
    @abstractmethod
    def handle(self, event: Event) -> None:
        """
        Handle an event.
        
        Args:
            event: The event to handle
        """
        pass
    
    def can_handle(self, event: Event) -> bool:
        """
        Check if this observer can handle the event.
        
        Args:
            event: The event to check
            
        Returns:
            True if the observer can handle the event
        """
        return True


class AsyncObserver(ABC):
    """Async observer interface."""
    
    @abstractmethod
    async def handle(self, event: Event) -> None:
        """
        Handle an event asynchronously.
        
        Args:
            event: The event to handle
        """
        pass
    
    async def can_handle(self, event: Event) -> bool:
        """
        Check if this observer can handle the event.
        
        Args:
            event: The event to check
            
        Returns:
            True if the observer can handle the event
        """
        return True


class EventBus:
    """
    Central event dispatcher for PACER services.
    
    Supports both sync and async observers with weak references
    to prevent memory leaks.
    """
    
    def __init__(self, logger: Optional[LoggerProtocol] = None):
        self.logger = logger
        self._observers: Dict[EventType, Set[weakref.ref]] = defaultdict(set)
        self._async_observers: Dict[EventType, Set[weakref.ref]] = defaultdict(set)
        self._wildcard_observers: Set[weakref.ref] = set()
        self._async_wildcard_observers: Set[weakref.ref] = set()
        self._event_history: List[Event] = []
        self._max_history = 1000
        self._lock = asyncio.Lock()
    
    def subscribe(self, event_type: EventType, observer: Observer) -> None:
        """
        Subscribe an observer to an event type.
        
        Args:
            event_type: Type of event to subscribe to
            observer: Observer to subscribe
        """
        observer_ref = weakref.ref(observer, self._cleanup_observer)
        self._observers[event_type].add(observer_ref)
        
        if self.logger:
            self.logger.debug(f"Observer {observer.__class__.__name__} subscribed to {event_type.value}")
    
    def subscribe_async(self, event_type: EventType, observer: AsyncObserver) -> None:
        """
        Subscribe an async observer to an event type.
        
        Args:
            event_type: Type of event to subscribe to
            observer: Async observer to subscribe
        """
        observer_ref = weakref.ref(observer, self._cleanup_async_observer)
        self._async_observers[event_type].add(observer_ref)
        
        if self.logger:
            self.logger.debug(f"Async observer {observer.__class__.__name__} subscribed to {event_type.value}")
    
    def subscribe_all(self, observer: Observer) -> None:
        """
        Subscribe an observer to all events.
        
        Args:
            observer: Observer to subscribe
        """
        observer_ref = weakref.ref(observer, self._cleanup_observer)
        self._wildcard_observers.add(observer_ref)
        
        if self.logger:
            self.logger.debug(f"Observer {observer.__class__.__name__} subscribed to all events")
    
    def subscribe_all_async(self, observer: AsyncObserver) -> None:
        """
        Subscribe an async observer to all events.
        
        Args:
            observer: Async observer to subscribe
        """
        observer_ref = weakref.ref(observer, self._cleanup_async_observer)
        self._async_wildcard_observers.add(observer_ref)
        
        if self.logger:
            self.logger.debug(f"Async observer {observer.__class__.__name__} subscribed to all events")
    
    def unsubscribe(self, event_type: EventType, observer: Observer) -> None:
        """
        Unsubscribe an observer from an event type.
        
        Args:
            event_type: Type of event to unsubscribe from
            observer: Observer to unsubscribe
        """
        self._observers[event_type] = {
            ref for ref in self._observers[event_type]
            if ref() is not observer
        }
    
    def unsubscribe_async(self, event_type: EventType, observer: AsyncObserver) -> None:
        """
        Unsubscribe an async observer from an event type.
        
        Args:
            event_type: Type of event to unsubscribe from
            observer: Async observer to unsubscribe
        """
        self._async_observers[event_type] = {
            ref for ref in self._async_observers[event_type]
            if ref() is not observer
        }
    
    def publish(self, event: Event) -> None:
        """
        Publish an event synchronously.
        
        Args:
            event: Event to publish
        """
        self._add_to_history(event)
        
        if self.logger:
            self.logger.debug(f"Publishing event: {event.event_type.value} from {event.source}")
        
        # Notify specific observers
        observers = self._get_observers(event.event_type)
        
        # Notify wildcard observers
        for observer_ref in self._wildcard_observers:
            observer = observer_ref()
            if observer and observer.can_handle(event):
                observers.append(observer)
        
        # Handle event
        for observer in observers:
            try:
                observer.handle(event)
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Observer {observer.__class__.__name__} failed: {str(e)}")
    
    async def publish_async(self, event: Event) -> None:
        """
        Publish an event asynchronously.
        
        Args:
            event: Event to publish
        """
        async with self._lock:
            self._add_to_history(event)
        
        if self.logger:
            self.logger.debug(f"Publishing async event: {event.event_type.value} from {event.source}")
        
        # Gather all observers
        tasks = []
        
        # Specific async observers
        for observer_ref in self._async_observers[event.event_type]:
            observer = observer_ref()
            if observer and await observer.can_handle(event):
                tasks.append(observer.handle(event))
        
        # Wildcard async observers
        for observer_ref in self._async_wildcard_observers:
            observer = observer_ref()
            if observer and await observer.can_handle(event):
                tasks.append(observer.handle(event))
        
        # Execute all handlers
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Log any exceptions
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    if self.logger:
                        self.logger.error(f"Async observer failed: {str(result)}")
    
    def _get_observers(self, event_type: EventType) -> List[Observer]:
        """Get all valid observers for an event type."""
        observers = []
        
        # Clean up dead references and get valid observers
        valid_refs = []
        for observer_ref in self._observers[event_type]:
            observer = observer_ref()
            if observer:
                valid_refs.append(observer_ref)
                observers.append(observer)
        
        # Update with only valid references
        self._observers[event_type] = set(valid_refs)
        
        return observers
    
    def _cleanup_observer(self, ref: weakref.ref) -> None:
        """Clean up dead observer reference."""
        for event_type in self._observers:
            self._observers[event_type].discard(ref)
        self._wildcard_observers.discard(ref)
    
    def _cleanup_async_observer(self, ref: weakref.ref) -> None:
        """Clean up dead async observer reference."""
        for event_type in self._async_observers:
            self._async_observers[event_type].discard(ref)
        self._async_wildcard_observers.discard(ref)
    
    def _add_to_history(self, event: Event) -> None:
        """Add event to history with size limit."""
        self._event_history.append(event)
        
        # Trim history if needed
        if len(self._event_history) > self._max_history:
            self._event_history = self._event_history[-self._max_history:]
    
    def get_history(self, event_type: Optional[EventType] = None, 
                    limit: int = 100) -> List[Event]:
        """
        Get event history.
        
        Args:
            event_type: Optional filter by event type
            limit: Maximum number of events to return
            
        Returns:
            List of historical events
        """
        history = self._event_history[-limit:]
        
        if event_type:
            history = [e for e in history if e.event_type == event_type]
        
        return history
    
    def clear_history(self) -> None:
        """Clear event history."""
        self._event_history.clear()
    
    def get_observer_count(self, event_type: Optional[EventType] = None) -> int:
        """
        Get count of observers.
        
        Args:
            event_type: Optional filter by event type
            
        Returns:
            Number of observers
        """
        if event_type:
            sync_count = len([ref for ref in self._observers[event_type] if ref()])
            async_count = len([ref for ref in self._async_observers[event_type] if ref()])
            return sync_count + async_count
        else:
            total = len([ref for ref in self._wildcard_observers if ref()])
            total += len([ref for ref in self._async_wildcard_observers if ref()])
            
            for event_type in EventType:
                total += self.get_observer_count(event_type)
            
            return total


# Global event bus instance
_global_event_bus: Optional[EventBus] = None


def get_event_bus(logger: Optional[LoggerProtocol] = None) -> EventBus:
    """Get the global event bus instance."""
    global _global_event_bus
    
    if _global_event_bus is None:
        _global_event_bus = EventBus(logger)
    
    return _global_event_bus


def reset_event_bus() -> None:
    """Reset the global event bus (mainly for testing)."""
    global _global_event_bus
    _global_event_bus = None