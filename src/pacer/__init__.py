"""
PACER Domain Module.

This module provides the consolidated PACER domain with 9 core services,
design pattern implementations, and component-based architecture.

Architecture:
- Core Services (9): Configuration, Browser, Case Processing, Relevance, 
  Classification, Verification, Download Orchestration, File Operations, 
  Metrics Reporting, S3 Management
- Design Patterns: Strategy, Observer, Builder, Factory, Registry
- Components: Authentication, Navigation, File Management, etc.
- Facades: Simplified interfaces for complex subsystems
"""

# Import base class from infrastructure
from src.infrastructure.patterns.component_base import AsyncServiceBase

# Import core services from services layer
from .services.browser_service import BrowserService
from .services.classification_service import ClassificationService
from .services.configuration_service import ConfigurationService
from .services.case_processing_service import CaseProcessingService
from .services.relevance_service import RelevanceService
from .services.file_operations_service import FileOperationsService
from .services.verification_service import VerificationService
from .facades.docket_orchestrator import DocketOrchestrator

# Import design patterns
from .strategies import *
from .observers import *
from .builders import *
from .factories import *
from .registries import *

# Import interfaces and exceptions
from .interfaces import *
from .exceptions import *

__all__ = [
    # Base classes
    "AsyncServiceBase",
    
    # Core services
    "BrowserService",
    "CaseProcessingService",
    "ClassificationService", 
    "ConfigurationService",
    "DocketOrchestrator",
    "FileOperationsService",
    "RelevanceService",
    "VerificationService",
    
    # Interfaces
    "PacerCoreServiceProtocol",
    "ConfigurationServiceInterface",
    "CaseProcessingServiceInterface", 
    "RelevanceServiceInterface",
    "ClassificationServiceInterface",
    "VerificationServiceInterface",
    "FileOperationsServiceInterface",
    "DocketProcessorInterface",
    
    # Exceptions
    "PacerException",
    "ValidationError",
    "ProcessingError",
    "AuthenticationError",
    "NavigationError",
    "DownloadError",
    "ConfigurationError",
    "ServiceError",
    "PatternError",
]