"""
Case domain model for PACER.

This module defines the Case entity which represents a legal case
with all its attributes and business rules.
"""

from dataclasses import dataclass, field
from datetime import datetime, date
from enum import Enum
from typing import Optional, List, Dict, Any


class CaseStatus(Enum):
    """Enumeration of case statuses."""
    OPEN = "open"
    CLOSED = "closed"
    PENDING = "pending"
    DISMISSED = "dismissed"
    SETTLED = "settled"
    TRANSFERRED = "transferred"
    REMANDED = "remanded"


class CaseType(Enum):
    """Enumeration of case types."""
    CIVIL = "civil"
    CRIMINAL = "criminal"
    BANKRUPTCY = "bankruptcy"
    APPELLATE = "appellate"
    MDL = "mdl"
    REMOVAL = "removal"
    TRANSFER = "transfer"


@dataclass
class Case:
    """
    Represents a legal case in the PACER system.
    
    This is the core domain entity containing all case-related information
    and business logic.
    """
    # Core identifiers
    case_id: str
    docket_number: str
    court_id: str
    
    # Case metadata
    case_title: str
    case_type: CaseType
    status: CaseStatus
    
    # Dates
    filed_date: date
    closed_date: Optional[date] = None
    last_updated: datetime = field(default_factory=datetime.utcnow)
    
    # Classification flags
    is_removal: bool = False
    is_transfer: bool = False
    is_mdl_related: bool = False
    transferred_from: Optional[str] = None
    transferred_to: Optional[str] = None
    removal_date: Optional[date] = None
    
    # Case details
    cause_of_action: Optional[str] = None
    nature_of_suit: Optional[str] = None
    jurisdiction: Optional[str] = None
    judge_name: Optional[str] = None
    magistrate_name: Optional[str] = None
    
    # Party information (IDs reference Party model)
    plaintiff_ids: List[str] = field(default_factory=list)
    defendant_ids: List[str] = field(default_factory=list)
    attorney_ids: List[str] = field(default_factory=list)
    
    # Processing metadata
    base_filename: Optional[str] = None
    html_content_hash: Optional[str] = None
    relevance_score: float = 0.0
    relevance_reasons: List[str] = field(default_factory=list)
    
    # Verification flags
    is_verified: bool = False
    verification_timestamp: Optional[datetime] = None
    verification_errors: List[str] = field(default_factory=list)
    
    # Additional metadata
    flags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate and normalize case data after initialization."""
        if not self.case_id:
            raise ValueError("Case ID is required")
        if not self.docket_number:
            raise ValueError("Docket number is required")
        if not self.court_id:
            raise ValueError("Court ID is required")
    
    def is_eligible_for_processing(self) -> bool:
        """Determine if the case is eligible for processing."""
        return (
            self.status not in [CaseStatus.CLOSED, CaseStatus.DISMISSED] and
            self.is_verified and
            len(self.verification_errors) == 0
        )
    
    def requires_transfer_processing(self) -> bool:
        """Check if the case requires transfer processing."""
        return self.is_transfer or self.transferred_from is not None
    
    def calculate_complexity_score(self) -> float:
        """Calculate a complexity score for the case."""
        score = 0.0
        
        # Increase complexity for special case types
        if self.is_mdl_related:
            score += 2.0
        if self.is_transfer:
            score += 1.5
        if self.is_removal:
            score += 1.5
        
        # Consider party count
        score += len(self.plaintiff_ids) * 0.2
        score += len(self.defendant_ids) * 0.2
        
        return min(score, 10.0)  # Cap at 10
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert case to dictionary representation."""
        return {
            "case_id": self.case_id,
            "docket_number": self.docket_number,
            "court_id": self.court_id,
            "case_title": self.case_title,
            "case_type": self.case_type.value,
            "status": self.status.value,
            "filed_date": self.filed_date.isoformat() if self.filed_date else None,
            "closed_date": self.closed_date.isoformat() if self.closed_date else None,
            "last_updated": self.last_updated.isoformat() if self.last_updated else None,
            "is_removal": self.is_removal,
            "is_transfer": self.is_transfer,
            "is_mdl_related": self.is_mdl_related,
            "transferred_from": self.transferred_from,
            "transferred_to": self.transferred_to,
            "removal_date": self.removal_date.isoformat() if self.removal_date else None,
            "cause_of_action": self.cause_of_action,
            "nature_of_suit": self.nature_of_suit,
            "jurisdiction": self.jurisdiction,
            "judge_name": self.judge_name,
            "magistrate_name": self.magistrate_name,
            "plaintiff_ids": self.plaintiff_ids,
            "defendant_ids": self.defendant_ids,
            "attorney_ids": self.attorney_ids,
            "base_filename": self.base_filename,
            "relevance_score": self.relevance_score,
            "relevance_reasons": self.relevance_reasons,
            "is_verified": self.is_verified,
            "verification_timestamp": self.verification_timestamp.isoformat() if self.verification_timestamp else None,
            "verification_errors": self.verification_errors,
            "flags": self.flags,
            "metadata": self.metadata,
        }