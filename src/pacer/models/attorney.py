"""
Attorney domain model for PACER.

This module defines the Attorney entity which represents
legal counsel in PACER cases.
"""

from dataclasses import dataclass, field
from datetime import date
from enum import Enum
from typing import Optional, List, Dict, Any


class AttorneyStatus(Enum):
    """Enumeration of attorney statuses in a case."""
    ACTIVE = "active"
    WITHDRAWN = "withdrawn"
    SUBSTITUTED = "substituted"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"
    PRO_HAC_VICE = "pro_hac_vice"  # Admitted for this case only


class AttorneyRole(Enum):
    """Enumeration of attorney roles."""
    LEAD_ATTORNEY = "lead_attorney"
    ATTORNEY = "attorney"
    CO_COUNSEL = "co_counsel"
    LOCAL_COUNSEL = "local_counsel"
    GOVERNMENT_ATTORNEY = "government_attorney"
    PUBLIC_DEFENDER = "public_defender"
    COURT_APPOINTED = "court_appointed"


@dataclass
class Attorney:
    """
    Represents an attorney in the PACER system.
    
    Attorneys represent parties in legal cases and file documents.
    """
    # Core identifiers
    attorney_id: str
    bar_number: Optional[str] = None
    
    # Personal information
    first_name: str
    last_name: str
    middle_name: Optional[str] = None
    suffix: Optional[str] = None  # Jr., III, Esq., etc.
    
    # Professional information
    firm_name: Optional[str] = None
    attorney_role: AttorneyRole = AttorneyRole.ATTORNEY
    status: AttorneyStatus = AttorneyStatus.ACTIVE
    
    # Bar admission information
    bar_state: Optional[str] = None
    admission_date: Optional[date] = None
    pro_hac_vice: bool = False
    pro_hac_vice_date: Optional[date] = None
    
    # Contact information
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    phone: Optional[str] = None
    fax: Optional[str] = None
    email: Optional[str] = None
    
    # Case associations
    case_ids: List[str] = field(default_factory=list)
    party_ids: List[str] = field(default_factory=list)  # Parties represented
    
    # Filing statistics
    total_filings: int = 0
    recent_filings: int = 0  # Last 30 days
    last_filing_date: Optional[date] = None
    
    # Appearance information
    appearance_date: Optional[date] = None
    withdrawal_date: Optional[date] = None
    withdrawal_reason: Optional[str] = None
    
    # Specializations and certifications
    practice_areas: List[str] = field(default_factory=list)
    certifications: List[str] = field(default_factory=list)
    
    # Performance metrics
    cases_won: Optional[int] = None
    cases_lost: Optional[int] = None
    cases_settled: Optional[int] = None
    
    # Additional metadata
    notes: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate attorney data after initialization."""
        if not self.attorney_id:
            raise ValueError("Attorney ID is required")
        if not self.first_name or not self.last_name:
            raise ValueError("Attorney first and last names are required")
    
    def get_full_name(self) -> str:
        """Get the attorney's full name."""
        parts = []
        if self.first_name:
            parts.append(self.first_name)
        if self.middle_name:
            parts.append(self.middle_name)
        if self.last_name:
            parts.append(self.last_name)
        if self.suffix:
            parts.append(self.suffix)
        return " ".join(parts)
    
    def get_professional_name(self) -> str:
        """Get the attorney's professional display name."""
        name = self.get_full_name()
        if self.suffix and self.suffix.lower() != "esq.":
            name += ", Esq."
        elif not self.suffix:
            name += ", Esq."
        return name
    
    def get_firm_affiliation(self) -> str:
        """Get the attorney's firm affiliation string."""
        if self.firm_name:
            return f"{self.get_professional_name()} of {self.firm_name}"
        return self.get_professional_name()
    
    def get_contact_info(self) -> Dict[str, Any]:
        """Get formatted contact information."""
        return {
            "name": self.get_full_name(),
            "firm": self.firm_name,
            "address": self.get_full_address(),
            "phone": self.phone,
            "fax": self.fax,
            "email": self.email,
        }
    
    def get_full_address(self) -> Optional[str]:
        """Get the complete formatted address."""
        if not self.address:
            return None
        
        parts = []
        if self.firm_name:
            parts.append(self.firm_name)
        parts.append(self.address)
        if self.city:
            parts.append(self.city)
        if self.state:
            parts.append(self.state)
        if self.zip_code:
            parts.append(self.zip_code)
        
        return ", ".join(parts)
    
    def is_lead_counsel(self) -> bool:
        """Check if the attorney is lead counsel."""
        return self.attorney_role == AttorneyRole.LEAD_ATTORNEY
    
    def is_government_attorney(self) -> bool:
        """Check if the attorney is a government attorney."""
        return self.attorney_role in [
            AttorneyRole.GOVERNMENT_ATTORNEY,
            AttorneyRole.PUBLIC_DEFENDER
        ]
    
    def is_active_on_case(self, case_id: str) -> bool:
        """Check if the attorney is active on a specific case."""
        return (
            case_id in self.case_ids and 
            self.status == AttorneyStatus.ACTIVE
        )
    
    def calculate_experience_score(self) -> float:
        """Calculate an experience score based on case history."""
        score = 0.0
        
        # Base score from total cases
        score += min(len(self.case_ids) * 0.1, 5.0)
        
        # Add points for being lead counsel
        if self.is_lead_counsel():
            score += 2.0
        
        # Add points for specializations
        score += len(self.practice_areas) * 0.5
        
        # Add points for certifications
        score += len(self.certifications) * 1.0
        
        return min(score, 10.0)  # Cap at 10
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert attorney to dictionary representation."""
        return {
            "attorney_id": self.attorney_id,
            "bar_number": self.bar_number,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "middle_name": self.middle_name,
            "suffix": self.suffix,
            "firm_name": self.firm_name,
            "attorney_role": self.attorney_role.value,
            "status": self.status.value,
            "bar_state": self.bar_state,
            "admission_date": self.admission_date.isoformat() if self.admission_date else None,
            "pro_hac_vice": self.pro_hac_vice,
            "pro_hac_vice_date": self.pro_hac_vice_date.isoformat() if self.pro_hac_vice_date else None,
            "address": self.address,
            "city": self.city,
            "state": self.state,
            "zip_code": self.zip_code,
            "phone": self.phone,
            "fax": self.fax,
            "email": self.email,
            "case_ids": self.case_ids,
            "party_ids": self.party_ids,
            "total_filings": self.total_filings,
            "recent_filings": self.recent_filings,
            "last_filing_date": self.last_filing_date.isoformat() if self.last_filing_date else None,
            "appearance_date": self.appearance_date.isoformat() if self.appearance_date else None,
            "withdrawal_date": self.withdrawal_date.isoformat() if self.withdrawal_date else None,
            "withdrawal_reason": self.withdrawal_reason,
            "practice_areas": self.practice_areas,
            "certifications": self.certifications,
            "cases_won": self.cases_won,
            "cases_lost": self.cases_lost,
            "cases_settled": self.cases_settled,
            "notes": self.notes,
            "tags": self.tags,
            "metadata": self.metadata,
        }