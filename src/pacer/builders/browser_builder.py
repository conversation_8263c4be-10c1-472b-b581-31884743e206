"""
Browser Builder pattern implementation for PACER browser configurations.

Provides a fluent interface for building complex browser configurations
with authentication, navigation, and session management.
"""

from typing import Any, Dict, Optional, List, Union
from dataclasses import dataclass, field

from src.infrastructure.protocols.logger import LoggerProtocol
from src.pacer.builders.service_builder import ServiceBuilder


@dataclass
class BrowserConfiguration:
    """Configuration for browser setup."""
    headless: bool = True
    timeout: int = 30000
    viewport_width: int = 1280
    viewport_height: int = 720
    user_agent: Optional[str] = None
    enable_javascript: bool = True
    enable_images: bool = False
    enable_plugins: bool = False
    proxy_settings: Optional[Dict[str, str]] = None
    extra_headers: Dict[str, str] = field(default_factory=dict)
    download_path: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'headless': self.headless,
            'timeout': self.timeout,
            'viewport': {
                'width': self.viewport_width,
                'height': self.viewport_height
            },
            'user_agent': self.user_agent,
            'enable_javascript': self.enable_javascript,
            'enable_images': self.enable_images,
            'enable_plugins': self.enable_plugins,
            'proxy_settings': self.proxy_settings,
            'extra_headers': self.extra_headers,
            'download_path': self.download_path
        }


class BrowserBuilder(ServiceBuilder):
    """
    Builder for browser service configurations.
    
    Provides a fluent interface for building complex browser setups
    with authentication, navigation, and performance optimizations.
    
    Example:
        browser = (BrowserBuilder()
            .enable_headless_mode()
            .with_timeout(60000)
            .with_viewport(1920, 1080)
            .with_download_path('/tmp/pacer')
            .disable_images()
            .with_proxy('http://proxy:8080')
            .with_user_agent('PacerBot/1.0')
            .build())
    """
    
    def __init__(self):
        # Import here to avoid circular dependencies  
        from src.pacer.services.browser_service import BrowserService
        super().__init__(BrowserService)
        self._browser_config = BrowserConfiguration()
    
    def enable_headless_mode(self, headless: bool = True) -> 'BrowserBuilder':
        """Enable or disable headless mode."""
        self._browser_config.headless = headless
        return self
    
    def disable_headless_mode(self) -> 'BrowserBuilder':
        """Disable headless mode (show browser UI)."""
        self._browser_config.headless = False
        return self
    
    def with_timeout(self, timeout_ms: int) -> 'BrowserBuilder':
        """Set the default timeout in milliseconds."""
        self._browser_config.timeout = timeout_ms
        return self
    
    def with_viewport(self, width: int, height: int) -> 'BrowserBuilder':
        """Set the viewport dimensions."""
        self._browser_config.viewport_width = width
        self._browser_config.viewport_height = height
        return self
    
    def with_user_agent(self, user_agent: str) -> 'BrowserBuilder':
        """Set custom user agent string."""
        self._browser_config.user_agent = user_agent
        return self
    
    def enable_javascript(self, enabled: bool = True) -> 'BrowserBuilder':
        """Enable or disable JavaScript."""
        self._browser_config.enable_javascript = enabled
        return self
    
    def enable_images(self, enabled: bool = True) -> 'BrowserBuilder':
        """Enable or disable image loading."""
        self._browser_config.enable_images = enabled
        return self
    
    def disable_images(self) -> 'BrowserBuilder':
        """Disable image loading for better performance."""
        self._browser_config.enable_images = False
        return self
    
    def enable_plugins(self, enabled: bool = True) -> 'BrowserBuilder':
        """Enable or disable browser plugins."""
        self._browser_config.enable_plugins = enabled
        return self
    
    def with_proxy(self, proxy_url: str, username: Optional[str] = None, password: Optional[str] = None) -> 'BrowserBuilder':
        """Set proxy configuration."""
        proxy_settings = {'server': proxy_url}
        if username:
            proxy_settings['username'] = username
        if password:
            proxy_settings['password'] = password
        
        self._browser_config.proxy_settings = proxy_settings
        return self
    
    def with_header(self, name: str, value: str) -> 'BrowserBuilder':
        """Add custom HTTP header."""
        self._browser_config.extra_headers[name] = value
        return self
    
    def with_headers(self, headers: Dict[str, str]) -> 'BrowserBuilder':
        """Add multiple custom HTTP headers."""
        self._browser_config.extra_headers.update(headers)
        return self
    
    def with_download_path(self, path: str) -> 'BrowserBuilder':
        """Set the download directory path."""
        self._browser_config.download_path = path
        return self
    
    def with_playwright_manager(self, manager: Any) -> 'BrowserBuilder':
        """Set the playwright manager."""
        return self.with_param('playwright_manager', manager)
    
    def with_context_factory(self, factory: Any) -> 'BrowserBuilder':
        """Set the context factory."""
        return self.with_param('context_factory', factory)
    
    def with_auth_manager(self, manager: Any) -> 'BrowserBuilder':
        """Set the authentication manager."""
        return self.with_param('auth_manager', manager)
    
    def with_navigation_manager(self, manager: Any) -> 'BrowserBuilder':
        """Set the navigation manager."""
        return self.with_param('navigation_manager', manager)
    
    def with_session_timeout(self, timeout_seconds: int) -> 'BrowserBuilder':
        """Set session timeout in seconds."""
        return self.with_param('session_timeout', timeout_seconds)
    
    def with_retry_policy(self, max_retries: int = 3, delay: float = 1.0) -> 'BrowserBuilder':
        """Set retry policy for failed operations."""
        return self.with_param('retry_policy', {
            'max_retries': max_retries,
            'delay': delay
        })
    
    def enable_debug_mode(self, enabled: bool = True) -> 'BrowserBuilder':
        """Enable debug mode with detailed logging."""
        return self.with_param('debug_mode', enabled)
    
    def with_page_load_strategy(self, strategy: str = 'normal') -> 'BrowserBuilder':
        """Set page load strategy: 'normal', 'eager', or 'none'."""
        return self.with_param('page_load_strategy', strategy)
    
    def with_screenshot_on_failure(self, enabled: bool = True, path: Optional[str] = None) -> 'BrowserBuilder':
        """Enable automatic screenshots on failures."""
        return self.with_param('screenshot_on_failure', {
            'enabled': enabled,
            'path': path
        })
    
    def build(self) -> Any:
        """Build the browser service with all configurations."""
        # Add browser configuration to parameters
        self.with_param('browser_config', self._browser_config.to_dict())
        
        return super().build()


# Convenience functions for common browser configurations
def build_headless_browser(**kwargs) -> Any:
    """Quick function to build a headless browser for automated processing."""
    builder = (BrowserBuilder()
               .enable_headless_mode()
               .disable_images()
               .with_timeout(30000))
    
    for key, value in kwargs.items():
        if hasattr(builder, f'with_{key}'):
            getattr(builder, f'with_{key}')(value)
        else:
            builder.with_param(key, value)
    
    return builder.build()


def build_debug_browser(**kwargs) -> Any:
    """Quick function to build a browser with debug features enabled."""
    builder = (BrowserBuilder()
               .disable_headless_mode()
               .enable_debug_mode()
               .with_screenshot_on_failure(True)
               .with_timeout(60000))
    
    for key, value in kwargs.items():
        if hasattr(builder, f'with_{key}'):
            getattr(builder, f'with_{key}')(value)
        else:
            builder.with_param(key, value)
    
    return builder.build()


def build_performance_browser(**kwargs) -> Any:
    """Quick function to build a browser optimized for performance."""
    builder = (BrowserBuilder()
               .enable_headless_mode()
               .disable_images()
               .enable_plugins(False)
               .with_timeout(15000)
               .with_page_load_strategy('eager'))
    
    for key, value in kwargs.items():
        if hasattr(builder, f'with_{key}'):
            getattr(builder, f'with_{key}')(value)
        else:
            builder.with_param(key, value)
    
    return builder.build()