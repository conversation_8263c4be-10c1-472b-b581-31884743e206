"""
Docket Builder pattern implementation for PACER docket processing.

Provides a fluent interface for building complex docket processing workflows
with many parameters and dependencies.
"""

from typing import Any, Dict, Optional, List, Union
from dataclasses import dataclass

from src.infrastructure.protocols.logger import LoggerProtocol
from src.pacer.builders.service_builder import ServiceBuilder


@dataclass 
class DocketConfiguration:
    """Configuration for docket processing."""
    court_id: str
    case_number: Optional[str] = None
    date_range: Optional[tuple] = None
    document_types: List[str] = None
    max_documents: int = 100
    parallel_processing: bool = False
    max_workers: int = 4
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'court_id': self.court_id,
            'case_number': self.case_number,
            'date_range': self.date_range,
            'document_types': self.document_types or [],
            'max_documents': self.max_documents,
            'parallel_processing': self.parallel_processing,
            'max_workers': self.max_workers
        }


class DocketBuilder(ServiceBuilder):
    """
    Builder for docket processing workflows.
    
    Provides a fluent interface for building complex docket processing
    operations with multiple parameters and configurations.
    
    Example:
        docket = (DocketBuilder()
            .with_court('nysd')
            .with_case_number('1:23-cv-12345')
            .with_document_types(['complaint', 'motion'])
            .with_date_range('2023-01-01', '2023-12-31')
            .enable_parallel_processing(max_workers=4)
            .with_max_documents(500)
            .build())
    """
    
    def __init__(self):
        # Import here to avoid circular dependencies
        from src.pacer.facades.docket_orchestrator import DocketOrchestrator as DocketProcessingOrchestrator
        super().__init__(DocketProcessingOrchestrator)
        self._docket_config = DocketConfiguration(court_id="")
    
    def with_court(self, court_id: str) -> 'DocketBuilder':
        """Set the court identifier."""
        self._docket_config.court_id = court_id
        return self
    
    def with_case_number(self, case_number: str) -> 'DocketBuilder':
        """Set the case number."""
        self._docket_config.case_number = case_number
        return self
    
    def with_date_range(self, start_date: str, end_date: str) -> 'DocketBuilder':
        """Set the date range for document retrieval."""
        self._docket_config.date_range = (start_date, end_date)
        return self
    
    def with_document_types(self, document_types: List[str]) -> 'DocketBuilder':
        """Set the document types to retrieve."""
        self._docket_config.document_types = document_types
        return self
    
    def with_max_documents(self, max_documents: int) -> 'DocketBuilder':
        """Set maximum number of documents to process."""
        self._docket_config.max_documents = max_documents
        return self
    
    def enable_parallel_processing(self, max_workers: int = 4) -> 'DocketBuilder':
        """Enable parallel processing with specified workers."""
        self._docket_config.parallel_processing = True
        self._docket_config.max_workers = max_workers
        return self
    
    def disable_parallel_processing(self) -> 'DocketBuilder':
        """Disable parallel processing."""
        self._docket_config.parallel_processing = False
        return self
    
    def with_configuration_service(self, service: Any) -> 'DocketBuilder':
        """Set the configuration service."""
        return self.with_param('configuration_service', service)
    
    def with_browser_service(self, service: Any) -> 'DocketBuilder':
        """Set the browser service."""
        return self.with_param('browser_service', service)
    
    def with_case_processing_service(self, service: Any) -> 'DocketBuilder':
        """Set the case processing service."""
        return self.with_param('case_processing_service', service)
    
    def with_relevance_service(self, service: Any) -> 'DocketBuilder':
        """Set the relevance service."""
        return self.with_param('relevance_service', service)
    
    def with_verification_service(self, service: Any) -> 'DocketBuilder':
        """Set the verification service."""
        return self.with_param('verification_service', service)
    
    def with_download_orchestration_service(self, service: Any) -> 'DocketBuilder':
        """Set the download orchestration service."""
        return self.with_param('download_orchestration_service', service)
    
    def with_file_operations_service(self, service: Any) -> 'DocketBuilder':
        """Set the file operations service."""
        return self.with_param('file_operations_service', service)
    
    def with_metrics_reporting_service(self, service: Any) -> 'DocketBuilder':
        """Set the metrics reporting service."""
        return self.with_param('metrics_reporting_service', service)
    
    def with_s3_management_service(self, service: Any) -> 'DocketBuilder':
        """Set the S3 management service."""
        return self.with_param('s3_management_service', service)
    
    def with_authentication_credentials(self, username: str, password: str) -> 'DocketBuilder':
        """Set authentication credentials."""
        return self.with_param('credentials', {'username': username, 'password': password})
    
    def with_custom_filters(self, filters: Dict[str, Any]) -> 'DocketBuilder':
        """Set custom document filters."""
        return self.with_param('custom_filters', filters)
    
    def with_output_format(self, format_type: str) -> 'DocketBuilder':
        """Set output format (csv, json, xml)."""
        return self.with_param('output_format', format_type)
    
    def with_retry_policy(self, max_retries: int = 3, delay: float = 1.0) -> 'DocketBuilder':
        """Set retry policy for failed operations."""
        return self.with_param('retry_policy', {
            'max_retries': max_retries,
            'delay': delay
        })
    
    def build(self) -> Any:
        """Build the docket processing orchestrator with all configurations."""
        # Add docket configuration to parameters
        self.with_param('docket_config', self._docket_config.to_dict())
        
        return super().build()


# Convenience functions for common docket processing patterns
def build_court_docket(court_id: str, **kwargs) -> Any:
    """Quick function to build a court docket processor."""
    builder = DocketBuilder().with_court(court_id)
    
    for key, value in kwargs.items():
        if hasattr(builder, f'with_{key}'):
            getattr(builder, f'with_{key}')(value)
        else:
            builder.with_param(key, value)
    
    return builder.build()


def build_case_docket(court_id: str, case_number: str, **kwargs) -> Any:
    """Quick function to build a case-specific docket processor."""
    builder = (DocketBuilder()
               .with_court(court_id)
               .with_case_number(case_number))
    
    for key, value in kwargs.items():
        if hasattr(builder, f'with_{key}'):
            getattr(builder, f'with_{key}')(value)
        else:
            builder.with_param(key, value)
    
    return builder.build()


def build_bulk_docket(courts: List[str], **kwargs) -> List[Any]:
    """Quick function to build multiple court docket processors."""
    dockets = []
    
    for court_id in courts:
        builder = DocketBuilder().with_court(court_id)
        
        for key, value in kwargs.items():
            if hasattr(builder, f'with_{key}'):
                getattr(builder, f'with_{key}')(value)
            else:
                builder.with_param(key, value)
        
        dockets.append(builder.build())
    
    return dockets