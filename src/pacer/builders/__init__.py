"""
PACER Builder Pattern Implementations.

This module contains builder pattern implementations for constructing
complex PACER services and configurations with fluent interfaces.
"""

from .service_builder import (
    ServiceBuilder,
    DocketProcessingOrchestratorBuilder,
    BrowserServiceBuilder,
    CaseProcessingServiceBuilder,
    RelevanceServiceBuilder,
    VerificationServiceBuilder,
    build_orchestrator,
    build_browser_service,
    build_case_service
)
from .docket_builder import (
    DocketBuilder,
    DocketConfiguration,
    build_court_docket,
    build_case_docket,
    build_bulk_docket
)
from .browser_builder import (
    BrowserBuilder,
    BrowserConfiguration,
    build_headless_browser,
    build_debug_browser,
    build_performance_browser
)

__all__ = [
    # Service builders
    'ServiceBuilder',
    'DocketProcessingOrchestratorBuilder',
    'BrowserServiceBuilder', 
    'CaseProcessingServiceBuilder',
    'RelevanceServiceBuilder',
    'VerificationServiceBuilder',
    'build_orchestrator',
    'build_browser_service',
    'build_case_service',
    
    # Specialized builders
    'DocketBuilder',
    'DocketConfiguration',
    'build_court_docket',
    'build_case_docket', 
    'build_bulk_docket',
    
    # Browser builders
    'BrowserBuilder',
    'BrowserConfiguration',
    'build_headless_browser',
    'build_debug_browser',
    'build_performance_browser'
]