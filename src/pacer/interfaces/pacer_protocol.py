"""
PACER Protocol Definitions.

This module defines the common interfaces and protocols that all PACER core services
must implement, following the AsyncServiceBase pattern and Facade pattern principles.
Moved from src/services/pacer/_core_services/shared/base_interfaces.py
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Protocol, Union
from pathlib import Path

from src.infrastructure.patterns.component_base import AsyncServiceBase


class PacerCoreServiceProtocol(Protocol):
    """Protocol that all PACER core services must implement."""
    
    async def initialize(self) -> None:
        """Initialize the service with all dependencies."""
        ...
    
    async def cleanup(self) -> None:
        """Clean up service resources."""
        ...
    
    async def health_check(self) -> Dict[str, Any]:
        """Return service health status."""
        ...
    
    def is_initialized(self) -> bool:
        """Check if service is properly initialized."""
        ...


class ConfigurationServiceInterface(ABC):
    """Interface for configuration management service."""
    
    @abstractmethod
    async def get_all_configs(self) -> Dict[str, Any]:
        """Get all PACER configurations."""
        pass
    
    @abstractmethod
    async def get_config_value(self, config_name: str, key_path: str, default: Any = None) -> Any:
        """Get a specific configuration value using dot notation."""
        pass
    
    @abstractmethod
    async def update_config_value(self, config_name: str, key_path: str, value: Any) -> None:
        """Update a specific configuration value."""
        pass
    
    @abstractmethod
    async def reload_configs(self) -> Dict[str, Any]:
        """Force reload all configurations from disk."""
        pass


class CaseProcessingServiceInterface(ABC):
    """Interface for case processing service."""
    
    @abstractmethod
    async def process_case(self, page: Any, initial_details: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process a complete case workflow."""
        pass
    
    @abstractmethod
    async def classify_case(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Classify case type and attributes."""
        pass
    
    @abstractmethod
    async def parse_html_content(self, html_content: str) -> Dict[str, Any]:
        """Parse HTML content to extract case information."""
        pass
    
    @abstractmethod
    async def enrich_case_data(self, case_details: Dict[str, Any], html_content: str) -> Dict[str, Any]:
        """Enrich case data with additional information."""
        pass
    
    @abstractmethod
    async def create_base_filename(self, case_details: Dict[str, Any]) -> str:
        """Create standardized filename for case."""
        pass


class RelevanceServiceInterface(ABC):
    """Interface for case relevance determination."""
    
    @abstractmethod
    async def is_relevant(self, case_details: Dict[str, Any]) -> bool:
        """Determine if a case is relevant for processing."""
        pass
    
    @abstractmethod
    async def should_ignore_download(self, case_details: Dict[str, Any]) -> bool:
        """Check if case download should be ignored."""
        pass
    
    @abstractmethod
    async def calculate_relevance_score(self, case_details: Dict[str, Any]) -> float:
        """Calculate numerical relevance score."""
        pass
    
    @abstractmethod
    async def get_relevance_reasons(self, case_details: Dict[str, Any]) -> List[str]:
        """Get reasons for relevance determination."""
        pass


class ClassificationServiceInterface(ABC):
    """Interface for advanced case classification."""
    
    @abstractmethod
    async def classify_case_type(self, case_details: Dict[str, Any]) -> str:
        """Classify the type of legal case."""
        pass
    
    @abstractmethod
    async def classify_court_jurisdiction(self, case_details: Dict[str, Any]) -> str:
        """Classify court jurisdiction level."""
        pass
    
    @abstractmethod
    async def classify_case_complexity(self, case_details: Dict[str, Any]) -> str:
        """Classify case complexity level."""
        pass
    
    @abstractmethod
    async def get_classification_confidence(self, classification: str, case_details: Dict[str, Any]) -> float:
        """Get confidence score for classification."""
        pass


class VerificationServiceInterface(ABC):
    """Interface for case and data verification."""
    
    @abstractmethod
    async def verify_case_completeness(self, case_details: Dict[str, Any]) -> bool:
        """Verify case data completeness."""
        pass
    
    @abstractmethod
    async def verify_data_integrity(self, data: Dict[str, Any]) -> bool:
        """Verify data integrity and consistency."""
        pass
    
    @abstractmethod
    async def validate_case_format(self, case_details: Dict[str, Any]) -> List[str]:
        """Validate case data format and return any errors."""
        pass
    
    @abstractmethod
    async def verify_processing_eligibility(self, case_details: Dict[str, Any]) -> bool:
        """Verify if case is eligible for processing."""
        pass


class DownloadOrchestrationServiceInterface(ABC):
    """Interface for download workflow orchestration."""
    
    @abstractmethod
    async def execute_download_workflow(self, case_details: Dict[str, Any]) -> Dict[str, Any]:
        """Execute complete download workflow for a case."""
        pass
    
    @abstractmethod
    async def authenticate_session(self, credentials: Dict[str, Any]) -> bool:
        """Authenticate PACER session."""
        pass
    
    @abstractmethod
    async def navigate_to_case(self, case_details: Dict[str, Any]) -> bool:
        """Navigate browser to case page."""
        pass
    
    @abstractmethod
    async def execute_query(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute PACER query and return results."""
        pass


class FileOperationsServiceInterface(ABC):
    """Interface for file operations and data persistence."""
    
    @abstractmethod
    async def setup_directories(self, iso_date: str) -> None:
        """Setup required directory structure."""
        pass
    
    @abstractmethod
    async def save_case_data(self, case_data: Dict[str, Any], iso_date: str) -> str:
        """Save case data to appropriate location."""
        pass
    
    @abstractmethod
    async def upload_to_s3(self, file_path: str, s3_key: str) -> str:
        """Upload file to S3 and return URL."""
        pass
    
    @abstractmethod
    async def export_to_csv(self, data: List[Dict[str, Any]], output_path: str) -> str:
        """Export data to CSV format."""
        pass
    
    @abstractmethod
    async def generate_report(self, report_type: str, data: List[Dict[str, Any]]) -> str:
        """Generate formatted report."""
        pass


class DocketProcessorInterface(ABC):
    """Interface for legacy docket processing workflows."""
    
    @abstractmethod
    async def process_docket(self, docket_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process docket using legacy workflow."""
        pass
    
    @abstractmethod
    async def orchestrate_job(self, job_data: Dict[str, Any]) -> str:
        """Orchestrate a processing job."""
        pass
    
    @abstractmethod
    async def execute_bulk_processing(self, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute bulk processing of multiple jobs."""
        pass
    
    @abstractmethod
    async def handle_legacy_workflows(self, workflow_type: str, data: Dict[str, Any]) -> Any:
        """Handle legacy workflow patterns."""
        pass


# Common type definitions
CaseDetails = Dict[str, Any]
ConfigurationData = Dict[str, Any]
ProcessingResult = Dict[str, Any]
ServiceHealthStatus = Dict[str, Any]

# Service action types
ServiceAction = Union[str, Dict[str, Any]]
ActionResult = Union[Any, Dict[str, Any], List[Any]]