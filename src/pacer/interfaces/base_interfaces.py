"""
Base interfaces for PACER design patterns.

This module defines the core interfaces and protocols that PACER pattern 
implementations must follow, including Strategy, Observer, and Builder patterns.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Protocol, Union, TypeVar, Generic
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from src.infrastructure.protocols.logger import LoggerProtocol


# Generic types
T = TypeVar('T')
EventData = Dict[str, Any]
StrategyResult = Union[bool, Dict[str, Any], Any]


class PatternType(Enum):
    """Types of design patterns used in PACER."""
    STRATEGY = "strategy"
    OBSERVER = "observer"
    BUILDER = "builder"
    FACTORY = "factory"
    REGISTRY = "registry"
    FACADE = "facade"


# Strategy Pattern Interfaces
class BaseStrategy(ABC):
    """Base interface for Strategy pattern implementations."""
    
    @abstractmethod
    def execute(self, context: Dict[str, Any]) -> StrategyResult:
        """
        Execute the strategy with given context.
        
        Args:
            context: Context data for strategy execution
            
        Returns:
            Strategy execution result
        """
        pass
    
    def can_handle(self, context: Dict[str, Any]) -> bool:
        """
        Check if this strategy can handle the given context.
        
        Args:
            context: Context to check
            
        Returns:
            True if strategy can handle the context
        """
        return True
    
    def get_priority(self) -> int:
        """
        Get strategy priority (higher numbers = higher priority).
        
        Returns:
            Priority value
        """
        return 0


class AsyncStrategy(ABC):
    """Base interface for async Strategy pattern implementations."""
    
    @abstractmethod
    async def execute(self, context: Dict[str, Any]) -> StrategyResult:
        """
        Execute the strategy asynchronously with given context.
        
        Args:
            context: Context data for strategy execution
            
        Returns:
            Strategy execution result
        """
        pass
    
    async def can_handle(self, context: Dict[str, Any]) -> bool:
        """
        Check if this strategy can handle the given context.
        
        Args:
            context: Context to check
            
        Returns:
            True if strategy can handle the context
        """
        return True
    
    def get_priority(self) -> int:
        """
        Get strategy priority (higher numbers = higher priority).
        
        Returns:
            Priority value
        """
        return 0


# Observer Pattern Interfaces  
@dataclass
class EventContext:
    """Context information for events."""
    event_id: str
    event_type: str
    source: str
    timestamp: datetime
    data: EventData
    metadata: Dict[str, Any]


class Observer(ABC):
    """Base interface for Observer pattern implementations."""
    
    @abstractmethod
    def notify(self, event: EventContext) -> None:
        """
        Handle an event notification.
        
        Args:
            event: Event context with all relevant data
        """
        pass
    
    def can_handle_event(self, event_type: str) -> bool:
        """
        Check if this observer can handle the given event type.
        
        Args:
            event_type: Type of event
            
        Returns:
            True if observer can handle the event type
        """
        return True
    
    def get_event_types(self) -> List[str]:
        """
        Get list of event types this observer handles.
        
        Returns:
            List of event type names
        """
        return ["*"]  # Default to all events


class AsyncObserver(ABC):
    """Base interface for async Observer pattern implementations."""
    
    @abstractmethod
    async def notify(self, event: EventContext) -> None:
        """
        Handle an event notification asynchronously.
        
        Args:
            event: Event context with all relevant data
        """
        pass
    
    async def can_handle_event(self, event_type: str) -> bool:
        """
        Check if this observer can handle the given event type.
        
        Args:
            event_type: Type of event
            
        Returns:
            True if observer can handle the event type
        """
        return True
    
    def get_event_types(self) -> List[str]:
        """
        Get list of event types this observer handles.
        
        Returns:
            List of event type names
        """
        return ["*"]  # Default to all events


# Builder Pattern Interfaces
class BuilderProtocol(Protocol, Generic[T]):
    """Protocol for Builder pattern implementations."""
    
    def build(self) -> T:
        """Build and return the constructed object."""
        ...
    
    def reset(self) -> None:
        """Reset the builder to initial state."""
        ...


class ConfigurableBuilder(ABC, Generic[T]):
    """Base class for configurable builders."""
    
    def __init__(self):
        self._config: Dict[str, Any] = {}
        self._logger: Optional[LoggerProtocol] = None
    
    def with_config(self, config: Dict[str, Any]) -> 'ConfigurableBuilder[T]':
        """Set configuration parameters."""
        self._config.update(config)
        return self
    
    def with_logger(self, logger: LoggerProtocol) -> 'ConfigurableBuilder[T]':
        """Set logger instance."""
        self._logger = logger
        return self
    
    def with_parameter(self, key: str, value: Any) -> 'ConfigurableBuilder[T]':
        """Set a single parameter."""
        self._config[key] = value
        return self
    
    @abstractmethod
    def build(self) -> T:
        """Build and return the constructed object."""
        pass
    
    def reset(self) -> None:
        """Reset the builder to initial state."""
        self._config.clear()
        self._logger = None


# Factory Pattern Interfaces
class Factory(ABC, Generic[T]):
    """Base interface for Factory pattern implementations."""
    
    @abstractmethod
    def create(self, factory_type: str, **kwargs) -> T:
        """
        Create an instance of the specified type.
        
        Args:
            factory_type: Type of object to create
            **kwargs: Additional parameters for object creation
            
        Returns:
            Created object instance
        """
        pass
    
    @abstractmethod
    def get_supported_types(self) -> List[str]:
        """
        Get list of supported object types.
        
        Returns:
            List of type names this factory can create
        """
        pass
    
    def can_create(self, factory_type: str) -> bool:
        """
        Check if this factory can create the specified type.
        
        Args:
            factory_type: Type to check
            
        Returns:
            True if factory can create the type
        """
        return factory_type in self.get_supported_types()


class AsyncFactory(ABC, Generic[T]):
    """Base interface for async Factory pattern implementations."""
    
    @abstractmethod
    async def create(self, factory_type: str, **kwargs) -> T:
        """
        Create an instance of the specified type asynchronously.
        
        Args:
            factory_type: Type of object to create
            **kwargs: Additional parameters for object creation
            
        Returns:
            Created object instance
        """
        pass
    
    @abstractmethod
    def get_supported_types(self) -> List[str]:
        """
        Get list of supported object types.
        
        Returns:
            List of type names this factory can create
        """
        pass
    
    def can_create(self, factory_type: str) -> bool:
        """
        Check if this factory can create the specified type.
        
        Args:
            factory_type: Type to check
            
        Returns:
            True if factory can create the type
        """
        return factory_type in self.get_supported_types()


# Registry Pattern Interfaces
class RegistryProtocol(Protocol, Generic[T]):
    """Protocol for Registry pattern implementations."""
    
    def register(self, name: str, item: T) -> None:
        """Register an item with a name."""
        ...
    
    def unregister(self, name: str) -> None:
        """Unregister an item by name."""
        ...
    
    def get(self, name: str) -> T:
        """Get an item by name."""
        ...
    
    def list_items(self) -> List[str]:
        """List all registered item names."""
        ...


class BaseRegistry(ABC, Generic[T]):
    """Base implementation for Registry pattern."""
    
    def __init__(self):
        self._items: Dict[str, T] = {}
        self._metadata: Dict[str, Dict[str, Any]] = {}
    
    def register(self, name: str, item: T, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Register an item with optional metadata.
        
        Args:
            name: Unique name for the item
            item: Item to register
            metadata: Optional metadata about the item
        """
        self._items[name] = item
        if metadata:
            self._metadata[name] = metadata
    
    def unregister(self, name: str) -> None:
        """
        Unregister an item by name.
        
        Args:
            name: Name of item to remove
        """
        self._items.pop(name, None)
        self._metadata.pop(name, None)
    
    def get(self, name: str) -> Optional[T]:
        """
        Get an item by name.
        
        Args:
            name: Name of item to retrieve
            
        Returns:
            Item if found, None otherwise
        """
        return self._items.get(name)
    
    def list_items(self) -> List[str]:
        """
        List all registered item names.
        
        Returns:
            List of registered names
        """
        return list(self._items.keys())
    
    def get_metadata(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get metadata for an item.
        
        Args:
            name: Name of item
            
        Returns:
            Metadata if available
        """
        return self._metadata.get(name)
    
    def has_item(self, name: str) -> bool:
        """
        Check if an item is registered.
        
        Args:
            name: Name to check
            
        Returns:
            True if item exists
        """
        return name in self._items


# Facade Pattern Interfaces
class ServiceFacade(ABC):
    """Base interface for Facade pattern implementations."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the facade and its dependencies."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up facade resources."""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """Check facade health status."""
        pass
    
    def is_initialized(self) -> bool:
        """Check if facade is initialized."""
        return True


# Common result types
@dataclass
class PatternResult:
    """Common result structure for pattern operations."""
    success: bool
    data: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def is_success(self) -> bool:
        """Check if operation was successful."""
        return self.success
    
    def get_error(self) -> Optional[str]:
        """Get error message if any."""
        return self.error


# Validation interfaces
class Validator(ABC):
    """Base interface for validation patterns."""
    
    @abstractmethod
    def validate(self, data: Any) -> PatternResult:
        """
        Validate data and return result.
        
        Args:
            data: Data to validate
            
        Returns:
            Validation result
        """
        pass
    
    def get_validation_rules(self) -> List[str]:
        """
        Get list of validation rules this validator implements.
        
        Returns:
            List of rule names
        """
        return []


class AsyncValidator(ABC):
    """Base interface for async validation patterns."""
    
    @abstractmethod
    async def validate(self, data: Any) -> PatternResult:
        """
        Validate data asynchronously and return result.
        
        Args:
            data: Data to validate
            
        Returns:
            Validation result
        """
        pass
    
    def get_validation_rules(self) -> List[str]:
        """
        Get list of validation rules this validator implements.
        
        Returns:
            List of rule names
        """
        return []