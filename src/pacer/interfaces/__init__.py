"""
PACER Interface Definitions.

This module contains interface definitions and protocols for PACER
design patterns and service implementations.
"""

from .pacer_protocol import (
    PacerCoreServiceProtocol,
    ConfigurationServiceInterface,
    CaseProcessingServiceInterface,
    RelevanceServiceInterface,
    ClassificationServiceInterface,
    VerificationServiceInterface,
    DownloadOrchestrationServiceInterface,
    FileOperationsServiceInterface,
    DocketProcessorInterface,
    CaseDetails,
    ConfigurationData,
    ProcessingResult,
    ServiceHealthStatus,
    ServiceAction,
    ActionResult
)
from .base_interfaces import (
    PatternType,
    BaseStrategy,
    AsyncStrategy,
    EventContext,
    Observer,
    AsyncObserver,
    BuilderProtocol,
    ConfigurableBuilder,
    Factory,
    AsyncFactory,
    RegistryProtocol,
    BaseRegistry,
    ServiceFacade,
    PatternResult,
    Validator,
    AsyncValidator
)

__all__ = [
    # PACER service protocols
    'PacerCoreServiceProtocol',
    'ConfigurationServiceInterface',
    'CaseProcessingServiceInterface',
    'RelevanceServiceInterface',
    'ClassificationServiceInterface',
    'VerificationServiceInterface',
    'DownloadOrchestrationServiceInterface',
    'FileOperationsServiceInterface',
    'DocketProcessorInterface',
    'CaseDetails',
    'ConfigurationData',
    'ProcessingResult',
    'ServiceHealthStatus',
    'ServiceAction',
    'ActionResult',
    
    # Pattern interfaces
    'PatternType',
    'BaseStrategy',
    'AsyncStrategy',
    'EventContext',
    'Observer',
    'AsyncObserver', 
    'BuilderProtocol',
    'ConfigurableBuilder',
    'Factory',
    'AsyncFactory',
    'RegistryProtocol',
    'BaseRegistry',
    'ServiceFacade',
    'PatternResult',
    'Validator',
    'AsyncValidator'
]