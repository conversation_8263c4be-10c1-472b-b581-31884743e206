"""
PACER-specific exceptions for design patterns and services.

This module defines custom exceptions used throughout the PACER domain,
providing clear error handling and debugging information.
"""

from typing import Any, Dict, Optional, List
from dataclasses import dataclass
from datetime import datetime


class PacerException(Exception):
    """Base exception for all PACER-related errors."""
    
    def __init__(self, 
                 message: str,
                 error_code: Optional[str] = None,
                 details: Optional[Dict[str, Any]] = None,
                 cause: Optional[Exception] = None):
        """
        Initialize PACER exception.
        
        Args:
            message: Human-readable error message
            error_code: Optional error code for programmatic handling
            details: Optional additional error details
            cause: Optional underlying exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.cause = cause
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for serialization."""
        return {
            'exception_type': self.__class__.__name__,
            'message': self.message,
            'error_code': self.error_code,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
            'cause': str(self.cause) if self.cause else None
        }
    
    def __str__(self) -> str:
        """String representation of the exception."""
        parts = [self.message]
        if self.error_code:
            parts.append(f"[{self.error_code}]")
        if self.details:
            parts.append(f"Details: {self.details}")
        return " ".join(parts)


class ValidationError(PacerException):
    """Exception raised when data validation fails."""
    
    def __init__(self,
                 message: str,
                 field_errors: Optional[Dict[str, List[str]]] = None,
                 error_code: str = "VALIDATION_ERROR",
                 **kwargs):
        """
        Initialize validation error.
        
        Args:
            message: General validation error message
            field_errors: Dictionary mapping field names to error lists
            error_code: Specific validation error code
            **kwargs: Additional parameters for base exception
        """
        details = kwargs.get('details', {})
        if field_errors:
            details['field_errors'] = field_errors
        
        super().__init__(
            message=message,
            error_code=error_code,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )
        self.field_errors = field_errors or {}
    
    def has_field_errors(self) -> bool:
        """Check if there are field-specific errors."""
        return bool(self.field_errors)
    
    def get_field_errors(self, field_name: str) -> List[str]:
        """Get errors for a specific field."""
        return self.field_errors.get(field_name, [])
    
    def add_field_error(self, field_name: str, error_message: str) -> None:
        """Add an error for a specific field."""
        if field_name not in self.field_errors:
            self.field_errors[field_name] = []
        self.field_errors[field_name].append(error_message)


class ProcessingError(PacerException):
    """Exception raised during document or case processing."""
    
    def __init__(self,
                 message: str,
                 processing_stage: Optional[str] = None,
                 case_id: Optional[str] = None,
                 document_id: Optional[str] = None,
                 error_code: str = "PROCESSING_ERROR",
                 **kwargs):
        """
        Initialize processing error.
        
        Args:
            message: Processing error message
            processing_stage: Stage where error occurred
            case_id: Case identifier if relevant
            document_id: Document identifier if relevant
            error_code: Specific processing error code
            **kwargs: Additional parameters for base exception
        """
        details = kwargs.get('details', {})
        if processing_stage:
            details['processing_stage'] = processing_stage
        if case_id:
            details['case_id'] = case_id
        if document_id:
            details['document_id'] = document_id
        
        super().__init__(
            message=message,
            error_code=error_code,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )
        self.processing_stage = processing_stage
        self.case_id = case_id
        self.document_id = document_id


class AuthenticationError(PacerException):
    """Exception raised when authentication fails."""
    
    def __init__(self,
                 message: str,
                 auth_type: Optional[str] = None,
                 court_id: Optional[str] = None,
                 username: Optional[str] = None,
                 error_code: str = "AUTH_ERROR",
                 **kwargs):
        """
        Initialize authentication error.
        
        Args:
            message: Authentication error message
            auth_type: Type of authentication (ECF, NextGen, PACER)
            court_id: Court identifier if relevant
            username: Username if relevant (for logging, not sensitive)
            error_code: Specific authentication error code
            **kwargs: Additional parameters for base exception
        """
        details = kwargs.get('details', {})
        if auth_type:
            details['auth_type'] = auth_type
        if court_id:
            details['court_id'] = court_id
        if username:
            details['username'] = username
        
        super().__init__(
            message=message,
            error_code=error_code,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )
        self.auth_type = auth_type
        self.court_id = court_id
        self.username = username


class NavigationError(PacerException):
    """Exception raised during browser navigation."""
    
    def __init__(self,
                 message: str,
                 url: Optional[str] = None,
                 element: Optional[str] = None,
                 timeout: Optional[int] = None,
                 error_code: str = "NAVIGATION_ERROR",
                 **kwargs):
        """
        Initialize navigation error.
        
        Args:
            message: Navigation error message
            url: URL that failed to load
            element: Element that couldn't be found/interacted with
            timeout: Timeout value if relevant
            error_code: Specific navigation error code
            **kwargs: Additional parameters for base exception
        """
        details = kwargs.get('details', {})
        if url:
            details['url'] = url
        if element:
            details['element'] = element
        if timeout:
            details['timeout'] = timeout
        
        super().__init__(
            message=message,
            error_code=error_code,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )
        self.url = url
        self.element = element
        self.timeout = timeout


class DownloadError(PacerException):
    """Exception raised during file download operations."""
    
    def __init__(self,
                 message: str,
                 download_url: Optional[str] = None,
                 filename: Optional[str] = None,
                 file_size: Optional[int] = None,
                 error_code: str = "DOWNLOAD_ERROR",
                 **kwargs):
        """
        Initialize download error.
        
        Args:
            message: Download error message
            download_url: URL of file being downloaded
            filename: Target filename
            file_size: Expected file size if known
            error_code: Specific download error code
            **kwargs: Additional parameters for base exception
        """
        details = kwargs.get('details', {})
        if download_url:
            details['download_url'] = download_url
        if filename:
            details['filename'] = filename
        if file_size:
            details['file_size'] = file_size
        
        super().__init__(
            message=message,
            error_code=error_code,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )
        self.download_url = download_url
        self.filename = filename
        self.file_size = file_size


class ConfigurationError(PacerException):
    """Exception raised when configuration is invalid or missing."""
    
    def __init__(self,
                 message: str,
                 config_section: Optional[str] = None,
                 config_key: Optional[str] = None,
                 expected_type: Optional[str] = None,
                 error_code: str = "CONFIG_ERROR",
                 **kwargs):
        """
        Initialize configuration error.
        
        Args:
            message: Configuration error message
            config_section: Configuration section with error
            config_key: Specific configuration key
            expected_type: Expected data type
            error_code: Specific configuration error code
            **kwargs: Additional parameters for base exception
        """
        details = kwargs.get('details', {})
        if config_section:
            details['config_section'] = config_section
        if config_key:
            details['config_key'] = config_key
        if expected_type:
            details['expected_type'] = expected_type
        
        super().__init__(
            message=message,
            error_code=error_code,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )
        self.config_section = config_section
        self.config_key = config_key
        self.expected_type = expected_type


class ServiceError(PacerException):
    """Exception raised when a service operation fails."""
    
    def __init__(self,
                 message: str,
                 service_name: Optional[str] = None,
                 operation: Optional[str] = None,
                 retry_count: Optional[int] = None,
                 error_code: str = "SERVICE_ERROR",
                 **kwargs):
        """
        Initialize service error.
        
        Args:
            message: Service error message
            service_name: Name of service that failed
            operation: Operation that failed
            retry_count: Number of retries attempted
            error_code: Specific service error code
            **kwargs: Additional parameters for base exception
        """
        details = kwargs.get('details', {})
        if service_name:
            details['service_name'] = service_name
        if operation:
            details['operation'] = operation
        if retry_count is not None:
            details['retry_count'] = retry_count
        
        super().__init__(
            message=message,
            error_code=error_code,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )
        self.service_name = service_name
        self.operation = operation
        self.retry_count = retry_count


class PatternError(PacerException):
    """Exception raised when design pattern implementation fails."""
    
    def __init__(self,
                 message: str,
                 pattern_type: Optional[str] = None,
                 pattern_name: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None,
                 error_code: str = "PATTERN_ERROR",
                 **kwargs):
        """
        Initialize pattern error.
        
        Args:
            message: Pattern error message
            pattern_type: Type of pattern (Strategy, Observer, etc.)
            pattern_name: Specific pattern implementation name
            context: Context data when error occurred
            error_code: Specific pattern error code
            **kwargs: Additional parameters for base exception
        """
        details = kwargs.get('details', {})
        if pattern_type:
            details['pattern_type'] = pattern_type
        if pattern_name:
            details['pattern_name'] = pattern_name
        if context:
            details['context'] = context
        
        super().__init__(
            message=message,
            error_code=error_code,
            details=details,
            **{k: v for k, v in kwargs.items() if k != 'details'}
        )
        self.pattern_type = pattern_type
        self.pattern_name = pattern_name
        self.context = context


# Convenience functions for creating common exceptions
def create_validation_error(message: str, field: str, field_error: str) -> ValidationError:
    """Create a validation error for a single field."""
    return ValidationError(
        message=message,
        field_errors={field: [field_error]}
    )


def create_processing_error(message: str, stage: str, case_id: str = None) -> ProcessingError:
    """Create a processing error for a specific stage."""
    return ProcessingError(
        message=message,
        processing_stage=stage,
        case_id=case_id
    )


def create_auth_error(message: str, court_id: str, auth_type: str = "PACER") -> AuthenticationError:
    """Create an authentication error for a specific court."""
    return AuthenticationError(
        message=message,
        court_id=court_id,
        auth_type=auth_type
    )


def create_config_error(message: str, section: str, key: str = None) -> ConfigurationError:
    """Create a configuration error for a specific section/key."""
    return ConfigurationError(
        message=message,
        config_section=section,
        config_key=key
    )