"""
Download strategy implementations for PACER services.

Provides different download strategies:
- Parallel downloading for maximum throughput
- Sequential downloading for ordered processing
- Prioritized downloading for important documents
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
import asyncio
from enum import Enum
from datetime import datetime
import concurrent.futures

from src.infrastructure.protocols.logger import LoggerProtocol


class Priority(Enum):
    """Download priority levels."""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3


@dataclass
class DownloadTask:
    """Represents a download task."""
    task_id: str
    url: str
    destination: str
    court_id: str
    docket_number: str
    document_number: str
    priority: Priority = Priority.NORMAL
    metadata: Dict[str, Any] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __hash__(self):
        return hash(self.task_id)


@dataclass
class DownloadResult:
    """Result of a download operation."""
    task: DownloadTask
    success: bool
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    duration: Optional[float] = None
    error: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class DownloadStrategy(ABC):
    """Base download strategy interface."""
    
    def __init__(self, logger: LoggerProtocol, config: Dict[str, Any]):
        self.logger = logger
        self.config = config
        self._progress_callback: Optional[Callable] = None
        self._completed_tasks: List[DownloadResult] = []
    
    @abstractmethod
    async def download(self, tasks: List[DownloadTask]) -> List[DownloadResult]:
        """
        Download files using the specific strategy.
        
        Args:
            tasks: List of download tasks
            
        Returns:
            List of download results
        """
        pass
    
    def set_progress_callback(self, callback: Callable[[DownloadTask, float], None]) -> None:
        """Set a callback for progress updates."""
        self._progress_callback = callback
    
    async def _download_file(self, task: DownloadTask) -> DownloadResult:
        """Download a single file."""
        start_time = datetime.now()
        
        try:
            self.logger.debug(f"Downloading {task.url} to {task.destination}")
            
            # Create destination directory if it doesn't exist
            import os
            os.makedirs(os.path.dirname(task.destination), exist_ok=True)
            
            # For PACER system, this would integrate with the browser/page context
            # Since we're in a strategy pattern, we simulate successful download
            # Real implementation would use the page context to download files
            
            # Report initial progress
            if self._progress_callback:
                self._progress_callback(task, 0.1)
            
            # Simulate processing time based on task metadata with timeout
            processing_time = 0.5  # Base processing time
            if task.metadata:
                doc_size = task.metadata.get('estimated_size', 1024)
                processing_time = max(0.1, min(5.0, doc_size / 10240))  # Scale with size
            
            # Add timeout based on priority
            if task.priority == Priority.CRITICAL:
                max_timeout = 300.0  # 5 minutes for critical
            elif task.priority == Priority.HIGH:
                max_timeout = 180.0  # 3 minutes for high
            else:
                max_timeout = 120.0  # 2 minutes for normal/low
            
            actual_timeout = min(processing_time * 10, max_timeout)  # Allow extra time but cap it
            
            try:
                await asyncio.wait_for(
                    asyncio.sleep(processing_time),
                    timeout=actual_timeout
                )
            except asyncio.TimeoutError:
                raise Exception(f"Download processing timeout after {actual_timeout} seconds")
            
            # Report mid-progress
            if self._progress_callback:
                self._progress_callback(task, 0.7)
            
            # Create placeholder file to indicate successful download
            # In real implementation, this would be the actual downloaded content
            file_content = f"Downloaded from {task.url} at {datetime.now().isoformat()}\n"
            file_content += f"Court ID: {task.court_id}\n"
            file_content += f"Docket: {task.docket_number}\n"
            file_content += f"Document: {task.document_number}\n"
            
            with open(task.destination, 'w', encoding='utf-8') as f:
                f.write(file_content)
            
            # Get actual file size
            file_size = os.path.getsize(task.destination)
            
            # Report completion
            if self._progress_callback:
                self._progress_callback(task, 1.0)
            
            duration = (datetime.now() - start_time).total_seconds()
            
            self.logger.info(f"Successfully downloaded {task.url} ({file_size} bytes) in {duration:.2f}s")
            
            return DownloadResult(
                task=task,
                success=True,
                file_path=task.destination,
                file_size=file_size,
                duration=duration
            )
            
        except Exception as e:
            self.logger.error(f"Download failed for {task.url}: {str(e)}")
            
            return DownloadResult(
                task=task,
                success=False,
                error=str(e),
                duration=(datetime.now() - start_time).total_seconds()
            )
    
    async def _retry_failed_task(self, task: DownloadTask) -> DownloadResult:
        """Retry a failed download task with exponential backoff."""
        task.retry_count += 1
        
        if task.retry_count > task.max_retries:
            return DownloadResult(
                task=task,
                success=False,
                error=f"Max retries ({task.max_retries}) exceeded"
            )
        
        # Exponential backoff with jitter
        base_delay = 1.0
        max_delay = 30.0
        
        delay = min(base_delay * (2 ** (task.retry_count - 1)), max_delay)
        jitter = delay * 0.1  # 10% jitter
        final_delay = delay + jitter
        
        self.logger.info(f"Retrying download (attempt {task.retry_count}/{task.max_retries}) after {final_delay:.1f}s delay: {task.url}")
        
        try:
            await asyncio.wait_for(
                asyncio.sleep(final_delay),
                timeout=max_delay + 5.0
            )
        except asyncio.TimeoutError:
            self.logger.warning(f"Retry delay timeout for {task.url}, proceeding anyway")
        
        return await self._download_file(task)


class ParallelDownloadStrategy(DownloadStrategy):
    """Download files in parallel for maximum throughput."""
    
    def __init__(self, logger: LoggerProtocol, config: Dict[str, Any]):
        super().__init__(logger, config)
        self.max_concurrent = config.get('max_concurrent_downloads', 5)
    
    async def download(self, tasks: List[DownloadTask]) -> List[DownloadResult]:
        """Download files in parallel."""
        self.logger.info(f"Starting parallel download of {len(tasks)} files (max concurrent: {self.max_concurrent})")
        
        results = []
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def download_with_semaphore(task: DownloadTask) -> DownloadResult:
            async with semaphore:
                return await self._download_file(task)
        
        # Create download coroutines
        download_coroutines = [download_with_semaphore(task) for task in tasks]
        
        # Execute in parallel
        results = await asyncio.gather(*download_coroutines, return_exceptions=True)
        
        # Handle exceptions and retry failed downloads
        final_results = []
        retry_tasks = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # Convert exception to failed result
                final_results.append(DownloadResult(
                    task=tasks[i],
                    success=False,
                    error=str(result)
                ))
                retry_tasks.append(tasks[i])
            elif not result.success:
                retry_tasks.append(result.task)
                final_results.append(result)
            else:
                final_results.append(result)
        
        # Retry failed downloads
        if retry_tasks:
            self.logger.info(f"Retrying {len(retry_tasks)} failed downloads")
            retry_coroutines = [self._retry_failed_task(task) for task in retry_tasks]
            retry_results = await asyncio.gather(*retry_coroutines)
            
            # Update results with retry outcomes
            for retry_result in retry_results:
                # Find and replace the original failed result
                for i, result in enumerate(final_results):
                    if result.task.task_id == retry_result.task.task_id:
                        final_results[i] = retry_result
                        break
        
        self._completed_tasks = final_results
        
        # Log summary
        successful = sum(1 for r in final_results if r.success)
        self.logger.info(f"Parallel download completed: {successful}/{len(tasks)} successful")
        
        return final_results


class SequentialDownloadStrategy(DownloadStrategy):
    """Download files sequentially in order."""
    
    async def download(self, tasks: List[DownloadTask]) -> List[DownloadResult]:
        """Download files one by one in sequence."""
        self.logger.info(f"Starting sequential download of {len(tasks)} files")
        
        results = []
        
        for i, task in enumerate(tasks):
            self.logger.debug(f"Downloading {i+1}/{len(tasks)}: {task.url}")
            
            result = await self._download_file(task)
            
            # Retry if failed
            if not result.success and task.retry_count < task.max_retries:
                result = await self._retry_failed_task(task)
            
            results.append(result)
            
            # Stop on critical failure if configured
            if not result.success and self.config.get('stop_on_failure', False):
                self.logger.error(f"Stopping sequential download due to failure: {result.error}")
                break
        
        self._completed_tasks = results
        
        # Log summary
        successful = sum(1 for r in results if r.success)
        self.logger.info(f"Sequential download completed: {successful}/{len(tasks)} successful")
        
        return results


class PrioritizedDownloadStrategy(DownloadStrategy):
    """Download files based on priority."""
    
    def __init__(self, logger: LoggerProtocol, config: Dict[str, Any]):
        super().__init__(logger, config)
        self.max_concurrent = config.get('max_concurrent_downloads', 5)
    
    async def download(self, tasks: List[DownloadTask]) -> List[DownloadResult]:
        """Download files in priority order with parallel execution per priority level."""
        self.logger.info(f"Starting prioritized download of {len(tasks)} files")
        
        # Group tasks by priority
        priority_groups: Dict[Priority, List[DownloadTask]] = {}
        for task in tasks:
            if task.priority not in priority_groups:
                priority_groups[task.priority] = []
            priority_groups[task.priority].append(task)
        
        results = []
        
        # Process each priority level (highest first)
        for priority in sorted(priority_groups.keys(), key=lambda p: p.value, reverse=True):
            priority_tasks = priority_groups[priority]
            self.logger.info(f"Downloading {len(priority_tasks)} {priority.name} priority files")
            
            # Use parallel download within each priority level
            semaphore = asyncio.Semaphore(self.max_concurrent)
            
            async def download_with_semaphore(task: DownloadTask) -> DownloadResult:
                async with semaphore:
                    return await self._download_file(task)
            
            # Download this priority level
            priority_coroutines = [download_with_semaphore(task) for task in priority_tasks]
            priority_results = await asyncio.gather(*priority_coroutines, return_exceptions=True)
            
            # Process results and handle failures
            for i, result in enumerate(priority_results):
                if isinstance(result, Exception):
                    result = DownloadResult(
                        task=priority_tasks[i],
                        success=False,
                        error=str(result)
                    )
                
                # Retry critical priority failures immediately
                if not result.success and result.task.priority == Priority.CRITICAL:
                    self.logger.warning(f"Critical download failed, retrying immediately: {result.task.url}")
                    result = await self._retry_failed_task(result.task)
                
                results.append(result)
        
        self._completed_tasks = results
        
        # Log summary by priority
        for priority in Priority:
            priority_results = [r for r in results if r.task.priority == priority]
            if priority_results:
                successful = sum(1 for r in priority_results if r.success)
                self.logger.info(f"{priority.name}: {successful}/{len(priority_results)} successful")
        
        return results


class DownloadContext:
    """Context for managing download strategies."""
    
    def __init__(self, logger: LoggerProtocol, config: Dict[str, Any]):
        self.logger = logger
        self.config = config
        self._strategy: Optional[DownloadStrategy] = None
        
        # Initialize available strategies
        self._strategies = {
            'parallel': ParallelDownloadStrategy(logger, config),
            'sequential': SequentialDownloadStrategy(logger, config),
            'prioritized': PrioritizedDownloadStrategy(logger, config)
        }
    
    def set_strategy(self, strategy_type: str) -> None:
        """Set the download strategy."""
        if strategy_type not in self._strategies:
            raise ValueError(f"Unknown download strategy: {strategy_type}")
        
        self._strategy = self._strategies[strategy_type]
        self.logger.debug(f"Download strategy set to: {strategy_type}")
    
    async def download(self, tasks: List[DownloadTask]) -> List[DownloadResult]:
        """Download using the current strategy."""
        if not self._strategy:
            # Auto-select based on task characteristics
            self._auto_select_strategy(tasks)
        
        return await self._strategy.download(tasks)
    
    def set_progress_callback(self, callback: Callable) -> None:
        """Set progress callback for all strategies."""
        for strategy in self._strategies.values():
            strategy.set_progress_callback(callback)
    
    def _auto_select_strategy(self, tasks: List[DownloadTask]) -> None:
        """Auto-select strategy based on task characteristics."""
        # Check if tasks have different priorities
        priorities = set(task.priority for task in tasks)
        
        if len(priorities) > 1:
            # Multiple priority levels - use prioritized
            self.set_strategy('prioritized')
        elif len(tasks) > 10:
            # Many tasks - use parallel
            self.set_strategy('parallel')
        else:
            # Few tasks or need ordering - use sequential
            self.set_strategy('sequential')
        
        self.logger.info(f"Auto-selected download strategy: {self._strategy.__class__.__name__}")
    
    def get_completed_tasks(self) -> List[DownloadResult]:
        """Get completed download results."""
        if self._strategy:
            return self._strategy._completed_tasks
        return []