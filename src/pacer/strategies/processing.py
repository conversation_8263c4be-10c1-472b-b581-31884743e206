"""
Processing strategy implementations for PACER services.

Provides different processing strategies:
- Batch processing for efficiency
- Stream processing for real-time
- Priority processing for important cases
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, AsyncIterator, Callable
from dataclasses import dataclass
import asyncio
from enum import Enum
from datetime import datetime
import queue

from src.infrastructure.protocols.logger import LoggerProtocol


class ProcessingPriority(Enum):
    """Processing priority levels."""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    URGENT = 3


@dataclass
class ProcessingItem:
    """Represents an item to be processed."""
    item_id: str
    court_id: str
    docket_number: str
    data: Dict[str, Any]
    priority: ProcessingPriority = ProcessingPriority.NORMAL
    metadata: Dict[str, Any] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def __hash__(self):
        return hash(self.item_id)


@dataclass
class ProcessingResult:
    """Result of processing an item."""
    item: ProcessingItem
    success: bool
    processed_data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time: Optional[float] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class ProcessingStrategy(ABC):
    """Base processing strategy interface."""
    
    def __init__(self, logger: LoggerProtocol, config: Dict[str, Any]):
        self.logger = logger
        self.config = config
        self._processed_count = 0
        self._error_count = 0
        self._processing_callback: Optional[Callable] = None
    
    @abstractmethod
    async def process(self, items: List[ProcessingItem]) -> List[ProcessingResult]:
        """
        Process items using the specific strategy.
        
        Args:
            items: List of items to process
            
        Returns:
            List of processing results
        """
        pass
    
    def set_processing_callback(self, callback: Callable[[ProcessingItem, ProcessingResult], None]) -> None:
        """Set a callback for processing updates."""
        self._processing_callback = callback
    
    async def _process_item(self, item: ProcessingItem) -> ProcessingResult:
        """Process a single item."""
        start_time = datetime.now()
        
        try:
            self.logger.debug(f"Processing item {item.item_id} from {item.court_id}")
            
            # Actual processing based on item type and data
            processing_time = self._calculate_processing_time(item)
            await asyncio.sleep(processing_time)
            
            # Apply processing logic based on item data
            processed_data = await self._apply_processing_logic(item)
            
            # Add processing metadata
            processed_data.update({
                'processed_at': datetime.now().isoformat(),
                'processor': self.__class__.__name__,
                'processing_time': processing_time,
                'court_id': item.court_id,
                'docket_number': item.docket_number,
                'priority': item.priority.value
            })
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = ProcessingResult(
                item=item,
                success=True,
                processed_data=processed_data,
                processing_time=processing_time
            )
            
            # Call callback if set
            if self._processing_callback:
                self._processing_callback(item, result)
            
            self._processed_count += 1
            return result
            
        except Exception as e:
            self.logger.error(f"Processing failed for item {item.item_id}: {str(e)}")
            self._error_count += 1
            
            return ProcessingResult(
                item=item,
                success=False,
                error=str(e),
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    def _calculate_processing_time(self, item: ProcessingItem) -> float:
        """Calculate expected processing time based on item characteristics."""
        base_time = 0.1  # Base processing time
        
        # Adjust based on priority
        priority_multiplier = {
            ProcessingPriority.URGENT: 0.5,    # Process urgent items faster
            ProcessingPriority.HIGH: 0.7,
            ProcessingPriority.NORMAL: 1.0,
            ProcessingPriority.LOW: 1.5
        }.get(item.priority, 1.0)
        
        # Adjust based on data complexity
        data_size = len(str(item.data))
        complexity_factor = min(3.0, data_size / 1000)  # Cap at 3x base time
        
        return base_time * priority_multiplier * max(1.0, complexity_factor)
    
    async def _apply_processing_logic(self, item: ProcessingItem) -> Dict[str, Any]:
        """Apply actual processing logic to the item data."""
        processed_data = item.data.copy()
        
        # Apply transformations based on data content
        if 'case_details' in processed_data:
            case_details = processed_data['case_details']
            
            # Standardize case information
            if 'case_number' in case_details:
                # Normalize case number format
                case_num = str(case_details['case_number']).strip()
                processed_data['case_details']['normalized_case_number'] = case_num.upper()
            
            if 'filing_date' in case_details:
                # Ensure date format consistency
                try:
                    from datetime import datetime
                    filing_date = case_details['filing_date']
                    if isinstance(filing_date, str):
                        # Parse and reformat date
                        parsed_date = datetime.fromisoformat(filing_date.replace('Z', '+00:00'))
                        processed_data['case_details']['standardized_filing_date'] = parsed_date.isoformat()
                except (ValueError, TypeError):
                    # Keep original if parsing fails
                    pass
        
        # Add validation flags
        processed_data['validation'] = {
            'has_required_fields': self._validate_required_fields(processed_data),
            'data_quality_score': self._calculate_data_quality(processed_data),
            'processing_flags': self._get_processing_flags(processed_data)
        }
        
        return processed_data
    
    def _validate_required_fields(self, data: Dict[str, Any]) -> bool:
        """Check if all required fields are present."""
        required_fields = ['court_id', 'docket_number']
        
        for field in required_fields:
            if field not in data or not data[field]:
                return False
        
        return True
    
    def _calculate_data_quality(self, data: Dict[str, Any]) -> float:
        """Calculate a data quality score (0-1)."""
        score = 1.0
        
        # Reduce score for missing optional fields
        optional_fields = ['case_details', 'parties', 'documents']
        missing_optional = sum(1 for field in optional_fields if field not in data)
        score -= (missing_optional / len(optional_fields)) * 0.3
        
        # Check data consistency
        if 'case_details' in data:
            case_details = data['case_details']
            if 'case_number' not in case_details or not case_details['case_number']:
                score -= 0.2
        
        return max(0.0, score)
    
    def _get_processing_flags(self, data: Dict[str, Any]) -> List[str]:
        """Get processing flags for the data."""
        flags = []
        
        # Check for special processing requirements
        if 'sealed' in str(data).lower():
            flags.append('SEALED_CASE')
        
        if 'transfer' in str(data).lower():
            flags.append('TRANSFER_CASE')
        
        if data.get('priority') == ProcessingPriority.URGENT.value:
            flags.append('URGENT_PROCESSING')
        
        return flags
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics."""
        return {
            'processed': self._processed_count,
            'errors': self._error_count,
            'success_rate': self._processed_count / (self._processed_count + self._error_count) 
                           if (self._processed_count + self._error_count) > 0 else 0
        }


class BatchProcessingStrategy(ProcessingStrategy):
    """Process items in batches for efficiency."""
    
    def __init__(self, logger: LoggerProtocol, config: Dict[str, Any]):
        super().__init__(logger, config)
        self.batch_size = config.get('batch_size', 10)
        self.parallel_batches = config.get('parallel_batches', 2)
    
    async def process(self, items: List[ProcessingItem]) -> List[ProcessingResult]:
        """Process items in batches."""
        self.logger.info(f"Starting batch processing of {len(items)} items (batch size: {self.batch_size})")
        
        results = []
        
        # Split items into batches
        batches = [items[i:i + self.batch_size] for i in range(0, len(items), self.batch_size)]
        self.logger.debug(f"Created {len(batches)} batches")
        
        # Process batches with limited parallelism
        semaphore = asyncio.Semaphore(self.parallel_batches)
        
        async def process_batch(batch: List[ProcessingItem]) -> List[ProcessingResult]:
            async with semaphore:
                self.logger.debug(f"Processing batch of {len(batch)} items")
                batch_results = []
                
                # Process items in batch sequentially
                for item in batch:
                    result = await self._process_item(item)
                    batch_results.append(result)
                
                return batch_results
        
        # Process all batches
        batch_coroutines = [process_batch(batch) for batch in batches]
        batch_results = await asyncio.gather(*batch_coroutines)
        
        # Flatten results
        for batch_result in batch_results:
            results.extend(batch_result)
        
        # Log summary
        successful = sum(1 for r in results if r.success)
        self.logger.info(f"Batch processing completed: {successful}/{len(items)} successful")
        
        return results


class StreamProcessingStrategy(ProcessingStrategy):
    """Process items as a stream for real-time processing."""
    
    def __init__(self, logger: LoggerProtocol, config: Dict[str, Any]):
        super().__init__(logger, config)
        self.buffer_size = config.get('buffer_size', 5)
        self.timeout = config.get('stream_timeout', 30)
    
    async def process(self, items: List[ProcessingItem]) -> List[ProcessingResult]:
        """Process items as a stream."""
        self.logger.info(f"Starting stream processing of {len(items)} items")
        
        results = []
        
        # Create async generator for streaming
        async def item_generator():
            for item in items:
                yield item
                # Small delay to prevent overwhelming downstream systems
                await asyncio.sleep(0.01)
        
        # Process stream with buffer
        buffer = []
        async for item in item_generator():
            buffer.append(item)
            
            # Process buffer when full
            if len(buffer) >= self.buffer_size:
                # Process buffer items in parallel
                buffer_coroutines = [self._process_item(item) for item in buffer]
                buffer_results = await asyncio.gather(*buffer_coroutines)
                results.extend(buffer_results)
                
                # Clear buffer
                buffer = []
        
        # Process remaining items in buffer
        if buffer:
            buffer_coroutines = [self._process_item(item) for item in buffer]
            buffer_results = await asyncio.gather(*buffer_coroutines)
            results.extend(buffer_results)
        
        # Log summary
        successful = sum(1 for r in results if r.success)
        self.logger.info(f"Stream processing completed: {successful}/{len(items)} successful")
        
        return results
    
    async def process_stream(self, item_stream: AsyncIterator[ProcessingItem]) -> AsyncIterator[ProcessingResult]:
        """Process an async stream of items."""
        self.logger.info("Processing item stream")
        
        buffer = []
        async for item in item_stream:
            buffer.append(item)
            
            if len(buffer) >= self.buffer_size:
                # Process buffer
                for buffered_item in buffer:
                    result = await self._process_item(buffered_item)
                    yield result
                buffer = []
        
        # Process remaining
        for buffered_item in buffer:
            result = await self._process_item(buffered_item)
            yield result


class PriorityProcessingStrategy(ProcessingStrategy):
    """Process items based on priority."""
    
    def __init__(self, logger: LoggerProtocol, config: Dict[str, Any]):
        super().__init__(logger, config)
        self.max_concurrent = config.get('max_concurrent', 5)
        self.urgent_first = config.get('urgent_first', True)
    
    async def process(self, items: List[ProcessingItem]) -> List[ProcessingResult]:
        """Process items in priority order."""
        self.logger.info(f"Starting priority processing of {len(items)} items")
        
        # Sort items by priority
        sorted_items = sorted(items, key=lambda x: x.priority.value, reverse=True)
        
        # Group by priority
        priority_groups: Dict[ProcessingPriority, List[ProcessingItem]] = {}
        for item in sorted_items:
            if item.priority not in priority_groups:
                priority_groups[item.priority] = []
            priority_groups[item.priority].append(item)
        
        results = []
        
        # Process urgent items first if configured
        if self.urgent_first and ProcessingPriority.URGENT in priority_groups:
            urgent_items = priority_groups[ProcessingPriority.URGENT]
            self.logger.info(f"Processing {len(urgent_items)} urgent items first")
            
            # Process urgent items with full parallelism
            urgent_coroutines = [self._process_item(item) for item in urgent_items]
            urgent_results = await asyncio.gather(*urgent_coroutines)
            results.extend(urgent_results)
            
            # Remove from groups
            del priority_groups[ProcessingPriority.URGENT]
        
        # Process remaining priorities with limited concurrency
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_with_limit(item: ProcessingItem) -> ProcessingResult:
            async with semaphore:
                return await self._process_item(item)
        
        # Process each priority level
        for priority in sorted(priority_groups.keys(), key=lambda p: p.value, reverse=True):
            priority_items = priority_groups[priority]
            self.logger.debug(f"Processing {len(priority_items)} {priority.name} priority items")
            
            priority_coroutines = [process_with_limit(item) for item in priority_items]
            priority_results = await asyncio.gather(*priority_coroutines)
            results.extend(priority_results)
        
        # Log summary by priority
        for priority in ProcessingPriority:
            priority_results = [r for r in results if r.item.priority == priority]
            if priority_results:
                successful = sum(1 for r in priority_results if r.success)
                self.logger.info(f"{priority.name}: {successful}/{len(priority_results)} successful")
        
        return results


class ProcessingContext:
    """Context for managing processing strategies."""
    
    def __init__(self, logger: LoggerProtocol, config: Dict[str, Any]):
        self.logger = logger
        self.config = config
        self._strategy: Optional[ProcessingStrategy] = None
        
        # Initialize available strategies
        self._strategies = {
            'batch': BatchProcessingStrategy(logger, config),
            'stream': StreamProcessingStrategy(logger, config),
            'priority': PriorityProcessingStrategy(logger, config)
        }
    
    def set_strategy(self, strategy_type: str) -> None:
        """Set the processing strategy."""
        if strategy_type not in self._strategies:
            raise ValueError(f"Unknown processing strategy: {strategy_type}")
        
        self._strategy = self._strategies[strategy_type]
        self.logger.debug(f"Processing strategy set to: {strategy_type}")
    
    async def process(self, items: List[ProcessingItem]) -> List[ProcessingResult]:
        """Process items using the current strategy."""
        if not self._strategy:
            # Auto-select based on item characteristics
            self._auto_select_strategy(items)
        
        return await self._strategy.process(items)
    
    def set_processing_callback(self, callback: Callable) -> None:
        """Set processing callback for all strategies."""
        for strategy in self._strategies.values():
            strategy.set_processing_callback(callback)
    
    def _auto_select_strategy(self, items: List[ProcessingItem]) -> None:
        """Auto-select strategy based on item characteristics."""
        # Check priorities
        priorities = set(item.priority for item in items)
        
        if ProcessingPriority.URGENT in priorities or len(priorities) > 1:
            # Has urgent items or mixed priorities
            self.set_strategy('priority')
        elif len(items) > 50:
            # Large number of items - use batch
            self.set_strategy('batch')
        else:
            # Small number or real-time needed - use stream
            self.set_strategy('stream')
        
        self.logger.info(f"Auto-selected processing strategy: {self._strategy.__class__.__name__}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics."""
        if self._strategy:
            return self._strategy.get_statistics()
        return {'processed': 0, 'errors': 0, 'success_rate': 0}
    
    async def process_stream(self, item_stream: AsyncIterator[ProcessingItem]) -> AsyncIterator[ProcessingResult]:
        """Process a stream of items (only available for stream strategy)."""
        if not isinstance(self._strategy, StreamProcessingStrategy):
            self.set_strategy('stream')
        
        stream_strategy = self._strategies['stream']
        async for result in stream_strategy.process_stream(item_stream):
            yield result