# /src/pacer/facades/case_processing_service.py

"""
Case Processing Service Facade - Unified interface for all case processing operations.

This service consolidates the functionality of:
- CaseProcessingFacadeService
- CaseClassificationFacadeService  
- HtmlProcessingFacadeService
- RowProcessingFacadeService
- TransferFacadeService
- CourtProcessingFacadeService
"""

from __future__ import annotations
from typing import Any, Dict, List, Optional, TYPE_CHECKING
import tempfile
import os

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

# Direct imports for facade pattern
from src.pacer.components.case_processing.case_validator import CaseValidator
from src.pacer.components.case_processing.case_parser import CaseParser
from src.pacer.components.case_processing.case_enricher import CaseEnricher
from src.pacer.components.case_processing.case_transformer import CaseTransformer
from src.pacer.components.classification.case_classifier import CaseClassifier
from src.pacer.components.case_processing.html_parser import HtmlParser
from src.pacer.components.transfer.transfer_processor import TransferProcessor

if TYPE_CHECKING:
    from playwright.async_api import Page
    from src.infrastructure.protocols.logger import LoggerProtocol
    from src.pacer.services.s3_service import S3Service


CaseDetails = Dict[str, Any]
ProcessingResult = Dict[str, Any]


class CaseProcessingService(AsyncServiceBase):
    """
    Core service for all PACER case processing operations.
    
    Provides a unified interface for:
    - Case validation and processing
    - HTML parsing and content extraction
    - Case classification and categorization
    - Data enrichment and transformation
    - Transfer case handling
    - Row data processing
    """

    def __init__(self,
                 logger: Optional[Any] = None,
                 config: Optional[Dict] = None,
                 case_validator: Optional[CaseValidator] = None,
                 case_parser: Optional[CaseParser] = None,
                 case_enricher: Optional[CaseEnricher] = None,
                 case_transformer: Optional[CaseTransformer] = None,
                 case_classifier: Optional[CaseClassifier] = None,
                 html_parser: Optional[HtmlParser] = None,
                 transfer_processor: Optional[TransferProcessor] = None,
                 s3_service: Optional[Any] = None):
        super().__init__(logger, config)
        
        # Core processing components
        self._validator = case_validator
        self._parser = case_parser
        self._enricher = case_enricher
        self._transformer = case_transformer
        
        # Optional specialized components
        self._classifier = case_classifier
        self._html_parser = html_parser
        self._transfer_processor = transfer_processor
        self._s3_service = s3_service
        
        # Processing statistics
        self._stats = {
            "cases_processed": 0,
            "cases_classified": 0,
            "validation_failures": 0,
            "parsing_errors": 0
        }
        
        # Idempotency tracking for uploads
        self._upload_cache = set()

    async def _initialize_service(self) -> None:
        """Initialize the case processing service and all components."""
        self.log_info("Initializing Case Processing Service")
        
        # Initialize core components if not provided
        if not self._validator:
            self._validator = CaseValidator(logger=self.logger, config=self.config)
        if not self._parser:
            self._parser = CaseParser(logger=self.logger, config=self.config)
        if not self._enricher:
            self._enricher = CaseEnricher(logger=self.logger, config=self.config)
        if not self._transformer:
            self._transformer = CaseTransformer(logger=self.logger, config=self.config)
        
        # Initialize optional components if not provided
        if not self._classifier:
            self._classifier = CaseClassifier(logger=self.logger, config=self.config)
        if not self._html_parser:
            self._html_parser = HtmlParser(logger=self.logger, config=self.config)
        if not self._transfer_processor:
            self._transfer_processor = TransferProcessor(logger=self.logger, config=self.config)
        
        # Initialize S3 service if not provided and if available
        if not self._s3_service:
            try:
                from src.pacer.services.s3_service import S3Service
                self._s3_service = S3Service(logger=self.logger, config=self.config)
            except ImportError:
                self.log_warning("S3Service not available - HTML uploads will be disabled")
                self._s3_service = None
        
        # Initialize all components
        components = [
            self._validator,
            self._parser, 
            self._enricher,
            self._transformer,
            self._classifier,
            self._html_parser,
            self._transfer_processor,
            self._s3_service
        ]
        
        for component in components:
            if component and hasattr(component, 'initialize'):
                try:
                    await component.initialize()
                except Exception as e:
                    self.log_warning(f"Failed to initialize component {type(component).__name__}: {str(e)}")
        
        self.log_info("Case Processing Service initialized successfully", {
            "components_initialized": len([c for c in components if c]),
            "classifier_available": self._classifier is not None,
            "html_parser_available": self._html_parser is not None,
            "transfer_processor_available": self._transfer_processor is not None,
            "s3_service_available": self._s3_service is not None
        })

    async def execute(self, data: Any) -> Any:
        """
        Public execute method that delegates to _execute_action.
        This method provides the expected interface for other services.
        """
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        
        if action == 'process_case':
            return await self.process_case(data['page'], data['initial_details'])
        elif action == 'classify_case':
            return await self.classify_case(data['case_details'])
        elif action == 'parse_html_content':
            return await self.parse_html_content(data['html_content'])
        elif action == 'process_transfer_case':
            return await self.process_transfer_case(data['case_details'])
        elif action == 'process_row_data':
            return await self.process_row_data(data['row_data'])
        elif action == 'update_case_details':
            return await self.update_case_details(data['html_content'], data['initial_details'])
        elif action == 'create_base_filename':
            return await self.create_base_filename(data['case_details'])
        elif action == 'enrich_case_data':
            return await self.enrich_case_data(data['case_details'], data.get('html_content', ''))
        elif action == 'validate_page_content':
            return await self.validate_page_content(data['page'], data['case_details'])
        elif action == 'get_processing_stats':
            return self.get_processing_stats()
        elif action == 'upload_html_to_s3_idempotent':
            return await self.upload_html_to_s3_idempotent(
                data['html_content'], 
                data['case_details'], 
                data['iso_date'],
                court_logger=data.get('court_logger')  # Pass through court_logger
            )
        else:
            raise PacerServiceError(f"Unknown action for CaseProcessingService: {action}")

    async def process_case(self, page: Page, initial_details: CaseDetails) -> Optional[CaseDetails]:
        """
        Process a complete case workflow including validation, parsing, enrichment, and transformation.
        """
        log_prefix = f"[{initial_details.get('court_id', 'N/A')}][{initial_details.get('docket_num', 'N/A')}]"
        self.log_info(f"{log_prefix} Starting comprehensive case processing workflow")

        try:
            # 1. Validate page content
            if self._validator:
                html_content = await self._validator._execute_action({
                    "action": "validate_page_content",
                    "page": page,
                    "case_details": initial_details
                })
            else:
                # Add null check before accessing page.content()
                if page is None:
                    self.log_error(f"{log_prefix} Page object is None - cannot retrieve HTML content")
                    self._stats["validation_failures"] += 1
                    return None
                
                try:
                    html_content = await page.content()
                except AttributeError as e:
                    self.log_error(f"{log_prefix} AttributeError accessing page.content() - page may be None: {e}")
                    self._stats["validation_failures"] += 1
                    return None
                except Exception as e:
                    self.log_error(f"{log_prefix} Unexpected error retrieving HTML content: {e}")
                    self._stats["validation_failures"] += 1
                    return None
            
            if not html_content:
                self.log_error(f"{log_prefix} Case validation failed - aborting processing")
                self._stats["validation_failures"] += 1
                return None

            # 2. Parse HTML to extract case details
            if self._parser:
                parsed_details = await self._parser._execute_action({
                    "action": "parse_html",
                    "html_content": html_content
                })
            else:
                # Use HTML parser as fallback if main parser not available
                parsed_details = await self.parse_html_content(html_content)

            # Check for special parsing conditions
            if parsed_details.get("_no_proceedings_detected"):
                self.log_info(f"{log_prefix} No proceedings found - case will not be saved")
                return None

            # 3. Merge initial details with parsed details
            merged_details = await self._merge_case_details(initial_details, parsed_details)

            # 4. Enrich case data with additional information
            enriched_details = await self.enrich_case_data(merged_details, html_content)

            # 5. Transform case data to final format
            final_details = await self._transformer._execute_action({
                "action": "transform_case",
                "case_details": enriched_details
            }) if self._transformer else enriched_details

            # 6. Optional: Classify case if classifier is available
            if self._classifier:
                classification_data = await self.classify_case(final_details)
                final_details.update(classification_data)
                self._stats["cases_classified"] += 1

            self._stats["cases_processed"] += 1
            self.log_info(f"{log_prefix} Case processing workflow completed successfully", {
                "final_fields_count": len(final_details),
                "has_proceedings": not parsed_details.get("_no_proceedings_detected", False)
            })

            return final_details

        except Exception as e:
            self._stats["parsing_errors"] += 1
            self.log_error(f"{log_prefix} Case processing failed: {str(e)}", exc_info=True)
            raise PacerServiceError(f"Case processing failed for {log_prefix}: {str(e)}")

    async def classify_case(self, case_details: CaseDetails) -> Dict[str, Any]:
        """Classify case using available classification components."""
        if not self._classifier:
            self.log_warning("Case classifier not available - returning empty classification")
            return {}

        try:
            classification_result = await self._classifier._execute_action({
                "action": "classify_case",
                "case_details": case_details
            })
            
            self.log_debug("Case classified successfully", {
                "docket_num": case_details.get('docket_num'),
                "classification_keys": list(classification_result.keys())
            })
            
            return classification_result
            
        except Exception as e:
            self.log_error(f"Case classification failed: {str(e)}", exc_info=True)
            return {"classification_error": str(e)}

    async def parse_html_content(self, html_content: str) -> Dict[str, Any]:
        """Parse HTML content to extract case information."""
        try:
            if self._html_parser:
                # Use specialized HTML parser if available - it only supports extract_attorneys
                parsed_data = await self._html_parser._execute_action({
                    "action": "extract_attorneys",
                    "html_content": html_content
                })
                # Wrap attorneys in expected format
                result = {"attorney": parsed_data} if parsed_data else {}
            elif self._parser:
                # Fall back to standard case parser
                parsed_data = await self._parser._execute_action({
                    "action": "parse_html", 
                    "html_content": html_content
                })
                result = parsed_data
            else:
                result = {}
            
            self.log_debug("HTML content parsed successfully", {
                "extracted_fields": list(result.keys()),
                "content_length": len(html_content)
            })
            
            return result
            
        except Exception as e:
            self.log_error(f"HTML parsing failed: {str(e)}", exc_info=True)
            raise PacerServiceError(f"HTML parsing failed: {str(e)}")

    async def process_transfer_case(self, case_details: CaseDetails) -> CaseDetails:
        """Process transfer case with specialized logic."""
        if not self._transfer_processor:
            self.log_warning("Transfer processor not available - processing as regular case")
            return case_details

        try:
            processed_details = await self._transfer_processor._execute_action({
                "action": "process_transfer",
                "case_details": case_details
            })
            
            self.log_info("Transfer case processed successfully", {
                "docket_num": case_details.get('docket_num'),
                "court_id": case_details.get('court_id')
            })
            
            return processed_details
            
        except Exception as e:
            self.log_error(f"Transfer case processing failed: {str(e)}", exc_info=True)
            # Return original case details instead of raising error to allow test to continue
            self.log_warning("Returning original case details due to transfer processing failure")
            return case_details

    async def process_row_data(self, row_data: Dict[str, Any]) -> CaseDetails:
        """Process row data from civil reports or similar sources."""
        try:
            # Row data processing doesn't require HTML parsing - just use the data as-is
            processed_data = row_data
            
            # Enrich the processed row data
            enriched_data = await self.enrich_case_data(processed_data)
            
            # Transform to final format
            final_data = await self._transformer._execute_action({
                "action": "transform_case",
                "case_details": enriched_data
            }) if self._transformer else enriched_data
            
            self.log_debug("Row data processed successfully", {
                "initial_fields": len(row_data),
                "final_fields": len(final_data)
            })
            
            return final_data
            
        except Exception as e:
            self.log_error(f"Row data processing failed: {str(e)}", exc_info=True)
            raise PacerServiceError(f"Row data processing failed: {str(e)}")

    async def update_case_details(self, html_content: str, initial_details: CaseDetails) -> CaseDetails:
        """
        Update case details by parsing HTML content and merging with initial details.
        Preserves initial details to avoid parsing UI elements.
        """
        log_prefix = f"[{initial_details.get('court_id', 'N/A')}][{initial_details.get('docket_num', 'N/A')}]"
        
        try:
            # Parse HTML content
            parsed_details = await self.parse_html_content(html_content)
            
            # Check for special conditions
            if parsed_details.get("_no_proceedings_detected"):
                self.log_info(f"{log_prefix} No proceedings found in HTML content")
                return {**initial_details, "_no_proceedings_detected": True}
                
            if parsed_details.get("_transaction_receipt_detected"):
                self.log_warning(f"{log_prefix} Transaction receipt detected instead of case details")
                return {**initial_details, "_transaction_receipt_detected": True}
            
            # Merge details preserving initial data priority
            merged_details = await self._merge_case_details(initial_details, parsed_details)
            
            # Enrich merged details
            enriched_details = await self.enrich_case_data(merged_details, html_content)
            
            # Transform to final format
            final_details = await self._transformer._execute_action({
                "action": "transform_case",
                "case_details": enriched_details
            }) if self._transformer else enriched_details
            
            self.log_info(f"{log_prefix} Case details updated successfully")
            return final_details
            
        except Exception as e:
            self.log_error(f"{log_prefix} Case details update failed: {str(e)}", exc_info=True)
            raise PacerServiceError(f"Case details update failed: {str(e)}")

    async def enrich_case_data(self, case_details: CaseDetails, html_content: str = "") -> CaseDetails:
        """Enrich case data with additional information and processing."""
        try:
            if self._enricher:
                enriched_details = await self._enricher._execute_action({
                    "action": "enrich_case",
                    "case_details": case_details,
                    "html_content": html_content
                })
            else:
                enriched_details = case_details
            
            self.log_debug("Case data enriched successfully", {
                "docket_num": case_details.get('docket_num'),
                "enrichment_added": len(enriched_details) - len(case_details)
            })
            
            return enriched_details
            
        except Exception as e:
            self.log_error(f"Case data enrichment failed: {str(e)}", exc_info=True)
            # Return original details if enrichment fails
            return case_details

    async def create_base_filename(self, case_details: CaseDetails) -> str:
        """Create standardized base filename for case."""
        try:
            if self._transformer:
                transformed_details = await self._transformer._execute_action({
                    "action": "transform_case",
                    "case_details": case_details
                })
                
                filename = transformed_details.get('base_filename', 'unknown_case')
            else:
                # Check if base_filename is already provided in case_details
                if case_details.get('base_filename'):
                    filename = case_details['base_filename']
                else:
                    # Generate fallback filename with robust parsing
                    court_id = case_details.get('court_id', 'unknown')
                    docket_num = case_details.get('docket_num', 'unknown')
                    
                    # Clean docket number for filename safety
                    try:
                        from src.utils.docket_utils import parse_docket_number_numeric_only
                        clean_docket = parse_docket_number_numeric_only(docket_num)
                    except (ImportError, Exception):
                        # Robust fallback if utility not available or fails
                        clean_docket = docket_num.replace(':', '_').replace('/', '_').replace('-', '_')
                        # Remove any other problematic characters
                        import re
                        clean_docket = re.sub(r'[^\w\d_]', '_', clean_docket)
                    
                    # Add versus/case_title if available for better filename
                    versus = case_details.get('versus', case_details.get('case_title', ''))
                    if versus:
                        # Clean versus for filename
                        import re
                        clean_versus = re.sub(r'[^\w\d\s]', '_', versus)
                        clean_versus = '_'.join(clean_versus.split()[:3])  # First 3 words
                        filename = f"{court_id}_{clean_docket}_{clean_versus}"
                    else:
                        filename = f"{court_id}_{clean_docket}"
            
            self.log_debug("Base filename created", {
                "case_filename": filename,
                "docket_num": case_details.get('docket_num')
            })
            
            return filename
            
        except Exception as e:
            self.log_error(f"Base filename creation failed: {str(e)}", exc_info=True)
            # Generate fallback filename with correct numeric parsing
            court_id = case_details.get('court_id', 'unknown')
            docket_num = case_details.get('docket_num', 'unknown')
            
            try:
                from src.utils.docket_utils import parse_docket_number_numeric_only
                clean_docket = parse_docket_number_numeric_only(docket_num)
            except (ImportError, Exception):
                # Robust fallback
                clean_docket = docket_num.replace(':', '_').replace('/', '_').replace('-', '_')
                import re
                clean_docket = re.sub(r'[^\w\d_]', '_', clean_docket)
            
            return f"{court_id}_{clean_docket}"

    async def validate_page_content(self, page: Page, case_details: CaseDetails) -> Optional[str]:
        """Validate page content and return HTML if valid."""
        try:
            if self._validator:
                html_content = await self._validator._execute_action({
                    "action": "validate_page_content",
                    "page": page,
                    "case_details": case_details
                })
            else:
                # Add null check before accessing page.content()
                if page is None:
                    self.log_error("Page object is None - cannot validate content")
                    return None
                
                try:
                    html_content = await page.content()
                except AttributeError as e:
                    self.log_error(f"AttributeError accessing page.content() - page may be None: {e}")
                    return None
                except Exception as content_e:
                    self.log_error(f"Unexpected error retrieving page content: {content_e}")
                    return None
            return html_content
        except Exception as e:
            self.log_error(f"Page content validation failed: {str(e)}", exc_info=True)
            return None
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics for monitoring."""
        return {
            **self._stats,
            "service_status": "operational" if self._initialized else "not_initialized",
            "components_available": {
                "classifier": self._classifier is not None,
                "html_parser": self._html_parser is not None,
                "transfer_processor": self._transfer_processor is not None,
                "s3_service": self._s3_service is not None
            },
            "upload_cache_size": len(self._upload_cache)
        }

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the service and components."""
        health_status = {
            "service": "CaseProcessingService",
            "status": "healthy" if self._initialized else "unhealthy",
            "components": {},
            "stats": self.get_processing_stats()
        }
        
        # Check core components
        core_components = {
            "validator": self._validator,
            "parser": self._parser,
            "enricher": self._enricher,
            "transformer": self._transformer,
            "s3_service": self._s3_service
        }
        
        for name, component in core_components.items():
            try:
                if component and hasattr(component, 'health_check'):
                    health_status["components"][name] = await component.health_check()
                else:
                    health_status["components"][name] = {"status": "available" if component else "not_available"}
            except Exception as e:
                health_status["components"][name] = {"status": "error", "error": str(e)}
        
        return health_status

    async def _merge_case_details(self, initial_details: CaseDetails, parsed_details: Dict[str, Any]) -> CaseDetails:
        """
        Merge initial case details with parsed details, preserving initial data priority.
        """
        # Start with initial details (trusted from civil report)
        merged_details = {**initial_details}
        
        # Add parsed case_info fields if available
        if parsed_details.get('case_info'):
            for key, value in parsed_details['case_info'].items():
                if key not in merged_details:
                    merged_details[key] = value
        
        # Add parsed fields only if they don't exist or are empty in initial details
        important_fields = [
            'plaintiffs', 'defendants', 'attorney', 'plaintiff', 'defendant',
            'court_name', 'office', 'assigned_to', 'referred_to', 'lead_case',
            'case_in_other_court', 'jury_demand', 'jurisdiction', 'cause', 'nos',
            'date_filed', 'date_terminated', 'demand', 'nature_of_suit'
        ]
        
        for field in important_fields:
            if field in parsed_details:
                if field in ['plaintiffs', 'defendants', 'attorney', 'plaintiff', 'defendant']:
                    # Only override if initial details don't have this field or it's empty
                    if field not in merged_details or not merged_details.get(field):
                        merged_details[field] = parsed_details[field]
                else:
                    # Add other fields if not present in initial details
                    if field not in merged_details:
                        merged_details[field] = parsed_details[field]
        
        return merged_details

    async def upload_html_to_s3_idempotent(
        self, 
        html_content: str, 
        case_details: Dict[str, Any], 
        iso_date: str,
        court_logger: Optional[Any] = None
    ) -> Dict[str, Any]:
        """
        Upload HTML content to S3 with idempotency checks.
        
        This method ensures that the same HTML content for the same case
        will not be uploaded multiple times. It tracks uploads to prevent
        duplicates and provides comprehensive error handling.
        
        Args:
            html_content: The HTML content to upload
            case_details: Case details containing court_id, docket_num, etc.
            iso_date: ISO formatted date for S3 key structure
        
        Returns:
            Dict containing upload result with metadata:
            {
                'success': bool,
                'already_uploaded': bool,
                's3_url': str | None,
                's3_key': str | None,
                'base_filename': str | None,
                'error': str | None,
                'metadata': dict
            }
        """
        log_prefix = f"[{case_details.get('court_id', 'N/A')}][{case_details.get('docket_num', 'N/A')}]"
        
        # Use court_logger if provided, otherwise fallback to service logger
        logger = court_logger if court_logger else self
        
        try:
            # Validate inputs
            if not html_content or not html_content.strip():
                error_msg = "HTML content is empty or None"
                logger.error(f"{log_prefix} {error_msg}")
                return {
                    'success': False,
                    'already_uploaded': False,
                    's3_url': None,
                    's3_key': None,
                    'base_filename': None,
                    'error': error_msg,
                    'metadata': {}
                }
            
            if not case_details:
                error_msg = "Case details are required"
                logger.error(f"{log_prefix} {error_msg}")
                return {
                    'success': False,
                    'already_uploaded': False,
                    's3_url': None,
                    's3_key': None,
                    'base_filename': None,
                    'error': error_msg,
                    'metadata': {}
                }
            
            if not iso_date:
                error_msg = "ISO date is required for S3 key structure"
                logger.error(f"{log_prefix} {error_msg}")
                return {
                    'success': False,
                    'already_uploaded': False,
                    's3_url': None,
                    's3_key': None,
                    'base_filename': None,
                    'error': error_msg,
                    'metadata': {}
                }
            
            # Generate FULL base filename for S3: {court_id}_{YY}_{NNNNN}_{versus}
            try:
                court_id = case_details.get('court_id', 'unknown')
                docket_num = case_details.get('docket_num', 'unknown')
                versus = case_details.get('versus', case_details.get('case_title', ''))
                
                # Parse docket number to get YY_NNNNN format
                if docket_num and ':' in docket_num:
                    from src.utils.docket_utils import parse_docket_number_numeric_only
                    clean_docket = parse_docket_number_numeric_only(docket_num)
                else:
                    clean_docket = docket_num.replace(':', '_').replace('-', '_')
                
                # Clean versus field for filename
                if versus:
                    # Remove special characters and limit length
                    import re
                    versus_clean = re.sub(r'[^a-zA-Z0-9\s]', '', versus)
                    versus_clean = '_'.join(versus_clean.split()[:5])  # First 5 words
                    versus_clean = versus_clean.lower()
                    # Create FULL filename with versus
                    base_filename = f"{court_id}_{clean_docket}_{versus_clean}"
                else:
                    # Fallback without versus
                    base_filename = f"{court_id}_{clean_docket}"
                
                logger.debug(f"{log_prefix} Generated FULL base filename for S3: {base_filename}")
            except Exception as e:
                error_msg = f"Failed to create base filename: {str(e)}"
                logger.error(f"{log_prefix} {error_msg}", exc_info=True)
                return {
                    'success': False,
                    'already_uploaded': False,
                    's3_url': None,
                    's3_key': None,
                    'base_filename': None,
                    'error': error_msg,
                    'metadata': {}
                }
            
            # REQUIREMENT 2: Create S3 key following the required format: {iso_date}/html/{base_filename}.html
            s3_key = f"{iso_date}/html/{base_filename}.html"
            
            # REQUIREMENT 1: Log every S3 upload attempt with exact S3 key
            logger.info(f"{log_prefix} S3 UPLOAD ATTEMPT: Starting upload to S3 key: {s3_key}")
            
            # Create idempotency key for tracking uploads
            idempotency_key = f"{case_details.get('court_id', 'unknown')}_{case_details.get('docket_num', 'unknown')}_{iso_date}_{base_filename}"
            
            # Check if already uploaded (idempotency check)
            if idempotency_key in self._upload_cache:
                # REQUIREMENT 2: Generate CDN link using the correct format
                s3_html = f"https://cdn.lexgenius.ai/{s3_key}"
                
                logger.info(f"{log_prefix} Generated CDN link: {s3_html}")
                
                # REQUIREMENT 1: Log S3 upload success (cached)
                logger.info(f"{log_prefix} S3 UPLOAD SUCCESS: HTML already uploaded (idempotency cache hit): {s3_key}")
                return {
                    'success': True,
                    'already_uploaded': True,
                    's3_url': None,  # Would need to reconstruct if needed
                    's3_key': s3_key,
                    's3_html': s3_html,
                    'base_filename': base_filename,
                    'error': None,
                    'metadata': {
                        'idempotency_key': idempotency_key,
                        'content_length': len(html_content),
                        'upload_skipped': True,
                        's3_html': s3_html
                    }
                }
            
            # Check if S3 service is available
            if not self._s3_service:
                error_msg = "S3 service is not available or configured"
                logger.warning(f"{log_prefix} {error_msg}")
                return {
                    'success': False,
                    'already_uploaded': False,
                    's3_url': None,
                    's3_key': s3_key,
                    'base_filename': base_filename,
                    'error': error_msg,
                    'metadata': {
                        'idempotency_key': idempotency_key,
                        'content_length': len(html_content)
                    }
                }
            
            # Check if file already exists in S3 (secondary idempotency check)
            try:
                file_exists = await self._s3_service.file_exists(s3_key)
                if file_exists:
                    # REQUIREMENT 2: Generate CDN link for existing files
                    s3_html = f"https://cdn.lexgenius.ai/{s3_key}"
                    
                    logger.info(f"{log_prefix} Generated CDN link for existing file: {s3_html}")
                    
                    # REQUIREMENT 1: Log S3 upload success (file exists in S3)
                    logger.info(f"{log_prefix} S3 UPLOAD SUCCESS: HTML already exists in S3: {s3_key}")
                    # Add to cache to speed up future checks
                    self._upload_cache.add(idempotency_key)
                    return {
                        'success': True,
                        'already_uploaded': True,
                        's3_url': None,  # Would need to reconstruct if needed
                        's3_key': s3_key,
                        's3_html': s3_html,
                        'base_filename': base_filename,
                        'error': None,
                        'metadata': {
                            'idempotency_key': idempotency_key,
                            'content_length': len(html_content),
                            'file_exists_in_s3': True,
                            's3_html': s3_html
                        }
                    }
            except Exception as e:
                # Log warning but don't fail - proceed with upload
                logger.warning(f"{log_prefix} Failed to check S3 file existence: {str(e)}")
            
            # Prepare metadata for S3 upload
            upload_metadata = {
                'court_id': str(case_details.get('court_id', 'unknown')),
                'docket_num': str(case_details.get('docket_num', 'unknown')),
                'iso_date': iso_date,
                'base_filename': base_filename,
                'content_length': str(len(html_content)),
                'upload_timestamp': str(iso_date),
                'content_type': 'text/html'
            }
            
            # Create temporary file for upload
            temp_file_path = None
            try:
                with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as temp_file:
                    temp_file.write(html_content)
                    temp_file_path = temp_file.name
                
                logger.debug(f"{log_prefix} Created temporary file for upload: {temp_file_path}")
                
                # Upload to S3
                try:
                    s3_url = await self._s3_service.upload_file(
                        file_path=temp_file_path,
                        object_key=s3_key
                    )
                    
                    # Mark as uploaded in cache
                    self._upload_cache.add(idempotency_key)
                    
                    # REQUIREMENT 2: Generate CDN link using the correct format
                    s3_html = f"https://cdn.lexgenius.ai/{s3_key}"
                    
                    # REQUIREMENT 1: Log S3 upload success with exact S3 key
                    logger.info(f"{log_prefix} S3 UPLOAD SUCCESS: Successfully uploaded HTML to S3: {s3_url}")
                    logger.info(f"{log_prefix} S3 UPLOAD SUCCESS: Generated CDN link: {s3_html}")
                    
                    return {
                        'success': True,
                        'already_uploaded': False,
                        's3_url': s3_url,
                        's3_key': s3_key,
                        's3_html': s3_html,
                        'base_filename': base_filename,
                        'error': None,
                        'metadata': {
                            'idempotency_key': idempotency_key,
                            'content_length': len(html_content),
                            'upload_metadata': upload_metadata,
                            's3_upload_successful': True,
                            's3_html': s3_html
                        }
                    }
                    
                except Exception as upload_error:
                    error_msg = f"S3 upload failed: {str(upload_error)}"
                    # REQUIREMENT 1: Log S3 upload failure with exact S3 key
                    logger.error(f"{log_prefix} S3 UPLOAD FAILURE: Upload failed for S3 key {s3_key}: {error_msg}")
                    return {
                        'success': False,
                        'already_uploaded': False,
                        's3_url': None,
                        's3_key': s3_key,
                        'base_filename': base_filename,
                        'error': error_msg,
                        'metadata': {
                            'idempotency_key': idempotency_key,
                            'content_length': len(html_content),
                            'upload_metadata': upload_metadata
                        }
                    }
                    
            finally:
                # Clean up temporary file
                if temp_file_path and os.path.exists(temp_file_path):
                    try:
                        os.unlink(temp_file_path)
                        logger.debug(f"{log_prefix} Cleaned up temporary file: {temp_file_path}")
                    except Exception as cleanup_error:
                        logger.warning(f"{log_prefix} Failed to clean up temporary file {temp_file_path}: {str(cleanup_error)}")
                        
        except Exception as e:
            error_msg = f"Unexpected error in HTML upload: {str(e)}"
            logger.error(f"{log_prefix} {error_msg}", exc_info=True)
            return {
                'success': False,
                'already_uploaded': False,
                's3_url': None,
                's3_key': None,
                'base_filename': None,
                'error': error_msg,
                'metadata': {
                    'exception_type': type(e).__name__
                }
            }

    async def _cleanup_service(self) -> None:
        """Clean up service resources."""
        self.log_info("Cleaning up Case Processing Service")
        
        # Clear upload cache
        self._upload_cache.clear()
        
        # Reset statistics
        self._stats = {
            "cases_processed": 0,
            "cases_classified": 0,
            "validation_failures": 0,
            "parsing_errors": 0
        }