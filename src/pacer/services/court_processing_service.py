# /src/services/pacer/court_processing_facade_service.py
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
# Real component imports - fixed for proper execution
from src.pacer.components.processing.workflow_orchestrator import WorkflowOrchestrator
from src.pacer.components.processing.download_path_manager import DownloadPathManager

# Stub classes removed - using real implementations from imports above


class CourtProcessingFacadeService(AsyncServiceBase):
    """
    Facade service for orchestrating all court-level processing workflows.
    It acts as the main entry point for processing a court, routing tasks
    to the appropriate components.
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        download_path_manager: Optional[DownloadPathManager] = None,
        workflow_orchestrator: Optional[WorkflowOrchestrator] = None,
    ):
        super().__init__(logger, config)
        self.download_path_manager = download_path_manager or DownloadPathManager(logger=logger, config=config)
        # Import the facades
        from src.pacer.facades.authentication_facade import AuthenticationFacade
        from src.pacer.facades.navigation_facade import NavigationFacade
        from src.pacer.facades.report_facade import ReportFacade
        from src.pacer.facades.row_processing_facade import RowProcessingFacade
        
        # Initialize facades - store for later court logger injection
        self._facade_logger = logger
        self._facade_config = config
        auth_facade = AuthenticationFacade(logger=logger, config=config)
        nav_facade = NavigationFacade(logger=logger, config=config)
        report_facade = ReportFacade(logger=logger, config=config)
        row_facade = RowProcessingFacade(logger=logger, config=config)
        
        self.workflow_orchestrator = workflow_orchestrator or WorkflowOrchestrator(
            logger=logger, 
            config=config,
            authentication_facade=auth_facade,
            navigation_facade=nav_facade,
            report_facade=report_facade,
            row_facade=row_facade
        )

    async def execute(self, data: Any) -> Any:
        """
        Main execution method for the facade.
        """
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """
        Routes actions to the appropriate components based on processing mode.
        """
        action = data.get("action")
        if action == "handle_court_workflow":
            return await self.handle_court_processing_workflow(**data)
        elif action == "setup_download_path":
            # Expose download path setup directly if needed
            data["action"] = "setup_download_path"
            return await self.download_path_manager.execute(data)
        else:
            raise ValueError(f"Unknown action for CourtProcessingFacadeService: {action}")

    async def handle_court_processing_workflow(self, **kwargs) -> Dict[str, Any]:
        """
        Handles the overall workflow for processing a court based on the mode.
        """
        processing_mode = kwargs.get("processing_mode")
        court_id = kwargs.get("court_id")
        workflow_config = kwargs.get("workflow_config", {})
        iso_date = workflow_config.get("iso_date")

        # Create court-specific logger for this workflow
        court_logger = None
        if court_id and iso_date:
            court_logger = self.create_court_logger(court_id, iso_date)
            court_logger.info(f"Starting court processing workflow - Mode: {processing_mode}")

        log_prefix = f"[{court_id}] Workflow({processing_mode}):"
        self.log_info(f"{log_prefix} Starting court workflow via facade.")

        # Note: workflow_orchestrator is now always initialized with stub during migration
        if not self.workflow_orchestrator:
            self.log_warning("WorkflowOrchestrator is None, creating stub instance")
            self.workflow_orchestrator = WorkflowOrchestrator()

        # The processor_config needs the download path.
        # We can create it here and pass it down.
        processor_config = kwargs.get("processor_config", {})
        workflow_config = kwargs.get("workflow_config", {})
        iso_date = workflow_config.get("iso_date")

        if iso_date:
             download_path = await self.download_path_manager.execute({
                 "action": "setup_download_path", "court_id": court_id,
                 "iso_date": iso_date, "mode": "report" # default mode for now
             })
             processor_config['context_download_path'] = download_path
             kwargs["processor_config"] = processor_config

        # Extract date objects from workflow_config and add to top-level kwargs for WorkflowOrchestrator
        if "start_date_obj" in workflow_config:
            kwargs["start_date_obj"] = workflow_config["start_date_obj"]
        if "end_date_obj" in workflow_config:
            kwargs["end_date_obj"] = workflow_config["end_date_obj"]
        if iso_date:
            kwargs["iso_date"] = iso_date
        
        # Pass court logger to workflow orchestrator if available
        if court_logger:
            kwargs["court_logger"] = court_logger
            court_logger.info(f"Delegating to WorkflowOrchestrator - Processing mode: {processing_mode}")
            
            # Recreate workflow orchestrator with court logger for this execution
            from src.pacer.facades.authentication_facade import AuthenticationFacade
            from src.pacer.facades.navigation_facade import NavigationFacade
            from src.pacer.facades.report_facade import ReportFacade
            from src.pacer.facades.row_processing_facade import RowProcessingFacade
            
            # Create facades with court logger
            auth_facade = AuthenticationFacade(logger=court_logger, config=self._facade_config)
            nav_facade = NavigationFacade(logger=court_logger, config=self._facade_config)
            report_facade = ReportFacade(logger=court_logger, config=self._facade_config)
            row_facade = RowProcessingFacade(logger=court_logger, config=self._facade_config)
            
            # Create temporary workflow orchestrator with court-specific facades
            court_workflow_orchestrator = WorkflowOrchestrator(
                logger=court_logger,
                config=self._facade_config,
                authentication_facade=auth_facade,
                navigation_facade=nav_facade,
                report_facade=report_facade,
                row_facade=row_facade
            )
            
            # Use the court-specific orchestrator for this execution
            orchestrator_to_use = court_workflow_orchestrator
            court_logger.info("Created court-specific WorkflowOrchestrator with court logger propagation")
        else:
            orchestrator_to_use = self.workflow_orchestrator

        try:
            if processing_mode == 'date_range':
                kwargs["action"] = "process_court_task"
                if court_logger:
                    court_logger.info("Executing date range processing workflow")
                result = await orchestrator_to_use.execute(kwargs)
                if court_logger:
                    court_logger.info(f"Date range workflow completed - Result status: {result.get('status', 'unknown')}")
                return result
            elif processing_mode == 'specific_dockets':
                kwargs["action"] = "process_multi_docket_task"
                if court_logger:
                    court_logger.info("Executing specific dockets processing workflow")
                result = await orchestrator_to_use.execute(kwargs)
                if court_logger:
                    court_logger.info(f"Specific dockets workflow completed - Result status: {result.get('status', 'unknown')}")
                return result
            else:
                raise ValueError(f"Unknown processing mode: {processing_mode}")
        except Exception as e:
            self.log_error(f"{log_prefix} Workflow failed in facade: {e}", exc_info=True)
            if court_logger:
                court_logger.error(f"Workflow execution failed: {str(e)}", exc_info=True)
            return {'court_id': court_id, 'status': 'failed', 'error': str(e)}
