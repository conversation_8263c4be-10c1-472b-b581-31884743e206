"""
File Operations Service

Handles file operations, directory management, and data persistence for PACER processing.
Implements comprehensive data merging and filename validation for JSON saves.
Updated to use legacy path structures per Section 3.2 of the implementation plan.
"""

import json
import csv
import re
from pathlib import Path
from typing import Any, Dict, List, Optional
from datetime import datetime

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.components.data_merging.data_merger import PacerDataMerger
from src.pacer.components.validation.filename_validator import FilenameValidator


class FileOperationsService(AsyncServiceBase):
    """
    Enhanced service for handling file operations and data persistence.
    
    Integrates comprehensive data merging and filename validation to ensure:
    - HTML content is properly merged into case data before JSON save
    - All JSON filenames use validated create_base_filename method
    - Data consistency across all checkpoint integrations
    - Legacy path structures are used for all file operations
    """
    
    def __init__(
        self, 
        logger=None, 
        config=None, 
        config_service=None, 
        s3_service=None,
        data_merger: Optional[PacerDataMerger] = None,
        filename_validator: Optional[FilenameValidator] = None
    ):
        super().__init__(logger, config)
        self.config_service = config_service
        self.s3_service = s3_service
        self.base_directory = Path('./data')
        self.s3_enabled = False
        
        # Data merging and validation components
        self.data_merger = data_merger
        self.filename_validator = filename_validator
        
        # Processing statistics
        self._save_statistics = {
            "total_saves": 0,
            "merged_saves": 0,
            "filename_validations": 0,
            "filename_corrections": 0,
            "save_failures": 0,
            "pdf_saves": 0
        }
    
    async def _initialize_service(self) -> None:
        """Initialize the enhanced file operations service."""        
        if self.config_service:
            file_config = await self.config_service.get_config_value(
                'file_operations', 'storage', {}
            )
            self.base_directory = Path(file_config.get('base_directory', './data'))
            self.s3_enabled = file_config.get('s3_enabled', False)
        else:
            self.base_directory = Path('./data')
        
        # Ensure base directory exists
        self.base_directory.mkdir(parents=True, exist_ok=True)
        
        # Initialize data merger if not provided
        if not self.data_merger:
            self.data_merger = PacerDataMerger(logger=self.logger, config=self.config)
            await self.data_merger.initialize()
        
        # Initialize filename validator if not provided
        if not self.filename_validator:
            self.filename_validator = FilenameValidator(logger=self.logger, config=self.config)
            await self.filename_validator.initialize()
        
        self.log_info(f"Enhanced FileOperationsService initialized with base directory: {self.base_directory}", {
            "data_merger_available": self.data_merger is not None,
            "filename_validator_available": self.filename_validator is not None
        })
    
    async def setup_directories(self, iso_date: str) -> None:
        """
        Setup required directory structure for a given date.
        Per Section 3.2: Create legacy directory structure
        """
        try:
            # Ensure service is initialized
            if not self._initialized:
                await self.initialize()
            
            date_dir = self.base_directory / iso_date
            date_dir.mkdir(parents=True, exist_ok=True)

            # Create standard PACER directory structure (legacy compliant)
            subdirs = ['dockets', 'html', 'logs', 'screenshots', 'reports']
            for subdir in subdirs:
                (date_dir / subdir).mkdir(exist_ok=True)

            # Create logs subdirectories
            (date_dir / 'logs' / 'pacer').mkdir(parents=True, exist_ok=True)
            (date_dir / 'logs' / 'docket_report').mkdir(parents=True, exist_ok=True)

            self.log_info(f"Directory structure created for date: {iso_date}")

        except Exception as e:
            self.log_error(f"Error setting up directories for {iso_date}: {str(e)}")
            raise
    
    async def save_case_data(
        self, 
        case_data: Dict[str, Any], 
        iso_date: str,
        html_content: Optional[str] = None,
        s3_metadata: Optional[Dict[str, Any]] = None,
        court_logger: Optional[Any] = None
    ) -> str:
        """
        Save case data with comprehensive data merging and filename validation.
        Per Section 3.2: Use legacy path structure data/{iso_date}/dockets/{base_filename}.json
        
        Args:
            case_data: Primary case data dictionary
            iso_date: Processing date for directory structure
            html_content: Optional HTML content to merge
            s3_metadata: Optional S3 metadata to include
            
        Returns:
            Path to saved JSON file
        """
        log_prefix = f"[{case_data.get('court_id', 'N/A')}][{case_data.get('docket_num', 'N/A')}]"
        
        # Use court_logger if provided, otherwise fallback to service logger
        logger = court_logger if court_logger else self
        
        try:
            self._save_statistics['total_saves'] += 1
            
            # Ensure service is initialized
            if not self._initialized:
                await self.initialize()
            
            # Ensure directories exist
            await self.setup_directories(iso_date)

            # CRITICAL: Merge HTML content and all data before filename generation
            merged_case_data = await self._merge_all_data(
                case_data, html_content, s3_metadata, log_prefix
            )

            # Data Enrichment - Add fields per Section 3.1 if not present
            merged_case_data = self._enrich_case_data(merged_case_data)

            # CRITICAL: Validate and generate filename using create_base_filename method
            validated_filename = await self._validate_filename(merged_case_data, log_prefix)

            # Prepare final data for save
            final_data = await self._prepare_final_save_data(
                merged_case_data, validated_filename, log_prefix
            )

            # Save to file using legacy path structure
            file_path = await self._save_to_file(final_data, iso_date, validated_filename, log_prefix)

            self.log_info(f"{log_prefix} Case data saved successfully to: {file_path}", {
                "merged_fields": len(final_data),
                "has_html": 'html_content' in final_data,
                "has_s3_html": 's3_html' in final_data,
                "s3_html_url": final_data.get('s3_html', 'N/A'),
                "saved_filename": validated_filename
            })

            return str(file_path)

        except Exception as e:
            self._save_statistics['save_failures'] += 1
            # REQUIREMENT: Log JSON save failure with docket number
            self.log_error(f"{log_prefix} Error saving case data: {str(e)}")
            raise

    async def save_pdf(
        self,
        pdf_content: bytes,
        case_data: Dict[str, Any],
        iso_date: str,
        document_info: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Save PDF file using legacy path structure.
        Per Section 3.2: Use path data/{iso_date}/dockets/{court_id}_{year}_{case_num}_{versus}.pdf
        
        Args:
            pdf_content: PDF file content as bytes
            case_data: Case data dictionary containing court_id, year, case_num, versus
            iso_date: Processing date for directory structure
            document_info: Optional document metadata
            
        Returns:
            Path to saved PDF file
        """
        try:
            self._save_statistics['pdf_saves'] += 1
            
            # Ensure service is initialized
            if not self._initialized:
                await self.initialize()
            
            # Ensure directories exist
            await self.setup_directories(iso_date)
            
            # Extract components for PDF filename per Section 3.2
            court_id = case_data.get('court_id', 'unknown')
            year = case_data.get('year', '')
            case_num = case_data.get('case_num', '')
            versus = case_data.get('versus', '')
            
            # If year not provided, try to extract from filing_date or docket_num
            if not year:
                filing_date = case_data.get('filing_date', '')
                if filing_date and len(filing_date) >= 4:
                    year = filing_date[:4]
                elif case_data.get('docket_num', ''):
                    # Try to extract year from docket number (e.g., "2:23-cv-12345")
                    docket_parts = case_data.get('docket_num', '').split('-')
                    if len(docket_parts) >= 2:
                        year_candidate = docket_parts[0].split(':')[-1]
                        if year_candidate.isdigit() and len(year_candidate) == 2:
                            year = f"20{year_candidate}"  # Assume 2000s
                        elif year_candidate.isdigit() and len(year_candidate) == 4:
                            year = year_candidate
            
            # Clean components for filesystem safety
            court_id = self._clean_filename_component(court_id)
            year = self._clean_filename_component(year or 'unknown')
            case_num = self._clean_filename_component(case_num or 'unknown')
            versus = self._clean_filename_component(versus or 'unknown')
            
            # Generate PDF filename per legacy structure
            pdf_filename = f"{court_id}_{year}_{case_num}_{versus}.pdf"
            
            # Save to legacy path structure: data/{iso_date}/dockets/
            dockets_dir = self.base_directory / iso_date / 'dockets'
            pdf_path = dockets_dir / pdf_filename
            
            # Write PDF content to file
            with open(pdf_path, 'wb') as f:
                f.write(pdf_content)
            
            self.log_info(f"PDF saved to legacy path: {pdf_path}")
            
            return str(pdf_path)
            
        except Exception as e:
            self.log_error(f"Error saving PDF: {str(e)}")
            raise

    async def save_case_data_to_json(
        self, 
        case_data: Dict[str, Any], 
        court_id: str, 
        iso_date: str,
        html_content: Optional[str] = None
    ) -> str:
        """
        Legacy method signature for backward compatibility.
        Delegates to enhanced save_case_data method.
        """
        return await self.save_case_data(case_data, iso_date, html_content)
    
    async def upload_to_s3(self, file_path: str, s3_key: str) -> str:
        """Upload file to S3 and return URL."""
        try:
            if not self.s3_enabled:
                self.log_warning("S3 upload requested but S3 is not enabled")
                return ""
            
            if not self.s3_service:
                self.log_error("S3 service not available for upload")
                raise ValueError("S3 service not configured")
            
            # Use S3 service to upload
            s3_url = await self.s3_service.upload_file(file_path, s3_key)
            
            self.log_info(f"File uploaded to S3: {s3_url}")
            return s3_url
            
        except Exception as e:
            self.log_error(f"Error uploading to S3: {str(e)}")
            raise
    
    async def export_to_csv(self, data: List[Dict[str, Any]], output_path: str) -> str:
        """Export data to CSV format."""
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            if not data:
                self.log_warning("No data to export to CSV")
                # Create empty CSV with headers
                with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['No data available'])
                return str(output_file)
            
            # Get all unique keys from all records
            all_keys = set()
            for record in data:
                all_keys.update(record.keys())
            
            fieldnames = sorted(list(all_keys))
            
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for record in data:
                    # Flatten nested dictionaries and handle special types
                    flat_record = self._flatten_record(record)
                    writer.writerow(flat_record)
            
            self.log_info(f"Data exported to CSV: {output_file}")
            return str(output_file)
            
        except Exception as e:
            self.log_error(f"Error exporting to CSV: {str(e)}")
            raise
    
    async def generate_report(self, report_type: str, data: List[Dict[str, Any]]) -> str:
        """Generate formatted report."""
        try:
            # Ensure service is initialized
            if not self._initialized:
                await self.initialize()
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_filename = f"{report_type}_report_{timestamp}.json"
            
            reports_dir = self.base_directory / 'reports'
            reports_dir.mkdir(parents=True, exist_ok=True)
            report_path = reports_dir / report_filename
            
            # Generate report metadata
            report = {
                'report_type': report_type,
                'generated_at': datetime.now().isoformat(),
                'record_count': len(data),
                'data': data
            }
            
            # Add report-specific metadata
            if report_type == 'processing_summary':
                report['summary'] = self._generate_processing_summary(data)
            elif report_type == 'error_analysis':
                report['error_analysis'] = self._generate_error_analysis(data)
            
            # Save report
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            self.log_info(f"Report generated: {report_path}")
            return str(report_path)
            
        except Exception as e:
            self.log_error(f"Error generating report: {str(e)}")
            raise
    
    # Private helper methods
    def _clean_filename_component(self, component: str) -> str:
        """Clean a filename component to be filesystem-safe."""
        if not component:
            return "unknown"
        
        # Remove or replace problematic characters
        clean = re.sub(r'[<>:"/\\|?*]', '_', str(component))
        clean = re.sub(r'[^\w\-_.]', '_', clean)
        return clean[:50]  # Limit length
    
    def _flatten_record(self, record: Dict[str, Any]) -> Dict[str, str]:
        """Flatten nested dictionaries for CSV export."""
        flat = {}
        
        for key, value in record.items():
            if isinstance(value, dict):
                # Flatten nested dictionary
                for nested_key, nested_value in value.items():
                    flat_key = f"{key}.{nested_key}"
                    flat[flat_key] = str(nested_value)
            elif isinstance(value, list):
                # Convert list to string
                flat[key] = '; '.join(str(item) for item in value)
            else:
                flat[key] = str(value) if value is not None else ''
        
        return flat
    
    def _generate_processing_summary(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate processing summary statistics."""
        total_cases = len(data)
        successful_cases = len([d for d in data if d.get('status') == 'success'])
        failed_cases = total_cases - successful_cases
        
        return {
            'total_cases': total_cases,
            'successful_cases': successful_cases,
            'failed_cases': failed_cases,
            'success_rate': (successful_cases / total_cases * 100) if total_cases > 0 else 0
        }
    
    def _generate_error_analysis(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate error analysis from data."""
        errors = []
        error_counts = {}
        
        for record in data:
            if record.get('status') == 'error' or record.get('errors'):
                record_errors = record.get('errors', [])
                errors.extend(record_errors)
                
                for error in record_errors:
                    error_type = str(error).split(':')[0] if ':' in str(error) else str(error)
                    error_counts[error_type] = error_counts.get(error_type, 0) + 1
        
        return {
            'total_errors': len(errors),
            'unique_error_types': len(error_counts),
            'error_frequency': error_counts
        }
    
    async def _execute_action(self, data: Any) -> Any:
        """Execute file operations actions based on request data."""
        if not isinstance(data, dict):
            raise ValueError("FileOperationsService requires dict input")
        
        action = data.get("action")
        
        if action == "save_case_data":
            case_data = data.get("case_data", {})
            iso_date = data.get("iso_date")
            if not iso_date:
                raise ValueError("iso_date required for save_case_data action")
            return await self.save_case_data(
                case_data, 
                iso_date, 
                html_content=data.get("html_content"),
                s3_metadata=data.get("s3_metadata"),
                court_logger=data.get("court_logger")  # Pass through court_logger
            )
            
        elif action == "save_pdf":
            pdf_content = data.get("pdf_content")
            case_data = data.get("case_data", {})
            iso_date = data.get("iso_date")
            if not iso_date or not pdf_content:
                raise ValueError("iso_date and pdf_content required for save_pdf action")
            return await self.save_pdf(
                pdf_content,
                case_data,
                iso_date,
                document_info=data.get("document_info")
            )
            
        elif action == "setup_directories":
            iso_date = data.get("iso_date")
            if not iso_date:
                raise ValueError("iso_date required for setup_directories action")
            await self.setup_directories(iso_date)
            return {"status": "success", "message": f"Directories setup for {iso_date}"}
            
        elif action == "export_to_csv":
            data_list = data.get("data", [])
            filename = data.get("filename", "export.csv")
            iso_date = data.get("iso_date")
            if not iso_date:
                raise ValueError("iso_date required for export_to_csv action")
            return await self.export_to_csv(data_list, filename, iso_date)
            
        elif action == "upload_to_s3":
            local_path = data.get("local_path")
            s3_key = data.get("s3_key")
            if not local_path or not s3_key:
                raise ValueError("local_path and s3_key required for upload_to_s3 action")
            return await self.upload_to_s3(local_path, s3_key)
            
        elif action == "health_check":
            return await self.health_check()
            
        else:
            raise ValueError(f"Unknown action for FileOperationsService: {action}")

    async def health_check(self) -> Dict[str, Any]:
        """Return service health status."""
        return {
            'service': 'FileOperationsService',
            'status': 'healthy' if self._initialized else 'not_initialized',
            'base_directory': str(self.base_directory) if self.base_directory else None,
            'base_directory_exists': self.base_directory.exists() if self.base_directory else False,
            's3_enabled': self.s3_enabled,
            'has_s3_service': self.s3_service is not None,
            'has_data_merger': self.data_merger is not None,
            'has_filename_validator': self.filename_validator is not None,
            'save_statistics': self._save_statistics
        }

    # Enhanced data merging and validation methods
    
    async def _merge_all_data(
        self, 
        case_data: Dict[str, Any], 
        html_content: Optional[str], 
        s3_metadata: Optional[Dict[str, Any]],
        log_prefix: str
    ) -> Dict[str, Any]:
        """Merge all available data into a comprehensive case record."""
        try:
            if self.data_merger:
                merged_data = await self.data_merger._execute_action({
                    'action': 'create_merged_record',
                    'case_data': case_data,
                    'html_content': html_content,
                    's3_metadata': s3_metadata or {},
                    'processing_metadata': {
                        'merged_by': 'FileOperationsService',
                        'merge_timestamp': datetime.now().isoformat()
                    }
                })
                self._save_statistics['merged_saves'] += 1
                return merged_data
            else:
                self.log_warning(f"{log_prefix} Data merger not available, using case data as-is")
                return case_data.copy()
                
        except Exception as e:
            self.log_error(f"{log_prefix} Data merge failed, using original case data: {e}")
            return case_data.copy()

    def _enrich_case_data(self, case_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enrich case data with fields per Section 3.1 of the implementation plan.
        Adds fields like assigned_to, plaintiffs, defendants if not already present.
        """
        enriched_data = case_data.copy()
        
        # Add assigned_to field (from judge_name if available)
        if 'assigned_to' not in enriched_data and 'judge_name' in enriched_data:
            enriched_data['assigned_to'] = enriched_data['judge_name']
        elif 'assigned_to' not in enriched_data:
            enriched_data['assigned_to'] = ''
        
        # Ensure plaintiffs field exists (as list)
        if 'plaintiffs' not in enriched_data:
            enriched_data['plaintiffs'] = []
        elif not isinstance(enriched_data['plaintiffs'], list):
            enriched_data['plaintiffs'] = [enriched_data['plaintiffs']] if enriched_data['plaintiffs'] else []
        
        # Ensure defendants field exists (as list)
        if 'defendants' not in enriched_data:
            enriched_data['defendants'] = []
        elif not isinstance(enriched_data['defendants'], list):
            enriched_data['defendants'] = [enriched_data['defendants']] if enriched_data['defendants'] else []
        
        # Ensure plaintiff_attorneys field exists (as list)
        if 'plaintiff_attorneys' not in enriched_data:
            enriched_data['plaintiff_attorneys'] = []
        elif not isinstance(enriched_data['plaintiff_attorneys'], list):
            enriched_data['plaintiff_attorneys'] = [enriched_data['plaintiff_attorneys']] if enriched_data['plaintiff_attorneys'] else []
        
        # Ensure defendant_attorneys field exists (as list)
        if 'defendant_attorneys' not in enriched_data:
            enriched_data['defendant_attorneys'] = []
        elif not isinstance(enriched_data['defendant_attorneys'], list):
            enriched_data['defendant_attorneys'] = [enriched_data['defendant_attorneys']] if enriched_data['defendant_attorneys'] else []
        
        # Add other legacy fields with defaults if missing
        legacy_fields = {
            'nature_of_suit': '',
            'cause': '',
            'jury_demand': '',
            'is_mdl': False,
            'lead_case_id': '',
            'case_status': '',
            'filing_date': '',
            'date_terminated': '',
            'case_title': '',
            'case_name': ''
        }
        
        for field, default_value in legacy_fields.items():
            if field not in enriched_data:
                enriched_data[field] = default_value
        
        return enriched_data

    async def _validate_filename(self, case_data: Dict[str, Any], log_prefix: str) -> str:
        """Validate and generate filename using create_base_filename logic."""
        try:
            if self.filename_validator:
                validated_filename = await self.filename_validator._execute_action({
                    'action': 'generate_validated_filename',
                    'case_data': case_data
                })
                self._save_statistics['filename_validations'] += 1
                
                # Check if filename was corrected
                if case_data.get('base_filename'):
                    expected = case_data['base_filename'] + '.json'
                    if expected != validated_filename:
                        self._save_statistics['filename_corrections'] += 1
                
                return validated_filename
            else:
                self.log_warning(f"{log_prefix} Filename validator not available, using fallback")
                return self._fallback_filename_generation(case_data)
                
        except Exception as e:
            self.log_error(f"{log_prefix} Filename validation failed, using fallback: {e}")
            return self._fallback_filename_generation(case_data)

    def _fallback_filename_generation(self, case_data: Dict[str, Any]) -> str:
        """Fallback filename generation when validator is unavailable."""
        base_filename = case_data.get('base_filename')
        if base_filename:
            if not base_filename.endswith('.json'):
                return base_filename + '.json'
            return base_filename
        
        # Generate basic filename
        court_id = case_data.get('court_id', 'unknown')
        docket_num = case_data.get('docket_num', 'unknown')
        return f"{court_id}_{docket_num}.json".replace(':', '_').replace('/', '_')

    async def _prepare_final_save_data(
        self, 
        merged_data: Dict[str, Any], 
        filename: str, 
        log_prefix: str
    ) -> Dict[str, Any]:
        """Prepare final data structure for JSON save."""
        final_data = merged_data.copy()
        
        # CRITICAL: Clean data for schema conformance BEFORE adding metadata
        final_data = self._clean_case_data_for_save(final_data)
        
        # Add save metadata
        final_data['_saved_timestamp'] = datetime.now().isoformat()
        final_data['_saved_by'] = 'EnhancedFileOperationsService'
        final_data['_json_filename'] = filename
        final_data['_save_version'] = '2.0'  # Enhanced version
        
        return final_data

    async def _save_to_file(
        self, 
        data: Dict[str, Any], 
        iso_date: str, 
        filename: str, 
        log_prefix: str,
        logger: Optional[Any] = None
    ) -> Path:
        """
        Save data to JSON file using legacy path structure.
        Per Section 3.2: data/{iso_date}/dockets/{base_filename}.json
        """
        # Save directly to dockets directory (not court-specific subdirectory)
        dockets_dir = self.base_directory / iso_date / 'dockets'
        file_path = dockets_dir / filename

        # Add final path to data
        data['json_path'] = str(file_path)

        # Save data as JSON
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)

        return file_path

    def _clean_case_data_for_save(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Clean case data to match GOOD EXAMPLE schema exactly.
        
        Per pacer_prompt.md Requirement 3:
        - Remove _html_content field (MUST BE DELETED)
        - Remove s3_upload_checkpoints, s3_upload, processing_metadata, _processing_phase
        - Add default empty string values for title and allegations if missing
        - Preserve _saved_timestamp and _saved_by (they ARE in final schema)
        
        Args:
            data: Raw case data dictionary
            
        Returns:
            Cleaned data dictionary conforming to GOOD EXAMPLE schema
        """
        cleaned_data = data.copy()
        
        # STEP 1: Remove unwanted fields that should NOT be in final JSON
        fields_to_remove = [
            # CRITICAL: These MUST BE DELETED per pacer_prompt.md
            '_html_content',           # MUST BE DELETED - verbose HTML content
            's3_upload_checkpoints',   # Internal metadata - not for final schema
            's3_upload',              # Internal metadata - not for final schema  
            'processing_metadata',     # Internal metadata - not for final schema
            '_processing_phase',       # Internal metadata - not for final schema
            
            # Additional internal fields to remove
            'html_content',           # Alternative field name for HTML content
            'workflow_metadata',      # Internal workflow tracking
            'checkpoint_metadata',    # Internal checkpoint tracking
            'upload_metadata',        # Internal upload tracking
            '_workflow_steps',        # Internal step tracking
            '_checkpoint_status',     # Internal checkpoint status
            '_upload_status',         # Internal upload status
            '_processing_source',     # Internal source tracking
            '_workflow_type',         # Internal workflow type
            '_merge_metadata',        # Internal merge tracking
            'merge_timestamp',        # Internal merge timing
            'merged_by',              # Internal merge source
            '_internal_status',       # Any internal status fields
            '_debug_info',            # Debug information
            '_temp_data',             # Temporary data fields
        ]
        
        removed_fields = []
        for field in fields_to_remove:
            if field in cleaned_data:
                cleaned_data.pop(field, None)
                removed_fields.append(field)
        
        # Log removed fields for transparency (if logger is available)
        if removed_fields and hasattr(self, 'logger') and self.logger:
            self.logger.debug(f"JSON cleanup: Removed internal metadata fields: {removed_fields}")
        
        # STEP 2: Add default fields if missing (per pacer_prompt.md Requirement 3)
        if 'title' not in cleaned_data or cleaned_data.get('title') is None:
            cleaned_data['title'] = ''
            
        if 'allegations' not in cleaned_data or cleaned_data.get('allegations') is None:
            cleaned_data['allegations'] = ''
        
        # STEP 3: Preserve required underscore-prefixed fields (these ARE in final schema)
        # Note: _saved_timestamp and _saved_by are preserved as they are required in GOOD EXAMPLE
        # These are added later in _prepare_final_save_data method
        
        return cleaned_data

    async def get_save_statistics(self) -> Dict[str, Any]:
        """Get enhanced save statistics."""
        stats = self._save_statistics.copy()
        
        # Add merger statistics if available
        if self.data_merger:
            try:
                merger_stats = await self.data_merger._execute_action({'action': 'get_merge_statistics'})
                stats['merger_statistics'] = merger_stats
            except:
                pass
        
        # Add filename validator statistics if available
        if self.filename_validator:
            try:
                validator_stats = await self.filename_validator._execute_action({'action': 'get_filename_statistics'})
                stats['validator_statistics'] = validator_stats
            except:
                pass
        
        return stats