"""
S3 Management Service

Handles AWS S3 operations including upload, download, and bucket management.
Provides secure and efficient file storage in the cloud.
"""

import os
import asyncio
from typing import Any, Dict, List, Optional
from pathlib import Path

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.storage.s3_async import S3AsyncStorage


class S3Service(AsyncServiceBase):
    """Service for managing AWS S3 operations."""
    
    def __init__(self, logger=None, config=None, config_service=None):
        super().__init__(logger, config)
        self.config_service = config_service
        self.s3_client = None
        self.bucket_name = None
        self.aws_access_key_id = None
        self.aws_secret_access_key = None
        self.aws_region = None
        self.enabled = False
        
    async def _initialize_service(self) -> None:
        """Initialize the S3 management service."""
        # Load configuration from config service, config dict, or use defaults
        if self.config_service:
            s3_config = await self.config_service.get_config_value('s3', 'settings', {})
            self.bucket_name = s3_config.get('bucket_name')
            self.aws_region = s3_config.get('region', 'us-east-1')
            self.enabled = s3_config.get('enabled', False)
        elif self.config and isinstance(self.config, dict):
            # CRITICAL FIX: Read from config dictionary passed from MainServiceFactory
            self.bucket_name = self.config.get('bucket_name') or os.environ.get('S3_BUCKET_NAME', 'lexgenius-dockets')
            self.aws_region = self.config.get('aws_region') or os.environ.get('AWS_REGION', 'us-west-2')
            # Default to enabled if config is provided with bucket name
            self.enabled = self.config.get('enabled', True) if self.bucket_name else False
        else:
            # Fallback to environment variables and basic config
            self.bucket_name = os.environ.get('S3_BUCKET_NAME', 'lexgenius-dockets')
            self.aws_region = os.environ.get('AWS_REGION', 'us-west-2')
            self.enabled = os.environ.get('S3_ENABLED', 'true').lower() == 'true'

        # Get credentials from config dict first, then environment
        if self.config and isinstance(self.config, dict):
            self.aws_access_key_id = self.config.get('aws_access_key') or os.environ.get('AWS_ACCESS_KEY_ID')
            self.aws_secret_access_key = self.config.get('aws_secret_key') or os.environ.get('AWS_SECRET_ACCESS_KEY')
        else:
            self.aws_access_key_id = os.environ.get('AWS_ACCESS_KEY_ID')
            self.aws_secret_access_key = os.environ.get('AWS_SECRET_ACCESS_KEY')
        
        # Debug logging for configuration validation
        self.log_info(f"🔧 S3Service Configuration:")
        self.log_info(f"  - enabled: {self.enabled}")
        self.log_info(f"  - bucket_name: {self.bucket_name}")
        self.log_info(f"  - aws_region: {self.aws_region}")
        self.log_info(f"  - has_access_key: {bool(self.aws_access_key_id)}")
        self.log_info(f"  - has_secret_key: {bool(self.aws_secret_access_key)}")

        if self.enabled and self.bucket_name and self.aws_access_key_id:
            try:
                # Try to import boto3
                import boto3
                self.s3_client = boto3.client(
                    's3',
                    aws_access_key_id=self.aws_access_key_id,
                    aws_secret_access_key=self.aws_secret_access_key,
                    region_name=self.aws_region
                )

                # Test connection
                await self._test_connection()
                self.log_info("✅ S3Service initialized successfully")

            except ImportError:
                self.log_warning("❌ boto3 not available - S3 functionality disabled")
                self.enabled = False
            except Exception as e:
                self.log_error(f"❌ Failed to initialize S3 client: {str(e)}")
                self.enabled = False
        else:
            missing_items = []
            if not self.enabled:
                missing_items.append("enabled=False")
            if not self.bucket_name:
                missing_items.append("bucket_name")
            if not self.aws_access_key_id:
                missing_items.append("aws_access_key_id")
            self.log_warning(f"❌ S3Service initialized but disabled (missing: {', '.join(missing_items)})")
    
    async def _execute_action(self, data: Any) -> Any:
        """Execute S3 service actions."""
        action = data.get('action')
        court_logger = data.get('court_logger', self.logger)
        
        if action == 'upload_html':
            # Handle HTML upload action
            base_filename = data.get('base_filename')
            html_content = data.get('html_content')
            iso_date = data.get('iso_date')
            
            if not base_filename or not html_content:
                if court_logger:
                    court_logger.error("Missing required parameters for HTML upload")
                return {'success': False, 'error': 'Missing required parameters'}
                
            # Create S3 key with correct format: {iso_date}/html/{base_filename}.html
            s3_key = f"{iso_date}/html/{base_filename}.html"
            
            # Check if S3 is enabled and configured
            if not self.enabled or not self.s3_client:
                if court_logger:
                    court_logger.warning(f"S3 service not available - HTML upload skipped for {s3_key}")
                return {
                    'success': False, 
                    'error': 'S3 service not enabled or configured',
                    's3_key': s3_key,
                    's3_html': None,
                    's3_url': None
                }
            
            # Upload HTML content directly to S3 using upload_content
            try:
                if court_logger:
                    court_logger.info(f"Uploading HTML content to S3: {s3_key}")
                
                # Upload HTML content directly without temporary file
                # Need to use S3AsyncStorage for upload_content method
                s3_storage = S3AsyncStorage(
                    logger=self.logger,
                    bucket_name=self.bucket_name,
                    aws_access_key=self.aws_access_key_id,
                    aws_secret_key=self.aws_secret_access_key,
                    aws_region=self.aws_region
                )
                
                success = await s3_storage.upload_content(
                    content=html_content,
                    object_key=s3_key,
                    content_type='text/html',
                    overwrite=True
                )
                
                if not success:
                    raise Exception("Failed to upload HTML content to S3")
                
                # Generate URLs
                s3_url = f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{s3_key}"
                s3_html = f"https://cdn.lexgenius.ai/{s3_key}"
                
                if court_logger:
                    court_logger.info(f"S3 upload successful: {s3_html}")
                
                return {
                    'success': True, 
                    's3_key': s3_key,
                    's3_html': s3_html,
                    's3_url': s3_url
                }
            except Exception as e:
                if court_logger:
                    court_logger.error(f"S3 upload failed: {str(e)}")
                return {'success': False, 'error': str(e)}
        
        else:
            raise ValueError(f"Unknown S3Service action: {action}")
    
    async def upload_file(self, file_path: str, object_key: str, 
                         content_type: Optional[str] = None, 
                         force_upload: bool = False) -> str:
        """Upload a file to S3 with retry logic and return the S3 URL."""
        if not self.enabled or not self.s3_client:
            raise ValueError("S3 service is not enabled or properly configured")
        
        local_path = Path(file_path)
        if not local_path.exists():
            raise FileNotFoundError(f"Local file not found: {file_path}")
        
        # Retry configuration
        max_retries = 3
        base_delay = 1.0
        
        for attempt in range(max_retries + 1):
            try:
                # Prepare upload arguments
                upload_args = {
                    'Bucket': self.bucket_name,
                    'Key': object_key,
                    'Filename': str(local_path)
                }
                
                if content_type:
                    upload_args['ExtraArgs'] = {'ContentType': content_type}
                
                # Upload file
                self.s3_client.upload_file(**upload_args)
                
                # Generate S3 URL
                s3_url = f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{object_key}"
                
                self.log_info(f"File uploaded to S3: {s3_url} (attempt {attempt + 1})")
                return s3_url
                
            except Exception as e:
                self.log_warning(f"Upload attempt {attempt + 1} failed: {str(e)}")
                
                if attempt == max_retries:
                    self.log_error(f"All {max_retries + 1} upload attempts failed")
                    raise
                
                # Exponential backoff with jitter
                delay = base_delay * (2 ** attempt) + (0.1 * attempt)
                self.log_info(f"Retrying upload in {delay:.1f} seconds...")
                await asyncio.sleep(delay)
    
    async def download_file(self, s3_key: str, local_file_path: str) -> bool:
        """Download a file from S3 to local path with retry logic."""
        if not self.enabled or not self.s3_client:
            raise ValueError("S3 service is not enabled or properly configured")
        
        local_path = Path(local_file_path)
        local_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Retry configuration
        max_retries = 3
        base_delay = 1.0
        
        for attempt in range(max_retries + 1):
            try:
                # Download file
                self.s3_client.download_file(
                    Bucket=self.bucket_name,
                    Key=s3_key,
                    Filename=str(local_path)
                )
                
                self.log_info(f"File downloaded from S3: {s3_key} -> {local_file_path} (attempt {attempt + 1})")
                return True
                
            except Exception as e:
                self.log_warning(f"Download attempt {attempt + 1} failed: {str(e)}")
                
                if attempt == max_retries:
                    self.log_error(f"All {max_retries + 1} download attempts failed")
                    return False
                
                # Exponential backoff with jitter
                delay = base_delay * (2 ** attempt) + (0.1 * attempt)
                self.log_info(f"Retrying download in {delay:.1f} seconds...")
                await asyncio.sleep(delay)
    
    async def file_exists(self, s3_key: str) -> bool:
        """Check if a file exists in S3 with retry logic."""
        if not self.enabled or not self.s3_client:
            return False
        
        # Retry configuration
        max_retries = 2
        base_delay = 0.5
        
        for attempt in range(max_retries + 1):
            try:
                self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
                return True
            except Exception as e:
                # Don't log 404 errors as they're expected
                if "404" not in str(e) and "NoSuchKey" not in str(e):
                    self.log_debug(f"File existence check attempt {attempt + 1} failed: {str(e)}")
                
                if attempt == max_retries:
                    return False
                
                # Short exponential backoff
                delay = base_delay * (2 ** attempt)
                await asyncio.sleep(delay)
        
        return False
    
    async def delete_file(self, s3_key: str) -> bool:
        """Delete a file from S3 with retry logic."""
        if not self.enabled or not self.s3_client:
            raise ValueError("S3 service is not enabled or properly configured")
        
        # Retry configuration
        max_retries = 2
        base_delay = 0.5
        
        for attempt in range(max_retries + 1):
            try:
                self.s3_client.delete_object(Bucket=self.bucket_name, Key=s3_key)
                self.log_info(f"File deleted from S3: {s3_key} (attempt {attempt + 1})")
                return True
                
            except Exception as e:
                self.log_warning(f"Delete attempt {attempt + 1} failed: {str(e)}")
                
                if attempt == max_retries:
                    self.log_error(f"All {max_retries + 1} delete attempts failed")
                    return False
                
                # Short exponential backoff
                delay = base_delay * (2 ** attempt)
                await asyncio.sleep(delay)
    
    async def list_files(self, prefix: str = "", max_keys: int = 1000) -> List[Dict[str, Any]]:
        """List files in S3 with optional prefix filter."""
        if not self.enabled or not self.s3_client:
            return []
        
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix,
                MaxKeys=max_keys
            )
            
            files = []
            for obj in response.get('Contents', []):
                files.append({
                    'key': obj['Key'],
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'].isoformat(),
                    'etag': obj['ETag'].strip('"')
                })
            
            return files
            
        except Exception as e:
            self.log_error(f"Error listing S3 files: {str(e)}")
            return []
    
    async def get_file_metadata(self, s3_key: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a file in S3."""
        if not self.enabled or not self.s3_client:
            return None
        
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
            
            return {
                'key': s3_key,
                'size': response.get('ContentLength'),
                'last_modified': response.get('LastModified').isoformat() if response.get('LastModified') else None,
                'content_type': response.get('ContentType'),
                'metadata': response.get('Metadata', {}),
                'etag': response.get('ETag', '').strip('"')
            }
            
        except Exception as e:
            self.log_error(f"Error getting S3 file metadata: {str(e)}")
            return None
    
    async def generate_presigned_url(self, s3_key: str, expiration: int = 3600) -> Optional[str]:
        """Generate a presigned URL for temporary access to a file."""
        if not self.enabled or not self.s3_client:
            return None
        
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': s3_key},
                ExpiresIn=expiration
            )
            
            self.log_info(f"Generated presigned URL for {s3_key} (expires in {expiration}s)")
            return url
            
        except Exception as e:
            self.log_error(f"Error generating presigned URL: {str(e)}")
            return None
    
    async def get_bucket_info(self) -> Dict[str, Any]:
        """Get information about the configured S3 bucket."""
        if not self.enabled or not self.s3_client:
            return {'enabled': False}
        
        try:
            # Get bucket location
            location = self.s3_client.get_bucket_location(Bucket=self.bucket_name)
            
            # Count objects (sample)
            objects_response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                MaxKeys=1000
            )
            
            return {
                'enabled': True,
                'bucket_name': self.bucket_name,
                'region': location.get('LocationConstraint', 'us-east-1'),
                'sample_object_count': len(objects_response.get('Contents', [])),
                'is_truncated': objects_response.get('IsTruncated', False)
            }
            
        except Exception as e:
            self.log_error(f"Error getting bucket info: {str(e)}")
            return {
                'enabled': True,
                'bucket_name': self.bucket_name,
                'error': str(e)
            }
    
    async def _test_connection(self) -> bool:
        """Test S3 connection by listing bucket contents."""
        try:
            # Run synchronous boto3 call in executor to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.s3_client.list_objects_v2(Bucket=self.bucket_name, MaxKeys=1)
            )
            self.log_info(f"✅ S3 connection test successful for bucket: {self.bucket_name}")
            return True
        except Exception as e:
            self.log_warning(f"❌ S3 connection test failed: {str(e)}")
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """Return service health status."""
        status = {
            'service': 'S3Service',
            'status': 'healthy' if self._initialized else 'not_initialized',
            'enabled': self.enabled,
            'bucket_name': self.bucket_name,
            'aws_region': self.aws_region,
            'has_credentials': bool(self.aws_access_key_id and self.aws_secret_access_key)
        }
        
        if self.enabled and self.s3_client:
            status['connection_test'] = await self._test_connection()
        
        return status