# /src/services/pacer/_core_services/relevance/relevance_service.py

"""
Relevance Service for PACER processing.

Consolidates RelevanceFacadeService and IgnoreDownloadFacadeService into a single
unified service that handles all relevance determination logic following the workflow
phases from docket_processing.md.
"""

from __future__ import annotations
import re
from typing import Any, Dict, List, Optional, Set, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol


class RelevanceService(AsyncServiceBase):
    """
    Consolidated relevance service for PACER processing.
    
    This service implements PHASE 2 of the docket processing lifecycle:
    Relevance & Classification - determine_case_relevance method that:
    1. Checks for MDL Flag (if yes, set relevant - MDL Flag Override)
    2. Checks ignore_download patterns (if yes, set not relevant - Ignore Download)
    3. Checks exclusion by statute (if yes, set not relevant - Excluded)
    4. Checks explicitly relevant patterns (if yes, set relevant - Explicit Match)
    5. Default to not relevant
    
    Combines functionality from RelevanceFacadeService and IgnoreDownloadFacadeService.
    """

    def __init__(self,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None,
                 configuration_service=None):
        """
        Initialize RelevanceService with logger.
        
        Args:
            logger: Logger instance passed from parent components
            config: Configuration dictionary
            configuration_service: Configuration service instance
        """
        # Use logger as the main logger - no standalone logging
        super().__init__(logger, config)
        
        # Configuration will be injected from ConfigurationService
        self._configuration_service = configuration_service
        
        # Cached configuration data
        self._relevance_config: Optional[Dict[str, Any]] = None
        self._ignore_download_config: Optional[List[Dict[str, Any]]] = None
        self._relevant_defendants_lower: List[str] = []
        self._courts_with_ignore_download: Set[str] = set()

    async def _initialize_service(self) -> None:
        """Initialize the relevance service."""
        # Get injected dependencies
        if not self._configuration_service:
            try:
                self._configuration_service = self.get_dependency('configuration_service')
            except Exception as e:
                # No standalone logging - rely on court logger from parent
                # Failed to get configuration service, will operate with basic config
                self._configuration_service = None
        
        # Load configurations only if we have a configuration service
        if self._configuration_service:
            await self._load_relevance_configs()
        else:
            # Initialize with empty configs as fallback
            self._relevance_config = {}
            self._ignore_download_config = []
            self._relevant_defendants_lower = []
            self._courts_with_ignore_download = set()
        
        # Only log if we have a proper logger (court logger)
        if self.logger:
            self.log_info("Relevance service initialized successfully")

    def _get_logger_context(self, court_id: str = "", docket_num: str = "") -> Dict[str, Any]:
        """Get logger context with consistent [court_id][docket_num] prefix format."""
        context = {"service": "RelevanceService"}
        if court_id or docket_num:
            context["case_prefix"] = f"[{court_id}][{docket_num}]"
        return context

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        court_id = data.get('court_id', '')
        docket_num = data.get('docket_num', '')
        court_logger = data.get('court_logger', self.logger)
        
        context = self._get_logger_context(court_id, docket_num)
        if court_logger:
            court_logger.debug(f"Executing relevance action: {action}", extra=context)
        
        if action == 'determine_case_relevance':
            return await self.determine_case_relevance(
                data['case_details'],
                court_id=court_id,
                docket_num=docket_num,
                court_logger=court_logger
            )
        elif action == 'is_relevant':
            # Legacy method name support
            return await self.determine_case_relevance(
                data['case_details'],
                court_id=court_id,
                docket_num=docket_num,
                court_logger=court_logger
            )
        elif action == 'should_ignore_download':
            return await self.should_ignore_download(
                data['case_details'],
                court_id=court_id,
                docket_num=docket_num,
                court_logger=court_logger
            )
        elif action == 'reload_configs':
            return await self._load_relevance_configs()
        else:
            raise PacerServiceError(f"Unknown action for RelevanceService: {action}")

    async def _load_relevance_configs(self) -> None:
        """Load relevance and ignore download configurations."""
        if self.logger:
            self.log_info("Loading relevance configurations")
        
        if not self._configuration_service:
            if self.logger:
                self.log_warning("Configuration service not available")
            return
        
        try:
            # Load relevance config with comprehensive type validation
            relevance_config_result = await self._configuration_service.perform_action({
                'action': 'get_relevance_config'
            })
            self._relevance_config = self._validate_and_convert_config_dict(
                relevance_config_result, 'get_relevance_config', {}
            )
            
            # Load ignore download config with comprehensive type validation
            ignore_config_data = await self._configuration_service.perform_action({
                'action': 'get_ignore_download_config'
            })
            self._ignore_download_config = self._validate_and_convert_ignore_config(
                ignore_config_data
            )
            
            # Load relevant defendants with comprehensive type validation
            relevant_defendants_result = await self._configuration_service.perform_action({
                'action': 'get_relevant_defendants'
            })
            self._relevant_defendants_lower = self._validate_and_convert_defendants_list(
                relevant_defendants_result
            )
            
            # Build courts with ignore download set with error handling
            self._courts_with_ignore_download = self._build_courts_ignore_set()
            
            if self.logger:
                self.log_info("Relevance configurations loaded successfully", {
                    "relevance_rules": len(self._relevance_config) if self._relevance_config else 0,
                    "ignore_rules": len(self._ignore_download_config) if self._ignore_download_config else 0,
                    "relevant_defendants": len(self._relevant_defendants_lower),
                    "courts_with_ignore": len(self._courts_with_ignore_download)
                })
            
        except Exception as e:
            if self.logger:
                self.log_error(f"Critical error loading relevance configurations: {str(e)}")
            # Initialize with safe defaults to prevent further errors
            self._relevance_config = {}
            self._ignore_download_config = []
            self._relevant_defendants_lower = []
            self._courts_with_ignore_download = set()
            if self.logger:
                self.log_warning("Initialized with empty configurations due to loading error")

    def _validate_and_convert_config_dict(self, data: Any, method_name: str, default: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and convert configuration data to dictionary with comprehensive type checking."""
        if data is None:
            if self.logger:
                self.log_warning(f"{method_name} returned None, using default: {default}")
            return default
        
        if isinstance(data, dict):
            return data
        
        if isinstance(data, str):
            if self.logger:
                self.log_warning(
                    f"{method_name} returned string instead of dict. "
                    f"This causes 'str object has no attribute get' errors. "
                    f"String value: {data[:100]}{'...' if len(data) > 100 else ''}"
                )
            # Try to parse as JSON if it looks like JSON
            import json
            try:
                if data.strip().startswith(('{', '[')):
                    parsed = json.loads(data)
                    if isinstance(parsed, dict):
                        if self.logger:
                            self.log_info(f"Successfully parsed {method_name} string as JSON dict")
                        return parsed
                    else:
                        if self.logger:
                            self.log_warning(f"{method_name} JSON parsing resulted in non-dict: {type(parsed)}")
            except json.JSONDecodeError as e:
                if self.logger:
                    self.log_warning(f"{method_name} failed to parse as JSON: {str(e)}")
            return default
        
        if isinstance(data, (list, tuple)):
            if self.logger:
                self.log_warning(
                    f"{method_name} returned {type(data).__name__} instead of dict. "
                    f"Converting to dict with 'entries' key to prevent attribute errors."
                )
            return {'entries': list(data)}
        
        if self.logger:
            self.log_warning(
                f"{method_name} returned unexpected type {type(data)}. "
                f"This can cause 'object has no attribute get' errors. Using default: {default}"
            )
        return default
    
    def _validate_and_convert_ignore_config(self, data: Any) -> List[Dict[str, Any]]:
        """Validate and convert ignore download configuration with comprehensive type checking."""
        if data is None:
            if self.logger:
                self.log_warning("get_ignore_download_config returned None, using empty list")
            return []
        
        if isinstance(data, list):
            # Validate each entry is a dict
            valid_entries = []
            for i, entry in enumerate(data):
                if isinstance(entry, dict):
                    valid_entries.append(entry)
                elif isinstance(entry, str):
                    if self.logger:
                        self.log_warning(f"Ignore config entry {i} is string instead of dict: {entry}")
                else:
                    if self.logger:
                        self.log_warning(f"Ignore config entry {i} has unexpected type {type(entry)}")
            return valid_entries
        
        if isinstance(data, dict):
            # Handle both new format {'ignore_list': [...]} and legacy dict format
            if 'ignore_list' in data:
                ignore_list = data['ignore_list']
                if isinstance(ignore_list, list):
                    return ignore_list
                else:
                    if self.logger:
                        self.log_warning(f"ignore_list value is not a list: {type(ignore_list)}")
                    return []
            elif 'entries' in data:
                entries = data['entries']
                if isinstance(entries, list):
                    return entries
                else:
                    if self.logger:
                        self.log_warning(f"entries value is not a list: {type(entries)}")
                    return []
            else:
                # Treat the dict itself as a single configuration entry
                if self.logger:
                    self.log_warning("get_ignore_download_config returned dict without ignore_list/entries, treating as single entry")
                return [data]
        
        if isinstance(data, str):
            if self.logger:
                self.log_warning(
                    f"get_ignore_download_config returned string instead of dict/list. "
                    f"This causes attribute access errors. String value: {data[:100]}{'...' if len(data) > 100 else ''}"
                )
            # Try to parse as JSON
            import json
            try:
                if data.strip().startswith(('{', '[')):
                    parsed = json.loads(data)
                    if isinstance(parsed, list):
                        if self.logger:
                            self.log_info("Successfully parsed ignore config string as JSON list")
                        return parsed
                    elif isinstance(parsed, dict):
                        if self.logger:
                            self.log_info("Successfully parsed ignore config string as JSON dict")
                        return [parsed]  # Wrap single dict in list
            except json.JSONDecodeError as e:
                if self.logger:
                    self.log_warning(f"Failed to parse ignore config as JSON: {str(e)}")
            return []
        
        if self.logger:
            self.log_warning(
                f"get_ignore_download_config returned unexpected type {type(data)}. "
                f"This can cause attribute access errors. Using empty list."
            )
        return []
    
    def _validate_and_convert_defendants_list(self, data: Any) -> List[str]:
        """Validate and convert relevant defendants data with comprehensive type checking."""
        if data is None:
            if self.logger:
                self.log_warning("get_relevant_defendants returned None, using empty list")
            return []
        
        if isinstance(data, list):
            # Convert all items to strings and filter out empty ones
            valid_defendants = []
            for i, item in enumerate(data):
                if isinstance(item, str) and item.strip():
                    valid_defendants.append(item.lower())
                elif item is not None:
                    str_item = str(item).strip()
                    if str_item:
                        valid_defendants.append(str_item.lower())
                        if self.logger:
                            self.log_warning(f"Converted defendants entry {i} from {type(item)} to string: {str_item}")
            return valid_defendants
        
        if isinstance(data, dict):
            # Handle dict format with 'defendants' key
            if 'defendants' in data:
                defendants_value = data['defendants']
                if isinstance(defendants_value, list):
                    return self._validate_and_convert_defendants_list(defendants_value)
                else:
                    if self.logger:
                        self.log_warning(f"defendants value is not a list: {type(defendants_value)}")
                    return []
            else:
                if self.logger:
                    self.log_warning("get_relevant_defendants returned dict without 'defendants' key, using empty list")
                return []
        
        if isinstance(data, str):
            if self.logger:
                self.log_warning(
                    f"get_relevant_defendants returned string instead of list. "
                    f"This causes iteration/attribute errors. String value: {data[:100]}{'...' if len(data) > 100 else ''}"
                )
            # Try to parse as JSON
            import json
            try:
                if data.strip().startswith(('[', '{')):
                    parsed = json.loads(data)
                    if isinstance(parsed, list):
                        if self.logger:
                            self.log_info("Successfully parsed defendants string as JSON list")
                        return self._validate_and_convert_defendants_list(parsed)
                    elif isinstance(parsed, dict):
                        if self.logger:
                            self.log_info("Successfully parsed defendants string as JSON dict")
                        return self._validate_and_convert_defendants_list(parsed)
            except json.JSONDecodeError as e:
                if self.logger:
                    self.log_warning(f"Failed to parse defendants as JSON: {str(e)}")
            
            # Try to treat as comma-separated string
            if ',' in data:
                defendants = [d.strip().lower() for d in data.split(',') if d.strip()]
                if defendants:
                    if self.logger:
                        self.log_info(f"Parsed defendants string as comma-separated list: {len(defendants)} items")
                    return defendants
            
            # Single defendant string
            if data.strip():
                if self.logger:
                    self.log_info("Treating defendants string as single defendant")
                return [data.strip().lower()]
            
            return []
        
        if self.logger:
            self.log_warning(
                f"get_relevant_defendants returned unexpected type {type(data)}. "
                f"This can cause iteration errors. Using empty list."
            )
        return []
    
    def _build_courts_ignore_set(self) -> Set[str]:
        """Build set of courts with ignore download rules with error handling."""
        courts = set()
        
        if not self._ignore_download_config:
            return courts
        
        for i, entry in enumerate(self._ignore_download_config):
            try:
                if not isinstance(entry, dict):
                    if self.logger:
                        self.log_warning(f"Ignore config entry {i} is not a dict: {type(entry)}")
                    continue
                
                court_id = entry.get('court_id')
                if court_id and isinstance(court_id, str):
                    courts.add(court_id)
                elif court_id is not None:
                    # Convert to string if not None
                    courts.add(str(court_id))
                    if self.logger:
                        self.log_warning(f"Converted court_id from {type(court_id)} to string: {court_id}")
            except Exception as e:
                if self.logger:
                    self.log_warning(f"Error processing ignore config entry {i}: {str(e)}")
                continue
        
        return courts

    async def determine_case_relevance(self, 
                                     case_details: Dict[str, Any],
                                     court_id: str = "",
                                     docket_num: str = "",
                                     court_logger: Optional[Any] = None) -> Dict[str, Any]:
        """
        PHASE 2: Relevance & Classification
        
        Determines case relevance following the exact workflow from docket_processing.md:
        P2_2: Has MDL Flag? -> Yes: Set Relevant - MDL Flag Override
        P2_4: Matches ignore_download Pattern? -> Yes: Set Not Relevant - Ignore Download
        P2_6: Excluded by Statute? -> Yes: Set Not Relevant - Excluded  
        P2_8: Explicitly Relevant? -> Yes: Set Relevant - Explicit Match
        P2_10: Default: Set Not Relevant
        
        Args:
            case_details: Case details dictionary
            court_id: Court identifier for logging
            docket_num: Docket number for logging
            
        Returns:
            Dictionary with relevance determination results
        """
        logger = court_logger or self.logger
        context = self._get_logger_context(court_id, docket_num)
        if logger:
            logger.info("Starting PHASE 2: Relevance & Classification", extra=context)
        
        relevance_result = {
            'is_relevant': False,
            'relevance_reason': 'default_not_relevant',
            'mdl_override': False,
            'ignore_download_match': False,
            'statute_excluded': False,
            'explicit_match': False
        }
        
        # P2_2: Has MDL Flag?
        if case_details.get('mdl_num') or case_details.get('mdl_flag'):
            if self.logger:
                self.log_debug("P2_3: Set Relevant - MDL Flag Override", context)
            relevance_result.update({
                'is_relevant': True,
                'relevance_reason': 'mdl_flag_override',
                'mdl_override': True
            })
            return relevance_result
        
        # P2_4: Matches ignore_download Pattern?
        if await self.should_ignore_download(case_details, court_id, docket_num):
            if self.logger:
                self.log_debug("P2_5: Set Not Relevant - Ignore Download", context)
            relevance_result.update({
                'is_relevant': False,
                'relevance_reason': 'ignore_download_match',
                'ignore_download_match': True
            })
            return relevance_result
        
        # P2_6: Excluded by Statute?
        if await self._is_excluded_by_statute(case_details, court_id, docket_num):
            if self.logger:
                self.log_debug("P2_7: Set Not Relevant - Excluded", context)
            relevance_result.update({
                'is_relevant': False,
                'relevance_reason': 'statute_excluded',
                'statute_excluded': True
            })
            return relevance_result
        
        # P2_8: Explicitly Relevant?
        if await self._is_explicitly_relevant(case_details, court_id, docket_num):
            if self.logger:
                self.log_debug("P2_9: Set Relevant - Explicit Match", context)
            relevance_result.update({
                'is_relevant': True,
                'relevance_reason': 'explicit_match',
                'explicit_match': True
            })
            return relevance_result
        
        # P2_10: Set Not Relevant - Default
        if self.logger:
            self.log_debug("P2_10: Set Not Relevant - Default", context)
            self.log_info("PHASE 2: Relevance & Classification completed", context)
        
        return relevance_result

    async def should_ignore_download(self, 
                                   case_details: Dict[str, Any],
                                   court_id: str = "",
                                   docket_num: str = "",
                                   court_logger: Optional[Any] = None) -> bool:
        """
        Check if a case should be ignored for download based on ignore_download patterns.
        
        Args:
            case_details: Case details dictionary
            court_id: Court identifier for logging  
            docket_num: Docket number for logging
            
        Returns:
            True if case should be ignored for download
        """
        logger = court_logger or self.logger
        context = self._get_logger_context(court_id, docket_num)
        
        if not self._ignore_download_config:
            return False

        case_court_id = case_details.get('court_id', court_id)
        if case_court_id not in self._courts_with_ignore_download:
            return False

        # Check ignore download patterns for this court
        for entry in self._ignore_download_config:
            if entry.get('court_id') != case_court_id:
                continue
            
            # Check various ignore patterns
            if self._matches_ignore_pattern(entry, case_details):
                if self.logger:
                    self.log_info(f"Case matches ignore_download pattern", context)
                return True
                
        return False

    def _matches_ignore_pattern(self, ignore_entry: Dict[str, Any], case_details: Dict[str, Any]) -> bool:
        """Check if case details match an ignore download pattern."""
        # Check defendant patterns
        defendant_patterns = ignore_entry.get('defendant_patterns', [])
        case_defendants = case_details.get('defendant', [])
        
        # Handle None and ensure it's a list
        if case_defendants is None:
            case_defendants = []
        elif isinstance(case_defendants, str):
            case_defendants = [case_defendants]
        
        # Handle both string and dict formats for defendants with type validation
        defendant_strings = []
        for d in case_defendants:
            if isinstance(d, dict):
                defendant_strings.append(d.get('name', ''))
            elif isinstance(d, str):
                defendant_strings.append(d)
            elif d is not None:
                defendant_strings.append(str(d))
        defendant_text = ' '.join(defendant_strings).lower()
        
        for pattern in defendant_patterns:
            if isinstance(pattern, str) and pattern.lower() in defendant_text:
                return True
            elif isinstance(pattern, dict) and pattern.get('regex'):
                try:
                    if re.search(pattern['regex'], defendant_text, re.IGNORECASE):
                        return True
                except re.error:
                    if self.logger:
                        self.log_warning(f"Invalid regex pattern: {pattern['regex']}")
        
        # Check cause patterns
        cause_patterns = ignore_entry.get('cause_patterns', [])
        case_cause_raw = case_details.get('cause', '')
        
        # Handle different cause formats
        if isinstance(case_cause_raw, str):
            case_cause = case_cause_raw.lower()
        elif isinstance(case_cause_raw, dict):
            case_cause = ' '.join(str(v) for v in case_cause_raw.values() if v).lower()
        else:
            case_cause = str(case_cause_raw).lower()
        
        for pattern in cause_patterns:
            if isinstance(pattern, str) and pattern.lower() in case_cause:
                return True
            elif isinstance(pattern, dict) and pattern.get('regex'):
                try:
                    if re.search(pattern['regex'], case_cause, re.IGNORECASE):
                        return True
                except re.error:
                    if self.logger:
                        self.log_warning(f"Invalid regex pattern: {pattern['regex']}")
        
        # Check nature of suit patterns
        nos_patterns = ignore_entry.get('nos_patterns', [])
        case_nos_raw = case_details.get('nos', '')
        
        # Handle different nos formats
        if isinstance(case_nos_raw, str):
            case_nos = case_nos_raw.lower()
        elif isinstance(case_nos_raw, dict):
            case_nos = ' '.join(str(v) for v in case_nos_raw.values() if v).lower()
        else:
            case_nos = str(case_nos_raw).lower()
        
        for pattern in nos_patterns:
            if isinstance(pattern, str) and pattern.lower() in case_nos:
                return True
        
        return False

    async def _is_excluded_by_statute(self, 
                                    case_details: Dict[str, Any],
                                    court_id: str = "",
                                    docket_num: str = "") -> bool:
        """Check if case is excluded by statute."""
        if not self._relevance_config:
            return False
        
        context = self._get_logger_context(court_id, docket_num)
        
        # Check excluded cause statutes
        excluded_cause_statutes = self._relevance_config.get('excluded_cause_statutes', [])
        case_cause_raw = case_details.get('cause', '')
        
        # Handle different cause formats
        if isinstance(case_cause_raw, str):
            case_cause = case_cause_raw.lower()
        elif isinstance(case_cause_raw, dict):
            case_cause = ' '.join(str(v) for v in case_cause_raw.values() if v).lower()
        else:
            case_cause = str(case_cause_raw).lower()
        
        for statute in excluded_cause_statutes:
            if statute.lower() in case_cause:
                if self.logger:
                    self.log_debug(f"Case excluded by cause statute: {statute}", context)
                return True
        
        # Check irrelevant NOS codes
        irrelevant_nos = self._relevance_config.get('irrelevant_nos', [])
        case_nos = case_details.get('nos', '')
        
        if case_nos in irrelevant_nos:
            if self.logger:
                self.log_debug(f"Case excluded by irrelevant NOS: {case_nos}", context)
            return True
        
        # Check irrelevant defendant keywords
        irrelevant_defendant_keywords = self._relevance_config.get('irrelevant_defendant_keywords', [])
        defendants = case_details.get('defendant', [])
        
        # Handle None and ensure it's a list
        if defendants is None:
            defendants = []
        elif isinstance(defendants, str):
            defendants = [defendants]

        # Handle both string and dict formats for defendants with type validation
        defendant_strings = []
        for d in defendants:
            if isinstance(d, dict):
                defendant_strings.append(d.get('name', ''))
            elif isinstance(d, str):
                defendant_strings.append(d)
            elif d is not None:
                defendant_strings.append(str(d))
        defendant_text = ' '.join(defendant_strings).lower()
        
        for keyword in irrelevant_defendant_keywords:
            if keyword.lower() in defendant_text:
                if self.logger:
                    self.log_debug(f"Case excluded by irrelevant defendant keyword: {keyword}", context)
                return True
        
        # Check irrelevant cause keywords
        irrelevant_cause_keywords = self._relevance_config.get('irrelevant_cause_keywords', [])
        
        for keyword in irrelevant_cause_keywords:
            if keyword.lower() in case_cause:
                if self.logger:
                    self.log_debug(f"Case excluded by irrelevant cause keyword: {keyword}", context)
                return True
        
        # Check USA defendant regex (exclude government cases)
        usa_defendant_regex = self._relevance_config.get('usa_defendant_regex', '')
        if usa_defendant_regex:
            try:
                if re.search(usa_defendant_regex, defendant_text, re.IGNORECASE):
                    if self.logger:
                        self.log_debug("Case excluded by USA defendant regex", context)
                    return True
            except re.error:
                if self.logger:
                    self.log_warning(f"Invalid USA defendant regex: {usa_defendant_regex}", context)
        
        # Check excluded removal defendant keywords
        excluded_removal_defendant_keywords = self._relevance_config.get('excluded_removal_defendant_keywords', [])
        
        for keyword in excluded_removal_defendant_keywords:
            if keyword.lower() in defendant_text:
                if self.logger:
                    self.log_debug(f"Case excluded by removal defendant keyword: {keyword}", context)
                return True
        
        return False

    async def _is_explicitly_relevant(self, 
                                    case_details: Dict[str, Any],
                                    court_id: str = "",
                                    docket_num: str = "") -> bool:
        """Check if case is explicitly relevant."""
        context = self._get_logger_context(court_id, docket_num)
        
        if not self._relevance_config:
            return False
        
        # Check for relevant defendants
        defendants = case_details.get('defendant', [])
        
        # Handle None and ensure it's a list
        if defendants is None:
            defendants = []
        elif isinstance(defendants, str):
            defendants = [defendants]

        # Handle both string and dict formats for defendants with type validation
        defendant_strings = []
        for d in defendants:
            if isinstance(d, dict):
                defendant_strings.append(d.get('name', ''))
            elif isinstance(d, str):
                defendant_strings.append(d)
            elif d is not None:
                defendant_strings.append(str(d))
        defendant_text = ' '.join(defendant_strings).lower()
        
        for relevant_defendant in self._relevant_defendants_lower:
            if relevant_defendant in defendant_text:
                if self.logger:
                    self.log_debug(f"Found relevant defendant match: {relevant_defendant}", context)
                return True

        # Check explicitly relevant NOS codes
        explicitly_relevant_nos = self._relevance_config.get('explicitly_relevant_nos', [])
        case_nos = case_details.get('nos', '')
        
        if case_nos in explicitly_relevant_nos:
            if self.logger:
                self.log_debug(f"Case relevant by explicit NOS: {case_nos}", context)
            return True
        
        # Check potentially relevant NOS codes
        potentially_relevant_nos = self._relevance_config.get('potentially_relevant_nos', [])
        
        if case_nos in potentially_relevant_nos:
            if self.logger:
                self.log_debug(f"Case relevant by potentially relevant NOS: {case_nos}", context)
            return True
        
        # Check explicitly relevant causes
        explicitly_relevant_causes = self._relevance_config.get('explicitly_relevant_causes', [])
        case_cause_raw = case_details.get('cause', '')
        
        # Handle different cause formats
        if isinstance(case_cause_raw, str):
            case_cause = case_cause_raw.lower()
        elif isinstance(case_cause_raw, dict):
            case_cause = ' '.join(str(v) for v in case_cause_raw.values() if v).lower()
        else:
            case_cause = str(case_cause_raw).lower()
        
        for cause in explicitly_relevant_causes:
            if cause.lower() in case_cause:
                if self.logger:
                    self.log_debug(f"Case relevant by explicit cause: {cause}", context)
                return True
        
        # Check relevant NOS keywords
        relevant_nos_keywords = self._relevance_config.get('relevant_nos_keywords', [])
        
        for keyword in relevant_nos_keywords:
            if keyword.lower() in case_cause:
                if self.logger:
                    self.log_debug(f"Case relevant by NOS keyword: {keyword}", context)
                return True
        
        # Check other explicit relevance patterns from config (legacy support)
        relevant_patterns = self._relevance_config.get('relevant_patterns', [])
        
        for pattern in relevant_patterns:
            if self._matches_relevant_pattern(pattern, case_details):
                if self.logger:
                    self.log_debug(f"Found relevant pattern match", context)
                return True

        return False

    def _matches_relevant_pattern(self, pattern: Dict[str, Any], case_details: Dict[str, Any]) -> bool:
        """Check if case details match a relevant pattern."""
        # Check pattern type and apply appropriate matching logic
        pattern_type = pattern.get('type', 'defendant')
        pattern_value = pattern.get('value', '')
        field = pattern.get('field', 'defendant')
        
        case_value = case_details.get(field, '')
        if isinstance(case_value, list):
            case_value = ' '.join(str(v) for v in case_value)
        
        case_value = str(case_value).lower()
        pattern_value = str(pattern_value).lower()
        
        if pattern.get('regex', False):
            try:
                return bool(re.search(pattern_value, case_value, re.IGNORECASE))
            except re.error:
                if self.logger:
                    self.log_warning(f"Invalid regex pattern: {pattern_value}")
                return False
        else:
            return pattern_value in case_value

    async def is_relevant(self, case_details: Dict[str, Any]) -> bool:
        """
        Legacy method for backwards compatibility.
        Returns boolean result of relevance determination.
        """
        result = await self.determine_case_relevance(case_details)
        return result['is_relevant']