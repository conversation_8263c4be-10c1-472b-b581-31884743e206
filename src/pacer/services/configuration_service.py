# /src/services/pacer/_core_services/configuration/configuration_service.py

"""
Configuration Service for PACER processing.

Consolidates ConfigurationFacadeService and PacerConfigProvider into a single
unified service that manages all PACER configuration needs.
"""

from __future__ import annotations
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, TYPE_CHECKING

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError, ConfigurationError

if TYPE_CHECKING:
    from src.infrastructure.protocols.logger import LoggerProtocol


class ConfigurationService(AsyncServiceBase):
    """
    Consolidated configuration service for PACER processing.
    
    This service combines the functionality of ConfigurationFacadeService and 
    PacerConfigProvider to provide a unified interface for all PACER configuration
    management, following the workflow phases from docket_processing.md.
    
    Key Features:
    - Court ID mapping: Handles variations like 'ilnd' vs 'ilndc' automatically
    - Robust error handling: Prevents NoneType iteration errors
    - Configuration caching: Loads configs once and caches for performance
    - Fallback support: Uses default configs when files are missing
    - Flexible court lookup: Tries multiple court ID variations for better matching
    """

    def __init__(self,
                 logger: Optional[LoggerProtocol] = None,
                 config: Optional[Dict] = None):
        super().__init__(logger, config)
        
        # Configuration paths - ensure it works even if config is None
        config_dict = config or {}
        config_dir = config_dict.get('config_dir', 'src/config/pacer')
        self._config_dir = Path(config_dir) if config_dir else Path('src/config/pacer')
        
        # Cached configurations
        self._all_configs: Optional[Dict[str, Any]] = None
        self._configs_loaded = False

    @property
    def config_dir(self) -> Path:
        """Get the configuration directory path."""
        return self._config_dir

    def _get_default_configs(self) -> Dict[str, Any]:
        """Get default configuration values when files are not available."""
        return {
            "relevance": {
                "keywords": [],
                "required_terms": [],
                "scoring": {
                    "weights": {
                        "title": 0.3,
                        "content": 0.4,
                        "parties": 0.3
                    }
                }
            },
            "stability": {
                "timeouts": {
                    "page_load": 30,
                    "element_wait": 10,
                    "ajax_wait": 5
                },
                "retries": {
                    "max_attempts": 3,
                    "delay_seconds": 2
                }
            },
            "paths": {
                "downloads": "data/downloads",
                "reports": "data/reports",
                "logs": "data/logs"
            },
            "ignore_download": {
                "entries": []
            },
            "relevant_defendants": {
                "defendants": []
            }
        }

    async def _initialize_service(self) -> None:
        """Initialize the configuration service."""
        await self._load_all_configs()
        self.log_info("Configuration service initialized successfully")

    def _get_logger_context(self, court_id: str = "", docket_num: str = "") -> Dict[str, Any]:
        """Get logger context with consistent [court_id][docket_num] prefix format."""
        context = {"service": "ConfigurationService"}
        if court_id or docket_num:
            context["case_prefix"] = f"[{court_id}][{docket_num}]"
        return context

    async def _execute_action(self, data: Any) -> Any:
        """Route actions to appropriate methods."""
        action = data.get('action')
        court_id = data.get('court_id', '')
        docket_num = data.get('docket_num', '')
        
        context = self._get_logger_context(court_id, docket_num)
        self.log_debug(f"Executing configuration action: {action}", context)
        
        if action == 'get_all_configs':
            return await self.get_all_configs()
        elif action == 'get_relevance_config':
            return await self.get_relevance_config()
        elif action == 'get_relevant_defendants':
            return await self.get_relevant_defendants()
        elif action == 'get_ignore_download_config':
            return await self.get_ignore_download_config()
        elif action == 'get_stability_config':
            return await self.get_stability_config()
        elif action == 'get_paths_config':
            return await self.get_paths_config()
        elif action == 'reload_configs':
            return await self.reload_configs()
        elif action == 'get_court_config':
            court_id = data.get('court_id')
            if not court_id:
                raise PacerServiceError("court_id is required for get_court_config action")
            return await self.get_court_config(court_id)
        else:
            raise PacerServiceError(f"Unknown action for ConfigurationService: {action}")

    async def _load_all_configs(self) -> Dict[str, Any]:
        """Load all PACER configurations from JSON files."""
        self.log_info("Loading all PACER configurations")
        
        # Start with default configurations
        defaults = self._get_default_configs()
        configs = defaults.copy()
        
        config_files = {
            "relevance": self._config_dir / 'relevance_config.json',
            "stability": self._config_dir / 'stability_config.json', 
            "paths": self._config_dir / 'paths_config.json',
            "ignore_download": self._config_dir / 'ignore_download/ignore_download.json',
            "relevant_defendants": self._config_dir / 'defendants/relevant_defendants.json',
        }
        
        for config_name, file_path in config_files.items():
            try:
                loaded_config = self._load_json_config(file_path)
                if loaded_config:  # Only override defaults if we successfully loaded something
                    configs[config_name] = loaded_config
                    self.log_debug(f"Loaded {config_name} configuration", 
                                  {"config_file": str(file_path)})
                else:
                    self.log_debug(f"Using default {config_name} configuration (file not found or empty)", 
                                  {"config_file": str(file_path)})
            except Exception as e:
                self.log_warning(f"Failed to load {config_name} config, using defaults: {str(e)}", 
                               {"config_file": str(file_path)})
                # Keep the default configuration for this config_name
        
        self._all_configs = configs
        self._configs_loaded = True
        
        self.log_info("All PACER configurations loaded successfully", 
                     {"loaded_configs": list(configs.keys())})
        
        return configs

    def _load_json_config(self, file_path: Path) -> Dict[str, Any]:
        """Load a single JSON configuration file."""
        if not file_path.exists():
            self.log_warning(f"Configuration file not found: {file_path}")
            return {}
            
        try:
            with file_path.open('r', encoding='utf-8') as f:
                content = json.load(f)
                # Special handling for ignore_download.json which can be an array
                if 'ignore_download.json' in str(file_path) and isinstance(content, list):
                    return {'entries': content}  # Wrap array in dict structure
                elif not isinstance(content, dict):
                    self.log_warning(f"Configuration file contains non-dict content: {file_path}")
                    return {}
                return content
        except json.JSONDecodeError as e:
            self.log_error(f"Invalid JSON in configuration file {file_path}: {str(e)}")
            raise ConfigurationError(f"Invalid JSON in {file_path}: {str(e)}")
        except Exception as e:
            self.log_error(f"Error loading configuration file {file_path}: {str(e)}")
            raise ConfigurationError(f"Error loading {file_path}: {str(e)}")

    async def get_all_configs(self) -> Dict[str, Any]:
        """Get all PACER configurations."""
        if not self._configs_loaded or self._all_configs is None:
            await self._load_all_configs()
        return self._all_configs.copy() if self._all_configs else {}

    async def get_relevance_config(self) -> Dict[str, Any]:
        """Get the relevance configuration for case processing."""
        configs = await self.get_all_configs()
        return configs.get("relevance", {})

    async def get_relevant_defendants(self) -> List[str]:
        """Get the list of relevant defendants."""
        configs = await self.get_all_configs()
        defendants_data = configs.get("relevant_defendants", {})
        return defendants_data.get("defendants", [])

    async def get_ignore_download_config(self) -> Dict[str, Any]:
        """Get the ignore download configuration."""
        configs = await self.get_all_configs()
        return configs.get("ignore_download", {})

    async def get_stability_config(self) -> Dict[str, Any]:
        """Get the stability configuration for page processing."""
        configs = await self.get_all_configs()
        return configs.get("stability", {})

    async def get_paths_config(self) -> Dict[str, Any]:
        """Get the paths configuration."""
        configs = await self.get_all_configs()
        return configs.get("paths", {})

    async def reload_configs(self) -> Dict[str, Any]:
        """Reload all configurations from disk."""
        self.log_info("Reloading all PACER configurations")
        self._configs_loaded = False
        self._all_configs = None
        return await self._load_all_configs()

    async def get_config_value(self, config_name: str, key_path: str, default: Any = None) -> Any:
        """
        Get a specific configuration value using dot notation.
        
        Args:
            config_name: Name of the configuration (e.g., 'relevance', 'stability')
            key_path: Dot-separated path to the value (e.g., 'timeouts.page_load')
            default: Default value if key is not found
            
        Returns:
            The configuration value or default
        """
        configs = await self.get_all_configs()
        config = configs.get(config_name, {})
        
        keys = key_path.split('.')
        current = config
        
        for key in keys:
            if not isinstance(current, dict) or key not in current:
                return default
            current = current[key]
            
        return current

    async def is_config_loaded(self) -> bool:
        """Check if configurations are loaded."""
        return self._configs_loaded and self._all_configs is not None

    async def ensure_configs_loaded(self) -> None:
        """Ensure configurations are loaded, loading defaults if necessary."""
        if not self._configs_loaded or self._all_configs is None:
            await self._load_all_configs()

    async def get_court_config(self, court_id: str) -> Optional[Dict[str, Any]]:
        """
        Get court configuration for a specific court ID.
        
        This method handles court ID variations like 'ilnd' vs 'ilndc' by trying
        both the original court_id and court_id + 'c' if the first is not found.
        
        Args:
            court_id: The court identifier (e.g., 'akdc', 'candc', 'ilnd', etc.)
            
        Returns:
            Court configuration dictionary with 'court_id', 'login_url', and 'court_name'
            or None if the court is not found
            
        Raises:
            ConfigurationError: If the court configuration file cannot be loaded
        """
        if not court_id:
            raise PacerServiceError("court_id parameter is required")
            
        # Construct the path to the district courts configuration file
        courts_config_path = Path("src/config/courts/district_courts.json")
        
        self.log_debug(f"Loading court configuration for court_id: {court_id}", 
                      {"config_file": str(courts_config_path)})
        
        try:
            if not courts_config_path.exists():
                self.log_error(f"Court configuration file not found: {courts_config_path}")
                raise ConfigurationError(f"Court configuration file not found: {courts_config_path}")
                
            with courts_config_path.open('r', encoding='utf-8') as f:
                courts_data = json.load(f)
                
            # Handle None or invalid data
            if courts_data is None:
                self.log_error(f"Court configuration file is empty or invalid: {courts_config_path}")
                raise ConfigurationError(f"Court configuration file is empty: {courts_config_path}")
                
            if not isinstance(courts_data, list):
                self.log_error(f"Court configuration file contains invalid format (expected list): {courts_config_path}")
                raise ConfigurationError(f"Invalid format in court configuration file: {courts_config_path}")
                
            # Create a list of court IDs to try: original and with 'c' suffix
            court_ids_to_try = [court_id]
            
            # If the court_id doesn't already end with 'c', try adding 'c'
            if not court_id.endswith('c'):
                court_ids_to_try.append(court_id + 'c')
            
            # If the court_id ends with 'c', also try without 'c'
            if court_id.endswith('c'):
                court_ids_to_try.append(court_id[:-1])
                
            self.log_debug(f"Trying court IDs: {court_ids_to_try}")
            
            # Search for the court by trying different court_id variations
            for try_court_id in court_ids_to_try:
                for court in courts_data:
                    if not isinstance(court, dict):
                        continue
                        
                    if court.get('court_id') == try_court_id:
                        self.log_debug(f"Found court configuration for: {court_id} (matched as {try_court_id})", 
                                      {"court_name": court.get('court_name', 'Unknown')})
                        # IMPORTANT: Strip the last 'c' from court_id in the returned data
                        # The JSON file has court_ids with 'c' suffix (e.g., 'ilndc')
                        # but the system expects them without (e.g., 'ilnd')
                        result = court.copy()
                        json_court_id = result.get('court_id', '')
                        if json_court_id.endswith('c'):
                            result['court_id'] = json_court_id[:-1]  # Strip the 'c' suffix
                        return result
                        
            # Court not found with any variation
            self.log_warning(f"Court configuration not found for court_id: {court_id} (tried: {court_ids_to_try})")
            return None
            
        except json.JSONDecodeError as e:
            self.log_error(f"Invalid JSON in court configuration file {courts_config_path}: {str(e)}")
            raise ConfigurationError(f"Invalid JSON in court configuration file: {str(e)}")
        except Exception as e:
            self.log_error(f"Error loading court configuration: {str(e)}")
            raise ConfigurationError(f"Error loading court configuration: {str(e)}")