# /src/services/pacer/html_processing_facade_service.py
from typing import Any, Dict, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.components.case_processing.field_consistency_manager import FieldConsistencyManager
from src.pacer.components.case_processing.html_parser import HtmlParser
from src.pacer.components.case_processing.law_firm_corrector import LawFirmCorrector
from src.pacer.components.case_processing.transfer_info_processor import TransferInfoProcessor
from src.pacer.components.download.s3_manager import S3Manager


class HtmlProcessingFacadeService(AsyncServiceBase):
    """
    Facade service for orchestrating all HTML processing operations for PACER cases.
    It coordinates various components to parse, enrich, and manage HTML data.
    """

    def __init__(
        self,
        logger: Optional[Any] = None,
        config: Optional[Dict] = None,
        html_parser: Optional[HtmlParser] = None,
        law_firm_corrector: Optional[LawFirmCorrector] = None,
        s3_manager: Optional[S3Manager] = None,
        s3_async_storage: Optional[Any] = None,  # Direct S3 service for HTML upload
        field_consistency_manager: Optional[FieldConsistencyManager] = None,
        transfer_info_processor: Optional[TransferInfoProcessor] = None,
    ):
        super().__init__(logger, config)
        self._html_parser = html_parser
        self._law_firm_corrector = law_firm_corrector
        self._s3_manager = s3_manager
        self._s3_async_storage = s3_async_storage  # CRITICAL FIX: Store direct S3 storage
        self._field_consistency_manager = field_consistency_manager
        self._transfer_info_processor = transfer_info_processor

    async def _execute_action(self, data: Any) -> Any:
        """
        Routes actions to the appropriate methods. The primary action is
        'process_html_content'.
        """
        action = data.get("action")
        if action == "process_html_content":
            return await self.process_html_content(
                case_details=data.get("case_details", {}),
                html_content=data.get("html_content", ""),
                json_path=data.get("json_path", ""),
            )
        else:
            raise ValueError(f"Unknown action for HtmlProcessingFacadeService: {action}")

    async def process_html_content(
        self, case_details: Dict[str, Any], html_content: str, json_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Orchestrates the end-to-end process of handling PACER HTML content.

        Args:
            case_details: The case data dictionary to be updated.
            html_content: The raw HTML content of the case page.
            json_path: The path to the source JSON file for context.

        Returns:
            The updated case details dictionary.
        """
        try:
            self.log_info("Starting comprehensive HTML processing workflow.")

            # Step 1: Parse HTML to extract attorneys
            attorneys = await self._html_parser.execute({
                "action": "extract_attorneys", "html_content": html_content
            })

            # Step 2: Correct law firm names that are street addresses
            if attorneys:
                attorneys = await self._law_firm_corrector.execute({
                    "action": "correct_law_firms", "attorneys": attorneys
                })
                case_details["attorney"] = attorneys
                self.log_info(f"Extracted and corrected {len(attorneys)} attorneys.")

            # Step 3: Upload HTML to S3 (with fallback to direct S3 storage)
            if json_path and html_content:
                upload_success = False
                s3_html_link = None
                
                # Try S3Manager first
                if self._s3_manager:
                    try:
                        upload_result = await self._s3_manager.execute({
                            "action": "upload_html",
                            "case_details": case_details,
                            "html_content": html_content,
                            "json_path": json_path,
                        })
                        if isinstance(upload_result, dict) and upload_result.get('success'):
                            upload_success = True
                            s3_html_link = upload_result.get('s3_html')
                            self.log_info(f"S3Manager upload successful: {s3_html_link}")
                        else:
                            self.log_warning("S3Manager upload returned failure, trying direct S3 storage")
                    except Exception as e:
                        self.log_warning(f"S3Manager upload failed: {e}, trying direct S3 storage")
                
                # Fallback to direct S3 async storage if S3Manager failed
                if not upload_success and self._s3_async_storage:
                    try:
                        self.log_info("Attempting HTML upload via direct S3 async storage")
                        
                        # Extract date and construct S3 key
                        import re
                        date_match = re.search(r'/(\d{8})/', json_path)
                        if date_match:
                            iso_date = date_match.group(1)
                        else:
                            from datetime import datetime
                            iso_date = datetime.now().strftime('%Y%m%d')
                        
                        # Get base filename
                        base_filename = case_details.get('base_filename', case_details.get('new_filename', ''))
                        if not base_filename:
                            import os
                            base_filename = os.path.splitext(os.path.basename(json_path))[0]
                        
                        # Ensure .html extension
                        if not base_filename.endswith('.html'):
                            html_filename = f"{base_filename}.html"
                        else:
                            html_filename = base_filename
                        
                        # Construct S3 key
                        s3_key = f"{iso_date}/html/{html_filename}"
                        
                        # Upload directly
                        upload_success = await self._s3_async_storage.upload_content(
                            content=html_content,
                            object_key=s3_key,
                            content_type='text/html',
                            overwrite=True
                        )
                        
                        if upload_success:
                            s3_html_link = f"https://cdn.lexgenius.ai/{s3_key}"
                            case_details['s3_html'] = s3_html_link
                            case_details['s3_html_key'] = s3_key
                            self.log_info(f"Direct S3 upload successful: {s3_html_link}")
                        else:
                            self.log_error("Direct S3 upload also failed")
                            
                    except Exception as e:
                        self.log_error(f"Direct S3 upload failed: {e}")
                
                if not upload_success:
                    self.log_warning("All S3 upload methods failed, but continuing processing.")

            # Step 4: Find the verified S3 link for the uploaded HTML (only if not already set)
            if json_path and not case_details.get("s3_html") and self._s3_manager:
                try:
                    found_s3_html_link = await self._s3_manager.execute({
                        "action": "find_html_link",
                        "case_details": case_details,
                        "json_path": json_path,
                    })
                    if found_s3_html_link:
                        case_details["s3_html"] = found_s3_html_link
                        self.log_info(f"Found S3 HTML link via search: {found_s3_html_link}")
                except Exception as e:
                    self.log_warning(f"S3 link search failed: {e}")

            # Step 5: Process transfer information for compatibility
            if case_details.get("case_in_other_court"):
                case_details = await self._transfer_info_processor.execute({
                    "action": "process_transfer_info", "case_details": case_details
                })
                self.log_info("Transfer compatibility processing completed.")

            # Step 6: Ensure field consistency (plaintiffs vs plaintiff, etc.)
            case_details = await self._field_consistency_manager.execute({
                "action": "ensure_consistency", "case_details": case_details
            })
            self.log_info("Field consistency check completed.")

            self.log_info("HTML processing workflow completed successfully.")
            return case_details

        except Exception as e:
            self.log_error(f"Error in HTML processing workflow: {e}", exc_info=True)
            case_details["_html_processing_error"] = str(e)
            return case_details
