from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
import uuid
from datetime import datetime, date as DateType

@dataclass
class PacerJob:
    """
    Represents a job to process PACER data for a single court.
    Holds all state and data related to the processing of one court for a specific date range.

    Attributes:
        court_id: The unique identifier for the court (e.g., 'njd').
        iso_date: The primary date for which this job is being processed (YYYYMMDD format).
        start_date: The start date for the query range.
        end_date: The end date for the query range.
        config_snapshot: A dictionary containing relevant configuration settings at the time of job creation.
        docket_list_input: Optional list of specific dockets to process, bypassing report generation.

        job_id: A unique UUID for this job instance.
        status: Current processing status of the job (e.g., "PENDING", "COMPLETED", "ERROR").
        error_message: Optional message if the job encounters an error.

        results: A dictionary to store the results of the job's execution.
        metrics: A dictionary to store various metrics related to the job's execution.

        created_at_utc: Timestamp when the job instance was created.
        updated_at_utc: Timestamp when the job instance was last updated.
    """
    court_id: str
    iso_date: str
    start_date: DateType
    end_date: DateType
    config_snapshot: Dict[str, Any]
    docket_list_input: Optional[List[Dict[str, Any]]] = None

    job_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    status: str = "PENDING"
    error_message: Optional[str] = None

    results: Dict[str, Any] = field(default_factory=dict)
    metrics: Dict[str, Any] = field(default_factory=lambda: {
        "start_time_utc": None, # ISO format string
        "end_time_utc": None,   # ISO format string
        "duration_sec": None,
        "cases_found": 0,
        "cases_processed": 0,
    })

    created_at_utc: datetime = field(default_factory=datetime.utcnow)
    updated_at_utc: datetime = field(init=False)

    def __post_init__(self):
        """Initialize updated_at_utc to be the same as created_at_utc."""
        self.updated_at_utc = self.created_at_utc

    def update_status(self, new_status: str) -> None:
        """Updates the job's status and the updated_at_utc timestamp."""
        self.status = new_status
        self.updated_at_utc = datetime.utcnow()

    def set_error(self, message: str) -> None:
        """
        Sets the job status to "ERROR", records the error message,
        and updates the end_time and duration if the timer was started.
        """
        self.status = "ERROR"
        self.error_message = message
        current_time = datetime.utcnow()
        self.updated_at_utc = current_time
        if self.metrics.get("start_time_utc") and not self.metrics.get("end_time_utc"):
            self.metrics["end_time_utc"] = current_time.isoformat()
            try:
                start_time = datetime.fromisoformat(self.metrics["start_time_utc"])
                self.metrics["duration_sec"] = (current_time - start_time).total_seconds()
            except (TypeError, ValueError):
                 self.metrics["duration_sec"] = -1

    def start_timer(self) -> None:
        """Records the job's start time in UTC ISO format."""
        current_time = datetime.utcnow()
        self.metrics["start_time_utc"] = current_time.isoformat()
        self.updated_at_utc = current_time

    def end_timer(self) -> None:
        """
        Records the job's end time and calculates the duration if start_time is set.
        """
        if self.metrics.get("start_time_utc"):
            end_time = datetime.utcnow()
            self.metrics["end_time_utc"] = end_time.isoformat()
            try:
                start_time = datetime.fromisoformat(self.metrics["start_time_utc"])
                self.metrics["duration_sec"] = (end_time - start_time).total_seconds()
            except (TypeError, ValueError):
                self.metrics["duration_sec"] = -1
            self.updated_at_utc = end_time
        else:
            self.updated_at_utc = datetime.utcnow()

    def to_summary_dict(self) -> Dict[str, Any]:
        """Returns a serializable dictionary summary of the job."""
        return {
            "job_id": self.job_id,
            "court_id": self.court_id,
            "iso_date": self.iso_date,
            "status": self.status,
            "error_message": self.error_message,
            "metrics": self.metrics,
            "results": self.results,
            "created_at_utc": self.created_at_utc.isoformat(),
            "updated_at_utc": self.updated_at_utc.isoformat()
        }
