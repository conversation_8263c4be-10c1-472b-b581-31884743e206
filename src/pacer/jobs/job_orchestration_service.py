import asyncio
import logging
from typing import Any, Dict, List
from datetime import date as DateType

from src.pacer.jobs.jobs_models import PacerJob
from src.pacer.jobs.job_runner_service import PacerJobRunnerService


class PacerJobOrchestrationService:
    """
    Orchestrates the creation and sequential execution of PacerJob instances.
    Jobs are processed one at a time to prevent multiple browser tab conflicts.
    """

    def __init__(
        self,
        config: Dict[str, Any],
        job_runner_service: PacerJobRunnerService,
        logger: logging.Logger,
    ):
        """
        Initializes the PacerJobOrchestrationService.

        Args:
            config: Application configuration dictionary.
            job_runner_service: An instance of PacerJobRunnerService.
            logger: Logger instance.
        """
        self.config = config
        self.job_runner_service = job_runner_service
        self.logger = logger
        self.num_workers = self.config.get("num_workers", 10)

    async def process_courts_as_jobs(
        self,
        court_ids: List[str],
        iso_date: str,
        start_date: DateType,
        end_date: DateType,
        docket_list_input: List[Dict[str, Any]] | None = None,
    ) -> List[PacerJob]:
        """
        Creates PacerJob instances and processes them sequentially.

        Args:
            court_ids: List of court identifiers to process.
            iso_date: Date string in YYYYMMDD format.
            start_date: Start date for the query range.
            end_date: End date for the query range.
            docket_list_input: Optional list of specific dockets to process.

        Returns:
            A list of processed PacerJob objects.
        """
        if docket_list_input:
            # If a docket list is provided, group by court_id and create jobs
            court_to_dockets_map: Dict[str, List[Dict[str, Any]]] = {}
            for item in docket_list_input:
                court_id = item.get("court_id")
                if court_id:
                    if court_id not in court_to_dockets_map:
                        court_to_dockets_map[court_id] = []
                    court_to_dockets_map[court_id].append(item)

            jobs = [
                PacerJob(
                    court_id=court_id,
                    iso_date=iso_date,
                    start_date=start_date,
                    end_date=end_date,
                    config_snapshot=self.config.copy(),
                    docket_list_input=dockets,
                )
                for court_id, dockets in court_to_dockets_map.items()
            ]
        else:
            jobs = [
                PacerJob(
                    court_id=court_id,
                    iso_date=iso_date,
                    start_date=start_date,
                    end_date=end_date,
                    config_snapshot=self.config.copy(),
                )
                for court_id in court_ids
            ]

        if not jobs:
            self.logger.info("No jobs to process.")
            return []

        self.logger.info(
            f"Created {len(jobs)} jobs. Starting processing with {self.num_workers} workers.")

        semaphore = asyncio.Semaphore(self.num_workers)

        async def run_job_with_semaphore(job: PacerJob) -> PacerJob:
            async with semaphore:
                return await self.job_runner_service.run_job(job)

        # Process jobs SEQUENTIALLY to prevent multiple browser tabs opening
        processed_jobs = []
        for i, job in enumerate(jobs):
            self.logger.info(f"Processing job {i+1}/{len(jobs)} SEQUENTIALLY: {job.court_id}")
            processed_job = await run_job_with_semaphore(job)
            processed_jobs.append(processed_job)
            # Small delay to ensure proper cleanup between jobs
            if i < len(jobs) - 1:  # Don't delay after the last job
                await asyncio.sleep(1.0)

        self.logger.info(f"All {len(jobs)} jobs have been processed.")
        return processed_jobs
