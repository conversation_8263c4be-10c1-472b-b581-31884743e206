import asyncio
import logging
from typing import Any, Dict, TYPE_CHECKING

from playwright.async_api import <PERSON>rowser<PERSON>ontext

from src.pacer.jobs.jobs_models import PacerJob
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.services.browser_service import BrowserService

if TYPE_CHECKING:
    from src.pacer.pacer_orchestrator_service import PacerOrchestratorService

class PacerJobRunnerService(AsyncServiceBase):
    """
    Service responsible for executing a single PacerJob.
    It encapsulates the logic for processing a single court within an isolated browser context.
    
    Follows dependency injection pattern - dependencies are injected via constructor.
    """

    def __init__(
        self, 
        config: Dict[str, Any], 
        logger: logging.Logger, 
        pacer_orchestrator: 'PacerOrchestratorService',
        browser_service_factory: Any = None
    ):
        """
        Initializes the PacerJobRunnerService using dependency injection.

        Args:
            config: Application configuration dictionary.
            logger: Logger instance.
            pacer_orchestrator: An instance of the main PacerOrchestratorService.
            browser_service_factory: Factory for creating browser services (injected via DI).
        """
        super().__init__(logger, config)
        self.pacer_orchestrator = pacer_orchestrator
        self.browser_service_factory = browser_service_factory

    async def _execute_action(self, data: Any) -> Any:
        """
        Required abstract method implementation for AsyncServiceBase.
        Routes to run_job method for PacerJob execution.
        """
        if isinstance(data, PacerJob):
            return await self.run_job(data)
        elif isinstance(data, dict) and "job" in data:
            return await self.run_job(data["job"])
        else:
            raise ValueError(f"PacerJobRunnerService requires PacerJob data, got: {type(data)}")

    async def run_job(self, job: PacerJob) -> PacerJob:
        """
        Executes a PacerJob, processing a single court.

        Args:
            job: The PacerJob instance to run.

        Returns:
            The job instance, updated with its final status, metrics, and results.
        """
        # Create court-specific logger for this job
        # CRITICAL: This creates AND replaces self.logger with the court logger
        court_logger = self.create_court_logger(job.court_id, job.iso_date)
        
        # Now ALL self.log_* calls use the court logger
        self.log_info(f"[{job.court_id.upper()}] JobRunner: Starting job {job.job_id} for court {job.court_id}")
        self.log_info(f"Starting job execution - Job ID: {job.job_id}")
        job.start_timer()

        browser_service = None
        context = None
        try:
            # Create a new BrowserService using the injected factory for proper DI
            if self.browser_service_factory:
                browser_service = self.browser_service_factory(
                    headless=job.config_snapshot.get("headless", True),
                    timeout_ms=job.config_snapshot.get("timeout_ms", 60000),
                    config=job.config_snapshot,
                    logger=court_logger,  # Pass the court logger directly
                )
            else:
                # Fallback to manual creation if factory not available
                browser_service = BrowserService(
                    headless=job.config_snapshot.get("headless", True),
                    timeout_ms=job.config_snapshot.get("timeout_ms", 60000),
                    config=job.config_snapshot,
                    logger=court_logger,  # Pass the court logger directly
                )
            
            # The orchestrator is now responsible for creating the context with the correct download path
            context = await self.pacer_orchestrator.create_browser_context_with_download_path(
                browser_service,
                job.court_id,
                job.iso_date,
                "docket_log" if job.docket_list_input else "report"
            )

            # The core processing logic is now encapsulated in a new method in the orchestrator
            self.log_info(f"Processing court job with orchestrator - Context created successfully")
            result = await self.pacer_orchestrator.process_single_court_job(job, context)

            job.results = result
            job.metrics.update(result.get('metrics', {}))
            job.update_status("COMPLETED")
            # All logging uses self.log_* which now goes to court logger
            self.log_info(f"Job {job.job_id} completed successfully")
            self.log_info(f"[{job.court_id.upper()}] JobRunner: Successfully completed job {job.job_id}")
            self.log_info(f"Job completed successfully - Duration: {job.metrics.get('duration_sec', 0):.2f}s")

        except Exception as e:
            # All error logging uses self.log_error which now goes to court logger
            self.log_error(f"Job {job.job_id} failed with unhandled exception: {str(e)}", exc_info=True)
            self.log_error(f"[{job.court_id.upper()}] JobRunner: Unhandled exception in job {job.job_id}: {e}", exc_info=True)
            job.set_error(f"Unhandled job execution error: {str(e)}")
        finally:
            if context:
                await context.close()
                self.log_info("Browser context closed")
            if browser_service:
                await browser_service.close()
                self.log_info("Browser service closed")
            job.end_timer()
            # All logging uses self.log_* which goes to court logger
            self.log_info(f"[{job.court_id.upper()}] JobRunner: Finished job {job.job_id}. Status: {job.status}, Duration: {job.metrics.get('duration_sec', 0):.2f}s")
            self.log_info(f"Job execution complete - Final status: {job.status}")

        return job
