"""
Core Container Module

Provides the main application container and core infrastructure services.
"""

import logging
import os
from datetime import datetime
from typing import Any

import aiohttp
from dependency_injector import containers, providers

from . import fb_ads, pacer_core, reports, storage, transformer
from src.infrastructure.protocols.logger import LoggerProtocol
from src.infrastructure.utils.dual_logger import create_dual_logger


def _detect_pacer_context():
    """
    Detect if we're in a PACER context by examining the call stack.
    
    Returns:
        dict with court_id and iso_date if PACER context detected, None otherwise
    """
    import inspect
    import os
    from datetime import datetime
    
    # Examine the call stack to detect PACER operations
    stack = inspect.stack()
    court_id = None
    iso_date = None
    pacer_detected = False
    
    for frame_info in stack:
        filename = frame_info.filename
        function_name = frame_info.function
        
        # Enhanced PACER context detection - check filename paths and function names
        pacer_indicators = [
            'pacer' in filename.lower(),
            'court' in function_name.lower(),
            '/pacer/' in filename,
            'docket' in function_name.lower(),
            'ecf' in function_name.lower(),
            filename.endswith('pacer_cli.py'),
            'PacerJobRunner' in str(frame_info.code_context) if frame_info.code_context else False,
            'court_logger' in function_name.lower(),
        ]
        
        if any(pacer_indicators):
            pacer_detected = True
            
            # Try to extract court_id and iso_date from frame locals
            frame = frame_info.frame
            if frame and hasattr(frame, 'f_locals'):
                locals_dict = frame.f_locals
                
                # Look for court_id in various possible variable names
                if not court_id:
                    for var_name in ['court_id', 'courtId', 'court', 'court_identifier', 'target_court']:
                        if var_name in locals_dict and locals_dict[var_name]:
                            court_id = str(locals_dict[var_name])
                            break
                
                # Look for iso_date in various possible variable names
                if not iso_date:
                    for var_name in ['iso_date', 'isoDate', 'date', 'processing_date', 'target_date']:
                        if var_name in locals_dict and locals_dict[var_name]:
                            iso_date = str(locals_dict[var_name])
                            break
                
                # Also check 'self' for attributes
                if 'self' in locals_dict:
                    self_obj = locals_dict['self']
                    if not court_id and hasattr(self_obj, 'court_id'):
                        court_id = str(self_obj.court_id)
                    if not court_id and hasattr(self_obj, 'target_court'):
                        court_id = str(self_obj.target_court)
                    if not iso_date and hasattr(self_obj, 'iso_date'):
                        iso_date = str(self_obj.iso_date)
                    if not iso_date and hasattr(self_obj, 'target_date'):
                        iso_date = str(self_obj.target_date)
    
    # If PACER context detected, return what we found (may be None for court_id)
    if pacer_detected:
        # Only provide iso_date as fallback - do NOT provide fallback court_id
        if not iso_date:
            iso_date = datetime.now().strftime('%Y-%m-%d')
        
        return {'court_id': court_id, 'iso_date': iso_date}  # court_id may be None
    
    return None


def _get_log_directory(config) -> str:
    """
    Get the proper log directory from configuration.
    
    Args:
        config: Configuration object/provider
        
    Returns:
        Log directory path
    """
    try:
        # For dependency injector providers, try to get the actual config
        if hasattr(config, 'provided'):
            # This is a dependency injector provider
            try:
                # Try to access the provided config values
                config_dict = config()
                if 'log_dir' in config_dict and config_dict['log_dir']:
                    return str(config_dict['log_dir'])
                elif 'DATA_DIR' in config_dict:
                    return f"{config_dict['DATA_DIR']}/logs"
            except Exception:
                # If provider call fails, continue to fallback
                pass
        
        # Direct config access
        if hasattr(config, 'log_dir') and config.log_dir:
            return str(config.log_dir)
        elif hasattr(config, 'DATA_DIR'):
            return f"{config.DATA_DIR}/logs"
            
        # Dictionary-like access
        if isinstance(config, dict):
            if 'log_dir' in config and config['log_dir']:
                return str(config['log_dir'])
            elif 'DATA_DIR' in config:
                return f"{config['DATA_DIR']}/logs"
                
    except (AttributeError, TypeError):
        pass
    
    # Check environment variables as fallback
    if 'LEXGENIUS_DATA_DIR' in os.environ:
        return f"{os.environ['LEXGENIUS_DATA_DIR']}/logs"
    elif 'DATA_DIR' in os.environ:
        return f"{os.environ['DATA_DIR']}/logs"
    
    # Default fallback
    return os.path.join(os.getcwd(), "logs")


def _create_dual_logger_wrapper(config, court_id=None, iso_date=None):
    """
    Helper function to create logger wrapper with proper config access.
    
    FIXED BEHAVIOR:
    - ONLY creates court-specific logger if a REAL court_id is provided (e.g., 'ilnd', 'cand')
    - Court-specific logs go to: data/{iso_date}/logs/pacer/{court_id}.log
    - NO fallback to "pacer" court_id - creates general logger instead
    
    For PACER operations with real court_id:
    - Creates court-specific logger at data/{iso_date}/logs/pacer/{court_id}.log
    
    For all other operations (including PACER without real court_id):
    - Creates console-only logger (NO lexgenius.log files created)
    
    Args:
        config: Configuration object/provider
        court_id: Optional court ID - must be a real court code (not 'pacer', 'default', 'general')
        iso_date: Optional ISO date for PACER operations
        
    Returns:
        LoggerWrapper instance with appropriate logger configuration
    """
    # Auto-detect PACER context 
    pacer_context = _detect_pacer_context()
    
    # Get final values - prioritize explicit parameters over detected context
    final_court_id = court_id or (pacer_context.get('court_id') if pacer_context else None)
    final_iso_date = iso_date or (pacer_context.get('iso_date') if pacer_context else None)
    
    # CRITICAL: Only create court logger if we have a REAL court_id (not just PACER context)
    if final_court_id and final_court_id not in ['pacer', 'default', 'general']:
        # We have a real court ID - create court-specific logger
        if not final_iso_date:
            final_iso_date = datetime.now().strftime('%Y%m%d')  # YYYYMMDD format
        
        # Get configuration dictionary
        config_dict = _extract_config_dict(config)
        
        # Create court-specific logger - NO lexgenius logs
        try:
            from src.pacer.utils.court_logger import get_court_logger
            court_logger = get_court_logger(final_court_id, final_iso_date, config_dict)
            return LoggerWrapper(court_logger)
        except ImportError:
            # Fallback to minimal court logger
            return _create_minimal_court_logger(final_court_id, final_iso_date, config_dict)
        except Exception as e:
            # Even on error, use court logger
            return _create_fallback_court_logger(final_court_id, final_iso_date, str(e))
    
    # Additional check - examine call stack for PACER indicators
    import inspect
    import sys
    
    # No court_id detected - create a general logger, NOT a court-specific logger
    # This should be the standard lexgenius logger for non-PACER operations
    log_dir = _get_log_directory(config)
    
    # DISABLED: Do NOT create any lexgenius.log files
    # Instead, always use console-only logger for non-PACER operations
    try:
        # Create console-only logger that NEVER creates lexgenius.log files
        safe_logger_name = "general.console.no_files"
        safe_logger = logging.getLogger(safe_logger_name)
        
        # Ensure it has no file handlers and cannot create lexgenius.log
        if not safe_logger.handlers:
            from rich.logging import RichHandler
            from rich.console import Console
            
            console = Console(stderr=True, force_terminal=True)
            console_handler = RichHandler(
                console=console,
                rich_tracebacks=True,
                show_time=True,
                show_path=True
            )
            console_handler.setLevel(logging.DEBUG)
            safe_logger.addHandler(console_handler)
            safe_logger.setLevel(logging.DEBUG)
            safe_logger.propagate = False  # Prevent any propagation to root logger
        
        return LoggerWrapper(safe_logger)
    except Exception as e:
        # NUCLEAR PREVENTION: Do NOT create any lexgenius loggers that could create files
        # Instead, create a safe console-only logger with no file handlers
        safe_logger_name = "safe.console.fallback"
        safe_logger = logging.getLogger(safe_logger_name)
        
        # Ensure it has no file handlers and cannot create lexgenius.log
        if not safe_logger.handlers:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            formatter = logging.Formatter(
                "[SAFE-FALLBACK] %(asctime)s [%(levelname)s] %(message)s",
                datefmt="%H:%M:%S"
            )
            console_handler.setFormatter(formatter)
            safe_logger.addHandler(console_handler)
            safe_logger.setLevel(logging.DEBUG)
            safe_logger.propagate = False  # Prevent any propagation to root logger
        
        return LoggerWrapper(safe_logger)


def _extract_config_dict(config):
    """Extract configuration dictionary from various config types."""
    try:
        if hasattr(config, 'provided'):
            # This is a dependency injector provider
            return config() if callable(config) else {}
        elif hasattr(config, '__call__'):
            return config()
        elif isinstance(config, dict):
            return config
        else:
            return {}
    except Exception:
        return {}


def _create_minimal_court_logger(court_id: str, iso_date: str, config_dict: dict):
    """Create a minimal court-specific logger without external dependencies."""
    court_logger_name = f"pacer.court.{court_id.lower()}"
    court_logger = logging.getLogger(court_logger_name)
    
    # Avoid duplicate handlers
    if court_logger.handlers:
        return LoggerWrapper(court_logger)
    
    court_logger.setLevel(logging.DEBUG)
    court_logger.propagate = False  # Prevent propagation to root logger
    
    # Determine data directory
    data_dir = config_dict.get("DATA_DIR", os.path.join(os.getcwd(), "data"))
    log_dir = os.path.join(data_dir, iso_date, "logs", "pacer")
    os.makedirs(log_dir, exist_ok=True)
    
    # Create court-specific file handler ONLY
    log_file = os.path.join(log_dir, f"{court_id.lower()}.log")
    file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s:%(lineno)d - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    file_handler.setFormatter(file_formatter)
    court_logger.addHandler(file_handler)
    
    # Add console handler for immediate feedback
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter(
        f"[{court_id.upper()}] %(asctime)s [%(levelname)s] %(message)s",
        datefmt="%H:%M:%S"
    )
    console_handler.setFormatter(console_formatter)
    court_logger.addHandler(console_handler)
    
    # Log initialization
    court_logger.info(f"Court logger initialized for {court_id.upper()}")
    court_logger.debug(f"Logging to: {log_file}")
    
    return LoggerWrapper(court_logger)


def _create_fallback_court_logger(court_id: str, iso_date: str, error_msg: str):
    """Create a fallback court logger that only logs to console."""
    fallback_logger_name = f"pacer.fallback.{court_id.lower()}"
    fallback_logger = logging.getLogger(fallback_logger_name)
    
    # Avoid duplicate handlers
    if fallback_logger.handlers:
        return LoggerWrapper(fallback_logger)
    
    fallback_logger.setLevel(logging.DEBUG)
    fallback_logger.propagate = False  # Prevent propagation to root logger
    
    # Create console-only handler for fallback (no file to avoid permission issues)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter(
        f"[{court_id.upper()}-FALLBACK] %(asctime)s [%(levelname)s] %(message)s",
        datefmt="%H:%M:%S"
    )
    console_handler.setFormatter(console_formatter)
    fallback_logger.addHandler(console_handler)
    
    # Log the fallback situation
    fallback_logger.warning(f"Using fallback logger for {court_id}: {error_msg}")
    fallback_logger.info(f"Court processing will continue with console-only logging")
    
    return LoggerWrapper(fallback_logger)


class LoggerWrapper:
    """Simple wrapper to make standard logger compatible with LoggerProtocol."""
    
    def __init__(self, logger: logging.Logger):
        self._logger = logger
    
    def debug(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.debug(message, extra=extra)
    
    def info(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.info(message, extra=extra)
    
    def warning(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.warning(message, extra=extra)
    
    def error(self, message: str, extra: dict[str, Any] | None = None, exc_info: bool = False) -> None:
        self._logger.error(message, extra=extra, exc_info=exc_info)
    
    def exception(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.exception(message, extra=extra)
    
    def isEnabledFor(self, level: int) -> bool:
        """Check if logger is enabled for the given level."""
        return self._logger.isEnabledFor(level)


class CoreContainer(containers.DeclarativeContainer):
    """Core container for application-wide resources."""

    # Configuration provider
    config = providers.Configuration()

    # Logger factory - wrapped to provide LoggerProtocol interface with dual output
    # Use Factory to allow unique loggers per instantiation
    logger = providers.Factory(
        _create_dual_logger_wrapper,
        config=config
    )
    
    # Court-specific logger factory for PACER operations
    # This creates loggers that output to data/{iso_date}/logs/pacer/{court_id}.log
    court_logger = providers.Factory(
        _create_dual_logger_wrapper,
        config=config,
        court_id=providers.Dependency(),
        iso_date=providers.Dependency()
    )

    # HTTP Session resource - using Singleton to reuse session across dockets
    # This prevents creating new sessions for each docket and ensures proper lifecycle
    http_session = providers.Singleton(
        aiohttp.ClientSession,
        timeout=aiohttp.ClientTimeout(total=300),
    )


class MainContainer(containers.DeclarativeContainer):
    """Main application container that wires everything together."""

    # Configuration from environment and config files
    config = providers.Configuration()

    # Core services container
    core = providers.Container(CoreContainer, config=config)

    # Storage and repositories container
    storage = providers.Container(
        storage.StorageContainer,
        config=config.storage,
        logger=core.logger,
        aws_region=config.aws_region,
        aws_access_key=config.aws_access_key,
        aws_secret_key=config.aws_secret_key,
        dynamodb_endpoint=config.dynamodb_endpoint,
        s3_bucket_name=config.s3_bucket_name,
    )

    # PACER services container (using core container and add missing orchestrator)
    pacer = providers.Container(
        pacer_core.PacerCoreContainer,
        config=config,  # Pass full config instead of just config.pacer for AI services
        logger=core.logger,
        storage_container=storage,
        shutdown_event=providers.Dependency(),  # Provided at runtime
    )

    # Facebook Ads services container
    fb_ads = providers.Container(
        fb_ads.FbAdsContainer,
        config=config,  # Pass full config instead of just config.fb_ads
        logger=core.logger,
        storage_container=storage,
        http_session=core.http_session,
    )

    # Reports services container
    reports = providers.Container(
        reports.ReportsContainer,
        config=config,  # Pass full config instead of just config.reports
        logger=core.logger,
        storage_container=storage,
        # Removed cross-container dependencies - each container should be self-contained
    )

    # Transformer services container
    transformer = providers.Container(
        transformer.TransformerContainer,
        config=config,  # Pass full config instead of just config.transformer
        logger=core.logger,
        storage_container=storage,
        # Removed cross-container dependencies - each container should be self-contained
        shutdown_event=providers.Dependency(),  # Provided at runtime
    )


def create_container(config_dict: dict[str, Any]) -> MainContainer:
    """
    Create and configure the main DI container.

    Args:
        config_dict: Configuration dictionary

    Returns:
        Configured MainContainer instance
    """
    container = MainContainer()

    # Set default configuration values
    defaults = {
        "aws_region": "us-west-2",
        "dynamodb_endpoint": None,
        "s3_bucket_name": "lexgenius-dockets",
        "aws_access_key": "",
        "aws_secret_key": "",
        "deepseek_api_key": "",
        "openai_api_key": "",
        "llava_base_url": "http://localhost:11434",
        "llava_model": "llava",
        "llava_timeout": 60,
        "fb_ciphers": "TLS_AES_128_GCM_SHA256",
        "headless": False,
        "run_parallel": True,
        "timeout_ms": 60000,
        # Directory configuration
        "DATA_DIR": os.environ.get("LEXGENIUS_DATA_DIR", os.environ.get("DATA_DIR", "data")),
        "log_dir": None,  # Will be derived from DATA_DIR if not set
        # FB Ads NLP Configuration defaults
        "ner_model_name": "en_core_web_sm",
        "spacy_pipe_batch_size": 1000,
        "dynamodb_scan_workers": None,
        "ner_processing_workers": None,
        "text_fields": ["Title", "Body"],
        "cluster_min_k": 2,
        "cluster_max_k": 10,
        "cluster_k_step": 1,
        "cluster_output_enabled": True,
        "use_local_dynamodb": False,
        "dynamodb_update_workers": None,
        "storage": {},
        "pacer": {"headless": False, "run_parallel": True, "timeout_ms": 60000},
        "fb_ads": {},
        "transformer": {},
        "reports": {},
    }

    # Merge defaults with provided config
    final_config = {**defaults, **config_dict}

    # Configure the container
    container.config.from_dict(final_config)
    
    # Note: PACER container now uses its own PacerTransferService instead of transformer's TransferHandler

    return container


def wire_container(container: MainContainer, modules: list):
    """
    Wire the container to specified modules.

    Args:
        container: The container to wire
        modules: List of modules to wire
    """
    container.wire(modules=modules)


def unwire_container(container: MainContainer):
    """Unwire the container."""
    container.unwire()


# Export all key items
__all__ = [
    "CoreContainer",
    "MainContainer",
    "create_container",
    "wire_container",
    "unwire_container",
]
