"""
PACER Container Module

Provides PACER-related service providers using a component-based architecture
with facades, following the patterns of the transformer services.
"""
from dependency_injector import containers, providers

# --- Component Imports ---

# Analytics
from src.pacer.components.analytics.mdl_summarizer import MdlSummarizer

# Authentication
from src.pacer.components.authentication.credential_validator import CredentialValidator
from src.pacer.components.authentication.ecf_login_handler import ECFLoginHandler
from src.pacer.components.authentication.login_handler import <PERSON><PERSON>Handler
from src.pacer.components.authentication.session_manager import SessionManager

# Browser
from src.pacer._browser_components.context_factory import ContextFactory
from src.pacer._browser_components.playwright_manager import PlaywrightManager

# Case Processing
from src.pacer._case_processing_components.case_enricher import CaseEnricher
from src.pacer._case_processing_components.case_parser import CaseParser
from src.pacer._case_processing_components.case_transformer import CaseTransformer
from src.pacer._case_processing_components.case_validator import <PERSON>Validator
from src.pacer._case_processing_components.field_consistency_manager import FieldConsistencyManager
from src.pacer._case_processing_components.html_parser import HtmlParser
from src.pacer._case_processing_components.law_firm_corrector import LawFirmCorrector
from src.pacer._case_processing_components.transfer_info_processor import TransferInfoProcessor

# Config
from src.pacer._config_components.json_config_loader import JsonConfigLoader
from src.pacer._config_components.pacer_config_provider import PacerConfigProvider

# Download
from src.pacer._download_components.download_manager import DownloadManager
from src.pacer._download_components.download_validator import DownloadValidator
from src.pacer._download_components.file_downloader import FileDownloader
from src.pacer._download_components.s3_manager import S3Manager

# Export
from src.pacer._export_components.csv_exporter import CsvExporter

# File
from src.pacer._file_components.directory_manager import DirectoryManager
from src.pacer._file_components.file_manager import FileManager
from src.pacer._file_components.path_builder import PathBuilder

# Navigation
from src.pacer._navigation_components.element_locator import ElementLocator
from src.pacer._navigation_components.page_navigator import PageNavigator
from src.pacer._navigation_components.url_builder import UrlBuilder

# Processing
from src.pacer._processing_components.download_path_manager import DownloadPathManager
from src.pacer.jobs.job_processor import JobProcessor
from src.pacer._processing_components.report_processor import ReportProcessor
from src.pacer._processing_components.single_docket_processor import SingleDocketProcessor
from src.pacer._processing_components.workflow_orchestrator import WorkflowOrchestrator
from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager
from src.pacer.components.processing.sequential_docket_processor import SequentialDocketProcessor
from src.pacer.components.navigation.return_and_continue_manager import ReturnAndContinueManager
from src.pacer.components.processing.state_validator import StateValidator

# Query
from src.pacer._query_components.query_builder import QueryBuilder
from src.pacer._query_components.result_parser import ResultParser

# Report
from src.pacer._report_components.report_generator import ReportGenerator

# Transfer
from src.pacer.components.transfer.court_identifier import CourtIdentifier
from src.pacer.components.transfer.transfer_processor import TransferProcessor
from src.pacer.components.transfer.mdl_manager import MDLManager
from src.pacer.components.transfer.data_inheritor import DataInheritor

# Verification
from src.pacer._verification_components.case_verifier import CaseVerifier

# --- Facade Imports ---
from src.pacer.analytics_facade_service import AnalyticsFacadeService
from src.pacer.facades.authentication_facade import AuthenticationFacade
from src.pacer.services.browser_service import BrowserService as BrowserFacadeService
# Core Services (8 services architecture)
from src.pacer.services.configuration_service import ConfigurationService
from src.pacer.services.case_processing_service import CaseProcessingService
from src.pacer.services.relevance_service import RelevanceService
from src.pacer.core.verification.verification_service import VerificationService
from src.pacer.core.download_orchestration.download_orchestration_service import DownloadOrchestrationService
from src.pacer.core.file_operations.file_operations_service import FileOperationsService
from src.pacer.services.metrics_service import MetricsService as MetricsReportingService
from src.pacer.services.s3_service import S3Service as S3ManagementService
from src.pacer.docket_orchestrator_facade_service import DocketOrchestratorFacadeService
# Removed: DownloadOrchestrationFacadeService replaced by DownloadOrchestrationService (core service)
from src.pacer.export_facade_service import ExportFacadeService
from src.pacer.file_management_facade_service import FileManagementFacadeService
from src.pacer.html_processing_facade_service import HtmlProcessingFacadeService
from src.pacer.ignore_download_facade_service import IgnoreDownloadFacadeService
from src.pacer.navigation_facade_service import NavigationFacadeService
from src.pacer.query_facade_service import QueryFacadeService
# Legacy facade services for backward compatibility
from src.pacer.case_verification_facade_service import CaseVerificationFacadeService
from src.pacer.report_facade_service import ReportFacadeService
from src.pacer.row_processing_facade_service import RowProcessingFacadeService
from src.pacer.transfer_facade_service import TransferFacadeService

# --- Other Service Imports ---
from src.pacer.pacer_orchestrator_service import PacerOrchestratorService
from src.pacer.case_classification_facade_service import CaseClassificationFacadeService
from src.pacer.ignore_download_facade_service import IgnoreDownloadFacadeService
from src.pacer.interactive_service import PacerInteractiveService


class PacerContainer(containers.DeclarativeContainer):
    """Dependency injection container for the PACER services."""

    config = providers.Configuration()
    logger = providers.Dependency()
    storage_container = providers.DependenciesContainer()
    shutdown_event = providers.Dependency()

    # --- Component Providers ---

    # Analytics
    mdl_summarizer = providers.Singleton(MdlSummarizer, logger=logger, config=config)

    # Authentication
    credential_validator = providers.Singleton(CredentialValidator, logger=logger, config=config)
    ecf_login_handler = providers.Singleton(ECFLoginHandler, logger=logger, config=config)
    login_handler = providers.Singleton(LoginHandler, logger=logger, config=config)
    session_manager = providers.Singleton(SessionManager, logger=logger, config=config)

    # Browser
    playwright_manager = providers.Singleton(PlaywrightManager, logger=logger, config=config)
    context_factory = providers.Factory(
        ContextFactory,
        logger=logger,
        config=config,
        playwright=playwright_manager,
        headless=config.headless,
    )

    # Case Processing
    case_enricher = providers.Singleton(CaseEnricher, logger=logger, config=config)
    case_parser = providers.Singleton(CaseParser, logger=logger, config=config)
    case_transformer = providers.Singleton(CaseTransformer, logger=logger, config=config)
    case_validator = providers.Singleton(CaseValidator, logger=logger, config=config)
    field_consistency_manager = providers.Singleton(FieldConsistencyManager, logger=logger, config=config)
    html_parser = providers.Singleton(HtmlParser, logger=logger, config=config)
    law_firm_corrector = providers.Singleton(LawFirmCorrector, logger=logger, config=config)
    transfer_info_processor = providers.Singleton(TransferInfoProcessor, logger=logger, config=config)

    # Config
    json_config_loader = providers.Singleton(JsonConfigLoader, logger=logger, config=config)
    pacer_config_provider = providers.Singleton(PacerConfigProvider, json_loader=json_config_loader, logger=logger, config=config)

    # Download
    download_manager = providers.Singleton(DownloadManager, logger=logger, config=config, s3_storage=storage_container.s3_async_storage)
    download_validator = providers.Singleton(DownloadValidator, logger=logger, config=config)
    file_downloader = providers.Singleton(FileDownloader, logger=logger, config=config)
    s3_manager = providers.Singleton(S3Manager, logger=logger, config=config, s3_async_storage=storage_container.s3_async_storage)

    # Export
    csv_exporter = providers.Singleton(CsvExporter, logger=logger, config=config)

    # File
    path_builder = providers.Singleton(PathBuilder, logger=logger, config=config)
    directory_manager = providers.Singleton(DirectoryManager, path_builder=path_builder, logger=logger, config=config)
    file_manager = providers.Singleton(FileManager, logger=logger, config=config)
    
    # Navigation
    element_locator = providers.Singleton(ElementLocator, logger=logger, config=config)
    page_navigator = providers.Singleton(PageNavigator, element_locator=element_locator, logger=logger, config=config)
    url_builder = providers.Singleton(UrlBuilder, logger=logger, config=config)

    # Processing
    download_path_manager = providers.Singleton(DownloadPathManager, logger=logger, config=config)
    job_processor = providers.Singleton(JobProcessor, logger=logger, config=config)
    single_docket_processor = providers.Singleton(SingleDocketProcessor, logger=logger, config=config)

    # Query
    query_builder = providers.Singleton(QueryBuilder, logger=logger, config=config)
    result_parser = providers.Singleton(ResultParser, logger=logger, config=config)

    # Report
    report_generator = providers.Singleton(ReportGenerator, logger=logger, config=config)

    # Transfer components
    mdl_manager = providers.Singleton(
        MDLManager, 
        logger=logger, 
        config=config,
        district_court_repository=storage_container.district_courts_repository
    )
    data_inheritor = providers.Singleton(
        DataInheritor,
        logger=logger,
        config=config,
        pacer_repository=storage_container.pacer_repository
    )
    court_identifier = providers.Singleton(
        CourtIdentifier, 
        logger=logger, 
        config=config, 
        court_lookup=storage_container.court_lookup,
        gpt_interface=storage_container.openai_client,
        district_court_repository=storage_container.district_courts_repository
    )
    # Transfer processor with full functionality
    transfer_processor = providers.Singleton(
        TransferProcessor, 
        logger=logger, 
        config=config,
        court_identifier=court_identifier,
        mdl_manager=mdl_manager,
        data_inheritor=data_inheritor
    )

    # Verification
    case_verifier = providers.Singleton(CaseVerifier, logger=logger, config=config, pacer_repository=storage_container.pacer_repository)

    # --- Facade Providers ---

    analytics_facade = providers.Factory(AnalyticsFacadeService, logger=logger, config=config, mdl_summarizer=mdl_summarizer)
    authentication_facade = providers.Factory(AuthenticationFacade, logger=logger, config=config, login_handler=login_handler, ecf_login_handler=ecf_login_handler)
    browser_facade = providers.Factory(BrowserFacadeService, context_factory=context_factory, playwright_manager=playwright_manager, logger=logger, config=config)
    # Legacy facade services for backward compatibility
    configuration_facade = providers.Factory(ConfigurationService, config_provider=pacer_config_provider, logger=logger, config=config)
    case_processing_facade = providers.Factory(CaseProcessingService, logger=logger, config=config, case_validator=case_validator, case_parser=case_parser, case_enricher=case_enricher, case_transformer=case_transformer)
    case_verification_facade = providers.Factory(CaseVerificationFacadeService, logger=logger, config=config, case_verifier=case_verifier)
    # --- Core Services (8 services architecture) ---
    configuration_service = providers.Factory(ConfigurationService, config_provider=pacer_config_provider, logger=logger, config=config)
    case_processing_service = providers.Factory(CaseProcessingService, logger=logger, config=config, case_validator=case_validator, case_parser=case_parser, case_enricher=case_enricher, case_transformer=case_transformer)
    relevance_service = providers.Factory(RelevanceService, logger=logger, config=config)
    verification_service = providers.Factory(VerificationService, logger=logger, config=config, repository=storage_container.pacer_repository, file_manager=file_manager, case_verifier=case_verifier)
    download_orchestration_service = providers.Factory(DownloadOrchestrationService, logger=logger, config=config, download_validator=download_validator, file_downloader=file_downloader, download_manager=download_manager)
    file_operations_service = providers.Factory(FileOperationsService, logger=logger, config=config, directory_manager=directory_manager, file_manager=file_manager, path_builder=path_builder, csv_exporter=csv_exporter, s3_async_storage=storage_container.s3_async_storage)
    metrics_reporting_service = providers.Factory(MetricsReportingService, logger=logger, config=config)
    s3_management_service = providers.Factory(S3ManagementService, logger=logger, config=config)
    # Removed: download_orchestration_facade - replaced by download_orchestration_service (core service)
    export_facade = providers.Factory(ExportFacadeService, logger=logger, config=config, csv_exporter=csv_exporter)
    file_management_facade = providers.Factory(FileManagementFacadeService, logger=logger, config=config, directory_manager=directory_manager, file_manager=file_manager, path_builder=path_builder)
    html_processing_facade = providers.Factory(
        HtmlProcessingFacadeService, 
        logger=logger, 
        config=config, 
        html_parser=html_parser, 
        law_firm_corrector=law_firm_corrector, 
        s3_manager=s3_manager,  # Component interface for component operations
        s3_async_storage=storage_container.s3_async_storage,  # Direct service for HTML upload
        field_consistency_manager=field_consistency_manager, 
        transfer_info_processor=transfer_info_processor
    )
    
    # ReportProcessor with all dependencies (using core services)
    report_processor = providers.Singleton(
        ReportProcessor, 
        logger=logger, 
        config=config, 
        job_processor=job_processor,
        pacer_repository=storage_container.pacer_repository,
        file_manager=file_manager,
        case_processing_service=case_processing_service,
        download_orchestration_service=download_orchestration_service,
        file_operations_service=file_operations_service
    )
    navigation_facade = providers.Factory(NavigationFacadeService, logger=logger, config=config, url_builder=url_builder, element_locator=element_locator, page_navigator=page_navigator)
    query_facade = providers.Factory(QueryFacadeService, logger=logger, config=config, query_builder=query_builder, result_parser=result_parser)
    report_facade = providers.Factory(ReportFacadeService, logger=logger, config=config, report_generator=report_generator)
    row_processing_facade = providers.Factory(RowProcessingFacadeService, logger=logger, config=config, report_processor=report_processor, single_docket_processor=single_docket_processor)
    transfer_facade = providers.Factory(TransferFacadeService, logger=logger, config=config, transfer_processor=transfer_processor)

    # --- High-Level Services ---

    # Legacy services for backward compatibility
    ignore_download_service = providers.Singleton(IgnoreDownloadFacadeService, logger=logger, config=config)
    case_classification_service = providers.Singleton(CaseClassificationFacadeService, logger=logger, config=config, transfer_service=transfer_facade)

    docket_orchestrator_facade = providers.Factory(
        DocketOrchestratorFacadeService,
        logger=logger,
        config=config,
        html_processing_facade=html_processing_facade,
        relevance_service=relevance_service,
        case_classification_service=case_classification_service,
        case_verification_facade=case_verification_facade,
        download_orchestration_service=download_orchestration_service,
    )

    # Define workflow_orchestrator AFTER facades are defined
    workflow_orchestrator = providers.Singleton(
        WorkflowOrchestrator, 
        logger=logger, 
        config=config,
        authentication_facade=authentication_facade,
        navigation_facade=navigation_facade,
        row_processing_facade=row_processing_facade,
        report_facade=report_facade,
        file_service=file_management_facade,
        ignore_download_service=ignore_download_service,
    )

    # --- Sequential Workflow Components ---
    
    # Return and Continue Manager for navigation between dockets
    return_and_continue_manager = providers.Singleton(
        ReturnAndContinueManager,
        logger=logger,
        config=config,
        navigation_facade=navigation_facade
    )
    
    # State Validator for workflow state management
    state_validator = providers.Singleton(
        StateValidator,
        logger=logger,
        config=config
    )
    
    # Sequential Docket Processor for individual docket processing
    sequential_docket_processor = providers.Singleton(
        SequentialDocketProcessor,
        logger=logger,
        config=config,
        html_processing_facade=html_processing_facade,
        relevance_service=relevance_service,
        case_classification_service=case_classification_service,
        case_verification_facade=case_verification_facade,
        download_orchestration_service=download_orchestration_service,
        file_operations_service=file_operations_service,
        # Inject database storage dependencies
        async_dynamodb_storage=storage_container.async_dynamodb_storage,
        pacer_repository=storage_container.pacer_repository
    )
    
    # Sequential Workflow Manager with proper DI
    sequential_workflow_manager = providers.Singleton(
        SequentialWorkflowManager,
        logger=logger,
        config=config,
        navigation_facade=navigation_facade,
        docket_processor=sequential_docket_processor,
        state_validator=state_validator,
        return_manager=return_and_continue_manager,
        # CRITICAL FIX: Ensure ALL database dependencies are properly injected
        async_dynamodb_storage=storage_container.async_dynamodb_storage,
        pacer_repository=storage_container.pacer_repository,
        # Add court logger dependency for proper logging injection
        court_logger=providers.Dependency()
    )
    
    # Factory for creating database-enabled sequential processor with ALL dependencies
    database_enabled_sequential_processor = providers.Factory(
        SequentialWorkflowManager,
        logger=logger,
        config=config,
        navigation_facade=navigation_facade,
        docket_processor=sequential_docket_processor,
        state_validator=state_validator,
        return_manager=return_and_continue_manager,
        # CRITICAL: Ensure storage dependencies are properly injected using providers
        async_dynamodb_storage=storage_container.async_dynamodb_storage,
        pacer_repository=storage_container.pacer_repository,
        # Court logger will be provided at runtime
        court_logger=providers.Dependency()
    )

    # Enhanced Dependency Validation Factory - ensures ALL required dependencies are available
    @providers.Factory 
    def validated_sequential_workflow_factory(
        logger,
        config,
        navigation_facade,
        sequential_docket_processor,
        state_validator,
        return_and_continue_manager,
        storage_container
    ):
        """
        Factory to create SequentialWorkflowManager with validated dependencies.
        
        This factory ensures that ALL required dependencies are properly initialized
        and available before creating the SequentialWorkflowManager instance.
        
        Required Dependencies:
        1. PacerRepository - ALWAYS injected
        2. AsyncDynamoDBStorage - ALWAYS injected  
        3. S3AsyncStorage - ALWAYS injected
        4. Navigation and processing components
        
        Raises:
            RuntimeError: If any required dependency is missing or not initialized
        """
        # Enhanced storage dependency validation with detailed error reporting
        storage_errors = []
        storage_available = {}
        
        try:
            async_storage = storage_container.async_dynamodb_storage()
            storage_available['AsyncDynamoDBStorage'] = async_storage is not None
            if async_storage is None:
                storage_errors.append("AsyncDynamoDBStorage provider returned None")
        except Exception as e:
            storage_available['AsyncDynamoDBStorage'] = False
            storage_errors.append(f"AsyncDynamoDBStorage access failed: {e}")
        
        try:
            pacer_repo = storage_container.pacer_repository()
            storage_available['PacerRepository'] = pacer_repo is not None
            if pacer_repo is None:
                storage_errors.append("PacerRepository provider returned None")
        except Exception as e:
            storage_available['PacerRepository'] = False
            storage_errors.append(f"PacerRepository access failed: {e}")
        
        try:
            s3_storage = storage_container.s3_async_storage()
            storage_available['S3AsyncStorage'] = s3_storage is not None
            if s3_storage is None:
                storage_errors.append("S3AsyncStorage provider returned None")
        except Exception as e:
            storage_available['S3AsyncStorage'] = False
            storage_errors.append(f"S3AsyncStorage access failed: {e}")
        
        # Validate processing dependencies
        processing_available = {
            'NavigationFacade': navigation_facade is not None,
            'SequentialDocketProcessor': sequential_docket_processor is not None,
            'StateValidator': state_validator is not None,
            'ReturnAndContinueManager': return_and_continue_manager is not None
        }
        
        processing_errors = []
        for dep_name, available in processing_available.items():
            if not available:
                processing_errors.append(f"{dep_name} is None or not provided")
        
        # Check if we have at least one database dependency (as per SequentialWorkflowManager requirements)
        database_available = storage_available.get('PacerRepository', False) or storage_available.get('AsyncDynamoDBStorage', False)
        
        # Report dependency status
        if logger:
            logger.info("SequentialWorkflowManager dependency validation:")
            for dep_name, available in {**storage_available, **processing_available}.items():
                status = "✅" if available else "❌"
                logger.info(f"  {status} {dep_name}: {'Available' if available else 'Missing'}")
        
        # Collect all critical errors
        critical_errors = []
        
        if not database_available:
            critical_errors.append("No database dependencies available (need PacerRepository OR AsyncDynamoDBStorage)")
        
        if processing_errors:
            critical_errors.extend(processing_errors)
        
        # If we have critical errors, fail with detailed information
        if critical_errors:
            error_msg = "SequentialWorkflowManager dependency validation FAILED:\\n"
            for i, error in enumerate(critical_errors, 1):
                error_msg += f"  {i}. {error}\\n"
            
            if storage_errors:
                error_msg += "\\nStorage dependency errors:\\n"
                for error in storage_errors:
                    error_msg += f"  - {error}\\n"
            
            error_msg += "\\nThis indicates a dependency injection configuration problem."
            
            if logger:
                logger.error(error_msg)
            
            raise RuntimeError(error_msg)
        
        # Log successful dependency validation
        if logger:
            logger.info("✅ SequentialWorkflowManager dependency validation PASSED - creating instance")
        
        # Create and return SequentialWorkflowManager with validated dependencies
        return SequentialWorkflowManager(
            logger=logger,
            config=config,
            navigation_facade=navigation_facade,
            docket_processor=sequential_docket_processor,
            state_validator=state_validator,
            return_manager=return_and_continue_manager,
            # VALIDATED dependencies - at least one database dependency is guaranteed
            async_dynamodb_storage=storage_container.async_dynamodb_storage() if storage_available.get('AsyncDynamoDBStorage', False) else None,
            pacer_repository=storage_container.pacer_repository() if storage_available.get('PacerRepository', False) else None,
            # Court logger will be provided at runtime by the calling code
            court_logger=None  # Will be set by the DocketOrchestrator
        )
    
    # Court processing is now handled directly by core services
    # No longer needed as a separate facade

    interactive_service = providers.Factory(
        PacerInteractiveService,
        logger=logger,
        config=config,
        query_service=query_facade,
        export_facade=export_facade,
    )

    pacer_orchestrator = providers.Singleton(
        PacerOrchestratorService,
        logger=logger,
        config=config,
        browser_facade=browser_facade,
        # 8 Core Services
        configuration_service=configuration_service,
        case_processing_service=case_processing_service,
        relevance_service=relevance_service,
        verification_service=verification_service,
        download_orchestration_service=download_orchestration_service,
        file_operations_service=file_operations_service,
        metrics_reporting_service=metrics_reporting_service,
        s3_management_service=s3_management_service,
    )
