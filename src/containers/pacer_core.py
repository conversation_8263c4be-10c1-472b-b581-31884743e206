"""
PACER Core Services Container Module

Provides PACER-related service providers using the new 8 core services architecture.
This replaces the old facade-based container after the successful refactoring.
"""
from dependency_injector import containers, providers

# --- Component Imports ---

# Authentication
from src.pacer.components.authentication.credential_validator import CredentialValidator
from src.pacer.components.authentication.ecf_login_handler import ECFLoginHandler
from src.pacer.components.authentication.login_handler import <PERSON><PERSON><PERSON>andler
from src.pacer.components.authentication.session_manager import SessionManager

# Browser
from src.pacer.components.browser.context_factory import ContextFactory
from src.pacer.components.browser.playwright_manager import PlaywrightManager

# Case Processing
from src.pacer.components.case_processing.case_enricher import CaseEnricher
from src.pacer.components.case_processing.case_parser import CaseParser
from src.pacer.components.case_processing.case_transformer import CaseTransformer
from src.pacer.components.case_processing.case_validator import CaseValidator
from src.pacer.components.case_processing.field_consistency_manager import FieldConsistencyManager

# Config
from src.pacer.components.configuration.json_config_loader import JsonConfigLoader
from src.pacer.components.configuration.pacer_config_provider import PacerConfigProvider

# Download
from src.pacer.components.download.download_manager import DownloadManager
from src.pacer.components.download.download_validator import DownloadValidator
from src.pacer.components.download.file_downloader import FileDownloader

# Export
from src.pacer.components.export.csv_exporter import CsvExporter

# File
from src.pacer.components.file_operations.directory_manager import DirectoryManager
from src.pacer.components.file_operations.file_manager import FileManager
from src.pacer.components.file_operations.path_builder import PathBuilder

# Processing Components (Phase-Separated)
from src.pacer.components.processing.court_processor import CourtProcessor
from src.pacer.components.processing.docket_processor import DocketProcessor
from src.pacer.components.processing.row_processor import RowProcessor

# Verification
from src.pacer.components.verification.case_verifier import CaseVerifier

# --- FACADE SERVICES ---
# The REAL facade services for proper execution order
from src.pacer.services.court_processing_service import CourtProcessingFacadeService
from src.pacer.services.row_processing_service import RowProcessingFacadeService  
from src.pacer.facades.docket_orchestrator import DocketOrchestrator
from src.pacer.facades.navigation_facade import NavigationFacade
from src.pacer.services.html_processing_service import HtmlProcessingFacadeService

# --- HTML PROCESSING COMPONENTS ---
# Required for HtmlProcessingFacadeService (using correct import paths)
from src.pacer.components.case_processing.field_consistency_manager import FieldConsistencyManager
from src.pacer.components.case_processing.html_parser import HtmlParser
from src.pacer.components.case_processing.law_firm_corrector import LawFirmCorrector
from src.pacer.components.case_processing.transfer_info_processor import TransferInfoProcessor
from src.pacer.components.download.s3_manager import S3Manager

# Services needed by processor components and core services
from src.pacer.services.browser_service import BrowserService
from src.pacer.services.configuration_service import ConfigurationService
from src.pacer.services.case_processing_service import CaseProcessingService
from src.pacer.services.relevance_service import RelevanceService
from src.pacer.services.classification_service import ClassificationService
from src.pacer.services.verification_service import VerificationService
from src.pacer.services.file_operations_service import FileOperationsService

# Core Services
# Core services removed - _core_services directory deleted
# Import DownloadManager for backward compatibility
from src.pacer.components.download.download_manager import DownloadManager

# Create placeholder classes for removed services
class MetricsReportingService:
    def __init__(self, *args, **kwargs):
        pass

# Import the real S3ManagementService bridge
from src.pacer._core_services.s3_management.s3_management_service import S3ManagementService

# Also import S3AsyncStorage for direct usage if needed
from src.infrastructure.storage.s3_async import S3AsyncStorage
# PacerOrchestratorService - re-enabled for MainServiceFactory
from src.services.pacer.pacer_orchestrator_service import PacerOrchestratorService

# Sequential Workflow Components
from src.pacer.components.processing.sequential_docket_processor import SequentialDocketProcessor
from src.pacer.components.processing.sequential_workflow_manager import SequentialWorkflowManager
from src.pacer.components.navigation.return_and_continue_manager import ReturnAndContinueManager
from src.pacer.components.processing.state_validator import StateValidator

# --- Browser Service (compatibility layer) ---
# Note: BrowserFacadeService was removed during refactoring
# This is kept for compatibility but will be None
BrowserFacadeService = None


class PacerCoreContainer(containers.DeclarativeContainer):
    """Dependency injection container for the PACER core services architecture with 9 core services."""

    config = providers.Configuration()
    logger = providers.Dependency()
    storage_container = providers.DependenciesContainer()
    shutdown_event = providers.Dependency()

    # --- Component Providers ---

    # Authentication
    credential_validator = providers.Singleton(CredentialValidator, logger=logger, config=config)
    ecf_login_handler = providers.Singleton(ECFLoginHandler, logger=logger, config=config)
    login_handler = providers.Singleton(LoginHandler, logger=logger, config=config)
    session_manager = providers.Singleton(SessionManager, logger=logger, config=config)

    # Browser
    playwright_manager = providers.Singleton(PlaywrightManager, logger=logger, config=config)
    context_factory = providers.Factory(
        ContextFactory,
        logger=logger,
        config=config,
        playwright=playwright_manager,
        headless=config.headless,
    )

    # Case Processing
    case_enricher = providers.Singleton(CaseEnricher, logger=logger, config=config)
    case_parser = providers.Singleton(CaseParser, logger=logger, config=config)
    case_transformer = providers.Singleton(CaseTransformer, logger=logger, config=config)
    case_validator = providers.Singleton(CaseValidator, logger=logger, config=config)

    # Config
    json_config_loader = providers.Singleton(JsonConfigLoader, logger=logger, config=config)
    pacer_config_provider = providers.Singleton(PacerConfigProvider, json_loader=json_config_loader, logger=logger, config=config)

    # Download
    download_manager = providers.Singleton(DownloadManager, logger=logger, config=config, s3_storage=storage_container.s3_async_storage)
    download_validator = providers.Singleton(DownloadValidator, logger=logger, config=config)
    file_downloader = providers.Singleton(FileDownloader, logger=logger, config=config)

    # Export
    csv_exporter = providers.Singleton(CsvExporter, logger=logger, config=config)

    # File
    path_builder = providers.Singleton(PathBuilder, logger=logger, config=config)
    directory_manager = providers.Singleton(DirectoryManager, path_builder=path_builder, logger=logger, config=config)
    file_manager = providers.Singleton(FileManager, logger=logger, config=config)

    # Verification
    case_verifier = providers.Singleton(CaseVerifier, logger=logger, config=config, pacer_repository=storage_container.pacer_repository, file_manager=file_manager)

    # --- HTML Processing Components ---
    # Components required for HtmlProcessingFacadeService
    field_consistency_manager = providers.Singleton(FieldConsistencyManager, logger=logger, config=config)
    html_parser = providers.Singleton(HtmlParser, logger=logger, config=config)
    law_firm_corrector = providers.Singleton(LawFirmCorrector, logger=logger, config=config)
    transfer_info_processor = providers.Singleton(TransferInfoProcessor, logger=logger, config=config)
    s3_manager = providers.Singleton(S3Manager, logger=logger, config=config, s3_async_storage=storage_container.s3_async_storage)

    # --- Core Services (9 services architecture) ---

    configuration_service = providers.Factory(
        ConfigurationService, 
        logger=logger, 
        config=config
    )

    browser_service = providers.Factory(
        BrowserService,
        logger=logger,
        config=config
    )

    # --- Processor Component Services ---
    # These services are used by the processor components
    
    pacer_browser_service = providers.Factory(
        BrowserService,
        logger=logger,
        config=config
    )
    
    pacer_configuration_service = providers.Factory(
        ConfigurationService,
        logger=logger,
        config=config
    )
    
    # Core Infrastructure Services (defined before dependent services)
    metrics_reporting_service = providers.Factory(
        MetricsReportingService, 
        logger=logger, 
        config=config
    )
    
    s3_management_service = providers.Factory(
        S3ManagementService, 
        logger=logger, 
        config=config,
        config_service=configuration_service
    )
    
    pacer_case_processing_service = providers.Factory(
        CaseProcessingService,
        logger=logger,
        config=config,
        s3_service=s3_management_service
    )
    
    pacer_relevance_service = providers.Factory(
        RelevanceService,
        logger=logger,
        config=config
    )
    
    pacer_classification_service = providers.Factory(
        ClassificationService,
        logger=logger,
        config=config
    )
    
    pacer_verification_service = providers.Factory(
        VerificationService,
        logger=logger,
        config=config
    )
    
    pacer_file_operations_service = providers.Factory(
        FileOperationsService,
        logger=logger
        # FileOperationsService doesn't accept config parameter
    )

    case_processing_service = providers.Factory(
        CaseProcessingService, 
        case_validator=case_validator, 
        case_parser=case_parser, 
        case_enricher=case_enricher, 
        case_transformer=case_transformer,
        logger=logger, 
        config=config
    )

    relevance_service = providers.Factory(
        RelevanceService, 
        logger=logger, 
        config=config
    )

    verification_service = providers.Factory(
        VerificationService, 
        logger=logger, 
        config=config,
        repository=storage_container.pacer_repository, 
        file_manager=file_manager, 
        case_verifier=case_verifier
    )

    download_orchestration_service = providers.Factory(
        DownloadManager, 
        download_validator=download_validator, 
        file_downloader=file_downloader, 
        download_manager=download_manager,
        logger=logger, 
        config=config
    )

    file_operations_service = providers.Factory(
        FileOperationsService, 
        config_service=configuration_service,
        s3_service=None,  # Can be added if needed
        logger=logger
    )


    # --- Phase-Separated Processor Components ---
    
    court_processor = providers.Factory(
        CourtProcessor,
        browser_service=pacer_browser_service,
        configuration_service=pacer_configuration_service,
        logger=logger,
        config=config
    )
    
    docket_processor = providers.Factory(
        DocketProcessor,
        case_processing_service=pacer_case_processing_service,
        relevance_service=pacer_relevance_service,
        logger=logger,
        config=config
    )
    
    row_processor = providers.Factory(
        RowProcessor,
        relevance_service=pacer_relevance_service,
        classification_service=pacer_classification_service,
        verification_service=pacer_verification_service,
        logger=logger,
        config=config
    )
    
    # --- Facade Services (Optional Coordination Layer) ---
    
    court_processing_facade = providers.Factory(
        CourtProcessingFacadeService,
        logger=logger,
        config=config
    )
    
    row_processing_facade = providers.Factory(
        RowProcessingFacadeService,
        logger=logger,
        config=config
    )
    
    navigation_facade = providers.Factory(
        NavigationFacade,
        logger=logger,
        config=config
    )
    
    docket_orchestrator_facade = providers.Factory(
        DocketOrchestrator,
        court_processor=court_processor,
        docket_processor=docket_processor,
        row_processor=row_processor,
        download_manager=download_manager,
        file_operations_service=pacer_file_operations_service,
        navigation_facade=navigation_facade,  # Add the missing navigation facade
        pacer_repository=storage_container.pacer_repository,  # Add DynamoDB repository
        async_dynamodb_storage=storage_container.async_dynamodb_storage,  # Add DynamoDB storage
        logger=logger,
        config=config
    )
    
    # Import WorkflowOrchestrator (the new DI version)
    from src.pacer.components.processing.workflow_orchestrator_di import WorkflowOrchestrator
    
    # WorkflowOrchestrator with proper DI and storage dependencies
    workflow_orchestrator = providers.Factory(
        WorkflowOrchestrator,
        docket_orchestrator=docket_orchestrator_facade,  # Inject the DocketOrchestrator
        authentication_facade=None,  # Optional, for backward compatibility
        navigation_facade=navigation_facade,  # Pass navigation facade
        report_facade=None,  # Optional
        row_facade=row_processing_facade,  # Optional
        file_service=file_operations_service,  # Optional
        ignore_download_service=None,  # Optional
        relevance_service=relevance_service,  # Optional
        artifact_checker=None,  # Will be created if needed
        # CRITICAL: Pass storage dependencies to WorkflowOrchestrator
        pacer_repository=storage_container.pacer_repository,
        async_dynamodb_storage=storage_container.async_dynamodb_storage,
        logger=logger,
        config=config
    )
    
    # Note: CaseProcessingFacadeService not found in facades directory
    # case_processing_facade = providers.Factory(
    #     CaseProcessingFacadeService,
    #     logger=logger,
    #     config=config
    # )
    
    html_processing_facade = providers.Factory(
        HtmlProcessingFacadeService,
        logger=logger,
        config=config,
        html_parser=html_parser,
        law_firm_corrector=law_firm_corrector,
        s3_manager=s3_manager,
        field_consistency_manager=field_consistency_manager,
        transfer_info_processor=transfer_info_processor
    )

    # --- Orchestrator Services ---

    docket_processing_orchestrator = providers.Factory(
        DocketOrchestrator,
        # ALL 9 CORE SERVICES (SAME AS BEFORE)
        configuration_service=configuration_service,
        browser_service=browser_service,
        case_processing_service=case_processing_service,
        relevance_service=relevance_service,
        verification_service=verification_service,
        download_orchestration_service=download_orchestration_service,
        file_operations_service=file_operations_service,
        metrics_reporting_service=metrics_reporting_service,
        s3_management_service=s3_management_service,
        # PROCESSOR COMPONENTS (PHASE-SEPARATED)
        court_processor=court_processor,
        docket_processor=docket_processor,
        row_processor=row_processor,
        # OPTIONAL FACADE SERVICES FOR COORDINATION
        court_processing_facade=court_processing_facade,
        row_processing_facade=row_processing_facade,
        docket_orchestrator_facade=docket_orchestrator_facade,
        logger=logger,
        config=config
    )

    # --- Browser Service (temporary compatibility) ---
    # Note: browser_facade set to None as BrowserFacadeService was removed during refactoring
    browser_facade = None

    # --- Sequential Workflow Components (CRITICAL FIX) ---
    
    # State Validator for workflow state management
    state_validator = providers.Singleton(
        StateValidator,
        logger=logger,
        config=config
    )
    
    # Return and Continue Manager for navigation between dockets
    return_and_continue_manager = providers.Singleton(
        ReturnAndContinueManager,
        logger=logger,
        config=config,
        navigation_facade=navigation_facade
    )
    
    # Sequential Docket Processor for individual docket processing
    sequential_docket_processor = providers.Singleton(
        SequentialDocketProcessor,
        logger=logger,
        config=config,
        # Note: SequentialDocketProcessor uses injected services, not direct storage dependencies
        html_parser_service=html_processing_facade,
        relevance_service=relevance_service,
        s3_service=s3_management_service
    )
    
    # Sequential Workflow Manager with proper DI (CRITICAL FOR STORAGE INJECTION)
    sequential_workflow_manager = providers.Singleton(
        SequentialWorkflowManager,
        logger=logger,
        config=config,
        navigation_facade=navigation_facade,
        docket_processor=sequential_docket_processor,
        state_validator=state_validator,
        return_manager=return_and_continue_manager,
        # CRITICAL FIX: Ensure ALL database dependencies are properly injected
        async_dynamodb_storage=storage_container.async_dynamodb_storage,
        pacer_repository=storage_container.pacer_repository,
        # Court logger is optional and will be set at runtime
        court_logger=None
    )
    
    # Validated Sequential Workflow Factory - ensures ALL required dependencies are available
    verified_sequential_workflow_factory = providers.Factory(
        SequentialWorkflowManager,
        logger=logger,
        config=config,
        navigation_facade=navigation_facade,
        docket_processor=sequential_docket_processor,
        state_validator=state_validator,
        return_manager=return_and_continue_manager,
        # CRITICAL FIX: Ensure ALL database dependencies are properly injected
        async_dynamodb_storage=storage_container.async_dynamodb_storage,
        pacer_repository=storage_container.pacer_repository,
        # Court logger is optional and will be set at runtime
        court_logger=None
    )

    # --- Main Orchestrator ---
    # Re-enabled for MainServiceFactory compatibility
    pacer_orchestrator = providers.Singleton(
        PacerOrchestratorService,
        workflow_orchestrator=workflow_orchestrator,  # Inject the domain workflow orchestrator
        logger=logger
    )