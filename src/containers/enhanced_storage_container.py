"""
Enhanced Storage Container Module with S3 Service Injection Fix

This module extends the base StorageContainer with enhanced S3 service injection
capabilities for HTML processing components.
"""

from dependency_injector import containers, providers

# Import storage services
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.infrastructure.storage.s3_async import S3AsyncStorage
from src.repositories.district_courts_repository import DistrictCourtsRepository
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.repositories.fb_image_hash_repository import FBImageHashRepository
from src.repositories.law_firms_repository import LawFirmsRepository
from src.repositories.pacer_dockets_repository import PacerDocketsRepository

# Import repositories
from src.repositories.pacer_repository import PacerRepository
from src.utils.law_firm import LawFirmNameHandler
from src.infrastructure.external.openai_client import OpenAIClient

# Import enhanced HTML services
from src.services.html.html_service_factory_enhanced import EnhancedHtmlServiceFactory
from src.pacer.utils.s3_injection_helper import S3InjectionHelper


class EnhancedStorageContainer(containers.DeclarativeContainer):
    """Enhanced container for storage services with improved S3 injection."""

    # Configuration and dependencies
    config = providers.Configuration()
    logger = providers.Dependency()
    aws_region = providers.Dependency()
    aws_access_key = providers.Dependency()
    aws_secret_key = providers.Dependency()
    dynamodb_endpoint = providers.Dependency()
    s3_bucket_name = providers.Dependency()

    # Storage Services
    async_dynamodb_storage = providers.Singleton(
        AsyncDynamoDBStorage,
        config=providers.Dict(
            {
                "aws_region": aws_region,
                "dynamodb_endpoint": dynamodb_endpoint,
            }
        ),
        logger=logger,
    )

    s3_async_storage = providers.Singleton(
        S3AsyncStorage,
        logger=logger,
        bucket_name=s3_bucket_name,
        aws_access_key=aws_access_key,
        aws_secret_key=aws_secret_key,
        aws_region=aws_region,
        disable_versioning=True,  # Permanently disable S3 versioning to ensure overwrite behavior
    )

    # Repositories
    pacer_repository = providers.Singleton(
        PacerRepository, storage=async_dynamodb_storage, logger=logger
    )

    fb_archive_repository = providers.Singleton(
        FBArchiveRepository, storage=async_dynamodb_storage, logger=logger
    )

    law_firms_repository = providers.Singleton(
        LawFirmsRepository, storage=async_dynamodb_storage, logger=logger
    )

    district_courts_repository = providers.Singleton(
        DistrictCourtsRepository, storage=async_dynamodb_storage, logger=logger
    )

    fb_image_hash_repository = providers.Singleton(
        FBImageHashRepository, storage=async_dynamodb_storage, logger=logger
    )

    pacer_dockets_repository = providers.Singleton(
        PacerDocketsRepository, storage=async_dynamodb_storage, logger=logger
    )

    # Session storage for authentication
    session_storage = providers.Singleton(
        dict  # Simple in-memory session storage for now
    )

    # Court lookup for transfer processing
    court_lookup = providers.Singleton(
        dict  # Simple in-memory court lookup for now
    )
    
    # OpenAI client for AI-powered operations (basic placeholder)
    openai_client = providers.Singleton(
        OpenAIClient,
        api_key="placeholder",  # Should be configured via environment
        logger=logger,
        config=config
    )

    # Utility services
    law_firm_handler = providers.Singleton(LawFirmNameHandler)
    
    # Factory methods for database initialization
    @providers.Factory
    def create_initialized_storage(storage, logger):
        """Factory to create and initialize AsyncDynamoDBStorage"""
        async def _initialize_storage():
            if not hasattr(storage, '_initialized') or not storage._initialized:
                await storage.initialize()
            return storage
        return _initialize_storage()
    
    @providers.Factory
    def create_database_services(async_dynamodb_storage, pacer_repository, logger):
        """Factory to create database services bundle with proper initialization"""
        return {
            'async_storage': async_dynamodb_storage,
            'pacer_repo': pacer_repository,
            'logger': logger
        }
    
    # CRITICAL FIX: Enhanced S3 Service Bundle for HTML Processing
    s3_service_bundle = providers.Factory(
        S3InjectionHelper.create_s3_service_bundle,
        s3_async_storage=s3_async_storage,
        logger=logger
    )
    
    # Enhanced HTML Processing Bundle with guaranteed S3 injection
    @providers.Factory
    def create_enhanced_html_processing_bundle(s3_async_storage, pacer_repository, logger, config):
        """Factory to create enhanced HTML processing service bundle with S3 support"""
        return EnhancedHtmlServiceFactory.create_html_processing_bundle_with_dependencies(
            logger=logger,
            config=config or {},
            s3_async_storage=s3_async_storage,
            pacer_db=pacer_repository,
            court_id=None  # Will be set at runtime
        )
    
    # Factory for creating HTML Processing Orchestrator with S3 injection
    @providers.Factory 
    def create_html_processing_orchestrator_with_s3(s3_async_storage, logger, config):
        """Factory to create HTML processing orchestrator with guaranteed S3 injection"""
        return EnhancedHtmlServiceFactory.create_html_processing_orchestrator_with_s3_injection(
            logger=logger,
            config=config or {},
            s3_async_storage=s3_async_storage,
            court_id=None,  # Will be set at runtime
            html_data_updater=None,  # Can be injected later
            enhanced=True
        )
    
    # Factory for creating Data Updater with S3 support
    @providers.Factory
    def create_data_updater_with_s3(s3_async_storage, pacer_repository, logger, config):
        """Factory to create DataUpdaterService with S3 support"""
        return EnhancedHtmlServiceFactory.create_data_updater(
            logger=logger,
            config=config or {},
            s3_manager=s3_async_storage,
            pacer_db=pacer_repository
        )
    
    # Factory for validating S3 injection in components
    @providers.Factory
    def validate_s3_injection(component, logger):
        """Factory to validate S3 injection in components"""
        return S3InjectionHelper.verify_s3_injection(component, logger)
    
    # Complete HTML processing service factory with all dependencies
    @providers.Factory
    def create_complete_html_processing_service(
        s3_async_storage, 
        pacer_repository, 
        logger, 
        config
    ):
        """Create a complete HTML processing service with all dependencies properly injected"""
        # Create the enhanced bundle
        html_bundle = EnhancedHtmlServiceFactory.create_html_processing_bundle_with_dependencies(
            logger=logger,
            config=config or {},
            s3_async_storage=s3_async_storage,
            pacer_db=pacer_repository,
            court_id=None
        )
        
        # Validate S3 injection
        orchestrator = html_bundle['html_processing_orchestrator']
        s3_validation = S3InjectionHelper.verify_s3_injection(orchestrator, logger)
        
        # Log successful creation
        if logger:
            logger.info("Complete HTML processing service created", extra={
                'has_s3_async_storage': orchestrator.s3_async_storage is not None,
                's3_validation_passed': s3_validation,
                'service_type': type(orchestrator).__name__,
                'bundle_components': list(html_bundle.keys())
            })
        
        return {
            **html_bundle,
            's3_validation_passed': s3_validation,
            'creation_timestamp': __import__('datetime').datetime.now().isoformat()
        }