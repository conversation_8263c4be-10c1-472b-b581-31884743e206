import asyncio
import logging
import os

from src.config_models.base import WorkflowConfig
from src.config_models.utils import convert_datetime_fields_to_strings
from src.containers.core import MainContainer, create_container
from src.services.orchestration.fb_ads_orchestrator import FbAdsOrchestrator
from src.services.orchestration.processing_orchestrator import ProcessingOrchestrator
from src.services.orchestration.scraping_orchestrator import ScrapingOrchestrator
from src.services.orchestration.upload_orchestrator import UploadOrchestrator
from src.services.pacer.pacer_orchestrator_service import PacerOrchestratorService
from src.services.reports.reports_orchestrator_service import ReportsOrchestratorService
# PACER refactoring: Import DocketOrchestrator directly
from src.pacer.facades.docket_orchestrator import DocketOrchestrator


class MainServiceFactory:
    """Factory for creating services using dependency injection container."""

    def __init__(
        self, config: WorkflowConfig, shutdown_event: asyncio.Event | None = None
    ):
        self.config = config
        self.shutdown_event = shutdown_event
        self.logger = logging.getLogger(__name__)
        self._container: MainContainer | None = None

        self.logger.info(
            f"MainServiceFactory initialized with config: {config.config_name if hasattr(config, 'config_name') else 'Unknown config'}"
        )
        self.logger.info("🚀 Using dependency-injector framework")

    def _prepare_config_dict(self) -> dict:
        """Prepare configuration dictionary from WorkflowConfig."""
        # Convert config to dictionary
        if hasattr(self.config, "model_dump"):
            # Pydantic model
            config_dict = self.config.model_dump()
        elif isinstance(self.config, dict):
            # Already a dictionary
            config_dict = self.config.copy()
        else:
            # Object with attributes
            config_dict = self.config.__dict__

        # Add environment variables
        config_dict.update(
            {
                "aws_region": os.getenv("AWS_REGION", "us-west-2"),
                "aws_access_key": os.getenv("AWS_ACCESS_KEY")
                or os.getenv("AWS_ACCESS_KEY_ID", ""),
                "aws_secret_key": os.getenv("AWS_SECRET_KEY")
                or os.getenv("AWS_SECRET_ACCESS_KEY", ""),
                "dynamodb_endpoint": os.getenv("DYNAMODB_ENDPOINT"),
                "s3_bucket_name": os.getenv("S3_BUCKET_NAME", "lexgenius-data"),
                "deepseek_api_key": os.getenv("DEEPSEEK_API_KEY", ""),
                "openai_api_key": os.getenv("OPENAI_API_KEY", ""),
                "llava_base_url": os.getenv("LLAVA_BASE_URL", "http://localhost:11434"),
                "llava_model": os.getenv("LLAVA_MODEL", "llava"),
                "llava_timeout": int(os.getenv("LLAVA_TIMEOUT", "60")),
                "fb_ciphers": os.getenv("FB_CIPHERS", "TLS_AES_128_GCM_SHA256"),
                "username_prod": os.getenv("PACER_USERNAME_PROD") or os.getenv("PACER_USERNAME"),
                "password_prod": os.getenv("PACER_PASSWORD_PROD") or os.getenv("PACER_PASSWORD"),
            }
        )

        # Add root-level configurations for PACER container dependency injection
        config_dict["headless"] = self.config.headless
        config_dict["run_parallel"] = getattr(self.config, 'run_parallel', True)
        config_dict["timeout_ms"] = getattr(self.config, 'timeout_ms', 60000)
        
        # DEBUG: Log MainServiceFactory config transformation
        self.logger.warning(f"🔍 DEBUG FACTORY: self.config.headless = {getattr(self.config, 'headless', 'NOT_FOUND')}")
        self.logger.warning(f"🔍 DEBUG FACTORY: self.config.run_parallel = {getattr(self.config, 'run_parallel', 'NOT_FOUND')}")
        self.logger.warning(f"🔍 DEBUG FACTORY: config_dict['headless'] = {config_dict.get('headless', 'NOT_FOUND')}")
        self.logger.warning(f"🔍 DEBUG FACTORY: config_dict['run_parallel'] = {config_dict.get('run_parallel', 'NOT_FOUND')}")

        # Add nested configs if not present
        config_dict.setdefault(
            "pacer",
            {
                "workflow": config_dict.get("pacer", {}),
                "browser": {"headless": self.config.headless},
                "username_prod": config_dict.get("username_prod"),
                "password_prod": config_dict.get("password_prod"),
                # CRITICAL FIX: Add root-level config values to pacer sub-config
                "headless": config_dict.get("headless", False),
                "run_parallel": config_dict.get("run_parallel", True),
                "timeout_ms": config_dict.get("timeout_ms", 60000),
            },
        )
        
        # DEBUG: Log pacer sub-config
        self.logger.warning(f"🔍 DEBUG FACTORY: config_dict['pacer'] = {config_dict.get('pacer', {})}")
        
        config_dict.setdefault("storage", {})
        config_dict.setdefault("fb_ads", {})
        config_dict.setdefault("transformer", {})
        config_dict.setdefault("reports", {})

        # Inject project root and directories
        project_root = os.environ.get("LEXGENIUS_PROJECT_ROOT", os.getcwd())
        config_dict["project_root"] = project_root
        config_dict.setdefault("directories", {})
        config_dict["directories"]["base_dir"] = project_root

        # Convert datetime fields and generate iso_date
        config_dict = convert_datetime_fields_to_strings(config_dict)

        return config_dict

    async def __aenter__(self):
        """Enhanced async context entry with comprehensive validation and error handling."""
        self.logger.info("Entering MainServiceFactory async context")
        
        try:
            # Validate configuration before proceeding
            self._validate_configuration()
            
            # Create and configure container with enhanced error handling
            self.logger.info("Preparing configuration dictionary for DI container")
            config_dict = self._prepare_config_dict()
            
            # Validate prepared config
            self._validate_config_dict(config_dict)
            
            self.logger.info("Creating dependency injection container")
            self._container = create_container(config_dict)
            
            if not self._container:
                raise RuntimeError("Failed to create DI container - container is None")
            
            # CRITICAL: Validate that storage dependencies are properly registered
            self._validate_storage_dependencies()
            
            # Enhanced module wiring with validation
            modules_to_wire = [
                "src.pacer",
                "src.pacer.components.processing",
                "src.pacer.facades",
                "src.services.fb_ads", 
                "src.services.transformer",
                "src.services.reports",
                "src.services.orchestration",
                "src.services.ai",
                "src.services.html",
                "src.services.document",
                "src.services.uploader",
                "src.services.infrastructure",
                "src.services.monitoring",
                "src.services.district_courts",
                "src.services.law_firms",
                "src.services.pacer_dockets",
                "src.services.fb_archive",
                "src.containers",
                "src.repositories",
                "src.infrastructure.storage",
            ]
            
            self.logger.info(f"Wiring DI container to {len(modules_to_wire)} modules")
            
            try:
                self._container.wire(modules=modules_to_wire)
                self.logger.info("DI Container wiring completed successfully")
            except Exception as wiring_error:
                self.logger.error(f"Container wiring failed: {wiring_error}", exc_info=True)
                # Attempt to unwire any partially wired modules
                try:
                    self._container.unwire()
                except Exception as unwire_error:
                    self.logger.error(f"Failed to unwire after wiring error: {unwire_error}")
                raise RuntimeError(f"DI Container wiring failed: {wiring_error}") from wiring_error

            # Enhanced resource initialization with validation
            self.logger.info("Initializing container resources")
            try:
                init_result = self._container.init_resources()
                if init_result is not None:
                    await init_result
                self.logger.info("Container resource initialization completed")
            except Exception as init_error:
                self.logger.error(f"Resource initialization failed: {init_error}", exc_info=True)
                # Clean up partially initialized container
                await self._cleanup_container()
                raise RuntimeError(f"Resource initialization failed: {init_error}") from init_error
            
            # Validate container state after initialization
            self._validate_container_state()
            
            # CRITICAL: Validate dependencies are accessible for PACER components
            self._validate_pacer_dependencies()
            
            self.logger.info("MainServiceFactory initialization completed successfully")
            return self
            
        except Exception as setup_error:
            self.logger.critical(f"MainServiceFactory setup failed: {setup_error}", exc_info=True)
            # Ensure cleanup happens even if setup fails
            await self._cleanup_container()
            raise RuntimeError(f"MainServiceFactory initialization failed: {setup_error}") from setup_error
    
    def _validate_configuration(self):
        """Validate the factory configuration before initialization."""
        if not self.config:
            raise ValueError("Configuration is required but not provided")
        
        # Check for required configuration attributes
        required_attrs = []  # Add specific requirements if needed
        for attr in required_attrs:
            if not hasattr(self.config, attr):
                raise ValueError(f"Required configuration attribute missing: {attr}")
        
        self.logger.debug("Configuration validation passed")
    
    def _validate_config_dict(self, config_dict):
        """Validate the prepared configuration dictionary."""
        if not isinstance(config_dict, dict):
            raise ValueError(f"Config dict must be a dictionary, got {type(config_dict)}")
        
        # Validate critical configuration sections
        critical_sections = ["pacer", "storage", "directories"]
        for section in critical_sections:
            if section not in config_dict:
                self.logger.warning(f"Critical configuration section missing: {section}")
        
        # Validate environment variables are loaded
        if not config_dict.get("project_root"):
            raise ValueError("Project root not configured")
            
        self.logger.debug("Configuration dictionary validation passed")
    
    def _validate_container_state(self):
        """Validate the container state after initialization."""
        if not self._container:
            raise RuntimeError("Container is not initialized")
        
        # Add specific container validation if needed
        self.logger.debug("Container state validation passed")
    
    def _validate_storage_dependencies(self):
        """Validate that storage dependencies are properly registered."""
        if not self._container:
            raise RuntimeError("Container must be initialized before validating storage dependencies")
        
        # Validate storage container exists
        if not hasattr(self._container, 'storage'):
            raise RuntimeError("Storage container not found in main container")
        
        storage_container = self._container.storage
        
        # Validate critical storage dependencies
        required_storage_deps = {
            'async_dynamodb_storage': 'AsyncDynamoDBStorage',
            'pacer_repository': 'PacerRepository', 
            's3_async_storage': 'S3AsyncStorage'
        }
        
        for dep_name, dep_desc in required_storage_deps.items():
            if not hasattr(storage_container, dep_name):
                raise RuntimeError(f"Required storage dependency missing: {dep_desc} ({dep_name})")
            
            # Try to access the dependency to ensure it's properly configured
            try:
                dependency = getattr(storage_container, dep_name)
                if dependency is None:
                    raise RuntimeError(f"Storage dependency {dep_desc} is None")
                self.logger.debug(f"✅ Storage dependency validated: {dep_desc}")
            except Exception as e:
                raise RuntimeError(f"Failed to access storage dependency {dep_desc}: {e}")
        
        self.logger.info("✅ All storage dependencies validated successfully")
    
    def _validate_pacer_dependencies(self):
        """Validate that PACER dependencies can access storage components."""
        if not self._container:
            raise RuntimeError("Container must be initialized before validating PACER dependencies")
        
        # Validate PACER container exists
        if not hasattr(self._container, 'pacer'):
            raise RuntimeError("PACER container not found in main container")
        
        pacer_container = self._container.pacer
        
        # Test critical PACER dependencies that require storage
        critical_pacer_deps = [
            'sequential_workflow_manager',
            'sequential_docket_processor', 
            'verified_sequential_workflow_factory'
        ]
        
        for dep_name in critical_pacer_deps:
            if hasattr(pacer_container, dep_name):
                try:
                    # Don't instantiate, just verify the provider exists
                    dependency_provider = getattr(pacer_container, dep_name)
                    if dependency_provider is None:
                        self.logger.warning(f"⚠️ PACER dependency provider is None: {dep_name}")
                    else:
                        self.logger.debug(f"✅ PACER dependency provider validated: {dep_name}")
                except Exception as e:
                    self.logger.warning(f"⚠️ PACER dependency validation failed for {dep_name}: {e}")
            else:
                self.logger.debug(f"ℹ️ Optional PACER dependency not found: {dep_name}")
        
        self.logger.info("✅ PACER dependency validation completed")
    
    async def _cleanup_container(self):
        """Enhanced container cleanup with error handling."""
        if self._container:
            try:
                # Shutdown resources
                shutdown_result = self._container.shutdown_resources()
                if shutdown_result is not None:
                    await shutdown_result
                
                # Unwire container
                self._container.unwire()
                self.logger.info("Container cleanup completed")
                
            except Exception as cleanup_error:
                self.logger.error(f"Error during container cleanup: {cleanup_error}", exc_info=True)
            finally:
                self._container = None

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Enhanced async context exit with proper error handling."""
        if exc_type:
            self.logger.error(f"MainServiceFactory exiting due to exception: {exc_type.__name__}: {exc_val}")
        else:
            self.logger.info("MainServiceFactory exiting normally")
        
        # Use enhanced cleanup method
        await self._cleanup_container()
        
        if exc_type:
            self.logger.info("MainServiceFactory cleanup completed after exception")
        else:
            self.logger.info("MainServiceFactory cleanup completed successfully")

    def get_dynamodb_storage(self):
        """Returns the DynamoDB storage from the container."""
        if not self._container:
            return None
        return self._container.storage.async_dynamodb_storage()

    def get_s3_storage(self):
        """Returns the S3 storage from the container."""
        if not self._container:
            return None
        return self._container.storage.s3_async_storage()

    def get_pacer_repository(self):
        """Returns the PacerRepository from the container."""
        if not self._container:
            return None
        return self._container.storage.pacer_repository()

    async def create_pacer_orchestrator_service(
        self, deepseek_service=None
    ) -> PacerOrchestratorService:
        """Create PacerOrchestratorService using DocketOrchestrator directly."""
        self.logger.info("Creating PacerOrchestratorService using DocketOrchestrator...")

        if not self._container:
            raise RuntimeError(
                "DI Container not initialized - cannot create PacerOrchestratorService"
            )

        try:
            # Extract PACER configuration from main config
            config_dict = self._prepare_config_dict()
            pacer_config = config_dict.get('pacer', {})
            
            # Import required processor classes
            from src.pacer.components.processing.court_processor import CourtProcessor
            from src.pacer.components.processing.docket_processor import DocketProcessor
            from src.pacer.components.processing.row_processor import RowProcessor
            from src.pacer.components.download.download_manager import DownloadManager
            from src.pacer.services.file_operations_service import FileOperationsService
            from src.pacer.facades.navigation_facade import NavigationFacade
            
            # ✅ USE DI CONTAINER ONLY - NO DIRECT INSTANTIATION!
            # All services MUST be obtained from the DI Container to ensure proper dependency injection

            # Use the PACER container which has all the properly configured services
            pacer_container = self._container.pacer

            # Get the orchestrator directly from the PACER container
            # This ensures all dependencies are properly injected through DI
            pacer_orchestrator_service = pacer_container.pacer_orchestrator()

            # Initialize the service
            await pacer_orchestrator_service.initialize()

            self.logger.info("✅ PacerOrchestratorService created using DI Container")
            return pacer_orchestrator_service

        except Exception as e:
            self.logger.error(f"Failed to create PacerOrchestratorService via DI Container: {e}", exc_info=True)
            raise

    async def create_reports_orchestrator_service(
        self, is_weekly: bool = False
    ) -> ReportsOrchestratorService:
        """Create ReportsOrchestratorService via DI Container."""
        self.logger.info(f"🔍 FACTORY DEBUG: Creating ReportsOrchestratorService with is_weekly={is_weekly}")

        if not self._container:
            raise RuntimeError(
                "DI Container not initialized - cannot create ReportsOrchestratorService"
            )

        service = self._container.reports.reports_orchestrator()
        
        # Override the is_weekly setting from the container with the parameter
        self.logger.info(f"🔍 FACTORY DEBUG: Before override - service.is_weekly = {getattr(service, 'is_weekly', 'NOT_SET')}")
        service.is_weekly = is_weekly
        self.logger.info(f"🔍 FACTORY DEBUG: After override - service.is_weekly = {service.is_weekly}")
        service.log_info(f"ReportsOrchestratorService is_weekly overridden to: {is_weekly}")
        
        self.logger.info("✅ ReportsOrchestratorService created via DI Container")
        return service

    async def create_scraping_orchestrator(self) -> ScrapingOrchestrator:
        """Create ScrapingOrchestrator via DI Container."""
        self.logger.info("Creating ScrapingOrchestrator via DI Container...")

        if not self._container:
            raise RuntimeError(
                "DI Container not initialized - cannot create ScrapingOrchestrator"
            )

        # ScrapingOrchestrator needs to be added to a container
        # For now, create it directly with dependencies
        pacer_orchestrator = await self.create_pacer_orchestrator_service()

        service = ScrapingOrchestrator(
            config=self.config,
            pacer_service=pacer_orchestrator,
            shutdown_event=self.shutdown_event,
        )

        self.logger.info("✅ ScrapingOrchestrator created")
        return service

    async def create_processing_orchestrator(self) -> ProcessingOrchestrator:
        """Create ProcessingOrchestrator via DI Container."""
        self.logger.info("Creating ProcessingOrchestrator via DI Container...")

        if not self._container:
            raise RuntimeError(
                "DI Container not initialized - cannot create ProcessingOrchestrator"
            )

        # ProcessingOrchestrator needs transformer services
        # Only provide the shutdown_event dependency that's required
        self._container.transformer.shutdown_event.override(self.shutdown_event)
        data_transformer = self._container.transformer.data_transformer()

        service = ProcessingOrchestrator(
            config=self.config, data_transformer=data_transformer, logger=self.logger
        )

        self.logger.info("✅ ProcessingOrchestrator created")
        return service

    async def create_upload_orchestrator(self) -> UploadOrchestrator:
        """Create UploadOrchestrator via DI Container."""
        self.logger.info("Creating UploadOrchestrator via DI Container...")

        if not self._container:
            raise RuntimeError(
                "DI Container not initialized - cannot create UploadOrchestrator"
            )

        # Get dependencies from container
        data_upload_service = self._container.transformer.data_upload_service()
        file_handler = self._container.transformer.file_handler_core()

        service = UploadOrchestrator(
            logger=self.logger,
            config=self.config,
            data_upload_service=data_upload_service,
            file_handler=file_handler,
            shutdown_event=self.shutdown_event,
        )

        self.logger.info("✅ UploadOrchestrator created")
        return service

    async def create_fb_ads_orchestrator(self) -> FbAdsOrchestrator:
        """Create FbAdsOrchestrator via DI Container."""
        self.logger.info("Creating FbAdsOrchestrator via DI Container...")

        if not self._container:
            raise RuntimeError(
                "DI Container not initialized - cannot create FbAdsOrchestrator"
            )

        # Get dependencies from FB ADS container only - NO OTHER CONTAINERS!
        ai_orchestrator = self._container.fb_ads.ai_orchestrator()
        deepseek_service = self._container.fb_ads.deepseek_service()
        prompt_manager = self._container.fb_ads.prompt_manager()

        # Get storage container from main container (with .env configuration)
        storage_container = self._container.storage
        
        # Get pre-configured FB ads container (properly wired with dependencies)
        fb_ads_container = self._container.fb_ads
        
        # Create the orchestrator with proper DI injection
        service = FbAdsOrchestrator(
            config=self.config,
            shutdown_event=self.shutdown_event,
            ai_orchestrator=ai_orchestrator,
            deepseek_service=deepseek_service,
            prompt_manager=prompt_manager,
            storage_container=storage_container,
            fb_ads_container=fb_ads_container,
        )

        self.logger.info("✅ FbAdsOrchestrator created via FB ADS container only")
        return service
