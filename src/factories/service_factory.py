"""Factory for creating service instances with proper dependencies."""
from typing import Optional

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.infrastructure.storage.dynamodb_base import DynamoDBBaseStorage as DynamoDBStorage
from src.repositories.district_courts_repository import DistrictCourtsRepository
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.repositories.law_firms_repository import LawFirmsRepository
from src.repositories.pacer_dockets_repository import PacerDocketsRepository
from src.repositories.pacer_repository import PacerRepository
from src.services.district_courts.query_service import DistrictCourtsQueryService
from src.services.fb_archive.query_service import FBArchiveQueryService
from src.services.law_firms.query_service import LawFirmsQueryService
from src.services.pacer_dockets.query_service import PacerDocketsQueryService


class ServiceFactory:
    """Factory for creating service instances with dependencies."""

    def __init__(self, storage: Optional[DynamoDBStorage] = None):
        """Initialize factory with storage backend.
        
        Args:
            storage: DynamoDB storage instance. If None, creates default.
        """
        self.storage = storage or DynamoDBStorage()
        self._repositories = {}
        self._services = {}

    def get_pacer_service(self) -> PacerDocketsQueryService:
        """Get or create PACER dockets query service."""
        if 'pacer' not in self._services:
            repo = self._get_or_create_repository('pacer', PacerRepository)
            self._services['pacer'] = PacerDocketsQueryService(repository=repo)
        return self._services['pacer']

    def get_pacer_dockets_service(self) -> PacerDocketsQueryService:
        """Get or create PACER dockets query service."""
        if 'pacer_dockets' not in self._services:
            repo = self._get_or_create_repository('pacer_dockets', PacerDocketsRepository)
            self._services['pacer_dockets'] = PacerDocketsQueryService(repo)
        return self._services['pacer_dockets']

    def get_district_courts_service(self) -> DistrictCourtsQueryService:
        """Get or create district courts query service."""
        if 'district_courts' not in self._services:
            repo = self._get_or_create_repository('district_courts', DistrictCourtsRepository)
            self._services['district_courts'] = DistrictCourtsQueryService(repo)
        return self._services['district_courts']

    def get_law_firms_service(self) -> LawFirmsQueryService:
        """Get or create law firms query service."""
        if 'law_firms' not in self._services:
            repo = self._get_or_create_repository('law_firms', LawFirmsRepository)
            self._services['law_firms'] = LawFirmsQueryService(repo)
        return self._services['law_firms']

    def get_fb_archive_service(self) -> FBArchiveQueryService:
        """Get or create FB archive query service."""
        if 'fb_archive' not in self._services:
            repo = self._get_or_create_repository('fb_archive', FBArchiveRepository)
            self._services['fb_archive'] = FBArchiveQueryService(repo)
        return self._services['fb_archive']

    def _get_or_create_repository(self, name: str, repo_class):
        """Get or create a repository instance."""
        if name not in self._repositories:
            self._repositories[name] = repo_class(self.storage)
        return self._repositories[name]


class AsyncServiceFactory:
    """Factory for creating async service instances with dependencies."""

    def __init__(self, storage: Optional[AsyncDynamoDBStorage] = None):
        """Initialize factory with async storage backend.
        
        Args:
            storage: Async DynamoDB storage instance. If None, will error on usage.
        """
        if storage is None:
            raise ValueError("AsyncServiceFactory requires an initialized AsyncDynamoDBStorage instance")
        self.storage = storage
        self._repositories = {}
        self._services = {}

    async def get_pacer_service(self) -> PacerDocketsQueryService:
        """Get or create async PACER dockets query service."""
        if 'pacer' not in self._services:
            repo = await self._get_or_create_repository('pacer', PacerRepository)
            self._services['pacer'] = PacerDocketsQueryService(repository=repo)
        return self._services['pacer']

    async def get_pacer_dockets_service(self) -> PacerDocketsQueryService:
        """Get or create async PACER dockets query service."""
        if 'pacer_dockets' not in self._services:
            repo = await self._get_or_create_repository('pacer_dockets', PacerDocketsRepository)
            self._services['pacer_dockets'] = PacerDocketsQueryService(repo)
        return self._services['pacer_dockets']

    async def get_district_courts_service(self) -> DistrictCourtsQueryService:
        """Get or create async district courts query service."""
        if 'district_courts' not in self._services:
            repo = await self._get_or_create_repository('district_courts', DistrictCourtsRepository)
            self._services['district_courts'] = DistrictCourtsQueryService(repo)
        return self._services['district_courts']

    async def get_law_firms_service(self) -> LawFirmsQueryService:
        """Get or create async law firms query service."""
        if 'law_firms' not in self._services:
            repo = await self._get_or_create_repository('law_firms', LawFirmsRepository)
            self._services['law_firms'] = LawFirmsQueryService(repo)
        return self._services['law_firms']

    async def get_fb_archive_service(self) -> FBArchiveQueryService:
        """Get or create async FB archive query service."""
        if 'fb_archive' not in self._services:
            repo = await self._get_or_create_repository('fb_archive', FBArchiveRepository)
            self._services['fb_archive'] = FBArchiveQueryService(repo)
        return self._services['fb_archive']

    async def _get_or_create_repository(self, name: str, repo_class):
        """Get or create a repository instance."""
        if name not in self._repositories:
            self._repositories[name] = repo_class(self.storage)
        return self._repositories[name]
