# Reports Domain Module

## Overview
This is the Reports domain module containing all business logic for report generation, data aggregation, and publishing. This is a SIMPLE domain with ~10-15 files.

## Architecture Status
🚧 **NEW DOMAIN**: Being created from components in `src/services/reports/`

## Directory Structure
```
reports/
├── models/              # Domain models
│   ├── report.py
│   ├── daily_report.py
│   └── weekly_report.py
├── core/                # Core business logic
│   ├── report_generator.py
│   ├── data_aggregator.py
│   ├── ad_processor.py
│   ├── ad_page_generator.py
│   ├── formatter.py
│   ├── publisher.py
│   └── s3_cache.py
├── factories/           # Domain factories
│   └── report_factory.py
├── interfaces/          # Domain interfaces and protocols
│   └── reports_protocol.py
└── exceptions.py        # Domain-specific exceptions
```

## Key Principles
1. **NO cross-domain dependencies** - Reports cannot import from pacer, transformer, or fb_ads
2. **Use infrastructure patterns** - Inherit from `src.infrastructure.patterns.component_base.AsyncServiceBase`
3. **Simple domain** - Keep it simple, this is not a complex domain
4. **Domain-driven design** - Business logic lives here, not in service layer

## Migration Notes
- Moving from `src/services/reports/` (11 files)
- Service layer will only contain orchestration
- Most logic is straightforward report generation

## Base Classes
```python
# ALWAYS use this base class, NEVER create domain-specific base classes
from src.infrastructure.patterns.component_base import AsyncServiceBase
```

## Core Services
1. **ReportGenerator** - Main report generation logic
2. **DataAggregator** - Aggregates data for reports
3. **AdProcessor** - Processes ad data for reports
4. **Publisher** - Publishes reports to S3/email
5. **S3Cache** - Manages S3 caching

## Testing
All components should have corresponding tests in `tests/unit/reports/`

## Dependencies
- Infrastructure patterns
- External: S3, Database
- NO dependencies on other domains

## Contact
Architecture questions: See docs/ARCHITECTURAL_CONSISTENCY_PLAN.md