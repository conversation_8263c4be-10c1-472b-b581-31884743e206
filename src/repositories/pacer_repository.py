"""
PACER Repository - Data Access Layer
Handles all DynamoDB operations for PACER data
"""
import logging
from typing import Dict, List, Any, Optional, Union

import pandas as pd
from boto3.dynamodb.conditions import Key, Attr

from src.infrastructure.patterns.repository_base import RepositoryBase, convert_case_on_read, convert_case_on_write
from src.infrastructure.protocols.exceptions import StorageError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage as DynamoDBStorage
from src.infrastructure.storage.retry_decorators import with_retry, aggressive_retry
from src.utils.docket_utils import normalize_docket_number


class PacerRepository(RepositoryBase):
    """Repository for PACER data access operations"""

    DEFAULT_TABLE_NAME = 'Pacer'

    def __init__(self, storage: DynamoDBStorage, logger: Optional[LoggerProtocol] = None):
        logger_instance = logger if logger else logging.getLogger(__name__)
        # Set custom data parameter names for PACER-specific operations
        config = {
            'data_param_names': ['record', 'update_data', 'transfer_info']
        }
        super().__init__(storage, logger_instance, config)

    @property
    def primary_key_fields(self) -> List[str]:
        """Return primary key field names in snake_case."""
        return ['filing_date', 'docket_num']

    def _handle_docket_normalization(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Handle docket number normalization before case conversion."""
        if 'docket_num' in record and record['docket_num']:
            record = record.copy()
            record['docket_num'] = normalize_docket_number(record['docket_num'])
        return record

    def _snake_to_pascal(self, snake_str: str) -> str:
        """Override to handle PACER-specific field mappings."""
        # Handle specific known fields that come in as all lowercase or snake_case
        lowercase_to_pascal = {
            'filingdate': 'FilingDate',
            'filing_date': 'FilingDate',
            'docketnum': 'DocketNum',
            'docket_num': 'DocketNum',
            'courtid': 'CourtId',
            'court_id': 'CourtId',
            'addedon': 'AddedOn',
            'added_on': 'AddedOn',
            'mdlnum': 'MdlNum',
            'mdl_num': 'MdlNum',
            'lawfirm': 'LawFirm',
            'law_firm': 'LawFirm',
            'transfereecourtid': 'TransfereeCourtId',
            'transferee_court_id': 'TransfereeCourtId',
            'transfereedocketnum': 'TransfereeDocketNum',
            'transferee_docket_num': 'TransfereeDocketNum',
            'numplaintiffs': 'NumPlaintiffs',
            'num_plaintiffs': 'NumPlaintiffs',
            'versus': 'Versus',
            'title': 'Title',
            's3link': 'S3Link',
            's3_link': 'S3Link'
        }

        # First check if it's a known PACER field
        if snake_str.lower() in lowercase_to_pascal:
            self.log_debug(f"Converting known PACER field: {snake_str} -> {lowercase_to_pascal[snake_str.lower()]}")
            return lowercase_to_pascal[snake_str.lower()]

        # Use parent implementation for standard cases
        return super()._snake_to_pascal(snake_str)

    def _convert_dict_to_pascal(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Override to handle PACER-specific logic for None values and internal fields."""
        if not isinstance(data, dict):
            return data

        # Handle docket normalization before conversion
        data = self._handle_docket_normalization(data)

        converted = {}
        for key, value in data.items():
            # Skip None values and internal fields starting with '_' (PACER-specific)
            if value is None or (key.startswith('_') and key != '_processing_notes'):
                continue

            # Convert key to PascalCase using parent logic + PACER overrides
            pascal_key = self._snake_to_pascal(key)

            # DEBUG: Log specific field conversions
            if key in ['added_on', 'num_plaintiffs'] and value is not None:
                self.log_debug(f"Converting PACER field: {key}={value} -> {pascal_key}={value}")

            # Recursively convert nested dictionaries
            if isinstance(value, dict):
                converted[pascal_key] = self._convert_dict_to_pascal(value)
            elif isinstance(value, list):
                converted[pascal_key] = [
                    self._convert_dict_to_pascal(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                converted[pascal_key] = value

        return converted

    @convert_case_on_write
    @with_retry()
    async def add_or_update_record(self, record: Dict[str, Any]) -> bool:
        """Add or update a PACER record with proper field mapping"""
        try:
            # Record is already converted to PascalCase by decorator
            # Ensure required fields are present
            if 'FilingDate' not in record or 'DocketNum' not in record:
                print(f"❌ VALIDATION ERROR: Missing required fields in record: {list(record.keys())}")
                raise ValueError(f"Missing required fields (FilingDate or DocketNum) in record: {list(record.keys())}")

            print(f"✅ ABOUT TO PUT ITEM: FilingDate={record.get('FilingDate')}, DocketNum={record.get('DocketNum')}")
            await self.storage.put_item(self.table_name, record)
            print(f"🎉 SUCCESS: Item put to DynamoDB successfully!")
            return True
        except StorageError as storage_error:
            # Log the detailed error information from enhanced DynamoDB storage
            error_context = getattr(storage_error, 'context', {})
            filing_date = record.get('FilingDate', 'unknown')
            docket_num = record.get('DocketNum', 'unknown')

            # FORCE CRITICAL LOGGING FOR DEBUGGING
            print(f"❌ CRITICAL: DynamoDB put_item FAILED - FilingDate={filing_date}, DocketNum={docket_num}")
            print(f"❌ Storage Error: {str(storage_error)}")
            print(f"❌ Error Context: {error_context}")
            print(f"❌ Table: {self.table_name}")
            
            self.log_error("PACER Repository: DynamoDB put_item failed with detailed error", {
                "filing_date": filing_date,
                "docket_num": docket_num,
                "storage_error": str(storage_error),
                "error_context": error_context,
                "table_name": self.table_name
            })
            return False
        except Exception as e:
            # For validation and other non-storage errors
            raise ValueError(f"Unexpected error adding/updating record: {e}")

    @convert_case_on_write
    async def batch_add_or_update_records(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Batch add or update PACER records using DynamoDB batch write.

        Args:
            records: List of records to write (will be processed in batches of 25)

        Returns:
            Dictionary with results: {'successful': int, 'failed': int, 'errors': List[str]}
        """
        if not records:
            return {'successful': 0, 'failed': 0, 'errors': []}

        self.log_info(f"Starting batch write of {len(records)} PACER records")

        # Convert all records to PascalCase and validate
        converted_records = []
        validation_errors = []

        for idx, record in enumerate(records):
            try:
                # Handle docket normalization and case conversion
                record = self._handle_docket_normalization(record)
                converted_record = self._convert_dict_to_pascal(record)

                # Validate required fields
                if 'FilingDate' not in converted_record or 'DocketNum' not in converted_record:
                    validation_errors.append(f"Record {idx}: Missing required fields (FilingDate or DocketNum)")
                    continue

                converted_records.append(converted_record)

            except Exception as e:
                validation_errors.append(f"Record {idx}: Conversion/validation error: {str(e)}")

        if not converted_records:
            return {
                'successful': 0,
                'failed': len(records),
                'errors': validation_errors
            }

        # Use storage batch write
        try:
            await self.storage.batch_write_item(self.table_name, converted_records)

            return {
                'successful': len(converted_records),
                'failed': len(validation_errors),
                'errors': validation_errors
            }

        except StorageError as e:
            error_msg = f"Batch write failed: {str(e)}"
            self.log_error(error_msg, {"error_context": getattr(e, 'context', {})})

            return {
                'successful': 0,
                'failed': len(records),
                'errors': validation_errors + [error_msg]
            }
        except Exception as e:
            error_msg = f"Unexpected batch write error: {str(e)}"
            self.log_exception(error_msg)

            return {
                'successful': 0,
                'failed': len(records),
                'errors': validation_errors + [error_msg]
            }

    @convert_case_on_read
    async def get_by_filing_date_and_docket(
            self, filing_date: str, docket_num: str
    ) -> Optional[Dict[str, Any]]:
        """Get a record by filing date and docket number"""
        # Normalize docket number for consistent lookup
        normalized_docket = normalize_docket_number(docket_num)
        key = {'FilingDate': filing_date, 'DocketNum': normalized_docket}
        return await self.storage.get_item(self.table_name, key)

    @convert_case_on_read
    @with_retry()
    async def query_by_filing_date(self, filing_date: str, projection_expression: Optional[str] = None) -> List[
        Dict[str, Any]]:
        """Query records by filing date with optional projection"""
        key_condition = Key('FilingDate').eq(filing_date)

        try:
            # Build query parameters - key_condition is positional, others are keyword
            kwargs = {}
            if projection_expression:
                kwargs['projection_expression'] = projection_expression

            # Convert expression parameters from snake_case to PascalCase for DynamoDB
            if self.enable_case_conversion:
                converted_kwargs = self._convert_expression_parameters(**kwargs)
            else:
                converted_kwargs = kwargs

            return await self.storage.query(self.table_name, key_condition, **converted_kwargs)
        except StorageError:
            # Storage already logged the error with proper context
            return []

    @convert_case_on_read
    @with_retry()
    async def query_by_court_and_docket(
            self, court_id: str, docket_num: str, court_logger = None
    ) -> List[Dict[str, Any]]:
        """Query records by court ID and docket number with comprehensive logging"""
        # Use court_logger if provided for consistent logging
        logger = court_logger or self.logger
        log_prefix = "DYNAMODB_QUERY:"
        
        # Normalize docket number for consistent lookup
        normalized_docket = normalize_docket_number(docket_num)
        
        logger.info(f"{log_prefix} Executing DynamoDB query")
        logger.info(f"{log_prefix} Query parameters: court_id='{court_id}', normalized_docket='{normalized_docket}'")
        
        # Use GSI if available
        index_name = 'CourtId-DocketNum-index'
        key_condition = Key('CourtId').eq(court_id) & Key('DocketNum').eq(normalized_docket)
        
        logger.info(f"{log_prefix} Using GSI index: '{index_name}'")
        logger.info(f"{log_prefix} Key condition: CourtId='{court_id}' AND DocketNum='{normalized_docket}'")
        logger.info(f"{log_prefix} Target table: '{self.table_name}'")

        try:
            logger.info(f"{log_prefix} Sending query to DynamoDB...")
            
            results = await self.storage.query(
                self.table_name,
                key_condition,
                index_name=index_name
            )
            
            logger.info(f"{log_prefix} DynamoDB query completed successfully")
            logger.info(f"{log_prefix} Query returned {len(results)} items")
            
            if results:
                logger.info(f"{log_prefix} Query results summary:")
                for i, result in enumerate(results[:3]):  # Log details of first 3 results
                    filing_date = result.get('filing_date', result.get('FilingDate', 'Unknown'))
                    versus = result.get('versus', result.get('Versus', 'Unknown'))
                    added_on = result.get('added_on', result.get('AddedOn', 'Unknown'))
                    logger.info(f"{log_prefix} Result {i+1}: filing_date='{filing_date}', versus='{versus}', added_on='{added_on}'")
                
                if len(results) > 3:
                    logger.info(f"{log_prefix} ... and {len(results) - 3} more results")
            else:
                logger.info(f"{log_prefix} No matching records found in DynamoDB")
            
            return results
            
        except StorageError as e:
            logger.error(f"{log_prefix} DynamoDB StorageError: {e}", exc_info=True)
            logger.error(f"{log_prefix} Query failed for court_id='{court_id}', docket_num='{normalized_docket}'")
            logger.error(f"{log_prefix} Index: '{index_name}', Table: '{self.table_name}'")
            # Storage already logged the error with proper context
            return []
        except Exception as e:
            logger.error(f"{log_prefix} Unexpected error during DynamoDB query: {e}", exc_info=True)
            logger.error(f"{log_prefix} Query parameters: court_id='{court_id}', normalized_docket='{normalized_docket}'")
            return []

    # Alias for backward compatibility
    query_pacer_by_court_id_and_docket_num = query_by_court_and_docket

    @convert_case_on_read
    async def query_by_transferee_docket(
            self, court_id: str, docket_num: str
    ) -> List[Dict[str, Any]]:
        """Query by transferee court and docket"""
        index_name = 'TransfereeCourtIdDocketNum-index'
        key_condition = (
                Key('TransfereeCourtId').eq(court_id) &
                Key('TransfereeDocketNum').eq(docket_num)
        )
        return await self.storage.query(
            self.table_name,
            key_condition,
            index_name=index_name
        )

    async def check_docket_exists(
            self, court_id: str, docket_num: str, court_logger = None
    ) -> bool:
        """Check if a docket exists with comprehensive logging"""
        # Use court_logger if provided for consistent logging
        logger = court_logger or self.logger
        log_prefix = "DYNAMODB_CHECK:"
        
        logger.info(f"{log_prefix} ============ STARTING DOCKET EXISTENCE CHECK ============")
        logger.info(f"{log_prefix} Input parameters: court_id='{court_id}', docket_num='{docket_num}'")
        
        # Normalize docket number and log the normalization
        normalized_docket = normalize_docket_number(docket_num)
        if normalized_docket != docket_num:
            logger.info(f"{log_prefix} Docket number normalized: '{docket_num}' -> '{normalized_docket}'")
        else:
            logger.info(f"{log_prefix} Docket number unchanged: '{docket_num}'")
        
        logger.info(f"{log_prefix} Querying DynamoDB with: court_id='{court_id}', normalized_docket='{normalized_docket}'")
        
        try:
            results = await self.query_by_court_and_docket(court_id, docket_num, logger)
            
            exists = len(results) > 0
            
            logger.info(f"{log_prefix} ============ DOCKET EXISTENCE CHECK RESULT ============")
            logger.info(f"{log_prefix} Query returned {len(results)} results")
            logger.info(f"{log_prefix} Docket exists: {exists}")
            
            if exists:
                logger.info(f"{log_prefix} DUPLICATE DETECTED - Docket {court_id}/{docket_num} already exists in DynamoDB")
                # Log sample of found results for debugging
                for i, result in enumerate(results[:3]):  # Log first 3 results
                    filing_date = result.get('filing_date', result.get('FilingDate', 'Unknown'))
                    versus = result.get('versus', result.get('Versus', 'Unknown'))
                    logger.info(f"{log_prefix} Found result {i+1}: filing_date='{filing_date}', versus='{versus}'")
            else:
                logger.info(f"{log_prefix} NO DUPLICATE - Docket {court_id}/{docket_num} not found in DynamoDB")
            
            logger.info(f"{log_prefix} ============ DOCKET EXISTENCE CHECK COMPLETE ============")
            return exists
            
        except Exception as e:
            logger.error(f"{log_prefix} ERROR during docket existence check: {e}", exc_info=True)
            logger.error(f"{log_prefix} ERROR - Query failed for court_id='{court_id}', docket_num='{docket_num}'")
            logger.info(f"{log_prefix} ============ DOCKET EXISTENCE CHECK FAILED ============")
            # Return False on error to avoid blocking processing
            return False

    @convert_case_on_read
    async def docket_exists(
            self,
            filing_date: str,
            docket_num: str,
            court_id: Optional[str] = None
    ) -> Union[bool, List[Dict[str, Any]]]:
        """Check if a docket exists - backward compatibility method

        Returns the list of found items (empty list if not found) to match legacy behavior
        """
        if court_id:
            # Use GSI to query by court ID and docket number
            results = await self.query_by_court_and_docket(court_id, docket_num)
            return results
        else:
            # Use primary key if we have filing date and docket number
            key = {'FilingDate': filing_date, 'DocketNum': docket_num}
            item = await self.storage.get_item(self.table_name, key)
            # Don't convert here since decorator will handle it
            return [item] if item else []

    async def get_docket_items(
            self, court_id: str, docket_num: str
    ) -> List[Dict[str, Any]]:
        """Get all items for a specific docket"""
        # Query main table
        main_results = await self.query_by_court_and_docket(court_id, docket_num)

        # Query as transferee
        transferee_results = await self.query_by_transferee_docket(court_id, docket_num)

        # Combine and deduplicate
        all_results = main_results + transferee_results
        seen = set()
        unique_results = []

        for item in all_results:
            # Use snake_case since items are already converted by decorated methods
            key = (item.get('filing_date'), item.get('docket_num'))
            if key not in seen:
                seen.add(key)
                unique_results.append(item)

        return unique_results

    @convert_case_on_write
    async def update_added_on_date(
            self, current_added_on: str, new_added_on: str
    ) -> int:
        """Update AddedOn date for all matching records"""
        # First, find all records with the current AddedOn date
        filter_expression = Attr('AddedOn').eq(current_added_on)
        items = await self.storage.scan(
            self.table_name,
            filter_expression=filter_expression
        )

        # Update each item
        update_count = 0
        for item in items:
            key = {
                'FilingDate': item['FilingDate'],
                'DocketNum': item['DocketNum']
            }
            update_expression = 'SET AddedOn = :new_date'
            expression_values = {':new_date': new_added_on}

            await self.storage.update_item(
                self.table_name,
                key,
                update_expression,
                expression_values
            )
            update_count += 1

        return update_count

    @convert_case_on_read
    async def scan_all(self) -> List[Dict[str, Any]]:
        """Scan all records in the table"""
        return await self.storage.scan(self.table_name, None)

    @convert_case_on_read
    async def query_by_date_range(
            self, start_date: str, end_date: str
    ) -> List[Dict[str, Any]]:
        """Query records within a date range"""
        # Since FilingDate is the partition key, we need to scan with filter
        filter_expression = (
                Attr('FilingDate').gte(start_date) &
                Attr('FilingDate').lte(end_date)
        )
        return await self.storage.scan(
            self.table_name,
            filter_expression=filter_expression
        )

    @convert_case_on_read
    async def query_by_mdl_and_date_range(
            self, mdl_num: str, start_date: str, end_date: str
    ) -> List[Dict[str, Any]]:
        """Query records by MDL number within a date range using GSI"""
        from datetime import datetime, timedelta

        results = []
        current_date = datetime.strptime(start_date, '%Y%m%d')
        end_date_obj = datetime.strptime(end_date, '%Y%m%d')

        index_name = 'MdlNum-FilingDate-index'

        while current_date <= end_date_obj:
            date_str = current_date.strftime('%Y%m%d')

            key_condition = Key('MdlNum').eq(mdl_num) & Key('FilingDate').eq(date_str)

            try:
                daily_records = await self.storage.query(
                    self.table_name,
                    key_condition,
                    index_name=index_name
                )
                results.extend(daily_records)
            except Exception as e:
                self.logger.warning(f"Failed to query MdlNum {mdl_num} for date {date_str}: {e}")

            current_date += timedelta(days=1)

        return results

    @convert_case_on_read
    @aggressive_retry
    async def query_by_added_on_range(
            self, start_date: str, end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Query records by AddedOn date range using GSI (much faster than scan)"""
        index_name = 'AddedOn-index'
        items = []

        if not end_date or start_date == end_date:
            # Single date query
            self.logger.info(f"Querying AddedOn-index GSI for single date: {start_date}")
            key_condition = Key('AddedOn').eq(start_date)
            return await self.storage.query(
                self.table_name,
                key_condition,
                index_name=index_name
            )

        # Date range query: iterate day by day using GSI (efficient)
        self.logger.info(f"Querying AddedOn-index GSI day by day from {start_date} to {end_date}")
        from datetime import datetime, timedelta

        start_dt = datetime.strptime(start_date, '%Y%m%d')
        end_dt = datetime.strptime(end_date, '%Y%m%d')

        current_dt = start_dt
        while current_dt <= end_dt:
            date_str = current_dt.strftime('%Y%m%d')
            try:
                key_condition = Key('AddedOn').eq(date_str)
                daily_items = await self.storage.query(
                    self.table_name,
                    key_condition,
                    index_name=index_name
                )
                items.extend(daily_items)
                self.logger.debug(f"Found {len(daily_items)} items for AddedOn={date_str}")
            except Exception as e:
                self.logger.warning(f"Error querying AddedOn={date_str}: {e}")

            current_dt += timedelta(days=1)

        self.logger.info(f"AddedOn range query returned {len(items)} total items")
        return items

    def get_records_by_added_on(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """
        Synchronous method for backward compatibility with original data loader.
        Creates its own async storage context to avoid event loop conflicts.
        """
        import asyncio

        async def query_with_own_storage():
            from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
            import logging

            # Create thread-local async storage with same config
            storage_config = self.storage.config if hasattr(self.storage, 'config') else {}
            logger = logging.getLogger(__name__)
            thread_storage = AsyncDynamoDBStorage(config=storage_config, logger=logger)

            async with thread_storage:
                # Create temporary repository with thread storage
                thread_repo = PacerRepository(thread_storage)
                return await thread_repo.query_by_added_on_range(start_date, end_date)

        try:
            return asyncio.run(query_with_own_storage())
        except Exception as e:
            # Can't use storage logger in sync context, use basic logging
            logging.getLogger(__name__).error(f"Error in get_records_by_added_on: {e}")
            return []

    @convert_case_on_read
    @with_retry()
    async def query_by_mdl_num(self, mdl_num: str) -> List[Dict[str, Any]]:
        """Query records by MDL number"""
        index_name = 'MdlNum-FilingDate-index'
        key_condition = Key('MdlNum').eq(mdl_num)
        return await self.storage.query(
            self.table_name,
            key_condition,
            index_name=index_name
        )

    async def get_mdl_summary(self, date_str: str) -> pd.DataFrame:
        """
        Query the past 30 days of filings from the given date, group by MDL number,
        and return a sorted DataFrame with the total count for each MDL.

        For MDL 2873 (AFFF): Deduplicates transfers by subtracting original case plaintiffs
        when transferor info matches another case in the period.
        """
        try:
            from datetime import datetime, timedelta

            # Convert input date to datetime
            end_date = datetime.strptime(date_str, '%Y%m%d')
            start_date = end_date - timedelta(days=29)  # 30 days including the end date

            items = []
            current_date = end_date

            # Query for each day in the 30-day range
            while current_date >= start_date:
                filing_date = current_date.strftime('%Y%m%d')

                try:
                    # Get ALL fields for transfer matching
                    daily_records = await self.query_by_filing_date(filing_date)
                    items.extend(daily_records)
                except Exception as e:
                    self.logger.warning(f"Failed to query FilingDate {filing_date}: {e}")

                current_date -= timedelta(days=1)

            self.logger.info(f"Total records retrieved for MDL summary (30 days): {len(items)}")

            if not items:
                return pd.DataFrame(columns=['mdl_num', 'total'])

            # Convert to DataFrame
            df = pd.DataFrame(items)

            # Filter valid MDL numbers
            df_filtered = df[
                df['mdl_num'].notna() & (df['mdl_num'] != '') & (df['mdl_num'] != 'nan') & (df['mdl_num'] != 'NA')]

            if df_filtered.empty:
                return pd.DataFrame(columns=['mdl_num', 'total'])

            # Step 1: Remove duplicates by mdl_num + versus
            if 'versus' in df_filtered.columns:
                df_deduped = df_filtered.drop_duplicates(subset=['mdl_num', 'versus'], keep='first')
            else:
                df_deduped = df_filtered

            # Step 2: Calculate weights with transfer deduplication
            df_deduped = df_deduped.copy()

            # Convert num_plaintiffs to numeric
            if 'num_plaintiffs' in df_deduped.columns:
                df_deduped['num_plaintiffs'] = pd.to_numeric(df_deduped['num_plaintiffs'], errors='coerce').fillna(
                    1).astype(int)
            else:
                df_deduped['num_plaintiffs'] = 1

            # Default weight is num_plaintiffs
            df_deduped['Weight'] = df_deduped['num_plaintiffs']

            # Special handling for MDL 2873 (AFFF) - deduplicate transfers
            if '2873' in df_deduped['mdl_num'].values:
                # Create lookup for all cases by (court_id, docket_num)
                case_lookup = {}
                for idx, row in df_deduped.iterrows():
                    court_id = row.get('court_id')
                    docket_num = row.get('docket_num')
                    if court_id and docket_num:
                        case_lookup[(court_id, docket_num)] = row

                # Find transfers and subtract original case plaintiffs
                for idx, row in df_deduped.iterrows():
                    if row['mdl_num'] == '2873':
                        transferor_court = row.get('transferor_court_id')
                        transferor_docket = row.get('transferor_docket_num')

                        if transferor_court and transferor_docket:
                            # Check if original case exists in our period
                            original_case = case_lookup.get((transferor_court, transferor_docket))
                            if original_case is not None and original_case['mdl_num'] == '2873':
                                # Subtract the original case's plaintiffs from total
                                original_plaintiffs = int(original_case.get('num_plaintiffs', 1))
                                # Set original case weight to 0 to exclude from total
                                df_deduped.loc[df_deduped.index == original_case.name, 'Weight'] = 0
                                self.logger.debug(
                                    f"AFFF dedup: Removed {original_plaintiffs} plaintiffs from {transferor_court}/{transferor_docket}")

            # Step 3: Group by MDL and sum weights
            mdl_summary = df_deduped.groupby('mdl_num')['Weight'].sum().reset_index()
            mdl_summary.columns = ['mdl_num', 'total']
            mdl_summary = mdl_summary.sort_values('total', ascending=False).reset_index(drop=True)

            self.logger.info(f"Generated MDL summary with AFFF deduplication: {len(mdl_summary)} MDLs")

            return mdl_summary

        except Exception as e:
            self.logger.error(f"Error generating MDL summary: {e}", exc_info=True)
            return pd.DataFrame(columns=['mdl_num', 'total'])

    @convert_case_on_read
    @with_retry()
    async def query_by_law_firm(self, law_firm: str, filing_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """Query records by law firm and optionally by filing date"""
        index_name = 'LawFirm-FilingDate-index'

        if filing_date:
            # Query with both law firm and filing date
            key_condition = Key('LawFirm').eq(law_firm) & Key('FilingDate').eq(filing_date)
        else:
            # Query just by law firm
            key_condition = Key('LawFirm').eq(law_firm)

        return await self.storage.query(
            self.table_name,
            key_condition,
            index_name=index_name
        )

    @convert_case_on_write
    async def update_transfer_info(
            self,
            filing_date: str,
            docket_num: str,
            transfer_info: Dict[str, Any]
    ) -> bool:
        """Update transfer information for a record"""
        # Normalize docket number for consistent lookup
        normalized_docket = normalize_docket_number(docket_num)
        key = {'FilingDate': filing_date, 'DocketNum': normalized_docket}

        # Build update expression (transfer_info already converted to PascalCase by decorator)
        update_parts = []
        expression_values = {}

        for field, value in transfer_info.items():
            placeholder = f":{field.lower()}"
            update_parts.append(f"{field} = {placeholder}")
            expression_values[placeholder] = value

        update_expression = "SET " + ", ".join(update_parts)

        try:
            await self.storage.update_item(
                self.table_name,
                key,
                update_expression,
                expression_values
            )
            return True
        except StorageError:
            # Storage already logged the error with proper context
            return False

    @convert_case_on_write
    async def update_item(
            self,
            key: Dict[str, Any],
            update_data: Dict[str, Any],
            consistent_read_verify: bool = False
    ) -> bool:
        """Update an item with given data - backward compatibility method"""
        # Delegate to update_transfer_info for now
        filing_date = key.get('FilingDate')
        docket_num = key.get('DocketNum')

        if not filing_date or not docket_num:
            self.logger.info("Missing required key fields for update - case may not exist yet")
            return False

        return await self.update_transfer_info(filing_date, docket_num, update_data)

    @convert_case_on_read
    async def get_recent_filings(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get recent filings from the last N days"""
        from datetime import datetime, timedelta

        results = []
        end_date = datetime.now()

        # Query each day individually
        for i in range(days):
            date = end_date - timedelta(days=i)
            date_str = date.strftime('%Y%m%d')

            # Query by filing date
            day_results = await self.query_by_filing_date(date_str)
            results.extend(day_results)

        return results

    async def query_transfer_info_async(
            self, court_id: str, docket_num: str
    ) -> Optional[Dict[str, Any]]:
        """Query transfer information for a specific court and docket

        Returns the first matching record that contains S3 link information,
        which is what the transformer expects for PDF lookup.

        Args:
            court_id: Court identifier
            docket_num: Docket number

        Returns:
            Single record dict with S3 link information, or None if not found
        """
        try:
            # Query records for this court and docket
            records = await self.query_by_court_and_docket(court_id, docket_num)

            if not records:
                return None

            # Look for a record with S3 link information
            for record in records:
                # Check for S3 link fields (records are already in snake_case from decorated query method)
                s3_link = record.get('s3_link')
                if s3_link and isinstance(s3_link, str) and s3_link.lower().endswith('.pdf'):
                    return record

            # If no record with S3 link found, return the first record
            # This maintains compatibility with the original behavior
            return records[0]

        except Exception as e:
            self.storage.log_error(f"Error querying transfer info", {
                "court_id": court_id,
                "docket_num": docket_num,
                "error": str(e)
            })
            return None

    def query_transfer_info(
            self, court_id: str, docket_num: str
    ) -> Optional[Dict[str, Any]]:
        """Synchronous wrapper for query_transfer_info_async

        This method exists for backward compatibility with legacy code that expects
        synchronous database operations. It creates an event loop if none exists
        and executes the async method.

        Args:
            court_id: Court identifier
            docket_num: Docket number

        Returns:
            Single record dict with S3 link information, or None if not found
        """
        import asyncio

        try:
            # Try to get current event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, we need to create a new thread
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.query_transfer_info_async(court_id, docket_num))
                    return future.result()
            else:
                # Loop exists but not running, we can use it
                return loop.run_until_complete(self.query_transfer_info_async(court_id, docket_num))
        except RuntimeError:
            # No event loop exists, create one
            return asyncio.run(self.query_transfer_info_async(court_id, docket_num))
        except Exception as e:
            # Can't use storage logger in sync context, use basic logging
            logging.getLogger(__name__).error(f"Error in synchronous query_transfer_info wrapper: {e}")
            return None
