#!/usr/bin/env python3
"""
Test script to validate resource cleanup fixes.

This script creates LLM clients and tests that their sessions are properly 
tracked and cleaned up without resource leaks.
"""

import asyncio
import logging
import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

# Enable resource tracking early
from src.lib.utils.enable_resource_tracking import resource_tracker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_deepseek_client_cleanup():
    """Test DeepSeek client resource management."""
    logger.info("Testing DeepSeek client resource cleanup...")
    
    try:
        from src.infrastructure.external.deepseek_client import DeepSeekClient
        
        # Create client with dummy API key for testing
        client = DeepSeekClient(api_key="test-key")
        
        # Test context manager usage
        async with client:
            logger.info("DeepSeek client initialized with context manager")
            # The session should be created and tracked
            
        logger.info("DeepSeek client context exited")
        
        # Test manual initialization and cleanup
        client2 = DeepSeekClient(api_key="test-key")
        await client2._initialize()
        logger.info("DeepSeek client manually initialized")
        
        await client2.close_session()
        logger.info("DeepSeek client manually closed")
        
    except Exception as e:
        logger.error(f"Error testing DeepSeek client: {e}")


async def test_openai_client_cleanup():
    """Test OpenAI client resource management."""
    logger.info("Testing OpenAI client resource cleanup...")
    
    try:
        from src.infrastructure.external.openai_client import OpenAIClient
        
        # Create client with dummy API key for testing
        client = OpenAIClient(api_key="test-key")
        
        # Test context manager usage
        async with client:
            logger.info("OpenAI client initialized with context manager")
            # The session should be created and tracked
            
        logger.info("OpenAI client context exited")
        
    except Exception as e:
        logger.error(f"Error testing OpenAI client: {e}")


async def test_resource_manager():
    """Test the LLM resource manager."""
    logger.info("Testing LLM resource manager...")
    
    try:
        from src.infrastructure.lifecycle.llm_resource_manager import (
            llm_resource_manager,
            get_llm_resource_status
        )
        
        # Get initial status
        status = await get_llm_resource_status()
        logger.info(f"Initial resource status: {status}")
        
        # Create some clients to test tracking
        from src.infrastructure.external.deepseek_client import DeepSeekClient
        from src.infrastructure.external.openai_client import OpenAIClient
        
        clients = []
        
        # Create DeepSeek client
        deepseek_client = DeepSeekClient(api_key="test-key")
        await deepseek_client._initialize()
        clients.append(deepseek_client)
        
        # Create OpenAI client
        openai_client = OpenAIClient(api_key="test-key")
        async with openai_client:
            clients.append(openai_client)
            
            # Get status with active clients
            status = await get_llm_resource_status()
            logger.info(f"Status with active clients: {status}")
        
        # Clean up manually created client
        await deepseek_client.close_session()
        
        # Final cleanup
        cleanup_count = await llm_resource_manager.cleanup_all_clients()
        logger.info(f"Final cleanup removed {cleanup_count} clients")
        
        # Final status
        status = await get_llm_resource_status()
        logger.info(f"Final resource status: {status}")
        
    except Exception as e:
        logger.error(f"Error testing resource manager: {e}")


async def main():
    """Main test function."""
    logger.info("Starting resource cleanup validation tests...")
    
    # Print initial resource state
    logger.info("=== Initial Resource State ===")
    resource_tracker.print_unclosed_resources()
    
    # Run tests
    await test_deepseek_client_cleanup()
    await test_openai_client_cleanup()
    await test_resource_manager()
    
    # Print final resource state
    logger.info("=== Final Resource State ===")
    resource_tracker.print_unclosed_resources()
    
    # Do comprehensive cleanup
    from src.lib.utils.resource_tracker import cleanup_all_resources
    await cleanup_all_resources()
    
    logger.info("Resource cleanup validation completed!")


if __name__ == "__main__":
    asyncio.run(main())